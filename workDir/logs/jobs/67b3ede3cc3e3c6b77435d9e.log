[TRACE] 2025-02-18 10:18:43.276 - [任务 6] - Task initialization... 
[TRACE] 2025-02-18 10:18:43.277 - [任务 6] - Start task milestones: 67b3ede3cc3e3c6b77435d9e(任务 6) 
[INFO ] 2025-02-18 10:18:43.404 - [任务 6] - Loading table structure completed 
[TRACE] 2025-02-18 10:18:43.405 - [任务 6] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-18 10:18:43.456 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-18 10:18:43.456 - [任务 6] - Task started 
[TRACE] 2025-02-18 10:18:43.480 - [任务 6][表编辑] - Node 表编辑[58436480-1965-47dc-b688-0a58a9342228] start preload schema,table counts: 1 
[TRACE] 2025-02-18 10:18:43.481 - [任务 6][PG] - Node PG[bf3cc9a8-b543-4d4f-9c60-72c21f7386fa] start preload schema,table counts: 1 
[TRACE] 2025-02-18 10:18:43.481 - [任务 6][Sybase] - Node Sybase[cff4cb90-29a4-4414-aaa6-5a3c8acf894e] start preload schema,table counts: 1 
[TRACE] 2025-02-18 10:18:43.481 - [任务 6][表编辑] - Node 表编辑[58436480-1965-47dc-b688-0a58a9342228] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 10:18:43.481 - [任务 6][PG] - Node PG[bf3cc9a8-b543-4d4f-9c60-72c21f7386fa] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 10:18:43.481 - [任务 6][Sybase] - Node Sybase[cff4cb90-29a4-4414-aaa6-5a3c8acf894e] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 10:18:43.598 - [任务 6][表编辑] - Node table_rename_processor(表编辑: 58436480-1965-47dc-b688-0a58a9342228) enable batch process 
[INFO ] 2025-02-18 10:18:44.046 - [任务 6][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-02-18 10:18:44.049 - [任务 6][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-02-18 10:18:44.049 - [任务 6][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-02-18 10:18:44.050 - [任务 6][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-18 10:18:44.050 - [任务 6][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-02-18 10:18:44.431 - [任务 6][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-02-18 10:18:44.431 - [任务 6][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[TRACE] 2025-02-18 10:18:44.431 - [任务 6][Sybase] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-18 10:18:44.495 - [任务 6][Sybase] - Starting batch read from 1 tables 
[TRACE] 2025-02-18 10:18:44.509 - [任务 6][Sybase] - Initial sync started 
[INFO ] 2025-02-18 10:18:44.509 - [任务 6][Sybase] - Starting batch read from table: td_text 
[TRACE] 2025-02-18 10:18:44.509 - [任务 6][Sybase] - Table td_text is going to be initial synced 
[INFO ] 2025-02-18 10:18:44.878 - [任务 6][PG] - Sink connector(PG) initialization completed 
[TRACE] 2025-02-18 10:18:44.880 - [任务 6][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-18 10:18:44.880 - [任务 6][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-18 10:18:44.909 - [任务 6][PG] - Apply table structure to target database 
[TRACE] 2025-02-18 10:18:44.909 - [任务 6][Sybase] - Query snapshot row size completed: Sybase(cff4cb90-29a4-4414-aaa6-5a3c8acf894e) 
[INFO ] 2025-02-18 10:18:44.923 - [任务 6][Sybase] - Table td_text has been completed batch read 
[TRACE] 2025-02-18 10:18:44.924 - [任务 6][Sybase] - Initial sync completed 
[INFO ] 2025-02-18 10:18:44.925 - [任务 6][Sybase] - Batch read completed. 
[TRACE] 2025-02-18 10:18:44.925 - [任务 6][Sybase] - Incremental sync starting... 
[TRACE] 2025-02-18 10:18:44.925 - [任务 6][Sybase] - Initial sync completed 
[TRACE] 2025-02-18 10:18:44.925 - [任务 6][Sybase] - Starting stream read, table list: [td_text], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-02-18 10:18:44.925 - [任务 6][Sybase] - Starting incremental sync using database log parser 
[WARN ] 2025-02-18 10:18:45.115 - [任务 6][PG] - Table text_td_text not exists, skip drop 
[INFO ] 2025-02-18 10:18:45.118 - [任务 6][Sybase] - startRid: 368324, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 10:18:45.118 - [任务 6][Sybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 10:18:45.118 - [任务 6][Sybase] - sybase offset in database is: startRid: 368324, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-02-18 10:18:45.118 - [任务 6][Sybase] - we will use offset in database, how ever, this is safe: startRid: 368324, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 10:18:45.322 - [任务 6][Sybase] - sybase cdc work with mode v1: auto rescan 
[INFO ] 2025-02-18 10:18:46.151 - [任务 6][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-02-18 10:18:46.152 - [任务 6][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-02-18 10:18:46.185 - [任务 6][Sybase] - opened cdc for tables: {dbo=[td_text]} 
[INFO ] 2025-02-18 10:18:46.185 - [任务 6][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-02-18 10:18:46.391 - [任务 6][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-02-18 10:18:46.590 - [任务 6][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-02-18 10:18:46.590 - [任务 6][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-02-18 10:18:46.590 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 0 
[INFO ] 2025-02-18 10:18:49.597 - [任务 6][Sybase] - rebuild statement with 368324, 0 
[INFO ] 2025-02-18 10:18:49.766 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 10:18:49.766 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 10:18:49.777 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 28 
[TRACE] 2025-02-18 10:18:51.372 - [任务 6][PG] - Table 'text_td_text' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2025-02-18 10:18:52.926 - [任务 6][Sybase] - rebuild statement with 368324, 28 
[INFO ] 2025-02-18 10:18:52.949 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 28 
[INFO ] 2025-02-18 10:18:55.941 - [任务 6][Sybase] - rebuild statement with 368324, 28 
[INFO ] 2025-02-18 10:18:56.454 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 28 
[INFO ] 2025-02-18 10:18:59.292 - [任务 6][Sybase] - rebuild statement with 368324, 28 
[INFO ] 2025-02-18 10:18:59.380 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 28 
[INFO ] 2025-02-18 10:19:02.501 - [任务 6][Sybase] - rebuild statement with 368324, 28 
[INFO ] 2025-02-18 10:19:02.523 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 28 
[INFO ] 2025-02-18 10:19:05.527 - [任务 6][Sybase] - rebuild statement with 368324, 28 
[INFO ] 2025-02-18 10:19:05.626 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 28 
[INFO ] 2025-02-18 10:19:08.628 - [任务 6][Sybase] - rebuild statement with 368324, 28 
[INFO ] 2025-02-18 10:19:08.922 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 28 
[INFO ] 2025-02-18 10:19:11.833 - [任务 6][Sybase] - rebuild statement with 368324, 28 
[INFO ] 2025-02-18 10:19:11.863 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 28 
[INFO ] 2025-02-18 10:19:14.845 - [任务 6][Sybase] - rebuild statement with 368324, 28 
[INFO ] 2025-02-18 10:19:14.946 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 28 
[INFO ] 2025-02-18 10:19:17.957 - [任务 6][Sybase] - rebuild statement with 368324, 28 
[INFO ] 2025-02-18 10:19:18.079 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 28 
[INFO ] 2025-02-18 10:19:21.099 - [任务 6][Sybase] - rebuild statement with 368324, 28 
[INFO ] 2025-02-18 10:19:21.229 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 28 
[INFO ] 2025-02-18 10:19:24.265 - [任务 6][Sybase] - rebuild statement with 368324, 28 
[INFO ] 2025-02-18 10:19:24.525 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 28 
[INFO ] 2025-02-18 10:19:27.344 - [任务 6][Sybase] - rebuild statement with 368324, 28 
[INFO ] 2025-02-18 10:19:27.416 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 28 
[INFO ] 2025-02-18 10:19:30.429 - [任务 6][Sybase] - rebuild statement with 368324, 28 
[INFO ] 2025-02-18 10:19:30.563 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 10:19:30.564 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 10:19:30.769 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:19:33.629 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:19:33.655 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:19:36.660 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:19:36.766 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:19:39.771 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:19:39.840 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:19:42.846 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:19:43.088 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:19:46.268 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:19:46.574 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:19:49.582 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:19:49.681 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:19:52.682 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:19:52.765 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:19:55.763 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:19:55.853 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:19:58.856 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:19:59.560 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:20:02.557 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:20:05.127 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:20:08.135 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:20:11.546 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:20:14.384 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:20:14.519 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:20:17.696 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:20:19.184 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:20:22.325 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:20:23.411 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:20:26.415 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:20:27.190 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:20:30.325 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:20:30.654 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:20:33.857 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:20:34.018 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:20:37.024 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:20:40.126 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:20:43.130 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:20:44.541 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:20:47.550 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:20:47.756 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 41 
[INFO ] 2025-02-18 10:20:50.650 - [任务 6][Sybase] - rebuild statement with 368324, 41 
[INFO ] 2025-02-18 10:20:51.164 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 10:20:51.164 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 10:20:51.371 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:20:54.182 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:20:54.383 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:20:57.405 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:20:57.406 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:00.525 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:00.577 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:03.655 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:03.655 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:06.758 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:06.978 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:09.920 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:09.921 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:13.042 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:13.042 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:16.045 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:16.256 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:19.186 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:19.330 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:22.443 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:22.443 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:25.448 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:25.633 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:28.709 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:28.710 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:31.717 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:31.864 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:34.883 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:34.995 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:38.093 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:38.093 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:41.110 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:41.230 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:44.231 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:44.315 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:47.323 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:47.509 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:50.513 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:50.708 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:53.750 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:54.266 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:21:57.282 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:21:57.705 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:22:00.853 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:22:02.375 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:22:05.208 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:22:05.598 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:22:08.781 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:22:09.084 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:22:12.088 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:22:12.292 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:22:15.222 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:22:18.441 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:22:21.447 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:22:22.697 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:22:25.666 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:22:26.465 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:22:29.288 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:22:29.697 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:22:32.651 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:22:33.410 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:22:36.438 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:22:36.784 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:22:39.768 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:22:41.219 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:22:44.105 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:22:45.729 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:22:48.729 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:22:49.908 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:22:52.766 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:22:53.163 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:22:56.368 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:22:57.339 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:23:00.375 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:23:01.165 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:23:04.170 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:23:04.523 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:23:07.526 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:23:08.593 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:23:11.489 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:23:11.826 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:23:14.972 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:23:15.301 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:23:18.307 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:23:20.518 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:23:23.326 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:23:24.330 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:23:27.337 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:23:28.556 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:23:31.683 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:23:32.247 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:23:35.250 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:23:35.884 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:23:38.844 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:23:39.901 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:23:43.034 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:23:43.649 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:23:46.497 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:23:47.716 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:23:50.593 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:23:51.684 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:23:54.690 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:23:55.062 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:23:58.198 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:23:58.515 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:24:01.514 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:24:02.328 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:24:05.267 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:24:05.357 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:24:08.473 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:24:08.677 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:24:11.514 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:24:12.519 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:24:15.330 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:24:15.541 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:24:18.487 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:24:18.631 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:24:21.721 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:24:22.492 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:24:25.643 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:24:25.849 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 44 
[INFO ] 2025-02-18 10:24:28.856 - [任务 6][Sybase] - rebuild statement with 368324, 44 
[INFO ] 2025-02-18 10:24:29.117 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 10:24:29.117 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 10:24:29.323 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 57 
[INFO ] 2025-02-18 10:24:32.215 - [任务 6][Sybase] - rebuild statement with 368324, 57 
[INFO ] 2025-02-18 10:24:34.205 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 57 
[INFO ] 2025-02-18 10:24:37.211 - [任务 6][Sybase] - rebuild statement with 368324, 57 
[INFO ] 2025-02-18 10:24:38.898 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 57 
[INFO ] 2025-02-18 10:24:41.988 - [任务 6][Sybase] - rebuild statement with 368324, 57 
[INFO ] 2025-02-18 10:24:42.988 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 57 
[INFO ] 2025-02-18 10:24:45.996 - [任务 6][Sybase] - rebuild statement with 368324, 57 
[INFO ] 2025-02-18 10:24:47.332 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 57 
[INFO ] 2025-02-18 10:24:50.200 - [任务 6][Sybase] - rebuild statement with 368324, 57 
[INFO ] 2025-02-18 10:24:50.462 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 57 
[INFO ] 2025-02-18 10:24:53.575 - [任务 6][Sybase] - rebuild statement with 368324, 57 
[INFO ] 2025-02-18 10:24:54.136 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 57 
[INFO ] 2025-02-18 10:24:57.142 - [任务 6][Sybase] - rebuild statement with 368324, 57 
[INFO ] 2025-02-18 10:24:58.487 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 57 
[INFO ] 2025-02-18 10:25:01.428 - [任务 6][Sybase] - rebuild statement with 368324, 57 
[INFO ] 2025-02-18 10:25:02.750 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 57 
[INFO ] 2025-02-18 10:25:05.699 - [任务 6][Sybase] - rebuild statement with 368324, 57 
[INFO ] 2025-02-18 10:25:06.043 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 57 
[INFO ] 2025-02-18 10:25:09.050 - [任务 6][Sybase] - rebuild statement with 368324, 57 
[INFO ] 2025-02-18 10:25:10.215 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 10:25:10.215 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 10:25:10.422 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:25:13.232 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:25:14.438 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:25:17.444 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:25:18.771 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:25:21.773 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:25:23.775 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:25:26.779 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:25:27.483 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:25:30.490 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:25:31.305 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:25:34.157 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:25:35.556 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:25:38.558 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:25:39.374 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:25:42.213 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:25:42.623 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:25:45.570 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:25:45.776 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:25:48.733 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:25:50.363 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:25:53.380 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:25:53.409 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:25:56.588 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:25:56.936 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:00.012 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:02.154 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:05.160 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:06.893 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:09.900 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:10.246 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:13.255 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:13.501 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:16.500 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:17.043 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:20.021 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:20.306 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:23.241 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:24.049 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:26.975 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:27.587 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:30.404 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:30.546 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:33.553 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:33.755 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:36.759 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:36.963 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:39.970 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:39.970 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:43.084 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:43.084 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:46.201 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:46.201 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:49.306 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:49.512 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:52.454 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:52.455 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:55.573 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:55.573 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:26:58.701 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:26:58.903 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:01.723 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:01.927 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:04.823 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:05.028 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:07.946 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:08.114 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:11.120 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:11.325 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:14.363 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:14.363 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:17.368 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:17.574 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:20.600 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:20.801 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:23.665 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:23.871 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:26.797 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:26.919 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:30.045 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:30.047 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:33.172 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:33.172 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:36.311 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:36.518 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:39.320 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:39.523 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:42.507 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:42.913 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:45.718 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:45.913 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:49.103 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:49.304 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:52.112 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:52.315 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:55.478 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:55.687 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:27:58.491 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:27:58.665 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:01.671 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:02.116 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:05.121 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:05.445 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:08.337 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:08.620 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:11.624 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:11.939 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:14.855 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:15.059 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:17.895 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:18.033 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:21.014 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:21.395 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:24.232 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:24.348 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:27.334 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:27.570 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:30.751 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:30.756 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:33.763 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:33.930 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:36.933 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:37.096 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:40.067 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:40.216 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:43.225 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:43.373 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:46.455 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:46.456 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:49.493 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:49.611 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:52.614 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:52.766 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:55.919 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:56.120 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:28:59.113 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:28:59.113 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:02.156 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:02.335 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:05.340 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:05.456 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:08.434 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:08.778 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:11.733 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:11.939 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:14.951 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:14.955 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:17.961 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:18.360 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:21.361 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:21.361 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:24.367 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:24.530 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:27.676 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:27.814 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:30.696 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:30.890 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:33.866 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:34.099 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:37.217 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:37.218 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:40.220 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:40.395 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:43.502 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:43.505 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:46.508 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:46.713 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:49.718 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:50.035 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:52.892 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:53.041 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 60 
[INFO ] 2025-02-18 10:29:56.079 - [任务 6][Sybase] - rebuild statement with 368324, 60 
[INFO ] 2025-02-18 10:29:56.250 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 10:29:56.250 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 10:29:56.453 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 73 
[INFO ] 2025-02-18 10:29:59.267 - [任务 6][Sybase] - rebuild statement with 368324, 73 
[INFO ] 2025-02-18 10:29:59.373 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 73 
[INFO ] 2025-02-18 10:30:02.377 - [任务 6][Sybase] - rebuild statement with 368324, 73 
[INFO ] 2025-02-18 10:30:02.527 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 73 
[INFO ] 2025-02-18 10:30:05.533 - [任务 6][Sybase] - rebuild statement with 368324, 73 
[INFO ] 2025-02-18 10:30:05.776 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 73 
[INFO ] 2025-02-18 10:30:08.779 - [任务 6][Sybase] - rebuild statement with 368324, 73 
[INFO ] 2025-02-18 10:30:08.899 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 10:30:08.899 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 10:30:08.998 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:30:12.106 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:30:12.147 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:30:15.150 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:30:15.434 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:30:18.480 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:30:18.535 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:30:21.590 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:30:21.670 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:30:24.673 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:30:24.860 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:30:27.869 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:30:28.070 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:30:31.082 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:30:31.196 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:30:34.198 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:30:34.384 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:30:37.390 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:30:37.546 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:30:40.555 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:30:40.697 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:30:43.701 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:30:43.875 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:30:46.881 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:30:47.139 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:30:50.242 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:30:50.290 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:30:53.296 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:30:53.426 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:30:56.431 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:30:56.633 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:30:59.769 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:30:59.930 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:03.117 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:03.163 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:06.311 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:06.312 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:09.438 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:09.588 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:12.682 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:12.805 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:15.810 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:15.954 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:18.961 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:19.093 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:22.095 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:22.253 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:25.299 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:25.457 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:28.612 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:28.738 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:31.627 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:31.799 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:34.803 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:34.970 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:37.954 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:38.178 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:41.290 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:41.291 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:44.296 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:44.480 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:47.510 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:47.745 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:50.669 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:50.797 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:53.812 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:54.114 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:31:57.122 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:31:57.199 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:00.176 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:00.370 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:03.455 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:03.594 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:06.645 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:06.768 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:09.770 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:09.935 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:12.940 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:13.119 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:16.183 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:16.346 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:19.351 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:19.453 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:22.457 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:22.622 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:25.629 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:25.815 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:28.820 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:29.215 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:32.219 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:32.473 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:35.443 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:35.752 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:38.889 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:38.890 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:41.895 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:42.145 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:45.149 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:45.331 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:48.337 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:48.504 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:51.514 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:51.723 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:54.730 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:54.905 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:32:57.912 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:32:58.095 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:01.104 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:01.249 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:04.255 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:04.438 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:07.393 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:07.607 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:10.609 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:10.734 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:13.750 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:14.099 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:16.947 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:17.085 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:20.090 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:20.289 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:23.271 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:23.610 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:26.498 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:26.657 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:29.663 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:29.821 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:32.826 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:32.951 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:35.954 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:36.120 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:39.123 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:39.283 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:42.288 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:42.433 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:45.405 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:45.572 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:48.592 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:48.736 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:51.856 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:51.857 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:54.857 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:55.024 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:33:58.029 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:33:58.234 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:01.242 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:01.389 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:04.395 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:04.552 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:07.554 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:07.752 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:10.752 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:10.875 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:13.881 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:14.030 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:17.167 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:17.168 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:20.209 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:20.334 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:23.335 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:23.485 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:26.511 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:26.659 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:29.661 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:29.799 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:32.783 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:33.129 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:35.960 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:36.120 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:39.127 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:39.322 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:42.327 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:42.471 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:45.476 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:45.653 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:48.658 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:48.807 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:51.809 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:51.948 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:54.948 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:55.149 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:34:58.175 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:34:58.309 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:01.314 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:01.497 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:04.458 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:04.711 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:07.854 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:08.061 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:11.013 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:11.014 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:14.019 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:14.402 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:17.388 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:17.389 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:20.393 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:20.734 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:23.715 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:23.716 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:26.746 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:26.937 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:29.945 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:30.081 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:33.089 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:33.366 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:36.372 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:36.534 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:39.541 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:39.736 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:42.705 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:42.863 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:46.026 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:46.027 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:49.016 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:49.391 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:52.213 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:52.404 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:55.409 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:55.567 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:35:58.573 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:35:58.929 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:01.893 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:02.099 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:05.046 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:05.255 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:08.241 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:08.241 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:11.256 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:11.441 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:14.548 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:14.549 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:17.551 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:17.811 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:20.814 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:21.389 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:24.375 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:25.177 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:28.166 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:28.553 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:31.597 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:31.767 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:34.835 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:35.171 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:38.081 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:38.345 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:41.346 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:41.623 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:44.629 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:45.380 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:48.312 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:49.609 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:52.613 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:52.992 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:56.074 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:56.477 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:36:59.482 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:36:59.680 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:37:02.837 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:37:02.909 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:37:06.048 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:37:06.273 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:37:09.278 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:37:10.370 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:37:13.373 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:37:14.462 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:37:17.466 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:37:19.187 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:37:22.334 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:37:23.119 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:37:26.077 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:37:26.862 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:37:29.813 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:37:30.515 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:37:33.520 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:37:35.106 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:37:38.305 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:37:38.385 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:37:41.553 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:37:41.908 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:37:44.982 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:37:45.578 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:37:48.584 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:37:49.807 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:37:52.874 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:37:53.184 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:37:56.098 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:37:56.445 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:37:59.404 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:38:00.049 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:38:03.057 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:38:03.404 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:38:06.409 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:38:06.772 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:38:09.777 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:38:09.960 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:38:12.965 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:38:13.215 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:38:16.187 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:38:16.590 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:38:19.596 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:38:20.909 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:38:23.910 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:38:24.212 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:38:27.215 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:38:27.803 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:38:30.807 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:38:31.480 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:38:34.666 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:38:36.300 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:38:39.308 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:38:39.916 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:38:42.839 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:38:43.892 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:38:47.098 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:38:47.337 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:38:50.343 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:38:51.255 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:38:54.259 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:38:54.710 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:38:57.714 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:38:58.215 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:39:01.221 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:39:02.519 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:39:05.524 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:39:05.730 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:39:08.761 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:39:09.175 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:39:12.001 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:39:12.892 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:39:15.736 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:39:16.344 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:39:19.389 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:39:19.608 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:39:22.613 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:39:23.418 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:39:26.426 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:39:27.034 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:39:29.892 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:39:30.504 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:39:33.515 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:39:35.654 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:39:38.522 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:39:39.340 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 76 
[INFO ] 2025-02-18 10:39:42.172 - [任务 6][Sybase] - rebuild statement with 368324, 76 
[INFO ] 2025-02-18 10:39:42.754 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 10:39:42.755 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 10:39:43.008 - [任务 6][Sybase] - dump tran success 
[WARN ] 2025-02-18 10:39:43.245 - [任务 6][Sybase] - update logs hold failed, error: The log transfer context for the current database is already reserved by ASE process 40. The log transfer context for the current database is not reserved.
 
[WARN ] 2025-02-18 10:39:43.246 - [任务 6][Sybase] - update sybase offset startRid to: 368324, rowId to: 80, and dump logs failed 
[INFO ] 2025-02-18 10:39:43.246 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:39:46.399 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:39:47.607 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:39:50.633 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:39:50.817 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:39:53.794 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:39:54.244 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:39:57.343 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:39:57.802 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:40:00.672 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:40:01.179 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:40:04.162 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:40:05.016 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:40:08.207 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:40:09.013 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:40:12.016 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:40:12.230 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:40:15.231 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:40:15.974 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:40:18.984 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:40:20.674 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:40:23.679 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:40:23.995 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:40:27.001 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:40:27.680 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:40:30.690 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:40:31.227 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:40:34.034 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:40:34.700 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:40:37.702 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:40:39.168 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:40:42.170 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:40:43.144 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:40:46.150 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:40:46.498 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:40:49.603 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:40:49.937 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:40:52.945 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:40:53.372 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:40:56.239 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:40:56.581 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:40:59.465 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:40:59.669 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:41:02.682 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:41:03.255 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:41:06.176 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:41:06.274 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:41:09.411 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:41:09.411 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 80 
[INFO ] 2025-02-18 10:41:12.417 - [任务 6][Sybase] - rebuild statement with 368324, 80 
[INFO ] 2025-02-18 10:41:12.696 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 10:41:12.696 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 10:41:12.712 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:41:15.717 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:41:16.214 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:41:19.221 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:41:20.677 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:41:23.519 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:41:23.872 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:41:26.854 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:41:27.709 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:41:30.713 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:41:31.037 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:41:34.042 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:41:34.515 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:41:37.518 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:41:38.042 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:41:41.050 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:41:41.559 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:41:44.563 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:41:45.357 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:41:48.311 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:41:48.898 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:41:51.813 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:41:52.339 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:41:55.304 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:41:55.819 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:41:58.983 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:41:59.156 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:42:02.215 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:42:03.384 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:42:06.388 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:42:06.679 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:42:09.709 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:42:11.008 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:42:13.922 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:42:14.279 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:42:17.287 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:42:17.573 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:42:20.576 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:42:22.108 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:42:25.058 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:42:25.469 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:42:28.275 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:42:29.841 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:42:32.686 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:42:33.381 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:42:36.516 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:42:37.104 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:42:40.167 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:42:40.572 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:42:43.588 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:42:44.410 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:42:47.415 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:42:47.828 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:42:50.664 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:42:51.642 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:42:54.838 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:42:55.079 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:42:58.276 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:42:59.148 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:43:02.056 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:43:02.668 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:43:05.479 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:43:05.775 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:43:08.780 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:43:09.517 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:43:12.527 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:43:13.264 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:43:16.269 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:43:16.773 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:43:19.822 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:43:20.263 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:43:23.269 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:43:24.579 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:43:27.586 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:43:28.198 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:43:31.047 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:43:32.188 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:43:35.261 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:43:35.654 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:43:38.663 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:43:38.992 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:43:41.874 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:43:42.460 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:43:45.453 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:43:46.331 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:43:49.278 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:43:49.762 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:43:52.767 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:43:52.968 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:43:56.085 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:43:56.085 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:43:59.089 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:43:59.271 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:02.424 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:44:02.425 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:05.539 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:44:05.539 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:08.572 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:44:08.778 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:11.882 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:44:11.911 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:14.915 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:44:15.325 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:18.196 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:44:18.807 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:21.757 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:44:22.143 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:25.147 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:44:25.347 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:28.548 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:44:28.756 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:31.677 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:44:31.861 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:34.967 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:44:35.307 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:38.316 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:44:38.534 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:41.538 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:44:41.832 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:44.837 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:44:46.477 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:49.482 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:44:49.831 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:52.776 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:44:53.218 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:56.224 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:44:56.917 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:44:59.919 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:45:00.219 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:45:03.226 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:45:05.925 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:45:08.968 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:45:09.161 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:45:12.238 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:45:12.649 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:45:15.628 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:45:15.696 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:45:18.880 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:45:18.917 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:45:21.923 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:45:23.020 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:45:26.104 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:45:27.126 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:45:30.135 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:45:30.354 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:45:33.361 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:45:33.972 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:45:36.918 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:45:37.321 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:45:40.326 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:45:40.941 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:45:43.931 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:45:43.966 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:45:46.971 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:45:47.248 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:45:50.252 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:45:50.852 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:45:53.809 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:45:54.231 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:45:57.293 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:45:57.761 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:46:00.765 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:46:01.001 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:46:04.012 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:46:04.485 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:46:07.474 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:46:07.951 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:46:10.935 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:46:11.508 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:46:14.320 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:46:15.442 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:46:18.487 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:46:18.701 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:46:21.878 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:46:22.227 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:46:25.256 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:46:25.657 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:46:28.782 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:46:28.863 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:46:31.993 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:46:32.198 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:46:35.203 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:46:36.297 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:46:39.304 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:46:40.597 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:46:43.772 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:46:44.401 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:46:47.264 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:46:48.066 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:46:51.055 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:46:51.426 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:46:54.432 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:46:54.676 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:46:57.650 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:46:59.359 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:47:02.208 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:47:02.998 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:47:06.001 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:47:06.160 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:47:09.168 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:47:10.389 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:47:13.292 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:47:14.029 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:47:17.229 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:47:17.447 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:47:20.640 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:47:22.067 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:47:25.203 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:47:25.203 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:47:28.378 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:47:28.608 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:47:31.595 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:47:31.829 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:47:34.814 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:47:34.981 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:47:37.985 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:47:38.165 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:47:41.171 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:47:41.549 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:47:44.520 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:47:44.727 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:47:47.733 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:47:48.090 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:47:51.097 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:47:51.300 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:47:54.255 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:47:54.456 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:47:57.642 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:47:57.643 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:00.648 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:00.757 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:03.764 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:03.971 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:06.977 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:07.206 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:10.195 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:10.567 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:13.578 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:14.352 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:17.236 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:18.726 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:21.731 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:21.881 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:24.887 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:25.013 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:28.016 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:28.271 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:31.276 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:31.458 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:34.464 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:35.071 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:38.075 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:38.280 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:41.288 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:41.439 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:44.447 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:44.773 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:47.621 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:47.790 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:50.797 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:51.076 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:54.077 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:54.267 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:48:57.453 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:48:57.659 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:00.611 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:00.612 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:03.625 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:03.991 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:06.830 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:06.998 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:09.973 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:10.171 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:13.177 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:13.415 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:16.419 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:16.541 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:19.702 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:19.704 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:22.708 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:22.890 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:25.898 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:26.033 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:29.042 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:29.247 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:32.257 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:32.411 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:35.416 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:35.610 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:38.614 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:38.777 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:41.782 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:42.215 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:45.158 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:45.177 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:48.172 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:48.328 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:51.331 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:51.525 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:54.532 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:54.717 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:49:57.681 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:49:57.825 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:00.832 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:01.004 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:04.013 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:04.186 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:07.191 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:07.374 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:10.378 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:10.551 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:13.562 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:13.800 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:16.801 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:17.020 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:20.153 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:20.223 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:23.365 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:23.395 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:26.383 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:26.587 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:29.630 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:29.812 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:32.820 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:33.030 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:36.036 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:36.261 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:39.263 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:39.527 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:42.413 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:42.655 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:45.664 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:45.841 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:48.843 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:49.168 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:52.175 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:52.408 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:55.377 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:55.529 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:50:58.507 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:50:58.663 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:01.836 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:01.865 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:04.871 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:05.015 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:08.097 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:08.225 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:11.215 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:11.407 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:14.618 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:14.643 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:17.765 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:17.888 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:20.926 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:21.037 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:24.142 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:24.177 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:27.190 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:27.356 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:30.358 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:30.701 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:33.622 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:34.201 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:37.278 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:37.500 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:40.508 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:40.841 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:43.843 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:44.134 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:47.036 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:47.282 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:50.290 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:50.501 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:53.512 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:53.750 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:56.702 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:51:56.947 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:51:59.949 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:00.187 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:03.195 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:03.421 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:06.428 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:06.609 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:09.613 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:09.815 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:12.841 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:13.064 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:16.070 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:16.381 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:19.387 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:19.529 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:22.534 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:22.761 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:25.769 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:26.039 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:29.249 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:29.281 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:32.274 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:32.597 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:35.603 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:35.814 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:38.821 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:39.092 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:42.100 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:42.483 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:45.490 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:45.815 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:48.821 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:48.986 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:51.993 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:52.229 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:55.231 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:55.415 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:52:58.407 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:52:58.726 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:01.732 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:01.964 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:04.967 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:05.200 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:08.205 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:08.440 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:11.445 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:11.732 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:14.734 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:15.227 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:18.426 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:18.655 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:21.690 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:21.896 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:24.902 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:25.110 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:28.116 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:28.375 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:31.377 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:31.620 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:34.624 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:34.880 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:37.886 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:38.099 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:41.103 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:41.310 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:44.327 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:44.547 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:47.552 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:47.791 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:50.793 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:50.963 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:54.158 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:54.189 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:53:57.214 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:53:57.424 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:54:00.433 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:54:00.665 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:54:03.669 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:54:03.930 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:54:06.937 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:54:07.101 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 99 
[INFO ] 2025-02-18 10:54:10.265 - [任务 6][Sybase] - rebuild statement with 368324, 99 
[INFO ] 2025-02-18 10:54:10.360 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 10:54:10.360 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 10:54:10.412 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:54:13.377 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:54:13.577 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:54:16.604 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:54:16.766 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:54:19.839 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:54:20.046 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:54:23.058 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:54:23.435 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:54:26.442 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:54:26.776 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:54:29.656 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:54:29.757 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:54:32.764 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:54:32.913 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:54:35.930 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:54:36.056 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:54:39.064 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:54:39.219 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:54:42.225 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:54:42.461 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:54:45.471 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:54:45.613 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:54:48.617 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:54:48.807 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:54:51.809 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:54:51.919 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:54:54.924 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:54:55.187 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:54:58.185 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:54:58.407 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:01.403 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:01.597 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:04.602 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:04.872 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:07.972 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:08.227 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:11.232 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:11.394 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:14.394 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:14.640 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:17.616 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:17.927 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:20.935 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:21.126 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:24.127 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:24.573 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:27.547 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:27.593 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:30.597 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:31.165 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:34.129 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:34.450 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:37.378 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:37.669 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:40.682 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:40.759 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:43.764 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:44.057 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:47.079 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:47.522 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:50.687 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:50.687 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:53.694 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:53.878 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:55:56.884 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:55:57.041 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:00.047 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:00.244 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:03.251 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:03.655 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:06.603 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:06.604 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:09.623 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:09.874 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:12.819 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:12.923 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:15.971 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:16.050 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:19.223 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:19.318 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:22.282 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:22.493 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:25.460 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:25.629 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:28.641 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:28.869 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:31.917 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:32.242 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:35.245 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:35.700 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:38.705 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:39.169 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:41.978 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:42.276 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:45.316 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:45.439 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:48.443 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:48.671 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:51.628 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:51.853 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:54.860 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:55.075 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:56:58.082 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:56:58.289 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:01.293 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:01.576 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:04.578 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:04.737 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:07.757 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:07.975 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:10.981 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:11.282 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:14.284 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:14.360 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:17.364 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:17.704 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:20.731 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:20.899 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:23.907 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:24.057 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:27.062 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:27.296 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:30.351 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:30.568 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:33.520 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:33.711 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:36.722 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:36.923 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:39.933 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:40.165 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:43.169 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:43.476 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:46.481 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:46.713 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:49.709 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:49.870 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:52.876 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:53.050 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:56.053 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:56.322 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:57:59.347 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:57:59.588 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:02.593 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:02.806 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:05.810 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:05.933 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:08.937 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:09.176 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:12.178 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:12.390 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:15.396 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:15.615 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:18.622 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:18.843 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:21.901 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:22.048 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:25.050 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:25.277 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:28.281 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:28.557 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:31.562 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:31.704 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:34.711 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:34.891 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:37.894 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:38.109 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:41.114 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:41.330 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:44.315 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:44.510 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:47.574 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:47.690 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:50.697 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:50.857 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:53.836 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:54.078 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:58:57.123 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:58:57.329 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:00.471 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:00.482 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:03.488 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:03.636 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:06.643 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:06.828 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:09.833 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:10.079 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:13.084 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:13.509 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:16.341 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:16.535 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:19.543 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:19.749 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:22.755 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:23.008 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:26.159 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:26.215 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:29.224 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:29.349 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:32.358 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:32.596 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:35.603 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:35.781 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:38.800 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:39.155 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:42.170 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:42.601 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:45.602 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:45.694 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:48.687 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:49.001 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:51.946 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:52.170 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:55.144 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:55.432 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 10:59:58.347 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 10:59:58.509 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 11:00:01.477 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 11:00:01.755 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 11:00:04.760 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 11:00:05.005 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 11:00:08.029 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 11:00:08.264 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 11:00:11.476 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 11:00:11.516 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 11:00:14.491 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 11:00:14.747 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 115 
[INFO ] 2025-02-18 11:00:17.747 - [任务 6][Sybase] - rebuild statement with 368324, 115 
[INFO ] 2025-02-18 11:00:17.972 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 11:00:17.972 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 11:00:18.183 - [任务 6][Sybase] - dump tran success 
[WARN ] 2025-02-18 11:00:18.184 - [任务 6][Sybase] - update logs hold failed, error: The log transfer context for the current database is already reserved by ASE process 47. The log transfer context for the current database is not reserved.
 
[WARN ] 2025-02-18 11:00:18.184 - [任务 6][Sybase] - update sybase offset startRid to: 368324, rowId to: 128, and dump logs failed 
[INFO ] 2025-02-18 11:00:18.184 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 128 
[INFO ] 2025-02-18 11:00:21.190 - [任务 6][Sybase] - rebuild statement with 368324, 128 
[INFO ] 2025-02-18 11:00:21.382 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 128 
[INFO ] 2025-02-18 11:00:24.544 - [任务 6][Sybase] - rebuild statement with 368324, 128 
[INFO ] 2025-02-18 11:00:24.591 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 128 
[INFO ] 2025-02-18 11:00:27.607 - [任务 6][Sybase] - rebuild statement with 368324, 128 
[INFO ] 2025-02-18 11:00:27.744 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 128 
[INFO ] 2025-02-18 11:00:30.748 - [任务 6][Sybase] - rebuild statement with 368324, 128 
[INFO ] 2025-02-18 11:00:30.865 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 128 
[INFO ] 2025-02-18 11:00:33.872 - [任务 6][Sybase] - rebuild statement with 368324, 128 
[INFO ] 2025-02-18 11:00:34.151 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 11:00:34.152 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 11:00:34.373 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:00:37.273 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:00:37.314 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:00:40.461 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:00:40.462 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:00:43.573 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:00:43.813 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:00:46.787 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:00:46.825 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:00:49.908 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:00:49.951 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:00:52.936 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:00:53.086 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:00:56.102 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:00:56.416 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:00:59.419 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:00:59.627 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:02.628 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:02.841 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:05.843 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:06.035 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:09.039 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:09.265 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:12.268 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:12.496 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:15.499 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:15.681 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:18.685 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:18.882 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:21.887 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:22.132 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:25.258 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:25.303 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:28.502 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:28.572 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:31.579 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:31.834 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:34.842 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:35.017 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:38.029 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:38.417 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:41.379 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:41.434 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:44.439 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:44.774 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:47.761 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:47.964 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:50.969 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:51.261 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:54.265 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:54.475 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:01:57.315 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:01:57.530 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:00.535 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:00.900 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:03.854 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:03.854 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:06.862 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:07.285 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:10.285 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:10.345 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:13.351 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:13.623 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:16.630 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:16.804 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:19.857 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:20.007 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:22.975 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:24.260 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:27.087 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:27.531 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:30.519 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:30.740 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:33.744 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:33.971 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:36.977 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:37.401 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:40.213 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:40.369 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:43.372 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:43.631 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:46.635 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:47.044 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:50.047 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:50.327 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:53.269 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:53.502 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:56.507 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:56.830 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:02:59.833 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:02:59.859 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:02.861 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:03.305 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:06.103 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:06.273 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:09.278 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:09.486 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:12.491 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:12.707 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:15.710 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:15.910 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:18.918 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:19.120 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:22.066 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:22.352 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:25.356 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:25.619 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:28.624 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:28.841 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:31.843 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:31.985 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:34.987 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:35.251 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:38.256 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:38.626 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:41.461 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:41.628 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:44.639 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:44.800 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:47.805 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:48.072 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:51.077 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:51.262 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:54.360 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:54.391 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:03:57.370 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:03:57.468 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:00.475 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:00.652 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:03.662 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:03.786 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:06.788 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:06.964 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:09.966 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:10.091 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:13.097 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:13.265 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:16.270 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:16.473 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:19.481 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:19.787 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:22.698 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:22.890 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:25.897 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:26.079 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:29.085 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:29.257 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:32.394 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:32.600 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:35.443 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:35.590 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:38.597 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:38.774 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:41.775 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:41.888 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:44.894 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:45.034 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:48.095 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:48.294 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:51.411 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:51.435 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:54.438 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:54.656 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:04:57.554 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:04:57.676 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:00.676 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:00.795 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:03.895 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:03.935 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:06.935 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:07.136 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:10.122 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:10.303 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:13.275 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:13.437 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:16.439 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:16.577 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:19.668 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:19.669 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:22.787 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:22.791 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:25.796 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:25.887 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:28.990 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:28.990 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:32.001 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:32.123 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:35.125 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:35.229 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:38.366 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:38.373 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:41.452 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:41.453 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:44.557 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:44.560 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:47.658 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:47.658 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:50.752 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:50.753 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:53.834 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:53.834 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:56.862 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:05:56.990 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:05:59.994 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:00.125 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:03.085 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:03.200 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:06.190 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:06.299 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:09.304 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:09.422 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:12.459 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:12.550 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:15.572 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:15.657 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:18.697 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:18.762 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:21.711 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:21.819 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:24.828 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:24.913 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:28.009 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:28.010 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:31.012 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:31.102 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:34.107 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:34.293 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:37.313 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:37.317 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:40.419 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:40.510 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:43.548 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:43.862 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:46.892 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:47.007 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:50.093 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:50.117 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:53.124 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:53.303 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:56.258 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:56.385 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:06:59.391 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:06:59.479 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:02.509 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:02.701 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:05.706 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:05.780 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:08.743 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:08.886 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:11.892 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:11.997 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:15.000 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:15.101 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:18.123 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:18.234 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:21.199 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:21.307 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:24.286 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:24.589 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:27.474 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:27.510 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:30.515 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:30.596 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:33.600 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:33.715 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:36.797 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:36.816 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:39.819 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:40.090 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:42.977 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:42.996 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:46.006 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:46.132 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:49.194 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:49.438 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:52.316 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:52.350 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:55.352 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:55.435 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:07:58.441 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:07:58.526 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:01.532 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:01.863 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:04.779 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:04.807 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:07.809 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:07.917 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:10.923 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:11.017 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:14.020 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:14.401 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:17.355 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:17.355 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:20.359 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:20.454 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:23.465 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:23.564 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:26.570 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:26.883 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:29.787 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:29.814 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:32.819 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:32.925 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:35.935 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:36.053 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:39.057 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:39.134 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:42.141 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:42.258 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:45.264 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:45.369 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:48.371 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:48.476 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:51.494 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:51.577 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:54.708 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:54.708 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:08:57.741 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:08:57.821 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:00.823 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:00.929 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:03.935 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:04.040 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:07.047 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:07.215 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:10.221 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:10.509 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:13.319 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:13.726 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:16.645 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:16.755 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:19.758 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:19.832 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:22.883 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:22.951 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:25.952 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:26.061 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:29.053 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:29.217 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:32.223 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:32.316 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:35.286 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:35.643 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:38.650 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:38.731 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:41.941 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:42.066 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:45.167 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:45.369 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:48.183 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:48.357 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:51.358 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:51.449 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:54.455 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:54.568 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:09:57.571 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:09:57.680 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:00.689 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:00.808 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:03.810 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:03.929 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:06.905 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:07.039 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:10.043 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:10.172 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:13.260 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:13.261 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:16.295 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:16.407 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:19.412 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:19.581 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:22.686 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:22.686 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:25.691 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:26.261 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:29.107 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:29.196 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:32.285 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:32.286 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:35.336 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:35.540 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:38.468 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:38.679 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:41.758 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:41.758 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:44.890 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:45.091 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:48.117 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:48.117 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:51.246 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:51.453 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:54.375 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:54.586 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:10:57.455 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:10:57.632 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:00.641 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:00.755 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:03.760 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:04.258 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:07.260 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:07.394 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:10.354 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:10.478 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:13.491 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:14.116 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:16.955 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:17.095 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:20.290 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:20.487 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:23.489 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:23.699 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:26.597 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:26.713 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:29.797 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:30.000 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:32.851 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:32.952 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:36.142 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:36.739 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:39.753 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:39.802 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:42.778 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:42.953 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:45.961 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:46.101 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:49.215 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:49.215 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:52.224 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:52.354 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:55.478 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:55.655 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:11:58.623 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:11:58.960 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:02.164 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:02.300 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:05.364 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:05.459 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:08.464 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:08.613 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:11.625 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:11.931 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:14.766 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:14.856 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:17.989 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:17.989 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:21.101 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:21.208 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:24.315 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:24.316 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:27.421 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:27.425 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:30.555 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:30.555 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:33.648 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:33.648 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:36.660 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:36.871 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:39.865 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:39.865 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:43.016 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:43.174 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:46.186 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:46.611 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:49.496 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:49.906 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:52.965 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:52.966 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:56.033 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:56.450 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:12:59.295 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:12:59.385 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:02.596 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:02.805 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:05.889 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:06.100 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:08.951 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:09.160 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:12.099 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:12.437 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:15.557 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:15.558 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:18.686 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:19.101 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:21.907 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:22.259 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:25.368 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:25.368 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:28.379 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:28.492 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:31.687 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:31.687 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:34.882 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:34.883 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:37.894 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:38.211 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:41.337 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:41.337 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:44.470 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:44.674 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:47.518 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:47.728 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:50.635 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:51.258 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:54.394 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:54.397 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:13:57.402 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:13:57.603 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:00.512 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:00.923 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:03.950 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:03.952 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:06.957 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:07.167 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:10.075 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:10.286 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:13.189 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:13.396 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:16.276 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:16.363 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:19.368 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:19.489 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:22.566 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:22.743 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:25.748 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:26.307 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:29.311 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:30.126 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:33.012 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:33.233 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:36.384 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:36.789 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:39.650 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:39.818 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:42.821 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:43.230 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:46.140 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:46.140 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:49.201 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:49.406 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:52.431 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:52.432 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:55.495 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:55.697 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:14:58.591 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:14:58.730 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:01.731 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:01.906 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:04.911 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:05.319 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:08.279 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:08.484 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:11.363 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:11.483 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:14.552 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:14.755 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:17.679 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:17.881 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:20.885 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:20.885 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:23.895 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:24.101 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:26.996 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:27.130 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:30.203 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:30.609 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:33.534 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:33.738 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:36.660 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:36.866 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:39.672 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:39.878 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:42.793 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:43.001 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:45.877 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:46.081 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:48.956 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:49.161 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:52.122 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:52.732 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:55.619 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:55.728 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:15:58.733 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:15:58.803 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:01.854 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:02.054 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:04.891 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:05.007 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:08.011 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:08.421 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:11.227 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:11.348 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:14.355 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:14.566 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:17.546 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:17.547 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:20.656 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:20.656 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:23.663 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:23.864 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:26.864 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:27.277 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:30.182 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:30.288 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:33.399 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:33.401 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:36.495 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:36.496 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:39.498 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:39.608 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:42.610 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:42.815 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:45.712 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:45.914 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:48.959 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:49.165 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:52.041 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:52.243 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:55.223 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:55.223 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:16:58.308 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:16:58.309 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:01.397 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:01.398 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:04.402 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:04.605 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:07.579 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:07.579 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:10.666 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:10.666 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:13.669 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:13.873 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:16.871 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:16.871 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:19.906 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:20.111 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:22.995 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:23.201 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:26.240 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:26.240 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:29.390 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:29.391 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:32.393 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:32.598 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:35.604 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:35.604 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:38.610 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:38.812 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:41.726 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:41.928 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:44.896 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:44.896 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:48.044 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:48.045 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:51.141 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:51.141 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:54.229 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:54.231 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:17:57.233 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:17:57.434 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:18:00.455 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:18:00.456 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:18:03.576 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:18:03.577 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:18:06.656 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:18:06.863 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:18:09.676 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:18:09.883 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:18:12.848 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:18:12.848 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:18:15.996 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:18:15.997 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:18:19.101 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:18:19.305 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:18:22.208 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:18:22.410 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:18:25.319 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:18:25.524 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:18:28.346 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:18:28.476 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:18:31.482 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:18:31.588 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:18:34.595 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:18:34.684 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:18:37.689 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:18:37.896 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:18:40.860 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:18:40.861 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:18:43.870 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:18:44.073 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 131 
[INFO ] 2025-02-18 11:18:46.990 - [任务 6][Sybase] - rebuild statement with 368324, 131 
[INFO ] 2025-02-18 11:18:47.050 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 11:18:47.050 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 11:18:47.129 - [任务 6][Sybase] - dump tran success 
[INFO ] 2025-02-18 11:18:47.138 - [任务 6][Sybase] - update logs hold success, startRid: 368324 
[INFO ] 2025-02-18 11:18:47.139 - [任务 6][Sybase] - update sybase offset startRid to: 368324, rowId to: 144, and dump logs 
[INFO ] 2025-02-18 11:18:47.139 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:18:50.160 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:18:50.248 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:18:53.252 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:18:53.457 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:18:56.453 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:18:56.454 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:18:59.547 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:18:59.547 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:02.556 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:02.760 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:05.659 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:05.864 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:08.737 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:08.942 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:11.809 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:12.012 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:14.990 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:14.991 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:18.113 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:18.114 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:21.192 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:21.192 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:24.271 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:24.272 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:27.350 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:27.351 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:30.355 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:30.559 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:33.550 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:33.550 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:36.555 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:36.760 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:39.712 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:39.712 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:42.787 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:42.787 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:45.790 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:45.991 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:48.867 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:48.999 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:52.000 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:52.097 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:55.152 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:55.358 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:19:58.296 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:19:58.296 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:01.301 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:01.504 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:04.473 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:04.473 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:07.568 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:07.568 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:10.642 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:10.642 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:13.731 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:13.731 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:16.737 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:16.942 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:19.905 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:19.995 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:22.998 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:23.097 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:26.108 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:26.172 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:29.175 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:29.250 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:32.254 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:32.327 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:35.330 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:35.390 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:38.394 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:38.463 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:41.469 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:41.545 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:44.550 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:44.633 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:47.639 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:47.728 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:50.734 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:50.832 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 144 
[INFO ] 2025-02-18 11:20:53.834 - [任务 6][Sybase] - rebuild statement with 368324, 144 
[INFO ] 2025-02-18 11:20:53.925 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 11:20:53.925 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 11:20:54.126 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:20:56.931 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:20:57.008 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:00.012 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:00.144 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:03.147 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:03.221 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:06.225 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:06.314 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:09.315 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:09.420 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:12.513 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:12.540 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:15.717 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:16.304 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:19.305 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:19.384 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:22.385 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:22.472 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:25.475 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:25.572 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:28.578 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:28.664 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:31.660 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:31.973 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:34.778 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:34.872 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:37.875 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:37.935 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:40.937 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:41.027 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:44.032 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:44.122 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:47.126 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:47.202 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:50.218 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:50.286 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:53.291 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:53.381 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:56.387 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:56.450 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:21:59.456 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:21:59.535 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:02.539 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:02.624 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:05.633 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:05.748 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:08.753 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:08.888 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:11.860 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:12.181 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:15.185 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:15.478 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:18.341 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:18.381 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:21.474 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:21.474 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:24.478 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:24.582 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:27.592 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:27.687 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:30.691 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:31.072 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:34.092 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:34.293 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:37.197 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:37.436 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:40.373 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:40.418 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:43.539 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:43.540 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:46.552 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:46.831 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:49.655 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:49.750 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:52.775 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:52.840 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:55.846 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:55.989 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:22:58.996 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:22:59.087 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:02.095 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:02.208 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:05.216 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:05.522 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:08.413 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:08.456 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:11.464 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:11.540 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:14.542 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:14.606 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:17.614 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:17.726 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:20.731 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:20.999 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:23.855 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:23.870 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:26.869 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:26.967 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:29.971 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:30.069 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:33.074 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:33.171 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:36.175 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:36.533 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:39.453 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:39.486 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:42.473 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:42.626 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:45.811 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:45.861 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:48.868 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:48.957 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:51.985 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:52.110 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:55.116 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:55.279 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:23:58.286 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:23:58.442 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:01.446 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:01.651 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:04.654 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:04.743 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:07.747 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:07.888 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:10.879 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:11.186 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:14.234 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:14.536 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:17.403 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:17.539 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:20.624 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:20.625 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:23.715 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:23.789 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:26.749 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:26.897 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:29.896 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:30.074 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:33.083 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:33.254 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:36.225 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:36.403 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:39.409 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:39.748 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:42.683 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:42.730 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:45.707 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:45.848 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:48.852 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:48.998 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:51.986 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:52.191 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:55.197 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:55.335 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:24:58.351 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:24:58.505 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:01.509 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:01.638 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:04.630 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:04.794 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:07.797 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:07.970 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:10.974 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:11.155 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:14.157 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:14.313 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:17.321 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:17.439 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:20.441 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:20.556 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:23.560 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:23.675 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:26.680 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:26.807 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:29.817 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:30.113 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:33.018 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:33.075 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:36.080 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:36.161 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:39.151 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:39.263 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:42.286 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:42.359 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:45.364 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:45.468 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:48.472 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:48.566 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:51.570 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:51.652 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:54.658 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:54.787 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:25:57.792 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:25:57.874 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:00.876 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:01.006 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:04.011 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:04.119 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:07.121 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:07.195 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:10.200 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:10.499 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:13.388 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:13.421 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:16.430 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:16.514 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:19.517 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:19.777 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:22.778 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:22.905 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:25.896 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:26.098 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:29.112 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:29.298 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:32.465 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:32.515 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:35.522 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:35.904 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:38.939 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:39.065 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:42.169 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:42.200 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:45.339 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:45.395 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:48.398 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:48.788 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:51.599 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:51.811 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:54.814 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:54.999 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:26:58.008 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:26:58.383 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:01.260 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:01.487 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:04.487 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:05.231 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:08.045 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:08.265 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:11.301 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:11.701 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:14.501 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:14.790 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:17.797 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:18.075 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:21.082 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:21.473 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:24.481 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:24.787 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:27.811 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:28.088 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:31.092 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:31.394 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:34.399 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:34.921 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:37.905 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:37.950 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:40.922 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:41.141 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:44.159 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:44.291 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:47.298 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:47.460 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:50.500 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:51.080 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:53.972 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:54.072 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:27:57.080 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:27:57.435 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:00.358 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:00.472 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:03.493 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:03.603 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:06.641 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:06.858 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:09.788 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:10.042 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:12.929 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:13.165 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:16.149 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:16.414 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:19.313 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:19.587 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:22.545 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:22.546 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:25.585 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:25.698 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:28.703 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:28.916 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:31.874 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:32.085 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:35.157 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:35.287 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:38.296 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:38.719 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:41.713 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:41.750 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:44.736 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:44.914 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:47.917 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:48.139 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:51.156 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:51.326 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:54.331 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:54.498 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:28:57.505 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:28:57.704 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:00.711 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:00.915 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:03.922 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:04.052 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:07.057 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:07.248 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:10.250 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:10.471 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:13.474 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:13.632 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:16.641 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:16.795 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:19.799 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:19.944 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:22.954 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:23.378 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:26.308 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:26.574 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:29.477 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:29.511 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:32.518 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:32.707 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:35.698 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:35.845 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:38.848 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:39.205 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:42.211 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:42.388 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:45.396 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:45.561 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:48.568 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:48.906 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:51.727 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:51.820 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:54.894 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:54.928 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:29:57.930 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:29:58.032 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:01.154 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:01.356 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:04.192 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:04.333 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:07.307 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:07.427 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:10.433 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:10.579 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:13.588 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:13.822 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:16.902 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:16.917 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:19.922 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:20.175 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:23.183 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:23.341 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:26.345 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:26.503 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:29.506 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:29.656 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:32.663 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:33.254 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:36.214 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:36.220 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:39.224 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:39.588 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:42.463 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:42.726 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:45.642 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:45.687 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:48.689 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:48.787 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:51.791 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:51.940 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:54.942 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:55.110 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:30:58.114 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:30:58.309 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:01.457 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:01.457 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:04.461 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:04.569 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:07.574 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:08.120 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:11.303 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:11.506 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:14.431 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:14.635 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:17.545 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:17.546 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:20.547 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:20.631 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:23.636 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:23.716 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:26.721 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:26.850 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:29.851 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:29.928 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:32.933 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:32.999 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:36.002 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:36.113 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:39.227 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:39.227 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:42.231 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:42.320 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:45.320 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:45.443 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:48.445 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:48.566 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:51.563 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:51.652 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:54.656 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:54.737 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:31:57.738 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:31:57.821 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:00.912 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:01.113 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:03.925 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:04.046 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:07.048 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:07.171 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:10.175 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:10.259 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:13.323 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:13.524 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:16.399 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:16.400 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:19.403 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:19.494 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:22.498 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:22.587 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:25.669 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:25.875 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:28.739 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:28.940 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:31.744 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:31.829 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:34.834 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:34.918 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:38.004 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:38.005 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:41.009 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:41.120 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:44.128 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:44.329 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:47.337 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:47.399 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:50.397 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:50.472 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:53.474 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:53.555 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:56.556 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:56.838 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:32:59.638 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:32:59.715 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:02.717 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:02.789 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:05.790 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:05.870 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:08.872 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:08.945 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:11.950 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:12.037 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:15.043 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:15.156 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:18.161 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:18.241 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:21.258 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:21.331 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:24.334 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:24.455 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:27.456 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:27.531 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:30.533 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:30.633 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:33.640 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:33.734 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:36.738 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:36.846 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:39.848 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:40.129 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:42.953 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:43.055 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:46.056 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:46.142 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:49.149 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:49.289 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:52.330 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:52.557 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:55.358 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:55.451 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:33:58.455 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:33:58.552 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:01.557 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:01.669 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:04.673 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:04.755 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:07.771 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:07.831 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:10.835 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:10.920 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:13.922 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:14.051 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:17.052 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:17.152 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:20.154 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:20.277 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:23.278 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:23.352 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:26.357 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:26.447 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:29.452 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:29.546 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:32.547 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:32.675 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:35.679 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:35.795 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:38.863 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:38.871 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:41.874 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:41.936 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:44.942 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:45.051 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:48.057 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:48.379 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:51.298 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:51.505 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:54.323 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:54.400 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:34:57.393 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:34:57.482 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:00.487 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:00.577 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:03.655 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:03.676 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:06.754 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:06.758 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:09.760 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:09.885 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:12.889 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:12.991 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:15.992 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:16.129 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:19.130 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:19.216 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:22.219 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:22.317 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:25.318 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:25.389 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:28.414 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:28.497 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:31.505 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:31.589 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:34.587 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:34.945 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:37.962 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:38.053 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:41.058 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:41.150 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:44.160 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:44.464 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:47.284 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:47.400 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:50.406 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:50.510 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:53.518 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:53.616 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:56.622 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:56.742 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:35:59.748 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:35:59.814 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:02.821 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:03.216 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:06.150 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:06.169 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:09.175 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:09.302 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:12.304 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:12.398 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:15.404 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:15.507 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:18.513 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:18.630 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:21.635 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:21.757 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:24.769 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:24.904 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:27.908 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:28.074 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:31.176 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:31.180 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:34.185 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:34.305 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:37.311 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:37.378 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:40.384 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:40.486 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:43.497 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:43.601 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:46.607 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:46.692 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:49.698 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:49.816 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:52.815 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:52.907 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:55.907 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:55.988 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:36:58.993 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:36:59.103 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:02.112 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:02.193 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:05.218 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:05.309 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:08.318 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:08.499 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:11.503 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:11.633 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:14.635 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:14.753 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:17.758 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:18.069 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:20.957 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:20.973 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:23.974 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:24.074 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:27.076 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:27.189 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:30.191 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:30.332 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:33.337 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:33.449 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:36.453 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:36.549 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:39.686 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:39.714 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:42.718 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:42.866 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:45.937 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:45.938 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:48.941 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:49.040 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:52.046 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:52.187 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:55.188 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:55.281 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:37:58.286 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:37:58.592 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:01.508 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:01.526 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:04.532 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:04.660 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:07.668 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:07.788 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:10.793 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:10.873 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:13.882 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:14.023 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:17.045 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:17.351 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:20.252 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:20.253 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:23.259 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:23.355 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:26.360 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:26.466 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:29.471 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:29.569 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:32.577 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:32.725 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:35.733 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:35.862 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:38.837 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:38.912 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:41.915 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:42.078 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:45.083 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:45.220 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:48.230 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:48.339 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:51.347 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:51.552 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:54.558 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:54.690 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:38:57.677 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:38:58.023 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:01.061 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:01.266 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:04.260 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:04.260 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:07.267 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:07.473 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:10.441 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:10.441 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:13.447 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:13.554 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:16.561 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:16.745 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:19.763 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:19.850 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:22.884 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:22.938 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:25.941 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:26.171 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:29.177 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:29.401 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:32.414 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:32.541 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:35.545 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:35.729 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:38.735 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:38.897 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:41.900 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:42.300 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:45.222 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:45.222 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:48.229 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:48.364 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:51.371 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:51.536 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:54.541 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:54.652 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:39:57.660 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:39:58.131 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:40:01.138 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:40:01.457 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:40:04.366 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:40:04.366 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:40:07.371 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:40:07.498 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:40:10.505 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:40:10.627 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:40:13.634 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:40:13.750 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:40:16.760 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:40:16.886 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:40:19.893 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:40:19.975 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:40:22.980 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:40:23.150 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:40:26.155 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:40:26.260 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:40:29.258 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:40:29.380 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:40:32.425 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:40:32.543 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:40:35.541 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:40:35.669 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:40:38.780 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:40:38.804 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:40:41.785 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:40:41.866 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:40:44.869 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:40:45.034 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 147 
[INFO ] 2025-02-18 11:40:48.036 - [任务 6][Sybase] - rebuild statement with 368324, 147 
[INFO ] 2025-02-18 11:40:48.142 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 11:40:48.143 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 11:40:48.226 - [任务 6][Sybase] - dump tran success 
[INFO ] 2025-02-18 11:40:48.226 - [任务 6][Sybase] - update logs hold success, startRid: 368324 
[INFO ] 2025-02-18 11:40:48.234 - [任务 6][Sybase] - update sybase offset startRid to: 368324, rowId to: 160, and dump logs 
[INFO ] 2025-02-18 11:40:48.235 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 160 
[INFO ] 2025-02-18 11:40:51.254 - [任务 6][Sybase] - rebuild statement with 368324, 160 
[INFO ] 2025-02-18 11:40:51.379 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 160 
[INFO ] 2025-02-18 11:40:54.380 - [任务 6][Sybase] - rebuild statement with 368324, 160 
[INFO ] 2025-02-18 11:40:54.511 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 160 
[INFO ] 2025-02-18 11:40:57.516 - [任务 6][Sybase] - rebuild statement with 368324, 160 
[INFO ] 2025-02-18 11:40:57.639 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 160 
[INFO ] 2025-02-18 11:41:00.644 - [任务 6][Sybase] - rebuild statement with 368324, 160 
[INFO ] 2025-02-18 11:41:00.784 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 160 
[INFO ] 2025-02-18 11:41:03.788 - [任务 6][Sybase] - rebuild statement with 368324, 160 
[INFO ] 2025-02-18 11:41:03.922 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 160 
[INFO ] 2025-02-18 11:41:06.917 - [任务 6][Sybase] - rebuild statement with 368324, 160 
[INFO ] 2025-02-18 11:41:07.053 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 160 
[INFO ] 2025-02-18 11:41:10.055 - [任务 6][Sybase] - rebuild statement with 368324, 160 
[INFO ] 2025-02-18 11:41:10.172 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 160 
[INFO ] 2025-02-18 11:41:13.179 - [任务 6][Sybase] - rebuild statement with 368324, 160 
[INFO ] 2025-02-18 11:41:13.304 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 160 
[INFO ] 2025-02-18 11:41:16.307 - [任务 6][Sybase] - rebuild statement with 368324, 160 
[INFO ] 2025-02-18 11:41:16.391 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368324, rowId: 160 
[INFO ] 2025-02-18 11:41:19.393 - [任务 6][Sybase] - rebuild statement with 368324, 160 
[INFO ] 2025-02-18 11:41:19.511 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 11:41:19.512 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 11:41:19.717 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:41:22.630 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:41:22.647 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:41:25.643 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:41:25.789 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:41:28.793 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:41:28.958 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:41:31.969 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:41:32.145 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:41:35.135 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:41:35.217 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:41:38.243 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:41:38.361 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:41:41.485 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:41:41.485 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:41:44.487 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:41:44.834 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:41:47.748 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:41:47.974 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:41:50.856 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:41:50.886 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:41:53.890 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:41:53.978 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:41:56.995 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:41:57.128 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:00.132 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:00.235 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:03.241 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:03.350 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:06.354 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:06.690 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:09.696 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:09.784 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:12.787 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:12.944 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:15.949 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:16.075 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:19.080 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:19.186 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:22.192 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:22.348 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:25.352 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:25.465 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:28.467 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:28.547 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:31.548 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:31.616 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:34.620 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:34.689 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:37.695 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:37.817 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:40.820 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:40.922 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:43.924 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:43.989 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:47.033 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:47.171 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:50.175 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:50.308 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:53.316 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:53.445 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:56.455 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:56.572 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:42:59.575 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:42:59.690 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:02.699 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:02.865 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:05.868 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:06.000 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:09.004 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:09.116 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:12.123 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:12.310 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:15.313 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:15.419 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:18.427 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:18.994 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:21.891 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:21.967 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:24.972 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:25.081 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:28.087 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:28.264 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:31.205 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:31.346 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:34.348 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:34.454 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:37.450 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:37.569 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:40.579 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:40.707 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:43.715 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:43.871 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:46.875 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:47.026 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:50.032 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:50.160 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:53.173 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:53.345 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:56.352 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:56.507 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:43:59.513 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:43:59.822 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:44:02.720 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:44:02.734 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:44:05.733 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:44:05.842 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:44:08.852 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:44:09.201 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:44:12.006 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:44:12.131 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:44:15.179 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:44:15.211 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:44:18.214 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:44:18.332 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:44:21.336 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:44:21.442 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:44:24.454 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:44:24.571 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:44:27.581 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:44:27.681 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 1 
[INFO ] 2025-02-18 11:44:30.690 - [任务 6][Sybase] - rebuild statement with 368325, 1 
[INFO ] 2025-02-18 11:44:30.823 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 11:44:30.823 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 11:44:30.832 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 14 
[INFO ] 2025-02-18 11:44:33.837 - [任务 6][Sybase] - rebuild statement with 368325, 14 
[INFO ] 2025-02-18 11:44:33.926 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 14 
[INFO ] 2025-02-18 11:44:36.943 - [任务 6][Sybase] - rebuild statement with 368325, 14 
[INFO ] 2025-02-18 11:44:37.305 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 14 
[INFO ] 2025-02-18 11:44:40.144 - [任务 6][Sybase] - rebuild statement with 368325, 14 
[INFO ] 2025-02-18 11:44:40.235 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 14 
[INFO ] 2025-02-18 11:44:43.240 - [任务 6][Sybase] - rebuild statement with 368325, 14 
[INFO ] 2025-02-18 11:44:43.334 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 14 
[INFO ] 2025-02-18 11:44:46.345 - [任务 6][Sybase] - rebuild statement with 368325, 14 
[INFO ] 2025-02-18 11:44:46.453 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 14 
[INFO ] 2025-02-18 11:44:49.455 - [任务 6][Sybase] - rebuild statement with 368325, 14 
[INFO ] 2025-02-18 11:44:49.585 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 14 
[INFO ] 2025-02-18 11:44:52.589 - [任务 6][Sybase] - rebuild statement with 368325, 14 
[INFO ] 2025-02-18 11:44:52.737 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 14 
[INFO ] 2025-02-18 11:44:55.730 - [任务 6][Sybase] - rebuild statement with 368325, 14 
[INFO ] 2025-02-18 11:44:55.833 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 14 
[INFO ] 2025-02-18 11:44:58.838 - [任务 6][Sybase] - rebuild statement with 368325, 14 
[INFO ] 2025-02-18 11:44:58.953 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 14 
[INFO ] 2025-02-18 11:45:01.953 - [任务 6][Sybase] - rebuild statement with 368325, 14 
[INFO ] 2025-02-18 11:45:02.088 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 14 
[INFO ] 2025-02-18 11:45:05.092 - [任务 6][Sybase] - rebuild statement with 368325, 14 
[INFO ] 2025-02-18 11:45:05.221 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 14 
[INFO ] 2025-02-18 11:45:08.230 - [任务 6][Sybase] - rebuild statement with 368325, 14 
[INFO ] 2025-02-18 11:45:08.330 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 11:45:08.331 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 11:45:08.444 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:45:11.470 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:45:11.482 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:45:14.477 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:45:14.650 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:45:17.759 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:45:17.759 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:45:20.761 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:45:20.848 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:45:23.865 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:45:24.001 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:45:27.013 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:45:27.250 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:45:30.255 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:45:30.342 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:45:33.348 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:45:33.378 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:45:36.383 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:45:36.500 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:45:39.503 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:45:39.629 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:45:42.633 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:45:42.782 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:45:45.787 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:45:45.916 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:45:48.920 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:45:49.099 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:45:52.104 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:45:52.263 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:45:55.267 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:45:55.374 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:45:58.375 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:45:58.709 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:01.609 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:01.650 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:04.654 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:05.039 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:08.044 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:08.176 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:11.181 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:11.326 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:14.332 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:14.467 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:17.468 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:17.647 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:20.634 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:20.762 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:23.765 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:23.878 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:26.881 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:26.991 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:29.993 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:30.582 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:33.474 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:33.675 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:36.497 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:36.599 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:39.601 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:39.806 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:42.689 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:42.893 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:45.843 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:45.843 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:48.846 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:49.052 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:51.926 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:52.129 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:55.032 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:55.238 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:46:58.150 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:46:58.356 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:47:01.223 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:47:01.480 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:47:04.349 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:47:04.592 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:47:07.498 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:47:07.704 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:47:10.598 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:47:10.644 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:47:13.648 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:47:13.901 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:47:16.977 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:47:17.237 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:47:20.052 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:47:20.171 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:47:23.265 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:47:23.268 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 17 
[INFO ] 2025-02-18 11:47:26.294 - [任务 6][Sybase] - rebuild statement with 368325, 17 
[INFO ] 2025-02-18 11:47:26.386 - [任务 6][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 11:47:26.386 - [任务 6][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 11:47:26.407 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:47:29.411 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:47:29.535 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:47:32.543 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:47:32.724 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:47:35.732 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:47:35.924 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:47:38.930 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:47:39.055 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:47:42.059 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:47:42.192 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:47:45.195 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:47:45.295 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:47:48.302 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:47:48.408 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:47:51.423 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:47:51.523 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:47:54.538 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:47:54.671 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:47:57.681 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:47:57.994 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:00.998 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:01.150 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:04.155 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:04.249 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:07.258 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:07.357 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:10.453 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:10.453 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:13.459 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:13.575 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:16.585 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:16.703 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:19.723 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:19.799 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:22.805 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:22.945 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:25.948 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:26.066 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:29.073 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:29.390 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:32.280 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:32.481 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:35.368 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:35.574 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:38.478 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:38.518 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:41.524 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:41.736 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:44.742 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:44.866 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:47.873 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:47.987 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:50.989 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:51.162 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:54.171 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:54.322 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:48:57.309 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:48:57.472 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:00.565 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:00.607 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:03.678 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:03.744 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:06.750 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:07.050 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:09.936 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:09.960 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:12.993 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:13.098 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:16.092 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:16.219 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:19.223 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:19.319 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:22.328 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:22.453 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:25.449 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:25.616 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:28.712 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:28.719 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:31.732 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:31.882 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:34.880 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:34.999 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:38.003 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:38.111 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:41.117 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:41.229 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:44.283 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:44.368 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:47.375 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:47.730 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:50.599 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:50.640 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:53.641 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:54.001 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:56.892 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:56.916 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:49:59.920 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:49:59.991 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:03.025 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:03.173 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:06.175 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:06.255 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:09.280 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:09.400 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:12.406 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:12.546 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:15.597 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:15.676 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:18.685 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:18.830 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:21.836 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:21.915 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:24.920 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:25.046 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:28.058 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:28.172 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:31.177 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:31.339 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:34.345 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:34.432 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:37.440 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:37.517 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:40.519 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:40.628 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:43.634 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:43.772 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:46.776 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:46.870 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:49.877 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:50.039 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:53.045 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:53.181 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:56.187 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:56.272 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:50:59.276 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:50:59.355 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:02.369 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:03.042 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:06.047 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:06.213 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:09.219 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:09.616 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:12.512 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:12.719 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:15.623 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:15.722 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:18.735 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:18.991 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:21.851 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:22.103 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:24.990 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:25.055 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:28.057 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:28.595 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:31.574 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:31.710 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:34.699 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:34.791 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:37.798 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:37.946 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:40.913 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:41.061 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:44.025 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:44.196 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:47.279 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:47.315 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:50.315 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:50.386 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:53.393 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:53.495 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:56.514 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:56.618 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:51:59.625 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:51:59.779 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:02.815 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:02.906 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:05.919 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:06.035 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:09.038 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:09.167 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:12.247 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:12.292 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:15.295 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:15.417 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:18.426 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:18.546 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:21.553 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:21.680 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:24.689 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:24.819 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:27.822 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:27.913 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:30.961 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:31.061 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:34.066 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:34.163 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:37.198 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:37.278 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:40.282 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:40.407 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:43.412 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:43.504 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:46.549 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:46.585 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:49.591 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:49.737 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:52.814 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:52.820 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:55.827 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:55.994 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:52:59.000 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:52:59.162 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:02.137 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:02.281 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:05.280 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:05.379 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:08.390 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:08.471 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:11.489 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:11.602 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:14.601 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:14.729 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:17.720 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:17.874 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:20.881 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:20.977 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:23.983 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:24.219 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:27.174 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:27.336 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:30.341 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:30.485 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:33.499 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:33.589 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:36.600 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:36.922 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:39.832 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:39.857 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:42.957 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:42.960 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:45.964 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:46.125 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:49.134 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:49.267 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:52.276 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:52.611 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:55.540 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:55.541 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:53:58.572 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:53:58.650 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:01.660 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:01.778 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:04.782 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:04.882 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:07.901 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:07.999 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:11.004 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:11.084 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:14.093 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:14.218 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:17.224 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:17.530 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:20.539 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:20.669 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:23.708 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:24.050 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:27.051 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:27.313 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:30.347 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:30.574 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:33.576 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:33.721 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:36.725 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:36.905 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:39.914 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:40.106 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:43.112 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:43.216 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:46.222 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:46.355 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:49.371 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:49.511 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:52.515 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:52.671 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:55.678 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:55.787 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:54:58.762 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:54:58.889 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:02.011 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:02.016 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:05.010 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:05.186 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:08.192 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:08.299 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:11.300 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:11.408 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:14.417 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:14.579 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:17.589 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:17.982 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:20.922 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:21.135 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:24.112 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:24.247 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:27.265 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:27.391 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:30.396 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:30.504 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:33.508 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:33.610 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:36.634 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:36.747 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:39.745 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:39.837 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:42.846 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:43.158 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:45.970 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:46.097 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:49.101 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:49.236 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:52.247 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:52.304 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:55.399 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:55.402 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:55:58.408 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:55:58.533 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:01.568 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:01.660 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:04.665 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:04.800 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:07.803 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:07.914 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:10.921 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:11.069 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:14.082 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:14.190 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:17.192 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:17.292 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:20.297 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:20.456 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:23.461 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:23.557 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:26.568 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:26.697 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:29.775 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:29.776 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:32.785 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:32.919 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:35.922 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:36.186 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:39.190 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:39.294 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:42.433 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:42.433 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:45.440 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:45.547 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:48.555 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:48.685 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:51.693 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:51.784 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:54.788 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:54.886 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:56:57.888 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:56:58.022 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:01.051 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:01.142 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:04.170 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:04.292 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:07.295 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:07.655 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:10.556 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:10.585 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:13.591 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:13.708 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:16.808 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:16.809 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:19.956 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:19.958 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:22.965 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:23.078 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:26.087 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:26.195 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:29.199 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:29.324 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:32.336 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:32.684 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:35.594 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:35.621 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:38.625 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:38.769 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:41.772 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:41.881 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:44.884 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:44.996 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:48.017 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:48.198 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:51.363 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:51.788 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:54.793 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:55.092 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:57:58.128 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:57:58.584 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:58:01.579 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:58:03.100 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:58:06.143 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:58:06.349 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:58:09.368 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:58:09.572 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:58:12.444 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:58:12.934 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:58:15.937 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:58:16.397 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:58:19.599 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:58:20.469 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:58:23.477 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:58:23.768 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:58:26.857 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:58:27.208 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:58:30.052 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:58:30.460 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:58:33.548 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:58:33.897 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:58:36.903 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:58:37.521 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:58:40.346 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:58:41.507 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:58:44.512 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:58:45.260 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:58:48.149 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:58:48.291 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:58:51.440 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:58:51.730 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:58:54.736 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:58:55.154 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:58:58.162 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:58:58.879 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:01.949 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:02.357 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:05.202 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:06.189 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:09.200 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:09.649 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:12.658 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:13.207 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:16.361 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:17.204 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:20.205 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:20.368 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:23.374 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:23.490 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:26.613 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:26.613 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:29.621 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:29.869 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:32.999 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:32.999 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:36.013 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:36.155 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:39.163 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:39.302 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:42.308 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:42.469 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:45.475 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:45.599 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:48.622 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:48.816 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:51.823 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:52.039 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:55.040 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:56.292 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 11:59:59.123 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 11:59:59.476 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:02.298 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:02.440 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:05.442 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:05.614 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:08.620 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:08.772 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:11.777 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:11.922 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:15.109 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:15.109 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:18.117 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:18.250 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:21.257 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:21.394 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:24.384 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:24.547 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:27.552 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:27.965 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:30.966 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:31.312 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:34.260 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:34.787 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:37.836 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:38.299 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:41.482 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:41.965 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:44.937 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:45.275 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:48.409 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:48.560 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:51.470 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:51.778 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:54.782 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:54.901 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:00:57.971 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:00:57.971 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:00.980 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:01.691 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:04.615 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:04.616 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:07.622 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:08.077 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:11.086 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:11.116 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:14.122 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:14.671 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:17.679 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:18.224 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:21.385 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:21.482 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:24.486 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:25.002 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:28.011 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:28.258 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:31.265 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:31.680 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:34.809 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:34.809 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:37.828 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:38.310 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:41.250 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:41.817 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:44.824 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:45.079 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:48.080 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:48.235 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:51.238 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:51.853 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:54.713 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:55.526 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:01:58.707 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:01:59.565 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:02:02.716 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:02:02.719 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:02:05.788 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:02:07.023 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:02:09.863 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:02:10.461 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:02:13.474 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:02:13.681 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:02:16.489 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:02:16.639 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:02:19.641 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:02:20.178 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:02:23.112 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:02:23.537 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:02:26.543 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:02:27.029 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:02:29.982 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:02:31.137 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:02:34.138 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:02:34.932 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:02:38.013 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:02:39.239 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:02:42.358 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:02:42.358 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:02:45.364 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:02:45.773 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:02:48.783 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:02:49.189 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:02:52.168 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:02:52.576 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:02:55.663 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:02:56.023 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:02:59.124 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:02:59.326 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:02.306 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:02.512 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:05.477 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:05.682 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:08.500 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:08.669 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:11.679 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:11.805 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:14.810 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:14.897 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:17.903 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:18.556 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:21.564 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:21.748 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:24.926 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:24.926 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:27.934 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:28.343 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:31.149 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:31.322 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:34.333 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:34.517 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:37.523 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:37.705 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:40.703 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:41.001 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:44.156 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:44.432 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:47.574 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:47.802 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:50.747 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:50.928 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:53.934 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:54.086 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:03:57.117 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:03:57.311 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:00.318 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:00.461 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:03.466 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:03.654 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:06.654 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:06.768 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:09.791 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:10.174 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:13.182 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:13.552 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:16.470 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:16.557 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:19.526 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:19.633 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:22.641 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:22.770 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:25.776 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:25.910 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:28.938 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:29.057 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:32.121 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:32.281 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:35.286 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:35.486 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:38.602 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:38.685 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:41.693 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:42.643 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:45.690 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:45.993 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:48.996 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:49.402 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:52.442 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:52.548 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:55.553 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:56.167 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:04:59.076 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:04:59.230 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:05:02.237 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:05:02.421 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:05:05.428 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:05:05.633 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:05:08.722 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:05:09.261 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:05:12.267 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:05:12.675 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:05:15.643 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:05:15.643 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:05:18.836 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:05:20.766 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:05:23.872 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:05:24.570 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:05:27.657 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:05:28.743 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:05:31.948 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:05:32.042 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:05:34.980 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:05:35.695 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:05:38.497 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:05:38.970 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:05:42.035 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:05:43.016 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:05:45.952 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:05:46.280 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:05:49.177 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:05:49.511 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:05:52.620 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:05:53.119 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:05:56.131 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:05:56.751 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:05:59.754 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:05:59.950 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:06:02.955 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:06:03.492 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:06:06.570 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:06:07.598 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:06:10.611 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:06:11.216 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:06:14.252 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:06:14.822 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:06:17.828 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:06:18.007 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:06:21.196 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:06:21.424 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:06:24.434 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:06:25.121 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:06:28.278 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:06:28.563 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:06:31.578 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:06:31.981 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:06:34.798 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:06:35.784 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:06:38.790 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:06:39.573 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:06:42.580 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:06:42.779 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:06:45.832 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:06:46.037 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:06:49.082 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:06:49.143 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:06:52.342 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:06:52.397 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:06:55.519 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:06:55.519 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:06:58.524 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:06:58.930 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:01.775 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:07:01.976 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:05.063 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:07:05.063 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:08.068 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:07:08.678 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:11.496 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:07:11.564 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:14.571 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:07:14.695 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:17.700 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:07:17.905 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:20.843 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:07:22.254 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:25.304 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:07:25.710 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:28.686 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:07:28.686 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:31.882 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:07:32.126 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:35.127 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:07:36.148 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:39.142 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:07:39.142 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:42.148 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:07:42.963 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:45.939 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:07:46.154 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:49.159 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:07:49.788 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:52.916 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:07:53.134 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:56.126 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:07:56.620 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:07:59.624 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:08:00.234 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:08:03.111 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:08:04.691 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:08:07.849 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:08:08.079 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:08:11.095 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:08:11.498 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:08:14.494 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:08:14.798 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:08:17.873 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:08:19.452 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:08:22.293 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:08:22.579 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:08:25.561 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:08:26.283 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:08:29.292 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:08:29.785 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:08:32.788 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:08:34.120 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:08:37.238 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:08:38.180 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:08:41.182 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:08:41.556 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:08:44.562 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:08:44.972 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:08:47.822 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:08:48.223 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:08:51.101 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:08:51.725 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:08:54.564 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:08:54.956 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:08:57.959 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:08:58.142 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:09:01.147 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:09:02.155 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:09:05.209 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:09:05.379 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:09:08.383 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:09:08.623 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:09:11.825 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:09:11.952 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:09:14.953 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:09:15.493 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:09:18.354 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:09:18.930 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:09:21.850 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:09:23.288 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:09:26.243 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:09:26.648 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:09:29.591 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:09:30.077 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:09:33.137 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:09:33.952 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:09:36.893 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:09:37.653 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:09:40.472 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:09:40.880 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:09:43.745 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:09:44.862 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:09:47.729 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:09:48.543 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:09:51.498 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:09:53.069 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:09:56.075 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:09:58.326 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:10:01.335 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:10:02.103 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:10:05.059 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:10:05.795 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:10:08.934 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:10:09.723 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:10:12.857 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:10:13.535 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:10:16.542 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:10:17.316 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:10:20.319 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:10:20.953 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:10:23.956 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:10:24.576 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:10:27.424 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:10:28.331 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:10:31.442 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:10:32.011 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:10:35.198 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:10:35.313 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:10:38.319 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:10:38.985 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:10:42.137 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:10:42.345 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:10:45.143 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:10:46.162 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:10:49.125 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:10:49.286 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:10:52.295 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:10:52.706 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:10:55.537 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:10:55.682 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:10:58.687 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:10:58.832 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:02.003 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:02.013 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:05.017 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:05.677 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:08.779 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:09.101 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:12.050 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:12.433 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:15.439 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:15.934 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:18.942 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:19.282 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:22.254 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:23.202 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:26.202 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:26.770 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:29.777 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:30.255 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:33.382 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:33.583 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:36.439 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:36.537 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:39.548 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:39.752 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:42.774 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:42.775 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:45.931 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:45.932 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:49.066 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:49.066 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:52.189 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:52.191 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:55.196 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:55.398 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:11:58.368 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:11:58.369 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:01.478 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:01.479 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:04.546 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:04.752 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:07.617 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:07.731 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:10.800 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:11.011 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:13.903 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:14.109 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:17.022 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:17.024 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:20.079 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:20.282 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:23.182 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:23.388 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:26.287 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:26.494 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:29.473 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:29.679 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:32.595 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:32.595 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:35.683 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:35.684 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:38.687 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:38.821 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:41.794 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:41.908 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:44.914 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:44.984 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:47.992 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:48.128 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:51.136 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:51.242 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:54.249 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:54.570 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:12:57.487 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:12:57.510 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:00.498 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:00.656 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:03.664 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:03.983 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:06.790 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:07.065 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:09.973 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:10.002 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:13.015 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:13.144 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:16.148 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:16.231 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:19.239 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:19.365 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:22.371 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:22.466 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:25.492 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:25.639 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:28.645 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:28.849 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:31.854 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:32.034 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:35.036 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:35.536 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:38.546 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:39.054 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:42.234 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:43.228 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:46.285 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:46.380 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:49.518 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:49.518 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:52.633 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:52.686 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:55.662 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:55.797 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:13:58.888 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:13:58.958 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:01.962 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:02.059 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:05.043 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:05.138 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:08.144 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:08.299 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:11.376 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:11.442 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:14.450 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:14.530 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:17.556 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:17.658 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:20.665 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:20.804 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:23.818 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:23.941 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:26.947 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:27.053 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:30.054 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:30.211 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:33.220 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:33.853 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:36.775 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:36.775 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:39.780 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:40.063 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:42.956 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:42.957 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:45.963 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:46.293 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:49.116 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:49.251 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:52.252 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:52.383 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:55.388 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:55.508 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:14:58.599 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:14:58.807 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:15:01.740 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:15:01.947 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:15:04.793 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:15:04.938 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:15:07.944 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:15:08.133 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:15:11.230 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:15:11.230 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:15:14.238 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:15:14.368 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:15:17.373 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:15:17.455 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:15:20.436 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:15:20.594 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:15:23.710 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:15:23.711 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:15:26.820 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:15:26.930 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:15:29.841 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:15:29.947 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:15:32.958 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:15:33.149 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:15:36.098 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:15:36.212 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:15:39.246 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:15:39.353 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:15:42.360 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:15:42.483 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[INFO ] 2025-02-18 12:15:45.490 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[INFO ] 2025-02-18 12:15:45.587 - [任务 6][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368325, rowId: 24 
[TRACE] 2025-02-18 12:15:48.390 - [任务 6][Sybase] - Node Sybase[cff4cb90-29a4-4414-aaa6-5a3c8acf894e] running status set to false 
[INFO ] 2025-02-18 12:15:48.398 - [任务 6][Sybase] - Log Miner is shutting down... 
[INFO ] 2025-02-18 12:15:48.398 - [任务 6][Sybase] - rebuild statement with 368325, 24 
[TRACE] 2025-02-18 12:15:49.629 - [任务 6][Sybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_cff4cb90-29a4-4414-aaa6-5a3c8acf894e_1739845123968 
[TRACE] 2025-02-18 12:15:49.630 - [任务 6][Sybase] - PDK connector node released: HazelcastSourcePdkDataNode_cff4cb90-29a4-4414-aaa6-5a3c8acf894e_1739845123968 
[TRACE] 2025-02-18 12:15:49.630 - [任务 6][Sybase] - Node Sybase[cff4cb90-29a4-4414-aaa6-5a3c8acf894e] schema data cleaned 
[TRACE] 2025-02-18 12:15:49.636 - [任务 6][Sybase] - Node Sybase[cff4cb90-29a4-4414-aaa6-5a3c8acf894e] monitor closed 
[TRACE] 2025-02-18 12:15:49.636 - [任务 6][Sybase] - Node Sybase[cff4cb90-29a4-4414-aaa6-5a3c8acf894e] close complete, cost 1269 ms 
[TRACE] 2025-02-18 12:15:49.637 - [任务 6][表编辑] - Node 表编辑[58436480-1965-47dc-b688-0a58a9342228] running status set to false 
[TRACE] 2025-02-18 12:15:49.637 - [任务 6][表编辑] - Node 表编辑[58436480-1965-47dc-b688-0a58a9342228] schema data cleaned 
[TRACE] 2025-02-18 12:15:49.637 - [任务 6][表编辑] - Node 表编辑[58436480-1965-47dc-b688-0a58a9342228] monitor closed 
[TRACE] 2025-02-18 12:15:49.637 - [任务 6][表编辑] - Node 表编辑[58436480-1965-47dc-b688-0a58a9342228] close complete, cost 1 ms 
[TRACE] 2025-02-18 12:15:49.637 - [任务 6][PG] - Node PG[bf3cc9a8-b543-4d4f-9c60-72c21f7386fa] running status set to false 
[TRACE] 2025-02-18 12:15:49.655 - [任务 6][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode_bf3cc9a8-b543-4d4f-9c60-72c21f7386fa_1739845124087 
[TRACE] 2025-02-18 12:15:49.655 - [任务 6][PG] - PDK connector node released: HazelcastTargetPdkDataNode_bf3cc9a8-b543-4d4f-9c60-72c21f7386fa_1739845124087 
[TRACE] 2025-02-18 12:15:49.655 - [任务 6][PG] - Node PG[bf3cc9a8-b543-4d4f-9c60-72c21f7386fa] schema data cleaned 
[TRACE] 2025-02-18 12:15:49.655 - [任务 6][PG] - Node PG[bf3cc9a8-b543-4d4f-9c60-72c21f7386fa] monitor closed 
[TRACE] 2025-02-18 12:15:49.857 - [任务 6][PG] - Node PG[bf3cc9a8-b543-4d4f-9c60-72c21f7386fa] close complete, cost 18 ms 
[TRACE] 2025-02-18 12:15:50.559 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-18 12:15:50.559 - [任务 6] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@27d1bd03 
[TRACE] 2025-02-18 12:15:50.688 - [任务 6] - Stop task milestones: 67b3ede3cc3e3c6b77435d9e(任务 6)  
[TRACE] 2025-02-18 12:15:50.689 - [任务 6] - Stopped task aspect(s) 
[TRACE] 2025-02-18 12:15:50.689 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2025-02-18 12:15:50.689 - [任务 6] - Task stopped. 
[TRACE] 2025-02-18 12:15:50.722 - [任务 6] - Remove memory task client succeed, task: 任务 6[67b3ede3cc3e3c6b77435d9e] 
[TRACE] 2025-02-18 12:15:50.722 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[67b3ede3cc3e3c6b77435d9e] 
