[INFO ] 2024-06-24 14:57:08.930 - [任务 19] - Task initialization... 
[INFO ] 2024-06-24 14:57:09.135 - [任务 19] - Start task milestones: 66791888e2fb9a28343e59f9(任务 19) 
[INFO ] 2024-06-24 14:57:09.155 - [任务 19] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-06-24 14:57:09.216 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-24 14:57:09.279 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] start preload schema,table counts: 1 
[INFO ] 2024-06-24 14:57:09.279 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] start preload schema,table counts: 1 
[INFO ] 2024-06-24 14:57:09.280 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] start preload schema,table counts: 1 
[INFO ] 2024-06-24 14:57:09.283 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] preload schema finished, cost 1 ms 
[INFO ] 2024-06-24 14:57:09.283 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 14:57:09.288 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 14:57:09.904 - [任务 19][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-06-24 14:57:09.904 - [任务 19][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-06-24 14:57:09.908 - [任务 19][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-24 14:57:09.908 - [任务 19][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000031","position":1058090632,"gtidSet":""} 
[INFO ] 2024-06-24 14:57:10.034 - [任务 19][smallCaseTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-24 14:57:10.035 - [任务 19][CLAIM] - Initial sync started 
[INFO ] 2024-06-24 14:57:10.035 - [任务 19][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-24 14:57:10.043 - [任务 19][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-24 14:57:10.272 - [任务 19][CLAIM] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-24 14:57:10.298 - [任务 19][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-24 14:57:10.299 - [任务 19][CLAIM] - Initial sync completed 
[INFO ] 2024-06-24 14:57:10.300 - [任务 19][CLAIM] - Incremental sync starting... 
[INFO ] 2024-06-24 14:57:10.300 - [任务 19][CLAIM] - Initial sync completed 
[INFO ] 2024-06-24 14:57:10.301 - [任务 19][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000031","position":1058090632,"gtidSet":""} 
[INFO ] 2024-06-24 14:57:10.370 - [任务 19][CLAIM] - Starting mysql cdc, server name: b21acb6c-ee80-4543-8098-9fd4b4aebd59 
[INFO ] 2024-06-24 14:57:10.371 - [任务 19][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2103899864
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.hostname: localhost
  database.password: ********
  name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  pdk.offset.string: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-24 14:57:10.577 - [任务 19][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-24 15:09:47.573 - [任务 19][CLAIM] - Mysql binlog reader stopped 
[WARN ] 2024-06-24 15:09:47.584 - [任务 19][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Unknown exception occur when operate table: unknown
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-24 15:10:47.691 - [任务 19][CLAIM] - Starting mysql cdc, server name: b21acb6c-ee80-4543-8098-9fd4b4aebd59 
[INFO ] 2024-06-24 15:10:47.902 - [任务 19][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 32679377
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.hostname: localhost
  database.password: ********
  name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  pdk.offset.string: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"ts_sec\":1719212230,\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-24 15:10:47.903 - [任务 19][CLAIM] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-06-24 15:10:47.938 - [任务 19][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-24 16:35:33.149 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] running status set to false 
[INFO ] 2024-06-24 16:35:33.608 - [任务 19] - Task initialization... 
[INFO ] 2024-06-24 16:35:33.608 - [任务 19] - Start task milestones: 66791888e2fb9a28343e59f9(任务 19) 
[INFO ] 2024-06-24 16:35:34.624 - [任务 19] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-24 16:35:34.728 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-24 16:35:35.412 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] start preload schema,table counts: 1 
[INFO ] 2024-06-24 16:35:35.421 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 16:35:35.429 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] start preload schema,table counts: 1 
[INFO ] 2024-06-24 16:35:35.444 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] start preload schema,table counts: 1 
[INFO ] 2024-06-24 16:35:35.445 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 16:35:35.445 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 16:35:35.847 - [任务 19][smallCaseTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-24 16:35:36.289 - [任务 19][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-06-24 16:35:36.289 - [任务 19][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-06-24 16:35:36.294 - [任务 19][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-24 16:35:36.362 - [任务 19][CLAIM] - batch offset found: {"CLAIM":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 16:35:36.363 - [任务 19][CLAIM] - Incremental sync starting... 
[INFO ] 2024-06-24 16:35:36.363 - [任务 19][CLAIM] - Initial sync completed 
[INFO ] 2024-06-24 16:35:36.363 - [任务 19][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 16:35:36.392 - [任务 19][CLAIM] - Starting mysql cdc, server name: b21acb6c-ee80-4543-8098-9fd4b4aebd59 
[INFO ] 2024-06-24 16:35:36.393 - [任务 19][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2138896239
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.hostname: localhost
  database.password: ********
  name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  pdk.offset.string: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-24 16:35:37.203 - [任务 19][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-24 16:58:06.807 - [任务 19] - Task initialization... 
[INFO ] 2024-06-24 16:58:06.893 - [任务 19] - Start task milestones: 66791888e2fb9a28343e59f9(任务 19) 
[INFO ] 2024-06-24 16:58:08.083 - [任务 19] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-24 16:58:08.156 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-24 16:58:08.954 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] start preload schema,table counts: 1 
[INFO ] 2024-06-24 16:58:08.968 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] start preload schema,table counts: 1 
[INFO ] 2024-06-24 16:58:08.969 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] start preload schema,table counts: 1 
[INFO ] 2024-06-24 16:58:09.002 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 16:58:09.002 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 16:58:09.023 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 16:58:09.533 - [任务 19][smallCaseTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-24 16:58:09.763 - [任务 19][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-06-24 16:58:09.768 - [任务 19][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-06-24 16:58:09.768 - [任务 19][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-24 16:58:09.784 - [任务 19][CLAIM] - batch offset found: {},stream offset found: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 16:58:09.863 - [任务 19][CLAIM] - Incremental sync starting... 
[INFO ] 2024-06-24 16:58:09.863 - [任务 19][CLAIM] - Initial sync completed 
[INFO ] 2024-06-24 16:58:09.870 - [任务 19][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 16:58:09.917 - [任务 19][CLAIM] - Starting mysql cdc, server name: b21acb6c-ee80-4543-8098-9fd4b4aebd59 
[INFO ] 2024-06-24 16:58:09.917 - [任务 19][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1747231444
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.hostname: localhost
  database.password: ********
  name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  pdk.offset.string: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-24 16:58:11.052 - [任务 19][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-24 17:06:45.785 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] running status set to false 
[INFO ] 2024-06-24 17:06:46.420 - [任务 19] - Task initialization... 
[INFO ] 2024-06-24 17:06:46.422 - [任务 19] - Start task milestones: 66791888e2fb9a28343e59f9(任务 19) 
[INFO ] 2024-06-24 17:06:47.185 - [任务 19] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-24 17:06:47.322 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-24 17:06:47.975 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:06:47.986 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:06:47.996 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:06:48.001 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 17:06:48.001 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 17:06:48.002 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 17:06:48.456 - [任务 19][smallCaseTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-24 17:06:48.644 - [任务 19][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-06-24 17:06:48.646 - [任务 19][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-06-24 17:06:48.647 - [任务 19][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-24 17:06:48.653 - [任务 19][CLAIM] - batch offset found: {},stream offset found: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 17:06:48.715 - [任务 19][CLAIM] - Incremental sync starting... 
[INFO ] 2024-06-24 17:06:48.715 - [任务 19][CLAIM] - Initial sync completed 
[INFO ] 2024-06-24 17:06:48.750 - [任务 19][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 17:06:48.753 - [任务 19][CLAIM] - Starting mysql cdc, server name: b21acb6c-ee80-4543-8098-9fd4b4aebd59 
[INFO ] 2024-06-24 17:06:48.963 - [任务 19][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1698301504
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.hostname: localhost
  database.password: ********
  name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  pdk.offset.string: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-24 17:06:49.716 - [任务 19][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-24 17:13:13.899 - [任务 19] - Task initialization... 
[INFO ] 2024-06-24 17:13:13.902 - [任务 19] - Start task milestones: 66791888e2fb9a28343e59f9(任务 19) 
[INFO ] 2024-06-24 17:13:15.045 - [任务 19] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-24 17:13:15.113 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-24 17:13:15.671 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:13:15.674 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:13:15.674 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:13:15.675 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] preload schema finished, cost 1 ms 
[INFO ] 2024-06-24 17:13:15.675 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 17:13:15.676 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 17:13:16.020 - [任务 19][smallCaseTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-24 17:13:16.186 - [任务 19][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-06-24 17:13:16.187 - [任务 19][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-06-24 17:13:16.188 - [任务 19][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-24 17:13:16.289 - [任务 19][CLAIM] - batch offset found: {},stream offset found: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 17:13:16.289 - [任务 19][CLAIM] - Incremental sync starting... 
[INFO ] 2024-06-24 17:13:16.289 - [任务 19][CLAIM] - Initial sync completed 
[INFO ] 2024-06-24 17:13:16.291 - [任务 19][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 17:13:16.327 - [任务 19][CLAIM] - Starting mysql cdc, server name: b21acb6c-ee80-4543-8098-9fd4b4aebd59 
[INFO ] 2024-06-24 17:13:16.328 - [任务 19][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 704645719
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.hostname: localhost
  database.password: ********
  name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  pdk.offset.string: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-24 17:13:17.188 - [任务 19][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-24 17:20:50.354 - [任务 19] - Task initialization... 
[INFO ] 2024-06-24 17:20:50.359 - [任务 19] - Start task milestones: 66791888e2fb9a28343e59f9(任务 19) 
[INFO ] 2024-06-24 17:20:50.943 - [任务 19] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-24 17:20:51.010 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-24 17:20:51.668 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:20:51.668 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:20:51.669 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:20:51.669 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] preload schema finished, cost 4 ms 
[INFO ] 2024-06-24 17:20:51.675 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] preload schema finished, cost 3 ms 
[INFO ] 2024-06-24 17:20:51.676 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 17:20:52.004 - [任务 19][smallCaseTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-24 17:20:52.085 - [任务 19][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-06-24 17:20:52.085 - [任务 19][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-06-24 17:20:52.086 - [任务 19][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-24 17:20:52.107 - [任务 19][CLAIM] - batch offset found: {},stream offset found: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 17:20:52.166 - [任务 19][CLAIM] - Incremental sync starting... 
[INFO ] 2024-06-24 17:20:52.169 - [任务 19][CLAIM] - Initial sync completed 
[INFO ] 2024-06-24 17:20:52.211 - [任务 19][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 17:20:52.212 - [任务 19][CLAIM] - Starting mysql cdc, server name: b21acb6c-ee80-4543-8098-9fd4b4aebd59 
[INFO ] 2024-06-24 17:20:52.286 - [任务 19][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1897965403
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.hostname: localhost
  database.password: ********
  name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  pdk.offset.string: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-24 17:20:53.383 - [任务 19][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-24 17:20:55.118 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] running status set to false 
[INFO ] 2024-06-24 17:25:56.586 - [任务 19] - Start task milestones: 66791888e2fb9a28343e59f9(任务 19) 
[INFO ] 2024-06-24 17:25:56.587 - [任务 19] - Task initialization... 
[INFO ] 2024-06-24 17:25:57.510 - [任务 19] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-24 17:25:57.803 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-24 17:25:58.268 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:25:58.294 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:25:58.302 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 17:25:58.306 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 17:25:58.315 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:25:58.316 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 17:25:58.746 - [任务 19][smallCaseTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-24 17:25:58.847 - [任务 19][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-06-24 17:25:58.848 - [任务 19][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-06-24 17:25:58.849 - [任务 19][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-24 17:25:58.961 - [任务 19][CLAIM] - batch offset found: {},stream offset found: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 17:25:58.965 - [任务 19][CLAIM] - Incremental sync starting... 
[INFO ] 2024-06-24 17:25:58.966 - [任务 19][CLAIM] - Initial sync completed 
[INFO ] 2024-06-24 17:25:59.016 - [任务 19][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 17:25:59.018 - [任务 19][CLAIM] - Starting mysql cdc, server name: b21acb6c-ee80-4543-8098-9fd4b4aebd59 
[INFO ] 2024-06-24 17:25:59.097 - [任务 19][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1335592579
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.hostname: localhost
  database.password: ********
  name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  pdk.offset.string: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-24 17:26:00.205 - [任务 19][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-24 17:43:43.932 - [任务 19] - Task initialization... 
[INFO ] 2024-06-24 17:43:43.971 - [任务 19] - Start task milestones: 66791888e2fb9a28343e59f9(任务 19) 
[INFO ] 2024-06-24 17:43:44.938 - [任务 19] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-24 17:43:45.013 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-24 17:43:45.616 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:43:45.622 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:43:45.623 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:43:45.626 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] preload schema finished, cost 1 ms 
[INFO ] 2024-06-24 17:43:45.637 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 17:43:45.641 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 17:43:46.064 - [任务 19][smallCaseTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-24 17:43:46.297 - [任务 19][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-06-24 17:43:46.297 - [任务 19][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-06-24 17:43:46.300 - [任务 19][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-24 17:43:46.300 - [任务 19][CLAIM] - batch offset found: {},stream offset found: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 17:43:46.353 - [任务 19][CLAIM] - Incremental sync starting... 
[INFO ] 2024-06-24 17:43:46.353 - [任务 19][CLAIM] - Initial sync completed 
[INFO ] 2024-06-24 17:43:46.360 - [任务 19][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 17:43:46.393 - [任务 19][CLAIM] - Starting mysql cdc, server name: b21acb6c-ee80-4543-8098-9fd4b4aebd59 
[INFO ] 2024-06-24 17:43:46.394 - [任务 19][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 896175199
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.hostname: localhost
  database.password: ********
  name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  pdk.offset.string: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-24 17:43:47.502 - [任务 19][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-24 17:52:46.661 - [任务 19] - Task initialization... 
[INFO ] 2024-06-24 17:52:46.663 - [任务 19] - Start task milestones: 66791888e2fb9a28343e59f9(任务 19) 
[INFO ] 2024-06-24 17:52:47.349 - [任务 19] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-24 17:52:47.434 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-24 17:52:48.160 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:52:48.161 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:52:48.162 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] start preload schema,table counts: 1 
[INFO ] 2024-06-24 17:52:48.163 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] preload schema finished, cost 1 ms 
[INFO ] 2024-06-24 17:52:48.165 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] preload schema finished, cost 2 ms 
[INFO ] 2024-06-24 17:52:48.167 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 17:52:48.476 - [任务 19][smallCaseTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-24 17:52:48.784 - [任务 19][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-06-24 17:52:48.803 - [任务 19][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-06-24 17:52:48.816 - [任务 19][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-24 17:52:48.831 - [任务 19][CLAIM] - batch offset found: {},stream offset found: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 17:52:48.905 - [任务 19][CLAIM] - Incremental sync starting... 
[INFO ] 2024-06-24 17:52:48.907 - [任务 19][CLAIM] - Initial sync completed 
[INFO ] 2024-06-24 17:52:48.913 - [任务 19][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 17:52:48.944 - [任务 19][CLAIM] - Starting mysql cdc, server name: b21acb6c-ee80-4543-8098-9fd4b4aebd59 
[INFO ] 2024-06-24 17:52:48.945 - [任务 19][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2093688626
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.hostname: localhost
  database.password: ********
  name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  pdk.offset.string: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-24 17:52:49.866 - [任务 19][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-24 18:24:45.744 - [任务 19] - Start task milestones: 66791888e2fb9a28343e59f9(任务 19) 
[INFO ] 2024-06-24 18:24:45.745 - [任务 19] - Task initialization... 
[INFO ] 2024-06-24 18:24:46.866 - [任务 19] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-24 18:24:46.929 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-24 18:24:47.519 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] start preload schema,table counts: 1 
[INFO ] 2024-06-24 18:24:47.522 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] start preload schema,table counts: 1 
[INFO ] 2024-06-24 18:24:47.523 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] start preload schema,table counts: 1 
[INFO ] 2024-06-24 18:24:47.523 - [任务 19][smallCaseTest] - Node smallCaseTest[0767ede8-0d1f-451b-bdce-589dab32775d] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 18:24:47.524 - [任务 19][CLAIM] - Node CLAIM[c16cad9e-641a-4464-b3dd-98a6739fc241] preload schema finished, cost 2 ms 
[INFO ] 2024-06-24 18:24:47.524 - [任务 19][字段改名] - Node 字段改名[b8b6f239-99eb-4d31-885f-a4c6a9cf4e6e] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 18:24:47.829 - [任务 19][smallCaseTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-24 18:24:48.072 - [任务 19][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-06-24 18:24:48.074 - [任务 19][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-06-24 18:24:48.077 - [任务 19][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-24 18:24:48.089 - [任务 19][CLAIM] - batch offset found: {},stream offset found: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 18:24:48.132 - [任务 19][CLAIM] - Incremental sync starting... 
[INFO ] 2024-06-24 18:24:48.132 - [任务 19][CLAIM] - Initial sync completed 
[INFO ] 2024-06-24 18:24:48.140 - [任务 19][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-24 18:24:48.165 - [任务 19][CLAIM] - Starting mysql cdc, server name: b21acb6c-ee80-4543-8098-9fd4b4aebd59 
[INFO ] 2024-06-24 18:24:48.166 - [任务 19][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1070761971
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.hostname: localhost
  database.password: ********
  name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  pdk.offset.string: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-24 18:24:49.254 - [任务 19][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-24 19:08:26.925 - [任务 19][CLAIM] - Mysql binlog reader stopped 
[WARN ] 2024-06-24 19:08:26.935 - [任务 19][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Unknown exception occur when operate table: unknown
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-24 19:09:54.471 - [任务 19][CLAIM] - Starting mysql cdc, server name: b21acb6c-ee80-4543-8098-9fd4b4aebd59 
[INFO ] 2024-06-24 19:09:54.506 - [任务 19][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1871900783
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b21acb6c-ee80-4543-8098-9fd4b4aebd59
  database.hostname: localhost
  database.password: ********
  name: b21acb6c-ee80-4543-8098-9fd4b4aebd59
  pdk.offset.string: {"name":"b21acb6c-ee80-4543-8098-9fd4b4aebd59","offset":{"{\"server\":\"b21acb6c-ee80-4543-8098-9fd4b4aebd59\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-24 19:09:54.861 - [任务 19][CLAIM] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-06-24 19:09:54.901 - [任务 19][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
