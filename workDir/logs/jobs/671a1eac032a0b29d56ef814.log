[INFO ] 2024-10-25 14:32:08.801 - [任务 1] - Start task milestones: 671a1eac032a0b29d56ef814(任务 1) 
[INFO ] 2024-10-25 14:32:09.015 - [任务 1] - Task initialization... 
[INFO ] 2024-10-25 14:32:09.732 - [任务 1] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-10-25 14:32:09.938 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-25 14:32:10.256 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] start preload schema,table counts: 1 
[INFO ] 2024-10-25 14:32:10.258 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] start preload schema,table counts: 1 
[INFO ] 2024-10-25 14:32:10.261 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-25 14:32:10.261 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-25 14:32:11.940 - [任务 1][targetDummy] - Node(targetDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-25 14:32:11.951 - [任务 1][targetDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-25 14:32:12.670 - [任务 1][Oracle11] - Source node "Oracle11" read batch size: 100 
[INFO ] 2024-10-25 14:32:12.671 - [任务 1][Oracle11] - Source node "Oracle11" event queue capacity: 200 
[INFO ] 2024-10-25 14:32:12.672 - [任务 1][Oracle11] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-25 14:32:12.983 - [任务 1][Oracle11] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":126360716,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-25 14:32:12.983 - [任务 1][Oracle11] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-25 14:32:13.054 - [任务 1][Oracle11] - Initial sync started 
[INFO ] 2024-10-25 14:32:13.073 - [任务 1][Oracle11] - Starting batch read, table name: 0620_CAR_CLAIM_M, offset: null 
[INFO ] 2024-10-25 14:32:13.073 - [任务 1][Oracle11] - Table 0620_CAR_CLAIM_M is going to be initial synced 
[INFO ] 2024-10-25 14:32:13.278 - [任务 1][Oracle11] - Query table '0620_CAR_CLAIM_M' counts: 1090 
[INFO ] 2024-10-25 14:32:13.318 - [任务 1][Oracle11] - Table [0620_CAR_CLAIM_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-25 14:32:13.319 - [任务 1][Oracle11] - Initial sync completed 
[INFO ] 2024-10-25 14:32:13.322 - [任务 1][Oracle11] - Incremental sync starting... 
[INFO ] 2024-10-25 14:32:13.324 - [任务 1][Oracle11] - Initial sync completed 
[INFO ] 2024-10-25 14:32:13.525 - [任务 1][Oracle11] - Starting stream read, table list: [0620_CAR_CLAIM_M], offset: {"sortString":null,"offsetValue":null,"lastScn":126360716,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-25 14:32:13.542 - [任务 1][Oracle11] - total start mining scn: 126360716 
[INFO ] 2024-10-25 14:32:14.956 - [任务 1][Oracle11] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-10-25 14:33:26.644 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] running status set to false 
[INFO ] 2024-10-25 14:33:26.645 - [任务 1][Oracle11] - Log Miner is shutting down... 
[INFO ] 2024-10-25 14:33:26.650 - [任务 1][Oracle11] - Log Miner has been closed! 
[INFO ] 2024-10-25 14:33:26.652 - [任务 1][Oracle11] - Incremental sync completed 
[INFO ] 2024-10-25 14:33:26.657 - [任务 1][Oracle11] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-10-25 14:33:26.717 - [任务 1][Oracle11] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:444)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-10-25 14:33:26.718 - [任务 1][Oracle11] - PDK connector node stopped: HazelcastSourcePdkDataNode-ea76bfcd-d784-4639-9567-9a83ae747bc8 
[INFO ] 2024-10-25 14:33:26.721 - [任务 1][Oracle11] - PDK connector node released: HazelcastSourcePdkDataNode-ea76bfcd-d784-4639-9567-9a83ae747bc8 
[INFO ] 2024-10-25 14:33:26.721 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] schema data cleaned 
[INFO ] 2024-10-25 14:33:26.731 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] monitor closed 
[INFO ] 2024-10-25 14:33:26.731 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] close complete, cost 173 ms 
[INFO ] 2024-10-25 14:33:26.742 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] running status set to false 
[INFO ] 2024-10-25 14:33:26.743 - [任务 1][targetDummy] - Stop connector 
[INFO ] 2024-10-25 14:33:26.749 - [任务 1][targetDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-986885e6-ed60-4de5-bd80-dc4308a3273d 
[INFO ] 2024-10-25 14:33:26.749 - [任务 1][targetDummy] - PDK connector node released: HazelcastTargetPdkDataNode-986885e6-ed60-4de5-bd80-dc4308a3273d 
[INFO ] 2024-10-25 14:33:26.750 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] schema data cleaned 
[INFO ] 2024-10-25 14:33:26.751 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] monitor closed 
[INFO ] 2024-10-25 14:33:26.956 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] close complete, cost 19 ms 
[INFO ] 2024-10-25 14:33:30.919 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-25 14:33:30.920 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3898b039 
[INFO ] 2024-10-25 14:33:31.083 - [任务 1] - Stop task milestones: 671a1eac032a0b29d56ef814(任务 1)  
[INFO ] 2024-10-25 14:33:31.083 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-10-25 14:33:31.084 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-10-25 14:33:31.149 - [任务 1] - Remove memory task client succeed, task: 任务 1[671a1eac032a0b29d56ef814] 
[INFO ] 2024-10-25 14:33:31.149 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[671a1eac032a0b29d56ef814] 
[INFO ] 2024-10-25 14:34:51.667 - [任务 1] - Start task milestones: 671a1eac032a0b29d56ef814(任务 1) 
[INFO ] 2024-10-25 14:34:51.668 - [任务 1] - Task initialization... 
[INFO ] 2024-10-25 14:34:51.872 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-25 14:34:51.920 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-25 14:34:51.982 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] start preload schema,table counts: 1 
[INFO ] 2024-10-25 14:34:51.982 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] start preload schema,table counts: 1 
[INFO ] 2024-10-25 14:34:51.982 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-25 14:34:51.984 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-25 14:35:04.430 - [任务 1][targetDummy] - Node(targetDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-25 14:35:04.431 - [任务 1][targetDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-25 14:35:04.714 - [任务 1][Oracle11] - Source node "Oracle11" read batch size: 100 
[INFO ] 2024-10-25 14:35:04.717 - [任务 1][Oracle11] - Source node "Oracle11" event queue capacity: 200 
[INFO ] 2024-10-25 14:35:04.717 - [任务 1][Oracle11] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-25 14:35:05.085 - [任务 1][Oracle11] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":126361224,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-25 14:35:05.230 - [任务 1][Oracle11] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-25 14:35:05.230 - [任务 1][Oracle11] - Initial sync started 
[INFO ] 2024-10-25 14:35:05.236 - [任务 1][Oracle11] - Starting batch read, table name: 0620_CAR_CLAIM_M, offset: null 
[INFO ] 2024-10-25 14:35:05.237 - [任务 1][Oracle11] - Table 0620_CAR_CLAIM_M is going to be initial synced 
[INFO ] 2024-10-25 14:35:05.385 - [任务 1][Oracle11] - Query table '0620_CAR_CLAIM_M' counts: 1090 
[INFO ] 2024-10-25 14:35:05.385 - [任务 1][Oracle11] - Table [0620_CAR_CLAIM_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-25 14:35:05.386 - [任务 1][Oracle11] - Initial sync completed 
[INFO ] 2024-10-25 14:35:05.386 - [任务 1][Oracle11] - Incremental sync starting... 
[INFO ] 2024-10-25 14:35:05.386 - [任务 1][Oracle11] - Initial sync completed 
[INFO ] 2024-10-25 14:35:05.389 - [任务 1][Oracle11] - Starting stream read, table list: [0620_CAR_CLAIM_M], offset: {"sortString":null,"offsetValue":null,"lastScn":126361224,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[WARN ] 2024-10-25 14:35:05.590 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-10-25 14:35:26.282 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] running status set to false 
[INFO ] 2024-10-25 14:35:26.282 - [任务 1][Oracle11] - Incremental sync completed 
[INFO ] 2024-10-25 14:35:26.293 - [任务 1][Oracle11] - Exception skipping - The current exception does not match the skip exception strategy, message: The client connection was terminated by the 10001 server 
[ERROR] 2024-10-25 14:35:26.296 - [任务 1][Oracle11] - java.lang.RuntimeException: connect error <-- Error Message -->
java.lang.RuntimeException: connect error

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: connect error
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: connect error
	... 21 more

[INFO ] 2024-10-25 14:35:26.331 - [任务 1][Oracle11] - PDK connector node stopped: HazelcastSourcePdkDataNode-ea76bfcd-d784-4639-9567-9a83ae747bc8 
[INFO ] 2024-10-25 14:35:26.331 - [任务 1][Oracle11] - PDK connector node released: HazelcastSourcePdkDataNode-ea76bfcd-d784-4639-9567-9a83ae747bc8 
[INFO ] 2024-10-25 14:35:26.332 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] schema data cleaned 
[INFO ] 2024-10-25 14:35:26.335 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] monitor closed 
[INFO ] 2024-10-25 14:35:26.337 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] close complete, cost 110 ms 
[INFO ] 2024-10-25 14:35:26.344 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] running status set to false 
[INFO ] 2024-10-25 14:35:26.344 - [任务 1][targetDummy] - Stop connector 
[INFO ] 2024-10-25 14:35:26.347 - [任务 1][targetDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-986885e6-ed60-4de5-bd80-dc4308a3273d 
[INFO ] 2024-10-25 14:35:26.347 - [任务 1][targetDummy] - PDK connector node released: HazelcastTargetPdkDataNode-986885e6-ed60-4de5-bd80-dc4308a3273d 
[INFO ] 2024-10-25 14:35:26.348 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] schema data cleaned 
[INFO ] 2024-10-25 14:35:26.348 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] monitor closed 
[INFO ] 2024-10-25 14:35:26.353 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] close complete, cost 12 ms 
[INFO ] 2024-10-25 14:35:29.333 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-25 14:35:29.337 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4bbf5747 
[INFO ] 2024-10-25 14:35:29.342 - [任务 1] - Stop task milestones: 671a1eac032a0b29d56ef814(任务 1)  
[INFO ] 2024-10-25 14:35:29.481 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-10-25 14:35:29.481 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-10-25 14:35:29.539 - [任务 1] - Remove memory task client succeed, task: 任务 1[671a1eac032a0b29d56ef814] 
[INFO ] 2024-10-25 14:35:29.540 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[671a1eac032a0b29d56ef814] 
[INFO ] 2024-10-25 14:35:34.031 - [任务 1] - Start task milestones: 671a1eac032a0b29d56ef814(任务 1) 
[INFO ] 2024-10-25 14:35:34.031 - [任务 1] - Task initialization... 
[INFO ] 2024-10-25 14:35:34.197 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-25 14:35:34.197 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-25 14:35:34.235 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] start preload schema,table counts: 1 
[INFO ] 2024-10-25 14:35:34.236 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] start preload schema,table counts: 1 
[INFO ] 2024-10-25 14:35:34.236 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-25 14:35:34.442 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-25 14:35:35.475 - [任务 1][targetDummy] - Node(targetDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-25 14:35:35.481 - [任务 1][targetDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-25 14:35:35.490 - [任务 1][Oracle11] - Source node "Oracle11" read batch size: 100 
[INFO ] 2024-10-25 14:35:35.495 - [任务 1][Oracle11] - Source node "Oracle11" event queue capacity: 200 
[INFO ] 2024-10-25 14:35:35.499 - [任务 1][Oracle11] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-25 14:35:35.752 - [任务 1][Oracle11] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":126361309,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-25 14:35:35.752 - [任务 1][Oracle11] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-25 14:35:35.802 - [任务 1][Oracle11] - Initial sync started 
[INFO ] 2024-10-25 14:35:35.808 - [任务 1][Oracle11] - Starting batch read, table name: 0620_CAR_CLAIM_M, offset: null 
[INFO ] 2024-10-25 14:35:35.808 - [任务 1][Oracle11] - Table 0620_CAR_CLAIM_M is going to be initial synced 
[INFO ] 2024-10-25 14:35:35.940 - [任务 1][Oracle11] - Query table '0620_CAR_CLAIM_M' counts: 1090 
[INFO ] 2024-10-25 14:35:35.940 - [任务 1][Oracle11] - Table [0620_CAR_CLAIM_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-25 14:35:35.941 - [任务 1][Oracle11] - Initial sync completed 
[INFO ] 2024-10-25 14:35:35.941 - [任务 1][Oracle11] - Incremental sync starting... 
[INFO ] 2024-10-25 14:35:35.942 - [任务 1][Oracle11] - Initial sync completed 
[INFO ] 2024-10-25 14:35:35.953 - [任务 1][Oracle11] - Starting stream read, table list: [0620_CAR_CLAIM_M], offset: {"sortString":null,"offsetValue":null,"lastScn":126361309,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[WARN ] 2024-10-25 14:35:35.953 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-10-25 14:35:55.272 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] running status set to false 
[INFO ] 2024-10-25 14:35:55.273 - [任务 1][Oracle11] - PDK connector node stopped: HazelcastSourcePdkDataNode-ea76bfcd-d784-4639-9567-9a83ae747bc8 
[INFO ] 2024-10-25 14:35:55.275 - [任务 1][Oracle11] - PDK connector node released: HazelcastSourcePdkDataNode-ea76bfcd-d784-4639-9567-9a83ae747bc8 
[INFO ] 2024-10-25 14:35:55.275 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] schema data cleaned 
[INFO ] 2024-10-25 14:35:55.288 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] monitor closed 
[INFO ] 2024-10-25 14:35:55.288 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] close complete, cost 39 ms 
[INFO ] 2024-10-25 14:35:55.288 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] running status set to false 
[INFO ] 2024-10-25 14:35:55.306 - [任务 1][targetDummy] - Stop connector 
[INFO ] 2024-10-25 14:35:55.306 - [任务 1][targetDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-986885e6-ed60-4de5-bd80-dc4308a3273d 
[INFO ] 2024-10-25 14:35:55.307 - [任务 1][targetDummy] - PDK connector node released: HazelcastTargetPdkDataNode-986885e6-ed60-4de5-bd80-dc4308a3273d 
[INFO ] 2024-10-25 14:35:55.307 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] schema data cleaned 
[INFO ] 2024-10-25 14:35:55.307 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] monitor closed 
[INFO ] 2024-10-25 14:35:55.345 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] close complete, cost 24 ms 
[INFO ] 2024-10-25 14:35:55.347 - [任务 1][Oracle11] - Incremental sync completed 
[INFO ] 2024-10-25 14:35:55.347 - [任务 1][Oracle11] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.NullPointerException 
[ERROR] 2024-10-25 14:35:55.558 - [任务 1][Oracle11] - java.lang.NullPointerException <-- Error Message -->
java.lang.NullPointerException

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 18 more

[INFO ] 2024-10-25 14:35:59.622 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-25 14:35:59.623 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@56adc118 
[INFO ] 2024-10-25 14:35:59.627 - [任务 1] - Stop task milestones: 671a1eac032a0b29d56ef814(任务 1)  
[INFO ] 2024-10-25 14:35:59.782 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-10-25 14:35:59.784 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-10-25 14:35:59.854 - [任务 1] - Remove memory task client succeed, task: 任务 1[671a1eac032a0b29d56ef814] 
[INFO ] 2024-10-25 14:35:59.854 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[671a1eac032a0b29d56ef814] 
[INFO ] 2024-10-25 14:36:32.456 - [任务 1] - Start task milestones: 671a1eac032a0b29d56ef814(任务 1) 
[INFO ] 2024-10-25 14:36:32.457 - [任务 1] - Task initialization... 
[INFO ] 2024-10-25 14:36:32.632 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-25 14:36:32.632 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-25 14:36:32.669 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] start preload schema,table counts: 1 
[INFO ] 2024-10-25 14:36:32.669 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-25 14:36:32.716 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] start preload schema,table counts: 1 
[INFO ] 2024-10-25 14:36:32.716 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-25 14:36:33.570 - [任务 1][targetDummy] - Node(targetDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-25 14:36:33.577 - [任务 1][targetDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-25 14:36:34.032 - [任务 1][Oracle11] - Source node "Oracle11" read batch size: 100 
[INFO ] 2024-10-25 14:36:34.033 - [任务 1][Oracle11] - Source node "Oracle11" event queue capacity: 200 
[INFO ] 2024-10-25 14:36:34.235 - [任务 1][Oracle11] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-25 14:36:34.352 - [任务 1][Oracle11] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":126361598,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-25 14:36:34.353 - [任务 1][Oracle11] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-25 14:36:34.472 - [任务 1][Oracle11] - Initial sync started 
[INFO ] 2024-10-25 14:36:34.478 - [任务 1][Oracle11] - Starting batch read, table name: 0620_CAR_CLAIM_M, offset: null 
[INFO ] 2024-10-25 14:36:34.478 - [任务 1][Oracle11] - Table 0620_CAR_CLAIM_M is going to be initial synced 
[INFO ] 2024-10-25 14:36:34.599 - [任务 1][Oracle11] - Query table '0620_CAR_CLAIM_M' counts: 1090 
[INFO ] 2024-10-25 14:36:34.602 - [任务 1][Oracle11] - Table [0620_CAR_CLAIM_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-25 14:36:34.602 - [任务 1][Oracle11] - Initial sync completed 
[INFO ] 2024-10-25 14:36:34.603 - [任务 1][Oracle11] - Incremental sync starting... 
[INFO ] 2024-10-25 14:36:34.603 - [任务 1][Oracle11] - Initial sync completed 
[INFO ] 2024-10-25 14:36:34.603 - [任务 1][Oracle11] - Starting stream read, table list: [0620_CAR_CLAIM_M], offset: {"sortString":null,"offsetValue":null,"lastScn":126361598,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[WARN ] 2024-10-25 14:36:34.804 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-10-25 14:37:03.628 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] running status set to false 
[INFO ] 2024-10-25 14:37:03.628 - [任务 1][Oracle11] - PDK connector node stopped: HazelcastSourcePdkDataNode-ea76bfcd-d784-4639-9567-9a83ae747bc8 
[INFO ] 2024-10-25 14:37:03.629 - [任务 1][Oracle11] - PDK connector node released: HazelcastSourcePdkDataNode-ea76bfcd-d784-4639-9567-9a83ae747bc8 
[INFO ] 2024-10-25 14:37:03.629 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] schema data cleaned 
[INFO ] 2024-10-25 14:37:03.631 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] monitor closed 
[INFO ] 2024-10-25 14:37:03.632 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] close complete, cost 61 ms 
[INFO ] 2024-10-25 14:37:03.632 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] running status set to false 
[INFO ] 2024-10-25 14:37:03.640 - [任务 1][targetDummy] - Stop connector 
[INFO ] 2024-10-25 14:37:03.644 - [任务 1][targetDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-986885e6-ed60-4de5-bd80-dc4308a3273d 
[INFO ] 2024-10-25 14:37:03.644 - [任务 1][targetDummy] - PDK connector node released: HazelcastTargetPdkDataNode-986885e6-ed60-4de5-bd80-dc4308a3273d 
[INFO ] 2024-10-25 14:37:03.645 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] schema data cleaned 
[INFO ] 2024-10-25 14:37:03.645 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] monitor closed 
[INFO ] 2024-10-25 14:37:03.647 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] close complete, cost 13 ms 
[INFO ] 2024-10-25 14:37:03.685 - [任务 1][Oracle11] - Incremental sync completed 
[INFO ] 2024-10-25 14:37:03.685 - [任务 1][Oracle11] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.NullPointerException 
[ERROR] 2024-10-25 14:37:03.891 - [任务 1][Oracle11] - java.lang.NullPointerException <-- Error Message -->
java.lang.NullPointerException

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 18 more

[INFO ] 2024-10-25 14:37:04.931 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-25 14:37:04.931 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4a1a0379 
[INFO ] 2024-10-25 14:37:05.062 - [任务 1] - Stop task milestones: 671a1eac032a0b29d56ef814(任务 1)  
[INFO ] 2024-10-25 14:37:05.064 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-10-25 14:37:05.064 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-10-25 14:37:05.093 - [任务 1] - Remove memory task client succeed, task: 任务 1[671a1eac032a0b29d56ef814] 
[INFO ] 2024-10-25 14:37:05.094 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[671a1eac032a0b29d56ef814] 
[INFO ] 2024-10-25 14:37:12.391 - [任务 1] - Start task milestones: 671a1eac032a0b29d56ef814(任务 1) 
[INFO ] 2024-10-25 14:37:12.508 - [任务 1] - Task initialization... 
[INFO ] 2024-10-25 14:37:12.508 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-25 14:37:12.579 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-25 14:37:12.579 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] start preload schema,table counts: 1 
[INFO ] 2024-10-25 14:37:12.580 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] start preload schema,table counts: 1 
[INFO ] 2024-10-25 14:37:12.580 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-25 14:37:12.784 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-25 14:37:13.465 - [任务 1][targetDummy] - Node(targetDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-25 14:37:13.472 - [任务 1][targetDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-25 14:37:13.953 - [任务 1][Oracle11] - Source node "Oracle11" read batch size: 100 
[INFO ] 2024-10-25 14:37:13.953 - [任务 1][Oracle11] - Source node "Oracle11" event queue capacity: 200 
[INFO ] 2024-10-25 14:37:13.953 - [任务 1][Oracle11] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-25 14:37:14.248 - [任务 1][Oracle11] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":126362157,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-25 14:37:14.249 - [任务 1][Oracle11] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-25 14:37:14.312 - [任务 1][Oracle11] - Initial sync started 
[INFO ] 2024-10-25 14:37:14.315 - [任务 1][Oracle11] - Starting batch read, table name: 0620_CAR_CLAIM_M, offset: null 
[INFO ] 2024-10-25 14:37:14.410 - [任务 1][Oracle11] - Table 0620_CAR_CLAIM_M is going to be initial synced 
[INFO ] 2024-10-25 14:37:14.411 - [任务 1][Oracle11] - Query table '0620_CAR_CLAIM_M' counts: 1090 
[INFO ] 2024-10-25 14:37:14.437 - [任务 1][Oracle11] - Table [0620_CAR_CLAIM_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-25 14:37:14.437 - [任务 1][Oracle11] - Initial sync completed 
[INFO ] 2024-10-25 14:37:14.437 - [任务 1][Oracle11] - Incremental sync starting... 
[INFO ] 2024-10-25 14:37:14.437 - [任务 1][Oracle11] - Initial sync completed 
[INFO ] 2024-10-25 14:37:14.442 - [任务 1][Oracle11] - Starting stream read, table list: [0620_CAR_CLAIM_M], offset: {"sortString":null,"offsetValue":null,"lastScn":126362157,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[WARN ] 2024-10-25 14:37:14.442 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-10-25 14:37:32.380 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] running status set to false 
[WARN ] 2024-10-25 14:49:13.769 - [任务 1][Oracle11] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-ea76bfcd-d784-4639-9567-9a83ae747bc8 
[INFO ] 2024-10-25 14:49:13.770 - [任务 1][Oracle11] - PDK connector node released: HazelcastSourcePdkDataNode-ea76bfcd-d784-4639-9567-9a83ae747bc8 
[INFO ] 2024-10-25 14:49:13.771 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] schema data cleaned 
[INFO ] 2024-10-25 14:49:13.799 - [任务 1] - Start task milestones: 671a1eac032a0b29d56ef814(任务 1) 
[INFO ] 2024-10-25 14:49:14.006 - [任务 1] - Task initialization... 
[INFO ] 2024-10-25 14:49:14.618 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-25 14:49:14.825 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-25 14:49:15.078 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] start preload schema,table counts: 1 
[INFO ] 2024-10-25 14:49:15.080 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] start preload schema,table counts: 1 
[INFO ] 2024-10-25 14:49:15.080 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-25 14:49:15.080 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-25 14:49:15.914 - [任务 1][targetDummy] - Node(targetDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-25 14:49:15.917 - [任务 1][targetDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-25 14:49:16.421 - [任务 1][Oracle11] - Source node "Oracle11" read batch size: 100 
[INFO ] 2024-10-25 14:49:16.424 - [任务 1][Oracle11] - Source node "Oracle11" event queue capacity: 200 
[INFO ] 2024-10-25 14:49:16.424 - [任务 1][Oracle11] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-25 14:49:16.660 - [任务 1][Oracle11] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":126363983,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-25 14:49:16.662 - [任务 1][Oracle11] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-25 14:49:16.731 - [任务 1][Oracle11] - Initial sync started 
[INFO ] 2024-10-25 14:49:16.740 - [任务 1][Oracle11] - Starting batch read, table name: 0620_CAR_CLAIM_M, offset: null 
[INFO ] 2024-10-25 14:49:16.741 - [任务 1][Oracle11] - Table 0620_CAR_CLAIM_M is going to be initial synced 
[INFO ] 2024-10-25 14:49:16.909 - [任务 1][Oracle11] - Query table '0620_CAR_CLAIM_M' counts: 1090 
[INFO ] 2024-10-25 14:49:16.912 - [任务 1][Oracle11] - Table [0620_CAR_CLAIM_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-25 14:49:16.912 - [任务 1][Oracle11] - Initial sync completed 
[INFO ] 2024-10-25 14:49:16.915 - [任务 1][Oracle11] - Incremental sync starting... 
[INFO ] 2024-10-25 14:49:16.915 - [任务 1][Oracle11] - Initial sync completed 
[INFO ] 2024-10-25 14:49:16.920 - [任务 1][Oracle11] - Starting stream read, table list: [0620_CAR_CLAIM_M], offset: {"sortString":null,"offsetValue":null,"lastScn":126363983,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[WARN ] 2024-10-25 14:49:19.480 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 14:50:43.956 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 14:51:44.432 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 14:52:44.798 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 14:53:45.222 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 14:54:45.675 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 14:55:45.957 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 14:56:46.220 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 8 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 14:57:46.610 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 7 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 14:58:46.964 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 6 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 14:59:47.651 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 5 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:00:48.159 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 4 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:01:48.652 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 3 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:02:48.923 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 2 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:03:49.322 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 1 time(s)
 - Period 60 second(s) 
[INFO ] 2024-10-25 15:04:49.696 - [任务 1][Oracle11] - Incremental sync completed 
[INFO ] 2024-10-25 15:04:49.750 - [任务 1][Oracle11] - Exception skipping - The current exception does not match the skip exception strategy, message: The client connection was terminated by the 10001 server 
[ERROR] 2024-10-25 15:04:49.751 - [任务 1][Oracle11] - java.lang.RuntimeException: connect error <-- Error Message -->
java.lang.RuntimeException: connect error

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: connect error
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: connect error
	... 21 more

[INFO ] 2024-10-25 15:04:49.957 - [任务 1][Oracle11] - Job suspend in error handle 
[INFO ] 2024-10-25 15:04:50.353 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] running status set to false 
[INFO ] 2024-10-25 15:04:50.353 - [任务 1][Oracle11] - PDK connector node stopped: HazelcastSourcePdkDataNode-ea76bfcd-d784-4639-9567-9a83ae747bc8 
[INFO ] 2024-10-25 15:04:50.355 - [任务 1][Oracle11] - PDK connector node released: HazelcastSourcePdkDataNode-ea76bfcd-d784-4639-9567-9a83ae747bc8 
[INFO ] 2024-10-25 15:04:50.355 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] schema data cleaned 
[INFO ] 2024-10-25 15:04:50.369 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] monitor closed 
[INFO ] 2024-10-25 15:04:50.369 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] close complete, cost 121 ms 
[INFO ] 2024-10-25 15:04:50.370 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] running status set to false 
[INFO ] 2024-10-25 15:04:50.379 - [任务 1][targetDummy] - Stop connector 
[INFO ] 2024-10-25 15:04:50.397 - [任务 1][targetDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-986885e6-ed60-4de5-bd80-dc4308a3273d 
[INFO ] 2024-10-25 15:04:50.397 - [任务 1][targetDummy] - PDK connector node released: HazelcastTargetPdkDataNode-986885e6-ed60-4de5-bd80-dc4308a3273d 
[INFO ] 2024-10-25 15:04:50.397 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] schema data cleaned 
[INFO ] 2024-10-25 15:04:50.397 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] monitor closed 
[INFO ] 2024-10-25 15:04:50.398 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] close complete, cost 29 ms 
[INFO ] 2024-10-25 15:04:54.832 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-25 15:04:54.842 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3ec6768d 
[INFO ] 2024-10-25 15:04:54.842 - [任务 1] - Stop task milestones: 671a1eac032a0b29d56ef814(任务 1)  
[INFO ] 2024-10-25 15:04:54.983 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-10-25 15:04:54.985 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-10-25 15:04:54.986 - [任务 1] - Remove memory task client succeed, task: 任务 1[671a1eac032a0b29d56ef814] 
[INFO ] 2024-10-25 15:04:54.986 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[671a1eac032a0b29d56ef814] 
[INFO ] 2024-10-25 15:04:55.016 - [任务 1] - Resume task[任务 1] 
[INFO ] 2024-10-25 15:04:55.043 - [任务 1] - Start task milestones: 671a1eac032a0b29d56ef814(任务 1) 
[INFO ] 2024-10-25 15:04:55.044 - [任务 1] - Task initialization... 
[INFO ] 2024-10-25 15:04:55.243 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-25 15:04:55.246 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-25 15:04:55.311 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] start preload schema,table counts: 1 
[INFO ] 2024-10-25 15:04:55.312 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] start preload schema,table counts: 1 
[INFO ] 2024-10-25 15:04:55.312 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-25 15:04:55.515 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-25 15:04:55.625 - [任务 1][targetDummy] - Node(targetDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-25 15:04:55.628 - [任务 1][targetDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-25 15:04:55.928 - [任务 1][Oracle11] - Source node "Oracle11" read batch size: 100 
[INFO ] 2024-10-25 15:04:55.929 - [任务 1][Oracle11] - Source node "Oracle11" event queue capacity: 200 
[INFO ] 2024-10-25 15:04:55.931 - [任务 1][Oracle11] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-25 15:04:55.936 - [任务 1][Oracle11] - batch offset found: {"0620_CAR_CLAIM_M":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":126363983,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-25 15:04:55.936 - [任务 1][Oracle11] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-25 15:04:56.020 - [任务 1][Oracle11] - Incremental sync starting... 
[INFO ] 2024-10-25 15:04:56.020 - [任务 1][Oracle11] - Initial sync completed 
[INFO ] 2024-10-25 15:04:56.022 - [任务 1][Oracle11] - Starting stream read, table list: [0620_CAR_CLAIM_M], offset: {"sortString":null,"offsetValue":null,"lastScn":126363983,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[WARN ] 2024-10-25 15:04:56.223 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:05:56.405 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:09:34.317 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:10:34.774 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:11:35.364 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:12:35.752 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:13:36.175 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:14:36.339 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 8 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:15:36.668 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 7 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:16:37.172 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 6 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:17:37.504 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 5 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:18:37.941 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 4 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:19:38.414 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 3 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:20:38.594 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 2 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:21:39.112 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 1 time(s)
 - Period 60 second(s) 
[INFO ] 2024-10-25 15:22:39.287 - [任务 1][Oracle11] - Incremental sync completed 
[INFO ] 2024-10-25 15:22:39.300 - [任务 1][Oracle11] - Exception skipping - The current exception does not match the skip exception strategy, message: The client connection was terminated by the 10001 server 
[ERROR] 2024-10-25 15:22:39.301 - [任务 1][Oracle11] - java.lang.RuntimeException: connect error <-- Error Message -->
java.lang.RuntimeException: connect error

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: connect error
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: connect error
	... 21 more

[INFO ] 2024-10-25 15:22:39.490 - [任务 1][Oracle11] - Job suspend in error handle 
[INFO ] 2024-10-25 15:22:39.491 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] running status set to false 
[INFO ] 2024-10-25 15:22:39.579 - [任务 1][Oracle11] - PDK connector node stopped: HazelcastSourcePdkDataNode-ea76bfcd-d784-4639-9567-9a83ae747bc8 
[INFO ] 2024-10-25 15:22:39.580 - [任务 1][Oracle11] - PDK connector node released: HazelcastSourcePdkDataNode-ea76bfcd-d784-4639-9567-9a83ae747bc8 
[INFO ] 2024-10-25 15:22:39.580 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] schema data cleaned 
[INFO ] 2024-10-25 15:22:39.583 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] monitor closed 
[INFO ] 2024-10-25 15:22:39.585 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] close complete, cost 102 ms 
[INFO ] 2024-10-25 15:22:39.585 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] running status set to false 
[INFO ] 2024-10-25 15:22:39.585 - [任务 1][targetDummy] - Stop connector 
[INFO ] 2024-10-25 15:22:39.592 - [任务 1][targetDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-986885e6-ed60-4de5-bd80-dc4308a3273d 
[INFO ] 2024-10-25 15:22:39.593 - [任务 1][targetDummy] - PDK connector node released: HazelcastTargetPdkDataNode-986885e6-ed60-4de5-bd80-dc4308a3273d 
[INFO ] 2024-10-25 15:22:39.593 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] schema data cleaned 
[INFO ] 2024-10-25 15:22:39.593 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] monitor closed 
[INFO ] 2024-10-25 15:22:39.596 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] close complete, cost 5 ms 
[INFO ] 2024-10-25 15:22:39.838 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-25 15:22:39.841 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@235034ce 
[INFO ] 2024-10-25 15:22:39.971 - [任务 1] - Stop task milestones: 671a1eac032a0b29d56ef814(任务 1)  
[INFO ] 2024-10-25 15:22:39.971 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-10-25 15:22:39.971 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-10-25 15:22:39.972 - [任务 1] - Remove memory task client succeed, task: 任务 1[671a1eac032a0b29d56ef814] 
[INFO ] 2024-10-25 15:22:39.989 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[671a1eac032a0b29d56ef814] 
[INFO ] 2024-10-25 15:22:39.991 - [任务 1] - Resume task[任务 1] 
[INFO ] 2024-10-25 15:22:40.018 - [任务 1] - Start task milestones: 671a1eac032a0b29d56ef814(任务 1) 
[INFO ] 2024-10-25 15:22:40.022 - [任务 1] - Task initialization... 
[INFO ] 2024-10-25 15:22:40.197 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-25 15:22:40.199 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-25 15:22:40.259 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] start preload schema,table counts: 1 
[INFO ] 2024-10-25 15:22:40.260 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] start preload schema,table counts: 1 
[INFO ] 2024-10-25 15:22:40.263 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-25 15:22:40.263 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-25 15:22:40.537 - [任务 1][targetDummy] - Node(targetDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-25 15:22:40.740 - [任务 1][targetDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-25 15:22:40.755 - [任务 1][Oracle11] - Source node "Oracle11" read batch size: 100 
[INFO ] 2024-10-25 15:22:40.755 - [任务 1][Oracle11] - Source node "Oracle11" event queue capacity: 200 
[INFO ] 2024-10-25 15:22:40.756 - [任务 1][Oracle11] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-25 15:22:40.759 - [任务 1][Oracle11] - batch offset found: {"0620_CAR_CLAIM_M":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":126363983,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-10-25 15:22:40.759 - [任务 1][Oracle11] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-25 15:22:40.825 - [任务 1][Oracle11] - Incremental sync starting... 
[INFO ] 2024-10-25 15:22:40.825 - [任务 1][Oracle11] - Initial sync completed 
[INFO ] 2024-10-25 15:22:40.830 - [任务 1][Oracle11] - Starting stream read, table list: [0620_CAR_CLAIM_M], offset: {"sortString":null,"offsetValue":null,"lastScn":126363983,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[WARN ] 2024-10-25 15:22:41.030 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:23:41.167 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:24:41.491 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:25:41.821 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:26:42.375 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:27:42.918 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:28:43.121 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:29:43.488 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 8 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:30:43.734 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 7 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-25 15:31:44.117 - [任务 1][Oracle11] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.lang.RuntimeException: connect error
	io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:440)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...
 - Remaining retry 6 time(s)
 - Period 60 second(s) 
[INFO ] 2024-10-25 15:31:49.968 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] running status set to false 
[INFO ] 2024-10-25 15:31:49.989 - [任务 1][Oracle11] - PDK connector node stopped: HazelcastSourcePdkDataNode-ea76bfcd-d784-4639-9567-9a83ae747bc8 
[INFO ] 2024-10-25 15:31:49.989 - [任务 1][Oracle11] - PDK connector node released: HazelcastSourcePdkDataNode-ea76bfcd-d784-4639-9567-9a83ae747bc8 
[INFO ] 2024-10-25 15:31:49.991 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] schema data cleaned 
[INFO ] 2024-10-25 15:31:49.991 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] monitor closed 
[INFO ] 2024-10-25 15:31:49.994 - [任务 1][Oracle11] - Node Oracle11[ea76bfcd-d784-4639-9567-9a83ae747bc8] close complete, cost 28 ms 
[INFO ] 2024-10-25 15:31:49.994 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] running status set to false 
[INFO ] 2024-10-25 15:31:49.995 - [任务 1][targetDummy] - Stop connector 
[INFO ] 2024-10-25 15:31:49.998 - [任务 1][targetDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-986885e6-ed60-4de5-bd80-dc4308a3273d 
[INFO ] 2024-10-25 15:31:49.998 - [任务 1][targetDummy] - PDK connector node released: HazelcastTargetPdkDataNode-986885e6-ed60-4de5-bd80-dc4308a3273d 
[INFO ] 2024-10-25 15:31:49.998 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] schema data cleaned 
[INFO ] 2024-10-25 15:31:49.998 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] monitor closed 
[INFO ] 2024-10-25 15:31:50.032 - [任务 1][targetDummy] - Node targetDummy[986885e6-ed60-4de5-bd80-dc4308a3273d] close complete, cost 5 ms 
[INFO ] 2024-10-25 15:31:50.032 - [任务 1][Oracle11] - Incremental sync completed 
[INFO ] 2024-10-25 15:31:50.045 - [任务 1][Oracle11] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.NullPointerException 
[ERROR] 2024-10-25 15:31:50.046 - [任务 1][Oracle11] - java.lang.NullPointerException <-- Error Message -->
java.lang.NullPointerException

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 18 more

[INFO ] 2024-10-25 15:31:50.636 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-25 15:31:50.637 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2f63b878 
[INFO ] 2024-10-25 15:31:50.773 - [任务 1] - Stop task milestones: 671a1eac032a0b29d56ef814(任务 1)  
[INFO ] 2024-10-25 15:31:50.774 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-10-25 15:31:50.774 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-10-25 15:31:50.818 - [任务 1] - Remove memory task client succeed, task: 任务 1[671a1eac032a0b29d56ef814] 
[INFO ] 2024-10-25 15:31:50.818 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[671a1eac032a0b29d56ef814] 
