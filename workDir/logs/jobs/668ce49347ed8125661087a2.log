[INFO ] 2024-07-09 17:40:26.271 - [任务 52] - Task initialization... 
[INFO ] 2024-07-09 17:40:26.272 - [任务 52] - Start task milestones: 668ce49347ed8125661087a2(任务 52) 
[INFO ] 2024-07-09 17:40:26.784 - [任务 52] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-09 17:40:26.887 - [任务 52] - The engine receives 任务 52 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-09 17:40:26.985 - [任务 52][dummy_test] - Node dummy_test[00654fb3-1840-451b-89d8-db5e0316dcf8] start preload schema,table counts: 1 
[INFO ] 2024-07-09 17:40:26.985 - [任务 52][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[c9467720-6240-405a-aed0-155019552204] start preload schema,table counts: 1 
[INFO ] 2024-07-09 17:40:26.990 - [任务 52][dummy_test] - Node dummy_test[00654fb3-1840-451b-89d8-db5e0316dcf8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-09 17:40:26.990 - [任务 52][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[c9467720-6240-405a-aed0-155019552204] preload schema finished, cost 0 ms 
[INFO ] 2024-07-09 17:40:27.845 - [任务 52][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-09 17:40:31.806 - [任务 52][0620_CAR_CLAIM_M] - Source node "0620_CAR_CLAIM_M" read batch size: 100 
[INFO ] 2024-07-09 17:40:31.809 - [任务 52][0620_CAR_CLAIM_M] - Source node "0620_CAR_CLAIM_M" event queue capacity: 200 
[INFO ] 2024-07-09 17:40:31.809 - [任务 52][0620_CAR_CLAIM_M] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-09 17:40:33.409 - [任务 52][0620_CAR_CLAIM_M] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":73502085,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-09 17:40:33.475 - [任务 52][0620_CAR_CLAIM_M] - Initial sync started 
[INFO ] 2024-07-09 17:40:33.475 - [任务 52][0620_CAR_CLAIM_M] - Starting batch read, table name: 0620_CAR_CLAIM_M, offset: null 
[INFO ] 2024-07-09 17:40:33.672 - [任务 52][0620_CAR_CLAIM_M] - Table 0620_CAR_CLAIM_M is going to be initial synced 
[INFO ] 2024-07-09 17:40:33.672 - [任务 52][0620_CAR_CLAIM_M] - Query table '0620_CAR_CLAIM_M' counts: 1080 
[INFO ] 2024-07-09 17:40:33.804 - [任务 52][0620_CAR_CLAIM_M] - Table [0620_CAR_CLAIM_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-09 17:40:33.806 - [任务 52][0620_CAR_CLAIM_M] - Initial sync completed 
[INFO ] 2024-07-09 17:40:33.806 - [任务 52][0620_CAR_CLAIM_M] - Incremental sync starting... 
[INFO ] 2024-07-09 17:40:33.807 - [任务 52][0620_CAR_CLAIM_M] - Initial sync completed 
[INFO ] 2024-07-09 17:40:39.125 - [任务 52][0620_CAR_CLAIM_M] - Starting stream read, table list: [0620_CAR_CLAIM_M, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":73502085,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-09 17:40:42.552 - [任务 52][0620_CAR_CLAIM_M] - total start mining scn: 73502085 
[INFO ] 2024-07-09 17:40:43.858 - [任务 52][0620_CAR_CLAIM_M] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-09 17:40:53.793 - [任务 52][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[c9467720-6240-405a-aed0-155019552204] running status set to false 
[INFO ] 2024-07-09 17:40:53.826 - [任务 52][0620_CAR_CLAIM_M] - Log Miner is shutting down... 
[INFO ] 2024-07-09 17:40:53.828 - [任务 52][0620_CAR_CLAIM_M] - Log Miner has been closed! 
[INFO ] 2024-07-09 17:40:53.834 - [任务 52][0620_CAR_CLAIM_M] - Incremental sync completed 
[INFO ] 2024-07-09 17:40:53.835 - [任务 52][0620_CAR_CLAIM_M] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-07-09 17:40:54.040 - [任务 52][0620_CAR_CLAIM_M] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:414)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-07-09 17:40:54.609 - [任务 52][0620_CAR_CLAIM_M] - PDK connector node stopped: HazelcastSourcePdkDataNode-c9467720-6240-405a-aed0-155019552204 
[INFO ] 2024-07-09 17:40:54.686 - [任务 52][0620_CAR_CLAIM_M] - PDK connector node released: HazelcastSourcePdkDataNode-c9467720-6240-405a-aed0-155019552204 
[INFO ] 2024-07-09 17:40:54.690 - [任务 52][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[c9467720-6240-405a-aed0-155019552204] schema data cleaned 
[INFO ] 2024-07-09 17:40:54.691 - [任务 52][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[c9467720-6240-405a-aed0-155019552204] monitor closed 
[INFO ] 2024-07-09 17:40:54.741 - [任务 52][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[c9467720-6240-405a-aed0-155019552204] close complete, cost 903 ms 
[INFO ] 2024-07-09 17:40:54.746 - [任务 52][dummy_test] - Node dummy_test[00654fb3-1840-451b-89d8-db5e0316dcf8] running status set to false 
[INFO ] 2024-07-09 17:40:54.847 - [任务 52][dummy_test] - Stop connector: first 1720518033722 43ms, last 1720518033775 5331ms, counts: 1080/5384ms, min: 2, max: 5333, QPS: 216/s 
[INFO ] 2024-07-09 17:40:54.848 - [任务 52][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-00654fb3-1840-451b-89d8-db5e0316dcf8 
[INFO ] 2024-07-09 17:40:54.848 - [任务 52][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-00654fb3-1840-451b-89d8-db5e0316dcf8 
[INFO ] 2024-07-09 17:40:54.848 - [任务 52][dummy_test] - Node dummy_test[00654fb3-1840-451b-89d8-db5e0316dcf8] schema data cleaned 
[INFO ] 2024-07-09 17:40:54.848 - [任务 52][dummy_test] - Node dummy_test[00654fb3-1840-451b-89d8-db5e0316dcf8] monitor closed 
[INFO ] 2024-07-09 17:40:54.849 - [任务 52][dummy_test] - Node dummy_test[00654fb3-1840-451b-89d8-db5e0316dcf8] close complete, cost 108 ms 
[INFO ] 2024-07-09 17:40:59.139 - [任务 52] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-09 17:40:59.145 - [任务 52] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@23c76a1b 
[INFO ] 2024-07-09 17:40:59.152 - [任务 52] - Stop task milestones: 668ce49347ed8125661087a2(任务 52)  
[INFO ] 2024-07-09 17:40:59.274 - [任务 52] - Stopped task aspect(s) 
[INFO ] 2024-07-09 17:40:59.274 - [任务 52] - Snapshot order controller have been removed 
[INFO ] 2024-07-09 17:40:59.325 - [任务 52] - Remove memory task client succeed, task: 任务 52[668ce49347ed8125661087a2] 
[INFO ] 2024-07-09 17:40:59.326 - [任务 52] - Destroy memory task client cache succeed, task: 任务 52[668ce49347ed8125661087a2] 
[INFO ] 2024-07-09 17:41:21.804 - [任务 52] - Task initialization... 
[INFO ] 2024-07-09 17:41:21.805 - [任务 52] - Start task milestones: 668ce49347ed8125661087a2(任务 52) 
[INFO ] 2024-07-09 17:41:21.968 - [任务 52] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-09 17:41:22.013 - [任务 52] - The engine receives 任务 52 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-09 17:41:22.053 - [任务 52][dummy_test] - Node dummy_test[00654fb3-1840-451b-89d8-db5e0316dcf8] start preload schema,table counts: 1 
[INFO ] 2024-07-09 17:41:22.054 - [任务 52][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[c9467720-6240-405a-aed0-155019552204] start preload schema,table counts: 1 
[INFO ] 2024-07-09 17:41:22.060 - [任务 52][dummy_test] - Node dummy_test[00654fb3-1840-451b-89d8-db5e0316dcf8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-09 17:41:22.060 - [任务 52][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[c9467720-6240-405a-aed0-155019552204] preload schema finished, cost 0 ms 
[INFO ] 2024-07-09 17:41:22.668 - [任务 52][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-09 17:41:23.626 - [任务 52][0620_CAR_CLAIM_M] - Source node "0620_CAR_CLAIM_M" read batch size: 100 
[INFO ] 2024-07-09 17:41:23.626 - [任务 52][0620_CAR_CLAIM_M] - Source node "0620_CAR_CLAIM_M" event queue capacity: 200 
[INFO ] 2024-07-09 17:41:23.626 - [任务 52][0620_CAR_CLAIM_M] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-09 17:41:24.642 - [任务 52][0620_CAR_CLAIM_M] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":73508738,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-09 17:41:24.673 - [任务 52][0620_CAR_CLAIM_M] - Initial sync started 
[INFO ] 2024-07-09 17:41:24.674 - [任务 52][0620_CAR_CLAIM_M] - Starting batch read, table name: 0620_CAR_CLAIM_M, offset: null 
[INFO ] 2024-07-09 17:41:24.674 - [任务 52][0620_CAR_CLAIM_M] - Table 0620_CAR_CLAIM_M is going to be initial synced 
[INFO ] 2024-07-09 17:41:24.881 - [任务 52][0620_CAR_CLAIM_M] - Query table '0620_CAR_CLAIM_M' counts: 1080 
[INFO ] 2024-07-09 17:41:24.886 - [任务 52][0620_CAR_CLAIM_M] - Table [0620_CAR_CLAIM_M] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-09 17:41:24.886 - [任务 52][0620_CAR_CLAIM_M] - Initial sync completed 
[INFO ] 2024-07-09 17:41:24.888 - [任务 52][0620_CAR_CLAIM_M] - Incremental sync starting... 
[INFO ] 2024-07-09 17:41:24.888 - [任务 52][0620_CAR_CLAIM_M] - Initial sync completed 
[INFO ] 2024-07-09 17:41:24.889 - [任务 52][0620_CAR_CLAIM_M] - Starting stream read, table list: [0620_CAR_CLAIM_M, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":73508738,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-09 17:42:03.856 - [任务 52][0620_CAR_CLAIM_M] - total start mining scn: 73508738 
[INFO ] 2024-07-09 17:42:04.936 - [任务 52][0620_CAR_CLAIM_M] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-09 17:43:13.078 - [任务 52][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[c9467720-6240-405a-aed0-155019552204] running status set to false 
[INFO ] 2024-07-09 17:43:13.079 - [任务 52][0620_CAR_CLAIM_M] - Log Miner is shutting down... 
[INFO ] 2024-07-09 17:43:13.087 - [任务 52][0620_CAR_CLAIM_M] - Log Miner has been closed! 
[INFO ] 2024-07-09 17:43:13.088 - [任务 52][0620_CAR_CLAIM_M] - Incremental sync completed 
[INFO ] 2024-07-09 17:43:13.098 - [任务 52][0620_CAR_CLAIM_M] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-07-09 17:43:13.101 - [任务 52][0620_CAR_CLAIM_M] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:414)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-07-09 17:43:13.185 - [任务 52][0620_CAR_CLAIM_M] - PDK connector node stopped: HazelcastSourcePdkDataNode-c9467720-6240-405a-aed0-155019552204 
[INFO ] 2024-07-09 17:43:13.186 - [任务 52][0620_CAR_CLAIM_M] - PDK connector node released: HazelcastSourcePdkDataNode-c9467720-6240-405a-aed0-155019552204 
[INFO ] 2024-07-09 17:43:13.186 - [任务 52][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[c9467720-6240-405a-aed0-155019552204] schema data cleaned 
[INFO ] 2024-07-09 17:43:13.186 - [任务 52][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[c9467720-6240-405a-aed0-155019552204] monitor closed 
[INFO ] 2024-07-09 17:43:13.190 - [任务 52][0620_CAR_CLAIM_M] - Node 0620_CAR_CLAIM_M[c9467720-6240-405a-aed0-155019552204] close complete, cost 137 ms 
[INFO ] 2024-07-09 17:43:13.190 - [任务 52][dummy_test] - Node dummy_test[00654fb3-1840-451b-89d8-db5e0316dcf8] running status set to false 
[INFO ] 2024-07-09 17:43:13.207 - [任务 52][dummy_test] - Stop connector: first 1720518084835 12ms, last 1720518084851 38170ms, counts: 1080/38186ms, min: 1, max: 38170, QPS: 28/s 
[INFO ] 2024-07-09 17:43:13.213 - [任务 52][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-00654fb3-1840-451b-89d8-db5e0316dcf8 
[INFO ] 2024-07-09 17:43:13.213 - [任务 52][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-00654fb3-1840-451b-89d8-db5e0316dcf8 
[INFO ] 2024-07-09 17:43:13.213 - [任务 52][dummy_test] - Node dummy_test[00654fb3-1840-451b-89d8-db5e0316dcf8] schema data cleaned 
[INFO ] 2024-07-09 17:43:13.213 - [任务 52][dummy_test] - Node dummy_test[00654fb3-1840-451b-89d8-db5e0316dcf8] monitor closed 
[INFO ] 2024-07-09 17:43:13.214 - [任务 52][dummy_test] - Node dummy_test[00654fb3-1840-451b-89d8-db5e0316dcf8] close complete, cost 26 ms 
[INFO ] 2024-07-09 17:43:18.087 - [任务 52] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-09 17:43:18.096 - [任务 52] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@509300f3 
[INFO ] 2024-07-09 17:43:18.227 - [任务 52] - Stop task milestones: 668ce49347ed8125661087a2(任务 52)  
[INFO ] 2024-07-09 17:43:18.227 - [任务 52] - Stopped task aspect(s) 
[INFO ] 2024-07-09 17:43:18.227 - [任务 52] - Snapshot order controller have been removed 
[INFO ] 2024-07-09 17:43:18.247 - [任务 52] - Remove memory task client succeed, task: 任务 52[668ce49347ed8125661087a2] 
[INFO ] 2024-07-09 17:43:18.248 - [任务 52] - Destroy memory task client cache succeed, task: 任务 52[668ce49347ed8125661087a2] 
