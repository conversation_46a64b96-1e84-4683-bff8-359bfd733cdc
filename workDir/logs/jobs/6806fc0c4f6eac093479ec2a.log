[TRACE] 2025-04-24 10:11:40.870 - [任务 34] - Task initialization... 
[TRACE] 2025-04-24 10:11:40.897 - [任务 34] - Start task milestones: 6806fc0c4f6eac093479ec2a(任务 34) 
[INFO ] 2025-04-24 10:11:41.513 - [任务 34] - Loading table structure completed 
[TRACE] 2025-04-24 10:11:41.651 - [任务 34] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-04-24 10:11:41.651 - [任务 34] - The engine receives 任务 34 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-24 10:11:41.827 - [任务 34] - Task started 
[TRACE] 2025-04-24 10:11:41.986 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] start preload schema,table counts: 1 
[TRACE] 2025-04-24 10:11:41.990 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] preload schema finished, cost 0 ms 
[TRACE] 2025-04-24 10:11:42.182 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] start preload schema,table counts: 1 
[TRACE] 2025-04-24 10:11:42.185 - [任务 34][字段编辑] - Node 字段编辑[f41b3b82-9373-4c6f-91de-2369f5f8e975] start preload schema,table counts: 1 
[TRACE] 2025-04-24 10:11:42.195 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] start preload schema,table counts: 1 
[TRACE] 2025-04-24 10:11:42.199 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] preload schema finished, cost 0 ms 
[TRACE] 2025-04-24 10:11:42.205 - [任务 34][字段编辑] - Node 字段编辑[f41b3b82-9373-4c6f-91de-2369f5f8e975] preload schema finished, cost 0 ms 
[TRACE] 2025-04-24 10:11:42.206 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] preload schema finished, cost 0 ms 
[TRACE] 2025-04-24 10:11:42.208 - [任务 34][表编辑] - Node table_rename_processor(表编辑: 024ffb72-4e44-4954-b919-28e843ebc241) enable batch process 
[TRACE] 2025-04-24 10:11:42.208 - [任务 34][字段编辑] - Node migrate_field_rename_processor(字段编辑: f41b3b82-9373-4c6f-91de-2369f5f8e975) enable batch process 
[INFO ] 2025-04-24 10:11:44.574 - [任务 34][Target3306] - Sink connector(Target3306) initialization completed 
[TRACE] 2025-04-24 10:11:44.576 - [任务 34][Target3306] - Node(Target3306) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-24 10:11:44.576 - [任务 34][Target3306] - Write batch size: 4000, max wait ms per batch: 500 
[INFO ] 2025-04-24 10:11:44.587 - [任务 34][Target3306] - Apply table structure to target database 
[INFO ] 2025-04-24 10:11:44.597 - [任务 34][Source3306] - Source connector(Source3306) initialization completed 
[TRACE] 2025-04-24 10:11:44.604 - [任务 34][Source3306] - Source node "Source3306" read batch size: 100 
[TRACE] 2025-04-24 10:11:44.607 - [任务 34][Source3306] - Source node "Source3306" event queue capacity: 200 
[TRACE] 2025-04-24 10:11:44.610 - [任务 34][Source3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-24 10:11:45.251 - [任务 34][Source3306] - Starting batch read from 1 tables 
[TRACE] 2025-04-24 10:11:45.489 - [任务 34][Source3306] - Initial sync started 
[INFO ] 2025-04-24 10:11:45.489 - [任务 34][Source3306] - Starting batch read from table: customer6 
[TRACE] 2025-04-24 10:11:45.505 - [任务 34][Source3306] - Table customer6 is going to be initial synced 
[TRACE] 2025-04-24 10:11:45.506 - [任务 34][Source3306] - Query snapshot row size completed: Source3306(5420740b-4ad6-484c-a4b1-b576d3dd8134) 
[INFO ] 2025-04-24 10:11:47.357 - [任务 34][Source3306] - Table customer6 has been completed batch read 
[TRACE] 2025-04-24 10:11:47.371 - [任务 34][Source3306] - Initial sync completed 
[INFO ] 2025-04-24 10:11:47.371 - [任务 34][Source3306] - Batch read completed. 
[INFO ] 2025-04-24 10:11:47.403 - [任务 34][Source3306] - Task run completed 
[TRACE] 2025-04-24 10:11:53.530 - [任务 34][Target3306] - Process after table "wim_customer60" initial sync finished, cost: 0 ms 
[INFO ] 2025-04-24 10:11:53.531 - [任务 34][Target3306] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-04-24 10:11:53.563 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] running status set to false 
[TRACE] 2025-04-24 10:11:53.564 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] running status set to false 
[TRACE] 2025-04-24 10:11:53.565 - [任务 34][字段编辑] - Node 字段编辑[f41b3b82-9373-4c6f-91de-2369f5f8e975] running status set to false 
[TRACE] 2025-04-24 10:11:53.566 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] running status set to false 
[TRACE] 2025-04-24 10:11:53.566 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] schema data cleaned 
[TRACE] 2025-04-24 10:11:53.572 - [任务 34][字段编辑] - Node 字段编辑[f41b3b82-9373-4c6f-91de-2369f5f8e975] schema data cleaned 
[TRACE] 2025-04-24 10:11:53.572 - [任务 34][字段编辑] - Node 字段编辑[f41b3b82-9373-4c6f-91de-2369f5f8e975] monitor closed 
[TRACE] 2025-04-24 10:11:53.576 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] monitor closed 
[TRACE] 2025-04-24 10:11:53.581 - [任务 34][字段编辑] - Node 字段编辑[f41b3b82-9373-4c6f-91de-2369f5f8e975] close complete, cost 14 ms 
[TRACE] 2025-04-24 10:11:53.583 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] close complete, cost 18 ms 
[TRACE] 2025-04-24 10:11:53.604 - [任务 34][Source3306] - PDK connector node stopped: HazelcastSourcePdkDataNode_5420740b-4ad6-484c-a4b1-b576d3dd8134_1745460704153 
[TRACE] 2025-04-24 10:11:53.604 - [任务 34][Source3306] - PDK connector node released: HazelcastSourcePdkDataNode_5420740b-4ad6-484c-a4b1-b576d3dd8134_1745460704153 
[TRACE] 2025-04-24 10:11:53.605 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] schema data cleaned 
[TRACE] 2025-04-24 10:11:53.611 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] monitor closed 
[TRACE] 2025-04-24 10:11:53.612 - [任务 34][Target3306] - PDK connector node stopped: HazelcastTargetPdkDataNode_b70a06d6-3948-4a0a-82e3-de9095551300_1745460704051 
[TRACE] 2025-04-24 10:11:53.614 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] close complete, cost 55 ms 
[TRACE] 2025-04-24 10:11:53.614 - [任务 34][Target3306] - PDK connector node released: HazelcastTargetPdkDataNode_b70a06d6-3948-4a0a-82e3-de9095551300_1745460704051 
[TRACE] 2025-04-24 10:11:53.614 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] schema data cleaned 
[TRACE] 2025-04-24 10:11:53.616 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] monitor closed 
[TRACE] 2025-04-24 10:11:53.616 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] close complete, cost 51 ms 
[TRACE] 2025-04-24 10:11:55.514 - [任务 34] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-24 10:11:55.521 - [任务 34] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4d67f3b8 
[TRACE] 2025-04-24 10:11:55.522 - [任务 34] - Stop task milestones: 6806fc0c4f6eac093479ec2a(任务 34)  
[TRACE] 2025-04-24 10:11:55.657 - [任务 34] - Stopped task aspect(s) 
[TRACE] 2025-04-24 10:11:55.659 - [任务 34] - Snapshot order controller have been removed 
[INFO ] 2025-04-24 10:11:55.862 - [任务 34] - Task stopped. 
[TRACE] 2025-04-24 10:12:00.682 - [任务 34] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-24 10:12:00.682 - [任务 34] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4d67f3b8 
[TRACE] 2025-04-24 10:12:00.684 - [任务 34] - Stopped task aspect(s) 
[INFO ] 2025-04-24 10:12:00.772 - [任务 34] - Task stopped. 
[TRACE] 2025-04-24 10:12:00.775 - [任务 34] - Remove memory task client succeed, task: 任务 34[6806fc0c4f6eac093479ec2a] 
[TRACE] 2025-04-24 10:12:00.777 - [任务 34] - Destroy memory task client cache succeed, task: 任务 34[6806fc0c4f6eac093479ec2a] 
[TRACE] 2025-04-24 14:36:57.664 - [任务 34] - Task initialization... 
[TRACE] 2025-04-24 14:36:57.666 - [任务 34] - Start task milestones: 6806fc0c4f6eac093479ec2a(任务 34) 
[INFO ] 2025-04-24 14:36:58.618 - [任务 34] - Loading table structure completed 
[TRACE] 2025-04-24 14:36:59.144 - [任务 34] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-24 14:36:59.350 - [任务 34] - The engine receives 任务 34 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-24 14:36:59.552 - [任务 34] - Task started 
[TRACE] 2025-04-24 14:36:59.620 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] start preload schema,table counts: 1 
[TRACE] 2025-04-24 14:36:59.622 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] start preload schema,table counts: 1 
[TRACE] 2025-04-24 14:36:59.627 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] preload schema finished, cost 1 ms 
[TRACE] 2025-04-24 14:36:59.628 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] preload schema finished, cost 0 ms 
[TRACE] 2025-04-24 14:36:59.628 - [任务 34][表编辑] - Node table_rename_processor(表编辑: 024ffb72-4e44-4954-b919-28e843ebc241) enable batch process 
[TRACE] 2025-04-24 14:36:59.681 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] start preload schema,table counts: 1 
[TRACE] 2025-04-24 14:36:59.682 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] preload schema finished, cost 0 ms 
[INFO ] 2025-04-24 14:37:00.649 - [任务 34][Source3306] - Source connector(Source3306) initialization completed 
[TRACE] 2025-04-24 14:37:00.653 - [任务 34][Source3306] - Source node "Source3306" read batch size: 100 
[TRACE] 2025-04-24 14:37:00.655 - [任务 34][Source3306] - Source node "Source3306" event queue capacity: 200 
[TRACE] 2025-04-24 14:37:00.660 - [任务 34][Source3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-24 14:37:00.805 - [任务 34][Target3306] - Sink connector(Target3306) initialization completed 
[TRACE] 2025-04-24 14:37:00.805 - [任务 34][Target3306] - Node(Target3306) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-24 14:37:00.805 - [任务 34][Target3306] - Write batch size: 4000, max wait ms per batch: 500 
[INFO ] 2025-04-24 14:37:00.879 - [任务 34][Target3306] - Apply table structure to target database 
[INFO ] 2025-04-24 14:37:00.880 - [任务 34][Source3306] - Starting batch read from 1 tables 
[TRACE] 2025-04-24 14:37:01.107 - [任务 34][Source3306] - Initial sync started 
[INFO ] 2025-04-24 14:37:01.108 - [任务 34][Source3306] - Starting batch read from table: customer6 
[TRACE] 2025-04-24 14:37:01.146 - [任务 34][Source3306] - Table customer6 is going to be initial synced 
[TRACE] 2025-04-24 14:37:01.163 - [任务 34][Source3306] - Query snapshot row size completed: Source3306(5420740b-4ad6-484c-a4b1-b576d3dd8134) 
[INFO ] 2025-04-24 14:37:03.433 - [任务 34][Source3306] - Table customer6 has been completed batch read 
[TRACE] 2025-04-24 14:37:03.443 - [任务 34][Source3306] - Initial sync completed 
[INFO ] 2025-04-24 14:37:03.444 - [任务 34][Source3306] - Batch read completed. 
[INFO ] 2025-04-24 14:37:03.645 - [任务 34][Source3306] - Task run completed 
[TRACE] 2025-04-24 14:37:09.547 - [任务 34][Target3306] - Process after table "wim_customer60" initial sync finished, cost: 1 ms 
[INFO ] 2025-04-24 14:37:09.549 - [任务 34][Target3306] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-04-24 14:37:09.644 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] running status set to false 
[TRACE] 2025-04-24 14:37:09.648 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] running status set to false 
[TRACE] 2025-04-24 14:37:09.663 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] schema data cleaned 
[TRACE] 2025-04-24 14:37:09.663 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] running status set to false 
[TRACE] 2025-04-24 14:37:09.685 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] monitor closed 
[TRACE] 2025-04-24 14:37:09.685 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] close complete, cost 45 ms 
[TRACE] 2025-04-24 14:37:09.685 - [任务 34][Source3306] - PDK connector node stopped: HazelcastSourcePdkDataNode_5420740b-4ad6-484c-a4b1-b576d3dd8134_1745476620241 
[TRACE] 2025-04-24 14:37:09.689 - [任务 34][Source3306] - PDK connector node released: HazelcastSourcePdkDataNode_5420740b-4ad6-484c-a4b1-b576d3dd8134_1745476620241 
[TRACE] 2025-04-24 14:37:09.689 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] schema data cleaned 
[TRACE] 2025-04-24 14:37:09.692 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] monitor closed 
[TRACE] 2025-04-24 14:37:09.692 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] close complete, cost 64 ms 
[TRACE] 2025-04-24 14:37:09.725 - [任务 34][Target3306] - PDK connector node stopped: HazelcastTargetPdkDataNode_b70a06d6-3948-4a0a-82e3-de9095551300_1745476620626 
[TRACE] 2025-04-24 14:37:09.725 - [任务 34][Target3306] - PDK connector node released: HazelcastTargetPdkDataNode_b70a06d6-3948-4a0a-82e3-de9095551300_1745476620626 
[TRACE] 2025-04-24 14:37:09.725 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] schema data cleaned 
[TRACE] 2025-04-24 14:37:09.727 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] monitor closed 
[TRACE] 2025-04-24 14:37:09.932 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] close complete, cost 96 ms 
[TRACE] 2025-04-24 14:37:10.447 - [任务 34] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-24 14:37:10.453 - [任务 34] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2f45ac57 
[TRACE] 2025-04-24 14:37:10.456 - [任务 34] - Stop task milestones: 6806fc0c4f6eac093479ec2a(任务 34)  
[TRACE] 2025-04-24 14:37:10.597 - [任务 34] - Stopped task aspect(s) 
[TRACE] 2025-04-24 14:37:10.597 - [任务 34] - Snapshot order controller have been removed 
[INFO ] 2025-04-24 14:37:10.801 - [任务 34] - Task stopped. 
[TRACE] 2025-04-24 14:37:15.622 - [任务 34] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-24 14:37:15.626 - [任务 34] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2f45ac57 
[TRACE] 2025-04-24 14:37:15.626 - [任务 34] - Stopped task aspect(s) 
[INFO ] 2025-04-24 14:37:15.626 - [任务 34] - Task stopped. 
[TRACE] 2025-04-24 14:37:15.715 - [任务 34] - Remove memory task client succeed, task: 任务 34[6806fc0c4f6eac093479ec2a] 
[TRACE] 2025-04-24 14:37:15.716 - [任务 34] - Destroy memory task client cache succeed, task: 任务 34[6806fc0c4f6eac093479ec2a] 
[TRACE] 2025-04-24 14:38:15.529 - [任务 34] - Task initialization... 
[TRACE] 2025-04-24 14:38:15.531 - [任务 34] - Start task milestones: 6806fc0c4f6eac093479ec2a(任务 34) 
[INFO ] 2025-04-24 14:38:16.544 - [任务 34] - Loading table structure completed 
[TRACE] 2025-04-24 14:38:16.644 - [任务 34] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-24 14:38:16.646 - [任务 34] - The engine receives 任务 34 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-24 14:38:16.879 - [任务 34] - Task started 
[TRACE] 2025-04-24 14:38:16.912 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] start preload schema,table counts: 20 
[TRACE] 2025-04-24 14:38:16.913 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] start preload schema,table counts: 20 
[TRACE] 2025-04-24 14:38:16.913 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] start preload schema,table counts: 20 
[TRACE] 2025-04-24 14:38:16.913 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] preload schema finished, cost 1 ms 
[TRACE] 2025-04-24 14:38:16.915 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] preload schema finished, cost 1 ms 
[TRACE] 2025-04-24 14:38:16.915 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] preload schema finished, cost 1 ms 
[TRACE] 2025-04-24 14:38:17.120 - [任务 34][表编辑] - Node table_rename_processor(表编辑: 024ffb72-4e44-4954-b919-28e843ebc241) enable batch process 
[INFO ] 2025-04-24 14:38:17.719 - [任务 34][Target3306] - Sink connector(Target3306) initialization completed 
[TRACE] 2025-04-24 14:38:17.720 - [任务 34][Target3306] - Node(Target3306) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-24 14:38:17.720 - [任务 34][Target3306] - Write batch size: 4000, max wait ms per batch: 500 
[INFO ] 2025-04-24 14:38:17.729 - [任务 34][Target3306] - Apply table structure to target database 
[INFO ] 2025-04-24 14:38:17.729 - [任务 34][Target3306] - Table "target.wim_customer90" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:17.729 - [任务 34][Target3306] - The table wim_customer90 has already exist. 
[INFO ] 2025-04-24 14:38:17.865 - [任务 34][Source3306] - Source connector(Source3306) initialization completed 
[TRACE] 2025-04-24 14:38:17.866 - [任务 34][Source3306] - Source node "Source3306" read batch size: 100 
[TRACE] 2025-04-24 14:38:17.868 - [任务 34][Source3306] - Source node "Source3306" event queue capacity: 200 
[TRACE] 2025-04-24 14:38:17.868 - [任务 34][Source3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-24 14:38:17.881 - [任务 34][Target3306] - Table "target.wim_customer130" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:17.882 - [任务 34][Target3306] - The table wim_customer130 has already exist. 
[INFO ] 2025-04-24 14:38:17.933 - [任务 34][Target3306] - Table "target.wim_customer120" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:17.934 - [任务 34][Target3306] - The table wim_customer120 has already exist. 
[INFO ] 2025-04-24 14:38:18.003 - [任务 34][Target3306] - Table "target.wim_customer70" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:18.004 - [任务 34][Target3306] - The table wim_customer70 has already exist. 
[INFO ] 2025-04-24 14:38:18.062 - [任务 34][Source3306] - Starting batch read from 20 tables 
[TRACE] 2025-04-24 14:38:18.066 - [任务 34][Source3306] - Initial sync started 
[INFO ] 2025-04-24 14:38:18.066 - [任务 34][Source3306] - Starting batch read from table: customer14 
[TRACE] 2025-04-24 14:38:18.066 - [任务 34][Source3306] - Table customer14 is going to be initial synced 
[INFO ] 2025-04-24 14:38:18.083 - [任务 34][Target3306] - Table "target.wim_customer110" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:18.083 - [任务 34][Target3306] - The table wim_customer110 has already exist. 
[INFO ] 2025-04-24 14:38:18.116 - [任务 34][Target3306] - Table "target.wim_customer80" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:18.160 - [任务 34][Target3306] - The table wim_customer80 has already exist. 
[INFO ] 2025-04-24 14:38:18.163 - [任务 34][Target3306] - Table "target.wim_customer100" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:18.163 - [任务 34][Target3306] - The table wim_customer100 has already exist. 
[INFO ] 2025-04-24 14:38:18.206 - [任务 34][Target3306] - Table "target.wim_customer50" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:18.206 - [任务 34][Target3306] - The table wim_customer50 has already exist. 
[INFO ] 2025-04-24 14:38:18.267 - [任务 34][Target3306] - Table "target.wim_customer170" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:18.267 - [任务 34][Target3306] - The table wim_customer170 has already exist. 
[INFO ] 2025-04-24 14:38:18.304 - [任务 34][Target3306] - Table "target.wim_customer60" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:18.305 - [任务 34][Target3306] - The table wim_customer60 has already exist. 
[INFO ] 2025-04-24 14:38:18.346 - [任务 34][Target3306] - Table "target.wim_customer160" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:18.346 - [任务 34][Target3306] - The table wim_customer160 has already exist. 
[INFO ] 2025-04-24 14:38:18.417 - [任务 34][Target3306] - Table "target.wim_customer30" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:18.418 - [任务 34][Target3306] - The table wim_customer30 has already exist. 
[INFO ] 2025-04-24 14:38:18.454 - [任务 34][Target3306] - Table "target.wim_customer150" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:18.454 - [任务 34][Target3306] - The table wim_customer150 has already exist. 
[INFO ] 2025-04-24 14:38:18.495 - [任务 34][Target3306] - Table "target.wim_customer40" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:18.495 - [任务 34][Target3306] - The table wim_customer40 has already exist. 
[INFO ] 2025-04-24 14:38:18.539 - [任务 34][Target3306] - Table "target.wim_customer140" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:18.539 - [任务 34][Target3306] - The table wim_customer140 has already exist. 
[INFO ] 2025-04-24 14:38:18.625 - [任务 34][Target3306] - Table "target.wim_customer190" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:18.625 - [任务 34][Target3306] - The table wim_customer190 has already exist. 
[INFO ] 2025-04-24 14:38:18.767 - [任务 34][Target3306] - Table "target.wim_customer180" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:18.767 - [任务 34][Target3306] - The table wim_customer180 has already exist. 
[INFO ] 2025-04-24 14:38:19.005 - [任务 34][Target3306] - Table "target.wim_customer10" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.005 - [任务 34][Target3306] - The table wim_customer10 has already exist. 
[INFO ] 2025-04-24 14:38:19.127 - [任务 34][Target3306] - Table "target.wim_customer20" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.128 - [任务 34][Target3306] - The table wim_customer20 has already exist. 
[INFO ] 2025-04-24 14:38:19.164 - [任务 34][Target3306] - Table "target.wim_customer200" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.164 - [任务 34][Target3306] - The table wim_customer200 has already exist. 
[INFO ] 2025-04-24 14:38:19.219 - [任务 34][Target3306] - Table "target.wim_customer90" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.219 - [任务 34][Target3306] - The table wim_customer90 has already exist. 
[INFO ] 2025-04-24 14:38:19.263 - [任务 34][Target3306] - Table "target.wim_customer80" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.264 - [任务 34][Target3306] - The table wim_customer80 has already exist. 
[INFO ] 2025-04-24 14:38:19.308 - [任务 34][Target3306] - Table "target.wim_customer70" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.309 - [任务 34][Target3306] - The table wim_customer70 has already exist. 
[INFO ] 2025-04-24 14:38:19.334 - [任务 34][Target3306] - Table "target.wim_customer50" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.334 - [任务 34][Target3306] - The table wim_customer50 has already exist. 
[INFO ] 2025-04-24 14:38:19.363 - [任务 34][Target3306] - Table "target.wim_customer40" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.363 - [任务 34][Target3306] - The table wim_customer40 has already exist. 
[INFO ] 2025-04-24 14:38:19.398 - [任务 34][Target3306] - Table "target.wim_customer30" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.398 - [任务 34][Target3306] - The table wim_customer30 has already exist. 
[INFO ] 2025-04-24 14:38:19.445 - [任务 34][Target3306] - Table "target.wim_customer200" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.445 - [任务 34][Target3306] - The table wim_customer200 has already exist. 
[INFO ] 2025-04-24 14:38:19.484 - [任务 34][Target3306] - Table "target.wim_customer20" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.484 - [任务 34][Target3306] - The table wim_customer20 has already exist. 
[INFO ] 2025-04-24 14:38:19.523 - [任务 34][Target3306] - Table "target.wim_customer190" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.523 - [任务 34][Target3306] - The table wim_customer190 has already exist. 
[INFO ] 2025-04-24 14:38:19.559 - [任务 34][Target3306] - Table "target.wim_customer180" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.559 - [任务 34][Target3306] - The table wim_customer180 has already exist. 
[INFO ] 2025-04-24 14:38:19.594 - [任务 34][Target3306] - Table "target.wim_customer170" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.594 - [任务 34][Target3306] - The table wim_customer170 has already exist. 
[INFO ] 2025-04-24 14:38:19.642 - [任务 34][Target3306] - Table "target.wim_customer160" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.642 - [任务 34][Target3306] - The table wim_customer160 has already exist. 
[INFO ] 2025-04-24 14:38:19.691 - [任务 34][Target3306] - Table "target.wim_customer150" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.691 - [任务 34][Target3306] - The table wim_customer150 has already exist. 
[INFO ] 2025-04-24 14:38:19.764 - [任务 34][Target3306] - Table "target.wim_customer140" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.764 - [任务 34][Target3306] - The table wim_customer140 has already exist. 
[INFO ] 2025-04-24 14:38:19.802 - [任务 34][Target3306] - Table "target.wim_customer130" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.802 - [任务 34][Target3306] - The table wim_customer130 has already exist. 
[INFO ] 2025-04-24 14:38:19.841 - [任务 34][Target3306] - Table "target.wim_customer120" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.841 - [任务 34][Target3306] - The table wim_customer120 has already exist. 
[INFO ] 2025-04-24 14:38:19.886 - [任务 34][Target3306] - Table "target.wim_customer110" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.886 - [任务 34][Target3306] - The table wim_customer110 has already exist. 
[INFO ] 2025-04-24 14:38:19.921 - [任务 34][Target3306] - Table "target.wim_customer100" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.921 - [任务 34][Target3306] - The table wim_customer100 has already exist. 
[INFO ] 2025-04-24 14:38:19.956 - [任务 34][Target3306] - Table "target.wim_customer10" exists, skip auto create table 
[TRACE] 2025-04-24 14:38:19.956 - [任务 34][Target3306] - The table wim_customer10 has already exist. 
[TRACE] 2025-04-24 14:38:20.256 - [任务 34][Target3306] - Table 'wim_customer140' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-04-24 14:38:20.256 - [任务 34][Target3306] - Table 'wim_customer140' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-04-24 14:38:20.257 - [任务 34][Target3306] - Table 'wim_customer140' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-04-24 14:38:20.257 - [任务 34][Target3306] - Table 'wim_customer140' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-04-24 14:38:20.257 - [任务 34][Target3306] - Table 'wim_customer140' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-04-24 14:38:20.257 - [任务 34][Target3306] - Table 'wim_customer140' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-04-24 14:38:20.257 - [任务 34][Target3306] - Table 'wim_customer140' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-04-24 14:38:20.257 - [任务 34][Target3306] - Table 'wim_customer140' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-04-24 14:38:20.377 - [任务 34][Target3306] - Table 'wim_customer140' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-04-24 14:38:20.386 - [任务 34][Target3306] - Table 'wim_customer140' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-04-24 14:38:20.391 - [任务 34][Target3306] - Table 'wim_customer140' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-04-24 14:38:20.392 - [任务 34][Target3306] - Table 'wim_customer140' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-04-24 14:38:20.392 - [任务 34][Target3306] - Table 'wim_customer140' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-04-24 14:38:20.393 - [任务 34][Target3306] - Table 'wim_customer140' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 13 
[TRACE] 2025-04-24 14:38:20.393 - [任务 34][Target3306] - Table 'wim_customer140' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 14 
[TRACE] 2025-04-24 14:38:20.393 - [任务 34][Target3306] - Table 'wim_customer140' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 14 
[TRACE] 2025-04-24 14:38:20.393 - [任务 34][Target3306] - Table 'wim_customer140' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[TRACE] 2025-04-24 14:38:33.970 - [任务 34][Source3306] - Query snapshot row size completed: Source3306(5420740b-4ad6-484c-a4b1-b576d3dd8134) 
[INFO ] 2025-04-24 14:38:40.871 - [任务 34][Source3306] - Table customer14 has been completed batch read 
[INFO ] 2025-04-24 14:38:40.871 - [任务 34][Source3306] - Starting batch read from table: customer13 
[TRACE] 2025-04-24 14:38:40.871 - [任务 34][Source3306] - Table customer13 is going to be initial synced 
[TRACE] 2025-04-24 14:38:44.060 - [任务 34][Target3306] - Table 'wim_customer130' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-04-24 14:38:44.060 - [任务 34][Target3306] - Table 'wim_customer130' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-04-24 14:38:44.060 - [任务 34][Target3306] - Table 'wim_customer130' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-04-24 14:38:44.061 - [任务 34][Target3306] - Table 'wim_customer130' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-04-24 14:38:44.061 - [任务 34][Target3306] - Table 'wim_customer130' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-04-24 14:38:44.061 - [任务 34][Target3306] - Table 'wim_customer130' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-04-24 14:38:44.061 - [任务 34][Target3306] - Table 'wim_customer130' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-04-24 14:38:44.061 - [任务 34][Target3306] - Table 'wim_customer130' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-04-24 14:38:44.160 - [任务 34][Target3306] - Table 'wim_customer130' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-04-24 14:38:44.185 - [任务 34][Target3306] - Table 'wim_customer130' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-04-24 14:38:44.187 - [任务 34][Target3306] - Table 'wim_customer130' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-04-24 14:38:44.189 - [任务 34][Target3306] - Table 'wim_customer130' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-04-24 14:38:44.190 - [任务 34][Target3306] - Table 'wim_customer130' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-04-24 14:38:44.190 - [任务 34][Target3306] - Table 'wim_customer130' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-04-24 14:38:44.190 - [任务 34][Target3306] - Table 'wim_customer130' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-04-24 14:38:44.190 - [任务 34][Target3306] - Table 'wim_customer130' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-04-24 14:38:44.190 - [任务 34][Target3306] - Table 'wim_customer130' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[TRACE] 2025-04-24 14:38:44.191 - [任务 34][Target3306] - Table 'wim_customer130' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 16 
[INFO ] 2025-04-24 14:39:02.694 - [任务 34][Source3306] - Table customer13 has been completed batch read 
[INFO ] 2025-04-24 14:39:02.696 - [任务 34][Source3306] - Starting batch read from table: customer16 
[TRACE] 2025-04-24 14:39:02.697 - [任务 34][Source3306] - Table customer16 is going to be initial synced 
[TRACE] 2025-04-24 14:39:04.972 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] running status set to false 
[TRACE] 2025-04-24 14:39:04.972 - [任务 34][Source3306] - PDK connector node stopped: HazelcastSourcePdkDataNode_5420740b-4ad6-484c-a4b1-b576d3dd8134_1745476697630 
[TRACE] 2025-04-24 14:39:04.972 - [任务 34][Source3306] - Initial sync completed 
[TRACE] 2025-04-24 14:39:04.973 - [任务 34][Source3306] - PDK connector node released: HazelcastSourcePdkDataNode_5420740b-4ad6-484c-a4b1-b576d3dd8134_1745476697630 
[TRACE] 2025-04-24 14:39:04.973 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] schema data cleaned 
[TRACE] 2025-04-24 14:39:04.974 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] monitor closed 
[TRACE] 2025-04-24 14:39:04.978 - [任务 34][Source3306] - Node Source3306[5420740b-4ad6-484c-a4b1-b576d3dd8134] close complete, cost 12 ms 
[TRACE] 2025-04-24 14:39:04.979 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] running status set to false 
[TRACE] 2025-04-24 14:39:04.979 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] schema data cleaned 
[TRACE] 2025-04-24 14:39:04.980 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] monitor closed 
[TRACE] 2025-04-24 14:39:04.984 - [任务 34][表编辑] - Node 表编辑[024ffb72-4e44-4954-b919-28e843ebc241] close complete, cost 5 ms 
[TRACE] 2025-04-24 14:39:04.985 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] running status set to false 
[TRACE] 2025-04-24 14:39:04.996 - [任务 34][Source3306] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.Exception: Execute steaming query failed, sql: SELECT `CUSTOMER_ID`,`CITY`,`AGE`,`FIRST_NAME`,`LAST_NAME`,`DATE_OF_BIRTH`,`JOB`,`EMAIL`,`ZIP`,`PHONE` FROM `test`.`customer16`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.net.SocketException message given: Socket closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.net.SocketException
MESSAGE: Socket closed

STACKTRACE:

java.net.SocketException: Socket closed
	at java.base/sun.nio.ch.NioSocketImpl.ensureOpenAndConnected(NioSocketImpl.java:165)
	at java.base/sun.nio.ch.NioSocketImpl.available(NioSocketImpl.java:843)
	at java.base/sun.nio.ch.NioSocketImpl$1.available(NioSocketImpl.java:812)
	at java.base/java.net.Socket$SocketInputStream.available(Socket.java:970)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.base/java.io.FilterInputStream.read(FilterInputStream.java:132)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:218)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:793)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:711)
	at io.tapdata.connector.mysql.MysqlConnector.lambda$registerCapabilities$16(MysqlConnector.java:273)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:501)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:493)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:415)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:359)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)


** END NESTED EXCEPTION **

 
[TRACE] 2025-04-24 14:39:04.996 - [任务 34][Target3306] - PDK connector node stopped: HazelcastTargetPdkDataNode_b70a06d6-3948-4a0a-82e3-de9095551300_1745476697513 
[TRACE] 2025-04-24 14:39:04.997 - [任务 34][Target3306] - PDK connector node released: HazelcastTargetPdkDataNode_b70a06d6-3948-4a0a-82e3-de9095551300_1745476697513 
[TRACE] 2025-04-24 14:39:04.997 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] schema data cleaned 
[TRACE] 2025-04-24 14:39:04.998 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] monitor closed 
[TRACE] 2025-04-24 14:39:04.998 - [任务 34][Target3306] - Node Target3306[b70a06d6-3948-4a0a-82e3-de9095551300] close complete, cost 14 ms 
[ERROR] 2025-04-24 14:39:05.200 - [任务 34][Source3306] - Unknown PDK exception occur, java.lang.Exception: Execute steaming query failed, sql: SELECT `CUSTOMER_ID`,`CITY`,`AGE`,`FIRST_NAME`,`LAST_NAME`,`DATE_OF_BIRTH`,`JOB`,`EMAIL`,`ZIP`,`PHONE` FROM `test`.`customer16`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.net.SocketException message given: Socket closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.net.SocketException
MESSAGE: Socket closed

STACKTRACE:

java.net.SocketException: Socket closed
	at java.base/sun.nio.ch.NioSocketImpl.ensureOpenAndConnected(NioSocketImpl.java:165)
	at java.base/sun.nio.ch.NioSocketImpl.available(NioSocketImpl.java:843)
	at java.base/sun.nio.ch.NioSocketImpl$1.available(NioSocketImpl.java:812)
	at java.base/java.net.Socket$SocketInputStream.available(Socket.java:970)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.base/java.io.FilterInputStream.read(FilterInputStream.java:132)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:218)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:793)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:711)
	at io.tapdata.connector.mysql.MysqlConnector.lambda$registerCapabilities$16(MysqlConnector.java:273)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:501)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:493)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:415)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:359)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)


** END NESTED EXCEPTION **

 <-- Error Message -->
Unknown PDK exception occur, java.lang.Exception: Execute steaming query failed, sql: SELECT `CUSTOMER_ID`,`CITY`,`AGE`,`FIRST_NAME`,`LAST_NAME`,`DATE_OF_BIRTH`,`JOB`,`EMAIL`,`ZIP`,`PHONE` FROM `test`.`customer16`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.net.SocketException message given: Socket closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.net.SocketException
MESSAGE: Socket closed

STACKTRACE:

java.net.SocketException: Socket closed
	at java.base/sun.nio.ch.NioSocketImpl.ensureOpenAndConnected(NioSocketImpl.java:165)
	at java.base/sun.nio.ch.NioSocketImpl.available(NioSocketImpl.java:843)
	at java.base/sun.nio.ch.NioSocketImpl$1.available(NioSocketImpl.java:812)
	at java.base/java.net.Socket$SocketInputStream.available(Socket.java:970)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.base/java.io.FilterInputStream.read(FilterInputStream.java:132)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:218)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:793)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:711)
	at io.tapdata.connector.mysql.MysqlConnector.lambda$registerCapabilities$16(MysqlConnector.java:273)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:501)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:493)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:415)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:359)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)


** END NESTED EXCEPTION **



<-- Simple Stack Trace -->
Caused by: java.net.SocketException: Socket closed
	java.base/sun.nio.ch.NioSocketImpl.ensureOpenAndConnected(NioSocketImpl.java:165)
	java.base/sun.nio.ch.NioSocketImpl.available(NioSocketImpl.java:843)
	java.base/sun.nio.ch.NioSocketImpl$1.available(NioSocketImpl.java:812)
	java.base/java.net.Socket$SocketInputStream.available(Socket.java:970)
	com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	...

<-- Full Stack Trace -->
java.lang.Exception: Execute steaming query failed, sql: SELECT `CUSTOMER_ID`,`CITY`,`AGE`,`FIRST_NAME`,`LAST_NAME`,`DATE_OF_BIRTH`,`JOB`,`EMAIL`,`ZIP`,`PHONE` FROM `test`.`customer16`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.net.SocketException message given: Socket closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.net.SocketException
MESSAGE: Socket closed

STACKTRACE:

java.net.SocketException: Socket closed
	at java.base/sun.nio.ch.NioSocketImpl.ensureOpenAndConnected(NioSocketImpl.java:165)
	at java.base/sun.nio.ch.NioSocketImpl.available(NioSocketImpl.java:843)
	at java.base/sun.nio.ch.NioSocketImpl$1.available(NioSocketImpl.java:812)
	at java.base/java.net.Socket$SocketInputStream.available(Socket.java:970)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.base/java.io.FilterInputStream.read(FilterInputStream.java:132)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:218)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:793)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:711)
	at io.tapdata.connector.mysql.MysqlConnector.lambda$registerCapabilities$16(MysqlConnector.java:273)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:501)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:493)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:415)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:359)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)


** END NESTED EXCEPTION **


	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:501)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:493)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:415)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:359)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `CUSTOMER_ID`,`CITY`,`AGE`,`FIRST_NAME`,`LAST_NAME`,`DATE_OF_BIRTH`,`JOB`,`EMAIL`,`ZIP`,`PHONE` FROM `test`.`customer16`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.net.SocketException message given: Socket closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.net.SocketException
MESSAGE: Socket closed

STACKTRACE:

java.net.SocketException: Socket closed
	at java.base/sun.nio.ch.NioSocketImpl.ensureOpenAndConnected(NioSocketImpl.java:165)
	at java.base/sun.nio.ch.NioSocketImpl.available(NioSocketImpl.java:843)
	at java.base/sun.nio.ch.NioSocketImpl$1.available(NioSocketImpl.java:812)
	at java.base/java.net.Socket$SocketInputStream.available(Socket.java:970)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.base/java.io.FilterInputStream.read(FilterInputStream.java:132)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:218)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:793)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:711)
	at io.tapdata.connector.mysql.MysqlConnector.lambda$registerCapabilities$16(MysqlConnector.java:273)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:501)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:493)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:415)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:359)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)


** END NESTED EXCEPTION **


	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:220)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:793)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:711)
	at io.tapdata.connector.mysql.MysqlConnector.lambda$registerCapabilities$16(MysqlConnector.java:273)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 19 more
Caused by: java.sql.SQLException: Error retrieving record: Unexpected Exception: java.net.SocketException message given: Socket closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.net.SocketException
MESSAGE: Socket closed

STACKTRACE:

java.net.SocketException: Socket closed
	at java.base/sun.nio.ch.NioSocketImpl.ensureOpenAndConnected(NioSocketImpl.java:165)
	at java.base/sun.nio.ch.NioSocketImpl.available(NioSocketImpl.java:843)
	at java.base/sun.nio.ch.NioSocketImpl$1.available(NioSocketImpl.java:812)
	at java.base/java.net.Socket$SocketInputStream.available(Socket.java:970)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.base/java.io.FilterInputStream.read(FilterInputStream.java:132)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:218)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:793)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:711)
	at io.tapdata.connector.mysql.MysqlConnector.lambda$registerCapabilities$16(MysqlConnector.java:273)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:501)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:493)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:415)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:359)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)


** END NESTED EXCEPTION **


	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:131)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1952)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:218)
	... 24 more
Caused by: java.net.SocketException: Socket closed
	at java.base/sun.nio.ch.NioSocketImpl.ensureOpenAndConnected(NioSocketImpl.java:165)
	at java.base/sun.nio.ch.NioSocketImpl.available(NioSocketImpl.java:843)
	at java.base/sun.nio.ch.NioSocketImpl$1.available(NioSocketImpl.java:812)
	at java.base/java.net.Socket$SocketInputStream.available(Socket.java:970)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.base/java.io.FilterInputStream.read(FilterInputStream.java:132)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	... 27 more

[TRACE] 2025-04-24 14:39:05.823 - [任务 34] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-24 14:39:05.823 - [任务 34] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3544f1ae 
[TRACE] 2025-04-24 14:39:05.964 - [任务 34] - Stop task milestones: 6806fc0c4f6eac093479ec2a(任务 34)  
[TRACE] 2025-04-24 14:39:05.964 - [任务 34] - Stopped task aspect(s) 
[TRACE] 2025-04-24 14:39:05.964 - [任务 34] - Snapshot order controller have been removed 
[INFO ] 2025-04-24 14:39:05.964 - [任务 34] - Task stopped. 
[TRACE] 2025-04-24 14:39:10.977 - [任务 34] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-24 14:39:10.977 - [任务 34] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3544f1ae 
[TRACE] 2025-04-24 14:39:10.977 - [任务 34] - Stopped task aspect(s) 
[INFO ] 2025-04-24 14:39:10.977 - [任务 34] - Task stopped. 
[TRACE] 2025-04-24 14:39:11.032 - [任务 34] - Remove memory task client succeed, task: 任务 34[6806fc0c4f6eac093479ec2a] 
[TRACE] 2025-04-24 14:39:11.033 - [任务 34] - Destroy memory task client cache succeed, task: 任务 34[6806fc0c4f6eac093479ec2a] 
