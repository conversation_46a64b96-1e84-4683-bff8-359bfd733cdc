[TRACE] 2025-03-11 11:46:00.524 - [任务 44] - Task initialization... 
[TRACE] 2025-03-11 11:46:00.525 - [任务 44] - Start task milestones: 67cfb1bc0f0fca3a975cf266(任务 44) 
[INFO ] 2025-03-11 11:46:00.730 - [任务 44] - Loading table structure completed 
[TRACE] 2025-03-11 11:46:00.808 - [任务 44] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-03-11 11:46:00.808 - [任务 44] - The engine receives 任务 44 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-11 11:46:00.853 - [任务 44] - Task started 
[TRACE] 2025-03-11 11:46:00.853 - [任务 44][Test7] - Node Test7[11e4c5e6-d169-4dba-a2f2-81b25c16f4fa] start preload schema,table counts: 4 
[TRACE] 2025-03-11 11:46:00.853 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Node Sybase LAB_DB_9 INT DB - Copy[90cabf61-3c8e-4b1a-8350-fd066f5f5303] start preload schema,table counts: 4 
[TRACE] 2025-03-11 11:46:00.854 - [任务 44][Test7] - Node Test7[11e4c5e6-d169-4dba-a2f2-81b25c16f4fa] preload schema finished, cost 0 ms 
[TRACE] 2025-03-11 11:46:00.854 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Node Sybase LAB_DB_9 INT DB - Copy[90cabf61-3c8e-4b1a-8350-fd066f5f5303] preload schema finished, cost 1 ms 
[INFO ] 2025-03-11 11:46:01.657 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Source connector(Sybase LAB_DB_9 INT DB - Copy) initialization completed 
[TRACE] 2025-03-11 11:46:01.657 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Source node "Sybase LAB_DB_9 INT DB - Copy" read batch size: 100 
[TRACE] 2025-03-11 11:46:01.657 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Source node "Sybase LAB_DB_9 INT DB - Copy" event queue capacity: 200 
[TRACE] 2025-03-11 11:46:01.657 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-03-11 11:46:01.658 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='COM_DB') 
[INFO ] 2025-03-11 11:46:01.782 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - logs holder exists, will skip valid operation 
[INFO ] 2025-03-11 11:46:01.845 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-03-11 11:46:01.846 - [任务 44][Test7] - Sink connector(Test7) initialization completed 
[TRACE] 2025-03-11 11:46:01.846 - [任务 44][Test7] - Node(Test7) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-11 11:46:01.846 - [任务 44][Test7] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-11 11:46:01.892 - [任务 44][Test7] - Apply table structure to target database 
[INFO ] 2025-03-11 11:46:01.895 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Starting batch read from 4 tables 
[TRACE] 2025-03-11 11:46:01.909 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Initial sync started 
[INFO ] 2025-03-11 11:46:01.915 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Starting batch read from table: als_config 
[TRACE] 2025-03-11 11:46:01.916 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Table als_config is going to be initial synced 
[WARN ] 2025-03-11 11:46:02.112 - [任务 44][Test7] - Table als_config not exists, skip drop 
[WARN ] 2025-03-11 11:46:02.112 - [任务 44][Test7] - Table als_sysout_log_filter not exists, skip drop 
[INFO ] 2025-03-11 11:46:02.383 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Table als_config has been completed batch read 
[INFO ] 2025-03-11 11:46:02.384 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Starting batch read from table: als_sysout_log_filter 
[TRACE] 2025-03-11 11:46:02.384 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Table als_sysout_log_filter is going to be initial synced 
[WARN ] 2025-03-11 11:46:02.446 - [任务 44][Test7] - Create index failed ERROR: must be owner of table als_sysout_log_filter, please execute it manually [create unique index "als_sysout_log_filter_CIdx" on "test7"."als_sysout_log_filter"("location" asc,"workstation_id" asc,"user_id" asc,"function_id" asc,"log_month" asc,"log_day" asc,"log_weekday" asc,"log_hour" asc,"log_minute" asc,"log_priority" asc)] 
[WARN ] 2025-03-11 11:46:02.447 - [任务 44][Test7] - Table access_control not exists, skip drop 
[INFO ] 2025-03-11 11:46:02.559 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Table als_sysout_log_filter has been completed batch read 
[INFO ] 2025-03-11 11:46:02.559 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Starting batch read from table: access_control 
[TRACE] 2025-03-11 11:46:02.704 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Table access_control is going to be initial synced 
[WARN ] 2025-03-11 11:46:02.704 - [任务 44][Test7] - Create index failed ERROR: must be owner of table access_control, please execute it manually [create unique index "access_control_uIdx" on "test7"."access_control"("method" asc,"screen" asc)] 
[WARN ] 2025-03-11 11:46:02.905 - [任务 44][Test7] - Table als_log_filter not exists, skip drop 
[INFO ] 2025-03-11 11:46:03.271 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Table access_control has been completed batch read 
[INFO ] 2025-03-11 11:46:03.272 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Starting batch read from table: als_log_filter 
[TRACE] 2025-03-11 11:46:03.272 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Table als_log_filter is going to be initial synced 
[TRACE] 2025-03-11 11:46:03.381 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Query snapshot row size completed: Sybase LAB_DB_9 INT DB - Copy(90cabf61-3c8e-4b1a-8350-fd066f5f5303) 
[WARN ] 2025-03-11 11:46:03.382 - [任务 44][Test7] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67cfb1fade1a8e614aa229e4, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[90cabf61-3c8e-4b1a-8350-fd066f5f5303], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-03-11 11:46:03.387 - [任务 44][Test7] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67cfb1fade1a8e614aa229e5, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[90cabf61-3c8e-4b1a-8350-fd066f5f5303], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-03-11 11:46:03.390 - [任务 44][Test7] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67cfb1fbde1a8e614aa229e6, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[90cabf61-3c8e-4b1a-8350-fd066f5f5303], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2025-03-11 11:46:03.531 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Table als_log_filter has been completed batch read 
[TRACE] 2025-03-11 11:46:03.533 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Initial sync completed 
[INFO ] 2025-03-11 11:46:03.533 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Batch read completed. 
[TRACE] 2025-03-11 11:46:03.533 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Incremental sync starting... 
[TRACE] 2025-03-11 11:46:03.533 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Initial sync completed 
[TRACE] 2025-03-11 11:46:03.554 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Starting stream read, table list: [als_config, als_sysout_log_filter, access_control, als_log_filter], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-03-11 11:46:03.556 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Starting incremental sync using database log parser 
[INFO ] 2025-03-11 11:46:03.755 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - startRid: 270699, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-11 11:46:03.755 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-11 11:46:03.755 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - sybase offset in database is: startRid: 270699, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-03-11 11:46:03.801 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - we will use offset in database, how ever, this is safe: startRid: 270699, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-11 11:46:03.802 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - sybase cdc work with mode v1: auto rescan 
[WARN ] 2025-03-11 11:46:03.952 - [任务 44][Test7] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67cfb1fbde1a8e614aa229e7, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[90cabf61-3c8e-4b1a-8350-fd066f5f5303], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-03-11 11:46:03.952 - [任务 44][Test7] - Exception skipping - The current exception does not match the skip exception strategy, message: org.postgresql.util.PSQLException: ERROR: must be owner of table access_control 
[ERROR] 2025-03-11 11:46:03.973 - [任务 44][Test7] - org.postgresql.util.PSQLException: ERROR: must be owner of table access_control <-- Error Message -->
org.postgresql.util.PSQLException: ERROR: must be owner of table access_control

<-- Simple Stack Trace -->
Caused by: org.postgresql.util.PSQLException: ERROR: must be owner of table access_control
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...

<-- Full Stack Trace -->
org.postgresql.util.PSQLException: ERROR: must be owner of table access_control
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processConnectorAfterSnapshot(HazelcastTargetPdkDataNode.java:1323)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processConnectorAfterSnapshot$42(HazelcastTargetPdkBaseNode.java:1822)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1632)
	at java.util.concurrent.ForkJoinTask.doExec$$$capture(ForkJoinTask.java:289)
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java)
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1056)
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1692)
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:175)
Caused by: org.postgresql.util.PSQLException: ERROR: must be owner of table access_control
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:329)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:315)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:291)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:286)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.execute(JdbcContext.java:155)
	at io.tapdata.connector.postgres.PostgresConnector.openIdentity(PostgresConnector.java:384)
	at io.tapdata.connector.postgres.PostgresConnector.beforeWriteRecord(PostgresConnector.java:402)
	at io.tapdata.connector.postgres.PostgresConnector.afterInitialSync(PostgresConnector.java:467)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processConnectorAfterSnapshot(HazelcastTargetPdkDataNode.java:1321)
	... 9 more

[TRACE] 2025-03-11 11:46:03.975 - [任务 44][Test7] - Job suspend in error handle 
[TRACE] 2025-03-11 11:46:03.994 - [任务 44][Test7] - Process after table "access_control" initial sync finished, cost: 79 ms 
[TRACE] 2025-03-11 11:46:03.994 - [任务 44][Test7] - Process after table "als_config" initial sync finished, cost: 97 ms 
[TRACE] 2025-03-11 11:46:04.023 - [任务 44][Test7] - Process after table "als_log_filter" initial sync finished, cost: 100 ms 
[TRACE] 2025-03-11 11:46:04.023 - [任务 44][Test7] - Exception skipping - The current exception does not match the skip exception strategy, message: org.postgresql.util.PSQLException: ERROR: must be owner of table als_sysout_log_filter 
[TRACE] 2025-03-11 11:46:04.023 - [任务 44][Test7] - Process after table "als_sysout_log_filter" initial sync finished, cost: 125 ms 
[INFO ] 2025-03-11 11:46:04.023 - [任务 44][Test7] - Process after all table(s) initial sync are finished，table number: 4 
[TRACE] 2025-03-11 11:46:04.085 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Node Sybase LAB_DB_9 INT DB - Copy[90cabf61-3c8e-4b1a-8350-fd066f5f5303] running status set to false 
[INFO ] 2025-03-11 11:46:04.085 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Log Miner is shutting down... 
[INFO ] 2025-03-11 11:46:04.211 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - sp_config_rep_agent disabled, database: COM_DB 
[INFO ] 2025-03-11 11:46:04.212 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - turned off automatic log cleaning for sybase 
[INFO ] 2025-03-11 11:46:04.300 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - opened cdc for tables: {dbo=[als_config, als_sysout_log_filter, access_control, als_log_filter]} 
[INFO ] 2025-03-11 11:46:04.300 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='COM_DB') 
[TRACE] 2025-03-11 11:46:04.361 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_90cabf61-3c8e-4b1a-8350-fd066f5f5303_1741664761562 
[TRACE] 2025-03-11 11:46:04.361 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_90cabf61-3c8e-4b1a-8350-fd066f5f5303_1741664761562 
[TRACE] 2025-03-11 11:46:04.361 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Node Sybase LAB_DB_9 INT DB - Copy[90cabf61-3c8e-4b1a-8350-fd066f5f5303] schema data cleaned 
[TRACE] 2025-03-11 11:46:04.361 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Node Sybase LAB_DB_9 INT DB - Copy[90cabf61-3c8e-4b1a-8350-fd066f5f5303] monitor closed 
[TRACE] 2025-03-11 11:46:04.378 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Node Sybase LAB_DB_9 INT DB - Copy[90cabf61-3c8e-4b1a-8350-fd066f5f5303] close complete, cost 287 ms 
[TRACE] 2025-03-11 11:46:04.379 - [任务 44][Test7] - Node Test7[11e4c5e6-d169-4dba-a2f2-81b25c16f4fa] running status set to false 
[TRACE] 2025-03-11 11:46:04.384 - [任务 44][Test7] - PDK connector node stopped: HazelcastTargetPdkDataNode_11e4c5e6-d169-4dba-a2f2-81b25c16f4fa_1741664761464 
[TRACE] 2025-03-11 11:46:04.384 - [任务 44][Test7] - PDK connector node released: HazelcastTargetPdkDataNode_11e4c5e6-d169-4dba-a2f2-81b25c16f4fa_1741664761464 
[TRACE] 2025-03-11 11:46:04.384 - [任务 44][Test7] - Node Test7[11e4c5e6-d169-4dba-a2f2-81b25c16f4fa] schema data cleaned 
[TRACE] 2025-03-11 11:46:04.385 - [任务 44][Test7] - Node Test7[11e4c5e6-d169-4dba-a2f2-81b25c16f4fa] monitor closed 
[TRACE] 2025-03-11 11:46:04.497 - [任务 44][Test7] - Node Test7[11e4c5e6-d169-4dba-a2f2-81b25c16f4fa] close complete, cost 15 ms 
[INFO ] 2025-03-11 11:46:04.500 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - logs holder exists, will skip valid operation 
[INFO ] 2025-03-11 11:46:05.100 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - trans timestamp offset: 28800000 
[INFO ] 2025-03-11 11:46:05.100 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - sybase cdc debug log is disabled 
[INFO ] 2025-03-11 11:46:05.100 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:46:05.102 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:05.336 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:46:05.473 - [任务 44] - Task [任务 44] cannot retry, reason: Task retry service not start 
[TRACE] 2025-03-11 11:46:05.474 - [任务 44] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-11 11:46:05.474 - [任务 44] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@66d6a3b 
[TRACE] 2025-03-11 11:46:05.609 - [任务 44] - Stop task milestones: 67cfb1bc0f0fca3a975cf266(任务 44)  
[TRACE] 2025-03-11 11:46:05.630 - [任务 44] - Stopped task aspect(s) 
[TRACE] 2025-03-11 11:46:05.630 - [任务 44] - Snapshot order controller have been removed 
[INFO ] 2025-03-11 11:46:05.630 - [任务 44] - Task stopped. 
[TRACE] 2025-03-11 11:46:05.653 - [任务 44] - Remove memory task client succeed, task: 任务 44[67cfb1bc0f0fca3a975cf266] 
[TRACE] 2025-03-11 11:46:05.654 - [任务 44] - Destroy memory task client cache succeed, task: 任务 44[67cfb1bc0f0fca3a975cf266] 
[INFO ] 2025-03-11 11:46:08.487 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:08.689 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:46:11.731 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:11.832 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:46:14.838 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:15.248 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:46:18.300 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:18.506 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:46:21.327 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:21.444 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:46:24.448 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:24.650 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:46:27.683 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:27.684 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:46:30.688 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:30.890 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:46:33.954 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:34.159 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:46:36.969 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:37.062 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:46:40.067 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:40.273 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:46:43.261 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:43.262 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:46:46.291 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:46.494 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:46:49.545 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:49.751 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:46:52.592 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:52.798 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[TRACE] 2025-03-11 11:46:55.273 - [任务 44] - Task initialization... 
[TRACE] 2025-03-11 11:46:55.273 - [任务 44] - Start task milestones: 67cfb1bc0f0fca3a975cf266(任务 44) 
[INFO ] 2025-03-11 11:46:55.422 - [任务 44] - Loading table structure completed 
[TRACE] 2025-03-11 11:46:55.422 - [任务 44] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-11 11:46:55.468 - [任务 44] - The engine receives 任务 44 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-11 11:46:55.468 - [任务 44] - Task started 
[TRACE] 2025-03-11 11:46:55.497 - [任务 44][Test7] - Node Test7[11e4c5e6-d169-4dba-a2f2-81b25c16f4fa] start preload schema,table counts: 4 
[TRACE] 2025-03-11 11:46:55.497 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Node Sybase LAB_DB_9 INT DB - Copy[90cabf61-3c8e-4b1a-8350-fd066f5f5303] start preload schema,table counts: 4 
[TRACE] 2025-03-11 11:46:55.498 - [任务 44][Test7] - Node Test7[11e4c5e6-d169-4dba-a2f2-81b25c16f4fa] preload schema finished, cost 1 ms 
[TRACE] 2025-03-11 11:46:55.498 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Node Sybase LAB_DB_9 INT DB - Copy[90cabf61-3c8e-4b1a-8350-fd066f5f5303] preload schema finished, cost 0 ms 
[INFO ] 2025-03-11 11:46:55.874 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:55.877 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:46:56.330 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Source connector(Sybase LAB_DB_9 INT DB - Copy) initialization completed 
[TRACE] 2025-03-11 11:46:56.331 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Source node "Sybase LAB_DB_9 INT DB - Copy" read batch size: 100 
[TRACE] 2025-03-11 11:46:56.331 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Source node "Sybase LAB_DB_9 INT DB - Copy" event queue capacity: 200 
[TRACE] 2025-03-11 11:46:56.331 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-03-11 11:46:56.421 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='COM_DB') 
[INFO ] 2025-03-11 11:46:56.421 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - logs holder exists, will skip valid operation 
[INFO ] 2025-03-11 11:46:56.437 - [任务 44][Test7] - Sink connector(Test7) initialization completed 
[INFO ] 2025-03-11 11:46:56.437 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[TRACE] 2025-03-11 11:46:56.437 - [任务 44][Test7] - Node(Test7) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-11 11:46:56.437 - [任务 44][Test7] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-11 11:46:56.438 - [任务 44][Test7] - Apply table structure to target database 
[INFO ] 2025-03-11 11:46:56.519 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Starting batch read from 4 tables 
[TRACE] 2025-03-11 11:46:56.519 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Initial sync started 
[INFO ] 2025-03-11 11:46:56.521 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Starting batch read from table: als_config 
[TRACE] 2025-03-11 11:46:56.521 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Table als_config is going to be initial synced 
[INFO ] 2025-03-11 11:46:56.634 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Table als_config has been completed batch read 
[INFO ] 2025-03-11 11:46:56.634 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Starting batch read from table: als_sysout_log_filter 
[TRACE] 2025-03-11 11:46:56.762 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Table als_sysout_log_filter is going to be initial synced 
[INFO ] 2025-03-11 11:46:56.762 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Table als_sysout_log_filter has been completed batch read 
[INFO ] 2025-03-11 11:46:56.762 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Starting batch read from table: access_control 
[TRACE] 2025-03-11 11:46:56.762 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Table access_control is going to be initial synced 
[INFO ] 2025-03-11 11:46:56.854 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Table access_control has been completed batch read 
[INFO ] 2025-03-11 11:46:56.854 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Starting batch read from table: als_log_filter 
[TRACE] 2025-03-11 11:46:56.855 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Table als_log_filter is going to be initial synced 
[INFO ] 2025-03-11 11:46:56.958 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Table als_log_filter has been completed batch read 
[TRACE] 2025-03-11 11:46:56.958 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Initial sync completed 
[INFO ] 2025-03-11 11:46:56.959 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Batch read completed. 
[TRACE] 2025-03-11 11:46:56.959 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Incremental sync starting... 
[TRACE] 2025-03-11 11:46:56.959 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Initial sync completed 
[TRACE] 2025-03-11 11:46:56.960 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Starting stream read, table list: [als_config, als_sysout_log_filter, access_control, als_log_filter], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-03-11 11:46:56.960 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Starting incremental sync using database log parser 
[TRACE] 2025-03-11 11:46:57.053 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Query snapshot row size completed: Sybase LAB_DB_9 INT DB - Copy(90cabf61-3c8e-4b1a-8350-fd066f5f5303) 
[INFO ] 2025-03-11 11:46:57.053 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - startRid: 270699, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-11 11:46:57.053 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-11 11:46:57.054 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - sybase offset in database is: startRid: 270699, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-03-11 11:46:57.065 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - we will use offset in database, how ever, this is safe: startRid: 270699, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-11 11:46:57.070 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - sybase cdc work with mode v1: auto rescan 
[INFO ] 2025-03-11 11:46:57.298 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - sp_config_rep_agent disabled, database: COM_DB 
[INFO ] 2025-03-11 11:46:57.298 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - turned off automatic log cleaning for sybase 
[INFO ] 2025-03-11 11:46:57.339 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - opened cdc for tables: {dbo=[als_config, als_sysout_log_filter, access_control, als_log_filter]} 
[INFO ] 2025-03-11 11:46:57.339 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='COM_DB') 
[INFO ] 2025-03-11 11:46:57.540 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - logs holder exists, will skip valid operation 
[INFO ] 2025-03-11 11:46:57.661 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - trans timestamp offset: 28800000 
[INFO ] 2025-03-11 11:46:57.661 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - sybase cdc debug log is disabled 
[INFO ] 2025-03-11 11:46:57.661 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[WARN ] 2025-03-11 11:46:57.978 - [任务 44][Test7] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67cfb230de1a8e614aa22a17, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[90cabf61-3c8e-4b1a-8350-fd066f5f5303], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-03-11 11:46:57.979 - [任务 44][Test7] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67cfb230de1a8e614aa22a18, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[90cabf61-3c8e-4b1a-8350-fd066f5f5303], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-03-11 11:46:57.979 - [任务 44][Test7] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67cfb230de1a8e614aa22a19, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[90cabf61-3c8e-4b1a-8350-fd066f5f5303], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-03-11 11:46:57.979 - [任务 44][Test7] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67cfb230de1a8e614aa22a1a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[90cabf61-3c8e-4b1a-8350-fd066f5f5303], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-03-11 11:46:58.031 - [任务 44][Test7] - Process after table "als_config" initial sync finished, cost: 66 ms 
[TRACE] 2025-03-11 11:46:58.032 - [任务 44][Test7] - Process after table "als_log_filter" initial sync finished, cost: 79 ms 
[TRACE] 2025-03-11 11:46:58.107 - [任务 44][Test7] - Process after table "als_sysout_log_filter" initial sync finished, cost: 126 ms 
[TRACE] 2025-03-11 11:46:58.107 - [任务 44][Test7] - Process after table "access_control" initial sync finished, cost: 156 ms 
[INFO ] 2025-03-11 11:46:58.107 - [任务 44][Test7] - Process after all table(s) initial sync are finished，table number: 4 
[INFO ] 2025-03-11 11:46:58.880 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:46:58.994 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:00.782 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:00.783 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:01.998 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:02.128 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:03.921 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:03.924 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:05.139 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:05.325 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:06.927 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:07.132 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:08.327 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:08.531 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:10.149 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:10.150 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:11.534 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:11.534 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:13.152 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:13.358 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:14.540 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:14.748 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:16.349 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:16.350 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:17.741 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:17.742 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:19.353 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:19.468 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:20.744 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:20.886 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:22.473 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:22.572 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:23.989 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:24.194 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:25.614 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:25.820 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:27.000 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:27.180 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:28.807 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:29.010 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:30.183 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:30.278 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:31.898 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:32.101 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:33.284 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:33.423 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:35.048 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:35.251 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:36.433 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:36.555 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:38.184 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:38.392 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:39.560 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:39.687 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:41.320 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:41.526 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:42.691 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:42.894 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:44.523 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:44.725 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:45.913 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:45.913 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:47.698 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:47.698 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:49.120 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:49.326 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:50.701 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:51.094 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:52.305 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:52.506 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:54.098 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:54.238 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:55.460 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:55.666 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:57.241 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:57.330 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:47:58.546 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:47:58.752 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:00.339 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:00.545 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:01.763 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:01.966 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:03.466 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:03.672 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:04.840 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:05.046 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:06.676 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:06.882 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:07.995 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:08.199 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:09.826 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:09.944 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:11.155 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:11.336 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:12.953 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:13.202 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:14.337 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:14.524 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:16.356 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:16.357 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:17.529 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:17.650 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:19.484 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:19.686 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:20.652 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:20.805 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:22.614 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:22.615 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:23.809 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:24.016 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:25.620 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:25.743 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:26.919 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:27.025 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:28.866 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:29.073 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:30.091 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:30.293 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:31.877 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:32.029 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:33.243 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:33.449 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:35.035 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:35.238 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:36.456 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:36.662 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:38.162 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:38.368 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:39.585 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:39.791 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:41.285 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:41.692 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:42.706 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:42.912 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:44.634 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:44.838 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:45.757 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:45.963 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:47.741 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:47.943 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:48.967 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:49.172 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:50.863 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:51.071 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:52.017 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:52.423 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:53.990 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:54.175 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:55.378 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:55.378 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:57.180 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:57.309 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:48:58.492 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:48:58.493 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:00.314 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:00.463 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:01.612 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:01.613 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:03.582 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:03.583 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:04.767 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:04.768 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:06.587 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:06.701 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:07.893 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:07.894 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:09.708 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:09.879 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:10.896 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:11.101 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:12.884 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:13.091 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:14.016 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:14.219 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:16.042 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:16.248 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:17.224 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:17.430 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:19.178 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:19.383 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:20.321 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:20.728 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:22.358 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:22.564 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:23.537 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:23.741 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:25.575 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:25.782 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:26.789 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:26.789 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:28.782 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:28.783 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:29.790 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:29.923 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:31.914 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:31.915 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - uncommit trans size: 0 
[INFO ] 2025-03-11 11:49:31.915 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - uncommit trans: {} 
[INFO ] 2025-03-11 11:49:32.121 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:49:32.933 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:33.046 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:35.074 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:49:35.281 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:49:36.049 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:36.234 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:38.265 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:49:38.470 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:49:39.244 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:39.343 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:41.378 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:49:41.581 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:49:42.347 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:42.465 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:44.503 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:49:44.707 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:49:45.470 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:45.673 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:47.639 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:49:47.844 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:49:48.601 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:48.806 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:50.787 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:49:50.979 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:49:51.733 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:52.140 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:53.985 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:49:54.392 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:49:54.945 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:55.056 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:49:57.232 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:49:57.337 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:49:58.159 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:49:58.366 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:50:00.346 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:00.500 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:50:01.318 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:50:01.530 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:50:03.507 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:03.640 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:50:04.456 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:50:04.663 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:50:06.642 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:06.806 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:50:07.624 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:50:07.834 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:50:09.812 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:10.228 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:50:10.789 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:50:10.789 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[INFO ] 2025-03-11 11:50:13.223 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:13.224 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:50:13.794 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:50:13.899 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270699, rowId: 0 
[TRACE] 2025-03-11 11:50:15.816 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Node Sybase LAB_DB_9 INT DB - Copy[90cabf61-3c8e-4b1a-8350-fd066f5f5303] running status set to false 
[INFO ] 2025-03-11 11:50:15.816 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:15.816 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Log Miner is shutting down... 
[TRACE] 2025-03-11 11:50:15.991 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Incremental sync completed 
[TRACE] 2025-03-11 11:50:15.991 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode_90cabf61-3c8e-4b1a-8350-fd066f5f5303_1741664816218 
[TRACE] 2025-03-11 11:50:15.992 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - PDK connector node released: HazelcastSourcePdkDataNode_90cabf61-3c8e-4b1a-8350-fd066f5f5303_1741664816218 
[TRACE] 2025-03-11 11:50:15.992 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Node Sybase LAB_DB_9 INT DB - Copy[90cabf61-3c8e-4b1a-8350-fd066f5f5303] schema data cleaned 
[TRACE] 2025-03-11 11:50:15.992 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Node Sybase LAB_DB_9 INT DB - Copy[90cabf61-3c8e-4b1a-8350-fd066f5f5303] monitor closed 
[TRACE] 2025-03-11 11:50:15.995 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - Node Sybase LAB_DB_9 INT DB - Copy[90cabf61-3c8e-4b1a-8350-fd066f5f5303] close complete, cost 181 ms 
[TRACE] 2025-03-11 11:50:16.007 - [任务 44][Test7] - Node Test7[11e4c5e6-d169-4dba-a2f2-81b25c16f4fa] running status set to false 
[TRACE] 2025-03-11 11:50:16.007 - [任务 44][Test7] - PDK connector node stopped: HazelcastTargetPdkDataNode_11e4c5e6-d169-4dba-a2f2-81b25c16f4fa_1741664816137 
[TRACE] 2025-03-11 11:50:16.007 - [任务 44][Test7] - PDK connector node released: HazelcastTargetPdkDataNode_11e4c5e6-d169-4dba-a2f2-81b25c16f4fa_1741664816137 
[TRACE] 2025-03-11 11:50:16.007 - [任务 44][Test7] - Node Test7[11e4c5e6-d169-4dba-a2f2-81b25c16f4fa] schema data cleaned 
[TRACE] 2025-03-11 11:50:16.007 - [任务 44][Test7] - Node Test7[11e4c5e6-d169-4dba-a2f2-81b25c16f4fa] monitor closed 
[TRACE] 2025-03-11 11:50:16.215 - [任务 44][Test7] - Node Test7[11e4c5e6-d169-4dba-a2f2-81b25c16f4fa] close complete, cost 12 ms 
[INFO ] 2025-03-11 11:50:16.910 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270699, 0 
[INFO ] 2025-03-11 11:50:17.108 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - uncommit trans size: 0 
[INFO ] 2025-03-11 11:50:17.109 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - uncommit trans: {} 
[INFO ] 2025-03-11 11:50:17.315 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:50:20.364 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:20.395 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[TRACE] 2025-03-11 11:50:20.900 - [任务 44] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-11 11:50:20.901 - [任务 44] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2bc26ea3 
[TRACE] 2025-03-11 11:50:21.041 - [任务 44] - Stop task milestones: 67cfb1bc0f0fca3a975cf266(任务 44)  
[TRACE] 2025-03-11 11:50:21.041 - [任务 44] - Stopped task aspect(s) 
[TRACE] 2025-03-11 11:50:21.041 - [任务 44] - Snapshot order controller have been removed 
[INFO ] 2025-03-11 11:50:21.041 - [任务 44] - Task stopped. 
[TRACE] 2025-03-11 11:50:21.074 - [任务 44] - Remove memory task client succeed, task: 任务 44[67cfb1bc0f0fca3a975cf266] 
[TRACE] 2025-03-11 11:50:21.083 - [任务 44] - Destroy memory task client cache succeed, task: 任务 44[67cfb1bc0f0fca3a975cf266] 
[INFO ] 2025-03-11 11:50:23.511 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:23.716 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:50:26.773 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:26.978 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:50:29.781 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:30.020 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:50:33.026 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:33.179 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:50:36.186 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:36.388 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:50:39.447 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:39.447 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:50:42.457 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:42.565 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:50:45.572 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:45.778 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:50:48.829 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:49.035 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:50:52.092 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:52.380 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:50:55.387 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:56.596 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:50:59.642 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:50:59.844 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:02.901 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:02.988 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:06.020 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:06.211 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:09.246 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:09.857 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:12.901 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:13.105 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:15.935 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:16.053 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:19.059 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:19.215 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:22.247 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:22.449 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:25.493 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:25.493 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:28.499 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:28.612 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:31.620 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:31.753 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:34.759 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:34.962 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:38.008 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:38.008 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:41.051 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:41.257 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:44.316 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:44.528 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:47.339 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:47.500 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:50.501 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:50.632 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:53.753 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:53.753 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:56.763 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:51:56.971 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:51:59.899 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:00.092 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:03.097 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:03.202 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:06.210 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:06.416 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:09.448 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:09.652 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:12.546 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:12.749 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:15.616 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:15.759 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:18.823 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:19.025 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:22.018 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:22.019 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:25.024 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:25.226 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:28.240 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:28.240 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:31.246 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:31.452 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:34.480 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:34.480 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:37.487 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:37.693 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:40.739 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:40.942 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:43.762 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:43.833 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:46.837 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:47.039 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:50.064 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:50.064 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:53.067 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:53.196 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:56.233 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:56.439 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:52:59.427 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:52:59.428 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:02.431 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:02.636 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:05.637 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:05.638 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:08.642 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:08.848 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:11.900 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:11.901 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:14.905 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:15.033 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:18.093 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:18.297 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:21.145 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:21.249 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:24.253 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:24.455 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:27.469 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:27.469 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:30.473 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:30.678 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:33.684 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:33.685 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:36.689 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:36.892 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:39.897 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:39.898 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:42.902 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:43.104 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:46.161 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:46.162 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:49.167 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:49.266 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:52.271 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:52.477 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:55.525 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:55.730 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:53:58.754 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:53:58.754 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:01.760 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:01.891 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:04.897 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:05.006 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:08.014 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:08.218 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:11.267 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:11.468 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:14.296 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:14.393 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:17.400 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:17.606 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:20.593 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:20.593 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:23.595 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:23.797 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:26.823 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:26.823 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:29.836 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:30.040 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:33.070 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:33.071 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:36.074 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:36.215 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:39.222 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:39.366 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:42.464 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:42.465 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:45.468 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:45.673 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:48.699 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:48.699 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:51.704 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:51.911 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:54.934 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:54.935 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:54:57.940 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:54:58.068 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:01.074 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:01.218 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:04.225 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:04.431 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:07.440 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:07.440 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:10.447 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:10.652 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:13.663 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:13.664 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:16.670 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:16.874 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:19.881 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:19.881 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:22.885 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:23.089 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:26.144 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:26.192 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:29.196 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:29.304 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:32.307 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:32.510 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:35.559 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:35.765 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:38.602 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:38.748 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:41.756 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:41.963 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:45.012 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:45.218 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:48.034 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:48.210 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:51.217 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:51.335 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:54.380 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:54.584 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:55:57.601 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:55:57.602 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:00.606 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:00.723 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:03.726 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:03.865 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:06.872 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:07.278 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:10.321 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:10.524 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:13.366 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:13.570 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:16.544 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:16.545 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:19.547 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:19.754 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:22.793 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:22.793 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:25.797 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:25.934 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:28.992 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:29.201 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:32.209 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:32.209 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:35.211 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:35.416 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:38.461 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:38.666 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:41.481 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:41.623 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:44.630 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:44.759 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:47.764 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:47.910 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:50.917 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:51.119 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:54.156 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:54.156 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:56:57.156 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:56:57.358 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:00.389 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:00.390 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:03.393 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:03.798 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:06.621 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:06.720 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:09.761 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:09.967 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:12.960 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:12.961 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:15.967 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:16.173 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:19.228 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:19.431 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:22.284 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:22.489 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:25.448 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:25.448 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:28.450 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:28.653 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:31.666 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:31.666 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:34.668 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:34.873 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:37.918 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:37.919 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:40.921 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:41.126 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:44.122 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:44.122 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:47.125 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:47.336 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:50.370 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:50.371 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:53.375 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:53.539 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:56.585 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:56.791 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:57:59.800 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:57:59.801 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:02.803 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:03.006 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:06.049 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:06.050 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:09.054 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:09.214 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:12.259 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:12.462 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:15.475 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:15.475 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:18.480 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:18.647 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:21.731 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:21.934 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:24.761 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:24.863 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:27.870 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:28.276 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:31.092 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:31.239 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:34.241 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:34.445 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:37.468 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:37.468 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:40.555 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:40.598 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:43.760 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:43.760 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:46.763 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:46.907 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:49.912 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:50.320 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:53.294 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:53.294 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:56.297 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:56.503 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:58:59.507 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:58:59.508 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:02.514 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:02.718 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:05.734 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:05.734 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:08.740 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:08.945 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:11.936 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:11.937 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:14.942 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:15.145 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:18.203 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:18.409 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:21.212 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:21.350 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:24.352 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:24.558 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:27.580 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:27.580 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:30.584 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:30.789 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:33.812 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:33.813 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:36.819 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:36.961 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:39.969 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:40.091 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:43.098 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:43.304 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:46.351 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:46.351 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:49.355 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:49.560 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:52.604 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:53.014 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:56.048 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:56.049 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 11:59:59.055 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 11:59:59.221 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:02.225 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:02.395 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:05.449 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:06.460 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:09.291 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:09.441 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:12.446 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:12.856 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:15.697 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:15.903 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:18.936 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:19.072 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:22.101 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:22.308 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:25.368 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:25.521 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:28.527 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:28.731 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:31.773 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:32.259 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:35.236 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:35.435 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:38.556 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:38.763 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:41.809 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:42.014 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:44.832 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:44.985 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:47.989 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:48.192 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:51.198 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:51.402 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:54.450 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:54.655 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:00:57.693 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:00:57.813 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:00.815 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:01.221 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:04.061 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:04.471 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:07.278 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:07.379 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:10.387 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:10.572 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:13.576 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:13.733 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:16.735 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:16.942 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:19.848 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:19.936 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:23.000 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:23.205 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:26.352 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:26.562 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:29.379 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:29.479 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:32.582 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:32.790 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:35.612 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:35.701 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:38.775 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:38.976 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:41.793 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:41.900 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:44.973 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:45.180 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:48.062 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:48.178 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:51.276 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:51.485 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:54.353 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:54.557 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:01:57.445 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:01:57.653 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:02:00.487 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:02:00.581 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:02:03.669 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:02:03.871 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:02:06.858 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:02:06.858 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:02:09.867 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:02:10.069 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:02:13.117 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:02:13.118 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:02:16.119 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:02:16.279 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:03:47.989 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:03:48.194 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:03:51.293 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:03:51.294 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:03:54.301 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:03:54.509 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:03:57.512 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:03:57.513 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:04:00.521 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:04:00.931 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:04:03.813 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:04:04.553 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:04:07.634 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:04:08.048 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:04:11.020 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:04:11.022 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:04:14.106 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:04:14.316 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:04:17.158 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:04:17.264 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:04:20.333 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:04:20.543 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:04:23.542 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:04:23.543 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:04:26.636 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:04:26.637 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:04:29.647 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:04:29.848 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:04:32.885 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:04:32.885 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:05:29.880 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:05:30.083 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:05:32.982 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:05:33.140 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:05:36.205 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:05:36.827 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:05:39.760 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:05:39.907 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:05:42.913 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:05:43.323 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:05:46.134 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:05:46.479 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:05:49.485 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:05:51.712 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:05:54.718 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:05:55.123 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:05:58.181 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:05:58.382 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:01.215 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:01.317 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:04.318 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:05.125 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:07.949 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:08.063 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:11.068 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:11.269 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:14.317 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:14.522 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:17.330 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:17.436 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:20.442 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:20.648 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:23.659 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:23.660 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:26.664 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:26.867 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:29.877 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:29.877 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:32.883 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:33.288 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:36.132 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:36.336 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:39.334 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:39.334 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:42.338 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:42.544 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:45.582 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:45.582 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:48.584 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:48.789 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:51.797 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:51.797 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:54.799 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:55.004 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:06:58.031 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:06:58.031 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:01.036 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:01.241 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:04.290 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:04.496 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:07.335 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:07.517 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:10.520 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:10.691 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:13.695 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:14.105 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:17.156 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:17.489 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:20.496 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:20.671 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:23.700 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:24.716 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:27.567 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:27.977 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:30.808 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:31.168 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:34.173 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:34.778 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:37.601 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:37.740 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:40.746 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:40.903 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:43.910 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:44.116 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:47.143 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:47.144 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:50.148 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:50.241 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:53.361 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:53.564 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:56.456 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:56.612 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:07:59.618 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:07:59.820 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:02.870 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:03.032 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:06.035 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:06.241 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:09.302 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:09.507 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:12.499 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:12.500 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:15.502 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:15.654 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:18.713 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:18.917 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:21.868 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:21.868 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:24.870 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:25.022 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:28.026 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:28.231 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:31.285 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:31.491 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:34.312 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:34.447 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:37.453 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:37.657 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:40.705 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:40.908 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:43.753 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:43.880 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:46.887 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:47.072 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:50.082 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:50.248 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:53.292 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:53.702 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:56.720 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:08:56.720 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:08:59.726 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:00.135 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:02.953 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:03.297 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:06.299 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:06.618 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:09.564 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:09.735 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:12.739 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:12.904 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:15.910 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:16.318 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:19.190 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:19.393 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:22.406 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:22.611 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:25.567 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:25.773 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:28.646 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:28.851 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:31.830 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:32.039 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:34.965 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:35.171 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:38.067 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:38.269 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:41.271 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:41.477 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:44.326 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:44.503 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:47.509 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:47.640 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:50.647 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:50.852 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:53.820 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:54.025 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:09:57.002 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:09:57.207 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:00.021 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:00.214 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:03.264 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:03.470 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:06.373 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:06.491 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:09.531 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:09.732 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:12.752 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:12.957 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:15.898 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:16.100 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:18.930 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:19.131 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:22.093 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:22.299 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:25.242 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:25.444 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:28.368 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:28.477 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:31.537 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:31.740 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:34.620 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:34.714 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:37.793 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:37.793 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:40.794 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:40.880 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:43.959 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:44.160 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:46.982 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:47.107 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:50.217 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:50.423 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:53.230 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:53.436 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:56.364 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:56.568 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:10:59.542 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:10:59.743 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:02.785 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:02.786 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:05.790 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:05.996 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:09.033 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:09.033 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:12.036 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:12.256 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:15.292 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:15.292 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:18.422 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:18.422 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:21.426 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:21.577 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:24.608 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:24.812 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:27.871 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:28.073 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:30.885 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:31.022 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:34.027 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:34.216 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:37.216 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:37.422 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:40.447 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:40.447 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:43.452 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:43.657 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:46.708 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:46.910 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:49.711 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:49.865 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:52.870 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:53.013 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:56.060 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:56.265 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:11:59.284 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:11:59.284 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:02.289 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:02.492 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:05.515 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:05.515 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:08.520 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:08.681 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:11.724 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:11.928 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:14.947 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:14.947 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:17.953 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:18.101 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:21.106 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:21.202 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:24.208 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:24.411 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:27.427 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:27.427 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:30.430 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:30.632 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:33.632 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:33.632 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:36.637 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:36.839 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:39.826 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:39.826 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:42.831 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:43.035 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:46.061 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:46.061 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:49.066 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:49.243 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:52.250 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:52.455 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:55.509 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:55.715 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:12:58.567 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:12:58.772 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:01.817 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:01.818 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:04.821 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:04.959 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:07.985 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:08.189 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:11.235 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:11.440 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:14.248 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:14.353 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:17.357 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:18.165 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:21.210 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:21.415 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:24.232 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:24.417 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:27.424 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:28.628 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:31.633 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:31.838 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:34.850 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:34.850 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:37.868 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:38.073 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:41.111 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:41.111 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:44.112 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:44.258 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:47.265 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:47.467 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:50.473 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:50.882 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:53.707 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:53.844 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:13:56.852 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:13:57.054 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:00.110 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:00.316 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:03.117 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:03.262 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:06.268 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:06.472 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:09.489 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:09.489 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:12.496 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:12.698 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:15.741 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:15.947 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:18.775 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:18.875 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:21.882 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:22.088 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:25.151 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:25.357 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:28.198 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:28.291 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:31.298 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:31.503 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:34.554 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:34.764 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:37.585 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:37.736 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:40.740 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:40.895 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:43.903 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:44.062 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:47.064 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:47.270 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:50.328 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:50.533 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:53.500 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:53.501 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:56.503 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:56.709 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:14:59.722 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:14:59.723 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:02.730 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:02.934 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:05.995 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:06.197 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:09.028 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:09.179 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:12.185 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:12.594 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:15.409 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:15.542 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:18.546 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:18.751 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:21.752 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:21.753 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:24.758 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:24.964 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:28.015 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:28.219 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:31.068 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:31.190 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:34.192 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:34.394 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:37.437 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:37.438 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:40.441 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:40.647 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:43.647 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:43.647 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:46.651 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:46.800 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:49.845 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:50.052 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:53.093 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:53.093 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:56.098 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:56.228 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:15:59.282 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:15:59.488 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:02.527 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:02.528 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:05.535 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:05.678 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:08.739 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:08.945 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:11.941 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:11.941 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:14.946 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:15.151 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:18.201 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:18.405 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:21.380 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:21.381 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:24.386 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:24.594 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:27.613 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:27.613 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:30.617 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:30.822 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:33.835 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:33.835 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:36.837 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:37.039 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:40.082 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:40.288 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:43.123 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:43.261 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:46.266 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:46.468 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:49.526 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:49.937 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:52.787 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:52.991 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:56.024 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:56.025 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:16:59.030 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:16:59.163 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:02.203 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:02.404 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:05.440 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:05.441 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:08.445 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:08.597 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:11.649 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:11.853 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:14.840 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:14.842 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:17.844 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:17.976 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:20.984 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:21.101 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:24.108 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:24.311 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:27.369 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:27.574 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:30.567 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:30.568 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:33.571 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:33.776 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:36.775 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:36.776 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:39.777 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:39.982 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:43.026 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:43.026 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:46.030 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:46.165 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:49.224 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:49.429 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:52.444 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:52.444 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:55.449 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:55.578 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:17:58.586 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:17:58.689 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:01.695 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:02.100 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:04.928 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:05.070 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:08.073 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:08.242 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:11.286 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:11.490 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:14.560 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:14.766 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:17.576 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:17.726 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:20.733 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:20.881 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:23.918 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:24.124 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:27.167 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:27.371 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:30.390 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:30.390 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:33.393 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:33.809 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:36.614 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:36.850 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:39.857 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:39.986 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:42.991 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:43.174 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:46.224 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:46.427 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:49.475 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:49.475 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:52.480 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:52.647 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:55.654 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:55.859 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:18:58.909 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:18:59.114 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:01.947 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:02.147 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:05.152 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:05.257 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:08.263 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:08.467 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:11.495 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:11.496 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:14.500 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:14.705 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:17.754 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:17.754 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:20.760 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:20.894 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:23.953 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:24.160 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:27.219 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:27.220 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:30.227 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:30.375 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:33.378 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:33.576 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:36.581 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:36.786 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:39.803 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:39.804 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:42.809 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:43.015 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:46.060 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:46.060 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:49.064 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:49.213 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:52.218 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:52.332 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:55.337 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:55.539 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:19:58.579 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:19:58.579 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:01.583 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:01.785 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:04.827 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:05.033 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:07.842 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:08.247 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:11.058 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:11.464 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:14.305 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:14.506 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:17.558 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:17.662 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:20.699 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:21.517 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:24.365 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:24.528 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:27.574 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:27.756 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:30.762 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:30.964 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:33.971 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:34.381 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:37.423 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:37.626 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:40.633 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:41.042 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:43.860 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:44.266 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:47.113 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:48.127 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:50.974 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:51.175 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:54.182 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:54.791 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:20:57.839 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:20:58.045 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:01.082 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:01.082 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:04.129 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:04.536 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:07.574 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:07.779 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:10.826 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:10.827 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:13.872 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:14.078 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:17.146 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:17.352 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:20.191 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:20.328 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:23.331 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:23.538 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:26.549 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:26.550 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:29.555 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:29.760 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:32.791 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:32.792 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:35.796 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:35.980 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:38.987 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:39.191 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:42.266 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:42.471 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:45.308 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:45.513 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:48.558 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:48.558 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:51.562 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:51.743 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:54.751 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:54.953 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:21:58.008 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:21:58.422 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:01.258 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:01.463 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:04.511 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:04.712 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:07.753 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:07.753 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:10.803 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:11.009 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:14.061 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:14.133 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:17.138 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:17.320 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:20.325 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:20.520 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:23.526 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:23.687 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:26.692 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:26.877 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:29.883 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:30.296 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:33.107 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:33.272 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:36.276 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:36.678 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:39.743 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:39.949 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:42.795 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:42.998 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:46.060 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:46.269 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:49.073 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:49.213 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:52.215 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:52.371 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:55.377 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:55.546 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:22:58.549 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:22:58.705 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:01.712 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:01.915 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:04.968 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:05.173 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:08.189 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:08.190 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:11.193 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:11.347 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:14.352 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:14.558 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:17.615 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:17.821 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:20.877 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:20.925 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:23.930 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:24.132 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:27.112 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:27.285 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:30.292 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:30.492 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:33.498 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:33.701 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:36.751 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:36.955 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:39.790 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:39.994 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:43.043 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:43.115 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:46.163 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:46.368 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:49.366 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:49.367 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:52.370 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:52.573 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:55.601 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:55.602 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:23:58.605 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:23:58.807 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:01.860 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:02.062 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:05.110 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:05.313 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:08.159 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:08.364 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:11.327 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:11.328 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:14.333 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:14.514 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:17.521 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:17.629 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:20.633 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:20.837 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:23.883 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:24.087 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:26.932 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:27.137 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:30.138 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:30.138 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:33.140 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:33.281 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:36.286 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:36.405 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:39.412 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:39.616 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:42.671 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:42.877 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:45.693 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:45.827 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:48.833 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:49.039 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:52.086 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:52.291 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:55.101 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:55.232 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:24:58.239 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:24:58.443 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:01.498 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:01.616 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:04.662 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:04.866 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:07.882 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:07.882 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:10.888 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:11.093 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:14.123 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:14.123 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:17.129 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:17.283 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:20.336 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:20.541 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:23.529 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:23.530 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:26.535 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:26.738 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:29.759 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:29.759 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:32.765 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:32.971 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:35.992 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:35.993 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:38.997 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:39.163 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:42.222 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:42.424 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:45.458 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:45.458 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:48.464 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:48.666 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:51.695 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:51.696 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:54.701 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:54.881 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:25:57.887 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:25:58.094 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:01.151 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:01.247 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:04.297 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:04.499 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:07.521 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:07.522 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:10.525 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:10.727 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:13.755 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:13.756 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:16.759 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:16.910 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:19.937 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:20.144 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:23.194 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:23.397 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:26.247 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:26.457 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:29.321 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:29.484 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:32.591 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:32.592 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:35.601 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:35.812 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:38.827 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:38.828 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:41.922 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:42.124 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:44.983 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:45.150 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:48.252 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:48.457 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:51.336 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:51.454 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:54.466 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:54.588 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:26:57.682 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:26:57.889 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:00.729 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:00.819 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:03.904 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:04.112 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:07.181 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:07.182 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:10.191 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:10.396 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:13.439 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:13.440 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:16.442 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:16.650 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:19.689 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:19.690 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:22.694 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:22.904 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:25.926 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:25.927 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:28.934 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:29.145 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:32.159 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:32.159 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:35.168 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:35.376 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:38.453 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:38.454 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:41.463 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:41.673 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:44.721 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:44.721 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:47.729 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:47.937 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:50.994 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:50.994 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:54.004 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:54.214 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:27:57.266 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:27:57.267 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:00.275 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:00.485 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:03.551 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:03.551 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:06.562 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:06.769 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:09.791 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:09.791 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:12.802 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:13.013 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:16.085 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:16.217 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:19.335 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:19.546 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:22.377 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:22.483 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:25.574 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:25.785 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:28.629 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:28.761 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:31.772 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:31.887 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:34.990 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:35.197 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:38.040 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:38.179 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:41.275 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:41.485 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:44.356 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:44.480 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:47.491 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:47.612 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:50.694 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:50.902 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:53.736 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:53.894 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:28:56.985 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:28:57.193 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:00.059 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:00.237 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:03.344 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:03.483 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:06.495 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:06.632 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:09.734 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:09.945 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:12.798 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:12.955 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:16.065 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:16.272 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:19.121 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:19.280 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:22.369 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:22.581 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:25.467 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:25.588 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:28.680 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:28.889 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:31.737 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:31.846 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:34.931 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:35.134 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:37.984 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:38.098 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:41.179 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:41.389 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:44.262 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:44.395 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:47.480 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:47.685 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:50.577 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:50.720 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:53.833 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:54.044 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:29:56.899 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:29:57.075 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:00.182 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:00.508 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:03.593 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:03.795 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:06.660 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:06.849 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:09.860 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:10.070 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:13.178 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:13.179 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:16.190 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:16.350 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:19.423 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:19.626 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:22.521 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:22.672 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:25.784 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:25.991 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:28.849 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:29.014 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:32.137 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:32.348 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:35.182 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:35.331 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:38.444 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:38.655 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:41.530 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:41.722 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:44.822 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:45.031 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:47.873 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:48.073 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:51.084 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:51.498 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:54.368 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:54.578 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:30:57.666 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:30:57.872 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:00.678 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:00.846 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:03.963 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:04.172 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:07.000 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:07.119 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:10.215 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:10.425 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:13.285 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:13.416 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:16.424 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:16.538 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:19.642 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:19.851 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:22.908 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:23.113 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:25.962 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:26.105 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:29.108 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:29.310 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:32.360 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:32.561 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:35.563 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:35.563 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:38.566 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:38.697 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:41.721 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:41.907 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:44.943 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:45.146 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:48.191 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:48.395 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:51.228 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:51.416 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:54.422 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:54.562 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:31:57.569 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:31:57.679 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:00.687 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:00.893 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:03.942 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:03.943 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:06.945 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:07.083 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:10.086 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:10.264 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:13.268 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:13.379 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:16.382 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:16.529 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:19.536 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:19.741 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:22.790 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:22.790 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:25.795 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:25.953 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:28.985 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:29.188 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:32.217 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:32.217 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:35.223 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:35.425 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:38.477 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:38.679 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:41.484 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:41.602 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:44.609 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:44.812 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:47.871 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:48.078 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:50.918 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:51.124 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:54.131 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:54.131 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:32:57.136 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:32:57.342 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:00.351 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:00.352 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:03.354 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:03.560 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:06.607 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:06.608 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:09.614 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:09.818 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:12.844 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:12.845 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:15.849 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:15.995 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:19.030 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:19.236 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:22.286 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:22.491 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:25.334 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:25.540 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:28.588 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:28.791 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:31.616 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:31.736 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:34.739 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:34.945 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:37.962 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:37.963 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:40.966 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:41.172 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:44.209 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:44.211 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:47.215 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:47.419 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:50.463 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:50.669 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:53.508 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:53.616 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:56.625 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:56.827 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:33:59.859 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:33:59.860 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:02.865 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:03.071 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:06.123 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:06.326 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:09.150 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:09.269 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:12.276 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:12.481 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:15.491 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:15.492 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:18.497 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:18.703 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:21.765 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:21.972 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:24.782 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:24.920 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:27.930 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:28.081 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:31.128 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:31.335 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:34.361 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:34.362 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:37.370 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:37.549 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:40.593 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:40.798 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:43.773 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:43.773 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:46.780 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:46.984 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:50.047 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:50.254 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:53.268 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:53.269 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:56.269 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:56.475 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:34:59.508 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:34:59.509 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:02.511 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:02.634 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:05.641 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:05.772 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:08.778 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:08.981 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:11.997 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:11.998 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:15.002 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:15.207 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:18.231 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:18.231 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:21.237 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:21.441 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:24.479 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:24.480 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:27.481 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:27.627 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:30.664 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:30.869 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:33.711 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:33.808 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:36.860 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:37.062 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:40.004 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:40.005 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:43.010 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:43.215 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:46.259 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:46.260 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:49.261 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:49.467 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:52.519 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:52.520 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:55.523 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:55.605 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:35:58.653 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:35:58.857 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:01.847 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:01.848 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:04.852 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:05.058 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:08.057 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:08.057 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:11.061 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:11.267 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:14.315 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:14.520 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:17.328 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:17.422 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:20.427 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:20.633 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:23.690 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:23.691 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:26.695 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:26.901 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:29.916 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:29.916 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:32.920 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:33.099 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:36.156 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:36.358 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:39.391 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:39.391 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:42.391 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:42.521 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:45.528 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:45.657 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:48.664 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:48.865 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:51.867 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:51.868 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:54.872 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:55.075 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:36:58.115 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:36:58.320 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:01.154 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:01.252 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:04.255 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:04.462 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:07.459 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:07.459 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:10.465 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:10.667 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:13.677 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:13.678 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:16.683 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:16.888 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:19.872 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:19.873 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:22.876 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:23.079 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:26.069 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:26.070 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:29.074 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:29.278 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:32.278 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:32.279 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:35.281 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:35.488 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:38.503 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:38.504 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:41.506 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:41.710 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:44.751 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:44.751 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:47.755 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:47.873 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:50.881 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:50.980 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:53.988 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:54.199 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:37:57.253 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:37:57.253 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:00.259 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:00.464 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:03.442 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:03.443 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:06.448 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:06.650 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:09.644 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:09.645 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:12.649 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:12.852 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:15.865 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:15.866 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:18.870 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:19.077 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:22.073 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:22.073 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:25.077 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:25.283 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:28.302 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:28.303 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:31.308 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:31.514 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:34.516 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:34.516 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:37.518 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:37.723 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:40.766 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:40.973 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:43.778 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:43.871 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:46.878 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:47.084 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:50.126 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:50.331 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:53.176 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:53.381 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:56.411 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:56.531 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:38:59.579 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:38:59.784 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:02.837 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:02.837 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:05.839 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:05.965 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:09.012 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:09.216 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:12.197 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:12.197 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:15.202 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:15.368 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:18.413 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:18.619 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:21.669 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:21.670 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:24.672 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:25.084 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:27.908 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:28.057 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:31.062 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:31.266 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:34.295 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:34.295 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:37.302 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:37.425 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:40.430 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:40.553 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:43.557 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:43.763 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:46.819 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:47.024 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:49.863 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:50.071 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:53.063 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:53.063 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:56.070 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:56.275 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:39:59.326 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:39:59.327 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:02.331 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:02.536 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:05.588 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:05.589 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:08.590 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:08.718 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:11.760 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:11.964 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:14.955 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:14.956 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:17.959 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:18.112 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:21.117 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:21.728 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:24.592 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:25.004 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:28.042 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:28.042 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:31.048 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:31.252 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:34.301 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:34.507 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:37.340 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:37.439 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:40.490 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:40.695 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:43.655 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:43.656 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:46.658 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:46.794 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:49.837 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:50.040 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:53.055 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:53.056 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:56.058 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:56.263 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:40:59.310 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:40:59.721 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:02.648 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:02.648 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:05.653 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:05.860 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:08.914 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:08.915 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:11.918 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:12.124 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:15.083 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:15.084 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:18.090 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:18.265 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:21.269 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:21.401 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:24.408 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:24.613 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:27.613 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:27.613 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:30.619 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:30.823 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:33.824 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:33.825 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:36.829 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:36.981 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:40.017 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:40.225 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:43.263 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:43.264 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:46.269 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:46.400 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:49.404 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:49.537 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:52.542 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:52.745 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:55.799 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:55.800 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:41:58.803 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:41:58.924 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:01.929 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:02.051 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:05.058 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:05.238 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:08.280 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:08.482 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:11.536 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:11.741 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:14.568 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:14.671 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:17.678 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:17.886 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:20.874 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:20.875 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:23.879 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:24.081 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:27.126 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:27.331 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:30.135 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:30.263 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:33.268 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:33.474 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:36.523 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:36.725 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:39.561 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:39.695 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:42.698 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:42.900 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:45.929 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:45.930 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:48.932 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:49.136 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:52.191 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:52.396 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:55.212 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:55.353 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:42:58.359 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:42:58.493 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:01.502 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:01.625 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:04.630 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:05.036 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:08.089 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:08.295 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:11.119 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:11.226 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:14.228 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:14.433 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:17.444 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:17.444 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:20.448 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:20.653 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:23.712 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:23.917 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:26.722 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:26.821 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:29.824 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:30.029 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:33.093 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:33.295 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:36.102 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:36.246 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:39.253 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:39.659 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:42.622 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:42.623 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:45.625 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:45.767 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:48.775 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:48.884 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:51.889 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:52.093 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:55.147 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:55.350 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:43:58.362 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:43:58.363 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:01.366 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:01.486 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:04.517 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:04.724 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:07.777 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:08.185 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:11.033 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:11.203 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:14.211 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:14.421 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:17.498 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:17.499 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:20.505 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:20.707 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:23.708 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:23.709 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:26.715 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:26.921 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:29.949 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:29.950 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:32.956 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:33.159 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:36.235 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:36.235 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:39.245 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:39.402 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:42.475 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:42.678 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:45.695 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:45.696 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:48.801 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:49.004 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:51.818 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:51.907 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:55.002 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:55.212 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:44:58.011 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:44:58.111 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:45:01.158 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:45:01.360 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:45:04.349 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:45:04.350 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:45:07.358 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:45:07.561 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:45:10.498 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:45:10.701 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:45:13.788 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:45:13.944 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:45:16.945 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:45:17.150 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:45:20.042 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:45:20.160 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:45:23.231 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:45:23.439 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:45:26.263 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:45:26.471 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:45:29.461 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:45:29.462 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:45:32.466 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:45:32.671 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:45:35.690 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:45:35.690 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:45:38.694 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:45:38.894 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
[INFO ] 2025-03-11 12:51:12.538 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - rebuild statement with 270703, 27 
[INFO ] 2025-03-11 12:51:14.195 - [任务 44][Sybase LAB_DB_9 INT DB - Copy] - normal rescan, will sleep 3s, and scan from startRid: 270703, rowId: 27 
