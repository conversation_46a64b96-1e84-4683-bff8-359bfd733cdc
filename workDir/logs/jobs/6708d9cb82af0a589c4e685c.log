[INFO ] 2024-10-11 15:56:23.290 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:56:23.291 - [任务 8(100)][2a3b1fef-62bb-4a73-b598-d09a0742bfba] - Node 2a3b1fef-62bb-4a73-b598-d09a0742bfba[2a3b1fef-62bb-4a73-b598-d09a0742bfba] start preload schema,table counts: 0 
[INFO ] 2024-10-11 15:56:23.292 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:56:23.292 - [任务 8(100)][2a3b1fef-62bb-4a73-b598-d09a0742bfba] - Node 2a3b1fef-62bb-4a73-b598-d09a0742bfba[2a3b1fef-62bb-4a73-b598-d09a0742bfba] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:23.292 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:23.292 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:23.292 - [任务 8(100)][增强JS] - Node js_processor(增强JS: 9d8b036b-1579-4638-974e-c9caacc934d7) enable batch process 
[INFO ] 2024-10-11 15:56:26.490 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:56:26.491 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:56:26.492 - [任务 8(100)][dd7e575a-bcea-4fbc-81a9-832143be1999] - Node dd7e575a-bcea-4fbc-81a9-832143be1999[dd7e575a-bcea-4fbc-81a9-832143be1999] start preload schema,table counts: 0 
[INFO ] 2024-10-11 15:56:26.492 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:26.492 - [任务 8(100)][dd7e575a-bcea-4fbc-81a9-832143be1999] - Node dd7e575a-bcea-4fbc-81a9-832143be1999[dd7e575a-bcea-4fbc-81a9-832143be1999] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:26.492 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:26.493 - [任务 8(100)][增强JS] - Node js_processor(增强JS: 9d8b036b-1579-4638-974e-c9caacc934d7) enable batch process 
[INFO ] 2024-10-11 15:56:28.699 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:56:28.700 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:56:28.701 - [任务 8(100)][1422e621-8fc1-4c4c-819d-462f2005227a] - Node 1422e621-8fc1-4c4c-819d-462f2005227a[1422e621-8fc1-4c4c-819d-462f2005227a] start preload schema,table counts: 0 
[INFO ] 2024-10-11 15:56:28.701 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 15:56:28.701 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] preload schema finished, cost 2 ms 
[INFO ] 2024-10-11 15:56:28.701 - [任务 8(100)][1422e621-8fc1-4c4c-819d-462f2005227a] - Node 1422e621-8fc1-4c4c-819d-462f2005227a[1422e621-8fc1-4c4c-819d-462f2005227a] preload schema finished, cost 2 ms 
[INFO ] 2024-10-11 15:56:28.701 - [任务 8(100)][增强JS] - Node js_processor(增强JS: 9d8b036b-1579-4638-974e-c9caacc934d7) enable batch process 
[INFO ] 2024-10-11 15:56:28.917 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:56:28.917 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:56:28.918 - [任务 8(100)][3bbfed72-1b34-4cb8-856e-7ff0869470d8] - Node 3bbfed72-1b34-4cb8-856e-7ff0869470d8[3bbfed72-1b34-4cb8-856e-7ff0869470d8] start preload schema,table counts: 0 
[INFO ] 2024-10-11 15:56:28.918 - [任务 8(100)][3bbfed72-1b34-4cb8-856e-7ff0869470d8] - Node 3bbfed72-1b34-4cb8-856e-7ff0869470d8[3bbfed72-1b34-4cb8-856e-7ff0869470d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:28.918 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 15:56:28.918 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:28.918 - [任务 8(100)][增强JS] - Node js_processor(增强JS: 9d8b036b-1579-4638-974e-c9caacc934d7) enable batch process 
[INFO ] 2024-10-11 15:56:28.972 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:56:28.973 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:56:28.973 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:28.973 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:28.973 - [任务 8(100)][f7916960-5614-4abf-be05-37856cd1f54b] - Node f7916960-5614-4abf-be05-37856cd1f54b[f7916960-5614-4abf-be05-37856cd1f54b] start preload schema,table counts: 0 
[INFO ] 2024-10-11 15:56:28.973 - [任务 8(100)][增强JS] - Node js_processor(增强JS: 9d8b036b-1579-4638-974e-c9caacc934d7) enable batch process 
[INFO ] 2024-10-11 15:56:29.175 - [任务 8(100)][f7916960-5614-4abf-be05-37856cd1f54b] - Node f7916960-5614-4abf-be05-37856cd1f54b[f7916960-5614-4abf-be05-37856cd1f54b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:29.376 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] running status set to false 
[INFO ] 2024-10-11 15:56:29.377 - [任务 8(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:56:29.378 - [任务 8(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:56:29.378 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] schema data cleaned 
[INFO ] 2024-10-11 15:56:29.378 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] monitor closed 
[INFO ] 2024-10-11 15:56:29.582 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] close complete, cost 56 ms 
[INFO ] 2024-10-11 15:56:35.177 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] running status set to false 
[INFO ] 2024-10-11 15:56:35.179 - [任务 8(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:56:35.179 - [任务 8(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:56:35.180 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] schema data cleaned 
[INFO ] 2024-10-11 15:56:35.180 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] monitor closed 
[INFO ] 2024-10-11 15:56:35.400 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] close complete, cost 74 ms 
[INFO ] 2024-10-11 15:56:35.424 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] running status set to false 
[INFO ] 2024-10-11 15:56:35.444 - [任务 8(100)][2a3b1fef-62bb-4a73-b598-d09a0742bfba] - Node 2a3b1fef-62bb-4a73-b598-d09a0742bfba[2a3b1fef-62bb-4a73-b598-d09a0742bfba] running status set to false 
[INFO ] 2024-10-11 15:56:35.458 - [任务 8(100)][2a3b1fef-62bb-4a73-b598-d09a0742bfba] - Node 2a3b1fef-62bb-4a73-b598-d09a0742bfba[2a3b1fef-62bb-4a73-b598-d09a0742bfba] schema data cleaned 
[INFO ] 2024-10-11 15:56:35.459 - [任务 8(100)][2a3b1fef-62bb-4a73-b598-d09a0742bfba] - Node 2a3b1fef-62bb-4a73-b598-d09a0742bfba[2a3b1fef-62bb-4a73-b598-d09a0742bfba] monitor closed 
[INFO ] 2024-10-11 15:56:35.459 - [任务 8(100)][2a3b1fef-62bb-4a73-b598-d09a0742bfba] - Node 2a3b1fef-62bb-4a73-b598-d09a0742bfba[2a3b1fef-62bb-4a73-b598-d09a0742bfba] close complete, cost 5 ms 
[INFO ] 2024-10-11 15:56:35.481 - [任务 8(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-9e36ab5e-d176-4ae6-a57b-d3f8820ce828 
[INFO ] 2024-10-11 15:56:35.481 - [任务 8(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-9e36ab5e-d176-4ae6-a57b-d3f8820ce828 
[INFO ] 2024-10-11 15:56:35.481 - [任务 8(100)][增强JS] - [ScriptExecutorsManager-6708d9cb82af0a589c4e685c-9d8b036b-1579-4638-974e-c9caacc934d7-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 15:56:35.484 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] schema data cleaned 
[INFO ] 2024-10-11 15:56:35.484 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] monitor closed 
[INFO ] 2024-10-11 15:56:35.489 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] close complete, cost 71 ms 
[INFO ] 2024-10-11 15:56:35.490 - [任务 8(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 15:56:35.490 - [任务 8(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 15:56:35.490 - [任务 8(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 15:56:36.126 - [任务 8(100)][14c642a4-a0db-4f29-8ac4-fabce295ac5b] - Node 14c642a4-a0db-4f29-8ac4-fabce295ac5b[14c642a4-a0db-4f29-8ac4-fabce295ac5b] start preload schema,table counts: 0 
[INFO ] 2024-10-11 15:56:36.127 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:56:36.127 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:56:36.127 - [任务 8(100)][14c642a4-a0db-4f29-8ac4-fabce295ac5b] - Node 14c642a4-a0db-4f29-8ac4-fabce295ac5b[14c642a4-a0db-4f29-8ac4-fabce295ac5b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:36.127 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:36.127 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:36.332 - [任务 8(100)][增强JS] - Node js_processor(增强JS: 9d8b036b-1579-4638-974e-c9caacc934d7) enable batch process 
[INFO ] 2024-10-11 15:56:41.703 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] running status set to false 
[INFO ] 2024-10-11 15:56:41.769 - [任务 8(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:56:41.769 - [任务 8(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:56:41.771 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] schema data cleaned 
[INFO ] 2024-10-11 15:56:41.771 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] monitor closed 
[INFO ] 2024-10-11 15:56:41.802 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] close complete, cost 86 ms 
[INFO ] 2024-10-11 15:56:41.802 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] running status set to false 
[INFO ] 2024-10-11 15:56:41.817 - [任务 8(100)][f7916960-5614-4abf-be05-37856cd1f54b] - Node f7916960-5614-4abf-be05-37856cd1f54b[f7916960-5614-4abf-be05-37856cd1f54b] running status set to false 
[INFO ] 2024-10-11 15:56:41.817 - [任务 8(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-4bb16d65-9f3a-4fc6-a954-3cd69a52aeca 
[INFO ] 2024-10-11 15:56:41.817 - [任务 8(100)][f7916960-5614-4abf-be05-37856cd1f54b] - Node f7916960-5614-4abf-be05-37856cd1f54b[f7916960-5614-4abf-be05-37856cd1f54b] schema data cleaned 
[INFO ] 2024-10-11 15:56:41.817 - [任务 8(100)][f7916960-5614-4abf-be05-37856cd1f54b] - Node f7916960-5614-4abf-be05-37856cd1f54b[f7916960-5614-4abf-be05-37856cd1f54b] monitor closed 
[INFO ] 2024-10-11 15:56:41.818 - [任务 8(100)][f7916960-5614-4abf-be05-37856cd1f54b] - Node f7916960-5614-4abf-be05-37856cd1f54b[f7916960-5614-4abf-be05-37856cd1f54b] close complete, cost 6 ms 
[INFO ] 2024-10-11 15:56:41.818 - [任务 8(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-4bb16d65-9f3a-4fc6-a954-3cd69a52aeca 
[INFO ] 2024-10-11 15:56:41.818 - [任务 8(100)][增强JS] - [ScriptExecutorsManager-6708d9cb82af0a589c4e685c-9d8b036b-1579-4638-974e-c9caacc934d7-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 15:56:41.822 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] schema data cleaned 
[INFO ] 2024-10-11 15:56:41.822 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] monitor closed 
[INFO ] 2024-10-11 15:56:41.827 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] close complete, cost 22 ms 
[INFO ] 2024-10-11 15:56:41.828 - [任务 8(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 15:56:41.828 - [任务 8(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 15:56:41.829 - [任务 8(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 15:56:42.884 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] running status set to false 
[INFO ] 2024-10-11 15:56:42.928 - [任务 8(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:56:42.929 - [任务 8(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:56:42.930 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] schema data cleaned 
[INFO ] 2024-10-11 15:56:42.931 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] monitor closed 
[INFO ] 2024-10-11 15:56:42.932 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] close complete, cost 59 ms 
[INFO ] 2024-10-11 15:56:43.740 - [任务 8(100)][6718b4fc-2c28-4bcb-9082-ac1c18f42a42] - Node 6718b4fc-2c28-4bcb-9082-ac1c18f42a42[6718b4fc-2c28-4bcb-9082-ac1c18f42a42] start preload schema,table counts: 0 
[INFO ] 2024-10-11 15:56:43.741 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:56:43.741 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:56:43.741 - [任务 8(100)][6718b4fc-2c28-4bcb-9082-ac1c18f42a42] - Node 6718b4fc-2c28-4bcb-9082-ac1c18f42a42[6718b4fc-2c28-4bcb-9082-ac1c18f42a42] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:43.741 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 15:56:43.741 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] preload schema finished, cost 3 ms 
[INFO ] 2024-10-11 15:56:43.742 - [任务 8(100)][增强JS] - Node js_processor(增强JS: 9d8b036b-1579-4638-974e-c9caacc934d7) enable batch process 
[INFO ] 2024-10-11 15:56:47.389 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] running status set to false 
[INFO ] 2024-10-11 15:56:47.438 - [任务 8(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:56:47.439 - [任务 8(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:56:47.439 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] schema data cleaned 
[INFO ] 2024-10-11 15:56:47.439 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] monitor closed 
[INFO ] 2024-10-11 15:56:47.440 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] close complete, cost 84 ms 
[INFO ] 2024-10-11 15:56:49.005 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] running status set to false 
[INFO ] 2024-10-11 15:56:49.006 - [任务 8(100)][3bbfed72-1b34-4cb8-856e-7ff0869470d8] - Node 3bbfed72-1b34-4cb8-856e-7ff0869470d8[3bbfed72-1b34-4cb8-856e-7ff0869470d8] running status set to false 
[INFO ] 2024-10-11 15:56:49.010 - [任务 8(100)][3bbfed72-1b34-4cb8-856e-7ff0869470d8] - Node 3bbfed72-1b34-4cb8-856e-7ff0869470d8[3bbfed72-1b34-4cb8-856e-7ff0869470d8] schema data cleaned 
[INFO ] 2024-10-11 15:56:49.011 - [任务 8(100)][3bbfed72-1b34-4cb8-856e-7ff0869470d8] - Node 3bbfed72-1b34-4cb8-856e-7ff0869470d8[3bbfed72-1b34-4cb8-856e-7ff0869470d8] monitor closed 
[INFO ] 2024-10-11 15:56:49.011 - [任务 8(100)][3bbfed72-1b34-4cb8-856e-7ff0869470d8] - Node 3bbfed72-1b34-4cb8-856e-7ff0869470d8[3bbfed72-1b34-4cb8-856e-7ff0869470d8] close complete, cost 6 ms 
[INFO ] 2024-10-11 15:56:49.014 - [任务 8(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-7b488a2f-f0ee-4ee6-b94d-12b98c792263 
[INFO ] 2024-10-11 15:56:49.014 - [任务 8(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-7b488a2f-f0ee-4ee6-b94d-12b98c792263 
[INFO ] 2024-10-11 15:56:49.019 - [任务 8(100)][增强JS] - [ScriptExecutorsManager-6708d9cb82af0a589c4e685c-9d8b036b-1579-4638-974e-c9caacc934d7-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 15:56:49.019 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] schema data cleaned 
[INFO ] 2024-10-11 15:56:49.019 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] monitor closed 
[INFO ] 2024-10-11 15:56:49.020 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] close complete, cost 38 ms 
[INFO ] 2024-10-11 15:56:49.022 - [任务 8(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 15:56:49.023 - [任务 8(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 15:56:49.023 - [任务 8(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 15:56:56.351 - [任务 8(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: Col is not defined 
[ERROR] 2024-10-11 15:56:56.556 - [任务 8(100)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: Col is not defined <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: Col is not defined

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: Col is not defined
	<js>.process(<eval>:2)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: Col is not defined
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: Col is not defined
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: Col is not defined
	at <js>.process(<eval>:2)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 2 more

[INFO ] 2024-10-11 15:56:56.558 - [任务 8(100)][d05ba632-8f2f-48df-80f4-007f683d5592] - Node d05ba632-8f2f-48df-80f4-007f683d5592[d05ba632-8f2f-48df-80f4-007f683d5592] start preload schema,table counts: 0 
[INFO ] 2024-10-11 15:56:56.559 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:56:56.559 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:56:56.559 - [任务 8(100)][d05ba632-8f2f-48df-80f4-007f683d5592] - Node d05ba632-8f2f-48df-80f4-007f683d5592[d05ba632-8f2f-48df-80f4-007f683d5592] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:56.559 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:56.559 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:56:56.559 - [任务 8(100)][增强JS] - Node js_processor(增强JS: 9d8b036b-1579-4638-974e-c9caacc934d7) enable batch process 
[INFO ] 2024-10-11 15:56:58.976 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] running status set to false 
[INFO ] 2024-10-11 15:56:58.984 - [任务 8(100)][14c642a4-a0db-4f29-8ac4-fabce295ac5b] - Node 14c642a4-a0db-4f29-8ac4-fabce295ac5b[14c642a4-a0db-4f29-8ac4-fabce295ac5b] running status set to false 
[INFO ] 2024-10-11 15:56:58.985 - [任务 8(100)][14c642a4-a0db-4f29-8ac4-fabce295ac5b] - Node 14c642a4-a0db-4f29-8ac4-fabce295ac5b[14c642a4-a0db-4f29-8ac4-fabce295ac5b] schema data cleaned 
[INFO ] 2024-10-11 15:56:58.985 - [任务 8(100)][14c642a4-a0db-4f29-8ac4-fabce295ac5b] - Node 14c642a4-a0db-4f29-8ac4-fabce295ac5b[14c642a4-a0db-4f29-8ac4-fabce295ac5b] monitor closed 
[INFO ] 2024-10-11 15:56:58.985 - [任务 8(100)][14c642a4-a0db-4f29-8ac4-fabce295ac5b] - Node 14c642a4-a0db-4f29-8ac4-fabce295ac5b[14c642a4-a0db-4f29-8ac4-fabce295ac5b] close complete, cost 5 ms 
[INFO ] 2024-10-11 15:56:58.996 - [任务 8(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-cebacbd2-530e-4351-972c-ee3d23b80792 
[INFO ] 2024-10-11 15:56:58.996 - [任务 8(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-cebacbd2-530e-4351-972c-ee3d23b80792 
[INFO ] 2024-10-11 15:56:58.996 - [任务 8(100)][增强JS] - [ScriptExecutorsManager-6708d9cb82af0a589c4e685c-9d8b036b-1579-4638-974e-c9caacc934d7-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 15:56:59.000 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] schema data cleaned 
[INFO ] 2024-10-11 15:56:59.000 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] monitor closed 
[INFO ] 2024-10-11 15:56:59.001 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] close complete, cost 40 ms 
[INFO ] 2024-10-11 15:56:59.002 - [任务 8(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 15:56:59.002 - [任务 8(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 15:56:59.170 - [任务 8(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 15:56:59.170 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] running status set to false 
[INFO ] 2024-10-11 15:56:59.188 - [任务 8(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:56:59.188 - [任务 8(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:56:59.394 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] schema data cleaned 
[INFO ] 2024-10-11 15:56:59.395 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] monitor closed 
[INFO ] 2024-10-11 15:56:59.395 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] close complete, cost 19 ms 
[INFO ] 2024-10-11 15:57:01.289 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] running status set to false 
[INFO ] 2024-10-11 15:57:01.293 - [任务 8(100)][1422e621-8fc1-4c4c-819d-462f2005227a] - Node 1422e621-8fc1-4c4c-819d-462f2005227a[1422e621-8fc1-4c4c-819d-462f2005227a] running status set to false 
[INFO ] 2024-10-11 15:57:01.302 - [任务 8(100)][1422e621-8fc1-4c4c-819d-462f2005227a] - Node 1422e621-8fc1-4c4c-819d-462f2005227a[1422e621-8fc1-4c4c-819d-462f2005227a] schema data cleaned 
[INFO ] 2024-10-11 15:57:01.310 - [任务 8(100)][1422e621-8fc1-4c4c-819d-462f2005227a] - Node 1422e621-8fc1-4c4c-819d-462f2005227a[1422e621-8fc1-4c4c-819d-462f2005227a] monitor closed 
[INFO ] 2024-10-11 15:57:01.315 - [任务 8(100)][1422e621-8fc1-4c4c-819d-462f2005227a] - Node 1422e621-8fc1-4c4c-819d-462f2005227a[1422e621-8fc1-4c4c-819d-462f2005227a] close complete, cost 9 ms 
[INFO ] 2024-10-11 15:57:01.333 - [任务 8(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-914dab97-ffdd-4e0f-922f-7ff250d365d4 
[INFO ] 2024-10-11 15:57:01.336 - [任务 8(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-914dab97-ffdd-4e0f-922f-7ff250d365d4 
[INFO ] 2024-10-11 15:57:01.336 - [任务 8(100)][增强JS] - [ScriptExecutorsManager-6708d9cb82af0a589c4e685c-9d8b036b-1579-4638-974e-c9caacc934d7-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 15:57:01.337 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] schema data cleaned 
[INFO ] 2024-10-11 15:57:01.337 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] monitor closed 
[INFO ] 2024-10-11 15:57:01.340 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] close complete, cost 61 ms 
[INFO ] 2024-10-11 15:57:01.340 - [任务 8(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 15:57:01.340 - [任务 8(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 15:57:01.544 - [任务 8(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 15:57:02.092 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] running status set to false 
[INFO ] 2024-10-11 15:57:02.092 - [任务 8(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:57:02.095 - [任务 8(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:57:02.095 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] schema data cleaned 
[INFO ] 2024-10-11 15:57:02.095 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] monitor closed 
[INFO ] 2024-10-11 15:57:02.304 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] close complete, cost 55 ms 
[INFO ] 2024-10-11 15:57:07.149 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] running status set to false 
[INFO ] 2024-10-11 15:57:07.152 - [任务 8(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:57:07.152 - [任务 8(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:57:07.153 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] schema data cleaned 
[INFO ] 2024-10-11 15:57:07.153 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] monitor closed 
[INFO ] 2024-10-11 15:57:07.153 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] close complete, cost 59 ms 
[INFO ] 2024-10-11 15:57:07.820 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] running status set to false 
[INFO ] 2024-10-11 15:57:07.832 - [任务 8(100)][dd7e575a-bcea-4fbc-81a9-832143be1999] - Node dd7e575a-bcea-4fbc-81a9-832143be1999[dd7e575a-bcea-4fbc-81a9-832143be1999] running status set to false 
[INFO ] 2024-10-11 15:57:07.844 - [任务 8(100)][dd7e575a-bcea-4fbc-81a9-832143be1999] - Node dd7e575a-bcea-4fbc-81a9-832143be1999[dd7e575a-bcea-4fbc-81a9-832143be1999] schema data cleaned 
[INFO ] 2024-10-11 15:57:07.844 - [任务 8(100)][dd7e575a-bcea-4fbc-81a9-832143be1999] - Node dd7e575a-bcea-4fbc-81a9-832143be1999[dd7e575a-bcea-4fbc-81a9-832143be1999] monitor closed 
[INFO ] 2024-10-11 15:57:07.848 - [任务 8(100)][dd7e575a-bcea-4fbc-81a9-832143be1999] - Node dd7e575a-bcea-4fbc-81a9-832143be1999[dd7e575a-bcea-4fbc-81a9-832143be1999] close complete, cost 7 ms 
[INFO ] 2024-10-11 15:57:07.866 - [任务 8(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-1803197e-5f1b-4f2b-96a5-9780bbb1974e 
[INFO ] 2024-10-11 15:57:07.866 - [任务 8(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-1803197e-5f1b-4f2b-96a5-9780bbb1974e 
[INFO ] 2024-10-11 15:57:07.867 - [任务 8(100)][增强JS] - [ScriptExecutorsManager-6708d9cb82af0a589c4e685c-9d8b036b-1579-4638-974e-c9caacc934d7-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 15:57:07.871 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] schema data cleaned 
[INFO ] 2024-10-11 15:57:07.872 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] monitor closed 
[INFO ] 2024-10-11 15:57:07.875 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] close complete, cost 65 ms 
[INFO ] 2024-10-11 15:57:07.876 - [任务 8(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 15:57:07.876 - [任务 8(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 15:57:08.086 - [任务 8(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 15:57:08.109 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] running status set to false 
[INFO ] 2024-10-11 15:57:08.113 - [任务 8(100)][d05ba632-8f2f-48df-80f4-007f683d5592] - Node d05ba632-8f2f-48df-80f4-007f683d5592[d05ba632-8f2f-48df-80f4-007f683d5592] running status set to false 
[INFO ] 2024-10-11 15:57:08.113 - [任务 8(100)][d05ba632-8f2f-48df-80f4-007f683d5592] - Node d05ba632-8f2f-48df-80f4-007f683d5592[d05ba632-8f2f-48df-80f4-007f683d5592] schema data cleaned 
[INFO ] 2024-10-11 15:57:08.113 - [任务 8(100)][d05ba632-8f2f-48df-80f4-007f683d5592] - Node d05ba632-8f2f-48df-80f4-007f683d5592[d05ba632-8f2f-48df-80f4-007f683d5592] monitor closed 
[INFO ] 2024-10-11 15:57:08.131 - [任务 8(100)][d05ba632-8f2f-48df-80f4-007f683d5592] - Node d05ba632-8f2f-48df-80f4-007f683d5592[d05ba632-8f2f-48df-80f4-007f683d5592] close complete, cost 6 ms 
[INFO ] 2024-10-11 15:57:08.135 - [任务 8(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-a39fb600-58c0-483d-aa20-5b8f9073478d 
[INFO ] 2024-10-11 15:57:08.135 - [任务 8(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-a39fb600-58c0-483d-aa20-5b8f9073478d 
[INFO ] 2024-10-11 15:57:08.137 - [任务 8(100)][增强JS] - [ScriptExecutorsManager-6708d9cb82af0a589c4e685c-9d8b036b-1579-4638-974e-c9caacc934d7-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 15:57:08.137 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] schema data cleaned 
[INFO ] 2024-10-11 15:57:08.137 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] monitor closed 
[INFO ] 2024-10-11 15:57:08.137 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] close complete, cost 40 ms 
[INFO ] 2024-10-11 15:57:08.140 - [任务 8(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 15:57:08.140 - [任务 8(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 15:57:08.141 - [任务 8(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 15:57:13.451 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] running status set to false 
[INFO ] 2024-10-11 15:57:13.452 - [任务 8(100)][6718b4fc-2c28-4bcb-9082-ac1c18f42a42] - Node 6718b4fc-2c28-4bcb-9082-ac1c18f42a42[6718b4fc-2c28-4bcb-9082-ac1c18f42a42] running status set to false 
[INFO ] 2024-10-11 15:57:13.452 - [任务 8(100)][6718b4fc-2c28-4bcb-9082-ac1c18f42a42] - Node 6718b4fc-2c28-4bcb-9082-ac1c18f42a42[6718b4fc-2c28-4bcb-9082-ac1c18f42a42] schema data cleaned 
[INFO ] 2024-10-11 15:57:13.452 - [任务 8(100)][6718b4fc-2c28-4bcb-9082-ac1c18f42a42] - Node 6718b4fc-2c28-4bcb-9082-ac1c18f42a42[6718b4fc-2c28-4bcb-9082-ac1c18f42a42] monitor closed 
[INFO ] 2024-10-11 15:57:13.452 - [任务 8(100)][6718b4fc-2c28-4bcb-9082-ac1c18f42a42] - Node 6718b4fc-2c28-4bcb-9082-ac1c18f42a42[6718b4fc-2c28-4bcb-9082-ac1c18f42a42] close complete, cost 0 ms 
[INFO ] 2024-10-11 15:57:13.479 - [任务 8(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-7d98f24d-12ab-41a8-8b44-2b9a1a0d8a7c 
[INFO ] 2024-10-11 15:57:13.479 - [任务 8(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-7d98f24d-12ab-41a8-8b44-2b9a1a0d8a7c 
[INFO ] 2024-10-11 15:57:13.479 - [任务 8(100)][增强JS] - [ScriptExecutorsManager-6708d9cb82af0a589c4e685c-9d8b036b-1579-4638-974e-c9caacc934d7-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 15:57:13.481 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] schema data cleaned 
[INFO ] 2024-10-11 15:57:13.482 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] monitor closed 
[INFO ] 2024-10-11 15:57:13.482 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] close complete, cost 35 ms 
[INFO ] 2024-10-11 15:57:13.484 - [任务 8(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 15:57:13.484 - [任务 8(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 15:57:13.484 - [任务 8(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 15:57:22.249 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:57:22.251 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:57:22.251 - [任务 8(100)][333ab1e2-3bcc-4c6f-a7ca-e1c353da7259] - Node 333ab1e2-3bcc-4c6f-a7ca-e1c353da7259[333ab1e2-3bcc-4c6f-a7ca-e1c353da7259] start preload schema,table counts: 0 
[INFO ] 2024-10-11 15:57:22.251 - [任务 8(100)][333ab1e2-3bcc-4c6f-a7ca-e1c353da7259] - Node 333ab1e2-3bcc-4c6f-a7ca-e1c353da7259[333ab1e2-3bcc-4c6f-a7ca-e1c353da7259] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:57:22.251 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:57:22.251 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:57:22.251 - [任务 8(100)][增强JS] - Node js_processor(增强JS: 9d8b036b-1579-4638-974e-c9caacc934d7) enable batch process 
[INFO ] 2024-10-11 15:57:27.816 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] running status set to false 
[INFO ] 2024-10-11 15:57:27.860 - [任务 8(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:57:27.860 - [任务 8(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:57:27.860 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] schema data cleaned 
[INFO ] 2024-10-11 15:57:27.860 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] monitor closed 
[INFO ] 2024-10-11 15:57:27.861 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] close complete, cost 44 ms 
[INFO ] 2024-10-11 15:57:33.322 - [任务 8(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: java.lang.UnsupportedOperationException 
[ERROR] 2024-10-11 15:57:33.325 - [任务 8(100)][增强JS] - javax.script.ScriptException: java.lang.UnsupportedOperationException <-- Error Message -->
javax.script.ScriptException: java.lang.UnsupportedOperationException

<-- Simple Stack Trace -->
Caused by: java.lang.UnsupportedOperationException: null
	java.util.AbstractCollection.add(AbstractCollection.java:262)
	com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: java.lang.UnsupportedOperationException
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: java.lang.UnsupportedOperationException
	at java.util.AbstractCollection.add(AbstractCollection.java:262)
	at <js>.process(<eval>:3)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: java.lang.UnsupportedOperationException
	at java.util.AbstractCollection.add(AbstractCollection.java:262)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	at com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	at com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callInlined(DefaultRuntimeAccessor.java:142)
	at com.oracle.truffle.host.GuestToHostRootNode.guestToHostCall(GuestToHostRootNode.java:107)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeGuestToHost(HostMethodDesc.java:364)
	at com.oracle.truffle.host.HostExecuteNode.doInvoke(HostExecuteNode.java:873)
	at com.oracle.truffle.host.HostExecuteNode.doFixed(HostExecuteNode.java:137)
	at com.oracle.truffle.host.HostExecuteNodeGen.executeAndSpecialize(HostExecuteNodeGen.java:143)
	at com.oracle.truffle.host.HostExecuteNodeGen.execute(HostExecuteNodeGen.java:96)
	at com.oracle.truffle.host.HostObject.invokeMember(HostObject.java:451)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMemberNode_AndSpecialize(HostObjectGen.java:2695)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMember(HostObjectGen.java:2677)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.invokeMember(InteropLibraryGen.java:8138)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$ForeignInvokeNode.executeCall(JSFunctionCallNode.java:1475)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$InvokeNode.execute(JSFunctionCallNode.java:722)
	at com.oracle.truffle.js.nodes.JavaScriptNode.executeVoid(JavaScriptNode.java:191)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:80)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:55)
	at com.oracle.truffle.api.impl.DefaultBlockNode.executeGeneric(DefaultBlockNode.java:63)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.execute(AbstractBlockNode.java:75)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:125)
	at com.oracle.truffle.js.nodes.function.FunctionBodyNode.execute(FunctionBodyNode.java:73)
	at com.oracle.truffle.js.nodes.function.FunctionRootNode.executeInRealm(FunctionRootNode.java:150)
	at com.oracle.truffle.js.runtime.JavaScriptRealmBoundaryRootNode.execute(JavaScriptRealmBoundaryRootNode.java:93)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultDirectCallNode.call(DefaultDirectCallNode.java:59)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$UnboundJSFunctionCacheNode.executeCall(JSFunctionCallNode.java:1256)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNode.doDefault(JSInteropExecuteNode.java:68)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.executeAndSpecialize(JSInteropExecuteNodeGen.java:61)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.execute(JSInteropExecuteNodeGen.java:43)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunction.execute(InteropBoundFunction.java:111)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.executeNode_AndSpecialize(InteropBoundFunctionGen.java:188)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.execute(InteropBoundFunctionGen.java:171)
	at com.oracle.truffle.api.interop.InteropLibraryGen$Delegate.execute(InteropLibraryGen.java:3801)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.execute(InteropLibraryGen.java:7592)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$AbstractExecuteNode.executeShared(PolyglotValueDispatch.java:4231)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$ExecuteNode.executeImpl(PolyglotValueDispatch.java:4312)
	at com.oracle.truffle.polyglot.HostToGuestRootNode.execute(HostToGuestRootNode.java:127)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultCallTarget.call(DefaultCallTarget.java:102)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callProfiled(DefaultRuntimeAccessor.java:147)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue.execute(PolyglotValueDispatch.java:2369)
	... 4 more

[INFO ] 2024-10-11 15:57:35.864 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] running status set to false 
[INFO ] 2024-10-11 15:57:35.886 - [任务 8(100)][333ab1e2-3bcc-4c6f-a7ca-e1c353da7259] - Node 333ab1e2-3bcc-4c6f-a7ca-e1c353da7259[333ab1e2-3bcc-4c6f-a7ca-e1c353da7259] running status set to false 
[INFO ] 2024-10-11 15:57:35.886 - [任务 8(100)][333ab1e2-3bcc-4c6f-a7ca-e1c353da7259] - Node 333ab1e2-3bcc-4c6f-a7ca-e1c353da7259[333ab1e2-3bcc-4c6f-a7ca-e1c353da7259] schema data cleaned 
[INFO ] 2024-10-11 15:57:35.889 - [任务 8(100)][333ab1e2-3bcc-4c6f-a7ca-e1c353da7259] - Node 333ab1e2-3bcc-4c6f-a7ca-e1c353da7259[333ab1e2-3bcc-4c6f-a7ca-e1c353da7259] monitor closed 
[INFO ] 2024-10-11 15:57:35.889 - [任务 8(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-adf42181-b629-4947-912c-76628a1de182 
[INFO ] 2024-10-11 15:57:35.889 - [任务 8(100)][333ab1e2-3bcc-4c6f-a7ca-e1c353da7259] - Node 333ab1e2-3bcc-4c6f-a7ca-e1c353da7259[333ab1e2-3bcc-4c6f-a7ca-e1c353da7259] close complete, cost 29 ms 
[INFO ] 2024-10-11 15:57:35.889 - [任务 8(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-adf42181-b629-4947-912c-76628a1de182 
[INFO ] 2024-10-11 15:57:35.892 - [任务 8(100)][增强JS] - [ScriptExecutorsManager-6708d9cb82af0a589c4e685c-9d8b036b-1579-4638-974e-c9caacc934d7-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 15:57:35.892 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] schema data cleaned 
[INFO ] 2024-10-11 15:57:35.892 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] monitor closed 
[INFO ] 2024-10-11 15:57:35.893 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] close complete, cost 36 ms 
[INFO ] 2024-10-11 15:57:35.896 - [任务 8(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 15:57:35.896 - [任务 8(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 15:57:36.105 - [任务 8(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 15:58:26.036 - [任务 8(100)][1690efd7-580a-41e9-ad72-a946a1951025] - Node 1690efd7-580a-41e9-ad72-a946a1951025[1690efd7-580a-41e9-ad72-a946a1951025] start preload schema,table counts: 0 
[INFO ] 2024-10-11 15:58:26.036 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:58:26.036 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:58:26.036 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:58:26.036 - [任务 8(100)][1690efd7-580a-41e9-ad72-a946a1951025] - Node 1690efd7-580a-41e9-ad72-a946a1951025[1690efd7-580a-41e9-ad72-a946a1951025] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 15:58:26.036 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 15:58:26.036 - [任务 8(100)][增强JS] - Node js_processor(增强JS: 9d8b036b-1579-4638-974e-c9caacc934d7) enable batch process 
[INFO ] 2024-10-11 15:58:31.549 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] running status set to false 
[INFO ] 2024-10-11 15:58:31.580 - [任务 8(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:58:31.581 - [任务 8(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:58:31.581 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] schema data cleaned 
[INFO ] 2024-10-11 15:58:31.581 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] monitor closed 
[INFO ] 2024-10-11 15:58:31.582 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] close complete, cost 32 ms 
[INFO ] 2024-10-11 15:58:37.420 - [任务 8(100)][0aa040f3-8cf1-41d3-91ab-136a5e88f343] - Node 0aa040f3-8cf1-41d3-91ab-136a5e88f343[0aa040f3-8cf1-41d3-91ab-136a5e88f343] start preload schema,table counts: 0 
[INFO ] 2024-10-11 15:58:37.421 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:58:37.421 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] start preload schema,table counts: 1 
[INFO ] 2024-10-11 15:58:37.421 - [任务 8(100)][0aa040f3-8cf1-41d3-91ab-136a5e88f343] - Node 0aa040f3-8cf1-41d3-91ab-136a5e88f343[0aa040f3-8cf1-41d3-91ab-136a5e88f343] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 15:58:37.421 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 15:58:37.421 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] preload schema finished, cost 3 ms 
[INFO ] 2024-10-11 15:58:37.422 - [任务 8(100)][增强JS] - Node js_processor(增强JS: 9d8b036b-1579-4638-974e-c9caacc934d7) enable batch process 
[INFO ] 2024-10-11 15:58:37.487 - [任务 8(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: java.lang.UnsupportedOperationException 
[ERROR] 2024-10-11 15:58:37.489 - [任务 8(100)][增强JS] - javax.script.ScriptException: java.lang.UnsupportedOperationException <-- Error Message -->
javax.script.ScriptException: java.lang.UnsupportedOperationException

<-- Simple Stack Trace -->
Caused by: java.lang.UnsupportedOperationException: null
	java.util.AbstractCollection.add(AbstractCollection.java:262)
	com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: java.lang.UnsupportedOperationException
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: java.lang.UnsupportedOperationException
	at java.util.AbstractCollection.add(AbstractCollection.java:262)
	at <js>.process(<eval>:3)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: java.lang.UnsupportedOperationException
	at java.util.AbstractCollection.add(AbstractCollection.java:262)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	at com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	at com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callInlined(DefaultRuntimeAccessor.java:142)
	at com.oracle.truffle.host.GuestToHostRootNode.guestToHostCall(GuestToHostRootNode.java:107)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeGuestToHost(HostMethodDesc.java:364)
	at com.oracle.truffle.host.HostExecuteNode.doInvoke(HostExecuteNode.java:873)
	at com.oracle.truffle.host.HostExecuteNode.doFixed(HostExecuteNode.java:137)
	at com.oracle.truffle.host.HostExecuteNodeGen.executeAndSpecialize(HostExecuteNodeGen.java:143)
	at com.oracle.truffle.host.HostExecuteNodeGen.execute(HostExecuteNodeGen.java:96)
	at com.oracle.truffle.host.HostObject.invokeMember(HostObject.java:451)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMemberNode_AndSpecialize(HostObjectGen.java:2695)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMember(HostObjectGen.java:2677)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.invokeMember(InteropLibraryGen.java:8138)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$ForeignInvokeNode.executeCall(JSFunctionCallNode.java:1475)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$InvokeNode.execute(JSFunctionCallNode.java:722)
	at com.oracle.truffle.js.nodes.JavaScriptNode.executeVoid(JavaScriptNode.java:191)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:80)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:55)
	at com.oracle.truffle.api.impl.DefaultBlockNode.executeGeneric(DefaultBlockNode.java:63)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.execute(AbstractBlockNode.java:75)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:125)
	at com.oracle.truffle.js.nodes.function.FunctionBodyNode.execute(FunctionBodyNode.java:73)
	at com.oracle.truffle.js.nodes.function.FunctionRootNode.executeInRealm(FunctionRootNode.java:150)
	at com.oracle.truffle.js.runtime.JavaScriptRealmBoundaryRootNode.execute(JavaScriptRealmBoundaryRootNode.java:93)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultDirectCallNode.call(DefaultDirectCallNode.java:59)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$UnboundJSFunctionCacheNode.executeCall(JSFunctionCallNode.java:1256)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNode.doDefault(JSInteropExecuteNode.java:68)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.executeAndSpecialize(JSInteropExecuteNodeGen.java:61)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.execute(JSInteropExecuteNodeGen.java:43)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunction.execute(InteropBoundFunction.java:111)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.executeNode_AndSpecialize(InteropBoundFunctionGen.java:188)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.execute(InteropBoundFunctionGen.java:171)
	at com.oracle.truffle.api.interop.InteropLibraryGen$Delegate.execute(InteropLibraryGen.java:3801)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.execute(InteropLibraryGen.java:7592)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$AbstractExecuteNode.executeShared(PolyglotValueDispatch.java:4231)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$ExecuteNode.executeImpl(PolyglotValueDispatch.java:4312)
	at com.oracle.truffle.polyglot.HostToGuestRootNode.execute(HostToGuestRootNode.java:127)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultCallTarget.call(DefaultCallTarget.java:102)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callProfiled(DefaultRuntimeAccessor.java:147)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue.execute(PolyglotValueDispatch.java:2369)
	... 4 more

[INFO ] 2024-10-11 15:58:40.034 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] running status set to false 
[INFO ] 2024-10-11 15:58:40.043 - [任务 8(100)][1690efd7-580a-41e9-ad72-a946a1951025] - Node 1690efd7-580a-41e9-ad72-a946a1951025[1690efd7-580a-41e9-ad72-a946a1951025] running status set to false 
[INFO ] 2024-10-11 15:58:40.043 - [任务 8(100)][1690efd7-580a-41e9-ad72-a946a1951025] - Node 1690efd7-580a-41e9-ad72-a946a1951025[1690efd7-580a-41e9-ad72-a946a1951025] schema data cleaned 
[INFO ] 2024-10-11 15:58:40.046 - [任务 8(100)][1690efd7-580a-41e9-ad72-a946a1951025] - Node 1690efd7-580a-41e9-ad72-a946a1951025[1690efd7-580a-41e9-ad72-a946a1951025] monitor closed 
[INFO ] 2024-10-11 15:58:40.047 - [任务 8(100)][1690efd7-580a-41e9-ad72-a946a1951025] - Node 1690efd7-580a-41e9-ad72-a946a1951025[1690efd7-580a-41e9-ad72-a946a1951025] close complete, cost 22 ms 
[INFO ] 2024-10-11 15:58:40.134 - [任务 8(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-fcfcf75e-9012-4b9d-82f1-61637acc4656 
[INFO ] 2024-10-11 15:58:40.134 - [任务 8(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-fcfcf75e-9012-4b9d-82f1-61637acc4656 
[INFO ] 2024-10-11 15:58:40.134 - [任务 8(100)][增强JS] - [ScriptExecutorsManager-6708d9cb82af0a589c4e685c-9d8b036b-1579-4638-974e-c9caacc934d7-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 15:58:40.139 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] schema data cleaned 
[INFO ] 2024-10-11 15:58:40.139 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] monitor closed 
[INFO ] 2024-10-11 15:58:40.139 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] close complete, cost 116 ms 
[INFO ] 2024-10-11 15:58:40.146 - [任务 8(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 15:58:40.149 - [任务 8(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 15:58:40.149 - [任务 8(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 15:58:43.881 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] running status set to false 
[INFO ] 2024-10-11 15:58:43.882 - [任务 8(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:58:43.882 - [任务 8(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 15:58:43.882 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] schema data cleaned 
[INFO ] 2024-10-11 15:58:43.883 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] monitor closed 
[INFO ] 2024-10-11 15:58:43.883 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] close complete, cost 41 ms 
[INFO ] 2024-10-11 15:58:49.545 - [任务 8(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: java.lang.UnsupportedOperationException 
[ERROR] 2024-10-11 15:58:49.546 - [任务 8(100)][增强JS] - javax.script.ScriptException: java.lang.UnsupportedOperationException <-- Error Message -->
javax.script.ScriptException: java.lang.UnsupportedOperationException

<-- Simple Stack Trace -->
Caused by: java.lang.UnsupportedOperationException: null
	java.util.AbstractCollection.add(AbstractCollection.java:262)
	com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: java.lang.UnsupportedOperationException
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: java.lang.UnsupportedOperationException
	at java.util.AbstractCollection.add(AbstractCollection.java:262)
	at <js>.process(<eval>:3)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: java.lang.UnsupportedOperationException
	at java.util.AbstractCollection.add(AbstractCollection.java:262)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	at com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	at com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callInlined(DefaultRuntimeAccessor.java:142)
	at com.oracle.truffle.host.GuestToHostRootNode.guestToHostCall(GuestToHostRootNode.java:107)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeGuestToHost(HostMethodDesc.java:364)
	at com.oracle.truffle.host.HostExecuteNode.doInvoke(HostExecuteNode.java:873)
	at com.oracle.truffle.host.HostExecuteNode.doFixed(HostExecuteNode.java:137)
	at com.oracle.truffle.host.HostExecuteNodeGen.executeAndSpecialize(HostExecuteNodeGen.java:143)
	at com.oracle.truffle.host.HostExecuteNodeGen.execute(HostExecuteNodeGen.java:96)
	at com.oracle.truffle.host.HostObject.invokeMember(HostObject.java:451)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMemberNode_AndSpecialize(HostObjectGen.java:2695)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMember(HostObjectGen.java:2677)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.invokeMember(InteropLibraryGen.java:8138)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$ForeignInvokeNode.executeCall(JSFunctionCallNode.java:1475)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$InvokeNode.execute(JSFunctionCallNode.java:722)
	at com.oracle.truffle.js.nodes.JavaScriptNode.executeVoid(JavaScriptNode.java:191)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:80)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:55)
	at com.oracle.truffle.api.impl.DefaultBlockNode.executeGeneric(DefaultBlockNode.java:63)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.execute(AbstractBlockNode.java:75)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:125)
	at com.oracle.truffle.js.nodes.function.FunctionBodyNode.execute(FunctionBodyNode.java:73)
	at com.oracle.truffle.js.nodes.function.FunctionRootNode.executeInRealm(FunctionRootNode.java:150)
	at com.oracle.truffle.js.runtime.JavaScriptRealmBoundaryRootNode.execute(JavaScriptRealmBoundaryRootNode.java:93)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultDirectCallNode.call(DefaultDirectCallNode.java:59)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$UnboundJSFunctionCacheNode.executeCall(JSFunctionCallNode.java:1256)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNode.doDefault(JSInteropExecuteNode.java:68)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.executeAndSpecialize(JSInteropExecuteNodeGen.java:61)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.execute(JSInteropExecuteNodeGen.java:43)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunction.execute(InteropBoundFunction.java:111)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.executeNode_AndSpecialize(InteropBoundFunctionGen.java:188)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.execute(InteropBoundFunctionGen.java:171)
	at com.oracle.truffle.api.interop.InteropLibraryGen$Delegate.execute(InteropLibraryGen.java:3801)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.execute(InteropLibraryGen.java:7592)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$AbstractExecuteNode.executeShared(PolyglotValueDispatch.java:4231)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$ExecuteNode.executeImpl(PolyglotValueDispatch.java:4312)
	at com.oracle.truffle.polyglot.HostToGuestRootNode.execute(HostToGuestRootNode.java:127)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultCallTarget.call(DefaultCallTarget.java:102)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callProfiled(DefaultRuntimeAccessor.java:147)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue.execute(PolyglotValueDispatch.java:2369)
	... 4 more

[INFO ] 2024-10-11 15:58:52.073 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] running status set to false 
[INFO ] 2024-10-11 15:58:52.076 - [任务 8(100)][0aa040f3-8cf1-41d3-91ab-136a5e88f343] - Node 0aa040f3-8cf1-41d3-91ab-136a5e88f343[0aa040f3-8cf1-41d3-91ab-136a5e88f343] running status set to false 
[INFO ] 2024-10-11 15:58:52.076 - [任务 8(100)][0aa040f3-8cf1-41d3-91ab-136a5e88f343] - Node 0aa040f3-8cf1-41d3-91ab-136a5e88f343[0aa040f3-8cf1-41d3-91ab-136a5e88f343] schema data cleaned 
[INFO ] 2024-10-11 15:58:52.076 - [任务 8(100)][0aa040f3-8cf1-41d3-91ab-136a5e88f343] - Node 0aa040f3-8cf1-41d3-91ab-136a5e88f343[0aa040f3-8cf1-41d3-91ab-136a5e88f343] monitor closed 
[INFO ] 2024-10-11 15:58:52.097 - [任务 8(100)][0aa040f3-8cf1-41d3-91ab-136a5e88f343] - Node 0aa040f3-8cf1-41d3-91ab-136a5e88f343[0aa040f3-8cf1-41d3-91ab-136a5e88f343] close complete, cost 1 ms 
[INFO ] 2024-10-11 15:58:52.097 - [任务 8(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-ab15a1f2-fcf7-4262-8970-8370f358d0ed 
[INFO ] 2024-10-11 15:58:52.097 - [任务 8(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-ab15a1f2-fcf7-4262-8970-8370f358d0ed 
[INFO ] 2024-10-11 15:58:52.097 - [任务 8(100)][增强JS] - [ScriptExecutorsManager-6708d9cb82af0a589c4e685c-9d8b036b-1579-4638-974e-c9caacc934d7-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 15:58:52.100 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] schema data cleaned 
[INFO ] 2024-10-11 15:58:52.100 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] monitor closed 
[INFO ] 2024-10-11 15:58:52.103 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] close complete, cost 28 ms 
[INFO ] 2024-10-11 15:58:52.103 - [任务 8(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 15:58:52.103 - [任务 8(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 15:58:52.306 - [任务 8(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 16:02:20.931 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] start preload schema,table counts: 1 
[INFO ] 2024-10-11 16:02:20.939 - [任务 8(100)][8feb5657-2e09-4f54-a089-940c8dac0f95] - Node 8feb5657-2e09-4f54-a089-940c8dac0f95[8feb5657-2e09-4f54-a089-940c8dac0f95] start preload schema,table counts: 0 
[INFO ] 2024-10-11 16:02:20.939 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] start preload schema,table counts: 1 
[INFO ] 2024-10-11 16:02:20.953 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 16:02:20.956 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 16:02:20.956 - [任务 8(100)][8feb5657-2e09-4f54-a089-940c8dac0f95] - Node 8feb5657-2e09-4f54-a089-940c8dac0f95[8feb5657-2e09-4f54-a089-940c8dac0f95] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 16:02:21.161 - [任务 8(100)][增强JS] - Node js_processor(增强JS: 9d8b036b-1579-4638-974e-c9caacc934d7) enable batch process 
[INFO ] 2024-10-11 16:02:26.979 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] running status set to false 
[INFO ] 2024-10-11 16:02:26.980 - [任务 8(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 16:02:26.980 - [任务 8(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 16:02:26.981 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] schema data cleaned 
[INFO ] 2024-10-11 16:02:26.982 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] monitor closed 
[INFO ] 2024-10-11 16:02:26.982 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] close complete, cost 74 ms 
[INFO ] 2024-10-11 16:02:32.757 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] running status set to false 
[INFO ] 2024-10-11 16:02:32.772 - [任务 8(100)][8feb5657-2e09-4f54-a089-940c8dac0f95] - Node 8feb5657-2e09-4f54-a089-940c8dac0f95[8feb5657-2e09-4f54-a089-940c8dac0f95] running status set to false 
[INFO ] 2024-10-11 16:02:32.777 - [任务 8(100)][8feb5657-2e09-4f54-a089-940c8dac0f95] - Node 8feb5657-2e09-4f54-a089-940c8dac0f95[8feb5657-2e09-4f54-a089-940c8dac0f95] schema data cleaned 
[INFO ] 2024-10-11 16:02:32.777 - [任务 8(100)][8feb5657-2e09-4f54-a089-940c8dac0f95] - Node 8feb5657-2e09-4f54-a089-940c8dac0f95[8feb5657-2e09-4f54-a089-940c8dac0f95] monitor closed 
[INFO ] 2024-10-11 16:02:32.777 - [任务 8(100)][8feb5657-2e09-4f54-a089-940c8dac0f95] - Node 8feb5657-2e09-4f54-a089-940c8dac0f95[8feb5657-2e09-4f54-a089-940c8dac0f95] close complete, cost 2 ms 
[INFO ] 2024-10-11 16:02:32.797 - [任务 8(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-614ceb8f-1a0c-475b-99bd-b7fcd84f8846 
[INFO ] 2024-10-11 16:02:32.797 - [任务 8(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-614ceb8f-1a0c-475b-99bd-b7fcd84f8846 
[INFO ] 2024-10-11 16:02:32.797 - [任务 8(100)][增强JS] - [ScriptExecutorsManager-6708d9cb82af0a589c4e685c-9d8b036b-1579-4638-974e-c9caacc934d7-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 16:02:32.800 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] schema data cleaned 
[INFO ] 2024-10-11 16:02:32.801 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] monitor closed 
[INFO ] 2024-10-11 16:02:32.801 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] close complete, cost 57 ms 
[INFO ] 2024-10-11 16:02:32.805 - [任务 8(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 16:02:32.805 - [任务 8(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 16:02:32.806 - [任务 8(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 16:04:08.209 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] start preload schema,table counts: 1 
[INFO ] 2024-10-11 16:04:08.212 - [任务 8(100)][cb1299f5-f465-403c-ba92-6c06c9a6f031] - Node cb1299f5-f465-403c-ba92-6c06c9a6f031[cb1299f5-f465-403c-ba92-6c06c9a6f031] start preload schema,table counts: 0 
[INFO ] 2024-10-11 16:04:08.212 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] start preload schema,table counts: 1 
[INFO ] 2024-10-11 16:04:08.212 - [任务 8(100)][cb1299f5-f465-403c-ba92-6c06c9a6f031] - Node cb1299f5-f465-403c-ba92-6c06c9a6f031[cb1299f5-f465-403c-ba92-6c06c9a6f031] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 16:04:08.212 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] preload schema finished, cost 2 ms 
[INFO ] 2024-10-11 16:04:08.212 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] preload schema finished, cost 2 ms 
[INFO ] 2024-10-11 16:04:08.212 - [任务 8(100)][增强JS] - Node js_processor(增强JS: 9d8b036b-1579-4638-974e-c9caacc934d7) enable batch process 
[INFO ] 2024-10-11 16:04:13.821 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] running status set to false 
[INFO ] 2024-10-11 16:04:13.891 - [任务 8(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 16:04:13.893 - [任务 8(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 16:04:13.893 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] schema data cleaned 
[INFO ] 2024-10-11 16:04:13.893 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] monitor closed 
[INFO ] 2024-10-11 16:04:13.894 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] close complete, cost 88 ms 
[INFO ] 2024-10-11 16:04:19.575 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] running status set to false 
[INFO ] 2024-10-11 16:04:19.576 - [任务 8(100)][cb1299f5-f465-403c-ba92-6c06c9a6f031] - Node cb1299f5-f465-403c-ba92-6c06c9a6f031[cb1299f5-f465-403c-ba92-6c06c9a6f031] running status set to false 
[INFO ] 2024-10-11 16:04:19.576 - [任务 8(100)][cb1299f5-f465-403c-ba92-6c06c9a6f031] - Node cb1299f5-f465-403c-ba92-6c06c9a6f031[cb1299f5-f465-403c-ba92-6c06c9a6f031] schema data cleaned 
[INFO ] 2024-10-11 16:04:19.576 - [任务 8(100)][cb1299f5-f465-403c-ba92-6c06c9a6f031] - Node cb1299f5-f465-403c-ba92-6c06c9a6f031[cb1299f5-f465-403c-ba92-6c06c9a6f031] monitor closed 
[INFO ] 2024-10-11 16:04:19.576 - [任务 8(100)][cb1299f5-f465-403c-ba92-6c06c9a6f031] - Node cb1299f5-f465-403c-ba92-6c06c9a6f031[cb1299f5-f465-403c-ba92-6c06c9a6f031] close complete, cost 1 ms 
[INFO ] 2024-10-11 16:04:19.593 - [任务 8(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-7bc67e2e-acfe-4844-947d-fd0cd6a30a92 
[INFO ] 2024-10-11 16:04:19.593 - [任务 8(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-7bc67e2e-acfe-4844-947d-fd0cd6a30a92 
[INFO ] 2024-10-11 16:04:19.593 - [任务 8(100)][增强JS] - [ScriptExecutorsManager-6708d9cb82af0a589c4e685c-9d8b036b-1579-4638-974e-c9caacc934d7-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 16:04:19.596 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] schema data cleaned 
[INFO ] 2024-10-11 16:04:19.596 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] monitor closed 
[INFO ] 2024-10-11 16:04:19.597 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] close complete, cost 28 ms 
[INFO ] 2024-10-11 16:04:19.599 - [任务 8(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 16:04:19.599 - [任务 8(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 16:04:19.600 - [任务 8(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 16:04:52.729 - [任务 8(100)][8aa08986-97ab-420e-aacd-7263fc2ec452] - Node 8aa08986-97ab-420e-aacd-7263fc2ec452[8aa08986-97ab-420e-aacd-7263fc2ec452] start preload schema,table counts: 0 
[INFO ] 2024-10-11 16:04:52.730 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] start preload schema,table counts: 1 
[INFO ] 2024-10-11 16:04:52.730 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] start preload schema,table counts: 1 
[INFO ] 2024-10-11 16:04:52.730 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 16:04:52.730 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 16:04:52.730 - [任务 8(100)][8aa08986-97ab-420e-aacd-7263fc2ec452] - Node 8aa08986-97ab-420e-aacd-7263fc2ec452[8aa08986-97ab-420e-aacd-7263fc2ec452] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 16:04:52.764 - [任务 8(100)][增强JS] - Node js_processor(增强JS: 9d8b036b-1579-4638-974e-c9caacc934d7) enable batch process 
[INFO ] 2024-10-11 16:05:03.799 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] running status set to false 
[INFO ] 2024-10-11 16:05:03.838 - [任务 8(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 16:05:03.845 - [任务 8(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 16:05:03.847 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] schema data cleaned 
[INFO ] 2024-10-11 16:05:03.847 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] monitor closed 
[INFO ] 2024-10-11 16:05:03.850 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] close complete, cost 65 ms 
[INFO ] 2024-10-11 16:05:09.235 - [任务 8(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: java.lang.UnsupportedOperationException 
[ERROR] 2024-10-11 16:05:09.236 - [任务 8(100)][增强JS] - javax.script.ScriptException: java.lang.UnsupportedOperationException <-- Error Message -->
javax.script.ScriptException: java.lang.UnsupportedOperationException

<-- Simple Stack Trace -->
Caused by: java.lang.UnsupportedOperationException: null
	java.util.AbstractCollection.add(AbstractCollection.java:262)
	com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: java.lang.UnsupportedOperationException
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: java.lang.UnsupportedOperationException
	at java.util.AbstractCollection.add(AbstractCollection.java:262)
	at <js>.process(<eval>:3)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: java.lang.UnsupportedOperationException
	at java.util.AbstractCollection.add(AbstractCollection.java:262)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	at com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	at com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callInlined(DefaultRuntimeAccessor.java:142)
	at com.oracle.truffle.host.GuestToHostRootNode.guestToHostCall(GuestToHostRootNode.java:107)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeGuestToHost(HostMethodDesc.java:364)
	at com.oracle.truffle.host.HostExecuteNode.doInvoke(HostExecuteNode.java:873)
	at com.oracle.truffle.host.HostExecuteNode.doFixed(HostExecuteNode.java:137)
	at com.oracle.truffle.host.HostExecuteNodeGen.executeAndSpecialize(HostExecuteNodeGen.java:143)
	at com.oracle.truffle.host.HostExecuteNodeGen.execute(HostExecuteNodeGen.java:96)
	at com.oracle.truffle.host.HostObject.invokeMember(HostObject.java:451)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMemberNode_AndSpecialize(HostObjectGen.java:2695)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMember(HostObjectGen.java:2677)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.invokeMember(InteropLibraryGen.java:8138)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$ForeignInvokeNode.executeCall(JSFunctionCallNode.java:1475)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$InvokeNode.execute(JSFunctionCallNode.java:722)
	at com.oracle.truffle.js.nodes.JavaScriptNode.executeVoid(JavaScriptNode.java:191)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:80)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:55)
	at com.oracle.truffle.api.impl.DefaultBlockNode.executeGeneric(DefaultBlockNode.java:63)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.execute(AbstractBlockNode.java:75)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:125)
	at com.oracle.truffle.js.nodes.function.FunctionBodyNode.execute(FunctionBodyNode.java:73)
	at com.oracle.truffle.js.nodes.function.FunctionRootNode.executeInRealm(FunctionRootNode.java:150)
	at com.oracle.truffle.js.runtime.JavaScriptRealmBoundaryRootNode.execute(JavaScriptRealmBoundaryRootNode.java:93)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultDirectCallNode.call(DefaultDirectCallNode.java:59)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$UnboundJSFunctionCacheNode.executeCall(JSFunctionCallNode.java:1256)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNode.doDefault(JSInteropExecuteNode.java:68)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.executeAndSpecialize(JSInteropExecuteNodeGen.java:61)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.execute(JSInteropExecuteNodeGen.java:43)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunction.execute(InteropBoundFunction.java:111)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.executeNode_AndSpecialize(InteropBoundFunctionGen.java:188)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.execute(InteropBoundFunctionGen.java:171)
	at com.oracle.truffle.api.interop.InteropLibraryGen$Delegate.execute(InteropLibraryGen.java:3801)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.execute(InteropLibraryGen.java:7592)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$AbstractExecuteNode.executeShared(PolyglotValueDispatch.java:4231)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$ExecuteNode.executeImpl(PolyglotValueDispatch.java:4312)
	at com.oracle.truffle.polyglot.HostToGuestRootNode.execute(HostToGuestRootNode.java:127)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultCallTarget.call(DefaultCallTarget.java:102)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callProfiled(DefaultRuntimeAccessor.java:147)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue.execute(PolyglotValueDispatch.java:2369)
	... 4 more

[INFO ] 2024-10-11 16:05:11.785 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] running status set to false 
[INFO ] 2024-10-11 16:05:11.790 - [任务 8(100)][8aa08986-97ab-420e-aacd-7263fc2ec452] - Node 8aa08986-97ab-420e-aacd-7263fc2ec452[8aa08986-97ab-420e-aacd-7263fc2ec452] running status set to false 
[INFO ] 2024-10-11 16:05:11.790 - [任务 8(100)][8aa08986-97ab-420e-aacd-7263fc2ec452] - Node 8aa08986-97ab-420e-aacd-7263fc2ec452[8aa08986-97ab-420e-aacd-7263fc2ec452] schema data cleaned 
[INFO ] 2024-10-11 16:05:11.790 - [任务 8(100)][8aa08986-97ab-420e-aacd-7263fc2ec452] - Node 8aa08986-97ab-420e-aacd-7263fc2ec452[8aa08986-97ab-420e-aacd-7263fc2ec452] monitor closed 
[INFO ] 2024-10-11 16:05:11.790 - [任务 8(100)][8aa08986-97ab-420e-aacd-7263fc2ec452] - Node 8aa08986-97ab-420e-aacd-7263fc2ec452[8aa08986-97ab-420e-aacd-7263fc2ec452] close complete, cost 2 ms 
[INFO ] 2024-10-11 16:05:11.835 - [任务 8(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-81df3476-e6d2-4128-9d95-49f45ff5fb84 
[INFO ] 2024-10-11 16:05:11.835 - [任务 8(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-81df3476-e6d2-4128-9d95-49f45ff5fb84 
[INFO ] 2024-10-11 16:05:11.835 - [任务 8(100)][增强JS] - [ScriptExecutorsManager-6708d9cb82af0a589c4e685c-9d8b036b-1579-4638-974e-c9caacc934d7-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 16:05:11.839 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] schema data cleaned 
[INFO ] 2024-10-11 16:05:11.839 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] monitor closed 
[INFO ] 2024-10-11 16:05:11.839 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] close complete, cost 59 ms 
[INFO ] 2024-10-11 16:05:11.841 - [任务 8(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 16:05:11.841 - [任务 8(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 16:05:11.842 - [任务 8(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 16:14:48.462 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] start preload schema,table counts: 1 
[INFO ] 2024-10-11 16:14:48.464 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] start preload schema,table counts: 1 
[INFO ] 2024-10-11 16:14:48.472 - [任务 8(100)][29c7c17c-00b9-441b-aa42-01df17ff6697] - Node 29c7c17c-00b9-441b-aa42-01df17ff6697[29c7c17c-00b9-441b-aa42-01df17ff6697] start preload schema,table counts: 0 
[INFO ] 2024-10-11 16:14:48.477 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 16:14:48.479 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 16:14:48.479 - [任务 8(100)][29c7c17c-00b9-441b-aa42-01df17ff6697] - Node 29c7c17c-00b9-441b-aa42-01df17ff6697[29c7c17c-00b9-441b-aa42-01df17ff6697] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 16:14:48.479 - [任务 8(100)][增强JS] - Node js_processor(增强JS: 9d8b036b-1579-4638-974e-c9caacc934d7) enable batch process 
[INFO ] 2024-10-11 16:14:54.355 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] running status set to false 
[INFO ] 2024-10-11 16:15:32.517 - [任务 8(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 16:15:32.517 - [任务 8(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37 
[INFO ] 2024-10-11 16:15:32.518 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] schema data cleaned 
[INFO ] 2024-10-11 16:15:32.518 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] monitor closed 
[INFO ] 2024-10-11 16:15:32.726 - [任务 8(100)][BMSQL_ITEM] - Node BMSQL_ITEM[62e39c91-c3a7-4cc7-87d7-bd42c5c0ab37] close complete, cost 38168 ms 
[INFO ] 2024-10-11 16:15:38.187 - [任务 8(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: java.lang.UnsupportedOperationException 
[ERROR] 2024-10-11 16:15:38.392 - [任务 8(100)][增强JS] - javax.script.ScriptException: java.lang.UnsupportedOperationException <-- Error Message -->
javax.script.ScriptException: java.lang.UnsupportedOperationException

<-- Simple Stack Trace -->
Caused by: java.lang.UnsupportedOperationException: null
	java.util.AbstractCollection.add(AbstractCollection.java:262)
	com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: java.lang.UnsupportedOperationException
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: java.lang.UnsupportedOperationException
	at java.util.AbstractCollection.add(AbstractCollection.java:262)
	at <js>.process(<eval>:4)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: java.lang.UnsupportedOperationException
	at java.util.AbstractCollection.add(AbstractCollection.java:262)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeHandle(HostMethodDesc.java:333)
	at com.oracle.truffle.host.GuestToHostCodeCache$1.executeImpl(GuestToHostCodeCache.java:98)
	at com.oracle.truffle.host.GuestToHostRootNode.execute(GuestToHostRootNode.java:81)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callInlined(DefaultRuntimeAccessor.java:142)
	at com.oracle.truffle.host.GuestToHostRootNode.guestToHostCall(GuestToHostRootNode.java:107)
	at com.oracle.truffle.host.HostMethodDesc$SingleMethod$MHBase.invokeGuestToHost(HostMethodDesc.java:364)
	at com.oracle.truffle.host.HostExecuteNode.doInvoke(HostExecuteNode.java:873)
	at com.oracle.truffle.host.HostExecuteNode.doFixed(HostExecuteNode.java:137)
	at com.oracle.truffle.host.HostExecuteNodeGen.executeAndSpecialize(HostExecuteNodeGen.java:143)
	at com.oracle.truffle.host.HostExecuteNodeGen.execute(HostExecuteNodeGen.java:96)
	at com.oracle.truffle.host.HostObject.invokeMember(HostObject.java:451)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMemberNode_AndSpecialize(HostObjectGen.java:2695)
	at com.oracle.truffle.host.HostObjectGen$InteropLibraryExports$Cached.invokeMember(HostObjectGen.java:2677)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.invokeMember(InteropLibraryGen.java:8138)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$ForeignInvokeNode.executeCall(JSFunctionCallNode.java:1475)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$InvokeNode.execute(JSFunctionCallNode.java:722)
	at com.oracle.truffle.js.nodes.JavaScriptNode.executeVoid(JavaScriptNode.java:191)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:80)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.executeVoid(AbstractBlockNode.java:55)
	at com.oracle.truffle.api.impl.DefaultBlockNode.executeGeneric(DefaultBlockNode.java:63)
	at com.oracle.truffle.js.nodes.control.AbstractBlockNode.execute(AbstractBlockNode.java:75)
	at com.oracle.truffle.js.nodes.binary.DualNode.execute(DualNode.java:125)
	at com.oracle.truffle.js.nodes.function.FunctionBodyNode.execute(FunctionBodyNode.java:73)
	at com.oracle.truffle.js.nodes.function.FunctionRootNode.executeInRealm(FunctionRootNode.java:150)
	at com.oracle.truffle.js.runtime.JavaScriptRealmBoundaryRootNode.execute(JavaScriptRealmBoundaryRootNode.java:93)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultDirectCallNode.call(DefaultDirectCallNode.java:59)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode$UnboundJSFunctionCacheNode.executeCall(JSFunctionCallNode.java:1256)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeAndSpecialize(JSFunctionCallNode.java:303)
	at com.oracle.truffle.js.nodes.function.JSFunctionCallNode.executeCall(JSFunctionCallNode.java:249)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNode.doDefault(JSInteropExecuteNode.java:68)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.executeAndSpecialize(JSInteropExecuteNodeGen.java:61)
	at com.oracle.truffle.js.nodes.interop.JSInteropExecuteNodeGen.execute(JSInteropExecuteNodeGen.java:43)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunction.execute(InteropBoundFunction.java:111)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.executeNode_AndSpecialize(InteropBoundFunctionGen.java:188)
	at com.oracle.truffle.js.runtime.interop.InteropBoundFunctionGen$InteropLibraryExports$Cached.execute(InteropBoundFunctionGen.java:171)
	at com.oracle.truffle.api.interop.InteropLibraryGen$Delegate.execute(InteropLibraryGen.java:3801)
	at com.oracle.truffle.api.interop.InteropLibraryGen$CachedDispatch.execute(InteropLibraryGen.java:7592)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$AbstractExecuteNode.executeShared(PolyglotValueDispatch.java:4231)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue$ExecuteNode.executeImpl(PolyglotValueDispatch.java:4312)
	at com.oracle.truffle.polyglot.HostToGuestRootNode.execute(HostToGuestRootNode.java:127)
	at com.oracle.truffle.api.impl.DefaultCallTarget.callDirectOrIndirect(DefaultCallTarget.java:85)
	at com.oracle.truffle.api.impl.DefaultCallTarget.call(DefaultCallTarget.java:102)
	at com.oracle.truffle.api.impl.DefaultRuntimeAccessor$DefaultRuntimeSupport.callProfiled(DefaultRuntimeAccessor.java:147)
	at com.oracle.truffle.polyglot.PolyglotValueDispatch$InteropValue.execute(PolyglotValueDispatch.java:2369)
	... 4 more

[INFO ] 2024-10-11 16:15:40.737 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] running status set to false 
[INFO ] 2024-10-11 16:15:40.750 - [任务 8(100)][29c7c17c-00b9-441b-aa42-01df17ff6697] - Node 29c7c17c-00b9-441b-aa42-01df17ff6697[29c7c17c-00b9-441b-aa42-01df17ff6697] running status set to false 
[INFO ] 2024-10-11 16:15:40.758 - [任务 8(100)][29c7c17c-00b9-441b-aa42-01df17ff6697] - Node 29c7c17c-00b9-441b-aa42-01df17ff6697[29c7c17c-00b9-441b-aa42-01df17ff6697] schema data cleaned 
[INFO ] 2024-10-11 16:15:40.766 - [任务 8(100)][29c7c17c-00b9-441b-aa42-01df17ff6697] - Node 29c7c17c-00b9-441b-aa42-01df17ff6697[29c7c17c-00b9-441b-aa42-01df17ff6697] monitor closed 
[INFO ] 2024-10-11 16:15:40.771 - [任务 8(100)][29c7c17c-00b9-441b-aa42-01df17ff6697] - Node 29c7c17c-00b9-441b-aa42-01df17ff6697[29c7c17c-00b9-441b-aa42-01df17ff6697] close complete, cost 30 ms 
[INFO ] 2024-10-11 16:15:40.786 - [任务 8(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-b57a6d4a-45a1-4213-965d-bd25ce34c19f 
[INFO ] 2024-10-11 16:15:40.787 - [任务 8(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-b57a6d4a-45a1-4213-965d-bd25ce34c19f 
[INFO ] 2024-10-11 16:15:40.789 - [任务 8(100)][增强JS] - [ScriptExecutorsManager-6708d9cb82af0a589c4e685c-9d8b036b-1579-4638-974e-c9caacc934d7-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 16:15:40.789 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] schema data cleaned 
[INFO ] 2024-10-11 16:15:40.790 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] monitor closed 
[INFO ] 2024-10-11 16:15:40.790 - [任务 8(100)][增强JS] - Node 增强JS[9d8b036b-1579-4638-974e-c9caacc934d7] close complete, cost 59 ms 
[INFO ] 2024-10-11 16:15:40.795 - [任务 8(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 16:15:40.795 - [任务 8(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 16:15:40.796 - [任务 8(100)] - Stopped task aspect(s) 
