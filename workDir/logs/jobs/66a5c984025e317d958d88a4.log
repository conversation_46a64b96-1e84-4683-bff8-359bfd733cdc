[INFO ] 2024-07-28 12:32:17.617 - [任务 1] - Start task milestones: 66a5c984025e317d958d88a4(任务 1) 
[INFO ] 2024-07-28 12:32:17.786 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 12:32:18.772 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 12:32:18.841 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 12:32:19.340 - [任务 1][POLICY] - Node POLICY[6a085553-a258-4398-bf0c-7102a27b5276] start preload schema,table counts: 1 
[INFO ] 2024-07-28 12:32:19.340 - [任务 1][TestPO] - Node TestPO[db462da6-8546-4e11-860c-35125ac559ff] start preload schema,table counts: 1 
[INFO ] 2024-07-28 12:32:19.341 - [任务 1][POLICY] - Node POLICY[6a085553-a258-4398-bf0c-7102a27b5276] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 12:32:19.342 - [任务 1][TestPO] - Node TestPO[db462da6-8546-4e11-860c-35125ac559ff] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 12:32:20.637 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-28 12:32:20.648 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-28 12:32:20.649 - [任务 1][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-28 12:32:20.862 - [任务 1][TestPO] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 12:32:20.899 - [任务 1][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1722141140,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 12:32:20.952 - [任务 1][POLICY] - Initial sync started 
[INFO ] 2024-07-28 12:32:20.953 - [任务 1][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-28 12:32:20.961 - [任务 1][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-28 12:32:21.027 - [任务 1][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-28 12:32:21.027 - [任务 1][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-28 12:32:21.028 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-28 12:32:21.029 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-28 12:32:21.029 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-28 12:32:21.031 - [任务 1][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1722141140,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 12:32:21.251 - [任务 1][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
