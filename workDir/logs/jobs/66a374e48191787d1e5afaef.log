[INFO ] 2024-07-26 18:05:25.079 - [Heartbeat-TestMongoHeartBeat - Copy] - Start task milestones: 66a374e48191787d1e5afaef(Heartbeat-TestMongoHeartBeat - Copy) 
[INFO ] 2024-07-26 18:05:25.150 - [Heartbeat-TestMongoHeartBeat - Copy] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 18:05:25.177 - [Heartbeat-TestMongoHeartBeat - Copy] - The engine receives Heartbeat-TestMongoHeartBeat - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 18:05:25.252 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[81377798-e423-46ef-b757-3880ead2478b] start preload schema,table counts: 1 
[INFO ] 2024-07-26 18:05:25.253 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a286bf8d-665b-43d0-955e-360ebcec11c7] start preload schema,table counts: 1 
[INFO ] 2024-07-26 18:05:25.253 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[81377798-e423-46ef-b757-3880ead2478b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 18:05:25.253 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a286bf8d-665b-43d0-955e-360ebcec11c7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 18:05:26.470 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[WARN ] 2024-07-26 18:05:26.534 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Index [{id=1}] already exists but options is inconsistent, will ignore creating this index, server error detail message: Command failed with error 85 (IndexOptionsConflict): 'Index with name: id_1 already exists with different options' on server localhost:27017. The full response is {"operationTime": {"$timestamp": {"t": 1721988326, "i": 43}}, "ok": 0.0, "errmsg": "Index with name: id_1 already exists with different options", "code": 85, "codeName": "IndexOptionsConflict", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721988326, "i": 43}}, "signature": {"hash": {"$binary": {"base64": "JCc1mNu3fOq6CaBriuviJK20XCM=", "subType": "00"}}, "keyId": 7376103549123428362}}} 
[INFO ] 2024-07-26 18:05:26.534 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-26 18:05:26.534 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-26 18:05:26.534 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 18:05:26.534 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721988326534,"lastTimes":1721988326534,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 18:05:26.586 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-26 18:05:26.586 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-26 18:05:26.596 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-26 18:05:26.596 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 18:05:26.596 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-26 18:05:26.597 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721988326534,"lastTimes":1721988326534,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 18:05:26.597 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-26 18:05:26.597 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 18:05:45.685 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a286bf8d-665b-43d0-955e-360ebcec11c7] running status set to false 
[INFO ] 2024-07-26 18:05:45.693 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-26 18:05:45.697 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-a286bf8d-665b-43d0-955e-360ebcec11c7 
[INFO ] 2024-07-26 18:05:45.697 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-a286bf8d-665b-43d0-955e-360ebcec11c7 
[INFO ] 2024-07-26 18:05:45.697 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a286bf8d-665b-43d0-955e-360ebcec11c7] schema data cleaned 
[INFO ] 2024-07-26 18:05:45.699 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a286bf8d-665b-43d0-955e-360ebcec11c7] monitor closed 
[INFO ] 2024-07-26 18:05:45.699 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a286bf8d-665b-43d0-955e-360ebcec11c7] close complete, cost 24 ms 
[INFO ] 2024-07-26 18:05:45.721 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[81377798-e423-46ef-b757-3880ead2478b] running status set to false 
[INFO ] 2024-07-26 18:05:45.721 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-81377798-e423-46ef-b757-3880ead2478b 
[INFO ] 2024-07-26 18:05:45.721 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-81377798-e423-46ef-b757-3880ead2478b 
[INFO ] 2024-07-26 18:05:45.721 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[81377798-e423-46ef-b757-3880ead2478b] schema data cleaned 
[INFO ] 2024-07-26 18:05:45.721 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[81377798-e423-46ef-b757-3880ead2478b] monitor closed 
[INFO ] 2024-07-26 18:05:45.922 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[81377798-e423-46ef-b757-3880ead2478b] close complete, cost 22 ms 
[INFO ] 2024-07-26 18:05:48.210 - [Heartbeat-TestMongoHeartBeat - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:05:48.210 - [Heartbeat-TestMongoHeartBeat - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5be0cc86 
[INFO ] 2024-07-26 18:05:48.211 - [Heartbeat-TestMongoHeartBeat - Copy] - Stop task milestones: 66a374e48191787d1e5afaef(Heartbeat-TestMongoHeartBeat - Copy)  
[INFO ] 2024-07-26 18:05:48.346 - [Heartbeat-TestMongoHeartBeat - Copy] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:05:48.347 - [Heartbeat-TestMongoHeartBeat - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 18:05:48.382 - [Heartbeat-TestMongoHeartBeat - Copy] - Remove memory task client succeed, task: Heartbeat-TestMongoHeartBeat - Copy[66a374e48191787d1e5afaef] 
[INFO ] 2024-07-26 18:05:48.384 - [Heartbeat-TestMongoHeartBeat - Copy] - Destroy memory task client cache succeed, task: Heartbeat-TestMongoHeartBeat - Copy[66a374e48191787d1e5afaef] 
[INFO ] 2024-07-26 18:07:38.826 - [Heartbeat-TestMongoHeartBeat - Copy] - Start task milestones: 66a374e48191787d1e5afaef(Heartbeat-TestMongoHeartBeat - Copy) 
[INFO ] 2024-07-26 18:07:39.046 - [Heartbeat-TestMongoHeartBeat - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 18:07:39.071 - [Heartbeat-TestMongoHeartBeat - Copy] - The engine receives Heartbeat-TestMongoHeartBeat - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 18:07:39.116 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[81377798-e423-46ef-b757-3880ead2478b] start preload schema,table counts: 1 
[INFO ] 2024-07-26 18:07:39.117 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a286bf8d-665b-43d0-955e-360ebcec11c7] start preload schema,table counts: 1 
[INFO ] 2024-07-26 18:07:39.117 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a286bf8d-665b-43d0-955e-360ebcec11c7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 18:07:39.117 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[81377798-e423-46ef-b757-3880ead2478b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 18:07:39.231 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-26 18:07:39.232 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-26 18:07:39.232 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-26 18:07:39.232 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-26 18:07:39.288 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - batch offset found: {"_tapdata_heartbeat_table":{"batch_read_connector_offset":{"syncStage":"Initial","beginTimes":1721988326586,"lastTimes":1721988326587,"lastTN":1,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1,"updateTotals":0,"deleteTotals":0}}},"batch_read_connector_status":"RUNNING"}},stream offset found: {"syncStage":"Incremental","beginTimes":1721988326534,"lastTimes":1721988345670,"lastTN":21,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":20,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-26 18:07:39.290 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721988326534,"lastTimes":1721988345670,"lastTN":21,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":20,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-26 18:07:39.290 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-26 18:07:39.291 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 18:07:52.334 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a286bf8d-665b-43d0-955e-360ebcec11c7] running status set to false 
[INFO ] 2024-07-26 18:07:52.335 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-26 18:07:52.337 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-a286bf8d-665b-43d0-955e-360ebcec11c7 
[INFO ] 2024-07-26 18:07:52.337 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-a286bf8d-665b-43d0-955e-360ebcec11c7 
[INFO ] 2024-07-26 18:07:52.337 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a286bf8d-665b-43d0-955e-360ebcec11c7] schema data cleaned 
[INFO ] 2024-07-26 18:07:52.337 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a286bf8d-665b-43d0-955e-360ebcec11c7] monitor closed 
[INFO ] 2024-07-26 18:07:52.338 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[a286bf8d-665b-43d0-955e-360ebcec11c7] close complete, cost 4 ms 
[INFO ] 2024-07-26 18:07:52.338 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[81377798-e423-46ef-b757-3880ead2478b] running status set to false 
[INFO ] 2024-07-26 18:07:52.344 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-81377798-e423-46ef-b757-3880ead2478b 
[INFO ] 2024-07-26 18:07:52.351 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-81377798-e423-46ef-b757-3880ead2478b 
[INFO ] 2024-07-26 18:07:52.351 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[81377798-e423-46ef-b757-3880ead2478b] schema data cleaned 
[INFO ] 2024-07-26 18:07:52.351 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[81377798-e423-46ef-b757-3880ead2478b] monitor closed 
[INFO ] 2024-07-26 18:07:52.351 - [Heartbeat-TestMongoHeartBeat - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[81377798-e423-46ef-b757-3880ead2478b] close complete, cost 7 ms 
[INFO ] 2024-07-26 18:07:54.708 - [Heartbeat-TestMongoHeartBeat - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:07:54.708 - [Heartbeat-TestMongoHeartBeat - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@507b5d16 
[INFO ] 2024-07-26 18:07:54.709 - [Heartbeat-TestMongoHeartBeat - Copy] - Stop task milestones: 66a374e48191787d1e5afaef(Heartbeat-TestMongoHeartBeat - Copy)  
[INFO ] 2024-07-26 18:07:54.840 - [Heartbeat-TestMongoHeartBeat - Copy] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:07:54.840 - [Heartbeat-TestMongoHeartBeat - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 18:07:54.868 - [Heartbeat-TestMongoHeartBeat - Copy] - Remove memory task client succeed, task: Heartbeat-TestMongoHeartBeat - Copy[66a374e48191787d1e5afaef] 
[INFO ] 2024-07-26 18:07:54.870 - [Heartbeat-TestMongoHeartBeat - Copy] - Destroy memory task client cache succeed, task: Heartbeat-TestMongoHeartBeat - Copy[66a374e48191787d1e5afaef] 
