[INFO ] 2024-07-16 08:03:59.032 - [来自Mysql的共享挖掘任务] - Start task milestones: 6695b8df6d76494ed53f38c9(来自Mysql的共享挖掘任务) 
[INFO ] 2024-07-16 08:03:59.424 - [来自Mysql的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-16 08:03:59.469 - [来自Mysql的共享挖掘任务] - The engine receives 来自Mysql的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-16 08:04:00.117 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[43908d36c4db4802a5b552c937742416] start preload schema,table counts: 1 
[INFO ] 2024-07-16 08:04:00.117 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[43908d36c4db4802a5b552c937742416] preload schema finished, cost 4 ms 
[INFO ] 2024-07-16 08:04:00.121 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-16 08:04:00.121 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-16 08:04:00.329 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695b8df66ab5ede8a9daf12, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6695b8aa6d76494ed53f3882_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1452526791, shareCdcTaskId=6695b8df6d76494ed53f38c9, connectionId=6695b8aa6d76494ed53f3882) 
[INFO ] 2024-07-16 08:04:00.541 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务_POLICY', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_-1452526791', head seq: 0, tail seq: -1 
[INFO ] 2024-07-16 08:04:00.743 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav392?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-16 08:04:01.512 - [来自Mysql的共享挖掘任务][Mysql] - Source node "Mysql" read batch size: 2000 
[INFO ] 2024-07-16 08:04:01.512 - [来自Mysql的共享挖掘任务][Mysql] - Source node "Mysql" event queue capacity: 4000 
[INFO ] 2024-07-16 08:04:01.513 - [来自Mysql的共享挖掘任务][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-16 08:04:01.528 - [来自Mysql的共享挖掘任务][Mysql] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":111797458,"gtidSet":""} 
[INFO ] 2024-07-16 08:04:01.573 - [来自Mysql的共享挖掘任务][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-16 08:04:01.574 - [来自Mysql的共享挖掘任务][Mysql] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"filename":"binlog.000032","position":111797458,"gtidSet":""} 
[INFO ] 2024-07-16 08:04:01.649 - [来自Mysql的共享挖掘任务][Mysql] - Starting mysql cdc, server name: 4b4aa799-6030-4b09-89ac-0f774f2b1a0e 
[INFO ] 2024-07-16 08:04:01.649 - [来自Mysql的共享挖掘任务][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 9159526
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 4b4aa799-6030-4b09-89ac-0f774f2b1a0e
  database.port: 3306
  threadName: Debezium-Mysql-Connector-4b4aa799-6030-4b09-89ac-0f774f2b1a0e
  database.hostname: localhost
  database.password: ********
  name: 4b4aa799-6030-4b09-89ac-0f774f2b1a0e
  pdk.offset.string: {"name":"4b4aa799-6030-4b09-89ac-0f774f2b1a0e","offset":{"{\"server\":\"4b4aa799-6030-4b09-89ac-0f774f2b1a0e\"}":"{\"file\":\"binlog.000032\",\"pos\":111797458,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.POLICY,test._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-16 08:04:01.820 - [来自Mysql的共享挖掘任务][Mysql] - Connector Mysql incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-16 08:04:34.880 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[43908d36c4db4802a5b552c937742416] running status set to false 
[INFO ] 2024-07-16 08:04:35.002 - [来自Mysql的共享挖掘任务][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-16 08:04:35.013 - [来自Mysql的共享挖掘任务][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-07-16 08:04:35.014 - [来自Mysql的共享挖掘任务][Mysql] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-43908d36c4db4802a5b552c937742416 
[INFO ] 2024-07-16 08:04:35.018 - [来自Mysql的共享挖掘任务][Mysql] - PDK connector node released: HazelcastSourcePdkShareCDCNode-43908d36c4db4802a5b552c937742416 
[INFO ] 2024-07-16 08:04:35.018 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[43908d36c4db4802a5b552c937742416] schema data cleaned 
[INFO ] 2024-07-16 08:04:35.032 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[43908d36c4db4802a5b552c937742416] monitor closed 
[INFO ] 2024-07-16 08:04:35.033 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[43908d36c4db4802a5b552c937742416] close complete, cost 151 ms 
[INFO ] 2024-07-16 08:04:35.033 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[2ed50dc3392b4b5b8aa2052963a3c279] running status set to false 
[INFO ] 2024-07-16 08:04:35.051 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-16 08:04:35.052 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-16 08:04:35.053 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[2ed50dc3392b4b5b8aa2052963a3c279] schema data cleaned 
[INFO ] 2024-07-16 08:04:35.053 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[2ed50dc3392b4b5b8aa2052963a3c279] monitor closed 
[INFO ] 2024-07-16 08:04:35.260 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[2ed50dc3392b4b5b8aa2052963a3c279] close complete, cost 21 ms 
[INFO ] 2024-07-16 08:04:38.500 - [来自Mysql的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-16 08:04:38.508 - [来自Mysql的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@595a7399 
[INFO ] 2024-07-16 08:04:38.509 - [来自Mysql的共享挖掘任务] - Stop task milestones: 6695b8df6d76494ed53f38c9(来自Mysql的共享挖掘任务)  
[INFO ] 2024-07-16 08:04:38.645 - [来自Mysql的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-16 08:04:38.645 - [来自Mysql的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-16 08:04:38.722 - [来自Mysql的共享挖掘任务] - Remove memory task client succeed, task: 来自Mysql的共享挖掘任务[6695b8df6d76494ed53f38c9] 
[INFO ] 2024-07-16 08:04:38.722 - [来自Mysql的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自Mysql的共享挖掘任务[6695b8df6d76494ed53f38c9] 
