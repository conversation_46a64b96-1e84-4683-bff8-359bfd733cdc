[INFO ] 2024-07-17 18:21:57.280 - [Heartbeat-LocalMongo] - Start task milestones: 66979b44b92eda1a86f520d8(Heartbeat-LocalMongo) 
[INFO ] 2024-07-17 18:21:57.492 - [Heartbeat-LocalMongo] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-17 18:21:57.493 - [Heartbeat-LocalMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 18:21:57.494 - [Heartbeat-LocalMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6ddaf2de 
[INFO ] 2024-07-17 18:21:57.495 - [Heartbeat-LocalMongo] - Stop task milestones: 66979b44b92eda1a86f520d8(Heartbeat-LocalMongo)  
[INFO ] 2024-07-17 18:21:57.652 - [Heartbeat-LocalMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-17 18:21:57.652 - [Heartbeat-LocalMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-17 18:21:57.662 - [Heartbeat-LocalMongo] - Node [id fdc66c2e-06d6-4297-8bde-80b7aa0403f9, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id fdc66c2e-06d6-4297-8bde-80b7aa0403f9, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-17 18:22:07.980 - [Heartbeat-LocalMongo] - Start task milestones: 66979b44b92eda1a86f520d8(Heartbeat-LocalMongo) 
[INFO ] 2024-07-17 18:22:08.093 - [Heartbeat-LocalMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-17 18:22:08.093 - [Heartbeat-LocalMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 18:22:08.093 - [Heartbeat-LocalMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1a7de62f 
[INFO ] 2024-07-17 18:22:08.093 - [Heartbeat-LocalMongo] - Stop task milestones: 66979b44b92eda1a86f520d8(Heartbeat-LocalMongo)  
[INFO ] 2024-07-17 18:22:08.220 - [Heartbeat-LocalMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-17 18:22:08.220 - [Heartbeat-LocalMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-17 18:22:08.221 - [Heartbeat-LocalMongo] - Node [id fdc66c2e-06d6-4297-8bde-80b7aa0403f9, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id fdc66c2e-06d6-4297-8bde-80b7aa0403f9, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

