[INFO ] 2024-08-15 10:55:18.358 - [任务 1] - Start task milestones: 66bd6df92be3652f3fae918d(任务 1) 
[INFO ] 2024-08-15 10:55:22.635 - [任务 1] - Task initialization... 
[INFO ] 2024-08-15 10:55:23.711 - [任务 1] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-08-15 10:55:23.712 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-15 10:55:24.117 - [任务 1][Mongo] - <PERSON><PERSON>[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] start preload schema,table counts: 4 
[INFO ] 2024-08-15 10:55:24.118 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] start preload schema,table counts: 4 
[INFO ] 2024-08-15 10:55:24.120 - [任务 1][Mon<PERSON>] - <PERSON><PERSON>[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] preload schema finished, cost 0 ms 
[INFO ] 2024-08-15 10:55:24.322 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] preload schema finished, cost 0 ms 
[INFO ] 2024-08-15 10:55:24.844 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-15 10:55:24.854 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-15 10:55:25.101 - [任务 1][Mysql] - Source node "Mysql" read batch size: 100 
[INFO ] 2024-08-15 10:55:25.101 - [任务 1][Mysql] - Source node "Mysql" event queue capacity: 200 
[INFO ] 2024-08-15 10:55:25.102 - [任务 1][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-15 10:55:25.172 - [任务 1][Mysql] - batch offset found: {},stream offset found: {"filename":"binlog.000034","position":308236702,"gtidSet":""} 
[INFO ] 2024-08-15 10:55:25.174 - [任务 1][Mysql] - Initial sync started 
[INFO ] 2024-08-15 10:55:25.184 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-08-15 10:55:25.190 - [任务 1][Mysql] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-08-15 10:55:25.234 - [任务 1][Mysql] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-15 10:55:25.237 - [任务 1][Mysql] - Query table 'BMSQL_ITEM' counts: 0 
[INFO ] 2024-08-15 10:55:25.240 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_DISTRICT 
[INFO ] 2024-08-15 10:55:25.240 - [任务 1][Mysql] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-15 10:55:25.287 - [任务 1][Mysql] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-15 10:55:25.288 - [任务 1][Mysql] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-15 10:55:25.289 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-08-15 10:55:25.289 - [任务 1][Mysql] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-08-15 10:55:25.293 - [任务 1][Mysql] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-15 10:55:25.293 - [任务 1][Mysql] - Query table 'BMSQL_CUSTOMER' counts: 0 
[INFO ] 2024-08-15 10:55:25.294 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_CONFIG 
[INFO ] 2024-08-15 10:55:25.294 - [任务 1][Mysql] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-15 10:55:25.295 - [任务 1][Mysql] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-15 10:55:25.298 - [任务 1][Mysql] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-15 10:55:25.298 - [任务 1][Mysql] - Initial sync completed 
[INFO ] 2024-08-15 10:55:25.300 - [任务 1][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-15 10:55:25.308 - [任务 1][Mysql] - Initial sync completed 
[INFO ] 2024-08-15 10:55:25.311 - [任务 1][Mysql] - Starting stream read, table list: [BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], offset: {"filename":"binlog.000034","position":308236702,"gtidSet":""} 
[INFO ] 2024-08-15 10:55:25.396 - [任务 1][Mysql] - Starting mysql cdc, server name: ed92cfbe-25b8-47b7-b77e-2092f774c331 
[INFO ] 2024-08-15 10:55:25.400 - [任务 1][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 709690225
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ed92cfbe-25b8-47b7-b77e-2092f774c331
  database.port: 3306
  threadName: Debezium-Mysql-Connector-ed92cfbe-25b8-47b7-b77e-2092f774c331
  database.hostname: localhost
  database.password: ********
  name: ed92cfbe-25b8-47b7-b77e-2092f774c331
  pdk.offset.string: {"name":"ed92cfbe-25b8-47b7-b77e-2092f774c331","offset":{"{\"server\":\"ed92cfbe-25b8-47b7-b77e-2092f774c331\"}":"{\"file\":\"binlog.000034\",\"pos\":308236702,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_ITEM,test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[WARN ] 2024-08-15 10:55:25.804 - [任务 1][Mongo] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[138b03cf-faec-4b6e-bbfd-f8290025d34a], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-08-15 10:55:25.806 - [任务 1][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-15 10:55:39.586 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] running status set to false 
[INFO ] 2024-08-15 10:55:39.633 - [任务 1][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-15 10:55:39.634 - [任务 1][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-15 10:55:39.634 - [任务 1][Mysql] - Incremental sync completed 
[INFO ] 2024-08-15 10:55:39.648 - [任务 1][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-138b03cf-faec-4b6e-bbfd-f8290025d34a 
[INFO ] 2024-08-15 10:55:39.648 - [任务 1][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-138b03cf-faec-4b6e-bbfd-f8290025d34a 
[INFO ] 2024-08-15 10:55:39.652 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] schema data cleaned 
[INFO ] 2024-08-15 10:55:39.652 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] monitor closed 
[INFO ] 2024-08-15 10:55:39.669 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] close complete, cost 80 ms 
[INFO ] 2024-08-15 10:55:39.670 - [任务 1][Mongo] - Node Mongo[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] running status set to false 
[INFO ] 2024-08-15 10:55:39.805 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-d60b4e78-9c7a-48f4-9c22-eb0042c0dec5 
[INFO ] 2024-08-15 10:55:39.807 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-d60b4e78-9c7a-48f4-9c22-eb0042c0dec5 
[INFO ] 2024-08-15 10:55:39.808 - [任务 1][Mongo] - Node Mongo[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] schema data cleaned 
[INFO ] 2024-08-15 10:55:39.809 - [任务 1][Mongo] - Node Mongo[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] monitor closed 
[INFO ] 2024-08-15 10:55:40.012 - [任务 1][Mongo] - Node Mongo[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] close complete, cost 142 ms 
[INFO ] 2024-08-15 10:55:43.442 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-15 10:55:43.455 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5e19c552 
[INFO ] 2024-08-15 10:55:43.598 - [任务 1] - Stop task milestones: 66bd6df92be3652f3fae918d(任务 1)  
[INFO ] 2024-08-15 10:55:43.599 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-15 10:55:43.601 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-15 10:55:43.722 - [任务 1] - Remove memory task client succeed, task: 任务 1[66bd6df92be3652f3fae918d] 
[INFO ] 2024-08-15 10:55:43.723 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66bd6df92be3652f3fae918d] 
[INFO ] 2024-08-15 10:55:57.796 - [任务 1] - Start task milestones: 66bd6df92be3652f3fae918d(任务 1) 
[INFO ] 2024-08-15 10:55:58.004 - [任务 1] - Task initialization... 
[INFO ] 2024-08-15 10:55:58.146 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-15 10:55:58.277 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-15 10:55:58.278 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] start preload schema,table counts: 7 
[INFO ] 2024-08-15 10:55:58.278 - [任务 1][Mongo] - Node Mongo[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] start preload schema,table counts: 7 
[INFO ] 2024-08-15 10:55:58.282 - [任务 1][Mongo] - Node Mongo[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] preload schema finished, cost 0 ms 
[INFO ] 2024-08-15 10:55:58.490 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] preload schema finished, cost 0 ms 
[INFO ] 2024-08-15 10:55:58.550 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-15 10:55:58.552 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-15 10:55:58.619 - [任务 1][Mysql] - Source node "Mysql" read batch size: 100 
[INFO ] 2024-08-15 10:55:58.619 - [任务 1][Mysql] - Source node "Mysql" event queue capacity: 200 
[INFO ] 2024-08-15 10:55:58.620 - [任务 1][Mysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-15 10:55:58.630 - [任务 1][Mysql] - batch offset found: {"BMSQL_ITEM":{"batch_read_connector_status":"OVER"},"BMSQL_DISTRICT":{"batch_read_connector_status":"OVER"},"BMSQL_CONFIG":{"batch_read_connector_status":"OVER"},"BMSQL_CUSTOMER":{"batch_read_connector_status":"OVER"}},stream offset found: {"name":"ed92cfbe-25b8-47b7-b77e-2092f774c331","offset":{"{\"server\":\"ed92cfbe-25b8-47b7-b77e-2092f774c331\"}":"{\"ts_sec\":1723690526,\"file\":\"binlog.000034\",\"pos\":308236702}"}} 
[INFO ] 2024-08-15 10:55:58.691 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_NEW_ORDER 
[INFO ] 2024-08-15 10:55:58.693 - [任务 1][Mysql] - Table BMSQL_NEW_ORDER is going to be initial synced 
[INFO ] 2024-08-15 10:55:58.900 - [任务 1][Mysql] - Query table 'BMSQL_NEW_ORDER' counts: 2899 
[INFO ] 2024-08-15 10:55:59.260 - [任务 1][Mysql] - Table [BMSQL_NEW_ORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-15 10:55:59.262 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_OORDER 
[INFO ] 2024-08-15 10:55:59.271 - [任务 1][Mysql] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-08-15 10:55:59.278 - [任务 1][Mysql] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-15 10:55:59.278 - [任务 1][Mysql] - Query table 'BMSQL_OORDER' counts: 0 
[INFO ] 2024-08-15 10:55:59.285 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_OORDER_BACK 
[INFO ] 2024-08-15 10:55:59.286 - [任务 1][Mysql] - Table BMSQL_OORDER_BACK is going to be initial synced 
[INFO ] 2024-08-15 10:55:59.286 - [任务 1][Mysql] - Table [BMSQL_OORDER_BACK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-15 10:55:59.287 - [任务 1][Mysql] - Query table 'BMSQL_OORDER_BACK' counts: 1 
[INFO ] 2024-08-15 10:55:59.287 - [任务 1][Mysql] - Initial sync completed 
[INFO ] 2024-08-15 10:55:59.287 - [任务 1][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-15 10:55:59.289 - [任务 1][Mysql] - Initial sync completed 
[INFO ] 2024-08-15 10:55:59.289 - [任务 1][Mysql] - Starting stream read, table list: [BMSQL_OORDER_BACK, BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER], offset: {"name":"ed92cfbe-25b8-47b7-b77e-2092f774c331","offset":{"{\"server\":\"ed92cfbe-25b8-47b7-b77e-2092f774c331\"}":"{\"ts_sec\":1723690526,\"file\":\"binlog.000034\",\"pos\":308236702}"}} 
[INFO ] 2024-08-15 10:55:59.328 - [任务 1][Mysql] - Starting mysql cdc, server name: ed92cfbe-25b8-47b7-b77e-2092f774c331 
[INFO ] 2024-08-15 10:55:59.328 - [任务 1][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 838249362
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ed92cfbe-25b8-47b7-b77e-2092f774c331
  database.port: 3306
  threadName: Debezium-Mysql-Connector-ed92cfbe-25b8-47b7-b77e-2092f774c331
  database.hostname: localhost
  database.password: ********
  name: ed92cfbe-25b8-47b7-b77e-2092f774c331
  pdk.offset.string: {"name":"ed92cfbe-25b8-47b7-b77e-2092f774c331","offset":{"{\"server\":\"ed92cfbe-25b8-47b7-b77e-2092f774c331\"}":"{\"ts_sec\":1723690526,\"file\":\"binlog.000034\",\"pos\":308236702}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_OORDER_BACK,test.BMSQL_NEW_ORDER,test.BMSQL_ITEM,test.BMSQL_DISTRICT,test.BMSQL_CONFIG,test.BMSQL_CUSTOMER,test.BMSQL_OORDER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-15 10:55:59.536 - [任务 1][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_OORDER_BACK, BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER], data change syncing 
[INFO ] 2024-08-15 10:56:17.210 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] running status set to false 
[INFO ] 2024-08-15 10:56:17.215 - [任务 1][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-15 10:56:17.215 - [任务 1][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-15 10:56:17.219 - [任务 1][Mysql] - Incremental sync completed 
[INFO ] 2024-08-15 10:56:17.226 - [任务 1][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-138b03cf-faec-4b6e-bbfd-f8290025d34a 
[INFO ] 2024-08-15 10:56:17.226 - [任务 1][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-138b03cf-faec-4b6e-bbfd-f8290025d34a 
[INFO ] 2024-08-15 10:56:17.227 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] schema data cleaned 
[INFO ] 2024-08-15 10:56:17.227 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] monitor closed 
[INFO ] 2024-08-15 10:56:17.230 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] close complete, cost 152 ms 
[INFO ] 2024-08-15 10:56:17.230 - [任务 1][Mongo] - Node Mongo[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] running status set to false 
[INFO ] 2024-08-15 10:56:17.248 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-d60b4e78-9c7a-48f4-9c22-eb0042c0dec5 
[INFO ] 2024-08-15 10:56:17.249 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-d60b4e78-9c7a-48f4-9c22-eb0042c0dec5 
[INFO ] 2024-08-15 10:56:17.249 - [任务 1][Mongo] - Node Mongo[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] schema data cleaned 
[INFO ] 2024-08-15 10:56:17.250 - [任务 1][Mongo] - Node Mongo[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] monitor closed 
[INFO ] 2024-08-15 10:56:17.250 - [任务 1][Mongo] - Node Mongo[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] close complete, cost 20 ms 
[INFO ] 2024-08-15 10:56:18.786 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-15 10:56:18.792 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@75d020ec 
[INFO ] 2024-08-15 10:56:18.913 - [任务 1] - Stop task milestones: 66bd6df92be3652f3fae918d(任务 1)  
[INFO ] 2024-08-15 10:56:18.913 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-15 10:56:18.943 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-15 10:56:18.944 - [任务 1] - Remove memory task client succeed, task: 任务 1[66bd6df92be3652f3fae918d] 
[INFO ] 2024-08-15 10:56:19.150 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66bd6df92be3652f3fae918d] 
[INFO ] 2024-08-15 10:57:05.993 - [任务 1] - Start task milestones: 66bd6df92be3652f3fae918d(任务 1) 
[INFO ] 2024-08-15 10:57:06.206 - [任务 1] - Task initialization... 
[INFO ] 2024-08-15 10:57:06.287 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-15 10:57:06.387 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-15 10:57:06.388 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] start preload schema,table counts: 10 
[INFO ] 2024-08-15 10:57:06.388 - [任务 1][Mongo] - Node Mongo[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] start preload schema,table counts: 10 
[INFO ] 2024-08-15 10:57:06.390 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] preload schema finished, cost 0 ms 
[INFO ] 2024-08-15 10:57:06.390 - [任务 1][Mongo] - Node Mongo[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] preload schema finished, cost 0 ms 
[INFO ] 2024-08-15 10:57:06.647 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-15 10:57:06.763 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-15 10:57:06.766 - [任务 1][Mysql] - Source node "Mysql" read batch size: 100 
[INFO ] 2024-08-15 10:57:06.766 - [任务 1][Mysql] - Source node "Mysql" event queue capacity: 200 
[INFO ] 2024-08-15 10:57:06.766 - [任务 1][Mysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-15 10:57:19.870 - [任务 1][Mysql] - batch offset found: {"BMSQL_OORDER_BACK":{"batch_read_connector_status":"OVER"},"BMSQL_NEW_ORDER":{"batch_read_connector_status":"OVER"},"BMSQL_OORDER":{"batch_read_connector_status":"OVER"}},stream offset found: {"name":"ed92cfbe-25b8-47b7-b77e-2092f774c331","offset":{"{\"server\":\"ed92cfbe-25b8-47b7-b77e-2092f774c331\"}":"{\"ts_sec\":1723690526,\"file\":\"binlog.000034\",\"pos\":308236702}"}} 
[INFO ] 2024-08-15 10:57:19.953 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_OORDER_BACK2 
[INFO ] 2024-08-15 10:57:19.956 - [任务 1][Mysql] - Table BMSQL_OORDER_BACK2 is going to be initial synced 
[INFO ] 2024-08-15 10:57:20.030 - [任务 1][Mysql] - Table [BMSQL_OORDER_BACK2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-15 10:57:20.034 - [任务 1][Mysql] - Query table 'BMSQL_OORDER_BACK2' counts: 1 
[INFO ] 2024-08-15 10:57:20.040 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_ORDER_LINE 
[INFO ] 2024-08-15 10:57:20.044 - [任务 1][Mysql] - Table BMSQL_ORDER_LINE is going to be initial synced 
[INFO ] 2024-08-15 10:57:20.053 - [任务 1][Mysql] - Table [BMSQL_ORDER_LINE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-15 10:57:20.060 - [任务 1][Mysql] - Query table 'BMSQL_ORDER_LINE' counts: 0 
[INFO ] 2024-08-15 10:57:20.063 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_STOCK 
[INFO ] 2024-08-15 10:57:20.064 - [任务 1][Mysql] - Table BMSQL_STOCK is going to be initial synced 
[INFO ] 2024-08-15 10:57:20.068 - [任务 1][Mysql] - Query table 'BMSQL_STOCK' counts: 7899 
[INFO ] 2024-08-15 10:57:20.946 - [任务 1][Mysql] - Table [BMSQL_STOCK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-15 10:57:20.955 - [任务 1][Mysql] - Initial sync completed 
[INFO ] 2024-08-15 10:57:20.957 - [任务 1][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-15 10:57:20.957 - [任务 1][Mysql] - Initial sync completed 
[INFO ] 2024-08-15 10:57:20.971 - [任务 1][Mysql] - Starting stream read, table list: [BMSQL_OORDER_BACK, BMSQL_NEW_ORDER, BMSQL_STOCK, BMSQL_ITEM, BMSQL_ORDER_LINE, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER, BMSQL_OORDER_BACK2], offset: {"name":"ed92cfbe-25b8-47b7-b77e-2092f774c331","offset":{"{\"server\":\"ed92cfbe-25b8-47b7-b77e-2092f774c331\"}":"{\"ts_sec\":1723690526,\"file\":\"binlog.000034\",\"pos\":308236702}"}} 
[INFO ] 2024-08-15 10:57:21.001 - [任务 1][Mysql] - Starting mysql cdc, server name: ed92cfbe-25b8-47b7-b77e-2092f774c331 
[INFO ] 2024-08-15 10:57:21.002 - [任务 1][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1559131083
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ed92cfbe-25b8-47b7-b77e-2092f774c331
  database.port: 3306
  threadName: Debezium-Mysql-Connector-ed92cfbe-25b8-47b7-b77e-2092f774c331
  database.hostname: localhost
  database.password: ********
  name: ed92cfbe-25b8-47b7-b77e-2092f774c331
  pdk.offset.string: {"name":"ed92cfbe-25b8-47b7-b77e-2092f774c331","offset":{"{\"server\":\"ed92cfbe-25b8-47b7-b77e-2092f774c331\"}":"{\"ts_sec\":1723690526,\"file\":\"binlog.000034\",\"pos\":308236702}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_OORDER_BACK,test.BMSQL_NEW_ORDER,test.BMSQL_STOCK,test.BMSQL_ITEM,test.BMSQL_ORDER_LINE,test.BMSQL_DISTRICT,test.BMSQL_CONFIG,test.BMSQL_CUSTOMER,test.BMSQL_OORDER,test.BMSQL_OORDER_BACK2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-15 10:57:21.203 - [任务 1][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_OORDER_BACK, BMSQL_NEW_ORDER, BMSQL_STOCK, BMSQL_ITEM, BMSQL_ORDER_LINE, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER, BMSQL_OORDER_BACK2], data change syncing 
[INFO ] 2024-08-15 10:57:23.512 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] running status set to false 
[INFO ] 2024-08-15 10:57:23.512 - [任务 1][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-15 10:57:23.512 - [任务 1][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-15 10:57:23.514 - [任务 1][Mysql] - Incremental sync completed 
[INFO ] 2024-08-15 10:57:23.518 - [任务 1][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-138b03cf-faec-4b6e-bbfd-f8290025d34a 
[INFO ] 2024-08-15 10:57:23.518 - [任务 1][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-138b03cf-faec-4b6e-bbfd-f8290025d34a 
[INFO ] 2024-08-15 10:57:23.521 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] schema data cleaned 
[INFO ] 2024-08-15 10:57:23.522 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] monitor closed 
[INFO ] 2024-08-15 10:57:23.523 - [任务 1][Mysql] - Node Mysql[138b03cf-faec-4b6e-bbfd-f8290025d34a] close complete, cost 82 ms 
[INFO ] 2024-08-15 10:57:23.523 - [任务 1][Mongo] - Node Mongo[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] running status set to false 
[INFO ] 2024-08-15 10:57:23.549 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-d60b4e78-9c7a-48f4-9c22-eb0042c0dec5 
[INFO ] 2024-08-15 10:57:23.551 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-d60b4e78-9c7a-48f4-9c22-eb0042c0dec5 
[INFO ] 2024-08-15 10:57:23.551 - [任务 1][Mongo] - Node Mongo[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] schema data cleaned 
[INFO ] 2024-08-15 10:57:23.551 - [任务 1][Mongo] - Node Mongo[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] monitor closed 
[INFO ] 2024-08-15 10:57:23.761 - [任务 1][Mongo] - Node Mongo[d60b4e78-9c7a-48f4-9c22-eb0042c0dec5] close complete, cost 28 ms 
[INFO ] 2024-08-15 10:57:24.924 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-15 10:57:24.925 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7923139b 
[INFO ] 2024-08-15 10:57:25.062 - [任务 1] - Stop task milestones: 66bd6df92be3652f3fae918d(任务 1)  
[INFO ] 2024-08-15 10:57:25.062 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-15 10:57:25.062 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-15 10:57:25.086 - [任务 1] - Remove memory task client succeed, task: 任务 1[66bd6df92be3652f3fae918d] 
[INFO ] 2024-08-15 10:57:25.088 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66bd6df92be3652f3fae918d] 
