[INFO ] 2024-08-16 03:32:38.003 - [任务 10] - Task initialization... 
[INFO ] 2024-08-16 03:32:38.019 - [任务 10] - Start task milestones: 66bdcff6e914743d45ca759a(任务 10) 
[INFO ] 2024-08-16 03:32:38.086 - [任务 10] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-08-16 03:32:38.294 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-16 03:32:38.485 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] start preload schema,table counts: 1 
[INFO ] 2024-08-16 03:32:38.486 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] start preload schema,table counts: 1 
[INFO ] 2024-08-16 03:32:38.706 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] preload schema finished, cost 214 ms 
[INFO ] 2024-08-16 03:32:38.715 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] preload schema finished, cost 213 ms 
[INFO ] 2024-08-16 03:32:39.829 - [任务 10][Mysql3306] - Node(Mysql3306) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-16 03:32:39.830 - [任务 10][Mysql3306] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-16 03:32:40.449 - [任务 10][Oracle] - Source node "Oracle" read batch size: 100 
[INFO ] 2024-08-16 03:32:40.452 - [任务 10][Oracle] - Source node "Oracle" event queue capacity: 200 
[INFO ] 2024-08-16 03:32:40.454 - [任务 10][Oracle] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-16 03:32:41.139 - [任务 10][Oracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":96807812,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-16 03:32:41.307 - [任务 10][Oracle] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-16 03:32:41.307 - [任务 10][Oracle] - Initial sync started 
[INFO ] 2024-08-16 03:32:41.313 - [任务 10][Oracle] - Starting batch read, table name: TESTDDL2, offset: null 
[INFO ] 2024-08-16 03:32:41.326 - [任务 10][Oracle] - Table TESTDDL2 is going to be initial synced 
[INFO ] 2024-08-16 03:32:41.457 - [任务 10][Oracle] - Query table 'TESTDDL2' counts: 1 
[INFO ] 2024-08-16 03:32:41.459 - [任务 10][Oracle] - Table [TESTDDL2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-16 03:32:41.459 - [任务 10][Oracle] - Initial sync completed 
[INFO ] 2024-08-16 03:32:41.460 - [任务 10][Oracle] - Incremental sync starting... 
[INFO ] 2024-08-16 03:32:41.460 - [任务 10][Oracle] - Initial sync completed 
[INFO ] 2024-08-16 03:32:41.467 - [任务 10][Oracle] - Starting stream read, table list: [TESTDDL2], offset: {"sortString":null,"offsetValue":null,"lastScn":96807812,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-16 03:32:41.924 - [任务 10][Oracle] - total start mining scn: 96807812 
[INFO ] 2024-08-16 03:32:43.762 - [任务 10][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-16 03:57:15.199 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] running status set to false 
[INFO ] 2024-08-16 03:57:15.296 - [任务 10][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-16 03:57:15.298 - [任务 10][Oracle] - Incremental sync completed 
[INFO ] 2024-08-16 04:02:16.478 - [任务 10] - Start task milestones: 66bdcff6e914743d45ca759a(任务 10) 
[INFO ] 2024-08-16 04:02:16.553 - [任务 10] - Task initialization... 
[INFO ] 2024-08-16 04:02:16.553 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-16 04:02:16.715 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-16 04:02:16.716 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] start preload schema,table counts: 1 
[INFO ] 2024-08-16 04:02:16.716 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] start preload schema,table counts: 1 
[INFO ] 2024-08-16 04:02:16.748 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] preload schema finished, cost 29 ms 
[INFO ] 2024-08-16 04:02:16.960 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] preload schema finished, cost 30 ms 
[INFO ] 2024-08-16 04:02:17.571 - [任务 10][Mysql3306] - Node(Mysql3306) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-16 04:02:17.574 - [任务 10][Mysql3306] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-16 04:02:17.862 - [任务 10][Oracle] - Source node "Oracle" read batch size: 100 
[INFO ] 2024-08-16 04:02:17.863 - [任务 10][Oracle] - Source node "Oracle" event queue capacity: 200 
[INFO ] 2024-08-16 04:02:17.877 - [任务 10][Oracle] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-16 04:02:17.878 - [任务 10][Oracle] - batch offset found: {"TESTDDL2":{"offset":{},"status":"RUNNING"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":96817655,"pendingScn":96817699,"timestamp":1723751764000,"hexScn":null,"fno":0} 
[INFO ] 2024-08-16 04:02:18.083 - [任务 10][Oracle] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-16 04:02:18.096 - [任务 10][Oracle] - Incremental sync starting... 
[INFO ] 2024-08-16 04:02:18.101 - [任务 10][Oracle] - Initial sync completed 
[INFO ] 2024-08-16 04:02:18.102 - [任务 10][Oracle] - Starting stream read, table list: [TESTDDL2], offset: {"sortString":null,"offsetValue":null,"lastScn":96817655,"pendingScn":96817699,"timestamp":1723751764000,"hexScn":null,"fno":0} 
[INFO ] 2024-08-16 04:02:19.315 - [任务 10][Oracle] - total start mining scn: 96817655 
[INFO ] 2024-08-16 04:02:20.811 - [任务 10][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-16 04:10:48.128 - [任务 10] - Start task milestones: 66bdcff6e914743d45ca759a(任务 10) 
[INFO ] 2024-08-16 04:10:48.130 - [任务 10] - Task initialization... 
[INFO ] 2024-08-16 04:10:48.247 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-16 04:10:48.247 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-16 04:10:48.321 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] start preload schema,table counts: 1 
[INFO ] 2024-08-16 04:10:48.323 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] start preload schema,table counts: 1 
[INFO ] 2024-08-16 04:10:48.351 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] preload schema finished, cost 25 ms 
[INFO ] 2024-08-16 04:10:48.352 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] preload schema finished, cost 35 ms 
[INFO ] 2024-08-16 04:10:49.090 - [任务 10][Mysql3306] - Node(Mysql3306) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-16 04:10:49.093 - [任务 10][Mysql3306] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-16 04:10:49.644 - [任务 10][Oracle] - Source node "Oracle" read batch size: 100 
[INFO ] 2024-08-16 04:10:49.645 - [任务 10][Oracle] - Source node "Oracle" event queue capacity: 200 
[INFO ] 2024-08-16 04:10:49.652 - [任务 10][Oracle] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-16 04:10:49.655 - [任务 10][Oracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":96822507,"pendingScn":96822630,"timestamp":1723752331000,"hexScn":null,"fno":0} 
[INFO ] 2024-08-16 04:10:49.657 - [任务 10][Oracle] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-16 04:10:49.731 - [任务 10][Oracle] - Incremental sync starting... 
[INFO ] 2024-08-16 04:10:49.731 - [任务 10][Oracle] - Initial sync completed 
[INFO ] 2024-08-16 04:10:49.937 - [任务 10][Oracle] - Starting stream read, table list: [TESTDDL2], offset: {"sortString":null,"offsetValue":null,"lastScn":96822507,"pendingScn":96822630,"timestamp":1723752331000,"hexScn":null,"fno":0} 
[INFO ] 2024-08-16 04:10:50.545 - [任务 10][Oracle] - total start mining scn: 96822507 
[INFO ] 2024-08-16 04:10:51.975 - [任务 10][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-16 04:19:09.571 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] running status set to false 
[INFO ] 2024-08-16 04:24:10.024 - [任务 10] - Start task milestones: 66bdcff6e914743d45ca759a(任务 10) 
[INFO ] 2024-08-16 04:24:10.025 - [任务 10] - Task initialization... 
[INFO ] 2024-08-16 04:24:10.143 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-16 04:24:10.145 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-16 04:24:10.267 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] start preload schema,table counts: 1 
[INFO ] 2024-08-16 04:24:10.269 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] start preload schema,table counts: 1 
[INFO ] 2024-08-16 04:24:10.269 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] preload schema finished, cost 29 ms 
[INFO ] 2024-08-16 04:24:10.269 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] preload schema finished, cost 32 ms 
[INFO ] 2024-08-16 04:24:10.580 - [任务 10][Mysql3306] - Node(Mysql3306) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-16 04:24:10.580 - [任务 10][Mysql3306] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-16 04:24:11.417 - [任务 10][Oracle] - Source node "Oracle" read batch size: 100 
[INFO ] 2024-08-16 04:24:11.418 - [任务 10][Oracle] - Source node "Oracle" event queue capacity: 200 
[INFO ] 2024-08-16 04:24:11.418 - [任务 10][Oracle] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-16 04:24:11.423 - [任务 10][Oracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":96825802,"pendingScn":96825802,"timestamp":1723753133000,"hexScn":null,"fno":0} 
[INFO ] 2024-08-16 04:24:11.423 - [任务 10][Oracle] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-16 04:24:11.475 - [任务 10][Oracle] - Incremental sync starting... 
[INFO ] 2024-08-16 04:24:11.475 - [任务 10][Oracle] - Initial sync completed 
[INFO ] 2024-08-16 04:24:11.680 - [任务 10][Oracle] - Starting stream read, table list: [TESTDDL2], offset: {"sortString":null,"offsetValue":null,"lastScn":96825802,"pendingScn":96825802,"timestamp":1723753133000,"hexScn":null,"fno":0} 
[INFO ] 2024-08-16 04:24:12.689 - [任务 10][Oracle] - total start mining scn: 96825802 
[INFO ] 2024-08-16 04:24:14.313 - [任务 10][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-16 04:32:32.094 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] running status set to false 
[INFO ] 2024-08-16 11:10:25.414 - [任务 10] - Task initialization... 
[INFO ] 2024-08-16 11:10:25.417 - [任务 10] - Start task milestones: 66bdcff6e914743d45ca759a(任务 10) 
[INFO ] 2024-08-16 11:10:25.651 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-16 11:10:26.061 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-16 11:10:26.519 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] start preload schema,table counts: 1 
[INFO ] 2024-08-16 11:10:26.523 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] start preload schema,table counts: 1 
[INFO ] 2024-08-16 11:10:26.771 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] preload schema finished, cost 253 ms 
[INFO ] 2024-08-16 11:10:26.774 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] preload schema finished, cost 254 ms 
[INFO ] 2024-08-16 11:10:27.835 - [任务 10][Mysql3306] - Node(Mysql3306) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-16 11:10:27.837 - [任务 10][Mysql3306] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-16 11:10:28.837 - [任务 10][Oracle] - Source node "Oracle" read batch size: 100 
[INFO ] 2024-08-16 11:10:28.837 - [任务 10][Oracle] - Source node "Oracle" event queue capacity: 200 
[INFO ] 2024-08-16 11:10:28.845 - [任务 10][Oracle] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-16 11:10:28.861 - [任务 10][Oracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":96828307,"pendingScn":96828313,"timestamp":1723753942000,"hexScn":null,"fno":0} 
[INFO ] 2024-08-16 11:10:28.862 - [任务 10][Oracle] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-16 11:10:29.065 - [任务 10][Oracle] - Incremental sync starting... 
[INFO ] 2024-08-16 11:10:29.067 - [任务 10][Oracle] - Initial sync completed 
[INFO ] 2024-08-16 11:10:29.087 - [任务 10][Oracle] - Starting stream read, table list: [TESTDDL2], offset: {"sortString":null,"offsetValue":null,"lastScn":96828307,"pendingScn":96828313,"timestamp":1723753942000,"hexScn":null,"fno":0} 
[INFO ] 2024-08-16 11:10:29.548 - [任务 10][Oracle] - total start mining scn: 96828307 
[INFO ] 2024-08-16 11:10:31.027 - [任务 10][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_08_16/o1_mf_1_2285_mcwt1wsj_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-16 11:10:42.869 - [任务 10][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_08_16/o1_mf_1_2286_mcwto6xs_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-16 11:11:03.151 - [任务 10][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_08_16/o1_mf_1_2287_mcx5ny3b_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-16 11:11:19.771 - [任务 10][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-16 11:24:00.737 - [任务 10] - Stop task milestones: 66bdcff6e914743d45ca759a(任务 10)  
[INFO ] 2024-08-16 11:24:01.352 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] running status set to false 
[INFO ] 2024-08-16 11:24:01.409 - [任务 10][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-16 11:24:01.410 - [任务 10][Oracle] - Log Miner has been closed! 
[INFO ] 2024-08-16 11:24:01.518 - [任务 10][Oracle] - PDK connector node stopped: HazelcastSourcePdkDataNode-4148a62c-c07d-487a-8c36-ba9a9cdfc78e 
[INFO ] 2024-08-16 11:24:01.518 - [任务 10][Oracle] - PDK connector node released: HazelcastSourcePdkDataNode-4148a62c-c07d-487a-8c36-ba9a9cdfc78e 
[INFO ] 2024-08-16 11:24:01.520 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] schema data cleaned 
[INFO ] 2024-08-16 11:24:01.521 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] monitor closed 
[INFO ] 2024-08-16 11:24:01.524 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] close complete, cost 363 ms 
[INFO ] 2024-08-16 11:24:01.528 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] running status set to false 
[INFO ] 2024-08-16 11:24:01.554 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastTargetPdkDataNode-6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9 
[INFO ] 2024-08-16 11:24:01.554 - [任务 10][Mysql3306] - PDK connector node released: HazelcastTargetPdkDataNode-6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9 
[INFO ] 2024-08-16 11:24:01.554 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] schema data cleaned 
[INFO ] 2024-08-16 11:24:01.554 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] monitor closed 
[INFO ] 2024-08-16 11:24:01.758 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] close complete, cost 32 ms 
[INFO ] 2024-08-16 11:24:06.391 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-16 11:24:06.391 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-08-16 11:24:06.391 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-08-16 11:24:06.416 - [任务 10] - Remove memory task client succeed, task: 任务 10[66bdcff6e914743d45ca759a] 
[INFO ] 2024-08-16 11:24:06.416 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[66bdcff6e914743d45ca759a] 
[INFO ] 2024-08-16 11:31:16.620 - [任务 10] - Task initialization... 
[INFO ] 2024-08-16 11:31:16.621 - [任务 10] - Start task milestones: 66bdcff6e914743d45ca759a(任务 10) 
[INFO ] 2024-08-16 11:31:16.636 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-16 11:31:16.741 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-16 11:31:16.741 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] start preload schema,table counts: 1 
[INFO ] 2024-08-16 11:31:16.741 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] start preload schema,table counts: 1 
[INFO ] 2024-08-16 11:31:16.772 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] preload schema finished, cost 30 ms 
[INFO ] 2024-08-16 11:31:16.772 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] preload schema finished, cost 30 ms 
[INFO ] 2024-08-16 11:31:17.565 - [任务 10][Mysql3306] - Node(Mysql3306) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-16 11:31:17.565 - [任务 10][Mysql3306] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-16 11:31:17.778 - [任务 10][Oracle] - Source node "Oracle" read batch size: 100 
[INFO ] 2024-08-16 11:31:17.778 - [任务 10][Oracle] - Source node "Oracle" event queue capacity: 200 
[INFO ] 2024-08-16 11:31:17.778 - [任务 10][Oracle] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-16 11:31:18.125 - [任务 10][Oracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":96982326,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-16 11:31:18.180 - [任务 10][Oracle] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-16 11:31:18.180 - [任务 10][Oracle] - Initial sync started 
[INFO ] 2024-08-16 11:31:18.194 - [任务 10][Oracle] - Starting batch read, table name: TESTDDL2, offset: null 
[INFO ] 2024-08-16 11:31:18.194 - [任务 10][Oracle] - Table TESTDDL2 is going to be initial synced 
[INFO ] 2024-08-16 11:31:18.284 - [任务 10][Oracle] - Table [TESTDDL2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-16 11:31:18.285 - [任务 10][Oracle] - Query table 'TESTDDL2' counts: 1 
[INFO ] 2024-08-16 11:31:18.290 - [任务 10][Oracle] - Initial sync completed 
[INFO ] 2024-08-16 11:31:18.290 - [任务 10][Oracle] - Incremental sync starting... 
[INFO ] 2024-08-16 11:31:18.293 - [任务 10][Oracle] - Initial sync completed 
[INFO ] 2024-08-16 11:31:18.294 - [任务 10][Oracle] - Starting stream read, table list: [TESTDDL2], offset: {"sortString":null,"offsetValue":null,"lastScn":96982326,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-16 11:31:18.500 - [任务 10][Oracle] - total start mining scn: 96982326 
[INFO ] 2024-08-16 11:31:19.931 - [任务 10][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-16 11:32:55.704 - [任务 10][Oracle] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@4888bcf1: {"newFields":[{"autoInc":false,"dataType":"INTEGER","name":"A11","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"virtual":false}],"originDDL":"ALTER TABLE TESTDDL2 ADD (A11 INT NULL);","referenceTime":1723779168000,"tableId":"TESTDDL2","time":1723779175686,"type":209} 
[INFO ] 2024-08-16 11:32:55.769 - [任务 10][Oracle] - Alter table in memory, qualified name: T_oracle_io_tapdata_1_0-SNAPSHOT_TESTDDL2_66bdc702e914743d45ca7383_66bdcff6e914743d45ca759a 
[INFO ] 2024-08-16 11:32:55.769 - [任务 10][Oracle] - Alter table schema transform finished 
[INFO ] 2024-08-16 11:33:58.584 - [任务 10][Oracle] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='TESTDDL2', fieldName='A11', dataTypeChange=ValueChange{before=null, after=VARCHAR2(100)}, checkChange=null, constraintChange=null, nullableChange=null, commentChange=null, defaultChange=null, primaryChange=null} 
[INFO ] 2024-08-16 11:33:58.653 - [任务 10][Oracle] - Alter table in memory, qualified name: T_oracle_io_tapdata_1_0-SNAPSHOT_TESTDDL2_66bdc702e914743d45ca7383_66bdcff6e914743d45ca759a 
[INFO ] 2024-08-16 11:33:58.653 - [任务 10][Oracle] - Alter table schema transform finished 
[INFO ] 2024-08-16 11:34:50.596 - [任务 10][Oracle] - Source node received an ddl event: TapDropFieldEvent{tableId='TESTDDL2', fieldName='A11'} 
[INFO ] 2024-08-16 11:34:50.661 - [任务 10][Oracle] - Alter table in memory, qualified name: T_oracle_io_tapdata_1_0-SNAPSHOT_TESTDDL2_66bdc702e914743d45ca7383_66bdcff6e914743d45ca759a 
[INFO ] 2024-08-16 11:34:50.662 - [任务 10][Oracle] - Alter table schema transform finished 
[INFO ] 2024-08-16 11:57:34.948 - [任务 10] - Stop task milestones: 66bdcff6e914743d45ca759a(任务 10)  
[INFO ] 2024-08-16 11:57:35.017 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] running status set to false 
[INFO ] 2024-08-16 11:57:35.176 - [任务 10][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-16 11:57:35.176 - [任务 10][Oracle] - Log Miner has been closed! 
[ERROR] 2024-08-16 11:57:35.305 - [任务 10][Oracle] - Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 96987860 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TESTDDL2'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 96987860 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TESTDDL2'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()
 <-- Error Message -->
Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 96987860 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TESTDDL2'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 96987860 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TESTDDL2'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()


<-- Simple Stack Trace -->
Caused by: oracle.jdbc.OracleDatabaseException: ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	...

<-- Full Stack Trace -->
Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 96987860 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TESTDDL2'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 96987860 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TESTDDL2'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:444)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 96987860 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TESTDDL2'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 96987860 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TESTDDL2'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 19 more

[INFO ] 2024-08-16 11:57:35.306 - [任务 10][Oracle] - PDK connector node stopped: HazelcastSourcePdkDataNode-4148a62c-c07d-487a-8c36-ba9a9cdfc78e 
[INFO ] 2024-08-16 11:57:35.306 - [任务 10][Oracle] - PDK connector node released: HazelcastSourcePdkDataNode-4148a62c-c07d-487a-8c36-ba9a9cdfc78e 
[INFO ] 2024-08-16 11:57:35.306 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] schema data cleaned 
[INFO ] 2024-08-16 11:57:35.306 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] monitor closed 
[INFO ] 2024-08-16 11:57:35.307 - [任务 10][Oracle] - Node Oracle[4148a62c-c07d-487a-8c36-ba9a9cdfc78e] close complete, cost 291 ms 
[INFO ] 2024-08-16 11:57:35.307 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] running status set to false 
[INFO ] 2024-08-16 11:57:35.337 - [任务 10][Mysql3306] - PDK connector node stopped: HazelcastTargetPdkDataNode-6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9 
[INFO ] 2024-08-16 11:57:35.337 - [任务 10][Mysql3306] - PDK connector node released: HazelcastTargetPdkDataNode-6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9 
[INFO ] 2024-08-16 11:57:35.337 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] schema data cleaned 
[INFO ] 2024-08-16 11:57:35.337 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] monitor closed 
[INFO ] 2024-08-16 11:57:35.546 - [任务 10][Mysql3306] - Node Mysql3306[6b1f026c-4f22-4e4a-87ac-b1fa0eb3a3c9] close complete, cost 30 ms 
[INFO ] 2024-08-16 11:57:40.123 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-16 11:57:40.125 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-08-16 11:57:40.125 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-08-16 11:57:40.152 - [任务 10] - Remove memory task client succeed, task: 任务 10[66bdcff6e914743d45ca759a] 
[INFO ] 2024-08-16 11:57:40.154 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[66bdcff6e914743d45ca759a] 
