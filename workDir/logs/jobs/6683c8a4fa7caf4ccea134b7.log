[INFO ] 2024-07-02 17:30:45.072 - [任务 44] - Start task milestones: 6683c8a4fa7caf4ccea134b7(任务 44) 
[INFO ] 2024-07-02 17:30:45.072 - [任务 44] - Task initialization... 
[INFO ] 2024-07-02 17:30:45.247 - [任务 44] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:30:45.264 - [任务 44] - The engine receives 任务 44 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 17:30:45.305 - [任务 44][POLICYB] - Node POLICYB[74346065-1510-4111-840f-9af7fc9db7d4] start preload schema,table counts: 1 
[INFO ] 2024-07-02 17:30:45.305 - [任务 44][POLICY] - Node POLICY[9c05e2ea-e680-4bc6-8c71-c3aa122b23dd] start preload schema,table counts: 1 
[INFO ] 2024-07-02 17:30:45.305 - [任务 44][POLICY] - Node POLICY[9c05e2ea-e680-4bc6-8c71-c3aa122b23dd] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 17:30:45.306 - [任务 44][POLICYB] - Node POLICYB[74346065-1510-4111-840f-9af7fc9db7d4] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 17:30:46.113 - [任务 44][POLICYB] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 17:30:46.559 - [任务 44][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-02 17:30:46.559 - [任务 44][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-02 17:30:46.559 - [任务 44][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-02 17:30:46.614 - [任务 44][POLICY] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":5281215,"gtidSet":""} 
[INFO ] 2024-07-02 17:30:46.614 - [任务 44][POLICY] - Initial sync started 
[INFO ] 2024-07-02 17:30:46.622 - [任务 44][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-02 17:30:46.624 - [任务 44][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-02 17:30:46.695 - [任务 44][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-02 17:30:46.785 - [任务 44][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-02 17:30:46.789 - [任务 44][POLICY] - Initial sync completed 
[INFO ] 2024-07-02 17:30:46.789 - [任务 44][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-02 17:30:46.789 - [任务 44][POLICY] - Initial sync completed 
[INFO ] 2024-07-02 17:30:46.789 - [任务 44][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"filename":"binlog.000032","position":5281215,"gtidSet":""} 
[INFO ] 2024-07-02 17:30:46.905 - [任务 44][POLICY] - Starting mysql cdc, server name: dbf2e389-8f6f-4d88-96d3-dd12386d1f63 
[INFO ] 2024-07-02 17:30:46.905 - [任务 44][POLICY] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 184293016
  time.precision.mode: adaptive_time_microseconds
  database.server.name: dbf2e389-8f6f-4d88-96d3-dd12386d1f63
  database.port: 3306
  threadName: Debezium-Mysql-Connector-dbf2e389-8f6f-4d88-96d3-dd12386d1f63
  database.hostname: localhost
  database.password: ********
  name: dbf2e389-8f6f-4d88-96d3-dd12386d1f63
  pdk.offset.string: {"name":"dbf2e389-8f6f-4d88-96d3-dd12386d1f63","offset":{"{\"server\":\"dbf2e389-8f6f-4d88-96d3-dd12386d1f63\"}":"{\"file\":\"binlog.000032\",\"pos\":5281215,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.POLICY,test2._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-02 17:30:47.309 - [任务 44][POLICY] - Connector Mysql incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-02 17:33:20.641 - [任务 44][POLICY] - Node POLICY[9c05e2ea-e680-4bc6-8c71-c3aa122b23dd] running status set to false 
[INFO ] 2024-07-02 17:33:20.689 - [任务 44][POLICY] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-02 17:33:20.692 - [任务 44][POLICY] - Mysql binlog reader stopped 
[INFO ] 2024-07-02 17:33:20.692 - [任务 44][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-9c05e2ea-e680-4bc6-8c71-c3aa122b23dd 
[INFO ] 2024-07-02 17:33:20.692 - [任务 44][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-9c05e2ea-e680-4bc6-8c71-c3aa122b23dd 
[INFO ] 2024-07-02 17:33:20.693 - [任务 44][POLICY] - Node POLICY[9c05e2ea-e680-4bc6-8c71-c3aa122b23dd] schema data cleaned 
[INFO ] 2024-07-02 17:33:20.695 - [任务 44][POLICY] - Node POLICY[9c05e2ea-e680-4bc6-8c71-c3aa122b23dd] monitor closed 
[INFO ] 2024-07-02 17:33:20.696 - [任务 44][POLICY] - Node POLICY[9c05e2ea-e680-4bc6-8c71-c3aa122b23dd] close complete, cost 56 ms 
[INFO ] 2024-07-02 17:33:20.696 - [任务 44][POLICYB] - Node POLICYB[74346065-1510-4111-840f-9af7fc9db7d4] running status set to false 
[INFO ] 2024-07-02 17:33:20.708 - [任务 44][POLICYB] - PDK connector node stopped: HazelcastTargetPdkDataNode-74346065-1510-4111-840f-9af7fc9db7d4 
[INFO ] 2024-07-02 17:33:20.708 - [任务 44][POLICYB] - PDK connector node released: HazelcastTargetPdkDataNode-74346065-1510-4111-840f-9af7fc9db7d4 
[INFO ] 2024-07-02 17:33:20.708 - [任务 44][POLICYB] - Node POLICYB[74346065-1510-4111-840f-9af7fc9db7d4] schema data cleaned 
[INFO ] 2024-07-02 17:33:20.708 - [任务 44][POLICYB] - Node POLICYB[74346065-1510-4111-840f-9af7fc9db7d4] monitor closed 
[INFO ] 2024-07-02 17:33:20.910 - [任务 44][POLICYB] - Node POLICYB[74346065-1510-4111-840f-9af7fc9db7d4] close complete, cost 14 ms 
[INFO ] 2024-07-02 17:33:28.107 - [任务 44] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:33:28.108 - [任务 44] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3f607df7 
[INFO ] 2024-07-02 17:33:28.249 - [任务 44] - Stop task milestones: 6683c8a4fa7caf4ccea134b7(任务 44)  
[INFO ] 2024-07-02 17:33:28.251 - [任务 44] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:33:28.251 - [任务 44] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 17:33:28.275 - [任务 44] - Remove memory task client succeed, task: 任务 44[6683c8a4fa7caf4ccea134b7] 
[INFO ] 2024-07-02 17:33:28.277 - [任务 44] - Destroy memory task client cache succeed, task: 任务 44[6683c8a4fa7caf4ccea134b7] 
