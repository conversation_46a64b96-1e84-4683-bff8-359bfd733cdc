[INFO ] 2024-08-15 16:14:41.074 - [任务 5] - Start task milestones: 66bdb8cde914743d45ca708d(任务 5) 
[INFO ] 2024-08-15 16:14:41.076 - [任务 5] - Task initialization... 
[INFO ] 2024-08-15 16:14:41.135 - [任务 5] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-08-15 16:14:41.354 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-15 16:14:41.693 - [任务 5][DB2TEST] - Node DB2TEST[11c157a1-f32a-4226-99a4-0a277b65d341] start preload schema,table counts: 3 
[INFO ] 2024-08-15 16:14:41.694 - [任务 5][Mongo] - Node <PERSON>[b669c666-47dd-4b6e-aaa7-649f5855d310] start preload schema,table counts: 3 
[INFO ] 2024-08-15 16:14:41.886 - [任务 5][Mongo] - No<PERSON>[b669c666-47dd-4b6e-aaa7-649f5855d310] preload schema finished, cost 185 ms 
[INFO ] 2024-08-15 16:14:41.894 - [任务 5][DB2TEST] - Node DB2TEST[11c157a1-f32a-4226-99a4-0a277b65d341] preload schema finished, cost 186 ms 
[INFO ] 2024-08-15 16:14:43.022 - [任务 5][DB2TEST] - Source node "DB2TEST" read batch size: 100 
[INFO ] 2024-08-15 16:14:43.025 - [任务 5][DB2TEST] - Source node "DB2TEST" event queue capacity: 200 
[INFO ] 2024-08-15 16:14:43.026 - [任务 5][DB2TEST] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-15 16:14:43.202 - [任务 5][DB2TEST] - Table [CUSTOMERS, CUSTOMERS1, TEST1] not open CDC 
[INFO ] 2024-08-15 16:14:43.438 - [任务 5][DB2TEST] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1723709683200} 
[INFO ] 2024-08-15 16:14:43.661 - [任务 5][DB2TEST] - Initial sync started 
[INFO ] 2024-08-15 16:14:43.672 - [任务 5][DB2TEST] - Starting batch read, table name: CUSTOMERS, offset: null 
[INFO ] 2024-08-15 16:14:43.735 - [任务 5][DB2TEST] - Table CUSTOMERS is going to be initial synced 
[INFO ] 2024-08-15 16:14:43.736 - [任务 5][DB2TEST] - Table [CUSTOMERS] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-15 16:14:43.756 - [任务 5][DB2TEST] - Query table 'CUSTOMERS' counts: 3 
[INFO ] 2024-08-15 16:14:43.757 - [任务 5][DB2TEST] - Starting batch read, table name: CUSTOMERS1, offset: null 
[INFO ] 2024-08-15 16:14:43.757 - [任务 5][DB2TEST] - Table CUSTOMERS1 is going to be initial synced 
[INFO ] 2024-08-15 16:14:43.789 - [任务 5][DB2TEST] - Table [CUSTOMERS1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-15 16:14:43.790 - [任务 5][DB2TEST] - Query table 'CUSTOMERS1' counts: 2 
[INFO ] 2024-08-15 16:14:43.791 - [任务 5][DB2TEST] - Starting batch read, table name: TEST1, offset: null 
[INFO ] 2024-08-15 16:14:43.825 - [任务 5][DB2TEST] - Table TEST1 is going to be initial synced 
[INFO ] 2024-08-15 16:14:43.825 - [任务 5][DB2TEST] - Table [TEST1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-15 16:14:43.825 - [任务 5][DB2TEST] - Query table 'TEST1' counts: 1 
[INFO ] 2024-08-15 16:14:43.833 - [任务 5][DB2TEST] - Initial sync completed 
[INFO ] 2024-08-15 16:14:43.842 - [任务 5][DB2TEST] - Incremental sync starting... 
[INFO ] 2024-08-15 16:14:43.843 - [任务 5][DB2TEST] - Initial sync completed 
[INFO ] 2024-08-15 16:14:43.849 - [任务 5][DB2TEST] - Starting stream read, table list: [CUSTOMERS, CUSTOMERS1, TEST1], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1723709683200} 
[INFO ] 2024-08-15 16:14:45.187 - [任务 5][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-15 16:14:45.192 - [任务 5][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-15 16:15:07.279 - [任务 5] - Stop task milestones: 66bdb8cde914743d45ca708d(任务 5)  
[INFO ] 2024-08-15 16:15:07.282 - [任务 5][DB2TEST] - Node DB2TEST[11c157a1-f32a-4226-99a4-0a277b65d341] running status set to false 
[INFO ] 2024-08-15 16:15:07.329 - [任务 5][DB2TEST] - Log Miner is shutting down... 
[ERROR] 2024-08-15 16:15:07.332 - [任务 5][DB2TEST] - io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted <-- Full Stack Trace -->
java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:567)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:223)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	... 6 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.grpc.Status.asRuntimeException(Status.java:535)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:649)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:175)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.InterruptedException
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.throwIfInterrupted(ClientCalls.java:750)
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.waitAndDrain(ClientCalls.java:733)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.waitForNext(ClientCalls.java:623)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:644)
	... 21 more

[INFO ] 2024-08-15 16:15:09.606 - [任务 5][DB2TEST] - PDK connector node stopped: HazelcastSourcePdkDataNode-11c157a1-f32a-4226-99a4-0a277b65d341 
[INFO ] 2024-08-15 16:15:09.606 - [任务 5][DB2TEST] - PDK connector node released: HazelcastSourcePdkDataNode-11c157a1-f32a-4226-99a4-0a277b65d341 
[INFO ] 2024-08-15 16:15:09.608 - [任务 5][DB2TEST] - Node DB2TEST[11c157a1-f32a-4226-99a4-0a277b65d341] schema data cleaned 
[INFO ] 2024-08-15 16:15:09.613 - [任务 5][DB2TEST] - Node DB2TEST[11c157a1-f32a-4226-99a4-0a277b65d341] monitor closed 
[INFO ] 2024-08-15 16:15:09.614 - [任务 5][DB2TEST] - Node DB2TEST[11c157a1-f32a-4226-99a4-0a277b65d341] close complete, cost 2337 ms 
[INFO ] 2024-08-15 16:15:09.614 - [任务 5][Mongo] - Node Mongo[b669c666-47dd-4b6e-aaa7-649f5855d310] running status set to false 
[INFO ] 2024-08-15 16:15:09.636 - [任务 5][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-b669c666-47dd-4b6e-aaa7-649f5855d310 
[INFO ] 2024-08-15 16:15:09.638 - [任务 5][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-b669c666-47dd-4b6e-aaa7-649f5855d310 
[INFO ] 2024-08-15 16:15:09.638 - [任务 5][Mongo] - Node Mongo[b669c666-47dd-4b6e-aaa7-649f5855d310] schema data cleaned 
[INFO ] 2024-08-15 16:15:09.641 - [任务 5][Mongo] - Node Mongo[b669c666-47dd-4b6e-aaa7-649f5855d310] monitor closed 
[INFO ] 2024-08-15 16:15:09.641 - [任务 5][Mongo] - Node Mongo[b669c666-47dd-4b6e-aaa7-649f5855d310] close complete, cost 25 ms 
[INFO ] 2024-08-15 16:15:09.820 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-15 16:15:09.820 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-08-15 16:15:09.820 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-08-15 16:15:09.962 - [任务 5] - Remove memory task client succeed, task: 任务 5[66bdb8cde914743d45ca708d] 
[INFO ] 2024-08-15 16:15:09.962 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[66bdb8cde914743d45ca708d] 
