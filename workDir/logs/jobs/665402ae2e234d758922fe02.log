[INFO ] 2024-05-27 11:49:38.014 - [任务 1] - Task initialization... 
[INFO ] 2024-05-27 11:49:38.016 - [任务 1] - Start task milestones: 665402ae2e234d758922fe02(任务 1) 
[INFO ] 2024-05-27 11:49:38.016 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-27 11:49:38.154 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-27 11:49:38.563 - [任务 1][TEST2] - Node TEST2[4a8af383-b825-48d9-960a-6f30ef501866] start preload schema,table counts: 1 
[INFO ] 2024-05-27 11:49:38.564 - [任务 1][TEST2] - Node TEST2[7003d9f9-7685-481b-99b3-0da673182b16] start preload schema,table counts: 1 
[INFO ] 2024-05-27 11:49:38.696 - [任务 1][TEST2] - Node TEST2[4a8af383-b825-48d9-960a-6f30ef501866] preload schema finished, cost 132 ms 
[INFO ] 2024-05-27 11:49:38.901 - [任务 1][TEST2] - Node TEST2[7003d9f9-7685-481b-99b3-0da673182b16] preload schema finished, cost 131 ms 
[INFO ] 2024-05-27 11:49:39.519 - [任务 1][TEST2] - Source node "TEST2" read batch size: 100 
[INFO ] 2024-05-27 11:49:39.521 - [任务 1][TEST2] - Source node "TEST2" event queue capacity: 200 
[INFO ] 2024-05-27 11:49:39.525 - [任务 1][TEST2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-27 11:49:39.729 - [任务 1][TEST2] - batch offset found: {},stream offset found: {"cdcOffset":1716781779,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-05-27 11:49:39.862 - [任务 1][TEST2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-27 11:49:39.863 - [任务 1][TEST2] - Initial sync started 
[INFO ] 2024-05-27 11:49:39.864 - [任务 1][TEST2] - Starting batch read, table name: TEST2, offset: null 
[INFO ] 2024-05-27 11:49:39.879 - [任务 1][TEST2] - Table TEST2 is going to be initial synced 
[INFO ] 2024-05-27 11:49:43.293 - [任务 1][TEST2] - Query table 'TEST2' counts: 1076 
[INFO ] 2024-05-27 11:49:43.443 - [任务 1][TEST2] - Initial sync completed 
[INFO ] 2024-05-27 11:49:43.446 - [任务 1][TEST2] - Incremental sync starting... 
[INFO ] 2024-05-27 11:49:43.446 - [任务 1][TEST2] - Initial sync completed 
[INFO ] 2024-05-27 11:49:43.451 - [任务 1][TEST2] - Starting stream read, table list: [TEST2], offset: {"cdcOffset":1716781779,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-05-27 11:49:43.652 - [任务 1][TEST2] - Connector MongoDB incremental start succeed, tables: [TEST2], data change syncing 
[WARN ] 2024-05-27 11:50:27.784 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: record Time Out
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-27 11:51:28.845 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-05-27 12:01:21.759 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: record Time Out
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-05-27 12:02:21.948 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: time out
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-27 12:03:23.076 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-05-27 12:03:47.886 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: record Time Out
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-27 12:04:48.951 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-05-27 12:05:18.437 - [任务 1][TEST2] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLException: Connection is closed
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-05-27 12:05:34.062 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: record Time Out
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-27 12:06:22.997 - [任务 1][TEST2] - [Auto Retry] Method (target_write_record) retry succeed 
[WARN ] 2024-05-27 12:06:34.147 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: time out
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-27 12:07:37.861 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-27 12:10:28.602 - [任务 1] - Stop task milestones: 665402ae2e234d758922fe02(任务 1)  
[INFO ] 2024-05-27 12:10:29.087 - [任务 1][TEST2] - Node TEST2[4a8af383-b825-48d9-960a-6f30ef501866] running status set to false 
[INFO ] 2024-05-27 12:10:29.088 - [任务 1][TEST2] - PDK connector node stopped: HazelcastSourcePdkDataNode-4a8af383-b825-48d9-960a-6f30ef501866 
[INFO ] 2024-05-27 12:10:29.091 - [任务 1][TEST2] - PDK connector node released: HazelcastSourcePdkDataNode-4a8af383-b825-48d9-960a-6f30ef501866 
[INFO ] 2024-05-27 12:10:29.091 - [任务 1][TEST2] - Node TEST2[4a8af383-b825-48d9-960a-6f30ef501866] schema data cleaned 
[INFO ] 2024-05-27 12:10:29.100 - [任务 1][TEST2] - Node TEST2[4a8af383-b825-48d9-960a-6f30ef501866] monitor closed 
[INFO ] 2024-05-27 12:10:29.100 - [任务 1][TEST2] - Node TEST2[4a8af383-b825-48d9-960a-6f30ef501866] close complete, cost 56 ms 
[INFO ] 2024-05-27 12:10:29.100 - [任务 1][TEST2] - Node TEST2[7003d9f9-7685-481b-99b3-0da673182b16] running status set to false 
[INFO ] 2024-05-27 12:10:29.116 - [任务 1][TEST2] - PDK connector node stopped: HazelcastTargetPdkDataNode-7003d9f9-7685-481b-99b3-0da673182b16 
[INFO ] 2024-05-27 12:10:29.117 - [任务 1][TEST2] - PDK connector node released: HazelcastTargetPdkDataNode-7003d9f9-7685-481b-99b3-0da673182b16 
[INFO ] 2024-05-27 12:10:29.118 - [任务 1][TEST2] - Node TEST2[7003d9f9-7685-481b-99b3-0da673182b16] schema data cleaned 
[INFO ] 2024-05-27 12:10:29.119 - [任务 1][TEST2] - Node TEST2[7003d9f9-7685-481b-99b3-0da673182b16] monitor closed 
[INFO ] 2024-05-27 12:10:29.120 - [任务 1][TEST2] - Node TEST2[7003d9f9-7685-481b-99b3-0da673182b16] close complete, cost 19 ms 
[INFO ] 2024-05-27 12:10:33.680 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-27 12:10:33.680 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-05-27 12:10:33.733 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-05-27 12:10:33.734 - [任务 1] - Remove memory task client succeed, task: 任务 1[665402ae2e234d758922fe02] 
[INFO ] 2024-05-27 12:10:33.734 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[665402ae2e234d758922fe02] 
[INFO ] 2024-05-27 13:26:50.103 - [任务 1] - Task initialization... 
[INFO ] 2024-05-27 13:26:50.182 - [任务 1] - Start task milestones: 665402ae2e234d758922fe02(任务 1) 
[INFO ] 2024-05-27 13:26:50.182 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-27 13:26:50.342 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-27 13:26:50.425 - [任务 1][TEST2] - Node TEST2[4a8af383-b825-48d9-960a-6f30ef501866] start preload schema,table counts: 1 
[INFO ] 2024-05-27 13:26:50.477 - [任务 1][TEST2] - Node TEST2[7003d9f9-7685-481b-99b3-0da673182b16] start preload schema,table counts: 1 
[INFO ] 2024-05-27 13:26:50.478 - [任务 1][TEST2] - Node TEST2[7003d9f9-7685-481b-99b3-0da673182b16] preload schema finished, cost 46 ms 
[INFO ] 2024-05-27 13:26:50.478 - [任务 1][TEST2] - Node TEST2[4a8af383-b825-48d9-960a-6f30ef501866] preload schema finished, cost 51 ms 
[INFO ] 2024-05-27 13:26:50.823 - [任务 1][TEST2] - Source node "TEST2" read batch size: 100 
[INFO ] 2024-05-27 13:26:50.823 - [任务 1][TEST2] - Source node "TEST2" event queue capacity: 200 
[INFO ] 2024-05-27 13:26:50.849 - [任务 1][TEST2] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-05-27 13:26:50.876 - [任务 1][TEST2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-27 13:26:50.877 - [任务 1][TEST2] - batch offset found: {"TEST2":{"sortKey":"_id","value":"655474f6c9a4136eae187031","objectId":null}},stream offset found: {"_data":{"value":"826654068C0000000B2B022C0100296E5A1004D30D423397B54580BC9C24EE2636280A463C5F6964003C363535343734663363396134313336656165313836623639000004","bsonType":"STRING","binary":false,"double":false,"int32":false,"string":true,"int64":false,"symbol":false,"boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"number":false,"array":false,"null":false}} 
[INFO ] 2024-05-27 13:26:50.939 - [任务 1][TEST2] - Incremental sync starting... 
[INFO ] 2024-05-27 13:26:50.939 - [任务 1][TEST2] - Initial sync completed 
[INFO ] 2024-05-27 13:26:51.009 - [任务 1][TEST2] - Starting stream read, table list: [TEST2], offset: {"_data":{"value":"826654068C0000000B2B022C0100296E5A1004D30D423397B54580BC9C24EE2636280A463C5F6964003C363535343734663363396134313336656165313836623639000004","bsonType":"STRING","binary":false,"double":false,"int32":false,"string":true,"int64":false,"symbol":false,"boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"number":false,"array":false,"null":false}} 
[INFO ] 2024-05-27 13:26:51.009 - [任务 1][TEST2] - Connector MongoDB incremental start succeed, tables: [TEST2], data change syncing 
[WARN ] 2024-05-27 13:28:51.258 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: record Time Out
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-27 13:29:52.443 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-27 14:16:24.171 - [任务 1] - Stop task milestones: 665402ae2e234d758922fe02(任务 1)  
[INFO ] 2024-05-27 14:16:24.433 - [任务 1][TEST2] - Node TEST2[4a8af383-b825-48d9-960a-6f30ef501866] running status set to false 
[INFO ] 2024-05-27 14:16:24.434 - [任务 1][TEST2] - PDK connector node stopped: HazelcastSourcePdkDataNode-4a8af383-b825-48d9-960a-6f30ef501866 
[INFO ] 2024-05-27 14:16:24.434 - [任务 1][TEST2] - PDK connector node released: HazelcastSourcePdkDataNode-4a8af383-b825-48d9-960a-6f30ef501866 
[INFO ] 2024-05-27 14:16:24.434 - [任务 1][TEST2] - Node TEST2[4a8af383-b825-48d9-960a-6f30ef501866] schema data cleaned 
[INFO ] 2024-05-27 14:16:24.436 - [任务 1][TEST2] - Node TEST2[4a8af383-b825-48d9-960a-6f30ef501866] monitor closed 
[INFO ] 2024-05-27 14:16:24.437 - [任务 1][TEST2] - Node TEST2[4a8af383-b825-48d9-960a-6f30ef501866] close complete, cost 44 ms 
[INFO ] 2024-05-27 14:16:24.437 - [任务 1][TEST2] - Node TEST2[7003d9f9-7685-481b-99b3-0da673182b16] running status set to false 
[INFO ] 2024-05-27 14:16:24.459 - [任务 1][TEST2] - PDK connector node stopped: HazelcastTargetPdkDataNode-7003d9f9-7685-481b-99b3-0da673182b16 
[INFO ] 2024-05-27 14:16:24.463 - [任务 1][TEST2] - PDK connector node released: HazelcastTargetPdkDataNode-7003d9f9-7685-481b-99b3-0da673182b16 
[INFO ] 2024-05-27 14:16:24.463 - [任务 1][TEST2] - Node TEST2[7003d9f9-7685-481b-99b3-0da673182b16] schema data cleaned 
[INFO ] 2024-05-27 14:16:24.465 - [任务 1][TEST2] - Node TEST2[7003d9f9-7685-481b-99b3-0da673182b16] monitor closed 
[INFO ] 2024-05-27 14:16:24.465 - [任务 1][TEST2] - Node TEST2[7003d9f9-7685-481b-99b3-0da673182b16] close complete, cost 27 ms 
