[INFO ] 2024-03-29 15:00:07.024 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:07.025 - [orders_import_import_import_import_import(100)][f459e25a-1af7-4a1c-abec-0f827c019836] - Node f459e25a-1af7-4a1c-abec-0f827c019836[f459e25a-1af7-4a1c-abec-0f827c019836] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:00:07.026 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:07.026 - [orders_import_import_import_import_import(100)][f459e25a-1af7-4a1c-abec-0f827c019836] - Node f459e25a-1af7-4a1c-abec-0f827c019836[f459e25a-1af7-4a1c-abec-0f827c019836] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:07.026 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:07.027 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:07.290 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:00:07.438 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6acf6e18 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6acf6e18 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6acf6e18 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:00:08.539 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] running status set to false 
[INFO ] 2024-03-29 15:00:08.540 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:08.541 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:08.543 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] schema data cleaned 
[INFO ] 2024-03-29 15:00:08.553 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] monitor closed 
[INFO ] 2024-03-29 15:00:08.554 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] close complete, cost 52 ms 
[INFO ] 2024-03-29 15:00:09.974 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] running status set to false 
[INFO ] 2024-03-29 15:00:09.981 - [orders_import_import_import_import_import(100)][f459e25a-1af7-4a1c-abec-0f827c019836] - Node f459e25a-1af7-4a1c-abec-0f827c019836[f459e25a-1af7-4a1c-abec-0f827c019836] running status set to false 
[INFO ] 2024-03-29 15:00:09.982 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] schema data cleaned 
[INFO ] 2024-03-29 15:00:09.982 - [orders_import_import_import_import_import(100)][f459e25a-1af7-4a1c-abec-0f827c019836] - Node f459e25a-1af7-4a1c-abec-0f827c019836[f459e25a-1af7-4a1c-abec-0f827c019836] schema data cleaned 
[INFO ] 2024-03-29 15:00:09.985 - [orders_import_import_import_import_import(100)][f459e25a-1af7-4a1c-abec-0f827c019836] - Node f459e25a-1af7-4a1c-abec-0f827c019836[f459e25a-1af7-4a1c-abec-0f827c019836] monitor closed 
[INFO ] 2024-03-29 15:00:09.993 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] monitor closed 
[INFO ] 2024-03-29 15:00:09.997 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] close complete, cost 9 ms 
[INFO ] 2024-03-29 15:00:09.998 - [orders_import_import_import_import_import(100)][f459e25a-1af7-4a1c-abec-0f827c019836] - Node f459e25a-1af7-4a1c-abec-0f827c019836[f459e25a-1af7-4a1c-abec-0f827c019836] close complete, cost 8 ms 
[INFO ] 2024-03-29 15:00:09.999 - [orders_import_import_import_import_import(100)] - load tapTable task 660666f4143fbb641a910f3b-f459e25a-1af7-4a1c-abec-0f827c019836 complete, cost 4070ms 
[INFO ] 2024-03-29 15:00:17.759 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:17.760 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:17.761 - [orders_import_import_import_import_import(100)][4ff74db3-76cf-44dd-83e2-29a2fb8cf067] - Node 4ff74db3-76cf-44dd-83e2-29a2fb8cf067[4ff74db3-76cf-44dd-83e2-29a2fb8cf067] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:00:17.761 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:17.763 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:17.770 - [orders_import_import_import_import_import(100)][4ff74db3-76cf-44dd-83e2-29a2fb8cf067] - Node 4ff74db3-76cf-44dd-83e2-29a2fb8cf067[4ff74db3-76cf-44dd-83e2-29a2fb8cf067] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:17.829 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:00:18.040 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2ac3db05 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2ac3db05 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2ac3db05 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:00:18.068 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] running status set to false 
[INFO ] 2024-03-29 15:00:18.068 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:18.068 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:18.071 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] schema data cleaned 
[INFO ] 2024-03-29 15:00:18.072 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] monitor closed 
[INFO ] 2024-03-29 15:00:18.277 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] close complete, cost 31 ms 
[INFO ] 2024-03-29 15:00:18.541 - [orders_import_import_import_import_import(100)][03ae0b08-51b6-46fe-958f-773ee0fb6e6c] - Node 03ae0b08-51b6-46fe-958f-773ee0fb6e6c[03ae0b08-51b6-46fe-958f-773ee0fb6e6c] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:00:18.541 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:18.541 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:18.541 - [orders_import_import_import_import_import(100)][03ae0b08-51b6-46fe-958f-773ee0fb6e6c] - Node 03ae0b08-51b6-46fe-958f-773ee0fb6e6c[03ae0b08-51b6-46fe-958f-773ee0fb6e6c] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 15:00:18.541 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:18.541 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:18.601 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:00:18.808 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@32f15572 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@32f15572 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@32f15572 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:00:18.948 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] running status set to false 
[INFO ] 2024-03-29 15:00:18.987 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:18.987 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:18.988 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] schema data cleaned 
[INFO ] 2024-03-29 15:00:18.990 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] monitor closed 
[INFO ] 2024-03-29 15:00:18.990 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] close complete, cost 48 ms 
[INFO ] 2024-03-29 15:00:20.394 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] running status set to false 
[INFO ] 2024-03-29 15:00:20.395 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] schema data cleaned 
[INFO ] 2024-03-29 15:00:20.395 - [orders_import_import_import_import_import(100)][4ff74db3-76cf-44dd-83e2-29a2fb8cf067] - Node 4ff74db3-76cf-44dd-83e2-29a2fb8cf067[4ff74db3-76cf-44dd-83e2-29a2fb8cf067] running status set to false 
[INFO ] 2024-03-29 15:00:20.395 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] monitor closed 
[INFO ] 2024-03-29 15:00:20.395 - [orders_import_import_import_import_import(100)][4ff74db3-76cf-44dd-83e2-29a2fb8cf067] - Node 4ff74db3-76cf-44dd-83e2-29a2fb8cf067[4ff74db3-76cf-44dd-83e2-29a2fb8cf067] schema data cleaned 
[INFO ] 2024-03-29 15:00:20.396 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] close complete, cost 12 ms 
[INFO ] 2024-03-29 15:00:20.396 - [orders_import_import_import_import_import(100)][4ff74db3-76cf-44dd-83e2-29a2fb8cf067] - Node 4ff74db3-76cf-44dd-83e2-29a2fb8cf067[4ff74db3-76cf-44dd-83e2-29a2fb8cf067] monitor closed 
[INFO ] 2024-03-29 15:00:20.396 - [orders_import_import_import_import_import(100)][4ff74db3-76cf-44dd-83e2-29a2fb8cf067] - Node 4ff74db3-76cf-44dd-83e2-29a2fb8cf067[4ff74db3-76cf-44dd-83e2-29a2fb8cf067] close complete, cost 7 ms 
[INFO ] 2024-03-29 15:00:20.396 - [orders_import_import_import_import_import(100)] - load tapTable task 660666f4143fbb641a910f3b-4ff74db3-76cf-44dd-83e2-29a2fb8cf067 complete, cost 2730ms 
[INFO ] 2024-03-29 15:00:21.170 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] running status set to false 
[INFO ] 2024-03-29 15:00:21.173 - [orders_import_import_import_import_import(100)][03ae0b08-51b6-46fe-958f-773ee0fb6e6c] - Node 03ae0b08-51b6-46fe-958f-773ee0fb6e6c[03ae0b08-51b6-46fe-958f-773ee0fb6e6c] running status set to false 
[INFO ] 2024-03-29 15:00:21.173 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] schema data cleaned 
[INFO ] 2024-03-29 15:00:21.173 - [orders_import_import_import_import_import(100)][03ae0b08-51b6-46fe-958f-773ee0fb6e6c] - Node 03ae0b08-51b6-46fe-958f-773ee0fb6e6c[03ae0b08-51b6-46fe-958f-773ee0fb6e6c] schema data cleaned 
[INFO ] 2024-03-29 15:00:21.173 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] monitor closed 
[INFO ] 2024-03-29 15:00:21.174 - [orders_import_import_import_import_import(100)][03ae0b08-51b6-46fe-958f-773ee0fb6e6c] - Node 03ae0b08-51b6-46fe-958f-773ee0fb6e6c[03ae0b08-51b6-46fe-958f-773ee0fb6e6c] monitor closed 
[INFO ] 2024-03-29 15:00:21.174 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] close complete, cost 8 ms 
[INFO ] 2024-03-29 15:00:21.175 - [orders_import_import_import_import_import(100)][03ae0b08-51b6-46fe-958f-773ee0fb6e6c] - Node 03ae0b08-51b6-46fe-958f-773ee0fb6e6c[03ae0b08-51b6-46fe-958f-773ee0fb6e6c] close complete, cost 2 ms 
[INFO ] 2024-03-29 15:00:21.175 - [orders_import_import_import_import_import(100)] - load tapTable task 660666f4143fbb641a910f3b-03ae0b08-51b6-46fe-958f-773ee0fb6e6c complete, cost 2706ms 
[INFO ] 2024-03-29 15:00:22.738 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:22.738 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:22.741 - [orders_import_import_import_import_import(100)][3c7d88eb-5664-47ed-9720-e146f10a98af] - Node 3c7d88eb-5664-47ed-9720-e146f10a98af[3c7d88eb-5664-47ed-9720-e146f10a98af] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:00:22.744 - [orders_import_import_import_import_import(100)][3c7d88eb-5664-47ed-9720-e146f10a98af] - Node 3c7d88eb-5664-47ed-9720-e146f10a98af[3c7d88eb-5664-47ed-9720-e146f10a98af] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:22.744 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:22.749 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:22.951 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:00:22.988 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6bedfc33 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6bedfc33 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6bedfc33 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:00:22.992 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:22.993 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:23.003 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:23.007 - [orders_import_import_import_import_import(100)][ec02b00a-20e3-484f-92cf-7db0b9f3d8fa] - Node ec02b00a-20e3-484f-92cf-7db0b9f3d8fa[ec02b00a-20e3-484f-92cf-7db0b9f3d8fa] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:00:23.009 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:23.011 - [orders_import_import_import_import_import(100)][ec02b00a-20e3-484f-92cf-7db0b9f3d8fa] - Node ec02b00a-20e3-484f-92cf-7db0b9f3d8fa[ec02b00a-20e3-484f-92cf-7db0b9f3d8fa] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:23.086 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 15:00:23.092 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] running status set to false 
[INFO ] 2024-03-29 15:00:23.122 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:23.122 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:23.122 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] schema data cleaned 
[INFO ] 2024-03-29 15:00:23.125 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] monitor closed 
[INFO ] 2024-03-29 15:00:23.125 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] close complete, cost 58 ms 
[INFO ] 2024-03-29 15:00:23.129 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:23.129 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 15:00:23.131 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:23.131 - [orders_import_import_import_import_import(100)][c0b12940-564d-4c22-be12-c0138e4d46ff] - Node c0b12940-564d-4c22-be12-c0138e4d46ff[c0b12940-564d-4c22-be12-c0138e4d46ff] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:00:23.131 - [orders_import_import_import_import_import(100)][c0b12940-564d-4c22-be12-c0138e4d46ff] - Node c0b12940-564d-4c22-be12-c0138e4d46ff[c0b12940-564d-4c22-be12-c0138e4d46ff] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:23.142 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 15:00:23.142 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6eb55539 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6eb55539 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6eb55539 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:00:23.205 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:00:23.205 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2468ce6f error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2468ce6f error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2468ce6f error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:00:23.260 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] running status set to false 
[INFO ] 2024-03-29 15:00:23.261 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:23.261 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:23.261 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] schema data cleaned 
[INFO ] 2024-03-29 15:00:23.261 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] monitor closed 
[INFO ] 2024-03-29 15:00:23.391 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] close complete, cost 12 ms 
[INFO ] 2024-03-29 15:00:23.396 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] running status set to false 
[INFO ] 2024-03-29 15:00:23.404 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:23.404 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:23.405 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] schema data cleaned 
[INFO ] 2024-03-29 15:00:23.405 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] monitor closed 
[INFO ] 2024-03-29 15:00:23.406 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] close complete, cost 20 ms 
[INFO ] 2024-03-29 15:00:23.832 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:23.832 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:23.833 - [orders_import_import_import_import_import(100)][e8349e81-cd15-48f6-b750-6a78b2143b25] - Node e8349e81-cd15-48f6-b750-6a78b2143b25[e8349e81-cd15-48f6-b750-6a78b2143b25] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:00:23.833 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:23.834 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:23.834 - [orders_import_import_import_import_import(100)][e8349e81-cd15-48f6-b750-6a78b2143b25] - Node e8349e81-cd15-48f6-b750-6a78b2143b25[e8349e81-cd15-48f6-b750-6a78b2143b25] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:23.902 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:00:23.902 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@12c018f4 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@12c018f4 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@12c018f4 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:00:24.065 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] running status set to false 
[INFO ] 2024-03-29 15:00:24.065 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:24.066 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:24.066 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] schema data cleaned 
[INFO ] 2024-03-29 15:00:24.067 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] monitor closed 
[INFO ] 2024-03-29 15:00:24.068 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] close complete, cost 12 ms 
[INFO ] 2024-03-29 15:00:24.472 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:24.473 - [orders_import_import_import_import_import(100)][f5a39e9e-5dd4-4859-8dfb-ef7730423f18] - Node f5a39e9e-5dd4-4859-8dfb-ef7730423f18[f5a39e9e-5dd4-4859-8dfb-ef7730423f18] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:00:24.473 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:24.474 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:24.474 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:24.475 - [orders_import_import_import_import_import(100)][f5a39e9e-5dd4-4859-8dfb-ef7730423f18] - Node f5a39e9e-5dd4-4859-8dfb-ef7730423f18[f5a39e9e-5dd4-4859-8dfb-ef7730423f18] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:24.529 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:00:24.529 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@31e442c2 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@31e442c2 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@31e442c2 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:00:24.693 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] running status set to false 
[INFO ] 2024-03-29 15:00:24.693 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:24.694 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:24.694 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] schema data cleaned 
[INFO ] 2024-03-29 15:00:24.695 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] monitor closed 
[INFO ] 2024-03-29 15:00:24.695 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] close complete, cost 14 ms 
[INFO ] 2024-03-29 15:00:25.500 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] running status set to false 
[INFO ] 2024-03-29 15:00:25.505 - [orders_import_import_import_import_import(100)][3c7d88eb-5664-47ed-9720-e146f10a98af] - Node 3c7d88eb-5664-47ed-9720-e146f10a98af[3c7d88eb-5664-47ed-9720-e146f10a98af] running status set to false 
[INFO ] 2024-03-29 15:00:25.507 - [orders_import_import_import_import_import(100)][3c7d88eb-5664-47ed-9720-e146f10a98af] - Node 3c7d88eb-5664-47ed-9720-e146f10a98af[3c7d88eb-5664-47ed-9720-e146f10a98af] schema data cleaned 
[INFO ] 2024-03-29 15:00:25.509 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] schema data cleaned 
[INFO ] 2024-03-29 15:00:25.509 - [orders_import_import_import_import_import(100)][3c7d88eb-5664-47ed-9720-e146f10a98af] - Node 3c7d88eb-5664-47ed-9720-e146f10a98af[3c7d88eb-5664-47ed-9720-e146f10a98af] monitor closed 
[INFO ] 2024-03-29 15:00:25.510 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] monitor closed 
[INFO ] 2024-03-29 15:00:25.510 - [orders_import_import_import_import_import(100)][3c7d88eb-5664-47ed-9720-e146f10a98af] - Node 3c7d88eb-5664-47ed-9720-e146f10a98af[3c7d88eb-5664-47ed-9720-e146f10a98af] close complete, cost 22 ms 
[INFO ] 2024-03-29 15:00:25.511 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] close complete, cost 23 ms 
[INFO ] 2024-03-29 15:00:25.511 - [orders_import_import_import_import_import(100)] - load tapTable task 660666f4143fbb641a910f3b-3c7d88eb-5664-47ed-9720-e146f10a98af complete, cost 2856ms 
[INFO ] 2024-03-29 15:00:25.674 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] running status set to false 
[INFO ] 2024-03-29 15:00:25.674 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] schema data cleaned 
[INFO ] 2024-03-29 15:00:25.674 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] monitor closed 
[INFO ] 2024-03-29 15:00:25.674 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] close complete, cost 3 ms 
[INFO ] 2024-03-29 15:00:25.674 - [orders_import_import_import_import_import(100)][ec02b00a-20e3-484f-92cf-7db0b9f3d8fa] - Node ec02b00a-20e3-484f-92cf-7db0b9f3d8fa[ec02b00a-20e3-484f-92cf-7db0b9f3d8fa] running status set to false 
[INFO ] 2024-03-29 15:00:25.674 - [orders_import_import_import_import_import(100)][ec02b00a-20e3-484f-92cf-7db0b9f3d8fa] - Node ec02b00a-20e3-484f-92cf-7db0b9f3d8fa[ec02b00a-20e3-484f-92cf-7db0b9f3d8fa] schema data cleaned 
[INFO ] 2024-03-29 15:00:25.674 - [orders_import_import_import_import_import(100)][ec02b00a-20e3-484f-92cf-7db0b9f3d8fa] - Node ec02b00a-20e3-484f-92cf-7db0b9f3d8fa[ec02b00a-20e3-484f-92cf-7db0b9f3d8fa] monitor closed 
[INFO ] 2024-03-29 15:00:25.675 - [orders_import_import_import_import_import(100)][ec02b00a-20e3-484f-92cf-7db0b9f3d8fa] - Node ec02b00a-20e3-484f-92cf-7db0b9f3d8fa[ec02b00a-20e3-484f-92cf-7db0b9f3d8fa] close complete, cost 1 ms 
[INFO ] 2024-03-29 15:00:25.675 - [orders_import_import_import_import_import(100)] - load tapTable task 660666f4143fbb641a910f3b-ec02b00a-20e3-484f-92cf-7db0b9f3d8fa complete, cost 2795ms 
[INFO ] 2024-03-29 15:00:25.734 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] running status set to false 
[INFO ] 2024-03-29 15:00:25.734 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] schema data cleaned 
[INFO ] 2024-03-29 15:00:25.735 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] monitor closed 
[INFO ] 2024-03-29 15:00:25.735 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] close complete, cost 1 ms 
[INFO ] 2024-03-29 15:00:25.737 - [orders_import_import_import_import_import(100)][c0b12940-564d-4c22-be12-c0138e4d46ff] - Node c0b12940-564d-4c22-be12-c0138e4d46ff[c0b12940-564d-4c22-be12-c0138e4d46ff] running status set to false 
[INFO ] 2024-03-29 15:00:25.737 - [orders_import_import_import_import_import(100)][c0b12940-564d-4c22-be12-c0138e4d46ff] - Node c0b12940-564d-4c22-be12-c0138e4d46ff[c0b12940-564d-4c22-be12-c0138e4d46ff] schema data cleaned 
[INFO ] 2024-03-29 15:00:25.738 - [orders_import_import_import_import_import(100)][c0b12940-564d-4c22-be12-c0138e4d46ff] - Node c0b12940-564d-4c22-be12-c0138e4d46ff[c0b12940-564d-4c22-be12-c0138e4d46ff] monitor closed 
[INFO ] 2024-03-29 15:00:25.738 - [orders_import_import_import_import_import(100)][c0b12940-564d-4c22-be12-c0138e4d46ff] - Node c0b12940-564d-4c22-be12-c0138e4d46ff[c0b12940-564d-4c22-be12-c0138e4d46ff] close complete, cost 0 ms 
[INFO ] 2024-03-29 15:00:25.943 - [orders_import_import_import_import_import(100)] - load tapTable task 660666f4143fbb641a910f3b-c0b12940-564d-4c22-be12-c0138e4d46ff complete, cost 2703ms 
[INFO ] 2024-03-29 15:00:26.312 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:26.312 - [orders_import_import_import_import_import(100)][6b049c45-91d8-4a3b-aabc-6ed8ee75660d] - Node 6b049c45-91d8-4a3b-aabc-6ed8ee75660d[6b049c45-91d8-4a3b-aabc-6ed8ee75660d] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:00:26.312 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:26.312 - [orders_import_import_import_import_import(100)][6b049c45-91d8-4a3b-aabc-6ed8ee75660d] - Node 6b049c45-91d8-4a3b-aabc-6ed8ee75660d[6b049c45-91d8-4a3b-aabc-6ed8ee75660d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:26.312 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:26.371 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 15:00:26.371 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:00:26.418 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7eec0da8 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7eec0da8 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7eec0da8 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:00:26.419 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] running status set to false 
[INFO ] 2024-03-29 15:00:26.419 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] schema data cleaned 
[INFO ] 2024-03-29 15:00:26.419 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] monitor closed 
[INFO ] 2024-03-29 15:00:26.419 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] close complete, cost 1 ms 
[INFO ] 2024-03-29 15:00:26.424 - [orders_import_import_import_import_import(100)][e8349e81-cd15-48f6-b750-6a78b2143b25] - Node e8349e81-cd15-48f6-b750-6a78b2143b25[e8349e81-cd15-48f6-b750-6a78b2143b25] running status set to false 
[INFO ] 2024-03-29 15:00:26.425 - [orders_import_import_import_import_import(100)][e8349e81-cd15-48f6-b750-6a78b2143b25] - Node e8349e81-cd15-48f6-b750-6a78b2143b25[e8349e81-cd15-48f6-b750-6a78b2143b25] schema data cleaned 
[INFO ] 2024-03-29 15:00:26.425 - [orders_import_import_import_import_import(100)][e8349e81-cd15-48f6-b750-6a78b2143b25] - Node e8349e81-cd15-48f6-b750-6a78b2143b25[e8349e81-cd15-48f6-b750-6a78b2143b25] monitor closed 
[INFO ] 2024-03-29 15:00:26.426 - [orders_import_import_import_import_import(100)][e8349e81-cd15-48f6-b750-6a78b2143b25] - Node e8349e81-cd15-48f6-b750-6a78b2143b25[e8349e81-cd15-48f6-b750-6a78b2143b25] close complete, cost 0 ms 
[INFO ] 2024-03-29 15:00:26.426 - [orders_import_import_import_import_import(100)] - load tapTable task 660666f4143fbb641a910f3b-e8349e81-cd15-48f6-b750-6a78b2143b25 complete, cost 2674ms 
[INFO ] 2024-03-29 15:00:26.572 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] running status set to false 
[INFO ] 2024-03-29 15:00:26.572 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:26.572 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:26.572 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] schema data cleaned 
[INFO ] 2024-03-29 15:00:26.573 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] monitor closed 
[INFO ] 2024-03-29 15:00:26.573 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] close complete, cost 12 ms 
[INFO ] 2024-03-29 15:00:27.055 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] running status set to false 
[INFO ] 2024-03-29 15:00:27.055 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] schema data cleaned 
[INFO ] 2024-03-29 15:00:27.055 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] monitor closed 
[INFO ] 2024-03-29 15:00:27.057 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] close complete, cost 0 ms 
[INFO ] 2024-03-29 15:00:27.057 - [orders_import_import_import_import_import(100)][f5a39e9e-5dd4-4859-8dfb-ef7730423f18] - Node f5a39e9e-5dd4-4859-8dfb-ef7730423f18[f5a39e9e-5dd4-4859-8dfb-ef7730423f18] running status set to false 
[INFO ] 2024-03-29 15:00:27.058 - [orders_import_import_import_import_import(100)][f5a39e9e-5dd4-4859-8dfb-ef7730423f18] - Node f5a39e9e-5dd4-4859-8dfb-ef7730423f18[f5a39e9e-5dd4-4859-8dfb-ef7730423f18] schema data cleaned 
[INFO ] 2024-03-29 15:00:27.058 - [orders_import_import_import_import_import(100)][f5a39e9e-5dd4-4859-8dfb-ef7730423f18] - Node f5a39e9e-5dd4-4859-8dfb-ef7730423f18[f5a39e9e-5dd4-4859-8dfb-ef7730423f18] monitor closed 
[INFO ] 2024-03-29 15:00:27.059 - [orders_import_import_import_import_import(100)][f5a39e9e-5dd4-4859-8dfb-ef7730423f18] - Node f5a39e9e-5dd4-4859-8dfb-ef7730423f18[f5a39e9e-5dd4-4859-8dfb-ef7730423f18] close complete, cost 0 ms 
[INFO ] 2024-03-29 15:00:27.060 - [orders_import_import_import_import_import(100)] - load tapTable task 660666f4143fbb641a910f3b-f5a39e9e-5dd4-4859-8dfb-ef7730423f18 complete, cost 2631ms 
[INFO ] 2024-03-29 15:00:28.920 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] running status set to false 
[INFO ] 2024-03-29 15:00:28.920 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] schema data cleaned 
[INFO ] 2024-03-29 15:00:28.922 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] monitor closed 
[INFO ] 2024-03-29 15:00:28.922 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] close complete, cost 2 ms 
[INFO ] 2024-03-29 15:00:28.926 - [orders_import_import_import_import_import(100)][6b049c45-91d8-4a3b-aabc-6ed8ee75660d] - Node 6b049c45-91d8-4a3b-aabc-6ed8ee75660d[6b049c45-91d8-4a3b-aabc-6ed8ee75660d] running status set to false 
[INFO ] 2024-03-29 15:00:28.926 - [orders_import_import_import_import_import(100)][6b049c45-91d8-4a3b-aabc-6ed8ee75660d] - Node 6b049c45-91d8-4a3b-aabc-6ed8ee75660d[6b049c45-91d8-4a3b-aabc-6ed8ee75660d] schema data cleaned 
[INFO ] 2024-03-29 15:00:28.926 - [orders_import_import_import_import_import(100)][6b049c45-91d8-4a3b-aabc-6ed8ee75660d] - Node 6b049c45-91d8-4a3b-aabc-6ed8ee75660d[6b049c45-91d8-4a3b-aabc-6ed8ee75660d] monitor closed 
[INFO ] 2024-03-29 15:00:28.926 - [orders_import_import_import_import_import(100)][6b049c45-91d8-4a3b-aabc-6ed8ee75660d] - Node 6b049c45-91d8-4a3b-aabc-6ed8ee75660d[6b049c45-91d8-4a3b-aabc-6ed8ee75660d] close complete, cost 1 ms 
[INFO ] 2024-03-29 15:00:28.927 - [orders_import_import_import_import_import(100)] - load tapTable task 660666f4143fbb641a910f3b-6b049c45-91d8-4a3b-aabc-6ed8ee75660d complete, cost 2670ms 
[INFO ] 2024-03-29 15:00:30.530 - [orders_import_import_import_import_import(100)][d18a8b3f-1919-4fd4-a4c1-fa8733be8326] - Node d18a8b3f-1919-4fd4-a4c1-fa8733be8326[d18a8b3f-1919-4fd4-a4c1-fa8733be8326] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:00:30.530 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:30.530 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:00:30.536 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:00:30.536 - [orders_import_import_import_import_import(100)][d18a8b3f-1919-4fd4-a4c1-fa8733be8326] - Node d18a8b3f-1919-4fd4-a4c1-fa8733be8326[d18a8b3f-1919-4fd4-a4c1-fa8733be8326] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 15:00:30.589 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 15:00:30.592 - [orders_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:00:30.773 - [orders_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6ac99afc error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6ac99afc error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6ac99afc error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:00:30.783 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] running status set to false 
[INFO ] 2024-03-29 15:00:30.787 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:30.787 - [orders_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5 
[INFO ] 2024-03-29 15:00:30.787 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] schema data cleaned 
[INFO ] 2024-03-29 15:00:30.788 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] monitor closed 
[INFO ] 2024-03-29 15:00:30.788 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[8b4e6cde-dc9d-41ab-b3a3-8cb1bf7943a5] close complete, cost 16 ms 
[INFO ] 2024-03-29 15:00:33.132 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] running status set to false 
[INFO ] 2024-03-29 15:00:33.132 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] schema data cleaned 
[INFO ] 2024-03-29 15:00:33.132 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] monitor closed 
[INFO ] 2024-03-29 15:00:33.132 - [orders_import_import_import_import_import(100)][Order Details] - Node Order Details[eb21256a-79fd-47d8-91b9-6761c0baf61b] close complete, cost 1 ms 
[INFO ] 2024-03-29 15:00:33.138 - [orders_import_import_import_import_import(100)][d18a8b3f-1919-4fd4-a4c1-fa8733be8326] - Node d18a8b3f-1919-4fd4-a4c1-fa8733be8326[d18a8b3f-1919-4fd4-a4c1-fa8733be8326] running status set to false 
[INFO ] 2024-03-29 15:00:33.139 - [orders_import_import_import_import_import(100)][d18a8b3f-1919-4fd4-a4c1-fa8733be8326] - Node d18a8b3f-1919-4fd4-a4c1-fa8733be8326[d18a8b3f-1919-4fd4-a4c1-fa8733be8326] schema data cleaned 
[INFO ] 2024-03-29 15:00:33.139 - [orders_import_import_import_import_import(100)][d18a8b3f-1919-4fd4-a4c1-fa8733be8326] - Node d18a8b3f-1919-4fd4-a4c1-fa8733be8326[d18a8b3f-1919-4fd4-a4c1-fa8733be8326] monitor closed 
[INFO ] 2024-03-29 15:00:33.141 - [orders_import_import_import_import_import(100)][d18a8b3f-1919-4fd4-a4c1-fa8733be8326] - Node d18a8b3f-1919-4fd4-a4c1-fa8733be8326[d18a8b3f-1919-4fd4-a4c1-fa8733be8326] close complete, cost 2 ms 
[INFO ] 2024-03-29 15:00:33.141 - [orders_import_import_import_import_import(100)] - load tapTable task 660666f4143fbb641a910f3b-d18a8b3f-1919-4fd4-a4c1-fa8733be8326 complete, cost 2675ms 
