[INFO ] 2024-03-29 18:30:08.632 - [employees_import_import_import_import_import] - Start task milestones: 660697bd57c8b774d7a94234(employees_import_import_import_import_import) 
[INFO ] 2024-03-29 18:30:08.632 - [employees_import_import_import_import_import] - Task initialization... 
[INFO ] 2024-03-29 18:30:08.838 - [employees_import_import_import_import_import] - <PERSON><PERSON> performs snapshot read by order list: [ null ] -> [ null,null ] -> [ null ] -> [ null ] 
[INFO ] 2024-03-29 18:30:08.910 - [employees_import_import_import_import_import] - The engine receives employees_import_import_import_import_import task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 18:30:09.033 - [employees_import_import_import_import_import][Rename Employees] - Node Rename Employees[a38d72ef-e6d3-4a5b-9b4c-c8487a4341aa] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:30:09.034 - [employees_import_import_import_import_import][Employees] - Node Employees[5f989a90-48ad-47a7-9ad9-e6264997b95d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:30:09.035 - [employees_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[2f23f1b0-de90-4ba5-ae2d-5a56bd1d95b2] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:30:09.035 - [employees_import_import_import_import_import][Rename Region] - Node Rename Region[9209d335-020d-404f-bcd9-0416475f4d77] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:30:09.035 - [employees_import_import_import_import_import][Territories] - Node Territories[ff8a6d60-9f4f-448e-8a13-a682a80f045b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:30:09.052 - [employees_import_import_import_import_import][merge] - Node merge[257f8a42-9da8-41da-b801-2a53c4d9b084] start preload schema,table counts: 6 
[INFO ] 2024-03-29 18:30:09.052 - [employees_import_import_import_import_import][Delete Region] - Node Delete Region[5d489e45-a316-403f-8767-f8ac6926bf2c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:30:09.052 - [employees_import_import_import_import_import][Rename Territories] - Node Rename Territories[8ab0b655-31e9-4b27-92b8-da5ddedf86dd] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:30:09.052 - [employees_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[216011de-413e-48c7-b956-d126eb0626e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:30:09.052 - [employees_import_import_import_import_import][Rename Employees] - Node Rename Employees[26ee3dc6-6cd7-4e89-9d5f-4a2f476f7e8a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:30:09.052 - [employees_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[ab8a5647-8136-4458-811c-a6824a4f77e2] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:30:09.052 - [employees_import_import_import_import_import][employees] - Node employees[f3fb546e-cb2e-439f-852f-7ce73037f915] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:30:09.052 - [employees_import_import_import_import_import][Employees] - Node Employees[d14ce5a2-df5f-4fee-9f77-81b02f91621a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:30:09.104 - [employees_import_import_import_import_import][Region] - Node Region[138b73b6-aee6-41fb-8723-7feaa3e53425] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:30:09.106 - [employees_import_import_import_import_import][Delete Territories] - Node Delete Territories[5482f5b9-6483-4d0a-af5e-e6edfe74c3ec] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:30:09.238 - [employees_import_import_import_import_import][Territories] - Node Territories[ff8a6d60-9f4f-448e-8a13-a682a80f045b] preload schema finished, cost 201 ms 
[INFO ] 2024-03-29 18:30:09.240 - [employees_import_import_import_import_import][Employees] - Node Employees[5f989a90-48ad-47a7-9ad9-e6264997b95d] preload schema finished, cost 202 ms 
[INFO ] 2024-03-29 18:30:09.240 - [employees_import_import_import_import_import][Rename Employees] - Node Rename Employees[a38d72ef-e6d3-4a5b-9b4c-c8487a4341aa] preload schema finished, cost 201 ms 
[INFO ] 2024-03-29 18:30:09.240 - [employees_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[ab8a5647-8136-4458-811c-a6824a4f77e2] preload schema finished, cost 183 ms 
[INFO ] 2024-03-29 18:30:09.240 - [employees_import_import_import_import_import][Delete Region] - Node Delete Region[5d489e45-a316-403f-8767-f8ac6926bf2c] preload schema finished, cost 184 ms 
[INFO ] 2024-03-29 18:30:09.241 - [employees_import_import_import_import_import][Rename Employees] - Node Rename Employees[26ee3dc6-6cd7-4e89-9d5f-4a2f476f7e8a] preload schema finished, cost 184 ms 
[INFO ] 2024-03-29 18:30:09.241 - [employees_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[2f23f1b0-de90-4ba5-ae2d-5a56bd1d95b2] preload schema finished, cost 201 ms 
[INFO ] 2024-03-29 18:30:09.245 - [employees_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[216011de-413e-48c7-b956-d126eb0626e9] preload schema finished, cost 192 ms 
[INFO ] 2024-03-29 18:30:09.246 - [employees_import_import_import_import_import][Rename Territories] - Node Rename Territories[8ab0b655-31e9-4b27-92b8-da5ddedf86dd] preload schema finished, cost 193 ms 
[INFO ] 2024-03-29 18:30:09.246 - [employees_import_import_import_import_import][Employees] - Node Employees[d14ce5a2-df5f-4fee-9f77-81b02f91621a] preload schema finished, cost 192 ms 
[INFO ] 2024-03-29 18:30:09.345 - [employees_import_import_import_import_import][Rename Region] - Node Rename Region[9209d335-020d-404f-bcd9-0416475f4d77] preload schema finished, cost 287 ms 
[INFO ] 2024-03-29 18:30:09.346 - [employees_import_import_import_import_import][Delete Territories] - Node Delete Territories[5482f5b9-6483-4d0a-af5e-e6edfe74c3ec] preload schema finished, cost 216 ms 
[INFO ] 2024-03-29 18:30:09.346 - [employees_import_import_import_import_import][Region] - Node Region[138b73b6-aee6-41fb-8723-7feaa3e53425] preload schema finished, cost 216 ms 
[INFO ] 2024-03-29 18:30:09.346 - [employees_import_import_import_import_import][employees] - Node employees[f3fb546e-cb2e-439f-852f-7ce73037f915] preload schema finished, cost 278 ms 
[INFO ] 2024-03-29 18:30:09.571 - [employees_import_import_import_import_import][merge] - Node merge[257f8a42-9da8-41da-b801-2a53c4d9b084] preload schema finished, cost 517 ms 
[INFO ] 2024-03-29 18:30:09.572 - [employees_import_import_import_import_import][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 18:30:09.573 - [employees_import_import_import_import_import][merge] - 
Merge lookup relation{
  Territories(5482f5b9-6483-4d0a-af5e-e6edfe74c3ec)
    ->Region(5d489e45-a316-403f-8767-f8ac6926bf2c)
} 
[INFO ] 2024-03-29 18:30:09.573 - [employees_import_import_import_import_import][merge] - 
Merge lookup relation{
  EmployeeTerritories(2f23f1b0-de90-4ba5-ae2d-5a56bd1d95b2)
    ->Territories(5482f5b9-6483-4d0a-af5e-e6edfe74c3ec)
} 
[INFO ] 2024-03-29 18:30:09.573 - [employees_import_import_import_import_import][merge] - 
Merge lookup relation{
  Employees(26ee3dc6-6cd7-4e89-9d5f-4a2f476f7e8a)
    ->Employees(a38d72ef-e6d3-4a5b-9b4c-c8487a4341aa)
    ->EmployeeTerritories(2f23f1b0-de90-4ba5-ae2d-5a56bd1d95b2)
} 
[INFO ] 2024-03-29 18:30:10.582 - [employees_import_import_import_import_import][EmployeeTerritories] - Source node "EmployeeTerritories" read batch size: 500 
[INFO ] 2024-03-29 18:30:10.582 - [employees_import_import_import_import_import][EmployeeTerritories] - Source node "EmployeeTerritories" event queue capacity: 1000 
[INFO ] 2024-03-29 18:30:10.588 - [employees_import_import_import_import_import][EmployeeTerritories] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:30:10.588 - [employees_import_import_import_import_import][EmployeeTerritories] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:30:10.737 - [employees_import_import_import_import_import] - Node[EmployeeTerritories] is waiting for running 
[INFO ] 2024-03-29 18:30:10.737 - [employees_import_import_import_import_import][Employees] - Source node "Employees" read batch size: 500 
[INFO ] 2024-03-29 18:30:10.737 - [employees_import_import_import_import_import][Employees] - Source node "Employees" event queue capacity: 1000 
[INFO ] 2024-03-29 18:30:10.737 - [employees_import_import_import_import_import][Employees] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:30:10.797 - [employees_import_import_import_import_import][Employees] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:30:10.821 - [employees_import_import_import_import_import][Employees] - Initial sync started 
[INFO ] 2024-03-29 18:30:10.821 - [employees_import_import_import_import_import][Employees] - Starting batch read, table name: Employees, offset: null 
[INFO ] 2024-03-29 18:30:10.821 - [employees_import_import_import_import_import][Employees] - Table Employees is going to be initial synced 
[INFO ] 2024-03-29 18:30:10.856 - [employees_import_import_import_import_import][Employees] - Query table 'Employees' counts: 0 
[INFO ] 2024-03-29 18:30:10.856 - [employees_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 18:30:10.889 - [employees_import_import_import_import_import][Region] - Source node "Region" read batch size: 500 
[INFO ] 2024-03-29 18:30:10.889 - [employees_import_import_import_import_import][Region] - Source node "Region" event queue capacity: 1000 
[INFO ] 2024-03-29 18:30:10.889 - [employees_import_import_import_import_import][Region] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:30:10.905 - [employees_import_import_import_import_import][Region] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:30:10.906 - [employees_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Employees_a38d72ef-e6d3-4a5b-9b4c-c8487a4341aa__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:30:10.956 - [employees_import_import_import_import_import] - Node[Region] is waiting for running 
[INFO ] 2024-03-29 18:30:10.956 - [employees_import_import_import_import_import][employees] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 18:30:11.232 - [employees_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_EmployeeTerritories_2f23f1b0-de90-4ba5-ae2d-5a56bd1d95b2__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:30:11.358 - [employees_import_import_import_import_import][Employees] - Source node "Employees" read batch size: 500 
[INFO ] 2024-03-29 18:30:11.358 - [employees_import_import_import_import_import][Employees] - Source node "Employees" event queue capacity: 1000 
[INFO ] 2024-03-29 18:30:11.358 - [employees_import_import_import_import_import][Employees] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:30:11.444 - [employees_import_import_import_import_import][Employees] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:30:11.444 - [employees_import_import_import_import_import] - Node[Employees] is waiting for running 
[INFO ] 2024-03-29 18:30:11.605 - [employees_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Region_5d489e45-a316-403f-8767-f8ac6926bf2c__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:30:11.607 - [employees_import_import_import_import_import][Territories] - Source node "Territories" read batch size: 500 
[INFO ] 2024-03-29 18:30:11.607 - [employees_import_import_import_import_import][Territories] - Source node "Territories" event queue capacity: 1000 
[INFO ] 2024-03-29 18:30:11.607 - [employees_import_import_import_import_import][Territories] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:30:11.647 - [employees_import_import_import_import_import][Territories] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:30:11.647 - [employees_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Territories_5482f5b9-6483-4d0a-af5e-e6edfe74c3ec__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:30:11.695 - [employees_import_import_import_import_import] - Node[Territories] is waiting for running 
[INFO ] 2024-03-29 18:30:11.695 - [employees_import_import_import_import_import][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 18:30:11.696 - [employees_import_import_import_import_import][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 18:30:12.976 - [employees_import_import_import_import_import] - Node[Employees] finish, notify next layer to run 
[INFO ] 2024-03-29 18:30:12.976 - [employees_import_import_import_import_import] - Next layer have been notified: [null,null] 
[INFO ] 2024-03-29 18:30:12.977 - [employees_import_import_import_import_import][Employees] - Initial sync started 
[INFO ] 2024-03-29 18:30:12.977 - [employees_import_import_import_import_import][EmployeeTerritories] - Initial sync started 
[INFO ] 2024-03-29 18:30:12.977 - [employees_import_import_import_import_import][EmployeeTerritories] - Starting batch read, table name: EmployeeTerritories, offset: null 
[INFO ] 2024-03-29 18:30:12.977 - [employees_import_import_import_import_import][Employees] - Starting batch read, table name: Employees, offset: null 
[INFO ] 2024-03-29 18:30:13.027 - [employees_import_import_import_import_import][Employees] - Table Employees is going to be initial synced 
[INFO ] 2024-03-29 18:30:13.028 - [employees_import_import_import_import_import][EmployeeTerritories] - Table EmployeeTerritories is going to be initial synced 
[INFO ] 2024-03-29 18:30:13.044 - [employees_import_import_import_import_import][EmployeeTerritories] - Query table 'EmployeeTerritories' counts: 1 
[INFO ] 2024-03-29 18:30:13.045 - [employees_import_import_import_import_import][Employees] - Query table 'Employees' counts: 0 
[INFO ] 2024-03-29 18:30:13.045 - [employees_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 18:30:13.249 - [employees_import_import_import_import_import][EmployeeTerritories] - Initial sync completed 
[INFO ] 2024-03-29 18:30:14.089 - [employees_import_import_import_import_import] - Node[EmployeeTerritories] finish, notify next layer to run 
[INFO ] 2024-03-29 18:30:14.092 - [employees_import_import_import_import_import] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 18:30:14.092 - [employees_import_import_import_import_import][Territories] - Initial sync started 
[INFO ] 2024-03-29 18:30:14.092 - [employees_import_import_import_import_import][Territories] - Starting batch read, table name: Territories, offset: null 
[INFO ] 2024-03-29 18:30:14.092 - [employees_import_import_import_import_import][Territories] - Table Territories is going to be initial synced 
[INFO ] 2024-03-29 18:30:14.136 - [employees_import_import_import_import_import][Territories] - Query table 'Territories' counts: 1 
[INFO ] 2024-03-29 18:30:14.346 - [employees_import_import_import_import_import][Territories] - Initial sync completed 
[INFO ] 2024-03-29 18:30:15.181 - [employees_import_import_import_import_import] - Node[Territories] finish, notify next layer to run 
[INFO ] 2024-03-29 18:30:15.181 - [employees_import_import_import_import_import] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 18:30:15.181 - [employees_import_import_import_import_import][Region] - Initial sync started 
[INFO ] 2024-03-29 18:30:15.181 - [employees_import_import_import_import_import][Region] - Starting batch read, table name: Region, offset: null 
[INFO ] 2024-03-29 18:30:15.244 - [employees_import_import_import_import_import][Region] - Table Region is going to be initial synced 
[INFO ] 2024-03-29 18:30:15.245 - [employees_import_import_import_import_import][Region] - Query table 'Region' counts: 1 
[INFO ] 2024-03-29 18:30:15.450 - [employees_import_import_import_import_import][Region] - Initial sync completed 
[INFO ] 2024-03-29 18:30:15.883 - [employees_import_import_import_import_import][Employees] - Incremental sync starting... 
[INFO ] 2024-03-29 18:30:15.883 - [employees_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 18:30:15.884 - [employees_import_import_import_import_import][Employees] - Starting stream read, table list: [Employees], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:30:15.939 - [employees_import_import_import_import_import][Employees] - Starting mysql cdc, server name: c66109e4-21ab-4ac6-b916-189a0c4b9c5e 
[INFO ] 2024-03-29 18:30:15.939 - [employees_import_import_import_import_import][Employees] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1038180384
  time.precision.mode: adaptive_time_microseconds
  database.server.name: c66109e4-21ab-4ac6-b916-189a0c4b9c5e
  database.port: 3307
  threadName: Debezium-Mysql-Connector-c66109e4-21ab-4ac6-b916-189a0c4b9c5e
  database.hostname: 127.0.0.1
  database.password: ********
  name: c66109e4-21ab-4ac6-b916-189a0c4b9c5e
  pdk.offset.string: {"name":"c66109e4-21ab-4ac6-b916-189a0c4b9c5e","offset":{"{\"server\":\"c66109e4-21ab-4ac6-b916-189a0c4b9c5e\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Employees
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:30:16.063 - [employees_import_import_import_import_import][Employees] - Connector Mysql incremental start succeed, tables: [Employees], data change syncing 
[INFO ] 2024-03-29 18:30:16.064 - [employees_import_import_import_import_import][Employees] - Incremental sync starting... 
[INFO ] 2024-03-29 18:30:16.064 - [employees_import_import_import_import_import][EmployeeTerritories] - Incremental sync starting... 
[INFO ] 2024-03-29 18:30:16.064 - [employees_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 18:30:16.064 - [employees_import_import_import_import_import][EmployeeTerritories] - Initial sync completed 
[INFO ] 2024-03-29 18:30:16.064 - [employees_import_import_import_import_import][EmployeeTerritories] - Starting stream read, table list: [EmployeeTerritories], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:30:16.064 - [employees_import_import_import_import_import][Employees] - Starting stream read, table list: [Employees], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:30:16.128 - [employees_import_import_import_import_import][EmployeeTerritories] - Starting mysql cdc, server name: 4aaf68fb-d811-4ea1-bc06-40b91ee5996d 
[INFO ] 2024-03-29 18:30:16.130 - [employees_import_import_import_import_import][Employees] - Starting mysql cdc, server name: 01de0860-8d9d-4fe7-bc98-0ebb756ed737 
[INFO ] 2024-03-29 18:30:16.149 - [employees_import_import_import_import_import][Employees] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 406417258
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 01de0860-8d9d-4fe7-bc98-0ebb756ed737
  database.port: 3307
  threadName: Debezium-Mysql-Connector-01de0860-8d9d-4fe7-bc98-0ebb756ed737
  database.hostname: 127.0.0.1
  database.password: ********
  name: 01de0860-8d9d-4fe7-bc98-0ebb756ed737
  pdk.offset.string: {"name":"01de0860-8d9d-4fe7-bc98-0ebb756ed737","offset":{"{\"server\":\"01de0860-8d9d-4fe7-bc98-0ebb756ed737\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Employees
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:30:16.149 - [employees_import_import_import_import_import][EmployeeTerritories] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1585040816
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 4aaf68fb-d811-4ea1-bc06-40b91ee5996d
  database.port: 3307
  threadName: Debezium-Mysql-Connector-4aaf68fb-d811-4ea1-bc06-40b91ee5996d
  database.hostname: 127.0.0.1
  database.password: ********
  name: 4aaf68fb-d811-4ea1-bc06-40b91ee5996d
  pdk.offset.string: {"name":"4aaf68fb-d811-4ea1-bc06-40b91ee5996d","offset":{"{\"server\":\"4aaf68fb-d811-4ea1-bc06-40b91ee5996d\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.EmployeeTerritories
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:30:16.149 - [employees_import_import_import_import_import][Territories] - Incremental sync starting... 
[INFO ] 2024-03-29 18:30:16.149 - [employees_import_import_import_import_import][Territories] - Initial sync completed 
[INFO ] 2024-03-29 18:30:16.151 - [employees_import_import_import_import_import][Territories] - Starting stream read, table list: [Territories], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:30:16.217 - [employees_import_import_import_import_import][Territories] - Starting mysql cdc, server name: 4f5e1567-52a9-4057-af51-56db2ab07117 
[INFO ] 2024-03-29 18:30:16.218 - [employees_import_import_import_import_import][Territories] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 195409518
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 4f5e1567-52a9-4057-af51-56db2ab07117
  database.port: 3307
  threadName: Debezium-Mysql-Connector-4f5e1567-52a9-4057-af51-56db2ab07117
  database.hostname: 127.0.0.1
  database.password: ********
  name: 4f5e1567-52a9-4057-af51-56db2ab07117
  pdk.offset.string: {"name":"4f5e1567-52a9-4057-af51-56db2ab07117","offset":{"{\"server\":\"4f5e1567-52a9-4057-af51-56db2ab07117\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Territories
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:30:16.261 - [employees_import_import_import_import_import][EmployeeTerritories] - Connector Mysql incremental start succeed, tables: [EmployeeTerritories], data change syncing 
[INFO ] 2024-03-29 18:30:16.261 - [employees_import_import_import_import_import][Employees] - Connector Mysql incremental start succeed, tables: [Employees], data change syncing 
[INFO ] 2024-03-29 18:30:16.261 - [employees_import_import_import_import_import][Region] - Incremental sync starting... 
[INFO ] 2024-03-29 18:30:16.262 - [employees_import_import_import_import_import][Region] - Initial sync completed 
[INFO ] 2024-03-29 18:30:16.263 - [employees_import_import_import_import_import][Region] - Starting stream read, table list: [Region], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:30:16.363 - [employees_import_import_import_import_import][Territories] - Connector Mysql incremental start succeed, tables: [Territories], data change syncing 
[INFO ] 2024-03-29 18:30:16.364 - [employees_import_import_import_import_import][Region] - Starting mysql cdc, server name: aaa75fac-08cf-4a13-9788-cb9b0881f67e 
[INFO ] 2024-03-29 18:30:16.423 - [employees_import_import_import_import_import][Region] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1720120135
  time.precision.mode: adaptive_time_microseconds
  database.server.name: aaa75fac-08cf-4a13-9788-cb9b0881f67e
  database.port: 3307
  threadName: Debezium-Mysql-Connector-aaa75fac-08cf-4a13-9788-cb9b0881f67e
  database.hostname: 127.0.0.1
  database.password: ********
  name: aaa75fac-08cf-4a13-9788-cb9b0881f67e
  pdk.offset.string: {"name":"aaa75fac-08cf-4a13-9788-cb9b0881f67e","offset":{"{\"server\":\"aaa75fac-08cf-4a13-9788-cb9b0881f67e\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Region
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:30:16.423 - [employees_import_import_import_import_import][Region] - Connector Mysql incremental start succeed, tables: [Region], data change syncing 
[INFO ] 2024-03-29 18:36:29.690 - [employees_import_import_import_import_import] - Stop task milestones: 660697bd57c8b774d7a94234(employees_import_import_import_import_import)  
[INFO ] 2024-03-29 18:36:29.691 - [employees_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[216011de-413e-48c7-b956-d126eb0626e9] running status set to false 
[INFO ] 2024-03-29 18:36:29.691 - [employees_import_import_import_import_import][EmployeeTerritories] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:36:29.691 - [employees_import_import_import_import_import][EmployeeTerritories] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:36:29.691 - [employees_import_import_import_import_import][EmployeeTerritories] - PDK connector node stopped: HazelcastSourcePdkDataNode-216011de-413e-48c7-b956-d126eb0626e9 
[INFO ] 2024-03-29 18:36:29.691 - [employees_import_import_import_import_import][EmployeeTerritories] - PDK connector node released: HazelcastSourcePdkDataNode-216011de-413e-48c7-b956-d126eb0626e9 
[INFO ] 2024-03-29 18:36:29.692 - [employees_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[216011de-413e-48c7-b956-d126eb0626e9] schema data cleaned 
[INFO ] 2024-03-29 18:36:29.692 - [employees_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[216011de-413e-48c7-b956-d126eb0626e9] monitor closed 
[INFO ] 2024-03-29 18:36:29.692 - [employees_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[216011de-413e-48c7-b956-d126eb0626e9] close complete, cost 52 ms 
[INFO ] 2024-03-29 18:36:29.693 - [employees_import_import_import_import_import][Employees] - Node Employees[5f989a90-48ad-47a7-9ad9-e6264997b95d] running status set to false 
[INFO ] 2024-03-29 18:36:29.774 - [employees_import_import_import_import_import][Employees] - PDK connector node stopped: HazelcastSourcePdkDataNode-5f989a90-48ad-47a7-9ad9-e6264997b95d 
[INFO ] 2024-03-29 18:36:29.774 - [employees_import_import_import_import_import][Employees] - PDK connector node released: HazelcastSourcePdkDataNode-5f989a90-48ad-47a7-9ad9-e6264997b95d 
[INFO ] 2024-03-29 18:36:29.774 - [employees_import_import_import_import_import][Employees] - Node Employees[5f989a90-48ad-47a7-9ad9-e6264997b95d] schema data cleaned 
[INFO ] 2024-03-29 18:36:29.774 - [employees_import_import_import_import_import][Employees] - Node Employees[5f989a90-48ad-47a7-9ad9-e6264997b95d] monitor closed 
[INFO ] 2024-03-29 18:36:29.776 - [employees_import_import_import_import_import][Employees] - Node Employees[5f989a90-48ad-47a7-9ad9-e6264997b95d] close complete, cost 82 ms 
[INFO ] 2024-03-29 18:36:29.778 - [employees_import_import_import_import_import][Rename Employees] - Node Rename Employees[a38d72ef-e6d3-4a5b-9b4c-c8487a4341aa] running status set to false 
[INFO ] 2024-03-29 18:36:29.915 - [employees_import_import_import_import_import][Rename Employees] - Node Rename Employees[a38d72ef-e6d3-4a5b-9b4c-c8487a4341aa] schema data cleaned 
[INFO ] 2024-03-29 18:36:29.916 - [employees_import_import_import_import_import][Rename Employees] - Node Rename Employees[a38d72ef-e6d3-4a5b-9b4c-c8487a4341aa] monitor closed 
[INFO ] 2024-03-29 18:36:29.916 - [employees_import_import_import_import_import][Rename Employees] - Node Rename Employees[a38d72ef-e6d3-4a5b-9b4c-c8487a4341aa] close complete, cost 138 ms 
[INFO ] 2024-03-29 18:36:29.917 - [employees_import_import_import_import_import][Territories] - Node Territories[ff8a6d60-9f4f-448e-8a13-a682a80f045b] running status set to false 
[INFO ] 2024-03-29 18:36:29.930 - [employees_import_import_import_import_import][Territories] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:36:29.930 - [employees_import_import_import_import_import][Territories] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:36:29.936 - [employees_import_import_import_import_import][Territories] - PDK connector node stopped: HazelcastSourcePdkDataNode-ff8a6d60-9f4f-448e-8a13-a682a80f045b 
[INFO ] 2024-03-29 18:36:29.936 - [employees_import_import_import_import_import][Territories] - PDK connector node released: HazelcastSourcePdkDataNode-ff8a6d60-9f4f-448e-8a13-a682a80f045b 
[INFO ] 2024-03-29 18:36:29.937 - [employees_import_import_import_import_import][Territories] - Node Territories[ff8a6d60-9f4f-448e-8a13-a682a80f045b] schema data cleaned 
[INFO ] 2024-03-29 18:36:29.937 - [employees_import_import_import_import_import][Territories] - Node Territories[ff8a6d60-9f4f-448e-8a13-a682a80f045b] monitor closed 
[INFO ] 2024-03-29 18:36:29.937 - [employees_import_import_import_import_import][Territories] - Node Territories[ff8a6d60-9f4f-448e-8a13-a682a80f045b] close complete, cost 20 ms 
[INFO ] 2024-03-29 18:36:29.937 - [employees_import_import_import_import_import][Employees] - Node Employees[d14ce5a2-df5f-4fee-9f77-81b02f91621a] running status set to false 
[INFO ] 2024-03-29 18:36:30.029 - [employees_import_import_import_import_import][Employees] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:36:30.029 - [employees_import_import_import_import_import][Employees] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:36:30.031 - [employees_import_import_import_import_import][Employees] - PDK connector node stopped: HazelcastSourcePdkDataNode-d14ce5a2-df5f-4fee-9f77-81b02f91621a 
[INFO ] 2024-03-29 18:36:30.031 - [employees_import_import_import_import_import][Employees] - PDK connector node released: HazelcastSourcePdkDataNode-d14ce5a2-df5f-4fee-9f77-81b02f91621a 
[INFO ] 2024-03-29 18:36:30.031 - [employees_import_import_import_import_import][Employees] - Node Employees[d14ce5a2-df5f-4fee-9f77-81b02f91621a] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.031 - [employees_import_import_import_import_import][Employees] - Node Employees[d14ce5a2-df5f-4fee-9f77-81b02f91621a] monitor closed 
[INFO ] 2024-03-29 18:36:30.031 - [employees_import_import_import_import_import][Employees] - Node Employees[d14ce5a2-df5f-4fee-9f77-81b02f91621a] close complete, cost 94 ms 
[INFO ] 2024-03-29 18:36:30.031 - [employees_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[ab8a5647-8136-4458-811c-a6824a4f77e2] running status set to false 
[INFO ] 2024-03-29 18:36:30.114 - [employees_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[ab8a5647-8136-4458-811c-a6824a4f77e2] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.114 - [employees_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[ab8a5647-8136-4458-811c-a6824a4f77e2] monitor closed 
[INFO ] 2024-03-29 18:36:30.114 - [employees_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[ab8a5647-8136-4458-811c-a6824a4f77e2] close complete, cost 82 ms 
[INFO ] 2024-03-29 18:36:30.115 - [employees_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[2f23f1b0-de90-4ba5-ae2d-5a56bd1d95b2] running status set to false 
[INFO ] 2024-03-29 18:36:30.230 - [employees_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[2f23f1b0-de90-4ba5-ae2d-5a56bd1d95b2] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.230 - [employees_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[2f23f1b0-de90-4ba5-ae2d-5a56bd1d95b2] monitor closed 
[INFO ] 2024-03-29 18:36:30.231 - [employees_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[2f23f1b0-de90-4ba5-ae2d-5a56bd1d95b2] close complete, cost 116 ms 
[INFO ] 2024-03-29 18:36:30.231 - [employees_import_import_import_import_import][Region] - Node Region[138b73b6-aee6-41fb-8723-7feaa3e53425] running status set to false 
[INFO ] 2024-03-29 18:36:30.318 - [employees_import_import_import_import_import][Region] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:36:30.318 - [employees_import_import_import_import_import][Region] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:36:30.320 - [employees_import_import_import_import_import][Region] - PDK connector node stopped: HazelcastSourcePdkDataNode-138b73b6-aee6-41fb-8723-7feaa3e53425 
[INFO ] 2024-03-29 18:36:30.320 - [employees_import_import_import_import_import][Region] - PDK connector node released: HazelcastSourcePdkDataNode-138b73b6-aee6-41fb-8723-7feaa3e53425 
[INFO ] 2024-03-29 18:36:30.320 - [employees_import_import_import_import_import][Region] - Node Region[138b73b6-aee6-41fb-8723-7feaa3e53425] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.320 - [employees_import_import_import_import_import][Region] - Node Region[138b73b6-aee6-41fb-8723-7feaa3e53425] monitor closed 
[INFO ] 2024-03-29 18:36:30.320 - [employees_import_import_import_import_import][Region] - Node Region[138b73b6-aee6-41fb-8723-7feaa3e53425] close complete, cost 89 ms 
[INFO ] 2024-03-29 18:36:30.363 - [employees_import_import_import_import_import][Rename Region] - Node Rename Region[9209d335-020d-404f-bcd9-0416475f4d77] running status set to false 
[INFO ] 2024-03-29 18:36:30.363 - [employees_import_import_import_import_import][Rename Region] - Node Rename Region[9209d335-020d-404f-bcd9-0416475f4d77] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.363 - [employees_import_import_import_import_import][Rename Region] - Node Rename Region[9209d335-020d-404f-bcd9-0416475f4d77] monitor closed 
[INFO ] 2024-03-29 18:36:30.363 - [employees_import_import_import_import_import][Rename Region] - Node Rename Region[9209d335-020d-404f-bcd9-0416475f4d77] close complete, cost 42 ms 
[INFO ] 2024-03-29 18:36:30.426 - [employees_import_import_import_import_import][Rename Territories] - Node Rename Territories[8ab0b655-31e9-4b27-92b8-da5ddedf86dd] running status set to false 
[INFO ] 2024-03-29 18:36:30.426 - [employees_import_import_import_import_import][Rename Territories] - Node Rename Territories[8ab0b655-31e9-4b27-92b8-da5ddedf86dd] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.426 - [employees_import_import_import_import_import][Rename Territories] - Node Rename Territories[8ab0b655-31e9-4b27-92b8-da5ddedf86dd] monitor closed 
[INFO ] 2024-03-29 18:36:30.426 - [employees_import_import_import_import_import][Rename Territories] - Node Rename Territories[8ab0b655-31e9-4b27-92b8-da5ddedf86dd] close complete, cost 62 ms 
[INFO ] 2024-03-29 18:36:30.426 - [employees_import_import_import_import_import][Delete Territories] - Node Delete Territories[5482f5b9-6483-4d0a-af5e-e6edfe74c3ec] running status set to false 
[INFO ] 2024-03-29 18:36:30.470 - [employees_import_import_import_import_import][Delete Territories] - Node Delete Territories[5482f5b9-6483-4d0a-af5e-e6edfe74c3ec] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.470 - [employees_import_import_import_import_import][Delete Territories] - Node Delete Territories[5482f5b9-6483-4d0a-af5e-e6edfe74c3ec] monitor closed 
[INFO ] 2024-03-29 18:36:30.470 - [employees_import_import_import_import_import][Delete Territories] - Node Delete Territories[5482f5b9-6483-4d0a-af5e-e6edfe74c3ec] close complete, cost 44 ms 
[INFO ] 2024-03-29 18:36:30.471 - [employees_import_import_import_import_import][Delete Region] - Node Delete Region[5d489e45-a316-403f-8767-f8ac6926bf2c] running status set to false 
[INFO ] 2024-03-29 18:36:30.513 - [employees_import_import_import_import_import][Delete Region] - Node Delete Region[5d489e45-a316-403f-8767-f8ac6926bf2c] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.513 - [employees_import_import_import_import_import][Delete Region] - Node Delete Region[5d489e45-a316-403f-8767-f8ac6926bf2c] monitor closed 
[INFO ] 2024-03-29 18:36:30.513 - [employees_import_import_import_import_import][Delete Region] - Node Delete Region[5d489e45-a316-403f-8767-f8ac6926bf2c] close complete, cost 42 ms 
[INFO ] 2024-03-29 18:36:30.513 - [employees_import_import_import_import_import][Rename Employees] - Node Rename Employees[26ee3dc6-6cd7-4e89-9d5f-4a2f476f7e8a] running status set to false 
[INFO ] 2024-03-29 18:36:30.556 - [employees_import_import_import_import_import][Rename Employees] - Node Rename Employees[26ee3dc6-6cd7-4e89-9d5f-4a2f476f7e8a] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.559 - [employees_import_import_import_import_import][Rename Employees] - Node Rename Employees[26ee3dc6-6cd7-4e89-9d5f-4a2f476f7e8a] monitor closed 
[INFO ] 2024-03-29 18:36:30.559 - [employees_import_import_import_import_import][Rename Employees] - Node Rename Employees[26ee3dc6-6cd7-4e89-9d5f-4a2f476f7e8a] close complete, cost 42 ms 
[INFO ] 2024-03-29 18:36:30.559 - [employees_import_import_import_import_import][merge] - Node merge[257f8a42-9da8-41da-b801-2a53c4d9b084] running status set to false 
[INFO ] 2024-03-29 18:36:30.559 - [employees_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Employees_a38d72ef-e6d3-4a5b-9b4c-c8487a4341aa__TPORIG 
[INFO ] 2024-03-29 18:36:30.566 - [employees_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Territories_5482f5b9-6483-4d0a-af5e-e6edfe74c3ec__TPORIG 
[INFO ] 2024-03-29 18:36:30.566 - [employees_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Region_5d489e45-a316-403f-8767-f8ac6926bf2c__TPORIG 
[INFO ] 2024-03-29 18:36:30.583 - [employees_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_EmployeeTerritories_2f23f1b0-de90-4ba5-ae2d-5a56bd1d95b2__TPORIG 
[INFO ] 2024-03-29 18:36:30.583 - [employees_import_import_import_import_import][merge] - Node merge[257f8a42-9da8-41da-b801-2a53c4d9b084] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.583 - [employees_import_import_import_import_import][merge] - Node merge[257f8a42-9da8-41da-b801-2a53c4d9b084] monitor closed 
[INFO ] 2024-03-29 18:36:30.583 - [employees_import_import_import_import_import][merge] - Node merge[257f8a42-9da8-41da-b801-2a53c4d9b084] close complete, cost 27 ms 
[INFO ] 2024-03-29 18:36:30.595 - [employees_import_import_import_import_import][employees] - Node employees[f3fb546e-cb2e-439f-852f-7ce73037f915] running status set to false 
[INFO ] 2024-03-29 18:36:30.595 - [employees_import_import_import_import_import][employees] - PDK connector node stopped: HazelcastTargetPdkDataNode-f3fb546e-cb2e-439f-852f-7ce73037f915 
[INFO ] 2024-03-29 18:36:30.595 - [employees_import_import_import_import_import][employees] - PDK connector node released: HazelcastTargetPdkDataNode-f3fb546e-cb2e-439f-852f-7ce73037f915 
[INFO ] 2024-03-29 18:36:30.595 - [employees_import_import_import_import_import][employees] - Node employees[f3fb546e-cb2e-439f-852f-7ce73037f915] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.595 - [employees_import_import_import_import_import][employees] - Node employees[f3fb546e-cb2e-439f-852f-7ce73037f915] monitor closed 
[INFO ] 2024-03-29 18:36:30.801 - [employees_import_import_import_import_import][employees] - Node employees[f3fb546e-cb2e-439f-852f-7ce73037f915] close complete, cost 11 ms 
[INFO ] 2024-03-29 18:36:33.746 - [employees_import_import_import_import_import] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 18:36:33.747 - [employees_import_import_import_import_import] - Stopped task aspect(s) 
[INFO ] 2024-03-29 18:36:33.747 - [employees_import_import_import_import_import] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 18:36:33.773 - [employees_import_import_import_import_import] - Remove memory task client succeed, task: employees_import_import_import_import_import[660697bd57c8b774d7a94234] 
[INFO ] 2024-03-29 18:36:33.775 - [employees_import_import_import_import_import] - Destroy memory task client cache succeed, task: employees_import_import_import_import_import[660697bd57c8b774d7a94234] 
