[INFO ] 2024-09-06 10:25:58.073 - [任务 11] - Task initialization... 
[INFO ] 2024-09-06 10:25:58.237 - [任务 11] - Start task milestones: 66d82995a40a37725da9f3e6(任务 11) 
[INFO ] 2024-09-06 10:26:00.177 - [任务 11] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-09-06 10:26:00.586 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-06 10:26:00.909 - [任务 11][Mysql3306] - Node Mysql3306[4ba0ee37-5454-4aad-8e9e-22ff74232e96] start preload schema,table counts: 4 
[INFO ] 2024-09-06 10:26:00.910 - [任务 11][Oracle] - Node Oracle[6f76d241-7d62-478b-8227-1774a99c1c97] start preload schema,table counts: 4 
[INFO ] 2024-09-06 10:26:00.910 - [任务 11][Mysql3306] - Node Mysql3306[4ba0ee37-5454-4aad-8e9e-22ff74232e96] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:26:00.910 - [任务 11][Oracle] - Node Oracle[6f76d241-7d62-478b-8227-1774a99c1c97] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:26:02.584 - [任务 11][Oracle] - Node(Oracle) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-06 10:26:02.591 - [任务 11][Oracle] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-06 10:26:03.441 - [任务 11][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-09-06 10:26:03.443 - [任务 11][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-09-06 10:26:03.450 - [任务 11][Mysql3306] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-06 10:26:03.527 - [任务 11][Mysql3306] - batch offset found: {},stream offset found: {"name":"d0f18020-07af-4367-acba-e2023995c126","offset":{"{\"server\":\"d0f18020-07af-4367-acba-e2023995c126\"}":"{\"file\":\"binlog.000035\",\"pos\":2054948,\"server_id\":1}"}} 
[INFO ] 2024-09-06 10:26:03.537 - [任务 11][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-06 10:26:03.697 - [任务 11][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-09-06 10:26:03.700 - [任务 11][Mysql3306] - Initial sync completed 
[INFO ] 2024-09-06 10:26:03.749 - [任务 11][Mysql3306] - Starting stream read, table list: [BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], offset: {"name":"d0f18020-07af-4367-acba-e2023995c126","offset":{"{\"server\":\"d0f18020-07af-4367-acba-e2023995c126\"}":"{\"file\":\"binlog.000035\",\"pos\":2054948,\"server_id\":1}"}} 
[INFO ] 2024-09-06 10:26:03.752 - [任务 11][Mysql3306] - Starting mysql cdc, server name: d0f18020-07af-4367-acba-e2023995c126 
[INFO ] 2024-09-06 10:26:03.964 - [任务 11][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 643350534
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d0f18020-07af-4367-acba-e2023995c126
  database.port: 3306
  threadName: Debezium-Mysql-Connector-d0f18020-07af-4367-acba-e2023995c126
  database.hostname: localhost
  database.password: ********
  name: d0f18020-07af-4367-acba-e2023995c126
  pdk.offset.string: {"name":"d0f18020-07af-4367-acba-e2023995c126","offset":{"{\"server\":\"d0f18020-07af-4367-acba-e2023995c126\"}":"{\"file\":\"binlog.000035\",\"pos\":2054948,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_ITEM,test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-06 10:26:04.957 - [任务 11][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-09-06 10:29:24.718 - [任务 11][Mysql3306] - Node Mysql3306[4ba0ee37-5454-4aad-8e9e-22ff74232e96] running status set to false 
[INFO ] 2024-09-06 10:29:24.840 - [任务 11][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-06 10:29:24.840 - [任务 11][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-09-06 10:29:24.840 - [任务 11][Mysql3306] - Incremental sync completed 
[INFO ] 2024-09-06 10:29:24.850 - [任务 11][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-4ba0ee37-5454-4aad-8e9e-22ff74232e96 
[INFO ] 2024-09-06 10:29:24.850 - [任务 11][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-4ba0ee37-5454-4aad-8e9e-22ff74232e96 
[INFO ] 2024-09-06 10:29:24.851 - [任务 11][Mysql3306] - Node Mysql3306[4ba0ee37-5454-4aad-8e9e-22ff74232e96] schema data cleaned 
[INFO ] 2024-09-06 10:29:24.854 - [任务 11][Mysql3306] - Node Mysql3306[4ba0ee37-5454-4aad-8e9e-22ff74232e96] monitor closed 
[INFO ] 2024-09-06 10:29:24.859 - [任务 11][Mysql3306] - Node Mysql3306[4ba0ee37-5454-4aad-8e9e-22ff74232e96] close complete, cost 150 ms 
[INFO ] 2024-09-06 10:29:24.859 - [任务 11][Oracle] - Node Oracle[6f76d241-7d62-478b-8227-1774a99c1c97] running status set to false 
[INFO ] 2024-09-06 10:29:24.949 - [任务 11][Oracle] - PDK connector node stopped: HazelcastTargetPdkDataNode-6f76d241-7d62-478b-8227-1774a99c1c97 
[INFO ] 2024-09-06 10:29:24.950 - [任务 11][Oracle] - PDK connector node released: HazelcastTargetPdkDataNode-6f76d241-7d62-478b-8227-1774a99c1c97 
[INFO ] 2024-09-06 10:29:24.950 - [任务 11][Oracle] - Node Oracle[6f76d241-7d62-478b-8227-1774a99c1c97] schema data cleaned 
[INFO ] 2024-09-06 10:29:24.950 - [任务 11][Oracle] - Node Oracle[6f76d241-7d62-478b-8227-1774a99c1c97] monitor closed 
[INFO ] 2024-09-06 10:29:24.952 - [任务 11][Oracle] - Node Oracle[6f76d241-7d62-478b-8227-1774a99c1c97] close complete, cost 92 ms 
[INFO ] 2024-09-06 10:29:29.276 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-06 10:29:29.393 - [任务 11] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@59d8fe7b 
[INFO ] 2024-09-06 10:29:29.393 - [任务 11] - Stop task milestones: 66d82995a40a37725da9f3e6(任务 11)  
[INFO ] 2024-09-06 10:29:29.405 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:29:29.405 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-09-06 10:29:29.444 - [任务 11] - Remove memory task client succeed, task: 任务 11[66d82995a40a37725da9f3e6] 
[INFO ] 2024-09-06 10:29:29.645 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[66d82995a40a37725da9f3e6] 
