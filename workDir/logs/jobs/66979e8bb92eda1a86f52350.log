[INFO ] 2024-07-18 10:22:05.864 - [任务 8] - Task initialization... 
[INFO ] 2024-07-18 10:22:06.092 - [任务 8] - Start task milestones: 66979e8bb92eda1a86f52350(任务 8) 
[INFO ] 2024-07-18 10:22:07.915 - [任务 8] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 10:22:08.694 - [任务 8] - The engine receives 任务 8 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 10:22:09.356 - [任务 8][testPNew] - Node testPNew[f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:22:09.359 - [任务 8][testPNew] - Node testPNew[f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:22:09.369 - [任务 8][TESTPOLICY] - Node TESTPOLICY[a5ea2031-b57c-44dd-bf0b-44cc01db4703] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:22:09.370 - [任务 8][TESTPOLICY] - Node TESTPOLICY[a5ea2031-b57c-44dd-bf0b-44cc01db4703] preload schema finished, cost 0 ms 
[WARN ] 2024-07-18 10:22:09.699 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=f4fdbc16-28c5-44ce-97a4-5435dab7ffb1}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2880 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-18 10:22:10.918 - [任务 8][testPNew] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-07-18 10:23:09.823 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ad6aaff0-9b75-495c-893f-0e9d4939b752}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2879 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:24:10.346 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=46dd2821-c899-4d14-a058-57d3fe59cb73}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2878 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:25:10.448 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=528f8665-d2d5-4dbc-b9b3-8fd62c8507a4}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2877 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:26:10.692 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=249d72fa-fc37-45ab-9234-4f1a2615023a}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2876 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:27:10.954 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=34c8b73a-d935-4c8c-b9d6-1140aaad3cff}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2875 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:28:11.265 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=eef2a7a6-4476-4ef6-ba51-7a5ca10ba594}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2874 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:29:11.386 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=4c4fc785-bdda-4391-ad2c-eec97b86d7a8}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2873 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:30:11.557 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=1d588a81-890a-47c0-8971-da44cb09c731}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2872 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:31:11.737 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=445ee29a-5925-4b01-99ab-5bd2ad9bed70}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2871 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:32:11.860 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=b8f6a519-5ccf-4a63-b252-28ced2ef0215}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2870 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:33:11.949 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=7bf2dd20-1bed-4b07-895a-ee3008f7975a}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2869 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:34:12.117 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=d509efba-646e-4ff2-b913-0a857871468e}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2868 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:35:12.258 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=2abb0ebc-a399-4ff9-8b6d-3a216f2c37dc}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2867 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:36:12.361 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=1b76075b-e47e-4f94-8bdd-6ac777801a68}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2866 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:37:12.485 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=098052ca-d656-43eb-8526-5a36598c11eb}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2865 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:38:12.737 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ab8750ab-5e6d-43b1-85cd-8bb15bb40c73}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2864 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:39:12.895 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=9c7aeae2-b981-4f47-a8f9-f28e2bbe5998}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2863 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:40:13.002 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=bc01c251-4d53-4eb6-a5b2-c7e0780b1eca}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2862 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-18 10:41:13.345 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) retry succeed 
[INFO ] 2024-07-18 10:41:14.254 - [任务 8][TESTPOLICY] - Source node "TESTPOLICY" read batch size: 100 
[INFO ] 2024-07-18 10:41:14.258 - [任务 8][TESTPOLICY] - Source node "TESTPOLICY" event queue capacity: 200 
[INFO ] 2024-07-18 10:41:14.264 - [任务 8][TESTPOLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-18 10:41:14.370 - [任务 8][TESTPOLICY] - batch offset found: {"TESTPOLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":true},"batch_read_connector_status":"RUNNING"}},stream offset found: {"sequenceMap":{"TESTPOLICY":1,"_tapdata_heartbeat_table":1924},"streamOffset":{"_data":{"value":"826697A63A000000022B022C0100296E5A100462966C4D541F4BCEA02D0D8D9A2E5DC146645F6964006466979EAF66AB5EDE8ACB92DA0004","bsonType":"STRING","double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"array":false,"null":false,"number":false}}} 
[INFO ] 2024-07-18 10:41:14.585 - [任务 8][TESTPOLICY] - Incremental sync starting... 
[INFO ] 2024-07-18 10:41:14.585 - [任务 8][TESTPOLICY] - Initial sync completed 
[INFO ] 2024-07-18 10:41:14.686 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-18 10:41:14.687 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection NewSource enable share cdc: true 
[INFO ] 2024-07-18 10:41:14.688 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 8 enable share cdc: true 
[INFO ] 2024-07-18 10:41:14.851 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自NewSource的共享挖掘任务 
[INFO ] 2024-07-18 10:41:14.897 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://root:******@192.168.1.184:57017/tapdatav310?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-18 10:41:15.024 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979ead8e4a90a908f1d240, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66979e7cb92eda1a86f52345_TESTPOLICY, version=v2, tableName=TESTPOLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1547021255, shareCdcTaskId=66979eacb92eda1a86f5239b, connectionId=66979e7cb92eda1a86f52345) 
[INFO ] 2024-07-18 10:41:15.095 - [任务 8][TESTPOLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自NewSource的共享挖掘任务_TESTPOLICY_任务 8', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_1547021255', head seq: 0, tail seq: 0 
[INFO ] 2024-07-18 10:41:15.217 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979ead8e4a90a908f1d23f, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66979e7cb92eda1a86f52345__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_447599453, shareCdcTaskId=66979eacb92eda1a86f5239b, connectionId=66979e7cb92eda1a86f52345) 
[INFO ] 2024-07-18 10:41:15.300 - [任务 8][TESTPOLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自NewSource的共享挖掘任务__tapdata_heartbeat_table_任务 8', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_447599453', head seq: 0, tail seq: 1923 
[INFO ] 2024-07-18 10:41:15.302 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-18 10:41:15.302 - [任务 8][TESTPOLICY] - Init share cdc reader completed 
[INFO ] 2024-07-18 10:41:15.302 - [任务 8][TESTPOLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-18 10:41:15.302 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-18 10:41:15.333 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Read table count: 2, partition size: 1, read thread number: 2 
[INFO ] 2024-07-18 10:41:15.336 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979ead8e4a90a908f1d240, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66979e7cb92eda1a86f52345_TESTPOLICY, version=v2, tableName=TESTPOLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1547021255, shareCdcTaskId=66979eacb92eda1a86f5239b, connectionId=66979e7cb92eda1a86f52345) 
[INFO ] 2024-07-18 10:41:15.385 - [任务 8][TESTPOLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自NewSource的共享挖掘任务_TESTPOLICY_任务 8', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_1547021255', head seq: 0, tail seq: 0 
[INFO ] 2024-07-18 10:41:15.385 - [任务 8][TESTPOLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自NewSource的共享挖掘任务_TESTPOLICY_任务 8, external storage name: ExternalStorage_SHARE_CDC_1547021255 
[INFO ] 2024-07-18 10:41:15.386 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [TESTPOLICY] 
[INFO ] 2024-07-18 10:41:15.386 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Starting read 'TESTPOLICY' log, sequence: 1 
[INFO ] 2024-07-18 10:41:15.389 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Find by TESTPOLICY filter: {sequence=1} 
[INFO ] 2024-07-18 10:41:15.420 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979ead8e4a90a908f1d23f, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66979e7cb92eda1a86f52345__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_447599453, shareCdcTaskId=66979eacb92eda1a86f5239b, connectionId=66979e7cb92eda1a86f52345) 
[INFO ] 2024-07-18 10:41:15.440 - [任务 8][TESTPOLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自NewSource的共享挖掘任务__tapdata_heartbeat_table_任务 8', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_447599453', head seq: 0, tail seq: 1923 
[INFO ] 2024-07-18 10:41:15.440 - [任务 8][TESTPOLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自NewSource的共享挖掘任务__tapdata_heartbeat_table_任务 8, external storage name: ExternalStorage_SHARE_CDC_447599453 
[INFO ] 2024-07-18 10:41:15.441 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [_tapdata_heartbeat_table] 
[INFO ] 2024-07-18 10:41:15.442 - [任务 8][TESTPOLICY] - Connector MongoDB incremental start succeed, tables: [TESTPOLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 10:41:15.443 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Starting read '_tapdata_heartbeat_table' log, sequence: 1924 
[INFO ] 2024-07-18 10:41:15.462 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Find by _tapdata_heartbeat_table filter: {sequence=1924} 
[INFO ] 2024-07-18 17:27:27.771 - [任务 8] - Start task milestones: 66979e8bb92eda1a86f52350(任务 8) 
[INFO ] 2024-07-18 17:27:27.812 - [任务 8] - Task initialization... 
[INFO ] 2024-07-18 17:27:29.991 - [任务 8] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 17:27:30.369 - [任务 8] - The engine receives 任务 8 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 17:27:30.968 - [任务 8][TESTPOLICY] - Node TESTPOLICY[a5ea2031-b57c-44dd-bf0b-44cc01db4703] start preload schema,table counts: 1 
[INFO ] 2024-07-18 17:27:30.968 - [任务 8][testPNew] - Node testPNew[f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9] start preload schema,table counts: 1 
[INFO ] 2024-07-18 17:27:30.969 - [任务 8][TESTPOLICY] - Node TESTPOLICY[a5ea2031-b57c-44dd-bf0b-44cc01db4703] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 17:27:30.970 - [任务 8][testPNew] - Node testPNew[f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9] preload schema finished, cost 0 ms 
[WARN ] 2024-07-18 17:27:31.276 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=a5c1947dfcb94892a47148470a708e4d38eb696d10f1482babb2e454b0c2da7e&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ded73065-1c40-4ddf-baae-0de52e21d0c4}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2880 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-18 17:27:32.676 - [任务 8][testPNew] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-07-18 17:28:31.478 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=a5c1947dfcb94892a47148470a708e4d38eb696d10f1482babb2e454b0c2da7e&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=0eee508b-4650-462f-bb59-485809b65616}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2879 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 17:29:31.614 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=a5c1947dfcb94892a47148470a708e4d38eb696d10f1482babb2e454b0c2da7e&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=16251ed0-d78d-4f2e-9606-f8a931734ae6}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2878 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-18 17:30:25.904 - [任务 8][TESTPOLICY] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2806/1724376542@39800263 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
 
[ERROR] 2024-07-18 17:30:25.937 - [任务 8][TESTPOLICY] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2806/1724376542@39800263 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2806/1724376542@39800263 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more


<-- Simple Stack Trace -->
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2806/1724376542@39800263 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2806/1724376542@39800263 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more

[INFO ] 2024-07-18 17:30:25.959 - [任务 8][TESTPOLICY] - Node TESTPOLICY[a5ea2031-b57c-44dd-bf0b-44cc01db4703] running status set to false 
[INFO ] 2024-07-18 17:30:25.968 - [任务 8][TESTPOLICY] - PDK connector node stopped: null 
[INFO ] 2024-07-18 17:30:25.970 - [任务 8][TESTPOLICY] - PDK connector node released: null 
[INFO ] 2024-07-18 17:30:25.982 - [任务 8][TESTPOLICY] - Node TESTPOLICY[a5ea2031-b57c-44dd-bf0b-44cc01db4703] schema data cleaned 
[INFO ] 2024-07-18 17:30:25.984 - [任务 8][TESTPOLICY] - Node TESTPOLICY[a5ea2031-b57c-44dd-bf0b-44cc01db4703] monitor closed 
[INFO ] 2024-07-18 17:30:25.985 - [任务 8][TESTPOLICY] - Node TESTPOLICY[a5ea2031-b57c-44dd-bf0b-44cc01db4703] close complete, cost 26 ms 
[INFO ] 2024-07-18 17:30:26.005 - [任务 8][testPNew] - Node testPNew[f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9] running status set to false 
[WARN ] 2024-07-18 17:30:26.006 - [任务 8][testPNew] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastTargetPdkDataNode-f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9 
[INFO ] 2024-07-18 17:30:26.006 - [任务 8][testPNew] - PDK connector node released: HazelcastTargetPdkDataNode-f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9 
[INFO ] 2024-07-18 17:30:26.032 - [任务 8][testPNew] - Node testPNew[f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9] schema data cleaned 
[INFO ] 2024-07-18 17:30:26.038 - [任务 8][testPNew] - Node testPNew[f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9] monitor closed 
[INFO ] 2024-07-18 18:04:14.242 - [任务 8] - Start task milestones: 66979e8bb92eda1a86f52350(任务 8) 
[INFO ] 2024-07-18 18:04:14.264 - [任务 8] - Task initialization... 
[INFO ] 2024-07-18 18:04:16.255 - [任务 8] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 18:04:16.531 - [任务 8] - The engine receives 任务 8 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 18:04:17.435 - [任务 8][testPNew] - Node testPNew[f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9] start preload schema,table counts: 1 
[INFO ] 2024-07-18 18:04:17.446 - [任务 8][TESTPOLICY] - Node TESTPOLICY[a5ea2031-b57c-44dd-bf0b-44cc01db4703] start preload schema,table counts: 1 
[INFO ] 2024-07-18 18:04:17.483 - [任务 8][testPNew] - Node testPNew[f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9] preload schema finished, cost 1 ms 
[INFO ] 2024-07-18 18:04:17.486 - [任务 8][TESTPOLICY] - Node TESTPOLICY[a5ea2031-b57c-44dd-bf0b-44cc01db4703] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 18:04:19.231 - [任务 8][TESTPOLICY] - Source node "TESTPOLICY" read batch size: 100 
[INFO ] 2024-07-18 18:04:19.232 - [任务 8][TESTPOLICY] - Source node "TESTPOLICY" event queue capacity: 200 
[INFO ] 2024-07-18 18:04:19.232 - [任务 8][TESTPOLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-18 18:04:19.306 - [任务 8][TESTPOLICY] - batch offset found: {"TESTPOLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":true},"batch_read_connector_status":"RUNNING"}},stream offset found: {"sequenceMap":{"TESTPOLICY":1,"_tapdata_heartbeat_table":1924},"streamOffset":{"_data":{"value":"826697A63A000000022B022C0100296E5A100462966C4D541F4BCEA02D0D8D9A2E5DC146645F6964006466979EAF66AB5EDE8ACB92DA0004","bsonType":"STRING","string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"boolean":false,"number":false,"array":false,"null":false}}} 
[INFO ] 2024-07-18 18:04:19.563 - [任务 8][TESTPOLICY] - Incremental sync starting... 
[INFO ] 2024-07-18 18:04:19.564 - [任务 8][TESTPOLICY] - Initial sync completed 
[INFO ] 2024-07-18 18:04:19.678 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-18 18:04:19.680 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection NewSource enable share cdc: true 
[INFO ] 2024-07-18 18:04:19.702 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 8 enable share cdc: true 
[INFO ] 2024-07-18 18:04:19.747 - [任务 8][testPNew] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-18 18:04:19.822 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自NewSource的共享挖掘任务 
[INFO ] 2024-07-18 18:04:19.864 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://root:******@192.168.1.184:57017/tapdatav310?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-18 18:04:20.011 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979ead8e4a90a908f1d240, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66979e7cb92eda1a86f52345_TESTPOLICY, version=v2, tableName=TESTPOLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1547021255, shareCdcTaskId=66979eacb92eda1a86f5239b, connectionId=66979e7cb92eda1a86f52345) 
[INFO ] 2024-07-18 18:04:20.132 - [任务 8][TESTPOLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自NewSource的共享挖掘任务_TESTPOLICY_任务 8', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_1547021255', head seq: 0, tail seq: 0 
[INFO ] 2024-07-18 18:04:20.133 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979ead8e4a90a908f1d23f, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66979e7cb92eda1a86f52345__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_447599453, shareCdcTaskId=66979eacb92eda1a86f5239b, connectionId=66979e7cb92eda1a86f52345) 
[INFO ] 2024-07-18 18:04:20.382 - [任务 8][TESTPOLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自NewSource的共享挖掘任务__tapdata_heartbeat_table_任务 8', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_447599453', head seq: 0, tail seq: 1923 
[INFO ] 2024-07-18 18:04:20.383 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-18 18:04:20.383 - [任务 8][TESTPOLICY] - Init share cdc reader completed 
[INFO ] 2024-07-18 18:04:20.383 - [任务 8][TESTPOLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-18 18:04:20.383 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-18 18:04:20.383 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Read table count: 2, partition size: 1, read thread number: 2 
[INFO ] 2024-07-18 18:04:20.453 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979ead8e4a90a908f1d240, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66979e7cb92eda1a86f52345_TESTPOLICY, version=v2, tableName=TESTPOLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1547021255, shareCdcTaskId=66979eacb92eda1a86f5239b, connectionId=66979e7cb92eda1a86f52345) 
[INFO ] 2024-07-18 18:04:20.724 - [任务 8][TESTPOLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自NewSource的共享挖掘任务_TESTPOLICY_任务 8', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_1547021255', head seq: 0, tail seq: 0 
[INFO ] 2024-07-18 18:04:20.741 - [任务 8][TESTPOLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自NewSource的共享挖掘任务_TESTPOLICY_任务 8, external storage name: ExternalStorage_SHARE_CDC_1547021255 
[INFO ] 2024-07-18 18:04:20.741 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [TESTPOLICY] 
[INFO ] 2024-07-18 18:04:20.742 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Starting read 'TESTPOLICY' log, sequence: 1 
[INFO ] 2024-07-18 18:04:20.749 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Find by TESTPOLICY filter: {sequence=1} 
[INFO ] 2024-07-18 18:04:20.794 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979ead8e4a90a908f1d23f, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66979e7cb92eda1a86f52345__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_447599453, shareCdcTaskId=66979eacb92eda1a86f5239b, connectionId=66979e7cb92eda1a86f52345) 
[INFO ] 2024-07-18 18:04:20.812 - [任务 8][TESTPOLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自NewSource的共享挖掘任务__tapdata_heartbeat_table_任务 8', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_447599453', head seq: 0, tail seq: 1923 
[INFO ] 2024-07-18 18:04:20.812 - [任务 8][TESTPOLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自NewSource的共享挖掘任务__tapdata_heartbeat_table_任务 8, external storage name: ExternalStorage_SHARE_CDC_447599453 
[INFO ] 2024-07-18 18:04:20.815 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [_tapdata_heartbeat_table] 
[INFO ] 2024-07-18 18:04:20.815 - [任务 8][TESTPOLICY] - Connector MongoDB incremental start succeed, tables: [TESTPOLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 18:04:20.816 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Starting read '_tapdata_heartbeat_table' log, sequence: 1924 
[INFO ] 2024-07-18 18:04:20.899 - [任务 8][TESTPOLICY] - [Share CDC Task HZ Reader] - Find by _tapdata_heartbeat_table filter: {sequence=1924} 
[INFO ] 2024-07-18 18:09:04.444 - [任务 8][TESTPOLICY] - Node TESTPOLICY[a5ea2031-b57c-44dd-bf0b-44cc01db4703] running status set to false 
[INFO ] 2024-07-18 18:48:24.390 - [任务 8] - Start task milestones: 66979e8bb92eda1a86f52350(任务 8) 
[INFO ] 2024-07-18 18:48:30.118 - [任务 8] - Task initialization... 
[INFO ] 2024-07-18 18:48:31.910 - [任务 8] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 18:48:32.305 - [任务 8] - The engine receives 任务 8 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 18:48:33.272 - [任务 8][TESTPOLICY] - Node TESTPOLICY[a5ea2031-b57c-44dd-bf0b-44cc01db4703] start preload schema,table counts: 1 
[INFO ] 2024-07-18 18:48:33.288 - [任务 8][TESTPOLICY] - Node TESTPOLICY[a5ea2031-b57c-44dd-bf0b-44cc01db4703] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 18:48:33.289 - [任务 8][testPNew] - Node testPNew[f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9] start preload schema,table counts: 1 
[INFO ] 2024-07-18 18:48:33.289 - [任务 8][testPNew] - Node testPNew[f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9] preload schema finished, cost 1 ms 
[WARN ] 2024-07-18 18:48:34.057 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=5b7a21ed8bf146e9b2dea744d6e911bb0008903d8a194450b21c1df450b8f49d&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=ccab5904-2603-429a-bb2c-43416ca08a4c}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2880 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-18 18:48:37.995 - [任务 8][testPNew] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-07-18 18:49:34.157 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=5b7a21ed8bf146e9b2dea744d6e911bb0008903d8a194450b21c1df450b8f49d&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=a56b3803-cb5d-4568-ae94-1ab841a6c68e}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2879 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 18:50:34.276 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=5b7a21ed8bf146e9b2dea744d6e911bb0008903d8a194450b21c1df450b8f49d&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=d685a9a4-e057-4fa7-8196-837fce753bb0}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2878 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 18:51:34.421 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=5b7a21ed8bf146e9b2dea744d6e911bb0008903d8a194450b21c1df450b8f49d&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=371c9501-6f97-46d7-b7ef-e0198a28ae6c}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2877 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 18:52:34.593 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=5b7a21ed8bf146e9b2dea744d6e911bb0008903d8a194450b21c1df450b8f49d&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=5c1b3240-32c7-4808-b900-c11906a5fda5}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2876 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 18:53:34.728 - [任务 8][TESTPOLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=5b7a21ed8bf146e9b2dea744d6e911bb0008903d8a194450b21c1df450b8f49d&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66979e7cb92eda1a86f52345_66979eadb92eda1a86f523a3%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=6d356d09-5a06-4fcd-abe0-84361eacba27}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2875 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-18 18:54:30.267 - [任务 8][TESTPOLICY] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2998/718494524@46977c5f failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
 
[ERROR] 2024-07-18 18:54:30.280 - [任务 8][TESTPOLICY] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2998/718494524@46977c5f failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2998/718494524@46977c5f failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more


<-- Simple Stack Trace -->
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2998/718494524@46977c5f failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2998/718494524@46977c5f failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_a5ea2031-b57c-44dd-bf0b-44cc01db4703
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more

[INFO ] 2024-07-18 18:54:30.301 - [任务 8][TESTPOLICY] - Node TESTPOLICY[a5ea2031-b57c-44dd-bf0b-44cc01db4703] running status set to false 
[INFO ] 2024-07-18 18:54:30.316 - [任务 8][TESTPOLICY] - PDK connector node stopped: null 
[INFO ] 2024-07-18 18:54:30.323 - [任务 8][TESTPOLICY] - PDK connector node released: null 
[INFO ] 2024-07-18 18:54:30.325 - [任务 8][TESTPOLICY] - Node TESTPOLICY[a5ea2031-b57c-44dd-bf0b-44cc01db4703] schema data cleaned 
[INFO ] 2024-07-18 18:54:30.325 - [任务 8][TESTPOLICY] - Node TESTPOLICY[a5ea2031-b57c-44dd-bf0b-44cc01db4703] monitor closed 
[INFO ] 2024-07-18 18:54:30.328 - [任务 8][TESTPOLICY] - Node TESTPOLICY[a5ea2031-b57c-44dd-bf0b-44cc01db4703] close complete, cost 40 ms 
[INFO ] 2024-07-18 18:54:30.330 - [任务 8][testPNew] - Node testPNew[f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9] running status set to false 
[WARN ] 2024-07-18 18:54:30.372 - [任务 8][testPNew] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastTargetPdkDataNode-f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9 
[INFO ] 2024-07-18 18:54:30.372 - [任务 8][testPNew] - PDK connector node released: HazelcastTargetPdkDataNode-f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9 
[INFO ] 2024-07-18 18:54:30.372 - [任务 8][testPNew] - Node testPNew[f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9] schema data cleaned 
[INFO ] 2024-07-18 18:54:30.373 - [任务 8][testPNew] - Node testPNew[f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9] monitor closed 
[INFO ] 2024-07-18 18:54:30.374 - [任务 8][testPNew] - Node testPNew[f90cdcb4-9cff-4e3f-ba63-7c87128ae6a9] close complete, cost 45 ms 
