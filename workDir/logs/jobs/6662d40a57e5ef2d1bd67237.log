[INFO ] 2024-06-12 10:26:37.517 - [任务 6] - Task initialization... 
[INFO ] 2024-06-12 10:26:37.548 - [任务 6] - Start task milestones: 6662d40a57e5ef2d1bd67237(任务 6) 
[INFO ] 2024-06-12 10:26:37.549 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-12 10:26:37.551 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-12 10:26:39.592 - [任务 6][TESTVARCHAR2] - Node TESTVARCHAR2[e35d6ffa-b5e0-4e6a-93a3-e99e8c9947b9] start preload schema,table counts: 1 
[INFO ] 2024-06-12 10:26:39.594 - [任务 6][test2] - Node test2[c6638c0f-a9ae-4cb4-b8fa-74566f4b2e5b] start preload schema,table counts: 1 
[INFO ] 2024-06-12 10:26:39.743 - [任务 6][test2] - Node test2[c6638c0f-a9ae-4cb4-b8fa-74566f4b2e5b] preload schema finished, cost 146 ms 
[INFO ] 2024-06-12 10:26:39.745 - [任务 6][TESTVARCHAR2] - Node TESTVARCHAR2[e35d6ffa-b5e0-4e6a-93a3-e99e8c9947b9] preload schema finished, cost 148 ms 
[INFO ] 2024-06-12 10:26:49.382 - [任务 6][test2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-12 10:26:49.711 - [任务 6][TESTVARCHAR2] - Source node "TESTVARCHAR2" read batch size: 500 
[INFO ] 2024-06-12 10:26:49.716 - [任务 6][TESTVARCHAR2] - Source node "TESTVARCHAR2" event queue capacity: 1000 
[INFO ] 2024-06-12 10:26:49.728 - [任务 6][TESTVARCHAR2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-12 10:26:50.364 - [任务 6][TESTVARCHAR2] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":56574573,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-12 10:26:50.370 - [任务 6][TESTVARCHAR2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-12 10:26:50.630 - [任务 6][TESTVARCHAR2] - Initial sync started 
[INFO ] 2024-06-12 10:26:50.646 - [任务 6][TESTVARCHAR2] - Starting batch read, table name: TESTVARCHAR2, offset: null 
[INFO ] 2024-06-12 10:26:50.704 - [任务 6][TESTVARCHAR2] - Table TESTVARCHAR2 is going to be initial synced 
[INFO ] 2024-06-12 10:26:50.704 - [任务 6][TESTVARCHAR2] - Query table 'TESTVARCHAR2' counts: 1 
[INFO ] 2024-06-12 10:26:50.716 - [任务 6][TESTVARCHAR2] - Table [TESTVARCHAR2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-12 10:26:50.717 - [任务 6][TESTVARCHAR2] - Initial sync completed 
[INFO ] 2024-06-12 10:26:50.717 - [任务 6][TESTVARCHAR2] - Incremental sync starting... 
[INFO ] 2024-06-12 10:26:50.725 - [任务 6][TESTVARCHAR2] - Initial sync completed 
[INFO ] 2024-06-12 10:26:50.726 - [任务 6][TESTVARCHAR2] - Starting stream read, table list: [TESTVARCHAR2], offset: {"sortString":null,"offsetValue":null,"lastScn":56574573,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[WARN ] 2024-06-12 10:28:17.164 - [任务 6][test2] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: test2
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-12 10:28:17.178 - [任务 6][TESTVARCHAR2] - total start mining scn: 56574573 
[INFO ] 2024-06-12 10:28:18.793 - [任务 6][TESTVARCHAR2] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-06-12 10:28:31.368 - [任务 6] - Stop task milestones: 6662d40a57e5ef2d1bd67237(任务 6)  
[INFO ] 2024-06-12 10:28:31.723 - [任务 6][TESTVARCHAR2] - Node TESTVARCHAR2[e35d6ffa-b5e0-4e6a-93a3-e99e8c9947b9] running status set to false 
[INFO ] 2024-06-12 10:28:31.746 - [任务 6][TESTVARCHAR2] - Log Miner is shutting down... 
[INFO ] 2024-06-12 10:28:31.748 - [任务 6][TESTVARCHAR2] - Log Miner has been closed! 
[ERROR] 2024-06-12 10:28:31.852 - [任务 6][TESTVARCHAR2] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-06-12 10:28:31.855 - [任务 6][TESTVARCHAR2] - PDK connector node stopped: HazelcastSourcePdkDataNode-e35d6ffa-b5e0-4e6a-93a3-e99e8c9947b9 
[INFO ] 2024-06-12 10:28:31.861 - [任务 6][TESTVARCHAR2] - PDK connector node released: HazelcastSourcePdkDataNode-e35d6ffa-b5e0-4e6a-93a3-e99e8c9947b9 
[INFO ] 2024-06-12 10:28:31.864 - [任务 6][TESTVARCHAR2] - Node TESTVARCHAR2[e35d6ffa-b5e0-4e6a-93a3-e99e8c9947b9] schema data cleaned 
[INFO ] 2024-06-12 10:28:31.864 - [任务 6][TESTVARCHAR2] - Node TESTVARCHAR2[e35d6ffa-b5e0-4e6a-93a3-e99e8c9947b9] monitor closed 
[INFO ] 2024-06-12 10:28:31.870 - [任务 6][TESTVARCHAR2] - Node TESTVARCHAR2[e35d6ffa-b5e0-4e6a-93a3-e99e8c9947b9] close complete, cost 151 ms 
[INFO ] 2024-06-12 10:28:31.870 - [任务 6][test2] - Node test2[c6638c0f-a9ae-4cb4-b8fa-74566f4b2e5b] running status set to false 
[INFO ] 2024-06-12 10:28:31.918 - [任务 6][test2] - PDK connector node stopped: HazelcastTargetPdkDataNode-c6638c0f-a9ae-4cb4-b8fa-74566f4b2e5b 
[INFO ] 2024-06-12 10:28:31.924 - [任务 6][test2] - PDK connector node released: HazelcastTargetPdkDataNode-c6638c0f-a9ae-4cb4-b8fa-74566f4b2e5b 
[INFO ] 2024-06-12 10:28:31.925 - [任务 6][test2] - Node test2[c6638c0f-a9ae-4cb4-b8fa-74566f4b2e5b] schema data cleaned 
[ERROR] 2024-06-12 10:28:31.926 - [任务 6][test2] - Execute PDK method: TARGET_WRITE_RECORD, tableName: test2 <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test2

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: Connection pool shut down
	org.apache.http.util.Asserts.check(Asserts.java:34)
	org.apache.http.impl.conn.PoolingHttpClientConnectionManager.requestConnection(PoolingHttpClientConnectionManager.java:269)
	org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:176)
	org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test2
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:834)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$55(HazelcastTargetPdkDataNode.java:780)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:774)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$24(HazelcastTargetPdkDataNode.java:483)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:483)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:557)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:513)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:472)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:485)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:531)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.connector.doris.streamload.exception.DorisRuntimeException: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Call stream load error: Connection pool shut down
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.flush(DorisStreamLoader.java:265)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.writeRecord(DorisStreamLoader.java:142)
	at io.tapdata.connector.doris.DorisConnector.writeRecord(DorisConnector.java:174)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:828)
	... 26 more
Caused by: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Call stream load error: Connection pool shut down
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.put(DorisStreamLoader.java:220)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.flush(DorisStreamLoader.java:255)
	... 29 more
Caused by: java.lang.IllegalStateException: Connection pool shut down
	at org.apache.http.util.Asserts.check(Asserts.java:34)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.requestConnection(PoolingHttpClientConnectionManager.java:269)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:176)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.put(DorisStreamLoader.java:216)
	... 30 more

[INFO ] 2024-06-12 10:28:31.929 - [任务 6][test2] - Node test2[c6638c0f-a9ae-4cb4-b8fa-74566f4b2e5b] monitor closed 
[INFO ] 2024-06-12 10:28:31.929 - [任务 6][test2] - Node test2[c6638c0f-a9ae-4cb4-b8fa-74566f4b2e5b] close complete, cost 58 ms 
[INFO ] 2024-06-12 10:28:32.037 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-12 10:28:32.037 - [任务 6] - Stopped task aspect(s) 
[INFO ] 2024-06-12 10:28:32.040 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2024-06-12 10:28:32.145 - [任务 6] - Remove memory task client succeed, task: 任务 6[6662d40a57e5ef2d1bd67237] 
[INFO ] 2024-06-12 10:28:32.145 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[6662d40a57e5ef2d1bd67237] 
