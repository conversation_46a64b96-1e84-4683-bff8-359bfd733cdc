[INFO ] 2024-07-02 12:19:34.582 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 12:19:34.796 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 12:19:34.878 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 12:19:34.879 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 12:19:34.935 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 12:19:34.935 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 12:19:34.935 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 12:19:34.935 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 12:19:35.645 - [任务 40][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 12:19:35.647 - [任务 40][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 12:19:35.956 - [任务 40][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-07-02 12:19:35.956 - [任务 40][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-07-02 12:19:35.956 - [任务 40][SouceMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-02 12:19:35.961 - [任务 40][SouceMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":4849349,"gtidSet":""} 
[INFO ] 2024-07-02 12:19:36.014 - [任务 40][SouceMysql] - Initial sync started 
[INFO ] 2024-07-02 12:19:36.015 - [任务 40][SouceMysql] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-07-02 12:19:36.033 - [任务 40][SouceMysql] - Table CLAIM is going to be initial synced 
[INFO ] 2024-07-02 12:19:36.033 - [任务 40][SouceMysql] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-02 12:19:36.036 - [任务 40][SouceMysql] - Query table 'CLAIM' counts: 0 
[INFO ] 2024-07-02 12:19:36.036 - [任务 40][SouceMysql] - Initial sync completed 
[INFO ] 2024-07-02 12:19:36.036 - [任务 40][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-07-02 12:19:36.036 - [任务 40][SouceMysql] - Initial sync completed 
[INFO ] 2024-07-02 12:19:36.056 - [任务 40][SouceMysql] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000032","position":4849349,"gtidSet":""} 
[INFO ] 2024-07-02 12:19:36.056 - [任务 40][SouceMysql] - Starting mysql cdc, server name: a5424ad0-c640-4b2d-92bf-704e56f582b2 
[INFO ] 2024-07-02 12:19:36.096 - [任务 40][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1774606598
  time.precision.mode: adaptive_time_microseconds
  database.server.name: a5424ad0-c640-4b2d-92bf-704e56f582b2
  database.port: 3306
  threadName: Debezium-Mysql-Connector-a5424ad0-c640-4b2d-92bf-704e56f582b2
  database.hostname: localhost
  database.password: ********
  name: a5424ad0-c640-4b2d-92bf-704e56f582b2
  pdk.offset.string: {"name":"a5424ad0-c640-4b2d-92bf-704e56f582b2","offset":{"{\"server\":\"a5424ad0-c640-4b2d-92bf-704e56f582b2\"}":"{\"file\":\"binlog.000032\",\"pos\":4849349,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-02 12:19:36.096 - [任务 40][SouceMysql] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-07-02 12:19:51.893 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] running status set to false 
[INFO ] 2024-07-02 12:19:52.016 - [任务 40][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-02 12:19:52.016 - [任务 40][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-07-02 12:19:52.028 - [任务 40][SouceMysql] - Incremental sync completed 
[INFO ] 2024-07-02 12:19:52.028 - [任务 40][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 12:19:52.028 - [任务 40][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 12:19:52.029 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] schema data cleaned 
[INFO ] 2024-07-02 12:19:52.030 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] monitor closed 
[INFO ] 2024-07-02 12:19:52.030 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] close complete, cost 167 ms 
[INFO ] 2024-07-02 12:19:52.050 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 12:19:52.050 - [任务 40][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 12:19:52.050 - [任务 40][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 12:19:52.050 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] schema data cleaned 
[INFO ] 2024-07-02 12:19:52.051 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] monitor closed 
[INFO ] 2024-07-02 12:19:52.054 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] close complete, cost 20 ms 
[INFO ] 2024-07-02 12:19:55.653 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 12:19:55.665 - [任务 40] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@96ec520 
[INFO ] 2024-07-02 12:19:55.666 - [任务 40] - Stop task milestones: 66837fb973828322f83f7074(任务 40)  
[INFO ] 2024-07-02 12:19:55.797 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-07-02 12:19:55.797 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 12:19:55.834 - [任务 40] - Remove memory task client succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 12:19:55.835 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 12:20:23.530 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 12:20:23.531 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 12:20:23.755 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 12:20:23.895 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 12:20:23.895 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 12:20:23.895 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 12:20:23.896 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 12:20:24.104 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 12:20:24.621 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 12:20:24.621 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 12:20:24.760 - [任务 40][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-02 12:20:24.761 - [任务 40][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719894024,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 12:20:24.810 - [任务 40][SourceMongo] - Initial sync started 
[INFO ] 2024-07-02 12:20:24.810 - [任务 40][SourceMongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-07-02 12:20:24.815 - [任务 40][SourceMongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-07-02 12:20:24.817 - [任务 40][SourceMongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-02 12:20:24.865 - [任务 40][SourceMongo] - Query table 'CLAIM' counts: 0 
[INFO ] 2024-07-02 12:20:24.875 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 12:20:24.881 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 12:20:24.881 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 12:20:24.881 - [任务 40][SourceMongo] - Starting stream read, table list: [CLAIM], offset: {"cdcOffset":1719894024,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 12:20:24.904 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-07-02 12:20:24.904 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 12:20:24.904 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 12:20:24.973 - [任务 40][SouceMysql] - Exception skipping - The current exception does not match the skip exception strategy, message: Missing privileges when write data on mysql. 
 - Executing operation: createTable
 - Table name: [CLAIM]
 - Missing privileges: [] 
[ERROR] 2024-07-02 12:20:25.040 - [任务 40][SouceMysql] - Missing privileges when write data on mysql. 
 - Executing operation: createTable
 - Table name: [CLAIM]
 - Missing privileges: [] <-- Error Message -->
Missing privileges when write data on mysql. 
 - Executing operation: createTable
 - Table name: [CLAIM]
 - Missing privileges: []

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Row size too large. The maximum row size for the used table type, not counting BLOBs, is 65535. This includes storage overhead, check the manual. You have to change some columns to TEXT or BLOBs
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: Row size too large. The maximum row size for the used table type, not counting BLOBs, is 65535. This includes storage overhead, check the manual. You have to change some columns to TEXT or BLOBs
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectWritePrivileges(MysqlExceptionCollector.java:69)
	at io.tapdata.connector.mysql.MysqlConnector.createTableV2(MysqlConnector.java:371)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$6(HazelcastTargetPdkBaseNode.java:294)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createTable$7(HazelcastTargetPdkBaseNode.java:292)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:286)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:203)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:149)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Row size too large. The maximum row size for the used table type, not counting BLOBs, is 65535. This includes storage overhead, check the manual. You have to change some columns to TEXT or BLOBs
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.connector.mysql.MysqlConnector.createTableV2(MysqlConnector.java:366)
	... 30 more

[INFO ] 2024-07-02 12:20:25.042 - [任务 40][SouceMysql] - Job suspend in error handle 
[INFO ] 2024-07-02 12:20:25.440 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 12:20:25.440 - [任务 40][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 12:20:25.440 - [任务 40][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 12:20:25.441 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] schema data cleaned 
[INFO ] 2024-07-02 12:20:25.441 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] monitor closed 
[INFO ] 2024-07-02 12:20:25.452 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] close complete, cost 41 ms 
[INFO ] 2024-07-02 12:20:25.452 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] running status set to false 
[INFO ] 2024-07-02 12:20:25.465 - [任务 40][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 12:20:25.465 - [任务 40][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 12:20:25.465 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] schema data cleaned 
[INFO ] 2024-07-02 12:20:25.466 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] monitor closed 
[INFO ] 2024-07-02 12:20:25.668 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] close complete, cost 21 ms 
[INFO ] 2024-07-02 12:20:25.871 - [任务 40] - Task [任务 40] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-02 12:20:25.879 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 12:20:25.885 - [任务 40] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@52f6798d 
[INFO ] 2024-07-02 12:20:25.885 - [任务 40] - Stop task milestones: 66837fb973828322f83f7074(任务 40)  
[INFO ] 2024-07-02 12:20:26.012 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-07-02 12:20:26.012 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 12:20:26.027 - [任务 40] - Remove memory task client succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 12:20:26.030 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 12:20:48.119 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 12:20:48.122 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 12:20:48.290 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 12:20:48.331 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 12:20:48.395 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 12:20:48.395 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 12:20:48.395 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 12:20:48.395 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 12:20:49.085 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 12:20:49.085 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 12:20:49.085 - [任务 40][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-02 12:20:49.245 - [任务 40][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719894049,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 12:20:49.293 - [任务 40][SourceMongo] - Initial sync started 
[INFO ] 2024-07-02 12:20:49.298 - [任务 40][SourceMongo] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-02 12:20:49.298 - [任务 40][SourceMongo] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-02 12:20:49.354 - [任务 40][SourceMongo] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-02 12:20:49.354 - [任务 40][SourceMongo] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-02 12:20:49.354 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 12:20:49.355 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 12:20:49.356 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 12:20:49.356 - [任务 40][SourceMongo] - Starting stream read, table list: [POLICY], offset: {"cdcOffset":1719894049,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 12:20:49.443 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 12:20:49.443 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 12:20:49.443 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 13:45:47.039 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 13:45:47.046 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 13:45:48.112 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 13:45:48.171 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 13:45:48.722 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 13:45:48.726 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 13:45:48.727 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 13:45:48.941 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 1 ms 
[INFO ] 2024-07-02 13:45:49.015 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 13:45:49.015 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 13:45:49.072 - [任务 40][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 13:45:49.074 - [任务 40][SourceMongo] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":true},"batch_read_connector_status":"RUNNING"}},stream offset found: {"_data":{"value":"8266838036000000082B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"number":false,"array":false,"null":false}} 
[INFO ] 2024-07-02 13:45:49.237 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 13:45:49.238 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 13:45:49.254 - [任务 40][SourceMongo] - Starting stream read, table list: [POLICY], offset: {"_data":{"value":"8266838036000000082B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"number":false,"array":false,"null":false}} 
[INFO ] 2024-07-02 13:45:49.415 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 13:45:49.416 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 13:45:49.422 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[WARN ] 2024-07-02 14:41:31.983 - [任务 40] - Save milestone failed: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/update": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[WARN ] 2024-07-02 14:41:33.709 - [任务 40][SouceMysql] - Save to snapshot failed, collection: Task/syncProgress/66837fb973828322f83f7074, object: {e20711d3-9b90-48f9-bd6d-04a805c3e9a2,029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0=SyncProgress{eventSerialNo=3, syncStage='CDC', batchOffset='{}', streamOffset='{"_data": "8266839FA7000000032B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004"}'}}, errors: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/syncProgress/66837fb973828322f83f7074": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[INFO ] 2024-07-02 14:41:39.218 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 14:41:39.240 - [任务 40][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 14:41:39.241 - [任务 40][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 14:41:39.243 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] schema data cleaned 
[INFO ] 2024-07-02 14:41:39.243 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] monitor closed 
[INFO ] 2024-07-02 14:41:39.244 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] close complete, cost 48 ms 
[INFO ] 2024-07-02 14:41:39.244 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] running status set to false 
[INFO ] 2024-07-02 14:41:39.286 - [任务 40][SourceMongo] - Incremental sync completed 
[WARN ] 2024-07-02 14:46:01.221 - [任务 40] - Save milestone failed: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/update": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[WARN ] 2024-07-02 14:46:03.506 - [任务 40][SouceMysql] - Save to snapshot failed, collection: Task/syncProgress/66837fb973828322f83f7074, object: {e20711d3-9b90-48f9-bd6d-04a805c3e9a2,029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0=SyncProgress{eventSerialNo=3, syncStage='CDC', batchOffset='{}', streamOffset='{"_data": "8266839FA7000000032B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004"}'}}, errors: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/syncProgress/66837fb973828322f83f7074": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[INFO ] 2024-07-02 14:46:03.543 - [任务 40][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 14:46:03.544 - [任务 40][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 14:46:03.544 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] schema data cleaned 
[INFO ] 2024-07-02 14:46:03.545 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] monitor closed 
[INFO ] 2024-07-02 14:50:22.631 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] close complete, cost 523370 ms 
[INFO ] 2024-07-02 14:50:24.175 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 14:50:24.190 - [任务 40] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5af79db1 
[INFO ] 2024-07-02 14:50:24.190 - [任务 40] - Stop task milestones: 66837fb973828322f83f7074(任务 40)  
[WARN ] 2024-07-02 14:50:30.498 - [任务 40] - Save milestone failed: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/update": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[WARN ] 2024-07-02 14:54:54.698 - [任务 40] - Save milestone failed: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/update": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[INFO ] 2024-07-02 14:58:52.449 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-07-02 14:58:52.450 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 14:58:52.579 - [任务 40] - Remove memory task client succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 14:58:52.584 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 15:01:35.574 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 15:01:35.617 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 15:01:35.940 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 15:01:36.119 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 15:01:36.139 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 15:01:36.157 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 15:01:36.176 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 15:01:36.323 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 15:01:36.459 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 15:01:36.461 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 15:01:36.461 - [任务 40][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 15:01:36.469 - [任务 40][SourceMongo] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":true},"batch_read_connector_status":"RUNNING"}},stream offset found: {"_data":{"value":"8266838036000000082B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"number":false,"array":false,"null":false}} 
[INFO ] 2024-07-02 15:01:36.545 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 15:01:36.545 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 15:01:36.545 - [任务 40][SourceMongo] - Starting stream read, table list: [POLICY], offset: {"_data":{"value":"8266838036000000082B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"number":false,"array":false,"null":false}} 
[INFO ] 2024-07-02 15:01:36.636 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 15:01:36.636 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 15:01:36.636 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-07-02 15:09:47.446 - [任务 40] - Save milestone failed: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/update": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[INFO ] 2024-07-02 15:09:47.457 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 15:09:47.478 - [任务 40][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 15:09:47.478 - [任务 40][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 15:09:47.479 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] schema data cleaned 
[INFO ] 2024-07-02 15:09:47.479 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] monitor closed 
[INFO ] 2024-07-02 15:09:47.485 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] close complete, cost 29 ms 
[INFO ] 2024-07-02 15:09:47.486 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] running status set to false 
[WARN ] 2024-07-02 15:09:51.982 - [任务 40][SouceMysql] - Save to snapshot failed, collection: Task/syncProgress/66837fb973828322f83f7074, object: {e20711d3-9b90-48f9-bd6d-04a805c3e9a2,029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0=SyncProgress{eventSerialNo=3, syncStage='CDC', batchOffset='{}', streamOffset='{"_data": "8266839FA7000000032B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004"}'}}, errors: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/syncProgress/66837fb973828322f83f7074": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[WARN ] 2024-07-02 15:14:11.716 - [任务 40][SouceMysql] - Save to snapshot failed, collection: Task/syncProgress/66837fb973828322f83f7074, object: {e20711d3-9b90-48f9-bd6d-04a805c3e9a2,029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0=SyncProgress{eventSerialNo=3, syncStage='CDC', batchOffset='{}', streamOffset='{"_data": "8266839FA7000000032B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004"}'}}, errors: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/syncProgress/66837fb973828322f83f7074": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[INFO ] 2024-07-02 15:14:11.717 - [任务 40][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 15:14:11.719 - [任务 40][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 15:14:11.720 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] schema data cleaned 
[INFO ] 2024-07-02 15:14:11.734 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] monitor closed 
[INFO ] 2024-07-02 15:14:11.743 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] close complete, cost 264235 ms 
[INFO ] 2024-07-02 15:14:13.517 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 15:14:13.518 - [任务 40] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4458324a 
[INFO ] 2024-07-02 15:14:13.644 - [任务 40] - Stop task milestones: 66837fb973828322f83f7074(任务 40)  
[INFO ] 2024-07-02 15:14:13.644 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-07-02 15:14:13.644 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 15:14:13.645 - [任务 40] - Remove memory task client succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 15:14:13.655 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 15:19:10.132 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 15:19:10.133 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 15:19:10.447 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 15:19:10.497 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 15:19:10.561 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 15:19:10.564 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 15:19:10.565 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 15:19:10.634 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 15:19:10.941 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 15:19:10.941 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 15:19:10.941 - [任务 40][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 15:19:11.036 - [任务 40][SourceMongo] - batch offset found: {},stream offset found: {"_data":{"value":"8266839FA7000000032B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"number":false,"array":false,"null":false}} 
[INFO ] 2024-07-02 15:19:11.037 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 15:19:11.037 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 15:19:11.054 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 15:19:11.054 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 15:19:11.054 - [任务 40][SourceMongo] - Starting stream read, table list: [POLICY], offset: {"_data":{"value":"8266839FA7000000032B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"number":false,"array":false,"null":false}} 
[INFO ] 2024-07-02 15:19:11.143 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 15:35:58.332 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 16:29:32.080 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 16:29:32.291 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 16:29:32.412 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 16:29:32.613 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 16:29:32.615 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 16:29:32.615 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 16:29:32.616 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 16:29:32.617 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 16:29:32.865 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 16:29:32.865 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 16:29:32.890 - [任务 40][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 16:29:33.025 - [任务 40][SourceMongo] - batch offset found: {},stream offset found: {"_data":{"value":"8266839FA7000000032B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-02 16:29:33.026 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 16:29:33.026 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 16:29:33.235 - [任务 40][SourceMongo] - Starting stream read, table list: [POLICY], offset: {"_data":{"value":"8266839FA7000000032B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-02 16:29:33.266 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 16:29:33.353 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 16:29:33.354 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 16:35:24.475 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 16:35:24.481 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 16:35:24.668 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 16:35:24.668 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 16:35:24.758 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 16:35:24.759 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 16:35:24.769 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 16:35:24.774 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 16:35:25.009 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 16:35:25.010 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 16:35:25.017 - [任务 40][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 16:35:25.065 - [任务 40][SourceMongo] - batch offset found: {},stream offset found: {"_data":{"value":"8266839FA7000000032B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-02 16:35:25.065 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 16:35:25.065 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 16:35:25.114 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 16:35:25.116 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 16:35:25.117 - [任务 40][SourceMongo] - Starting stream read, table list: [POLICY], offset: {"_data":{"value":"8266839FA7000000032B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-02 16:35:25.218 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 16:42:19.485 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 16:47:18.949 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 16:47:19.152 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 16:47:19.152 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 16:47:19.204 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 16:47:19.283 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 16:47:19.283 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 16:47:19.283 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 16:47:19.284 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 16:47:19.478 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 16:47:19.478 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 16:47:19.500 - [任务 40][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 16:47:19.639 - [任务 40][SourceMongo] - batch offset found: {},stream offset found: {"_data":{"value":"826683BB89000000022B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","int32":false,"int64":false,"symbol":false,"string":true,"binary":false,"double":false,"boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-02 16:47:19.640 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 16:47:19.640 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 16:47:19.661 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 16:47:19.664 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 16:47:19.664 - [任务 40][SourceMongo] - Starting stream read, table list: [POLICY], offset: {"_data":{"value":"826683BB89000000022B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","int32":false,"int64":false,"symbol":false,"string":true,"binary":false,"double":false,"boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-02 16:47:19.869 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 17:10:50.871 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 17:10:51.085 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 17:10:51.175 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:10:51.176 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 17:10:51.253 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 17:10:51.254 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 17:10:51.259 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 17:10:51.260 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 17:10:51.397 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 17:10:51.397 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 17:10:51.412 - [任务 40][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 17:10:51.469 - [任务 40][SourceMongo] - batch offset found: {},stream offset found: {"_data":{"value":"826683BEC8000000022B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"number":false,"array":false,"null":false}} 
[INFO ] 2024-07-02 17:10:51.469 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 17:10:51.472 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 17:10:51.472 - [任务 40][SourceMongo] - Starting stream read, table list: [POLICY], offset: {"_data":{"value":"826683BEC8000000022B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"number":false,"array":false,"null":false}} 
[INFO ] 2024-07-02 17:10:51.537 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 17:10:51.537 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 17:10:51.538 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 17:36:39.570 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 17:36:39.572 - [任务 40][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 17:36:39.572 - [任务 40][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 17:36:39.572 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] schema data cleaned 
[INFO ] 2024-07-02 17:36:39.572 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] monitor closed 
[INFO ] 2024-07-02 17:36:39.575 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] close complete, cost 37 ms 
[INFO ] 2024-07-02 17:36:39.576 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] running status set to false 
[INFO ] 2024-07-02 17:36:39.605 - [任务 40][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 17:36:39.606 - [任务 40][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 17:36:39.606 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] schema data cleaned 
[INFO ] 2024-07-02 17:36:39.606 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] monitor closed 
[INFO ] 2024-07-02 17:36:39.812 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] close complete, cost 32 ms 
[INFO ] 2024-07-02 17:36:39.812 - [任务 40][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-02 17:36:43.729 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:36:43.731 - [任务 40] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@cc89327 
[INFO ] 2024-07-02 17:36:43.732 - [任务 40] - Stop task milestones: 66837fb973828322f83f7074(任务 40)  
[INFO ] 2024-07-02 17:36:43.857 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:36:43.858 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 17:36:43.884 - [任务 40] - Remove memory task client succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 17:36:43.886 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 18:19:32.183 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 18:19:32.187 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 18:19:32.333 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 18:19:32.365 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 18:19:32.409 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 18:19:32.410 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 18:19:32.410 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 18:19:32.410 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 18:19:33.475 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 18:19:33.475 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 18:19:33.475 - [任务 40][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-02 18:19:33.698 - [任务 40][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719915573,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 18:19:33.698 - [任务 40][SourceMongo] - Initial sync started 
[INFO ] 2024-07-02 18:19:33.698 - [任务 40][SourceMongo] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-02 18:19:33.698 - [任务 40][SourceMongo] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-02 18:19:33.740 - [任务 40][SourceMongo] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-02 18:19:33.740 - [任务 40][SourceMongo] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-02 18:19:33.740 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 18:19:33.741 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 18:19:33.741 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 18:19:33.741 - [任务 40][SourceMongo] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1719915573,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 18:19:33.858 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-02 18:19:33.858 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 18:19:33.858 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 18:20:24.228 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 18:20:24.228 - [任务 40][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 18:20:24.228 - [任务 40][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 18:20:24.228 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] schema data cleaned 
[INFO ] 2024-07-02 18:20:24.228 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] monitor closed 
[INFO ] 2024-07-02 18:20:24.231 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] close complete, cost 72 ms 
[INFO ] 2024-07-02 18:20:24.231 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] running status set to false 
[INFO ] 2024-07-02 18:20:24.250 - [任务 40][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 18:20:24.251 - [任务 40][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 18:20:24.251 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] schema data cleaned 
[INFO ] 2024-07-02 18:20:24.252 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] monitor closed 
[INFO ] 2024-07-02 18:20:24.462 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] close complete, cost 21 ms 
[INFO ] 2024-07-02 18:20:25.274 - [任务 40][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-02 18:20:28.360 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 18:20:28.364 - [任务 40] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@65cb9d29 
[INFO ] 2024-07-02 18:20:28.365 - [任务 40] - Stop task milestones: 66837fb973828322f83f7074(任务 40)  
[INFO ] 2024-07-02 18:20:28.493 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-07-02 18:20:28.494 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 18:20:28.524 - [任务 40] - Remove memory task client succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 18:20:28.526 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 18:20:48.004 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 18:20:48.007 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 18:20:48.195 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 18:20:48.196 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 18:20:48.235 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 18:20:48.236 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 18:20:48.236 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 18:20:48.236 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 18:20:48.979 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 18:20:48.979 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 18:20:48.979 - [任务 40][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-02 18:20:48.986 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 18:20:48.986 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 18:20:49.188 - [任务 40][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719915649,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 18:20:49.196 - [任务 40][SourceMongo] - Initial sync started 
[INFO ] 2024-07-02 18:20:49.196 - [任务 40][SourceMongo] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-02 18:20:49.231 - [任务 40][SourceMongo] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-02 18:20:49.231 - [任务 40][SourceMongo] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-02 18:20:49.276 - [任务 40][SourceMongo] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-02 18:20:49.277 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 18:20:49.277 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 18:20:49.277 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 18:20:49.369 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-02 18:20:49.369 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceMongo enable share cdc: true 
[INFO ] 2024-07-02 18:20:49.370 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 40 enable share cdc: true 
[INFO ] 2024-07-02 18:20:49.398 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceMongo的共享挖掘任务 
[INFO ] 2024-07-02 18:20:49.398 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-02 18:20:49.414 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 18:20:49.414 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 4 
[INFO ] 2024-07-02 18:20:49.427 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-02 18:20:49.427 - [任务 40][SourceMongo] - Init share cdc reader completed 
[INFO ] 2024-07-02 18:20:49.427 - [任务 40][SourceMongo] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-02 18:20:49.427 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-02 18:20:49.427 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-02 18:20:49.436 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 18:20:49.437 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 4 
[INFO ] 2024-07-02 18:20:49.437 - [任务 40][SourceMongo] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40, external storage name: ExternalStorage_SHARE_CDC_553058525 
[INFO ] 2024-07-02 18:20:49.438 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-02 18:20:49.450 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-02T10:20:48.979Z): 5 
[INFO ] 2024-07-02 18:20:49.451 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 18:20:49.458 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 5 
[INFO ] 2024-07-02 18:20:49.458 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=5} 
[INFO ] 2024-07-02 18:21:40.324 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 18:21:40.342 - [任务 40][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-02 18:21:40.342 - [任务 40][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 18:21:40.343 - [任务 40][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 18:21:40.343 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] schema data cleaned 
[INFO ] 2024-07-02 18:21:40.343 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] monitor closed 
[INFO ] 2024-07-02 18:21:40.348 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] close complete, cost 29 ms 
[INFO ] 2024-07-02 18:21:40.348 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] running status set to false 
[INFO ] 2024-07-02 18:21:40.373 - [任务 40][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 18:21:40.373 - [任务 40][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 18:21:40.374 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] schema data cleaned 
[INFO ] 2024-07-02 18:21:40.374 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] monitor closed 
[INFO ] 2024-07-02 18:21:40.379 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] close complete, cost 29 ms 
[INFO ] 2024-07-02 18:21:43.576 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 18:21:43.577 - [任务 40] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@17432fbb 
[INFO ] 2024-07-02 18:21:43.578 - [任务 40] - Stop task milestones: 66837fb973828322f83f7074(任务 40)  
[INFO ] 2024-07-02 18:21:43.719 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-07-02 18:21:43.720 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 18:21:43.748 - [任务 40] - Remove memory task client succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 18:21:43.750 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 18:22:13.164 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 18:22:13.164 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 18:22:13.292 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 18:22:13.292 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 18:22:13.329 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 18:22:13.329 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 18:22:13.329 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 18:22:13.329 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 18:22:14.320 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 18:22:14.320 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 18:22:14.320 - [任务 40][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-02 18:22:14.490 - [任务 40][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719915734,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 18:22:14.559 - [任务 40][SourceMongo] - Initial sync started 
[INFO ] 2024-07-02 18:22:14.559 - [任务 40][SourceMongo] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-02 18:22:14.588 - [任务 40][SourceMongo] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-02 18:22:14.589 - [任务 40][SourceMongo] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-02 18:22:14.630 - [任务 40][SourceMongo] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-02 18:22:14.630 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 18:22:14.630 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 18:22:14.630 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 18:22:14.695 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-02 18:22:14.695 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceMongo enable share cdc: true 
[INFO ] 2024-07-02 18:22:14.696 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 40 enable share cdc: true 
[INFO ] 2024-07-02 18:22:14.727 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceMongo的共享挖掘任务 
[INFO ] 2024-07-02 18:22:14.727 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-02 18:22:14.735 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 18:22:14.751 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 4 
[INFO ] 2024-07-02 18:22:14.764 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-02 18:22:14.764 - [任务 40][SourceMongo] - Init share cdc reader completed 
[INFO ] 2024-07-02 18:22:14.764 - [任务 40][SourceMongo] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-02 18:22:14.764 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-02 18:22:14.765 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-02 18:22:14.774 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 18:22:14.774 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 4 
[INFO ] 2024-07-02 18:22:14.783 - [任务 40][SourceMongo] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40, external storage name: ExternalStorage_SHARE_CDC_553058525 
[INFO ] 2024-07-02 18:22:14.783 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-02 18:22:14.786 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-02T10:22:14.320Z): 5 
[INFO ] 2024-07-02 18:22:14.787 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 18:22:14.787 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 5 
[INFO ] 2024-07-02 18:22:14.787 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=5} 
[INFO ] 2024-07-02 18:22:14.798 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 18:22:14.799 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 18:30:17.331 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=POLICY, timestamp=1719916211000, date=Tue Jul 02 18:30:11 CST 2024, after=Document{{_id=6510f74ca270a1cf5533cef3, CUSTOMER_ID=C000070250, MAX_COVERED=100.0, LAST_ANN_PREMIUM_GROSS=126.0, POLICY_ID=PC_000000001, CAR_MODEL=FF-es, COVER_START=Thu Jul 09 09:46:43 CST 2009, LAST_CHANGE=Fri Jul 05 09:13:42 CST 2019}}, op=i, offsetString=gAFkABVvcmcuYnNvbi5Cc29uRG9jdW1lbnQBFAAFX2RhdGEBAgATb3JnLmJzb24uQnNvblN0cmlu
ZwAAAe57ImFycmF5IjpmYWxzZSwiYmluYXJ5IjpmYWxzZSwiYm9vbGVhbiI6ZmFsc2UsImJzb25U
eXBlIjoiU1RSSU5HIiwiZEJQb2ludGVyIjpmYWxzZSwiZGF0ZVRpbWUiOmZhbHNlLCJkZWNpbWFs
MTI4IjpmYWxzZSwiZG9jdW1lbnQiOmZhbHNlLCJkb3VibGUiOmZhbHNlLCJpbnQzMiI6ZmFsc2Us
ImludDY0IjpmYWxzZSwiamF2YVNjcmlwdCI6ZmFsc2UsImphdmFTY3JpcHRXaXRoU2NvcGUiOmZh
bHNlLCJudWxsIjpmYWxzZSwibnVtYmVyIjpmYWxzZSwib2JqZWN0SWQiOmZhbHNlLCJyZWd1bGFy
RXhwcmVzc2lvbiI6ZmFsc2UsInN0cmluZyI6dHJ1ZSwic3ltYm9sIjpmYWxzZSwidGltZXN0YW1w
IjpmYWxzZSwidmFsdWUiOiI4MjY2ODNENkIzMDAwMDAwMDMyQjAyMkMwMTAwMjk2RTVBMTAwNDBE
MDgzN0VGNUM5RjQwNkE5NzZBRkJCMUE4MEI4NERDNDYzQzVGNjk2NDAwM0MzNjM1MzEzMDY2Mzcz
NDYzNjEzMjM3MzA2MTMxNjM2NjM1MzUzMzMzNjM2NTY2MzMwMDAwMDQifag=
, type=DATA, connectionId=6674feb868ca1e3afc2a0d99, isReplaceEvent=false, _ts=1719916211}} 
[INFO ] 2024-07-02 18:48:12.517 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 18:48:12.522 - [任务 40][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-02 18:48:12.533 - [任务 40][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 18:48:12.533 - [任务 40][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 18:48:12.533 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] schema data cleaned 
[INFO ] 2024-07-02 18:48:12.534 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] monitor closed 
[INFO ] 2024-07-02 18:48:12.537 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] close complete, cost 37 ms 
[INFO ] 2024-07-02 18:48:12.537 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] running status set to false 
[INFO ] 2024-07-02 18:48:12.559 - [任务 40][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 18:48:12.560 - [任务 40][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 18:48:12.560 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] schema data cleaned 
[INFO ] 2024-07-02 18:48:12.560 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] monitor closed 
[INFO ] 2024-07-02 18:48:12.561 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] close complete, cost 25 ms 
[INFO ] 2024-07-02 18:48:13.408 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 18:48:13.408 - [任务 40] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@47ad9afb 
[INFO ] 2024-07-02 18:48:13.549 - [任务 40] - Stop task milestones: 66837fb973828322f83f7074(任务 40)  
[INFO ] 2024-07-02 18:48:13.549 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-07-02 18:48:13.550 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 18:48:13.590 - [任务 40] - Remove memory task client succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 18:48:13.591 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 19:25:42.752 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 19:25:42.753 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 19:25:42.920 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 19:25:42.975 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 19:25:52.868 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 19:27:20.779 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 19:27:20.783 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 19:27:20.785 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 19:27:21.013 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 19:27:21.015 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 19:27:21.015 - [任务 40][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 19:27:21.088 - [任务 40][SourceMongo] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":true},"batch_read_connector_status":"RUNNING"}},stream offset found: {"sequenceMap":{"POLICY":700},"streamOffset":{"_data":{"value":"826683D6B3000002E92B022C0100296E5A10040D0837EF5C9F406A976AFBB1A80B84DC463C5F6964003C363531306637346361323730613163663535333364316230000004","bsonType":"STRING","boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"number":false,"array":false,"null":false}}} 
[INFO ] 2024-07-02 19:27:21.088 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 19:27:21.088 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 19:27:21.120 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 19:27:21.120 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 19:27:21.138 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-02 19:27:21.142 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceMongo enable share cdc: true 
[INFO ] 2024-07-02 19:27:21.142 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 40 enable share cdc: true 
[INFO ] 2024-07-02 19:27:21.206 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceMongo的共享挖掘任务 
[INFO ] 2024-07-02 19:27:21.206 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-02 19:27:21.226 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 19:27:21.227 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 19:27:21.227 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-02 19:27:21.227 - [任务 40][SourceMongo] - Init share cdc reader completed 
[INFO ] 2024-07-02 19:27:21.227 - [任务 40][SourceMongo] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-02 19:27:21.227 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-02 19:27:21.227 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-02 19:27:21.237 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 19:27:21.237 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 19:27:21.238 - [任务 40][SourceMongo] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40, external storage name: ExternalStorage_SHARE_CDC_553058525 
[INFO ] 2024-07-02 19:27:21.238 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-02 19:27:21.240 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 19:27:21.240 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 700 
[INFO ] 2024-07-02 19:27:21.441 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=700} 
[INFO ] 2024-07-02 19:27:46.592 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 19:27:46.604 - [任务 40][SourceMongo] - Incremental sync completed 
[WARN ] 2024-07-02 19:27:46.622 - [任务 40][SourceMongo] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 19:27:46.666 - [任务 40][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 19:27:46.666 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] schema data cleaned 
[INFO ] 2024-07-02 19:27:46.666 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] monitor closed 
[INFO ] 2024-07-02 19:27:46.668 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] close complete, cost 49 ms 
[INFO ] 2024-07-02 22:24:47.338 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 22:24:47.541 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 22:24:47.573 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 22:24:47.703 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 22:24:47.704 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 22:24:47.704 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 1 ms 
[INFO ] 2024-07-02 22:24:47.705 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 22:24:47.705 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 22:24:48.749 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 22:24:48.751 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 22:24:48.751 - [任务 40][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-02 22:24:48.911 - [任务 40][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719930288,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 22:24:48.975 - [任务 40][SourceMongo] - Initial sync started 
[INFO ] 2024-07-02 22:24:48.979 - [任务 40][SourceMongo] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-02 22:24:48.979 - [任务 40][SourceMongo] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-02 22:24:48.992 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 22:24:48.992 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 22:24:49.068 - [任务 40][SourceMongo] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-02 22:24:49.068 - [任务 40][SourceMongo] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-02 22:24:49.070 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 22:24:49.070 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 22:24:49.129 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 22:24:49.129 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-02 22:24:49.130 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceMongo enable share cdc: true 
[INFO ] 2024-07-02 22:24:49.131 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 40 enable share cdc: true 
[INFO ] 2024-07-02 22:24:49.197 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceMongo的共享挖掘任务 
[INFO ] 2024-07-02 22:24:49.197 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-02 22:24:49.237 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 22:24:49.246 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 22:24:49.287 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-02 22:24:49.287 - [任务 40][SourceMongo] - Init share cdc reader completed 
[INFO ] 2024-07-02 22:24:49.288 - [任务 40][SourceMongo] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-02 22:24:49.288 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-02 22:24:49.289 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-02 22:24:49.302 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 22:24:49.309 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 22:24:49.309 - [任务 40][SourceMongo] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40, external storage name: ExternalStorage_SHARE_CDC_553058525 
[INFO ] 2024-07-02 22:24:49.317 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-02 22:24:49.317 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-02T14:24:48.738Z): 700 
[INFO ] 2024-07-02 22:24:49.317 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 22:24:49.318 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 700 
[INFO ] 2024-07-02 22:24:49.520 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=700} 
[INFO ] 2024-07-02 22:29:23.964 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 22:29:23.993 - [任务 40][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-02 22:29:23.993 - [任务 40][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 22:29:23.993 - [任务 40][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 22:29:23.994 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] schema data cleaned 
[INFO ] 2024-07-02 22:29:23.994 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] monitor closed 
[INFO ] 2024-07-02 22:29:23.997 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] close complete, cost 40 ms 
[INFO ] 2024-07-02 22:29:23.998 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] running status set to false 
[INFO ] 2024-07-02 22:29:24.062 - [任务 40][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 22:29:24.062 - [任务 40][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 22:29:24.063 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] schema data cleaned 
[INFO ] 2024-07-02 22:29:24.064 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] monitor closed 
[INFO ] 2024-07-02 22:29:24.267 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] close complete, cost 69 ms 
[INFO ] 2024-07-02 22:29:26.737 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 22:29:26.738 - [任务 40] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@9a40d38 
[INFO ] 2024-07-02 22:29:26.883 - [任务 40] - Stop task milestones: 66837fb973828322f83f7074(任务 40)  
[INFO ] 2024-07-02 22:29:26.883 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-07-02 22:29:26.912 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 22:29:26.913 - [任务 40] - Remove memory task client succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 22:29:26.916 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 22:40:45.425 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 22:40:45.426 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 22:40:45.603 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 22:40:45.604 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 22:40:45.656 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 22:40:45.656 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 22:40:45.657 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 1 ms 
[INFO ] 2024-07-02 22:40:45.657 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 22:40:45.951 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 22:40:45.952 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 22:40:45.952 - [任务 40][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 22:40:45.992 - [任务 40][SourceMongo] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1719930288,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 22:40:45.992 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 22:40:45.992 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 22:40:46.056 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 22:40:46.056 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 22:42:21.847 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-02 22:42:21.851 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceMongo enable share cdc: true 
[INFO ] 2024-07-02 22:42:21.851 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 40 enable share cdc: true 
[INFO ] 2024-07-02 22:42:21.890 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceMongo的共享挖掘任务 
[INFO ] 2024-07-02 22:42:21.915 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-02 22:42:21.915 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 22:42:21.965 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 22:42:21.965 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-02 22:42:21.965 - [任务 40][SourceMongo] - Init share cdc reader completed 
[INFO ] 2024-07-02 22:42:21.965 - [任务 40][SourceMongo] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-02 22:42:21.965 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-02 22:42:21.984 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-02 22:42:21.985 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 22:42:21.988 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 22:42:21.988 - [任务 40][SourceMongo] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40, external storage name: ExternalStorage_SHARE_CDC_553058525 
[INFO ] 2024-07-02 22:42:21.993 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-02 22:42:21.993 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-02T14:24:48.738Z): 700 
[INFO ] 2024-07-02 22:42:21.996 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 22:42:21.996 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 700 
[INFO ] 2024-07-02 22:42:22.199 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=700} 
[INFO ] 2024-07-02 22:44:18.963 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 22:44:18.971 - [任务 40][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-02 22:44:18.977 - [任务 40][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 22:44:18.978 - [任务 40][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 22:44:18.978 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] schema data cleaned 
[INFO ] 2024-07-02 22:44:18.979 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] monitor closed 
[INFO ] 2024-07-02 22:44:18.980 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] close complete, cost 32 ms 
[INFO ] 2024-07-02 22:44:18.988 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] running status set to false 
[INFO ] 2024-07-02 22:44:18.988 - [任务 40][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 22:44:18.989 - [任务 40][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 22:44:18.992 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] schema data cleaned 
[INFO ] 2024-07-02 22:44:18.994 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] monitor closed 
[INFO ] 2024-07-02 22:44:18.995 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] close complete, cost 9 ms 
[INFO ] 2024-07-02 22:44:21.645 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 22:44:21.646 - [任务 40] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@503f5e71 
[INFO ] 2024-07-02 22:44:21.796 - [任务 40] - Stop task milestones: 66837fb973828322f83f7074(任务 40)  
[INFO ] 2024-07-02 22:44:21.797 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-07-02 22:44:21.797 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 22:44:21.825 - [任务 40] - Remove memory task client succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 22:44:21.826 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 22:48:38.493 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 22:48:38.494 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 22:48:38.677 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 22:48:38.677 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 22:48:38.741 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 22:48:38.742 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 22:48:38.742 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 22:48:38.745 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 22:48:39.028 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 22:48:39.031 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 22:48:39.035 - [任务 40][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 22:48:39.038 - [任务 40][SourceMongo] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1719930288,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 22:48:39.059 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 22:48:39.065 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 22:48:39.145 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 22:48:39.148 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 22:48:39.208 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-02 22:48:39.210 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceMongo enable share cdc: true 
[INFO ] 2024-07-02 22:48:39.210 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 40 enable share cdc: true 
[INFO ] 2024-07-02 22:48:39.234 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceMongo的共享挖掘任务 
[INFO ] 2024-07-02 22:48:39.234 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-02 22:48:39.257 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 22:48:39.258 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 22:48:39.269 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-02 22:48:39.270 - [任务 40][SourceMongo] - Init share cdc reader completed 
[INFO ] 2024-07-02 22:48:39.270 - [任务 40][SourceMongo] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-02 22:48:39.270 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-02 22:48:39.270 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-02 22:48:39.280 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 22:48:39.282 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 22:48:39.283 - [任务 40][SourceMongo] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40, external storage name: ExternalStorage_SHARE_CDC_553058525 
[INFO ] 2024-07-02 22:48:39.286 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-02 22:48:39.288 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-02T14:24:48.738Z): 700 
[INFO ] 2024-07-02 22:48:39.291 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 22:48:39.301 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 700 
[INFO ] 2024-07-02 22:48:39.306 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=700} 
[INFO ] 2024-07-02 22:54:45.469 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 22:54:45.488 - [任务 40][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-02 22:54:45.489 - [任务 40][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 22:54:45.489 - [任务 40][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 22:54:45.490 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] schema data cleaned 
[INFO ] 2024-07-02 22:54:45.490 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] monitor closed 
[INFO ] 2024-07-02 22:54:45.493 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] close complete, cost 72 ms 
[INFO ] 2024-07-02 22:54:45.513 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] running status set to false 
[INFO ] 2024-07-02 22:54:45.513 - [任务 40][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 22:54:45.515 - [任务 40][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 22:54:45.515 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] schema data cleaned 
[INFO ] 2024-07-02 22:54:45.517 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] monitor closed 
[INFO ] 2024-07-02 22:54:45.517 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] close complete, cost 23 ms 
[INFO ] 2024-07-02 22:54:47.323 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 22:54:47.324 - [任务 40] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@43c9283f 
[INFO ] 2024-07-02 22:54:47.447 - [任务 40] - Stop task milestones: 66837fb973828322f83f7074(任务 40)  
[INFO ] 2024-07-02 22:54:47.466 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-07-02 22:54:47.466 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 22:54:47.490 - [任务 40] - Remove memory task client succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 22:54:47.492 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 22:55:36.449 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 22:55:36.450 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 22:55:36.602 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 22:55:36.602 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 22:55:36.659 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 22:55:36.659 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 22:55:36.659 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 22:55:36.660 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 22:55:36.923 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 22:55:36.923 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 22:55:36.927 - [任务 40][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 22:55:36.927 - [任务 40][SourceMongo] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1719930288,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 22:55:36.997 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 22:55:36.997 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 22:55:37.006 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 22:55:37.006 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 22:55:37.065 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-02 22:55:37.067 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceMongo enable share cdc: true 
[INFO ] 2024-07-02 22:55:37.067 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 40 enable share cdc: true 
[INFO ] 2024-07-02 22:55:37.085 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceMongo的共享挖掘任务 
[INFO ] 2024-07-02 22:55:37.097 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-02 22:55:37.097 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 22:55:37.120 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 22:55:37.120 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-02 22:55:37.120 - [任务 40][SourceMongo] - Init share cdc reader completed 
[INFO ] 2024-07-02 22:55:37.120 - [任务 40][SourceMongo] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-02 22:55:37.121 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-02 22:55:37.121 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-02 22:55:37.132 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 22:55:37.132 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 22:55:37.133 - [任务 40][SourceMongo] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40, external storage name: ExternalStorage_SHARE_CDC_553058525 
[INFO ] 2024-07-02 22:55:37.133 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-02 22:55:37.140 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-02T14:24:48.738Z): 700 
[INFO ] 2024-07-02 22:55:37.140 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 22:55:37.140 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 700 
[INFO ] 2024-07-02 22:55:37.344 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=700} 
[INFO ] 2024-07-02 22:55:57.465 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 22:55:57.498 - [任务 40][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-02 22:55:57.498 - [任务 40][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 22:55:57.498 - [任务 40][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 22:55:57.500 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] schema data cleaned 
[INFO ] 2024-07-02 22:55:57.500 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] monitor closed 
[INFO ] 2024-07-02 22:55:57.502 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] close complete, cost 45 ms 
[INFO ] 2024-07-02 22:55:57.503 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] running status set to false 
[INFO ] 2024-07-02 22:55:57.525 - [任务 40][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 22:55:57.525 - [任务 40][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 22:55:57.526 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] schema data cleaned 
[INFO ] 2024-07-02 22:55:57.526 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] monitor closed 
[INFO ] 2024-07-02 22:55:57.587 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] close complete, cost 24 ms 
[INFO ] 2024-07-02 22:55:57.588 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 22:55:57.589 - [任务 40] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4a9e0d05 
[INFO ] 2024-07-02 22:55:57.733 - [任务 40] - Stop task milestones: 66837fb973828322f83f7074(任务 40)  
[INFO ] 2024-07-02 22:55:57.733 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-07-02 22:55:57.733 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 22:55:57.771 - [任务 40] - Remove memory task client succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 22:55:57.771 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 22:57:10.591 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 22:57:15.231 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 22:57:15.833 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 22:57:15.959 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 22:57:36.567 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 22:57:36.567 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 22:57:36.568 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 1 ms 
[INFO ] 2024-07-02 22:57:36.571 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 1 ms 
[INFO ] 2024-07-02 22:57:36.853 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 22:57:36.863 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 22:57:36.873 - [任务 40][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 22:57:36.873 - [任务 40][SourceMongo] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1719930288,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 22:57:36.902 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 22:57:36.903 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 22:57:37.061 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 22:57:37.064 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 22:57:37.132 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-02 22:57:37.132 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceMongo enable share cdc: true 
[INFO ] 2024-07-02 22:57:37.132 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 40 enable share cdc: true 
[INFO ] 2024-07-02 22:57:37.152 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceMongo的共享挖掘任务 
[INFO ] 2024-07-02 22:57:37.170 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-02 22:57:37.170 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 22:57:37.204 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 22:57:37.204 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-02 22:57:37.204 - [任务 40][SourceMongo] - Init share cdc reader completed 
[INFO ] 2024-07-02 22:57:37.204 - [任务 40][SourceMongo] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-02 22:57:37.205 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-02 22:57:37.205 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-02 22:57:37.214 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 22:57:37.217 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 22:57:37.217 - [任务 40][SourceMongo] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40, external storage name: ExternalStorage_SHARE_CDC_553058525 
[INFO ] 2024-07-02 22:57:37.222 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-02 22:57:37.222 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-02T14:24:48.738Z): 700 
[INFO ] 2024-07-02 22:57:37.223 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 22:57:37.223 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 700 
[INFO ] 2024-07-02 22:57:37.427 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=700} 
[INFO ] 2024-07-02 23:00:15.388 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 23:00:15.407 - [任务 40][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-02 23:00:15.407 - [任务 40][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 23:00:15.407 - [任务 40][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 23:00:15.409 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] schema data cleaned 
[INFO ] 2024-07-02 23:00:15.410 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] monitor closed 
[INFO ] 2024-07-02 23:00:15.411 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] close complete, cost 43 ms 
[INFO ] 2024-07-02 23:00:15.411 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] running status set to false 
[INFO ] 2024-07-02 23:00:15.422 - [任务 40][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 23:00:15.422 - [任务 40][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 23:00:15.423 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] schema data cleaned 
[INFO ] 2024-07-02 23:00:15.423 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] monitor closed 
[INFO ] 2024-07-02 23:00:15.628 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] close complete, cost 12 ms 
[INFO ] 2024-07-02 23:00:16.531 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 23:00:16.659 - [任务 40] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6b14d255 
[INFO ] 2024-07-02 23:00:16.659 - [任务 40] - Stop task milestones: 66837fb973828322f83f7074(任务 40)  
[INFO ] 2024-07-02 23:00:16.688 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-07-02 23:00:16.691 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 23:00:16.719 - [任务 40] - Remove memory task client succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 23:00:16.719 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 23:00:25.070 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 23:00:25.071 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 23:00:25.456 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 23:00:25.456 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 23:00:25.525 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 23:00:25.525 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 23:00:25.525 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 23:00:25.526 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 23:00:25.787 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 23:00:25.787 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 23:00:25.795 - [任务 40][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 23:00:25.795 - [任务 40][SourceMongo] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1719930288,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 23:00:25.836 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 23:00:25.836 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 23:00:25.883 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 23:00:29.169 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 23:00:29.309 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-02 23:00:29.309 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceMongo enable share cdc: true 
[INFO ] 2024-07-02 23:00:29.309 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 40 enable share cdc: true 
[INFO ] 2024-07-02 23:00:29.319 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceMongo的共享挖掘任务 
[INFO ] 2024-07-02 23:00:29.327 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-02 23:00:29.327 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 23:00:29.342 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 23:00:29.343 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-02 23:00:29.343 - [任务 40][SourceMongo] - Init share cdc reader completed 
[INFO ] 2024-07-02 23:00:29.343 - [任务 40][SourceMongo] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-02 23:00:29.343 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-02 23:00:29.343 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-02 23:00:29.351 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 23:00:29.351 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 23:00:29.351 - [任务 40][SourceMongo] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40, external storage name: ExternalStorage_SHARE_CDC_553058525 
[INFO ] 2024-07-02 23:00:29.351 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-02 23:00:29.356 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-02T14:24:48.738Z): 700 
[INFO ] 2024-07-02 23:00:29.356 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 23:00:29.357 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 700 
[INFO ] 2024-07-02 23:00:29.357 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=700} 
[INFO ] 2024-07-02 23:00:51.677 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 23:00:51.692 - [任务 40][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-02 23:00:51.692 - [任务 40][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 23:00:51.693 - [任务 40][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 23:00:51.693 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] schema data cleaned 
[INFO ] 2024-07-02 23:00:51.694 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] monitor closed 
[INFO ] 2024-07-02 23:00:51.695 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] close complete, cost 18 ms 
[INFO ] 2024-07-02 23:00:51.699 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] running status set to false 
[INFO ] 2024-07-02 23:00:51.721 - [任务 40][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 23:00:51.721 - [任务 40][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 23:00:51.722 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] schema data cleaned 
[INFO ] 2024-07-02 23:00:51.725 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] monitor closed 
[INFO ] 2024-07-02 23:00:51.725 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] close complete, cost 29 ms 
[INFO ] 2024-07-02 23:00:54.150 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 23:00:54.151 - [任务 40] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@52d3d5bc 
[INFO ] 2024-07-02 23:00:54.291 - [任务 40] - Stop task milestones: 66837fb973828322f83f7074(任务 40)  
[INFO ] 2024-07-02 23:00:54.292 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-07-02 23:00:54.292 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 23:00:54.313 - [任务 40] - Remove memory task client succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 23:00:54.315 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 23:01:04.193 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 23:01:07.788 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 23:01:26.295 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 23:01:26.295 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 23:01:26.361 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 23:01:26.361 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 23:01:26.362 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 2 ms 
[INFO ] 2024-07-02 23:01:26.363 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 2 ms 
[INFO ] 2024-07-02 23:01:26.638 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 23:01:26.640 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 23:01:26.640 - [任务 40][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 23:01:26.676 - [任务 40][SourceMongo] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1719930288,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 23:01:26.676 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 23:01:26.676 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 23:01:26.696 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 23:01:28.644 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 23:01:28.745 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-02 23:01:28.745 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceMongo enable share cdc: true 
[INFO ] 2024-07-02 23:01:28.778 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 40 enable share cdc: true 
[INFO ] 2024-07-02 23:01:28.782 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceMongo的共享挖掘任务 
[INFO ] 2024-07-02 23:01:28.801 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-02 23:01:28.804 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 23:01:28.823 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 23:01:28.823 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-02 23:01:28.823 - [任务 40][SourceMongo] - Init share cdc reader completed 
[INFO ] 2024-07-02 23:01:28.823 - [任务 40][SourceMongo] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-02 23:01:28.823 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-02 23:01:28.839 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-02 23:01:28.840 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 23:01:28.846 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 23:01:28.846 - [任务 40][SourceMongo] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40, external storage name: ExternalStorage_SHARE_CDC_553058525 
[INFO ] 2024-07-02 23:01:28.847 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-02 23:01:28.854 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-02T14:24:48.738Z): 700 
[INFO ] 2024-07-02 23:01:28.854 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 23:01:28.855 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 700 
[INFO ] 2024-07-02 23:01:28.856 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=700} 
[INFO ] 2024-07-02 23:08:13.290 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 23:08:13.313 - [任务 40][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-02 23:08:13.313 - [任务 40][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 23:08:13.313 - [任务 40][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 23:08:13.314 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] schema data cleaned 
[INFO ] 2024-07-02 23:08:13.315 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] monitor closed 
[INFO ] 2024-07-02 23:08:13.321 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] close complete, cost 47 ms 
[INFO ] 2024-07-02 23:08:13.322 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] running status set to false 
[INFO ] 2024-07-02 23:08:13.333 - [任务 40][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 23:08:13.333 - [任务 40][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 23:08:13.333 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] schema data cleaned 
[INFO ] 2024-07-02 23:08:13.333 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] monitor closed 
[INFO ] 2024-07-02 23:08:13.334 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] close complete, cost 18 ms 
[INFO ] 2024-07-02 23:08:17.957 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 23:08:18.084 - [任务 40] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1139d4fe 
[INFO ] 2024-07-02 23:08:18.084 - [任务 40] - Stop task milestones: 66837fb973828322f83f7074(任务 40)  
[INFO ] 2024-07-02 23:08:18.092 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-07-02 23:08:18.092 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 23:08:18.113 - [任务 40] - Remove memory task client succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 23:08:18.117 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 23:08:24.587 - [任务 40] - Task initialization... 
[INFO ] 2024-07-02 23:08:26.307 - [任务 40] - Start task milestones: 66837fb973828322f83f7074(任务 40) 
[INFO ] 2024-07-02 23:08:26.519 - [任务 40] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 23:08:26.589 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 23:08:26.590 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] start preload schema,table counts: 1 
[INFO ] 2024-07-02 23:11:20.292 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] start preload schema,table counts: 1 
[INFO ] 2024-07-02 23:11:20.312 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 23:11:20.312 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 23:11:20.830 - [任务 40][SourceMongo] - Source node "SourceMongo" read batch size: 500 
[INFO ] 2024-07-02 23:11:20.830 - [任务 40][SourceMongo] - Source node "SourceMongo" event queue capacity: 1000 
[INFO ] 2024-07-02 23:11:20.831 - [任务 40][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 23:11:20.875 - [任务 40][SourceMongo] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1719930288,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 23:11:20.875 - [任务 40][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 23:11:20.875 - [任务 40][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 23:11:20.894 - [任务 40][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 23:11:20.894 - [任务 40][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 23:11:20.932 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-02 23:11:20.936 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceMongo enable share cdc: true 
[INFO ] 2024-07-02 23:11:20.936 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 40 enable share cdc: true 
[INFO ] 2024-07-02 23:11:20.957 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceMongo的共享挖掘任务 
[INFO ] 2024-07-02 23:11:20.957 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-02 23:11:20.965 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 23:11:20.965 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 23:11:20.969 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-02 23:11:20.973 - [任务 40][SourceMongo] - Init share cdc reader completed 
[INFO ] 2024-07-02 23:11:20.973 - [任务 40][SourceMongo] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-02 23:11:20.973 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-02 23:11:20.973 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-02 23:11:20.973 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 23:11:20.977 - [任务 40][SourceMongo] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-02 23:11:20.977 - [任务 40][SourceMongo] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 40, external storage name: ExternalStorage_SHARE_CDC_553058525 
[INFO ] 2024-07-02 23:11:20.985 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-02 23:11:20.985 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-02T14:24:48.738Z): 700 
[INFO ] 2024-07-02 23:11:20.987 - [任务 40][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 23:11:20.987 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 700 
[INFO ] 2024-07-02 23:11:21.193 - [任务 40][SourceMongo] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=700} 
[INFO ] 2024-07-02 23:19:13.707 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] running status set to false 
[INFO ] 2024-07-02 23:19:13.751 - [任务 40][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-02 23:19:13.751 - [任务 40][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 23:19:13.751 - [任务 40][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e20711d3-9b90-48f9-bd6d-04a805c3e9a2 
[INFO ] 2024-07-02 23:19:13.751 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] schema data cleaned 
[INFO ] 2024-07-02 23:19:13.751 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] monitor closed 
[INFO ] 2024-07-02 23:19:13.754 - [任务 40][SourceMongo] - Node SourceMongo[e20711d3-9b90-48f9-bd6d-04a805c3e9a2] close complete, cost 67 ms 
[INFO ] 2024-07-02 23:19:13.754 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] running status set to false 
[INFO ] 2024-07-02 23:19:13.763 - [任务 40][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 23:19:13.774 - [任务 40][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0 
[INFO ] 2024-07-02 23:19:13.774 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] schema data cleaned 
[INFO ] 2024-07-02 23:19:13.774 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] monitor closed 
[INFO ] 2024-07-02 23:19:13.774 - [任务 40][SouceMysql] - Node SouceMysql[029d0ed3-0ac8-4ba0-9c08-e5dc01f5d2e0] close complete, cost 10 ms 
[INFO ] 2024-07-02 23:19:17.160 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 23:19:17.160 - [任务 40] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@34167a99 
[INFO ] 2024-07-02 23:19:17.280 - [任务 40] - Stop task milestones: 66837fb973828322f83f7074(任务 40)  
[INFO ] 2024-07-02 23:19:17.300 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-07-02 23:19:17.300 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 23:19:17.326 - [任务 40] - Remove memory task client succeed, task: 任务 40[66837fb973828322f83f7074] 
[INFO ] 2024-07-02 23:19:17.328 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[66837fb973828322f83f7074] 
