[TRACE] 2025-02-11 00:22:43.170 - [变态Schema与变态Database错误] - Start task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误) 
[TRACE] 2025-02-11 00:22:43.194 - [变态Schema与变态Database错误] - Task initialization... 
[INFO ] 2025-02-11 00:22:43.273 - [变态Schema与变态Database错误] - Loading table structure completed 
[TRACE] 2025-02-11 00:22:43.274 - [变态Schema与变态Database错误] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-11 00:22:43.330 - [变态Schema与变态Database错误] - The engine receives 变态Schema与变态Database错误 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-11 00:22:43.331 - [变态Schema与变态Database错误] - Task started 
[TRACE] 2025-02-11 00:22:43.359 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] start preload schema,table counts: 1 
[TRACE] 2025-02-11 00:22:43.359 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] start preload schema,table counts: 1 
[TRACE] 2025-02-11 00:22:43.359 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] preload schema finished, cost 1 ms 
[TRACE] 2025-02-11 00:22:43.359 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] preload schema finished, cost 0 ms 
[INFO ] 2025-02-11 00:22:43.560 - [变态Schema与变态Database错误][PG] - Enable partition table support for source database 
[INFO ] 2025-02-11 00:22:44.733 - [变态Schema与变态Database错误][SqlServer4433] - Sink connector(SqlServer4433) initialization completed 
[TRACE] 2025-02-11 00:22:53.522 - [变态Schema与变态Database错误][SqlServer4433] - Node(SqlServer4433) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-11 00:22:53.954 - [变态Schema与变态Database错误][SqlServer4433] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-11 00:22:53.958 - [变态Schema与变态Database错误][SqlServer4433] - Enable partition table support for target database 
[INFO ] 2025-02-11 00:22:53.958 - [变态Schema与变态Database错误][SqlServer4433] - Apply table structure to target database 
[INFO ] 2025-02-11 00:22:54.150 - [变态Schema与变态Database错误][PG] - Source connector(PG) initialization completed 
[TRACE] 2025-02-11 00:22:54.150 - [变态Schema与变态Database错误][PG] - Source node "PG" read batch size: 100 
[TRACE] 2025-02-11 00:22:54.150 - [变态Schema与变态Database错误][PG] - Source node "PG" event queue capacity: 200 
[TRACE] 2025-02-11 00:22:54.150 - [变态Schema与变态Database错误][PG] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-11 00:23:08.460 - [变态Schema与变态Database错误][SqlServer4433] - Will create master partition table [sales] to target, init sub partition list: [sales_2023] 
[WARN ] 2025-02-11 00:23:12.307 - [变态Schema与变态Database错误][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-11 00:23:12.405 - [变态Schema与变态Database错误][PG] - new logical replication slot created, slotName:tapdata_cdc_fe9cef0d_4b4a_4f5c_9771_fae5ae000a18 
[INFO ] 2025-02-11 00:23:12.431 - [变态Schema与变态Database错误][PG] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-11 00:23:12.432 - [变态Schema与变态Database错误][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-11 00:23:12.507 - [变态Schema与变态Database错误] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2025-02-11 00:23:12.507 - [变态Schema与变态Database错误][PG] - Starting batch read from 1 tables 
[TRACE] 2025-02-11 00:23:12.521 - [变态Schema与变态Database错误][PG] - Initial sync started 
[INFO ] 2025-02-11 00:23:12.521 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sales 
[TRACE] 2025-02-11 00:23:12.521 - [变态Schema与变态Database错误][PG] - Table sales is going to be initial synced 
[TRACE] 2025-02-11 00:23:12.723 - [变态Schema与变态Database错误][PG] - Query snapshot row size completed: PG(ea9c0610-bb94-4c0f-90f7-644daf40c5d6) 
[INFO ] 2025-02-11 00:23:12.744 - [变态Schema与变态Database错误][PG] - Table sales has been completed batch read 
[INFO ] 2025-02-11 00:23:12.745 - [变态Schema与变态Database错误][PG] - Found new table(s): [sales_2024, sales_2025, sales] 
[TRACE] 2025-02-11 00:23:13.214 - [变态Schema与变态Database错误][PG] - Load new table(s) schema finished, loaded schema count: 2 
[TRACE] 2025-02-11 00:23:13.218 - [变态Schema与变态Database错误][PG] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@2910b8e4: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2024","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2024_pkey","primary":true,"unique":true}],"lastUpdate":1739204593207,"maxPKPos":2,"maxPos":3,"name":"sales_2024","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"fixed":true,"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2024","type":206} 
[TRACE] 2025-02-11 00:23:13.244 - [变态Schema与变态Database错误][PG] - Sync sub table's [sales_2024] create table ddl, will add update master table [sales] metadata 
[TRACE] 2025-02-11 00:23:13.244 - [变态Schema与变态Database错误][PG] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_sales_2024_67a9e6877567c16109c866de_67a9ffd97567c16109c8678e 
[TRACE] 2025-02-11 00:23:13.279 - [变态Schema与变态Database错误][PG] - Create new table schema transform finished: TapTable id sales_2024 name sales_2024 storageEngine null charset null number of fields 3 
[TRACE] 2025-02-11 00:23:13.279 - [变态Schema与变态Database错误][PG] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@454df4eb: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2025","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2025_pkey","primary":true,"unique":true}],"lastUpdate":1739204593278,"maxPKPos":2,"maxPos":3,"name":"sales_2025","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"fixed":true,"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2025","type":206} 
[TRACE] 2025-02-11 00:23:13.289 - [变态Schema与变态Database错误][PG] - Sync sub table's [sales_2025] create table ddl, will add update master table [sales] metadata 
[TRACE] 2025-02-11 00:23:13.290 - [变态Schema与变态Database错误][PG] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_sales_2025_67a9e6877567c16109c866de_67a9ffd97567c16109c8678e 
[TRACE] 2025-02-11 00:23:13.309 - [变态Schema与变态Database错误][PG] - Create new table schema transform finished: TapTable id sales_2025 name sales_2025 storageEngine null charset null number of fields 3 
[INFO ] 2025-02-11 00:23:13.310 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sales_2024 
[TRACE] 2025-02-11 00:23:13.310 - [变态Schema与变态Database错误][PG] - Table sales_2024 is going to be initial synced 
[INFO ] 2025-02-11 00:23:13.405 - [变态Schema与变态Database错误][PG] - Table sales_2024 has been completed batch read 
[INFO ] 2025-02-11 00:23:13.406 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sales_2025 
[TRACE] 2025-02-11 00:23:13.406 - [变态Schema与变态Database错误][PG] - Table sales_2025 is going to be initial synced 
[TRACE] 2025-02-11 00:23:13.502 - [变态Schema与变态Database错误][SqlServer4433] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@2910b8e4: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2024","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2024_pkey","primary":true,"unique":true}],"lastUpdate":1739204593207,"maxPKPos":2,"maxPos":3,"name":"sales_2024","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2024","type":206}). Wait for all previous events to be processed 
[INFO ] 2025-02-11 00:23:13.502 - [变态Schema与变态Database错误][PG] - Table sales_2025 has been completed batch read 
[TRACE] 2025-02-11 00:23:13.503 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[INFO ] 2025-02-11 00:23:13.503 - [变态Schema与变态Database错误][PG] - Batch read completed. 
[TRACE] 2025-02-11 00:23:13.503 - [变态Schema与变态Database错误][PG] - Incremental sync starting... 
[TRACE] 2025-02-11 00:23:13.503 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[TRACE] 2025-02-11 00:23:13.504 - [变态Schema与变态Database错误][PG] - Starting stream read, table list: [sales_2025, sales_2024, sales], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-02-11 00:23:13.504 - [变态Schema与变态Database错误][PG] - Starting incremental sync using database log parser 
[WARN ] 2025-02-11 00:23:13.599 - [变态Schema与变态Database错误][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-11 00:23:13.599 - [变态Schema与变态Database错误][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_fe9cef0d_4b4a_4f5c_9771_fae5ae000a18 
[WARN ] 2025-02-11 00:23:13.719 - [变态Schema与变态Database错误][SqlServer4433] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 索引 1 超出范围。
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:237)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setterGetParam(SQLServerPreparedStatement.java:1131)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setObjectNoType(SQLServerPreparedStatement.java:1470)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setObject(SQLServerPreparedStatement.java:1510)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.setObject(HikariProxyPreparedStatement.java)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-11 00:23:13.723 - [变态Schema与变态Database错误][SqlServer4433] - Retry operation TARGET_WRITE_RECORD, retry times 1/15, first retry time 2025-02-11 00:23:13, next retry time 2025-02-11 00:24:13 
[TRACE] 2025-02-11 00:23:15.077 - [变态Schema与变态Database错误][PG] - Connector PostgreSQL incremental start succeed, tables: [sales_2025, sales_2024, sales, sales_2023], data change syncing 
[TRACE] 2025-02-11 00:24:00.141 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] running status set to false 
[TRACE] 2025-02-11 00:24:00.276 - [变态Schema与变态Database错误][PG] - Incremental sync completed 
[TRACE] 2025-02-11 00:24:00.277 - [变态Schema与变态Database错误][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739204564267 
[TRACE] 2025-02-11 00:24:00.280 - [变态Schema与变态Database错误][PG] - PDK connector node released: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739204564267 
[TRACE] 2025-02-11 00:24:00.280 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] schema data cleaned 
[TRACE] 2025-02-11 00:24:00.280 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] monitor closed 
[TRACE] 2025-02-11 00:24:00.281 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] close complete, cost 246 ms 
[TRACE] 2025-02-11 00:24:00.284 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] running status set to false 
[INFO ] 2025-02-11 00:24:00.301 - [变态Schema与变态Database错误][SqlServer4433] - Retry operation TARGET_WRITE_RECORD failed, total cost 00:00:46.657000 
[TRACE] 2025-02-11 00:24:00.301 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node stopped: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739204563999 
[TRACE] 2025-02-11 00:24:00.302 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node released: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739204563999 
[TRACE] 2025-02-11 00:24:00.302 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] schema data cleaned 
[TRACE] 2025-02-11 00:24:00.303 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] monitor closed 
[TRACE] 2025-02-11 00:24:00.303 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] close complete, cost 21 ms 
[TRACE] 2025-02-11 00:24:00.364 - [变态Schema与变态Database错误][SqlServer4433] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: sales 
[ERROR] 2025-02-11 00:24:00.366 - [变态Schema与变态Database错误][SqlServer4433] - java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: sales <-- Full Stack Trace -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: sales
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:764)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: sales
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:855)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:792)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$23(HazelcastTargetPdkBaseNode.java:752)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:638)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:724)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:776)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:723)
	... 6 more
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: sales
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:876)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$30(HazelcastTargetPdkDataNode.java:575)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:925)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:843)
	... 12 more
Caused by: Execute PDK method: TARGET_WRITE_RECORD, tableName: sales
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$63(HazelcastTargetPdkDataNode.java:964)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$64(HazelcastTargetPdkDataNode.java:901)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-29) has been closed.
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.getConnection(MssqlJdbcRunner.java:33)
	at io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:48)
	at io.tapdata.connector.mssql.dml.MssqlRecordWriterV2.<init>(MssqlRecordWriterV2.java:13)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:463)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$62(HazelcastTargetPdkDataNode.java:950)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$63(HazelcastTargetPdkDataNode.java:946)
	... 28 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-29) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 35 more

[TRACE] 2025-02-11 00:24:03.198 - [变态Schema与变态Database错误] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-11 00:24:03.198 - [变态Schema与变态Database错误] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@211974e6 
[TRACE] 2025-02-11 00:24:03.325 - [变态Schema与变态Database错误] - Stop task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误)  
[TRACE] 2025-02-11 00:24:03.347 - [变态Schema与变态Database错误] - Stopped task aspect(s) 
[TRACE] 2025-02-11 00:24:03.347 - [变态Schema与变态Database错误] - Snapshot order controller have been removed 
[INFO ] 2025-02-11 00:24:03.347 - [变态Schema与变态Database错误] - Task stopped. 
[TRACE] 2025-02-11 00:24:03.373 - [变态Schema与变态Database错误] - Remove memory task client succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 00:24:03.378 - [变态Schema与变态Database错误] - Destroy memory task client cache succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 00:25:08.012 - [变态Schema与变态Database错误] - Start task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误) 
[TRACE] 2025-02-11 00:25:08.012 - [变态Schema与变态Database错误] - Task initialization... 
[INFO ] 2025-02-11 00:25:08.178 - [变态Schema与变态Database错误] - Loading table structure completed 
[TRACE] 2025-02-11 00:25:08.238 - [变态Schema与变态Database错误] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-11 00:25:08.343 - [变态Schema与变态Database错误] - The engine receives 变态Schema与变态Database错误 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-11 00:25:08.343 - [变态Schema与变态Database错误] - Task started 
[TRACE] 2025-02-11 00:25:08.375 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] start preload schema,table counts: 1 
[TRACE] 2025-02-11 00:25:08.375 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] start preload schema,table counts: 1 
[TRACE] 2025-02-11 00:25:08.376 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] preload schema finished, cost 0 ms 
[TRACE] 2025-02-11 00:25:08.376 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] preload schema finished, cost 1 ms 
[INFO ] 2025-02-11 00:25:08.376 - [变态Schema与变态Database错误][PG] - Enable partition table support for source database 
[INFO ] 2025-02-11 00:25:09.797 - [变态Schema与变态Database错误][PG] - Source connector(PG) initialization completed 
[TRACE] 2025-02-11 00:25:09.797 - [变态Schema与变态Database错误][PG] - Source node "PG" read batch size: 100 
[TRACE] 2025-02-11 00:25:09.797 - [变态Schema与变态Database错误][PG] - Source node "PG" event queue capacity: 200 
[TRACE] 2025-02-11 00:25:09.800 - [变态Schema与变态Database错误][PG] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-11 00:25:09.849 - [变态Schema与变态Database错误][SqlServer4433] - Sink connector(SqlServer4433) initialization completed 
[TRACE] 2025-02-11 00:25:09.852 - [变态Schema与变态Database错误][SqlServer4433] - Node(SqlServer4433) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-11 00:25:09.852 - [变态Schema与变态Database错误][SqlServer4433] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-11 00:25:09.853 - [变态Schema与变态Database错误][SqlServer4433] - Enable partition table support for target database 
[INFO ] 2025-02-11 00:25:09.853 - [变态Schema与变态Database错误][SqlServer4433] - Apply table structure to target database 
[TRACE] 2025-02-11 00:25:15.884 - [变态Schema与变态Database错误][SqlServer4433] - Will create master partition table [sales] to target, init sub partition list: [sales_2023] 
[WARN ] 2025-02-11 00:25:20.219 - [变态Schema与变态Database错误][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-11 00:25:20.222 - [变态Schema与变态Database错误][PG] - new logical replication slot created, slotName:tapdata_cdc_4ed2270f_e6e8_4bc1_a54a_2bc613dac4fe 
[INFO ] 2025-02-11 00:25:20.244 - [变态Schema与变态Database错误][PG] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-11 00:25:20.246 - [变态Schema与变态Database错误][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-11 00:25:20.247 - [变态Schema与变态Database错误] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2025-02-11 00:25:22.256 - [变态Schema与变态Database错误][PG] - Starting batch read from 1 tables 
[TRACE] 2025-02-11 00:25:22.274 - [变态Schema与变态Database错误][PG] - Initial sync started 
[INFO ] 2025-02-11 00:25:22.274 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sales 
[TRACE] 2025-02-11 00:25:22.274 - [变态Schema与变态Database错误][PG] - Table sales is going to be initial synced 
[TRACE] 2025-02-11 00:25:22.385 - [变态Schema与变态Database错误][PG] - Query snapshot row size completed: PG(ea9c0610-bb94-4c0f-90f7-644daf40c5d6) 
[INFO ] 2025-02-11 00:25:22.469 - [变态Schema与变态Database错误][PG] - Table sales has been completed batch read 
[TRACE] 2025-02-11 00:25:22.470 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[INFO ] 2025-02-11 00:25:22.470 - [变态Schema与变态Database错误][PG] - Batch read completed. 
[TRACE] 2025-02-11 00:25:22.476 - [变态Schema与变态Database错误][PG] - Incremental sync starting... 
[TRACE] 2025-02-11 00:25:22.484 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[TRACE] 2025-02-11 00:25:22.485 - [变态Schema与变态Database错误][PG] - Starting stream read, table list: [sales], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-02-11 00:25:22.485 - [变态Schema与变态Database错误][PG] - Starting incremental sync using database log parser 
[WARN ] 2025-02-11 00:25:22.486 - [变态Schema与变态Database错误][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-11 00:25:22.690 - [变态Schema与变态Database错误][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_4ed2270f_e6e8_4bc1_a54a_2bc613dac4fe 
[WARN ] 2025-02-11 00:25:23.344 - [变态Schema与变态Database错误][SqlServer4433] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 索引 1 超出范围。
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:237)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setterGetParam(SQLServerPreparedStatement.java:1131)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setObjectNoType(SQLServerPreparedStatement.java:1470)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setObject(SQLServerPreparedStatement.java:1510)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.setObject(HikariProxyPreparedStatement.java)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-11 00:25:23.345 - [变态Schema与变态Database错误][SqlServer4433] - Retry operation TARGET_WRITE_RECORD, retry times 1/15, first retry time 2025-02-11 00:25:23, next retry time 2025-02-11 00:26:23 
[TRACE] 2025-02-11 00:25:24.153 - [变态Schema与变态Database错误][PG] - Connector PostgreSQL incremental start succeed, tables: [sales, sales_2023], data change syncing 
[INFO ] 2025-02-11 00:25:30.431 - [变态Schema与变态Database错误][PG] - Found new table(s): [sales_2024, sales_2025, sales] 
[TRACE] 2025-02-11 00:25:30.828 - [变态Schema与变态Database错误][PG] - Load new table(s) schema finished, loaded schema count: 2 
[WARN ] 2025-02-11 00:25:30.845 - [变态Schema与变态Database错误][PG] - It is expected to load 3 new table models, and 1 table models no longer exist and will be ignored. The table name(s) that does not exist: [sales] 
[TRACE] 2025-02-11 00:25:30.846 - [变态Schema与变态Database错误][PG] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@544ae83b: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2024","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2024_pkey","primary":true,"unique":true}],"lastUpdate":1739204730831,"maxPKPos":2,"maxPos":3,"name":"sales_2024","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"fixed":true,"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2024","type":206} 
[TRACE] 2025-02-11 00:25:30.866 - [变态Schema与变态Database错误][PG] - Sync sub table's [sales_2024] create table ddl, will add update master table [sales] metadata 
[TRACE] 2025-02-11 00:25:30.867 - [变态Schema与变态Database错误][PG] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_sales_2024_67a9e6877567c16109c866de_67a9ffd97567c16109c8678e 
[TRACE] 2025-02-11 00:25:30.895 - [变态Schema与变态Database错误][PG] - Create new table schema transform finished: TapTable id sales_2024 name sales_2024 storageEngine null charset null number of fields 3 
[TRACE] 2025-02-11 00:25:30.896 - [变态Schema与变态Database错误][PG] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@922cf65: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2025","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2025_pkey","primary":true,"unique":true}],"lastUpdate":1739204730894,"maxPKPos":2,"maxPos":3,"name":"sales_2025","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"fixed":true,"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2025","type":206} 
[TRACE] 2025-02-11 00:25:30.907 - [变态Schema与变态Database错误][PG] - Sync sub table's [sales_2025] create table ddl, will add update master table [sales] metadata 
[TRACE] 2025-02-11 00:25:30.907 - [变态Schema与变态Database错误][PG] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_sales_2025_67a9e6877567c16109c866de_67a9ffd97567c16109c8678e 
[TRACE] 2025-02-11 00:25:30.954 - [变态Schema与变态Database错误][PG] - Create new table schema transform finished: TapTable id sales_2025 name sales_2025 storageEngine null charset null number of fields 3 
[TRACE] 2025-02-11 00:25:30.954 - [变态Schema与变态Database错误][PG] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[TRACE] 2025-02-11 00:25:31.356 - [变态Schema与变态Database错误][SqlServer4433] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@544ae83b: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2024","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2024_pkey","primary":true,"unique":true}],"lastUpdate":1739204730831,"maxPKPos":2,"maxPos":3,"name":"sales_2024","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2024","type":206}). Wait for all previous events to be processed 
[TRACE] 2025-02-11 00:25:31.561 - [变态Schema与变态Database错误][PG] - Incremental sync completed 
[INFO ] 2025-02-11 00:25:32.256 - [变态Schema与变态Database错误][PG] - Starting batch read from 2 new tables 
[INFO ] 2025-02-11 00:25:32.272 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sales_2024 
[TRACE] 2025-02-11 00:25:32.272 - [变态Schema与变态Database错误][PG] - Table sales_2024 is going to be initial synced 
[INFO ] 2025-02-11 00:25:32.473 - [变态Schema与变态Database错误][PG] - Table sales_2024 has been completed batch read 
[INFO ] 2025-02-11 00:25:32.474 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sales_2025 
[TRACE] 2025-02-11 00:25:32.474 - [变态Schema与变态Database错误][PG] - Table sales_2025 is going to be initial synced 
[TRACE] 2025-02-11 00:25:32.672 - [变态Schema与变态Database错误][PG] - Query snapshot row size completed: PG(ea9c0610-bb94-4c0f-90f7-644daf40c5d6) 
[INFO ] 2025-02-11 00:25:32.675 - [变态Schema与变态Database错误][PG] - Table sales_2025 has been completed batch read 
[TRACE] 2025-02-11 00:25:32.675 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[INFO ] 2025-02-11 00:25:32.675 - [变态Schema与变态Database错误][PG] - Batch read completed. 
[TRACE] 2025-02-11 00:25:32.675 - [变态Schema与变态Database错误][PG] - Incremental sync starting... 
[TRACE] 2025-02-11 00:25:32.675 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[TRACE] 2025-02-11 00:25:32.676 - [变态Schema与变态Database错误][PG] - Starting stream read, table list: [sales_2025, sales_2024, sales], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-02-11 00:25:32.676 - [变态Schema与变态Database错误][PG] - Starting incremental sync using database log parser 
[WARN ] 2025-02-11 00:25:32.677 - [变态Schema与变态Database错误][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-11 00:25:32.878 - [变态Schema与变态Database错误][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_4ed2270f_e6e8_4bc1_a54a_2bc613dac4fe 
[TRACE] 2025-02-11 00:25:34.240 - [变态Schema与变态Database错误][PG] - Connector PostgreSQL incremental start succeed, tables: [sales_2025, sales_2024, sales, sales_2023], data change syncing 
[TRACE] 2025-02-11 00:26:23.421 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] running status set to false 
[WARN ] 2025-02-11 00:26:23.516 - [变态Schema与变态Database错误][SqlServer4433] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 索引 1 超出范围。
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:237)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setterGetParam(SQLServerPreparedStatement.java:1131)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setObjectNoType(SQLServerPreparedStatement.java:1470)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setObject(SQLServerPreparedStatement.java:1510)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.setObject(HikariProxyPreparedStatement.java)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-11 00:26:23.516 - [变态Schema与变态Database错误][SqlServer4433] - Retry operation TARGET_WRITE_RECORD, retry times 2/15, first retry time 2025-02-11 00:25:23, next retry time 2025-02-11 00:27:23 
[TRACE] 2025-02-11 00:26:23.537 - [变态Schema与变态Database错误][PG] - Incremental sync completed 
[TRACE] 2025-02-11 00:26:23.537 - [变态Schema与变态Database错误][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739204731479 
[TRACE] 2025-02-11 00:26:23.537 - [变态Schema与变态Database错误][PG] - PDK connector node released: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739204731479 
[TRACE] 2025-02-11 00:26:23.538 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] schema data cleaned 
[TRACE] 2025-02-11 00:26:23.545 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] monitor closed 
[TRACE] 2025-02-11 00:26:23.545 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] close complete, cost 142 ms 
[TRACE] 2025-02-11 00:26:23.548 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] running status set to false 
[INFO ] 2025-02-11 00:26:23.549 - [变态Schema与变态Database错误][SqlServer4433] - Retry operation TARGET_WRITE_RECORD failed, total cost 00:01:00.206000 
[TRACE] 2025-02-11 00:26:23.562 - [变态Schema与变态Database错误][SqlServer4433] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): when operate table: sales, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-35) has been closed. 
[TRACE] 2025-02-11 00:26:23.562 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node stopped: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739204709204 
[TRACE] 2025-02-11 00:26:23.562 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node released: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739204709204 
[TRACE] 2025-02-11 00:26:23.563 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] schema data cleaned 
[TRACE] 2025-02-11 00:26:23.563 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] monitor closed 
[TRACE] 2025-02-11 00:26:23.563 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] close complete, cost 18 ms 
[ERROR] 2025-02-11 00:26:23.769 - [变态Schema与变态Database错误][SqlServer4433] - PDK retry exception (Server Error Code 0): when operate table: sales, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-35) has been closed. <-- Error Message -->
PDK retry exception (Server Error Code 0): when operate table: sales, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-35) has been closed.

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-35) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.connector.mssql.MssqlJdbcRunner.getConnection(MssqlJdbcRunner.java:33)
	io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:48)
	io.tapdata.connector.mssql.dml.MssqlRecordWriterV2.<init>(MssqlRecordWriterV2.java:13)
	...

<-- Full Stack Trace -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-35) has been closed.
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.getConnection(MssqlJdbcRunner.java:33)
	at io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:48)
	at io.tapdata.connector.mssql.dml.MssqlRecordWriterV2.<init>(MssqlRecordWriterV2.java:13)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:463)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$62(HazelcastTargetPdkDataNode.java:950)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$63(HazelcastTargetPdkDataNode.java:946)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$64(HazelcastTargetPdkDataNode.java:901)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:876)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$30(HazelcastTargetPdkDataNode.java:575)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:925)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:843)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:792)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$23(HazelcastTargetPdkBaseNode.java:752)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:638)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:724)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:776)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:723)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-35) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 35 more

[TRACE] 2025-02-11 00:26:27.268 - [变态Schema与变态Database错误] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-11 00:26:27.268 - [变态Schema与变态Database错误] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7d8afaf9 
[TRACE] 2025-02-11 00:26:27.390 - [变态Schema与变态Database错误] - Stop task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误)  
[TRACE] 2025-02-11 00:26:27.420 - [变态Schema与变态Database错误] - Stopped task aspect(s) 
[TRACE] 2025-02-11 00:26:27.420 - [变态Schema与变态Database错误] - Snapshot order controller have been removed 
[INFO ] 2025-02-11 00:26:27.421 - [变态Schema与变态Database错误] - Task stopped. 
[TRACE] 2025-02-11 00:26:27.466 - [变态Schema与变态Database错误] - Remove memory task client succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 00:26:27.469 - [变态Schema与变态Database错误] - Destroy memory task client cache succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 00:28:12.559 - [变态Schema与变态Database错误] - Start task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误) 
[TRACE] 2025-02-11 00:28:12.683 - [变态Schema与变态Database错误] - Task initialization... 
[INFO ] 2025-02-11 00:28:12.685 - [变态Schema与变态Database错误] - Loading table structure completed 
[TRACE] 2025-02-11 00:28:12.745 - [变态Schema与变态Database错误] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-11 00:28:12.749 - [变态Schema与变态Database错误] - The engine receives 变态Schema与变态Database错误 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-11 00:28:12.802 - [变态Schema与变态Database错误] - Task started 
[TRACE] 2025-02-11 00:28:12.802 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] start preload schema,table counts: 1 
[TRACE] 2025-02-11 00:28:12.802 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] start preload schema,table counts: 1 
[TRACE] 2025-02-11 00:28:12.802 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] preload schema finished, cost 0 ms 
[TRACE] 2025-02-11 00:28:13.008 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] preload schema finished, cost 0 ms 
[INFO ] 2025-02-11 00:28:13.008 - [变态Schema与变态Database错误][PG] - Enable partition table support for source database 
[INFO ] 2025-02-11 00:28:14.268 - [变态Schema与变态Database错误][PG] - Source connector(PG) initialization completed 
[TRACE] 2025-02-11 00:28:14.269 - [变态Schema与变态Database错误][PG] - Source node "PG" read batch size: 100 
[TRACE] 2025-02-11 00:28:14.269 - [变态Schema与变态Database错误][PG] - Source node "PG" event queue capacity: 200 
[TRACE] 2025-02-11 00:28:14.269 - [变态Schema与变态Database错误][PG] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-11 00:28:14.291 - [变态Schema与变态Database错误][SqlServer4433] - Sink connector(SqlServer4433) initialization completed 
[TRACE] 2025-02-11 00:28:14.292 - [变态Schema与变态Database错误][SqlServer4433] - Node(SqlServer4433) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-11 00:28:14.292 - [变态Schema与变态Database错误][SqlServer4433] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-11 00:28:14.295 - [变态Schema与变态Database错误][SqlServer4433] - Enable partition table support for target database 
[INFO ] 2025-02-11 00:28:14.295 - [变态Schema与变态Database错误][SqlServer4433] - Apply table structure to target database 
[TRACE] 2025-02-11 00:28:14.905 - [变态Schema与变态Database错误][SqlServer4433] - Will create master partition table [sales] to target, init sub partition list: [sales_2023] 
[WARN ] 2025-02-11 00:28:15.493 - [变态Schema与变态Database错误][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-11 00:28:15.496 - [变态Schema与变态Database错误][PG] - new logical replication slot created, slotName:tapdata_cdc_37fc04e2_cd86_45bc_85aa_1feddb86b27f 
[INFO ] 2025-02-11 00:28:15.514 - [变态Schema与变态Database错误][PG] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-11 00:28:15.527 - [变态Schema与变态Database错误][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-11 00:28:15.527 - [变态Schema与变态Database错误] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2025-02-11 00:28:15.642 - [变态Schema与变态Database错误][PG] - Starting batch read from 1 tables 
[TRACE] 2025-02-11 00:28:15.643 - [变态Schema与变态Database错误][PG] - Initial sync started 
[INFO ] 2025-02-11 00:28:15.644 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sales 
[TRACE] 2025-02-11 00:28:15.644 - [变态Schema与变态Database错误][PG] - Table sales is going to be initial synced 
[INFO ] 2025-02-11 00:28:15.753 - [变态Schema与变态Database错误][PG] - Table sales has been completed batch read 
[INFO ] 2025-02-11 00:28:15.753 - [变态Schema与变态Database错误][PG] - Found new table(s): [sales_2024, sales_2025, sales] 
[TRACE] 2025-02-11 00:28:15.954 - [变态Schema与变态Database错误][PG] - Query snapshot row size completed: PG(ea9c0610-bb94-4c0f-90f7-644daf40c5d6) 
[TRACE] 2025-02-11 00:28:16.247 - [变态Schema与变态Database错误][PG] - Load new table(s) schema finished, loaded schema count: 2 
[TRACE] 2025-02-11 00:28:16.264 - [变态Schema与变态Database错误][PG] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@81dd59e: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2024","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2024_pkey","primary":true,"unique":true}],"lastUpdate":1739204896247,"maxPKPos":2,"maxPos":3,"name":"sales_2024","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"fixed":true,"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2024","type":206} 
[TRACE] 2025-02-11 00:28:16.265 - [变态Schema与变态Database错误][PG] - Sync sub table's [sales_2024] create table ddl, will add update master table [sales] metadata 
[TRACE] 2025-02-11 00:28:16.323 - [变态Schema与变态Database错误][PG] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_sales_2024_67a9e6877567c16109c866de_67a9ffd97567c16109c8678e 
[TRACE] 2025-02-11 00:28:16.323 - [变态Schema与变态Database错误][PG] - Create new table schema transform finished: TapTable id sales_2024 name sales_2024 storageEngine null charset null number of fields 3 
[TRACE] 2025-02-11 00:28:16.327 - [变态Schema与变态Database错误][PG] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@4eb04c3f: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2025","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2025_pkey","primary":true,"unique":true}],"lastUpdate":1739204896323,"maxPKPos":2,"maxPos":3,"name":"sales_2025","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"fixed":true,"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2025","type":206} 
[TRACE] 2025-02-11 00:28:16.327 - [变态Schema与变态Database错误][PG] - Sync sub table's [sales_2025] create table ddl, will add update master table [sales] metadata 
[TRACE] 2025-02-11 00:28:16.370 - [变态Schema与变态Database错误][PG] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_sales_2025_67a9e6877567c16109c866de_67a9ffd97567c16109c8678e 
[TRACE] 2025-02-11 00:28:16.370 - [变态Schema与变态Database错误][PG] - Create new table schema transform finished: TapTable id sales_2025 name sales_2025 storageEngine null charset null number of fields 3 
[INFO ] 2025-02-11 00:28:16.370 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sales_2024 
[TRACE] 2025-02-11 00:28:16.370 - [变态Schema与变态Database错误][PG] - Table sales_2024 is going to be initial synced 
[TRACE] 2025-02-11 00:28:16.467 - [变态Schema与变态Database错误][SqlServer4433] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): when operate table: sales, com.microsoft.sqlserver.jdbc.SQLServerException: 索引 1 超出范围。 
[ERROR] 2025-02-11 00:28:16.467 - [变态Schema与变态Database错误][SqlServer4433] - PDK retry exception (Server Error Code 0): when operate table: sales, com.microsoft.sqlserver.jdbc.SQLServerException: 索引 1 超出范围。 <-- Error Message -->
PDK retry exception (Server Error Code 0): when operate table: sales, com.microsoft.sqlserver.jdbc.SQLServerException: 索引 1 超出范围。

<-- Simple Stack Trace -->
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 索引 1 超出范围。
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:237)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setterGetParam(SQLServerPreparedStatement.java:1131)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setObjectNoType(SQLServerPreparedStatement.java:1470)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setObject(SQLServerPreparedStatement.java:1510)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.setObject(HikariProxyPreparedStatement.java)
	...

<-- Full Stack Trace -->
com.microsoft.sqlserver.jdbc.SQLServerException: 索引 1 超出范围。
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:143)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:468)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$62(HazelcastTargetPdkDataNode.java:950)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$63(HazelcastTargetPdkDataNode.java:946)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$64(HazelcastTargetPdkDataNode.java:901)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:876)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$30(HazelcastTargetPdkDataNode.java:575)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:925)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:843)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:792)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$23(HazelcastTargetPdkBaseNode.java:752)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:638)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:724)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:776)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:723)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 索引 1 超出范围。
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setterGetParam(SQLServerPreparedStatement.java:1131)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setObjectNoType(SQLServerPreparedStatement.java:1470)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setObject(SQLServerPreparedStatement.java:1510)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.setObject(HikariProxyPreparedStatement.java)
	at io.tapdata.connector.mssql.dml.MssqlWriteRecorderV2.setPrepareStatement(MssqlWriteRecorderV2.java:141)
	at io.tapdata.common.dml.NormalWriteRecorder.justInsert(NormalWriteRecorder.java:293)
	at io.tapdata.common.dml.NormalWriteRecorder.addInsertBatch(NormalWriteRecorder.java:239)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:96)
	... 30 more

[TRACE] 2025-02-11 00:28:16.481 - [变态Schema与变态Database错误][SqlServer4433] - Job suspend in error handle 
[INFO ] 2025-02-11 00:28:16.481 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sales_2025 
[TRACE] 2025-02-11 00:28:16.518 - [变态Schema与变态Database错误][PG] - Table sales_2025 is going to be initial synced 
[TRACE] 2025-02-11 00:28:16.518 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] running status set to false 
[TRACE] 2025-02-11 00:28:16.554 - [变态Schema与变态Database错误][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739204893432 
[TRACE] 2025-02-11 00:28:16.554 - [变态Schema与变态Database错误][PG] - PDK connector node released: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739204893432 
[TRACE] 2025-02-11 00:28:16.554 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] schema data cleaned 
[TRACE] 2025-02-11 00:28:16.564 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] monitor closed 
[TRACE] 2025-02-11 00:28:16.567 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] close complete, cost 43 ms 
[TRACE] 2025-02-11 00:28:16.568 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] running status set to false 
[TRACE] 2025-02-11 00:28:16.573 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[INFO ] 2025-02-11 00:28:16.573 - [变态Schema与变态Database错误][PG] - Batch read completed. 
[TRACE] 2025-02-11 00:28:16.574 - [变态Schema与变态Database错误][PG] - Incremental sync starting... 
[TRACE] 2025-02-11 00:28:16.580 - [变态Schema与变态Database错误][PG] - Incremental sync completed 
[TRACE] 2025-02-11 00:28:16.580 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node stopped: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739204893635 
[TRACE] 2025-02-11 00:28:16.580 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node released: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739204893635 
[TRACE] 2025-02-11 00:28:16.580 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] schema data cleaned 
[TRACE] 2025-02-11 00:28:16.580 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] monitor closed 
[TRACE] 2025-02-11 00:28:16.784 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] close complete, cost 16 ms 
[INFO ] 2025-02-11 00:28:17.599 - [变态Schema与变态Database错误] - Task [变态Schema与变态Database错误] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-02-11 00:28:17.615 - [变态Schema与变态Database错误] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-11 00:28:17.741 - [变态Schema与变态Database错误] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6ab576e3 
[TRACE] 2025-02-11 00:28:17.742 - [变态Schema与变态Database错误] - Stop task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误)  
[TRACE] 2025-02-11 00:28:17.765 - [变态Schema与变态Database错误] - Stopped task aspect(s) 
[TRACE] 2025-02-11 00:28:17.765 - [变态Schema与变态Database错误] - Snapshot order controller have been removed 
[INFO ] 2025-02-11 00:28:17.783 - [变态Schema与变态Database错误] - Task stopped. 
[TRACE] 2025-02-11 00:28:17.785 - [变态Schema与变态Database错误] - Remove memory task client succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 00:28:17.785 - [变态Schema与变态Database错误] - Destroy memory task client cache succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 00:30:09.880 - [变态Schema与变态Database错误] - Start task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误) 
[TRACE] 2025-02-11 00:30:09.882 - [变态Schema与变态Database错误] - Task initialization... 
[INFO ] 2025-02-11 00:30:09.995 - [变态Schema与变态Database错误] - Loading table structure completed 
[TRACE] 2025-02-11 00:30:10.063 - [变态Schema与变态Database错误] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-11 00:30:10.064 - [变态Schema与变态Database错误] - The engine receives 变态Schema与变态Database错误 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-11 00:30:10.129 - [变态Schema与变态Database错误] - Task started 
[TRACE] 2025-02-11 00:30:10.129 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] start preload schema,table counts: 1 
[TRACE] 2025-02-11 00:30:10.129 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] start preload schema,table counts: 1 
[TRACE] 2025-02-11 00:30:10.129 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] preload schema finished, cost 0 ms 
[TRACE] 2025-02-11 00:30:10.130 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] preload schema finished, cost 0 ms 
[INFO ] 2025-02-11 00:30:10.130 - [变态Schema与变态Database错误][PG] - Enable partition table support for source database 
[INFO ] 2025-02-11 00:30:11.490 - [变态Schema与变态Database错误][PG] - Source connector(PG) initialization completed 
[TRACE] 2025-02-11 00:30:11.492 - [变态Schema与变态Database错误][PG] - Source node "PG" read batch size: 100 
[TRACE] 2025-02-11 00:30:11.492 - [变态Schema与变态Database错误][PG] - Source node "PG" event queue capacity: 200 
[TRACE] 2025-02-11 00:30:11.492 - [变态Schema与变态Database错误][PG] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-11 00:30:11.545 - [变态Schema与变态Database错误][SqlServer4433] - Sink connector(SqlServer4433) initialization completed 
[TRACE] 2025-02-11 00:30:11.547 - [变态Schema与变态Database错误][SqlServer4433] - Node(SqlServer4433) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-11 00:30:11.547 - [变态Schema与变态Database错误][SqlServer4433] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-11 00:30:11.548 - [变态Schema与变态Database错误][SqlServer4433] - Enable partition table support for target database 
[INFO ] 2025-02-11 00:30:11.549 - [变态Schema与变态Database错误][SqlServer4433] - Apply table structure to target database 
[TRACE] 2025-02-11 00:30:15.559 - [变态Schema与变态Database错误][SqlServer4433] - Will create master partition table [sales] to target, init sub partition list: [sales_2023] 
[WARN ] 2025-02-11 00:30:18.193 - [变态Schema与变态Database错误][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-11 00:30:18.291 - [变态Schema与变态Database错误][PG] - new logical replication slot created, slotName:tapdata_cdc_51d9972c_97eb_441f_9ef9_0608d86696d0 
[INFO ] 2025-02-11 00:30:18.321 - [变态Schema与变态Database错误][PG] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-11 00:30:18.321 - [变态Schema与变态Database错误][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-11 00:30:18.397 - [变态Schema与变态Database错误] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2025-02-11 00:30:18.397 - [变态Schema与变态Database错误][PG] - Starting batch read from 1 tables 
[TRACE] 2025-02-11 00:30:18.404 - [变态Schema与变态Database错误][PG] - Initial sync started 
[INFO ] 2025-02-11 00:30:18.405 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sales 
[TRACE] 2025-02-11 00:30:18.410 - [变态Schema与变态Database错误][PG] - Table sales is going to be initial synced 
[TRACE] 2025-02-11 00:30:18.616 - [变态Schema与变态Database错误][PG] - Query snapshot row size completed: PG(ea9c0610-bb94-4c0f-90f7-644daf40c5d6) 
[INFO ] 2025-02-11 00:30:18.625 - [变态Schema与变态Database错误][PG] - Table sales has been completed batch read 
[INFO ] 2025-02-11 00:30:18.625 - [变态Schema与变态Database错误][PG] - Found new table(s): [sales_2024, sales_2025, sales] 
[TRACE] 2025-02-11 00:30:19.081 - [变态Schema与变态Database错误][PG] - Load new table(s) schema finished, loaded schema count: 2 
[TRACE] 2025-02-11 00:30:19.103 - [变态Schema与变态Database错误][PG] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@318e8352: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2024","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2024_pkey","primary":true,"unique":true}],"lastUpdate":1739205019081,"maxPKPos":2,"maxPos":3,"name":"sales_2024","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"fixed":true,"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2024","type":206} 
[TRACE] 2025-02-11 00:30:19.105 - [变态Schema与变态Database错误][PG] - Sync sub table's [sales_2024] create table ddl, will add update master table [sales] metadata 
[TRACE] 2025-02-11 00:30:19.175 - [变态Schema与变态Database错误][PG] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_sales_2024_67a9e6877567c16109c866de_67a9ffd97567c16109c8678e 
[TRACE] 2025-02-11 00:30:19.177 - [变态Schema与变态Database错误][PG] - Create new table schema transform finished: TapTable id sales_2024 name sales_2024 storageEngine null charset null number of fields 3 
[TRACE] 2025-02-11 00:30:19.177 - [变态Schema与变态Database错误][PG] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@5c983c35: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2025","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2025_pkey","primary":true,"unique":true}],"lastUpdate":1739205019175,"maxPKPos":2,"maxPos":3,"name":"sales_2025","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"fixed":true,"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2025","type":206} 
[TRACE] 2025-02-11 00:30:19.182 - [变态Schema与变态Database错误][PG] - Sync sub table's [sales_2025] create table ddl, will add update master table [sales] metadata 
[TRACE] 2025-02-11 00:30:19.182 - [变态Schema与变态Database错误][PG] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_sales_2025_67a9e6877567c16109c866de_67a9ffd97567c16109c8678e 
[TRACE] 2025-02-11 00:30:19.202 - [变态Schema与变态Database错误][PG] - Create new table schema transform finished: TapTable id sales_2025 name sales_2025 storageEngine null charset null number of fields 3 
[INFO ] 2025-02-11 00:30:19.202 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sales_2024 
[TRACE] 2025-02-11 00:30:19.294 - [变态Schema与变态Database错误][PG] - Table sales_2024 is going to be initial synced 
[INFO ] 2025-02-11 00:30:19.294 - [变态Schema与变态Database错误][PG] - Table sales_2024 has been completed batch read 
[INFO ] 2025-02-11 00:30:19.295 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sales_2025 
[TRACE] 2025-02-11 00:30:19.295 - [变态Schema与变态Database错误][PG] - Table sales_2025 is going to be initial synced 
[INFO ] 2025-02-11 00:30:19.385 - [变态Schema与变态Database错误][PG] - Table sales_2025 has been completed batch read 
[TRACE] 2025-02-11 00:30:19.385 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[INFO ] 2025-02-11 00:30:19.385 - [变态Schema与变态Database错误][PG] - Batch read completed. 
[TRACE] 2025-02-11 00:30:19.386 - [变态Schema与变态Database错误][PG] - Incremental sync starting... 
[TRACE] 2025-02-11 00:30:19.387 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[TRACE] 2025-02-11 00:30:19.387 - [变态Schema与变态Database错误][PG] - Starting stream read, table list: [sales_2025, sales_2024, sales], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-02-11 00:30:19.387 - [变态Schema与变态Database错误][PG] - Starting incremental sync using database log parser 
[WARN ] 2025-02-11 00:30:19.481 - [变态Schema与变态Database错误][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-11 00:30:19.482 - [变态Schema与变态Database错误][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_51d9972c_97eb_441f_9ef9_0608d86696d0 
[TRACE] 2025-02-11 00:30:32.498 - [变态Schema与变态Database错误][SqlServer4433] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@318e8352: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2024","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2024_pkey","primary":true,"unique":true}],"lastUpdate":1739205019081,"maxPKPos":2,"maxPos":3,"name":"sales_2024","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2024","type":206}). Wait for all previous events to be processed 
[TRACE] 2025-02-11 00:32:57.226 - [变态Schema与变态Database错误][SqlServer4433] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): when operate table: sales, com.microsoft.sqlserver.jdbc.SQLServerException: 索引 1 超出范围。 
[TRACE] 2025-02-11 00:32:57.237 - [变态Schema与变态Database错误][PG] - Incremental sync completed 
[TRACE] 2025-02-11 00:32:57.237 - [变态Schema与变态Database错误][PG] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.net.SocketException: Broken pipe (Write failed) 
[ERROR] 2025-02-11 00:32:57.306 - [变态Schema与变态Database错误][SqlServer4433] - PDK retry exception (Server Error Code 0): when operate table: sales, com.microsoft.sqlserver.jdbc.SQLServerException: 索引 1 超出范围。 <-- Error Message -->
PDK retry exception (Server Error Code 0): when operate table: sales, com.microsoft.sqlserver.jdbc.SQLServerException: 索引 1 超出范围。

<-- Simple Stack Trace -->
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 索引 1 超出范围。
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:237)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setterGetParam(SQLServerPreparedStatement.java:1131)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setObjectNoType(SQLServerPreparedStatement.java:1470)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setObject(SQLServerPreparedStatement.java:1510)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.setObject(HikariProxyPreparedStatement.java)
	...

<-- Full Stack Trace -->
com.microsoft.sqlserver.jdbc.SQLServerException: 索引 1 超出范围。
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:143)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:468)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$62(HazelcastTargetPdkDataNode.java:950)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$63(HazelcastTargetPdkDataNode.java:946)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$64(HazelcastTargetPdkDataNode.java:901)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:876)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$30(HazelcastTargetPdkDataNode.java:575)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:925)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:843)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:792)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$23(HazelcastTargetPdkBaseNode.java:752)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:638)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:724)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:776)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:723)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 索引 1 超出范围。
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setterGetParam(SQLServerPreparedStatement.java:1131)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setObjectNoType(SQLServerPreparedStatement.java:1470)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.setObject(SQLServerPreparedStatement.java:1510)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.setObject(HikariProxyPreparedStatement.java)
	at io.tapdata.connector.mssql.dml.MssqlWriteRecorderV2.setPrepareStatement(MssqlWriteRecorderV2.java:141)
	at io.tapdata.common.dml.NormalWriteRecorder.justInsert(NormalWriteRecorder.java:293)
	at io.tapdata.common.dml.NormalWriteRecorder.addInsertBatch(NormalWriteRecorder.java:239)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:96)
	... 30 more

[ERROR] 2025-02-11 00:32:57.308 - [变态Schema与变态Database错误][PG] - java.net.SocketException: Broken pipe (Write failed) <-- Error Message -->
java.net.SocketException: Broken pipe (Write failed)

<-- Simple Stack Trace -->
Caused by: java.net.SocketException: Broken pipe (Write failed)
	java.net.SocketOutputStream.socketWrite0(Native Method)
	java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111)
	java.net.SocketOutputStream.write(SocketOutputStream.java:155)
	java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	...

<-- Full Stack Trace -->
java.net.SocketException: Broken pipe (Write failed)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:923)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:913)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:801)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:292)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Broken pipe (Write failed)
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:155)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at org.postgresql.core.PGStream.flush(PGStream.java:709)
	at org.postgresql.jre7.sasl.ScramAuthenticator.sendAuthenticationMessage(ScramAuthenticator.java:50)
	at org.postgresql.jre7.sasl.ScramAuthenticator.sendScramClientFirstMessage(ScramAuthenticator.java:104)
	at org.postgresql.core.v3.ConnectionFactoryImpl.doAuthentication(ConnectionFactoryImpl.java:804)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:180)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at io.debezium.jdbc.JdbcConnection.lambda$patternBasedFactory$1(JdbcConnection.java:231)
	at io.debezium.jdbc.JdbcConnection.connection(JdbcConnection.java:872)
	at io.debezium.jdbc.JdbcConnection.connection(JdbcConnection.java:867)
	at io.debezium.connector.postgresql.TypeRegistry.<init>(TypeRegistry.java:122)
	at io.debezium.connector.postgresql.connection.PostgresConnection.<init>(PostgresConnection.java:76)
	at io.debezium.connector.postgresql.PostgresConnectorTask.start(PostgresConnectorTask.java:75)
	at io.debezium.connector.common.BaseSourceTask.start(BaseSourceTask.java:130)
	at io.debezium.embedded.EmbeddedEngine.run(EmbeddedEngine.java:759)
	at io.tapdata.connector.postgres.cdc.DebeziumCdcRunner.startCdcRunner(DebeziumCdcRunner.java:44)
	at io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:470)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:901)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-11 00:32:57.320 - [变态Schema与变态Database错误][PG] - Job suspend in error handle 
[TRACE] 2025-02-11 00:32:57.323 - [变态Schema与变态Database错误][SqlServer4433] - Job suspend in error handle 
[TRACE] 2025-02-11 00:32:57.329 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] running status set to false 
[TRACE] 2025-02-11 00:32:57.381 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] running status set to false 
[TRACE] 2025-02-11 00:32:57.381 - [变态Schema与变态Database错误][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739205010720 
[TRACE] 2025-02-11 00:32:57.383 - [变态Schema与变态Database错误][PG] - PDK connector node released: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739205010720 
[TRACE] 2025-02-11 00:32:57.383 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] schema data cleaned 
[TRACE] 2025-02-11 00:32:57.383 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] monitor closed 
[TRACE] 2025-02-11 00:32:57.409 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node stopped: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739205010899 
[TRACE] 2025-02-11 00:32:57.409 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] close complete, cost 91 ms 
[TRACE] 2025-02-11 00:32:57.410 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node released: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739205010899 
[TRACE] 2025-02-11 00:32:57.410 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] schema data cleaned 
[TRACE] 2025-02-11 00:32:57.410 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] monitor closed 
[TRACE] 2025-02-11 00:32:57.626 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] close complete, cost 73 ms 
[INFO ] 2025-02-11 00:33:01.869 - [变态Schema与变态Database错误] - Task [变态Schema与变态Database错误] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-02-11 00:33:01.869 - [变态Schema与变态Database错误] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-11 00:33:01.989 - [变态Schema与变态Database错误] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@477f8da5 
[TRACE] 2025-02-11 00:33:01.989 - [变态Schema与变态Database错误] - Stop task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误)  
[TRACE] 2025-02-11 00:33:02.012 - [变态Schema与变态Database错误] - Stopped task aspect(s) 
[TRACE] 2025-02-11 00:33:02.014 - [变态Schema与变态Database错误] - Snapshot order controller have been removed 
[INFO ] 2025-02-11 00:33:02.014 - [变态Schema与变态Database错误] - Task stopped. 
[TRACE] 2025-02-11 00:33:02.039 - [变态Schema与变态Database错误] - Remove memory task client succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 00:33:02.039 - [变态Schema与变态Database错误] - Destroy memory task client cache succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 00:39:25.251 - [变态Schema与变态Database错误] - Start task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误) 
[TRACE] 2025-02-11 00:39:25.252 - [变态Schema与变态Database错误] - Task initialization... 
[INFO ] 2025-02-11 00:39:25.368 - [变态Schema与变态Database错误] - Loading table structure completed 
[TRACE] 2025-02-11 00:39:25.368 - [变态Schema与变态Database错误] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-11 00:39:25.426 - [变态Schema与变态Database错误] - The engine receives 变态Schema与变态Database错误 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-11 00:39:25.427 - [变态Schema与变态Database错误] - Task started 
[TRACE] 2025-02-11 00:39:25.459 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] start preload schema,table counts: 1 
[TRACE] 2025-02-11 00:39:25.460 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] start preload schema,table counts: 1 
[TRACE] 2025-02-11 00:39:25.460 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] preload schema finished, cost 0 ms 
[TRACE] 2025-02-11 00:39:25.460 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] preload schema finished, cost 0 ms 
[INFO ] 2025-02-11 00:39:25.460 - [变态Schema与变态Database错误][PG] - Enable partition table support for source database 
[INFO ] 2025-02-11 00:39:27.013 - [变态Schema与变态Database错误][SqlServer4433] - Sink connector(SqlServer4433) initialization completed 
[TRACE] 2025-02-11 00:39:27.014 - [变态Schema与变态Database错误][SqlServer4433] - Node(SqlServer4433) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-11 00:39:27.014 - [变态Schema与变态Database错误][SqlServer4433] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-11 00:39:27.015 - [变态Schema与变态Database错误][SqlServer4433] - Enable partition table support for target database 
[INFO ] 2025-02-11 00:39:29.943 - [变态Schema与变态Database错误][SqlServer4433] - Apply table structure to target database 
[INFO ] 2025-02-11 00:39:30.088 - [变态Schema与变态Database错误][PG] - Source connector(PG) initialization completed 
[TRACE] 2025-02-11 00:39:30.091 - [变态Schema与变态Database错误][PG] - Source node "PG" read batch size: 100 
[TRACE] 2025-02-11 00:39:30.091 - [变态Schema与变态Database错误][PG] - Source node "PG" event queue capacity: 200 
[TRACE] 2025-02-11 00:39:30.091 - [变态Schema与变态Database错误][PG] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-11 00:39:30.520 - [变态Schema与变态Database错误][SqlServer4433] - Will create master partition table [sales] to target, init sub partition list: [sales_2023] 
[WARN ] 2025-02-11 00:39:31.260 - [变态Schema与变态Database错误][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-11 00:39:31.264 - [变态Schema与变态Database错误][PG] - new logical replication slot created, slotName:tapdata_cdc_f8b8de2b_91a1_406a_84ef_e5244901b820 
[INFO ] 2025-02-11 00:39:31.300 - [变态Schema与变态Database错误][PG] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-11 00:39:31.308 - [变态Schema与变态Database错误][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-11 00:39:31.308 - [变态Schema与变态Database错误] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2025-02-11 00:39:31.386 - [变态Schema与变态Database错误][PG] - Starting batch read from 1 tables 
[TRACE] 2025-02-11 00:39:31.386 - [变态Schema与变态Database错误][PG] - Initial sync started 
[INFO ] 2025-02-11 00:39:31.390 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sales 
[TRACE] 2025-02-11 00:39:31.390 - [变态Schema与变态Database错误][PG] - Table sales is going to be initial synced 
[INFO ] 2025-02-11 00:39:31.498 - [变态Schema与变态Database错误][PG] - Table sales has been completed batch read 
[INFO ] 2025-02-11 00:39:31.498 - [变态Schema与变态Database错误][PG] - Found new table(s): [sales_2024, sales_2025, sales] 
[TRACE] 2025-02-11 00:39:31.699 - [变态Schema与变态Database错误][PG] - Query snapshot row size completed: PG(ea9c0610-bb94-4c0f-90f7-644daf40c5d6) 
[TRACE] 2025-02-11 00:39:31.912 - [变态Schema与变态Database错误][PG] - Load new table(s) schema finished, loaded schema count: 2 
[TRACE] 2025-02-11 00:39:31.913 - [变态Schema与变态Database错误][PG] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@6a553822: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2024","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2024_pkey","primary":true,"unique":true}],"lastUpdate":1739205571907,"maxPKPos":2,"maxPos":3,"name":"sales_2024","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"fixed":true,"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2024","type":206} 
[TRACE] 2025-02-11 00:39:31.945 - [变态Schema与变态Database错误][PG] - Sync sub table's [sales_2024] create table ddl, will add update master table [sales] metadata 
[TRACE] 2025-02-11 00:39:31.946 - [变态Schema与变态Database错误][PG] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_sales_2024_67a9e6877567c16109c866de_67a9ffd97567c16109c8678e 
[TRACE] 2025-02-11 00:39:31.992 - [变态Schema与变态Database错误][PG] - Create new table schema transform finished: TapTable id sales_2024 name sales_2024 storageEngine null charset null number of fields 3 
[TRACE] 2025-02-11 00:39:31.992 - [变态Schema与变态Database错误][PG] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@371d9b1a: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2025","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2025_pkey","primary":true,"unique":true}],"lastUpdate":1739205571991,"maxPKPos":2,"maxPos":3,"name":"sales_2025","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"fixed":true,"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2025","type":206} 
[TRACE] 2025-02-11 00:39:32.003 - [变态Schema与变态Database错误][PG] - Sync sub table's [sales_2025] create table ddl, will add update master table [sales] metadata 
[TRACE] 2025-02-11 00:39:32.004 - [变态Schema与变态Database错误][PG] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_sales_2025_67a9e6877567c16109c866de_67a9ffd97567c16109c8678e 
[TRACE] 2025-02-11 00:39:32.047 - [变态Schema与变态Database错误][PG] - Create new table schema transform finished: TapTable id sales_2025 name sales_2025 storageEngine null charset null number of fields 3 
[INFO ] 2025-02-11 00:39:32.047 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sales_2024 
[TRACE] 2025-02-11 00:39:32.047 - [变态Schema与变态Database错误][PG] - Table sales_2024 is going to be initial synced 
[INFO ] 2025-02-11 00:39:32.147 - [变态Schema与变态Database错误][PG] - Table sales_2024 has been completed batch read 
[INFO ] 2025-02-11 00:39:32.150 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sales_2025 
[TRACE] 2025-02-11 00:39:32.151 - [变态Schema与变态Database错误][PG] - Table sales_2025 is going to be initial synced 
[INFO ] 2025-02-11 00:39:32.242 - [变态Schema与变态Database错误][PG] - Table sales_2025 has been completed batch read 
[TRACE] 2025-02-11 00:39:32.242 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[INFO ] 2025-02-11 00:39:32.243 - [变态Schema与变态Database错误][PG] - Batch read completed. 
[TRACE] 2025-02-11 00:39:32.243 - [变态Schema与变态Database错误][PG] - Incremental sync starting... 
[TRACE] 2025-02-11 00:39:32.243 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[TRACE] 2025-02-11 00:39:32.248 - [变态Schema与变态Database错误][PG] - Starting stream read, table list: [sales_2025, sales_2024, sales], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-02-11 00:39:32.248 - [变态Schema与变态Database错误][PG] - Starting incremental sync using database log parser 
[WARN ] 2025-02-11 00:39:32.249 - [变态Schema与变态Database错误][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-11 00:39:32.450 - [变态Schema与变态Database错误][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_f8b8de2b_91a1_406a_84ef_e5244901b820 
[TRACE] 2025-02-11 00:39:32.450 - [变态Schema与变态Database错误][SqlServer4433] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@6a553822: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2024","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2024_pkey","primary":true,"unique":true}],"lastUpdate":1739205571907,"maxPKPos":2,"maxPos":3,"name":"sales_2024","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2024","type":206}). Wait for all previous events to be processed 
[TRACE] 2025-02-11 00:39:33.237 - [变态Schema与变态Database错误][SqlServer4433] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@6a553822: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2024","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2024_pkey","primary":true,"unique":true}],"lastUpdate":1739205571907,"maxPKPos":2,"maxPos":3,"name":"sales_2024","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2024","type":206}) 
[TRACE] 2025-02-11 00:39:33.444 - [变态Schema与变态Database错误][SqlServer4433] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@371d9b1a: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2025","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2025_pkey","primary":true,"unique":true}],"lastUpdate":1739205571991,"maxPKPos":2,"maxPos":3,"name":"sales_2025","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2025","type":206}). Wait for all previous events to be processed 
[TRACE] 2025-02-11 00:39:33.773 - [变态Schema与变态Database错误][SqlServer4433] - Will create sub partition table [sales_2024] to target, master table is: sales 
[TRACE] 2025-02-11 00:39:33.773 - [变态Schema与变态Database错误][PG] - Connector PostgreSQL incremental start succeed, tables: [sales_2025, sales_2024, sales, sales_2023], data change syncing 
[TRACE] 2025-02-11 00:39:34.341 - [变态Schema与变态Database错误][SqlServer4433] - The table sales_2024 has already exist. 
[TRACE] 2025-02-11 00:39:34.342 - [变态Schema与变态Database错误][SqlServer4433] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@371d9b1a: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2025","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2025_pkey","primary":true,"unique":true}],"lastUpdate":1739205571991,"maxPKPos":2,"maxPos":3,"name":"sales_2025","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2025","type":206}) 
[TRACE] 2025-02-11 00:39:34.959 - [变态Schema与变态Database错误][SqlServer4433] - Will create sub partition table [sales_2025] to target, master table is: sales 
[TRACE] 2025-02-11 00:39:35.565 - [变态Schema与变态Database错误][SqlServer4433] - The table sales_2025 has already exist. 
[TRACE] 2025-02-11 00:39:35.819 - [变态Schema与变态Database错误][SqlServer4433] - Table 'sales' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-02-11 00:39:36.221 - [变态Schema与变态Database错误][SqlServer4433] - Table 'sales' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2025-02-11 00:41:41.343 - [变态Schema与变态Database错误][PG] - Found new table(s): ['sales_2026, sales] 
[TRACE] 2025-02-11 00:41:41.807 - [变态Schema与变态Database错误][PG] - Exception skipping - The current exception does not match the skip exception strategy, message: ERROR: syntax error at or near "sales_2026"
  位置：761 
[ERROR] 2025-02-11 00:41:41.828 - [变态Schema与变态Database错误][PG] - ERROR: syntax error at or near "sales_2026"
  位置：761 <-- Error Message -->
ERROR: syntax error at or near "sales_2026"
  位置：761

<-- Simple Stack Trace -->
Caused by: org.postgresql.util.PSQLException: ERROR: syntax error at or near "sales_2026"
  位置：761
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...

<-- Full Stack Trace -->
org.postgresql.util.PSQLException: ERROR: syntax error at or near "sales_2026"
  位置：761
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:782)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:664)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:60)
	at io.tapdata.connector.postgres.partition.PostgresPartitionContext.discoverPartitionInfo(PostgresPartitionContext.java:92)
	at io.tapdata.connector.postgres.PostgresConnector.discoverPartitionInfo(PostgresConnector.java:774)
	at io.tapdata.common.CommonDbConnector.singleThreadDiscoverSchema(CommonDbConnector.java:122)
	at io.tapdata.base.ConnectorBase.lambda$multiThreadDiscoverSchema$1(ConnectorBase.java:423)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.postgresql.util.PSQLException: ERROR: syntax error at or near "sales_2026"
  位置：761
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:329)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:315)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:291)
	at org.postgresql.jdbc.PgStatement.executeQuery(PgStatement.java:243)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:94)
	at io.tapdata.connector.postgres.partition.PostgresPartitionContext.discoverPartitionInfo(PostgresPartitionContext.java:109)
	at io.tapdata.connector.postgres.partition.PostgresPartitionContext.discoverPartitionInfo(PostgresPartitionContext.java:88)
	... 9 more

[TRACE] 2025-02-11 00:41:41.830 - [变态Schema与变态Database错误][PG] - Job suspend in error handle 
[TRACE] 2025-02-11 00:41:41.845 - [变态Schema与变态Database错误][PG] - Load new table(s) schema finished, loaded schema count: 1 
[WARN ] 2025-02-11 00:41:41.846 - [变态Schema与变态Database错误][PG] - It is expected to load 2 new table models, and 1 table models no longer exist and will be ignored. The table name(s) that does not exist: [sales] 
[TRACE] 2025-02-11 00:41:42.049 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] running status set to false 
[TRACE] 2025-02-11 00:41:42.391 - [变态Schema与变态Database错误][PG] - Incremental sync completed 
[TRACE] 2025-02-11 00:41:42.407 - [变态Schema与变态Database错误][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739205566374 
[TRACE] 2025-02-11 00:41:42.407 - [变态Schema与变态Database错误][PG] - PDK connector node released: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739205566374 
[TRACE] 2025-02-11 00:41:42.408 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] schema data cleaned 
[TRACE] 2025-02-11 00:41:42.416 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] monitor closed 
[TRACE] 2025-02-11 00:41:42.420 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] close complete, cost 562 ms 
[TRACE] 2025-02-11 00:41:42.420 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] running status set to false 
[TRACE] 2025-02-11 00:41:42.441 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node stopped: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739205566159 
[TRACE] 2025-02-11 00:41:42.441 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node released: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739205566159 
[TRACE] 2025-02-11 00:41:42.441 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] schema data cleaned 
[TRACE] 2025-02-11 00:41:42.441 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] monitor closed 
[TRACE] 2025-02-11 00:41:42.441 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] close complete, cost 21 ms 
[INFO ] 2025-02-11 00:41:45.167 - [变态Schema与变态Database错误] - Task [变态Schema与变态Database错误] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-02-11 00:41:45.168 - [变态Schema与变态Database错误] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-11 00:41:45.168 - [变态Schema与变态Database错误] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@46bc5597 
[TRACE] 2025-02-11 00:41:45.283 - [变态Schema与变态Database错误] - Stop task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误)  
[TRACE] 2025-02-11 00:41:45.298 - [变态Schema与变态Database错误] - Stopped task aspect(s) 
[TRACE] 2025-02-11 00:41:45.299 - [变态Schema与变态Database错误] - Snapshot order controller have been removed 
[INFO ] 2025-02-11 00:41:45.320 - [变态Schema与变态Database错误] - Task stopped. 
[TRACE] 2025-02-11 00:41:45.320 - [变态Schema与变态Database错误] - Remove memory task client succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 00:41:45.320 - [变态Schema与变态Database错误] - Destroy memory task client cache succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 00:45:02.970 - [变态Schema与变态Database错误] - Start task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误) 
[TRACE] 2025-02-11 00:45:02.970 - [变态Schema与变态Database错误] - Task initialization... 
[INFO ] 2025-02-11 00:45:03.051 - [变态Schema与变态Database错误] - Loading table structure completed 
[TRACE] 2025-02-11 00:45:03.051 - [变态Schema与变态Database错误] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-11 00:45:03.099 - [变态Schema与变态Database错误] - The engine receives 变态Schema与变态Database错误 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-11 00:45:03.099 - [变态Schema与变态Database错误] - Task started 
[TRACE] 2025-02-11 00:45:03.124 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] start preload schema,table counts: 1 
[TRACE] 2025-02-11 00:45:03.124 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] start preload schema,table counts: 1 
[TRACE] 2025-02-11 00:45:03.124 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] preload schema finished, cost 1 ms 
[TRACE] 2025-02-11 00:45:03.124 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] preload schema finished, cost 0 ms 
[INFO ] 2025-02-11 00:45:03.124 - [变态Schema与变态Database错误][PG] - Enable partition table support for source database 
[INFO ] 2025-02-11 00:45:04.783 - [变态Schema与变态Database错误][SqlServer4433] - Sink connector(SqlServer4433) initialization completed 
[TRACE] 2025-02-11 00:45:04.789 - [变态Schema与变态Database错误][SqlServer4433] - Node(SqlServer4433) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-11 00:45:04.789 - [变态Schema与变态Database错误][SqlServer4433] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-11 00:45:04.789 - [变态Schema与变态Database错误][SqlServer4433] - Enable partition table support for target database 
[INFO ] 2025-02-11 00:45:04.789 - [变态Schema与变态Database错误][SqlServer4433] - Apply table structure to target database 
[INFO ] 2025-02-11 00:45:05.111 - [变态Schema与变态Database错误][PG] - Source connector(PG) initialization completed 
[TRACE] 2025-02-11 00:45:05.112 - [变态Schema与变态Database错误][PG] - Source node "PG" read batch size: 100 
[TRACE] 2025-02-11 00:45:05.112 - [变态Schema与变态Database错误][PG] - Source node "PG" event queue capacity: 200 
[TRACE] 2025-02-11 00:45:05.112 - [变态Schema与变态Database错误][PG] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-02-11 00:45:06.277 - [变态Schema与变态Database错误][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-11 00:45:06.377 - [变态Schema与变态Database错误][PG] - new logical replication slot created, slotName:tapdata_cdc_48d44c70_12b3_4d50_862a_f3581f038297 
[INFO ] 2025-02-11 00:45:06.393 - [变态Schema与变态Database错误][PG] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-11 00:45:06.394 - [变态Schema与变态Database错误][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-11 00:45:06.472 - [变态Schema与变态Database错误][PG] - Starting batch read from 1 tables 
[TRACE] 2025-02-11 00:45:06.472 - [变态Schema与变态Database错误][PG] - Initial sync started 
[INFO ] 2025-02-11 00:45:06.474 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sa les 
[TRACE] 2025-02-11 00:45:06.474 - [变态Schema与变态Database错误][PG] - Table sa les is going to be initial synced 
[TRACE] 2025-02-11 00:45:06.677 - [变态Schema与变态Database错误][PG] - Query snapshot row size completed: PG(ea9c0610-bb94-4c0f-90f7-644daf40c5d6) 
[INFO ] 2025-02-11 00:45:06.678 - [变态Schema与变态Database错误][PG] - Table sa les has been completed batch read 
[TRACE] 2025-02-11 00:45:06.679 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[INFO ] 2025-02-11 00:45:06.679 - [变态Schema与变态Database错误][PG] - Batch read completed. 
[TRACE] 2025-02-11 00:45:06.679 - [变态Schema与变态Database错误][PG] - Incremental sync starting... 
[TRACE] 2025-02-11 00:45:06.679 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[TRACE] 2025-02-11 00:45:06.680 - [变态Schema与变态Database错误][PG] - Starting stream read, table list: [sa les], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-02-11 00:45:06.683 - [变态Schema与变态Database错误][PG] - Starting incremental sync using database log parser 
[WARN ] 2025-02-11 00:45:06.684 - [变态Schema与变态Database错误][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-11 00:45:06.889 - [变态Schema与变态Database错误][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_48d44c70_12b3_4d50_862a_f3581f038297 
[WARN ] 2025-02-11 00:45:07.504 - [变态Schema与变态Database错误][SqlServer4433] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aa2d12d303ec209a1818ab, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[ea9c0610-bb94-4c0f-90f7-644daf40c5d6], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-11 00:45:08.271 - [变态Schema与变态Database错误][PG] - Connector PostgreSQL incremental start succeed, tables: [sa les], data change syncing 
[TRACE] 2025-02-11 01:08:09.372 - [变态Schema与变态Database错误][PG] - Incremental sync completed 
[TRACE] 2025-02-11 01:08:09.589 - [变态Schema与变态Database错误][PG] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.net.SocketException: Broken pipe (Write failed) 
[ERROR] 2025-02-11 01:08:09.671 - [变态Schema与变态Database错误][PG] - java.net.SocketException: Broken pipe (Write failed) <-- Error Message -->
java.net.SocketException: Broken pipe (Write failed)

<-- Simple Stack Trace -->
Caused by: java.net.SocketException: Broken pipe (Write failed)
	java.net.SocketOutputStream.socketWrite0(Native Method)
	java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111)
	java.net.SocketOutputStream.write(SocketOutputStream.java:155)
	java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	...

<-- Full Stack Trace -->
java.net.SocketException: Broken pipe (Write failed)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:923)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:913)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:801)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:292)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Broken pipe (Write failed)
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:155)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at org.postgresql.core.PGStream.flush(PGStream.java:709)
	at org.postgresql.core.v3.QueryExecutorImpl.flushCopy(QueryExecutorImpl.java:1141)
	at org.postgresql.core.v3.CopyDualImpl.flushCopy(CopyDualImpl.java:30)
	at org.postgresql.core.v3.replication.V3PGReplicationStream.updateStatusInternal(V3PGReplicationStream.java:195)
	at org.postgresql.core.v3.replication.V3PGReplicationStream.forceUpdateStatus(V3PGReplicationStream.java:113)
	at io.debezium.connector.postgresql.connection.PostgresReplicationConnection$1.doFlushLsn(PostgresReplicationConnection.java:505)
	at io.debezium.connector.postgresql.connection.PostgresReplicationConnection$1.flushLsn(PostgresReplicationConnection.java:498)
	at io.debezium.connector.postgresql.PostgresStreamingChangeEventSource.commitOffset(PostgresStreamingChangeEventSource.java:346)
	at io.debezium.pipeline.ChangeEventSourceCoordinator.commitOffset(ChangeEventSourceCoordinator.java:158)
	at io.debezium.connector.common.BaseSourceTask.commit(BaseSourceTask.java:281)
	at io.debezium.embedded.EmbeddedEngine.commitOffsets(EmbeddedEngine.java:1003)
	at io.debezium.embedded.EmbeddedEngine.maybeFlush(EmbeddedEngine.java:977)
	at io.debezium.embedded.EmbeddedEngine$4.markBatchFinished(EmbeddedEngine.java:916)
	at io.tapdata.connector.postgres.cdc.PostgresCdcRunner.consumeRecords(PostgresCdcRunner.java:283)
	at io.debezium.embedded.EmbeddedEngine.run(EmbeddedEngine.java:821)
	at io.tapdata.connector.postgres.cdc.DebeziumCdcRunner.startCdcRunner(DebeziumCdcRunner.java:44)
	at io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:470)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:901)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-11 01:08:09.716 - [变态Schema与变态Database错误][PG] - Job suspend in error handle 
[TRACE] 2025-02-11 01:08:09.918 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] running status set to false 
[TRACE] 2025-02-11 01:08:09.991 - [变态Schema与变态Database错误][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739205904307 
[TRACE] 2025-02-11 01:08:09.992 - [变态Schema与变态Database错误][PG] - PDK connector node released: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739205904307 
[TRACE] 2025-02-11 01:08:09.992 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] schema data cleaned 
[TRACE] 2025-02-11 01:08:09.993 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] monitor closed 
[TRACE] 2025-02-11 01:08:09.993 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] close complete, cost 262 ms 
[TRACE] 2025-02-11 01:08:09.993 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] running status set to false 
[TRACE] 2025-02-11 01:08:10.039 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node stopped: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739205904085 
[TRACE] 2025-02-11 01:08:10.049 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node released: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739205904085 
[TRACE] 2025-02-11 01:08:10.049 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] schema data cleaned 
[TRACE] 2025-02-11 01:08:10.049 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] monitor closed 
[TRACE] 2025-02-11 01:08:10.049 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] close complete, cost 46 ms 
[INFO ] 2025-02-11 01:08:14.427 - [变态Schema与变态Database错误] - Task [变态Schema与变态Database错误] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-02-11 01:08:14.432 - [变态Schema与变态Database错误] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-11 01:08:14.432 - [变态Schema与变态Database错误] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2a73bf15 
[TRACE] 2025-02-11 01:08:14.570 - [变态Schema与变态Database错误] - Stop task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误)  
[TRACE] 2025-02-11 01:08:14.570 - [变态Schema与变态Database错误] - Stopped task aspect(s) 
[TRACE] 2025-02-11 01:08:14.570 - [变态Schema与变态Database错误] - Snapshot order controller have been removed 
[INFO ] 2025-02-11 01:08:14.570 - [变态Schema与变态Database错误] - Task stopped. 
[TRACE] 2025-02-11 01:08:14.591 - [变态Schema与变态Database错误] - Remove memory task client succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 01:08:14.595 - [变态Schema与变态Database错误] - Destroy memory task client cache succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 12:21:20.140 - [变态Schema与变态Database错误] - Task initialization... 
[TRACE] 2025-02-11 12:21:20.147 - [变态Schema与变态Database错误] - Start task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误) 
[INFO ] 2025-02-11 12:21:20.311 - [变态Schema与变态Database错误] - Loading table structure completed 
[TRACE] 2025-02-11 12:21:20.382 - [变态Schema与变态Database错误] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-11 12:21:20.384 - [变态Schema与变态Database错误] - The engine receives 变态Schema与变态Database错误 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-11 12:21:20.452 - [变态Schema与变态Database错误] - Task started 
[TRACE] 2025-02-11 12:21:20.453 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] start preload schema,table counts: 1 
[TRACE] 2025-02-11 12:21:20.453 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] start preload schema,table counts: 1 
[TRACE] 2025-02-11 12:21:20.453 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] preload schema finished, cost 0 ms 
[TRACE] 2025-02-11 12:21:20.458 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] preload schema finished, cost 0 ms 
[INFO ] 2025-02-11 12:21:20.458 - [变态Schema与变态Database错误][PG] - Enable partition table support for source database 
[INFO ] 2025-02-11 12:21:21.389 - [变态Schema与变态Database错误][PG] - Source connector(PG) initialization completed 
[TRACE] 2025-02-11 12:21:21.390 - [变态Schema与变态Database错误][PG] - Source node "PG" read batch size: 100 
[TRACE] 2025-02-11 12:21:21.390 - [变态Schema与变态Database错误][PG] - Source node "PG" event queue capacity: 200 
[TRACE] 2025-02-11 12:21:21.393 - [变态Schema与变态Database错误][PG] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-11 12:21:21.495 - [变态Schema与变态Database错误][SqlServer4433] - Sink connector(SqlServer4433) initialization completed 
[TRACE] 2025-02-11 12:21:21.497 - [变态Schema与变态Database错误][SqlServer4433] - Node(SqlServer4433) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-11 12:21:21.497 - [变态Schema与变态Database错误][SqlServer4433] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-11 12:21:21.497 - [变态Schema与变态Database错误][SqlServer4433] - Enable partition table support for target database 
[INFO ] 2025-02-11 12:21:21.497 - [变态Schema与变态Database错误][SqlServer4433] - Apply table structure to target database 
[WARN ] 2025-02-11 12:21:21.563 - [变态Schema与变态Database错误][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-11 12:21:21.564 - [变态Schema与变态Database错误][PG] - new logical replication slot created, slotName:tapdata_cdc_44bf3125_664e_475b_8408_2790051ce803 
[INFO ] 2025-02-11 12:21:21.570 - [变态Schema与变态Database错误][PG] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-11 12:21:21.574 - [变态Schema与变态Database错误][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-11 12:21:21.630 - [变态Schema与变态Database错误][PG] - Starting batch read from 1 tables 
[TRACE] 2025-02-11 12:21:21.630 - [变态Schema与变态Database错误][PG] - Initial sync started 
[INFO ] 2025-02-11 12:21:21.630 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sa les 
[TRACE] 2025-02-11 12:21:21.652 - [变态Schema与变态Database错误][PG] - Table sa les is going to be initial synced 
[WARN ] 2025-02-11 12:21:21.652 - [变态Schema与变态Database错误][PG] - Query snapshot row size failed: java.lang.RuntimeException: io.tapdata.exception.NodeException: Count sa les failed: ERROR: relation "public.sa les" does not exist
  位置：22
java.util.concurrent.CompletionException: java.lang.RuntimeException: io.tapdata.exception.NodeException: Count sa les failed: ERROR: relation "public.sa les" does not exist
  位置：22
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:273)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:280)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1643)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Count sa les failed: ERROR: relation "public.sa les" does not exist
  位置：22
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doCountSynchronously(HazelcastSourcePdkBaseNode.java:1569)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$asyncCountTable$47(HazelcastSourcePdkBaseNode.java:1615)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640)
	... 4 more
Caused by: io.tapdata.exception.NodeException: Count sa les failed: ERROR: relation "public.sa les" does not exist
  位置：22
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doCountSynchronously$46(HazelcastSourcePdkBaseNode.java:1572)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 8 more
Caused by: io.tapdata.exception.NodeException: Count sa les failed: ERROR: relation "public.sa les" does not exist
  位置：22
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$null$45(HazelcastSourcePdkBaseNode.java:1590)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 14 more
Caused by: org.postgresql.util.PSQLException: ERROR: relation "public.sa les" does not exist
  位置：22
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:329)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:315)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:291)
	at org.postgresql.jdbc.PgStatement.executeQuery(PgStatement.java:243)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:77)
	at io.tapdata.common.CommonDbConnector.batchCount(CommonDbConnector.java:372)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$null$45(HazelcastSourcePdkBaseNode.java:1580)
	... 15 more
 
[TRACE] 2025-02-11 12:21:21.662 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[TRACE] 2025-02-11 12:21:21.663 - [变态Schema与变态Database错误][PG] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, org.postgresql.util.PSQLException: ERROR: relation "public.sa les" does not exist
  位置：28 
[ERROR] 2025-02-11 12:21:21.710 - [变态Schema与变态Database错误][PG] - Unknown PDK exception occur, org.postgresql.util.PSQLException: ERROR: relation "public.sa les" does not exist
  位置：28 <-- Error Message -->
Unknown PDK exception occur, org.postgresql.util.PSQLException: ERROR: relation "public.sa les" does not exist
  位置：28

<-- Simple Stack Trace -->
Caused by: org.postgresql.util.PSQLException: ERROR: relation "public.sa les" does not exist
  位置：28
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...

<-- Full Stack Trace -->
org.postgresql.util.PSQLException: ERROR: relation "public.sa les" does not exist
  位置：28
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:498)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:490)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:412)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:356)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:269)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.postgresql.util.PSQLException: ERROR: relation "public.sa les" does not exist
  位置：28
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:329)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:315)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:291)
	at org.postgresql.jdbc.PgStatement.executeQuery(PgStatement.java:243)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:94)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutHashSplit(CommonDbConnector.java:585)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:579)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:577)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 19 more

[TRACE] 2025-02-11 12:21:21.711 - [变态Schema与变态Database错误][PG] - Job suspend in error handle 
[TRACE] 2025-02-11 12:21:21.729 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] running status set to false 
[TRACE] 2025-02-11 12:21:21.730 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] running status set to false 
[TRACE] 2025-02-11 12:21:21.767 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node stopped: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739247681336 
[TRACE] 2025-02-11 12:21:21.768 - [变态Schema与变态Database错误][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739247681097 
[TRACE] 2025-02-11 12:21:21.768 - [变态Schema与变态Database错误][PG] - PDK connector node released: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739247681097 
[TRACE] 2025-02-11 12:21:21.768 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node released: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739247681336 
[TRACE] 2025-02-11 12:21:21.772 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] schema data cleaned 
[TRACE] 2025-02-11 12:21:21.773 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] schema data cleaned 
[TRACE] 2025-02-11 12:21:21.773 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] monitor closed 
[TRACE] 2025-02-11 12:21:21.773 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] monitor closed 
[TRACE] 2025-02-11 12:21:21.786 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] close complete, cost 47 ms 
[TRACE] 2025-02-11 12:21:21.791 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] close complete, cost 54 ms 
[INFO ] 2025-02-11 12:21:26.106 - [变态Schema与变态Database错误] - Task [变态Schema与变态Database错误] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-02-11 12:21:26.106 - [变态Schema与变态Database错误] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-11 12:21:26.108 - [变态Schema与变态Database错误] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@52857927 
[TRACE] 2025-02-11 12:21:26.108 - [变态Schema与变态Database错误] - Stop task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误)  
[TRACE] 2025-02-11 12:21:26.235 - [变态Schema与变态Database错误] - Stopped task aspect(s) 
[TRACE] 2025-02-11 12:21:26.235 - [变态Schema与变态Database错误] - Snapshot order controller have been removed 
[INFO ] 2025-02-11 12:21:26.268 - [变态Schema与变态Database错误] - Task stopped. 
[TRACE] 2025-02-11 12:21:26.269 - [变态Schema与变态Database错误] - Remove memory task client succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 12:21:26.270 - [变态Schema与变态Database错误] - Destroy memory task client cache succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 12:21:55.054 - [变态Schema与变态Database错误] - Task initialization... 
[TRACE] 2025-02-11 12:21:55.055 - [变态Schema与变态Database错误] - Start task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误) 
[INFO ] 2025-02-11 12:21:55.319 - [变态Schema与变态Database错误] - Loading table structure completed 
[TRACE] 2025-02-11 12:21:55.368 - [变态Schema与变态Database错误] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-11 12:21:55.368 - [变态Schema与变态Database错误] - The engine receives 变态Schema与变态Database错误 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-11 12:21:55.418 - [变态Schema与变态Database错误] - Task started 
[TRACE] 2025-02-11 12:21:55.418 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] start preload schema,table counts: 2 
[TRACE] 2025-02-11 12:21:55.418 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] start preload schema,table counts: 2 
[TRACE] 2025-02-11 12:21:55.418 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] preload schema finished, cost 0 ms 
[TRACE] 2025-02-11 12:21:55.418 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] preload schema finished, cost 0 ms 
[INFO ] 2025-02-11 12:21:55.620 - [变态Schema与变态Database错误][PG] - Enable partition table support for source database 
[INFO ] 2025-02-11 12:21:56.300 - [变态Schema与变态Database错误][SqlServer4433] - Sink connector(SqlServer4433) initialization completed 
[TRACE] 2025-02-11 12:21:56.308 - [变态Schema与变态Database错误][SqlServer4433] - Node(SqlServer4433) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-11 12:21:56.308 - [变态Schema与变态Database错误][SqlServer4433] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-11 12:21:56.309 - [变态Schema与变态Database错误][SqlServer4433] - Enable partition table support for target database 
[INFO ] 2025-02-11 12:21:56.309 - [变态Schema与变态Database错误][SqlServer4433] - Apply table structure to target database 
[TRACE] 2025-02-11 12:21:56.515 - [变态Schema与变态Database错误][SqlServer4433] - Will create master partition table [salesb] to target, init sub partition list: [salesb_100, salesb_200, salesb_300] 
[INFO ] 2025-02-11 12:21:56.694 - [变态Schema与变态Database错误][PG] - Source connector(PG) initialization completed 
[TRACE] 2025-02-11 12:21:56.699 - [变态Schema与变态Database错误][PG] - Source node "PG" read batch size: 100 
[TRACE] 2025-02-11 12:21:56.707 - [变态Schema与变态Database错误][PG] - Source node "PG" event queue capacity: 200 
[TRACE] 2025-02-11 12:21:56.813 - [变态Schema与变态Database错误][PG] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-11 12:21:56.814 - [变态Schema与变态Database错误][SqlServer4433] - Will create master partition table [sales] to target, init sub partition list: [sales_2023, sales_2024, sales_2026, sales_2025] 
[WARN ] 2025-02-11 12:21:56.930 - [变态Schema与变态Database错误][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-11 12:21:56.933 - [变态Schema与变态Database错误][PG] - new logical replication slot created, slotName:tapdata_cdc_06a70969_e724_4d70_ae38_ed39a1fb1a51 
[INFO ] 2025-02-11 12:21:56.948 - [变态Schema与变态Database错误][PG] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-11 12:21:56.953 - [变态Schema与变态Database错误][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-11 12:21:56.954 - [变态Schema与变态Database错误] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2025-02-11 12:21:56.992 - [变态Schema与变态Database错误][PG] - Found new table(s): [sales_2027, sales] 
[INFO ] 2025-02-11 12:21:57.043 - [变态Schema与变态Database错误][PG] - Starting batch read from 2 tables 
[TRACE] 2025-02-11 12:21:57.043 - [变态Schema与变态Database错误][PG] - Initial sync started 
[TRACE] 2025-02-11 12:21:57.137 - [变态Schema与变态Database错误][PG] - Load new table(s) schema finished, loaded schema count: 1 
[TRACE] 2025-02-11 12:21:57.137 - [变态Schema与变态Database错误][PG] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@70af51af: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2027","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2027_pkey","primary":true,"unique":true}],"lastUpdate":1739247717135,"maxPKPos":2,"maxPos":3,"name":"sales_2027","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"fixed":true,"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2026-01-01') TO ('2026-12-31')","tableName":"sales_2026","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2026-01-01'","value":"'2026-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2026-12-31'","value":"'2026-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2027-01-01') TO ('2027-12-31')","tableName":"sales_2027","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2027-01-01'","value":"'2027-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2027-12-31'","value":"'2027-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2027","type":206} 
[TRACE] 2025-02-11 12:21:57.152 - [变态Schema与变态Database错误][PG] - Sync sub table's [sales_2027] create table ddl, will add update master table [sales] metadata 
[TRACE] 2025-02-11 12:21:57.152 - [变态Schema与变态Database错误][PG] - Query snapshot row size completed: PG(ea9c0610-bb94-4c0f-90f7-644daf40c5d6) 
[TRACE] 2025-02-11 12:21:57.152 - [变态Schema与变态Database错误][PG] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_sales_2027_67a9e6877567c16109c866de_67a9ffd97567c16109c8678e 
[TRACE] 2025-02-11 12:21:57.228 - [变态Schema与变态Database错误][PG] - Create new table schema transform finished: TapTable id sales_2027 name sales_2027 storageEngine null charset null number of fields 3 
[INFO ] 2025-02-11 12:21:57.228 - [变态Schema与变态Database错误][PG] - Starting batch read from table: salesb 
[TRACE] 2025-02-11 12:21:57.228 - [变态Schema与变态Database错误][PG] - Table salesb is going to be initial synced 
[INFO ] 2025-02-11 12:21:57.242 - [变态Schema与变态Database错误][PG] - Table salesb has been completed batch read 
[INFO ] 2025-02-11 12:21:57.242 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sales 
[TRACE] 2025-02-11 12:21:57.254 - [变态Schema与变态Database错误][PG] - Table sales is going to be initial synced 
[INFO ] 2025-02-11 12:21:57.255 - [变态Schema与变态Database错误][PG] - Table sales has been completed batch read 
[INFO ] 2025-02-11 12:21:57.255 - [变态Schema与变态Database错误][PG] - Starting batch read from table: sales_2027 
[TRACE] 2025-02-11 12:21:57.255 - [变态Schema与变态Database错误][PG] - Table sales_2027 is going to be initial synced 
[INFO ] 2025-02-11 12:21:57.267 - [变态Schema与变态Database错误][PG] - Table sales_2027 has been completed batch read 
[TRACE] 2025-02-11 12:21:57.267 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[INFO ] 2025-02-11 12:21:57.267 - [变态Schema与变态Database错误][PG] - Batch read completed. 
[TRACE] 2025-02-11 12:21:57.267 - [变态Schema与变态Database错误][PG] - Incremental sync starting... 
[TRACE] 2025-02-11 12:21:57.268 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[TRACE] 2025-02-11 12:21:57.268 - [变态Schema与变态Database错误][PG] - Starting stream read, table list: [salesb, sales_2027, sales], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-02-11 12:21:57.268 - [变态Schema与变态Database错误][PG] - Starting incremental sync using database log parser 
[WARN ] 2025-02-11 12:21:57.268 - [变态Schema与变态Database错误][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-11 12:21:57.313 - [变态Schema与变态Database错误][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_06a70969_e724_4d70_ae38_ed39a1fb1a51 
[TRACE] 2025-02-11 12:21:57.315 - [变态Schema与变态Database错误][SqlServer4433] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@70af51af: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2027","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2027_pkey","primary":true,"unique":true}],"lastUpdate":1739247717135,"maxPKPos":2,"maxPos":3,"name":"sales_2027","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2026-01-01') TO ('2026-12-31')","tableName":"sales_2026","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2026-01-01'","value":"'2026-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2026-12-31'","value":"'2026-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2027-01-01') TO ('2027-12-31')","tableName":"sales_2027","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2027-01-01'","value":"'2027-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2027-12-31'","value":"'2027-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2027","type":206}). Wait for all previous events to be processed 
[TRACE] 2025-02-11 12:21:57.725 - [变态Schema与变态Database错误][PG] - Connector PostgreSQL incremental start succeed, tables: [salesb, sales_2027, sales, salesb_100, salesb_200, salesb_300, sales_2023, sales_2024, sales_2026, sales_2025], data change syncing 
[TRACE] 2025-02-11 12:21:57.822 - [变态Schema与变态Database错误][SqlServer4433] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@70af51af: {"partitionMasterTableId":"sales","table":{"comment":"","id":"sales_2027","indexList":[{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"name":"sales_2027_pkey","primary":true,"unique":true}],"lastUpdate":1739247717135,"maxPKPos":2,"maxPos":3,"name":"sales_2027","nameFieldMap":{"key":{"autoInc":false,"dataType":"integer","name":"key","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"sale_date":{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"date","tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false},"amount":{"autoInc":false,"dataType":"numeric(10,2)","name":"amount","nullable":false,"partitionKey":false,"pos":3,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"numeric","tapType":{"maxValue":9999999999,"minValue":-9999999999,"precision":10,"scale":2,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"key"},{"fieldAsc":true,"name":"sale_date"}],"indexMap":{"key":{"fieldAsc":true,"name":"key"},"sale_date":{"fieldAsc":true,"name":"sale_date"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (sale_date)","partitionFields":[{"autoInc":false,"dataType":"date","name":"sale_date","nullable":false,"partitionKey":false,"pos":2,"primaryKey":true,"primaryKeyPos":2,"tapType":{"max":"9999-12-31T00:00:00Z","min":"0001-01-01T00:00:00Z","type":11},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2023-01-01') TO ('2023-12-31')","tableName":"sales_2023","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2023-01-01'","value":"'2023-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2023-12-31'","value":"'2023-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-01-01') TO ('2024-12-31')","tableName":"sales_2024","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-01-01'","value":"'2024-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-31'","value":"'2024-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2025-01-01') TO ('2025-12-31')","tableName":"sales_2025","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2025-01-01'","value":"'2025-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2025-12-31'","value":"'2025-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2026-01-01') TO ('2026-12-31')","tableName":"sales_2026","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2026-01-01'","value":"'2026-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2026-12-31'","value":"'2026-12-31'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2027-01-01') TO ('2027-12-31')","tableName":"sales_2027","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2027-01-01'","value":"'2027-01-01'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2027-12-31'","value":"'2027-12-31'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"sales"},"tableId":"sales_2027","type":206}) 
[TRACE] 2025-02-11 12:21:58.431 - [变态Schema与变态Database错误][SqlServer4433] - Will create sub partition table [sales_2027] to target, master table is: sales 
[TRACE] 2025-02-11 12:21:58.635 - [变态Schema与变态Database错误][SqlServer4433] - The table sales_2027 has already exist. 
[TRACE] 2025-02-11 12:23:42.729 - [变态Schema与变态Database错误][SqlServer4433] - Table 'sales' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-02-11 12:36:54.750 - [变态Schema与变态Database错误][PG] - Incremental sync completed 
[TRACE] 2025-02-11 12:36:54.754 - [变态Schema与变态Database错误][PG] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.net.SocketException: Broken pipe (Write failed) 
[ERROR] 2025-02-11 12:36:54.790 - [变态Schema与变态Database错误][PG] - java.net.SocketException: Broken pipe (Write failed) <-- Error Message -->
java.net.SocketException: Broken pipe (Write failed)

<-- Simple Stack Trace -->
Caused by: java.net.SocketException: Broken pipe (Write failed)
	java.net.SocketOutputStream.socketWrite0(Native Method)
	java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111)
	java.net.SocketOutputStream.write(SocketOutputStream.java:155)
	java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	...

<-- Full Stack Trace -->
java.net.SocketException: Broken pipe (Write failed)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:923)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:913)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:801)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:292)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Broken pipe (Write failed)
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:155)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at org.postgresql.core.PGStream.flush(PGStream.java:709)
	at org.postgresql.core.v3.QueryExecutorImpl.flushCopy(QueryExecutorImpl.java:1141)
	at org.postgresql.core.v3.CopyDualImpl.flushCopy(CopyDualImpl.java:30)
	at org.postgresql.core.v3.replication.V3PGReplicationStream.updateStatusInternal(V3PGReplicationStream.java:195)
	at org.postgresql.core.v3.replication.V3PGReplicationStream.forceUpdateStatus(V3PGReplicationStream.java:113)
	at io.debezium.connector.postgresql.connection.PostgresReplicationConnection$1.doFlushLsn(PostgresReplicationConnection.java:505)
	at io.debezium.connector.postgresql.connection.PostgresReplicationConnection$1.flushLsn(PostgresReplicationConnection.java:498)
	at io.debezium.connector.postgresql.PostgresStreamingChangeEventSource.commitOffset(PostgresStreamingChangeEventSource.java:346)
	at io.debezium.pipeline.ChangeEventSourceCoordinator.commitOffset(ChangeEventSourceCoordinator.java:158)
	at io.debezium.connector.common.BaseSourceTask.commit(BaseSourceTask.java:281)
	at io.debezium.embedded.EmbeddedEngine.commitOffsets(EmbeddedEngine.java:1003)
	at io.debezium.embedded.EmbeddedEngine.maybeFlush(EmbeddedEngine.java:977)
	at io.debezium.embedded.EmbeddedEngine$4.markBatchFinished(EmbeddedEngine.java:916)
	at io.tapdata.connector.postgres.cdc.PostgresCdcRunner.consumeRecords(PostgresCdcRunner.java:283)
	at io.debezium.embedded.EmbeddedEngine.run(EmbeddedEngine.java:821)
	at io.tapdata.connector.postgres.cdc.DebeziumCdcRunner.startCdcRunner(DebeziumCdcRunner.java:44)
	at io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:470)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:901)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-11 12:36:54.795 - [变态Schema与变态Database错误][PG] - Job suspend in error handle 
[TRACE] 2025-02-11 12:36:54.872 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] running status set to false 
[TRACE] 2025-02-11 12:36:54.888 - [变态Schema与变态Database错误][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739247716403 
[TRACE] 2025-02-11 12:36:54.888 - [变态Schema与变态Database错误][PG] - PDK connector node released: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739247716403 
[TRACE] 2025-02-11 12:36:54.889 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] schema data cleaned 
[TRACE] 2025-02-11 12:36:54.889 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] monitor closed 
[TRACE] 2025-02-11 12:36:54.893 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] close complete, cost 30 ms 
[TRACE] 2025-02-11 12:36:54.893 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] running status set to false 
[TRACE] 2025-02-11 12:36:54.910 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node stopped: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739247716147 
[TRACE] 2025-02-11 12:36:54.910 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node released: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739247716147 
[TRACE] 2025-02-11 12:36:54.911 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] schema data cleaned 
[TRACE] 2025-02-11 12:36:54.911 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] monitor closed 
[TRACE] 2025-02-11 12:36:55.121 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] close complete, cost 19 ms 
[TRACE] 2025-02-11 13:03:46.528 - [变态Schema与变态Database错误] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-11 13:03:46.529 - [变态Schema与变态Database错误] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6a9142dc 
[TRACE] 2025-02-11 13:03:46.529 - [变态Schema与变态Database错误] - Stop task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误)  
[TRACE] 2025-02-11 13:03:46.661 - [变态Schema与变态Database错误] - Stopped task aspect(s) 
[TRACE] 2025-02-11 13:03:46.661 - [变态Schema与变态Database错误] - Snapshot order controller have been removed 
[INFO ] 2025-02-11 13:03:46.661 - [变态Schema与变态Database错误] - Task stopped. 
[TRACE] 2025-02-11 13:03:46.689 - [变态Schema与变态Database错误] - Remove memory task client succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 13:03:46.691 - [变态Schema与变态Database错误] - Destroy memory task client cache succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 13:11:01.459 - [变态Schema与变态Database错误] - Task initialization... 
[TRACE] 2025-02-11 13:11:01.460 - [变态Schema与变态Database错误] - Start task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误) 
[INFO ] 2025-02-11 13:11:01.651 - [变态Schema与变态Database错误] - Loading table structure completed 
[TRACE] 2025-02-11 13:11:01.652 - [变态Schema与变态Database错误] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-11 13:11:01.713 - [变态Schema与变态Database错误] - The engine receives 变态Schema与变态Database错误 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-11 13:11:01.714 - [变态Schema与变态Database错误] - Task started 
[TRACE] 2025-02-11 13:11:01.752 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] start preload schema,table counts: 3 
[TRACE] 2025-02-11 13:11:01.752 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] start preload schema,table counts: 3 
[TRACE] 2025-02-11 13:11:01.752 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] preload schema finished, cost 0 ms 
[TRACE] 2025-02-11 13:11:01.752 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] preload schema finished, cost 0 ms 
[INFO ] 2025-02-11 13:11:01.752 - [变态Schema与变态Database错误][PG] - Enable partition table support for source database 
[INFO ] 2025-02-11 13:11:02.666 - [变态Schema与变态Database错误][PG] - Source connector(PG) initialization completed 
[TRACE] 2025-02-11 13:11:02.666 - [变态Schema与变态Database错误][PG] - Source node "PG" read batch size: 100 
[TRACE] 2025-02-11 13:11:02.666 - [变态Schema与变态Database错误][PG] - Source node "PG" event queue capacity: 200 
[TRACE] 2025-02-11 13:11:02.773 - [变态Schema与变态Database错误][PG] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-11 13:11:02.774 - [变态Schema与变态Database错误][SqlServer4433] - Sink connector(SqlServer4433) initialization completed 
[TRACE] 2025-02-11 13:11:02.774 - [变态Schema与变态Database错误][SqlServer4433] - Node(SqlServer4433) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-11 13:11:02.774 - [变态Schema与变态Database错误][SqlServer4433] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-11 13:11:02.776 - [变态Schema与变态Database错误][SqlServer4433] - Enable partition table support for target database 
[INFO ] 2025-02-11 13:11:02.776 - [变态Schema与变态Database错误][SqlServer4433] - Apply table structure to target database 
[WARN ] 2025-02-11 13:11:02.907 - [变态Schema与变态Database错误][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-11 13:11:02.909 - [变态Schema与变态Database错误][PG] - new logical replication slot created, slotName:tapdata_cdc_c3ed75b3_94a5_4953_a9a2_707eacd72832 
[INFO ] 2025-02-11 13:11:02.925 - [变态Schema与变态Database错误][PG] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-11 13:11:02.935 - [变态Schema与变态Database错误][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-11 13:11:02.935 - [变态Schema与变态Database错误] - Dynamic table monitor started, interval: 60 seconds 
[TRACE] 2025-02-11 13:11:03.000 - [变态Schema与变态Database错误][SqlServer4433] - Will create master partition table [salesb] to target, init sub partition list: [salesb_200, salesb_300, salesb_100] 
[INFO ] 2025-02-11 13:11:03.001 - [变态Schema与变态Database错误][PG] - Starting batch read from 3 tables 
[TRACE] 2025-02-11 13:11:03.016 - [变态Schema与变态Database错误][PG] - Initial sync started 
[INFO ] 2025-02-11 13:11:03.018 - [变态Schema与变态Database错误][PG] - Starting batch read from table: salesb 
[TRACE] 2025-02-11 13:11:03.018 - [变态Schema与变态Database错误][PG] - Table salesb is going to be initial synced 
[INFO ] 2025-02-11 13:11:03.044 - [变态Schema与变态Database错误][PG] - Table salesb has been completed batch read 
[INFO ] 2025-02-11 13:11:03.045 - [变态Schema与变态Database错误][PG] - Starting batch read from table: salesvvvv 
[TRACE] 2025-02-11 13:11:03.045 - [变态Schema与变态Database错误][PG] - Table salesvvvv is going to be initial synced 
[TRACE] 2025-02-11 13:11:03.066 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[TRACE] 2025-02-11 13:11:03.066 - [变态Schema与变态Database错误][PG] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, org.postgresql.util.PSQLException: Unterminated identifier started at position 47 in SQL SELECT "key","amount"" FROM "public"."salesvvvv". Expected " char 
[ERROR] 2025-02-11 13:11:03.089 - [变态Schema与变态Database错误][PG] - Unknown PDK exception occur, org.postgresql.util.PSQLException: Unterminated identifier started at position 47 in SQL SELECT "key","amount"" FROM "public"."salesvvvv". Expected " char <-- Error Message -->
Unknown PDK exception occur, org.postgresql.util.PSQLException: Unterminated identifier started at position 47 in SQL SELECT "key","amount"" FROM "public"."salesvvvv". Expected " char

<-- Simple Stack Trace -->
Caused by: org.postgresql.util.PSQLException: Unterminated identifier started at position 47 in SQL SELECT "key","amount"" FROM "public"."salesvvvv". Expected " char
	org.postgresql.core.Parser.checkParsePosition(Parser.java:1380)
	org.postgresql.core.Parser.parseSql(Parser.java:1295)
	org.postgresql.core.Parser.replaceProcessing(Parser.java:1231)
	org.postgresql.core.CachedQueryCreateAction.create(CachedQueryCreateAction.java:43)
	org.postgresql.core.QueryExecutorBase.createQueryByKey(QueryExecutorBase.java:340)
	...

<-- Full Stack Trace -->
org.postgresql.util.PSQLException: Unterminated identifier started at position 47 in SQL SELECT "key","amount"" FROM "public"."salesvvvv". Expected " char
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:498)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:490)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:412)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:356)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:269)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.postgresql.util.PSQLException: Unterminated identifier started at position 47 in SQL SELECT "key","amount"" FROM "public"."salesvvvv". Expected " char
	at org.postgresql.core.Parser.checkParsePosition(Parser.java:1380)
	at org.postgresql.core.Parser.parseSql(Parser.java:1295)
	at org.postgresql.core.Parser.replaceProcessing(Parser.java:1231)
	at org.postgresql.core.CachedQueryCreateAction.create(CachedQueryCreateAction.java:43)
	at org.postgresql.core.QueryExecutorBase.createQueryByKey(QueryExecutorBase.java:340)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:307)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:291)
	at org.postgresql.jdbc.PgStatement.executeQuery(PgStatement.java:243)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:94)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutHashSplit(CommonDbConnector.java:585)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:579)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:577)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 19 more

[TRACE] 2025-02-11 13:11:03.090 - [变态Schema与变态Database错误][PG] - Job suspend in error handle 
[TRACE] 2025-02-11 13:11:03.090 - [变态Schema与变态Database错误][PG] - Query snapshot row size completed: PG(ea9c0610-bb94-4c0f-90f7-644daf40c5d6) 
[WARN ] 2025-02-11 13:11:03.296 - [变态Schema与变态Database错误][SqlServer4433] - Target can not support to create invalid partition master table [salesvvvv] be ignore Unable to get partition fields from RANGE ("amount""") (partition field setting sql is invalid or not be support), only support simple partition stage,such as: (field_name) or  (field1,field2,...), may create as a normal table 
[TRACE] 2025-02-11 13:11:03.388 - [变态Schema与变态Database错误][SqlServer4433] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, when operate table: salesvvvv, java.lang.RuntimeException: Append create table sql(s) failed: Failed to find any partition fields, sub partition table: salesvvvv_100 | Table: salesvvvv 
[ERROR] 2025-02-11 13:11:03.424 - [变态Schema与变态Database错误][SqlServer4433] - Unknown PDK exception occur, when operate table: salesvvvv, java.lang.RuntimeException: Append create table sql(s) failed: Failed to find any partition fields, sub partition table: salesvvvv_100 | Table: salesvvvv <-- Error Message -->
Unknown PDK exception occur, when operate table: salesvvvv, java.lang.RuntimeException: Append create table sql(s) failed: Failed to find any partition fields, sub partition table: salesvvvv_100 | Table: salesvvvv

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Failed to find any partition fields, sub partition table: salesvvvv_100
	io.tapdata.connector.mssql.MssqlMaker.getOnePartitionInfo(MssqlMaker.java:418)
	io.tapdata.connector.mssql.MssqlMaker.lambda$getPartitionInfos$10(MssqlMaker.java:401)
	java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Append create table sql(s) failed: Failed to find any partition fields, sub partition table: salesvvvv_100 | Table: salesvvvv
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:77)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$19(HazelcastTargetPdkBaseNode.java:439)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createTable$20(HazelcastTargetPdkBaseNode.java:433)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doCreateTable(HazelcastTargetPdkBaseNode.java:325)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:432)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:227)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$3(HazelcastTargetPdkDataNode.java:174)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:160)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:111)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Append create table sql(s) failed: Failed to find any partition fields, sub partition table: salesvvvv_100 | Table: salesvvvv
	at io.tapdata.connector.mssql.MssqlConnector.createTableV2(MssqlConnector.java:369)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$18(HazelcastTargetPdkBaseNode.java:441)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 30 more
Caused by: code: 0 | message: Failed to find any partition fields, sub partition table: salesvvvv_100
	at io.tapdata.connector.mssql.MssqlMaker.getOnePartitionInfo(MssqlMaker.java:418)
	at io.tapdata.connector.mssql.MssqlMaker.lambda$getPartitionInfos$10(MssqlMaker.java:401)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:485)
	at io.tapdata.connector.mssql.MssqlMaker.getPartitionInfos(MssqlMaker.java:403)
	at io.tapdata.connector.mssql.MssqlMaker.getPartitionField(MssqlMaker.java:443)
	at io.tapdata.connector.mssql.MssqlMaker.createTable(MssqlMaker.java:121)
	at io.tapdata.connector.mssql.MssqlMaker.createTable(MssqlMaker.java:69)
	at io.tapdata.connector.mssql.MssqlConnector.createTableV2(MssqlConnector.java:367)
	... 32 more

[TRACE] 2025-02-11 13:11:03.442 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] running status set to false 
[TRACE] 2025-02-11 13:11:03.453 - [变态Schema与变态Database错误][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739250662366 
[TRACE] 2025-02-11 13:11:03.456 - [变态Schema与变态Database错误][PG] - PDK connector node released: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739250662366 
[TRACE] 2025-02-11 13:11:03.457 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] schema data cleaned 
[TRACE] 2025-02-11 13:11:03.457 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] monitor closed 
[TRACE] 2025-02-11 13:11:03.461 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] close complete, cost 44 ms 
[TRACE] 2025-02-11 13:11:03.526 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] running status set to false 
[TRACE] 2025-02-11 13:11:03.526 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node stopped: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739250662580 
[TRACE] 2025-02-11 13:11:03.526 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node released: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739250662580 
[TRACE] 2025-02-11 13:11:03.528 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] schema data cleaned 
[TRACE] 2025-02-11 13:11:03.528 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] monitor closed 
[TRACE] 2025-02-11 13:11:03.735 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] close complete, cost 67 ms 
[INFO ] 2025-02-11 13:11:06.906 - [变态Schema与变态Database错误] - Task [变态Schema与变态Database错误] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-02-11 13:11:06.907 - [变态Schema与变态Database错误] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-11 13:11:06.908 - [变态Schema与变态Database错误] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@174a2b6d 
[TRACE] 2025-02-11 13:11:07.069 - [变态Schema与变态Database错误] - Stop task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误)  
[TRACE] 2025-02-11 13:11:07.069 - [变态Schema与变态Database错误] - Stopped task aspect(s) 
[TRACE] 2025-02-11 13:11:07.069 - [变态Schema与变态Database错误] - Snapshot order controller have been removed 
[INFO ] 2025-02-11 13:11:07.069 - [变态Schema与变态Database错误] - Task stopped. 
[TRACE] 2025-02-11 13:11:07.102 - [变态Schema与变态Database错误] - Remove memory task client succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 13:11:07.105 - [变态Schema与变态Database错误] - Destroy memory task client cache succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 13:12:09.442 - [变态Schema与变态Database错误] - Task initialization... 
[TRACE] 2025-02-11 13:12:09.442 - [变态Schema与变态Database错误] - Start task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误) 
[INFO ] 2025-02-11 13:12:09.598 - [变态Schema与变态Database错误] - Loading table structure completed 
[TRACE] 2025-02-11 13:12:09.598 - [变态Schema与变态Database错误] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-11 13:12:09.645 - [变态Schema与变态Database错误] - The engine receives 变态Schema与变态Database错误 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-11 13:12:09.645 - [变态Schema与变态Database错误] - Task started 
[TRACE] 2025-02-11 13:12:09.680 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] start preload schema,table counts: 3 
[TRACE] 2025-02-11 13:12:09.680 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] start preload schema,table counts: 3 
[TRACE] 2025-02-11 13:12:09.680 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] preload schema finished, cost 0 ms 
[TRACE] 2025-02-11 13:12:09.680 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] preload schema finished, cost 0 ms 
[INFO ] 2025-02-11 13:12:09.680 - [变态Schema与变态Database错误][PG] - Enable partition table support for source database 
[INFO ] 2025-02-11 13:12:10.562 - [变态Schema与变态Database错误][SqlServer4433] - Sink connector(SqlServer4433) initialization completed 
[TRACE] 2025-02-11 13:12:10.565 - [变态Schema与变态Database错误][SqlServer4433] - Node(SqlServer4433) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-11 13:12:10.565 - [变态Schema与变态Database错误][SqlServer4433] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-11 13:12:10.567 - [变态Schema与变态Database错误][SqlServer4433] - Apply table structure to target database 
[INFO ] 2025-02-11 13:12:10.858 - [变态Schema与变态Database错误][PG] - Source connector(PG) initialization completed 
[TRACE] 2025-02-11 13:12:10.859 - [变态Schema与变态Database错误][PG] - Source node "PG" read batch size: 100 
[TRACE] 2025-02-11 13:12:10.859 - [变态Schema与变态Database错误][PG] - Source node "PG" event queue capacity: 200 
[TRACE] 2025-02-11 13:12:10.859 - [变态Schema与变态Database错误][PG] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-02-11 13:12:11.016 - [变态Schema与变态Database错误][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-11 13:12:11.017 - [变态Schema与变态Database错误][PG] - new logical replication slot created, slotName:tapdata_cdc_3e7121eb_7549_453a_b5ff_ace1dae6a3e3 
[INFO ] 2025-02-11 13:12:11.021 - [变态Schema与变态Database错误][PG] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-11 13:12:11.021 - [变态Schema与变态Database错误][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-11 13:12:11.080 - [变态Schema与变态Database错误] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2025-02-11 13:12:11.084 - [变态Schema与变态Database错误][PG] - Starting batch read from 3 tables 
[TRACE] 2025-02-11 13:12:11.093 - [变态Schema与变态Database错误][PG] - Initial sync started 
[INFO ] 2025-02-11 13:12:11.100 - [变态Schema与变态Database错误][PG] - Starting batch read from table: salesb 
[TRACE] 2025-02-11 13:12:11.103 - [变态Schema与变态Database错误][PG] - Table salesb is going to be initial synced 
[INFO ] 2025-02-11 13:12:11.115 - [变态Schema与变态Database错误][PG] - Table salesb has been completed batch read 
[INFO ] 2025-02-11 13:12:11.115 - [变态Schema与变态Database错误][PG] - Starting batch read from table: salesvvvv 
[TRACE] 2025-02-11 13:12:11.115 - [变态Schema与变态Database错误][PG] - Table salesvvvv is going to be initial synced 
[TRACE] 2025-02-11 13:12:11.130 - [变态Schema与变态Database错误][PG] - Initial sync completed 
[TRACE] 2025-02-11 13:12:11.134 - [变态Schema与变态Database错误][PG] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, org.postgresql.util.PSQLException: Unterminated identifier started at position 47 in SQL SELECT "key","amount"" FROM "public"."salesvvvv". Expected " char 
[TRACE] 2025-02-11 13:12:11.143 - [变态Schema与变态Database错误][PG] - Query snapshot row size completed: PG(ea9c0610-bb94-4c0f-90f7-644daf40c5d6) 
[ERROR] 2025-02-11 13:12:11.147 - [变态Schema与变态Database错误][PG] - Unknown PDK exception occur, org.postgresql.util.PSQLException: Unterminated identifier started at position 47 in SQL SELECT "key","amount"" FROM "public"."salesvvvv". Expected " char <-- Error Message -->
Unknown PDK exception occur, org.postgresql.util.PSQLException: Unterminated identifier started at position 47 in SQL SELECT "key","amount"" FROM "public"."salesvvvv". Expected " char

<-- Simple Stack Trace -->
Caused by: org.postgresql.util.PSQLException: Unterminated identifier started at position 47 in SQL SELECT "key","amount"" FROM "public"."salesvvvv". Expected " char
	org.postgresql.core.Parser.checkParsePosition(Parser.java:1380)
	org.postgresql.core.Parser.parseSql(Parser.java:1295)
	org.postgresql.core.Parser.replaceProcessing(Parser.java:1231)
	org.postgresql.core.CachedQueryCreateAction.create(CachedQueryCreateAction.java:43)
	org.postgresql.core.QueryExecutorBase.createQueryByKey(QueryExecutorBase.java:340)
	...

<-- Full Stack Trace -->
org.postgresql.util.PSQLException: Unterminated identifier started at position 47 in SQL SELECT "key","amount"" FROM "public"."salesvvvv". Expected " char
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:498)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:490)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:412)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:356)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:269)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.postgresql.util.PSQLException: Unterminated identifier started at position 47 in SQL SELECT "key","amount"" FROM "public"."salesvvvv". Expected " char
	at org.postgresql.core.Parser.checkParsePosition(Parser.java:1380)
	at org.postgresql.core.Parser.parseSql(Parser.java:1295)
	at org.postgresql.core.Parser.replaceProcessing(Parser.java:1231)
	at org.postgresql.core.CachedQueryCreateAction.create(CachedQueryCreateAction.java:43)
	at org.postgresql.core.QueryExecutorBase.createQueryByKey(QueryExecutorBase.java:340)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:307)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:291)
	at org.postgresql.jdbc.PgStatement.executeQuery(PgStatement.java:243)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:94)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutHashSplit(CommonDbConnector.java:585)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:579)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:577)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 19 more

[TRACE] 2025-02-11 13:12:11.153 - [变态Schema与变态Database错误][PG] - Job suspend in error handle 
[TRACE] 2025-02-11 13:12:11.211 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] running status set to false 
[TRACE] 2025-02-11 13:12:11.219 - [变态Schema与变态Database错误][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739250730611 
[TRACE] 2025-02-11 13:12:11.228 - [变态Schema与变态Database错误][PG] - PDK connector node released: HazelcastSourcePdkDataNode_ea9c0610-bb94-4c0f-90f7-644daf40c5d6_1739250730611 
[TRACE] 2025-02-11 13:12:11.230 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] schema data cleaned 
[TRACE] 2025-02-11 13:12:11.231 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] monitor closed 
[TRACE] 2025-02-11 13:12:11.234 - [变态Schema与变态Database错误][PG] - Node PG[ea9c0610-bb94-4c0f-90f7-644daf40c5d6] close complete, cost 32 ms 
[TRACE] 2025-02-11 13:12:11.234 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] running status set to false 
[TRACE] 2025-02-11 13:12:11.234 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node stopped: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739250730365 
[TRACE] 2025-02-11 13:12:11.234 - [变态Schema与变态Database错误][SqlServer4433] - PDK connector node released: HazelcastTargetPdkDataNode_3efeb4b4-6f39-4e2b-addc-4cab57106101_1739250730365 
[TRACE] 2025-02-11 13:12:11.236 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] schema data cleaned 
[TRACE] 2025-02-11 13:12:11.243 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] monitor closed 
[TRACE] 2025-02-11 13:12:11.247 - [变态Schema与变态Database错误][SqlServer4433] - Node SqlServer4433[3efeb4b4-6f39-4e2b-addc-4cab57106101] close complete, cost 13 ms 
[INFO ] 2025-02-11 13:12:12.218 - [变态Schema与变态Database错误] - Task [变态Schema与变态Database错误] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-02-11 13:12:12.219 - [变态Schema与变态Database错误] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-11 13:12:12.219 - [变态Schema与变态Database错误] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@11575784 
[TRACE] 2025-02-11 13:12:12.219 - [变态Schema与变态Database错误] - Stop task milestones: 67a9ffd97567c16109c8678e(变态Schema与变态Database错误)  
[TRACE] 2025-02-11 13:12:12.383 - [变态Schema与变态Database错误] - Stopped task aspect(s) 
[TRACE] 2025-02-11 13:12:12.383 - [变态Schema与变态Database错误] - Snapshot order controller have been removed 
[INFO ] 2025-02-11 13:12:12.384 - [变态Schema与变态Database错误] - Task stopped. 
[TRACE] 2025-02-11 13:12:12.406 - [变态Schema与变态Database错误] - Remove memory task client succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
[TRACE] 2025-02-11 13:12:12.409 - [变态Schema与变态Database错误] - Destroy memory task client cache succeed, task: 变态Schema与变态Database错误[67a9ffd97567c16109c8678e] 
