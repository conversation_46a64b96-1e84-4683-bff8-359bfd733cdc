[INFO ] 2024-05-20 10:33:53.553 - [任务 10(101)] - 6647290582e56d0d1f7383eb task start 
[INFO ] 2024-05-20 10:33:53.573 - [任务 10(101)][2fff9327-eba1-4243-99ff-2fd6c5fc70c5] - Node 2fff9327-eba1-4243-99ff-2fd6c5fc70c5[2fff9327-eba1-4243-99ff-2fd6c5fc70c5] start preload schema,table counts: 0 
[INFO ] 2024-05-20 10:33:53.573 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] start preload schema,table counts: 1 
[INFO ] 2024-05-20 10:33:53.573 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] start preload schema,table counts: 1 
[INFO ] 2024-05-20 10:33:53.573 - [任务 10(101)][2fff9327-eba1-4243-99ff-2fd6c5fc70c5] - Node 2fff9327-eba1-4243-99ff-2fd6c5fc70c5[2fff9327-eba1-4243-99ff-2fd6c5fc70c5] preload schema finished, cost 0 ms 
[INFO ] 2024-05-20 10:33:53.673 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] preload schema finished, cost 98 ms 
[INFO ] 2024-05-20 10:33:53.675 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] preload schema finished, cost 98 ms 
[INFO ] 2024-05-20 10:33:54.126 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] running status set to false 
[INFO ] 2024-05-20 10:33:54.127 - [任务 10(101)][testtime1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e 
[INFO ] 2024-05-20 10:33:54.128 - [任务 10(101)][testtime1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e 
[INFO ] 2024-05-20 10:33:54.129 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] schema data cleaned 
[INFO ] 2024-05-20 10:33:54.129 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] monitor closed 
[INFO ] 2024-05-20 10:33:54.130 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] close complete, cost 29 ms 
[INFO ] 2024-05-20 10:33:54.302 - [任务 10(101)][增强JS] - DateTime nano 42000000 seconds 4071646107 timeZone null 
[INFO ] 2024-05-20 10:33:54.309 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] running status set to false 
[INFO ] 2024-05-20 10:33:54.310 - [任务 10(101)][2fff9327-eba1-4243-99ff-2fd6c5fc70c5] - Node 2fff9327-eba1-4243-99ff-2fd6c5fc70c5[2fff9327-eba1-4243-99ff-2fd6c5fc70c5] running status set to false 
[INFO ] 2024-05-20 10:33:54.320 - [任务 10(101)][2fff9327-eba1-4243-99ff-2fd6c5fc70c5] - Node 2fff9327-eba1-4243-99ff-2fd6c5fc70c5[2fff9327-eba1-4243-99ff-2fd6c5fc70c5] schema data cleaned 
[INFO ] 2024-05-20 10:33:54.320 - [任务 10(101)][2fff9327-eba1-4243-99ff-2fd6c5fc70c5] - Node 2fff9327-eba1-4243-99ff-2fd6c5fc70c5[2fff9327-eba1-4243-99ff-2fd6c5fc70c5] monitor closed 
[INFO ] 2024-05-20 10:33:54.321 - [任务 10(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePG-627d27df-83ff-4380-a26a-b2243f1d796f 
[INFO ] 2024-05-20 10:33:54.321 - [任务 10(101)][增强JS] - PDK connector node released: ScriptExecutor-SourcePG-627d27df-83ff-4380-a26a-b2243f1d796f 
[INFO ] 2024-05-20 10:33:54.321 - [任务 10(101)][2fff9327-eba1-4243-99ff-2fd6c5fc70c5] - Node 2fff9327-eba1-4243-99ff-2fd6c5fc70c5[2fff9327-eba1-4243-99ff-2fd6c5fc70c5] close complete, cost 23 ms 
[INFO ] 2024-05-20 10:33:54.322 - [任务 10(101)][增强JS] - [ScriptExecutorsManager-6647290582e56d0d1f7383eb-b1c23729-0ca2-4c00-8f92-8f25a40d3990-6647213882e56d0d1f73814f] schema data cleaned 
[INFO ] 2024-05-20 10:33:54.324 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] schema data cleaned 
[INFO ] 2024-05-20 10:33:54.324 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] monitor closed 
[INFO ] 2024-05-20 10:33:54.361 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] close complete, cost 28 ms 
[INFO ] 2024-05-20 10:33:54.364 - [任务 10(101)] - test run task 6647290582e56d0d1f7383eb complete, cost 887ms 
[INFO ] 2024-05-20 14:11:54.715 - [任务 10(101)] - 6647290582e56d0d1f7383eb task start 
[INFO ] 2024-05-20 14:11:54.900 - [任务 10(101)][029b52aa-508f-4b3f-b996-0a019954c625] - Node 029b52aa-508f-4b3f-b996-0a019954c625[029b52aa-508f-4b3f-b996-0a019954c625] start preload schema,table counts: 0 
[INFO ] 2024-05-20 14:11:54.900 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] start preload schema,table counts: 1 
[INFO ] 2024-05-20 14:11:54.900 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] start preload schema,table counts: 1 
[INFO ] 2024-05-20 14:11:54.901 - [任务 10(101)][029b52aa-508f-4b3f-b996-0a019954c625] - Node 029b52aa-508f-4b3f-b996-0a019954c625[029b52aa-508f-4b3f-b996-0a019954c625] preload schema finished, cost 0 ms 
[INFO ] 2024-05-20 14:11:54.947 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] preload schema finished, cost 48 ms 
[INFO ] 2024-05-20 14:11:54.951 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] preload schema finished, cost 49 ms 
[ERROR] 2024-05-20 14:11:55.699 - [任务 10(101)][testtime1] - start source runner failed: Failed to create pdk connector node, database type: postgres-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: postgres-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: postgres-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:567)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:212)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: postgres-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:194)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:221)
	... 16 more

[INFO ] 2024-05-20 14:11:58.259 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] running status set to false 
[INFO ] 2024-05-20 14:11:58.262 - [任务 10(101)][029b52aa-508f-4b3f-b996-0a019954c625] - Node 029b52aa-508f-4b3f-b996-0a019954c625[029b52aa-508f-4b3f-b996-0a019954c625] running status set to false 
[INFO ] 2024-05-20 14:11:58.262 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] running status set to false 
[INFO ] 2024-05-20 14:11:58.273 - [任务 10(101)][029b52aa-508f-4b3f-b996-0a019954c625] - Node 029b52aa-508f-4b3f-b996-0a019954c625[029b52aa-508f-4b3f-b996-0a019954c625] schema data cleaned 
[INFO ] 2024-05-20 14:11:58.282 - [任务 10(101)][029b52aa-508f-4b3f-b996-0a019954c625] - Node 029b52aa-508f-4b3f-b996-0a019954c625[029b52aa-508f-4b3f-b996-0a019954c625] monitor closed 
[INFO ] 2024-05-20 14:11:58.286 - [任务 10(101)][testtime1] - PDK connector node stopped: null 
[INFO ] 2024-05-20 14:11:58.286 - [任务 10(101)][testtime1] - PDK connector node released: null 
[INFO ] 2024-05-20 14:11:58.286 - [任务 10(101)][029b52aa-508f-4b3f-b996-0a019954c625] - Node 029b52aa-508f-4b3f-b996-0a019954c625[029b52aa-508f-4b3f-b996-0a019954c625] close complete, cost 32 ms 
[INFO ] 2024-05-20 14:11:58.286 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] schema data cleaned 
[INFO ] 2024-05-20 14:11:58.286 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] monitor closed 
[INFO ] 2024-05-20 14:11:58.295 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] close complete, cost 53 ms 
[INFO ] 2024-05-20 14:11:58.295 - [任务 10(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePG-6c2003d7-7201-42fe-a83c-a25bf09b7bb1 
[INFO ] 2024-05-20 14:11:58.296 - [任务 10(101)][增强JS] - PDK connector node released: ScriptExecutor-SourcePG-6c2003d7-7201-42fe-a83c-a25bf09b7bb1 
[INFO ] 2024-05-20 14:11:58.296 - [任务 10(101)][增强JS] - [ScriptExecutorsManager-6647290582e56d0d1f7383eb-b1c23729-0ca2-4c00-8f92-8f25a40d3990-6647213882e56d0d1f73814f] schema data cleaned 
[INFO ] 2024-05-20 14:11:58.305 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] schema data cleaned 
[INFO ] 2024-05-20 14:11:58.306 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] monitor closed 
[INFO ] 2024-05-20 14:11:58.313 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] close complete, cost 71 ms 
[INFO ] 2024-05-20 14:11:58.313 - [任务 10(101)] - test run task 6647290582e56d0d1f7383eb complete, cost 3605ms 
[INFO ] 2024-05-20 14:12:00.129 - [任务 10(101)] - 6647290582e56d0d1f7383eb task start 
[INFO ] 2024-05-20 14:12:00.142 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] start preload schema,table counts: 1 
[INFO ] 2024-05-20 14:12:00.142 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] start preload schema,table counts: 1 
[INFO ] 2024-05-20 14:12:00.143 - [任务 10(101)][dd0140d2-5440-41da-bd07-d10663bfd09c] - Node dd0140d2-5440-41da-bd07-d10663bfd09c[dd0140d2-5440-41da-bd07-d10663bfd09c] start preload schema,table counts: 0 
[INFO ] 2024-05-20 14:12:00.143 - [任务 10(101)][dd0140d2-5440-41da-bd07-d10663bfd09c] - Node dd0140d2-5440-41da-bd07-d10663bfd09c[dd0140d2-5440-41da-bd07-d10663bfd09c] preload schema finished, cost 0 ms 
[INFO ] 2024-05-20 14:12:00.164 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] preload schema finished, cost 22 ms 
[INFO ] 2024-05-20 14:12:00.164 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] preload schema finished, cost 23 ms 
[INFO ] 2024-05-20 14:12:00.726 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] running status set to false 
[INFO ] 2024-05-20 14:12:00.772 - [任务 10(101)][增强JS] - 4071674907 
[INFO ] 2024-05-20 14:12:00.772 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] running status set to false 
[INFO ] 2024-05-20 14:12:00.772 - [任务 10(101)][dd0140d2-5440-41da-bd07-d10663bfd09c] - Node dd0140d2-5440-41da-bd07-d10663bfd09c[dd0140d2-5440-41da-bd07-d10663bfd09c] running status set to false 
[INFO ] 2024-05-20 14:12:00.772 - [任务 10(101)][dd0140d2-5440-41da-bd07-d10663bfd09c] - Node dd0140d2-5440-41da-bd07-d10663bfd09c[dd0140d2-5440-41da-bd07-d10663bfd09c] schema data cleaned 
[INFO ] 2024-05-20 14:12:00.772 - [任务 10(101)][dd0140d2-5440-41da-bd07-d10663bfd09c] - Node dd0140d2-5440-41da-bd07-d10663bfd09c[dd0140d2-5440-41da-bd07-d10663bfd09c] monitor closed 
[INFO ] 2024-05-20 14:12:00.772 - [任务 10(101)][dd0140d2-5440-41da-bd07-d10663bfd09c] - Node dd0140d2-5440-41da-bd07-d10663bfd09c[dd0140d2-5440-41da-bd07-d10663bfd09c] close complete, cost 2 ms 
[INFO ] 2024-05-20 14:12:00.785 - [任务 10(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePG-e3a27e58-1d8c-4f8d-91f4-225b602a9b85 
[INFO ] 2024-05-20 14:12:00.786 - [任务 10(101)][增强JS] - PDK connector node released: ScriptExecutor-SourcePG-e3a27e58-1d8c-4f8d-91f4-225b602a9b85 
[INFO ] 2024-05-20 14:12:00.786 - [任务 10(101)][增强JS] - [ScriptExecutorsManager-6647290582e56d0d1f7383eb-b1c23729-0ca2-4c00-8f92-8f25a40d3990-6647213882e56d0d1f73814f] schema data cleaned 
[INFO ] 2024-05-20 14:12:00.788 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] schema data cleaned 
[INFO ] 2024-05-20 14:12:00.789 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] monitor closed 
[INFO ] 2024-05-20 14:12:00.789 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] close complete, cost 19 ms 
[INFO ] 2024-05-20 14:12:00.804 - [任务 10(101)][testtime1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e 
[INFO ] 2024-05-20 14:12:00.805 - [任务 10(101)][testtime1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e 
[INFO ] 2024-05-20 14:12:00.805 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] schema data cleaned 
[INFO ] 2024-05-20 14:12:00.805 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] monitor closed 
[INFO ] 2024-05-20 14:12:00.817 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] close complete, cost 83 ms 
[INFO ] 2024-05-20 14:12:00.818 - [任务 10(101)] - test run task 6647290582e56d0d1f7383eb complete, cost 791ms 
[INFO ] 2024-05-20 14:12:18.049 - [任务 10(101)] - 6647290582e56d0d1f7383eb task start 
[INFO ] 2024-05-20 14:12:18.049 - [任务 10(101)][0c6f544b-7e68-4acc-a0f4-911a854954e1] - Node 0c6f544b-7e68-4acc-a0f4-911a854954e1[0c6f544b-7e68-4acc-a0f4-911a854954e1] start preload schema,table counts: 0 
[INFO ] 2024-05-20 14:12:18.049 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] start preload schema,table counts: 1 
[INFO ] 2024-05-20 14:12:18.049 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] start preload schema,table counts: 1 
[INFO ] 2024-05-20 14:12:18.049 - [任务 10(101)][0c6f544b-7e68-4acc-a0f4-911a854954e1] - Node 0c6f544b-7e68-4acc-a0f4-911a854954e1[0c6f544b-7e68-4acc-a0f4-911a854954e1] preload schema finished, cost 0 ms 
[INFO ] 2024-05-20 14:12:18.075 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] preload schema finished, cost 23 ms 
[INFO ] 2024-05-20 14:12:18.075 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] preload schema finished, cost 23 ms 
[INFO ] 2024-05-20 14:12:18.421 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] running status set to false 
[INFO ] 2024-05-20 14:12:18.421 - [任务 10(101)][testtime1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e 
[INFO ] 2024-05-20 14:12:18.421 - [任务 10(101)][testtime1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e 
[INFO ] 2024-05-20 14:12:18.421 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] schema data cleaned 
[INFO ] 2024-05-20 14:12:18.422 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] monitor closed 
[INFO ] 2024-05-20 14:12:18.622 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] close complete, cost 19 ms 
[INFO ] 2024-05-20 14:12:18.725 - [任务 10(101)][增强JS] - DateTime nano 42000000 seconds 4071674907 timeZone null 
[INFO ] 2024-05-20 14:12:18.725 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] running status set to false 
[INFO ] 2024-05-20 14:12:18.725 - [任务 10(101)][0c6f544b-7e68-4acc-a0f4-911a854954e1] - Node 0c6f544b-7e68-4acc-a0f4-911a854954e1[0c6f544b-7e68-4acc-a0f4-911a854954e1] running status set to false 
[INFO ] 2024-05-20 14:12:18.725 - [任务 10(101)][0c6f544b-7e68-4acc-a0f4-911a854954e1] - Node 0c6f544b-7e68-4acc-a0f4-911a854954e1[0c6f544b-7e68-4acc-a0f4-911a854954e1] schema data cleaned 
[INFO ] 2024-05-20 14:12:18.725 - [任务 10(101)][0c6f544b-7e68-4acc-a0f4-911a854954e1] - Node 0c6f544b-7e68-4acc-a0f4-911a854954e1[0c6f544b-7e68-4acc-a0f4-911a854954e1] monitor closed 
[INFO ] 2024-05-20 14:12:18.735 - [任务 10(101)][0c6f544b-7e68-4acc-a0f4-911a854954e1] - Node 0c6f544b-7e68-4acc-a0f4-911a854954e1[0c6f544b-7e68-4acc-a0f4-911a854954e1] close complete, cost 1 ms 
[INFO ] 2024-05-20 14:12:18.735 - [任务 10(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePG-83dd9577-95d8-4441-9e0a-3ee17ccb94c8 
[INFO ] 2024-05-20 14:12:18.735 - [任务 10(101)][增强JS] - PDK connector node released: ScriptExecutor-SourcePG-83dd9577-95d8-4441-9e0a-3ee17ccb94c8 
[INFO ] 2024-05-20 14:12:18.735 - [任务 10(101)][增强JS] - [ScriptExecutorsManager-6647290582e56d0d1f7383eb-b1c23729-0ca2-4c00-8f92-8f25a40d3990-6647213882e56d0d1f73814f] schema data cleaned 
[INFO ] 2024-05-20 14:12:18.740 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] schema data cleaned 
[INFO ] 2024-05-20 14:12:18.740 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] monitor closed 
[INFO ] 2024-05-20 14:12:18.751 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] close complete, cost 15 ms 
[INFO ] 2024-05-20 14:12:18.752 - [任务 10(101)] - test run task 6647290582e56d0d1f7383eb complete, cost 790ms 
[INFO ] 2024-05-20 14:31:24.936 - [任务 10(101)] - 6647290582e56d0d1f7383eb task start 
[INFO ] 2024-05-20 14:31:24.990 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] start preload schema,table counts: 1 
[INFO ] 2024-05-20 14:31:24.990 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] start preload schema,table counts: 1 
[INFO ] 2024-05-20 14:31:24.991 - [任务 10(101)][672effe8-a189-4c3b-b00a-8e3e63aebaea] - Node 672effe8-a189-4c3b-b00a-8e3e63aebaea[672effe8-a189-4c3b-b00a-8e3e63aebaea] start preload schema,table counts: 0 
[INFO ] 2024-05-20 14:31:24.991 - [任务 10(101)][672effe8-a189-4c3b-b00a-8e3e63aebaea] - Node 672effe8-a189-4c3b-b00a-8e3e63aebaea[672effe8-a189-4c3b-b00a-8e3e63aebaea] preload schema finished, cost 0 ms 
[INFO ] 2024-05-20 14:31:25.007 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] preload schema finished, cost 18 ms 
[INFO ] 2024-05-20 14:31:25.008 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] preload schema finished, cost 18 ms 
[INFO ] 2024-05-20 14:31:25.700 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] running status set to false 
[INFO ] 2024-05-20 14:31:25.714 - [任务 10(101)][testtime1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e 
[INFO ] 2024-05-20 14:31:25.714 - [任务 10(101)][testtime1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e 
[INFO ] 2024-05-20 14:31:25.714 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] schema data cleaned 
[INFO ] 2024-05-20 14:31:25.723 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] monitor closed 
[INFO ] 2024-05-20 14:31:25.737 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] close complete, cost 43 ms 
[INFO ] 2024-05-20 14:31:25.737 - [任务 10(101)][增强JS] - 4071674907 
[INFO ] 2024-05-20 14:31:25.745 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] running status set to false 
[INFO ] 2024-05-20 14:31:25.745 - [任务 10(101)][672effe8-a189-4c3b-b00a-8e3e63aebaea] - Node 672effe8-a189-4c3b-b00a-8e3e63aebaea[672effe8-a189-4c3b-b00a-8e3e63aebaea] running status set to false 
[INFO ] 2024-05-20 14:31:25.745 - [任务 10(101)][672effe8-a189-4c3b-b00a-8e3e63aebaea] - Node 672effe8-a189-4c3b-b00a-8e3e63aebaea[672effe8-a189-4c3b-b00a-8e3e63aebaea] schema data cleaned 
[INFO ] 2024-05-20 14:31:25.745 - [任务 10(101)][672effe8-a189-4c3b-b00a-8e3e63aebaea] - Node 672effe8-a189-4c3b-b00a-8e3e63aebaea[672effe8-a189-4c3b-b00a-8e3e63aebaea] monitor closed 
[INFO ] 2024-05-20 14:31:25.757 - [任务 10(101)][672effe8-a189-4c3b-b00a-8e3e63aebaea] - Node 672effe8-a189-4c3b-b00a-8e3e63aebaea[672effe8-a189-4c3b-b00a-8e3e63aebaea] close complete, cost 1 ms 
[INFO ] 2024-05-20 14:31:25.758 - [任务 10(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePG-564214b9-8bb1-42ca-8761-4bcec5063492 
[INFO ] 2024-05-20 14:31:25.758 - [任务 10(101)][增强JS] - PDK connector node released: ScriptExecutor-SourcePG-564214b9-8bb1-42ca-8761-4bcec5063492 
[INFO ] 2024-05-20 14:31:25.758 - [任务 10(101)][增强JS] - [ScriptExecutorsManager-6647290582e56d0d1f7383eb-b1c23729-0ca2-4c00-8f92-8f25a40d3990-6647213882e56d0d1f73814f] schema data cleaned 
[INFO ] 2024-05-20 14:31:25.760 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] schema data cleaned 
[INFO ] 2024-05-20 14:31:25.760 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] monitor closed 
[INFO ] 2024-05-20 14:31:25.772 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] close complete, cost 18 ms 
[INFO ] 2024-05-20 14:31:25.772 - [任务 10(101)] - test run task 6647290582e56d0d1f7383eb complete, cost 869ms 
[INFO ] 2024-05-20 14:35:43.480 - [任务 10(101)] - 6647290582e56d0d1f7383eb task start 
[INFO ] 2024-05-20 14:35:43.551 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] start preload schema,table counts: 1 
[INFO ] 2024-05-20 14:35:43.551 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] start preload schema,table counts: 1 
[INFO ] 2024-05-20 14:35:43.553 - [任务 10(101)][01be9830-4c4b-4b4e-a705-d3bbd4155801] - Node 01be9830-4c4b-4b4e-a705-d3bbd4155801[01be9830-4c4b-4b4e-a705-d3bbd4155801] start preload schema,table counts: 0 
[INFO ] 2024-05-20 14:35:43.553 - [任务 10(101)][01be9830-4c4b-4b4e-a705-d3bbd4155801] - Node 01be9830-4c4b-4b4e-a705-d3bbd4155801[01be9830-4c4b-4b4e-a705-d3bbd4155801] preload schema finished, cost 0 ms 
[INFO ] 2024-05-20 14:35:43.575 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] preload schema finished, cost 23 ms 
[INFO ] 2024-05-20 14:35:43.575 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] preload schema finished, cost 23 ms 
[INFO ] 2024-05-20 14:35:43.900 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] running status set to false 
[INFO ] 2024-05-20 14:35:43.908 - [任务 10(101)][testtime1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e 
[INFO ] 2024-05-20 14:35:43.908 - [任务 10(101)][testtime1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e 
[INFO ] 2024-05-20 14:35:43.908 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] schema data cleaned 
[INFO ] 2024-05-20 14:35:43.908 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] monitor closed 
[INFO ] 2024-05-20 14:35:43.908 - [任务 10(101)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] close complete, cost 15 ms 
[INFO ] 2024-05-20 14:35:44.001 - [任务 10(101)][增强JS] - true 
[INFO ] 2024-05-20 14:35:44.008 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] running status set to false 
[INFO ] 2024-05-20 14:35:44.013 - [任务 10(101)][01be9830-4c4b-4b4e-a705-d3bbd4155801] - Node 01be9830-4c4b-4b4e-a705-d3bbd4155801[01be9830-4c4b-4b4e-a705-d3bbd4155801] running status set to false 
[INFO ] 2024-05-20 14:35:44.016 - [任务 10(101)][01be9830-4c4b-4b4e-a705-d3bbd4155801] - Node 01be9830-4c4b-4b4e-a705-d3bbd4155801[01be9830-4c4b-4b4e-a705-d3bbd4155801] schema data cleaned 
[INFO ] 2024-05-20 14:35:44.016 - [任务 10(101)][01be9830-4c4b-4b4e-a705-d3bbd4155801] - Node 01be9830-4c4b-4b4e-a705-d3bbd4155801[01be9830-4c4b-4b4e-a705-d3bbd4155801] monitor closed 
[INFO ] 2024-05-20 14:35:44.016 - [任务 10(101)][01be9830-4c4b-4b4e-a705-d3bbd4155801] - Node 01be9830-4c4b-4b4e-a705-d3bbd4155801[01be9830-4c4b-4b4e-a705-d3bbd4155801] close complete, cost 7 ms 
[INFO ] 2024-05-20 14:35:44.020 - [任务 10(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePG-18b74e1c-53ec-4dba-86ab-847d44226ff1 
[INFO ] 2024-05-20 14:35:44.020 - [任务 10(101)][增强JS] - PDK connector node released: ScriptExecutor-SourcePG-18b74e1c-53ec-4dba-86ab-847d44226ff1 
[INFO ] 2024-05-20 14:35:44.020 - [任务 10(101)][增强JS] - [ScriptExecutorsManager-6647290582e56d0d1f7383eb-b1c23729-0ca2-4c00-8f92-8f25a40d3990-6647213882e56d0d1f73814f] schema data cleaned 
[INFO ] 2024-05-20 14:35:44.022 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] schema data cleaned 
[INFO ] 2024-05-20 14:35:44.022 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] monitor closed 
[INFO ] 2024-05-20 14:35:44.032 - [任务 10(101)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] close complete, cost 17 ms 
[INFO ] 2024-05-20 14:35:44.032 - [任务 10(101)] - test run task 6647290582e56d0d1f7383eb complete, cost 577ms 
