[INFO ] 2024-07-01 18:11:04.704 - [任务 5] - Task initialization... 
[INFO ] 2024-07-01 18:11:04.917 - [任务 5] - Start task milestones: 66827f422501e932ee7992e7(任务 5) 
[INFO ] 2024-07-01 18:11:05.430 - [任务 5] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-01 18:11:05.430 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-01 18:11:05.885 - [任务 5][字段改名] - Node 字段改名[7d7a1c1e-d8ac-4e2a-91ae-8b9aa1d58be0] start preload schema,table counts: 1 
[INFO ] 2024-07-01 18:11:05.886 - [任务 5][CLAIM] - Node CLAIM[c70e7e1a-dee9-471b-8fb8-7ca30bf5de72] start preload schema,table counts: 1 
[INFO ] 2024-07-01 18:11:05.887 - [任务 5][字段改名] - Node 字段改名[7d7a1c1e-d8ac-4e2a-91ae-8b9aa1d58be0] preload schema finished, cost 1 ms 
[INFO ] 2024-07-01 18:11:05.888 - [任务 5][CLAIM] - Node CLAIM[c70e7e1a-dee9-471b-8fb8-7ca30bf5de72] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 18:11:05.910 - [任务 5][CLAIM] - Node CLAIM[9a0638c6-c0f4-4028-89a6-a140216b0268] start preload schema,table counts: 1 
[INFO ] 2024-07-01 18:11:05.922 - [任务 5][CLAIM] - Node CLAIM[9a0638c6-c0f4-4028-89a6-a140216b0268] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 18:11:06.571 - [任务 5][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-07-01 18:11:06.575 - [任务 5][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-07-01 18:11:06.575 - [任务 5][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-01 18:11:06.705 - [任务 5][CLAIM] - batch offset found: {},stream offset found: {"cdcOffset":1719828666,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 18:11:06.758 - [任务 5][CLAIM] - Initial sync started 
[INFO ] 2024-07-01 18:11:06.759 - [任务 5][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-07-01 18:11:06.869 - [任务 5][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-07-01 18:11:06.870 - [任务 5][CLAIM] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-07-01 18:11:07.073 - [任务 5][CLAIM] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-01 18:11:07.288 - [任务 5][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 18:11:07.292 - [任务 5][CLAIM] - Initial sync completed 
[INFO ] 2024-07-01 18:11:07.293 - [任务 5][CLAIM] - Incremental sync starting... 
[INFO ] 2024-07-01 18:11:07.293 - [任务 5][CLAIM] - Initial sync completed 
[INFO ] 2024-07-01 18:11:07.299 - [任务 5][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"cdcOffset":1719828666,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 18:11:07.500 - [任务 5][CLAIM] - Connector MongoDB incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-07-01 18:13:31.237 - [任务 5] - Stop task milestones: 66827f422501e932ee7992e7(任务 5)  
[INFO ] 2024-07-01 18:13:31.348 - [任务 5][CLAIM] - Node CLAIM[c70e7e1a-dee9-471b-8fb8-7ca30bf5de72] running status set to false 
[INFO ] 2024-07-01 18:13:31.348 - [任务 5][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-c70e7e1a-dee9-471b-8fb8-7ca30bf5de72 
[INFO ] 2024-07-01 18:13:31.352 - [任务 5][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-c70e7e1a-dee9-471b-8fb8-7ca30bf5de72 
[INFO ] 2024-07-01 18:13:31.354 - [任务 5][CLAIM] - Node CLAIM[c70e7e1a-dee9-471b-8fb8-7ca30bf5de72] schema data cleaned 
[INFO ] 2024-07-01 18:13:31.355 - [任务 5][CLAIM] - Node CLAIM[c70e7e1a-dee9-471b-8fb8-7ca30bf5de72] monitor closed 
[INFO ] 2024-07-01 18:13:31.358 - [任务 5][CLAIM] - Node CLAIM[c70e7e1a-dee9-471b-8fb8-7ca30bf5de72] close complete, cost 33 ms 
[INFO ] 2024-07-01 18:13:31.563 - [任务 5][字段改名] - Node 字段改名[7d7a1c1e-d8ac-4e2a-91ae-8b9aa1d58be0] running status set to false 
[INFO ] 2024-07-01 18:13:31.566 - [任务 5][字段改名] - Node 字段改名[7d7a1c1e-d8ac-4e2a-91ae-8b9aa1d58be0] schema data cleaned 
[INFO ] 2024-07-01 18:13:31.567 - [任务 5][字段改名] - Node 字段改名[7d7a1c1e-d8ac-4e2a-91ae-8b9aa1d58be0] monitor closed 
[INFO ] 2024-07-01 18:13:31.568 - [任务 5][字段改名] - Node 字段改名[7d7a1c1e-d8ac-4e2a-91ae-8b9aa1d58be0] close complete, cost 209 ms 
[INFO ] 2024-07-01 18:13:31.599 - [任务 5][CLAIM] - Node CLAIM[9a0638c6-c0f4-4028-89a6-a140216b0268] running status set to false 
[INFO ] 2024-07-01 18:13:31.600 - [任务 5][CLAIM] - PDK connector node stopped: HazelcastTargetPdkDataNode-9a0638c6-c0f4-4028-89a6-a140216b0268 
[INFO ] 2024-07-01 18:13:31.600 - [任务 5][CLAIM] - PDK connector node released: HazelcastTargetPdkDataNode-9a0638c6-c0f4-4028-89a6-a140216b0268 
[INFO ] 2024-07-01 18:13:31.600 - [任务 5][CLAIM] - Node CLAIM[9a0638c6-c0f4-4028-89a6-a140216b0268] schema data cleaned 
[INFO ] 2024-07-01 18:13:31.601 - [任务 5][CLAIM] - Node CLAIM[9a0638c6-c0f4-4028-89a6-a140216b0268] monitor closed 
[INFO ] 2024-07-01 18:13:31.808 - [任务 5][CLAIM] - Node CLAIM[9a0638c6-c0f4-4028-89a6-a140216b0268] close complete, cost 33 ms 
[INFO ] 2024-07-01 18:13:36.255 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-01 18:13:36.255 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-07-01 18:13:36.255 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-07-01 18:13:36.347 - [任务 5] - Remove memory task client succeed, task: 任务 5[66827f422501e932ee7992e7] 
[INFO ] 2024-07-01 18:13:36.347 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[66827f422501e932ee7992e7] 
