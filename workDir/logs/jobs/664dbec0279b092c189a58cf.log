[INFO ] 2024-05-23 00:10:43.179 - [任务 14][CLAIM] - Mysql binlog reader stopped 
[WARN ] 2024-05-23 00:10:43.199 - [任务 14][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-23 00:29:46.743 - [任务 14][CLAIM] - Starting mysql cdc, server name: ff06717b-21f7-4fc1-a772-a572a6bfee9c 
[INFO ] 2024-05-23 00:29:46.744 - [任务 14][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 440993425
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.hostname: localhost
  database.password: ********
  name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  pdk.offset.string: {"name":"ff06717b-21f7-4fc1-a772-a572a6bfee9c","offset":{"{\"server\":\"ff06717b-21f7-4fc1-a772-a572a6bfee9c\"}":"{\"file\":\"binlog.000031\",\"pos\":1057410043,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-05-23 00:29:46.813 - [任务 14][CLAIM] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-23 00:29:46.821 - [任务 14][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-05-23 01:29:39.382 - [任务 14][CLAIM] - Mysql binlog reader stopped 
[WARN ] 2024-05-23 01:29:39.593 - [任务 14][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-23 01:49:52.723 - [任务 14][CLAIM] - Starting mysql cdc, server name: ff06717b-21f7-4fc1-a772-a572a6bfee9c 
[INFO ] 2024-05-23 01:49:52.724 - [任务 14][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 811034282
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.hostname: localhost
  database.password: ********
  name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  pdk.offset.string: {"name":"ff06717b-21f7-4fc1-a772-a572a6bfee9c","offset":{"{\"server\":\"ff06717b-21f7-4fc1-a772-a572a6bfee9c\"}":"{\"file\":\"binlog.000031\",\"pos\":1057410043,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-05-23 01:49:52.800 - [任务 14][CLAIM] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-23 01:49:52.827 - [任务 14][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-05-23 04:44:02.378 - [任务 14][CLAIM] - Mysql binlog reader stopped 
[WARN ] 2024-05-23 04:44:02.379 - [任务 14][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-23 05:02:52.664 - [任务 14][CLAIM] - Starting mysql cdc, server name: ff06717b-21f7-4fc1-a772-a572a6bfee9c 
[INFO ] 2024-05-23 05:02:52.666 - [任务 14][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1817935876
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.hostname: localhost
  database.password: ********
  name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  pdk.offset.string: {"name":"ff06717b-21f7-4fc1-a772-a572a6bfee9c","offset":{"{\"server\":\"ff06717b-21f7-4fc1-a772-a572a6bfee9c\"}":"{\"file\":\"binlog.000031\",\"pos\":1057410043,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-05-23 05:02:52.667 - [任务 14][CLAIM] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-23 05:02:52.667 - [任务 14][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-05-23 06:40:27.628 - [任务 14][CLAIM] - Mysql binlog reader stopped 
[WARN ] 2024-05-23 06:40:27.630 - [任务 14][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-23 06:58:57.695 - [任务 14][CLAIM] - Starting mysql cdc, server name: ff06717b-21f7-4fc1-a772-a572a6bfee9c 
[INFO ] 2024-05-23 06:58:57.696 - [任务 14][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 398939860
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.hostname: localhost
  database.password: ********
  name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  pdk.offset.string: {"name":"ff06717b-21f7-4fc1-a772-a572a6bfee9c","offset":{"{\"server\":\"ff06717b-21f7-4fc1-a772-a572a6bfee9c\"}":"{\"file\":\"binlog.000031\",\"pos\":1057410043,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-05-23 06:58:57.696 - [任务 14][CLAIM] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-23 06:58:57.696 - [任务 14][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-05-23 08:14:11.894 - [任务 14][CLAIM] - Mysql binlog reader stopped 
[WARN ] 2024-05-23 08:14:11.895 - [任务 14][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-23 08:32:22.580 - [任务 14][CLAIM] - Starting mysql cdc, server name: ff06717b-21f7-4fc1-a772-a572a6bfee9c 
[INFO ] 2024-05-23 08:32:22.581 - [任务 14][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 167448355
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.hostname: localhost
  database.password: ********
  name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  pdk.offset.string: {"name":"ff06717b-21f7-4fc1-a772-a572a6bfee9c","offset":{"{\"server\":\"ff06717b-21f7-4fc1-a772-a572a6bfee9c\"}":"{\"file\":\"binlog.000031\",\"pos\":1057410043,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-05-23 08:32:22.581 - [任务 14][CLAIM] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-23 08:32:22.581 - [任务 14][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-05-23 11:26:49.021 - [任务 14][CLAIM] - Starting mysql cdc, server name: ff06717b-21f7-4fc1-a772-a572a6bfee9c 
[INFO ] 2024-05-23 11:26:49.022 - [任务 14][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1453092911
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.hostname: localhost
  database.password: ********
  name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  pdk.offset.string: {"name":"ff06717b-21f7-4fc1-a772-a572a6bfee9c","offset":{"{\"server\":\"ff06717b-21f7-4fc1-a772-a572a6bfee9c\"}":"{\"file\":\"binlog.000031\",\"pos\":1057410043,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-05-23 11:26:49.022 - [任务 14][CLAIM] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-23 11:26:49.022 - [任务 14][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-05-23 13:07:23.514 - [任务 14][CLAIM] - Mysql binlog reader stopped 
[WARN ] 2024-05-23 13:07:23.515 - [任务 14][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-23 13:08:23.612 - [任务 14][CLAIM] - Starting mysql cdc, server name: ff06717b-21f7-4fc1-a772-a572a6bfee9c 
[INFO ] 2024-05-23 13:08:23.680 - [任务 14][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1466950285
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.hostname: localhost
  database.password: ********
  name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  pdk.offset.string: {"name":"ff06717b-21f7-4fc1-a772-a572a6bfee9c","offset":{"{\"server\":\"ff06717b-21f7-4fc1-a772-a572a6bfee9c\"}":"{\"file\":\"binlog.000031\",\"pos\":1057410043,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-05-23 13:08:23.692 - [任务 14][CLAIM] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-23 13:08:23.905 - [任务 14][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-05-23 14:57:27.814 - [任务 14][CLAIM] - Mysql binlog reader stopped 
[WARN ] 2024-05-23 14:57:27.823 - [任务 14][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-23 14:58:27.996 - [任务 14][CLAIM] - Starting mysql cdc, server name: ff06717b-21f7-4fc1-a772-a572a6bfee9c 
[INFO ] 2024-05-23 14:58:28.006 - [任务 14][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1160434848
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.hostname: localhost
  database.password: ********
  name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  pdk.offset.string: {"name":"ff06717b-21f7-4fc1-a772-a572a6bfee9c","offset":{"{\"server\":\"ff06717b-21f7-4fc1-a772-a572a6bfee9c\"}":"{\"ts_sec\":1716447175,\"file\":\"binlog.000031\",\"pos\":1057411337,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-05-23 14:58:28.156 - [任务 14][CLAIM] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-23 14:58:28.156 - [任务 14][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-05-23 16:23:18.607 - [任务 14] - Stop task milestones: 664dbec0279b092c189a58cf(任务 14)  
[INFO ] 2024-05-23 16:23:18.609 - [任务 14][CLAIM] - Node CLAIM[0cd72d3a-d2e7-4fb6-8ab8-c28c28dc5561] running status set to false 
[INFO ] 2024-05-23 16:23:18.609 - [任务 14][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-05-23 16:23:18.610 - [任务 14][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-05-23 16:23:18.610 - [任务 14][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-0cd72d3a-d2e7-4fb6-8ab8-c28c28dc5561 
[INFO ] 2024-05-23 16:23:18.610 - [任务 14][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-0cd72d3a-d2e7-4fb6-8ab8-c28c28dc5561 
[INFO ] 2024-05-23 16:23:18.610 - [任务 14][CLAIM] - Node CLAIM[0cd72d3a-d2e7-4fb6-8ab8-c28c28dc5561] schema data cleaned 
[INFO ] 2024-05-23 16:23:18.610 - [任务 14][CLAIM] - Node CLAIM[0cd72d3a-d2e7-4fb6-8ab8-c28c28dc5561] monitor closed 
[INFO ] 2024-05-23 16:23:18.611 - [任务 14][CLAIM] - Node CLAIM[0cd72d3a-d2e7-4fb6-8ab8-c28c28dc5561] close complete, cost 167 ms 
[INFO ] 2024-05-23 16:23:18.611 - [任务 14][test1] - Node test1[60eb58c1-78cc-432a-9acd-8a7aab0ad242] running status set to false 
[INFO ] 2024-05-23 16:23:18.612 - [任务 14][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-60eb58c1-78cc-432a-9acd-8a7aab0ad242 
[INFO ] 2024-05-23 16:23:18.613 - [任务 14][test1] - PDK connector node released: HazelcastTargetPdkDataNode-60eb58c1-78cc-432a-9acd-8a7aab0ad242 
[INFO ] 2024-05-23 16:23:18.613 - [任务 14][test1] - Node test1[60eb58c1-78cc-432a-9acd-8a7aab0ad242] schema data cleaned 
[INFO ] 2024-05-23 16:23:18.614 - [任务 14][test1] - Node test1[60eb58c1-78cc-432a-9acd-8a7aab0ad242] monitor closed 
[INFO ] 2024-05-23 16:23:18.614 - [任务 14][test1] - Node test1[60eb58c1-78cc-432a-9acd-8a7aab0ad242] close complete, cost 55 ms 
[INFO ] 2024-05-23 16:23:19.436 - [任务 14] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-23 16:23:19.437 - [任务 14] - Stopped task aspect(s) 
[INFO ] 2024-05-23 16:23:19.437 - [任务 14] - Snapshot order controller have been removed 
[INFO ] 2024-05-23 16:23:19.485 - [任务 14] - Remove memory task client succeed, task: 任务 14[664dbec0279b092c189a58cf] 
[INFO ] 2024-05-23 16:23:19.485 - [任务 14] - Destroy memory task client cache succeed, task: 任务 14[664dbec0279b092c189a58cf] 
[INFO ] 2024-05-23 17:52:44.790 - [任务 14] - Task initialization... 
[INFO ] 2024-05-23 17:52:44.791 - [任务 14] - Start task milestones: 664dbec0279b092c189a58cf(任务 14) 
[INFO ] 2024-05-23 17:52:44.792 - [任务 14] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-23 17:52:44.794 - [任务 14] - The engine receives 任务 14 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-23 17:52:44.795 - [任务 14][CLAIM] - Node CLAIM[0cd72d3a-d2e7-4fb6-8ab8-c28c28dc5561] start preload schema,table counts: 1 
[INFO ] 2024-05-23 17:52:44.795 - [任务 14][test1] - Node test1[60eb58c1-78cc-432a-9acd-8a7aab0ad242] start preload schema,table counts: 1 
[INFO ] 2024-05-23 17:52:44.835 - [任务 14][test1] - Node test1[60eb58c1-78cc-432a-9acd-8a7aab0ad242] preload schema finished, cost 45 ms 
[INFO ] 2024-05-23 17:52:44.836 - [任务 14][CLAIM] - Node CLAIM[0cd72d3a-d2e7-4fb6-8ab8-c28c28dc5561] preload schema finished, cost 47 ms 
[INFO ] 2024-05-23 17:52:45.246 - [任务 14][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-05-23 17:52:45.246 - [任务 14][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-05-23 17:52:45.257 - [任务 14][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-05-23 17:52:45.268 - [任务 14][CLAIM] - batch offset found: {},stream offset found: {"name":"ff06717b-21f7-4fc1-a772-a572a6bfee9c","offset":{"{\"server\":\"ff06717b-21f7-4fc1-a772-a572a6bfee9c\"}":"{\"file\":\"binlog.000031\",\"pos\":1057411337,\"server_id\":1}"}} 
[INFO ] 2024-05-23 17:52:45.269 - [任务 14][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-23 17:52:45.358 - [任务 14][CLAIM] - Incremental sync starting... 
[INFO ] 2024-05-23 17:52:45.358 - [任务 14][CLAIM] - Initial sync completed 
[INFO ] 2024-05-23 17:52:45.395 - [任务 14][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"ff06717b-21f7-4fc1-a772-a572a6bfee9c","offset":{"{\"server\":\"ff06717b-21f7-4fc1-a772-a572a6bfee9c\"}":"{\"file\":\"binlog.000031\",\"pos\":1057411337,\"server_id\":1}"}} 
[INFO ] 2024-05-23 17:52:45.398 - [任务 14][CLAIM] - Starting mysql cdc, server name: ff06717b-21f7-4fc1-a772-a572a6bfee9c 
[INFO ] 2024-05-23 17:52:45.441 - [任务 14][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 471262754
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-ff06717b-21f7-4fc1-a772-a572a6bfee9c
  database.hostname: localhost
  database.password: ********
  name: ff06717b-21f7-4fc1-a772-a572a6bfee9c
  pdk.offset.string: {"name":"ff06717b-21f7-4fc1-a772-a572a6bfee9c","offset":{"{\"server\":\"ff06717b-21f7-4fc1-a772-a572a6bfee9c\"}":"{\"file\":\"binlog.000031\",\"pos\":1057411337,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-05-23 17:52:45.443 - [任务 14][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-23 17:52:45.645 - [任务 14][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-05-23 18:59:07.250 - [任务 14] - Stop task milestones: 664dbec0279b092c189a58cf(任务 14)  
[INFO ] 2024-05-23 18:59:07.426 - [任务 14][CLAIM] - Node CLAIM[0cd72d3a-d2e7-4fb6-8ab8-c28c28dc5561] running status set to false 
[INFO ] 2024-05-23 18:59:07.430 - [任务 14][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-05-23 18:59:07.430 - [任务 14][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-05-23 18:59:07.445 - [任务 14][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-0cd72d3a-d2e7-4fb6-8ab8-c28c28dc5561 
[INFO ] 2024-05-23 18:59:07.445 - [任务 14][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-0cd72d3a-d2e7-4fb6-8ab8-c28c28dc5561 
[INFO ] 2024-05-23 18:59:07.447 - [任务 14][CLAIM] - Node CLAIM[0cd72d3a-d2e7-4fb6-8ab8-c28c28dc5561] schema data cleaned 
[INFO ] 2024-05-23 18:59:07.447 - [任务 14][CLAIM] - Node CLAIM[0cd72d3a-d2e7-4fb6-8ab8-c28c28dc5561] monitor closed 
[INFO ] 2024-05-23 18:59:07.457 - [任务 14][CLAIM] - Node CLAIM[0cd72d3a-d2e7-4fb6-8ab8-c28c28dc5561] close complete, cost 158 ms 
[INFO ] 2024-05-23 18:59:07.464 - [任务 14][test1] - Node test1[60eb58c1-78cc-432a-9acd-8a7aab0ad242] running status set to false 
[INFO ] 2024-05-23 18:59:07.489 - [任务 14][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-60eb58c1-78cc-432a-9acd-8a7aab0ad242 
[INFO ] 2024-05-23 18:59:07.489 - [任务 14][test1] - PDK connector node released: HazelcastTargetPdkDataNode-60eb58c1-78cc-432a-9acd-8a7aab0ad242 
[INFO ] 2024-05-23 18:59:07.490 - [任务 14][test1] - Node test1[60eb58c1-78cc-432a-9acd-8a7aab0ad242] schema data cleaned 
[INFO ] 2024-05-23 18:59:07.490 - [任务 14][test1] - Node test1[60eb58c1-78cc-432a-9acd-8a7aab0ad242] monitor closed 
[INFO ] 2024-05-23 18:59:07.692 - [任务 14][test1] - Node test1[60eb58c1-78cc-432a-9acd-8a7aab0ad242] close complete, cost 42 ms 
[INFO ] 2024-05-23 18:59:10.676 - [任务 14] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-23 18:59:10.676 - [任务 14] - Stopped task aspect(s) 
[INFO ] 2024-05-23 18:59:10.676 - [任务 14] - Snapshot order controller have been removed 
[INFO ] 2024-05-23 18:59:10.728 - [任务 14] - Remove memory task client succeed, task: 任务 14[664dbec0279b092c189a58cf] 
[INFO ] 2024-05-23 18:59:10.732 - [任务 14] - Destroy memory task client cache succeed, task: 任务 14[664dbec0279b092c189a58cf] 
