[INFO ] 2024-07-18 12:22:02.451 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480] - Task initialization... 
[INFO ] 2024-07-18 12:22:02.655 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480] - Start task milestones: 6698985f8315b25db9f5479e(t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480) 
[INFO ] 2024-07-18 12:22:02.740 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 12:22:02.924 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480] - The engine receives t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 12:22:02.991 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[4ef4937a-fa22-4210-a56c-1ff9ef582032] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:22:02.991 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[36a9b0ff-6c29-4d99-9cf3-2845b2079d1c] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:22:02.991 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[36a9b0ff-6c29-4d99-9cf3-2845b2079d1c] preload schema finished, cost 1 ms 
[INFO ] 2024-07-18 12:22:02.991 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[4ef4937a-fa22-4210-a56c-1ff9ef582032] preload schema finished, cost 2 ms 
[INFO ] 2024-07-18 12:22:03.389 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Source node "qa_mongodb_repl_42240_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 12:22:03.389 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Source node "qa_mongodb_repl_42240_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 12:22:03.389 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 12:22:03.517 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mysql_repl_33306_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 12:22:03.660 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - batch offset found: {},stream offset found: {"cdcOffset":1721276523,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 12:22:03.823 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 12:22:03.824 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Starting batch read, table name: t_4_2709, offset: null 
[INFO ] 2024-07-18 12:22:03.824 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Table t_4_2709 is going to be initial synced 
[INFO ] 2024-07-18 12:22:03.850 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Table [t_4_2709] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 12:22:03.851 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Query table 't_4_2709' counts: 5 
[INFO ] 2024-07-18 12:22:03.851 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 12:22:03.851 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 12:22:03.851 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 12:22:03.859 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Starting stream read, table list: [t_4_2709, _tapdata_heartbeat_table], offset: {"cdcOffset":1721276523,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 12:22:03.860 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [t_4_2709, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 12:24:20.931 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[36a9b0ff-6c29-4d99-9cf3-2845b2079d1c] running status set to false 
[INFO ] 2024-07-18 12:24:20.947 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-36a9b0ff-6c29-4d99-9cf3-2845b2079d1c 
[INFO ] 2024-07-18 12:24:20.947 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-36a9b0ff-6c29-4d99-9cf3-2845b2079d1c 
[INFO ] 2024-07-18 12:24:20.947 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[36a9b0ff-6c29-4d99-9cf3-2845b2079d1c] schema data cleaned 
[INFO ] 2024-07-18 12:24:20.948 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[36a9b0ff-6c29-4d99-9cf3-2845b2079d1c] monitor closed 
[INFO ] 2024-07-18 12:24:20.949 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[36a9b0ff-6c29-4d99-9cf3-2845b2079d1c] close complete, cost 20 ms 
[INFO ] 2024-07-18 12:24:20.949 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[4ef4937a-fa22-4210-a56c-1ff9ef582032] running status set to false 
[INFO ] 2024-07-18 12:24:20.974 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-4ef4937a-fa22-4210-a56c-1ff9ef582032 
[INFO ] 2024-07-18 12:24:20.974 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-4ef4937a-fa22-4210-a56c-1ff9ef582032 
[INFO ] 2024-07-18 12:24:20.974 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[4ef4937a-fa22-4210-a56c-1ff9ef582032] schema data cleaned 
[INFO ] 2024-07-18 12:24:20.974 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[4ef4937a-fa22-4210-a56c-1ff9ef582032] monitor closed 
[INFO ] 2024-07-18 12:24:21.178 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[4ef4937a-fa22-4210-a56c-1ff9ef582032] close complete, cost 26 ms 
[INFO ] 2024-07-18 12:24:21.582 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480][qa_mongodb_repl_42240_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 12:24:24.892 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 12:24:24.893 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@287a1c6b 
[INFO ] 2024-07-18 12:24:25.039 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480] - Stop task milestones: 6698985f8315b25db9f5479e(t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480)  
[INFO ] 2024-07-18 12:24:25.039 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480] - Stopped task aspect(s) 
[INFO ] 2024-07-18 12:24:25.039 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 12:24:25.085 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480] - Remove memory task client succeed, task: t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480[6698985f8315b25db9f5479e] 
[INFO ] 2024-07-18 12:24:25.085 - [t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480] - Destroy memory task client cache succeed, task: t_4.2709-mdb_to_mysql_embedded_document_array_1717403468657_3537-1721276480[6698985f8315b25db9f5479e] 
