[INFO ] 2024-07-17 19:10:33.921 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622] - Start task milestones: 6697a69fb92eda1a86f52599(t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622) 
[INFO ] 2024-07-17 19:10:33.921 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622] - Task initialization... 
[INFO ] 2024-07-17 19:10:34.107 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-17 19:10:34.223 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622] - The engine receives t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 19:10:34.265 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[d0ebe45c-0ede-463f-8d4b-0cdbeb609f8e] start preload schema,table counts: 1 
[INFO ] 2024-07-17 19:10:34.265 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[98818eb8-fb8f-430e-b1d4-6eba567dff95] start preload schema,table counts: 1 
[INFO ] 2024-07-17 19:10:34.265 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[d0ebe45c-0ede-463f-8d4b-0cdbeb609f8e] preload schema finished, cost 1 ms 
[INFO ] 2024-07-17 19:10:34.265 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[98818eb8-fb8f-430e-b1d4-6eba567dff95] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 19:10:34.606 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-17 19:10:34.611 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Source node "qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-17 19:10:34.611 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Source node "qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-17 19:10:34.611 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 19:10:34.611 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-17 19:10:34.738 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-17 19:10:34.744 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Starting batch read, table name: mock_1000w, offset: null 
[INFO ] 2024-07-17 19:10:34.744 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Table mock_1000w is going to be initial synced 
[INFO ] 2024-07-17 19:10:34.756 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Start mock_1000w batch read 
[INFO ] 2024-07-17 19:10:34.756 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Query table 'mock_1000w' counts: 10000000 
[INFO ] 2024-07-17 19:11:13.103 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Compile mock_1000w batch read 
[INFO ] 2024-07-17 19:11:13.104 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Table [mock_1000w] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-17 19:11:13.104 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-17 19:11:19.180 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[98818eb8-fb8f-430e-b1d4-6eba567dff95] running status set to false 
[INFO ] 2024-07-17 19:11:19.188 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Stop connector 
[INFO ] 2024-07-17 19:11:19.188 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[d0ebe45c-0ede-463f-8d4b-0cdbeb609f8e] running status set to false 
[INFO ] 2024-07-17 19:11:19.191 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-98818eb8-fb8f-430e-b1d4-6eba567dff95 
[INFO ] 2024-07-17 19:11:19.191 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-98818eb8-fb8f-430e-b1d4-6eba567dff95 
[INFO ] 2024-07-17 19:11:19.192 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[98818eb8-fb8f-430e-b1d4-6eba567dff95] schema data cleaned 
[INFO ] 2024-07-17 19:11:19.192 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[98818eb8-fb8f-430e-b1d4-6eba567dff95] monitor closed 
[INFO ] 2024-07-17 19:11:19.193 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[98818eb8-fb8f-430e-b1d4-6eba567dff95] close complete, cost 13 ms 
[INFO ] 2024-07-17 19:11:19.213 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Stop connector: first 1721214634748 91ms, last 1721214673093 3019ms, counts: 10000000/41364ms, min: 8, max: 3025, QPS: 243902/s 
[INFO ] 2024-07-17 19:11:19.216 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-d0ebe45c-0ede-463f-8d4b-0cdbeb609f8e 
[INFO ] 2024-07-17 19:11:19.216 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-d0ebe45c-0ede-463f-8d4b-0cdbeb609f8e 
[INFO ] 2024-07-17 19:11:19.217 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[d0ebe45c-0ede-463f-8d4b-0cdbeb609f8e] schema data cleaned 
[INFO ] 2024-07-17 19:11:19.217 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[d0ebe45c-0ede-463f-8d4b-0cdbeb609f8e] monitor closed 
[INFO ] 2024-07-17 19:11:19.217 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[d0ebe45c-0ede-463f-8d4b-0cdbeb609f8e] close complete, cost 35 ms 
[INFO ] 2024-07-17 19:11:19.907 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 19:11:19.907 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5083b83f 
[INFO ] 2024-07-17 19:11:19.924 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622] - Stop task milestones: 6697a69fb92eda1a86f52599(t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622)  
[INFO ] 2024-07-17 19:11:20.069 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622] - Stopped task aspect(s) 
[INFO ] 2024-07-17 19:11:20.069 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 19:11:20.120 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622] - Remove memory task client succeed, task: t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622[6697a69fb92eda1a86f52599] 
[INFO ] 2024-07-17 19:11:20.120 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622] - Destroy memory task client cache succeed, task: t_1.2-mock_to_mock_3fields_1717403468657_3537-1721214622[6697a69fb92eda1a86f52599] 
