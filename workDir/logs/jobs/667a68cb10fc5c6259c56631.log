[INFO ] 2024-06-25 14:51:43.882 - [任务 29] - Start task milestones: 667a68cb10fc5c6259c56631(任务 29) 
[INFO ] 2024-06-25 14:51:43.882 - [任务 29] - Task initialization... 
[INFO ] 2024-06-25 14:51:44.055 - [任务 29] - No<PERSON> performs snapshot read asynchronously 
[INFO ] 2024-06-25 14:51:44.056 - [任务 29] - The engine receives 任务 29 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 14:51:44.111 - [任务 29][SouceMysql] - Node SouceMysql[48f462cf-d965-47bb-863a-418dbf1f0cae] start preload schema,table counts: 1 
[INFO ] 2024-06-25 14:51:44.111 - [任务 29][表编辑] - Node 表编辑[644f083a-d238-4cb0-91e4-6345a7048cb1] start preload schema,table counts: 1 
[INFO ] 2024-06-25 14:51:44.111 - [任务 29][SourceMongo] - Node SourceMongo[f2e8d733-4090-4ff4-8525-edcceb1c0663] start preload schema,table counts: 1 
[INFO ] 2024-06-25 14:51:44.111 - [任务 29][表编辑] - Node 表编辑[644f083a-d238-4cb0-91e4-6345a7048cb1] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:51:44.111 - [任务 29][SouceMysql] - Node SouceMysql[48f462cf-d965-47bb-863a-418dbf1f0cae] preload schema finished, cost 1 ms 
[INFO ] 2024-06-25 14:51:44.111 - [任务 29][SourceMongo] - Node SourceMongo[f2e8d733-4090-4ff4-8525-edcceb1c0663] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:51:44.746 - [任务 29][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-06-25 14:51:44.746 - [任务 29][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-06-25 14:51:44.746 - [任务 29][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 14:51:44.853 - [任务 29][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719298304,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 14:51:44.944 - [任务 29] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 14:51:44.944 - [任务 29][SourceMongo] - Initial sync started 
[INFO ] 2024-06-25 14:51:44.946 - [任务 29][SourceMongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-25 14:51:44.946 - [任务 29][SourceMongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-25 14:51:44.987 - [任务 29][SourceMongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-25 14:51:44.987 - [任务 29][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 14:51:44.987 - [任务 29][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 14:51:45.149 - [任务 29][SourceMongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 14:51:45.149 - [任务 29][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 14:51:45.149 - [任务 29][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-25 14:51:45.149 - [任务 29][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 14:51:45.151 - [任务 29][SourceMongo] - Starting stream read, table list: [CLAIM], offset: {"cdcOffset":1719298304,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 14:51:45.357 - [任务 29][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-25 14:54:03.879 - [任务 29][SourceMongo] - Node SourceMongo[f2e8d733-4090-4ff4-8525-edcceb1c0663] running status set to false 
[INFO ] 2024-06-25 14:54:03.881 - [任务 29] - Stop task milestones: 667a68cb10fc5c6259c56631(任务 29)  
[INFO ] 2024-06-25 14:54:03.915 - [任务 29][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-f2e8d733-4090-4ff4-8525-edcceb1c0663 
[INFO ] 2024-06-25 14:54:03.915 - [任务 29][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-f2e8d733-4090-4ff4-8525-edcceb1c0663 
[INFO ] 2024-06-25 14:54:03.916 - [任务 29][SourceMongo] - Node SourceMongo[f2e8d733-4090-4ff4-8525-edcceb1c0663] schema data cleaned 
[INFO ] 2024-06-25 14:54:03.916 - [任务 29][SourceMongo] - Node SourceMongo[f2e8d733-4090-4ff4-8525-edcceb1c0663] monitor closed 
[INFO ] 2024-06-25 14:54:03.917 - [任务 29][SourceMongo] - Node SourceMongo[f2e8d733-4090-4ff4-8525-edcceb1c0663] close complete, cost 65 ms 
[INFO ] 2024-06-25 14:54:03.918 - [任务 29][表编辑] - Node 表编辑[644f083a-d238-4cb0-91e4-6345a7048cb1] running status set to false 
[INFO ] 2024-06-25 14:54:03.918 - [任务 29][表编辑] - Node 表编辑[644f083a-d238-4cb0-91e4-6345a7048cb1] schema data cleaned 
[INFO ] 2024-06-25 14:54:03.918 - [任务 29][表编辑] - Node 表编辑[644f083a-d238-4cb0-91e4-6345a7048cb1] monitor closed 
[INFO ] 2024-06-25 14:54:03.919 - [任务 29][表编辑] - Node 表编辑[644f083a-d238-4cb0-91e4-6345a7048cb1] close complete, cost 1 ms 
[INFO ] 2024-06-25 14:54:03.919 - [任务 29][SouceMysql] - Node SouceMysql[48f462cf-d965-47bb-863a-418dbf1f0cae] running status set to false 
[INFO ] 2024-06-25 14:54:03.947 - [任务 29][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-48f462cf-d965-47bb-863a-418dbf1f0cae 
[INFO ] 2024-06-25 14:54:03.947 - [任务 29][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-48f462cf-d965-47bb-863a-418dbf1f0cae 
[INFO ] 2024-06-25 14:54:03.948 - [任务 29][SouceMysql] - Node SouceMysql[48f462cf-d965-47bb-863a-418dbf1f0cae] schema data cleaned 
[INFO ] 2024-06-25 14:54:03.948 - [任务 29][SouceMysql] - Node SouceMysql[48f462cf-d965-47bb-863a-418dbf1f0cae] monitor closed 
[INFO ] 2024-06-25 14:54:03.949 - [任务 29][SouceMysql] - Node SouceMysql[48f462cf-d965-47bb-863a-418dbf1f0cae] close complete, cost 30 ms 
[INFO ] 2024-06-25 14:54:05.414 - [任务 29] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 14:54:05.415 - [任务 29] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3b301bd 
[INFO ] 2024-06-25 14:54:05.415 - [任务 29] - Stopped task aspect(s) 
[INFO ] 2024-06-25 14:54:05.415 - [任务 29] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 14:54:05.452 - [任务 29] - Remove memory task client succeed, task: 任务 29[667a68cb10fc5c6259c56631] 
[INFO ] 2024-06-25 14:54:05.452 - [任务 29] - Destroy memory task client cache succeed, task: 任务 29[667a68cb10fc5c6259c56631] 
