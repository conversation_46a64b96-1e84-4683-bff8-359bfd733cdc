[INFO ] 2024-07-15 12:04:02.671 - [来自Mongo的共享挖掘任务] - Start task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务) 
[INFO ] 2024-07-15 12:04:02.764 - [来自Mongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 12:04:02.772 - [来自Mongo的共享挖掘任务] - The engine receives 来自Mongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 12:04:02.901 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] start preload schema,table counts: 1 
[INFO ] 2024-07-15 12:04:02.903 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-15 12:04:02.903 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 12:04:02.903 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 12:04:02.927 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 12:04:03.208 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-15 12:04:03.701 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" read batch size: 2000 
[INFO ] 2024-07-15 12:04:03.703 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" event queue capacity: 4000 
[INFO ] 2024-07-15 12:04:03.703 - [来自Mongo的共享挖掘任务][Mongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 12:04:03.904 - [来自Mongo的共享挖掘任务][Mongo] - batch offset found: {},stream offset found: {"cdcOffset":1721016243,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 12:04:03.959 - [来自Mongo的共享挖掘任务][Mongo] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721016243,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 12:04:03.960 - [来自Mongo的共享挖掘任务][Mongo] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 14:09:52.593 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] running status set to false 
[WARN ] 2024-07-15 14:09:52.636 - [来自Mongo的共享挖掘任务][Mongo] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 14:09:52.637 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 14:09:52.638 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] schema data cleaned 
[INFO ] 2024-07-15 14:09:52.638 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] monitor closed 
[INFO ] 2024-07-15 14:17:40.374 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] close complete, cost 61 ms 
[INFO ] 2024-07-15 14:17:40.579 - [来自Mongo的共享挖掘任务] - Start task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务) 
[INFO ] 2024-07-15 14:17:41.079 - [来自Mongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 14:17:41.079 - [来自Mongo的共享挖掘任务] - The engine receives 来自Mongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 14:17:41.606 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:17:41.607 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 14:17:41.614 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-15 14:17:41.617 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 14:17:41.676 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 14:17:41.798 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 14:17:41.798 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-15 14:17:42.687 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" read batch size: 2000 
[INFO ] 2024-07-15 14:17:42.689 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" event queue capacity: 4000 
[INFO ] 2024-07-15 14:17:42.706 - [来自Mongo的共享挖掘任务][Mongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 14:17:42.823 - [来自Mongo的共享挖掘任务][Mongo] - batch offset found: {},stream offset found: {"_data":{"value":"8266949FD2000000022B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F6964006466949FB566AB5EDE8A8579950004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-15 14:17:42.825 - [来自Mongo的共享挖掘任务][Mongo] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"_data":{"value":"8266949FD2000000022B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F6964006466949FB566AB5EDE8A8579950004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-15 14:17:43.027 - [来自Mongo的共享挖掘任务][Mongo] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 14:31:24.341 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] running status set to false 
[INFO ] 2024-07-15 14:31:24.344 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 14:31:24.344 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 14:31:24.344 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] schema data cleaned 
[INFO ] 2024-07-15 14:31:24.344 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] monitor closed 
[INFO ] 2024-07-15 14:31:24.345 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] close complete, cost 31 ms 
[INFO ] 2024-07-15 14:31:24.345 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] running status set to false 
[INFO ] 2024-07-15 14:31:24.353 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-15 14:31:24.362 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-15 14:31:24.365 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] schema data cleaned 
[INFO ] 2024-07-15 14:31:24.365 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] monitor closed 
[INFO ] 2024-07-15 14:31:24.366 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] close complete, cost 8 ms 
[INFO ] 2024-07-15 14:31:28.421 - [来自Mongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 14:31:28.422 - [来自Mongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1c3e5d50 
[INFO ] 2024-07-15 14:31:28.423 - [来自Mongo的共享挖掘任务] - Stop task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务)  
[INFO ] 2024-07-15 14:31:28.562 - [来自Mongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-15 14:31:28.563 - [来自Mongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 14:31:28.564 - [来自Mongo的共享挖掘任务] - Remove memory task client succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 14:31:28.770 - [来自Mongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 14:37:06.924 - [来自Mongo的共享挖掘任务] - Start task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务) 
[INFO ] 2024-07-15 14:37:06.924 - [来自Mongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 14:37:07.019 - [来自Mongo的共享挖掘任务] - The engine receives 来自Mongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 14:37:07.021 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] start preload schema,table counts: 1 
[INFO ] 2024-07-15 14:37:07.023 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-15 14:37:07.024 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 14:37:07.024 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 14:37:07.038 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 14:37:07.044 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 14:37:07.238 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-15 14:37:07.238 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" read batch size: 2000 
[INFO ] 2024-07-15 14:37:07.238 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" event queue capacity: 4000 
[INFO ] 2024-07-15 14:37:07.239 - [来自Mongo的共享挖掘任务][Mongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 14:37:07.333 - [来自Mongo的共享挖掘任务][Mongo] - batch offset found: {},stream offset found: {"_data":{"value":"8266949FD2000000022B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F6964006466949FB566AB5EDE8A8579950004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-15 14:37:07.333 - [来自Mongo的共享挖掘任务][Mongo] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"_data":{"value":"8266949FD2000000022B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F6964006466949FB566AB5EDE8A8579950004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-15 14:37:07.544 - [来自Mongo的共享挖掘任务][Mongo] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 15:17:41.873 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 16:17:42.092 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 17:17:41.989 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 17:59:36.143 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@6b34f9be: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T09:59:32.671Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721037574000,"tableId":"_tapdata_heartbeat_table","time":1721037574660,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721037574000, sourceSerialNo=null} 
[ERROR] 2024-07-15 17:59:36.148 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@6b34f9be: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T09:59:32.671Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721037574000,"tableId":"_tapdata_heartbeat_table","time":1721037574660,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721037574000, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@6b34f9be: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T09:59:32.671Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721037574000,"tableId":"_tapdata_heartbeat_table","time":1721037574660,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721037574000, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$getConstruct$17(TapTargetShareCDCNode.java:537)
	java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1688)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.getConstruct(TapTargetShareCDCNode.java:532)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:633)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.writeLogContents(TapTargetShareCDCNode.java:415)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:601)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:582)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:500)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:546)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.processShareLog(TapTargetShareCDCNode.java:255)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkShareCDCNode.processShareLog(HazelcastTargetPdkShareCDCNode.java:55)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:627)
	... 12 more
Caused by: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.handleTapRecordEvents(TapTargetShareCDCNode.java:288)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.handleTapEvents(TapTargetShareCDCNode.java:268)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.processShareLog(TapTargetShareCDCNode.java:250)
	... 14 more
Caused by: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:641)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.writeLogContents(TapTargetShareCDCNode.java:415)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$handleTapRecordEvents$5(TapTargetShareCDCNode.java:296)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 17 more
Caused by: java.lang.RuntimeException: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$getConstruct$17(TapTargetShareCDCNode.java:537)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1688)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.getConstruct(TapTargetShareCDCNode.java:532)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:633)
	... 20 more

[INFO ] 2024-07-15 17:59:36.149 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Job suspend in error handle 
[INFO ] 2024-07-15 17:59:36.205 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] running status set to false 
[INFO ] 2024-07-15 17:59:36.205 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 17:59:36.205 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 17:59:36.205 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] schema data cleaned 
[INFO ] 2024-07-15 17:59:36.206 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] monitor closed 
[INFO ] 2024-07-15 17:59:36.207 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] close complete, cost 12 ms 
[INFO ] 2024-07-15 17:59:36.207 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] running status set to false 
[INFO ] 2024-07-15 17:59:36.207 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-15 17:59:36.207 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-15 17:59:36.207 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] schema data cleaned 
[INFO ] 2024-07-15 17:59:36.207 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] monitor closed 
[INFO ] 2024-07-15 17:59:36.207 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] close complete, cost 0 ms 
[INFO ] 2024-07-15 17:59:36.230 - [来自Mongo的共享挖掘任务] - Task [来自Mongo的共享挖掘任务] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-15 17:59:36.230 - [来自Mongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 17:59:36.230 - [来自Mongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@61204625 
[INFO ] 2024-07-15 17:59:36.230 - [来自Mongo的共享挖掘任务] - Stop task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务)  
[INFO ] 2024-07-15 17:59:36.343 - [来自Mongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-15 17:59:36.343 - [来自Mongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 17:59:36.367 - [来自Mongo的共享挖掘任务] - Remove memory task client succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 17:59:36.370 - [来自Mongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 18:16:35.392 - [来自Mongo的共享挖掘任务] - Start task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务) 
[INFO ] 2024-07-15 18:16:35.452 - [来自Mongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 18:16:35.453 - [来自Mongo的共享挖掘任务] - The engine receives 来自Mongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 18:16:35.543 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] start preload schema,table counts: 1 
[INFO ] 2024-07-15 18:16:35.543 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-15 18:16:35.543 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 18:16:35.543 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 18:16:35.560 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 18:16:35.560 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 18:16:35.703 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-15 18:16:35.703 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" read batch size: 2000 
[INFO ] 2024-07-15 18:16:35.703 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" event queue capacity: 4000 
[INFO ] 2024-07-15 18:16:35.704 - [来自Mongo的共享挖掘任务][Mongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 18:16:35.738 - [来自Mongo的共享挖掘任务][Mongo] - batch offset found: {},stream offset found: {"_data":{"value":"8266949FD2000000022B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F6964006466949FB566AB5EDE8A8579950004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-15 18:16:35.738 - [来自Mongo的共享挖掘任务][Mongo] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"_data":{"value":"8266949FD2000000022B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F6964006466949FB566AB5EDE8A8579950004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-15 18:16:35.864 - [来自Mongo的共享挖掘任务][Mongo] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 18:16:35.864 - [来自Mongo的共享挖掘任务][Mongo] - Exception skipping - The current exception does not match the skip exception strategy, message: Increment start point exceeds the log time window of mongodb, start point: {"_data": "8266949FD2000000022B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F6964006466949FB566AB5EDE8A8579950004"} 
[ERROR] 2024-07-15 18:16:35.873 - [来自Mongo的共享挖掘任务][Mongo] - com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1721038595, "i": 32}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721038595, "i": 32}}, "signature": {"hash": {"$binary": {"base64": "mGYlYYq2hLZUj/txeyoaPcLb9jg=", "subType": "00"}}, "keyId": 7376103549123428362}}} <-- Error Message -->
com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1721038595, "i": 32}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721038595, "i": 32}}, "signature": {"hash": {"$binary": {"base64": "mGYlYYq2hLZUj/txeyoaPcLb9jg=", "subType": "00"}}, "keyId": 7376103549123428362}}}

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1721038595, "i": 32}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721038595, "i": 32}}, "signature": {"hash": {"$binary": {"base64": "mGYlYYq2hLZUj/txeyoaPcLb9jg=", "subType": "00"}}, "keyId": 7376103549123428362}}}
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:114)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:643)
	...

<-- Full Stack Trace -->
com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1721038595, "i": 32}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721038595, "i": 32}}, "signature": {"hash": {"$binary": {"base64": "mGYlYYq2hLZUj/txeyoaPcLb9jg=", "subType": "00"}}, "keyId": 7376103549123428362}}}
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:258)
	at io.tapdata.mongodb.MongodbConnector.doStreamRead(MongodbConnector.java:1559)
	at io.tapdata.mongodb.MongodbConnector.streamRead(MongodbConnector.java:1553)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:612)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1721038595, "i": 32}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721038595, "i": 32}}, "signature": {"hash": {"$binary": {"base64": "mGYlYYq2hLZUj/txeyoaPcLb9jg=", "subType": "00"}}, "keyId": 7376103549123428362}}}
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:114)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:643)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:73)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:204)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:122)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:87)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:76)
	at com.mongodb.internal.connection.DefaultServer$OperationCountTrackingConnection.command(DefaultServer.java:288)
	at com.mongodb.internal.operation.CommandOperationHelper.createReadCommandAndExecute(CommandOperationHelper.java:239)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$4(CommandOperationHelper.java:220)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$0(OperationHelper.java:358)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$1(OperationHelper.java:357)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.withSourceAndConnection(OperationHelper.java:356)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$5(CommandOperationHelper.java:218)
	at com.mongodb.internal.async.function.RetryingSyncSupplier.get(RetryingSyncSupplier.java:67)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:223)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:204)
	at com.mongodb.internal.operation.AggregateOperationImpl.execute(AggregateOperationImpl.java:191)
	at com.mongodb.internal.operation.ChangeStreamOperation.lambda$execute$0(ChangeStreamOperation.java:187)
	at com.mongodb.internal.operation.OperationHelper.withReadConnectionSource(OperationHelper.java:321)
	at com.mongodb.internal.operation.ChangeStreamOperation.execute(ChangeStreamOperation.java:185)
	at com.mongodb.internal.operation.ChangeStreamOperation.execute(ChangeStreamOperation.java:55)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:185)
	at com.mongodb.client.internal.ChangeStreamIterableImpl.execute(ChangeStreamIterableImpl.java:212)
	at com.mongodb.client.internal.ChangeStreamIterableImpl.cursor(ChangeStreamIterableImpl.java:187)
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:104)
	... 22 more

[INFO ] 2024-07-15 18:16:35.881 - [来自Mongo的共享挖掘任务][Mongo] - Job suspend in error handle 
[INFO ] 2024-07-15 18:16:36.319 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] running status set to false 
[INFO ] 2024-07-15 18:16:36.319 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 18:16:36.319 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 18:16:36.319 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] schema data cleaned 
[INFO ] 2024-07-15 18:16:36.321 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] monitor closed 
[INFO ] 2024-07-15 18:16:36.321 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] close complete, cost 13 ms 
[INFO ] 2024-07-15 18:16:36.321 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] running status set to false 
[INFO ] 2024-07-15 18:16:36.322 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-15 18:16:36.322 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-15 18:16:36.322 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] schema data cleaned 
[INFO ] 2024-07-15 18:16:36.322 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] monitor closed 
[INFO ] 2024-07-15 18:16:36.323 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] close complete, cost 1 ms 
[INFO ] 2024-07-15 18:16:39.063 - [来自Mongo的共享挖掘任务] - Task [来自Mongo的共享挖掘任务] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-15 18:16:39.063 - [来自Mongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 18:16:39.063 - [来自Mongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@60e0031a 
[INFO ] 2024-07-15 18:16:39.064 - [来自Mongo的共享挖掘任务] - Stop task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务)  
[INFO ] 2024-07-15 18:16:39.190 - [来自Mongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-15 18:16:39.190 - [来自Mongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 18:16:39.210 - [来自Mongo的共享挖掘任务] - Remove memory task client succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 18:16:39.212 - [来自Mongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 18:17:20.106 - [来自Mongo的共享挖掘任务] - Start task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务) 
[INFO ] 2024-07-15 18:17:20.106 - [来自Mongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 18:17:20.195 - [来自Mongo的共享挖掘任务] - The engine receives 来自Mongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 18:17:20.196 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] start preload schema,table counts: 1 
[INFO ] 2024-07-15 18:17:20.196 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 18:17:20.197 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-15 18:17:20.202 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 18:17:20.232 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 18:17:20.232 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 18:17:20.423 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-15 18:17:20.423 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" read batch size: 2000 
[INFO ] 2024-07-15 18:17:20.423 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" event queue capacity: 4000 
[INFO ] 2024-07-15 18:17:20.427 - [来自Mongo的共享挖掘任务][Mongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 18:17:20.472 - [来自Mongo的共享挖掘任务][Mongo] - batch offset found: {},stream offset found: {"_data":{"value":"8266949FD2000000022B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F6964006466949FB566AB5EDE8A8579950004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-15 18:17:20.472 - [来自Mongo的共享挖掘任务][Mongo] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"_data":{"value":"8266949FD2000000022B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F6964006466949FB566AB5EDE8A8579950004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-15 18:17:20.584 - [来自Mongo的共享挖掘任务][Mongo] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 18:17:20.584 - [来自Mongo的共享挖掘任务][Mongo] - Exception skipping - The current exception does not match the skip exception strategy, message: Increment start point exceeds the log time window of mongodb, start point: {"_data": "8266949FD2000000022B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F6964006466949FB566AB5EDE8A8579950004"} 
[ERROR] 2024-07-15 18:17:20.601 - [来自Mongo的共享挖掘任务][Mongo] - com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1721038640, "i": 27}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721038640, "i": 27}}, "signature": {"hash": {"$binary": {"base64": "p3s5C+cuqYBOJc4znVrIu/VjaUU=", "subType": "00"}}, "keyId": 7376103549123428362}}} <-- Error Message -->
com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1721038640, "i": 27}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721038640, "i": 27}}, "signature": {"hash": {"$binary": {"base64": "p3s5C+cuqYBOJc4znVrIu/VjaUU=", "subType": "00"}}, "keyId": 7376103549123428362}}}

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1721038640, "i": 27}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721038640, "i": 27}}, "signature": {"hash": {"$binary": {"base64": "p3s5C+cuqYBOJc4znVrIu/VjaUU=", "subType": "00"}}, "keyId": 7376103549123428362}}}
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:114)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:643)
	...

<-- Full Stack Trace -->
com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1721038640, "i": 27}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721038640, "i": 27}}, "signature": {"hash": {"$binary": {"base64": "p3s5C+cuqYBOJc4znVrIu/VjaUU=", "subType": "00"}}, "keyId": 7376103549123428362}}}
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:258)
	at io.tapdata.mongodb.MongodbConnector.doStreamRead(MongodbConnector.java:1559)
	at io.tapdata.mongodb.MongodbConnector.streamRead(MongodbConnector.java:1553)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:612)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1721038640, "i": 27}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721038640, "i": 27}}, "signature": {"hash": {"$binary": {"base64": "p3s5C+cuqYBOJc4znVrIu/VjaUU=", "subType": "00"}}, "keyId": 7376103549123428362}}}
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:114)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:643)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:73)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:204)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:122)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:87)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:76)
	at com.mongodb.internal.connection.DefaultServer$OperationCountTrackingConnection.command(DefaultServer.java:288)
	at com.mongodb.internal.operation.CommandOperationHelper.createReadCommandAndExecute(CommandOperationHelper.java:239)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$4(CommandOperationHelper.java:220)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$0(OperationHelper.java:358)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$1(OperationHelper.java:357)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.withSourceAndConnection(OperationHelper.java:356)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$5(CommandOperationHelper.java:218)
	at com.mongodb.internal.async.function.RetryingSyncSupplier.get(RetryingSyncSupplier.java:67)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:223)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:204)
	at com.mongodb.internal.operation.AggregateOperationImpl.execute(AggregateOperationImpl.java:191)
	at com.mongodb.internal.operation.ChangeStreamOperation.lambda$execute$0(ChangeStreamOperation.java:187)
	at com.mongodb.internal.operation.OperationHelper.withReadConnectionSource(OperationHelper.java:321)
	at com.mongodb.internal.operation.ChangeStreamOperation.execute(ChangeStreamOperation.java:185)
	at com.mongodb.internal.operation.ChangeStreamOperation.execute(ChangeStreamOperation.java:55)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:185)
	at com.mongodb.client.internal.ChangeStreamIterableImpl.execute(ChangeStreamIterableImpl.java:212)
	at com.mongodb.client.internal.ChangeStreamIterableImpl.cursor(ChangeStreamIterableImpl.java:187)
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:104)
	... 22 more

[INFO ] 2024-07-15 18:17:20.602 - [来自Mongo的共享挖掘任务][Mongo] - Job suspend in error handle 
[INFO ] 2024-07-15 18:17:21.065 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] running status set to false 
[INFO ] 2024-07-15 18:17:21.065 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 18:17:21.065 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 18:17:21.066 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] schema data cleaned 
[INFO ] 2024-07-15 18:17:21.066 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] monitor closed 
[INFO ] 2024-07-15 18:17:21.069 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] close complete, cost 26 ms 
[INFO ] 2024-07-15 18:17:21.070 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] running status set to false 
[INFO ] 2024-07-15 18:17:21.071 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-15 18:17:21.071 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-15 18:17:21.071 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] schema data cleaned 
[INFO ] 2024-07-15 18:17:21.071 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] monitor closed 
[INFO ] 2024-07-15 18:17:21.071 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] close complete, cost 1 ms 
[INFO ] 2024-07-15 18:17:25.300 - [来自Mongo的共享挖掘任务] - Task [来自Mongo的共享挖掘任务] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-15 18:17:25.308 - [来自Mongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 18:17:25.309 - [来自Mongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@955dc4 
[INFO ] 2024-07-15 18:17:25.309 - [来自Mongo的共享挖掘任务] - Stop task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务)  
[INFO ] 2024-07-15 18:17:25.424 - [来自Mongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-15 18:17:25.424 - [来自Mongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 18:17:25.447 - [来自Mongo的共享挖掘任务] - Remove memory task client succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 18:17:25.450 - [来自Mongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 18:17:42.067 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 18:34:04.895 - [来自Mongo的共享挖掘任务] - Start task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务) 
[INFO ] 2024-07-15 18:34:04.932 - [来自Mongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 18:34:04.933 - [来自Mongo的共享挖掘任务] - The engine receives 来自Mongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 18:34:04.967 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] start preload schema,table counts: 1 
[INFO ] 2024-07-15 18:34:04.967 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 18:34:04.979 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-15 18:34:04.985 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 18:34:04.995 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 18:34:04.995 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 18:34:05.196 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-15 18:34:05.510 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" read batch size: 2000 
[INFO ] 2024-07-15 18:34:05.510 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" event queue capacity: 4000 
[INFO ] 2024-07-15 18:34:05.510 - [来自Mongo的共享挖掘任务][Mongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 18:34:05.643 - [来自Mongo的共享挖掘任务][Mongo] - batch offset found: {},stream offset found: {"cdcOffset":1721039645,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 18:34:05.647 - [来自Mongo的共享挖掘任务][Mongo] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721039645,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 18:34:05.848 - [来自Mongo的共享挖掘任务][Mongo] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 18:34:11.441 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@4bc7e4ff: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T10:34:03.361Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721039645000,"tableId":"_tapdata_heartbeat_table","time":1721039645661,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721039645000, sourceSerialNo=null} 
[ERROR] 2024-07-15 18:34:11.451 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@4bc7e4ff: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T10:34:03.361Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721039645000,"tableId":"_tapdata_heartbeat_table","time":1721039645661,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721039645000, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@4bc7e4ff: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T10:34:03.361Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721039645000,"tableId":"_tapdata_heartbeat_table","time":1721039645661,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721039645000, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$getConstruct$17(TapTargetShareCDCNode.java:537)
	java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1688)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.getConstruct(TapTargetShareCDCNode.java:532)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:633)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.writeLogContents(TapTargetShareCDCNode.java:415)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:601)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:582)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:500)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:546)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.processShareLog(TapTargetShareCDCNode.java:255)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkShareCDCNode.processShareLog(HazelcastTargetPdkShareCDCNode.java:55)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:627)
	... 12 more
Caused by: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.handleTapRecordEvents(TapTargetShareCDCNode.java:288)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.handleTapEvents(TapTargetShareCDCNode.java:268)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.processShareLog(TapTargetShareCDCNode.java:250)
	... 14 more
Caused by: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:641)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.writeLogContents(TapTargetShareCDCNode.java:415)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$handleTapRecordEvents$5(TapTargetShareCDCNode.java:296)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 17 more
Caused by: java.lang.RuntimeException: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$getConstruct$17(TapTargetShareCDCNode.java:537)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1688)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.getConstruct(TapTargetShareCDCNode.java:532)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:633)
	... 20 more

[INFO ] 2024-07-15 18:34:11.464 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Job suspend in error handle 
[INFO ] 2024-07-15 18:34:11.909 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] running status set to false 
[INFO ] 2024-07-15 18:34:11.909 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 18:34:11.909 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 18:34:11.910 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] schema data cleaned 
[INFO ] 2024-07-15 18:34:11.910 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] monitor closed 
[INFO ] 2024-07-15 18:34:11.911 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] close complete, cost 11 ms 
[INFO ] 2024-07-15 18:34:11.912 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] running status set to false 
[INFO ] 2024-07-15 18:34:11.919 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-15 18:34:11.919 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-15 18:34:11.919 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] schema data cleaned 
[INFO ] 2024-07-15 18:34:11.919 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] monitor closed 
[INFO ] 2024-07-15 18:34:11.919 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] close complete, cost 7 ms 
[INFO ] 2024-07-15 18:34:16.396 - [来自Mongo的共享挖掘任务] - Task [来自Mongo的共享挖掘任务] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-15 18:34:16.405 - [来自Mongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 18:34:16.406 - [来自Mongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@169e980d 
[INFO ] 2024-07-15 18:34:16.406 - [来自Mongo的共享挖掘任务] - Stop task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务)  
[INFO ] 2024-07-15 18:34:16.518 - [来自Mongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-15 18:34:16.518 - [来自Mongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 18:34:16.530 - [来自Mongo的共享挖掘任务] - Remove memory task client succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 18:34:16.534 - [来自Mongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 18:51:41.810 - [来自Mongo的共享挖掘任务] - Start task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务) 
[INFO ] 2024-07-15 18:51:41.889 - [来自Mongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 18:51:41.889 - [来自Mongo的共享挖掘任务] - The engine receives 来自Mongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 18:51:41.945 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] start preload schema,table counts: 1 
[INFO ] 2024-07-15 18:51:41.945 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 18:51:41.952 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-15 18:51:41.952 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 18:51:48.534 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 18:51:48.561 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 18:51:48.561 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-15 18:51:49.044 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" read batch size: 2000 
[INFO ] 2024-07-15 18:51:49.044 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" event queue capacity: 4000 
[INFO ] 2024-07-15 18:51:49.044 - [来自Mongo的共享挖掘任务][Mongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 18:51:49.183 - [来自Mongo的共享挖掘任务][Mongo] - batch offset found: {},stream offset found: {"cdcOffset":1721040709,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 18:51:49.183 - [来自Mongo的共享挖掘任务][Mongo] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721040709,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 18:51:49.384 - [来自Mongo的共享挖掘任务][Mongo] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 18:52:59.445 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@abeeb4c: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T10:51:48.370Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721040709000,"tableId":"_tapdata_heartbeat_table","time":1721040709398,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721040709000, sourceSerialNo=null} 
[ERROR] 2024-07-15 18:52:59.445 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@abeeb4c: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T10:51:48.370Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721040709000,"tableId":"_tapdata_heartbeat_table","time":1721040709398,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721040709000, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@abeeb4c: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T10:51:48.370Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721040709000,"tableId":"_tapdata_heartbeat_table","time":1721040709398,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721040709000, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$getConstruct$17(TapTargetShareCDCNode.java:537)
	java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1688)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.getConstruct(TapTargetShareCDCNode.java:532)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:633)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.writeLogContents(TapTargetShareCDCNode.java:415)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:601)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:582)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:500)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:546)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.processShareLog(TapTargetShareCDCNode.java:255)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkShareCDCNode.processShareLog(HazelcastTargetPdkShareCDCNode.java:55)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:627)
	... 12 more
Caused by: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.handleTapRecordEvents(TapTargetShareCDCNode.java:288)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.handleTapEvents(TapTargetShareCDCNode.java:268)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.processShareLog(TapTargetShareCDCNode.java:250)
	... 14 more
Caused by: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:641)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.writeLogContents(TapTargetShareCDCNode.java:415)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$handleTapRecordEvents$5(TapTargetShareCDCNode.java:296)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 17 more
Caused by: java.lang.RuntimeException: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$getConstruct$17(TapTargetShareCDCNode.java:537)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1688)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.getConstruct(TapTargetShareCDCNode.java:532)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:633)
	... 20 more

[INFO ] 2024-07-15 18:52:59.654 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Job suspend in error handle 
[INFO ] 2024-07-15 18:52:59.946 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] running status set to false 
[INFO ] 2024-07-15 18:52:59.959 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 18:52:59.960 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 18:52:59.960 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] schema data cleaned 
[INFO ] 2024-07-15 18:52:59.960 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] monitor closed 
[INFO ] 2024-07-15 18:52:59.962 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] close complete, cost 24 ms 
[INFO ] 2024-07-15 18:52:59.967 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] running status set to false 
[INFO ] 2024-07-15 18:52:59.968 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-15 18:52:59.968 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-15 18:52:59.968 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] schema data cleaned 
[INFO ] 2024-07-15 18:52:59.968 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] monitor closed 
[INFO ] 2024-07-15 18:53:00.173 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] close complete, cost 6 ms 
[INFO ] 2024-07-15 18:53:04.399 - [来自Mongo的共享挖掘任务] - Task [来自Mongo的共享挖掘任务] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-15 18:53:04.399 - [来自Mongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 18:53:04.399 - [来自Mongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@15b31e04 
[INFO ] 2024-07-15 18:53:04.520 - [来自Mongo的共享挖掘任务] - Stop task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务)  
[INFO ] 2024-07-15 18:53:04.523 - [来自Mongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-15 18:53:04.523 - [来自Mongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 18:53:04.537 - [来自Mongo的共享挖掘任务] - Remove memory task client succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 18:53:04.540 - [来自Mongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 19:04:43.441 - [来自Mongo的共享挖掘任务] - Start task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务) 
[INFO ] 2024-07-15 19:04:43.985 - [来自Mongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 19:04:43.985 - [来自Mongo的共享挖掘任务] - The engine receives 来自Mongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 19:04:44.726 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:04:44.746 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:04:44.747 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-15 19:04:44.748 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 19:04:44.806 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 19:04:44.952 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 19:04:44.954 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-15 19:04:45.603 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" read batch size: 2000 
[INFO ] 2024-07-15 19:04:45.604 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" event queue capacity: 4000 
[INFO ] 2024-07-15 19:04:45.604 - [来自Mongo的共享挖掘任务][Mongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 19:04:45.810 - [来自Mongo的共享挖掘任务][Mongo] - batch offset found: {},stream offset found: {"cdcOffset":1721041485,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 19:04:45.879 - [来自Mongo的共享挖掘任务][Mongo] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721041485,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 19:04:45.880 - [来自Mongo的共享挖掘任务][Mongo] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 19:05:46.107 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@3b3a711c: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T11:05:42.269Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721041544000,"tableId":"_tapdata_heartbeat_table","time":1721041544237,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721041544000, sourceSerialNo=null} 
[ERROR] 2024-07-15 19:05:46.110 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@3b3a711c: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T11:05:42.269Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721041544000,"tableId":"_tapdata_heartbeat_table","time":1721041544237,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721041544000, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@3b3a711c: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T11:05:42.269Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721041544000,"tableId":"_tapdata_heartbeat_table","time":1721041544237,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721041544000, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$getConstruct$17(TapTargetShareCDCNode.java:537)
	java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1688)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.getConstruct(TapTargetShareCDCNode.java:532)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:633)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.writeLogContents(TapTargetShareCDCNode.java:415)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:601)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:582)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:500)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:546)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.processShareLog(TapTargetShareCDCNode.java:255)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkShareCDCNode.processShareLog(HazelcastTargetPdkShareCDCNode.java:55)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:627)
	... 12 more
Caused by: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.handleTapRecordEvents(TapTargetShareCDCNode.java:288)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.handleTapEvents(TapTargetShareCDCNode.java:268)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.processShareLog(TapTargetShareCDCNode.java:250)
	... 14 more
Caused by: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:641)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.writeLogContents(TapTargetShareCDCNode.java:415)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$handleTapRecordEvents$5(TapTargetShareCDCNode.java:296)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 17 more
Caused by: java.lang.RuntimeException: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$getConstruct$17(TapTargetShareCDCNode.java:537)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1688)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.getConstruct(TapTargetShareCDCNode.java:532)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:633)
	... 20 more

[INFO ] 2024-07-15 19:05:46.278 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Job suspend in error handle 
[INFO ] 2024-07-15 19:05:46.278 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] running status set to false 
[INFO ] 2024-07-15 19:05:46.307 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 19:05:46.307 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 19:05:46.311 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] schema data cleaned 
[INFO ] 2024-07-15 19:05:46.312 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] monitor closed 
[INFO ] 2024-07-15 19:05:46.326 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] close complete, cost 39 ms 
[INFO ] 2024-07-15 19:05:46.326 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] running status set to false 
[INFO ] 2024-07-15 19:05:46.356 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-15 19:05:46.357 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-15 19:05:46.358 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] schema data cleaned 
[INFO ] 2024-07-15 19:05:46.358 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] monitor closed 
[INFO ] 2024-07-15 19:05:46.564 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] close complete, cost 33 ms 
[INFO ] 2024-07-15 19:05:47.567 - [来自Mongo的共享挖掘任务] - Task [来自Mongo的共享挖掘任务] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-15 19:05:47.568 - [来自Mongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 19:05:47.569 - [来自Mongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6ce5a731 
[INFO ] 2024-07-15 19:05:47.702 - [来自Mongo的共享挖掘任务] - Stop task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务)  
[INFO ] 2024-07-15 19:05:47.702 - [来自Mongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-15 19:05:47.750 - [来自Mongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 19:05:47.753 - [来自Mongo的共享挖掘任务] - Remove memory task client succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 19:05:47.753 - [来自Mongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 19:06:10.361 - [来自Mongo的共享挖掘任务] - Start task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务) 
[INFO ] 2024-07-15 19:06:10.559 - [来自Mongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 19:06:10.636 - [来自Mongo的共享挖掘任务] - The engine receives 来自Mongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 19:06:10.637 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:06:10.637 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:06:10.646 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-15 19:06:10.659 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 19:06:10.660 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 19:06:10.676 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 19:06:10.678 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-15 19:06:10.890 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" read batch size: 2000 
[INFO ] 2024-07-15 19:06:10.891 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" event queue capacity: 4000 
[INFO ] 2024-07-15 19:06:10.892 - [来自Mongo的共享挖掘任务][Mongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 19:06:10.955 - [来自Mongo的共享挖掘任务][Mongo] - batch offset found: {},stream offset found: {"cdcOffset":1721041485,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 19:06:10.955 - [来自Mongo的共享挖掘任务][Mongo] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721041485,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 19:06:11.161 - [来自Mongo的共享挖掘任务][Mongo] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 19:06:11.700 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@2b3e9a8: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T11:06:09.328Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721041569000,"tableId":"_tapdata_heartbeat_table","time":1721041571194,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721041569000, sourceSerialNo=null} 
[ERROR] 2024-07-15 19:06:11.737 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@2b3e9a8: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T11:06:09.328Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721041569000,"tableId":"_tapdata_heartbeat_table","time":1721041571194,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721041569000, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@2b3e9a8: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T11:06:09.328Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721041569000,"tableId":"_tapdata_heartbeat_table","time":1721041571194,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721041569000, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$getConstruct$17(TapTargetShareCDCNode.java:537)
	java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1688)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.getConstruct(TapTargetShareCDCNode.java:532)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:633)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.writeLogContents(TapTargetShareCDCNode.java:415)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:601)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:582)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:500)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:546)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.processShareLog(TapTargetShareCDCNode.java:255)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkShareCDCNode.processShareLog(HazelcastTargetPdkShareCDCNode.java:55)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:627)
	... 12 more
Caused by: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.handleTapRecordEvents(TapTargetShareCDCNode.java:288)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.handleTapEvents(TapTargetShareCDCNode.java:268)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.processShareLog(TapTargetShareCDCNode.java:250)
	... 14 more
Caused by: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:641)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.writeLogContents(TapTargetShareCDCNode.java:415)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$handleTapRecordEvents$5(TapTargetShareCDCNode.java:296)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 17 more
Caused by: java.lang.RuntimeException: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$getConstruct$17(TapTargetShareCDCNode.java:537)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1688)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.getConstruct(TapTargetShareCDCNode.java:532)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:633)
	... 20 more

[INFO ] 2024-07-15 19:06:11.744 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Job suspend in error handle 
[INFO ] 2024-07-15 19:06:11.796 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] running status set to false 
[INFO ] 2024-07-15 19:06:11.797 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 19:06:11.797 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 19:06:11.798 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] schema data cleaned 
[INFO ] 2024-07-15 19:06:11.804 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] monitor closed 
[INFO ] 2024-07-15 19:06:11.805 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] close complete, cost 36 ms 
[INFO ] 2024-07-15 19:06:11.805 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] running status set to false 
[INFO ] 2024-07-15 19:06:11.812 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-15 19:06:11.812 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-15 19:06:11.812 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] schema data cleaned 
[INFO ] 2024-07-15 19:06:11.816 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] monitor closed 
[INFO ] 2024-07-15 19:06:11.819 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] close complete, cost 10 ms 
[INFO ] 2024-07-15 19:06:12.880 - [来自Mongo的共享挖掘任务] - Task [来自Mongo的共享挖掘任务] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-15 19:06:12.894 - [来自Mongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 19:06:12.894 - [来自Mongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5aa9071d 
[INFO ] 2024-07-15 19:06:13.016 - [来自Mongo的共享挖掘任务] - Stop task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务)  
[INFO ] 2024-07-15 19:06:13.017 - [来自Mongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-15 19:06:13.017 - [来自Mongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 19:06:13.045 - [来自Mongo的共享挖掘任务] - Remove memory task client succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 19:06:13.047 - [来自Mongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 19:06:40.894 - [来自Mongo的共享挖掘任务] - Start task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务) 
[INFO ] 2024-07-15 19:06:40.894 - [来自Mongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 19:06:40.963 - [来自Mongo的共享挖掘任务] - The engine receives 来自Mongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 19:06:40.963 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:06:40.989 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:06:40.993 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-15 19:06:40.993 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 19:06:41.011 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 19:06:41.011 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 19:06:41.217 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-15 19:06:41.440 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" read batch size: 2000 
[INFO ] 2024-07-15 19:06:41.440 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" event queue capacity: 4000 
[INFO ] 2024-07-15 19:06:41.440 - [来自Mongo的共享挖掘任务][Mongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 19:06:41.638 - [来自Mongo的共享挖掘任务][Mongo] - batch offset found: {},stream offset found: {"cdcOffset":1721041601,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 19:06:41.640 - [来自Mongo的共享挖掘任务][Mongo] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721041601,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 19:06:41.841 - [来自Mongo的共享挖掘任务][Mongo] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 19:06:43.054 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@3b5284fc: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T11:06:39.391Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721041601000,"tableId":"_tapdata_heartbeat_table","time":1721041601660,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721041601000, sourceSerialNo=null} 
[ERROR] 2024-07-15 19:06:43.072 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@3b5284fc: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T11:06:39.391Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721041601000,"tableId":"_tapdata_heartbeat_table","time":1721041601660,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721041601000, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@3b5284fc: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T11:06:39.391Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721041601000,"tableId":"_tapdata_heartbeat_table","time":1721041601660,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721041601000, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$getConstruct$17(TapTargetShareCDCNode.java:537)
	java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1688)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.getConstruct(TapTargetShareCDCNode.java:532)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:633)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.writeLogContents(TapTargetShareCDCNode.java:415)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:601)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:582)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:500)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:546)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.processShareLog(TapTargetShareCDCNode.java:255)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkShareCDCNode.processShareLog(HazelcastTargetPdkShareCDCNode.java:55)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:627)
	... 12 more
Caused by: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.handleTapRecordEvents(TapTargetShareCDCNode.java:288)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.handleTapEvents(TapTargetShareCDCNode.java:268)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.processShareLog(TapTargetShareCDCNode.java:250)
	... 14 more
Caused by: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:641)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.writeLogContents(TapTargetShareCDCNode.java:415)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$handleTapRecordEvents$5(TapTargetShareCDCNode.java:296)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 17 more
Caused by: java.lang.RuntimeException: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$getConstruct$17(TapTargetShareCDCNode.java:537)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1688)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.getConstruct(TapTargetShareCDCNode.java:532)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:633)
	... 20 more

[INFO ] 2024-07-15 19:06:43.072 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Job suspend in error handle 
[INFO ] 2024-07-15 19:06:43.229 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] running status set to false 
[INFO ] 2024-07-15 19:06:43.229 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 19:06:43.231 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 19:06:43.231 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] schema data cleaned 
[INFO ] 2024-07-15 19:06:43.235 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] monitor closed 
[INFO ] 2024-07-15 19:06:43.237 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] close complete, cost 42 ms 
[INFO ] 2024-07-15 19:06:43.238 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] running status set to false 
[INFO ] 2024-07-15 19:06:43.250 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-15 19:06:43.251 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-15 19:06:43.251 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] schema data cleaned 
[INFO ] 2024-07-15 19:06:43.251 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] monitor closed 
[INFO ] 2024-07-15 19:06:43.459 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] close complete, cost 17 ms 
[INFO ] 2024-07-15 19:06:43.935 - [来自Mongo的共享挖掘任务] - Task [来自Mongo的共享挖掘任务] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-15 19:06:43.935 - [来自Mongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 19:06:43.940 - [来自Mongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@729c1da4 
[INFO ] 2024-07-15 19:06:43.942 - [来自Mongo的共享挖掘任务] - Stop task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务)  
[INFO ] 2024-07-15 19:06:44.070 - [来自Mongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-15 19:06:44.071 - [来自Mongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 19:06:44.091 - [来自Mongo的共享挖掘任务] - Remove memory task client succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 19:06:44.296 - [来自Mongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 19:43:02.317 - [来自Mongo的共享挖掘任务] - Start task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务) 
[INFO ] 2024-07-15 19:43:02.388 - [来自Mongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 19:43:02.389 - [来自Mongo的共享挖掘任务] - The engine receives 来自Mongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 19:43:02.436 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] start preload schema,table counts: 1 
[INFO ] 2024-07-15 19:43:02.436 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 19:43:02.461 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-15 19:43:02.462 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 19:43:02.597 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 19:43:02.599 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 19:43:02.802 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-15 19:43:03.103 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" read batch size: 2000 
[INFO ] 2024-07-15 19:43:03.103 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" event queue capacity: 4000 
[INFO ] 2024-07-15 19:43:03.103 - [来自Mongo的共享挖掘任务][Mongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 19:43:03.309 - [来自Mongo的共享挖掘任务][Mongo] - batch offset found: {},stream offset found: {"cdcOffset":1721043782,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 19:43:03.362 - [来自Mongo的共享挖掘任务][Mongo] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721043782,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 19:43:03.362 - [来自Mongo的共享挖掘任务][Mongo] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 19:43:04.608 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@1a9b4700: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T11:43:01.151Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721043782000,"tableId":"_tapdata_heartbeat_table","time":1721043783384,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721043782000, sourceSerialNo=null} 
[ERROR] 2024-07-15 19:43:04.609 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@1a9b4700: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T11:43:01.151Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721043782000,"tableId":"_tapdata_heartbeat_table","time":1721043783384,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721043782000, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@1a9b4700: {"after":{"_id":"66949fb566ab5ede8a857995","id":"669497151df4b966216a48aa","ts":"2024-07-15T11:43:01.151Z"},"before":{},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721043782000,"tableId":"_tapdata_heartbeat_table","time":1721043783384,"type":302}, nodeIds=[3f643370339343e698937efbba0e74ff], sourceTime=1721043782000, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$getConstruct$17(TapTargetShareCDCNode.java:537)
	java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1688)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.getConstruct(TapTargetShareCDCNode.java:532)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:633)
	io.tapdata.node.pdk.processor.TapTargetShareCDCNode.writeLogContents(TapTargetShareCDCNode.java:415)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:601)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:582)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:500)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:546)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.processShareLog(TapTargetShareCDCNode.java:255)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkShareCDCNode.processShareLog(HazelcastTargetPdkShareCDCNode.java:55)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:627)
	... 12 more
Caused by: java.lang.RuntimeException: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.handleTapRecordEvents(TapTargetShareCDCNode.java:288)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.handleTapEvents(TapTargetShareCDCNode.java:268)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.processShareLog(TapTargetShareCDCNode.java:250)
	... 14 more
Caused by: Ring buffer name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table, table: _tapdata_heartbeat_table, size: 0
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:641)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.writeLogContents(TapTargetShareCDCNode.java:415)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$handleTapRecordEvents$5(TapTargetShareCDCNode.java:296)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 17 more
Caused by: java.lang.RuntimeException: Share cdc table mapping not found, sign: 669497151df4b966216a48aa__tapdata_heartbeat_table
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.lambda$getConstruct$17(TapTargetShareCDCNode.java:537)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1688)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.getConstruct(TapTargetShareCDCNode.java:532)
	at io.tapdata.node.pdk.processor.TapTargetShareCDCNode.insertMany(TapTargetShareCDCNode.java:633)
	... 20 more

[INFO ] 2024-07-15 19:43:04.627 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Job suspend in error handle 
[INFO ] 2024-07-15 19:43:04.628 - [来自Mongo的共享挖掘任务] - Task [来自Mongo的共享挖掘任务] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-15 19:43:04.804 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] running status set to false 
[INFO ] 2024-07-15 19:43:04.809 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 19:43:04.809 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 19:43:04.809 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] schema data cleaned 
[INFO ] 2024-07-15 19:43:04.809 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] monitor closed 
[INFO ] 2024-07-15 19:43:04.811 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] close complete, cost 43 ms 
[INFO ] 2024-07-15 19:43:04.812 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] running status set to false 
[INFO ] 2024-07-15 19:43:04.830 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-15 19:43:04.831 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-15 19:43:04.832 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] schema data cleaned 
[INFO ] 2024-07-15 19:43:04.832 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] monitor closed 
[INFO ] 2024-07-15 19:43:04.833 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] close complete, cost 22 ms 
[INFO ] 2024-07-15 19:43:09.675 - [来自Mongo的共享挖掘任务] - Task [来自Mongo的共享挖掘任务] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-15 19:43:09.704 - [来自Mongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 19:43:09.830 - [来自Mongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@33f5e01a 
[INFO ] 2024-07-15 19:43:09.831 - [来自Mongo的共享挖掘任务] - Stop task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务)  
[INFO ] 2024-07-15 19:43:09.868 - [来自Mongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-15 19:43:09.868 - [来自Mongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 19:43:09.887 - [来自Mongo的共享挖掘任务] - Remove memory task client succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 19:43:09.889 - [来自Mongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 20:00:41.637 - [来自Mongo的共享挖掘任务] - Start task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务) 
[INFO ] 2024-07-15 20:00:42.307 - [来自Mongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 20:00:42.510 - [来自Mongo的共享挖掘任务] - The engine receives 来自Mongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 20:00:43.835 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] start preload schema,table counts: 2 
[INFO ] 2024-07-15 20:00:43.841 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-15 20:00:43.849 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 20:00:43.871 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 20:00:48.781 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 20:00:52.083 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 20:00:54.705 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66950f6866ab5ede8a947bb9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1931322122, shareCdcTaskId=66949fb11df4b966216a4f28, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 20:00:54.807 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1931322122', head seq: 0, tail seq: -1 
[INFO ] 2024-07-15 20:00:55.012 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-15 20:00:57.091 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" read batch size: 2000 
[INFO ] 2024-07-15 20:00:57.094 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" event queue capacity: 4000 
[INFO ] 2024-07-15 20:00:57.128 - [来自Mongo的共享挖掘任务][Mongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 20:00:57.156 - [来自Mongo的共享挖掘任务][Mongo] - batch offset found: {},stream offset found: {"cdcOffset":1721043782,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 20:00:57.368 - [来自Mongo的共享挖掘任务][Mongo] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721043782,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 20:00:57.424 - [来自Mongo的共享挖掘任务][Mongo] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 20:04:20.494 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] running status set to false 
[INFO ] 2024-07-15 20:04:20.495 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 20:04:20.495 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 20:04:20.497 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] schema data cleaned 
[INFO ] 2024-07-15 20:04:20.514 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] monitor closed 
[INFO ] 2024-07-15 20:04:20.515 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] close complete, cost 67 ms 
[INFO ] 2024-07-15 20:04:20.515 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] running status set to false 
[INFO ] 2024-07-15 20:04:20.558 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-15 20:04:20.560 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-15 20:04:20.561 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] schema data cleaned 
[INFO ] 2024-07-15 20:04:20.561 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] monitor closed 
[INFO ] 2024-07-15 20:04:20.768 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] close complete, cost 47 ms 
[INFO ] 2024-07-15 20:04:24.690 - [来自Mongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 20:04:24.696 - [来自Mongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@10979195 
[INFO ] 2024-07-15 20:04:24.697 - [来自Mongo的共享挖掘任务] - Stop task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务)  
[INFO ] 2024-07-15 20:04:24.817 - [来自Mongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-15 20:04:24.818 - [来自Mongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 20:04:24.854 - [来自Mongo的共享挖掘任务] - Remove memory task client succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 20:04:24.854 - [来自Mongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 20:04:45.373 - [来自Mongo的共享挖掘任务] - Start task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务) 
[INFO ] 2024-07-15 20:04:45.373 - [来自Mongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 20:04:45.445 - [来自Mongo的共享挖掘任务] - The engine receives 来自Mongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 20:04:45.445 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] start preload schema,table counts: 2 
[INFO ] 2024-07-15 20:04:45.464 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 20:04:45.464 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-15 20:04:45.464 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 20:07:24.677 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66950f6866ab5ede8a947bb9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1931322122, shareCdcTaskId=66949fb11df4b966216a4f28, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 20:07:24.723 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1931322122', head seq: 0, tail seq: 747 
[INFO ] 2024-07-15 20:07:24.724 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 20:07:24.745 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 20:07:24.753 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-15 20:07:24.903 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" read batch size: 2000 
[INFO ] 2024-07-15 20:07:24.903 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" event queue capacity: 4000 
[INFO ] 2024-07-15 20:07:24.909 - [来自Mongo的共享挖掘任务][Mongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 20:07:25.028 - [来自Mongo的共享挖掘任务][Mongo] - batch offset found: {},stream offset found: {"_data":{"value":"8266951042000000032B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F6964006466949FB566AB5EDE8A8579950004","bsonType":"STRING","boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-15 20:07:25.030 - [来自Mongo的共享挖掘任务][Mongo] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"_data":{"value":"8266951042000000032B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F6964006466949FB566AB5EDE8A8579950004","bsonType":"STRING","boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-15 20:07:25.242 - [来自Mongo的共享挖掘任务][Mongo] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 20:22:55.692 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] running status set to false 
[INFO ] 2024-07-15 20:22:55.725 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 20:22:55.725 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-3f643370339343e698937efbba0e74ff 
[INFO ] 2024-07-15 20:22:55.727 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] schema data cleaned 
[INFO ] 2024-07-15 20:22:55.728 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] monitor closed 
[INFO ] 2024-07-15 20:22:55.731 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[3f643370339343e698937efbba0e74ff] close complete, cost 75 ms 
[INFO ] 2024-07-15 20:22:55.731 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] running status set to false 
[INFO ] 2024-07-15 20:22:55.840 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-15 20:22:55.840 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-15 20:22:55.841 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] schema data cleaned 
[INFO ] 2024-07-15 20:22:55.841 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] monitor closed 
[INFO ] 2024-07-15 20:22:55.851 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ae1048651c52417dbb83a662c305a12a] close complete, cost 111 ms 
[INFO ] 2024-07-15 20:23:00.412 - [来自Mongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 20:23:00.413 - [来自Mongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@623b441d 
[INFO ] 2024-07-15 20:23:00.531 - [来自Mongo的共享挖掘任务] - Stop task milestones: 66949fb11df4b966216a4f28(来自Mongo的共享挖掘任务)  
[INFO ] 2024-07-15 20:23:00.531 - [来自Mongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-15 20:23:00.555 - [来自Mongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 20:23:00.555 - [来自Mongo的共享挖掘任务] - Remove memory task client succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
[INFO ] 2024-07-15 20:23:00.759 - [来自Mongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自Mongo的共享挖掘任务[66949fb11df4b966216a4f28] 
