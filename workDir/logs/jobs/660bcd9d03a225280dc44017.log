[INFO ] 2024-04-02 17:19:45.843 - [任务 42] - Start task milestones: 660bcd9d03a225280dc44017(任务 42) 
[INFO ] 2024-04-02 17:19:45.844 - [任务 42] - Task initialization... 
[INFO ] 2024-04-02 17:19:45.845 - [任务 42] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-02 17:19:45.980 - [任务 42] - The engine receives 任务 42 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-02 17:19:46.660 - [任务 42][test1] - Node test1[85b3fc39-0ba6-435c-abcd-75a9560a090c] start preload schema,table counts: 1 
[INFO ] 2024-04-02 17:19:46.661 - [任务 42][CLAIM] - Node CLAIM[81ad49a5-d835-4db5-a653-21e5443a9e9e] start preload schema,table counts: 1 
[INFO ] 2024-04-02 17:19:46.750 - [任务 42][CLAIM] - Node CLAIM[81ad49a5-d835-4db5-a653-21e5443a9e9e] preload schema finished, cost 89 ms 
[INFO ] 2024-04-02 17:19:46.956 - [任务 42][test1] - Node test1[85b3fc39-0ba6-435c-abcd-75a9560a090c] preload schema finished, cost 90 ms 
[INFO ] 2024-04-02 17:19:49.758 - [任务 42][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-02 17:19:49.758 - [任务 42][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-02 17:19:49.760 - [任务 42][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-02 17:19:49.793 - [任务 42][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145949093,"gtidSet":""} 
[INFO ] 2024-04-02 17:19:49.922 - [任务 42][CLAIM] - Initial sync started 
[INFO ] 2024-04-02 17:19:49.923 - [任务 42][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-02 17:19:49.988 - [任务 42][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-02 17:19:49.988 - [任务 42][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-04-02 17:19:55.046 - [任务 42][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-02 17:19:55.559 - [任务 42][CLAIM] - Initial sync completed 
[INFO ] 2024-04-02 17:19:55.565 - [任务 42][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-02 17:19:55.566 - [任务 42][CLAIM] - Initial sync completed 
[INFO ] 2024-04-02 17:19:55.574 - [任务 42][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":145949093,"gtidSet":""} 
[INFO ] 2024-04-02 17:19:55.705 - [任务 42][CLAIM] - Starting mysql cdc, server name: 7437f105-9b49-4644-a0a9-5743d42d1321 
[INFO ] 2024-04-02 17:19:55.706 - [任务 42][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 343146779
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 7437f105-9b49-4644-a0a9-5743d42d1321
  database.port: 3306
  threadName: Debezium-Mysql-Connector-7437f105-9b49-4644-a0a9-5743d42d1321
  database.hostname: 127.0.0.1
  database.password: ********
  name: 7437f105-9b49-4644-a0a9-5743d42d1321
  pdk.offset.string: {"name":"7437f105-9b49-4644-a0a9-5743d42d1321","offset":{"{\"server\":\"7437f105-9b49-4644-a0a9-5743d42d1321\"}":"{\"file\":\"binlog.000020\",\"pos\":145949093,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-02 17:19:56.472 - [任务 42][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-04-02 17:25:45.470 - [任务 42][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-04-02 17:25:45.470 - [任务 42][CLAIM] - Incremental sync completed 
[ERROR] 2024-04-02 17:25:45.471 - [任务 42][CLAIM] - java.lang.RuntimeException: java.io.EOFException <-- Error Message -->
java.lang.RuntimeException: java.io.EOFException

<-- Simple Stack Trace -->
Caused by: java.io.EOFException: null
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:580)
	com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
java.lang.RuntimeException: java.io.EOFException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.io.EOFException
	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:142)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:333)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:458)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.io.EOFException
	at com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:580)
	at com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	... 1 more

[INFO ] 2024-04-02 17:25:45.471 - [任务 42][CLAIM] - Job suspend in error handle 
[INFO ] 2024-04-02 17:25:45.471 - [任务 42][CLAIM] - Node CLAIM[81ad49a5-d835-4db5-a653-21e5443a9e9e] running status set to false 
[INFO ] 2024-04-02 17:25:45.471 - [任务 42][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-81ad49a5-d835-4db5-a653-21e5443a9e9e 
[INFO ] 2024-04-02 17:25:45.472 - [任务 42][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-81ad49a5-d835-4db5-a653-21e5443a9e9e 
[INFO ] 2024-04-02 17:25:45.472 - [任务 42][CLAIM] - Node CLAIM[81ad49a5-d835-4db5-a653-21e5443a9e9e] schema data cleaned 
[INFO ] 2024-04-02 17:25:45.484 - [任务 42][CLAIM] - Node CLAIM[81ad49a5-d835-4db5-a653-21e5443a9e9e] monitor closed 
[INFO ] 2024-04-02 17:25:45.484 - [任务 42][CLAIM] - Node CLAIM[81ad49a5-d835-4db5-a653-21e5443a9e9e] close complete, cost 62 ms 
[INFO ] 2024-04-02 17:25:45.485 - [任务 42][test1] - Node test1[85b3fc39-0ba6-435c-abcd-75a9560a090c] running status set to false 
[INFO ] 2024-04-02 17:25:45.530 - [任务 42][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-85b3fc39-0ba6-435c-abcd-75a9560a090c 
[INFO ] 2024-04-02 17:25:45.531 - [任务 42][test1] - PDK connector node released: HazelcastTargetPdkDataNode-85b3fc39-0ba6-435c-abcd-75a9560a090c 
[INFO ] 2024-04-02 17:25:45.531 - [任务 42][test1] - Node test1[85b3fc39-0ba6-435c-abcd-75a9560a090c] schema data cleaned 
[INFO ] 2024-04-02 17:25:45.532 - [任务 42][test1] - Node test1[85b3fc39-0ba6-435c-abcd-75a9560a090c] monitor closed 
[INFO ] 2024-04-02 17:25:45.734 - [任务 42][test1] - Node test1[85b3fc39-0ba6-435c-abcd-75a9560a090c] close complete, cost 48 ms 
[INFO ] 2024-04-02 17:25:48.563 - [任务 42] - Task [任务 42] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-04-02 17:25:48.608 - [任务 42] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-02 17:25:48.652 - [任务 42] - Stop task milestones: 660bcd9d03a225280dc44017(任务 42)  
[INFO ] 2024-04-02 17:25:48.653 - [任务 42] - Stopped task aspect(s) 
[INFO ] 2024-04-02 17:25:48.711 - [任务 42] - Snapshot order controller have been removed 
[INFO ] 2024-04-02 17:25:48.713 - [任务 42] - Remove memory task client succeed, task: 任务 42[660bcd9d03a225280dc44017] 
[INFO ] 2024-04-02 17:25:48.713 - [任务 42] - Destroy memory task client cache succeed, task: 任务 42[660bcd9d03a225280dc44017] 
