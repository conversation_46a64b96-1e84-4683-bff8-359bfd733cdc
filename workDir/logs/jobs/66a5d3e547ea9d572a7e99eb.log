[INFO ] 2024-07-28 13:18:01.532 - [任务 1] - Start task milestones: 66a5d3e547ea9d572a7e99eb(任务 1) 
[INFO ] 2024-07-28 13:18:01.946 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 13:18:02.661 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:18:02.764 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 13:18:03.154 - [任务 1][TESTPO] - Node TESTPO[c0901354-9e84-4056-9f57-90570a879e86] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:18:03.154 - [任务 1][TestPO] - Node TestPO[d4cc7f49-d0e8-494b-8e54-c69637d000b9] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:18:03.155 - [任务 1][TESTPO] - Node TESTPO[c0901354-9e84-4056-9f57-90570a879e86] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 13:18:03.157 - [任务 1][TestPO] - Node TestPO[d4cc7f49-d0e8-494b-8e54-c69637d000b9] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 13:18:04.089 - [任务 1][TestPO] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 13:18:04.090 - [任务 1][TESTPO] - Source node "TESTPO" read batch size: 100 
[INFO ] 2024-07-28 13:18:04.091 - [任务 1][TESTPO] - Source node "TESTPO" event queue capacity: 200 
[INFO ] 2024-07-28 13:18:04.091 - [任务 1][TESTPO] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-28 13:18:04.250 - [任务 1][TESTPO] - batch offset found: {},stream offset found: {"cdcOffset":1722143884,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:18:04.335 - [任务 1][TESTPO] - Initial sync started 
[INFO ] 2024-07-28 13:18:04.344 - [任务 1][TESTPO] - Starting batch read, table name: TESTPO, offset: null 
[INFO ] 2024-07-28 13:18:04.345 - [任务 1][TESTPO] - Table TESTPO is going to be initial synced 
[INFO ] 2024-07-28 13:18:04.452 - [任务 1][TESTPO] - Table [TESTPO] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-28 13:18:04.455 - [任务 1][TESTPO] - Query table 'TESTPO' counts: 600 
[INFO ] 2024-07-28 13:18:04.455 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 13:18:04.456 - [任务 1][TESTPO] - Incremental sync starting... 
[INFO ] 2024-07-28 13:18:04.456 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 13:18:04.457 - [任务 1][TESTPO] - Starting stream read, table list: [TESTPO, _tapdata_heartbeat_table], offset: {"cdcOffset":1722143884,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:18:04.658 - [任务 1][TESTPO] - Connector MongoDB incremental start succeed, tables: [TESTPO, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-28 13:20:20.248 - [任务 1][TESTPO] - Node TESTPO[c0901354-9e84-4056-9f57-90570a879e86] running status set to false 
[INFO ] 2024-07-28 13:20:20.263 - [任务 1][TESTPO] - PDK connector node stopped: HazelcastSourcePdkDataNode-c0901354-9e84-4056-9f57-90570a879e86 
[INFO ] 2024-07-28 13:20:20.264 - [任务 1][TESTPO] - PDK connector node released: HazelcastSourcePdkDataNode-c0901354-9e84-4056-9f57-90570a879e86 
[INFO ] 2024-07-28 13:20:20.269 - [任务 1][TESTPO] - Node TESTPO[c0901354-9e84-4056-9f57-90570a879e86] schema data cleaned 
[INFO ] 2024-07-28 13:20:20.269 - [任务 1][TESTPO] - Node TESTPO[c0901354-9e84-4056-9f57-90570a879e86] monitor closed 
[INFO ] 2024-07-28 13:20:20.281 - [任务 1][TESTPO] - Node TESTPO[c0901354-9e84-4056-9f57-90570a879e86] close complete, cost 32 ms 
[INFO ] 2024-07-28 13:20:20.282 - [任务 1][TestPO] - Node TestPO[d4cc7f49-d0e8-494b-8e54-c69637d000b9] running status set to false 
[INFO ] 2024-07-28 13:20:20.315 - [任务 1][TestPO] - PDK connector node stopped: HazelcastTargetPdkDataNode-d4cc7f49-d0e8-494b-8e54-c69637d000b9 
[INFO ] 2024-07-28 13:20:20.320 - [任务 1][TestPO] - PDK connector node released: HazelcastTargetPdkDataNode-d4cc7f49-d0e8-494b-8e54-c69637d000b9 
[INFO ] 2024-07-28 13:20:20.321 - [任务 1][TestPO] - Node TestPO[d4cc7f49-d0e8-494b-8e54-c69637d000b9] schema data cleaned 
[INFO ] 2024-07-28 13:20:20.321 - [任务 1][TestPO] - Node TestPO[d4cc7f49-d0e8-494b-8e54-c69637d000b9] monitor closed 
[INFO ] 2024-07-28 13:20:20.524 - [任务 1][TestPO] - Node TestPO[d4cc7f49-d0e8-494b-8e54-c69637d000b9] close complete, cost 44 ms 
[INFO ] 2024-07-28 13:20:21.116 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 13:20:21.122 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@70859b4c 
[INFO ] 2024-07-28 13:20:21.125 - [任务 1] - Stop task milestones: 66a5d3e547ea9d572a7e99eb(任务 1)  
[INFO ] 2024-07-28 13:20:21.260 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-28 13:20:21.261 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-28 13:20:21.302 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a5d3e547ea9d572a7e99eb] 
[INFO ] 2024-07-28 13:20:21.302 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a5d3e547ea9d572a7e99eb] 
[INFO ] 2024-07-28 13:22:45.654 - [任务 1] - Start task milestones: 66a5d3e547ea9d572a7e99eb(任务 1) 
[INFO ] 2024-07-28 13:22:45.825 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 13:22:45.967 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:22:46.102 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 13:22:46.203 - [任务 1][TestPO] - Node TestPO[d4cc7f49-d0e8-494b-8e54-c69637d000b9] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:22:46.204 - [任务 1][TestPO] - Node TestPO[d4cc7f49-d0e8-494b-8e54-c69637d000b9] preload schema finished, cost 1 ms 
[INFO ] 2024-07-28 13:22:46.246 - [任务 1][TESTPO] - Node TESTPO[c0901354-9e84-4056-9f57-90570a879e86] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:22:46.247 - [任务 1][TESTPO] - Node TESTPO[c0901354-9e84-4056-9f57-90570a879e86] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 13:22:46.455 - [任务 1][TESTPO] - Source node "TESTPO" read batch size: 100 
[INFO ] 2024-07-28 13:22:46.455 - [任务 1][TESTPO] - Source node "TESTPO" event queue capacity: 200 
[INFO ] 2024-07-28 13:22:46.473 - [任务 1][TESTPO] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-28 13:22:46.473 - [任务 1][TESTPO] - batch offset found: {"TESTPO":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1722143884,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:22:46.522 - [任务 1][TESTPO] - Incremental sync starting... 
[INFO ] 2024-07-28 13:22:46.524 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 13:22:46.543 - [任务 1][TESTPO] - Starting stream read, table list: [TESTPO, _tapdata_heartbeat_table], offset: {"cdcOffset":1722143884,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:22:46.543 - [任务 1][TestPO] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 13:22:46.750 - [任务 1][TESTPO] - Connector MongoDB incremental start succeed, tables: [TESTPO, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-28 13:23:05.910 - [任务 1][TESTPO] - Node TESTPO[c0901354-9e84-4056-9f57-90570a879e86] running status set to false 
[INFO ] 2024-07-28 13:23:05.918 - [任务 1][TESTPO] - PDK connector node stopped: HazelcastSourcePdkDataNode-c0901354-9e84-4056-9f57-90570a879e86 
[INFO ] 2024-07-28 13:23:05.918 - [任务 1][TESTPO] - PDK connector node released: HazelcastSourcePdkDataNode-c0901354-9e84-4056-9f57-90570a879e86 
[INFO ] 2024-07-28 13:23:05.920 - [任务 1][TESTPO] - Node TESTPO[c0901354-9e84-4056-9f57-90570a879e86] schema data cleaned 
[INFO ] 2024-07-28 13:23:05.920 - [任务 1][TESTPO] - Node TESTPO[c0901354-9e84-4056-9f57-90570a879e86] monitor closed 
[INFO ] 2024-07-28 13:23:05.927 - [任务 1][TESTPO] - Node TESTPO[c0901354-9e84-4056-9f57-90570a879e86] close complete, cost 21 ms 
[INFO ] 2024-07-28 13:23:05.936 - [任务 1][TestPO] - Node TestPO[d4cc7f49-d0e8-494b-8e54-c69637d000b9] running status set to false 
[INFO ] 2024-07-28 13:23:05.947 - [任务 1][TestPO] - PDK connector node stopped: HazelcastTargetPdkDataNode-d4cc7f49-d0e8-494b-8e54-c69637d000b9 
[INFO ] 2024-07-28 13:23:05.947 - [任务 1][TestPO] - PDK connector node released: HazelcastTargetPdkDataNode-d4cc7f49-d0e8-494b-8e54-c69637d000b9 
[INFO ] 2024-07-28 13:23:05.948 - [任务 1][TestPO] - Node TestPO[d4cc7f49-d0e8-494b-8e54-c69637d000b9] schema data cleaned 
[INFO ] 2024-07-28 13:23:05.948 - [任务 1][TestPO] - Node TestPO[d4cc7f49-d0e8-494b-8e54-c69637d000b9] monitor closed 
[INFO ] 2024-07-28 13:23:05.968 - [任务 1][TestPO] - Node TestPO[d4cc7f49-d0e8-494b-8e54-c69637d000b9] close complete, cost 28 ms 
[WARN ] 2024-07-28 13:23:05.969 - [任务 1] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=a77cd38277e84c2eab9ad0a1562978add9a55d8a3d7e4b7d8b78dec6c933aea4&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266a5d3e547ea9d572a7e99eb%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@88aa992, CDC=io.tapdata.milestone.entity.MilestoneEntity@64d56f3, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@272c2e2, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@47705cc8, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@1aebbb8d, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@76fd9ced}, attrs.nodeMilestones={c0901354-9e84-4056-9f57-90570a879e86={OPEN_CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@628c2502, NODE=io.tapdata.milestone.entity.MilestoneEntity@275fa387, SNAPSHOT_READ=io.tapdata.milestone.entity.MilestoneEntity@60af4332, CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@7a2fb13e}, d4cc7f49-d0e8-494b-8e54-c69637d000b9={CDC_WRITE=io.tapdata.milestone.entity.MilestoneEntity@79882f4c, NODE=io.tapdata.milestone.entity.MilestoneEntity@2311f861, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@393d89e2, SNAPSHOT_WRITE=io.tapdata.milestone.entity.MilestoneEntity@3e9c69ba}}, syncStatus=CDC}}}}, code='IllegalArgument', data=null, reqId=19d00ba9-27e3-4342-a36d-e4f46e8edc41}: 无效参数: task not found 
