[INFO ] 2024-03-29 18:28:40.911 - [orders_import_import_import_import_import] - Start task milestones: 660697bd57c8b774d7a9422f(orders_import_import_import_import_import) 
[INFO ] 2024-03-29 18:28:40.946 - [orders_import_import_import_import_import] - Task initialization... 
[INFO ] 2024-03-29 18:28:40.947 - [orders_import_import_import_import_import] - <PERSON><PERSON> performs snapshot read by order list: [ null ] -> [ null ] -> [ null ] 
[INFO ] 2024-03-29 18:28:41.562 - [orders_import_import_import_import_import] - The engine receives orders_import_import_import_import_import task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 18:28:41.564 - [orders_import_import_import_import_import][Rename Order Details] - Node Rename Order Details[f7f7348c-d52d-47b5-a67f-36b11f001117] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:41.564 - [orders_import_import_import_import_import][Orders] - Node Orders[7886c735-23fd-4c15-8843-23ea318b30b1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:41.571 - [orders_import_import_import_import_import][Products] - Node Products[b79e3732-0512-4ce2-a25e-09d0b116a135] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:41.571 - [orders_import_import_import_import_import][Delete Products] - Node Delete Products[6ad1fb8c-0004-4d3e-b20d-debecdddf8ab] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:41.571 - [orders_import_import_import_import_import][orders] - Node orders[608b4452-65fa-4afa-8475-354d908e900f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:41.571 - [orders_import_import_import_import_import][Rename Products] - Node Rename Products[2a6d1710-2294-4268-9928-07b2fd94352a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:41.571 - [orders_import_import_import_import_import][Rename Orders] - Node Rename Orders[2ecfacab-e769-4411-b645-0a41b339d188] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:41.571 - [orders_import_import_import_import_import][Delete Order Details] - Node Delete Order Details[ec5bb5bc-b1c3-4e2f-88e9-a9e9d6d0b25b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:41.571 - [orders_import_import_import_import_import][merge] - Node merge[ad0831cc-442e-4ea2-8661-82ca40d6b971] start preload schema,table counts: 4 
[INFO ] 2024-03-29 18:28:41.571 - [orders_import_import_import_import_import][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:41.572 - [orders_import_import_import_import_import][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:28:41.738 - [orders_import_import_import_import_import][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] preload schema finished, cost 142 ms 
[INFO ] 2024-03-29 18:28:41.738 - [orders_import_import_import_import_import][Rename Orders] - Node Rename Orders[2ecfacab-e769-4411-b645-0a41b339d188] preload schema finished, cost 144 ms 
[INFO ] 2024-03-29 18:28:41.739 - [orders_import_import_import_import_import][Delete Products] - Node Delete Products[6ad1fb8c-0004-4d3e-b20d-debecdddf8ab] preload schema finished, cost 143 ms 
[INFO ] 2024-03-29 18:28:41.739 - [orders_import_import_import_import_import][Delete Order Details] - Node Delete Order Details[ec5bb5bc-b1c3-4e2f-88e9-a9e9d6d0b25b] preload schema finished, cost 144 ms 
[INFO ] 2024-03-29 18:28:41.739 - [orders_import_import_import_import_import][Rename Order Details] - Node Rename Order Details[f7f7348c-d52d-47b5-a67f-36b11f001117] preload schema finished, cost 150 ms 
[INFO ] 2024-03-29 18:28:41.739 - [orders_import_import_import_import_import][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] preload schema finished, cost 141 ms 
[INFO ] 2024-03-29 18:28:41.739 - [orders_import_import_import_import_import][Orders] - Node Orders[7886c735-23fd-4c15-8843-23ea318b30b1] preload schema finished, cost 145 ms 
[INFO ] 2024-03-29 18:28:41.739 - [orders_import_import_import_import_import][Rename Products] - Node Rename Products[2a6d1710-2294-4268-9928-07b2fd94352a] preload schema finished, cost 152 ms 
[INFO ] 2024-03-29 18:28:41.739 - [orders_import_import_import_import_import][Products] - Node Products[b79e3732-0512-4ce2-a25e-09d0b116a135] preload schema finished, cost 147 ms 
[INFO ] 2024-03-29 18:28:41.818 - [orders_import_import_import_import_import][orders] - Node orders[608b4452-65fa-4afa-8475-354d908e900f] preload schema finished, cost 186 ms 
[INFO ] 2024-03-29 18:28:41.820 - [orders_import_import_import_import_import][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:28:41.820 - [orders_import_import_import_import_import][merge] - Node merge[ad0831cc-442e-4ea2-8661-82ca40d6b971] preload schema finished, cost 248 ms 
[INFO ] 2024-03-29 18:28:41.820 - [orders_import_import_import_import_import][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 18:28:41.823 - [orders_import_import_import_import_import][merge] - 
Merge lookup relation{
  Order Details(ec5bb5bc-b1c3-4e2f-88e9-a9e9d6d0b25b)
    ->Products(6ad1fb8c-0004-4d3e-b20d-debecdddf8ab)
} 
[INFO ] 2024-03-29 18:28:41.823 - [orders_import_import_import_import_import][merge] - 
Merge lookup relation{
  Orders(2ecfacab-e769-4411-b645-0a41b339d188)
    ->Order Details(ec5bb5bc-b1c3-4e2f-88e9-a9e9d6d0b25b)
} 
[INFO ] 2024-03-29 18:28:42.225 - [orders_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Products_6ad1fb8c-0004-4d3e-b20d-debecdddf8ab__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:28:42.628 - [orders_import_import_import_import_import][Order Details] - Source node "Order Details" read batch size: 500 
[INFO ] 2024-03-29 18:28:42.628 - [orders_import_import_import_import_import][Order Details] - Source node "Order Details" event queue capacity: 1000 
[INFO ] 2024-03-29 18:28:42.628 - [orders_import_import_import_import_import][Order Details] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:28:42.781 - [orders_import_import_import_import_import][Order Details] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:28:42.796 - [orders_import_import_import_import_import] - Node[Order Details] is waiting for running 
[INFO ] 2024-03-29 18:28:42.798 - [orders_import_import_import_import_import][Products] - Source node "Products" read batch size: 500 
[INFO ] 2024-03-29 18:28:42.798 - [orders_import_import_import_import_import][Products] - Source node "Products" event queue capacity: 1000 
[INFO ] 2024-03-29 18:28:42.798 - [orders_import_import_import_import_import][Products] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:28:42.799 - [orders_import_import_import_import_import][Products] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:28:42.856 - [orders_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Order Details_ec5bb5bc-b1c3-4e2f-88e9-a9e9d6d0b25b__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:28:42.856 - [orders_import_import_import_import_import][orders] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 18:28:42.896 - [orders_import_import_import_import_import] - Node[Products] is waiting for running 
[INFO ] 2024-03-29 18:28:42.896 - [orders_import_import_import_import_import][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 18:28:42.896 - [orders_import_import_import_import_import][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 18:28:43.543 - [orders_import_import_import_import_import][Orders] - Source node "Orders" read batch size: 500 
[INFO ] 2024-03-29 18:28:43.544 - [orders_import_import_import_import_import][Orders] - Source node "Orders" event queue capacity: 1000 
[INFO ] 2024-03-29 18:28:43.544 - [orders_import_import_import_import_import][Orders] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:28:43.544 - [orders_import_import_import_import_import][Orders] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:28:43.623 - [orders_import_import_import_import_import][Orders] - Initial sync started 
[INFO ] 2024-03-29 18:28:43.624 - [orders_import_import_import_import_import][Orders] - Starting batch read, table name: Orders, offset: null 
[INFO ] 2024-03-29 18:28:43.638 - [orders_import_import_import_import_import][Orders] - Table Orders is going to be initial synced 
[INFO ] 2024-03-29 18:28:44.201 - [orders_import_import_import_import_import][Orders] - Query table 'Orders' counts: 1 
[INFO ] 2024-03-29 18:28:44.201 - [orders_import_import_import_import_import][Orders] - Initial sync completed 
[INFO ] 2024-03-29 18:28:45.635 - [orders_import_import_import_import_import] - Node[Orders] finish, notify next layer to run 
[INFO ] 2024-03-29 18:28:45.636 - [orders_import_import_import_import_import] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 18:28:45.759 - [orders_import_import_import_import_import][Order Details] - Initial sync started 
[INFO ] 2024-03-29 18:28:45.760 - [orders_import_import_import_import_import][Order Details] - Starting batch read, table name: Order Details, offset: null 
[INFO ] 2024-03-29 18:28:45.760 - [orders_import_import_import_import_import][Order Details] - Table Order Details is going to be initial synced 
[INFO ] 2024-03-29 18:28:45.760 - [orders_import_import_import_import_import][Order Details] - Query table 'Order Details' counts: 1 
[INFO ] 2024-03-29 18:28:45.760 - [orders_import_import_import_import_import][Order Details] - Initial sync completed 
[INFO ] 2024-03-29 18:28:46.288 - [orders_import_import_import_import_import] - Node[Order Details] finish, notify next layer to run 
[INFO ] 2024-03-29 18:28:46.316 - [orders_import_import_import_import_import] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 18:28:46.317 - [orders_import_import_import_import_import][Products] - Initial sync started 
[INFO ] 2024-03-29 18:28:46.317 - [orders_import_import_import_import_import][Products] - Starting batch read, table name: Products, offset: null 
[INFO ] 2024-03-29 18:28:46.317 - [orders_import_import_import_import_import][Products] - Table Products is going to be initial synced 
[INFO ] 2024-03-29 18:28:46.388 - [orders_import_import_import_import_import][Products] - Query table 'Products' counts: 1 
[INFO ] 2024-03-29 18:28:46.388 - [orders_import_import_import_import_import][Products] - Initial sync completed 
[INFO ] 2024-03-29 18:28:47.785 - [orders_import_import_import_import_import][Order Details] - Incremental sync starting... 
[INFO ] 2024-03-29 18:28:47.792 - [orders_import_import_import_import_import][Order Details] - Initial sync completed 
[INFO ] 2024-03-29 18:28:47.792 - [orders_import_import_import_import_import][Order Details] - Starting stream read, table list: [Order Details], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:28:47.880 - [orders_import_import_import_import_import][Order Details] - Starting mysql cdc, server name: ad8fd645-6fd9-433e-b30e-0b9a6bab8acd 
[INFO ] 2024-03-29 18:28:47.883 - [orders_import_import_import_import_import][Order Details] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 826056082
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ad8fd645-6fd9-433e-b30e-0b9a6bab8acd
  database.port: 3307
  threadName: Debezium-Mysql-Connector-ad8fd645-6fd9-433e-b30e-0b9a6bab8acd
  database.hostname: 127.0.0.1
  database.password: ********
  name: ad8fd645-6fd9-433e-b30e-0b9a6bab8acd
  pdk.offset.string: {"name":"ad8fd645-6fd9-433e-b30e-0b9a6bab8acd","offset":{"{\"server\":\"ad8fd645-6fd9-433e-b30e-0b9a6bab8acd\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Order Details
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:28:48.087 - [orders_import_import_import_import_import][Order Details] - Connector Mysql incremental start succeed, tables: [Order Details], data change syncing 
[INFO ] 2024-03-29 18:28:48.224 - [orders_import_import_import_import_import][Orders] - Incremental sync starting... 
[INFO ] 2024-03-29 18:28:48.224 - [orders_import_import_import_import_import][Orders] - Initial sync completed 
[INFO ] 2024-03-29 18:28:48.225 - [orders_import_import_import_import_import][Orders] - Starting stream read, table list: [Orders], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:28:48.278 - [orders_import_import_import_import_import][Orders] - Starting mysql cdc, server name: c2e47b10-16b1-4b77-9f03-ffc3a579bb46 
[INFO ] 2024-03-29 18:28:48.279 - [orders_import_import_import_import_import][Orders] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 134059442
  time.precision.mode: adaptive_time_microseconds
  database.server.name: c2e47b10-16b1-4b77-9f03-ffc3a579bb46
  database.port: 3307
  threadName: Debezium-Mysql-Connector-c2e47b10-16b1-4b77-9f03-ffc3a579bb46
  database.hostname: 127.0.0.1
  database.password: ********
  name: c2e47b10-16b1-4b77-9f03-ffc3a579bb46
  pdk.offset.string: {"name":"c2e47b10-16b1-4b77-9f03-ffc3a579bb46","offset":{"{\"server\":\"c2e47b10-16b1-4b77-9f03-ffc3a579bb46\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Orders
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:28:48.400 - [orders_import_import_import_import_import][Orders] - Connector Mysql incremental start succeed, tables: [Orders], data change syncing 
[INFO ] 2024-03-29 18:28:48.401 - [orders_import_import_import_import_import][Products] - Incremental sync starting... 
[INFO ] 2024-03-29 18:28:48.401 - [orders_import_import_import_import_import][Products] - Initial sync completed 
[INFO ] 2024-03-29 18:28:48.401 - [orders_import_import_import_import_import][Products] - Starting stream read, table list: [Products], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:28:48.429 - [orders_import_import_import_import_import][Products] - Starting mysql cdc, server name: 78df08de-b1f0-4dc6-810c-37272b0a4327 
[INFO ] 2024-03-29 18:28:48.429 - [orders_import_import_import_import_import][Products] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1436117413
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 78df08de-b1f0-4dc6-810c-37272b0a4327
  database.port: 3307
  threadName: Debezium-Mysql-Connector-78df08de-b1f0-4dc6-810c-37272b0a4327
  database.hostname: 127.0.0.1
  database.password: ********
  name: 78df08de-b1f0-4dc6-810c-37272b0a4327
  pdk.offset.string: {"name":"78df08de-b1f0-4dc6-810c-37272b0a4327","offset":{"{\"server\":\"78df08de-b1f0-4dc6-810c-37272b0a4327\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Products
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:28:48.637 - [orders_import_import_import_import_import][Products] - Connector Mysql incremental start succeed, tables: [Products], data change syncing 
[INFO ] 2024-03-29 18:36:29.690 - [orders_import_import_import_import_import] - Stop task milestones: 660697bd57c8b774d7a9422f(orders_import_import_import_import_import)  
[INFO ] 2024-03-29 18:36:29.690 - [orders_import_import_import_import_import][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] running status set to false 
[INFO ] 2024-03-29 18:36:29.690 - [orders_import_import_import_import_import][Order Details] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:36:29.690 - [orders_import_import_import_import_import][Order Details] - PDK connector node stopped: HazelcastSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:36:29.690 - [orders_import_import_import_import_import][Order Details] - PDK connector node released: HazelcastSourcePdkDataNode-fe43f88f-94bb-43c4-a74f-bd3d99ceca0b 
[INFO ] 2024-03-29 18:36:29.690 - [orders_import_import_import_import_import][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] schema data cleaned 
[INFO ] 2024-03-29 18:36:29.690 - [orders_import_import_import_import_import][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] monitor closed 
[INFO ] 2024-03-29 18:36:29.690 - [orders_import_import_import_import_import][Order Details] - Node Order Details[fe43f88f-94bb-43c4-a74f-bd3d99ceca0b] close complete, cost 89 ms 
[INFO ] 2024-03-29 18:36:29.690 - [orders_import_import_import_import_import][Orders] - Node Orders[7886c735-23fd-4c15-8843-23ea318b30b1] running status set to false 
[INFO ] 2024-03-29 18:36:29.691 - [orders_import_import_import_import_import][Orders] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:36:29.691 - [orders_import_import_import_import_import][Orders] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:36:29.691 - [orders_import_import_import_import_import][Orders] - PDK connector node stopped: HazelcastSourcePdkDataNode-7886c735-23fd-4c15-8843-23ea318b30b1 
[INFO ] 2024-03-29 18:36:29.691 - [orders_import_import_import_import_import][Orders] - PDK connector node released: HazelcastSourcePdkDataNode-7886c735-23fd-4c15-8843-23ea318b30b1 
[INFO ] 2024-03-29 18:36:29.691 - [orders_import_import_import_import_import][Orders] - Node Orders[7886c735-23fd-4c15-8843-23ea318b30b1] schema data cleaned 
[INFO ] 2024-03-29 18:36:29.691 - [orders_import_import_import_import_import][Orders] - Node Orders[7886c735-23fd-4c15-8843-23ea318b30b1] monitor closed 
[INFO ] 2024-03-29 18:36:29.691 - [orders_import_import_import_import_import][Orders] - Node Orders[7886c735-23fd-4c15-8843-23ea318b30b1] close complete, cost 99 ms 
[INFO ] 2024-03-29 18:36:29.691 - [orders_import_import_import_import_import][Rename Orders] - Node Rename Orders[2ecfacab-e769-4411-b645-0a41b339d188] running status set to false 
[INFO ] 2024-03-29 18:36:29.914 - [orders_import_import_import_import_import][Rename Orders] - Node Rename Orders[2ecfacab-e769-4411-b645-0a41b339d188] schema data cleaned 
[INFO ] 2024-03-29 18:36:29.915 - [orders_import_import_import_import_import][Rename Orders] - Node Rename Orders[2ecfacab-e769-4411-b645-0a41b339d188] monitor closed 
[INFO ] 2024-03-29 18:36:29.916 - [orders_import_import_import_import_import][Rename Orders] - Node Rename Orders[2ecfacab-e769-4411-b645-0a41b339d188] close complete, cost 257 ms 
[INFO ] 2024-03-29 18:36:29.917 - [orders_import_import_import_import_import][Products] - Node Products[b79e3732-0512-4ce2-a25e-09d0b116a135] running status set to false 
[INFO ] 2024-03-29 18:36:29.956 - [orders_import_import_import_import_import][Products] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:36:29.956 - [orders_import_import_import_import_import][Products] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:36:29.959 - [orders_import_import_import_import_import][Products] - PDK connector node stopped: HazelcastSourcePdkDataNode-b79e3732-0512-4ce2-a25e-09d0b116a135 
[INFO ] 2024-03-29 18:36:29.959 - [orders_import_import_import_import_import][Products] - PDK connector node released: HazelcastSourcePdkDataNode-b79e3732-0512-4ce2-a25e-09d0b116a135 
[INFO ] 2024-03-29 18:36:29.959 - [orders_import_import_import_import_import][Products] - Node Products[b79e3732-0512-4ce2-a25e-09d0b116a135] schema data cleaned 
[INFO ] 2024-03-29 18:36:29.959 - [orders_import_import_import_import_import][Products] - Node Products[b79e3732-0512-4ce2-a25e-09d0b116a135] monitor closed 
[INFO ] 2024-03-29 18:36:29.959 - [orders_import_import_import_import_import][Products] - Node Products[b79e3732-0512-4ce2-a25e-09d0b116a135] close complete, cost 42 ms 
[INFO ] 2024-03-29 18:36:29.959 - [orders_import_import_import_import_import][Rename Products] - Node Rename Products[2a6d1710-2294-4268-9928-07b2fd94352a] running status set to false 
[INFO ] 2024-03-29 18:36:30.013 - [orders_import_import_import_import_import][Rename Products] - Node Rename Products[2a6d1710-2294-4268-9928-07b2fd94352a] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.013 - [orders_import_import_import_import_import][Rename Products] - Node Rename Products[2a6d1710-2294-4268-9928-07b2fd94352a] monitor closed 
[INFO ] 2024-03-29 18:36:30.013 - [orders_import_import_import_import_import][Rename Products] - Node Rename Products[2a6d1710-2294-4268-9928-07b2fd94352a] close complete, cost 52 ms 
[INFO ] 2024-03-29 18:36:30.013 - [orders_import_import_import_import_import][Delete Products] - Node Delete Products[6ad1fb8c-0004-4d3e-b20d-debecdddf8ab] running status set to false 
[INFO ] 2024-03-29 18:36:30.078 - [orders_import_import_import_import_import][Delete Products] - Node Delete Products[6ad1fb8c-0004-4d3e-b20d-debecdddf8ab] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.078 - [orders_import_import_import_import_import][Delete Products] - Node Delete Products[6ad1fb8c-0004-4d3e-b20d-debecdddf8ab] monitor closed 
[INFO ] 2024-03-29 18:36:30.078 - [orders_import_import_import_import_import][Delete Products] - Node Delete Products[6ad1fb8c-0004-4d3e-b20d-debecdddf8ab] close complete, cost 66 ms 
[INFO ] 2024-03-29 18:36:30.092 - [orders_import_import_import_import_import][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] running status set to false 
[INFO ] 2024-03-29 18:36:30.101 - [orders_import_import_import_import_import][Order Details] - PDK connector node stopped: ScriptExecutor-TargetMysql-6b85a6d4-8e87-4a21-8a9c-50cd2bbfd4ab 
[INFO ] 2024-03-29 18:36:30.101 - [orders_import_import_import_import_import][Order Details] - PDK connector node released: ScriptExecutor-TargetMysql-6b85a6d4-8e87-4a21-8a9c-50cd2bbfd4ab 
[INFO ] 2024-03-29 18:36:30.101 - [orders_import_import_import_import_import][Order Details] - [ScriptExecutorsManager-660697bd57c8b774d7a9422f-07089f27-d80b-4a2a-b3bc-c65910c88215-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.102 - [orders_import_import_import_import_import][Order Details] - PDK connector node stopped: ScriptExecutor-TestMongo-ab5ebe73-c007-4e8b-a066-a15dcab2e4e6 
[INFO ] 2024-03-29 18:36:30.102 - [orders_import_import_import_import_import][Order Details] - PDK connector node released: ScriptExecutor-TestMongo-ab5ebe73-c007-4e8b-a066-a15dcab2e4e6 
[INFO ] 2024-03-29 18:36:30.109 - [orders_import_import_import_import_import][Order Details] - [ScriptExecutorsManager-660697bd57c8b774d7a9422f-07089f27-d80b-4a2a-b3bc-c65910c88215-6600b651928fc21057b4810b] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.109 - [orders_import_import_import_import_import][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.109 - [orders_import_import_import_import_import][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] monitor closed 
[INFO ] 2024-03-29 18:36:30.111 - [orders_import_import_import_import_import][Order Details] - Node Order Details[07089f27-d80b-4a2a-b3bc-c65910c88215] close complete, cost 30 ms 
[INFO ] 2024-03-29 18:36:30.111 - [orders_import_import_import_import_import][Rename Order Details] - Node Rename Order Details[f7f7348c-d52d-47b5-a67f-36b11f001117] running status set to false 
[INFO ] 2024-03-29 18:36:30.209 - [orders_import_import_import_import_import][Rename Order Details] - Node Rename Order Details[f7f7348c-d52d-47b5-a67f-36b11f001117] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.209 - [orders_import_import_import_import_import][Rename Order Details] - Node Rename Order Details[f7f7348c-d52d-47b5-a67f-36b11f001117] monitor closed 
[INFO ] 2024-03-29 18:36:30.209 - [orders_import_import_import_import_import][Rename Order Details] - Node Rename Order Details[f7f7348c-d52d-47b5-a67f-36b11f001117] close complete, cost 98 ms 
[INFO ] 2024-03-29 18:36:30.230 - [orders_import_import_import_import_import][Delete Order Details] - Node Delete Order Details[ec5bb5bc-b1c3-4e2f-88e9-a9e9d6d0b25b] running status set to false 
[INFO ] 2024-03-29 18:36:30.267 - [orders_import_import_import_import_import][Delete Order Details] - Node Delete Order Details[ec5bb5bc-b1c3-4e2f-88e9-a9e9d6d0b25b] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.267 - [orders_import_import_import_import_import][Delete Order Details] - Node Delete Order Details[ec5bb5bc-b1c3-4e2f-88e9-a9e9d6d0b25b] monitor closed 
[INFO ] 2024-03-29 18:36:30.267 - [orders_import_import_import_import_import][Delete Order Details] - Node Delete Order Details[ec5bb5bc-b1c3-4e2f-88e9-a9e9d6d0b25b] close complete, cost 57 ms 
[INFO ] 2024-03-29 18:36:30.267 - [orders_import_import_import_import_import][merge] - Node merge[ad0831cc-442e-4ea2-8661-82ca40d6b971] running status set to false 
[INFO ] 2024-03-29 18:36:30.267 - [orders_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Products_6ad1fb8c-0004-4d3e-b20d-debecdddf8ab__TPORIG 
[INFO ] 2024-03-29 18:36:30.275 - [orders_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Order Details_ec5bb5bc-b1c3-4e2f-88e9-a9e9d6d0b25b__TPORIG 
[INFO ] 2024-03-29 18:36:30.283 - [orders_import_import_import_import_import][merge] - Node merge[ad0831cc-442e-4ea2-8661-82ca40d6b971] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.283 - [orders_import_import_import_import_import][merge] - Node merge[ad0831cc-442e-4ea2-8661-82ca40d6b971] monitor closed 
[INFO ] 2024-03-29 18:36:30.283 - [orders_import_import_import_import_import][merge] - Node merge[ad0831cc-442e-4ea2-8661-82ca40d6b971] close complete, cost 15 ms 
[INFO ] 2024-03-29 18:36:30.283 - [orders_import_import_import_import_import][orders] - Node orders[608b4452-65fa-4afa-8475-354d908e900f] running status set to false 
[INFO ] 2024-03-29 18:36:30.292 - [orders_import_import_import_import_import][orders] - PDK connector node stopped: HazelcastTargetPdkDataNode-608b4452-65fa-4afa-8475-354d908e900f 
[INFO ] 2024-03-29 18:36:30.292 - [orders_import_import_import_import_import][orders] - PDK connector node released: HazelcastTargetPdkDataNode-608b4452-65fa-4afa-8475-354d908e900f 
[INFO ] 2024-03-29 18:36:30.293 - [orders_import_import_import_import_import][orders] - Node orders[608b4452-65fa-4afa-8475-354d908e900f] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.301 - [orders_import_import_import_import_import][orders] - Node orders[608b4452-65fa-4afa-8475-354d908e900f] monitor closed 
[INFO ] 2024-03-29 18:36:30.301 - [orders_import_import_import_import_import][orders] - Node orders[608b4452-65fa-4afa-8475-354d908e900f] close complete, cost 9 ms 
[INFO ] 2024-03-29 18:36:33.679 - [orders_import_import_import_import_import] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 18:36:33.679 - [orders_import_import_import_import_import] - Stopped task aspect(s) 
[INFO ] 2024-03-29 18:36:33.679 - [orders_import_import_import_import_import] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 18:36:33.738 - [orders_import_import_import_import_import] - Remove memory task client succeed, task: orders_import_import_import_import_import[660697bd57c8b774d7a9422f] 
[INFO ] 2024-03-29 18:36:33.738 - [orders_import_import_import_import_import] - Destroy memory task client cache succeed, task: orders_import_import_import_import_import[660697bd57c8b774d7a9422f] 
