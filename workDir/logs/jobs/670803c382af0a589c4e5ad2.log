[INFO ] 2024-10-11 00:42:04.280 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-4fb10019-71f8-43da-afd1-b96dbb982690 complete, cost 11837ms 
[INFO ] 2024-10-11 00:42:18.504 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-30ff1ec8-453d-41c8-8758-033c924e276e complete, cost 11448ms 
[INFO ] 2024-10-11 00:42:26.332 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-6996e29e-5b29-462a-a493-5be9bd3bec67 complete, cost 15765ms 
[INFO ] 2024-10-11 00:42:31.557 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-fd05de0f-9966-4fb9-b8b5-1551d449a4a3 complete, cost 14980ms 
[INFO ] 2024-10-11 00:42:45.489 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-3f63cc00-8fce-4880-aebf-fbcefbe6d3bd complete, cost 11424ms 
[INFO ] 2024-10-11 00:43:02.024 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-e70e1ff0-d4b6-40c8-962d-d47f400c6aaf complete, cost 11115ms 
[INFO ] 2024-10-11 00:43:07.756 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-a5ffa98b-150c-4628-9c61-6d543930f315 complete, cost 16273ms 
[INFO ] 2024-10-11 00:43:10.861 - [任务 5] - Task initialization... 
[INFO ] 2024-10-11 00:43:10.861 - [任务 5] - Start task milestones: 670803c382af0a589c4e5ad2(任务 5) 
[INFO ] 2024-10-11 00:43:22.740 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-ee45832d-fd4a-4181-b12b-505a4c029b89 complete, cost 11772ms 
[INFO ] 2024-10-11 00:43:22.843 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-11 00:43:22.993 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-11 00:43:22.993 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:43:22.993 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:43:22.993 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:43:22.993 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:43:22.993 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:43:22.995 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:43:22.995 - [任务 5][增强JS] - Node js_processor(增强JS: d6529fe4-891b-4589-8881-0ca9bc2e4301) enable batch process 
[INFO ] 2024-10-11 00:43:26.259 - [任务 5][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-11 00:43:28.860 - [任务 5][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-10-11 00:43:28.861 - [任务 5][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-10-11 00:43:28.861 - [任务 5][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-11 00:43:28.876 - [任务 5][BMSQL_ITEM] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":4751,"gtidSet":""} 
[INFO ] 2024-10-11 00:43:28.876 - [任务 5][BMSQL_ITEM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-11 00:43:28.958 - [任务 5][BMSQL_ITEM] - Initial sync started 
[INFO ] 2024-10-11 00:43:28.964 - [任务 5][BMSQL_ITEM] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-10-11 00:43:28.964 - [任务 5][BMSQL_ITEM] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-10-11 00:43:29.021 - [任务 5][BMSQL_ITEM] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-11 00:43:29.022 - [任务 5][BMSQL_ITEM] - Query table 'BMSQL_ITEM' counts: 3 
[INFO ] 2024-10-11 00:43:29.022 - [任务 5][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-11 00:43:29.022 - [任务 5][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-10-11 00:43:29.022 - [任务 5][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-11 00:43:29.026 - [任务 5][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: {"filename":"binlog.000036","position":4751,"gtidSet":""} 
[INFO ] 2024-10-11 00:43:29.130 - [任务 5][BMSQL_ITEM] - Starting mysql cdc, server name: 3585c4c6-4692-403f-bbff-e29665b89cc7 
[INFO ] 2024-10-11 00:43:29.131 - [任务 5][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"3585c4c6-4692-403f-bbff-e29665b89cc7","offset":{"{\"server\":\"3585c4c6-4692-403f-bbff-e29665b89cc7\"}":"{\"file\":\"binlog.000036\",\"pos\":4751,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1354330154
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 3585c4c6-4692-403f-bbff-e29665b89cc7
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-3585c4c6-4692-403f-bbff-e29665b89cc7
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 3585c4c6-4692-403f-bbff-e29665b89cc7
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-11 00:43:29.542 - [任务 5][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-10-11 00:43:58.491 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] running status set to false 
[INFO ] 2024-10-11 00:43:58.581 - [任务 5][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-11 00:43:58.591 - [任务 5][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-10-11 00:43:58.591 - [任务 5][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-10-11 00:43:58.598 - [任务 5][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-39d7338a-b41e-46de-8184-03b38b9bedf9 
[INFO ] 2024-10-11 00:43:58.598 - [任务 5][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-39d7338a-b41e-46de-8184-03b38b9bedf9 
[INFO ] 2024-10-11 00:43:58.598 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] schema data cleaned 
[INFO ] 2024-10-11 00:43:58.600 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] monitor closed 
[INFO ] 2024-10-11 00:43:58.601 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] close complete, cost 141 ms 
[INFO ] 2024-10-11 00:43:58.610 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] running status set to false 
[INFO ] 2024-10-11 00:43:58.610 - [任务 5][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-65b22ee5-6bfc-4dd1-aeff-c42ffffda38e 
[INFO ] 2024-10-11 00:43:58.611 - [任务 5][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-65b22ee5-6bfc-4dd1-aeff-c42ffffda38e 
[INFO ] 2024-10-11 00:43:58.612 - [任务 5][增强JS] - [ScriptExecutorsManager-670803c382af0a589c4e5ad2-d6529fe4-891b-4589-8881-0ca9bc2e4301-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:43:58.620 - [任务 5][增强JS] - PDK connector node stopped: ScriptExecutor-targetDummy-0bff2433-e6f7-4e1d-ac20-4714a16efe81 
[INFO ] 2024-10-11 00:43:58.620 - [任务 5][增强JS] - PDK connector node released: ScriptExecutor-targetDummy-0bff2433-e6f7-4e1d-ac20-4714a16efe81 
[INFO ] 2024-10-11 00:43:58.620 - [任务 5][增强JS] - [ScriptExecutorsManager-670803c382af0a589c4e5ad2-d6529fe4-891b-4589-8881-0ca9bc2e4301-6708035d82af0a589c4e59c9] schema data cleaned 
[INFO ] 2024-10-11 00:43:58.626 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] schema data cleaned 
[INFO ] 2024-10-11 00:43:58.629 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] monitor closed 
[INFO ] 2024-10-11 00:43:58.629 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] close complete, cost 28 ms 
[INFO ] 2024-10-11 00:43:58.642 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] running status set to false 
[INFO ] 2024-10-11 00:43:58.647 - [任务 5][dummy_test] - Stop connector 
[INFO ] 2024-10-11 00:43:58.651 - [任务 5][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-e3ed973e-23c2-49ab-92ff-b387acd07c75 
[INFO ] 2024-10-11 00:43:58.651 - [任务 5][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-e3ed973e-23c2-49ab-92ff-b387acd07c75 
[INFO ] 2024-10-11 00:43:58.652 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] schema data cleaned 
[INFO ] 2024-10-11 00:43:58.652 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] monitor closed 
[INFO ] 2024-10-11 00:43:58.858 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] close complete, cost 23 ms 
[INFO ] 2024-10-11 00:44:01.094 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-11 00:44:01.096 - [任务 5] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3be3b3b4 
[INFO ] 2024-10-11 00:44:01.096 - [任务 5] - Stop task milestones: 670803c382af0a589c4e5ad2(任务 5)  
[INFO ] 2024-10-11 00:44:01.230 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:44:01.231 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-10-11 00:44:01.255 - [任务 5] - Remove memory task client succeed, task: 任务 5[670803c382af0a589c4e5ad2] 
[INFO ] 2024-10-11 00:44:01.255 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[670803c382af0a589c4e5ad2] 
[INFO ] 2024-10-11 00:45:00.406 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-538b7c2a-9935-4a2f-a9a4-d4fb793425b0 complete, cost 11281ms 
[INFO ] 2024-10-11 00:45:17.845 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-f736593e-d7d2-4f35-a548-b73ae8b278b0 complete, cost 11487ms 
[INFO ] 2024-10-11 00:45:31.945 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-5b04bf77-2617-43de-ba4c-f0d54d7850e9 complete, cost 11535ms 
[INFO ] 2024-10-11 00:45:37.633 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-119b280c-f65b-4d2f-b3a8-85f9565cf6dd complete, cost 16654ms 
[INFO ] 2024-10-11 00:45:39.501 - [任务 5] - Task initialization... 
[INFO ] 2024-10-11 00:45:39.502 - [任务 5] - Start task milestones: 670803c382af0a589c4e5ad2(任务 5) 
[INFO ] 2024-10-11 00:45:51.321 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-3253aee3-f85f-4df7-adb4-47b89b930e2f complete, cost 11690ms 
[INFO ] 2024-10-11 00:45:51.448 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-11 00:45:51.449 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-11 00:45:51.521 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:45:51.521 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:45:51.521 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:45:51.521 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 00:45:51.521 - [任务 5][增强JS] - Node js_processor(增强JS: d6529fe4-891b-4589-8881-0ca9bc2e4301) enable batch process 
[INFO ] 2024-10-11 00:45:51.552 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:45:51.552 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 00:45:54.474 - [任务 5][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-11 00:45:57.146 - [任务 5][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-10-11 00:45:57.146 - [任务 5][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-10-11 00:45:57.154 - [任务 5][BMSQL_ITEM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-11 00:45:57.155 - [任务 5][BMSQL_ITEM] - batch offset found: {"BMSQL_ITEM":{"batch_read_connector_status":"OVER"}},stream offset found: {"name":"3585c4c6-4692-403f-bbff-e29665b89cc7","offset":{"{\"server\":\"3585c4c6-4692-403f-bbff-e29665b89cc7\"}":"{\"ts_sec\":1728578609,\"file\":\"binlog.000036\",\"pos\":4751}"}} 
[INFO ] 2024-10-11 00:45:57.155 - [任务 5][BMSQL_ITEM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-11 00:45:57.214 - [任务 5][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-10-11 00:45:57.214 - [任务 5][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-11 00:45:57.221 - [任务 5][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: {"name":"3585c4c6-4692-403f-bbff-e29665b89cc7","offset":{"{\"server\":\"3585c4c6-4692-403f-bbff-e29665b89cc7\"}":"{\"ts_sec\":1728578609,\"file\":\"binlog.000036\",\"pos\":4751}"}} 
[INFO ] 2024-10-11 00:45:57.297 - [任务 5][BMSQL_ITEM] - Starting mysql cdc, server name: 3585c4c6-4692-403f-bbff-e29665b89cc7 
[INFO ] 2024-10-11 00:45:57.298 - [任务 5][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"3585c4c6-4692-403f-bbff-e29665b89cc7","offset":{"{\"server\":\"3585c4c6-4692-403f-bbff-e29665b89cc7\"}":"{\"ts_sec\":1728578609,\"file\":\"binlog.000036\",\"pos\":4751}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1049505474
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 3585c4c6-4692-403f-bbff-e29665b89cc7
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-3585c4c6-4692-403f-bbff-e29665b89cc7
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 3585c4c6-4692-403f-bbff-e29665b89cc7
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-11 00:45:58.110 - [任务 5][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-10-11 00:46:13.057 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] running status set to false 
[INFO ] 2024-10-11 00:46:13.059 - [任务 5][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-11 00:46:13.059 - [任务 5][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-10-11 00:46:13.085 - [任务 5][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-10-11 00:46:13.085 - [任务 5][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-39d7338a-b41e-46de-8184-03b38b9bedf9 
[INFO ] 2024-10-11 00:46:13.086 - [任务 5][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-39d7338a-b41e-46de-8184-03b38b9bedf9 
[INFO ] 2024-10-11 00:46:13.086 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] schema data cleaned 
[INFO ] 2024-10-11 00:46:13.089 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] monitor closed 
[INFO ] 2024-10-11 00:46:13.090 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] close complete, cost 112 ms 
[INFO ] 2024-10-11 00:46:13.090 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] running status set to false 
[INFO ] 2024-10-11 00:46:13.090 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] schema data cleaned 
[INFO ] 2024-10-11 00:46:13.090 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] monitor closed 
[INFO ] 2024-10-11 00:46:13.091 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] close complete, cost 1 ms 
[INFO ] 2024-10-11 00:46:13.097 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] running status set to false 
[INFO ] 2024-10-11 00:46:13.097 - [任务 5][dummy_test] - Stop connector 
[INFO ] 2024-10-11 00:46:13.101 - [任务 5][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-e3ed973e-23c2-49ab-92ff-b387acd07c75 
[INFO ] 2024-10-11 00:46:13.101 - [任务 5][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-e3ed973e-23c2-49ab-92ff-b387acd07c75 
[INFO ] 2024-10-11 00:46:13.101 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] schema data cleaned 
[INFO ] 2024-10-11 00:46:13.101 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] monitor closed 
[INFO ] 2024-10-11 00:46:13.307 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] close complete, cost 10 ms 
[INFO ] 2024-10-11 00:46:16.390 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-11 00:46:16.391 - [任务 5] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@52c8f21a 
[INFO ] 2024-10-11 00:46:16.392 - [任务 5] - Stop task milestones: 670803c382af0a589c4e5ad2(任务 5)  
[INFO ] 2024-10-11 00:46:16.531 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:46:16.532 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-10-11 00:46:16.556 - [任务 5] - Remove memory task client succeed, task: 任务 5[670803c382af0a589c4e5ad2] 
[INFO ] 2024-10-11 00:46:16.557 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[670803c382af0a589c4e5ad2] 
[INFO ] 2024-10-11 00:46:29.240 - [任务 5] - Task initialization... 
[INFO ] 2024-10-11 00:46:29.241 - [任务 5] - Start task milestones: 670803c382af0a589c4e5ad2(任务 5) 
[INFO ] 2024-10-11 00:46:42.045 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-d55bddd5-5554-45ac-aa90-c8ee0846640c complete, cost 12714ms 
[INFO ] 2024-10-11 00:46:42.182 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-11 00:46:42.182 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-11 00:46:42.270 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:46:42.270 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:46:42.271 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:46:42.271 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 00:46:42.271 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 00:46:42.271 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 00:46:42.271 - [任务 5][增强JS] - Node js_processor(增强JS: d6529fe4-891b-4589-8881-0ca9bc2e4301) enable batch process 
[INFO ] 2024-10-11 00:46:45.708 - [任务 5][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-11 00:46:48.013 - [任务 5][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-10-11 00:46:48.014 - [任务 5][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-10-11 00:46:48.015 - [任务 5][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-11 00:46:48.036 - [任务 5][BMSQL_ITEM] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":4751,"gtidSet":""} 
[INFO ] 2024-10-11 00:46:48.036 - [任务 5][BMSQL_ITEM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-11 00:46:48.090 - [任务 5][BMSQL_ITEM] - Initial sync started 
[INFO ] 2024-10-11 00:46:48.103 - [任务 5][BMSQL_ITEM] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-10-11 00:46:48.103 - [任务 5][BMSQL_ITEM] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-10-11 00:46:48.152 - [任务 5][BMSQL_ITEM] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-11 00:46:48.152 - [任务 5][BMSQL_ITEM] - Query table 'BMSQL_ITEM' counts: 3 
[INFO ] 2024-10-11 00:46:48.153 - [任务 5][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-11 00:46:48.153 - [任务 5][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-10-11 00:46:48.158 - [任务 5][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-11 00:46:48.158 - [任务 5][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: {"filename":"binlog.000036","position":4751,"gtidSet":""} 
[INFO ] 2024-10-11 00:46:48.264 - [任务 5][BMSQL_ITEM] - Starting mysql cdc, server name: 1d744b61-767b-4fba-b77f-3eccafe165cf 
[INFO ] 2024-10-11 00:46:48.265 - [任务 5][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"1d744b61-767b-4fba-b77f-3eccafe165cf","offset":{"{\"server\":\"1d744b61-767b-4fba-b77f-3eccafe165cf\"}":"{\"file\":\"binlog.000036\",\"pos\":4751,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2006930937
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 1d744b61-767b-4fba-b77f-3eccafe165cf
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-1d744b61-767b-4fba-b77f-3eccafe165cf
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 1d744b61-767b-4fba-b77f-3eccafe165cf
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-11 00:46:48.679 - [任务 5][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-10-11 00:46:56.961 - [任务 5][增强JS] - null 
[INFO ] 2024-10-11 00:46:56.964 - [任务 5][增强JS] - null 
[INFO ] 2024-10-11 00:46:56.964 - [任务 5][增强JS] - null 
[INFO ] 2024-10-11 00:47:16.079 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] running status set to false 
[INFO ] 2024-10-11 00:47:16.175 - [任务 5][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-11 00:47:16.175 - [任务 5][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-10-11 00:47:16.176 - [任务 5][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-10-11 00:47:16.191 - [任务 5][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-39d7338a-b41e-46de-8184-03b38b9bedf9 
[INFO ] 2024-10-11 00:47:16.191 - [任务 5][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-39d7338a-b41e-46de-8184-03b38b9bedf9 
[INFO ] 2024-10-11 00:47:16.191 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] schema data cleaned 
[INFO ] 2024-10-11 00:47:16.192 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] monitor closed 
[INFO ] 2024-10-11 00:47:16.197 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] close complete, cost 129 ms 
[INFO ] 2024-10-11 00:47:16.202 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] running status set to false 
[INFO ] 2024-10-11 00:47:16.205 - [任务 5][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-6834de7f-882e-4765-af6d-eae0fec19bbc 
[INFO ] 2024-10-11 00:47:16.207 - [任务 5][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-6834de7f-882e-4765-af6d-eae0fec19bbc 
[INFO ] 2024-10-11 00:47:16.207 - [任务 5][增强JS] - [ScriptExecutorsManager-670803c382af0a589c4e5ad2-d6529fe4-891b-4589-8881-0ca9bc2e4301-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:47:16.215 - [任务 5][增强JS] - PDK connector node stopped: ScriptExecutor-targetDummy-a653d0c4-0d9f-407f-8b44-8adff935ddbb 
[INFO ] 2024-10-11 00:47:16.215 - [任务 5][增强JS] - PDK connector node released: ScriptExecutor-targetDummy-a653d0c4-0d9f-407f-8b44-8adff935ddbb 
[INFO ] 2024-10-11 00:47:16.216 - [任务 5][增强JS] - [ScriptExecutorsManager-670803c382af0a589c4e5ad2-d6529fe4-891b-4589-8881-0ca9bc2e4301-6708035d82af0a589c4e59c9] schema data cleaned 
[INFO ] 2024-10-11 00:47:16.222 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] schema data cleaned 
[INFO ] 2024-10-11 00:47:16.222 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] monitor closed 
[INFO ] 2024-10-11 00:47:16.223 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] close complete, cost 29 ms 
[INFO ] 2024-10-11 00:47:16.223 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] running status set to false 
[INFO ] 2024-10-11 00:47:16.244 - [任务 5][dummy_test] - Stop connector 
[INFO ] 2024-10-11 00:47:16.244 - [任务 5][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-e3ed973e-23c2-49ab-92ff-b387acd07c75 
[INFO ] 2024-10-11 00:47:16.245 - [任务 5][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-e3ed973e-23c2-49ab-92ff-b387acd07c75 
[INFO ] 2024-10-11 00:47:16.245 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] schema data cleaned 
[INFO ] 2024-10-11 00:47:16.247 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] monitor closed 
[INFO ] 2024-10-11 00:47:16.247 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] close complete, cost 24 ms 
[INFO ] 2024-10-11 00:47:16.650 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-11 00:47:16.653 - [任务 5] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1697bb1e 
[INFO ] 2024-10-11 00:47:16.653 - [任务 5] - Stop task milestones: 670803c382af0a589c4e5ad2(任务 5)  
[INFO ] 2024-10-11 00:47:16.786 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:47:16.786 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-10-11 00:47:16.815 - [任务 5] - Remove memory task client succeed, task: 任务 5[670803c382af0a589c4e5ad2] 
[INFO ] 2024-10-11 00:47:16.816 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[670803c382af0a589c4e5ad2] 
[INFO ] 2024-10-11 00:47:31.563 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-151a35eb-12bc-4437-9d96-4072fd608854 complete, cost 11730ms 
[INFO ] 2024-10-11 00:47:45.593 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-d0324d0a-38aa-4bb2-899f-5ed0d3e16660 complete, cost 14225ms 
[INFO ] 2024-10-11 00:47:50.720 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-444dcfa3-d5ec-47c0-865d-5d31ee8dc0ba complete, cost 19994ms 
[INFO ] 2024-10-11 00:48:07.592 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-827ff1a7-d4dc-45c2-9e01-77ff4b717aca complete, cost 14232ms 
[INFO ] 2024-10-11 00:48:17.982 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-3ba68e26-ec88-4c5c-932b-1019f666559b complete, cost 24041ms 
[INFO ] 2024-10-11 00:49:13.346 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-83eb97ac-82d8-48c8-9624-a8fa1e98cf23 complete, cost 14506ms 
[INFO ] 2024-10-11 00:49:28.967 - [任务 5] - Task initialization... 
[INFO ] 2024-10-11 00:49:28.970 - [任务 5] - Start task milestones: 670803c382af0a589c4e5ad2(任务 5) 
[INFO ] 2024-10-11 00:49:43.204 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-d796e13c-b3cc-405b-823d-008a735eb830 complete, cost 14125ms 
[INFO ] 2024-10-11 00:49:43.333 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-11 00:49:43.334 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-11 00:49:43.402 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:49:43.403 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:49:43.403 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:49:43.403 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:49:43.403 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:49:43.403 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:49:43.403 - [任务 5][增强JS] - Node js_processor(增强JS: d6529fe4-891b-4589-8881-0ca9bc2e4301) enable batch process 
[INFO ] 2024-10-11 00:49:46.832 - [任务 5][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-11 00:49:49.245 - [任务 5][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-10-11 00:49:49.246 - [任务 5][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-10-11 00:49:49.246 - [任务 5][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-11 00:49:49.260 - [任务 5][BMSQL_ITEM] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":4751,"gtidSet":""} 
[INFO ] 2024-10-11 00:49:49.260 - [任务 5][BMSQL_ITEM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-11 00:49:49.325 - [任务 5][BMSQL_ITEM] - Initial sync started 
[INFO ] 2024-10-11 00:49:49.325 - [任务 5][BMSQL_ITEM] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-10-11 00:49:49.372 - [任务 5][BMSQL_ITEM] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-10-11 00:49:49.372 - [任务 5][BMSQL_ITEM] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-11 00:49:49.374 - [任务 5][BMSQL_ITEM] - Query table 'BMSQL_ITEM' counts: 3 
[INFO ] 2024-10-11 00:49:49.374 - [任务 5][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-11 00:49:49.375 - [任务 5][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-10-11 00:49:49.378 - [任务 5][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-11 00:49:49.378 - [任务 5][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: {"filename":"binlog.000036","position":4751,"gtidSet":""} 
[INFO ] 2024-10-11 00:49:49.466 - [任务 5][BMSQL_ITEM] - Starting mysql cdc, server name: aff58925-c806-4c7b-9543-b5f47f6133f0 
[INFO ] 2024-10-11 00:49:49.467 - [任务 5][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"aff58925-c806-4c7b-9543-b5f47f6133f0","offset":{"{\"server\":\"aff58925-c806-4c7b-9543-b5f47f6133f0\"}":"{\"file\":\"binlog.000036\",\"pos\":4751,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 996513084
  time.precision.mode: adaptive_time_microseconds
  database.server.name: aff58925-c806-4c7b-9543-b5f47f6133f0
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-aff58925-c806-4c7b-9543-b5f47f6133f0
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: aff58925-c806-4c7b-9543-b5f47f6133f0
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-11 00:49:49.873 - [任务 5][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-10-11 00:49:58.010 - [任务 5][增强JS] - null 
[INFO ] 2024-10-11 00:49:58.012 - [任务 5][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length 
[ERROR] 2024-10-11 00:49:58.021 - [任务 5][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length
	<js>.process(<eval>:4)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:282)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:388)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:573)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:566)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$0(HazelcastProcessorBaseNode.java:470)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:282)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:388)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$batchProcess$9(HazelcastProcessorBaseNode.java:190)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:186)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:131)
	... 7 more
Caused by: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length
	at <js>.process(<eval>:4)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 13 more

[INFO ] 2024-10-11 00:49:58.023 - [任务 5][增强JS] - Job suspend in error handle 
[INFO ] 2024-10-11 00:49:58.606 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] running status set to false 
[INFO ] 2024-10-11 00:49:58.611 - [任务 5][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-11 00:49:58.611 - [任务 5][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-10-11 00:49:58.622 - [任务 5][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-10-11 00:49:58.628 - [任务 5][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-39d7338a-b41e-46de-8184-03b38b9bedf9 
[INFO ] 2024-10-11 00:49:58.628 - [任务 5][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-39d7338a-b41e-46de-8184-03b38b9bedf9 
[INFO ] 2024-10-11 00:49:58.629 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] schema data cleaned 
[INFO ] 2024-10-11 00:49:58.629 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] monitor closed 
[INFO ] 2024-10-11 00:49:58.633 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] close complete, cost 185 ms 
[INFO ] 2024-10-11 00:49:58.638 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] running status set to false 
[INFO ] 2024-10-11 00:49:58.643 - [任务 5][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-7130ab01-bb61-41fc-bc3a-86c809c725e1 
[INFO ] 2024-10-11 00:49:58.643 - [任务 5][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-7130ab01-bb61-41fc-bc3a-86c809c725e1 
[INFO ] 2024-10-11 00:49:58.654 - [任务 5][增强JS] - [ScriptExecutorsManager-670803c382af0a589c4e5ad2-d6529fe4-891b-4589-8881-0ca9bc2e4301-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:49:58.654 - [任务 5][增强JS] - PDK connector node stopped: ScriptExecutor-targetDummy-15f84606-c4d7-4db5-bc11-3e17cf0ad2d4 
[INFO ] 2024-10-11 00:49:58.654 - [任务 5][增强JS] - PDK connector node released: ScriptExecutor-targetDummy-15f84606-c4d7-4db5-bc11-3e17cf0ad2d4 
[INFO ] 2024-10-11 00:49:58.654 - [任务 5][增强JS] - [ScriptExecutorsManager-670803c382af0a589c4e5ad2-d6529fe4-891b-4589-8881-0ca9bc2e4301-6708035d82af0a589c4e59c9] schema data cleaned 
[INFO ] 2024-10-11 00:49:58.656 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] schema data cleaned 
[INFO ] 2024-10-11 00:49:58.656 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] monitor closed 
[INFO ] 2024-10-11 00:49:58.656 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] close complete, cost 24 ms 
[INFO ] 2024-10-11 00:49:58.656 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] running status set to false 
[INFO ] 2024-10-11 00:49:58.659 - [任务 5][dummy_test] - Stop connector 
[INFO ] 2024-10-11 00:49:58.659 - [任务 5][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-e3ed973e-23c2-49ab-92ff-b387acd07c75 
[INFO ] 2024-10-11 00:49:58.659 - [任务 5][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-e3ed973e-23c2-49ab-92ff-b387acd07c75 
[INFO ] 2024-10-11 00:49:58.659 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] schema data cleaned 
[INFO ] 2024-10-11 00:49:58.659 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] monitor closed 
[INFO ] 2024-10-11 00:49:58.862 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] close complete, cost 3 ms 
[INFO ] 2024-10-11 00:50:01.968 - [任务 5] - Task [任务 5] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-11 00:50:01.973 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-11 00:50:01.974 - [任务 5] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2392c7e5 
[INFO ] 2024-10-11 00:50:01.977 - [任务 5] - Stop task milestones: 670803c382af0a589c4e5ad2(任务 5)  
[INFO ] 2024-10-11 00:50:02.109 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:50:02.109 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-10-11 00:50:02.124 - [任务 5] - Remove memory task client succeed, task: 任务 5[670803c382af0a589c4e5ad2] 
[INFO ] 2024-10-11 00:50:02.124 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[670803c382af0a589c4e5ad2] 
[INFO ] 2024-10-11 00:53:46.187 - [任务 5] - Task initialization... 
[INFO ] 2024-10-11 00:53:46.189 - [任务 5] - Start task milestones: 670803c382af0a589c4e5ad2(任务 5) 
[INFO ] 2024-10-11 00:54:00.879 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-1a266c09-2509-474a-a291-5add4849edf8 complete, cost 14551ms 
[INFO ] 2024-10-11 00:54:00.936 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-11 00:54:01.069 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-11 00:54:01.069 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:54:01.069 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:54:01.069 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:54:01.069 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:54:01.070 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 00:54:01.070 - [任务 5][增强JS] - Node js_processor(增强JS: d6529fe4-891b-4589-8881-0ca9bc2e4301) enable batch process 
[INFO ] 2024-10-11 00:54:01.070 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 00:54:04.503 - [任务 5][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-11 00:54:07.031 - [任务 5][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-10-11 00:54:07.031 - [任务 5][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-10-11 00:54:07.031 - [任务 5][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-11 00:54:07.047 - [任务 5][BMSQL_ITEM] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":4751,"gtidSet":""} 
[INFO ] 2024-10-11 00:54:07.047 - [任务 5][BMSQL_ITEM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-11 00:54:07.101 - [任务 5][BMSQL_ITEM] - Initial sync started 
[INFO ] 2024-10-11 00:54:07.101 - [任务 5][BMSQL_ITEM] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-10-11 00:54:07.106 - [任务 5][BMSQL_ITEM] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-10-11 00:54:07.145 - [任务 5][BMSQL_ITEM] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-11 00:54:07.145 - [任务 5][BMSQL_ITEM] - Query table 'BMSQL_ITEM' counts: 3 
[INFO ] 2024-10-11 00:54:07.145 - [任务 5][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-11 00:54:07.145 - [任务 5][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-10-11 00:54:07.146 - [任务 5][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-11 00:54:07.150 - [任务 5][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: {"filename":"binlog.000036","position":4751,"gtidSet":""} 
[INFO ] 2024-10-11 00:54:07.225 - [任务 5][BMSQL_ITEM] - Starting mysql cdc, server name: 26bad55b-0eaa-42c1-9be2-3e37f1520adf 
[INFO ] 2024-10-11 00:54:07.226 - [任务 5][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"26bad55b-0eaa-42c1-9be2-3e37f1520adf","offset":{"{\"server\":\"26bad55b-0eaa-42c1-9be2-3e37f1520adf\"}":"{\"file\":\"binlog.000036\",\"pos\":4751,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 829918360
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 26bad55b-0eaa-42c1-9be2-3e37f1520adf
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-26bad55b-0eaa-42c1-9be2-3e37f1520adf
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 26bad55b-0eaa-42c1-9be2-3e37f1520adf
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-11 00:54:07.634 - [任务 5][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-10-11 00:54:15.931 - [任务 5][增强JS] - null 
[INFO ] 2024-10-11 00:54:15.947 - [任务 5][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length 
[ERROR] 2024-10-11 00:54:15.952 - [任务 5][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length
	<js>.process(<eval>:4)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:282)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:388)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:573)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:566)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$0(HazelcastProcessorBaseNode.java:470)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:282)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:388)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$batchProcess$9(HazelcastProcessorBaseNode.java:190)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:186)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:131)
	... 7 more
Caused by: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length
	at <js>.process(<eval>:4)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 13 more

[INFO ] 2024-10-11 00:54:16.155 - [任务 5][增强JS] - Job suspend in error handle 
[INFO ] 2024-10-11 00:54:16.350 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] running status set to false 
[INFO ] 2024-10-11 00:54:16.363 - [任务 5][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-11 00:54:16.364 - [任务 5][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-10-11 00:54:16.366 - [任务 5][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-10-11 00:54:16.373 - [任务 5][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-39d7338a-b41e-46de-8184-03b38b9bedf9 
[INFO ] 2024-10-11 00:54:16.373 - [任务 5][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-39d7338a-b41e-46de-8184-03b38b9bedf9 
[INFO ] 2024-10-11 00:54:16.374 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] schema data cleaned 
[INFO ] 2024-10-11 00:54:16.374 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] monitor closed 
[INFO ] 2024-10-11 00:54:16.375 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] close complete, cost 129 ms 
[INFO ] 2024-10-11 00:54:16.375 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] running status set to false 
[INFO ] 2024-10-11 00:54:16.391 - [任务 5][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-6a3736c5-caeb-4613-81ef-966df76f333c 
[INFO ] 2024-10-11 00:54:16.391 - [任务 5][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-6a3736c5-caeb-4613-81ef-966df76f333c 
[INFO ] 2024-10-11 00:54:16.392 - [任务 5][增强JS] - [ScriptExecutorsManager-670803c382af0a589c4e5ad2-d6529fe4-891b-4589-8881-0ca9bc2e4301-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:54:16.402 - [任务 5][增强JS] - PDK connector node stopped: ScriptExecutor-targetDummy-be383175-eef8-4087-981e-9afc6ea7d7fc 
[INFO ] 2024-10-11 00:54:16.402 - [任务 5][增强JS] - PDK connector node released: ScriptExecutor-targetDummy-be383175-eef8-4087-981e-9afc6ea7d7fc 
[INFO ] 2024-10-11 00:54:16.402 - [任务 5][增强JS] - [ScriptExecutorsManager-670803c382af0a589c4e5ad2-d6529fe4-891b-4589-8881-0ca9bc2e4301-6708035d82af0a589c4e59c9] schema data cleaned 
[INFO ] 2024-10-11 00:54:16.404 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] schema data cleaned 
[INFO ] 2024-10-11 00:54:16.404 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] monitor closed 
[INFO ] 2024-10-11 00:54:16.405 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] close complete, cost 29 ms 
[INFO ] 2024-10-11 00:54:16.405 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] running status set to false 
[INFO ] 2024-10-11 00:54:16.411 - [任务 5][dummy_test] - Stop connector 
[INFO ] 2024-10-11 00:54:16.411 - [任务 5][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-e3ed973e-23c2-49ab-92ff-b387acd07c75 
[INFO ] 2024-10-11 00:54:16.411 - [任务 5][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-e3ed973e-23c2-49ab-92ff-b387acd07c75 
[INFO ] 2024-10-11 00:54:16.411 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] schema data cleaned 
[INFO ] 2024-10-11 00:54:16.412 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] monitor closed 
[INFO ] 2024-10-11 00:54:16.412 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] close complete, cost 7 ms 
[INFO ] 2024-10-11 00:54:17.299 - [任务 5] - Task [任务 5] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-11 00:54:17.299 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-11 00:54:17.300 - [任务 5] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1d043764 
[INFO ] 2024-10-11 00:54:17.427 - [任务 5] - Stop task milestones: 670803c382af0a589c4e5ad2(任务 5)  
[INFO ] 2024-10-11 00:54:17.427 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:54:17.427 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-10-11 00:54:17.443 - [任务 5] - Remove memory task client succeed, task: 任务 5[670803c382af0a589c4e5ad2] 
[INFO ] 2024-10-11 00:54:17.447 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[670803c382af0a589c4e5ad2] 
[INFO ] 2024-10-11 00:54:54.052 - [任务 5] - Task initialization... 
[INFO ] 2024-10-11 00:54:54.054 - [任务 5] - Start task milestones: 670803c382af0a589c4e5ad2(任务 5) 
[INFO ] 2024-10-11 00:55:28.157 - [任务 5] - load tapTable task 670803c382af0a589c4e5ad1-8347a779-2da8-4d65-ac70-b63e51e80dbc complete, cost 34012ms 
[INFO ] 2024-10-11 00:55:28.217 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-11 00:55:28.296 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-11 00:55:28.296 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:55:28.296 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:55:28.296 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:55:28.296 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 00:55:28.296 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 00:55:28.296 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:55:28.296 - [任务 5][增强JS] - Node js_processor(增强JS: d6529fe4-891b-4589-8881-0ca9bc2e4301) enable batch process 
[INFO ] 2024-10-11 00:55:31.735 - [任务 5][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-11 00:55:34.237 - [任务 5][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-10-11 00:55:34.239 - [任务 5][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-10-11 00:55:34.239 - [任务 5][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-11 00:55:34.252 - [任务 5][BMSQL_ITEM] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":4751,"gtidSet":""} 
[INFO ] 2024-10-11 00:55:34.252 - [任务 5][BMSQL_ITEM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-11 00:55:34.302 - [任务 5][BMSQL_ITEM] - Initial sync started 
[INFO ] 2024-10-11 00:55:34.302 - [任务 5][BMSQL_ITEM] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-10-11 00:55:34.346 - [任务 5][BMSQL_ITEM] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-10-11 00:55:34.347 - [任务 5][BMSQL_ITEM] - Query table 'BMSQL_ITEM' counts: 3 
[INFO ] 2024-10-11 00:55:34.348 - [任务 5][BMSQL_ITEM] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-11 00:55:34.348 - [任务 5][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-11 00:55:34.348 - [任务 5][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-10-11 00:55:34.349 - [任务 5][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-11 00:55:34.415 - [任务 5][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: {"filename":"binlog.000036","position":4751,"gtidSet":""} 
[INFO ] 2024-10-11 00:55:34.416 - [任务 5][BMSQL_ITEM] - Starting mysql cdc, server name: 26efce3e-8587-462d-8d26-137e4edf2116 
[INFO ] 2024-10-11 00:55:34.628 - [任务 5][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"26efce3e-8587-462d-8d26-137e4edf2116","offset":{"{\"server\":\"26efce3e-8587-462d-8d26-137e4edf2116\"}":"{\"file\":\"binlog.000036\",\"pos\":4751,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1104377825
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 26efce3e-8587-462d-8d26-137e4edf2116
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-26efce3e-8587-462d-8d26-137e4edf2116
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 26efce3e-8587-462d-8d26-137e4edf2116
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-11 00:55:34.725 - [任务 5][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-10-11 00:55:52.312 - [任务 5][增强JS] - null 
[INFO ] 2024-10-11 00:55:52.340 - [任务 5][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length 
[ERROR] 2024-10-11 00:55:52.350 - [任务 5][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length
	<js>.process(<eval>:4)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:282)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:388)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:573)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:566)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$0(HazelcastProcessorBaseNode.java:470)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:282)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:388)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$batchProcess$9(HazelcastProcessorBaseNode.java:190)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:186)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:131)
	... 7 more
Caused by: org.graalvm.polyglot.PolyglotException: RangeError: Invalid array length
	at <js>.process(<eval>:4)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 13 more

[INFO ] 2024-10-11 00:55:52.351 - [任务 5][增强JS] - Job suspend in error handle 
[INFO ] 2024-10-11 00:55:52.953 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] running status set to false 
[INFO ] 2024-10-11 00:55:52.959 - [任务 5][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-11 00:55:52.971 - [任务 5][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-10-11 00:55:52.971 - [任务 5][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-10-11 00:55:52.975 - [任务 5][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-39d7338a-b41e-46de-8184-03b38b9bedf9 
[INFO ] 2024-10-11 00:55:52.975 - [任务 5][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-39d7338a-b41e-46de-8184-03b38b9bedf9 
[INFO ] 2024-10-11 00:55:52.975 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] schema data cleaned 
[INFO ] 2024-10-11 00:55:52.975 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] monitor closed 
[INFO ] 2024-10-11 00:55:52.979 - [任务 5][BMSQL_ITEM] - Node BMSQL_ITEM[39d7338a-b41e-46de-8184-03b38b9bedf9] close complete, cost 174 ms 
[INFO ] 2024-10-11 00:55:52.979 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] running status set to false 
[INFO ] 2024-10-11 00:55:52.992 - [任务 5][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-a26a22db-e5db-4499-8c1d-7dbdc4a018e0 
[INFO ] 2024-10-11 00:55:52.992 - [任务 5][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-a26a22db-e5db-4499-8c1d-7dbdc4a018e0 
[INFO ] 2024-10-11 00:55:52.992 - [任务 5][增强JS] - [ScriptExecutorsManager-670803c382af0a589c4e5ad2-d6529fe4-891b-4589-8881-0ca9bc2e4301-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:55:53.002 - [任务 5][增强JS] - PDK connector node stopped: ScriptExecutor-targetDummy-08ba6aae-a5e1-486f-9cbc-33d81f627c0f 
[INFO ] 2024-10-11 00:55:53.002 - [任务 5][增强JS] - PDK connector node released: ScriptExecutor-targetDummy-08ba6aae-a5e1-486f-9cbc-33d81f627c0f 
[INFO ] 2024-10-11 00:55:53.002 - [任务 5][增强JS] - [ScriptExecutorsManager-670803c382af0a589c4e5ad2-d6529fe4-891b-4589-8881-0ca9bc2e4301-6708035d82af0a589c4e59c9] schema data cleaned 
[INFO ] 2024-10-11 00:55:53.009 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] schema data cleaned 
[INFO ] 2024-10-11 00:55:53.009 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] monitor closed 
[INFO ] 2024-10-11 00:55:53.009 - [任务 5][增强JS] - Node 增强JS[d6529fe4-891b-4589-8881-0ca9bc2e4301] close complete, cost 30 ms 
[INFO ] 2024-10-11 00:55:53.009 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] running status set to false 
[INFO ] 2024-10-11 00:55:53.012 - [任务 5][dummy_test] - Stop connector 
[INFO ] 2024-10-11 00:55:53.012 - [任务 5][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-e3ed973e-23c2-49ab-92ff-b387acd07c75 
[INFO ] 2024-10-11 00:55:53.013 - [任务 5][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-e3ed973e-23c2-49ab-92ff-b387acd07c75 
[INFO ] 2024-10-11 00:55:53.013 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] schema data cleaned 
[INFO ] 2024-10-11 00:55:53.013 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] monitor closed 
[INFO ] 2024-10-11 00:55:53.013 - [任务 5][dummy_test] - Node dummy_test[e3ed973e-23c2-49ab-92ff-b387acd07c75] close complete, cost 3 ms 
[INFO ] 2024-10-11 00:55:57.322 - [任务 5] - Task [任务 5] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-11 00:55:57.336 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-11 00:55:57.339 - [任务 5] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@74558c9 
[INFO ] 2024-10-11 00:55:57.339 - [任务 5] - Stop task milestones: 670803c382af0a589c4e5ad2(任务 5)  
[INFO ] 2024-10-11 00:55:57.462 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:55:57.462 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-10-11 00:55:57.480 - [任务 5] - Remove memory task client succeed, task: 任务 5[670803c382af0a589c4e5ad2] 
[INFO ] 2024-10-11 00:55:57.482 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[670803c382af0a589c4e5ad2] 
