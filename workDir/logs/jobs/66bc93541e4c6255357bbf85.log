[INFO ] 2024-08-14 19:22:43.982 - [任务 4] - Start task milestones: 66bc93541e4c6255357bbf85(任务 4) 
[INFO ] 2024-08-14 19:22:43.983 - [任务 4] - Task initialization... 
[INFO ] 2024-08-14 19:22:44.134 - [任务 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-14 19:22:44.136 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-14 19:22:44.197 - [任务 4][PG] - Node PG[12021672-f358-4881-84e1-da26120d8a13] start preload schema,table counts: 1 
[INFO ] 2024-08-14 19:22:44.197 - [任务 4][Sql] - Node Sql[6bed8bf4-6724-4e2e-86ec-62f43ea4b081] start preload schema,table counts: 1 
[INFO ] 2024-08-14 19:22:44.199 - [任务 4][PG] - Node PG[12021672-f358-4881-84e1-da26120d8a13] preload schema finished, cost 0 ms 
[INFO ] 2024-08-14 19:22:44.200 - [任务 4][Sql] - Node Sql[6bed8bf4-6724-4e2e-86ec-62f43ea4b081] preload schema finished, cost 0 ms 
[INFO ] 2024-08-14 19:22:45.237 - [任务 4][Sql] - Node(Sql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-14 19:22:45.239 - [任务 4][Sql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-14 19:22:45.285 - [任务 4][PG] - Source node "PG" read batch size: 100 
[INFO ] 2024-08-14 19:22:45.286 - [任务 4][PG] - Source node "PG" event queue capacity: 200 
[INFO ] 2024-08-14 19:22:45.286 - [任务 4][PG] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-14 19:22:45.507 - [任务 4][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-08-14 19:22:45.508 - [任务 4][PG] - new logical replication slot created, slotName:tapdata_cdc_6f987e4d_4819_4e8f_aba7_7be8e0e83dfc 
[INFO ] 2024-08-14 19:22:45.516 - [任务 4][PG] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2024-08-14 19:22:45.518 - [任务 4][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-14 19:22:45.627 - [任务 4][PG] - Initial sync started 
[INFO ] 2024-08-14 19:22:45.627 - [任务 4][PG] - Starting batch read, table name: sales 
[INFO ] 2024-08-14 19:22:45.666 - [任务 4][PG] - Table sales is going to be initial synced 
[INFO ] 2024-08-14 19:22:45.666 - [任务 4][PG] - Table [sales] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 19:22:45.681 - [任务 4][PG] - Query table 'sales' counts: 1 
[INFO ] 2024-08-14 19:22:45.681 - [任务 4][PG] - Initial sync completed 
[INFO ] 2024-08-14 19:22:45.681 - [任务 4][PG] - Incremental sync starting... 
[INFO ] 2024-08-14 19:22:45.681 - [任务 4][PG] - Initial sync completed 
[INFO ] 2024-08-14 19:22:45.697 - [任务 4][PG] - Starting stream read, table list: [sales], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[WARN ] 2024-08-14 19:22:45.698 - [任务 4][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-08-14 19:22:45.903 - [任务 4][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_6f987e4d_4819_4e8f_aba7_7be8e0e83dfc 
[INFO ] 2024-08-14 19:22:45.912 - [任务 4][PG] - Connector PostgreSQL incremental start succeed, tables: [sales], data change syncing 
[INFO ] 2024-08-14 19:29:41.168 - [任务 4][PG] - Node PG[12021672-f358-4881-84e1-da26120d8a13] running status set to false 
[INFO ] 2024-08-14 19:29:41.169 - [任务 4][PG] - Incremental sync completed 
[INFO ] 2024-08-14 19:29:41.181 - [任务 4][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode-12021672-f358-4881-84e1-da26120d8a13 
[INFO ] 2024-08-14 19:29:41.182 - [任务 4][PG] - PDK connector node released: HazelcastSourcePdkDataNode-12021672-f358-4881-84e1-da26120d8a13 
[INFO ] 2024-08-14 19:29:41.183 - [任务 4][PG] - Node PG[12021672-f358-4881-84e1-da26120d8a13] schema data cleaned 
[INFO ] 2024-08-14 19:29:41.184 - [任务 4][PG] - Node PG[12021672-f358-4881-84e1-da26120d8a13] monitor closed 
[INFO ] 2024-08-14 19:29:41.187 - [任务 4][PG] - Node PG[12021672-f358-4881-84e1-da26120d8a13] close complete, cost 59 ms 
[INFO ] 2024-08-14 19:29:41.187 - [任务 4][Sql] - Node Sql[6bed8bf4-6724-4e2e-86ec-62f43ea4b081] running status set to false 
[INFO ] 2024-08-14 19:29:41.213 - [任务 4][Sql] - PDK connector node stopped: HazelcastTargetPdkDataNode-6bed8bf4-6724-4e2e-86ec-62f43ea4b081 
[INFO ] 2024-08-14 19:29:41.213 - [任务 4][Sql] - PDK connector node released: HazelcastTargetPdkDataNode-6bed8bf4-6724-4e2e-86ec-62f43ea4b081 
[INFO ] 2024-08-14 19:29:41.213 - [任务 4][Sql] - Node Sql[6bed8bf4-6724-4e2e-86ec-62f43ea4b081] schema data cleaned 
[INFO ] 2024-08-14 19:29:41.213 - [任务 4][Sql] - Node Sql[6bed8bf4-6724-4e2e-86ec-62f43ea4b081] monitor closed 
[INFO ] 2024-08-14 19:29:41.421 - [任务 4][Sql] - Node Sql[6bed8bf4-6724-4e2e-86ec-62f43ea4b081] close complete, cost 27 ms 
[INFO ] 2024-08-14 19:29:43.965 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-14 19:29:43.967 - [任务 4] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@16bfcde4 
[INFO ] 2024-08-14 19:29:43.969 - [任务 4] - Stop task milestones: 66bc93541e4c6255357bbf85(任务 4)  
[INFO ] 2024-08-14 19:29:44.087 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-08-14 19:29:44.088 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-08-14 19:29:44.118 - [任务 4] - Remove memory task client succeed, task: 任务 4[66bc93541e4c6255357bbf85] 
[INFO ] 2024-08-14 19:29:44.120 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[66bc93541e4c6255357bbf85] 
