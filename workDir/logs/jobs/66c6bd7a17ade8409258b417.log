[INFO ] 2024-08-22 12:24:48.317 - [任务 14] - Start task milestones: 66c6bd7a17ade8409258b417(任务 14) 
[INFO ] 2024-08-22 12:24:48.345 - [任务 14] - Task initialization... 
[INFO ] 2024-08-22 12:24:48.345 - [任务 14] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-08-22 12:24:48.548 - [任务 14] - The engine receives 任务 14 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-22 12:24:48.639 - [任务 14][TestDummy] - Node TestDummy[0bc97ef4-a719-4abe-bdc4-b2de72a568df] start preload schema,table counts: 3 
[INFO ] 2024-08-22 12:24:48.639 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[1a71c5c8-82e6-44d3-95f8-921ba896bb59] start preload schema,table counts: 3 
[INFO ] 2024-08-22 12:24:48.639 - [任务 14][TestDummy] - Node TestDummy[0bc97ef4-a719-4abe-bdc4-b2de72a568df] preload schema finished, cost 57 ms 
[INFO ] 2024-08-22 12:24:48.639 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[1a71c5c8-82e6-44d3-95f8-921ba896bb59] preload schema finished, cost 57 ms 
[INFO ] 2024-08-22 12:24:49.287 - [任务 14][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-22 12:24:49.289 - [任务 14][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-22 12:24:49.494 - [任务 14][DB2132WIMTEST] - Source node "DB2132WIMTEST" read batch size: 100 
[INFO ] 2024-08-22 12:24:49.495 - [任务 14][DB2132WIMTEST] - Source node "DB2132WIMTEST" event queue capacity: 200 
[INFO ] 2024-08-22 12:24:49.495 - [任务 14][DB2132WIMTEST] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-22 12:24:49.715 - [任务 14][DB2132WIMTEST] - Table [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG] not open CDC 
[INFO ] 2024-08-22 12:24:49.715 - [任务 14][DB2132WIMTEST] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724300689714} 
[INFO ] 2024-08-22 12:24:49.715 - [任务 14][DB2132WIMTEST] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-22 12:24:49.786 - [任务 14][DB2132WIMTEST] - Initial sync started 
[INFO ] 2024-08-22 12:24:49.787 - [任务 14][DB2132WIMTEST] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-22 12:24:49.794 - [任务 14][DB2132WIMTEST] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-22 12:24:49.851 - [任务 14][DB2132WIMTEST] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 12:24:49.870 - [任务 14][DB2132WIMTEST] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-22 12:24:49.870 - [任务 14][DB2132WIMTEST] - Starting batch read, table name: BMSQL_CUSTOMER, offset: null 
[INFO ] 2024-08-22 12:24:50.071 - [任务 14][DB2132WIMTEST] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-08-22 12:24:50.127 - [任务 14][DB2132WIMTEST] - Query table 'BMSQL_CUSTOMER' counts: 0 
[INFO ] 2024-08-22 12:24:50.134 - [任务 14][DB2132WIMTEST] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 12:24:50.134 - [任务 14][DB2132WIMTEST] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-22 12:24:50.162 - [任务 14][DB2132WIMTEST] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-22 12:24:50.163 - [任务 14][DB2132WIMTEST] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 12:24:50.163 - [任务 14][DB2132WIMTEST] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-22 12:24:50.166 - [任务 14][DB2132WIMTEST] - Initial sync completed 
[INFO ] 2024-08-22 12:24:50.167 - [任务 14][DB2132WIMTEST] - Incremental sync starting... 
[INFO ] 2024-08-22 12:24:50.168 - [任务 14][DB2132WIMTEST] - Initial sync completed 
[INFO ] 2024-08-22 12:24:50.169 - [任务 14][DB2132WIMTEST] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724300689714} 
[INFO ] 2024-08-22 12:28:33.489 - [任务 14] - Stop task milestones: 66c6bd7a17ade8409258b417(任务 14)  
[INFO ] 2024-08-22 12:28:33.945 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[1a71c5c8-82e6-44d3-95f8-921ba896bb59] running status set to false 
[INFO ] 2024-08-22 12:28:33.946 - [任务 14][DB2132WIMTEST] - Log Miner is shutting down... 
[ERROR] 2024-08-22 12:28:34.152 - [任务 14][DB2132WIMTEST] - io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted <-- Full Stack Trace -->
java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:567)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:223)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	... 6 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.grpc.Status.asRuntimeException(Status.java:535)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:649)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:175)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.InterruptedException
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.throwIfInterrupted(ClientCalls.java:750)
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.waitAndDrain(ClientCalls.java:733)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.waitForNext(ClientCalls.java:623)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:644)
	... 21 more

[INFO ] 2024-08-22 12:28:36.002 - [任务 14][DB2132WIMTEST] - PDK connector node stopped: HazelcastSourcePdkDataNode-1a71c5c8-82e6-44d3-95f8-921ba896bb59 
[INFO ] 2024-08-22 12:28:36.002 - [任务 14][DB2132WIMTEST] - PDK connector node released: HazelcastSourcePdkDataNode-1a71c5c8-82e6-44d3-95f8-921ba896bb59 
[INFO ] 2024-08-22 12:28:36.002 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[1a71c5c8-82e6-44d3-95f8-921ba896bb59] schema data cleaned 
[INFO ] 2024-08-22 12:28:36.003 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[1a71c5c8-82e6-44d3-95f8-921ba896bb59] monitor closed 
[INFO ] 2024-08-22 12:28:36.003 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[1a71c5c8-82e6-44d3-95f8-921ba896bb59] close complete, cost 2062 ms 
[INFO ] 2024-08-22 12:28:36.005 - [任务 14][TestDummy] - Node TestDummy[0bc97ef4-a719-4abe-bdc4-b2de72a568df] running status set to false 
[INFO ] 2024-08-22 12:28:36.027 - [任务 14][TestDummy] - Stop connector 
[INFO ] 2024-08-22 12:28:36.028 - [任务 14][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-0bc97ef4-a719-4abe-bdc4-b2de72a568df 
[INFO ] 2024-08-22 12:28:36.029 - [任务 14][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-0bc97ef4-a719-4abe-bdc4-b2de72a568df 
[INFO ] 2024-08-22 12:28:36.029 - [任务 14][TestDummy] - Node TestDummy[0bc97ef4-a719-4abe-bdc4-b2de72a568df] schema data cleaned 
[INFO ] 2024-08-22 12:28:36.030 - [任务 14][TestDummy] - Node TestDummy[0bc97ef4-a719-4abe-bdc4-b2de72a568df] monitor closed 
[INFO ] 2024-08-22 12:28:36.030 - [任务 14][TestDummy] - Node TestDummy[0bc97ef4-a719-4abe-bdc4-b2de72a568df] close complete, cost 26 ms 
[INFO ] 2024-08-22 12:28:40.284 - [任务 14] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-22 12:28:40.284 - [任务 14] - Stopped task aspect(s) 
[INFO ] 2024-08-22 12:28:40.284 - [任务 14] - Snapshot order controller have been removed 
[INFO ] 2024-08-22 12:28:40.312 - [任务 14] - Remove memory task client succeed, task: 任务 14[66c6bd7a17ade8409258b417] 
[INFO ] 2024-08-22 12:28:40.316 - [任务 14] - Destroy memory task client cache succeed, task: 任务 14[66c6bd7a17ade8409258b417] 
[INFO ] 2024-08-22 14:12:27.041 - [任务 14] - Start task milestones: 66c6bd7a17ade8409258b417(任务 14) 
[INFO ] 2024-08-22 14:12:27.041 - [任务 14] - Task initialization... 
[INFO ] 2024-08-22 14:12:27.060 - [任务 14] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-22 14:12:27.160 - [任务 14] - The engine receives 任务 14 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-22 14:12:27.161 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[1a71c5c8-82e6-44d3-95f8-921ba896bb59] start preload schema,table counts: 3 
[INFO ] 2024-08-22 14:12:27.217 - [任务 14][TestDummy] - Node TestDummy[0bc97ef4-a719-4abe-bdc4-b2de72a568df] start preload schema,table counts: 3 
[INFO ] 2024-08-22 14:12:27.218 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[1a71c5c8-82e6-44d3-95f8-921ba896bb59] preload schema finished, cost 56 ms 
[INFO ] 2024-08-22 14:12:27.218 - [任务 14][TestDummy] - Node TestDummy[0bc97ef4-a719-4abe-bdc4-b2de72a568df] preload schema finished, cost 54 ms 
[INFO ] 2024-08-22 14:12:27.808 - [任务 14][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-22 14:12:27.808 - [任务 14][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-22 14:12:27.958 - [任务 14][DB2132WIMTEST] - Source node "DB2132WIMTEST" read batch size: 100 
[INFO ] 2024-08-22 14:12:27.964 - [任务 14][DB2132WIMTEST] - Source node "DB2132WIMTEST" event queue capacity: 200 
[INFO ] 2024-08-22 14:12:27.964 - [任务 14][DB2132WIMTEST] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-22 14:12:28.347 - [任务 14][DB2132WIMTEST] - Table [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG] not open CDC 
[INFO ] 2024-08-22 14:12:28.348 - [任务 14][DB2132WIMTEST] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724307148346} 
[INFO ] 2024-08-22 14:12:28.348 - [任务 14][DB2132WIMTEST] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-22 14:12:28.414 - [任务 14][DB2132WIMTEST] - Initial sync started 
[INFO ] 2024-08-22 14:12:28.414 - [任务 14][DB2132WIMTEST] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-22 14:12:28.461 - [任务 14][DB2132WIMTEST] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-22 14:12:28.462 - [任务 14][DB2132WIMTEST] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 14:12:28.515 - [任务 14][DB2132WIMTEST] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-22 14:12:28.518 - [任务 14][DB2132WIMTEST] - Starting batch read, table name: BMSQL_CUSTOMER, offset: null 
[INFO ] 2024-08-22 14:12:28.518 - [任务 14][DB2132WIMTEST] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-08-22 14:12:28.565 - [任务 14][DB2132WIMTEST] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 14:12:28.565 - [任务 14][DB2132WIMTEST] - Query table 'BMSQL_CUSTOMER' counts: 0 
[INFO ] 2024-08-22 14:12:28.566 - [任务 14][DB2132WIMTEST] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-22 14:12:28.567 - [任务 14][DB2132WIMTEST] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-22 14:12:28.621 - [任务 14][DB2132WIMTEST] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-22 14:12:28.621 - [任务 14][DB2132WIMTEST] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 14:12:28.622 - [任务 14][DB2132WIMTEST] - Initial sync completed 
[INFO ] 2024-08-22 14:12:28.623 - [任务 14][DB2132WIMTEST] - Incremental sync starting... 
[INFO ] 2024-08-22 14:12:28.623 - [任务 14][DB2132WIMTEST] - Initial sync completed 
[INFO ] 2024-08-22 14:12:28.827 - [任务 14][DB2132WIMTEST] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724307148346} 
[INFO ] 2024-08-22 14:45:23.705 - [任务 14] - Stop task milestones: 66c6bd7a17ade8409258b417(任务 14)  
[INFO ] 2024-08-22 14:45:24.104 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[1a71c5c8-82e6-44d3-95f8-921ba896bb59] running status set to false 
[INFO ] 2024-08-22 14:45:24.105 - [任务 14][DB2132WIMTEST] - Log Miner is shutting down... 
[ERROR] 2024-08-22 14:45:24.127 - [任务 14][DB2132WIMTEST] - io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted <-- Full Stack Trace -->
java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:567)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:223)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	... 6 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.grpc.Status.asRuntimeException(Status.java:535)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:649)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:175)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.InterruptedException
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.throwIfInterrupted(ClientCalls.java:750)
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.waitAndDrain(ClientCalls.java:733)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.waitForNext(ClientCalls.java:623)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:644)
	... 21 more

[INFO ] 2024-08-22 14:45:26.013 - [任务 14][DB2132WIMTEST] - PDK connector node stopped: HazelcastSourcePdkDataNode-1a71c5c8-82e6-44d3-95f8-921ba896bb59 
[INFO ] 2024-08-22 14:45:26.014 - [任务 14][DB2132WIMTEST] - PDK connector node released: HazelcastSourcePdkDataNode-1a71c5c8-82e6-44d3-95f8-921ba896bb59 
[INFO ] 2024-08-22 14:45:26.014 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[1a71c5c8-82e6-44d3-95f8-921ba896bb59] schema data cleaned 
[INFO ] 2024-08-22 14:45:26.015 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[1a71c5c8-82e6-44d3-95f8-921ba896bb59] monitor closed 
[INFO ] 2024-08-22 14:45:26.017 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[1a71c5c8-82e6-44d3-95f8-921ba896bb59] close complete, cost 1914 ms 
[INFO ] 2024-08-22 14:45:26.017 - [任务 14][TestDummy] - Node TestDummy[0bc97ef4-a719-4abe-bdc4-b2de72a568df] running status set to false 
[INFO ] 2024-08-22 14:45:26.052 - [任务 14][TestDummy] - Stop connector 
[INFO ] 2024-08-22 14:45:26.052 - [任务 14][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-0bc97ef4-a719-4abe-bdc4-b2de72a568df 
[INFO ] 2024-08-22 14:45:26.052 - [任务 14][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-0bc97ef4-a719-4abe-bdc4-b2de72a568df 
[INFO ] 2024-08-22 14:45:26.053 - [任务 14][TestDummy] - Node TestDummy[0bc97ef4-a719-4abe-bdc4-b2de72a568df] schema data cleaned 
[INFO ] 2024-08-22 14:45:26.068 - [任务 14][TestDummy] - Node TestDummy[0bc97ef4-a719-4abe-bdc4-b2de72a568df] monitor closed 
[INFO ] 2024-08-22 14:45:26.070 - [任务 14][TestDummy] - Node TestDummy[0bc97ef4-a719-4abe-bdc4-b2de72a568df] close complete, cost 36 ms 
[INFO ] 2024-08-22 14:45:27.584 - [任务 14] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-22 14:45:27.584 - [任务 14] - Stopped task aspect(s) 
[INFO ] 2024-08-22 14:45:27.584 - [任务 14] - Snapshot order controller have been removed 
[INFO ] 2024-08-22 14:45:27.602 - [任务 14] - Remove memory task client succeed, task: 任务 14[66c6bd7a17ade8409258b417] 
[INFO ] 2024-08-22 14:45:27.603 - [任务 14] - Destroy memory task client cache succeed, task: 任务 14[66c6bd7a17ade8409258b417] 
