[INFO ] 2024-07-15 12:02:32.327 - [Heartbeat-TestMysql] - Start task milestones: 66949f581df4b966216a4dda(Heartbeat-TestMysql) 
[INFO ] 2024-07-15 12:02:32.419 - [Heartbeat-TestMysql] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-15 12:02:32.419 - [Heartbeat-TestMysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 12:02:32.420 - [Heartbeat-TestMysql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@394a6938 
[INFO ] 2024-07-15 12:02:32.548 - [Heartbeat-TestMysql] - Stop task milestones: 66949f581df4b966216a4dda(Heartbeat-TestMysql)  
[INFO ] 2024-07-15 12:02:32.548 - [Heartbeat-TestMysql] - Stopped task aspect(s) 
[INFO ] 2024-07-15 12:02:32.548 - [Heartbeat-TestMysql] - Snapshot order controller have been removed 
[ERROR] 2024-07-15 12:02:32.565 - [Heartbeat-TestMysql] - Node [id beeb4005-a451-42a9-9332-28a684b89378, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id beeb4005-a451-42a9-9332-28a684b89378, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-15 12:02:50.530 - [Heartbeat-TestMysql] - Start task milestones: 66949f581df4b966216a4dda(Heartbeat-TestMysql) 
[INFO ] 2024-07-15 12:02:50.577 - [Heartbeat-TestMysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 12:02:50.581 - [Heartbeat-TestMysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 12:02:50.672 - [Heartbeat-TestMysql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6ab4fc9a 
[INFO ] 2024-07-15 12:02:50.691 - [Heartbeat-TestMysql] - Stop task milestones: 66949f581df4b966216a4dda(Heartbeat-TestMysql)  
[INFO ] 2024-07-15 12:02:50.695 - [Heartbeat-TestMysql] - Stopped task aspect(s) 
[INFO ] 2024-07-15 12:02:50.695 - [Heartbeat-TestMysql] - Snapshot order controller have been removed 
[ERROR] 2024-07-15 12:02:50.695 - [Heartbeat-TestMysql] - Node [id beeb4005-a451-42a9-9332-28a684b89378, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id beeb4005-a451-42a9-9332-28a684b89378, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-15 12:03:10.848 - [Heartbeat-TestMysql] - Start task milestones: 66949f581df4b966216a4dda(Heartbeat-TestMysql) 
[INFO ] 2024-07-15 12:03:10.946 - [Heartbeat-TestMysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 12:03:10.946 - [Heartbeat-TestMysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 12:03:10.947 - [Heartbeat-TestMysql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6c242f6d 
[INFO ] 2024-07-15 12:03:11.066 - [Heartbeat-TestMysql] - Stop task milestones: 66949f581df4b966216a4dda(Heartbeat-TestMysql)  
[INFO ] 2024-07-15 12:03:11.066 - [Heartbeat-TestMysql] - Stopped task aspect(s) 
[INFO ] 2024-07-15 12:03:11.066 - [Heartbeat-TestMysql] - Snapshot order controller have been removed 
[ERROR] 2024-07-15 12:03:11.268 - [Heartbeat-TestMysql] - Node [id beeb4005-a451-42a9-9332-28a684b89378, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id beeb4005-a451-42a9-9332-28a684b89378, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-15 19:47:23.109 - [Heartbeat-TestMysql] - Start task milestones: 66949f581df4b966216a4dda(Heartbeat-TestMysql) 
[INFO ] 2024-07-15 19:47:23.379 - [Heartbeat-TestMysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 19:47:23.381 - [Heartbeat-TestMysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 19:47:23.496 - [Heartbeat-TestMysql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@59588a2b 
[INFO ] 2024-07-15 19:47:23.496 - [Heartbeat-TestMysql] - Stop task milestones: 66949f581df4b966216a4dda(Heartbeat-TestMysql)  
[INFO ] 2024-07-15 19:47:23.517 - [Heartbeat-TestMysql] - Stopped task aspect(s) 
[INFO ] 2024-07-15 19:47:23.518 - [Heartbeat-TestMysql] - Snapshot order controller have been removed 
[ERROR] 2024-07-15 19:47:23.521 - [Heartbeat-TestMysql] - Node [id beeb4005-a451-42a9-9332-28a684b89378, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id beeb4005-a451-42a9-9332-28a684b89378, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

