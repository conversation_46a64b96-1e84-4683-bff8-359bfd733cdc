[INFO ] 2024-06-05 15:18:20.514 - [任务 1] - Task initialization... 
[INFO ] 2024-06-05 15:18:20.515 - [任务 1] - Start task milestones: 6660112b0ea2021850b5a52f(任务 1) 
[INFO ] 2024-06-05 15:18:20.533 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-05 15:18:20.672 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-05 15:18:20.674 - [任务 1][TestAA] - Node TestAA[143dff48-68ed-40d0-b2f7-6e2d55cb9fc0] start preload schema,table counts: 1 
[INFO ] 2024-06-05 15:18:20.674 - [任务 1][AA_0514] - Node AA_0514[c028f868-bae9-4e68-be68-e54efaa64139] start preload schema,table counts: 1 
[INFO ] 2024-06-05 15:18:20.720 - [任务 1][AA_0514] - Node AA_0514[c028f868-bae9-4e68-be68-e54efaa64139] preload schema finished, cost 51 ms 
[INFO ] 2024-06-05 15:18:20.720 - [任务 1][TestAA] - Node TestAA[143dff48-68ed-40d0-b2f7-6e2d55cb9fc0] preload schema finished, cost 51 ms 
[INFO ] 2024-06-05 15:18:21.778 - [任务 1][AA_0514] - Source node "AA_0514" read batch size: 100 
[INFO ] 2024-06-05 15:18:21.780 - [任务 1][AA_0514] - Source node "AA_0514" event queue capacity: 200 
[INFO ] 2024-06-05 15:18:21.780 - [任务 1][AA_0514] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-05 15:18:21.981 - [任务 1][TestAA] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-06-05 15:18:22.019 - [任务 1][AA_0514] - Found pending transaction, please check if there are any earlier transactions to prevent data loss, the earliest startScn: 53823610 
[INFO ] 2024-06-05 15:18:22.222 - [任务 1][AA_0514] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53823609,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-05 15:18:22.222 - [任务 1][AA_0514] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-05 15:18:24.281 - [任务 1][AA_0514] - Initial sync started 
[INFO ] 2024-06-05 15:18:24.346 - [任务 1][AA_0514] - Starting batch read, table name: AA_0514, offset: null 
[INFO ] 2024-06-05 15:18:24.350 - [任务 1][AA_0514] - Table AA_0514 is going to be initial synced 
[INFO ] 2024-06-05 15:18:41.857 - [任务 1][AA_0514] - Query table 'AA_0514' counts: 20 
[INFO ] 2024-06-05 15:18:41.868 - [任务 1][AA_0514] - Table [AA_0514] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-05 15:18:41.868 - [任务 1][AA_0514] - Initial sync completed 
[INFO ] 2024-06-05 15:18:41.868 - [任务 1][AA_0514] - Incremental sync starting... 
[INFO ] 2024-06-05 15:18:41.868 - [任务 1][AA_0514] - Initial sync completed 
[INFO ] 2024-06-05 15:18:41.868 - [任务 1][AA_0514] - Starting stream read, table list: [AA_0514], offset: {"sortString":null,"offsetValue":null,"lastScn":53823609,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-05 15:18:42.010 - [任务 1][AA_0514] - total start mining scn: 53823609 
[INFO ] 2024-06-05 15:18:43.169 - [任务 1][AA_0514] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-06-05 16:00:00.774 - [任务 1] - Stop task milestones: 6660112b0ea2021850b5a52f(任务 1)  
[INFO ] 2024-06-05 16:00:00.777 - [任务 1][AA_0514] - Node AA_0514[c028f868-bae9-4e68-be68-e54efaa64139] running status set to false 
[INFO ] 2024-06-05 16:00:00.804 - [任务 1][AA_0514] - Log Miner is shutting down... 
[INFO ] 2024-06-05 16:00:00.959 - [任务 1][AA_0514] - Log Miner has been closed! 
[INFO ] 2024-06-05 16:00:00.959 - [任务 1][AA_0514] - PDK connector node stopped: HazelcastSourcePdkDataNode-c028f868-bae9-4e68-be68-e54efaa64139 
[INFO ] 2024-06-05 16:00:00.967 - [任务 1][AA_0514] - PDK connector node released: HazelcastSourcePdkDataNode-c028f868-bae9-4e68-be68-e54efaa64139 
[INFO ] 2024-06-05 16:00:00.968 - [任务 1][AA_0514] - Node AA_0514[c028f868-bae9-4e68-be68-e54efaa64139] schema data cleaned 
[INFO ] 2024-06-05 16:00:00.968 - [任务 1][AA_0514] - Node AA_0514[c028f868-bae9-4e68-be68-e54efaa64139] monitor closed 
[INFO ] 2024-06-05 16:00:00.969 - [任务 1][AA_0514] - Node AA_0514[c028f868-bae9-4e68-be68-e54efaa64139] close complete, cost 210 ms 
[INFO ] 2024-06-05 16:00:00.969 - [任务 1][TestAA] - Node TestAA[143dff48-68ed-40d0-b2f7-6e2d55cb9fc0] running status set to false 
[INFO ] 2024-06-05 16:00:01.015 - [任务 1][TestAA] - PDK connector node stopped: HazelcastTargetPdkDataNode-143dff48-68ed-40d0-b2f7-6e2d55cb9fc0 
[INFO ] 2024-06-05 16:00:01.015 - [任务 1][TestAA] - PDK connector node released: HazelcastTargetPdkDataNode-143dff48-68ed-40d0-b2f7-6e2d55cb9fc0 
[INFO ] 2024-06-05 16:00:01.015 - [任务 1][TestAA] - Node TestAA[143dff48-68ed-40d0-b2f7-6e2d55cb9fc0] schema data cleaned 
[INFO ] 2024-06-05 16:00:01.015 - [任务 1][TestAA] - Node TestAA[143dff48-68ed-40d0-b2f7-6e2d55cb9fc0] monitor closed 
[INFO ] 2024-06-05 16:00:01.222 - [任务 1][TestAA] - Node TestAA[143dff48-68ed-40d0-b2f7-6e2d55cb9fc0] close complete, cost 46 ms 
[INFO ] 2024-06-05 16:00:02.890 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-05 16:00:02.890 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-06-05 16:00:02.890 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-06-05 16:00:02.964 - [任务 1] - Remove memory task client succeed, task: 任务 1[6660112b0ea2021850b5a52f] 
[INFO ] 2024-06-05 16:00:02.964 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6660112b0ea2021850b5a52f] 
