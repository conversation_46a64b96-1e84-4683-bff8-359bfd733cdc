[INFO ] 2024-06-27 15:12:15.678 - [任务 2] - Start task milestones: 667d086313fc7a5f4e97fdb4(任务 2) 
[INFO ] 2024-06-27 15:12:15.884 - [任务 2] - Task initialization... 
[INFO ] 2024-06-27 15:12:16.001 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-27 15:12:16.001 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-27 15:12:16.087 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] start preload schema,table counts: 1 
[INFO ] 2024-06-27 15:12:16.088 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] start preload schema,table counts: 1 
[INFO ] 2024-06-27 15:12:16.089 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 15:12:16.295 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 15:12:16.922 - [任务 2][POCCOLL] - Source node "POCCOLL" read batch size: 100 
[INFO ] 2024-06-27 15:12:16.925 - [任务 2][POCCOLL] - Source node "POCCOLL" event queue capacity: 200 
[INFO ] 2024-06-27 15:12:16.926 - [任务 2][POCCOLL] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-27 15:12:17.083 - [任务 2][POCCOLL] - batch offset found: {},stream offset found: {"cdcOffset":1719472336,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-27 15:12:17.083 - [任务 2][TEST2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-27 15:12:17.102 - [任务 2][POCCOLL] - Initial sync started 
[INFO ] 2024-06-27 15:12:17.110 - [任务 2][POCCOLL] - Starting batch read, table name: POCCOLL, offset: null 
[INFO ] 2024-06-27 15:12:17.111 - [任务 2][POCCOLL] - Table POCCOLL is going to be initial synced 
[INFO ] 2024-06-27 15:12:17.162 - [任务 2][POCCOLL] - Query table 'POCCOLL' counts: 2555392 
[INFO ] 2024-06-27 15:13:13.137 - [任务 2] - Stop task milestones: 667d086313fc7a5f4e97fdb4(任务 2)  
[INFO ] 2024-06-27 15:13:13.148 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] running status set to false 
[INFO ] 2024-06-27 15:13:13.151 - [任务 2][POCCOLL] - Table [POCCOLL] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-27 15:13:13.153 - [任务 2][POCCOLL] - Initial sync completed 
[INFO ] 2024-06-27 15:13:13.155 - [任务 2][POCCOLL] - Incremental sync starting... 
[INFO ] 2024-06-27 15:13:13.173 - [任务 2][POCCOLL] - Incremental sync completed 
[INFO ] 2024-06-27 15:13:13.174 - [任务 2][POCCOLL] - PDK connector node stopped: HazelcastSourcePdkDataNode-9ece4091-8896-4f74-8375-42f199171cf7 
[INFO ] 2024-06-27 15:13:13.177 - [任务 2][POCCOLL] - PDK connector node released: HazelcastSourcePdkDataNode-9ece4091-8896-4f74-8375-42f199171cf7 
[INFO ] 2024-06-27 15:13:13.177 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] schema data cleaned 
[INFO ] 2024-06-27 15:13:13.182 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] monitor closed 
[INFO ] 2024-06-27 15:13:13.184 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] close complete, cost 56 ms 
[INFO ] 2024-06-27 15:13:13.185 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] running status set to false 
[INFO ] 2024-06-27 15:13:13.201 - [任务 2][TEST2] - PDK connector node stopped: HazelcastTargetPdkDataNode-2f6ca1df-678c-4cc5-84f6-67ca7cb3a597 
[INFO ] 2024-06-27 15:13:13.203 - [任务 2][TEST2] - PDK connector node released: HazelcastTargetPdkDataNode-2f6ca1df-678c-4cc5-84f6-67ca7cb3a597 
[INFO ] 2024-06-27 15:13:13.203 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] schema data cleaned 
[INFO ] 2024-06-27 15:13:13.205 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] monitor closed 
[INFO ] 2024-06-27 15:13:13.205 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] close complete, cost 22 ms 
[INFO ] 2024-06-27 15:13:13.979 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-27 15:13:13.979 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-06-27 15:13:13.979 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-06-27 15:13:14.024 - [任务 2] - Remove memory task client succeed, task: 任务 2[667d086313fc7a5f4e97fdb4] 
[INFO ] 2024-06-27 15:13:14.027 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[667d086313fc7a5f4e97fdb4] 
[INFO ] 2024-06-27 15:13:33.895 - [任务 2] - Start task milestones: 667d086313fc7a5f4e97fdb4(任务 2) 
[INFO ] 2024-06-27 15:13:33.896 - [任务 2] - Task initialization... 
[INFO ] 2024-06-27 15:13:34.097 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-27 15:13:34.243 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-27 15:13:34.243 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] start preload schema,table counts: 1 
[INFO ] 2024-06-27 15:13:34.244 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] preload schema finished, cost 1 ms 
[INFO ] 2024-06-27 15:13:34.296 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] start preload schema,table counts: 1 
[INFO ] 2024-06-27 15:13:34.297 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 15:13:34.987 - [任务 2][POCCOLL] - Source node "POCCOLL" read batch size: 100 
[INFO ] 2024-06-27 15:13:34.987 - [任务 2][POCCOLL] - Source node "POCCOLL" event queue capacity: 200 
[INFO ] 2024-06-27 15:13:34.988 - [任务 2][POCCOLL] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-27 15:13:35.126 - [任务 2][POCCOLL] - batch offset found: {},stream offset found: {"cdcOffset":1719472415,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-27 15:13:35.126 - [任务 2][TEST2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-27 15:13:35.196 - [任务 2][POCCOLL] - Initial sync started 
[INFO ] 2024-06-27 15:13:35.203 - [任务 2][POCCOLL] - Starting batch read, table name: POCCOLL, offset: null 
[INFO ] 2024-06-27 15:13:35.204 - [任务 2][POCCOLL] - Table POCCOLL is going to be initial synced 
[INFO ] 2024-06-27 15:13:35.405 - [任务 2][POCCOLL] - Query table 'POCCOLL' counts: 2555392 
[INFO ] 2024-06-27 15:15:12.278 - [任务 2][POCCOLL] - Table [POCCOLL] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-27 15:15:12.289 - [任务 2][POCCOLL] - Initial sync completed 
[INFO ] 2024-06-27 15:15:12.298 - [任务 2][POCCOLL] - Incremental sync starting... 
[INFO ] 2024-06-27 15:15:12.303 - [任务 2][POCCOLL] - Initial sync completed 
[INFO ] 2024-06-27 15:15:12.306 - [任务 2][POCCOLL] - Starting stream read, table list: [POCCOLL], offset: {"cdcOffset":1719472415,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-27 15:15:12.510 - [任务 2][POCCOLL] - Connector MongoDB incremental start succeed, tables: [POCCOLL], data change syncing 
[WARN ] 2024-06-27 15:31:23.519 - [任务 2][POCCOLL] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoQueryException: Command failed with error 136 (CappedPositionLost): 'CollectionScan died due to position in capped collection being deleted. Last seen record id: RecordId(7385082152485928984)' on server localhost:27017. The full response is {"operationTime": {"$timestamp": {"t": 1719473483, "i": 4096}}, "ok": 0.0, "errmsg": "CollectionScan died due to position in capped collection being deleted. Last seen record id: RecordId(7385082152485928984)", "code": 136, "codeName": "CappedPositionLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1719473483, "i": 4096}}, "signature": {"hash": {"$binary": {"base64": "RDJXXFXk8yIa303gfDEQXbq630I=", "subType": "00"}}, "keyId": 7329804974198620162}}}
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-27 15:32:23.604 - [任务 2][POCCOLL] - Incremental sync completed 
[INFO ] 2024-06-27 15:32:23.608 - [任务 2][POCCOLL] - Exception skipping - The current exception does not match the skip exception strategy, message: Increment start point exceeds the log time window of mongodb, start point: {"_data": "82667D151700003F4B2B022C0100296E5A10044BFB4BE31B0E497C8F8E2CF9E80F8D9146465F696400461E77002B061E69002D010EF6000004"} 
[ERROR] 2024-06-27 15:32:23.657 - [任务 2][POCCOLL] - com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1719473543, "i": 242}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1719473543, "i": 1269}}, "signature": {"hash": {"$binary": {"base64": "Ae6HD8DxXTTxOu/2gbGcVULRErc=", "subType": "00"}}, "keyId": 7329804974198620162}}} <-- Error Message -->
com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1719473543, "i": 242}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1719473543, "i": 1269}}, "signature": {"hash": {"$binary": {"base64": "Ae6HD8DxXTTxOu/2gbGcVULRErc=", "subType": "00"}}, "keyId": 7329804974198620162}}}

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1719473543, "i": 242}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1719473543, "i": 1269}}, "signature": {"hash": {"$binary": {"base64": "Ae6HD8DxXTTxOu/2gbGcVULRErc=", "subType": "00"}}, "keyId": 7329804974198620162}}}
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:114)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:643)
	...

<-- Full Stack Trace -->
com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1719473543, "i": 242}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1719473543, "i": 1269}}, "signature": {"hash": {"$binary": {"base64": "Ae6HD8DxXTTxOu/2gbGcVULRErc=", "subType": "00"}}, "keyId": 7329804974198620162}}}
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:265)
	at io.tapdata.mongodb.MongodbConnector.doStreamRead(MongodbConnector.java:1545)
	at io.tapdata.mongodb.MongodbConnector.streamRead(MongodbConnector.java:1539)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1719473543, "i": 242}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1719473543, "i": 1269}}, "signature": {"hash": {"$binary": {"base64": "Ae6HD8DxXTTxOu/2gbGcVULRErc=", "subType": "00"}}, "keyId": 7329804974198620162}}}
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:114)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:643)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:73)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:204)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:122)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:87)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:76)
	at com.mongodb.internal.connection.DefaultServer$OperationCountTrackingConnection.command(DefaultServer.java:288)
	at com.mongodb.internal.operation.CommandOperationHelper.createReadCommandAndExecute(CommandOperationHelper.java:239)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$4(CommandOperationHelper.java:220)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$0(OperationHelper.java:358)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$1(OperationHelper.java:357)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.withSourceAndConnection(OperationHelper.java:356)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$5(CommandOperationHelper.java:218)
	at com.mongodb.internal.async.function.RetryingSyncSupplier.get(RetryingSyncSupplier.java:67)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:223)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:204)
	at com.mongodb.internal.operation.AggregateOperationImpl.execute(AggregateOperationImpl.java:191)
	at com.mongodb.internal.operation.ChangeStreamOperation.lambda$execute$0(ChangeStreamOperation.java:187)
	at com.mongodb.internal.operation.OperationHelper.withReadConnectionSource(OperationHelper.java:321)
	at com.mongodb.internal.operation.ChangeStreamOperation.execute(ChangeStreamOperation.java:185)
	at com.mongodb.internal.operation.ChangeStreamOperation.execute(ChangeStreamOperation.java:55)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:185)
	at com.mongodb.client.internal.ChangeStreamIterableImpl.execute(ChangeStreamIterableImpl.java:212)
	at com.mongodb.client.internal.ChangeStreamIterableImpl.cursor(ChangeStreamIterableImpl.java:187)
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:111)
	... 22 more

[INFO ] 2024-06-27 15:32:23.661 - [任务 2][POCCOLL] - Job suspend in error handle 
[INFO ] 2024-06-27 15:32:24.025 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] running status set to false 
[INFO ] 2024-06-27 15:32:24.026 - [任务 2][POCCOLL] - PDK connector node stopped: HazelcastSourcePdkDataNode-9ece4091-8896-4f74-8375-42f199171cf7 
[INFO ] 2024-06-27 15:32:24.038 - [任务 2][POCCOLL] - PDK connector node released: HazelcastSourcePdkDataNode-9ece4091-8896-4f74-8375-42f199171cf7 
[INFO ] 2024-06-27 15:32:24.040 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] schema data cleaned 
[INFO ] 2024-06-27 15:32:24.041 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] monitor closed 
[INFO ] 2024-06-27 15:32:24.047 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] close complete, cost 36 ms 
[INFO ] 2024-06-27 15:32:24.047 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] running status set to false 
[INFO ] 2024-06-27 15:32:24.070 - [任务 2][TEST2] - PDK connector node stopped: HazelcastTargetPdkDataNode-2f6ca1df-678c-4cc5-84f6-67ca7cb3a597 
[INFO ] 2024-06-27 15:32:24.073 - [任务 2][TEST2] - PDK connector node released: HazelcastTargetPdkDataNode-2f6ca1df-678c-4cc5-84f6-67ca7cb3a597 
[INFO ] 2024-06-27 15:32:24.076 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] schema data cleaned 
[INFO ] 2024-06-27 15:32:24.077 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] monitor closed 
[INFO ] 2024-06-27 15:32:24.077 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] close complete, cost 28 ms 
[INFO ] 2024-06-27 15:32:24.976 - [任务 2] - Task [任务 2] cannot retry, reason: Task retry service not start 
[INFO ] 2024-06-27 15:32:24.977 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-27 15:32:24.996 - [任务 2] - Stop task milestones: 667d086313fc7a5f4e97fdb4(任务 2)  
[INFO ] 2024-06-27 15:32:24.997 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-06-27 15:32:25.029 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-06-27 15:32:25.030 - [任务 2] - Remove memory task client succeed, task: 任务 2[667d086313fc7a5f4e97fdb4] 
[INFO ] 2024-06-27 15:32:25.030 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[667d086313fc7a5f4e97fdb4] 
[INFO ] 2024-06-27 16:18:30.785 - [任务 2] - Start task milestones: 667d086313fc7a5f4e97fdb4(任务 2) 
[INFO ] 2024-06-27 16:18:30.786 - [任务 2] - Task initialization... 
[INFO ] 2024-06-27 16:18:30.991 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-27 16:18:31.084 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-27 16:18:31.085 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] start preload schema,table counts: 1 
[INFO ] 2024-06-27 16:18:31.086 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 16:18:31.086 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] start preload schema,table counts: 1 
[INFO ] 2024-06-27 16:18:31.287 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 16:18:31.897 - [任务 2][TEST2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-27 16:18:32.077 - [任务 2][POCCOLL] - Source node "POCCOLL" read batch size: 100 
[INFO ] 2024-06-27 16:18:32.078 - [任务 2][POCCOLL] - Source node "POCCOLL" event queue capacity: 200 
[INFO ] 2024-06-27 16:18:32.078 - [任务 2][POCCOLL] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-27 16:18:32.149 - [任务 2][POCCOLL] - batch offset found: {},stream offset found: {"cdcOffset":1719473400,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-27 16:18:32.197 - [任务 2][POCCOLL] - Incremental sync starting... 
[INFO ] 2024-06-27 16:18:32.198 - [任务 2][POCCOLL] - Initial sync completed 
[INFO ] 2024-06-27 16:18:32.201 - [任务 2][POCCOLL] - Starting stream read, table list: [POCCOLL], offset: {"cdcOffset":1719473400,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-27 16:18:32.339 - [任务 2][POCCOLL] - Connector MongoDB incremental start succeed, tables: [POCCOLL], data change syncing 
[INFO ] 2024-06-27 16:18:32.339 - [任务 2][POCCOLL] - Incremental sync completed 
[INFO ] 2024-06-27 16:18:32.348 - [任务 2][POCCOLL] - Exception skipping - The current exception does not match the skip exception strategy, message: Increment start point exceeds the log time window of mongodb, start point: 1719473400 
[ERROR] 2024-06-27 16:18:32.353 - [任务 2][POCCOLL] - com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1719476312, "i": 10}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1719476312, "i": 10}}, "signature": {"hash": {"$binary": {"base64": "yCX/XSep7umYxgBRi0WBatV2JJ8=", "subType": "00"}}, "keyId": 7329804974198620162}}} <-- Error Message -->
com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1719476312, "i": 10}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1719476312, "i": 10}}, "signature": {"hash": {"$binary": {"base64": "yCX/XSep7umYxgBRi0WBatV2JJ8=", "subType": "00"}}, "keyId": 7329804974198620162}}}

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1719476312, "i": 10}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1719476312, "i": 10}}, "signature": {"hash": {"$binary": {"base64": "yCX/XSep7umYxgBRi0WBatV2JJ8=", "subType": "00"}}, "keyId": 7329804974198620162}}}
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:114)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:643)
	...

<-- Full Stack Trace -->
com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1719476312, "i": 10}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1719476312, "i": 10}}, "signature": {"hash": {"$binary": {"base64": "yCX/XSep7umYxgBRi0WBatV2JJ8=", "subType": "00"}}, "keyId": 7329804974198620162}}}
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:265)
	at io.tapdata.mongodb.MongodbConnector.doStreamRead(MongodbConnector.java:1545)
	at io.tapdata.mongodb.MongodbConnector.streamRead(MongodbConnector.java:1539)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1719476312, "i": 10}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1719476312, "i": 10}}, "signature": {"hash": {"$binary": {"base64": "yCX/XSep7umYxgBRi0WBatV2JJ8=", "subType": "00"}}, "keyId": 7329804974198620162}}}
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:114)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:643)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:73)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:204)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:122)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:87)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:76)
	at com.mongodb.internal.connection.DefaultServer$OperationCountTrackingConnection.command(DefaultServer.java:288)
	at com.mongodb.internal.operation.CommandOperationHelper.createReadCommandAndExecute(CommandOperationHelper.java:239)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$4(CommandOperationHelper.java:220)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$0(OperationHelper.java:358)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$1(OperationHelper.java:357)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.withSourceAndConnection(OperationHelper.java:356)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$5(CommandOperationHelper.java:218)
	at com.mongodb.internal.async.function.RetryingSyncSupplier.get(RetryingSyncSupplier.java:67)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:223)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:204)
	at com.mongodb.internal.operation.AggregateOperationImpl.execute(AggregateOperationImpl.java:191)
	at com.mongodb.internal.operation.ChangeStreamOperation.lambda$execute$0(ChangeStreamOperation.java:187)
	at com.mongodb.internal.operation.OperationHelper.withReadConnectionSource(OperationHelper.java:321)
	at com.mongodb.internal.operation.ChangeStreamOperation.execute(ChangeStreamOperation.java:185)
	at com.mongodb.internal.operation.ChangeStreamOperation.execute(ChangeStreamOperation.java:55)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:185)
	at com.mongodb.client.internal.ChangeStreamIterableImpl.execute(ChangeStreamIterableImpl.java:212)
	at com.mongodb.client.internal.ChangeStreamIterableImpl.cursor(ChangeStreamIterableImpl.java:187)
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:111)
	... 22 more

[INFO ] 2024-06-27 16:18:32.559 - [任务 2][POCCOLL] - Job suspend in error handle 
[INFO ] 2024-06-27 16:18:32.713 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] running status set to false 
[INFO ] 2024-06-27 16:18:32.723 - [任务 2][POCCOLL] - PDK connector node stopped: HazelcastSourcePdkDataNode-9ece4091-8896-4f74-8375-42f199171cf7 
[INFO ] 2024-06-27 16:18:32.723 - [任务 2][POCCOLL] - PDK connector node released: HazelcastSourcePdkDataNode-9ece4091-8896-4f74-8375-42f199171cf7 
[INFO ] 2024-06-27 16:18:32.725 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] schema data cleaned 
[INFO ] 2024-06-27 16:18:32.725 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] monitor closed 
[INFO ] 2024-06-27 16:18:32.729 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] close complete, cost 16 ms 
[INFO ] 2024-06-27 16:18:32.729 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] running status set to false 
[INFO ] 2024-06-27 16:18:32.741 - [任务 2][TEST2] - PDK connector node stopped: HazelcastTargetPdkDataNode-2f6ca1df-678c-4cc5-84f6-67ca7cb3a597 
[INFO ] 2024-06-27 16:18:32.742 - [任务 2][TEST2] - PDK connector node released: HazelcastTargetPdkDataNode-2f6ca1df-678c-4cc5-84f6-67ca7cb3a597 
[INFO ] 2024-06-27 16:18:32.742 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] schema data cleaned 
[INFO ] 2024-06-27 16:18:32.743 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] monitor closed 
[INFO ] 2024-06-27 16:18:32.746 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] close complete, cost 13 ms 
[INFO ] 2024-06-27 16:18:37.368 - [任务 2] - Task [任务 2] cannot retry, reason: Task retry service not start 
[INFO ] 2024-06-27 16:18:37.402 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-27 16:18:37.403 - [任务 2] - Stop task milestones: 667d086313fc7a5f4e97fdb4(任务 2)  
[INFO ] 2024-06-27 16:18:37.425 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-06-27 16:18:37.425 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-06-27 16:18:37.452 - [任务 2] - Remove memory task client succeed, task: 任务 2[667d086313fc7a5f4e97fdb4] 
[INFO ] 2024-06-27 16:18:37.454 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[667d086313fc7a5f4e97fdb4] 
[INFO ] 2024-06-27 16:19:09.497 - [任务 2] - Start task milestones: 667d086313fc7a5f4e97fdb4(任务 2) 
[INFO ] 2024-06-27 16:19:09.498 - [任务 2] - Task initialization... 
[INFO ] 2024-06-27 16:19:09.672 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-27 16:19:09.672 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-27 16:19:09.721 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] start preload schema,table counts: 1 
[INFO ] 2024-06-27 16:19:09.721 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 16:19:09.728 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] start preload schema,table counts: 1 
[INFO ] 2024-06-27 16:19:09.728 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 16:19:10.502 - [任务 2][POCCOLL] - Source node "POCCOLL" read batch size: 100 
[INFO ] 2024-06-27 16:19:10.502 - [任务 2][POCCOLL] - Source node "POCCOLL" event queue capacity: 200 
[INFO ] 2024-06-27 16:19:10.502 - [任务 2][POCCOLL] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-27 16:19:10.626 - [任务 2][TEST2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-27 16:19:10.721 - [任务 2][POCCOLL] - batch offset found: {},stream offset found: {"cdcOffset":1719476350,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-27 16:19:10.741 - [任务 2][POCCOLL] - Incremental sync starting... 
[INFO ] 2024-06-27 16:19:10.742 - [任务 2][POCCOLL] - Initial sync completed 
[INFO ] 2024-06-27 16:19:10.742 - [任务 2][POCCOLL] - Starting stream read, table list: [POCCOLL], offset: {"cdcOffset":1719476350,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-27 16:19:10.946 - [任务 2][POCCOLL] - Connector MongoDB incremental start succeed, tables: [POCCOLL], data change syncing 
[INFO ] 2024-06-27 16:20:33.058 - [任务 2] - Stop task milestones: 667d086313fc7a5f4e97fdb4(任务 2)  
[INFO ] 2024-06-27 16:20:33.227 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] running status set to false 
[INFO ] 2024-06-27 16:20:33.251 - [任务 2][POCCOLL] - PDK connector node stopped: HazelcastSourcePdkDataNode-9ece4091-8896-4f74-8375-42f199171cf7 
[INFO ] 2024-06-27 16:20:33.252 - [任务 2][POCCOLL] - PDK connector node released: HazelcastSourcePdkDataNode-9ece4091-8896-4f74-8375-42f199171cf7 
[INFO ] 2024-06-27 16:20:33.252 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] schema data cleaned 
[INFO ] 2024-06-27 16:20:33.253 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] monitor closed 
[INFO ] 2024-06-27 16:20:33.257 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] close complete, cost 27 ms 
[INFO ] 2024-06-27 16:20:33.257 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] running status set to false 
[INFO ] 2024-06-27 16:20:33.275 - [任务 2][TEST2] - PDK connector node stopped: HazelcastTargetPdkDataNode-2f6ca1df-678c-4cc5-84f6-67ca7cb3a597 
[INFO ] 2024-06-27 16:20:33.275 - [任务 2][TEST2] - PDK connector node released: HazelcastTargetPdkDataNode-2f6ca1df-678c-4cc5-84f6-67ca7cb3a597 
[INFO ] 2024-06-27 16:20:33.276 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] schema data cleaned 
[INFO ] 2024-06-27 16:20:33.276 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] monitor closed 
[INFO ] 2024-06-27 16:20:33.478 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] close complete, cost 23 ms 
[INFO ] 2024-06-27 16:20:37.574 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-27 16:20:37.576 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-06-27 16:20:37.577 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-06-27 16:20:37.689 - [任务 2] - Remove memory task client succeed, task: 任务 2[667d086313fc7a5f4e97fdb4] 
[INFO ] 2024-06-27 16:20:37.689 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[667d086313fc7a5f4e97fdb4] 
[INFO ] 2024-06-27 16:21:18.333 - [任务 2] - Start task milestones: 667d086313fc7a5f4e97fdb4(任务 2) 
[INFO ] 2024-06-27 16:21:18.333 - [任务 2] - Task initialization... 
[INFO ] 2024-06-27 16:21:18.563 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-27 16:21:18.667 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-27 16:21:18.668 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] start preload schema,table counts: 1 
[INFO ] 2024-06-27 16:21:18.668 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] start preload schema,table counts: 1 
[INFO ] 2024-06-27 16:21:18.668 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 16:21:18.668 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 16:21:19.479 - [任务 2][TEST2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-27 16:21:19.648 - [任务 2][POCCOLL] - Source node "POCCOLL" read batch size: 100 
[INFO ] 2024-06-27 16:21:19.648 - [任务 2][POCCOLL] - Source node "POCCOLL" event queue capacity: 200 
[INFO ] 2024-06-27 16:21:19.649 - [任务 2][POCCOLL] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-27 16:21:19.700 - [任务 2][POCCOLL] - batch offset found: {},stream offset found: {"cdcOffset":1719476340,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-27 16:21:19.755 - [任务 2][POCCOLL] - Incremental sync starting... 
[INFO ] 2024-06-27 16:21:19.755 - [任务 2][POCCOLL] - Initial sync completed 
[INFO ] 2024-06-27 16:21:19.758 - [任务 2][POCCOLL] - Starting stream read, table list: [POCCOLL], offset: {"cdcOffset":1719476340,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-27 16:21:19.968 - [任务 2][POCCOLL] - Connector MongoDB incremental start succeed, tables: [POCCOLL], data change syncing 
[INFO ] 2024-06-27 16:24:03.325 - [任务 2] - Stop task milestones: 667d086313fc7a5f4e97fdb4(任务 2)  
[INFO ] 2024-06-27 16:24:03.657 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] running status set to false 
[INFO ] 2024-06-27 16:24:03.658 - [任务 2][POCCOLL] - PDK connector node stopped: HazelcastSourcePdkDataNode-9ece4091-8896-4f74-8375-42f199171cf7 
[INFO ] 2024-06-27 16:24:03.658 - [任务 2][POCCOLL] - PDK connector node released: HazelcastSourcePdkDataNode-9ece4091-8896-4f74-8375-42f199171cf7 
[INFO ] 2024-06-27 16:24:03.659 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] schema data cleaned 
[INFO ] 2024-06-27 16:24:03.660 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] monitor closed 
[INFO ] 2024-06-27 16:24:03.661 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] close complete, cost 32 ms 
[INFO ] 2024-06-27 16:24:03.687 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] running status set to false 
[INFO ] 2024-06-27 16:24:03.688 - [任务 2][TEST2] - PDK connector node stopped: HazelcastTargetPdkDataNode-2f6ca1df-678c-4cc5-84f6-67ca7cb3a597 
[INFO ] 2024-06-27 16:24:03.689 - [任务 2][TEST2] - PDK connector node released: HazelcastTargetPdkDataNode-2f6ca1df-678c-4cc5-84f6-67ca7cb3a597 
[INFO ] 2024-06-27 16:24:03.689 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] schema data cleaned 
[INFO ] 2024-06-27 16:24:03.690 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] monitor closed 
[INFO ] 2024-06-27 16:24:03.690 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] close complete, cost 28 ms 
[INFO ] 2024-06-27 16:24:07.881 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-27 16:24:07.881 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-06-27 16:24:07.926 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-06-27 16:24:07.926 - [任务 2] - Remove memory task client succeed, task: 任务 2[667d086313fc7a5f4e97fdb4] 
[INFO ] 2024-06-27 16:24:08.132 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[667d086313fc7a5f4e97fdb4] 
[INFO ] 2024-06-27 16:24:26.086 - [任务 2] - Start task milestones: 667d086313fc7a5f4e97fdb4(任务 2) 
[INFO ] 2024-06-27 16:24:26.086 - [任务 2] - Task initialization... 
[INFO ] 2024-06-27 16:24:26.250 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-27 16:24:26.250 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-27 16:24:26.292 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] start preload schema,table counts: 1 
[INFO ] 2024-06-27 16:24:26.293 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 16:24:26.305 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] start preload schema,table counts: 1 
[INFO ] 2024-06-27 16:24:26.308 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 16:24:27.114 - [任务 2][TEST2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-27 16:24:27.296 - [任务 2][POCCOLL] - Source node "POCCOLL" read batch size: 100 
[INFO ] 2024-06-27 16:24:27.296 - [任务 2][POCCOLL] - Source node "POCCOLL" event queue capacity: 200 
[INFO ] 2024-06-27 16:24:27.296 - [任务 2][POCCOLL] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-27 16:24:27.363 - [任务 2][POCCOLL] - batch offset found: {},stream offset found: {"cdcOffset":1719476340,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-27 16:24:27.434 - [任务 2][POCCOLL] - Incremental sync starting... 
[INFO ] 2024-06-27 16:24:27.435 - [任务 2][POCCOLL] - Initial sync completed 
[INFO ] 2024-06-27 16:24:27.436 - [任务 2][POCCOLL] - Starting stream read, table list: [POCCOLL], offset: {"cdcOffset":1719476340,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-27 16:24:27.637 - [任务 2][POCCOLL] - Connector MongoDB incremental start succeed, tables: [POCCOLL], data change syncing 
[INFO ] 2024-06-27 17:07:26.186 - [任务 2] - Stop task milestones: 667d086313fc7a5f4e97fdb4(任务 2)  
[INFO ] 2024-06-27 17:07:26.357 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] running status set to false 
[INFO ] 2024-06-27 17:07:26.369 - [任务 2][POCCOLL] - PDK connector node stopped: HazelcastSourcePdkDataNode-9ece4091-8896-4f74-8375-42f199171cf7 
[INFO ] 2024-06-27 17:07:26.369 - [任务 2][POCCOLL] - PDK connector node released: HazelcastSourcePdkDataNode-9ece4091-8896-4f74-8375-42f199171cf7 
[INFO ] 2024-06-27 17:07:26.369 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] schema data cleaned 
[INFO ] 2024-06-27 17:07:26.370 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] monitor closed 
[INFO ] 2024-06-27 17:07:26.371 - [任务 2][POCCOLL] - Node POCCOLL[9ece4091-8896-4f74-8375-42f199171cf7] close complete, cost 16 ms 
[INFO ] 2024-06-27 17:07:26.371 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] running status set to false 
[INFO ] 2024-06-27 17:07:26.387 - [任务 2][TEST2] - PDK connector node stopped: HazelcastTargetPdkDataNode-2f6ca1df-678c-4cc5-84f6-67ca7cb3a597 
[INFO ] 2024-06-27 17:07:26.387 - [任务 2][TEST2] - PDK connector node released: HazelcastTargetPdkDataNode-2f6ca1df-678c-4cc5-84f6-67ca7cb3a597 
[INFO ] 2024-06-27 17:07:26.387 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] schema data cleaned 
[INFO ] 2024-06-27 17:07:26.388 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] monitor closed 
[INFO ] 2024-06-27 17:07:26.593 - [任务 2][TEST2] - Node TEST2[2f6ca1df-678c-4cc5-84f6-67ca7cb3a597] close complete, cost 17 ms 
[INFO ] 2024-06-27 17:07:30.478 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-27 17:07:30.478 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-06-27 17:07:30.479 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-06-27 17:07:30.515 - [任务 2] - Remove memory task client succeed, task: 任务 2[667d086313fc7a5f4e97fdb4] 
[INFO ] 2024-06-27 17:07:30.516 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[667d086313fc7a5f4e97fdb4] 
