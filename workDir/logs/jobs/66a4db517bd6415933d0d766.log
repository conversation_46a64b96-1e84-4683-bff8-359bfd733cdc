[INFO ] 2024-07-28 11:36:48.865 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] running status set to false 
[INFO ] 2024-07-28 11:36:48.913 - [任务 1][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-98331971-5193-49fb-b9a6-5e3500db997c 
[INFO ] 2024-07-28 11:36:48.914 - [任务 1][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-98331971-5193-49fb-b9a6-5e3500db997c 
[INFO ] 2024-07-28 11:36:48.916 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] schema data cleaned 
[INFO ] 2024-07-28 11:36:48.916 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] monitor closed 
[INFO ] 2024-07-28 11:36:48.931 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] close complete, cost 56 ms 
[INFO ] 2024-07-28 11:36:48.931 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] running status set to false 
[INFO ] 2024-07-28 11:36:49.090 - [任务 1][PoTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7 
[INFO ] 2024-07-28 11:36:49.090 - [任务 1][PoTest] - PDK connector node released: HazelcastTargetPdkDataNode-fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7 
[INFO ] 2024-07-28 11:36:49.090 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] schema data cleaned 
[INFO ] 2024-07-28 11:36:49.090 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] monitor closed 
[INFO ] 2024-07-28 11:36:49.090 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] close complete, cost 144 ms 
[INFO ] 2024-07-28 11:36:50.891 - [任务 1][POLICY] - Incremental sync completed 
[INFO ] 2024-07-28 11:36:52.827 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 11:36:52.827 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4a11b269 
[INFO ] 2024-07-28 11:36:52.952 - [任务 1] - Stop task milestones: 66a4db517bd6415933d0d766(任务 1)  
[INFO ] 2024-07-28 11:36:52.952 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-28 11:36:52.953 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-28 11:36:52.988 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a4db517bd6415933d0d766] 
[INFO ] 2024-07-28 11:36:52.989 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a4db517bd6415933d0d766] 
[INFO ] 2024-07-28 11:38:11.928 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 11:38:12.036 - [任务 1] - Start task milestones: 66a4db517bd6415933d0d766(任务 1) 
[INFO ] 2024-07-28 11:38:12.158 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 11:38:12.307 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 11:38:12.459 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] start preload schema,table counts: 1 
[INFO ] 2024-07-28 11:38:12.459 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] start preload schema,table counts: 1 
[INFO ] 2024-07-28 11:38:12.459 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] preload schema finished, cost 1 ms 
[INFO ] 2024-07-28 11:38:12.668 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 11:38:12.865 - [任务 1][PoTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 11:38:12.865 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-28 11:38:12.865 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-28 11:38:12.872 - [任务 1][POLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-28 11:38:13.001 - [任务 1][POLICY] - batch offset found: {"POLICY":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1722080130,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 11:38:13.003 - [任务 1][POLICY] - Initial sync started 
[INFO ] 2024-07-28 11:38:13.014 - [任务 1][POLICY] - Starting batch read, table name: POLICY, offset: {} 
[INFO ] 2024-07-28 11:38:13.014 - [任务 1][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-28 11:38:13.102 - [任务 1][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-28 11:38:13.103 - [任务 1][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-28 11:38:13.103 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-28 11:38:13.105 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-28 11:38:13.105 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-28 11:38:13.162 - [任务 1][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1722080130,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 11:38:13.162 - [任务 1][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-28 11:38:13.265 - [任务 1][POLICY] - Incremental sync completed 
[INFO ] 2024-07-28 11:38:13.265 - [任务 1][POLICY] - Exception skipping - The current exception does not match the skip exception strategy, message: Increment start point exceeds the log time window of mongodb, start point: 1722080130 
[ERROR] 2024-07-28 11:38:13.316 - [任务 1][POLICY] - com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1722137892, "i": 20}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1722137892, "i": 20}}, "signature": {"hash": {"$binary": {"base64": "xIYmR9TkiuCsKDCjqnigjRh7qTs=", "subType": "00"}}, "keyId": 7376103549123428362}}} <-- Error Message -->
com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1722137892, "i": 20}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1722137892, "i": 20}}, "signature": {"hash": {"$binary": {"base64": "xIYmR9TkiuCsKDCjqnigjRh7qTs=", "subType": "00"}}, "keyId": 7376103549123428362}}}

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1722137892, "i": 20}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1722137892, "i": 20}}, "signature": {"hash": {"$binary": {"base64": "xIYmR9TkiuCsKDCjqnigjRh7qTs=", "subType": "00"}}, "keyId": 7376103549123428362}}}
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:114)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:643)
	...

<-- Full Stack Trace -->
com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1722137892, "i": 20}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1722137892, "i": 20}}, "signature": {"hash": {"$binary": {"base64": "xIYmR9TkiuCsKDCjqnigjRh7qTs=", "subType": "00"}}, "keyId": 7376103549123428362}}}
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:176)
	at io.tapdata.mongodb.MongodbConnector.doStreamRead(MongodbConnector.java:1573)
	at io.tapdata.mongodb.MongodbConnector.streamRead(MongodbConnector.java:1567)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1722137892, "i": 20}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1722137892, "i": 20}}, "signature": {"hash": {"$binary": {"base64": "xIYmR9TkiuCsKDCjqnigjRh7qTs=", "subType": "00"}}, "keyId": 7376103549123428362}}}
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:114)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:643)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:73)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:204)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:122)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:87)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:76)
	at com.mongodb.internal.connection.DefaultServer$OperationCountTrackingConnection.command(DefaultServer.java:288)
	at com.mongodb.internal.operation.CommandOperationHelper.createReadCommandAndExecute(CommandOperationHelper.java:239)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$4(CommandOperationHelper.java:220)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$0(OperationHelper.java:358)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$1(OperationHelper.java:357)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.withSourceAndConnection(OperationHelper.java:356)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$5(CommandOperationHelper.java:218)
	at com.mongodb.internal.async.function.RetryingSyncSupplier.get(RetryingSyncSupplier.java:67)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:223)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:204)
	at com.mongodb.internal.operation.AggregateOperationImpl.execute(AggregateOperationImpl.java:191)
	at com.mongodb.internal.operation.ChangeStreamOperation.lambda$execute$0(ChangeStreamOperation.java:187)
	at com.mongodb.internal.operation.OperationHelper.withReadConnectionSource(OperationHelper.java:321)
	at com.mongodb.internal.operation.ChangeStreamOperation.execute(ChangeStreamOperation.java:185)
	at com.mongodb.internal.operation.ChangeStreamOperation.execute(ChangeStreamOperation.java:55)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:185)
	at com.mongodb.client.internal.ChangeStreamIterableImpl.execute(ChangeStreamIterableImpl.java:212)
	at com.mongodb.client.internal.ChangeStreamIterableImpl.cursor(ChangeStreamIterableImpl.java:187)
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:124)
	... 22 more

[INFO ] 2024-07-28 11:38:13.317 - [任务 1][POLICY] - Job suspend in error handle 
[INFO ] 2024-07-28 11:38:13.691 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] running status set to false 
[INFO ] 2024-07-28 11:38:13.691 - [任务 1][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-98331971-5193-49fb-b9a6-5e3500db997c 
[INFO ] 2024-07-28 11:38:13.692 - [任务 1][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-98331971-5193-49fb-b9a6-5e3500db997c 
[INFO ] 2024-07-28 11:38:13.692 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] schema data cleaned 
[INFO ] 2024-07-28 11:38:13.697 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] monitor closed 
[INFO ] 2024-07-28 11:38:13.700 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] close complete, cost 30 ms 
[INFO ] 2024-07-28 11:38:13.700 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] running status set to false 
[INFO ] 2024-07-28 11:38:13.717 - [任务 1][PoTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7 
[INFO ] 2024-07-28 11:38:13.717 - [任务 1][PoTest] - PDK connector node released: HazelcastTargetPdkDataNode-fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7 
[INFO ] 2024-07-28 11:38:13.722 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] schema data cleaned 
[INFO ] 2024-07-28 11:38:13.722 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] monitor closed 
[INFO ] 2024-07-28 11:38:13.923 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] close complete, cost 25 ms 
[INFO ] 2024-07-28 11:38:14.351 - [任务 1] - Task [任务 1] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-28 11:38:14.360 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 11:38:14.360 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@54155f15 
[INFO ] 2024-07-28 11:38:14.505 - [任务 1] - Stop task milestones: 66a4db517bd6415933d0d766(任务 1)  
[INFO ] 2024-07-28 11:38:14.506 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-28 11:38:14.522 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-28 11:38:14.523 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a4db517bd6415933d0d766] 
[INFO ] 2024-07-28 11:38:14.523 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a4db517bd6415933d0d766] 
[INFO ] 2024-07-28 11:38:25.347 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 11:38:25.350 - [任务 1] - Start task milestones: 66a4db517bd6415933d0d766(任务 1) 
[INFO ] 2024-07-28 11:38:25.457 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 11:38:25.535 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 11:38:25.535 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] start preload schema,table counts: 1 
[INFO ] 2024-07-28 11:38:25.535 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] start preload schema,table counts: 1 
[INFO ] 2024-07-28 11:38:25.536 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 11:38:25.540 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 11:38:26.427 - [任务 1][PoTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 11:38:26.428 - [任务 1][PoTest] - Table "test.PoTest" exists, skip auto create table 
[INFO ] 2024-07-28 11:38:26.428 - [任务 1][PoTest] - The table PoTest has already exist. 
[INFO ] 2024-07-28 11:38:26.449 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-28 11:38:26.449 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-28 11:38:26.449 - [任务 1][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-28 11:38:26.600 - [任务 1][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1722137906,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 11:38:26.749 - [任务 1][POLICY] - Initial sync started 
[INFO ] 2024-07-28 11:38:26.750 - [任务 1][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-28 11:38:26.751 - [任务 1][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-28 11:38:26.846 - [任务 1][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-28 11:38:26.847 - [任务 1][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-28 11:38:26.847 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-28 11:38:26.847 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-28 11:38:26.847 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-28 11:38:26.849 - [任务 1][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1722137906,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 11:38:27.049 - [任务 1][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-28 11:38:46.679 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] running status set to false 
[INFO ] 2024-07-28 11:38:46.718 - [任务 1][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-98331971-5193-49fb-b9a6-5e3500db997c 
[INFO ] 2024-07-28 11:38:46.718 - [任务 1][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-98331971-5193-49fb-b9a6-5e3500db997c 
[INFO ] 2024-07-28 11:38:46.718 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] schema data cleaned 
[INFO ] 2024-07-28 11:38:46.718 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] monitor closed 
[INFO ] 2024-07-28 11:38:46.720 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] close complete, cost 47 ms 
[INFO ] 2024-07-28 11:38:46.751 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] running status set to false 
[INFO ] 2024-07-28 11:38:46.751 - [任务 1][PoTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7 
[INFO ] 2024-07-28 11:38:46.751 - [任务 1][PoTest] - PDK connector node released: HazelcastTargetPdkDataNode-fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7 
[INFO ] 2024-07-28 11:38:46.751 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] schema data cleaned 
[INFO ] 2024-07-28 11:38:46.751 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] monitor closed 
[INFO ] 2024-07-28 11:38:46.958 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] close complete, cost 31 ms 
[INFO ] 2024-07-28 11:38:48.773 - [任务 1][POLICY] - Incremental sync completed 
[INFO ] 2024-07-28 11:38:50.310 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 11:38:50.423 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@36b07868 
[INFO ] 2024-07-28 11:38:50.426 - [任务 1] - Stop task milestones: 66a4db517bd6415933d0d766(任务 1)  
[INFO ] 2024-07-28 11:38:50.437 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-28 11:38:50.437 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-28 11:38:50.459 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a4db517bd6415933d0d766] 
[INFO ] 2024-07-28 11:38:50.459 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a4db517bd6415933d0d766] 
[INFO ] 2024-07-28 11:40:03.341 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 11:40:03.397 - [任务 1] - Start task milestones: 66a4db517bd6415933d0d766(任务 1) 
[INFO ] 2024-07-28 11:40:03.588 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 11:40:03.588 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 11:40:03.633 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] start preload schema,table counts: 1 
[INFO ] 2024-07-28 11:40:03.633 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] start preload schema,table counts: 1 
[INFO ] 2024-07-28 11:40:03.634 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 11:40:03.634 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 11:40:04.413 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-28 11:40:04.413 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-28 11:40:04.413 - [任务 1][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-28 11:40:04.414 - [任务 1][PoTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 11:40:04.433 - [任务 1][PoTest] - Table "test.PoTest" exists, skip auto create table 
[INFO ] 2024-07-28 11:40:04.433 - [任务 1][PoTest] - The table PoTest has already exist. 
[INFO ] 2024-07-28 11:40:04.539 - [任务 1][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1722138004,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 11:40:04.588 - [任务 1][POLICY] - Initial sync started 
[INFO ] 2024-07-28 11:40:04.588 - [任务 1][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-28 11:40:04.636 - [任务 1][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-28 11:40:04.637 - [任务 1][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-28 11:40:04.673 - [任务 1][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-28 11:40:04.675 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-28 11:40:04.675 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-28 11:40:04.675 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-28 11:40:04.684 - [任务 1][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1722138004,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 11:40:04.684 - [任务 1][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-28 11:46:59.524 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] running status set to false 
[INFO ] 2024-07-28 11:46:59.525 - [任务 1][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-98331971-5193-49fb-b9a6-5e3500db997c 
[INFO ] 2024-07-28 11:46:59.525 - [任务 1][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-98331971-5193-49fb-b9a6-5e3500db997c 
[INFO ] 2024-07-28 11:46:59.525 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] schema data cleaned 
[INFO ] 2024-07-28 11:46:59.525 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] monitor closed 
[INFO ] 2024-07-28 11:46:59.526 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] close complete, cost 66 ms 
[INFO ] 2024-07-28 11:46:59.527 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] running status set to false 
[INFO ] 2024-07-28 11:46:59.540 - [任务 1][PoTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7 
[INFO ] 2024-07-28 11:46:59.540 - [任务 1][PoTest] - PDK connector node released: HazelcastTargetPdkDataNode-fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7 
[INFO ] 2024-07-28 11:46:59.540 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] schema data cleaned 
[INFO ] 2024-07-28 11:46:59.540 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] monitor closed 
[INFO ] 2024-07-28 11:46:59.541 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] close complete, cost 14 ms 
[INFO ] 2024-07-28 11:47:01.504 - [任务 1][POLICY] - Incremental sync completed 
[INFO ] 2024-07-28 11:47:01.653 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 11:47:01.779 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@37f23d6f 
[INFO ] 2024-07-28 11:47:01.779 - [任务 1] - Stop task milestones: 66a4db517bd6415933d0d766(任务 1)  
[INFO ] 2024-07-28 11:47:01.820 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-28 11:47:01.820 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-28 11:47:01.844 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a4db517bd6415933d0d766] 
[INFO ] 2024-07-28 11:47:01.845 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a4db517bd6415933d0d766] 
[INFO ] 2024-07-28 11:49:14.937 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 11:49:14.939 - [任务 1] - Start task milestones: 66a4db517bd6415933d0d766(任务 1) 
[INFO ] 2024-07-28 11:49:15.148 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 11:49:15.204 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 11:49:15.204 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] start preload schema,table counts: 1 
[INFO ] 2024-07-28 11:49:15.205 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] start preload schema,table counts: 1 
[INFO ] 2024-07-28 11:49:15.205 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 11:49:15.407 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 11:49:16.012 - [任务 1][PoTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 11:49:16.029 - [任务 1][PoTest] - Table "test.PoTest" exists, skip auto create table 
[INFO ] 2024-07-28 11:49:16.030 - [任务 1][PoTest] - The table PoTest has already exist. 
[INFO ] 2024-07-28 11:49:16.078 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-28 11:49:16.082 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-28 11:49:16.082 - [任务 1][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-28 11:49:16.253 - [任务 1][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1722138556,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 11:49:16.401 - [任务 1][POLICY] - Initial sync started 
[INFO ] 2024-07-28 11:49:16.403 - [任务 1][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-28 11:49:16.403 - [任务 1][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-28 11:49:16.484 - [任务 1][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-28 11:49:16.485 - [任务 1][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-28 11:49:16.488 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-28 11:49:16.488 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-28 11:49:16.490 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-28 11:49:16.491 - [任务 1][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1722138556,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 11:49:16.693 - [任务 1][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-28 11:51:13.614 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] running status set to false 
[INFO ] 2024-07-28 11:51:13.615 - [任务 1][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-98331971-5193-49fb-b9a6-5e3500db997c 
[INFO ] 2024-07-28 11:51:13.615 - [任务 1][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-98331971-5193-49fb-b9a6-5e3500db997c 
[INFO ] 2024-07-28 11:51:13.615 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] schema data cleaned 
[INFO ] 2024-07-28 11:51:13.615 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] monitor closed 
[INFO ] 2024-07-28 11:51:13.616 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] close complete, cost 29 ms 
[INFO ] 2024-07-28 11:51:13.638 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] running status set to false 
[INFO ] 2024-07-28 11:51:13.638 - [任务 1][PoTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7 
[INFO ] 2024-07-28 11:51:13.638 - [任务 1][PoTest] - PDK connector node released: HazelcastTargetPdkDataNode-fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7 
[INFO ] 2024-07-28 11:51:13.638 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] schema data cleaned 
[INFO ] 2024-07-28 11:51:13.638 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] monitor closed 
[INFO ] 2024-07-28 11:51:13.640 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] close complete, cost 23 ms 
[INFO ] 2024-07-28 11:51:14.023 - [任务 1][POLICY] - Incremental sync completed 
[INFO ] 2024-07-28 11:51:18.511 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 11:51:18.630 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@76fd40e2 
[INFO ] 2024-07-28 11:51:18.630 - [任务 1] - Stop task milestones: 66a4db517bd6415933d0d766(任务 1)  
[INFO ] 2024-07-28 11:51:18.644 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-28 11:51:18.644 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-28 11:51:18.686 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a4db517bd6415933d0d766] 
[INFO ] 2024-07-28 11:51:18.686 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a4db517bd6415933d0d766] 
[INFO ] 2024-07-28 11:55:55.033 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 11:55:55.035 - [任务 1] - Start task milestones: 66a4db517bd6415933d0d766(任务 1) 
[INFO ] 2024-07-28 11:55:55.246 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 11:55:55.414 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 11:55:55.461 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] start preload schema,table counts: 1 
[INFO ] 2024-07-28 11:55:55.464 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] start preload schema,table counts: 1 
[INFO ] 2024-07-28 11:55:55.464 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 11:55:55.464 - [任务 1][PoTest] - Node PoTest[fd0f95b3-1ccd-40a8-b5c8-62df3ebc02e7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 11:55:56.614 - [任务 1][PoTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 11:55:56.615 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-28 11:55:56.618 - [任务 1][PoTest] - Table "test.PoTest" exists, skip auto create table 
[INFO ] 2024-07-28 11:55:56.618 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-28 11:55:56.618 - [任务 1][PoTest] - The table PoTest has already exist. 
[INFO ] 2024-07-28 11:55:56.618 - [任务 1][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-28 11:55:56.888 - [任务 1][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1722138956,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 11:55:56.944 - [任务 1][POLICY] - Initial sync started 
[INFO ] 2024-07-28 11:55:56.944 - [任务 1][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-28 11:55:56.944 - [任务 1][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-28 11:55:56.968 - [任务 1][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-28 11:55:57.034 - [任务 1][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-28 11:55:57.034 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-28 11:55:57.035 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-28 11:55:57.035 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-28 11:55:57.052 - [任务 1][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1722138956,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 11:55:57.052 - [任务 1][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-28 12:07:36.030 - [任务 1][POLICY] - Node POLICY[98331971-5193-49fb-b9a6-5e3500db997c] running status set to false 
