[INFO ] 2024-07-04 14:25:51.999 - [Heartbeat-SouceMysql] - Start task milestones: 6683d6b1fa7caf4ccea13b90(Heartbeat-SouceMysql) 
[INFO ] 2024-07-04 14:25:53.538 - [Heartbeat-SouceMysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-04 14:25:53.612 - [Heartbeat-SouceMysql] - The engine receives Heartbeat-SouceMysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-04 14:25:54.402 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d8810f56-d721-4d86-abca-9d898b9236cf] start preload schema,table counts: 1 
[INFO ] 2024-07-04 14:25:54.408 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[69c8f113-36f7-4db5-a3a7-c6d945f63402] start preload schema,table counts: 1 
[INFO ] 2024-07-04 14:25:54.428 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[69c8f113-36f7-4db5-a3a7-c6d945f63402] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 14:25:54.429 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d8810f56-d721-4d86-abca-9d898b9236cf] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 14:25:55.284 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-04 14:25:55.304 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-04 14:25:55.304 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-04 14:25:55.421 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1719916210896,"lastTimes":1720006652715,"lastTN":5030,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":37716,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-04 14:25:55.421 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1719916210896,"lastTimes":1720006652715,"lastTN":5030,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":37716,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-04 14:25:55.436 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-04 14:25:55.444 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-04 14:25:56.231 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-04 14:33:19.592 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d8810f56-d721-4d86-abca-9d898b9236cf] running status set to false 
[INFO ] 2024-07-04 14:33:19.603 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-04 14:33:19.603 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-d8810f56-d721-4d86-abca-9d898b9236cf 
[INFO ] 2024-07-04 14:33:19.604 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-d8810f56-d721-4d86-abca-9d898b9236cf 
[INFO ] 2024-07-04 14:33:19.604 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d8810f56-d721-4d86-abca-9d898b9236cf] schema data cleaned 
[INFO ] 2024-07-04 14:33:19.606 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d8810f56-d721-4d86-abca-9d898b9236cf] monitor closed 
[INFO ] 2024-07-04 14:33:19.606 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d8810f56-d721-4d86-abca-9d898b9236cf] close complete, cost 27 ms 
[INFO ] 2024-07-04 14:33:19.630 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[69c8f113-36f7-4db5-a3a7-c6d945f63402] running status set to false 
[INFO ] 2024-07-04 14:33:19.630 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-69c8f113-36f7-4db5-a3a7-c6d945f63402 
[INFO ] 2024-07-04 14:33:19.630 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-69c8f113-36f7-4db5-a3a7-c6d945f63402 
[INFO ] 2024-07-04 14:33:19.630 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[69c8f113-36f7-4db5-a3a7-c6d945f63402] schema data cleaned 
[INFO ] 2024-07-04 14:33:19.630 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[69c8f113-36f7-4db5-a3a7-c6d945f63402] monitor closed 
[INFO ] 2024-07-04 14:33:19.633 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[69c8f113-36f7-4db5-a3a7-c6d945f63402] close complete, cost 24 ms 
[INFO ] 2024-07-04 14:33:23.046 - [Heartbeat-SouceMysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-04 14:33:23.060 - [Heartbeat-SouceMysql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6606d74d 
[INFO ] 2024-07-04 14:33:23.061 - [Heartbeat-SouceMysql] - Stop task milestones: 6683d6b1fa7caf4ccea13b90(Heartbeat-SouceMysql)  
[INFO ] 2024-07-04 14:33:23.191 - [Heartbeat-SouceMysql] - Stopped task aspect(s) 
[INFO ] 2024-07-04 14:33:23.191 - [Heartbeat-SouceMysql] - Snapshot order controller have been removed 
[INFO ] 2024-07-04 14:33:23.220 - [Heartbeat-SouceMysql] - Remove memory task client succeed, task: Heartbeat-SouceMysql[6683d6b1fa7caf4ccea13b90] 
[INFO ] 2024-07-04 14:33:23.220 - [Heartbeat-SouceMysql] - Destroy memory task client cache succeed, task: Heartbeat-SouceMysql[6683d6b1fa7caf4ccea13b90] 
