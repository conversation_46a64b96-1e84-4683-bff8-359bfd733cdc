[INFO ] 2024-07-19 07:02:41.257 - [来自Mongo的共享挖掘任务] - Start task milestones: 6695b94e6d76494ed53f39a0(来自Mongo的共享挖掘任务) 
[INFO ] 2024-07-19 07:02:42.505 - [来自Mongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-19 07:02:42.645 - [来自Mongo的共享挖掘任务] - The engine receives 来自Mongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-19 07:02:43.606 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[8e515c574ee44fbdbb1f298f88a2ccf2] start preload schema,table counts: 2 
[INFO ] 2024-07-19 07:02:43.624 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-19 07:02:43.640 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-19 07:02:43.648 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[8e515c574ee44fbdbb1f298f88a2ccf2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-19 07:02:44.589 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695bb1d66ab5ede8a9de506, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6695b8966d76494ed53f3874__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_297978497, shareCdcTaskId=6695b94e6d76494ed53f39a0, connectionId=6695b8966d76494ed53f3874) 
[INFO ] 2024-07-19 07:02:44.825 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_297978497', head seq: 0, tail seq: 44453 
[INFO ] 2024-07-19 07:02:44.882 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695b94e66ab5ede8a9dc8b0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6695b8966d76494ed53f3874_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-384100775, shareCdcTaskId=6695b94e6d76494ed53f39a0, connectionId=6695b8966d76494ed53f3874) 
[INFO ] 2024-07-19 07:02:44.997 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_-384100775', head seq: 0, tail seq: 1 
[INFO ] 2024-07-19 07:02:44.997 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav392?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-19 07:02:45.772 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" read batch size: 2000 
[INFO ] 2024-07-19 07:02:45.772 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" event queue capacity: 4000 
[INFO ] 2024-07-19 07:02:45.774 - [来自Mongo的共享挖掘任务][Mongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-19 07:02:45.796 - [来自Mongo的共享挖掘任务][Mongo] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2864/1642961581@2f65b11a failed, java.lang.RuntimeException: java.lang.NullPointerException
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:397)
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:346)
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObject(ObjectSerializableImplV2.java:300)
	at io.tapdata.flow.engine.V2.util.PdkUtil.decodeOffset(PdkUtil.java:138)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readNormalAndLogCollectorTaskStreamOffset(HazelcastSourcePdkBaseNode.java:491)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readStreamOffset(HazelcastSourcePdkBaseNode.java:420)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:402)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:292)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:213)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
 
[ERROR] 2024-07-19 07:02:45.866 - [来自Mongo的共享挖掘任务][Mongo] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2864/1642961581@2f65b11a failed, java.lang.RuntimeException: java.lang.NullPointerException
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:397)
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:346)
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObject(ObjectSerializableImplV2.java:300)
	at io.tapdata.flow.engine.V2.util.PdkUtil.decodeOffset(PdkUtil.java:138)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readNormalAndLogCollectorTaskStreamOffset(HazelcastSourcePdkBaseNode.java:491)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readStreamOffset(HazelcastSourcePdkBaseNode.java:420)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:402)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:292)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:213)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2864/1642961581@2f65b11a failed, java.lang.RuntimeException: java.lang.NullPointerException
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:397)
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:346)
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObject(ObjectSerializableImplV2.java:300)
	at io.tapdata.flow.engine.V2.util.PdkUtil.decodeOffset(PdkUtil.java:138)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readNormalAndLogCollectorTaskStreamOffset(HazelcastSourcePdkBaseNode.java:491)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readStreamOffset(HazelcastSourcePdkBaseNode.java:420)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:402)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:292)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:213)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more


<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:397)
	io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:346)
	io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObject(ObjectSerializableImplV2.java:300)
	io.tapdata.flow.engine.V2.util.PdkUtil.decodeOffset(PdkUtil.java:138)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readNormalAndLogCollectorTaskStreamOffset(HazelcastSourcePdkBaseNode.java:491)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2864/1642961581@2f65b11a failed, java.lang.RuntimeException: java.lang.NullPointerException
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:397)
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:346)
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObject(ObjectSerializableImplV2.java:300)
	at io.tapdata.flow.engine.V2.util.PdkUtil.decodeOffset(PdkUtil.java:138)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readNormalAndLogCollectorTaskStreamOffset(HazelcastSourcePdkBaseNode.java:491)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readStreamOffset(HazelcastSourcePdkBaseNode.java:420)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:402)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:292)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:213)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkShareCDCNode.doInit(HazelcastSourcePdkShareCDCNode.java:62)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2864/1642961581@2f65b11a failed, java.lang.RuntimeException: java.lang.NullPointerException
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:397)
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:346)
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObject(ObjectSerializableImplV2.java:300)
	at io.tapdata.flow.engine.V2.util.PdkUtil.decodeOffset(PdkUtil.java:138)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readNormalAndLogCollectorTaskStreamOffset(HazelcastSourcePdkBaseNode.java:491)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readStreamOffset(HazelcastSourcePdkBaseNode.java:420)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:402)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:292)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:213)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 14 more
Caused by: java.lang.RuntimeException: java.lang.NullPointerException
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: java.lang.NullPointerException
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:397)
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:346)
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObject(ObjectSerializableImplV2.java:300)
	at io.tapdata.flow.engine.V2.util.PdkUtil.decodeOffset(PdkUtil.java:138)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readNormalAndLogCollectorTaskStreamOffset(HazelcastSourcePdkBaseNode.java:491)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readStreamOffset(HazelcastSourcePdkBaseNode.java:420)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.readBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:402)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:292)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:213)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more

[INFO ] 2024-07-19 07:02:45.950 - [来自Mongo的共享挖掘任务][Mongo] - Job suspend in error handle 
[INFO ] 2024-07-19 07:02:45.960 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[8e515c574ee44fbdbb1f298f88a2ccf2] running status set to false 
[INFO ] 2024-07-19 07:02:46.003 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[de0d452dc398459a945bb1ca7ed539a8] running status set to false 
[INFO ] 2024-07-19 07:02:46.003 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-19 07:02:46.003 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-19 07:02:46.004 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[de0d452dc398459a945bb1ca7ed539a8] schema data cleaned 
[INFO ] 2024-07-19 07:02:46.014 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[de0d452dc398459a945bb1ca7ed539a8] monitor closed 
[INFO ] 2024-07-19 07:02:46.014 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-8e515c574ee44fbdbb1f298f88a2ccf2 
[INFO ] 2024-07-19 07:02:46.014 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-8e515c574ee44fbdbb1f298f88a2ccf2 
[INFO ] 2024-07-19 07:02:46.015 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[8e515c574ee44fbdbb1f298f88a2ccf2] schema data cleaned 
[INFO ] 2024-07-19 07:02:46.028 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[8e515c574ee44fbdbb1f298f88a2ccf2] monitor closed 
[INFO ] 2024-07-19 07:02:46.028 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[8e515c574ee44fbdbb1f298f88a2ccf2] close complete, cost 77 ms 
[INFO ] 2024-07-19 07:02:46.029 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[de0d452dc398459a945bb1ca7ed539a8] close complete, cost 25 ms 
[INFO ] 2024-07-19 07:02:49.817 - [来自Mongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-19 07:02:49.922 - [来自Mongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@144137a9 
[INFO ] 2024-07-19 07:02:50.015 - [来自Mongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-19 07:02:50.015 - [来自Mongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-19 07:02:50.082 - [来自Mongo的共享挖掘任务] - Remove memory task client succeed, task: 来自Mongo的共享挖掘任务[6695b94e6d76494ed53f39a0] 
[INFO ] 2024-07-19 07:02:50.082 - [来自Mongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自Mongo的共享挖掘任务[6695b94e6d76494ed53f39a0] 
