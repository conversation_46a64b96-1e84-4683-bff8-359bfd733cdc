[INFO ] 2024-07-15 12:20:05.255 - [任务 5] - Task initialization... 
[INFO ] 2024-07-15 12:20:05.255 - [任务 5] - Start task milestones: 6694a3511df4b966216a510a(任务 5) 
[INFO ] 2024-07-15 12:20:05.383 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 12:20:05.383 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 12:20:05.475 - [任务 5][时间运算] - Node 时间运算[4d756919-083f-4c84-bdbc-3cbc5c5dcaf4] start preload schema,table counts: 1 
[INFO ] 2024-07-15 12:20:05.475 - [任务 5][AutoTestMongo] - Node AutoTestMongo[bcb403e9-b508-46ff-86b9-1eccc058e96f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 12:20:05.475 - [任务 5][AutoMysqlTest] - Node AutoMysqlTest[f3ca7627-abc6-4bf8-8070-f1a6ff2c8b1f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 12:20:05.476 - [任务 5][时间运算] - Node 时间运算[4d756919-083f-4c84-bdbc-3cbc5c5dcaf4] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 12:20:05.476 - [任务 5][AutoMysqlTest] - Node AutoMysqlTest[f3ca7627-abc6-4bf8-8070-f1a6ff2c8b1f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 12:20:05.681 - [任务 5][AutoTestMongo] - Node AutoTestMongo[bcb403e9-b508-46ff-86b9-1eccc058e96f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 12:20:06.355 - [任务 5][AutoTestMongo] - Source node "AutoTestMongo" read batch size: 100 
[INFO ] 2024-07-15 12:20:06.355 - [任务 5][AutoTestMongo] - Source node "AutoTestMongo" event queue capacity: 200 
[INFO ] 2024-07-15 12:20:06.356 - [任务 5][AutoTestMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 12:20:06.514 - [任务 5][AutoTestMongo] - batch offset found: {},stream offset found: {"cdcOffset":1721017205,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 12:20:06.526 - [任务 5][AutoMysqlTest] - Node(AutoMysqlTest) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-15 12:20:06.538 - [任务 5][AutoMysqlTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 12:20:06.572 - [任务 5][AutoTestMongo] - Initial sync started 
[INFO ] 2024-07-15 12:20:06.573 - [任务 5][AutoTestMongo] - Starting batch read, table name: ReplicationTimeCalculation, offset: null 
[INFO ] 2024-07-15 12:20:06.596 - [任务 5][AutoTestMongo] - Table ReplicationTimeCalculation is going to be initial synced 
[INFO ] 2024-07-15 12:20:06.597 - [任务 5][AutoTestMongo] - Table [ReplicationTimeCalculation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-15 12:20:06.604 - [任务 5][AutoTestMongo] - Query table 'ReplicationTimeCalculation' counts: 1 
[INFO ] 2024-07-15 12:20:06.605 - [任务 5][AutoTestMongo] - Initial sync completed 
[INFO ] 2024-07-15 12:20:06.615 - [任务 5][AutoTestMongo] - Incremental sync starting... 
[INFO ] 2024-07-15 12:20:06.622 - [任务 5][AutoTestMongo] - Initial sync completed 
[INFO ] 2024-07-15 12:20:06.622 - [任务 5][AutoTestMongo] - Starting stream read, table list: [ReplicationTimeCalculation], offset: {"cdcOffset":1721017205,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 12:20:06.827 - [任务 5][AutoTestMongo] - Connector MongoDB incremental start succeed, tables: [ReplicationTimeCalculation], data change syncing 
[INFO ] 2024-07-15 12:22:18.469 - [任务 5][AutoTestMongo] - Node AutoTestMongo[bcb403e9-b508-46ff-86b9-1eccc058e96f] running status set to false 
[INFO ] 2024-07-15 12:22:18.469 - [任务 5][AutoTestMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-bcb403e9-b508-46ff-86b9-1eccc058e96f 
[INFO ] 2024-07-15 12:22:18.469 - [任务 5][AutoTestMongo] - PDK connector node released: HazelcastSourcePdkDataNode-bcb403e9-b508-46ff-86b9-1eccc058e96f 
[INFO ] 2024-07-15 12:22:18.469 - [任务 5][AutoTestMongo] - Node AutoTestMongo[bcb403e9-b508-46ff-86b9-1eccc058e96f] schema data cleaned 
[INFO ] 2024-07-15 12:22:18.469 - [任务 5][AutoTestMongo] - Node AutoTestMongo[bcb403e9-b508-46ff-86b9-1eccc058e96f] monitor closed 
[INFO ] 2024-07-15 12:22:18.470 - [任务 5][AutoTestMongo] - Node AutoTestMongo[bcb403e9-b508-46ff-86b9-1eccc058e96f] close complete, cost 12 ms 
[INFO ] 2024-07-15 12:22:18.470 - [任务 5][时间运算] - Node 时间运算[4d756919-083f-4c84-bdbc-3cbc5c5dcaf4] running status set to false 
[INFO ] 2024-07-15 12:22:18.470 - [任务 5][时间运算] - Node 时间运算[4d756919-083f-4c84-bdbc-3cbc5c5dcaf4] schema data cleaned 
[INFO ] 2024-07-15 12:22:18.471 - [任务 5][时间运算] - Node 时间运算[4d756919-083f-4c84-bdbc-3cbc5c5dcaf4] monitor closed 
[INFO ] 2024-07-15 12:22:18.471 - [任务 5][时间运算] - Node 时间运算[4d756919-083f-4c84-bdbc-3cbc5c5dcaf4] close complete, cost 0 ms 
[INFO ] 2024-07-15 12:22:18.471 - [任务 5][AutoMysqlTest] - Node AutoMysqlTest[f3ca7627-abc6-4bf8-8070-f1a6ff2c8b1f] running status set to false 
[INFO ] 2024-07-15 12:22:18.492 - [任务 5][AutoMysqlTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-f3ca7627-abc6-4bf8-8070-f1a6ff2c8b1f 
[INFO ] 2024-07-15 12:22:18.492 - [任务 5][AutoMysqlTest] - PDK connector node released: HazelcastTargetPdkDataNode-f3ca7627-abc6-4bf8-8070-f1a6ff2c8b1f 
[INFO ] 2024-07-15 12:22:18.492 - [任务 5][AutoMysqlTest] - Node AutoMysqlTest[f3ca7627-abc6-4bf8-8070-f1a6ff2c8b1f] schema data cleaned 
[INFO ] 2024-07-15 12:22:18.492 - [任务 5][AutoMysqlTest] - Node AutoMysqlTest[f3ca7627-abc6-4bf8-8070-f1a6ff2c8b1f] monitor closed 
[INFO ] 2024-07-15 12:22:18.520 - [任务 5][AutoMysqlTest] - Node AutoMysqlTest[f3ca7627-abc6-4bf8-8070-f1a6ff2c8b1f] close complete, cost 21 ms 
[INFO ] 2024-07-15 12:22:18.520 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 12:22:18.521 - [任务 5] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1df6050b 
[INFO ] 2024-07-15 12:22:18.638 - [任务 5] - Stop task milestones: 6694a3511df4b966216a510a(任务 5)  
[INFO ] 2024-07-15 12:22:18.647 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-07-15 12:22:18.650 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 12:22:18.669 - [任务 5] - Remove memory task client succeed, task: 任务 5[6694a3511df4b966216a510a] 
[INFO ] 2024-07-15 12:22:18.672 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[6694a3511df4b966216a510a] 
[INFO ] 2024-07-15 12:22:31.856 - [任务 5] - Task initialization... 
[INFO ] 2024-07-15 12:22:31.856 - [任务 5] - Start task milestones: 6694a3511df4b966216a510a(任务 5) 
[INFO ] 2024-07-15 12:22:32.150 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 12:22:32.151 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 12:22:32.204 - [任务 5][AutoTestMongo] - Node AutoTestMongo[bcb403e9-b508-46ff-86b9-1eccc058e96f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 12:22:32.204 - [任务 5][时间运算] - Node 时间运算[4d756919-083f-4c84-bdbc-3cbc5c5dcaf4] start preload schema,table counts: 1 
[INFO ] 2024-07-15 12:22:32.204 - [任务 5][AutoMysqlTest] - Node AutoMysqlTest[f3ca7627-abc6-4bf8-8070-f1a6ff2c8b1f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 12:22:32.204 - [任务 5][AutoTestMongo] - Node AutoTestMongo[bcb403e9-b508-46ff-86b9-1eccc058e96f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 12:22:32.204 - [任务 5][时间运算] - Node 时间运算[4d756919-083f-4c84-bdbc-3cbc5c5dcaf4] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 12:22:32.204 - [任务 5][AutoMysqlTest] - Node AutoMysqlTest[f3ca7627-abc6-4bf8-8070-f1a6ff2c8b1f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 12:22:32.895 - [任务 5][AutoTestMongo] - Source node "AutoTestMongo" read batch size: 100 
[INFO ] 2024-07-15 12:22:32.899 - [任务 5][AutoTestMongo] - Source node "AutoTestMongo" event queue capacity: 200 
[INFO ] 2024-07-15 12:22:32.899 - [任务 5][AutoTestMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 12:22:33.182 - [任务 5][AutoTestMongo] - batch offset found: {},stream offset found: {"cdcOffset":1721017345,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 12:22:33.182 - [任务 5][AutoTestMongo] - Initial sync started 
[INFO ] 2024-07-15 12:22:33.188 - [任务 5][AutoTestMongo] - Starting batch read, table name: ReplicationTimeCalculation, offset: null 
[INFO ] 2024-07-15 12:22:33.188 - [任务 5][AutoTestMongo] - Table ReplicationTimeCalculation is going to be initial synced 
[INFO ] 2024-07-15 12:22:33.215 - [任务 5][AutoTestMongo] - Table [ReplicationTimeCalculation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-15 12:22:33.216 - [任务 5][AutoTestMongo] - Query table 'ReplicationTimeCalculation' counts: 1 
[INFO ] 2024-07-15 12:22:33.216 - [任务 5][AutoTestMongo] - Initial sync completed 
[INFO ] 2024-07-15 12:22:33.216 - [任务 5][AutoTestMongo] - Incremental sync starting... 
[INFO ] 2024-07-15 12:22:33.217 - [任务 5][AutoTestMongo] - Initial sync completed 
[INFO ] 2024-07-15 12:22:33.217 - [任务 5][AutoTestMongo] - Starting stream read, table list: [ReplicationTimeCalculation], offset: {"cdcOffset":1721017345,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 12:22:33.346 - [任务 5][AutoTestMongo] - Connector MongoDB incremental start succeed, tables: [ReplicationTimeCalculation], data change syncing 
[INFO ] 2024-07-15 12:22:33.347 - [任务 5][AutoMysqlTest] - Node(AutoMysqlTest) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-15 12:22:33.347 - [任务 5][AutoMysqlTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 12:23:01.234 - [任务 5][AutoTestMongo] - Node AutoTestMongo[bcb403e9-b508-46ff-86b9-1eccc058e96f] running status set to false 
[INFO ] 2024-07-15 12:23:01.395 - [任务 5][AutoTestMongo] - Incremental sync completed 
[INFO ] 2024-07-15 12:23:01.395 - [任务 5][AutoTestMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-bcb403e9-b508-46ff-86b9-1eccc058e96f 
[INFO ] 2024-07-15 12:23:01.396 - [任务 5][AutoTestMongo] - PDK connector node released: HazelcastSourcePdkDataNode-bcb403e9-b508-46ff-86b9-1eccc058e96f 
[INFO ] 2024-07-15 12:23:01.396 - [任务 5][AutoTestMongo] - Node AutoTestMongo[bcb403e9-b508-46ff-86b9-1eccc058e96f] schema data cleaned 
[INFO ] 2024-07-15 12:23:01.398 - [任务 5][AutoTestMongo] - Node AutoTestMongo[bcb403e9-b508-46ff-86b9-1eccc058e96f] monitor closed 
[INFO ] 2024-07-15 12:23:01.399 - [任务 5][AutoTestMongo] - Node AutoTestMongo[bcb403e9-b508-46ff-86b9-1eccc058e96f] close complete, cost 236 ms 
[INFO ] 2024-07-15 12:23:01.399 - [任务 5][时间运算] - Node 时间运算[4d756919-083f-4c84-bdbc-3cbc5c5dcaf4] running status set to false 
[INFO ] 2024-07-15 12:23:01.399 - [任务 5][时间运算] - Node 时间运算[4d756919-083f-4c84-bdbc-3cbc5c5dcaf4] schema data cleaned 
[INFO ] 2024-07-15 12:23:01.399 - [任务 5][时间运算] - Node 时间运算[4d756919-083f-4c84-bdbc-3cbc5c5dcaf4] monitor closed 
[INFO ] 2024-07-15 12:23:01.400 - [任务 5][时间运算] - Node 时间运算[4d756919-083f-4c84-bdbc-3cbc5c5dcaf4] close complete, cost 1 ms 
[INFO ] 2024-07-15 12:23:01.400 - [任务 5][AutoMysqlTest] - Node AutoMysqlTest[f3ca7627-abc6-4bf8-8070-f1a6ff2c8b1f] running status set to false 
[INFO ] 2024-07-15 12:23:01.449 - [任务 5][AutoMysqlTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-f3ca7627-abc6-4bf8-8070-f1a6ff2c8b1f 
[INFO ] 2024-07-15 12:23:01.449 - [任务 5][AutoMysqlTest] - PDK connector node released: HazelcastTargetPdkDataNode-f3ca7627-abc6-4bf8-8070-f1a6ff2c8b1f 
[INFO ] 2024-07-15 12:23:01.449 - [任务 5][AutoMysqlTest] - Node AutoMysqlTest[f3ca7627-abc6-4bf8-8070-f1a6ff2c8b1f] schema data cleaned 
[INFO ] 2024-07-15 12:23:01.450 - [任务 5][AutoMysqlTest] - Node AutoMysqlTest[f3ca7627-abc6-4bf8-8070-f1a6ff2c8b1f] monitor closed 
[INFO ] 2024-07-15 12:23:01.450 - [任务 5][AutoMysqlTest] - Node AutoMysqlTest[f3ca7627-abc6-4bf8-8070-f1a6ff2c8b1f] close complete, cost 50 ms 
[INFO ] 2024-07-15 12:23:03.744 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 12:23:03.745 - [任务 5] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@51661f07 
[INFO ] 2024-07-15 12:23:03.864 - [任务 5] - Stop task milestones: 6694a3511df4b966216a510a(任务 5)  
[INFO ] 2024-07-15 12:23:03.877 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-07-15 12:23:03.877 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 12:23:03.898 - [任务 5] - Remove memory task client succeed, task: 任务 5[6694a3511df4b966216a510a] 
[INFO ] 2024-07-15 12:23:03.900 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[6694a3511df4b966216a510a] 
