[INFO ] 2024-03-26 12:31:56.654 - [任务 7] - Start task milestones: 660235c455700b348d5e79d5(任务 7) 
[INFO ] 2024-03-26 12:31:56.663 - [任务 7] - Task initialization... 
[INFO ] 2024-03-26 12:31:56.681 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-26 12:31:56.792 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-26 12:31:57.265 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] start preload schema,table counts: 1 
[INFO ] 2024-03-26 12:31:57.272 - [任务 7][test2] - Node test2[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] start preload schema,table counts: 1 
[INFO ] 2024-03-26 12:31:57.389 - [任务 7][test2] - Node test2[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] preload schema finished, cost 117 ms 
[INFO ] 2024-03-26 12:31:57.590 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] preload schema finished, cost 118 ms 
[INFO ] 2024-03-26 12:32:00.691 - [任务 7][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-26 12:32:00.692 - [任务 7][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-26 12:32:00.692 - [任务 7][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-26 12:32:00.716 - [任务 7][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":144624863,"gtidSet":""} 
[INFO ] 2024-03-26 12:32:00.982 - [任务 7][CLAIM] - Initial sync started 
[INFO ] 2024-03-26 12:32:00.982 - [任务 7][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-26 12:32:01.074 - [任务 7][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-26 12:32:01.075 - [任务 7][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-26 12:32:05.935 - [任务 7][test2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-26 12:32:06.386 - [任务 7][CLAIM] - Initial sync completed 
[INFO ] 2024-03-26 12:32:06.390 - [任务 7][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-26 12:32:06.390 - [任务 7][CLAIM] - Initial sync completed 
[INFO ] 2024-03-26 12:32:06.399 - [任务 7][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":144624863,"gtidSet":""} 
[INFO ] 2024-03-26 12:32:06.528 - [任务 7][CLAIM] - Starting mysql cdc, server name: 3785e2f0-b6de-4904-a7fa-f279ea6bb6af 
[INFO ] 2024-03-26 12:32:06.528 - [任务 7][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1934079143
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 3785e2f0-b6de-4904-a7fa-f279ea6bb6af
  database.port: 3306
  threadName: Debezium-Mysql-Connector-3785e2f0-b6de-4904-a7fa-f279ea6bb6af
  database.hostname: 127.0.0.1
  database.password: ********
  name: 3785e2f0-b6de-4904-a7fa-f279ea6bb6af
  pdk.offset.string: {"name":"3785e2f0-b6de-4904-a7fa-f279ea6bb6af","offset":{"{\"server\":\"3785e2f0-b6de-4904-a7fa-f279ea6bb6af\"}":"{\"file\":\"binlog.000020\",\"pos\":144624863,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-26 12:32:06.933 - [任务 7][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-26 12:32:42.305 - [任务 7][CLAIM] - Read DDL: alter table CLAIM add COLUMN `name` varchar(50), about to be packaged as some event(s) 
[INFO ] 2024-03-26 12:32:42.308 - [任务 7][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='3785e2f0-b6de-4904-a7fa-f279ea6bb6af', offset={{"server":"3785e2f0-b6de-4904-a7fa-f279ea6bb6af"}={"ts_sec":1711427561,"file":"binlog.000020","pos":144625122,"server_id":1}}} 
[INFO ] 2024-03-26 12:32:42.335 - [任务 7][CLAIM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@de57099: {"newFields":[{"autoInc":false,"dataType":"varchar(50)","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false}],"referenceTime":1711427561781,"tableId":"CLAIM","time":1711427562293,"type":209} 
[WARN ] 2024-03-26 12:32:42.536 - [任务 7][CLAIM] - DDL events are filtered
 - Event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@de57099: {"newFields":[{"autoInc":false,"dataType":"varchar(50)","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false}],"referenceTime":1711427561781,"tableId":"CLAIM","time":1711427562293,"type":209}
 - Filter: {} 
[INFO ] 2024-03-26 12:33:29.873 - [任务 7] - Stop task milestones: 660235c455700b348d5e79d5(任务 7)  
[INFO ] 2024-03-26 12:33:30.199 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] running status set to false 
[INFO ] 2024-03-26 12:33:30.259 - [任务 7][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-26 12:33:30.260 - [任务 7][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-26 12:33:30.275 - [任务 7][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb 
[INFO ] 2024-03-26 12:33:30.275 - [任务 7][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb 
[INFO ] 2024-03-26 12:33:30.277 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] schema data cleaned 
[INFO ] 2024-03-26 12:33:30.277 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] monitor closed 
[INFO ] 2024-03-26 12:33:30.283 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] close complete, cost 90 ms 
[INFO ] 2024-03-26 12:33:30.283 - [任务 7][test2] - Node test2[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] running status set to false 
[INFO ] 2024-03-26 12:33:30.348 - [任务 7][test2] - PDK connector node stopped: HazelcastTargetPdkDataNode-1d47d5cb-effc-4b87-87ab-c3d11c45a4be 
[INFO ] 2024-03-26 12:33:30.348 - [任务 7][test2] - PDK connector node released: HazelcastTargetPdkDataNode-1d47d5cb-effc-4b87-87ab-c3d11c45a4be 
[INFO ] 2024-03-26 12:33:30.349 - [任务 7][test2] - Node test2[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] schema data cleaned 
[INFO ] 2024-03-26 12:33:30.350 - [任务 7][test2] - Node test2[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] monitor closed 
[INFO ] 2024-03-26 12:33:30.555 - [任务 7][test2] - Node test2[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] close complete, cost 67 ms 
[INFO ] 2024-03-26 12:33:33.295 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-26 12:33:33.295 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-03-26 12:33:33.356 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-03-26 12:33:33.356 - [任务 7] - Remove memory task client succeed, task: 任务 7[660235c455700b348d5e79d5] 
[INFO ] 2024-03-26 12:33:33.561 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[660235c455700b348d5e79d5] 
[INFO ] 2024-03-26 12:34:27.620 - [任务 7] - Start task milestones: 660235c455700b348d5e79d5(任务 7) 
[INFO ] 2024-03-26 12:34:27.648 - [任务 7] - Task initialization... 
[INFO ] 2024-03-26 12:34:27.648 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-26 12:34:27.801 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-26 12:34:27.801 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] start preload schema,table counts: 1 
[INFO ] 2024-03-26 12:34:27.802 - [任务 7][test2] - Node test3[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] start preload schema,table counts: 1 
[INFO ] 2024-03-26 12:34:27.852 - [任务 7][test2] - Node test3[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] preload schema finished, cost 50 ms 
[INFO ] 2024-03-26 12:34:27.856 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] preload schema finished, cost 50 ms 
[INFO ] 2024-03-26 12:34:28.604 - [任务 7][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-26 12:34:28.607 - [任务 7][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-26 12:34:28.607 - [任务 7][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-26 12:34:28.659 - [任务 7][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":144625122,"gtidSet":""} 
[INFO ] 2024-03-26 12:34:28.659 - [任务 7][CLAIM] - Initial sync started 
[INFO ] 2024-03-26 12:34:28.669 - [任务 7][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-26 12:34:28.672 - [任务 7][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-26 12:34:28.883 - [任务 7][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-26 12:34:35.945 - [任务 7][test2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-26 12:34:36.332 - [任务 7][CLAIM] - Initial sync completed 
[INFO ] 2024-03-26 12:34:36.333 - [任务 7][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-26 12:34:36.333 - [任务 7][CLAIM] - Initial sync completed 
[INFO ] 2024-03-26 12:34:36.336 - [任务 7][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":144625122,"gtidSet":""} 
[INFO ] 2024-03-26 12:34:36.387 - [任务 7][CLAIM] - Starting mysql cdc, server name: 9cc39fac-27b7-4951-bc8c-e2cf3bc005bc 
[INFO ] 2024-03-26 12:34:36.388 - [任务 7][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 660109057
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9cc39fac-27b7-4951-bc8c-e2cf3bc005bc
  database.port: 3306
  threadName: Debezium-Mysql-Connector-9cc39fac-27b7-4951-bc8c-e2cf3bc005bc
  database.hostname: 127.0.0.1
  database.password: ********
  name: 9cc39fac-27b7-4951-bc8c-e2cf3bc005bc
  pdk.offset.string: {"name":"9cc39fac-27b7-4951-bc8c-e2cf3bc005bc","offset":{"{\"server\":\"9cc39fac-27b7-4951-bc8c-e2cf3bc005bc\"}":"{\"file\":\"binlog.000020\",\"pos\":144625122,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-26 12:34:36.598 - [任务 7][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-26 12:34:54.491 - [任务 7][CLAIM] - Read DDL: alter table CLAIM drop column `name`, about to be packaged as some event(s) 
[INFO ] 2024-03-26 12:34:54.491 - [任务 7][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapDropFieldEvent
  - Offset: MysqlStreamOffset{name='9cc39fac-27b7-4951-bc8c-e2cf3bc005bc', offset={{"server":"9cc39fac-27b7-4951-bc8c-e2cf3bc005bc"}={"ts_sec":1711427693,"file":"binlog.000020","pos":144625368,"server_id":1}}} 
[INFO ] 2024-03-26 12:34:54.493 - [任务 7][CLAIM] - Source node received an ddl event: TapDropFieldEvent{tableId='CLAIM', fieldName='name'} 
[INFO ] 2024-03-26 12:34:54.698 - [任务 7][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660235c455700b348d5e79d5 
[INFO ] 2024-03-26 12:34:54.858 - [任务 7][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-26 12:37:35.418 - [任务 7] - Stop task milestones: 660235c455700b348d5e79d5(任务 7)  
[INFO ] 2024-03-26 12:37:35.752 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] running status set to false 
[INFO ] 2024-03-26 12:37:35.752 - [任务 7][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-26 12:37:35.754 - [任务 7][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb 
[INFO ] 2024-03-26 12:37:35.755 - [任务 7][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb 
[INFO ] 2024-03-26 12:37:35.756 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] schema data cleaned 
[INFO ] 2024-03-26 12:37:35.758 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] monitor closed 
[INFO ] 2024-03-26 12:37:35.758 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] close complete, cost 119 ms 
[INFO ] 2024-03-26 12:37:35.781 - [任务 7][test2] - Node test3[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] running status set to false 
[INFO ] 2024-03-26 12:37:35.781 - [任务 7][test2] - PDK connector node stopped: HazelcastTargetPdkDataNode-1d47d5cb-effc-4b87-87ab-c3d11c45a4be 
[INFO ] 2024-03-26 12:37:35.781 - [任务 7][test2] - PDK connector node released: HazelcastTargetPdkDataNode-1d47d5cb-effc-4b87-87ab-c3d11c45a4be 
[INFO ] 2024-03-26 12:37:35.786 - [任务 7][test2] - Node test3[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] schema data cleaned 
[INFO ] 2024-03-26 12:37:35.787 - [任务 7][test2] - Node test3[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] monitor closed 
[INFO ] 2024-03-26 12:37:35.787 - [任务 7][test2] - Node test3[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] close complete, cost 23 ms 
[INFO ] 2024-03-26 12:37:40.312 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-26 12:37:40.312 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-03-26 12:37:40.313 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-03-26 12:37:40.364 - [任务 7] - Remove memory task client succeed, task: 任务 7[660235c455700b348d5e79d5] 
[INFO ] 2024-03-26 12:37:40.364 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[660235c455700b348d5e79d5] 
[INFO ] 2024-03-26 12:41:02.194 - [任务 7] - Start task milestones: 660235c455700b348d5e79d5(任务 7) 
[INFO ] 2024-03-26 12:41:02.306 - [任务 7] - Task initialization... 
[INFO ] 2024-03-26 12:41:02.306 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-26 12:41:02.421 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-26 12:41:02.509 - [任务 7][test2] - Node test3[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] start preload schema,table counts: 1 
[INFO ] 2024-03-26 12:41:02.509 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] start preload schema,table counts: 1 
[INFO ] 2024-03-26 12:41:02.554 - [任务 7][test2] - Node test3[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] preload schema finished, cost 45 ms 
[INFO ] 2024-03-26 12:41:02.755 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] preload schema finished, cost 44 ms 
[INFO ] 2024-03-26 12:41:03.331 - [任务 7][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-26 12:41:03.336 - [任务 7][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-26 12:41:03.336 - [任务 7][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-26 12:41:03.404 - [任务 7][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":144625368,"gtidSet":""} 
[INFO ] 2024-03-26 12:41:03.404 - [任务 7][CLAIM] - Initial sync started 
[INFO ] 2024-03-26 12:41:03.413 - [任务 7][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-26 12:41:03.413 - [任务 7][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-26 12:41:03.617 - [任务 7][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-26 12:42:15.965 - [任务 7][test2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-26 12:42:16.144 - [任务 7][test2] - The table test3 has already exist. 
[INFO ] 2024-03-26 12:42:16.305 - [任务 7][CLAIM] - Initial sync completed 
[INFO ] 2024-03-26 12:42:16.307 - [任务 7][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-26 12:42:16.307 - [任务 7][CLAIM] - Initial sync completed 
[INFO ] 2024-03-26 12:42:16.427 - [任务 7][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":144625368,"gtidSet":""} 
[INFO ] 2024-03-26 12:42:16.427 - [任务 7][CLAIM] - Starting mysql cdc, server name: 44932e13-ee02-4da7-90a4-618467a16521 
[INFO ] 2024-03-26 12:42:16.503 - [任务 7][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1536922134
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 44932e13-ee02-4da7-90a4-618467a16521
  database.port: 3306
  threadName: Debezium-Mysql-Connector-44932e13-ee02-4da7-90a4-618467a16521
  database.hostname: 127.0.0.1
  database.password: ********
  name: 44932e13-ee02-4da7-90a4-618467a16521
  pdk.offset.string: {"name":"44932e13-ee02-4da7-90a4-618467a16521","offset":{"{\"server\":\"44932e13-ee02-4da7-90a4-618467a16521\"}":"{\"file\":\"binlog.000020\",\"pos\":144625368,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-26 12:42:16.505 - [任务 7][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-26 12:42:42.997 - [任务 7][CLAIM] - Read DDL: alter table CLAIM add COLUMN `name` varchar(50), about to be packaged as some event(s) 
[INFO ] 2024-03-26 12:42:43.000 - [任务 7][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='44932e13-ee02-4da7-90a4-618467a16521', offset={{"server":"44932e13-ee02-4da7-90a4-618467a16521"}={"ts_sec":1711428162,"file":"binlog.000020","pos":144625627,"server_id":1}}} 
[INFO ] 2024-03-26 12:42:43.000 - [任务 7][CLAIM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@43c45625: {"newFields":[{"autoInc":false,"dataType":"varchar(50)","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false}],"referenceTime":1711428162795,"tableId":"CLAIM","time":1711428162992,"type":209} 
[INFO ] 2024-03-26 12:42:43.066 - [任务 7][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660235c455700b348d5e79d5 
[INFO ] 2024-03-26 12:42:43.066 - [任务 7][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-26 12:43:51.472 - [任务 7] - Stop task milestones: 660235c455700b348d5e79d5(任务 7)  
[INFO ] 2024-03-26 12:43:51.571 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] running status set to false 
[INFO ] 2024-03-26 12:43:51.574 - [任务 7][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-26 12:43:51.575 - [任务 7][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-26 12:43:51.580 - [任务 7][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb 
[INFO ] 2024-03-26 12:43:51.580 - [任务 7][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb 
[INFO ] 2024-03-26 12:43:51.582 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] schema data cleaned 
[INFO ] 2024-03-26 12:43:51.582 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] monitor closed 
[INFO ] 2024-03-26 12:43:51.583 - [任务 7][CLAIM] - Node CLAIM[9a2abe66-52f4-4c2e-a37f-ff2ede56e0fb] close complete, cost 39 ms 
[INFO ] 2024-03-26 12:43:51.583 - [任务 7][test2] - Node test3[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] running status set to false 
[INFO ] 2024-03-26 12:43:51.614 - [任务 7][test2] - PDK connector node stopped: HazelcastTargetPdkDataNode-1d47d5cb-effc-4b87-87ab-c3d11c45a4be 
[INFO ] 2024-03-26 12:43:51.614 - [任务 7][test2] - PDK connector node released: HazelcastTargetPdkDataNode-1d47d5cb-effc-4b87-87ab-c3d11c45a4be 
[INFO ] 2024-03-26 12:43:51.614 - [任务 7][test2] - Node test3[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] schema data cleaned 
[INFO ] 2024-03-26 12:43:51.614 - [任务 7][test2] - Node test3[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] monitor closed 
[INFO ] 2024-03-26 12:43:51.817 - [任务 7][test2] - Node test3[1d47d5cb-effc-4b87-87ab-c3d11c45a4be] close complete, cost 32 ms 
[INFO ] 2024-03-26 12:43:56.322 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-26 12:43:56.322 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-03-26 12:43:56.354 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-03-26 12:43:56.354 - [任务 7] - Remove memory task client succeed, task: 任务 7[660235c455700b348d5e79d5] 
[INFO ] 2024-03-26 12:43:56.354 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[660235c455700b348d5e79d5] 
