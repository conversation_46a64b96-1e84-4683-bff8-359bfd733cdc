[INFO ] 2024-11-29 11:52:07.649 - [任务 33] - Start task milestones: 67492a90479c873ac4b7d62e(任务 33) 
[INFO ] 2024-11-29 11:52:07.838 - [任务 33] - Task initialization... 
[INFO ] 2024-11-29 11:52:07.840 - [任务 33] - No<PERSON> performs snapshot read asynchronously 
[INFO ] 2024-11-29 11:52:08.010 - [任务 33] - The engine receives 任务 33 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-29 11:52:08.014 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] start preload schema,table counts: 1 
[INFO ] 2024-11-29 11:52:08.015 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] start preload schema,table counts: 1 
[INFO ] 2024-11-29 11:52:08.026 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 11:52:08.028 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 11:52:08.771 - [任务 33][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-29 11:52:08.775 - [任务 33][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-29 11:52:08.909 - [任务 33][DB2] - Source node "DB2" read batch size: 100 
[INFO ] 2024-11-29 11:52:08.909 - [任务 33][DB2] - Source node "DB2" event queue capacity: 200 
[INFO ] 2024-11-29 11:52:08.921 - [任务 33][DB2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-29 11:52:09.113 - [任务 33][DB2] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732852329100} 
[INFO ] 2024-11-29 11:52:09.113 - [任务 33][DB2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-29 11:52:09.206 - [任务 33][DB2] - Initial sync started 
[INFO ] 2024-11-29 11:52:09.208 - [任务 33][DB2] - Starting batch read, table name: BMSQL_OORDER 
[INFO ] 2024-11-29 11:52:09.209 - [任务 33][DB2] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-11-29 11:52:09.216 - [任务 33][DB2] - batchRead, splitSql[1]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=0 
[INFO ] 2024-11-29 11:52:09.220 - [任务 33][DB2] - batchRead, splitSql[4]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=3 
[INFO ] 2024-11-29 11:52:09.221 - [任务 33][DB2] - batchRead, splitSql[2]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=1 
[INFO ] 2024-11-29 11:52:09.222 - [任务 33][DB2] - batchRead, splitSql[3]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=2 
[INFO ] 2024-11-29 11:52:09.409 - [任务 33][DB2] - Query snapshot row size completed: DB2(531eeacc-3ca5-4851-acec-e2b937be2a37) 
[INFO ] 2024-11-29 11:52:09.412 - [任务 33][DB2] - batchRead, splitSql[5]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=4 
[INFO ] 2024-11-29 11:52:09.481 - [任务 33][DB2] - batchRead, splitSql[8]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=7 
[INFO ] 2024-11-29 11:52:09.620 - [任务 33][DB2] - batchRead, splitSql[6]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=5 
[INFO ] 2024-11-29 11:52:09.623 - [任务 33][DB2] - batchRead, splitSql[7]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=6 
[INFO ] 2024-11-29 11:52:09.627 - [任务 33][DB2] - batchRead, splitSql[9]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=8 
[INFO ] 2024-11-29 11:52:09.627 - [任务 33][DB2] - batchRead, splitSql[12]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=11 
[INFO ] 2024-11-29 11:52:09.782 - [任务 33][DB2] - batchRead, splitSql[10]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=9 
[INFO ] 2024-11-29 11:52:09.782 - [任务 33][DB2] - batchRead, splitSql[13]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=12 
[INFO ] 2024-11-29 11:52:09.796 - [任务 33][DB2] - batchRead, splitSql[11]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=10 
[INFO ] 2024-11-29 11:52:09.797 - [任务 33][DB2] - batchRead, splitSql[16]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=15 
[INFO ] 2024-11-29 11:52:09.956 - [任务 33][DB2] - batchRead, splitSql[14]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=13 
[INFO ] 2024-11-29 11:52:09.956 - [任务 33][DB2] - batchRead, splitSql[17]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=16 
[INFO ] 2024-11-29 11:52:09.965 - [任务 33][DB2] - batchRead, splitSql[15]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=14 
[INFO ] 2024-11-29 11:52:09.965 - [任务 33][DB2] - batchRead, splitSql[20]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=19 
[INFO ] 2024-11-29 11:52:10.126 - [任务 33][DB2] - batchRead, splitSql[18]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=17 
[INFO ] 2024-11-29 11:52:10.127 - [任务 33][DB2] - batchRead, splitSql[19]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=18 
[INFO ] 2024-11-29 11:52:10.303 - [任务 33][DB2] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-29 11:52:10.303 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 11:52:10.303 - [任务 33][DB2] - Incremental sync starting... 
[INFO ] 2024-11-29 11:52:10.303 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 11:52:10.508 - [任务 33][DB2] - Starting stream read, table list: [BMSQL_OORDER], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732852329100} 
[WARN ] 2024-11-29 11:52:10.592 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 11:52:28.029 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] running status set to false 
[INFO ] 2024-11-29 11:52:28.030 - [任务 33][DB2] - Log Miner is shutting down... 
[INFO ] 2024-11-29 11:52:28.030 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 11:52:28.043 - [任务 33][DB2] - Stop PDK connector node failed: Unknown PDK exception occur, io.grpc.StatusRuntimeException: UNAVAILABLE: io exception | Associate id: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732852328733 
[INFO ] 2024-11-29 11:52:28.045 - [任务 33][DB2] - PDK connector node released: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732852328733 
[INFO ] 2024-11-29 11:52:28.045 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] schema data cleaned 
[INFO ] 2024-11-29 11:52:28.048 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] monitor closed 
[INFO ] 2024-11-29 11:52:28.048 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] close complete, cost 36 ms 
[INFO ] 2024-11-29 11:52:28.049 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] running status set to false 
[INFO ] 2024-11-29 11:52:28.066 - [任务 33][Dummy] - Stop connector 
[INFO ] 2024-11-29 11:52:28.086 - [任务 33][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732852328607 
[INFO ] 2024-11-29 11:52:28.087 - [任务 33][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732852328607 
[INFO ] 2024-11-29 11:52:28.087 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] schema data cleaned 
[INFO ] 2024-11-29 11:52:28.087 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] monitor closed 
[INFO ] 2024-11-29 11:52:28.136 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] close complete, cost 39 ms 
[INFO ] 2024-11-29 11:52:28.136 - [任务 33][DB2] - Incremental sync completed 
[INFO ] 2024-11-29 11:52:28.138 - [任务 33][DB2] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {} 
[ERROR] 2024-11-29 11:52:28.154 - [任务 33][DB2] - java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {} <-- Error Message -->
java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:462)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:219)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:281)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:142)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:187)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.schema.PdkTableMap.get(PdkTableMap.java:50)
	at io.tapdata.schema.PdkTableMap.get(PdkTableMap.java:13)
	at io.tapdata.common.cdc.NormalLogMiner.lambda$makeLobTables$2(NormalLogMiner.java:103)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.common.cdc.NormalLogMiner.makeLobTables(NormalLogMiner.java:102)
	at io.tapdata.common.cdc.NormalLogMiner.init(NormalLogMiner.java:61)
	at io.tapdata.connector.db2.cdc.Db2LogMiner.init(Db2LogMiner.java:49)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner2.init(Db2GrpcLogMiner2.java:69)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.init(Db2CdcRunner.java:23)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:287)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:173)
	... 31 more
Caused by: java.lang.RuntimeException: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:462)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 33 more

[INFO ] 2024-11-29 11:52:31.245 - [任务 33] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-29 11:52:31.249 - [任务 33] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1434c27 
[INFO ] 2024-11-29 11:52:31.251 - [任务 33] - Stop task milestones: 67492a90479c873ac4b7d62e(任务 33)  
[INFO ] 2024-11-29 11:52:31.379 - [任务 33] - Stopped task aspect(s) 
[INFO ] 2024-11-29 11:52:31.380 - [任务 33] - Snapshot order controller have been removed 
[INFO ] 2024-11-29 11:52:31.404 - [任务 33] - Remove memory task client succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 11:52:31.406 - [任务 33] - Destroy memory task client cache succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 11:52:38.483 - [任务 33] - Start task milestones: 67492a90479c873ac4b7d62e(任务 33) 
[INFO ] 2024-11-29 11:52:38.483 - [任务 33] - Task initialization... 
[INFO ] 2024-11-29 11:52:38.666 - [任务 33] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-29 11:52:38.796 - [任务 33] - The engine receives 任务 33 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-29 11:52:38.796 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] start preload schema,table counts: 1 
[INFO ] 2024-11-29 11:52:38.796 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] start preload schema,table counts: 1 
[INFO ] 2024-11-29 11:52:38.796 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 11:52:38.796 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 11:52:39.559 - [任务 33][DB2] - Source node "DB2" read batch size: 100 
[INFO ] 2024-11-29 11:52:39.563 - [任务 33][DB2] - Source node "DB2" event queue capacity: 200 
[INFO ] 2024-11-29 11:52:39.564 - [任务 33][DB2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-29 11:52:39.597 - [任务 33][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-29 11:52:39.597 - [任务 33][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-29 11:52:39.691 - [任务 33][DB2] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732852359689} 
[INFO ] 2024-11-29 11:52:39.691 - [任务 33][DB2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-29 11:52:39.769 - [任务 33][DB2] - Initial sync started 
[INFO ] 2024-11-29 11:52:39.769 - [任务 33][DB2] - Starting batch read, table name: BMSQL_OORDER 
[INFO ] 2024-11-29 11:52:39.770 - [任务 33][DB2] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-11-29 11:52:39.776 - [任务 33][DB2] - batchRead, splitSql[1]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=0 
[INFO ] 2024-11-29 11:52:39.776 - [任务 33][DB2] - batchRead, splitSql[3]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=2 
[INFO ] 2024-11-29 11:52:39.776 - [任务 33][DB2] - batchRead, splitSql[4]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=3 
[INFO ] 2024-11-29 11:52:39.776 - [任务 33][DB2] - batchRead, splitSql[2]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=1 
[INFO ] 2024-11-29 11:52:39.982 - [任务 33][DB2] - Query snapshot row size completed: DB2(531eeacc-3ca5-4851-acec-e2b937be2a37) 
[INFO ] 2024-11-29 11:52:40.018 - [任务 33][DB2] - batchRead, splitSql[5]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=4 
[INFO ] 2024-11-29 11:52:40.019 - [任务 33][DB2] - batchRead, splitSql[7]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=6 
[INFO ] 2024-11-29 11:52:40.193 - [任务 33][DB2] - batchRead, splitSql[8]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=7 
[INFO ] 2024-11-29 11:52:40.193 - [任务 33][DB2] - batchRead, splitSql[6]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=5 
[INFO ] 2024-11-29 11:52:40.214 - [任务 33][DB2] - batchRead, splitSql[9]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=8 
[INFO ] 2024-11-29 11:52:40.218 - [任务 33][DB2] - batchRead, splitSql[11]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=10 
[INFO ] 2024-11-29 11:52:40.264 - [任务 33][DB2] - batchRead, splitSql[12]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=11 
[INFO ] 2024-11-29 11:52:40.385 - [任务 33][DB2] - batchRead, splitSql[10]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=9 
[INFO ] 2024-11-29 11:52:40.388 - [任务 33][DB2] - batchRead, splitSql[15]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=14 
[INFO ] 2024-11-29 11:52:40.388 - [任务 33][DB2] - batchRead, splitSql[13]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=12 
[INFO ] 2024-11-29 11:52:40.542 - [任务 33][DB2] - batchRead, splitSql[16]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=15 
[INFO ] 2024-11-29 11:52:40.542 - [任务 33][DB2] - batchRead, splitSql[14]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=13 
[INFO ] 2024-11-29 11:52:40.542 - [任务 33][DB2] - batchRead, splitSql[17]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=16 
[INFO ] 2024-11-29 11:52:40.562 - [任务 33][DB2] - batchRead, splitSql[19]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=18 
[INFO ] 2024-11-29 11:52:40.731 - [任务 33][DB2] - batchRead, splitSql[20]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=19 
[INFO ] 2024-11-29 11:52:40.731 - [任务 33][DB2] - batchRead, splitSql[18]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=17 
[INFO ] 2024-11-29 11:52:56.982 - [任务 33][DB2] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-29 11:52:56.992 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 11:52:56.993 - [任务 33][DB2] - Incremental sync starting... 
[INFO ] 2024-11-29 11:52:56.993 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 11:52:56.995 - [任务 33][DB2] - Starting stream read, table list: [BMSQL_OORDER], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732852359689} 
[WARN ] 2024-11-29 11:52:57.402 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 11:53:07.327 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] running status set to false 
[INFO ] 2024-11-29 11:53:07.328 - [任务 33][DB2] - Log Miner is shutting down... 
[INFO ] 2024-11-29 11:53:07.328 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 11:53:07.332 - [任务 33][DB2] - Stop PDK connector node failed: Unknown PDK exception occur, io.grpc.StatusRuntimeException: UNAVAILABLE: io exception | Associate id: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732852359378 
[INFO ] 2024-11-29 11:53:07.332 - [任务 33][DB2] - PDK connector node released: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732852359378 
[INFO ] 2024-11-29 11:53:07.333 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] schema data cleaned 
[INFO ] 2024-11-29 11:53:07.340 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] monitor closed 
[INFO ] 2024-11-29 11:53:07.340 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] close complete, cost 17 ms 
[INFO ] 2024-11-29 11:53:07.340 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] running status set to false 
[INFO ] 2024-11-29 11:53:07.353 - [任务 33][Dummy] - Stop connector 
[INFO ] 2024-11-29 11:53:07.373 - [任务 33][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732852359486 
[INFO ] 2024-11-29 11:53:07.373 - [任务 33][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732852359486 
[INFO ] 2024-11-29 11:53:07.373 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] schema data cleaned 
[INFO ] 2024-11-29 11:53:07.373 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] monitor closed 
[INFO ] 2024-11-29 11:53:07.440 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] close complete, cost 35 ms 
[INFO ] 2024-11-29 11:53:07.441 - [任务 33][DB2] - Incremental sync completed 
[INFO ] 2024-11-29 11:53:07.441 - [任务 33][DB2] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {} 
[ERROR] 2024-11-29 11:53:07.646 - [任务 33][DB2] - java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {} <-- Error Message -->
java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:462)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:219)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:281)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:142)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:187)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.schema.PdkTableMap.get(PdkTableMap.java:50)
	at io.tapdata.schema.PdkTableMap.get(PdkTableMap.java:13)
	at io.tapdata.common.cdc.NormalLogMiner.lambda$makeLobTables$2(NormalLogMiner.java:103)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.common.cdc.NormalLogMiner.makeLobTables(NormalLogMiner.java:102)
	at io.tapdata.common.cdc.NormalLogMiner.init(NormalLogMiner.java:61)
	at io.tapdata.connector.db2.cdc.Db2LogMiner.init(Db2LogMiner.java:49)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner2.init(Db2GrpcLogMiner2.java:69)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.init(Db2CdcRunner.java:23)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:287)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:173)
	... 31 more
Caused by: java.lang.RuntimeException: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:462)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 33 more

[INFO ] 2024-11-29 11:53:11.939 - [任务 33] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-29 11:53:11.939 - [任务 33] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@38aeb30f 
[INFO ] 2024-11-29 11:53:12.065 - [任务 33] - Stop task milestones: 67492a90479c873ac4b7d62e(任务 33)  
[INFO ] 2024-11-29 11:53:12.065 - [任务 33] - Stopped task aspect(s) 
[INFO ] 2024-11-29 11:53:12.065 - [任务 33] - Snapshot order controller have been removed 
[INFO ] 2024-11-29 11:53:12.084 - [任务 33] - Remove memory task client succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 11:53:12.085 - [任务 33] - Destroy memory task client cache succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 11:53:18.162 - [任务 33] - Start task milestones: 67492a90479c873ac4b7d62e(任务 33) 
[INFO ] 2024-11-29 11:53:18.724 - [任务 33] - Task initialization... 
[INFO ] 2024-11-29 11:53:18.917 - [任务 33] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-29 11:53:19.055 - [任务 33] - The engine receives 任务 33 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-29 11:53:19.056 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] start preload schema,table counts: 1 
[INFO ] 2024-11-29 11:53:19.056 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] start preload schema,table counts: 1 
[INFO ] 2024-11-29 11:53:19.056 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 11:53:19.056 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 11:53:19.842 - [任务 33][DB2] - Source node "DB2" read batch size: 100 
[INFO ] 2024-11-29 11:53:19.843 - [任务 33][DB2] - Source node "DB2" event queue capacity: 200 
[INFO ] 2024-11-29 11:53:19.843 - [任务 33][DB2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-29 11:53:19.922 - [任务 33][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-29 11:53:19.924 - [任务 33][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-29 11:53:19.969 - [任务 33][DB2] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732852399964} 
[INFO ] 2024-11-29 11:53:19.969 - [任务 33][DB2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-29 11:53:20.059 - [任务 33][DB2] - Initial sync started 
[INFO ] 2024-11-29 11:53:20.061 - [任务 33][DB2] - Starting batch read, table name: BMSQL_OORDER 
[INFO ] 2024-11-29 11:53:20.061 - [任务 33][DB2] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-11-29 11:53:20.062 - [任务 33][DB2] - batchRead, splitSql[2]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=1 
[INFO ] 2024-11-29 11:53:20.065 - [任务 33][DB2] - batchRead, splitSql[1]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=0 
[INFO ] 2024-11-29 11:53:20.065 - [任务 33][DB2] - batchRead, splitSql[3]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=2 
[INFO ] 2024-11-29 11:53:20.065 - [任务 33][DB2] - batchRead, splitSql[4]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=3 
[INFO ] 2024-11-29 11:53:20.270 - [任务 33][DB2] - Query snapshot row size completed: DB2(531eeacc-3ca5-4851-acec-e2b937be2a37) 
[INFO ] 2024-11-29 11:53:20.342 - [任务 33][DB2] - batchRead, splitSql[5]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=4 
[INFO ] 2024-11-29 11:53:20.459 - [任务 33][DB2] - batchRead, splitSql[6]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=5 
[INFO ] 2024-11-29 11:53:20.459 - [任务 33][DB2] - batchRead, splitSql[7]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=6 
[INFO ] 2024-11-29 11:53:20.560 - [任务 33][DB2] - batchRead, splitSql[8]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=7 
[INFO ] 2024-11-29 11:53:20.560 - [任务 33][DB2] - batchRead, splitSql[9]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=8 
[INFO ] 2024-11-29 11:53:20.615 - [任务 33][DB2] - batchRead, splitSql[10]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=9 
[INFO ] 2024-11-29 11:53:20.616 - [任务 33][DB2] - batchRead, splitSql[11]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=10 
[INFO ] 2024-11-29 11:53:20.716 - [任务 33][DB2] - batchRead, splitSql[12]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=11 
[INFO ] 2024-11-29 11:53:20.716 - [任务 33][DB2] - batchRead, splitSql[13]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=12 
[INFO ] 2024-11-29 11:53:20.781 - [任务 33][DB2] - batchRead, splitSql[14]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=13 
[INFO ] 2024-11-29 11:53:20.781 - [任务 33][DB2] - batchRead, splitSql[15]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=14 
[INFO ] 2024-11-29 11:53:20.901 - [任务 33][DB2] - batchRead, splitSql[16]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=15 
[INFO ] 2024-11-29 11:53:20.901 - [任务 33][DB2] - batchRead, splitSql[17]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=16 
[INFO ] 2024-11-29 11:53:20.949 - [任务 33][DB2] - batchRead, splitSql[18]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=17 
[INFO ] 2024-11-29 11:53:20.949 - [任务 33][DB2] - batchRead, splitSql[19]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=18 
[INFO ] 2024-11-29 11:53:21.151 - [任务 33][DB2] - batchRead, splitSql[20]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=19 
[INFO ] 2024-11-29 11:53:34.111 - [任务 33][DB2] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-29 11:53:34.112 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 11:53:34.112 - [任务 33][DB2] - Incremental sync starting... 
[INFO ] 2024-11-29 11:53:34.112 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 11:53:34.324 - [任务 33][DB2] - Starting stream read, table list: [BMSQL_OORDER], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732852399964} 
[WARN ] 2024-11-29 11:53:34.373 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 11:54:34.447 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 11:54:34.649 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 11:55:28.686 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] running status set to false 
[INFO ] 2024-11-29 11:55:28.686 - [任务 33][DB2] - Log Miner is shutting down... 
[INFO ] 2024-11-29 11:55:28.687 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 11:55:28.692 - [任务 33][DB2] - Stop PDK connector node failed: Unknown PDK exception occur, io.grpc.StatusRuntimeException: UNAVAILABLE: io exception | Associate id: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732852399701 
[INFO ] 2024-11-29 11:55:28.692 - [任务 33][DB2] - PDK connector node released: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732852399701 
[INFO ] 2024-11-29 11:55:28.693 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] schema data cleaned 
[INFO ] 2024-11-29 11:55:28.693 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] monitor closed 
[INFO ] 2024-11-29 11:55:28.697 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] close complete, cost 28 ms 
[INFO ] 2024-11-29 11:55:28.697 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] running status set to false 
[INFO ] 2024-11-29 11:55:28.731 - [任务 33][Dummy] - Stop connector 
[INFO ] 2024-11-29 11:55:28.732 - [任务 33][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732852399814 
[INFO ] 2024-11-29 11:55:28.732 - [任务 33][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732852399814 
[INFO ] 2024-11-29 11:55:28.733 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] schema data cleaned 
[INFO ] 2024-11-29 11:55:28.733 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] monitor closed 
[INFO ] 2024-11-29 11:55:28.770 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] close complete, cost 37 ms 
[INFO ] 2024-11-29 11:55:28.771 - [任务 33][DB2] - Incremental sync completed 
[INFO ] 2024-11-29 11:55:28.771 - [任务 33][DB2] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {} 
[ERROR] 2024-11-29 11:55:28.782 - [任务 33][DB2] - java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {} <-- Error Message -->
java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:462)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:219)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:281)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:142)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:187)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.schema.PdkTableMap.get(PdkTableMap.java:50)
	at io.tapdata.schema.PdkTableMap.get(PdkTableMap.java:13)
	at io.tapdata.common.cdc.NormalLogMiner.lambda$makeLobTables$2(NormalLogMiner.java:103)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.common.cdc.NormalLogMiner.makeLobTables(NormalLogMiner.java:102)
	at io.tapdata.common.cdc.NormalLogMiner.init(NormalLogMiner.java:61)
	at io.tapdata.connector.db2.cdc.Db2LogMiner.init(Db2LogMiner.java:49)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner2.init(Db2GrpcLogMiner2.java:69)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.init(Db2CdcRunner.java:23)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:287)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:173)
	... 31 more
Caused by: java.lang.RuntimeException: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:462)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 33 more

[INFO ] 2024-11-29 11:55:29.097 - [任务 33] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-29 11:55:29.098 - [任务 33] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@c9d266f 
[INFO ] 2024-11-29 11:55:29.224 - [任务 33] - Stop task milestones: 67492a90479c873ac4b7d62e(任务 33)  
[INFO ] 2024-11-29 11:55:29.224 - [任务 33] - Stopped task aspect(s) 
[INFO ] 2024-11-29 11:55:29.224 - [任务 33] - Snapshot order controller have been removed 
[INFO ] 2024-11-29 11:55:29.252 - [任务 33] - Remove memory task client succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 11:55:29.457 - [任务 33] - Destroy memory task client cache succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 12:10:32.955 - [任务 33] - Start task milestones: 67492a90479c873ac4b7d62e(任务 33) 
[INFO ] 2024-11-29 12:10:33.127 - [任务 33] - Task initialization... 
[INFO ] 2024-11-29 12:10:33.128 - [任务 33] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-29 12:10:33.259 - [任务 33] - The engine receives 任务 33 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-29 12:10:33.259 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] start preload schema,table counts: 1 
[INFO ] 2024-11-29 12:10:33.259 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] start preload schema,table counts: 1 
[INFO ] 2024-11-29 12:10:33.259 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 12:10:33.260 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] preload schema finished, cost 1 ms 
[INFO ] 2024-11-29 12:10:33.887 - [任务 33][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-29 12:10:33.899 - [任务 33][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-29 12:10:34.098 - [任务 33][DB2] - Source node "DB2" read batch size: 100 
[INFO ] 2024-11-29 12:10:34.101 - [任务 33][DB2] - Source node "DB2" event queue capacity: 200 
[INFO ] 2024-11-29 12:10:34.102 - [任务 33][DB2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-29 12:10:34.220 - [任务 33][DB2] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732853434194} 
[INFO ] 2024-11-29 12:10:34.220 - [任务 33][DB2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-29 12:10:34.309 - [任务 33][DB2] - Initial sync started 
[INFO ] 2024-11-29 12:10:34.310 - [任务 33][DB2] - Starting batch read, table name: BMSQL_OORDER 
[INFO ] 2024-11-29 12:10:34.313 - [任务 33][DB2] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-11-29 12:10:34.315 - [任务 33][DB2] - batchRead, splitSql[4]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=3 
[INFO ] 2024-11-29 12:10:34.315 - [任务 33][DB2] - batchRead, splitSql[2]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=1 
[INFO ] 2024-11-29 12:10:34.315 - [任务 33][DB2] - batchRead, splitSql[3]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=2 
[INFO ] 2024-11-29 12:10:34.316 - [任务 33][DB2] - batchRead, splitSql[1]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=0 
[INFO ] 2024-11-29 12:10:34.525 - [任务 33][DB2] - Query snapshot row size completed: DB2(531eeacc-3ca5-4851-acec-e2b937be2a37) 
[INFO ] 2024-11-29 12:10:34.549 - [任务 33][DB2] - batchRead, splitSql[8]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=7 
[INFO ] 2024-11-29 12:10:34.590 - [任务 33][DB2] - batchRead, splitSql[5]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=4 
[INFO ] 2024-11-29 12:10:34.590 - [任务 33][DB2] - batchRead, splitSql[7]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=6 
[INFO ] 2024-11-29 12:10:34.593 - [任务 33][DB2] - batchRead, splitSql[6]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=5 
[INFO ] 2024-11-29 12:10:34.749 - [任务 33][DB2] - batchRead, splitSql[12]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=11 
[INFO ] 2024-11-29 12:10:34.749 - [任务 33][DB2] - batchRead, splitSql[10]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=9 
[INFO ] 2024-11-29 12:10:34.772 - [任务 33][DB2] - batchRead, splitSql[11]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=10 
[INFO ] 2024-11-29 12:10:34.772 - [任务 33][DB2] - batchRead, splitSql[9]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=8 
[INFO ] 2024-11-29 12:10:34.903 - [任务 33][DB2] - batchRead, splitSql[16]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=15 
[INFO ] 2024-11-29 12:10:34.903 - [任务 33][DB2] - batchRead, splitSql[14]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=13 
[INFO ] 2024-11-29 12:10:34.984 - [任务 33][DB2] - batchRead, splitSql[15]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=14 
[INFO ] 2024-11-29 12:10:34.984 - [任务 33][DB2] - batchRead, splitSql[13]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=12 
[INFO ] 2024-11-29 12:10:35.085 - [任务 33][DB2] - batchRead, splitSql[18]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=17 
[INFO ] 2024-11-29 12:10:35.085 - [任务 33][DB2] - batchRead, splitSql[20]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=19 
[INFO ] 2024-11-29 12:10:35.085 - [任务 33][DB2] - batchRead, splitSql[19]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=18 
[INFO ] 2024-11-29 12:10:35.097 - [任务 33][DB2] - batchRead, splitSql[17]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=16 
[INFO ] 2024-11-29 12:10:40.981 - [任务 33][DB2] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-29 12:10:40.993 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 12:10:40.995 - [任务 33][DB2] - Incremental sync starting... 
[INFO ] 2024-11-29 12:10:40.995 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 12:10:40.995 - [任务 33][DB2] - Starting stream read, table list: [BMSQL_OORDER], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732853434194} 
[WARN ] 2024-11-29 12:10:41.225 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 12:10:53.849 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] running status set to false 
[INFO ] 2024-11-29 12:10:53.850 - [任务 33][DB2] - Log Miner is shutting down... 
[INFO ] 2024-11-29 12:10:53.850 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 12:10:53.859 - [任务 33][DB2] - Stop PDK connector node failed: Unknown PDK exception occur, io.grpc.StatusRuntimeException: UNAVAILABLE: io exception | Associate id: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732853433950 
[INFO ] 2024-11-29 12:10:53.859 - [任务 33][DB2] - PDK connector node released: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732853433950 
[INFO ] 2024-11-29 12:10:53.861 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] schema data cleaned 
[INFO ] 2024-11-29 12:10:53.861 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] monitor closed 
[INFO ] 2024-11-29 12:10:53.863 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] close complete, cost 19 ms 
[INFO ] 2024-11-29 12:10:53.863 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] running status set to false 
[INFO ] 2024-11-29 12:10:53.879 - [任务 33][Dummy] - Stop connector 
[INFO ] 2024-11-29 12:10:53.888 - [任务 33][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732853433790 
[INFO ] 2024-11-29 12:10:53.889 - [任务 33][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732853433790 
[INFO ] 2024-11-29 12:10:53.889 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] schema data cleaned 
[INFO ] 2024-11-29 12:10:53.892 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] monitor closed 
[INFO ] 2024-11-29 12:10:53.892 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] close complete, cost 27 ms 
[INFO ] 2024-11-29 12:10:53.912 - [任务 33][DB2] - Incremental sync completed 
[INFO ] 2024-11-29 12:10:53.912 - [任务 33][DB2] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {} 
[ERROR] 2024-11-29 12:10:53.923 - [任务 33][DB2] - java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {} <-- Error Message -->
java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:462)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:219)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:281)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:142)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:187)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.schema.PdkTableMap.get(PdkTableMap.java:50)
	at io.tapdata.schema.PdkTableMap.get(PdkTableMap.java:13)
	at io.tapdata.common.cdc.NormalLogMiner.lambda$makeLobTables$2(NormalLogMiner.java:103)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.common.cdc.NormalLogMiner.makeLobTables(NormalLogMiner.java:102)
	at io.tapdata.common.cdc.NormalLogMiner.init(NormalLogMiner.java:61)
	at io.tapdata.connector.db2.cdc.Db2LogMiner.init(Db2LogMiner.java:49)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner2.init(Db2GrpcLogMiner2.java:69)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.init(Db2CdcRunner.java:23)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:287)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:173)
	... 31 more
Caused by: java.lang.RuntimeException: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:462)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 33 more

[INFO ] 2024-11-29 12:10:56.003 - [任务 33] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-29 12:10:56.007 - [任务 33] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@47ab0200 
[INFO ] 2024-11-29 12:10:56.007 - [任务 33] - Stop task milestones: 67492a90479c873ac4b7d62e(任务 33)  
[INFO ] 2024-11-29 12:10:56.165 - [任务 33] - Stopped task aspect(s) 
[INFO ] 2024-11-29 12:10:56.167 - [任务 33] - Snapshot order controller have been removed 
[INFO ] 2024-11-29 12:10:56.186 - [任务 33] - Remove memory task client succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 12:10:56.189 - [任务 33] - Destroy memory task client cache succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 14:09:40.829 - [任务 33] - Start task milestones: 67492a90479c873ac4b7d62e(任务 33) 
[INFO ] 2024-11-29 14:09:40.832 - [任务 33] - Task initialization... 
[INFO ] 2024-11-29 14:09:41.246 - [任务 33] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-29 14:09:41.372 - [任务 33] - The engine receives 任务 33 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-29 14:09:41.372 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] start preload schema,table counts: 1 
[INFO ] 2024-11-29 14:09:41.374 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] start preload schema,table counts: 1 
[INFO ] 2024-11-29 14:09:41.376 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 14:09:41.377 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 14:09:42.130 - [任务 33][DB2] - Source node "DB2" read batch size: 100 
[INFO ] 2024-11-29 14:09:42.132 - [任务 33][DB2] - Source node "DB2" event queue capacity: 200 
[INFO ] 2024-11-29 14:09:42.132 - [任务 33][DB2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-29 14:09:42.151 - [任务 33][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-29 14:09:42.153 - [任务 33][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-29 14:09:42.203 - [任务 33][DB2] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732860582199} 
[INFO ] 2024-11-29 14:09:42.203 - [任务 33][DB2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-29 14:09:42.295 - [任务 33][DB2] - Initial sync started 
[INFO ] 2024-11-29 14:09:42.295 - [任务 33][DB2] - Starting batch read, table name: BMSQL_OORDER 
[INFO ] 2024-11-29 14:09:42.310 - [任务 33][DB2] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-11-29 14:09:42.311 - [任务 33][DB2] - batchRead, splitSql[2]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=1 
[INFO ] 2024-11-29 14:09:42.311 - [任务 33][DB2] - batchRead, splitSql[1]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=0 
[INFO ] 2024-11-29 14:09:42.311 - [任务 33][DB2] - batchRead, splitSql[4]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=3 
[INFO ] 2024-11-29 14:09:42.312 - [任务 33][DB2] - batchRead, splitSql[3]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=2 
[INFO ] 2024-11-29 14:09:42.519 - [任务 33][DB2] - Query snapshot row size completed: DB2(531eeacc-3ca5-4851-acec-e2b937be2a37) 
[INFO ] 2024-11-29 14:09:42.668 - [任务 33][DB2] - batchRead, splitSql[5]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=4 
[INFO ] 2024-11-29 14:09:42.668 - [任务 33][DB2] - batchRead, splitSql[6]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=5 
[INFO ] 2024-11-29 14:09:42.670 - [任务 33][DB2] - batchRead, splitSql[8]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=7 
[INFO ] 2024-11-29 14:09:42.675 - [任务 33][DB2] - batchRead, splitSql[7]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=6 
[INFO ] 2024-11-29 14:09:42.840 - [任务 33][DB2] - batchRead, splitSql[12]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=11 
[INFO ] 2024-11-29 14:09:42.840 - [任务 33][DB2] - batchRead, splitSql[11]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=10 
[INFO ] 2024-11-29 14:09:42.878 - [任务 33][DB2] - batchRead, splitSql[9]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=8 
[INFO ] 2024-11-29 14:09:42.878 - [任务 33][DB2] - batchRead, splitSql[10]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=9 
[INFO ] 2024-11-29 14:09:42.986 - [任务 33][DB2] - batchRead, splitSql[16]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=15 
[INFO ] 2024-11-29 14:09:42.986 - [任务 33][DB2] - batchRead, splitSql[15]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=14 
[INFO ] 2024-11-29 14:09:43.067 - [任务 33][DB2] - batchRead, splitSql[13]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=12 
[INFO ] 2024-11-29 14:09:43.067 - [任务 33][DB2] - batchRead, splitSql[14]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=13 
[INFO ] 2024-11-29 14:09:43.188 - [任务 33][DB2] - batchRead, splitSql[17]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=16 
[INFO ] 2024-11-29 14:09:43.188 - [任务 33][DB2] - batchRead, splitSql[20]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=19 
[INFO ] 2024-11-29 14:09:43.229 - [任务 33][DB2] - batchRead, splitSql[19]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=18 
[INFO ] 2024-11-29 14:09:43.229 - [任务 33][DB2] - batchRead, splitSql[18]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=17 
[INFO ] 2024-11-29 14:09:49.380 - [任务 33][DB2] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-29 14:09:49.381 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 14:09:49.386 - [任务 33][DB2] - Incremental sync starting... 
[INFO ] 2024-11-29 14:09:49.387 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 14:09:49.596 - [任务 33][DB2] - Starting stream read, table list: [BMSQL_OORDER], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732860582199} 
[WARN ] 2024-11-29 14:09:49.597 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:10:49.668 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:10:49.880 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:11:49.984 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:11:49.985 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:12:50.094 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:12:50.301 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:13:50.156 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:13:50.357 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:14:50.393 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:14:50.516 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:15:50.668 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:15:50.670 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:16:20.528 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] running status set to false 
[INFO ] 2024-11-29 14:16:20.530 - [任务 33][DB2] - Log Miner is shutting down... 
[INFO ] 2024-11-29 14:16:20.531 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:16:20.533 - [任务 33][DB2] - Stop PDK connector node failed: Unknown PDK exception occur, io.grpc.StatusRuntimeException: UNAVAILABLE: io exception | Associate id: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732860581963 
[INFO ] 2024-11-29 14:16:20.534 - [任务 33][DB2] - PDK connector node released: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732860581963 
[INFO ] 2024-11-29 14:16:20.541 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] schema data cleaned 
[INFO ] 2024-11-29 14:16:20.543 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] monitor closed 
[INFO ] 2024-11-29 14:16:20.551 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] close complete, cost 25 ms 
[INFO ] 2024-11-29 14:16:20.551 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] running status set to false 
[INFO ] 2024-11-29 14:16:20.583 - [任务 33][Dummy] - Stop connector 
[INFO ] 2024-11-29 14:16:20.583 - [任务 33][DB2] - Incremental sync completed 
[INFO ] 2024-11-29 14:16:20.586 - [任务 33][DB2] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {} 
[INFO ] 2024-11-29 14:16:20.587 - [任务 33][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732860582042 
[INFO ] 2024-11-29 14:16:20.592 - [任务 33][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732860582042 
[INFO ] 2024-11-29 14:16:20.597 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] schema data cleaned 
[INFO ] 2024-11-29 14:16:20.598 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] monitor closed 
[INFO ] 2024-11-29 14:16:20.612 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] close complete, cost 48 ms 
[ERROR] 2024-11-29 14:16:20.614 - [任务 33][DB2] - java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {} <-- Error Message -->
java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:462)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:219)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:281)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:142)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:187)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.schema.PdkTableMap.get(PdkTableMap.java:50)
	at io.tapdata.schema.PdkTableMap.get(PdkTableMap.java:13)
	at io.tapdata.common.cdc.NormalLogMiner.lambda$makeLobTables$2(NormalLogMiner.java:103)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.common.cdc.NormalLogMiner.makeLobTables(NormalLogMiner.java:102)
	at io.tapdata.common.cdc.NormalLogMiner.init(NormalLogMiner.java:61)
	at io.tapdata.connector.db2.cdc.Db2LogMiner.init(Db2LogMiner.java:49)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner2.init(Db2GrpcLogMiner2.java:69)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.init(Db2CdcRunner.java:23)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:287)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:173)
	... 31 more
Caused by: java.lang.RuntimeException: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:462)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 33 more

[INFO ] 2024-11-29 14:16:24.657 - [任务 33] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-29 14:16:24.659 - [任务 33] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5f06c79d 
[INFO ] 2024-11-29 14:16:24.824 - [任务 33] - Stop task milestones: 67492a90479c873ac4b7d62e(任务 33)  
[INFO ] 2024-11-29 14:16:24.826 - [任务 33] - Stopped task aspect(s) 
[INFO ] 2024-11-29 14:16:24.828 - [任务 33] - Snapshot order controller have been removed 
[INFO ] 2024-11-29 14:16:24.943 - [任务 33] - Remove memory task client succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 14:16:24.948 - [任务 33] - Destroy memory task client cache succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 14:16:40.950 - [任务 33] - Start task milestones: 67492a90479c873ac4b7d62e(任务 33) 
[INFO ] 2024-11-29 14:16:40.955 - [任务 33] - Task initialization... 
[INFO ] 2024-11-29 14:16:41.168 - [任务 33] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-29 14:16:41.321 - [任务 33] - The engine receives 任务 33 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-29 14:16:41.321 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] start preload schema,table counts: 1 
[INFO ] 2024-11-29 14:16:41.322 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] start preload schema,table counts: 1 
[INFO ] 2024-11-29 14:16:41.322 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 14:16:41.322 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 14:16:42.142 - [任务 33][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-29 14:16:42.145 - [任务 33][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-29 14:16:42.270 - [任务 33][DB2] - Source node "DB2" read batch size: 100 
[INFO ] 2024-11-29 14:16:42.271 - [任务 33][DB2] - Source node "DB2" event queue capacity: 200 
[INFO ] 2024-11-29 14:16:42.272 - [任务 33][DB2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-29 14:16:42.371 - [任务 33][DB2] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732861002362} 
[INFO ] 2024-11-29 14:16:42.372 - [任务 33][DB2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-29 14:16:42.470 - [任务 33][DB2] - Initial sync started 
[INFO ] 2024-11-29 14:16:42.470 - [任务 33][DB2] - Starting batch read, table name: BMSQL_OORDER 
[INFO ] 2024-11-29 14:16:47.864 - [任务 33][DB2] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-11-29 14:17:59.485 - [任务 33][DB2] - batchRead, splitSql[3]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=2 
[INFO ] 2024-11-29 14:17:59.488 - [任务 33][DB2] - Query snapshot row size completed: DB2(531eeacc-3ca5-4851-acec-e2b937be2a37) 
[INFO ] 2024-11-29 14:17:59.488 - [任务 33][DB2] - batchRead, splitSql[2]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=1 
[INFO ] 2024-11-29 14:17:59.488 - [任务 33][DB2] - batchRead, splitSql[1]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=0 
[INFO ] 2024-11-29 14:17:59.489 - [任务 33][DB2] - batchRead, splitSql[4]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=3 
[INFO ] 2024-11-29 14:18:02.672 - [任务 33][DB2] - batchRead, splitSql[7]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=6 
[INFO ] 2024-11-29 14:18:02.699 - [任务 33][DB2] - batchRead, splitSql[5]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=4 
[INFO ] 2024-11-29 14:18:02.699 - [任务 33][DB2] - batchRead, splitSql[8]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=7 
[INFO ] 2024-11-29 14:18:02.699 - [任务 33][DB2] - batchRead, splitSql[6]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=5 
[INFO ] 2024-11-29 14:18:04.388 - [任务 33][DB2] - batchRead, splitSql[11]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=10 
[INFO ] 2024-11-29 14:18:04.390 - [任务 33][DB2] - batchRead, splitSql[9]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=8 
[INFO ] 2024-11-29 14:18:04.391 - [任务 33][DB2] - batchRead, splitSql[10]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=9 
[INFO ] 2024-11-29 14:18:04.391 - [任务 33][DB2] - batchRead, splitSql[12]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=11 
[INFO ] 2024-11-29 14:18:59.377 - [任务 33][DB2] - batchRead, splitSql[14]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=13 
[INFO ] 2024-11-29 14:18:59.379 - [任务 33][DB2] - batchRead, splitSql[13]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=12 
[INFO ] 2024-11-29 14:18:59.379 - [任务 33][DB2] - batchRead, splitSql[16]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=15 
[INFO ] 2024-11-29 14:18:59.380 - [任务 33][DB2] - batchRead, splitSql[15]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=14 
[INFO ] 2024-11-29 14:18:59.564 - [任务 33][DB2] - batchRead, splitSql[17]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=16 
[INFO ] 2024-11-29 14:18:59.569 - [任务 33][DB2] - batchRead, splitSql[18]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=17 
[INFO ] 2024-11-29 14:18:59.622 - [任务 33][DB2] - batchRead, splitSql[20]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=19 
[INFO ] 2024-11-29 14:18:59.622 - [任务 33][DB2] - batchRead, splitSql[19]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=18 
[INFO ] 2024-11-29 14:18:59.804 - [任务 33][DB2] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-29 14:18:59.807 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 14:18:59.808 - [任务 33][DB2] - Incremental sync starting... 
[INFO ] 2024-11-29 14:18:59.808 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 14:19:00.010 - [任务 33][DB2] - Starting stream read, table list: [BMSQL_OORDER], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732861002362} 
[WARN ] 2024-11-29 14:19:00.052 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:20:00.307 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:20:00.512 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:21:00.513 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:21:00.513 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:22:00.685 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:22:00.890 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:23:00.930 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:23:00.932 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:24:00.968 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:24:01.174 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:25:01.284 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:25:01.285 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:26:01.445 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:26:01.652 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 8 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:27:01.485 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:27:01.698 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 7 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:28:01.739 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:28:01.945 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 6 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:29:01.889 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:29:02.082 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 5 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:30:02.189 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:30:02.398 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 4 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:31:02.303 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:31:02.513 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 3 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:32:02.558 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:32:02.706 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 2 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:33:03.122 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:33:03.556 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 1 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:34:03.622 - [任务 33][DB2] - Log Miner is shutting down... 
[INFO ] 2024-11-29 14:34:03.691 - [任务 33][DB2] - Incremental sync completed 
[INFO ] 2024-11-29 14:34:03.705 - [任务 33][DB2] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, io.grpc.StatusRuntimeException: UNAVAILABLE: io exception 
[ERROR] 2024-11-29 14:34:03.707 - [任务 33][DB2] - io.grpc.StatusRuntimeException: UNAVAILABLE: io exception <-- Error Message -->
io.grpc.StatusRuntimeException: UNAVAILABLE: io exception

<-- Simple Stack Trace -->
Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...

<-- Full Stack Trace -->
io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at io.grpc.stub.ClientCalls.toStatusRuntimeException(ClientCalls.java:262)
	at io.grpc.stub.ClientCalls.getUnchecked(ClientCalls.java:243)
	at io.grpc.stub.ClientCalls.blockingUnaryCall(ClientCalls.java:156)
	at io.tapdata.data.db2.DB2ReadLogServerGrpc$DB2ReadLogServerBlockingStub.createReadLogTask(DB2ReadLogServerGrpc.java:648)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner2.startMiner(Db2GrpcLogMiner2.java:156)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:294)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 18 more
Caused by: java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.grpc.netty.shaded.io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.grpc.netty.shaded.io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 more

[INFO ] 2024-11-29 14:34:03.723 - [任务 33][DB2] - Job suspend in error handle 
[INFO ] 2024-11-29 14:34:03.725 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] running status set to false 
[INFO ] 2024-11-29 14:34:03.725 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:34:03.731 - [任务 33][DB2] - Stop PDK connector node failed: Unknown PDK exception occur, io.grpc.StatusRuntimeException: UNAVAILABLE: io exception | Associate id: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732861002095 
[INFO ] 2024-11-29 14:34:03.738 - [任务 33][DB2] - PDK connector node released: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732861002095 
[INFO ] 2024-11-29 14:34:03.739 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] schema data cleaned 
[INFO ] 2024-11-29 14:34:03.741 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] monitor closed 
[INFO ] 2024-11-29 14:34:03.741 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] running status set to false 
[INFO ] 2024-11-29 14:34:03.752 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] close complete, cost 26 ms 
[INFO ] 2024-11-29 14:34:03.752 - [任务 33][Dummy] - Stop connector 
[INFO ] 2024-11-29 14:34:03.770 - [任务 33][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732861001998 
[INFO ] 2024-11-29 14:34:03.771 - [任务 33][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732861001998 
[INFO ] 2024-11-29 14:34:03.772 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] schema data cleaned 
[INFO ] 2024-11-29 14:34:03.772 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] monitor closed 
[INFO ] 2024-11-29 14:34:03.772 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] close complete, cost 29 ms 
[INFO ] 2024-11-29 14:34:04.722 - [任务 33] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-29 14:34:04.726 - [任务 33] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@790f6608 
[INFO ] 2024-11-29 14:34:04.729 - [任务 33] - Stop task milestones: 67492a90479c873ac4b7d62e(任务 33)  
[INFO ] 2024-11-29 14:34:04.857 - [任务 33] - Stopped task aspect(s) 
[INFO ] 2024-11-29 14:34:04.859 - [任务 33] - Snapshot order controller have been removed 
[INFO ] 2024-11-29 14:34:04.859 - [任务 33] - Remove memory task client succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 14:34:04.859 - [任务 33] - Destroy memory task client cache succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 14:34:04.871 - [任务 33] - Resume task[任务 33] 
[INFO ] 2024-11-29 14:34:04.920 - [任务 33] - Start task milestones: 67492a90479c873ac4b7d62e(任务 33) 
[INFO ] 2024-11-29 14:34:04.927 - [任务 33] - Task initialization... 
[INFO ] 2024-11-29 14:34:05.145 - [任务 33] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-29 14:34:05.282 - [任务 33] - The engine receives 任务 33 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-29 14:34:05.282 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] start preload schema,table counts: 1 
[INFO ] 2024-11-29 14:34:05.283 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] start preload schema,table counts: 1 
[INFO ] 2024-11-29 14:34:05.283 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 14:34:05.457 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 14:34:05.457 - [任务 33][DB2] - Source node "DB2" read batch size: 100 
[INFO ] 2024-11-29 14:34:05.457 - [任务 33][DB2] - Source node "DB2" event queue capacity: 200 
[INFO ] 2024-11-29 14:34:05.459 - [任务 33][DB2] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-11-29 14:34:05.463 - [任务 33][DB2] - batch offset found: {"BMSQL_OORDER":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732861002362} 
[INFO ] 2024-11-29 14:34:05.463 - [任务 33][DB2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-29 14:34:05.502 - [任务 33][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-29 14:34:05.502 - [任务 33][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-29 14:34:05.549 - [任务 33][DB2] - Incremental sync starting... 
[INFO ] 2024-11-29 14:34:05.550 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 14:34:05.550 - [任务 33][DB2] - Starting stream read, table list: [BMSQL_OORDER], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732861002362} 
[WARN ] 2024-11-29 14:34:05.950 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:35:05.987 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:35:06.394 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:36:06.261 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:36:06.465 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:37:06.668 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:37:06.668 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:38:06.728 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:38:06.924 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:39:06.992 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:39:07.404 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:40:07.254 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:40:07.438 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:41:07.486 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:41:07.695 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 8 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:42:07.834 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:42:08.037 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 7 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:42:33.956 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] running status set to false 
[INFO ] 2024-11-29 14:42:33.957 - [任务 33][DB2] - Log Miner is shutting down... 
[INFO ] 2024-11-29 14:42:33.958 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:42:33.961 - [任务 33][DB2] - Stop PDK connector node failed: Unknown PDK exception occur, io.grpc.StatusRuntimeException: UNAVAILABLE: io exception | Associate id: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732862045321 
[INFO ] 2024-11-29 14:42:33.962 - [任务 33][DB2] - PDK connector node released: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732862045321 
[INFO ] 2024-11-29 14:42:33.962 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] schema data cleaned 
[INFO ] 2024-11-29 14:42:33.962 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] monitor closed 
[INFO ] 2024-11-29 14:42:33.963 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] close complete, cost 12 ms 
[INFO ] 2024-11-29 14:42:33.963 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] running status set to false 
[INFO ] 2024-11-29 14:42:33.982 - [任务 33][Dummy] - Stop connector 
[INFO ] 2024-11-29 14:42:34.005 - [任务 33][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732862045406 
[INFO ] 2024-11-29 14:42:34.006 - [任务 33][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732862045406 
[INFO ] 2024-11-29 14:42:34.006 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] schema data cleaned 
[INFO ] 2024-11-29 14:42:34.006 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] monitor closed 
[INFO ] 2024-11-29 14:42:34.026 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] close complete, cost 43 ms 
[INFO ] 2024-11-29 14:42:34.029 - [任务 33][DB2] - Incremental sync completed 
[INFO ] 2024-11-29 14:42:34.029 - [任务 33][DB2] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {} 
[ERROR] 2024-11-29 14:42:34.237 - [任务 33][DB2] - java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {} <-- Error Message -->
java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:462)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:219)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:281)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:142)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:187)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.schema.PdkTableMap.get(PdkTableMap.java:50)
	at io.tapdata.schema.PdkTableMap.get(PdkTableMap.java:13)
	at io.tapdata.common.cdc.NormalLogMiner.lambda$makeLobTables$2(NormalLogMiner.java:103)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.common.cdc.NormalLogMiner.makeLobTables(NormalLogMiner.java:102)
	at io.tapdata.common.cdc.NormalLogMiner.init(NormalLogMiner.java:61)
	at io.tapdata.connector.db2.cdc.Db2LogMiner.init(Db2LogMiner.java:49)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner2.init(Db2GrpcLogMiner2.java:69)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.init(Db2CdcRunner.java:23)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:287)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:173)
	... 31 more
Caused by: java.lang.RuntimeException: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:462)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 33 more

[INFO ] 2024-11-29 14:42:35.584 - [任务 33] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-29 14:42:35.595 - [任务 33] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@79bc026a 
[INFO ] 2024-11-29 14:42:35.596 - [任务 33] - Stop task milestones: 67492a90479c873ac4b7d62e(任务 33)  
[INFO ] 2024-11-29 14:42:35.736 - [任务 33] - Stopped task aspect(s) 
[INFO ] 2024-11-29 14:42:35.737 - [任务 33] - Snapshot order controller have been removed 
[INFO ] 2024-11-29 14:42:35.775 - [任务 33] - Remove memory task client succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 14:42:35.777 - [任务 33] - Destroy memory task client cache succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 14:47:03.274 - [任务 33] - Start task milestones: 67492a90479c873ac4b7d62e(任务 33) 
[INFO ] 2024-11-29 14:47:03.276 - [任务 33] - Task initialization... 
[INFO ] 2024-11-29 14:47:03.480 - [任务 33] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-29 14:47:03.568 - [任务 33] - The engine receives 任务 33 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-29 14:47:03.568 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] start preload schema,table counts: 1 
[INFO ] 2024-11-29 14:47:03.568 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] start preload schema,table counts: 1 
[INFO ] 2024-11-29 14:47:03.568 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 14:47:03.568 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 14:47:04.330 - [任务 33][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-29 14:47:04.331 - [任务 33][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-29 14:47:04.841 - [任务 33][DB2] - Source node "DB2" read batch size: 100 
[INFO ] 2024-11-29 14:47:04.842 - [任务 33][DB2] - Source node "DB2" event queue capacity: 200 
[INFO ] 2024-11-29 14:47:04.842 - [任务 33][DB2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-29 14:47:04.956 - [任务 33][DB2] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732862824874} 
[INFO ] 2024-11-29 14:47:04.960 - [任务 33][DB2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-29 14:47:05.039 - [任务 33][DB2] - Initial sync started 
[INFO ] 2024-11-29 14:47:05.040 - [任务 33][DB2] - Starting batch read, table name: BMSQL_OORDER 
[INFO ] 2024-11-29 14:47:05.040 - [任务 33][DB2] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-11-29 14:47:56.399 - [任务 33][DB2] - Query snapshot row size completed: DB2(531eeacc-3ca5-4851-acec-e2b937be2a37) 
[WARN ] 2024-11-29 14:47:56.411 - [任务 33][DB2] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.NumberFormatException: For input string: "11.5"
	java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	java.lang.Integer.parseInt(Integer.java:580)
	java.lang.Integer.parseInt(Integer.java:615)
	io.tapdata.connector.db2.Db2Connector.getHashSplitModConditions(Db2Connector.java:528)
	io.tapdata.common.CommonDbConnector.lambda$batchReadWithHashSplit$33(CommonDbConnector.java:632)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-11-29 14:48:56.573 - [任务 33][DB2] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.NumberFormatException: For input string: "11.5"
	java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	java.lang.Integer.parseInt(Integer.java:580)
	java.lang.Integer.parseInt(Integer.java:615)
	io.tapdata.connector.db2.Db2Connector.getHashSplitModConditions(Db2Connector.java:528)
	io.tapdata.common.CommonDbConnector.lambda$batchReadWithHashSplit$33(CommonDbConnector.java:632)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[WARN ] 2024-11-29 14:49:56.957 - [任务 33][DB2] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.NumberFormatException: For input string: "11.5"
	java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	java.lang.Integer.parseInt(Integer.java:580)
	java.lang.Integer.parseInt(Integer.java:615)
	io.tapdata.connector.db2.Db2Connector.getHashSplitModConditions(Db2Connector.java:528)
	io.tapdata.common.CommonDbConnector.lambda$batchReadWithHashSplit$33(CommonDbConnector.java:632)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[WARN ] 2024-11-29 14:50:57.140 - [任务 33][DB2] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.NumberFormatException: For input string: "11.5"
	java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	java.lang.Integer.parseInt(Integer.java:580)
	java.lang.Integer.parseInt(Integer.java:615)
	io.tapdata.connector.db2.Db2Connector.getHashSplitModConditions(Db2Connector.java:528)
	io.tapdata.common.CommonDbConnector.lambda$batchReadWithHashSplit$33(CommonDbConnector.java:632)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:51:54.356 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] running status set to false 
[INFO ] 2024-11-29 14:51:54.362 - [任务 33][DB2] - PDK connector node stopped: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732862824279 
[INFO ] 2024-11-29 14:51:54.362 - [任务 33][DB2] - PDK connector node released: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732862824279 
[INFO ] 2024-11-29 14:51:54.362 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] schema data cleaned 
[INFO ] 2024-11-29 14:51:54.362 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] monitor closed 
[INFO ] 2024-11-29 14:51:54.362 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 14:51:54.362 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] close complete, cost 48 ms 
[INFO ] 2024-11-29 14:51:54.362 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] running status set to false 
[INFO ] 2024-11-29 14:51:54.376 - [任务 33][DB2] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: java.lang.NumberFormatException: For input string: "11.5" 
[INFO ] 2024-11-29 14:51:54.380 - [任务 33][Dummy] - Stop connector 
[ERROR] 2024-11-29 14:51:54.382 - [任务 33][DB2] - Unknown PDK exception occur, java.lang.RuntimeException: java.lang.NumberFormatException: For input string: "11.5" <-- Error Message -->
Unknown PDK exception occur, java.lang.RuntimeException: java.lang.NumberFormatException: For input string: "11.5"

<-- Simple Stack Trace -->
Caused by: java.lang.NumberFormatException: For input string: "11.5"
	java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	java.lang.Integer.parseInt(Integer.java:580)
	java.lang.Integer.parseInt(Integer.java:615)
	io.tapdata.connector.db2.Db2Connector.getHashSplitModConditions(Db2Connector.java:528)
	io.tapdata.common.CommonDbConnector.lambda$batchReadWithHashSplit$33(CommonDbConnector.java:632)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.NumberFormatException: For input string: "11.5"
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:352)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:268)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.NumberFormatException: For input string: "11.5"
	at io.tapdata.common.exception.AbstractExceptionCollector.collectTerminateByServer(AbstractExceptionCollector.java:18)
	at io.tapdata.common.CommonDbConnector.batchReadWithHashSplit(CommonDbConnector.java:666)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:573)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:573)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more
Caused by: java.lang.NumberFormatException: For input string: "11.5"
	at java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	at java.lang.Integer.parseInt(Integer.java:580)
	at java.lang.Integer.parseInt(Integer.java:615)
	at io.tapdata.connector.db2.Db2Connector.getHashSplitModConditions(Db2Connector.java:528)
	at io.tapdata.common.CommonDbConnector.lambda$batchReadWithHashSplit$33(CommonDbConnector.java:632)
	... 6 more

[INFO ] 2024-11-29 14:51:54.384 - [任务 33][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732862824171 
[INFO ] 2024-11-29 14:51:54.384 - [任务 33][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732862824171 
[INFO ] 2024-11-29 14:51:54.384 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] schema data cleaned 
[INFO ] 2024-11-29 14:51:54.384 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] monitor closed 
[INFO ] 2024-11-29 14:51:54.592 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] close complete, cost 24 ms 
[INFO ] 2024-11-29 14:51:56.201 - [任务 33] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-29 14:51:56.201 - [任务 33] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@68febce3 
[INFO ] 2024-11-29 14:51:56.219 - [任务 33] - Stop task milestones: 67492a90479c873ac4b7d62e(任务 33)  
[INFO ] 2024-11-29 14:51:56.357 - [任务 33] - Stopped task aspect(s) 
[INFO ] 2024-11-29 14:51:56.358 - [任务 33] - Snapshot order controller have been removed 
[INFO ] 2024-11-29 14:51:56.381 - [任务 33] - Remove memory task client succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 14:51:56.381 - [任务 33] - Destroy memory task client cache succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 14:52:49.325 - [任务 33] - Start task milestones: 67492a90479c873ac4b7d62e(任务 33) 
[INFO ] 2024-11-29 14:52:49.513 - [任务 33] - Task initialization... 
[INFO ] 2024-11-29 14:52:49.516 - [任务 33] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-29 14:52:49.588 - [任务 33] - The engine receives 任务 33 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-29 14:52:49.657 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] start preload schema,table counts: 1 
[INFO ] 2024-11-29 14:52:49.658 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] start preload schema,table counts: 1 
[INFO ] 2024-11-29 14:52:49.658 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 14:52:49.658 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 14:52:50.497 - [任务 33][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-29 14:52:50.498 - [任务 33][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-29 14:52:50.540 - [任务 33][DB2] - Source node "DB2" read batch size: 100 
[INFO ] 2024-11-29 14:52:50.540 - [任务 33][DB2] - Source node "DB2" event queue capacity: 200 
[INFO ] 2024-11-29 14:52:50.540 - [任务 33][DB2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-29 14:52:50.585 - [任务 33][DB2] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732863170573} 
[INFO ] 2024-11-29 14:52:50.587 - [任务 33][DB2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-29 14:52:50.666 - [任务 33][DB2] - Initial sync started 
[INFO ] 2024-11-29 14:52:50.670 - [任务 33][DB2] - Starting batch read, table name: BMSQL_OORDER 
[INFO ] 2024-11-29 14:52:50.671 - [任务 33][DB2] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-11-29 14:53:55.871 - [任务 33][DB2] - Query snapshot row size completed: DB2(531eeacc-3ca5-4851-acec-e2b937be2a37) 
[WARN ] 2024-11-29 14:53:55.886 - [任务 33][DB2] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.ArrayIndexOutOfBoundsException: 0
	io.tapdata.connector.db2.Db2Connector.lambda$getHashSplitModConditions$21(Db2Connector.java:527)
	java.util.Optional.map(Optional.java:215)
	io.tapdata.connector.db2.Db2Connector.getHashSplitModConditions(Db2Connector.java:527)
	io.tapdata.common.CommonDbConnector.lambda$batchReadWithHashSplit$33(CommonDbConnector.java:632)
	java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-11-29 14:54:56.023 - [任务 33][DB2] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.ArrayIndexOutOfBoundsException: 0
	io.tapdata.connector.db2.Db2Connector.lambda$getHashSplitModConditions$21(Db2Connector.java:527)
	java.util.Optional.map(Optional.java:215)
	io.tapdata.connector.db2.Db2Connector.getHashSplitModConditions(Db2Connector.java:527)
	io.tapdata.common.CommonDbConnector.lambda$batchReadWithHashSplit$33(CommonDbConnector.java:632)
	java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:55:25.459 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] running status set to false 
[INFO ] 2024-11-29 14:55:25.459 - [任务 33][DB2] - PDK connector node stopped: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732863170281 
[INFO ] 2024-11-29 14:55:25.459 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 14:55:25.459 - [任务 33][DB2] - PDK connector node released: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732863170281 
[INFO ] 2024-11-29 14:55:25.460 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] schema data cleaned 
[INFO ] 2024-11-29 14:55:25.460 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] monitor closed 
[INFO ] 2024-11-29 14:55:25.461 - [任务 33][DB2] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: java.lang.ArrayIndexOutOfBoundsException: 0 
[INFO ] 2024-11-29 14:55:25.465 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] close complete, cost 39 ms 
[INFO ] 2024-11-29 14:55:25.465 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] running status set to false 
[ERROR] 2024-11-29 14:55:25.472 - [任务 33][DB2] - Unknown PDK exception occur, java.lang.RuntimeException: java.lang.ArrayIndexOutOfBoundsException: 0 <-- Error Message -->
Unknown PDK exception occur, java.lang.RuntimeException: java.lang.ArrayIndexOutOfBoundsException: 0

<-- Simple Stack Trace -->
Caused by: java.lang.ArrayIndexOutOfBoundsException: 0
	io.tapdata.connector.db2.Db2Connector.lambda$getHashSplitModConditions$21(Db2Connector.java:527)
	java.util.Optional.map(Optional.java:215)
	io.tapdata.connector.db2.Db2Connector.getHashSplitModConditions(Db2Connector.java:527)
	io.tapdata.common.CommonDbConnector.lambda$batchReadWithHashSplit$33(CommonDbConnector.java:632)
	java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.ArrayIndexOutOfBoundsException: 0
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:352)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:268)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.ArrayIndexOutOfBoundsException: 0
	at io.tapdata.common.exception.AbstractExceptionCollector.collectTerminateByServer(AbstractExceptionCollector.java:18)
	at io.tapdata.common.CommonDbConnector.batchReadWithHashSplit(CommonDbConnector.java:666)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:573)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:573)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more
Caused by: java.lang.ArrayIndexOutOfBoundsException: 0
	at io.tapdata.connector.db2.Db2Connector.lambda$getHashSplitModConditions$21(Db2Connector.java:527)
	at java.util.Optional.map(Optional.java:215)
	at io.tapdata.connector.db2.Db2Connector.getHashSplitModConditions(Db2Connector.java:527)
	at io.tapdata.common.CommonDbConnector.lambda$batchReadWithHashSplit$33(CommonDbConnector.java:632)
	... 6 more

[INFO ] 2024-11-29 14:55:25.473 - [任务 33][Dummy] - Stop connector 
[INFO ] 2024-11-29 14:55:25.487 - [任务 33][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732863170391 
[INFO ] 2024-11-29 14:55:25.488 - [任务 33][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732863170391 
[INFO ] 2024-11-29 14:55:25.488 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] schema data cleaned 
[INFO ] 2024-11-29 14:55:25.488 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] monitor closed 
[INFO ] 2024-11-29 14:55:25.696 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] close complete, cost 27 ms 
[INFO ] 2024-11-29 14:55:30.505 - [任务 33] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-29 14:55:30.518 - [任务 33] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1b7863b5 
[INFO ] 2024-11-29 14:55:30.518 - [任务 33] - Stop task milestones: 67492a90479c873ac4b7d62e(任务 33)  
[INFO ] 2024-11-29 14:55:30.638 - [任务 33] - Stopped task aspect(s) 
[INFO ] 2024-11-29 14:55:30.638 - [任务 33] - Snapshot order controller have been removed 
[INFO ] 2024-11-29 14:55:30.661 - [任务 33] - Remove memory task client succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 14:55:30.866 - [任务 33] - Destroy memory task client cache succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 14:57:45.381 - [任务 33] - Start task milestones: 67492a90479c873ac4b7d62e(任务 33) 
[INFO ] 2024-11-29 14:57:45.561 - [任务 33] - Task initialization... 
[INFO ] 2024-11-29 14:57:45.562 - [任务 33] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-29 14:57:45.629 - [任务 33] - The engine receives 任务 33 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-29 14:57:45.707 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] start preload schema,table counts: 1 
[INFO ] 2024-11-29 14:57:45.711 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] start preload schema,table counts: 1 
[INFO ] 2024-11-29 14:57:45.711 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 14:57:45.711 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 14:57:46.389 - [任务 33][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-29 14:57:46.389 - [任务 33][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-29 14:57:46.665 - [任务 33][DB2] - Source node "DB2" read batch size: 100 
[INFO ] 2024-11-29 14:57:46.665 - [任务 33][DB2] - Source node "DB2" event queue capacity: 200 
[INFO ] 2024-11-29 14:57:46.665 - [任务 33][DB2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-29 14:57:46.713 - [任务 33][DB2] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732863466697} 
[INFO ] 2024-11-29 14:57:46.714 - [任务 33][DB2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-29 14:57:46.798 - [任务 33][DB2] - Initial sync started 
[INFO ] 2024-11-29 14:57:46.807 - [任务 33][DB2] - Starting batch read, table name: BMSQL_OORDER 
[INFO ] 2024-11-29 14:57:46.811 - [任务 33][DB2] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-11-29 14:57:58.670 - [任务 33][DB2] - Query snapshot row size completed: DB2(531eeacc-3ca5-4851-acec-e2b937be2a37) 
[INFO ] 2024-11-29 14:57:58.686 - [任务 33][DB2] - batchRead, splitSql[1]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=0 
[INFO ] 2024-11-29 14:57:58.696 - [任务 33][DB2] - batchRead, splitSql[2]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=1 
[INFO ] 2024-11-29 14:57:58.696 - [任务 33][DB2] - batchRead, splitSql[3]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=2 
[INFO ] 2024-11-29 14:57:58.696 - [任务 33][DB2] - batchRead, splitSql[4]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=3 
[INFO ] 2024-11-29 14:58:00.514 - [任务 33][DB2] - batchRead, splitSql[5]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=4 
[INFO ] 2024-11-29 14:58:00.515 - [任务 33][DB2] - batchRead, splitSql[7]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=6 
[INFO ] 2024-11-29 14:58:00.515 - [任务 33][DB2] - batchRead, splitSql[6]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=5 
[INFO ] 2024-11-29 14:58:00.692 - [任务 33][DB2] - batchRead, splitSql[8]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=7 
[INFO ] 2024-11-29 14:58:00.693 - [任务 33][DB2] - batchRead, splitSql[9]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=8 
[INFO ] 2024-11-29 14:58:00.693 - [任务 33][DB2] - batchRead, splitSql[10]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=9 
[INFO ] 2024-11-29 14:58:00.810 - [任务 33][DB2] - batchRead, splitSql[11]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=10 
[INFO ] 2024-11-29 14:58:00.810 - [任务 33][DB2] - batchRead, splitSql[12]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=11 
[INFO ] 2024-11-29 14:58:00.896 - [任务 33][DB2] - batchRead, splitSql[13]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=12 
[INFO ] 2024-11-29 14:58:00.896 - [任务 33][DB2] - batchRead, splitSql[14]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=13 
[INFO ] 2024-11-29 14:58:00.999 - [任务 33][DB2] - batchRead, splitSql[17]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=16 
[INFO ] 2024-11-29 14:58:00.999 - [任务 33][DB2] - batchRead, splitSql[16]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=15 
[INFO ] 2024-11-29 14:58:01.141 - [任务 33][DB2] - batchRead, splitSql[15]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=14 
[INFO ] 2024-11-29 14:58:01.143 - [任务 33][DB2] - batchRead, splitSql[18]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=17 
[INFO ] 2024-11-29 14:58:01.194 - [任务 33][DB2] - batchRead, splitSql[20]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=19 
[INFO ] 2024-11-29 14:58:01.198 - [任务 33][DB2] - batchRead, splitSql[19]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=18 
[INFO ] 2024-11-29 14:58:01.381 - [任务 33][DB2] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-29 14:58:01.381 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 14:58:01.381 - [任务 33][DB2] - Incremental sync starting... 
[INFO ] 2024-11-29 14:58:01.382 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 14:58:01.385 - [任务 33][DB2] - Starting stream read, table list: [BMSQL_OORDER], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732863466697} 
[WARN ] 2024-11-29 14:58:01.589 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:59:01.636 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:59:02.047 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 14:59:57.650 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] running status set to false 
[INFO ] 2024-11-29 14:59:57.651 - [任务 33][DB2] - Log Miner is shutting down... 
[INFO ] 2024-11-29 14:59:57.651 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 14:59:57.654 - [任务 33][DB2] - Stop PDK connector node failed: Unknown PDK exception occur, io.grpc.StatusRuntimeException: UNAVAILABLE: io exception | Associate id: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732863466426 
[INFO ] 2024-11-29 14:59:57.654 - [任务 33][DB2] - PDK connector node released: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732863466426 
[INFO ] 2024-11-29 14:59:57.654 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] schema data cleaned 
[INFO ] 2024-11-29 14:59:57.658 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] monitor closed 
[INFO ] 2024-11-29 14:59:57.658 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] close complete, cost 21 ms 
[INFO ] 2024-11-29 14:59:57.658 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] running status set to false 
[INFO ] 2024-11-29 14:59:57.670 - [任务 33][Dummy] - Stop connector 
[INFO ] 2024-11-29 14:59:57.683 - [任务 33][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732863466291 
[INFO ] 2024-11-29 14:59:57.683 - [任务 33][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_f3175d72-143c-4143-90f6-4e088f45eeb0_1732863466291 
[INFO ] 2024-11-29 14:59:57.683 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] schema data cleaned 
[INFO ] 2024-11-29 14:59:57.683 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] monitor closed 
[INFO ] 2024-11-29 14:59:57.684 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] close complete, cost 26 ms 
[INFO ] 2024-11-29 14:59:57.722 - [任务 33][DB2] - Incremental sync completed 
[INFO ] 2024-11-29 14:59:57.722 - [任务 33][DB2] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {} 
[ERROR] 2024-11-29 14:59:57.728 - [任务 33][DB2] - java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {} <-- Error Message -->
java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:462)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:219)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:281)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:142)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:187)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.schema.PdkTableMap.get(PdkTableMap.java:50)
	at io.tapdata.schema.PdkTableMap.get(PdkTableMap.java:13)
	at io.tapdata.common.cdc.NormalLogMiner.lambda$makeLobTables$2(NormalLogMiner.java:103)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.common.cdc.NormalLogMiner.makeLobTables(NormalLogMiner.java:102)
	at io.tapdata.common.cdc.NormalLogMiner.init(NormalLogMiner.java:61)
	at io.tapdata.connector.db2.cdc.Db2LogMiner.init(Db2LogMiner.java:49)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner2.init(Db2GrpcLogMiner2.java:69)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.init(Db2CdcRunner.java:23)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:295)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:173)
	... 31 more
Caused by: java.lang.RuntimeException: Table name "BMSQL_OORDER" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:462)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 33 more

[INFO ] 2024-11-29 14:59:58.606 - [任务 33] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-29 14:59:58.607 - [任务 33] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2a6faf32 
[INFO ] 2024-11-29 14:59:58.615 - [任务 33] - Stop task milestones: 67492a90479c873ac4b7d62e(任务 33)  
[INFO ] 2024-11-29 14:59:58.733 - [任务 33] - Stopped task aspect(s) 
[INFO ] 2024-11-29 14:59:58.733 - [任务 33] - Snapshot order controller have been removed 
[INFO ] 2024-11-29 14:59:58.751 - [任务 33] - Remove memory task client succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 14:59:58.757 - [任务 33] - Destroy memory task client cache succeed, task: 任务 33[67492a90479c873ac4b7d62e] 
[INFO ] 2024-11-29 15:00:04.377 - [任务 33] - Start task milestones: 67492a90479c873ac4b7d62e(任务 33) 
[INFO ] 2024-11-29 15:00:04.593 - [任务 33] - Task initialization... 
[INFO ] 2024-11-29 15:00:04.594 - [任务 33] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-29 15:00:04.636 - [任务 33] - The engine receives 任务 33 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-29 15:00:04.694 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] start preload schema,table counts: 1 
[INFO ] 2024-11-29 15:00:04.694 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] start preload schema,table counts: 1 
[INFO ] 2024-11-29 15:00:04.694 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 15:00:04.694 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] preload schema finished, cost 0 ms 
[INFO ] 2024-11-29 15:00:05.530 - [任务 33][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-29 15:00:05.530 - [任务 33][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-29 15:00:05.588 - [任务 33][DB2] - Source node "DB2" read batch size: 100 
[INFO ] 2024-11-29 15:00:05.588 - [任务 33][DB2] - Source node "DB2" event queue capacity: 200 
[INFO ] 2024-11-29 15:00:05.588 - [任务 33][DB2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-29 15:00:05.644 - [任务 33][DB2] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732863605640} 
[INFO ] 2024-11-29 15:00:05.644 - [任务 33][DB2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-29 15:00:05.737 - [任务 33][DB2] - Initial sync started 
[INFO ] 2024-11-29 15:00:05.738 - [任务 33][DB2] - Starting batch read, table name: BMSQL_OORDER 
[INFO ] 2024-11-29 15:00:05.739 - [任务 33][DB2] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-11-29 15:00:39.135 - [任务 33][DB2] - batchRead, splitSql[3]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=2 
[INFO ] 2024-11-29 15:00:39.138 - [任务 33][DB2] - batchRead, splitSql[4]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=3 
[INFO ] 2024-11-29 15:00:39.138 - [任务 33][DB2] - batchRead, splitSql[1]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=0 
[INFO ] 2024-11-29 15:00:39.159 - [任务 33][DB2] - Query snapshot row size completed: DB2(531eeacc-3ca5-4851-acec-e2b937be2a37) 
[INFO ] 2024-11-29 15:00:39.159 - [任务 33][DB2] - batchRead, splitSql[2]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=1 
[INFO ] 2024-11-29 15:00:39.360 - [任务 33][DB2] - batchRead, splitSql[7]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=6 
[INFO ] 2024-11-29 15:00:39.501 - [任务 33][DB2] - batchRead, splitSql[5]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=4 
[INFO ] 2024-11-29 15:00:39.501 - [任务 33][DB2] - batchRead, splitSql[8]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=7 
[INFO ] 2024-11-29 15:00:39.502 - [任务 33][DB2] - batchRead, splitSql[6]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=5 
[INFO ] 2024-11-29 15:00:39.590 - [任务 33][DB2] - batchRead, splitSql[11]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=10 
[INFO ] 2024-11-29 15:00:39.591 - [任务 33][DB2] - batchRead, splitSql[12]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=11 
[INFO ] 2024-11-29 15:00:39.666 - [任务 33][DB2] - batchRead, splitSql[9]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=8 
[INFO ] 2024-11-29 15:00:39.667 - [任务 33][DB2] - batchRead, splitSql[10]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=9 
[INFO ] 2024-11-29 15:00:39.785 - [任务 33][DB2] - batchRead, splitSql[15]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=14 
[INFO ] 2024-11-29 15:00:39.785 - [任务 33][DB2] - batchRead, splitSql[13]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=12 
[INFO ] 2024-11-29 15:00:39.803 - [任务 33][DB2] - batchRead, splitSql[16]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=15 
[INFO ] 2024-11-29 15:00:39.803 - [任务 33][DB2] - batchRead, splitSql[14]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=13 
[INFO ] 2024-11-29 15:00:39.939 - [任务 33][DB2] - batchRead, splitSql[19]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=18 
[INFO ] 2024-11-29 15:00:39.939 - [任务 33][DB2] - batchRead, splitSql[17]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=16 
[INFO ] 2024-11-29 15:00:39.966 - [任务 33][DB2] - batchRead, splitSql[20]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=19 
[INFO ] 2024-11-29 15:00:39.966 - [任务 33][DB2] - batchRead, splitSql[18]: SELECT "O_W_ID","O_D_ID","O_ID","O_C_ID","O_CARRIER_ID","O_OL_CNT","O_ALL_LOCAL","O_ENTRY_D" FROM "DB2INST1"."BMSQL_OORDER" WHERE mod(HEX(HEX(RIGHT(HASH(ROWID, 0), 4))),20)=17 
[INFO ] 2024-11-29 15:00:40.148 - [任务 33][DB2] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-29 15:00:40.148 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 15:00:40.149 - [任务 33][DB2] - Incremental sync starting... 
[INFO ] 2024-11-29 15:00:40.149 - [任务 33][DB2] - Initial sync completed 
[INFO ] 2024-11-29 15:00:40.352 - [任务 33][DB2] - Starting stream read, table list: [BMSQL_OORDER], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1732863605640} 
[WARN ] 2024-11-29 15:00:40.860 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 15:01:41.027 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 15:01:41.145 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 15:02:41.208 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 15:02:41.617 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 15:03:41.673 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 15:03:41.884 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 15:04:41.769 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 15:04:41.982 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 15:05:41.927 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 15:05:42.095 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 15:06:42.329 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 15:06:42.330 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 15:07:42.531 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 15:07:42.744 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 8 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 15:08:42.586 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 15:08:42.791 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 7 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 15:09:42.899 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 15:09:43.103 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 6 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 15:10:43.050 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 15:10:43.457 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 5 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 15:11:43.367 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 15:11:43.575 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 4 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 15:12:43.711 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 15:12:43.917 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 3 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 15:13:43.889 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 15:13:44.299 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 2 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 15:14:44.199 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 15:14:44.383 - [任务 33][DB2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.IOException: Connection reset by peer
	sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	sun.nio.ch.IOUtil.read(IOUtil.java:192)
	sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	...
 - Remaining retry 1 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-29 15:15:41.567 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] running status set to false 
[INFO ] 2024-11-29 15:15:41.581 - [任务 33][DB2] - Log Miner is shutting down... 
[INFO ] 2024-11-29 15:15:41.581 - [任务 33][DB2] - Log Miner is shutting down... 
[WARN ] 2024-11-29 15:15:41.582 - [任务 33][DB2] - Stop PDK connector node failed: Unknown PDK exception occur, io.grpc.StatusRuntimeException: UNAVAILABLE: io exception | Associate id: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732863605312 
[INFO ] 2024-11-29 15:15:41.582 - [任务 33][DB2] - PDK connector node released: HazelcastSourcePdkDataNode_531eeacc-3ca5-4851-acec-e2b937be2a37_1732863605312 
[INFO ] 2024-11-29 15:15:41.583 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] schema data cleaned 
[INFO ] 2024-11-29 15:15:41.583 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] monitor closed 
[INFO ] 2024-11-29 15:15:41.630 - [任务 33][DB2] - Node DB2[531eeacc-3ca5-4851-acec-e2b937be2a37] close complete, cost 69 ms 
[INFO ] 2024-11-29 15:15:41.630 - [任务 33][Dummy] - Node Dummy[f3175d72-143c-4143-90f6-4e088f45eeb0] running status set to false 
