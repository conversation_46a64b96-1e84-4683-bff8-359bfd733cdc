[INFO ] 2024-09-04 08:13:59.869 - [任务 10] - Task initialization... 
[INFO ] 2024-09-04 08:13:59.874 - [任务 10] - Start task milestones: 66d7a5eda40a37725da9f105(任务 10) 
[INFO ] 2024-09-04 08:13:59.973 - [任务 10] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-09-04 08:14:00.046 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-04 08:14:00.046 - [任务 10][Oracle] - Node Oracle[84f80db7-e178-46a9-8ac1-1d38385bf671] start preload schema,table counts: 1 
[INFO ] 2024-09-04 08:14:00.046 - [任务 10][Ka<PERSON>ka] - Node <PERSON><PERSON>ka[85ce1deb-123b-443a-ac45-7c880c9e8f7d] start preload schema,table counts: 1 
[INFO ] 2024-09-04 08:14:00.047 - [任务 10][Ka<PERSON>ka] - <PERSON><PERSON>[85ce1deb-123b-443a-ac45-7c880c9e8f7d] preload schema finished, cost 0 ms 
[INFO ] 2024-09-04 08:14:00.047 - [任务 10][Oracle] - Node Oracle[84f80db7-e178-46a9-8ac1-1d38385bf671] preload schema finished, cost 0 ms 
[INFO ] 2024-09-04 08:14:01.087 - [任务 10][Oracle] - Source node "Oracle" read batch size: 100 
[INFO ] 2024-09-04 08:14:01.092 - [任务 10][Oracle] - Source node "Oracle" event queue capacity: 200 
[INFO ] 2024-09-04 08:14:01.093 - [任务 10][Oracle] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-04 08:14:01.140 - [任务 10][Kafka] - Node(Kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-04 08:14:01.140 - [任务 10][Kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-04 08:14:01.413 - [任务 10][Kafka] - The table BMSQL_CONFIG has already exist. 
[INFO ] 2024-09-04 08:14:01.521 - [任务 10][Oracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":106729216,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-09-04 08:14:01.528 - [任务 10][Oracle] - Initial sync started 
[INFO ] 2024-09-04 08:14:01.529 - [任务 10][Oracle] - Starting batch read, table name: BMSQL_CONFIG 
[INFO ] 2024-09-04 08:14:01.529 - [任务 10][Oracle] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-09-04 08:14:01.601 - [任务 10][Oracle] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-04 08:14:01.603 - [任务 10][Oracle] - Query table 'BMSQL_CONFIG' counts: 4 
[INFO ] 2024-09-04 08:14:01.604 - [任务 10][Oracle] - Initial sync completed 
[INFO ] 2024-09-04 08:14:01.604 - [任务 10][Oracle] - Incremental sync starting... 
[INFO ] 2024-09-04 08:14:01.605 - [任务 10][Oracle] - Initial sync completed 
[INFO ] 2024-09-04 08:14:01.605 - [任务 10][Oracle] - Starting stream read, table list: [BMSQL_CONFIG], offset: {"sortString":null,"offsetValue":null,"lastScn":106729216,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-09-04 08:14:01.809 - [任务 10][Oracle] - total start mining scn: 106729216 
[INFO ] 2024-09-04 08:14:03.029 - [任务 10][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-09-04 08:14:36.890 - [任务 10][Oracle] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@7ce0ac33: {"newFields":[{"autoInc":false,"dataType":"VARCHAR2(100)","name":"CONFIG_NAME","nullable":true,"partitionKey":false,"pos":3,"primaryKey":false,"virtual":false}],"originDDL":"ALTER TABLE C##TAPDATA.BMSQL_CONFIG ADD CONFIG_NAME VARCHAR2(100) NULL;","referenceTime":1725408871000,"tableId":"BMSQL_CONFIG","time":1725408876845,"type":209} 
[INFO ] 2024-09-04 08:14:36.892 - [任务 10][Oracle] - Alter table in memory, qualified name: T_oracle_io_tapdata_1_0-SNAPSHOT_BMSQL_CONFIG_66d688a8a40a37725da9ce03_66d7a5eda40a37725da9f105 
[INFO ] 2024-09-04 08:14:37.095 - [任务 10][Oracle] - Alter table schema transform finished 
[INFO ] 2024-09-04 08:14:37.423 - [任务 10][Kafka] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@7ce0ac33: {"newFields":[{"autoInc":false,"dataType":"VARCHAR2(100)","name":"CONFIG_NAME","nullable":true,"partitionKey":false,"pos":3,"primaryKey":false,"tapType":{"bytes":100,"type":10},"virtual":false}],"originDDL":"ALTER TABLE C##TAPDATA.BMSQL_CONFIG ADD CONFIG_NAME VARCHAR2(100) NULL;","referenceTime":1725408871000,"tableId":"BMSQL_CONFIG","time":1725408876845,"type":209}). Wait for all previous events to be processed 
[INFO ] 2024-09-04 08:14:37.632 - [任务 10][Kafka] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapNewFieldEvent@7ce0ac33: {"newFields":[{"autoInc":false,"dataType":"VARCHAR2(100)","name":"CONFIG_NAME","nullable":true,"partitionKey":false,"pos":3,"primaryKey":false,"tapType":{"bytes":100,"type":10},"virtual":false}],"originDDL":"ALTER TABLE C##TAPDATA.BMSQL_CONFIG ADD CONFIG_NAME VARCHAR2(100) NULL;","referenceTime":1725408871000,"tableId":"BMSQL_CONFIG","time":1725408876845,"type":209}) 
[WARN ] 2024-09-04 10:15:06.776 - [任务 10][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 17410, message: Caused by: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:855)
	oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-04 10:16:06.680 - [任务 10][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-09-04 10:16:06.881 - [任务 10][Oracle] - Log Miner has been closed! 
[INFO ] 2024-09-04 10:16:08.311 - [任务 10][Oracle] - total start mining scn: 106739832 
[INFO ] 2024-09-04 10:16:09.685 - [任务 10][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-09-04 10:16:16.886 - [任务 10][Oracle] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-09-04 10:35:24.730 - [任务 10][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:855)
	oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-04 10:36:24.805 - [任务 10][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-09-04 10:36:25.013 - [任务 10][Oracle] - Log Miner has been closed! 
[INFO ] 2024-09-04 10:36:36.158 - [任务 10][Oracle] - total start mining scn: 106758965 
[INFO ] 2024-09-04 10:36:37.581 - [任务 10][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-09-04 10:36:51.352 - [任务 10][Oracle] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-09-04 10:43:56.471 - [任务 10][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:855)
	oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-04 10:44:56.523 - [任务 10][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-09-04 10:44:56.523 - [任务 10][Oracle] - Log Miner has been closed! 
[INFO ] 2024-09-04 10:44:57.752 - [任务 10][Oracle] - total start mining scn: 106760141 
[INFO ] 2024-09-04 10:44:59.180 - [任务 10][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-09-04 10:45:07.361 - [任务 10][Oracle] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-04 13:00:28.538 - [任务 10][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_09_04/o1_mf_1_2559_mfhtc6ft_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-09-04 13:00:40.541 - [任务 10][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[WARN ] 2024-09-04 17:38:02.964 - [任务 10][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: java.sql.SQLTransientConnectionException: HikariPool-20 - Connection is not available, request timed out after 48191ms.
	com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:128)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-04 17:39:16.337 - [任务 10][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-09-04 17:39:16.544 - [任务 10][Oracle] - Log Miner has been closed! 
[INFO ] 2024-09-04 17:39:17.563 - [任务 10][Oracle] - total start mining scn: 106866400 
[INFO ] 2024-09-04 17:39:18.865 - [任务 10][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-09-04 17:39:31.696 - [任务 10][Oracle] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-04 18:00:15.975 - [任务 10][Oracle] - Node Oracle[84f80db7-e178-46a9-8ac1-1d38385bf671] running status set to false 
[INFO ] 2024-09-04 18:00:15.981 - [任务 10][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-09-04 18:00:15.981 - [任务 10][Oracle] - Log Miner has been closed! 
[INFO ] 2024-09-04 18:00:15.992 - [任务 10][Oracle] - Incremental sync completed 
[INFO ] 2024-09-04 18:00:15.999 - [任务 10][Oracle] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-09-04 18:00:16.003 - [任务 10][Oracle] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:444)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:790)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-09-04 18:00:16.021 - [任务 10][Oracle] - PDK connector node stopped: HazelcastSourcePdkDataNode-84f80db7-e178-46a9-8ac1-1d38385bf671 
[INFO ] 2024-09-04 18:00:16.021 - [任务 10][Oracle] - PDK connector node released: HazelcastSourcePdkDataNode-84f80db7-e178-46a9-8ac1-1d38385bf671 
[INFO ] 2024-09-04 18:00:16.021 - [任务 10][Oracle] - Node Oracle[84f80db7-e178-46a9-8ac1-1d38385bf671] schema data cleaned 
[INFO ] 2024-09-04 18:00:16.021 - [任务 10][Oracle] - Node Oracle[84f80db7-e178-46a9-8ac1-1d38385bf671] monitor closed 
[INFO ] 2024-09-04 18:00:16.022 - [任务 10][Oracle] - Node Oracle[84f80db7-e178-46a9-8ac1-1d38385bf671] close complete, cost 60 ms 
[INFO ] 2024-09-04 18:00:16.022 - [任务 10][Kafka] - Node Kafka[85ce1deb-123b-443a-ac45-7c880c9e8f7d] running status set to false 
[INFO ] 2024-09-04 18:00:16.034 - [任务 10][Kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode-85ce1deb-123b-443a-ac45-7c880c9e8f7d 
[INFO ] 2024-09-04 18:00:16.034 - [任务 10][Kafka] - PDK connector node released: HazelcastTargetPdkDataNode-85ce1deb-123b-443a-ac45-7c880c9e8f7d 
[INFO ] 2024-09-04 18:00:16.034 - [任务 10][Kafka] - Node Kafka[85ce1deb-123b-443a-ac45-7c880c9e8f7d] schema data cleaned 
[INFO ] 2024-09-04 18:00:16.035 - [任务 10][Kafka] - Node Kafka[85ce1deb-123b-443a-ac45-7c880c9e8f7d] monitor closed 
[INFO ] 2024-09-04 18:00:16.237 - [任务 10][Kafka] - Node Kafka[85ce1deb-123b-443a-ac45-7c880c9e8f7d] close complete, cost 12 ms 
[INFO ] 2024-09-04 18:00:19.189 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-04 18:00:19.297 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1790e6bb 
[INFO ] 2024-09-04 18:00:19.298 - [任务 10] - Stop task milestones: 66d7a5eda40a37725da9f105(任务 10)  
[INFO ] 2024-09-04 18:00:19.308 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-09-04 18:00:19.308 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-09-04 18:00:19.327 - [任务 10] - Remove memory task client succeed, task: 任务 10[66d7a5eda40a37725da9f105] 
[INFO ] 2024-09-04 18:00:19.330 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[66d7a5eda40a37725da9f105] 
