[INFO ] 2024-07-12 11:03:39.369 - [Heartbeat-SourceMysqlTestHeartBeat] - Start task milestones: 66909ce97c91bf6e98248b3a(Heartbeat-SourceMysqlTestHeartBeat) 
[INFO ] 2024-07-12 11:03:39.691 - [Heartbeat-SourceMysqlTestHeartBeat] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-12 11:03:39.691 - [Heartbeat-SourceMysqlTestHeartBeat] - The engine receives Heartbeat-SourceMysqlTestHeartBeat task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-12 11:03:39.753 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[cc8514b3-e5de-4445-bc5d-d975be6fca44] start preload schema,table counts: 1 
[INFO ] 2024-07-12 11:03:39.755 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[cc8514b3-e5de-4445-bc5d-d975be6fca44] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 11:03:39.785 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8f2b9970-c388-4bb2-a79e-05d38595280c] start preload schema,table counts: 1 
[INFO ] 2024-07-12 11:03:39.787 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8f2b9970-c388-4bb2-a79e-05d38595280c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 11:03:40.611 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-12 11:03:40.628 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-12 11:03:40.629 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-12 11:03:40.632 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1720753420598,"lastTimes":1720753420598,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-12 11:03:40.702 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-12 11:03:40.706 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-12 11:03:40.716 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-12 11:03:40.716 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-12 11:03:40.717 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-12 11:03:40.723 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1720753420598,"lastTimes":1720753420598,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-12 11:03:40.726 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-12 11:03:40.726 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-12 11:03:41.205 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-12 11:03:41.231 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Table "test._tapdata_heartbeat_table" exists, skip auto create table 
[INFO ] 2024-07-12 11:13:53.604 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8f2b9970-c388-4bb2-a79e-05d38595280c] running status set to false 
[INFO ] 2024-07-12 11:13:53.618 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-12 11:13:53.631 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-8f2b9970-c388-4bb2-a79e-05d38595280c 
[INFO ] 2024-07-12 11:13:53.632 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-8f2b9970-c388-4bb2-a79e-05d38595280c 
[INFO ] 2024-07-12 11:13:53.633 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8f2b9970-c388-4bb2-a79e-05d38595280c] schema data cleaned 
[INFO ] 2024-07-12 11:13:53.633 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8f2b9970-c388-4bb2-a79e-05d38595280c] monitor closed 
[INFO ] 2024-07-12 11:13:53.637 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8f2b9970-c388-4bb2-a79e-05d38595280c] close complete, cost 63 ms 
[INFO ] 2024-07-12 11:13:53.638 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[cc8514b3-e5de-4445-bc5d-d975be6fca44] running status set to false 
[INFO ] 2024-07-12 11:13:53.662 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-cc8514b3-e5de-4445-bc5d-d975be6fca44 
[INFO ] 2024-07-12 11:13:53.663 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-cc8514b3-e5de-4445-bc5d-d975be6fca44 
[INFO ] 2024-07-12 11:13:53.664 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[cc8514b3-e5de-4445-bc5d-d975be6fca44] schema data cleaned 
[INFO ] 2024-07-12 11:13:53.664 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[cc8514b3-e5de-4445-bc5d-d975be6fca44] monitor closed 
[INFO ] 2024-07-12 11:13:53.744 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[cc8514b3-e5de-4445-bc5d-d975be6fca44] close complete, cost 27 ms 
[INFO ] 2024-07-12 11:13:53.744 - [Heartbeat-SourceMysqlTestHeartBeat] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-12 11:13:53.861 - [Heartbeat-SourceMysqlTestHeartBeat] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@38631264 
[INFO ] 2024-07-12 11:13:53.861 - [Heartbeat-SourceMysqlTestHeartBeat] - Stop task milestones: 66909ce97c91bf6e98248b3a(Heartbeat-SourceMysqlTestHeartBeat)  
[INFO ] 2024-07-12 11:13:53.877 - [Heartbeat-SourceMysqlTestHeartBeat] - Stopped task aspect(s) 
[INFO ] 2024-07-12 11:13:53.877 - [Heartbeat-SourceMysqlTestHeartBeat] - Snapshot order controller have been removed 
[INFO ] 2024-07-12 11:13:53.909 - [Heartbeat-SourceMysqlTestHeartBeat] - Remove memory task client succeed, task: Heartbeat-SourceMysqlTestHeartBeat[66909ce97c91bf6e98248b3a] 
[INFO ] 2024-07-12 11:13:53.909 - [Heartbeat-SourceMysqlTestHeartBeat] - Destroy memory task client cache succeed, task: Heartbeat-SourceMysqlTestHeartBeat[66909ce97c91bf6e98248b3a] 
