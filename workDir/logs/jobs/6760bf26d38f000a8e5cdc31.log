[INFO ] 2024-12-17 08:02:01.392 - [任务 1] - Task initialization... 
[INFO ] 2024-12-17 08:02:01.396 - [任务 1] - Start task milestones: 6760bf26d38f000a8e5cdc31(任务 1) 
[INFO ] 2024-12-17 08:02:01.625 - [任务 1] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-12-17 08:02:01.720 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-17 08:02:01.722 - [任务 1][Dummy] - Node Dummy[da46dffd-bf62-4bf8-8416-ac3787fc9bfd] start preload schema,table counts: 1 
[INFO ] 2024-12-17 08:02:01.731 - [任务 1][Dummy] - Node Dummy[da46dffd-bf62-4bf8-8416-ac3787fc9bfd] preload schema finished, cost 0 ms 
[INFO ] 2024-12-17 08:02:01.732 - [任务 1][TestMongo] - Node TestMongo[3d98e94d-c3f1-4935-b4ac-7c008ed38aac] start preload schema,table counts: 1 
[INFO ] 2024-12-17 08:02:01.732 - [任务 1][TestMongo] - Node TestMongo[3d98e94d-c3f1-4935-b4ac-7c008ed38aac] preload schema finished, cost 0 ms 
[INFO ] 2024-12-17 08:02:02.472 - [任务 1][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-17 08:02:02.474 - [任务 1][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-17 08:02:02.586 - [任务 1][TestMongo] - Source node "TestMongo" read batch size: 100 
[INFO ] 2024-12-17 08:02:02.587 - [任务 1][TestMongo] - Source node "TestMongo" event queue capacity: 200 
[INFO ] 2024-12-17 08:02:02.587 - [任务 1][TestMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-17 08:02:02.788 - [任务 1][TestMongo] - batch offset found: {},stream offset found: {"cdcOffset":1734393722,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-17 08:02:02.830 - [任务 1][TestMongo] - Initial sync started 
[INFO ] 2024-12-17 08:02:02.831 - [任务 1][TestMongo] - Query snapshot row size completed: TestMongo(3d98e94d-c3f1-4935-b4ac-7c008ed38aac) 
[INFO ] 2024-12-17 08:02:02.839 - [任务 1][TestMongo] - Starting batch read, table name: BMSQL_CONFIG 
[INFO ] 2024-12-17 08:02:02.840 - [任务 1][TestMongo] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-12-17 08:02:02.869 - [任务 1][TestMongo] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-17 08:02:02.870 - [任务 1][TestMongo] - Initial sync completed 
[INFO ] 2024-12-17 08:02:02.870 - [任务 1][TestMongo] - Incremental sync starting... 
[INFO ] 2024-12-17 08:02:02.870 - [任务 1][TestMongo] - Initial sync completed 
[INFO ] 2024-12-17 08:02:02.913 - [任务 1][TestMongo] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-17 08:02:02.914 - [任务 1][TestMongo] - [Share CDC Task HZ Reader] - Step 1 - Check connection TestMongo enable share cdc: true 
[INFO ] 2024-12-17 08:02:02.914 - [任务 1][TestMongo] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 1 enable share cdc: true 
[INFO ] 2024-12-17 08:02:02.967 - [任务 1][TestMongo] - Incremental sync completed 
[INFO ] 2024-12-17 08:02:02.969 - [任务 1][TestMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, io.tapdata.flow.engine.V2.sharecdc.exception.ShareCdcUnsupportedException: Not found any log collector task 
[ERROR] 2024-12-17 08:02:02.987 - [任务 1][TestMongo] - Unknown PDK exception occur, io.tapdata.flow.engine.V2.sharecdc.exception.ShareCdcUnsupportedException: Not found any log collector task <-- Error Message -->
Unknown PDK exception occur, io.tapdata.flow.engine.V2.sharecdc.exception.ShareCdcUnsupportedException: Not found any log collector task

<-- Simple Stack Trace -->
Caused by: io.tapdata.flow.engine.V2.sharecdc.exception.ShareCdcUnsupportedException: Not found any log collector task
	io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.canShareCdc(ShareCdcPDKTaskReader.java:190)
	io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.init(ShareCdcPDKTaskReader.java:146)
	io.tapdata.flow.engine.V2.sharecdc.impl.ShareCdcFactory.shareCdcReader(ShareCdcFactory.java:36)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$48(HazelcastSourcePdkDataNode.java:1064)
	io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:173)
	...

<-- Full Stack Trace -->
io.tapdata.flow.engine.V2.sharecdc.exception.ShareCdcUnsupportedException: Not found any log collector task
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:219)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:208)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doShareCdc$49(HazelcastSourcePdkDataNode.java:1069)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doShareCdc(HazelcastSourcePdkDataNode.java:1052)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:775)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.flow.engine.V2.sharecdc.exception.ShareCdcUnsupportedException: Not found any log collector task
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.canShareCdc(ShareCdcPDKTaskReader.java:190)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.init(ShareCdcPDKTaskReader.java:146)
	at io.tapdata.flow.engine.V2.sharecdc.impl.ShareCdcFactory.shareCdcReader(ShareCdcFactory.java:36)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$48(HazelcastSourcePdkDataNode.java:1064)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:173)
	... 12 more

[INFO ] 2024-12-17 08:02:03.023 - [任务 1][TestMongo] - Job suspend in error handle 
[INFO ] 2024-12-17 08:02:03.026 - [任务 1][TestMongo] - Node TestMongo[3d98e94d-c3f1-4935-b4ac-7c008ed38aac] running status set to false 
[INFO ] 2024-12-17 08:02:03.050 - [任务 1][TestMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode_3d98e94d-c3f1-4935-b4ac-7c008ed38aac_1734393722449 
[INFO ] 2024-12-17 08:02:03.051 - [任务 1][TestMongo] - PDK connector node released: HazelcastSourcePdkDataNode_3d98e94d-c3f1-4935-b4ac-7c008ed38aac_1734393722449 
[INFO ] 2024-12-17 08:02:03.053 - [任务 1][TestMongo] - Node TestMongo[3d98e94d-c3f1-4935-b4ac-7c008ed38aac] schema data cleaned 
[INFO ] 2024-12-17 08:02:03.054 - [任务 1][TestMongo] - Node TestMongo[3d98e94d-c3f1-4935-b4ac-7c008ed38aac] monitor closed 
[INFO ] 2024-12-17 08:02:03.055 - [任务 1][TestMongo] - Node TestMongo[3d98e94d-c3f1-4935-b4ac-7c008ed38aac] close complete, cost 38 ms 
[INFO ] 2024-12-17 08:02:03.055 - [任务 1][Dummy] - Node Dummy[da46dffd-bf62-4bf8-8416-ac3787fc9bfd] running status set to false 
[INFO ] 2024-12-17 08:02:03.056 - [任务 1][Dummy] - Stop connector 
[INFO ] 2024-12-17 08:02:03.059 - [任务 1][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_da46dffd-bf62-4bf8-8416-ac3787fc9bfd_1734393722319 
[INFO ] 2024-12-17 08:02:03.059 - [任务 1][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_da46dffd-bf62-4bf8-8416-ac3787fc9bfd_1734393722319 
[INFO ] 2024-12-17 08:02:03.059 - [任务 1][Dummy] - Node Dummy[da46dffd-bf62-4bf8-8416-ac3787fc9bfd] schema data cleaned 
[INFO ] 2024-12-17 08:02:03.060 - [任务 1][Dummy] - Node Dummy[da46dffd-bf62-4bf8-8416-ac3787fc9bfd] monitor closed 
[INFO ] 2024-12-17 08:02:03.060 - [任务 1][Dummy] - Node Dummy[da46dffd-bf62-4bf8-8416-ac3787fc9bfd] close complete, cost 4 ms 
[INFO ] 2024-12-17 08:02:05.303 - [任务 1] - Task [任务 1] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-12-17 08:02:05.304 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-17 08:02:05.431 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2532019 
[INFO ] 2024-12-17 08:02:05.431 - [任务 1] - Stop task milestones: 6760bf26d38f000a8e5cdc31(任务 1)  
[INFO ] 2024-12-17 08:02:05.453 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-12-17 08:02:05.453 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-12-17 08:02:05.493 - [任务 1] - Remove memory task client succeed, task: 任务 1[6760bf26d38f000a8e5cdc31] 
[INFO ] 2024-12-17 08:02:05.494 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6760bf26d38f000a8e5cdc31] 
[INFO ] 2024-12-17 08:02:35.382 - [任务 1] - Task initialization... 
[INFO ] 2024-12-17 08:02:35.386 - [任务 1] - Start task milestones: 6760bf26d38f000a8e5cdc31(任务 1) 
[INFO ] 2024-12-17 08:02:35.512 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-17 08:02:35.595 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-17 08:02:35.651 - [任务 1][Dummy] - Node Dummy[da46dffd-bf62-4bf8-8416-ac3787fc9bfd] start preload schema,table counts: 1 
[INFO ] 2024-12-17 08:02:35.657 - [任务 1][Dummy] - Node Dummy[da46dffd-bf62-4bf8-8416-ac3787fc9bfd] preload schema finished, cost 0 ms 
[INFO ] 2024-12-17 08:02:35.657 - [任务 1][TestMongo] - Node TestMongo[3d98e94d-c3f1-4935-b4ac-7c008ed38aac] start preload schema,table counts: 1 
[INFO ] 2024-12-17 08:02:35.686 - [任务 1][TestMongo] - Node TestMongo[3d98e94d-c3f1-4935-b4ac-7c008ed38aac] preload schema finished, cost 0 ms 
[INFO ] 2024-12-17 08:03:06.624 - [任务 1][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-17 08:03:06.629 - [任务 1][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-17 08:03:06.889 - [任务 1][TestMongo] - Source node "TestMongo" read batch size: 100 
[INFO ] 2024-12-17 08:03:06.889 - [任务 1][TestMongo] - Source node "TestMongo" event queue capacity: 200 
[INFO ] 2024-12-17 08:03:06.889 - [任务 1][TestMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-17 08:03:07.015 - [任务 1][TestMongo] - batch offset found: {},stream offset found: {"cdcOffset":1734393786,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-17 08:03:07.057 - [任务 1][TestMongo] - Initial sync started 
[INFO ] 2024-12-17 08:03:07.061 - [任务 1][TestMongo] - Starting batch read, table name: BMSQL_CONFIG 
[INFO ] 2024-12-17 08:03:07.070 - [任务 1][TestMongo] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-12-17 08:03:07.070 - [任务 1][TestMongo] - Query snapshot row size completed: TestMongo(3d98e94d-c3f1-4935-b4ac-7c008ed38aac) 
[INFO ] 2024-12-17 08:03:07.137 - [任务 1][TestMongo] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-17 08:03:07.140 - [任务 1][TestMongo] - Initial sync completed 
[INFO ] 2024-12-17 08:03:07.140 - [任务 1][TestMongo] - Incremental sync starting... 
[INFO ] 2024-12-17 08:03:07.140 - [任务 1][TestMongo] - Initial sync completed 
[INFO ] 2024-12-17 08:03:07.221 - [任务 1][TestMongo] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-17 08:03:07.221 - [任务 1][TestMongo] - [Share CDC Task HZ Reader] - Step 1 - Check connection TestMongo enable share cdc: true 
[INFO ] 2024-12-17 08:03:07.221 - [任务 1][TestMongo] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 1 enable share cdc: true 
[INFO ] 2024-12-17 08:03:07.251 - [任务 1][TestMongo] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from TestMongo 
[INFO ] 2024-12-17 08:03:07.278 - [任务 1][TestMongo] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Mongo', type='mongodb', uri='mongodb://root:******@*************:7015/a?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-12-17 08:03:07.278 - [任务 1][TestMongo] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-12-17 08:03:07.279 - [任务 1][TestMongo] - Init share cdc reader completed 
[INFO ] 2024-12-17 08:03:07.281 - [任务 1][TestMongo] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-12-17 08:03:07.281 - [任务 1][TestMongo] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-12-17 08:03:07.281 - [任务 1][TestMongo] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-12-17 08:03:07.483 - [任务 1][TestMongo] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6760bf9b9ed70a1ee0d7189d, customId=null, createAt=null, lastUpdAt=null, userId=6760bd4bd38f000a8e5cdb56, lastUpdBy=null, createUser=null, permissionActions=null), sign=6757fbe637af450b34649c29_BMSQL_CONFIG, version=v2, tableName=BMSQL_CONFIG, externalStorageTableName=ExternalStorage_SHARE_CDC_1280072993, shareCdcTaskId=6760bf9bd38f000a8e5cdce2, connectionId=6757fbe637af450b34649c29) 
[INFO ] 2024-12-17 08:03:37.350 - [任务 1][TestMongo] - Incremental sync completed 
[INFO ] 2024-12-17 08:03:37.354 - [任务 1][TestMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast RingBuffer persistence failed. com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=*************:7015, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketException: Host is down}}] 
[ERROR] 2024-12-17 08:03:37.358 - [任务 1][TestMongo] - Unknown PDK exception occur, java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast RingBuffer persistence failed. com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=*************:7015, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketException: Host is down}}] <-- Error Message -->
Unknown PDK exception occur, java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast RingBuffer persistence failed. com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=*************:7015, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketException: Host is down}}]

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=*************:7015, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketException: Host is down}}]
	com.mongodb.internal.connection.BaseCluster.getDescription(BaseCluster.java:179)
	com.mongodb.internal.connection.SingleServerCluster.getDescription(SingleServerCluster.java:41)
	com.mongodb.client.internal.MongoClientDelegate.getConnectedClusterDescription(MongoClientDelegate.java:136)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:94)
	com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast RingBuffer persistence failed. com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=*************:7015, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketException: Host is down}}]
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:219)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:208)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doShareCdc$49(HazelcastSourcePdkDataNode.java:1069)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doShareCdc(HazelcastSourcePdkDataNode.java:1052)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:775)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast RingBuffer persistence failed. com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=*************:7015, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketException: Host is down}}]
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZRingBufferStorage(ExternalStorageUtil.java:74)
	at io.tapdata.construct.constructImpl.ConstructRingBuffer.<init>(ConstructRingBuffer.java:52)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.getConstruct(ShareCdcPDKTaskReader.java:309)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader$ReadRunner.<init>(ShareCdcPDKTaskReader.java:394)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.listen(ShareCdcPDKTaskReader.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$48(HazelcastSourcePdkDataNode.java:1067)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:173)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=*************:7015, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketException: Host is down}}]
	at com.hazelcast.persistence.PersistenceStorage.initRingBufferConfig(PersistenceStorage.java:236)
	at com.hazelcast.persistence.PersistenceStorage.initRingBufferConfig(PersistenceStorage.java:204)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZRingBufferStorage(ExternalStorageUtil.java:71)
	... 18 more
Caused by: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=*************:7015, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.SocketException: Host is down}}]
	at com.mongodb.internal.connection.BaseCluster.getDescription(BaseCluster.java:179)
	at com.mongodb.internal.connection.SingleServerCluster.getDescription(SingleServerCluster.java:41)
	at com.mongodb.client.internal.MongoClientDelegate.getConnectedClusterDescription(MongoClientDelegate.java:136)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:94)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBRingBuffer.createIndex(MongoDBRingBuffer.java:147)
	at com.hazelcast.persistence.store.impl.MongoDBRingBuffer.doInit(MongoDBRingBuffer.java:57)
	at com.hazelcast.persistence.store.impl.MongoDBRingBuffer.doInit(MongoDBRingBuffer.java:28)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initRingBufferConfig(PersistenceStorage.java:226)
	... 20 more

[INFO ] 2024-12-17 08:03:37.400 - [任务 1][TestMongo] - Job suspend in error handle 
[INFO ] 2024-12-17 08:03:37.400 - [任务 1][TestMongo] - Node TestMongo[3d98e94d-c3f1-4935-b4ac-7c008ed38aac] running status set to false 
[INFO ] 2024-12-17 08:03:37.420 - [任务 1][TestMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode_3d98e94d-c3f1-4935-b4ac-7c008ed38aac_1734393786811 
[INFO ] 2024-12-17 08:03:37.420 - [任务 1][TestMongo] - PDK connector node released: HazelcastSourcePdkDataNode_3d98e94d-c3f1-4935-b4ac-7c008ed38aac_1734393786811 
[INFO ] 2024-12-17 08:03:37.420 - [任务 1][TestMongo] - Node TestMongo[3d98e94d-c3f1-4935-b4ac-7c008ed38aac] schema data cleaned 
[INFO ] 2024-12-17 08:03:37.420 - [任务 1][TestMongo] - Node TestMongo[3d98e94d-c3f1-4935-b4ac-7c008ed38aac] monitor closed 
[INFO ] 2024-12-17 08:03:37.425 - [任务 1][TestMongo] - Node TestMongo[3d98e94d-c3f1-4935-b4ac-7c008ed38aac] close complete, cost 26 ms 
[INFO ] 2024-12-17 08:03:37.425 - [任务 1][Dummy] - Node Dummy[da46dffd-bf62-4bf8-8416-ac3787fc9bfd] running status set to false 
[INFO ] 2024-12-17 08:03:37.433 - [任务 1][Dummy] - Stop connector 
[INFO ] 2024-12-17 08:03:37.433 - [任务 1][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_da46dffd-bf62-4bf8-8416-ac3787fc9bfd_1734393786500 
[INFO ] 2024-12-17 08:03:37.435 - [任务 1][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_da46dffd-bf62-4bf8-8416-ac3787fc9bfd_1734393786500 
[INFO ] 2024-12-17 08:03:37.435 - [任务 1][Dummy] - Node Dummy[da46dffd-bf62-4bf8-8416-ac3787fc9bfd] schema data cleaned 
[INFO ] 2024-12-17 08:03:37.440 - [任务 1][Dummy] - Node Dummy[da46dffd-bf62-4bf8-8416-ac3787fc9bfd] monitor closed 
[INFO ] 2024-12-17 08:03:37.441 - [任务 1][Dummy] - Node Dummy[da46dffd-bf62-4bf8-8416-ac3787fc9bfd] close complete, cost 13 ms 
[INFO ] 2024-12-17 08:03:40.802 - [任务 1] - Task [任务 1] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-12-17 08:03:40.811 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-17 08:03:40.811 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4e7d5ac0 
[INFO ] 2024-12-17 08:03:40.947 - [任务 1] - Stop task milestones: 6760bf26d38f000a8e5cdc31(任务 1)  
[INFO ] 2024-12-17 08:03:40.947 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-12-17 08:03:40.947 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-12-17 08:03:40.967 - [任务 1] - Remove memory task client succeed, task: 任务 1[6760bf26d38f000a8e5cdc31] 
[INFO ] 2024-12-17 08:03:40.970 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6760bf26d38f000a8e5cdc31] 
