[INFO ] 2024-10-11 01:04:04.925 - [任务 6] - load tapTable task 670808eb82af0a589c4e5fe2-3d7e8850-6835-40a8-94c3-98e69f87320a complete, cost 6577ms 
[INFO ] 2024-10-11 01:04:12.463 - [任务 6] - load tapTable task 670808eb82af0a589c4e5fe2-6c15d509-7e43-4ec2-96a3-ac2992cba2c5 complete, cost 5783ms 
[INFO ] 2024-10-11 01:04:17.869 - [任务 6] - load tapTable task 670808eb82af0a589c4e5fe2-445cb0c2-af79-4b54-8b10-c40d0efc446b complete, cost 7519ms 
[INFO ] 2024-10-11 01:04:24.977 - [任务 6] - load tapTable task 670808eb82af0a589c4e5fe2-d1e66a58-ca56-4b54-ad4e-ce45fa1053e6 complete, cost 4075ms 
[INFO ] 2024-10-11 01:04:25.014 - [任务 6] - load tapTable task 670808eb82af0a589c4e5fe2-b4d0974d-0fc2-4a21-92d5-1efba6e840d7 complete, cost 16498ms 
[INFO ] 2024-10-11 01:04:32.955 - [任务 6] - load tapTable task 670808eb82af0a589c4e5fe2-d26e55af-d9e0-4305-a181-ded9dde4f622 complete, cost 24526ms 
[INFO ] 2024-10-11 01:04:36.365 - [任务 6] - load tapTable task 670808eb82af0a589c4e5fe2-3128d53c-b123-40f1-920f-4c35bba58f78 complete, cost 28405ms 
[INFO ] 2024-10-11 01:04:38.628 - [任务 6] - load tapTable task 670808eb82af0a589c4e5fe2-ab6bd799-19dd-4b69-82c8-4f69cddfc315 complete, cost 10205ms 
[INFO ] 2024-10-11 01:04:41.600 - [任务 6] - load tapTable task 670808eb82af0a589c4e5fe2-75800a84-4e2d-4c85-870d-955433577a50 complete, cost 13097ms 
[INFO ] 2024-10-11 01:05:56.290 - [任务 6] - load tapTable task 670808eb82af0a589c4e5fe2-b8ed0758-2943-4b4f-a282-382b48ecccc2 complete, cost 48271ms 
[INFO ] 2024-10-11 01:05:57.565 - [任务 6] - Task initialization... 
[INFO ] 2024-10-11 01:05:57.821 - [任务 6] - Start task milestones: 670808eb82af0a589c4e5fe3(任务 6) 
[INFO ] 2024-10-11 01:06:03.681 - [任务 6] - load tapTable task 670808eb82af0a589c4e5fe2-3e917a80-9ca4-4ee5-a783-f2be20564522 complete, cost 5785ms 
[INFO ] 2024-10-11 01:06:03.718 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-11 01:06:03.854 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-11 01:06:03.857 - [任务 6][dummy_test] - Node dummy_test[954260a3-805b-4ad9-82e7-ec87c1d13078] start preload schema,table counts: 1 
[INFO ] 2024-10-11 01:06:03.857 - [任务 6][打印日志节点] - Node 打印日志节点[14e1b4cb-eddf-4e56-942e-53027842a1f7] start preload schema,table counts: 1 
[INFO ] 2024-10-11 01:06:03.857 - [任务 6][BMSQL_ITEM] - Node BMSQL_ITEM[3187195c-a7ff-4aaa-9751-810123248c46] start preload schema,table counts: 1 
[INFO ] 2024-10-11 01:06:03.857 - [任务 6][dummy_test] - Node dummy_test[954260a3-805b-4ad9-82e7-ec87c1d13078] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 01:06:03.857 - [任务 6][打印日志节点] - Node 打印日志节点[14e1b4cb-eddf-4e56-942e-53027842a1f7] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 01:06:03.857 - [任务 6][BMSQL_ITEM] - Node BMSQL_ITEM[3187195c-a7ff-4aaa-9751-810123248c46] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 01:06:03.857 - [任务 6][打印日志节点] - Node custom_processor(打印日志节点: 14e1b4cb-eddf-4e56-942e-53027842a1f7) enable batch process 
[INFO ] 2024-10-11 01:06:07.083 - [任务 6][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-11 01:06:09.708 - [任务 6][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-10-11 01:06:09.714 - [任务 6][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-10-11 01:06:09.716 - [任务 6][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-11 01:06:09.733 - [任务 6][BMSQL_ITEM] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":4751,"gtidSet":""} 
[INFO ] 2024-10-11 01:06:09.734 - [任务 6][BMSQL_ITEM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-11 01:06:09.809 - [任务 6][BMSQL_ITEM] - Initial sync started 
[INFO ] 2024-10-11 01:06:09.810 - [任务 6][BMSQL_ITEM] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-10-11 01:06:09.810 - [任务 6][BMSQL_ITEM] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-10-11 01:06:09.861 - [任务 6][BMSQL_ITEM] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-11 01:06:09.862 - [任务 6][BMSQL_ITEM] - Query table 'BMSQL_ITEM' counts: 3 
[INFO ] 2024-10-11 01:06:09.862 - [任务 6][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-11 01:06:09.862 - [任务 6][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-10-11 01:06:09.863 - [任务 6][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-11 01:06:09.863 - [任务 6][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: {"filename":"binlog.000036","position":4751,"gtidSet":""} 
[INFO ] 2024-10-11 01:06:09.939 - [任务 6][BMSQL_ITEM] - Starting mysql cdc, server name: 9803ccf8-f213-42bf-908c-aeb6732dda8b 
[INFO ] 2024-10-11 01:06:09.941 - [任务 6][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"9803ccf8-f213-42bf-908c-aeb6732dda8b","offset":{"{\"server\":\"9803ccf8-f213-42bf-908c-aeb6732dda8b\"}":"{\"file\":\"binlog.000036\",\"pos\":4751,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1770630842
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9803ccf8-f213-42bf-908c-aeb6732dda8b
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-9803ccf8-f213-42bf-908c-aeb6732dda8b
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 9803ccf8-f213-42bf-908c-aeb6732dda8b
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-11 01:06:10.350 - [任务 6][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-10-11 07:24:17.863 - [任务 6][BMSQL_ITEM] - Node BMSQL_ITEM[3187195c-a7ff-4aaa-9751-810123248c46] running status set to false 
[INFO ] 2024-10-11 07:24:17.883 - [任务 6][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-11 07:24:17.884 - [任务 6][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-10-11 07:24:17.884 - [任务 6][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-10-11 07:24:17.898 - [任务 6][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-3187195c-a7ff-4aaa-9751-810123248c46 
[INFO ] 2024-10-11 07:24:17.898 - [任务 6][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-3187195c-a7ff-4aaa-9751-810123248c46 
[INFO ] 2024-10-11 07:24:17.898 - [任务 6][BMSQL_ITEM] - Node BMSQL_ITEM[3187195c-a7ff-4aaa-9751-810123248c46] schema data cleaned 
[INFO ] 2024-10-11 07:24:17.898 - [任务 6][BMSQL_ITEM] - Node BMSQL_ITEM[3187195c-a7ff-4aaa-9751-810123248c46] monitor closed 
[INFO ] 2024-10-11 07:24:17.899 - [任务 6][BMSQL_ITEM] - Node BMSQL_ITEM[3187195c-a7ff-4aaa-9751-810123248c46] close complete, cost 53 ms 
[INFO ] 2024-10-11 07:24:17.899 - [任务 6][打印日志节点] - Node 打印日志节点[14e1b4cb-eddf-4e56-942e-53027842a1f7] running status set to false 
[INFO ] 2024-10-11 07:24:17.911 - [任务 6][打印日志节点] - Node 打印日志节点[14e1b4cb-eddf-4e56-942e-53027842a1f7] schema data cleaned 
[INFO ] 2024-10-11 07:24:17.911 - [任务 6][打印日志节点] - Node 打印日志节点[14e1b4cb-eddf-4e56-942e-53027842a1f7] monitor closed 
[INFO ] 2024-10-11 07:24:17.912 - [任务 6][打印日志节点] - Node 打印日志节点[14e1b4cb-eddf-4e56-942e-53027842a1f7] close complete, cost 12 ms 
[INFO ] 2024-10-11 07:24:17.912 - [任务 6][dummy_test] - Node dummy_test[954260a3-805b-4ad9-82e7-ec87c1d13078] running status set to false 
[INFO ] 2024-10-11 07:24:17.931 - [任务 6][dummy_test] - Stop connector 
[INFO ] 2024-10-11 07:24:17.932 - [任务 6][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-954260a3-805b-4ad9-82e7-ec87c1d13078 
[INFO ] 2024-10-11 07:24:17.932 - [任务 6][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-954260a3-805b-4ad9-82e7-ec87c1d13078 
[INFO ] 2024-10-11 07:24:17.932 - [任务 6][dummy_test] - Node dummy_test[954260a3-805b-4ad9-82e7-ec87c1d13078] schema data cleaned 
[INFO ] 2024-10-11 07:24:17.932 - [任务 6][dummy_test] - Node dummy_test[954260a3-805b-4ad9-82e7-ec87c1d13078] monitor closed 
[INFO ] 2024-10-11 07:24:18.137 - [任务 6][dummy_test] - Node dummy_test[954260a3-805b-4ad9-82e7-ec87c1d13078] close complete, cost 20 ms 
[INFO ] 2024-10-11 07:24:21.048 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-11 07:24:21.048 - [任务 6] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7d4679e8 
[INFO ] 2024-10-11 07:24:21.048 - [任务 6] - Stop task milestones: 670808eb82af0a589c4e5fe3(任务 6)  
[INFO ] 2024-10-11 07:24:21.173 - [任务 6] - Stopped task aspect(s) 
[INFO ] 2024-10-11 07:24:21.174 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2024-10-11 07:24:21.193 - [任务 6] - Remove memory task client succeed, task: 任务 6[670808eb82af0a589c4e5fe3] 
[INFO ] 2024-10-11 07:24:21.195 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[670808eb82af0a589c4e5fe3] 
[INFO ] 2024-10-11 07:24:31.862 - [任务 6] - load tapTable task 670808eb82af0a589c4e5fe2-49e5f5d1-58b4-4e24-a329-ddd78a626479 complete, cost 5682ms 
