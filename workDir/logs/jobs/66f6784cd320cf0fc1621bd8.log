[INFO ] 2024-09-27 17:18:24.456 - [任务 8] - Task initialization... 
[INFO ] 2024-09-27 17:18:24.565 - [任务 8] - Start task milestones: 66f6784cd320cf0fc1621bd8(任务 8) 
[INFO ] 2024-09-27 17:18:24.565 - [任务 8] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 17:18:24.663 - [任务 8] - The engine receives 任务 8 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 17:18:24.664 - [任务 8][LocalhostMaster] - Node LocalhostMaster[252f440e-90d2-4965-a4ea-4c9cf33a3f45] start preload schema,table counts: 1 
[INFO ] 2024-09-27 17:18:24.665 - [任务 8][local3307] - Node local3307[42c128f4-4ef8-49b4-add8-45094f782f06] start preload schema,table counts: 1 
[INFO ] 2024-09-27 17:18:24.665 - [任务 8][LocalhostMaster] - Node LocalhostMaster[252f440e-90d2-4965-a4ea-4c9cf33a3f45] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 17:18:24.665 - [任务 8][local3307] - Node local3307[42c128f4-4ef8-49b4-add8-45094f782f06] preload schema finished, cost 0 ms 
[WARN ] 2024-09-27 17:18:25.430 - [任务 8][LocalhostMaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1727428704997} and {hostPort=localhost:43306, time=1727399904992} 
[INFO ] 2024-09-27 17:18:25.465 - [任务 8][LocalhostMaster] - Node(LocalhostMaster) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 17:18:25.467 - [任务 8][LocalhostMaster] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-27 17:18:25.578 - [任务 8][local3307] - Source node "local3307" read batch size: 100 
[INFO ] 2024-09-27 17:18:25.579 - [任务 8][local3307] - Source node "local3307" event queue capacity: 200 
[INFO ] 2024-09-27 17:18:25.579 - [任务 8][local3307] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-27 17:18:25.646 - [任务 8][local3307] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":282674752,"gtidSet":""} 
[INFO ] 2024-09-27 17:18:25.647 - [任务 8][local3307] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-27 17:18:25.717 - [任务 8][local3307] - Initial sync started 
[INFO ] 2024-09-27 17:18:25.718 - [任务 8][local3307] - Starting batch read, table name: BMSQL_STOCK 
[INFO ] 2024-09-27 17:18:25.719 - [任务 8][local3307] - Table BMSQL_STOCK is going to be initial synced 
[INFO ] 2024-09-27 17:18:25.904 - [任务 8][local3307] - Query table 'BMSQL_STOCK' counts: 100000 
[INFO ] 2024-09-27 17:18:32.231 - [任务 8][local3307] - Table [BMSQL_STOCK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-27 17:18:32.232 - [任务 8][local3307] - Initial sync completed 
[INFO ] 2024-09-27 17:18:32.232 - [任务 8][local3307] - Incremental sync starting... 
[INFO ] 2024-09-27 17:18:32.232 - [任务 8][local3307] - Initial sync completed 
[INFO ] 2024-09-27 17:18:32.268 - [任务 8][local3307] - Starting stream read, table list: [BMSQL_STOCK], offset: {"filename":"binlog.000032","position":282674752,"gtidSet":""} 
[INFO ] 2024-09-27 17:18:32.269 - [任务 8][local3307] - Starting mysql cdc, server name: 9c9bdef9-d9a6-416e-99b4-da76f08d28bc 
[INFO ] 2024-09-27 17:18:32.352 - [任务 8][local3307] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"9c9bdef9-d9a6-416e-99b4-da76f08d28bc","offset":{"{\"server\":\"9c9bdef9-d9a6-416e-99b4-da76f08d28bc\"}":"{\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1734895745
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9c9bdef9-d9a6-416e-99b4-da76f08d28bc
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-9c9bdef9-d9a6-416e-99b4-da76f08d28bc
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 9c9bdef9-d9a6-416e-99b4-da76f08d28bc
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_STOCK
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 17:18:32.353 - [任务 8][local3307] - Connector Mysql incremental start succeed, tables: [BMSQL_STOCK], data change syncing 
[INFO ] 2024-09-27 18:53:28.739 - [任务 8][local3307] - Node local3307[42c128f4-4ef8-49b4-add8-45094f782f06] running status set to false 
