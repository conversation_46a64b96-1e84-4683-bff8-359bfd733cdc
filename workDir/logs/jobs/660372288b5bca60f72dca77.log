[INFO ] 2024-03-27 09:11:36.394 - [任务 14] - Start task milestones: 660372288b5bca60f72dca77(任务 14) 
[INFO ] 2024-03-27 09:11:36.395 - [任务 14] - Task initialization... 
[INFO ] 2024-03-27 09:11:36.395 - [任务 14] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-03-27 09:11:36.396 - [任务 14] - The engine receives 任务 14 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 09:11:36.396 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] start preload schema,table counts: 1 
[INFO ] 2024-03-27 09:11:36.396 - [任务 14][test2] - Node test2[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] start preload schema,table counts: 1 
[INFO ] 2024-03-27 09:11:36.407 - [任务 14][test2] - Node test2[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] preload schema finished, cost 16 ms 
[INFO ] 2024-03-27 09:11:36.411 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] preload schema finished, cost 21 ms 
[INFO ] 2024-03-27 09:11:37.338 - [任务 14][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 09:11:37.342 - [任务 14][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 09:11:37.342 - [任务 14][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 09:11:37.343 - [任务 14][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":144632188,"gtidSet":""} 
[INFO ] 2024-03-27 09:11:37.485 - [任务 14][CLAIM] - Initial sync started 
[INFO ] 2024-03-27 09:11:37.490 - [任务 14][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-27 09:11:37.490 - [任务 14][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-27 09:11:37.528 - [任务 14][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-27 09:11:44.044 - [任务 14][test2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 09:11:44.503 - [任务 14][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 09:11:44.508 - [任务 14][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 09:11:44.508 - [任务 14][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 09:11:44.509 - [任务 14][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":144632188,"gtidSet":""} 
[INFO ] 2024-03-27 09:11:44.546 - [任务 14][CLAIM] - Starting mysql cdc, server name: 1dc0ec6a-cd8f-4c36-9f90-36aa0bb755cc 
[INFO ] 2024-03-27 09:11:44.552 - [任务 14][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2097724242
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 1dc0ec6a-cd8f-4c36-9f90-36aa0bb755cc
  database.port: 3306
  threadName: Debezium-Mysql-Connector-1dc0ec6a-cd8f-4c36-9f90-36aa0bb755cc
  database.hostname: 127.0.0.1
  database.password: ********
  name: 1dc0ec6a-cd8f-4c36-9f90-36aa0bb755cc
  pdk.offset.string: {"name":"1dc0ec6a-cd8f-4c36-9f90-36aa0bb755cc","offset":{"{\"server\":\"1dc0ec6a-cd8f-4c36-9f90-36aa0bb755cc\"}":"{\"file\":\"binlog.000020\",\"pos\":144632188,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 09:11:44.617 - [任务 14][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 09:13:36.291 - [任务 14][CLAIM] - Read DDL: alter table CLAIM add column `name` varchar(50), about to be packaged as some event(s) 
[INFO ] 2024-03-27 09:13:36.293 - [任务 14][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='1dc0ec6a-cd8f-4c36-9f90-36aa0bb755cc', offset={{"server":"1dc0ec6a-cd8f-4c36-9f90-36aa0bb755cc"}={"ts_sec":1711502003,"file":"binlog.000020","pos":144632447,"server_id":1}}} 
[INFO ] 2024-03-27 09:13:36.293 - [任务 14][CLAIM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@5c1c0148: {"newFields":[{"autoInc":false,"dataType":"varchar(50)","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false}],"referenceTime":1711502003158,"tableId":"CLAIM","time":1711502003615,"type":209} 
[WARN ] 2024-03-27 09:13:36.293 - [任务 14][CLAIM] - DDL events are filtered
 - Event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@5c1c0148: {"newFields":[{"autoInc":false,"dataType":"varchar(50)","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false}],"referenceTime":1711502003158,"tableId":"CLAIM","time":1711502003615,"type":209}
 - Filter: {} 
[INFO ] 2024-03-27 09:13:36.294 - [任务 14] - Stop task milestones: 660372288b5bca60f72dca77(任务 14)  
[INFO ] 2024-03-27 09:13:36.383 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] running status set to false 
[INFO ] 2024-03-27 09:13:36.504 - [任务 14][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-27 09:13:36.506 - [任务 14][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-27 09:13:36.529 - [任务 14][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-97486616-b2b8-450e-9d57-69256a3dbe76 
[INFO ] 2024-03-27 09:13:36.529 - [任务 14][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-97486616-b2b8-450e-9d57-69256a3dbe76 
[INFO ] 2024-03-27 09:13:36.529 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] schema data cleaned 
[INFO ] 2024-03-27 09:13:36.530 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] monitor closed 
[INFO ] 2024-03-27 09:13:36.530 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] close complete, cost 171 ms 
[INFO ] 2024-03-27 09:13:36.530 - [任务 14][test2] - Node test2[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] running status set to false 
[INFO ] 2024-03-27 09:13:36.550 - [任务 14][test2] - PDK connector node stopped: HazelcastTargetPdkDataNode-bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32 
[INFO ] 2024-03-27 09:13:36.551 - [任务 14][test2] - PDK connector node released: HazelcastTargetPdkDataNode-bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32 
[INFO ] 2024-03-27 09:13:36.551 - [任务 14][test2] - Node test2[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] schema data cleaned 
[INFO ] 2024-03-27 09:13:36.553 - [任务 14][test2] - Node test2[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] monitor closed 
[INFO ] 2024-03-27 09:13:36.553 - [任务 14][test2] - Node test2[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] close complete, cost 23 ms 
[INFO ] 2024-03-27 09:13:40.789 - [任务 14] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 09:13:40.790 - [任务 14] - Stopped task aspect(s) 
[INFO ] 2024-03-27 09:13:40.790 - [任务 14] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 09:13:40.813 - [任务 14] - Remove memory task client succeed, task: 任务 14[660372288b5bca60f72dca77] 
[INFO ] 2024-03-27 09:13:40.813 - [任务 14] - Destroy memory task client cache succeed, task: 任务 14[660372288b5bca60f72dca77] 
[INFO ] 2024-03-27 09:16:24.551 - [任务 14] - Start task milestones: 660372288b5bca60f72dca77(任务 14) 
[INFO ] 2024-03-27 09:16:24.551 - [任务 14] - Task initialization... 
[INFO ] 2024-03-27 09:16:24.552 - [任务 14] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 09:16:24.552 - [任务 14] - The engine receives 任务 14 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 09:16:24.552 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] start preload schema,table counts: 1 
[INFO ] 2024-03-27 09:16:24.552 - [任务 14][test2] - Node test2[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] start preload schema,table counts: 1 
[INFO ] 2024-03-27 09:16:24.563 - [任务 14][test2] - Node test2[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] preload schema finished, cost 14 ms 
[INFO ] 2024-03-27 09:16:24.564 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] preload schema finished, cost 17 ms 
[INFO ] 2024-03-27 09:16:25.410 - [任务 14][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 09:16:25.413 - [任务 14][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 09:16:25.413 - [任务 14][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 09:16:25.414 - [任务 14][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":144632447,"gtidSet":""} 
[INFO ] 2024-03-27 09:16:25.512 - [任务 14][CLAIM] - Initial sync started 
[INFO ] 2024-03-27 09:16:25.512 - [任务 14][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-27 09:16:25.519 - [任务 14][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-27 09:16:25.567 - [任务 14][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-27 09:16:32.261 - [任务 14][test2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 09:16:32.697 - [任务 14][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 09:16:32.700 - [任务 14][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 09:16:32.701 - [任务 14][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 09:16:32.702 - [任务 14][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":144632447,"gtidSet":""} 
[INFO ] 2024-03-27 09:16:32.748 - [任务 14][CLAIM] - Starting mysql cdc, server name: 64b45967-b223-4daa-ad1f-5d52b992792f 
[INFO ] 2024-03-27 09:16:32.752 - [任务 14][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1349778102
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 64b45967-b223-4daa-ad1f-5d52b992792f
  database.port: 3306
  threadName: Debezium-Mysql-Connector-64b45967-b223-4daa-ad1f-5d52b992792f
  database.hostname: 127.0.0.1
  database.password: ********
  name: 64b45967-b223-4daa-ad1f-5d52b992792f
  pdk.offset.string: {"name":"64b45967-b223-4daa-ad1f-5d52b992792f","offset":{"{\"server\":\"64b45967-b223-4daa-ad1f-5d52b992792f\"}":"{\"file\":\"binlog.000020\",\"pos\":144632447,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 09:16:32.797 - [任务 14][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 09:17:03.826 - [任务 14][CLAIM] - Read DDL: alter table CLAIM drop column `name`, about to be packaged as some event(s) 
[INFO ] 2024-03-27 09:17:03.829 - [任务 14][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapDropFieldEvent
  - Offset: MysqlStreamOffset{name='64b45967-b223-4daa-ad1f-5d52b992792f', offset={{"server":"64b45967-b223-4daa-ad1f-5d52b992792f"}={"ts_sec":1711502223,"file":"binlog.000020","pos":144632693,"server_id":1}}} 
[INFO ] 2024-03-27 09:17:03.829 - [任务 14][CLAIM] - Source node received an ddl event: TapDropFieldEvent{tableId='CLAIM', fieldName='name'} 
[INFO ] 2024-03-27 09:17:03.833 - [任务 14][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660372288b5bca60f72dca77 
[INFO ] 2024-03-27 09:17:03.893 - [任务 14][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 09:17:33.813 - [任务 14][CLAIM] - Read DDL: alter table CLAIM add column `name` varchar(50), about to be packaged as some event(s) 
[INFO ] 2024-03-27 09:17:33.817 - [任务 14][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='64b45967-b223-4daa-ad1f-5d52b992792f', offset={{"server":"64b45967-b223-4daa-ad1f-5d52b992792f"}={"ts_sec":1711502253,"file":"binlog.000020","pos":144632952,"server_id":1}}} 
[INFO ] 2024-03-27 09:17:33.817 - [任务 14][CLAIM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@2a3531b7: {"newFields":[{"autoInc":false,"dataType":"varchar(50)","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false}],"referenceTime":1711502253581,"tableId":"CLAIM","time":1711502253800,"type":209} 
[INFO ] 2024-03-27 09:17:33.830 - [任务 14][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660372288b5bca60f72dca77 
[INFO ] 2024-03-27 09:17:33.865 - [任务 14][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 09:18:58.826 - [任务 14][CLAIM] - Read DDL: alter table CLAIM modify column `name` varchar(50) comment "名字", about to be packaged as some event(s) 
[INFO ] 2024-03-27 09:18:58.827 - [任务 14][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='64b45967-b223-4daa-ad1f-5d52b992792f', offset={{"server":"64b45967-b223-4daa-ad1f-5d52b992792f"}={"ts_sec":1711502338,"file":"binlog.000020","pos":144633231,"server_id":1}}} 
[INFO ] 2024-03-27 09:18:58.827 - [任务 14][CLAIM] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='CLAIM', fieldName='name', dataTypeChange=io.tapdata.entity.event.ddl.entity.ValueChange@27b73730, checkChange=null, constraintChange=null, nullableChange=null, commentChange=io.tapdata.entity.event.ddl.entity.ValueChange@5d8e65b4, defaultChange=null, primaryChange=null} 
[INFO ] 2024-03-27 09:18:58.833 - [任务 14][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660372288b5bca60f72dca77 
[INFO ] 2024-03-27 09:18:58.904 - [任务 14][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 09:22:50.235 - [任务 14][CLAIM] - Read DDL: alter table CLAIM modify age int unique, about to be packaged as some event(s) 
[INFO ] 2024-03-27 09:22:50.236 - [任务 14][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='64b45967-b223-4daa-ad1f-5d52b992792f', offset={{"server":"64b45967-b223-4daa-ad1f-5d52b992792f"}={"ts_sec":1711502535,"file":"binlog.000020","pos":144633726,"server_id":1}}} 
[INFO ] 2024-03-27 09:22:50.237 - [任务 14][CLAIM] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='CLAIM', fieldName='age', dataTypeChange=io.tapdata.entity.event.ddl.entity.ValueChange@3db4e547, checkChange=null, constraintChange=null, nullableChange=null, commentChange=null, defaultChange=null, primaryChange=null} 
[INFO ] 2024-03-27 09:22:50.237 - [任务 14][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660372288b5bca60f72dca77 
[INFO ] 2024-03-27 09:22:50.237 - [任务 14][CLAIM] - Alter table schema transform finished 
[ERROR] 2024-03-27 09:27:28.692 - [任务 14][CLAIM] - java.lang.RuntimeException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>
 <-- Error Message -->
java.lang.RuntimeException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>


<-- Simple Stack Trace -->
Caused by: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>

	net.sf.jsqlparser.parser.CCJSqlParser.generateParseException(CCJSqlParser.java:31468)
	net.sf.jsqlparser.parser.CCJSqlParser.jj_consume_token(CCJSqlParser.java:31301)
	net.sf.jsqlparser.parser.CCJSqlParser.AlterExpression(CCJSqlParser.java:17496)
	net.sf.jsqlparser.parser.CCJSqlParser.AlterTable(CCJSqlParser.java:17947)
	net.sf.jsqlparser.parser.CCJSqlParser.SingleStatement(CCJSqlParser.java:267)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>

	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>

	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:142)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:333)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:458)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>

	at net.sf.jsqlparser.parser.CCJSqlParser.generateParseException(CCJSqlParser.java:31468)
	at net.sf.jsqlparser.parser.CCJSqlParser.jj_consume_token(CCJSqlParser.java:31301)
	at net.sf.jsqlparser.parser.CCJSqlParser.AlterExpression(CCJSqlParser.java:17496)
	at net.sf.jsqlparser.parser.CCJSqlParser.AlterTable(CCJSqlParser.java:17947)
	at net.sf.jsqlparser.parser.CCJSqlParser.SingleStatement(CCJSqlParser.java:267)
	at net.sf.jsqlparser.parser.CCJSqlParser.Statement(CCJSqlParser.java:153)
	at net.sf.jsqlparser.parser.CCJSqlParserUtil.parseStatement(CCJSqlParserUtil.java:188)
	at net.sf.jsqlparser.parser.CCJSqlParserUtil.parse(CCJSqlParserUtil.java:63)
	at net.sf.jsqlparser.parser.CCJSqlParserUtil.parse(CCJSqlParserUtil.java:38)
	at io.tapdata.common.ddl.parser.CCJSqlParser.parse(CCJSqlParser.java:18)
	at io.tapdata.common.ddl.parser.CCJSqlParser.parse(CCJSqlParser.java:12)
	at io.tapdata.common.ddl.DDLFactory.ddlToTapDDLEvent(DDLFactory.java:76)
	at io.tapdata.common.ddl.DDLFactory.ddlToTapDDLEvent(DDLFactory.java:58)
	at io.tapdata.connector.mysql.MysqlReader.wrapDDL(MysqlReader.java:556)
	at io.tapdata.connector.mysql.MysqlReader.consumeRecords(MysqlReader.java:434)
	at io.debezium.embedded.EmbeddedEngine.run(EmbeddedEngine.java:821)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:328)
	... 19 more

[INFO ] 2024-03-27 09:27:28.753 - [任务 14][CLAIM] - Job suspend in error handle 
[INFO ] 2024-03-27 09:27:28.759 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] running status set to false 
[INFO ] 2024-03-27 09:27:28.796 - [任务 14][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-97486616-b2b8-450e-9d57-69256a3dbe76 
[INFO ] 2024-03-27 09:27:28.797 - [任务 14][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-97486616-b2b8-450e-9d57-69256a3dbe76 
[INFO ] 2024-03-27 09:27:28.797 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] schema data cleaned 
[INFO ] 2024-03-27 09:27:28.797 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] monitor closed 
[INFO ] 2024-03-27 09:27:28.810 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] close complete, cost 138 ms 
[INFO ] 2024-03-27 09:27:28.816 - [任务 14][test2] - Node test2[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] running status set to false 
[INFO ] 2024-03-27 09:27:28.852 - [任务 14][test2] - PDK connector node stopped: HazelcastTargetPdkDataNode-bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32 
[INFO ] 2024-03-27 09:27:28.856 - [任务 14][test2] - PDK connector node released: HazelcastTargetPdkDataNode-bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32 
[INFO ] 2024-03-27 09:27:28.857 - [任务 14][test2] - Node test2[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] schema data cleaned 
[INFO ] 2024-03-27 09:27:28.857 - [任务 14][test2] - Node test2[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] monitor closed 
[INFO ] 2024-03-27 09:27:28.858 - [任务 14][test2] - Node test2[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] close complete, cost 48 ms 
[INFO ] 2024-03-27 09:27:32.326 - [任务 14] - Task [任务 14] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-27 09:27:32.337 - [任务 14] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 09:27:32.349 - [任务 14] - Stop task milestones: 660372288b5bca60f72dca77(任务 14)  
[INFO ] 2024-03-27 09:27:32.370 - [任务 14] - Stopped task aspect(s) 
[INFO ] 2024-03-27 09:27:32.371 - [任务 14] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 09:27:32.411 - [任务 14] - Remove memory task client succeed, task: 任务 14[660372288b5bca60f72dca77] 
[INFO ] 2024-03-27 09:27:32.415 - [任务 14] - Destroy memory task client cache succeed, task: 任务 14[660372288b5bca60f72dca77] 
[INFO ] 2024-03-27 09:30:36.303 - [任务 14] - Start task milestones: 660372288b5bca60f72dca77(任务 14) 
[INFO ] 2024-03-27 09:30:36.304 - [任务 14] - Task initialization... 
[INFO ] 2024-03-27 09:30:36.305 - [任务 14] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 09:30:36.305 - [任务 14] - The engine receives 任务 14 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 09:30:36.306 - [任务 14][test2] - Node test3[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] start preload schema,table counts: 1 
[INFO ] 2024-03-27 09:30:36.306 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] start preload schema,table counts: 1 
[INFO ] 2024-03-27 09:30:36.324 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] preload schema finished, cost 21 ms 
[INFO ] 2024-03-27 09:30:36.325 - [任务 14][test2] - Node test3[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] preload schema finished, cost 23 ms 
[INFO ] 2024-03-27 09:30:37.159 - [任务 14][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 09:30:37.159 - [任务 14][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 09:30:37.159 - [任务 14][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 09:30:37.165 - [任务 14][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":144633973,"gtidSet":""} 
[INFO ] 2024-03-27 09:30:37.218 - [任务 14][CLAIM] - Initial sync started 
[INFO ] 2024-03-27 09:30:37.226 - [任务 14][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-27 09:30:37.228 - [任务 14][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-27 09:30:37.263 - [任务 14][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-27 09:30:44.183 - [任务 14][test2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 09:30:44.834 - [任务 14][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 09:30:44.837 - [任务 14][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 09:30:44.837 - [任务 14][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 09:30:44.841 - [任务 14][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":144633973,"gtidSet":""} 
[INFO ] 2024-03-27 09:30:44.878 - [任务 14][CLAIM] - Starting mysql cdc, server name: e39dddaa-ee93-4b8b-951f-9a28c8514d78 
[INFO ] 2024-03-27 09:30:44.888 - [任务 14][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1954435598
  time.precision.mode: adaptive_time_microseconds
  database.server.name: e39dddaa-ee93-4b8b-951f-9a28c8514d78
  database.port: 3306
  threadName: Debezium-Mysql-Connector-e39dddaa-ee93-4b8b-951f-9a28c8514d78
  database.hostname: 127.0.0.1
  database.password: ********
  name: e39dddaa-ee93-4b8b-951f-9a28c8514d78
  pdk.offset.string: {"name":"e39dddaa-ee93-4b8b-951f-9a28c8514d78","offset":{"{\"server\":\"e39dddaa-ee93-4b8b-951f-9a28c8514d78\"}":"{\"file\":\"binlog.000020\",\"pos\":144633973,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 09:30:44.959 - [任务 14][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 09:35:11.646 - [任务 14][CLAIM] - Read DDL: alter table CLAIM modify age int comment "年级", about to be packaged as some event(s) 
[INFO ] 2024-03-27 09:35:11.647 - [任务 14][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='e39dddaa-ee93-4b8b-951f-9a28c8514d78', offset={{"server":"e39dddaa-ee93-4b8b-951f-9a28c8514d78"}={"ts_sec":1711503291,"file":"binlog.000020","pos":144634799,"server_id":1}}} 
[INFO ] 2024-03-27 09:35:11.648 - [任务 14][CLAIM] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='CLAIM', fieldName='age', dataTypeChange=io.tapdata.entity.event.ddl.entity.ValueChange@263ac720, checkChange=null, constraintChange=null, nullableChange=null, commentChange=io.tapdata.entity.event.ddl.entity.ValueChange@150cbb58, defaultChange=null, primaryChange=null} 
[INFO ] 2024-03-27 09:35:11.648 - [任务 14][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660372288b5bca60f72dca77 
[INFO ] 2024-03-27 09:35:11.648 - [任务 14][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 09:49:19.803 - [任务 14] - Stop task milestones: 660372288b5bca60f72dca77(任务 14)  
[INFO ] 2024-03-27 09:49:19.805 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] running status set to false 
[INFO ] 2024-03-27 09:49:19.805 - [任务 14][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-27 09:49:19.805 - [任务 14][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-27 09:49:19.808 - [任务 14][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-97486616-b2b8-450e-9d57-69256a3dbe76 
[INFO ] 2024-03-27 09:49:19.808 - [任务 14][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-97486616-b2b8-450e-9d57-69256a3dbe76 
[INFO ] 2024-03-27 09:49:19.808 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] schema data cleaned 
[INFO ] 2024-03-27 09:49:19.808 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] monitor closed 
[INFO ] 2024-03-27 09:49:19.809 - [任务 14][CLAIM] - Node CLAIM[97486616-b2b8-450e-9d57-69256a3dbe76] close complete, cost 138 ms 
[INFO ] 2024-03-27 09:49:19.810 - [任务 14][test2] - Node test3[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] running status set to false 
[INFO ] 2024-03-27 09:49:19.830 - [任务 14][test2] - PDK connector node stopped: HazelcastTargetPdkDataNode-bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32 
[INFO ] 2024-03-27 09:49:19.830 - [任务 14][test2] - PDK connector node released: HazelcastTargetPdkDataNode-bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32 
[INFO ] 2024-03-27 09:49:19.831 - [任务 14][test2] - Node test3[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] schema data cleaned 
[INFO ] 2024-03-27 09:49:19.831 - [任务 14][test2] - Node test3[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] monitor closed 
[INFO ] 2024-03-27 09:49:19.831 - [任务 14][test2] - Node test3[bba47ba6-8b7c-4fbe-bbf8-cdb51654cc32] close complete, cost 21 ms 
[INFO ] 2024-03-27 09:49:21.829 - [任务 14] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 09:49:21.829 - [任务 14] - Stopped task aspect(s) 
[INFO ] 2024-03-27 09:49:21.831 - [任务 14] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 09:49:21.868 - [任务 14] - Remove memory task client succeed, task: 任务 14[660372288b5bca60f72dca77] 
[INFO ] 2024-03-27 09:49:21.870 - [任务 14] - Destroy memory task client cache succeed, task: 任务 14[660372288b5bca60f72dca77] 
