[INFO ] 2024-03-31 20:42:28.085 - [任务 33] - Start task milestones: 660959e5e940ce2abb839cf8(任务 33) 
[INFO ] 2024-03-31 20:42:28.088 - [任务 33] - Task initialization... 
[INFO ] 2024-03-31 20:42:28.088 - [任务 33] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-31 20:42:28.089 - [任务 33] - The engine receives 任务 33 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-31 20:42:28.546 - [任务 33][test1] - Node test1[4cab7d4f-e084-47ac-bd8f-2e18c7251852] start preload schema,table counts: 1 
[INFO ] 2024-03-31 20:42:28.547 - [任务 33][CLAIM] - Node CLAIM[a862e777-c945-4f94-9689-98b82cf90a37] start preload schema,table counts: 1 
[INFO ] 2024-03-31 20:42:28.697 - [任务 33][test1] - Node test1[4cab7d4f-e084-47ac-bd8f-2e18c7251852] preload schema finished, cost 142 ms 
[INFO ] 2024-03-31 20:42:28.716 - [任务 33][CLAIM] - Node CLAIM[a862e777-c945-4f94-9689-98b82cf90a37] preload schema finished, cost 141 ms 
[INFO ] 2024-03-31 20:42:29.925 - [任务 33][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-31 20:42:30.250 - [任务 33][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-31 20:42:30.254 - [任务 33][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-31 20:42:30.254 - [任务 33][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-31 20:42:30.425 - [任务 33][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145947124,"gtidSet":""} 
[INFO ] 2024-03-31 20:42:30.426 - [任务 33][CLAIM] - Initial sync started 
[INFO ] 2024-03-31 20:42:30.437 - [任务 33][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-31 20:42:30.438 - [任务 33][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-31 20:42:30.501 - [任务 33][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-31 20:42:30.795 - [任务 33][CLAIM] - Initial sync completed 
[INFO ] 2024-03-31 20:42:30.811 - [任务 33][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-31 20:42:30.820 - [任务 33][CLAIM] - Initial sync completed 
[INFO ] 2024-03-31 20:42:30.829 - [任务 33][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":145947124,"gtidSet":""} 
[INFO ] 2024-03-31 20:42:30.944 - [任务 33][CLAIM] - Starting mysql cdc, server name: f2da87d2-e8d4-48da-ac6c-582b38d2bfb4 
[INFO ] 2024-03-31 20:42:30.948 - [任务 33][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 727817924
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f2da87d2-e8d4-48da-ac6c-582b38d2bfb4
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f2da87d2-e8d4-48da-ac6c-582b38d2bfb4
  database.hostname: 127.0.0.1
  database.password: ********
  name: f2da87d2-e8d4-48da-ac6c-582b38d2bfb4
  pdk.offset.string: {"name":"f2da87d2-e8d4-48da-ac6c-582b38d2bfb4","offset":{"{\"server\":\"f2da87d2-e8d4-48da-ac6c-582b38d2bfb4\"}":"{\"file\":\"binlog.000020\",\"pos\":145947124,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-31 20:42:31.356 - [任务 33][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-31 20:48:34.721 - [任务 33][CLAIM] - Read DDL: alter table CLAIM add column name1 varchar(50), about to be packaged as some event(s) 
[INFO ] 2024-03-31 20:48:34.724 - [任务 33][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='f2da87d2-e8d4-48da-ac6c-582b38d2bfb4', offset={{"server":"f2da87d2-e8d4-48da-ac6c-582b38d2bfb4"}={"ts_sec":1711889314,"file":"binlog.000020","pos":145947382,"server_id":1}}} 
[INFO ] 2024-03-31 20:48:34.812 - [任务 33][CLAIM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@117ce4f: {"newFields":[{"autoInc":false,"dataType":"varchar(50)","name":"name1","nullable":true,"partitionKey":false,"pos":8,"primaryKey":false}],"referenceTime":1711889314290,"tableId":"CLAIM","time":1711889314689,"type":209} 
[INFO ] 2024-03-31 20:48:34.812 - [任务 33][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660959e5e940ce2abb839cf8 
[INFO ] 2024-03-31 20:48:35.170 - [任务 33][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-31 21:35:56.727 - [任务 33][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-31 21:35:56.730 - [任务 33][CLAIM] - Incremental sync completed 
[ERROR] 2024-03-31 21:35:56.730 - [任务 33][CLAIM] - java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected <-- Error Message -->
java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: BinaryLogClient is already connected
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:566)
	com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected
	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:142)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:333)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:458)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.IllegalStateException: BinaryLogClient is already connected
	at com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:566)
	at com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	... 1 more

[INFO ] 2024-03-31 21:35:56.748 - [任务 33][CLAIM] - Job suspend in error handle 
[INFO ] 2024-03-31 21:35:57.236 - [任务 33][CLAIM] - Node CLAIM[a862e777-c945-4f94-9689-98b82cf90a37] running status set to false 
[INFO ] 2024-03-31 21:35:57.236 - [任务 33][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-a862e777-c945-4f94-9689-98b82cf90a37 
[INFO ] 2024-03-31 21:35:57.236 - [任务 33][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-a862e777-c945-4f94-9689-98b82cf90a37 
[INFO ] 2024-03-31 21:35:57.236 - [任务 33][CLAIM] - Node CLAIM[a862e777-c945-4f94-9689-98b82cf90a37] schema data cleaned 
[INFO ] 2024-03-31 21:35:57.237 - [任务 33][CLAIM] - Node CLAIM[a862e777-c945-4f94-9689-98b82cf90a37] monitor closed 
[INFO ] 2024-03-31 21:35:57.238 - [任务 33][CLAIM] - Node CLAIM[a862e777-c945-4f94-9689-98b82cf90a37] close complete, cost 44 ms 
[INFO ] 2024-03-31 21:35:57.238 - [任务 33][test1] - Node test1[4cab7d4f-e084-47ac-bd8f-2e18c7251852] running status set to false 
[INFO ] 2024-03-31 21:35:57.259 - [任务 33][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-4cab7d4f-e084-47ac-bd8f-2e18c7251852 
[INFO ] 2024-03-31 21:35:57.259 - [任务 33][test1] - PDK connector node released: HazelcastTargetPdkDataNode-4cab7d4f-e084-47ac-bd8f-2e18c7251852 
[INFO ] 2024-03-31 21:35:57.259 - [任务 33][test1] - Node test1[4cab7d4f-e084-47ac-bd8f-2e18c7251852] schema data cleaned 
[INFO ] 2024-03-31 21:35:57.260 - [任务 33][test1] - Node test1[4cab7d4f-e084-47ac-bd8f-2e18c7251852] monitor closed 
[INFO ] 2024-03-31 21:35:57.260 - [任务 33][test1] - Node test1[4cab7d4f-e084-47ac-bd8f-2e18c7251852] close complete, cost 21 ms 
[INFO ] 2024-03-31 21:36:01.397 - [任务 33] - Task [任务 33] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-31 21:36:01.397 - [任务 33] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-31 21:36:01.423 - [任务 33] - Stop task milestones: 660959e5e940ce2abb839cf8(任务 33)  
[INFO ] 2024-03-31 21:36:01.423 - [任务 33] - Stopped task aspect(s) 
[INFO ] 2024-03-31 21:36:01.424 - [任务 33] - Snapshot order controller have been removed 
[INFO ] 2024-03-31 21:36:01.443 - [任务 33] - Remove memory task client succeed, task: 任务 33[660959e5e940ce2abb839cf8] 
[INFO ] 2024-03-31 21:36:01.444 - [任务 33] - Destroy memory task client cache succeed, task: 任务 33[660959e5e940ce2abb839cf8] 
