[INFO ] 2024-03-29 18:29:22.988 - [suppliers_import_import_import_import_import] - Start task milestones: 660697bb57c8b774d7a94219(suppliers_import_import_import_import_import) 
[INFO ] 2024-03-29 18:29:23.048 - [suppliers_import_import_import_import_import] - Task initialization... 
[INFO ] 2024-03-29 18:29:23.050 - [suppliers_import_import_import_import_import] - <PERSON><PERSON> performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-03-29 18:29:23.261 - [suppliers_import_import_import_import_import] - The engine receives suppliers_import_import_import_import_import task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 18:29:23.281 - [suppliers_import_import_import_import_import][Products] - Node Products[5e28699d-c16a-43a1-8c99-9407bbde64e7] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:29:23.281 - [suppliers_import_import_import_import_import][Suppliers] - Node Suppliers[44dc4ca0-1a4d-4d10-8d5b-42533f78331a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:29:23.281 - [suppliers_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[862bc484-6f42-4abb-9661-045a322112ef] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:29:23.281 - [suppliers_import_import_import_import_import][Delete Products] - Node Delete Products[f1d4d499-d32f-44c5-abe0-fb97592b2924] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:29:23.281 - [suppliers_import_import_import_import_import][Rename Products] - Node Rename Products[b859308b-b3be-4260-84e0-4b371724946c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:29:23.282 - [suppliers_import_import_import_import_import][merge] - Node merge[8908bf54-39db-428e-b62d-21dbc9861810] start preload schema,table counts: 3 
[INFO ] 2024-03-29 18:29:23.282 - [suppliers_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[d0ae9dd0-9de2-4ea6-93f0-22dcbe1b42d7] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:29:23.282 - [suppliers_import_import_import_import_import][suppliers] - Node suppliers[6ec25617-52a2-4e91-bf14-6c363eb9582e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:29:23.282 - [suppliers_import_import_import_import_import][Suppliers] - Node Suppliers[e3115d49-a73a-4a53-b476-5c6db77a6a0a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:29:23.350 - [suppliers_import_import_import_import_import][Delete Products] - Node Delete Products[f1d4d499-d32f-44c5-abe0-fb97592b2924] preload schema finished, cost 50 ms 
[INFO ] 2024-03-29 18:29:23.352 - [suppliers_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[d0ae9dd0-9de2-4ea6-93f0-22dcbe1b42d7] preload schema finished, cost 52 ms 
[INFO ] 2024-03-29 18:29:23.352 - [suppliers_import_import_import_import_import][Suppliers] - Node Suppliers[44dc4ca0-1a4d-4d10-8d5b-42533f78331a] preload schema finished, cost 50 ms 
[INFO ] 2024-03-29 18:29:23.352 - [suppliers_import_import_import_import_import][suppliers] - Node suppliers[6ec25617-52a2-4e91-bf14-6c363eb9582e] preload schema finished, cost 49 ms 
[INFO ] 2024-03-29 18:29:23.352 - [suppliers_import_import_import_import_import][Suppliers] - Node Suppliers[e3115d49-a73a-4a53-b476-5c6db77a6a0a] preload schema finished, cost 53 ms 
[INFO ] 2024-03-29 18:29:23.352 - [suppliers_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[862bc484-6f42-4abb-9661-045a322112ef] preload schema finished, cost 50 ms 
[INFO ] 2024-03-29 18:29:23.352 - [suppliers_import_import_import_import_import][Rename Products] - Node Rename Products[b859308b-b3be-4260-84e0-4b371724946c] preload schema finished, cost 50 ms 
[INFO ] 2024-03-29 18:29:23.352 - [suppliers_import_import_import_import_import][Products] - Node Products[5e28699d-c16a-43a1-8c99-9407bbde64e7] preload schema finished, cost 50 ms 
[INFO ] 2024-03-29 18:29:23.381 - [suppliers_import_import_import_import_import][merge] - Node merge[8908bf54-39db-428e-b62d-21dbc9861810] preload schema finished, cost 95 ms 
[INFO ] 2024-03-29 18:29:23.381 - [suppliers_import_import_import_import_import][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 18:29:23.382 - [suppliers_import_import_import_import_import][merge] - 
Merge lookup relation{
  Suppliers(d0ae9dd0-9de2-4ea6-93f0-22dcbe1b42d7)
    ->Products(f1d4d499-d32f-44c5-abe0-fb97592b2924)
} 
[INFO ] 2024-03-29 18:29:23.405 - [suppliers_import_import_import_import_import][Suppliers] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:29:23.810 - [suppliers_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Products_f1d4d499-d32f-44c5-abe0-fb97592b2924__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:29:23.810 - [suppliers_import_import_import_import_import][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 18:29:23.810 - [suppliers_import_import_import_import_import][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 18:29:24.003 - [suppliers_import_import_import_import_import][Suppliers] - Source node "Suppliers" read batch size: 500 
[INFO ] 2024-03-29 18:29:24.003 - [suppliers_import_import_import_import_import][Suppliers] - Source node "Suppliers" event queue capacity: 1000 
[INFO ] 2024-03-29 18:29:24.003 - [suppliers_import_import_import_import_import][Suppliers] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:29:24.061 - [suppliers_import_import_import_import_import][Suppliers] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:29:24.072 - [suppliers_import_import_import_import_import][Suppliers] - Initial sync started 
[INFO ] 2024-03-29 18:29:24.072 - [suppliers_import_import_import_import_import][Suppliers] - Starting batch read, table name: Suppliers, offset: null 
[INFO ] 2024-03-29 18:29:24.072 - [suppliers_import_import_import_import_import][Suppliers] - Table Suppliers is going to be initial synced 
[INFO ] 2024-03-29 18:29:24.120 - [suppliers_import_import_import_import_import][Suppliers] - Query table 'Suppliers' counts: 1 
[INFO ] 2024-03-29 18:29:24.120 - [suppliers_import_import_import_import_import][Suppliers] - Initial sync completed 
[INFO ] 2024-03-29 18:29:24.262 - [suppliers_import_import_import_import_import][suppliers] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 18:29:24.526 - [suppliers_import_import_import_import_import][Products] - Source node "Products" read batch size: 500 
[INFO ] 2024-03-29 18:29:24.526 - [suppliers_import_import_import_import_import][Products] - Source node "Products" event queue capacity: 1000 
[INFO ] 2024-03-29 18:29:24.526 - [suppliers_import_import_import_import_import][Products] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:29:24.531 - [suppliers_import_import_import_import_import][Products] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:29:24.738 - [suppliers_import_import_import_import_import] - Node[Products] is waiting for running 
[INFO ] 2024-03-29 18:29:25.804 - [suppliers_import_import_import_import_import] - Node[Suppliers] finish, notify next layer to run 
[INFO ] 2024-03-29 18:29:25.804 - [suppliers_import_import_import_import_import][Products] - Initial sync started 
[INFO ] 2024-03-29 18:29:25.804 - [suppliers_import_import_import_import_import] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 18:29:25.809 - [suppliers_import_import_import_import_import][Products] - Starting batch read, table name: Products, offset: null 
[INFO ] 2024-03-29 18:29:25.814 - [suppliers_import_import_import_import_import][Products] - Table Products is going to be initial synced 
[INFO ] 2024-03-29 18:29:25.882 - [suppliers_import_import_import_import_import][Products] - Query table 'Products' counts: 1 
[INFO ] 2024-03-29 18:29:25.882 - [suppliers_import_import_import_import_import][Products] - Initial sync completed 
[INFO ] 2024-03-29 18:29:27.136 - [suppliers_import_import_import_import_import][Suppliers] - Incremental sync starting... 
[INFO ] 2024-03-29 18:29:27.138 - [suppliers_import_import_import_import_import][Suppliers] - Initial sync completed 
[INFO ] 2024-03-29 18:29:27.138 - [suppliers_import_import_import_import_import][Suppliers] - Starting stream read, table list: [Suppliers], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:29:27.166 - [suppliers_import_import_import_import_import][Suppliers] - Starting mysql cdc, server name: 4f6b314b-fea4-4bbf-a17a-28851b92438e 
[INFO ] 2024-03-29 18:29:27.166 - [suppliers_import_import_import_import_import][Suppliers] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1608949275
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 4f6b314b-fea4-4bbf-a17a-28851b92438e
  database.port: 3307
  threadName: Debezium-Mysql-Connector-4f6b314b-fea4-4bbf-a17a-28851b92438e
  database.hostname: 127.0.0.1
  database.password: ********
  name: 4f6b314b-fea4-4bbf-a17a-28851b92438e
  pdk.offset.string: {"name":"4f6b314b-fea4-4bbf-a17a-28851b92438e","offset":{"{\"server\":\"4f6b314b-fea4-4bbf-a17a-28851b92438e\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Suppliers
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:29:27.371 - [suppliers_import_import_import_import_import][Suppliers] - Connector Mysql incremental start succeed, tables: [Suppliers], data change syncing 
[INFO ] 2024-03-29 18:29:27.893 - [suppliers_import_import_import_import_import][Products] - Incremental sync starting... 
[INFO ] 2024-03-29 18:29:27.893 - [suppliers_import_import_import_import_import][Products] - Initial sync completed 
[INFO ] 2024-03-29 18:29:27.893 - [suppliers_import_import_import_import_import][Products] - Starting stream read, table list: [Products], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:29:27.916 - [suppliers_import_import_import_import_import][Products] - Starting mysql cdc, server name: 19a61a73-5da4-4194-b643-ddcd9e074932 
[INFO ] 2024-03-29 18:29:27.917 - [suppliers_import_import_import_import_import][Products] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 203320752
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 19a61a73-5da4-4194-b643-ddcd9e074932
  database.port: 3307
  threadName: Debezium-Mysql-Connector-19a61a73-5da4-4194-b643-ddcd9e074932
  database.hostname: 127.0.0.1
  database.password: ********
  name: 19a61a73-5da4-4194-b643-ddcd9e074932
  pdk.offset.string: {"name":"19a61a73-5da4-4194-b643-ddcd9e074932","offset":{"{\"server\":\"19a61a73-5da4-4194-b643-ddcd9e074932\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Products
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:29:28.121 - [suppliers_import_import_import_import_import][Products] - Connector Mysql incremental start succeed, tables: [Products], data change syncing 
[INFO ] 2024-03-29 18:36:29.690 - [suppliers_import_import_import_import_import] - Stop task milestones: 660697bb57c8b774d7a94219(suppliers_import_import_import_import_import)  
[INFO ] 2024-03-29 18:36:29.691 - [suppliers_import_import_import_import_import][Products] - Node Products[5e28699d-c16a-43a1-8c99-9407bbde64e7] running status set to false 
[INFO ] 2024-03-29 18:36:29.691 - [suppliers_import_import_import_import_import][Products] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:36:29.691 - [suppliers_import_import_import_import_import][Products] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:36:29.692 - [suppliers_import_import_import_import_import][Products] - PDK connector node stopped: HazelcastSourcePdkDataNode-5e28699d-c16a-43a1-8c99-9407bbde64e7 
[INFO ] 2024-03-29 18:36:29.692 - [suppliers_import_import_import_import_import][Products] - PDK connector node released: HazelcastSourcePdkDataNode-5e28699d-c16a-43a1-8c99-9407bbde64e7 
[INFO ] 2024-03-29 18:36:29.693 - [suppliers_import_import_import_import_import][Products] - Node Products[5e28699d-c16a-43a1-8c99-9407bbde64e7] schema data cleaned 
[INFO ] 2024-03-29 18:36:29.693 - [suppliers_import_import_import_import_import][Products] - Node Products[5e28699d-c16a-43a1-8c99-9407bbde64e7] monitor closed 
[INFO ] 2024-03-29 18:36:29.693 - [suppliers_import_import_import_import_import][Products] - Node Products[5e28699d-c16a-43a1-8c99-9407bbde64e7] close complete, cost 63 ms 
[INFO ] 2024-03-29 18:36:29.770 - [suppliers_import_import_import_import_import][Suppliers] - Node Suppliers[e3115d49-a73a-4a53-b476-5c6db77a6a0a] running status set to false 
[INFO ] 2024-03-29 18:36:29.781 - [suppliers_import_import_import_import_import][Suppliers] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:36:29.781 - [suppliers_import_import_import_import_import][Suppliers] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:36:29.783 - [suppliers_import_import_import_import_import][Suppliers] - PDK connector node stopped: HazelcastSourcePdkDataNode-e3115d49-a73a-4a53-b476-5c6db77a6a0a 
[INFO ] 2024-03-29 18:36:29.783 - [suppliers_import_import_import_import_import][Suppliers] - PDK connector node released: HazelcastSourcePdkDataNode-e3115d49-a73a-4a53-b476-5c6db77a6a0a 
[INFO ] 2024-03-29 18:36:29.783 - [suppliers_import_import_import_import_import][Suppliers] - Node Suppliers[e3115d49-a73a-4a53-b476-5c6db77a6a0a] schema data cleaned 
[INFO ] 2024-03-29 18:36:29.783 - [suppliers_import_import_import_import_import][Suppliers] - Node Suppliers[e3115d49-a73a-4a53-b476-5c6db77a6a0a] monitor closed 
[INFO ] 2024-03-29 18:36:29.783 - [suppliers_import_import_import_import_import][Suppliers] - Node Suppliers[e3115d49-a73a-4a53-b476-5c6db77a6a0a] close complete, cost 90 ms 
[INFO ] 2024-03-29 18:36:29.799 - [suppliers_import_import_import_import_import][Suppliers] - Node Suppliers[44dc4ca0-1a4d-4d10-8d5b-42533f78331a] running status set to false 
[INFO ] 2024-03-29 18:36:29.827 - [suppliers_import_import_import_import_import][Suppliers] - PDK connector node stopped: ScriptExecutor-TargetMysql-a3af36d3-ae1b-47d1-8127-d79cc5578bbf 
[INFO ] 2024-03-29 18:36:29.827 - [suppliers_import_import_import_import_import][Suppliers] - PDK connector node released: ScriptExecutor-TargetMysql-a3af36d3-ae1b-47d1-8127-d79cc5578bbf 
[INFO ] 2024-03-29 18:36:29.831 - [suppliers_import_import_import_import_import][Suppliers] - [ScriptExecutorsManager-660697bb57c8b774d7a94219-44dc4ca0-1a4d-4d10-8d5b-42533f78331a-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-29 18:36:29.841 - [suppliers_import_import_import_import_import][Suppliers] - PDK connector node stopped: ScriptExecutor-TestMongo-36540bf1-f5ff-4aa0-bfd6-81151a6525de 
[INFO ] 2024-03-29 18:36:29.841 - [suppliers_import_import_import_import_import][Suppliers] - PDK connector node released: ScriptExecutor-TestMongo-36540bf1-f5ff-4aa0-bfd6-81151a6525de 
[INFO ] 2024-03-29 18:36:29.841 - [suppliers_import_import_import_import_import][Suppliers] - [ScriptExecutorsManager-660697bb57c8b774d7a94219-44dc4ca0-1a4d-4d10-8d5b-42533f78331a-6600b651928fc21057b4810b] schema data cleaned 
[INFO ] 2024-03-29 18:36:29.858 - [suppliers_import_import_import_import_import][Suppliers] - Node Suppliers[44dc4ca0-1a4d-4d10-8d5b-42533f78331a] schema data cleaned 
[INFO ] 2024-03-29 18:36:29.863 - [suppliers_import_import_import_import_import][Suppliers] - Node Suppliers[44dc4ca0-1a4d-4d10-8d5b-42533f78331a] monitor closed 
[INFO ] 2024-03-29 18:36:29.863 - [suppliers_import_import_import_import_import][Suppliers] - Node Suppliers[44dc4ca0-1a4d-4d10-8d5b-42533f78331a] close complete, cost 77 ms 
[INFO ] 2024-03-29 18:36:29.864 - [suppliers_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[862bc484-6f42-4abb-9661-045a322112ef] running status set to false 
[INFO ] 2024-03-29 18:36:29.933 - [suppliers_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[862bc484-6f42-4abb-9661-045a322112ef] schema data cleaned 
[INFO ] 2024-03-29 18:36:29.933 - [suppliers_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[862bc484-6f42-4abb-9661-045a322112ef] monitor closed 
[INFO ] 2024-03-29 18:36:29.934 - [suppliers_import_import_import_import_import][Rename Suppliers] - Node Rename Suppliers[862bc484-6f42-4abb-9661-045a322112ef] close complete, cost 70 ms 
[INFO ] 2024-03-29 18:36:29.934 - [suppliers_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[d0ae9dd0-9de2-4ea6-93f0-22dcbe1b42d7] running status set to false 
[INFO ] 2024-03-29 18:36:29.997 - [suppliers_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[d0ae9dd0-9de2-4ea6-93f0-22dcbe1b42d7] schema data cleaned 
[INFO ] 2024-03-29 18:36:29.997 - [suppliers_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[d0ae9dd0-9de2-4ea6-93f0-22dcbe1b42d7] monitor closed 
[INFO ] 2024-03-29 18:36:29.997 - [suppliers_import_import_import_import_import][Delete Suppliers] - Node Delete Suppliers[d0ae9dd0-9de2-4ea6-93f0-22dcbe1b42d7] close complete, cost 63 ms 
[INFO ] 2024-03-29 18:36:29.998 - [suppliers_import_import_import_import_import][Rename Products] - Node Rename Products[b859308b-b3be-4260-84e0-4b371724946c] running status set to false 
[INFO ] 2024-03-29 18:36:30.060 - [suppliers_import_import_import_import_import][Rename Products] - Node Rename Products[b859308b-b3be-4260-84e0-4b371724946c] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.061 - [suppliers_import_import_import_import_import][Rename Products] - Node Rename Products[b859308b-b3be-4260-84e0-4b371724946c] monitor closed 
[INFO ] 2024-03-29 18:36:30.061 - [suppliers_import_import_import_import_import][Rename Products] - Node Rename Products[b859308b-b3be-4260-84e0-4b371724946c] close complete, cost 63 ms 
[INFO ] 2024-03-29 18:36:30.061 - [suppliers_import_import_import_import_import][Delete Products] - Node Delete Products[f1d4d499-d32f-44c5-abe0-fb97592b2924] running status set to false 
[INFO ] 2024-03-29 18:36:30.141 - [suppliers_import_import_import_import_import][Delete Products] - Node Delete Products[f1d4d499-d32f-44c5-abe0-fb97592b2924] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.151 - [suppliers_import_import_import_import_import][Delete Products] - Node Delete Products[f1d4d499-d32f-44c5-abe0-fb97592b2924] monitor closed 
[INFO ] 2024-03-29 18:36:30.151 - [suppliers_import_import_import_import_import][Delete Products] - Node Delete Products[f1d4d499-d32f-44c5-abe0-fb97592b2924] close complete, cost 80 ms 
[INFO ] 2024-03-29 18:36:30.151 - [suppliers_import_import_import_import_import][merge] - Node merge[8908bf54-39db-428e-b62d-21dbc9861810] running status set to false 
[INFO ] 2024-03-29 18:36:30.151 - [suppliers_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Products_f1d4d499-d32f-44c5-abe0-fb97592b2924__TPORIG 
[INFO ] 2024-03-29 18:36:30.170 - [suppliers_import_import_import_import_import][merge] - Node merge[8908bf54-39db-428e-b62d-21dbc9861810] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.170 - [suppliers_import_import_import_import_import][merge] - Node merge[8908bf54-39db-428e-b62d-21dbc9861810] monitor closed 
[INFO ] 2024-03-29 18:36:30.171 - [suppliers_import_import_import_import_import][merge] - Node merge[8908bf54-39db-428e-b62d-21dbc9861810] close complete, cost 29 ms 
[INFO ] 2024-03-29 18:36:30.172 - [suppliers_import_import_import_import_import][suppliers] - Node suppliers[6ec25617-52a2-4e91-bf14-6c363eb9582e] running status set to false 
[INFO ] 2024-03-29 18:36:30.198 - [suppliers_import_import_import_import_import][suppliers] - PDK connector node stopped: HazelcastTargetPdkDataNode-6ec25617-52a2-4e91-bf14-6c363eb9582e 
[INFO ] 2024-03-29 18:36:30.198 - [suppliers_import_import_import_import_import][suppliers] - PDK connector node released: HazelcastTargetPdkDataNode-6ec25617-52a2-4e91-bf14-6c363eb9582e 
[INFO ] 2024-03-29 18:36:30.198 - [suppliers_import_import_import_import_import][suppliers] - Node suppliers[6ec25617-52a2-4e91-bf14-6c363eb9582e] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.198 - [suppliers_import_import_import_import_import][suppliers] - Node suppliers[6ec25617-52a2-4e91-bf14-6c363eb9582e] monitor closed 
[INFO ] 2024-03-29 18:36:30.198 - [suppliers_import_import_import_import_import][suppliers] - Node suppliers[6ec25617-52a2-4e91-bf14-6c363eb9582e] close complete, cost 26 ms 
[INFO ] 2024-03-29 18:36:33.781 - [suppliers_import_import_import_import_import] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 18:36:33.781 - [suppliers_import_import_import_import_import] - Stopped task aspect(s) 
[INFO ] 2024-03-29 18:36:33.781 - [suppliers_import_import_import_import_import] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 18:36:33.803 - [suppliers_import_import_import_import_import] - Remove memory task client succeed, task: suppliers_import_import_import_import_import[660697bb57c8b774d7a94219] 
[INFO ] 2024-03-29 18:36:33.806 - [suppliers_import_import_import_import_import] - Destroy memory task client cache succeed, task: suppliers_import_import_import_import_import[660697bb57c8b774d7a94219] 
