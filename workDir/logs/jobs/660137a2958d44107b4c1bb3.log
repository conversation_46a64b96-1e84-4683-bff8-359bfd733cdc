[INFO ] 2024-03-27 10:01:55.974 - [任务 6(100)][f73db505-125e-4c02-b736-f9d5791db6a9] - Node f73db505-125e-4c02-b736-f9d5791db6a9[f73db505-125e-4c02-b736-f9d5791db6a9] start preload schema,table counts: 0 
[INFO ] 2024-03-27 10:01:55.991 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:01:55.992 - [任务 6(100)][f73db505-125e-4c02-b736-f9d5791db6a9] - Node f73db505-125e-4c02-b736-f9d5791db6a9[f73db505-125e-4c02-b736-f9d5791db6a9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 10:01:55.992 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:01:55.992 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 10:01:55.992 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] preload schema finished, cost 2 ms 
[INFO ] 2024-03-27 10:01:56.153 - [任务 6(100)][dummy_test] - Start dummy connector 
[INFO ] 2024-03-27 10:01:56.173 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] running status set to false 
[INFO ] 2024-03-27 10:01:56.175 - [任务 6(100)][dummy_test] - Stop connector 
[INFO ] 2024-03-27 10:01:56.178 - [任务 6(100)][dummy_test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d9e76165-ff0e-4d72-92ff-eb40749d7c7d 
[INFO ] 2024-03-27 10:01:56.179 - [任务 6(100)][dummy_test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d9e76165-ff0e-4d72-92ff-eb40749d7c7d 
[INFO ] 2024-03-27 10:01:56.179 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] schema data cleaned 
[INFO ] 2024-03-27 10:01:56.179 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] monitor closed 
[INFO ] 2024-03-27 10:01:56.179 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] close complete, cost 9 ms 
[INFO ] 2024-03-27 10:01:56.226 - [任务 6(100)][增强JS] - test5 
[INFO ] 2024-03-27 10:01:56.231 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] running status set to false 
[INFO ] 2024-03-27 10:01:56.236 - [任务 6(100)][f73db505-125e-4c02-b736-f9d5791db6a9] - Node f73db505-125e-4c02-b736-f9d5791db6a9[f73db505-125e-4c02-b736-f9d5791db6a9] running status set to false 
[INFO ] 2024-03-27 10:01:56.237 - [任务 6(100)][f73db505-125e-4c02-b736-f9d5791db6a9] - Node f73db505-125e-4c02-b736-f9d5791db6a9[f73db505-125e-4c02-b736-f9d5791db6a9] schema data cleaned 
[INFO ] 2024-03-27 10:01:56.237 - [任务 6(100)][f73db505-125e-4c02-b736-f9d5791db6a9] - Node f73db505-125e-4c02-b736-f9d5791db6a9[f73db505-125e-4c02-b736-f9d5791db6a9] monitor closed 
[INFO ] 2024-03-27 10:01:56.237 - [任务 6(100)][f73db505-125e-4c02-b736-f9d5791db6a9] - Node f73db505-125e-4c02-b736-f9d5791db6a9[f73db505-125e-4c02-b736-f9d5791db6a9] close complete, cost 1 ms 
[INFO ] 2024-03-27 10:01:56.237 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-TestDumy-7fb4194f-7e5c-4d6f-a3f9-309e7d342c95 
[INFO ] 2024-03-27 10:01:56.237 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-TestDumy-7fb4194f-7e5c-4d6f-a3f9-309e7d342c95 
[INFO ] 2024-03-27 10:01:56.237 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-660137a2958d44107b4c1bb3-fb3c3684-8044-4d54-a326-53592ce98fef-6600b629928fc21057b480fd] schema data cleaned 
[INFO ] 2024-03-27 10:01:56.239 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] schema data cleaned 
[INFO ] 2024-03-27 10:01:56.239 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] monitor closed 
[INFO ] 2024-03-27 10:01:56.239 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] close complete, cost 8 ms 
[INFO ] 2024-03-27 10:01:56.242 - [任务 6(100)] - load tapTable task 660137a2958d44107b4c1bb3-f73db505-125e-4c02-b736-f9d5791db6a9 complete, cost 448ms 
[INFO ] 2024-03-27 10:01:57.879 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:01:57.883 - [任务 6(100)][f811738e-852d-43c5-b2d0-51bd284ebb3b] - Node f811738e-852d-43c5-b2d0-51bd284ebb3b[f811738e-852d-43c5-b2d0-51bd284ebb3b] start preload schema,table counts: 0 
[INFO ] 2024-03-27 10:01:57.884 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:01:57.884 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 10:01:57.884 - [任务 6(100)][f811738e-852d-43c5-b2d0-51bd284ebb3b] - Node f811738e-852d-43c5-b2d0-51bd284ebb3b[f811738e-852d-43c5-b2d0-51bd284ebb3b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 10:01:57.884 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 10:01:57.945 - [任务 6(100)][dummy_test] - Start dummy connector 
[INFO ] 2024-03-27 10:01:57.952 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] running status set to false 
[INFO ] 2024-03-27 10:01:57.952 - [任务 6(100)][dummy_test] - Stop connector 
[INFO ] 2024-03-27 10:01:57.955 - [任务 6(100)][dummy_test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d9e76165-ff0e-4d72-92ff-eb40749d7c7d 
[INFO ] 2024-03-27 10:01:57.955 - [任务 6(100)][dummy_test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d9e76165-ff0e-4d72-92ff-eb40749d7c7d 
[INFO ] 2024-03-27 10:01:57.956 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] schema data cleaned 
[INFO ] 2024-03-27 10:01:57.956 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] monitor closed 
[INFO ] 2024-03-27 10:01:57.956 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] close complete, cost 4 ms 
[INFO ] 2024-03-27 10:01:58.011 - [任务 6(100)][增强JS] - test5 
[INFO ] 2024-03-27 10:01:58.014 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] running status set to false 
[INFO ] 2024-03-27 10:01:58.016 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-TestDumy-a0133555-3bf6-444a-a967-3cc6c1ad8e91 
[INFO ] 2024-03-27 10:01:58.017 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-TestDumy-a0133555-3bf6-444a-a967-3cc6c1ad8e91 
[INFO ] 2024-03-27 10:01:58.017 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-660137a2958d44107b4c1bb3-fb3c3684-8044-4d54-a326-53592ce98fef-6600b629928fc21057b480fd] schema data cleaned 
[INFO ] 2024-03-27 10:01:58.018 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] schema data cleaned 
[INFO ] 2024-03-27 10:01:58.018 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] monitor closed 
[INFO ] 2024-03-27 10:01:58.018 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] close complete, cost 4 ms 
[INFO ] 2024-03-27 10:01:58.019 - [任务 6(100)][f811738e-852d-43c5-b2d0-51bd284ebb3b] - Node f811738e-852d-43c5-b2d0-51bd284ebb3b[f811738e-852d-43c5-b2d0-51bd284ebb3b] running status set to false 
[INFO ] 2024-03-27 10:01:58.019 - [任务 6(100)][f811738e-852d-43c5-b2d0-51bd284ebb3b] - Node f811738e-852d-43c5-b2d0-51bd284ebb3b[f811738e-852d-43c5-b2d0-51bd284ebb3b] schema data cleaned 
[INFO ] 2024-03-27 10:01:58.019 - [任务 6(100)][f811738e-852d-43c5-b2d0-51bd284ebb3b] - Node f811738e-852d-43c5-b2d0-51bd284ebb3b[f811738e-852d-43c5-b2d0-51bd284ebb3b] monitor closed 
[INFO ] 2024-03-27 10:01:58.019 - [任务 6(100)][f811738e-852d-43c5-b2d0-51bd284ebb3b] - Node f811738e-852d-43c5-b2d0-51bd284ebb3b[f811738e-852d-43c5-b2d0-51bd284ebb3b] close complete, cost 0 ms 
[INFO ] 2024-03-27 10:01:58.020 - [任务 6(100)] - load tapTable task 660137a2958d44107b4c1bb3-f811738e-852d-43c5-b2d0-51bd284ebb3b complete, cost 227ms 
[INFO ] 2024-03-27 10:02:02.444 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:02:02.445 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:02:02.445 - [任务 6(100)][0a29392c-8dcb-4508-b0e4-2adf942998e2] - Node 0a29392c-8dcb-4508-b0e4-2adf942998e2[0a29392c-8dcb-4508-b0e4-2adf942998e2] start preload schema,table counts: 0 
[INFO ] 2024-03-27 10:02:02.445 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 10:02:02.445 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 10:02:02.445 - [任务 6(100)][0a29392c-8dcb-4508-b0e4-2adf942998e2] - Node 0a29392c-8dcb-4508-b0e4-2adf942998e2[0a29392c-8dcb-4508-b0e4-2adf942998e2] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 10:02:02.468 - [任务 6(100)][dummy_test] - Start dummy connector 
[INFO ] 2024-03-27 10:02:02.472 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] running status set to false 
[INFO ] 2024-03-27 10:02:02.472 - [任务 6(100)][dummy_test] - Stop connector 
[INFO ] 2024-03-27 10:02:02.474 - [任务 6(100)][dummy_test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d9e76165-ff0e-4d72-92ff-eb40749d7c7d 
[INFO ] 2024-03-27 10:02:02.474 - [任务 6(100)][dummy_test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d9e76165-ff0e-4d72-92ff-eb40749d7c7d 
[INFO ] 2024-03-27 10:02:02.474 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] schema data cleaned 
[INFO ] 2024-03-27 10:02:02.474 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] monitor closed 
[INFO ] 2024-03-27 10:02:02.474 - [任务 6(100)][dummy_test] - Node dummy_test[d9e76165-ff0e-4d72-92ff-eb40749d7c7d] close complete, cost 3 ms 
[INFO ] 2024-03-27 10:02:02.586 - [任务 6(100)][增强JS] - test5 
[INFO ] 2024-03-27 10:02:02.594 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] running status set to false 
[INFO ] 2024-03-27 10:02:02.595 - [任务 6(100)][0a29392c-8dcb-4508-b0e4-2adf942998e2] - Node 0a29392c-8dcb-4508-b0e4-2adf942998e2[0a29392c-8dcb-4508-b0e4-2adf942998e2] running status set to false 
[INFO ] 2024-03-27 10:02:02.597 - [任务 6(100)][0a29392c-8dcb-4508-b0e4-2adf942998e2] - Node 0a29392c-8dcb-4508-b0e4-2adf942998e2[0a29392c-8dcb-4508-b0e4-2adf942998e2] schema data cleaned 
[INFO ] 2024-03-27 10:02:02.597 - [任务 6(100)][0a29392c-8dcb-4508-b0e4-2adf942998e2] - Node 0a29392c-8dcb-4508-b0e4-2adf942998e2[0a29392c-8dcb-4508-b0e4-2adf942998e2] monitor closed 
[INFO ] 2024-03-27 10:02:02.599 - [任务 6(100)][0a29392c-8dcb-4508-b0e4-2adf942998e2] - Node 0a29392c-8dcb-4508-b0e4-2adf942998e2[0a29392c-8dcb-4508-b0e4-2adf942998e2] close complete, cost 2 ms 
[INFO ] 2024-03-27 10:02:02.599 - [任务 6(100)][增强JS] - PDK connector node stopped: ScriptExecutor-TestDumy-16b835ab-6174-4a22-9d65-1f792ad65aae 
[INFO ] 2024-03-27 10:02:02.600 - [任务 6(100)][增强JS] - PDK connector node released: ScriptExecutor-TestDumy-16b835ab-6174-4a22-9d65-1f792ad65aae 
[INFO ] 2024-03-27 10:02:02.600 - [任务 6(100)][增强JS] - [ScriptExecutorsManager-660137a2958d44107b4c1bb3-fb3c3684-8044-4d54-a326-53592ce98fef-6600b629928fc21057b480fd] schema data cleaned 
[INFO ] 2024-03-27 10:02:02.601 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] schema data cleaned 
[INFO ] 2024-03-27 10:02:02.601 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] monitor closed 
[INFO ] 2024-03-27 10:02:02.601 - [任务 6(100)][增强JS] - Node 增强JS[fb3c3684-8044-4d54-a326-53592ce98fef] close complete, cost 10 ms 
[INFO ] 2024-03-27 10:02:02.603 - [任务 6(100)] - load tapTable task 660137a2958d44107b4c1bb3-0a29392c-8dcb-4508-b0e4-2adf942998e2 complete, cost 197ms 
