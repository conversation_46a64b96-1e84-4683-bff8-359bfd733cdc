[INFO ] 2024-07-02 17:28:33.037 - [Heartbeat-SourceMongo] - Start task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo) 
[INFO ] 2024-07-02 17:28:33.061 - [Heartbeat-SourceMongo] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:28:33.061 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:28:33.061 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@54c1dee5 
[INFO ] 2024-07-02 17:28:33.061 - [Heartbeat-SourceMongo] - Stop task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo)  
[INFO ] 2024-07-02 17:28:33.183 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:28:33.183 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-02 17:28:33.184 - [Heartbeat-SourceMongo] - Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-02 17:28:59.698 - [Heartbeat-SourceMongo] - Start task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo) 
[INFO ] 2024-07-02 17:28:59.747 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:28:59.748 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:28:59.749 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1e6de23d 
[INFO ] 2024-07-02 17:28:59.749 - [Heartbeat-SourceMongo] - Stop task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo)  
[INFO ] 2024-07-02 17:28:59.859 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:28:59.860 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-02 17:28:59.861 - [Heartbeat-SourceMongo] - Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-02 17:37:40.406 - [Heartbeat-SourceMongo] - Start task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo) 
[INFO ] 2024-07-02 17:37:40.487 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:37:40.487 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:37:40.488 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@46e4c081 
[INFO ] 2024-07-02 17:37:40.488 - [Heartbeat-SourceMongo] - Stop task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo)  
[INFO ] 2024-07-02 17:37:40.606 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:37:40.606 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-02 17:37:40.611 - [Heartbeat-SourceMongo] - Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-02 17:37:56.482 - [Heartbeat-SourceMongo] - Start task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo) 
[INFO ] 2024-07-02 17:37:56.532 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:37:56.532 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:37:56.533 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6adf9d07 
[INFO ] 2024-07-02 17:37:56.533 - [Heartbeat-SourceMongo] - Stop task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo)  
[INFO ] 2024-07-02 17:37:56.644 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:37:56.644 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-02 17:37:56.645 - [Heartbeat-SourceMongo] - Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-02 17:40:25.916 - [Heartbeat-SourceMongo] - Start task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo) 
[INFO ] 2024-07-02 17:40:29.393 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:40:29.393 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:40:29.394 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@773acb7 
[INFO ] 2024-07-02 17:40:29.394 - [Heartbeat-SourceMongo] - Stop task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo)  
[INFO ] 2024-07-02 17:40:29.510 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:40:29.511 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-02 17:40:29.511 - [Heartbeat-SourceMongo] - Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-02 17:41:03.398 - [Heartbeat-SourceMongo] - Start task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo) 
[INFO ] 2024-07-02 17:41:03.398 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:41:03.399 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:41:03.400 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4f7e435d 
[INFO ] 2024-07-02 17:41:03.400 - [Heartbeat-SourceMongo] - Stop task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo)  
[INFO ] 2024-07-02 17:41:03.515 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:41:03.515 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-02 17:41:03.717 - [Heartbeat-SourceMongo] - Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-02 17:41:22.805 - [Heartbeat-SourceMongo] - Start task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo) 
[INFO ] 2024-07-02 17:41:22.949 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:41:22.951 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:41:22.954 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3359332f 
[INFO ] 2024-07-02 17:41:22.954 - [Heartbeat-SourceMongo] - Stop task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo)  
[INFO ] 2024-07-02 17:41:23.080 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:41:33.305 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-02 17:41:33.323 - [Heartbeat-SourceMongo] - Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-02 17:42:08.020 - [Heartbeat-SourceMongo] - Start task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo) 
[INFO ] 2024-07-02 17:42:56.351 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:42:56.351 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:42:56.353 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@31e1b6e 
[INFO ] 2024-07-02 17:42:56.353 - [Heartbeat-SourceMongo] - Stop task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo)  
[INFO ] 2024-07-02 17:42:56.471 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:42:56.475 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-02 17:42:56.475 - [Heartbeat-SourceMongo] - Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-02 17:44:24.635 - [Heartbeat-SourceMongo] - Start task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo) 
[INFO ] 2024-07-02 17:45:54.763 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:45:54.766 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:45:54.767 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@664064e 
[INFO ] 2024-07-02 17:45:54.767 - [Heartbeat-SourceMongo] - Stop task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo)  
[INFO ] 2024-07-02 17:45:54.888 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:45:54.889 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-02 17:45:54.890 - [Heartbeat-SourceMongo] - Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-02 17:46:39.825 - [Heartbeat-SourceMongo] - Start task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo) 
[INFO ] 2024-07-02 17:46:39.902 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:46:39.902 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:46:39.903 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4040b6cc 
[INFO ] 2024-07-02 17:46:39.903 - [Heartbeat-SourceMongo] - Stop task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo)  
[INFO ] 2024-07-02 17:46:40.021 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:46:40.021 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-02 17:46:40.022 - [Heartbeat-SourceMongo] - Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-02 17:47:21.459 - [Heartbeat-SourceMongo] - Start task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo) 
[INFO ] 2024-07-02 17:47:37.224 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:47:37.226 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:47:37.228 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@765144ad 
[INFO ] 2024-07-02 17:47:37.228 - [Heartbeat-SourceMongo] - Stop task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo)  
[INFO ] 2024-07-02 17:47:37.394 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:47:37.394 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-02 17:47:37.395 - [Heartbeat-SourceMongo] - Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-02 17:51:25.336 - [Heartbeat-SourceMongo] - Start task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo) 
[INFO ] 2024-07-02 17:53:45.498 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:53:45.544 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:53:45.547 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@cba695f 
[INFO ] 2024-07-02 17:53:45.547 - [Heartbeat-SourceMongo] - Stop task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo)  
[INFO ] 2024-07-02 17:53:45.666 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:53:45.666 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-02 17:53:45.668 - [Heartbeat-SourceMongo] - Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-02 17:54:10.392 - [Heartbeat-SourceMongo] - Start task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo) 
[INFO ] 2024-07-02 17:54:13.598 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:54:13.604 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:54:13.606 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1acc62f2 
[INFO ] 2024-07-02 17:54:13.607 - [Heartbeat-SourceMongo] - Stop task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo)  
[INFO ] 2024-07-02 17:54:13.732 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:54:13.733 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-02 17:54:13.943 - [Heartbeat-SourceMongo] - Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-02 17:54:23.085 - [Heartbeat-SourceMongo] - Start task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo) 
[INFO ] 2024-07-02 17:54:28.279 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:54:28.952 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:54:28.955 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2d4dfbed 
[INFO ] 2024-07-02 17:54:28.955 - [Heartbeat-SourceMongo] - Stop task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo)  
[INFO ] 2024-07-02 17:54:29.092 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:54:29.092 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-02 17:54:29.093 - [Heartbeat-SourceMongo] - Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-02 17:55:16.543 - [Heartbeat-SourceMongo] - Start task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo) 
[INFO ] 2024-07-02 17:58:28.490 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:58:28.490 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:58:28.492 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@63d5ddc6 
[INFO ] 2024-07-02 17:58:28.492 - [Heartbeat-SourceMongo] - Stop task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo)  
[INFO ] 2024-07-02 17:58:28.615 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:58:28.616 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-02 17:58:28.617 - [Heartbeat-SourceMongo] - Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-02 18:04:48.666 - [Heartbeat-SourceMongo] - Start task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo) 
[INFO ] 2024-07-02 18:04:48.765 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 18:04:48.765 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 18:04:48.766 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7102b0c0 
[INFO ] 2024-07-02 18:04:48.766 - [Heartbeat-SourceMongo] - Stop task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo)  
[INFO ] 2024-07-02 18:04:48.891 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-02 18:04:48.892 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-02 18:04:49.094 - [Heartbeat-SourceMongo] - Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-02 18:08:12.108 - [Heartbeat-SourceMongo] - Start task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo) 
[INFO ] 2024-07-02 18:08:12.108 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 18:08:12.110 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 18:08:12.110 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@71bb1ce1 
[INFO ] 2024-07-02 18:08:12.111 - [Heartbeat-SourceMongo] - Stop task milestones: 6683c840fa7caf4ccea1344c(Heartbeat-SourceMongo)  
[INFO ] 2024-07-02 18:08:12.223 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-02 18:08:12.223 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-02 18:08:12.423 - [Heartbeat-SourceMongo] - Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id b0bc8d61-9e56-4d03-abb4-cde3bf05bfa8, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

