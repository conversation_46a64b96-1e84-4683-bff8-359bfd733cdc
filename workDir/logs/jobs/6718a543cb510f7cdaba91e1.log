[INFO ] 2024-10-23 15:28:41.994 - [测试云版问题] - Task initialization... 
[INFO ] 2024-10-23 15:28:41.996 - [测试云版问题] - Start task milestones: 6718a543cb510f7cdaba91e1(测试云版问题) 
[INFO ] 2024-10-23 15:28:42.751 - [测试云版问题] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-10-23 15:28:42.751 - [测试云版问题] - The engine receives 测试云版问题 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-23 15:28:42.834 - [测试云版问题][增删字段] - Node 增删字段[5801e4fb-ba9c-408a-b08b-9d5eb961433b] start preload schema,table counts: 1 
[INFO ] 2024-10-23 15:28:42.835 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] start preload schema,table counts: 1 
[INFO ] 2024-10-23 15:28:42.836 - [测试云版问题][增删字段] - Node 增删字段[5801e4fb-ba9c-408a-b08b-9d5eb961433b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 15:28:42.839 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 15:28:42.873 - [测试云版问题][增删字段] - Node field_add_del_processor(增删字段: 5801e4fb-ba9c-408a-b08b-9d5eb961433b) enable batch process 
[INFO ] 2024-10-23 15:28:42.875 - [测试云版问题][cloud] - Node cloud[7e9800f8-27c1-4303-8e96-7b6c4464b97d] start preload schema,table counts: 1 
[INFO ] 2024-10-23 15:28:42.878 - [测试云版问题][cloud] - Node cloud[7e9800f8-27c1-4303-8e96-7b6c4464b97d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 15:28:43.627 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" read batch size: 100 
[INFO ] 2024-10-23 15:28:43.627 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" event queue capacity: 200 
[INFO ] 2024-10-23 15:28:43.628 - [测试云版问题][cloudTaskTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-23 15:28:43.792 - [测试云版问题][cloud] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-23 15:28:43.799 - [测试云版问题][cloudTaskTest] - batch offset found: {},stream offset found: {"cdcOffset":1729668523,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 15:28:43.896 - [测试云版问题][cloudTaskTest] - Initial sync started 
[INFO ] 2024-10-23 15:28:43.910 - [测试云版问题][cloudTaskTest] - Starting batch read, table name: cloudTaskTest 
[INFO ] 2024-10-23 15:28:43.910 - [测试云版问题][cloudTaskTest] - Table cloudTaskTest is going to be initial synced 
[INFO ] 2024-10-23 15:28:44.121 - [测试云版问题][cloudTaskTest] - Query table 'cloudTaskTest' counts: 26412 
[INFO ] 2024-10-23 15:28:45.990 - [测试云版问题][cloudTaskTest] - Table [cloudTaskTest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-23 15:28:45.999 - [测试云版问题][cloudTaskTest] - Initial sync completed 
[INFO ] 2024-10-23 15:28:45.999 - [测试云版问题][cloudTaskTest] - Incremental sync starting... 
[INFO ] 2024-10-23 15:28:46.004 - [测试云版问题][cloudTaskTest] - Initial sync completed 
[INFO ] 2024-10-23 15:28:46.005 - [测试云版问题][cloudTaskTest] - Starting stream read, table list: [cloudTaskTest], offset: {"cdcOffset":1729668523,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 15:28:46.208 - [测试云版问题][cloudTaskTest] - Connector MongoDB incremental start succeed, tables: [cloudTaskTest], data change syncing 
[INFO ] 2024-10-23 15:30:51.391 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] running status set to false 
[INFO ] 2024-10-23 15:30:51.392 - [测试云版问题][cloudTaskTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 15:30:51.392 - [测试云版问题][cloudTaskTest] - PDK connector node released: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 15:30:51.394 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] schema data cleaned 
[INFO ] 2024-10-23 15:30:51.401 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] monitor closed 
[INFO ] 2024-10-23 15:30:51.401 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] close complete, cost 57 ms 
[INFO ] 2024-10-23 15:30:51.486 - [测试云版问题][增删字段] - Node 增删字段[5801e4fb-ba9c-408a-b08b-9d5eb961433b] running status set to false 
[INFO ] 2024-10-23 15:30:51.487 - [测试云版问题][增删字段] - Node 增删字段[5801e4fb-ba9c-408a-b08b-9d5eb961433b] schema data cleaned 
[INFO ] 2024-10-23 15:30:51.493 - [测试云版问题][增删字段] - Node 增删字段[5801e4fb-ba9c-408a-b08b-9d5eb961433b] monitor closed 
[INFO ] 2024-10-23 15:30:51.493 - [测试云版问题][增删字段] - Node 增删字段[5801e4fb-ba9c-408a-b08b-9d5eb961433b] close complete, cost 90 ms 
[INFO ] 2024-10-23 15:30:51.519 - [测试云版问题][cloud] - Node cloud[7e9800f8-27c1-4303-8e96-7b6c4464b97d] running status set to false 
[INFO ] 2024-10-23 15:30:51.520 - [测试云版问题][cloud] - PDK connector node stopped: HazelcastTargetPdkDataNode-7e9800f8-27c1-4303-8e96-7b6c4464b97d 
[INFO ] 2024-10-23 15:30:51.520 - [测试云版问题][cloud] - PDK connector node released: HazelcastTargetPdkDataNode-7e9800f8-27c1-4303-8e96-7b6c4464b97d 
[INFO ] 2024-10-23 15:30:51.520 - [测试云版问题][cloud] - Node cloud[7e9800f8-27c1-4303-8e96-7b6c4464b97d] schema data cleaned 
[INFO ] 2024-10-23 15:30:51.522 - [测试云版问题][cloud] - Node cloud[7e9800f8-27c1-4303-8e96-7b6c4464b97d] monitor closed 
[INFO ] 2024-10-23 15:30:51.725 - [测试云版问题][cloud] - Node cloud[7e9800f8-27c1-4303-8e96-7b6c4464b97d] close complete, cost 28 ms 
[INFO ] 2024-10-23 15:30:51.930 - [测试云版问题][cloudTaskTest] - Incremental sync completed 
[INFO ] 2024-10-23 15:30:53.997 - [测试云版问题] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-23 15:30:54.002 - [测试云版问题] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4cde34ac 
[INFO ] 2024-10-23 15:30:54.128 - [测试云版问题] - Stop task milestones: 6718a543cb510f7cdaba91e1(测试云版问题)  
[INFO ] 2024-10-23 15:30:54.129 - [测试云版问题] - Stopped task aspect(s) 
[INFO ] 2024-10-23 15:30:54.129 - [测试云版问题] - Snapshot order controller have been removed 
[INFO ] 2024-10-23 15:30:54.178 - [测试云版问题] - Remove memory task client succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
[INFO ] 2024-10-23 15:30:54.178 - [测试云版问题] - Destroy memory task client cache succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
[INFO ] 2024-10-23 15:32:28.833 - [测试云版问题] - Task initialization... 
[INFO ] 2024-10-23 15:32:29.048 - [测试云版问题] - Start task milestones: 6718a543cb510f7cdaba91e1(测试云版问题) 
[INFO ] 2024-10-23 15:32:29.437 - [测试云版问题] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-23 15:32:29.438 - [测试云版问题] - The engine receives 测试云版问题 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-23 15:32:29.543 - [测试云版问题][增删字段] - Node 增删字段[5801e4fb-ba9c-408a-b08b-9d5eb961433b] start preload schema,table counts: 1 
[INFO ] 2024-10-23 15:32:29.545 - [测试云版问题][cloud] - Node cloud[7e9800f8-27c1-4303-8e96-7b6c4464b97d] start preload schema,table counts: 1 
[INFO ] 2024-10-23 15:32:29.545 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] start preload schema,table counts: 1 
[INFO ] 2024-10-23 15:32:29.557 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 15:32:29.558 - [测试云版问题][cloud] - Node cloud[7e9800f8-27c1-4303-8e96-7b6c4464b97d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 15:32:29.558 - [测试云版问题][增删字段] - Node 增删字段[5801e4fb-ba9c-408a-b08b-9d5eb961433b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 15:32:29.558 - [测试云版问题][增删字段] - Node field_add_del_processor(增删字段: 5801e4fb-ba9c-408a-b08b-9d5eb961433b) enable batch process 
[INFO ] 2024-10-23 15:32:30.250 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" read batch size: 100 
[INFO ] 2024-10-23 15:32:30.251 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" event queue capacity: 200 
[INFO ] 2024-10-23 15:32:30.251 - [测试云版问题][cloudTaskTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-23 15:32:30.330 - [测试云版问题][cloud] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-23 15:32:30.446 - [测试云版问题][cloudTaskTest] - batch offset found: {},stream offset found: {"cdcOffset":1729668660,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 15:32:30.452 - [测试云版问题][cloudTaskTest] - Incremental sync starting... 
[INFO ] 2024-10-23 15:32:30.452 - [测试云版问题][cloudTaskTest] - Initial sync completed 
[INFO ] 2024-10-23 15:32:30.452 - [测试云版问题][cloudTaskTest] - Starting stream read, table list: [cloudTaskTest], offset: {"cdcOffset":1729668660,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 15:32:30.654 - [测试云版问题][cloudTaskTest] - Connector MongoDB incremental start succeed, tables: [cloudTaskTest], data change syncing 
[INFO ] 2024-10-23 16:03:15.878 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] running status set to false 
[INFO ] 2024-10-23 16:03:15.896 - [测试云版问题][cloudTaskTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 16:03:15.897 - [测试云版问题][cloudTaskTest] - PDK connector node released: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 16:03:15.897 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] schema data cleaned 
[INFO ] 2024-10-23 16:03:15.897 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] monitor closed 
[INFO ] 2024-10-23 16:03:15.900 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] close complete, cost 52 ms 
[INFO ] 2024-10-23 16:03:15.901 - [测试云版问题][增删字段] - Node 增删字段[5801e4fb-ba9c-408a-b08b-9d5eb961433b] running status set to false 
[INFO ] 2024-10-23 16:03:15.986 - [测试云版问题][增删字段] - Node 增删字段[5801e4fb-ba9c-408a-b08b-9d5eb961433b] schema data cleaned 
[INFO ] 2024-10-23 16:03:15.986 - [测试云版问题][增删字段] - Node 增删字段[5801e4fb-ba9c-408a-b08b-9d5eb961433b] monitor closed 
[INFO ] 2024-10-23 16:03:15.997 - [测试云版问题][增删字段] - Node 增删字段[5801e4fb-ba9c-408a-b08b-9d5eb961433b] close complete, cost 86 ms 
[INFO ] 2024-10-23 16:03:15.997 - [测试云版问题][cloud] - Node cloud[7e9800f8-27c1-4303-8e96-7b6c4464b97d] running status set to false 
[INFO ] 2024-10-23 16:03:16.021 - [测试云版问题][cloud] - PDK connector node stopped: HazelcastTargetPdkDataNode-7e9800f8-27c1-4303-8e96-7b6c4464b97d 
[INFO ] 2024-10-23 16:03:16.021 - [测试云版问题][cloud] - PDK connector node released: HazelcastTargetPdkDataNode-7e9800f8-27c1-4303-8e96-7b6c4464b97d 
[INFO ] 2024-10-23 16:03:16.021 - [测试云版问题][cloud] - Node cloud[7e9800f8-27c1-4303-8e96-7b6c4464b97d] schema data cleaned 
[INFO ] 2024-10-23 16:03:16.021 - [测试云版问题][cloud] - Node cloud[7e9800f8-27c1-4303-8e96-7b6c4464b97d] monitor closed 
[INFO ] 2024-10-23 16:03:16.213 - [测试云版问题][cloud] - Node cloud[7e9800f8-27c1-4303-8e96-7b6c4464b97d] close complete, cost 34 ms 
[INFO ] 2024-10-23 16:03:16.213 - [测试云版问题] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-23 16:03:16.213 - [测试云版问题] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@8881e20 
[INFO ] 2024-10-23 16:03:16.222 - [测试云版问题] - Stop task milestones: 6718a543cb510f7cdaba91e1(测试云版问题)  
[INFO ] 2024-10-23 16:03:16.353 - [测试云版问题] - Stopped task aspect(s) 
[INFO ] 2024-10-23 16:03:16.353 - [测试云版问题] - Snapshot order controller have been removed 
[INFO ] 2024-10-23 16:03:16.385 - [测试云版问题] - Remove memory task client succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
[INFO ] 2024-10-23 16:03:16.386 - [测试云版问题] - Destroy memory task client cache succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
[INFO ] 2024-10-23 16:07:27.705 - [测试云版问题] - Task initialization... 
[INFO ] 2024-10-23 16:07:27.709 - [测试云版问题] - Start task milestones: 6718a543cb510f7cdaba91e1(测试云版问题) 
[INFO ] 2024-10-23 16:07:28.120 - [测试云版问题] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-23 16:07:28.172 - [测试云版问题] - The engine receives 测试云版问题 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-23 16:07:28.257 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:07:28.258 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:07:28.258 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:07:28.258 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] preload schema finished, cost 1 ms 
[INFO ] 2024-10-23 16:07:28.258 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:07:28.258 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:07:28.260 - [测试云版问题][增删字段] - Node field_add_del_processor(增删字段: 045a7c44-af82-45cb-b092-7cfdc8f1f185) enable batch process 
[INFO ] 2024-10-23 16:07:28.952 - [测试云版问题][CloudTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-23 16:07:29.116 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" read batch size: 100 
[INFO ] 2024-10-23 16:07:29.117 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" event queue capacity: 200 
[INFO ] 2024-10-23 16:07:29.117 - [测试云版问题][cloudTaskTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-23 16:07:29.250 - [测试云版问题][cloudTaskTest] - batch offset found: {},stream offset found: {"cdcOffset":1729670849,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 16:07:29.323 - [测试云版问题][cloudTaskTest] - Incremental sync starting... 
[INFO ] 2024-10-23 16:07:29.325 - [测试云版问题][cloudTaskTest] - Initial sync completed 
[INFO ] 2024-10-23 16:07:29.325 - [测试云版问题][cloudTaskTest] - Starting stream read, table list: [cloudTaskTest], offset: {"cdcOffset":1729670849,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 16:07:29.528 - [测试云版问题][cloudTaskTest] - Connector MongoDB incremental start succeed, tables: [cloudTaskTest], data change syncing 
[INFO ] 2024-10-23 16:07:49.662 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] running status set to false 
[INFO ] 2024-10-23 16:07:49.684 - [测试云版问题][cloudTaskTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 16:07:49.685 - [测试云版问题][cloudTaskTest] - PDK connector node released: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 16:07:49.685 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] schema data cleaned 
[INFO ] 2024-10-23 16:07:49.685 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] monitor closed 
[INFO ] 2024-10-23 16:07:49.687 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] close complete, cost 33 ms 
[INFO ] 2024-10-23 16:07:49.687 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] running status set to false 
[INFO ] 2024-10-23 16:07:49.800 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] schema data cleaned 
[INFO ] 2024-10-23 16:07:49.806 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] monitor closed 
[INFO ] 2024-10-23 16:07:49.806 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] close complete, cost 110 ms 
[INFO ] 2024-10-23 16:07:49.806 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] running status set to false 
[INFO ] 2024-10-23 16:07:49.849 - [测试云版问题][CloudTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-03b25f75-263e-4109-84b3-318c2b2ff7bf 
[INFO ] 2024-10-23 16:07:49.851 - [测试云版问题][CloudTest] - PDK connector node released: HazelcastTargetPdkDataNode-03b25f75-263e-4109-84b3-318c2b2ff7bf 
[INFO ] 2024-10-23 16:07:49.851 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] schema data cleaned 
[INFO ] 2024-10-23 16:07:49.852 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] monitor closed 
[INFO ] 2024-10-23 16:07:49.853 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] close complete, cost 53 ms 
[INFO ] 2024-10-23 16:07:50.664 - [测试云版问题][cloudTaskTest] - Incremental sync completed 
[INFO ] 2024-10-23 16:07:51.648 - [测试云版问题] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-23 16:07:51.648 - [测试云版问题] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@76630a45 
[INFO ] 2024-10-23 16:07:51.648 - [测试云版问题] - Stop task milestones: 6718a543cb510f7cdaba91e1(测试云版问题)  
[INFO ] 2024-10-23 16:07:51.800 - [测试云版问题] - Stopped task aspect(s) 
[INFO ] 2024-10-23 16:07:51.800 - [测试云版问题] - Snapshot order controller have been removed 
[INFO ] 2024-10-23 16:07:51.824 - [测试云版问题] - Remove memory task client succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
[INFO ] 2024-10-23 16:07:51.827 - [测试云版问题] - Destroy memory task client cache succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
[INFO ] 2024-10-23 16:07:56.001 - [测试云版问题] - Task initialization... 
[INFO ] 2024-10-23 16:07:56.207 - [测试云版问题] - Start task milestones: 6718a543cb510f7cdaba91e1(测试云版问题) 
[INFO ] 2024-10-23 16:07:56.508 - [测试云版问题] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-23 16:07:56.509 - [测试云版问题] - The engine receives 测试云版问题 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-23 16:07:56.573 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:07:56.573 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:07:56.576 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:07:56.576 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:07:56.576 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:07:56.576 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] preload schema finished, cost 1 ms 
[INFO ] 2024-10-23 16:07:56.576 - [测试云版问题][增删字段] - Node field_add_del_processor(增删字段: 045a7c44-af82-45cb-b092-7cfdc8f1f185) enable batch process 
[INFO ] 2024-10-23 16:07:57.319 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" read batch size: 100 
[INFO ] 2024-10-23 16:07:57.319 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" event queue capacity: 200 
[INFO ] 2024-10-23 16:07:57.479 - [测试云版问题][cloudTaskTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-23 16:07:57.480 - [测试云版问题][CloudTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-23 16:07:57.483 - [测试云版问题][cloudTaskTest] - batch offset found: {},stream offset found: {"cdcOffset":1729670877,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 16:07:57.567 - [测试云版问题][cloudTaskTest] - Incremental sync starting... 
[INFO ] 2024-10-23 16:07:57.569 - [测试云版问题][cloudTaskTest] - Initial sync completed 
[INFO ] 2024-10-23 16:07:57.574 - [测试云版问题][cloudTaskTest] - Starting stream read, table list: [cloudTaskTest], offset: {"cdcOffset":1729670877,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 16:07:57.774 - [测试云版问题][cloudTaskTest] - Connector MongoDB incremental start succeed, tables: [cloudTaskTest], data change syncing 
[INFO ] 2024-10-23 16:08:51.601 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] running status set to false 
[INFO ] 2024-10-23 16:08:51.602 - [测试云版问题][cloudTaskTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 16:08:51.602 - [测试云版问题][cloudTaskTest] - PDK connector node released: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 16:08:51.602 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] schema data cleaned 
[INFO ] 2024-10-23 16:08:51.602 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] monitor closed 
[INFO ] 2024-10-23 16:08:51.605 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] close complete, cost 37 ms 
[INFO ] 2024-10-23 16:08:51.605 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] running status set to false 
[INFO ] 2024-10-23 16:08:51.680 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] schema data cleaned 
[INFO ] 2024-10-23 16:08:51.680 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] monitor closed 
[INFO ] 2024-10-23 16:08:51.681 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] close complete, cost 76 ms 
[INFO ] 2024-10-23 16:08:51.682 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] running status set to false 
[INFO ] 2024-10-23 16:08:51.695 - [测试云版问题][CloudTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-03b25f75-263e-4109-84b3-318c2b2ff7bf 
[INFO ] 2024-10-23 16:08:51.697 - [测试云版问题][CloudTest] - PDK connector node released: HazelcastTargetPdkDataNode-03b25f75-263e-4109-84b3-318c2b2ff7bf 
[INFO ] 2024-10-23 16:08:51.697 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] schema data cleaned 
[INFO ] 2024-10-23 16:08:51.699 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] monitor closed 
[INFO ] 2024-10-23 16:08:51.699 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] close complete, cost 16 ms 
[INFO ] 2024-10-23 16:08:51.920 - [测试云版问题] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-23 16:08:51.920 - [测试云版问题] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3e94d911 
[INFO ] 2024-10-23 16:08:51.924 - [测试云版问题] - Stop task milestones: 6718a543cb510f7cdaba91e1(测试云版问题)  
[INFO ] 2024-10-23 16:08:52.058 - [测试云版问题] - Stopped task aspect(s) 
[INFO ] 2024-10-23 16:08:52.058 - [测试云版问题] - Snapshot order controller have been removed 
[INFO ] 2024-10-23 16:08:52.083 - [测试云版问题] - Remove memory task client succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
[INFO ] 2024-10-23 16:08:52.284 - [测试云版问题] - Destroy memory task client cache succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
[INFO ] 2024-10-23 16:09:52.492 - [测试云版问题] - Task initialization... 
[INFO ] 2024-10-23 16:09:52.493 - [测试云版问题] - Start task milestones: 6718a543cb510f7cdaba91e1(测试云版问题) 
[INFO ] 2024-10-23 16:09:52.694 - [测试云版问题] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-23 16:09:52.694 - [测试云版问题] - The engine receives 测试云版问题 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-23 16:09:52.786 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:09:52.787 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:09:52.787 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:09:52.787 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:09:52.787 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:09:52.788 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:09:52.788 - [测试云版问题][增删字段] - Node field_add_del_processor(增删字段: 045a7c44-af82-45cb-b092-7cfdc8f1f185) enable batch process 
[INFO ] 2024-10-23 16:09:53.460 - [测试云版问题][CloudTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-23 16:09:53.616 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" read batch size: 100 
[INFO ] 2024-10-23 16:09:53.617 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" event queue capacity: 200 
[INFO ] 2024-10-23 16:09:53.617 - [测试云版问题][cloudTaskTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-23 16:09:53.824 - [测试云版问题][cloudTaskTest] - batch offset found: {},stream offset found: {"cdcOffset":1729670993,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 16:09:53.862 - [测试云版问题][cloudTaskTest] - Incremental sync starting... 
[INFO ] 2024-10-23 16:09:53.869 - [测试云版问题][cloudTaskTest] - Initial sync completed 
[INFO ] 2024-10-23 16:09:53.869 - [测试云版问题][cloudTaskTest] - Starting stream read, table list: [cloudTaskTest], offset: {"cdcOffset":1729670993,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 16:09:54.075 - [测试云版问题][cloudTaskTest] - Connector MongoDB incremental start succeed, tables: [cloudTaskTest], data change syncing 
[INFO ] 2024-10-23 16:12:04.578 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] running status set to false 
[INFO ] 2024-10-23 16:12:04.597 - [测试云版问题][cloudTaskTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 16:12:04.598 - [测试云版问题][cloudTaskTest] - PDK connector node released: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 16:12:04.598 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] schema data cleaned 
[INFO ] 2024-10-23 16:12:04.599 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] monitor closed 
[INFO ] 2024-10-23 16:12:04.601 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] close complete, cost 42 ms 
[INFO ] 2024-10-23 16:12:04.601 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] running status set to false 
[INFO ] 2024-10-23 16:12:04.694 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] schema data cleaned 
[INFO ] 2024-10-23 16:12:04.694 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] monitor closed 
[INFO ] 2024-10-23 16:12:04.695 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] close complete, cost 93 ms 
[INFO ] 2024-10-23 16:12:04.695 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] running status set to false 
[INFO ] 2024-10-23 16:12:04.717 - [测试云版问题][CloudTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-03b25f75-263e-4109-84b3-318c2b2ff7bf 
[INFO ] 2024-10-23 16:12:04.717 - [测试云版问题][CloudTest] - PDK connector node released: HazelcastTargetPdkDataNode-03b25f75-263e-4109-84b3-318c2b2ff7bf 
[INFO ] 2024-10-23 16:12:04.717 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] schema data cleaned 
[INFO ] 2024-10-23 16:12:04.717 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] monitor closed 
[INFO ] 2024-10-23 16:12:04.921 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] close complete, cost 22 ms 
[INFO ] 2024-10-23 16:12:05.528 - [测试云版问题][cloudTaskTest] - Incremental sync completed 
[INFO ] 2024-10-23 16:12:07.257 - [测试云版问题] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-23 16:12:07.260 - [测试云版问题] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3b9fad9b 
[INFO ] 2024-10-23 16:12:07.260 - [测试云版问题] - Stop task milestones: 6718a543cb510f7cdaba91e1(测试云版问题)  
[INFO ] 2024-10-23 16:12:07.388 - [测试云版问题] - Stopped task aspect(s) 
[INFO ] 2024-10-23 16:12:07.388 - [测试云版问题] - Snapshot order controller have been removed 
[INFO ] 2024-10-23 16:12:07.428 - [测试云版问题] - Remove memory task client succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
[INFO ] 2024-10-23 16:12:07.428 - [测试云版问题] - Destroy memory task client cache succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
[INFO ] 2024-10-23 16:12:15.091 - [测试云版问题] - Task initialization... 
[INFO ] 2024-10-23 16:12:15.094 - [测试云版问题] - Start task milestones: 6718a543cb510f7cdaba91e1(测试云版问题) 
[INFO ] 2024-10-23 16:12:15.386 - [测试云版问题] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-23 16:12:15.386 - [测试云版问题] - The engine receives 测试云版问题 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-23 16:12:15.448 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:12:15.448 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:12:15.448 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:12:15.449 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:12:15.449 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] preload schema finished, cost 1 ms 
[INFO ] 2024-10-23 16:12:15.449 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:12:15.450 - [测试云版问题][增删字段] - Node field_add_del_processor(增删字段: 045a7c44-af82-45cb-b092-7cfdc8f1f185) enable batch process 
[INFO ] 2024-10-23 16:12:40.062 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" read batch size: 100 
[INFO ] 2024-10-23 16:12:40.063 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" event queue capacity: 200 
[INFO ] 2024-10-23 16:12:40.175 - [测试云版问题][cloudTaskTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-23 16:12:40.178 - [测试云版问题][CloudTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-23 16:12:40.303 - [测试云版问题][cloudTaskTest] - batch offset found: {},stream offset found: {"cdcOffset":1729671160,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 16:12:40.304 - [测试云版问题][cloudTaskTest] - Incremental sync starting... 
[INFO ] 2024-10-23 16:12:40.304 - [测试云版问题][cloudTaskTest] - Initial sync completed 
[INFO ] 2024-10-23 16:12:40.321 - [测试云版问题][cloudTaskTest] - Starting stream read, table list: [cloudTaskTest], offset: {"cdcOffset":1729671160,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 16:12:40.322 - [测试云版问题][cloudTaskTest] - Connector MongoDB incremental start succeed, tables: [cloudTaskTest], data change syncing 
[INFO ] 2024-10-23 16:17:03.440 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] running status set to false 
[INFO ] 2024-10-23 16:17:03.459 - [测试云版问题][cloudTaskTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 16:17:03.459 - [测试云版问题][cloudTaskTest] - PDK connector node released: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 16:17:03.459 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] schema data cleaned 
[INFO ] 2024-10-23 16:17:03.460 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] monitor closed 
[INFO ] 2024-10-23 16:17:03.463 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] close complete, cost 43 ms 
[INFO ] 2024-10-23 16:17:03.463 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] running status set to false 
[INFO ] 2024-10-23 16:17:03.539 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] schema data cleaned 
[INFO ] 2024-10-23 16:17:03.539 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] monitor closed 
[INFO ] 2024-10-23 16:17:03.540 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] close complete, cost 77 ms 
[INFO ] 2024-10-23 16:17:03.540 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] running status set to false 
[INFO ] 2024-10-23 16:17:03.557 - [测试云版问题][CloudTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-03b25f75-263e-4109-84b3-318c2b2ff7bf 
[INFO ] 2024-10-23 16:17:03.557 - [测试云版问题][CloudTest] - PDK connector node released: HazelcastTargetPdkDataNode-03b25f75-263e-4109-84b3-318c2b2ff7bf 
[INFO ] 2024-10-23 16:17:03.557 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] schema data cleaned 
[INFO ] 2024-10-23 16:17:03.558 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] monitor closed 
[INFO ] 2024-10-23 16:17:03.763 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] close complete, cost 17 ms 
[INFO ] 2024-10-23 16:17:03.968 - [测试云版问题][cloudTaskTest] - Incremental sync completed 
[INFO ] 2024-10-23 16:17:06.561 - [测试云版问题] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-23 16:17:06.565 - [测试云版问题] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2ea25e23 
[INFO ] 2024-10-23 16:17:06.565 - [测试云版问题] - Stop task milestones: 6718a543cb510f7cdaba91e1(测试云版问题)  
[INFO ] 2024-10-23 16:17:06.702 - [测试云版问题] - Stopped task aspect(s) 
[INFO ] 2024-10-23 16:17:06.702 - [测试云版问题] - Snapshot order controller have been removed 
[INFO ] 2024-10-23 16:17:06.741 - [测试云版问题] - Remove memory task client succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
[INFO ] 2024-10-23 16:17:06.751 - [测试云版问题] - Destroy memory task client cache succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
[INFO ] 2024-10-23 16:18:21.580 - [测试云版问题] - Task initialization... 
[INFO ] 2024-10-23 16:18:21.583 - [测试云版问题] - Start task milestones: 6718a543cb510f7cdaba91e1(测试云版问题) 
[INFO ] 2024-10-23 16:18:21.786 - [测试云版问题] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-23 16:18:21.914 - [测试云版问题] - The engine receives 测试云版问题 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-23 16:18:21.914 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:18:21.914 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:18:21.915 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:18:21.915 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:18:21.915 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:18:21.915 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:18:24.220 - [测试云版问题][增删字段] - Node field_add_del_processor(增删字段: 045a7c44-af82-45cb-b092-7cfdc8f1f185) enable batch process 
[INFO ] 2024-10-23 16:18:25.044 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" read batch size: 100 
[INFO ] 2024-10-23 16:18:25.045 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" event queue capacity: 200 
[INFO ] 2024-10-23 16:18:25.045 - [测试云版问题][cloudTaskTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-23 16:18:25.073 - [测试云版问题][CloudTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-23 16:18:25.193 - [测试云版问题][cloudTaskTest] - batch offset found: {},stream offset found: {"cdcOffset":1729671420,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 16:18:25.193 - [测试云版问题][cloudTaskTest] - Incremental sync starting... 
[INFO ] 2024-10-23 16:18:25.193 - [测试云版问题][cloudTaskTest] - Initial sync completed 
[INFO ] 2024-10-23 16:18:25.211 - [测试云版问题][cloudTaskTest] - Starting stream read, table list: [cloudTaskTest], offset: {"cdcOffset":1729671420,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 16:18:25.211 - [测试云版问题][cloudTaskTest] - Connector MongoDB incremental start succeed, tables: [cloudTaskTest], data change syncing 
[INFO ] 2024-10-23 16:35:57.277 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] running status set to false 
[INFO ] 2024-10-23 16:35:57.306 - [测试云版问题][cloudTaskTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 16:35:57.307 - [测试云版问题][cloudTaskTest] - PDK connector node released: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 16:35:57.307 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] schema data cleaned 
[INFO ] 2024-10-23 16:35:57.307 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] monitor closed 
[INFO ] 2024-10-23 16:35:57.310 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] close complete, cost 50 ms 
[INFO ] 2024-10-23 16:35:57.310 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] running status set to false 
[INFO ] 2024-10-23 16:35:57.395 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] schema data cleaned 
[INFO ] 2024-10-23 16:35:57.396 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] monitor closed 
[INFO ] 2024-10-23 16:35:57.399 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] close complete, cost 87 ms 
[INFO ] 2024-10-23 16:35:57.399 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] running status set to false 
[INFO ] 2024-10-23 16:35:57.448 - [测试云版问题][CloudTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-03b25f75-263e-4109-84b3-318c2b2ff7bf 
[INFO ] 2024-10-23 16:35:57.449 - [测试云版问题][CloudTest] - PDK connector node released: HazelcastTargetPdkDataNode-03b25f75-263e-4109-84b3-318c2b2ff7bf 
[INFO ] 2024-10-23 16:35:57.449 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] schema data cleaned 
[INFO ] 2024-10-23 16:35:57.449 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] monitor closed 
[INFO ] 2024-10-23 16:35:57.449 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] close complete, cost 51 ms 
[INFO ] 2024-10-23 16:35:57.653 - [测试云版问题][cloudTaskTest] - Incremental sync completed 
[INFO ] 2024-10-23 16:35:58.528 - [测试云版问题] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-23 16:35:58.533 - [测试云版问题] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@58b078b3 
[INFO ] 2024-10-23 16:35:58.534 - [测试云版问题] - Stop task milestones: 6718a543cb510f7cdaba91e1(测试云版问题)  
[INFO ] 2024-10-23 16:35:58.671 - [测试云版问题] - Stopped task aspect(s) 
[INFO ] 2024-10-23 16:35:58.671 - [测试云版问题] - Snapshot order controller have been removed 
[INFO ] 2024-10-23 16:35:58.690 - [测试云版问题] - Remove memory task client succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
[INFO ] 2024-10-23 16:35:58.692 - [测试云版问题] - Destroy memory task client cache succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
[INFO ] 2024-10-23 16:36:43.957 - [测试云版问题] - Task initialization... 
[INFO ] 2024-10-23 16:36:44.170 - [测试云版问题] - Start task milestones: 6718a543cb510f7cdaba91e1(测试云版问题) 
[INFO ] 2024-10-23 16:36:44.371 - [测试云版问题] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-23 16:36:44.378 - [测试云版问题] - The engine receives 测试云版问题 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-23 16:36:44.477 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:36:44.477 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:36:44.477 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:36:44.477 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:36:44.478 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:36:44.478 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:36:46.432 - [测试云版问题][增删字段] - Node field_add_del_processor(增删字段: 045a7c44-af82-45cb-b092-7cfdc8f1f185) enable batch process 
[INFO ] 2024-10-23 16:36:47.431 - [测试云版问题][CloudTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-23 16:36:47.432 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" read batch size: 100 
[INFO ] 2024-10-23 16:36:47.432 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" event queue capacity: 200 
[INFO ] 2024-10-23 16:36:47.432 - [测试云版问题][cloudTaskTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-23 16:36:47.519 - [测试云版问题][cloudTaskTest] - batch offset found: {},stream offset found: {"cdcOffset":1729671420,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 16:36:47.613 - [测试云版问题][cloudTaskTest] - Incremental sync starting... 
[INFO ] 2024-10-23 16:36:47.614 - [测试云版问题][cloudTaskTest] - Initial sync completed 
[INFO ] 2024-10-23 16:36:47.617 - [测试云版问题][cloudTaskTest] - Starting stream read, table list: [cloudTaskTest], offset: {"cdcOffset":1729671420,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 16:36:47.822 - [测试云版问题][cloudTaskTest] - Connector MongoDB incremental start succeed, tables: [cloudTaskTest], data change syncing 
[INFO ] 2024-10-23 16:37:19.791 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] running status set to false 
[INFO ] 2024-10-23 16:37:19.811 - [测试云版问题][cloudTaskTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 16:37:19.812 - [测试云版问题][cloudTaskTest] - PDK connector node released: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 16:37:19.812 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] schema data cleaned 
[INFO ] 2024-10-23 16:37:19.812 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] monitor closed 
[INFO ] 2024-10-23 16:37:19.814 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] close complete, cost 34 ms 
[INFO ] 2024-10-23 16:37:19.814 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] running status set to false 
[INFO ] 2024-10-23 16:37:19.904 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] schema data cleaned 
[INFO ] 2024-10-23 16:37:19.907 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] monitor closed 
[INFO ] 2024-10-23 16:37:19.908 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] close complete, cost 93 ms 
[INFO ] 2024-10-23 16:37:19.908 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] running status set to false 
[INFO ] 2024-10-23 16:37:19.940 - [测试云版问题][CloudTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-03b25f75-263e-4109-84b3-318c2b2ff7bf 
[INFO ] 2024-10-23 16:37:19.940 - [测试云版问题][CloudTest] - PDK connector node released: HazelcastTargetPdkDataNode-03b25f75-263e-4109-84b3-318c2b2ff7bf 
[INFO ] 2024-10-23 16:37:19.940 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] schema data cleaned 
[INFO ] 2024-10-23 16:37:19.940 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] monitor closed 
[INFO ] 2024-10-23 16:37:20.146 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] close complete, cost 33 ms 
[INFO ] 2024-10-23 16:37:20.556 - [测试云版问题][cloudTaskTest] - Incremental sync completed 
[INFO ] 2024-10-23 16:37:22.381 - [测试云版问题] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-23 16:37:22.384 - [测试云版问题] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@51fbb2a1 
[INFO ] 2024-10-23 16:37:22.384 - [测试云版问题] - Stop task milestones: 6718a543cb510f7cdaba91e1(测试云版问题)  
[INFO ] 2024-10-23 16:37:22.517 - [测试云版问题] - Stopped task aspect(s) 
[INFO ] 2024-10-23 16:37:22.518 - [测试云版问题] - Snapshot order controller have been removed 
[INFO ] 2024-10-23 16:37:22.539 - [测试云版问题] - Remove memory task client succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
[INFO ] 2024-10-23 16:37:22.540 - [测试云版问题] - Destroy memory task client cache succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
[INFO ] 2024-10-23 16:37:29.204 - [测试云版问题] - Task initialization... 
[INFO ] 2024-10-23 16:37:29.204 - [测试云版问题] - Start task milestones: 6718a543cb510f7cdaba91e1(测试云版问题) 
[INFO ] 2024-10-23 16:37:29.395 - [测试云版问题] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-23 16:37:29.396 - [测试云版问题] - The engine receives 测试云版问题 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-23 16:37:29.461 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:37:29.462 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:37:29.462 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] start preload schema,table counts: 1 
[INFO ] 2024-10-23 16:37:29.462 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:37:29.464 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:37:29.464 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 16:37:29.464 - [测试云版问题][增删字段] - Node field_add_del_processor(增删字段: 045a7c44-af82-45cb-b092-7cfdc8f1f185) enable batch process 
[INFO ] 2024-10-23 16:37:32.383 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" read batch size: 100 
[INFO ] 2024-10-23 16:37:32.383 - [测试云版问题][cloudTaskTest] - Source node "cloudTaskTest" event queue capacity: 200 
[INFO ] 2024-10-23 16:37:32.383 - [测试云版问题][cloudTaskTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-23 16:37:32.502 - [测试云版问题][CloudTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-23 16:37:32.503 - [测试云版问题][cloudTaskTest] - batch offset found: {},stream offset found: {"cdcOffset":1729671420,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 16:37:32.580 - [测试云版问题][cloudTaskTest] - Incremental sync starting... 
[INFO ] 2024-10-23 16:37:32.580 - [测试云版问题][cloudTaskTest] - Initial sync completed 
[INFO ] 2024-10-23 16:37:32.583 - [测试云版问题][cloudTaskTest] - Starting stream read, table list: [cloudTaskTest], offset: {"cdcOffset":1729671420,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 16:37:32.592 - [测试云版问题][cloudTaskTest] - Connector MongoDB incremental start succeed, tables: [cloudTaskTest], data change syncing 
[INFO ] 2024-10-23 16:44:45.565 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] running status set to false 
[INFO ] 2024-10-23 16:44:45.567 - [测试云版问题][cloudTaskTest] - PDK connector node stopped: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 16:44:45.567 - [测试云版问题][cloudTaskTest] - PDK connector node released: HazelcastSourcePdkDataNode-234c5355-7819-4a7a-a302-de041dd2966c 
[INFO ] 2024-10-23 16:44:45.567 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] schema data cleaned 
[INFO ] 2024-10-23 16:44:45.568 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] monitor closed 
[INFO ] 2024-10-23 16:44:45.571 - [测试云版问题][cloudTaskTest] - Node cloudTaskTest[234c5355-7819-4a7a-a302-de041dd2966c] close complete, cost 51 ms 
[INFO ] 2024-10-23 16:44:45.572 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] running status set to false 
[INFO ] 2024-10-23 16:44:45.660 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] schema data cleaned 
[INFO ] 2024-10-23 16:44:45.661 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] monitor closed 
[INFO ] 2024-10-23 16:44:45.663 - [测试云版问题][增删字段] - Node 增删字段[045a7c44-af82-45cb-b092-7cfdc8f1f185] close complete, cost 91 ms 
[INFO ] 2024-10-23 16:44:45.663 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] running status set to false 
[INFO ] 2024-10-23 16:44:45.686 - [测试云版问题][CloudTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-03b25f75-263e-4109-84b3-318c2b2ff7bf 
[INFO ] 2024-10-23 16:44:45.686 - [测试云版问题][CloudTest] - PDK connector node released: HazelcastTargetPdkDataNode-03b25f75-263e-4109-84b3-318c2b2ff7bf 
[INFO ] 2024-10-23 16:44:45.686 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] schema data cleaned 
[INFO ] 2024-10-23 16:44:45.686 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] monitor closed 
[INFO ] 2024-10-23 16:44:45.891 - [测试云版问题][CloudTest] - Node CloudTest[03b25f75-263e-4109-84b3-318c2b2ff7bf] close complete, cost 24 ms 
[INFO ] 2024-10-23 16:44:46.246 - [测试云版问题][cloudTaskTest] - Incremental sync completed 
[INFO ] 2024-10-23 16:44:47.949 - [测试云版问题] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-23 16:44:47.954 - [测试云版问题] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1d4ba000 
[INFO ] 2024-10-23 16:44:47.954 - [测试云版问题] - Stop task milestones: 6718a543cb510f7cdaba91e1(测试云版问题)  
[INFO ] 2024-10-23 16:44:48.103 - [测试云版问题] - Stopped task aspect(s) 
[INFO ] 2024-10-23 16:44:48.103 - [测试云版问题] - Snapshot order controller have been removed 
[INFO ] 2024-10-23 16:44:48.136 - [测试云版问题] - Remove memory task client succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
[INFO ] 2024-10-23 16:44:48.136 - [测试云版问题] - Destroy memory task client cache succeed, task: 测试云版问题[6718a543cb510f7cdaba91e1] 
