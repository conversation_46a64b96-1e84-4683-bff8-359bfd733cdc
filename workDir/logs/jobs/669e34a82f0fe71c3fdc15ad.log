[INFO ] 2024-07-22 18:31:36.687 - [任务 10] - Task initialization... 
[INFO ] 2024-07-22 18:31:36.882 - [任务 10] - Start task milestones: 669e34a82f0fe71c3fdc15ad(任务 10) 
[INFO ] 2024-07-22 18:31:36.886 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-22 18:31:36.974 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-22 18:31:37.058 - [任务 10][testPolicy] - Node testPolicy[fd11c9c4-40d5-43c9-b5dd-64443e2b58e6] start preload schema,table counts: 1 
[INFO ] 2024-07-22 18:31:37.058 - [任务 10][POLICY] - Node POLICY[933ea874-5036-4851-a0c2-0dcf72a70787] start preload schema,table counts: 1 
[INFO ] 2024-07-22 18:31:37.058 - [任务 10][testPolicy] - Node testPolicy[fd11c9c4-40d5-43c9-b5dd-64443e2b58e6] preload schema finished, cost 1 ms 
[INFO ] 2024-07-22 18:31:37.058 - [任务 10][POLICY] - Node POLICY[933ea874-5036-4851-a0c2-0dcf72a70787] preload schema finished, cost 0 ms 
[INFO ] 2024-07-22 18:31:37.829 - [任务 10][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-22 18:31:37.829 - [任务 10][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-22 18:31:37.829 - [任务 10][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-22 18:31:37.849 - [任务 10][testPolicy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-22 18:31:37.876 - [任务 10][testPolicy] - Table "test.testPolicy" exists, skip auto create table 
[INFO ] 2024-07-22 18:31:37.877 - [任务 10][testPolicy] - The table testPolicy has already exist. 
[INFO ] 2024-07-22 18:31:38.046 - [任务 10][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1721644297,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-22 18:31:38.061 - [任务 10][POLICY] - Initial sync started 
[INFO ] 2024-07-22 18:31:38.062 - [任务 10][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-22 18:31:38.062 - [任务 10][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-22 18:31:38.092 - [任务 10][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-22 18:31:38.112 - [任务 10][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-22 18:31:38.112 - [任务 10][POLICY] - Initial sync completed 
[INFO ] 2024-07-22 18:31:38.114 - [任务 10][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-22 18:31:38.114 - [任务 10][POLICY] - Initial sync completed 
[INFO ] 2024-07-22 18:31:38.183 - [任务 10][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721644297,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-22 18:31:38.183 - [任务 10][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-22 18:34:05.329 - [任务 10][POLICY] - Node POLICY[933ea874-5036-4851-a0c2-0dcf72a70787] running status set to false 
[INFO ] 2024-07-22 18:34:05.374 - [任务 10][POLICY] - Incremental sync completed 
[INFO ] 2024-07-22 18:34:05.387 - [任务 10][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-933ea874-5036-4851-a0c2-0dcf72a70787 
[INFO ] 2024-07-22 18:34:05.409 - [任务 10][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-933ea874-5036-4851-a0c2-0dcf72a70787 
[INFO ] 2024-07-22 18:34:05.409 - [任务 10][POLICY] - Node POLICY[933ea874-5036-4851-a0c2-0dcf72a70787] schema data cleaned 
[INFO ] 2024-07-22 18:34:05.444 - [任务 10][POLICY] - Node POLICY[933ea874-5036-4851-a0c2-0dcf72a70787] monitor closed 
[INFO ] 2024-07-22 18:34:05.448 - [任务 10][POLICY] - Node POLICY[933ea874-5036-4851-a0c2-0dcf72a70787] close complete, cost 113 ms 
[INFO ] 2024-07-22 18:34:05.449 - [任务 10][testPolicy] - Node testPolicy[fd11c9c4-40d5-43c9-b5dd-64443e2b58e6] running status set to false 
[INFO ] 2024-07-22 18:34:05.483 - [任务 10][testPolicy] - PDK connector node stopped: HazelcastTargetPdkDataNode-fd11c9c4-40d5-43c9-b5dd-64443e2b58e6 
[INFO ] 2024-07-22 18:34:05.483 - [任务 10][testPolicy] - PDK connector node released: HazelcastTargetPdkDataNode-fd11c9c4-40d5-43c9-b5dd-64443e2b58e6 
[INFO ] 2024-07-22 18:34:05.484 - [任务 10][testPolicy] - Node testPolicy[fd11c9c4-40d5-43c9-b5dd-64443e2b58e6] schema data cleaned 
[INFO ] 2024-07-22 18:34:05.485 - [任务 10][testPolicy] - Node testPolicy[fd11c9c4-40d5-43c9-b5dd-64443e2b58e6] monitor closed 
[INFO ] 2024-07-22 18:34:05.485 - [任务 10][testPolicy] - Node testPolicy[fd11c9c4-40d5-43c9-b5dd-64443e2b58e6] close complete, cost 55 ms 
[INFO ] 2024-07-22 18:34:12.912 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-22 18:34:12.913 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@d7f4e78 
[INFO ] 2024-07-22 18:34:13.041 - [任务 10] - Stop task milestones: 669e34a82f0fe71c3fdc15ad(任务 10)  
[INFO ] 2024-07-22 18:34:13.041 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-07-22 18:34:13.041 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-07-22 18:34:13.070 - [任务 10] - Remove memory task client succeed, task: 任务 10[669e34a82f0fe71c3fdc15ad] 
[INFO ] 2024-07-22 18:34:13.070 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[669e34a82f0fe71c3fdc15ad] 
