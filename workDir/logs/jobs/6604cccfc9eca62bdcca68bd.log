[INFO ] 2024-03-28 09:50:46.195 - [任务 31] - Start task milestones: 6604cccfc9eca62bdcca68bd(任务 31) 
[INFO ] 2024-03-28 09:50:46.240 - [任务 31] - Task initialization... 
[INFO ] 2024-03-28 09:50:46.240 - [任务 31] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 09:50:46.375 - [任务 31] - The engine receives 任务 31 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 09:50:46.376 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:50:46.376 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:50:46.376 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:50:46.410 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] preload schema finished, cost 29 ms 
[INFO ] 2024-03-28 09:50:46.420 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] preload schema finished, cost 28 ms 
[INFO ] 2024-03-28 09:50:46.421 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] preload schema finished, cost 28 ms 
[INFO ] 2024-03-28 09:50:46.745 - [任务 31][test2] - Source node "test2" read batch size: 100 
[INFO ] 2024-03-28 09:50:46.745 - [任务 31][test2] - Source node "test2" event queue capacity: 200 
[INFO ] 2024-03-28 09:50:46.745 - [任务 31][test2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 09:50:46.747 - [任务 31][test2] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 09:50:46.827 - [任务 31][test2] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 09:50:46.829 - [任务 31][test2] - Initial sync started 
[INFO ] 2024-03-28 09:50:46.832 - [任务 31][test2] - Starting batch read, table name: test2, offset: null 
[INFO ] 2024-03-28 09:50:46.833 - [任务 31][test2] - Table test2 is going to be initial synced 
[INFO ] 2024-03-28 09:50:47.245 - [任务 31][test6] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 09:50:53.902 - [任务 31][test2] - Initial sync completed 
[INFO ] 2024-03-28 09:50:53.903 - [任务 31][test2] - Incremental sync starting... 
[INFO ] 2024-03-28 09:50:53.903 - [任务 31][test2] - Initial sync completed 
[INFO ] 2024-03-28 09:50:53.906 - [任务 31][test2] - Starting stream read, table list: [test2], offset: [] 
[INFO ] 2024-03-28 09:51:27.943 - [任务 31] - Stop task milestones: 6604cccfc9eca62bdcca68bd(任务 31)  
[INFO ] 2024-03-28 09:51:27.943 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] running status set to false 
[ERROR] 2024-03-28 09:51:27.963 - [任务 31][test2] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 09:51:27.965 - [任务 31][test2] - PDK connector node stopped: HazelcastSourcePdkDataNode-44390385-f613-432e-83f8-802220979f8c 
[INFO ] 2024-03-28 09:51:27.965 - [任务 31][test2] - PDK connector node released: HazelcastSourcePdkDataNode-44390385-f613-432e-83f8-802220979f8c 
[INFO ] 2024-03-28 09:51:27.967 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] schema data cleaned 
[INFO ] 2024-03-28 09:51:27.968 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] monitor closed 
[INFO ] 2024-03-28 09:51:27.968 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] close complete, cost 27 ms 
[INFO ] 2024-03-28 09:51:27.987 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] running status set to false 
[INFO ] 2024-03-28 09:51:27.988 - [任务 31][增强JS] - PDK connector node stopped: ScriptExecutor-Test-469e9679-8759-4baf-99e0-507a334abe8c 
[INFO ] 2024-03-28 09:51:27.988 - [任务 31][增强JS] - PDK connector node released: ScriptExecutor-Test-469e9679-8759-4baf-99e0-507a334abe8c 
[INFO ] 2024-03-28 09:51:27.988 - [任务 31][增强JS] - [ScriptExecutorsManager-6604cccfc9eca62bdcca68bd-450a5214-9ffe-4d3d-9f78-9fe251cb3683-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:51:28.004 - [任务 31][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-d06ae37a-918e-47a2-aae0-a09499c997e5 
[INFO ] 2024-03-28 09:51:28.005 - [任务 31][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-d06ae37a-918e-47a2-aae0-a09499c997e5 
[INFO ] 2024-03-28 09:51:28.016 - [任务 31][增强JS] - [ScriptExecutorsManager-6604cccfc9eca62bdcca68bd-450a5214-9ffe-4d3d-9f78-9fe251cb3683-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 09:51:28.016 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] schema data cleaned 
[INFO ] 2024-03-28 09:51:28.017 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] monitor closed 
[INFO ] 2024-03-28 09:51:28.017 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] close complete, cost 48 ms 
[INFO ] 2024-03-28 09:51:28.043 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] running status set to false 
[INFO ] 2024-03-28 09:51:28.043 - [任务 31][test6] - PDK connector node stopped: HazelcastTargetPdkDataNode-cc828fa8-1bfd-497f-a9e6-a0517f0bc725 
[INFO ] 2024-03-28 09:51:28.043 - [任务 31][test6] - PDK connector node released: HazelcastTargetPdkDataNode-cc828fa8-1bfd-497f-a9e6-a0517f0bc725 
[INFO ] 2024-03-28 09:51:28.044 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] schema data cleaned 
[INFO ] 2024-03-28 09:51:28.044 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] monitor closed 
[INFO ] 2024-03-28 09:51:28.044 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] close complete, cost 27 ms 
[INFO ] 2024-03-28 09:51:30.065 - [任务 31] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 09:51:30.067 - [任务 31] - Stopped task aspect(s) 
[INFO ] 2024-03-28 09:51:30.067 - [任务 31] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 09:51:30.119 - [任务 31] - Remove memory task client succeed, task: 任务 31[6604cccfc9eca62bdcca68bd] 
[INFO ] 2024-03-28 09:51:30.120 - [任务 31] - Destroy memory task client cache succeed, task: 任务 31[6604cccfc9eca62bdcca68bd] 
[INFO ] 2024-03-28 09:59:40.887 - [任务 31] - Start task milestones: 6604cccfc9eca62bdcca68bd(任务 31) 
[INFO ] 2024-03-28 09:59:40.888 - [任务 31] - Task initialization... 
[INFO ] 2024-03-28 09:59:40.972 - [任务 31] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 09:59:40.972 - [任务 31] - The engine receives 任务 31 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 09:59:41.078 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:59:41.082 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:59:41.082 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:59:41.083 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] preload schema finished, cost 42 ms 
[INFO ] 2024-03-28 09:59:41.083 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] preload schema finished, cost 42 ms 
[INFO ] 2024-03-28 09:59:41.083 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] preload schema finished, cost 41 ms 
[INFO ] 2024-03-28 09:59:41.404 - [任务 31][test2] - Source node "test2" read batch size: 100 
[INFO ] 2024-03-28 09:59:41.405 - [任务 31][test2] - Source node "test2" event queue capacity: 200 
[INFO ] 2024-03-28 09:59:41.406 - [任务 31][test2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 09:59:41.406 - [任务 31][test2] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 09:59:41.460 - [任务 31][test2] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 09:59:41.460 - [任务 31][test2] - Initial sync started 
[INFO ] 2024-03-28 09:59:41.460 - [任务 31][test2] - Starting batch read, table name: test2, offset: null 
[INFO ] 2024-03-28 09:59:41.461 - [任务 31][test2] - Table test2 is going to be initial synced 
[INFO ] 2024-03-28 09:59:41.666 - [任务 31][test6] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 09:59:41.681 - [任务 31][test6] - Table "test.test6" exists, skip auto create table 
[INFO ] 2024-03-28 09:59:41.681 - [任务 31][test6] - The table test6 has already exist. 
[INFO ] 2024-03-28 09:59:43.506 - [任务 31] - Stop task milestones: 6604cccfc9eca62bdcca68bd(任务 31)  
[INFO ] 2024-03-28 09:59:43.616 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] running status set to false 
[ERROR] 2024-03-28 09:59:43.618 - [任务 31][test2] - java.lang.RuntimeException: java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:253)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:147)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:175)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:308)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshot$13(HazelcastSourcePdkDataNode.java:316)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 12 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.batchRead(KafkaConnector.java:305)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:388)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 18 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.consumeOne(KafkaService.java:557)
	at io.tapdata.connector.kafka.KafkaConnector.batchRead(KafkaConnector.java:303)
	... 20 more

[INFO ] 2024-03-28 09:59:43.629 - [任务 31][test2] - PDK connector node stopped: HazelcastSourcePdkDataNode-44390385-f613-432e-83f8-802220979f8c 
[INFO ] 2024-03-28 09:59:43.630 - [任务 31][test2] - PDK connector node released: HazelcastSourcePdkDataNode-44390385-f613-432e-83f8-802220979f8c 
[INFO ] 2024-03-28 09:59:43.630 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] schema data cleaned 
[INFO ] 2024-03-28 09:59:43.630 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] monitor closed 
[INFO ] 2024-03-28 09:59:43.631 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] close complete, cost 25 ms 
[INFO ] 2024-03-28 09:59:43.639 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] running status set to false 
[INFO ] 2024-03-28 09:59:43.639 - [任务 31][增强JS] - PDK connector node stopped: ScriptExecutor-Test-6cd322b1-885e-4fbb-bed7-a758af27385a 
[INFO ] 2024-03-28 09:59:43.639 - [任务 31][增强JS] - PDK connector node released: ScriptExecutor-Test-6cd322b1-885e-4fbb-bed7-a758af27385a 
[INFO ] 2024-03-28 09:59:43.639 - [任务 31][增强JS] - [ScriptExecutorsManager-6604cccfc9eca62bdcca68bd-450a5214-9ffe-4d3d-9f78-9fe251cb3683-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:59:43.650 - [任务 31][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-543ffd70-d87c-4403-b648-f2e7cefc0ff0 
[INFO ] 2024-03-28 09:59:43.651 - [任务 31][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-543ffd70-d87c-4403-b648-f2e7cefc0ff0 
[INFO ] 2024-03-28 09:59:43.651 - [任务 31][增强JS] - [ScriptExecutorsManager-6604cccfc9eca62bdcca68bd-450a5214-9ffe-4d3d-9f78-9fe251cb3683-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 09:59:43.654 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] schema data cleaned 
[INFO ] 2024-03-28 09:59:43.655 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] monitor closed 
[INFO ] 2024-03-28 09:59:43.655 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] close complete, cost 23 ms 
[INFO ] 2024-03-28 09:59:43.655 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] running status set to false 
[INFO ] 2024-03-28 09:59:43.677 - [任务 31][test6] - PDK connector node stopped: HazelcastTargetPdkDataNode-cc828fa8-1bfd-497f-a9e6-a0517f0bc725 
[INFO ] 2024-03-28 09:59:43.677 - [任务 31][test6] - PDK connector node released: HazelcastTargetPdkDataNode-cc828fa8-1bfd-497f-a9e6-a0517f0bc725 
[INFO ] 2024-03-28 09:59:43.678 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] schema data cleaned 
[INFO ] 2024-03-28 09:59:43.678 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] monitor closed 
[INFO ] 2024-03-28 09:59:43.882 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] close complete, cost 22 ms 
[INFO ] 2024-03-28 09:59:48.340 - [任务 31] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 09:59:48.340 - [任务 31] - Stopped task aspect(s) 
[INFO ] 2024-03-28 09:59:48.340 - [任务 31] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 09:59:48.374 - [任务 31] - Remove memory task client succeed, task: 任务 31[6604cccfc9eca62bdcca68bd] 
[INFO ] 2024-03-28 09:59:48.375 - [任务 31] - Destroy memory task client cache succeed, task: 任务 31[6604cccfc9eca62bdcca68bd] 
[INFO ] 2024-03-28 09:59:59.165 - [任务 31] - Start task milestones: 6604cccfc9eca62bdcca68bd(任务 31) 
[INFO ] 2024-03-28 09:59:59.167 - [任务 31] - Task initialization... 
[INFO ] 2024-03-28 09:59:59.310 - [任务 31] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 09:59:59.311 - [任务 31] - The engine receives 任务 31 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 09:59:59.372 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:59:59.373 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:59:59.373 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:59:59.418 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] preload schema finished, cost 38 ms 
[INFO ] 2024-03-28 09:59:59.420 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] preload schema finished, cost 38 ms 
[INFO ] 2024-03-28 09:59:59.421 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] preload schema finished, cost 38 ms 
[INFO ] 2024-03-28 09:59:59.751 - [任务 31][test6] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 09:59:59.751 - [任务 31][test2] - Source node "test2" read batch size: 100 
[INFO ] 2024-03-28 09:59:59.751 - [任务 31][test2] - Source node "test2" event queue capacity: 200 
[INFO ] 2024-03-28 09:59:59.751 - [任务 31][test2] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-28 09:59:59.803 - [任务 31][test2] - batch offset found: {"test2":[]},stream offset found: [] 
[WARN ] 2024-03-28 09:59:59.804 - [任务 31][test2] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 09:59:59.804 - [任务 31][test2] - Initial sync started 
[INFO ] 2024-03-28 09:59:59.804 - [任务 31][test2] - Starting batch read, table name: test2, offset: [] 
[INFO ] 2024-03-28 10:00:00.009 - [任务 31][test2] - Table test2 is going to be initial synced 
[INFO ] 2024-03-28 10:00:06.324 - [任务 31][test2] - Initial sync completed 
[INFO ] 2024-03-28 10:00:06.324 - [任务 31][test2] - Incremental sync starting... 
[INFO ] 2024-03-28 10:00:06.324 - [任务 31][test2] - Initial sync completed 
[INFO ] 2024-03-28 10:00:06.324 - [任务 31][test2] - Starting stream read, table list: [test2], offset: [] 
[INFO ] 2024-03-28 10:00:47.019 - [任务 31][增强JS] - Alter table in memory, qualified name: PN_450a5214-9ffe-4d3d-9f78-9fe251cb3683 
[INFO ] 2024-03-28 10:00:47.367 - [任务 31][增强JS] - Alter table schema transform finished 
[INFO ] 2024-03-28 10:03:01.485 - [任务 31][增强JS] - Alter table in memory, qualified name: PN_450a5214-9ffe-4d3d-9f78-9fe251cb3683 
[INFO ] 2024-03-28 10:03:27.330 - [任务 31][增强JS] - Alter table schema transform finished 
[INFO ] 2024-03-28 10:06:02.733 - [任务 31] - Stop task milestones: 6604cccfc9eca62bdcca68bd(任务 31)  
[INFO ] 2024-03-28 10:06:02.997 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] running status set to false 
[ERROR] 2024-03-28 10:06:02.997 - [任务 31][test2] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 10:06:03.004 - [任务 31][test2] - PDK connector node stopped: HazelcastSourcePdkDataNode-44390385-f613-432e-83f8-802220979f8c 
[INFO ] 2024-03-28 10:06:03.004 - [任务 31][test2] - PDK connector node released: HazelcastSourcePdkDataNode-44390385-f613-432e-83f8-802220979f8c 
[INFO ] 2024-03-28 10:06:03.005 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] schema data cleaned 
[INFO ] 2024-03-28 10:06:03.005 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] monitor closed 
[INFO ] 2024-03-28 10:06:03.005 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] close complete, cost 22 ms 
[INFO ] 2024-03-28 10:06:03.005 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] running status set to false 
[INFO ] 2024-03-28 10:06:03.015 - [任务 31][增强JS] - PDK connector node stopped: ScriptExecutor-Test-3a40342d-6f04-4928-bb69-95aeb1b74100 
[INFO ] 2024-03-28 10:06:03.020 - [任务 31][增强JS] - PDK connector node released: ScriptExecutor-Test-3a40342d-6f04-4928-bb69-95aeb1b74100 
[INFO ] 2024-03-28 10:06:03.020 - [任务 31][增强JS] - [ScriptExecutorsManager-6604cccfc9eca62bdcca68bd-450a5214-9ffe-4d3d-9f78-9fe251cb3683-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 10:06:03.032 - [任务 31][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-33150c39-5bea-4cd1-8d09-acc206f28d81 
[INFO ] 2024-03-28 10:06:03.033 - [任务 31][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-33150c39-5bea-4cd1-8d09-acc206f28d81 
[INFO ] 2024-03-28 10:06:03.033 - [任务 31][增强JS] - [ScriptExecutorsManager-6604cccfc9eca62bdcca68bd-450a5214-9ffe-4d3d-9f78-9fe251cb3683-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 10:06:03.049 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] schema data cleaned 
[INFO ] 2024-03-28 10:06:03.050 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] monitor closed 
[INFO ] 2024-03-28 10:06:03.051 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] close complete, cost 44 ms 
[INFO ] 2024-03-28 10:06:03.051 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] running status set to false 
[INFO ] 2024-03-28 10:06:03.073 - [任务 31][test6] - PDK connector node stopped: HazelcastTargetPdkDataNode-cc828fa8-1bfd-497f-a9e6-a0517f0bc725 
[INFO ] 2024-03-28 10:06:03.073 - [任务 31][test6] - PDK connector node released: HazelcastTargetPdkDataNode-cc828fa8-1bfd-497f-a9e6-a0517f0bc725 
[INFO ] 2024-03-28 10:06:03.073 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] schema data cleaned 
[INFO ] 2024-03-28 10:06:03.074 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] monitor closed 
[INFO ] 2024-03-28 10:06:03.074 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] close complete, cost 23 ms 
[INFO ] 2024-03-28 10:06:06.661 - [任务 31] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 10:06:06.661 - [任务 31] - Stopped task aspect(s) 
[INFO ] 2024-03-28 10:06:06.661 - [任务 31] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 10:06:06.684 - [任务 31] - Remove memory task client succeed, task: 任务 31[6604cccfc9eca62bdcca68bd] 
[INFO ] 2024-03-28 10:06:06.685 - [任务 31] - Destroy memory task client cache succeed, task: 任务 31[6604cccfc9eca62bdcca68bd] 
[INFO ] 2024-03-28 10:06:19.754 - [任务 31] - Start task milestones: 6604cccfc9eca62bdcca68bd(任务 31) 
[INFO ] 2024-03-28 10:06:19.754 - [任务 31] - Task initialization... 
[INFO ] 2024-03-28 10:06:19.853 - [任务 31] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 10:06:19.853 - [任务 31] - The engine receives 任务 31 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 10:06:19.921 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:06:19.921 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:06:19.921 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:06:19.959 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] preload schema finished, cost 38 ms 
[INFO ] 2024-03-28 10:06:19.960 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] preload schema finished, cost 39 ms 
[INFO ] 2024-03-28 10:06:19.960 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] preload schema finished, cost 38 ms 
[INFO ] 2024-03-28 10:06:20.723 - [任务 31][test2] - Source node "test2" read batch size: 100 
[INFO ] 2024-03-28 10:06:20.724 - [任务 31][test2] - Source node "test2" event queue capacity: 200 
[INFO ] 2024-03-28 10:06:20.724 - [任务 31][test2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 10:06:20.726 - [任务 31][test2] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 10:06:20.864 - [任务 31][test2] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 10:06:20.865 - [任务 31][test2] - Initial sync started 
[INFO ] 2024-03-28 10:06:20.865 - [任务 31][test2] - Starting batch read, table name: test2, offset: null 
[INFO ] 2024-03-28 10:06:20.866 - [任务 31][test2] - Table test2 is going to be initial synced 
[INFO ] 2024-03-28 10:06:21.070 - [任务 31][test6] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 10:06:27.204 - [任务 31][test2] - Initial sync completed 
[INFO ] 2024-03-28 10:06:27.204 - [任务 31][test2] - Incremental sync starting... 
[INFO ] 2024-03-28 10:06:27.204 - [任务 31][test2] - Initial sync completed 
[INFO ] 2024-03-28 10:06:30.046 - [任务 31][test2] - Starting stream read, table list: [test2], offset: [] 
[INFO ] 2024-03-28 10:06:48.980 - [任务 31][增强JS] - Alter table in memory, qualified name: PN_450a5214-9ffe-4d3d-9f78-9fe251cb3683 
[INFO ] 2024-03-28 10:07:07.173 - [任务 31][增强JS] - Alter table schema transform finished 
[INFO ] 2024-03-28 10:07:07.174 - [任务 31][增强JS] - Alter table in memory, qualified name: PN_450a5214-9ffe-4d3d-9f78-9fe251cb3683 
[INFO ] 2024-03-28 10:07:07.376 - [任务 31][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 10:07:07.496 - [任务 31][test6] - java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'test6' <-- Error Message -->
java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'test6'

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'test6'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'test6'
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$executeAlterFieldNameFunction$31(HazelcastTargetPdkDataNode.java:493)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeAlterFieldNameFunction(HazelcastTargetPdkDataNode.java:488)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'test6'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.fieldDDLHandler(CommonDbConnector.java:424)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$30(HazelcastTargetPdkDataNode.java:494)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 27 more

[INFO ] 2024-03-28 10:07:07.699 - [任务 31][test6] - Job suspend in error handle 
[INFO ] 2024-03-28 10:07:07.994 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] running status set to false 
[INFO ] 2024-03-28 10:07:08.004 - [任务 31][test2] - Incremental sync completed 
[ERROR] 2024-03-28 10:07:08.004 - [任务 31][test2] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 10:07:08.017 - [任务 31][test2] - PDK connector node stopped: HazelcastSourcePdkDataNode-44390385-f613-432e-83f8-802220979f8c 
[INFO ] 2024-03-28 10:07:08.017 - [任务 31][test2] - PDK connector node released: HazelcastSourcePdkDataNode-44390385-f613-432e-83f8-802220979f8c 
[INFO ] 2024-03-28 10:07:08.017 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] schema data cleaned 
[INFO ] 2024-03-28 10:07:08.017 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] monitor closed 
[INFO ] 2024-03-28 10:07:08.019 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] close complete, cost 26 ms 
[INFO ] 2024-03-28 10:07:08.020 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] running status set to false 
[INFO ] 2024-03-28 10:07:08.031 - [任务 31][增强JS] - PDK connector node stopped: ScriptExecutor-Test-e185f425-174f-4bf9-ba10-a2db430c080b 
[INFO ] 2024-03-28 10:07:08.031 - [任务 31][增强JS] - PDK connector node released: ScriptExecutor-Test-e185f425-174f-4bf9-ba10-a2db430c080b 
[INFO ] 2024-03-28 10:07:08.031 - [任务 31][增强JS] - [ScriptExecutorsManager-6604cccfc9eca62bdcca68bd-450a5214-9ffe-4d3d-9f78-9fe251cb3683-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 10:07:08.041 - [任务 31][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-b4d4e5c4-16d3-4cca-ae53-972d34b50577 
[INFO ] 2024-03-28 10:07:08.041 - [任务 31][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-b4d4e5c4-16d3-4cca-ae53-972d34b50577 
[INFO ] 2024-03-28 10:07:08.041 - [任务 31][增强JS] - [ScriptExecutorsManager-6604cccfc9eca62bdcca68bd-450a5214-9ffe-4d3d-9f78-9fe251cb3683-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 10:07:08.043 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] schema data cleaned 
[INFO ] 2024-03-28 10:07:08.044 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] monitor closed 
[INFO ] 2024-03-28 10:07:08.045 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] close complete, cost 24 ms 
[INFO ] 2024-03-28 10:07:08.046 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] running status set to false 
[INFO ] 2024-03-28 10:07:08.068 - [任务 31][test6] - PDK connector node stopped: HazelcastTargetPdkDataNode-cc828fa8-1bfd-497f-a9e6-a0517f0bc725 
[INFO ] 2024-03-28 10:07:08.068 - [任务 31][test6] - PDK connector node released: HazelcastTargetPdkDataNode-cc828fa8-1bfd-497f-a9e6-a0517f0bc725 
[INFO ] 2024-03-28 10:07:08.068 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] schema data cleaned 
[INFO ] 2024-03-28 10:07:08.069 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] monitor closed 
[INFO ] 2024-03-28 10:07:08.275 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] close complete, cost 25 ms 
[INFO ] 2024-03-28 10:07:12.037 - [任务 31] - Task [任务 31] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 10:07:12.037 - [任务 31] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 10:07:12.066 - [任务 31] - Stop task milestones: 6604cccfc9eca62bdcca68bd(任务 31)  
[INFO ] 2024-03-28 10:07:12.067 - [任务 31] - Stopped task aspect(s) 
[INFO ] 2024-03-28 10:07:12.067 - [任务 31] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 10:07:12.091 - [任务 31] - Remove memory task client succeed, task: 任务 31[6604cccfc9eca62bdcca68bd] 
[INFO ] 2024-03-28 10:07:12.099 - [任务 31] - Destroy memory task client cache succeed, task: 任务 31[6604cccfc9eca62bdcca68bd] 
[INFO ] 2024-03-28 12:20:32.410 - [任务 31] - Start task milestones: 6604cccfc9eca62bdcca68bd(任务 31) 
[INFO ] 2024-03-28 12:20:32.411 - [任务 31] - Task initialization... 
[INFO ] 2024-03-28 12:20:32.412 - [任务 31] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 12:20:32.412 - [任务 31] - The engine receives 任务 31 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 12:20:32.671 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 12:20:32.674 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] start preload schema,table counts: 1 
[INFO ] 2024-03-28 12:20:32.675 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] start preload schema,table counts: 1 
[INFO ] 2024-03-28 12:20:32.923 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] preload schema finished, cost 245 ms 
[INFO ] 2024-03-28 12:20:32.927 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] preload schema finished, cost 246 ms 
[INFO ] 2024-03-28 12:20:32.933 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] preload schema finished, cost 244 ms 
[INFO ] 2024-03-28 12:20:33.804 - [任务 31][test6] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 12:20:33.889 - [任务 31][test2] - Source node "test2" read batch size: 100 
[INFO ] 2024-03-28 12:20:33.890 - [任务 31][test2] - Source node "test2" event queue capacity: 200 
[INFO ] 2024-03-28 12:20:33.893 - [任务 31][test2] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-28 12:20:34.124 - [任务 31][test2] - batch offset found: {"test2":[]},stream offset found: [] 
[WARN ] 2024-03-28 12:20:34.223 - [任务 31][test2] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 12:20:34.224 - [任务 31][test2] - Initial sync started 
[INFO ] 2024-03-28 12:20:34.224 - [任务 31][test2] - Starting batch read, table name: test2, offset: [] 
[INFO ] 2024-03-28 12:20:34.233 - [任务 31][test2] - Table test2 is going to be initial synced 
[INFO ] 2024-03-28 12:20:41.070 - [任务 31][test2] - Initial sync completed 
[INFO ] 2024-03-28 12:20:41.073 - [任务 31][test2] - Incremental sync starting... 
[INFO ] 2024-03-28 12:20:41.073 - [任务 31][test2] - Initial sync completed 
[INFO ] 2024-03-28 12:20:41.083 - [任务 31][test2] - Starting stream read, table list: [test2], offset: [] 
[INFO ] 2024-03-28 12:20:43.577 - [任务 31][增强JS] - Alter table in memory, qualified name: PN_450a5214-9ffe-4d3d-9f78-9fe251cb3683 
[INFO ] 2024-03-28 12:20:43.985 - [任务 31][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 12:20:44.019 - [任务 31][test6] - java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'test6' <-- Error Message -->
java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'test6'

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'test6'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'test6'
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$executeAlterFieldNameFunction$31(HazelcastTargetPdkDataNode.java:493)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeAlterFieldNameFunction(HazelcastTargetPdkDataNode.java:488)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:445)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'test6'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.fieldDDLHandler(CommonDbConnector.java:424)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$30(HazelcastTargetPdkDataNode.java:494)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 27 more

[INFO ] 2024-03-28 12:20:44.020 - [任务 31][test6] - Job suspend in error handle 
[INFO ] 2024-03-28 12:20:44.126 - [任务 31][增强JS] - Alter table in memory, qualified name: PN_450a5214-9ffe-4d3d-9f78-9fe251cb3683 
[INFO ] 2024-03-28 12:20:44.126 - [任务 31][增强JS] - Alter table schema transform finished 
[INFO ] 2024-03-28 12:20:44.321 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] running status set to false 
[INFO ] 2024-03-28 12:20:44.339 - [任务 31][test2] - Incremental sync completed 
[ERROR] 2024-03-28 12:20:44.340 - [任务 31][test2] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 12:20:44.368 - [任务 31][test2] - PDK connector node stopped: HazelcastSourcePdkDataNode-44390385-f613-432e-83f8-802220979f8c 
[INFO ] 2024-03-28 12:20:44.371 - [任务 31][test2] - PDK connector node released: HazelcastSourcePdkDataNode-44390385-f613-432e-83f8-802220979f8c 
[INFO ] 2024-03-28 12:20:44.371 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] schema data cleaned 
[INFO ] 2024-03-28 12:20:44.376 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] monitor closed 
[INFO ] 2024-03-28 12:20:44.392 - [任务 31][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] close complete, cost 66 ms 
[INFO ] 2024-03-28 12:20:44.399 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] running status set to false 
[INFO ] 2024-03-28 12:20:44.427 - [任务 31][增强JS] - PDK connector node stopped: ScriptExecutor-Test-5b2f6734-d930-409b-8e22-650e1d93ad58 
[INFO ] 2024-03-28 12:20:44.434 - [任务 31][增强JS] - PDK connector node released: ScriptExecutor-Test-5b2f6734-d930-409b-8e22-650e1d93ad58 
[INFO ] 2024-03-28 12:20:44.434 - [任务 31][增强JS] - [ScriptExecutorsManager-6604cccfc9eca62bdcca68bd-450a5214-9ffe-4d3d-9f78-9fe251cb3683-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 12:20:44.533 - [任务 31][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-32b70145-7ec1-4e25-b5c9-2419f0f4ea48 
[INFO ] 2024-03-28 12:20:44.534 - [任务 31][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-32b70145-7ec1-4e25-b5c9-2419f0f4ea48 
[INFO ] 2024-03-28 12:20:44.534 - [任务 31][增强JS] - [ScriptExecutorsManager-6604cccfc9eca62bdcca68bd-450a5214-9ffe-4d3d-9f78-9fe251cb3683-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 12:20:44.560 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] schema data cleaned 
[INFO ] 2024-03-28 12:20:44.560 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] monitor closed 
[INFO ] 2024-03-28 12:20:44.566 - [任务 31][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] close complete, cost 179 ms 
[INFO ] 2024-03-28 12:20:44.566 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] running status set to false 
[INFO ] 2024-03-28 12:20:44.628 - [任务 31][test6] - PDK connector node stopped: HazelcastTargetPdkDataNode-cc828fa8-1bfd-497f-a9e6-a0517f0bc725 
[INFO ] 2024-03-28 12:20:44.629 - [任务 31][test6] - PDK connector node released: HazelcastTargetPdkDataNode-cc828fa8-1bfd-497f-a9e6-a0517f0bc725 
[INFO ] 2024-03-28 12:20:44.629 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] schema data cleaned 
[INFO ] 2024-03-28 12:20:44.629 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] monitor closed 
[INFO ] 2024-03-28 12:20:44.632 - [任务 31][test6] - Node test6[cc828fa8-1bfd-497f-a9e6-a0517f0bc725] close complete, cost 65 ms 
[INFO ] 2024-03-28 12:20:48.346 - [任务 31] - Task [任务 31] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 12:20:48.387 - [任务 31] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 12:20:48.388 - [任务 31] - Stop task milestones: 6604cccfc9eca62bdcca68bd(任务 31)  
[INFO ] 2024-03-28 12:20:48.413 - [任务 31] - Stopped task aspect(s) 
[INFO ] 2024-03-28 12:20:48.414 - [任务 31] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 12:20:48.468 - [任务 31] - Remove memory task client succeed, task: 任务 31[6604cccfc9eca62bdcca68bd] 
[INFO ] 2024-03-28 12:20:48.469 - [任务 31] - Destroy memory task client cache succeed, task: 任务 31[6604cccfc9eca62bdcca68bd] 
