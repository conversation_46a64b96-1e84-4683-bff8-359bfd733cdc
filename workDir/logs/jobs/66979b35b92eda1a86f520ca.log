[INFO ] 2024-07-17 18:21:57.477 - [来自LocalMongo的共享挖掘任务] - Start task milestones: 66979b35b92eda1a86f520ca(来自LocalMongo的共享挖掘任务) 
[INFO ] 2024-07-17 18:21:57.771 - [来自LocalMongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-17 18:21:57.835 - [来自LocalMongo的共享挖掘任务] - The engine receives 来自LocalMongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 18:21:57.929 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] start preload schema,table counts: 2 
[INFO ] 2024-07-17 18:21:57.929 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 18:21:57.962 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-17 18:21:57.962 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-17 18:21:58.055 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979b368e4a90a908ef0949, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697799db92eda1a86f5135f_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1610411823, shareCdcTaskId=66979b35b92eda1a86f520ca, connectionId=6697799db92eda1a86f5135f) 
[INFO ] 2024-07-17 18:21:58.304 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务_POLICY', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_1610411823', head seq: 0, tail seq: -1 
[INFO ] 2024-07-17 18:21:58.304 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979b368e4a90a908ef094a, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697799db92eda1a86f5135f__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1588831785, shareCdcTaskId=66979b35b92eda1a86f520ca, connectionId=6697799db92eda1a86f5135f) 
[INFO ] 2024-07-17 18:21:58.400 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_-1588831785', head seq: 0, tail seq: -1 
[INFO ] 2024-07-17 18:21:58.401 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://root:******@192.168.1.184:57017/tapdatav310?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-17 18:21:58.526 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Source node "LocalMongo" read batch size: 2000 
[INFO ] 2024-07-17 18:21:58.526 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Source node "LocalMongo" event queue capacity: 4000 
[INFO ] 2024-07-17 18:21:58.526 - [来自LocalMongo的共享挖掘任务][LocalMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 18:21:58.710 - [来自LocalMongo的共享挖掘任务][LocalMongo] - batch offset found: {},stream offset found: {"cdcOffset":1721211708,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 18:21:58.795 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Starting stream read, table list: [_tapdata_heartbeat_table, POLICY], offset: {"cdcOffset":1721211708,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 18:21:58.823 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Connector MongoDB incremental start succeed, tables: [_tapdata_heartbeat_table, POLICY], data change syncing 
[INFO ] 2024-07-17 18:22:27.363 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] running status set to false 
[INFO ] 2024-07-17 18:22:27.363 - [来自LocalMongo的共享挖掘任务][LocalMongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-5db1b8dd2e2742db97542cb0edf9e620 
[INFO ] 2024-07-17 18:22:27.364 - [来自LocalMongo的共享挖掘任务][LocalMongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-5db1b8dd2e2742db97542cb0edf9e620 
[INFO ] 2024-07-17 18:22:27.364 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] schema data cleaned 
[INFO ] 2024-07-17 18:22:27.364 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] monitor closed 
[INFO ] 2024-07-17 18:22:27.365 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] close complete, cost 34 ms 
[INFO ] 2024-07-17 18:22:27.365 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[404ea58b5e124373b7861a796d024624] running status set to false 
[INFO ] 2024-07-17 18:22:27.389 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-17 18:22:27.389 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-17 18:22:27.389 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[404ea58b5e124373b7861a796d024624] schema data cleaned 
[INFO ] 2024-07-17 18:22:27.389 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[404ea58b5e124373b7861a796d024624] monitor closed 
[INFO ] 2024-07-17 18:22:27.389 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[404ea58b5e124373b7861a796d024624] close complete, cost 24 ms 
[INFO ] 2024-07-17 18:22:28.739 - [来自LocalMongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 18:22:28.739 - [来自LocalMongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@21e0af8d 
[INFO ] 2024-07-17 18:22:28.740 - [来自LocalMongo的共享挖掘任务] - Stop task milestones: 66979b35b92eda1a86f520ca(来自LocalMongo的共享挖掘任务)  
[INFO ] 2024-07-17 18:22:28.876 - [来自LocalMongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-17 18:22:28.877 - [来自LocalMongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 18:22:28.938 - [来自LocalMongo的共享挖掘任务] - Remove memory task client succeed, task: 来自LocalMongo的共享挖掘任务[66979b35b92eda1a86f520ca] 
[INFO ] 2024-07-17 18:22:28.939 - [来自LocalMongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自LocalMongo的共享挖掘任务[66979b35b92eda1a86f520ca] 
[INFO ] 2024-07-17 18:24:25.600 - [来自LocalMongo的共享挖掘任务] - Start task milestones: 66979b35b92eda1a86f520ca(来自LocalMongo的共享挖掘任务) 
[INFO ] 2024-07-17 18:24:25.875 - [来自LocalMongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-17 18:24:25.951 - [来自LocalMongo的共享挖掘任务] - The engine receives 来自LocalMongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 18:24:25.975 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] start preload schema,table counts: 2 
[INFO ] 2024-07-17 18:24:25.975 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 18:24:26.031 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-17 18:24:26.031 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-17 18:24:26.089 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979b368e4a90a908ef094a, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697799db92eda1a86f5135f__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1588831785, shareCdcTaskId=66979b35b92eda1a86f520ca, connectionId=6697799db92eda1a86f5135f) 
[INFO ] 2024-07-17 18:24:26.394 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_-1588831785', head seq: 0, tail seq: 0 
[INFO ] 2024-07-17 18:24:26.476 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979b368e4a90a908ef0949, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697799db92eda1a86f5135f_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1610411823, shareCdcTaskId=66979b35b92eda1a86f520ca, connectionId=6697799db92eda1a86f5135f) 
[INFO ] 2024-07-17 18:24:26.477 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务_POLICY', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_1610411823', head seq: 0, tail seq: 0 
[INFO ] 2024-07-17 18:24:26.493 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://root:******@192.168.1.184:57017/tapdatav310?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-17 18:24:26.493 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Source node "LocalMongo" read batch size: 2000 
[INFO ] 2024-07-17 18:24:26.508 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Source node "LocalMongo" event queue capacity: 4000 
[INFO ] 2024-07-17 18:24:26.508 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-17 18:24:26.519 - [来自LocalMongo的共享挖掘任务][LocalMongo] - batch offset found: {},stream offset found: {"cdcOffset":1721211708,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 18:24:26.642 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Starting stream read, table list: [_tapdata_heartbeat_table, POLICY], offset: {"cdcOffset":1721211708,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 18:24:26.787 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Connector MongoDB incremental start succeed, tables: [_tapdata_heartbeat_table, POLICY], data change syncing 
[INFO ] 2024-07-17 18:25:33.100 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] running status set to false 
[INFO ] 2024-07-17 18:25:33.132 - [来自LocalMongo的共享挖掘任务][LocalMongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-5db1b8dd2e2742db97542cb0edf9e620 
[INFO ] 2024-07-17 18:25:33.132 - [来自LocalMongo的共享挖掘任务][LocalMongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-5db1b8dd2e2742db97542cb0edf9e620 
[INFO ] 2024-07-17 18:25:33.132 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] schema data cleaned 
[INFO ] 2024-07-17 18:25:33.132 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] monitor closed 
[INFO ] 2024-07-17 18:25:33.167 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] close complete, cost 83 ms 
[INFO ] 2024-07-17 18:25:33.167 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[404ea58b5e124373b7861a796d024624] running status set to false 
[INFO ] 2024-07-17 18:25:33.224 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-17 18:25:33.224 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-17 18:25:33.224 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[404ea58b5e124373b7861a796d024624] schema data cleaned 
[INFO ] 2024-07-17 18:25:33.226 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[404ea58b5e124373b7861a796d024624] monitor closed 
[INFO ] 2024-07-17 18:25:33.226 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[404ea58b5e124373b7861a796d024624] close complete, cost 58 ms 
[INFO ] 2024-07-17 18:25:35.672 - [来自LocalMongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 18:25:35.672 - [来自LocalMongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@493c1ec0 
[INFO ] 2024-07-17 18:25:35.809 - [来自LocalMongo的共享挖掘任务] - Stop task milestones: 66979b35b92eda1a86f520ca(来自LocalMongo的共享挖掘任务)  
[INFO ] 2024-07-17 18:25:35.809 - [来自LocalMongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-17 18:25:35.809 - [来自LocalMongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 18:25:35.875 - [来自LocalMongo的共享挖掘任务] - Remove memory task client succeed, task: 来自LocalMongo的共享挖掘任务[66979b35b92eda1a86f520ca] 
[INFO ] 2024-07-17 18:25:35.875 - [来自LocalMongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自LocalMongo的共享挖掘任务[66979b35b92eda1a86f520ca] 
[INFO ] 2024-07-17 18:32:17.613 - [来自LocalMongo的共享挖掘任务] - Start task milestones: 66979b35b92eda1a86f520ca(来自LocalMongo的共享挖掘任务) 
[INFO ] 2024-07-17 18:32:17.945 - [来自LocalMongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-17 18:32:17.945 - [来自LocalMongo的共享挖掘任务] - The engine receives 来自LocalMongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 18:32:18.024 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] start preload schema,table counts: 3 
[INFO ] 2024-07-17 18:32:18.024 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 18:32:18.056 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-17 18:32:18.056 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-17 18:32:18.112 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979b368e4a90a908ef094a, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697799db92eda1a86f5135f__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1588831785, shareCdcTaskId=66979b35b92eda1a86f520ca, connectionId=6697799db92eda1a86f5135f) 
[INFO ] 2024-07-17 18:32:18.112 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979c1c8e4a90a908efc3bb, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697799db92eda1a86f5135f_CLAIM3, version=v2, tableName=CLAIM3, externalStorageTableName=ExternalStorage_SHARE_CDC_1235134868, shareCdcTaskId=66979b35b92eda1a86f520ca, connectionId=6697799db92eda1a86f5135f) 
[INFO ] 2024-07-17 18:32:18.112 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_-1588831785', head seq: 0, tail seq: 64 
[INFO ] 2024-07-17 18:32:18.136 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979b368e4a90a908ef0949, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697799db92eda1a86f5135f_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1610411823, shareCdcTaskId=66979b35b92eda1a86f520ca, connectionId=6697799db92eda1a86f5135f) 
[INFO ] 2024-07-17 18:32:18.136 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务_CLAIM3', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_1235134868', head seq: 0, tail seq: -1 
[INFO ] 2024-07-17 18:32:18.147 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务_POLICY', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_1610411823', head seq: 0, tail seq: 0 
[INFO ] 2024-07-17 18:32:18.148 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://root:******@192.168.1.184:57017/tapdatav310?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-17 18:32:18.284 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Source node "LocalMongo" read batch size: 2000 
[INFO ] 2024-07-17 18:32:18.284 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Source node "LocalMongo" event queue capacity: 4000 
[INFO ] 2024-07-17 18:32:18.285 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-17 18:32:18.419 - [来自LocalMongo的共享挖掘任务][LocalMongo] - batch offset found: {},stream offset found: {"_data":{"value":"8266979C1A000000012B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F6964006466979BDB66AB5EDE8ACB84400004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-17 18:32:18.420 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Starting stream read, table list: [_tapdata_heartbeat_table, POLICY, CLAIM3], offset: {"_data":{"value":"8266979C1A000000012B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F6964006466979BDB66AB5EDE8ACB84400004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-17 18:32:18.621 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Connector MongoDB incremental start succeed, tables: [_tapdata_heartbeat_table, POLICY, CLAIM3], data change syncing 
[INFO ] 2024-07-17 19:08:38.843 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] running status set to false 
[INFO ] 2024-07-17 19:08:38.853 - [来自LocalMongo的共享挖掘任务][LocalMongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-5db1b8dd2e2742db97542cb0edf9e620 
[INFO ] 2024-07-17 19:08:38.854 - [来自LocalMongo的共享挖掘任务][LocalMongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-5db1b8dd2e2742db97542cb0edf9e620 
[INFO ] 2024-07-17 19:08:38.854 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] schema data cleaned 
[INFO ] 2024-07-17 19:08:38.855 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] monitor closed 
[INFO ] 2024-07-17 19:08:38.858 - [来自LocalMongo的共享挖掘任务][LocalMongo] - Node LocalMongo[5db1b8dd2e2742db97542cb0edf9e620] close complete, cost 36 ms 
[INFO ] 2024-07-17 19:08:38.858 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[404ea58b5e124373b7861a796d024624] running status set to false 
[INFO ] 2024-07-17 19:08:38.927 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-17 19:08:38.927 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-17 19:08:38.928 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[404ea58b5e124373b7861a796d024624] schema data cleaned 
[INFO ] 2024-07-17 19:08:38.928 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[404ea58b5e124373b7861a796d024624] monitor closed 
[INFO ] 2024-07-17 19:08:39.133 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[404ea58b5e124373b7861a796d024624] close complete, cost 70 ms 
[INFO ] 2024-07-17 19:08:43.565 - [来自LocalMongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 19:08:43.565 - [来自LocalMongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2d0c67c1 
[INFO ] 2024-07-17 19:08:43.718 - [来自LocalMongo的共享挖掘任务] - Stop task milestones: 66979b35b92eda1a86f520ca(来自LocalMongo的共享挖掘任务)  
[INFO ] 2024-07-17 19:08:43.718 - [来自LocalMongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-17 19:08:43.718 - [来自LocalMongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 19:08:43.804 - [来自LocalMongo的共享挖掘任务] - Remove memory task client succeed, task: 来自LocalMongo的共享挖掘任务[66979b35b92eda1a86f520ca] 
[INFO ] 2024-07-17 19:08:43.804 - [来自LocalMongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自LocalMongo的共享挖掘任务[66979b35b92eda1a86f520ca] 
[INFO ] 2024-07-17 19:21:58.337 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务_POLICY', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_1610411823', head seq: 0, tail seq: 2 
[INFO ] 2024-07-17 19:21:58.338 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务_CLAIM3', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_1235134868', head seq: 0, tail seq: 0 
[INFO ] 2024-07-17 19:21:58.338 - [来自LocalMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_-1588831785', head seq: 0, tail seq: 2639 
