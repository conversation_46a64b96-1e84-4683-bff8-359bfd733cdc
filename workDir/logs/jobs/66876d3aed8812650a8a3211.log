[INFO ] 2024-07-05 11:49:49.944 - [任务 49] - Task initialization... 
[INFO ] 2024-07-05 11:49:50.161 - [任务 49] - Start task milestones: 66876d3aed8812650a8a3211(任务 49) 
[INFO ] 2024-07-05 11:49:50.213 - [任务 49] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-05 11:49:50.253 - [任务 49] - The engine receives 任务 49 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 11:49:50.334 - [任务 49][dummy_test] - Node dummy_test[60590c45-3e39-4a9b-a13a-041758d8c0ff] start preload schema,table counts: 1 
[INFO ] 2024-07-05 11:49:50.340 - [任务 49][TEST1] - Node TEST1[06ad603d-c183-4904-be16-a7aae007654f] start preload schema,table counts: 1 
[INFO ] 2024-07-05 11:49:50.384 - [任务 49][dummy_test] - Node dummy_test[60590c45-3e39-4a9b-a13a-041758d8c0ff] preload schema finished, cost 1 ms 
[INFO ] 2024-07-05 11:49:50.386 - [任务 49][TEST1] - Node TEST1[06ad603d-c183-4904-be16-a7aae007654f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 11:49:51.492 - [任务 49][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-05 11:49:51.761 - [任务 49][TEST1] - Source node "TEST1" read batch size: 100 
[INFO ] 2024-07-05 11:49:51.762 - [任务 49][TEST1] - Source node "TEST1" event queue capacity: 200 
[INFO ] 2024-07-05 11:49:51.762 - [任务 49][TEST1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-05 11:49:52.012 - [任务 49][TEST1] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":68904331,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 11:49:52.076 - [任务 49][TEST1] - Initial sync started 
[INFO ] 2024-07-05 11:49:52.076 - [任务 49][TEST1] - Starting batch read, table name: TEST1, offset: null 
[INFO ] 2024-07-05 11:49:52.120 - [任务 49][TEST1] - Table TEST1 is going to be initial synced 
[INFO ] 2024-07-05 11:49:52.121 - [任务 49][TEST1] - Query table 'TEST1' counts: 0 
[INFO ] 2024-07-05 11:49:52.146 - [任务 49][TEST1] - Table [TEST1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-05 11:49:52.146 - [任务 49][TEST1] - Initial sync completed 
[INFO ] 2024-07-05 11:49:52.146 - [任务 49][TEST1] - Incremental sync starting... 
[INFO ] 2024-07-05 11:49:52.146 - [任务 49][TEST1] - Initial sync completed 
[INFO ] 2024-07-05 11:49:52.327 - [任务 49][TEST1] - Starting stream read, table list: [TEST1, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":68904331,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 11:49:52.328 - [任务 49][TEST1] - Incremental sync completed 
[INFO ] 2024-07-05 11:49:52.361 - [任务 49][TEST1] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-05 11:49:52.362 - [任务 49][TEST1] - java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: ORA-00942: 表或视图不存在
 <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: ORA-00942: 表或视图不存在


<-- Simple Stack Trace -->
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: 表或视图不存在

	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: ORA-00942: 表或视图不存在

	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: ORA-00942: 表或视图不存在

	at io.tapdata.connector.oracle.cdc.logminer.OracleLogMiner.lambda$getColumnType$1(OracleLogMiner.java:134)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.connector.oracle.cdc.logminer.OracleLogMiner.getColumnType(OracleLogMiner.java:121)
	at io.tapdata.connector.oracle.cdc.logminer.OracleLogMiner.init(OracleLogMiner.java:106)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.init(SingleOracleLogMiner.java:38)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.init(OracleCdcRunner.java:62)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:406)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.sql.SQLSyntaxErrorException: ORA-00942: 表或视图不存在

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:630)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:77)
	at io.tapdata.connector.oracle.cdc.logminer.OracleLogMiner.lambda$getColumnType$1(OracleLogMiner.java:123)
	... 24 more
Caused by: Error : 942, Position : 28, Sql = SELECT * FROM "C##TAPDATA2"."_tapdata_heartbeat_table", OriginalSql = SELECT * FROM "C##TAPDATA2"."_tapdata_heartbeat_table", Error Msg = ORA-00942: 表或视图不存在

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	... 41 more

[INFO ] 2024-07-05 11:49:52.569 - [任务 49][TEST1] - Job suspend in error handle 
[INFO ] 2024-07-05 11:49:52.661 - [任务 49][TEST1] - Node TEST1[06ad603d-c183-4904-be16-a7aae007654f] running status set to false 
[INFO ] 2024-07-05 11:49:52.711 - [任务 49][TEST1] - PDK connector node stopped: HazelcastSourcePdkDataNode-06ad603d-c183-4904-be16-a7aae007654f 
[INFO ] 2024-07-05 11:49:52.711 - [任务 49][TEST1] - PDK connector node released: HazelcastSourcePdkDataNode-06ad603d-c183-4904-be16-a7aae007654f 
[INFO ] 2024-07-05 11:49:52.712 - [任务 49][TEST1] - Node TEST1[06ad603d-c183-4904-be16-a7aae007654f] schema data cleaned 
[INFO ] 2024-07-05 11:49:52.712 - [任务 49][TEST1] - Node TEST1[06ad603d-c183-4904-be16-a7aae007654f] monitor closed 
[INFO ] 2024-07-05 11:49:52.724 - [任务 49][TEST1] - Node TEST1[06ad603d-c183-4904-be16-a7aae007654f] close complete, cost 58 ms 
[INFO ] 2024-07-05 11:49:52.724 - [任务 49][dummy_test] - Node dummy_test[60590c45-3e39-4a9b-a13a-041758d8c0ff] running status set to false 
[INFO ] 2024-07-05 11:49:52.756 - [任务 49][dummy_test] - Stop connector 
[INFO ] 2024-07-05 11:49:52.756 - [任务 49][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-60590c45-3e39-4a9b-a13a-041758d8c0ff 
[INFO ] 2024-07-05 11:49:52.792 - [任务 49][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-60590c45-3e39-4a9b-a13a-041758d8c0ff 
[INFO ] 2024-07-05 11:49:52.792 - [任务 49][dummy_test] - Node dummy_test[60590c45-3e39-4a9b-a13a-041758d8c0ff] schema data cleaned 
[INFO ] 2024-07-05 11:49:52.804 - [任务 49][dummy_test] - Node dummy_test[60590c45-3e39-4a9b-a13a-041758d8c0ff] monitor closed 
[INFO ] 2024-07-05 11:49:52.804 - [任务 49][dummy_test] - Node dummy_test[60590c45-3e39-4a9b-a13a-041758d8c0ff] close complete, cost 72 ms 
[INFO ] 2024-07-05 11:49:55.536 - [任务 49] - Task [任务 49] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-05 11:49:55.578 - [任务 49] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-05 11:49:55.578 - [任务 49] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5bb15c2d 
[INFO ] 2024-07-05 11:49:55.716 - [任务 49] - Stop task milestones: 66876d3aed8812650a8a3211(任务 49)  
[INFO ] 2024-07-05 11:49:55.716 - [任务 49] - Stopped task aspect(s) 
[INFO ] 2024-07-05 11:49:55.753 - [任务 49] - Snapshot order controller have been removed 
[INFO ] 2024-07-05 11:49:55.754 - [任务 49] - Remove memory task client succeed, task: 任务 49[66876d3aed8812650a8a3211] 
[INFO ] 2024-07-05 11:49:55.754 - [任务 49] - Destroy memory task client cache succeed, task: 任务 49[66876d3aed8812650a8a3211] 
[INFO ] 2024-07-05 12:00:46.517 - [任务 49] - Task initialization... 
[INFO ] 2024-07-05 12:00:46.519 - [任务 49] - Start task milestones: 66876d3aed8812650a8a3211(任务 49) 
[INFO ] 2024-07-05 12:00:46.725 - [任务 49] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-05 12:00:46.803 - [任务 49] - The engine receives 任务 49 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 12:00:46.807 - [任务 49][POLICY] - Node POLICY[c5d836ef-3a4f-49be-b13c-ea8b83ab0dba] start preload schema,table counts: 1 
[INFO ] 2024-07-05 12:00:46.807 - [任务 49][POLICY] - Node POLICY[c91d4ea4-17d8-4ea8-8159-66436f01c8c6] start preload schema,table counts: 1 
[INFO ] 2024-07-05 12:00:46.807 - [任务 49][POLICY] - Node POLICY[c5d836ef-3a4f-49be-b13c-ea8b83ab0dba] preload schema finished, cost 1 ms 
[INFO ] 2024-07-05 12:00:46.807 - [任务 49][POLICY] - Node POLICY[c91d4ea4-17d8-4ea8-8159-66436f01c8c6] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 12:00:47.617 - [任务 49][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-05 12:00:47.621 - [任务 49][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-05 12:00:47.621 - [任务 49][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-05 12:00:47.651 - [任务 49][POLICY] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":23575912,"gtidSet":""} 
[INFO ] 2024-07-05 12:00:47.651 - [任务 49][POLICY] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-05 12:00:47.705 - [任务 49][POLICY] - Initial sync started 
[INFO ] 2024-07-05 12:00:47.705 - [任务 49][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-05 12:00:47.706 - [任务 49][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-05 12:00:47.796 - [任务 49][POLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-05 12:00:47.798 - [任务 49][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-05 12:00:48.674 - [任务 49][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-05 12:00:48.675 - [任务 49][POLICY] - Initial sync completed 
[INFO ] 2024-07-05 12:00:48.675 - [任务 49][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-05 12:00:48.675 - [任务 49][POLICY] - Initial sync completed 
[INFO ] 2024-07-05 12:00:48.712 - [任务 49][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"filename":"binlog.000032","position":23575912,"gtidSet":""} 
[INFO ] 2024-07-05 12:00:48.714 - [任务 49][POLICY] - Starting mysql cdc, server name: 8b64ba4f-2f29-48b4-b6c8-12a0a60e248f 
[INFO ] 2024-07-05 12:00:48.797 - [任务 49][POLICY] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1163929093
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 8b64ba4f-2f29-48b4-b6c8-12a0a60e248f
  database.port: 3306
  threadName: Debezium-Mysql-Connector-8b64ba4f-2f29-48b4-b6c8-12a0a60e248f
  database.hostname: localhost
  database.password: ********
  name: 8b64ba4f-2f29-48b4-b6c8-12a0a60e248f
  pdk.offset.string: {"name":"8b64ba4f-2f29-48b4-b6c8-12a0a60e248f","offset":{"{\"server\":\"8b64ba4f-2f29-48b4-b6c8-12a0a60e248f\"}":"{\"file\":\"binlog.000032\",\"pos\":23575912,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.POLICY,test2._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-05 12:00:48.800 - [任务 49][POLICY] - Connector Mysql incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-05 12:01:13.413 - [任务 49][POLICY] - Node POLICY[c5d836ef-3a4f-49be-b13c-ea8b83ab0dba] running status set to false 
[INFO ] 2024-07-05 12:01:13.415 - [任务 49][POLICY] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-05 12:01:13.415 - [任务 49][POLICY] - Mysql binlog reader stopped 
[INFO ] 2024-07-05 12:01:13.415 - [任务 49][POLICY] - Incremental sync completed 
[INFO ] 2024-07-05 12:01:13.421 - [任务 49][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-c5d836ef-3a4f-49be-b13c-ea8b83ab0dba 
[INFO ] 2024-07-05 12:01:13.421 - [任务 49][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-c5d836ef-3a4f-49be-b13c-ea8b83ab0dba 
[INFO ] 2024-07-05 12:01:13.421 - [任务 49][POLICY] - Node POLICY[c5d836ef-3a4f-49be-b13c-ea8b83ab0dba] schema data cleaned 
[INFO ] 2024-07-05 12:01:13.421 - [任务 49][POLICY] - Node POLICY[c5d836ef-3a4f-49be-b13c-ea8b83ab0dba] monitor closed 
[INFO ] 2024-07-05 12:01:13.422 - [任务 49][POLICY] - Node POLICY[c5d836ef-3a4f-49be-b13c-ea8b83ab0dba] close complete, cost 148 ms 
[INFO ] 2024-07-05 12:01:13.628 - [任务 49][POLICY] - Node POLICY[c91d4ea4-17d8-4ea8-8159-66436f01c8c6] running status set to false 
[INFO ] 2024-07-05 12:01:13.661 - [任务 49][POLICY] - PDK connector node stopped: HazelcastTargetPdkDataNode-c91d4ea4-17d8-4ea8-8159-66436f01c8c6 
[INFO ] 2024-07-05 12:01:13.661 - [任务 49][POLICY] - PDK connector node released: HazelcastTargetPdkDataNode-c91d4ea4-17d8-4ea8-8159-66436f01c8c6 
[INFO ] 2024-07-05 12:01:13.661 - [任务 49][POLICY] - Node POLICY[c91d4ea4-17d8-4ea8-8159-66436f01c8c6] schema data cleaned 
[INFO ] 2024-07-05 12:01:13.661 - [任务 49][POLICY] - Node POLICY[c91d4ea4-17d8-4ea8-8159-66436f01c8c6] monitor closed 
[INFO ] 2024-07-05 12:01:13.661 - [任务 49][POLICY] - Node POLICY[c91d4ea4-17d8-4ea8-8159-66436f01c8c6] close complete, cost 238 ms 
[INFO ] 2024-07-05 12:01:17.167 - [任务 49] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-05 12:01:17.174 - [任务 49] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@59bf5652 
[INFO ] 2024-07-05 12:01:17.187 - [任务 49] - Stop task milestones: 66876d3aed8812650a8a3211(任务 49)  
[INFO ] 2024-07-05 12:01:17.314 - [任务 49] - Stopped task aspect(s) 
[INFO ] 2024-07-05 12:01:17.314 - [任务 49] - Snapshot order controller have been removed 
[INFO ] 2024-07-05 12:01:17.344 - [任务 49] - Remove memory task client succeed, task: 任务 49[66876d3aed8812650a8a3211] 
[INFO ] 2024-07-05 12:01:17.346 - [任务 49] - Destroy memory task client cache succeed, task: 任务 49[66876d3aed8812650a8a3211] 
