[INFO ] 2024-09-18 02:13:27.380 - [任务 3] - Task initialization... 
[INFO ] 2024-09-18 02:13:27.408 - [任务 3] - Start task milestones: 66dabc3a3445221e5bba8c79(任务 3) 
[INFO ] 2024-09-18 02:13:27.785 - [任务 3] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-09-18 02:13:27.785 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 02:13:27.841 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] start preload schema,table counts: 1 
[INFO ] 2024-09-18 02:13:27.851 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] start preload schema,table counts: 1 
[INFO ] 2024-09-18 02:13:27.852 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 02:13:27.852 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 02:13:28.028 - [任务 3][DummyTarget] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_5d2c5dbc-3578-478f-a841-0194907dec34 
[ERROR] 2024-09-18 02:13:28.065 - [任务 3][DummyTarget] - Map name: PdkStateMap_5d2c5dbc-3578-478f-a841-0194907dec34 <-- Error Message -->
Map name: PdkStateMap_5d2c5dbc-3578-478f-a841-0194907dec34

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_5d2c5dbc-3578-478f-a841-0194907dec34
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 13 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 18 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 19 more

[INFO ] 2024-09-18 02:13:28.272 - [任务 3][DummyTarget] - Job suspend in error handle 
[INFO ] 2024-09-18 02:13:28.365 - [任务 3][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-09-18 02:13:28.365 - [任务 3][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-09-18 02:13:28.365 - [任务 3][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-18 02:13:28.375 - [任务 3][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000035","position":286641728,"gtidSet":""} 
[INFO ] 2024-09-18 02:13:28.375 - [任务 3][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-18 02:13:28.452 - [任务 3][Mysql3306] - Initial sync started 
[INFO ] 2024-09-18 02:13:28.453 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] running status set to false 
[INFO ] 2024-09-18 02:13:28.453 - [任务 3][Mysql3306] - Initial sync completed 
[INFO ] 2024-09-18 02:13:28.454 - [任务 3][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-09-18 02:13:28.454 - [任务 3][Mysql3306] - Incremental sync completed 
[INFO ] 2024-09-18 02:13:28.476 - [任务 3][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-c9ca2cb4-45e8-4e88-a284-8001bfea68fb 
[INFO ] 2024-09-18 02:13:28.476 - [任务 3][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-c9ca2cb4-45e8-4e88-a284-8001bfea68fb 
[INFO ] 2024-09-18 02:13:28.476 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] schema data cleaned 
[INFO ] 2024-09-18 02:13:28.477 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] monitor closed 
[INFO ] 2024-09-18 02:13:28.482 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] close complete, cost 31 ms 
[INFO ] 2024-09-18 02:13:28.486 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] running status set to false 
[INFO ] 2024-09-18 02:13:28.486 - [任务 3][DummyTarget] - PDK connector node stopped: null 
[INFO ] 2024-09-18 02:13:28.487 - [任务 3][DummyTarget] - PDK connector node released: null 
[INFO ] 2024-09-18 02:13:28.487 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] schema data cleaned 
[INFO ] 2024-09-18 02:13:28.488 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] monitor closed 
[INFO ] 2024-09-18 02:13:28.488 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] close complete, cost 6 ms 
[INFO ] 2024-09-18 02:13:32.145 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 02:13:32.161 - [任务 3] - Stop task milestones: 66dabc3a3445221e5bba8c79(任务 3)  
[INFO ] 2024-09-18 02:13:32.191 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-09-18 02:13:32.191 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-09-18 02:13:32.223 - [任务 3] - Remove memory task client succeed, task: 任务 3[66dabc3a3445221e5bba8c79] 
[INFO ] 2024-09-18 02:13:32.227 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[66dabc3a3445221e5bba8c79] 
[INFO ] 2024-09-18 02:31:01.601 - [任务 3] - Task initialization... 
[INFO ] 2024-09-18 02:31:01.611 - [任务 3] - Start task milestones: 66dabc3a3445221e5bba8c79(任务 3) 
[INFO ] 2024-09-18 02:31:02.883 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 02:31:02.959 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 02:31:03.837 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] start preload schema,table counts: 1 
[INFO ] 2024-09-18 02:31:03.838 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] preload schema finished, cost 7 ms 
[INFO ] 2024-09-18 02:31:03.838 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] start preload schema,table counts: 1 
[INFO ] 2024-09-18 02:31:03.847 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 02:31:04.690 - [任务 3][DummyTarget] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_5d2c5dbc-3578-478f-a841-0194907dec34 
[ERROR] 2024-09-18 02:31:04.699 - [任务 3][DummyTarget] - Map name: PdkStateMap_5d2c5dbc-3578-478f-a841-0194907dec34 <-- Error Message -->
Map name: PdkStateMap_5d2c5dbc-3578-478f-a841-0194907dec34

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726597864, "i" : 32 } }, "signature" : { "hash" : { "$binary" : "PCpwtKN0sIUU/Gy1mMmwJQ99F5k=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726597864, "i" : 32 } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_5d2c5dbc-3578-478f-a841-0194907dec34
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 13 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 18 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 19 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726597864, "i" : 32 } }, "signature" : { "hash" : { "$binary" : "PCpwtKN0sIUU/Gy1mMmwJQ99F5k=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726597864, "i" : 32 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 46 more

[INFO ] 2024-09-18 02:31:04.756 - [任务 3][DummyTarget] - Job suspend in error handle 
[INFO ] 2024-09-18 02:31:05.002 - [任务 3][Mysql3306] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3136/1220849125@3a1edfda failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
 
[ERROR] 2024-09-18 02:31:05.057 - [任务 3][Mysql3306] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3136/1220849125@3a1edfda failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3136/1220849125@3a1edfda failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more


<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3136/1220849125@3a1edfda failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3136/1220849125@3a1edfda failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

[INFO ] 2024-09-18 02:31:05.067 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] running status set to false 
[INFO ] 2024-09-18 02:31:05.071 - [任务 3][Mysql3306] - PDK connector node stopped: null 
[INFO ] 2024-09-18 02:31:05.071 - [任务 3][Mysql3306] - PDK connector node released: null 
[INFO ] 2024-09-18 02:31:05.071 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] schema data cleaned 
[INFO ] 2024-09-18 02:31:05.071 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] monitor closed 
[INFO ] 2024-09-18 02:31:05.080 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] close complete, cost 8 ms 
[INFO ] 2024-09-18 02:31:05.086 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] running status set to false 
[INFO ] 2024-09-18 02:31:05.092 - [任务 3][DummyTarget] - PDK connector node stopped: null 
[INFO ] 2024-09-18 02:31:05.093 - [任务 3][DummyTarget] - PDK connector node released: null 
[INFO ] 2024-09-18 02:31:05.094 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] schema data cleaned 
[INFO ] 2024-09-18 02:31:05.096 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] monitor closed 
[INFO ] 2024-09-18 02:31:05.108 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] close complete, cost 20 ms 
[INFO ] 2024-09-18 02:31:06.184 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 02:31:06.184 - [任务 3] - Stop task milestones: 66dabc3a3445221e5bba8c79(任务 3)  
[INFO ] 2024-09-18 02:31:06.196 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-09-18 02:31:06.197 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-09-18 02:31:06.214 - [任务 3] - Remove memory task client succeed, task: 任务 3[66dabc3a3445221e5bba8c79] 
[INFO ] 2024-09-18 02:31:06.216 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[66dabc3a3445221e5bba8c79] 
[INFO ] 2024-09-18 02:31:34.623 - [任务 3] - Task initialization... 
[INFO ] 2024-09-18 02:31:34.627 - [任务 3] - Start task milestones: 66dabc3a3445221e5bba8c79(任务 3) 
[INFO ] 2024-09-18 02:31:34.926 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 02:31:34.977 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 02:31:35.130 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] start preload schema,table counts: 1 
[INFO ] 2024-09-18 02:31:35.130 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] start preload schema,table counts: 1 
[INFO ] 2024-09-18 02:31:35.132 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 02:31:35.132 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 02:31:41.089 - [任务 3][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-18 02:31:41.099 - [任务 3][DummyTarget] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-09-18 02:31:41.136 - [任务 3][DummyTarget] - Sync progress not exists, will run task as first time 
[INFO ] 2024-09-18 02:31:42.218 - [任务 3][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-09-18 02:31:42.219 - [任务 3][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-09-18 02:31:42.219 - [任务 3][Mysql3306] - Sync progress not exists, will run task as first time 
[INFO ] 2024-09-18 02:31:42.219 - [任务 3][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-18 02:31:42.231 - [任务 3][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000035","position":310950253,"gtidSet":""} 
[INFO ] 2024-09-18 02:31:42.231 - [任务 3][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-18 02:31:42.282 - [任务 3][Mysql3306] - Initial sync started 
[INFO ] 2024-09-18 02:31:42.283 - [任务 3][Mysql3306] - Starting batch read, table name: testDDL1, offset: null 
[INFO ] 2024-09-18 02:31:42.283 - [任务 3][Mysql3306] - Table testDDL1 is going to be initial synced 
[INFO ] 2024-09-18 02:31:42.341 - [任务 3][Mysql3306] - Table [testDDL1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-18 02:31:42.341 - [任务 3][Mysql3306] - Query table 'testDDL1' counts: 1 
[INFO ] 2024-09-18 02:31:42.345 - [任务 3][Mysql3306] - Initial sync completed 
[INFO ] 2024-09-18 02:31:42.345 - [任务 3][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-09-18 02:31:42.345 - [任务 3][Mysql3306] - Initial sync completed 
[INFO ] 2024-09-18 02:31:42.351 - [任务 3][Mysql3306] - Starting stream read, table list: [testDDL1], offset: {"filename":"binlog.000035","position":310950253,"gtidSet":""} 
[INFO ] 2024-09-18 02:31:42.442 - [任务 3][Mysql3306] - Starting mysql cdc, server name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5 
[INFO ] 2024-09-18 02:31:42.506 - [任务 3][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1295238956
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5
  database.port: 3306
  threadName: Debezium-Mysql-Connector-1c7acfda-a134-4014-b96b-8fc73f5f43f5
  database.hostname: localhost
  database.password: ********
  name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5
  pdk.offset.string: {"name":"1c7acfda-a134-4014-b96b-8fc73f5f43f5","offset":{"{\"server\":\"1c7acfda-a134-4014-b96b-8fc73f5f43f5\"}":"{\"file\":\"binlog.000035\",\"pos\":310950253,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDDL1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-18 02:31:42.510 - [任务 3][Mysql3306] - Connector Mysql incremental start succeed, tables: [testDDL1], data change syncing 
[WARN ] 2024-09-18 02:36:44.146 - [任务 3] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=4e9057d366d94697b1dc6049f4d9baef7138c099c4a5462ab5b1d2affa8f6be3&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266dabc3a3445221e5bba8c79%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@7957ba6, CDC=io.tapdata.milestone.entity.MilestoneEntity@33f93522, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@36d89e59, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@4b5e8e6f, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@67ba84e9, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@1d90ed19}, attrs.nodeMilestones={5d2c5dbc-3578-478f-a841-0194907dec34={CDC_WRITE=io.tapdata.milestone.entity.MilestoneEntity@6fc882a4, NODE=io.tapdata.milestone.entity.MilestoneEntity@505a44c4, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@63f41fea, SNAPSHOT_WRITE=io.tapdata.milestone.entity.MilestoneEntity@7916cea8}, c9ca2cb4-45e8-4e88-a284-8001bfea68fb={OPEN_CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@5260be8c, NODE=io.tapdata.milestone.entity.MilestoneEntity@509ebc60, SNAPSHOT_READ=io.tapdata.milestone.entity.MilestoneEntity@1356f2d6, CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@5cfa0d9a}}, syncStatus=CDC}}}}, code='SystemError', data=null, reqId=7898fe44-bedd-432d-8a36-bf5e2d7c5198}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[INFO ] 2024-09-18 02:36:44.234 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] running status set to false 
[INFO ] 2024-09-18 02:36:44.487 - [任务 3][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-c9ca2cb4-45e8-4e88-a284-8001bfea68fb 
[INFO ] 2024-09-18 02:36:44.487 - [任务 3][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-c9ca2cb4-45e8-4e88-a284-8001bfea68fb 
[INFO ] 2024-09-18 02:36:44.487 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] schema data cleaned 
[INFO ] 2024-09-18 02:36:44.487 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] monitor closed 
[INFO ] 2024-09-18 02:36:44.488 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] close complete, cost 301 ms 
[INFO ] 2024-09-18 02:36:44.489 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] running status set to false 
[WARN ] 2024-09-18 02:36:44.616 - [任务 3][DummyTarget] - Save to snapshot failed, collection: Task/syncProgress/66dabc3a3445221e5bba8c79, object: {c9ca2cb4-45e8-4e88-a284-8001bfea68fb,5d2c5dbc-3578-478f-a841-0194907dec34=SyncProgress{eventSerialNo=11, syncStage='CDC', batchOffset='{testDDL1={batch_read_connector_offset={}, batch_read_connector_status=RUNNING}}', streamOffset='MysqlStreamOffset{name='1c7acfda-a134-4014-b96b-8fc73f5f43f5', offset={{"server":"1c7acfda-a134-4014-b96b-8fc73f5f43f5"}={"ts_sec":1726597903,"file":"binlog.000035","pos":310951533,"server_id":1}}}'}}, errors: RestException{uri='http://localhost:3000/api/Task/syncProgress/66dabc3a3445221e5bba8c79?access_token=4e9057d366d94697b1dc6049f4d9baef7138c099c4a5462ab5b1d2affa8f6be3', method='POST', param={["c9ca2cb4-45e8-4e88-a284-8001bfea68fb","5d2c5dbc-3578-478f-a841-0194907dec34"]={"offset":null,"eventTime":1726598169536,"eventSerialNo":11,"sourceTime":1726598169536,"syncStage":"CDC","batchOffset":"gAFkABFqYXZhLnV0aWwuSGFzaE1hcAEUAAh0ZXN0RERMMQFkABFqYXZhLnV0aWwuSGFzaE1hcAEU\r\nABtiYXRjaF9yZWFkX2Nvbm5lY3Rvcl9vZmZzZXQBZAARamF2YS51dGlsLkhhc2hNYXCoARQAG2Jh\r\ndGNoX3JlYWRfY29ubmVjdG9yX3N0YXR1cwEUAAdSVU5OSU5HqKg=\r\n","streamOffset":"_tap_zip_\u001F\b\u0000\u0000\u0000\u0000\u0000\u0000\u0000-Ë£0\fE÷Tñ/@©Ê¢\u0017\u0002Âk°i\u0013lïx$ÁIÏ\u0004â¯\u001FÒ\u0013-¤*®t®.°sþ¦\u00068\u0014?ÁÃßÕÓ6\u0018uzFó6øQI{`üQ\u000Bûv¢x©\\{¨Æü»\tû;¡ª4a<°ÂüÒ5.¹â4\u0017×çÈÂí%\u0002ègp\u0002»­ãz&F¥ô-¶ßN¬°ÇÄÄF-;W7\u0004À\u001Fo½\u0001°Eºö4`\u0012ÃlkïöQ\u0019ÄßaËï¸\\\u0019ÝTvÃ\u001E,Ø5EJ¸HH+ª\u0015ï\u0012{XâÕ®ãÿa·Éx4Êb¾ï\u0003_»u+\u001CBk\n=_z9Ìàæ\u0017ø]\u0005Ûïxûã¼ïÁ¥ùµGU\u0006Ûí¬k_+ãÿð7îO^Éi\u0000Ó3^]Ä\u0016§HE´0Õ´Ìâ=Û-\"ÈHH6#ÝX×[L\u0018\u001F¯¿¡\r²É»l¥>3Í`?N¢\tñ¹¶]Ô]\u0017¬\"\u000B~\u0012P\u001BË²Xµ/Øú¿wL:\u0007J×ð>¢ÑyÔÏè\u0017R»9%\u0005\u0013ôR½¹âsYd\"íüí\t>?uí\u001Fvt\"Ò\u0006\u0002\u0000\u0000","type":"NORMAL"}}, code='SystemError', data=null, reqId=d38a920b-a619-4746-a35a-d1b2839f0294}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[INFO ] 2024-09-18 02:36:44.618 - [任务 3][DummyTarget] - Stop connector 
[INFO ] 2024-09-18 02:36:44.619 - [任务 3][DummyTarget] - PDK connector node stopped: HazelcastTargetPdkDataNode-5d2c5dbc-3578-478f-a841-0194907dec34 
[INFO ] 2024-09-18 02:36:44.620 - [任务 3][DummyTarget] - PDK connector node released: HazelcastTargetPdkDataNode-5d2c5dbc-3578-478f-a841-0194907dec34 
[INFO ] 2024-09-18 02:36:44.620 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] schema data cleaned 
[INFO ] 2024-09-18 02:36:44.620 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] monitor closed 
[INFO ] 2024-09-18 02:36:44.620 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] close complete, cost 108 ms 
[INFO ] 2024-09-18 02:36:48.864 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 02:36:48.908 - [任务 3] - Stop task milestones: 66dabc3a3445221e5bba8c79(任务 3)  
[WARN ] 2024-09-18 02:36:48.920 - [任务 3] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=4e9057d366d94697b1dc6049f4d9baef7138c099c4a5462ab5b1d2affa8f6be3&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266dabc3a3445221e5bba8c79%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@7957ba6, CDC=io.tapdata.milestone.entity.MilestoneEntity@33f93522, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@36d89e59, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@4b5e8e6f, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@67ba84e9, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@1d90ed19}, attrs.nodeMilestones={5d2c5dbc-3578-478f-a841-0194907dec34={CDC_WRITE=io.tapdata.milestone.entity.MilestoneEntity@6fc882a4, NODE=io.tapdata.milestone.entity.MilestoneEntity@505a44c4, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@63f41fea, SNAPSHOT_WRITE=io.tapdata.milestone.entity.MilestoneEntity@7916cea8}, c9ca2cb4-45e8-4e88-a284-8001bfea68fb={OPEN_CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@5260be8c, NODE=io.tapdata.milestone.entity.MilestoneEntity@509ebc60, SNAPSHOT_READ=io.tapdata.milestone.entity.MilestoneEntity@1356f2d6, CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@5cfa0d9a}}, syncStatus=CDC}}}}, code='SystemError', data=null, reqId=14e2ce55-c29f-4dae-bf7f-949676fb3681}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[INFO ] 2024-09-18 02:36:48.920 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-09-18 02:36:48.920 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-09-18 02:36:48.921 - [任务 3] - Remove memory task client succeed, task: 任务 3[66dabc3a3445221e5bba8c79] 
[INFO ] 2024-09-18 02:36:48.921 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[66dabc3a3445221e5bba8c79] 
[INFO ] 2024-09-18 10:33:24.838 - [任务 3] - Task initialization... 
[INFO ] 2024-09-18 10:33:25.639 - [任务 3] - Start task milestones: 66dabc3a3445221e5bba8c79(任务 3) 
[INFO ] 2024-09-18 10:33:27.414 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 10:33:27.786 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 10:33:28.888 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] start preload schema,table counts: 1 
[INFO ] 2024-09-18 10:33:28.890 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 10:33:28.890 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] start preload schema,table counts: 1 
[INFO ] 2024-09-18 10:33:28.891 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 10:33:30.738 - [任务 3][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-18 10:33:30.738 - [任务 3][DummyTarget] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-09-18 10:33:30.875 - [任务 3][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-09-18 10:33:30.876 - [任务 3][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-09-18 10:33:30.876 - [任务 3][Mysql3306] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-18 10:33:30.884 - [任务 3][Mysql3306] - batch offset found: {"testDDL1":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"name":"1c7acfda-a134-4014-b96b-8fc73f5f43f5","offset":{"{\"server\":\"1c7acfda-a134-4014-b96b-8fc73f5f43f5\"}":"{\"ts_sec\":1726597903,\"file\":\"binlog.000035\",\"pos\":310951533,\"server_id\":1}"}} 
[INFO ] 2024-09-18 10:33:30.885 - [任务 3][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-18 10:33:30.992 - [任务 3][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-09-18 10:33:30.992 - [任务 3][Mysql3306] - Initial sync completed 
[INFO ] 2024-09-18 10:33:30.993 - [任务 3][Mysql3306] - Starting stream read, table list: [testDDL1], offset: {"name":"1c7acfda-a134-4014-b96b-8fc73f5f43f5","offset":{"{\"server\":\"1c7acfda-a134-4014-b96b-8fc73f5f43f5\"}":"{\"ts_sec\":1726597903,\"file\":\"binlog.000035\",\"pos\":310951533,\"server_id\":1}"}} 
[INFO ] 2024-09-18 10:33:31.083 - [任务 3][Mysql3306] - Starting mysql cdc, server name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5 
[INFO ] 2024-09-18 10:33:31.227 - [任务 3][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 532609158
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5
  database.port: 3306
  threadName: Debezium-Mysql-Connector-1c7acfda-a134-4014-b96b-8fc73f5f43f5
  database.hostname: localhost
  database.password: ********
  name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5
  pdk.offset.string: {"name":"1c7acfda-a134-4014-b96b-8fc73f5f43f5","offset":{"{\"server\":\"1c7acfda-a134-4014-b96b-8fc73f5f43f5\"}":"{\"ts_sec\":1726597903,\"file\":\"binlog.000035\",\"pos\":310951533,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDDL1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-18 10:33:31.569 - [任务 3][Mysql3306] - Connector Mysql incremental start succeed, tables: [testDDL1], data change syncing 
[INFO ] 2024-09-18 10:37:42.348 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] running status set to false 
[INFO ] 2024-09-18 10:42:46.625 - [任务 3] - Task initialization... 
[INFO ] 2024-09-18 10:42:46.883 - [任务 3] - Start task milestones: 66dabc3a3445221e5bba8c79(任务 3) 
[INFO ] 2024-09-18 10:42:48.913 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 10:42:49.314 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 10:42:50.448 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] start preload schema,table counts: 1 
[INFO ] 2024-09-18 10:42:50.448 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] start preload schema,table counts: 1 
[INFO ] 2024-09-18 10:42:50.449 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 10:42:50.450 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 10:42:58.923 - [任务 3][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-18 10:42:58.923 - [任务 3][DummyTarget] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-09-18 10:43:00.736 - [任务 3][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-09-18 10:43:00.747 - [任务 3][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-09-18 10:43:00.748 - [任务 3][Mysql3306] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-18 10:43:00.784 - [任务 3][Mysql3306] - batch offset found: {},stream offset found: {"name":"1c7acfda-a134-4014-b96b-8fc73f5f43f5","offset":{"{\"server\":\"1c7acfda-a134-4014-b96b-8fc73f5f43f5\"}":"{\"ts_sec\":1726626812,\"file\":\"binlog.000035\",\"pos\":322299222,\"server_id\":1}"}} 
[INFO ] 2024-09-18 10:43:00.784 - [任务 3][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-18 10:43:00.944 - [任务 3][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-09-18 10:43:00.946 - [任务 3][Mysql3306] - Initial sync completed 
[INFO ] 2024-09-18 10:43:00.946 - [任务 3][Mysql3306] - Starting stream read, table list: [testDDL1], offset: {"name":"1c7acfda-a134-4014-b96b-8fc73f5f43f5","offset":{"{\"server\":\"1c7acfda-a134-4014-b96b-8fc73f5f43f5\"}":"{\"ts_sec\":1726626812,\"file\":\"binlog.000035\",\"pos\":322299222,\"server_id\":1}"}} 
[INFO ] 2024-09-18 10:43:00.996 - [任务 3][Mysql3306] - Starting mysql cdc, server name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5 
[INFO ] 2024-09-18 10:43:00.996 - [任务 3][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1571256763
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5
  database.port: 3306
  threadName: Debezium-Mysql-Connector-1c7acfda-a134-4014-b96b-8fc73f5f43f5
  database.hostname: localhost
  database.password: ********
  name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5
  pdk.offset.string: {"name":"1c7acfda-a134-4014-b96b-8fc73f5f43f5","offset":{"{\"server\":\"1c7acfda-a134-4014-b96b-8fc73f5f43f5\"}":"{\"ts_sec\":1726626812,\"file\":\"binlog.000035\",\"pos\":322299222,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDDL1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-18 10:43:01.260 - [任务 3][Mysql3306] - Connector Mysql incremental start succeed, tables: [testDDL1], data change syncing 
[INFO ] 2024-09-18 10:47:38.820 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] running status set to false 
[INFO ] 2024-09-18 10:52:37.484 - [任务 3] - Start task milestones: 66dabc3a3445221e5bba8c79(任务 3) 
[INFO ] 2024-09-18 10:52:37.486 - [任务 3] - Task initialization... 
[INFO ] 2024-09-18 10:52:40.684 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 10:52:41.277 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 10:52:42.575 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] start preload schema,table counts: 1 
[INFO ] 2024-09-18 10:52:42.576 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 10:52:42.577 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] start preload schema,table counts: 1 
[INFO ] 2024-09-18 10:52:42.577 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 10:52:44.151 - [任务 3][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-18 10:52:44.153 - [任务 3][DummyTarget] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-09-18 10:52:45.124 - [任务 3][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-09-18 10:52:45.124 - [任务 3][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-09-18 10:52:45.125 - [任务 3][Mysql3306] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-18 10:52:45.127 - [任务 3][Mysql3306] - batch offset found: {},stream offset found: {"name":"1c7acfda-a134-4014-b96b-8fc73f5f43f5","offset":{"{\"server\":\"1c7acfda-a134-4014-b96b-8fc73f5f43f5\"}":"{\"file\":\"binlog.000035\",\"pos\":344514785,\"server_id\":1}"}} 
[INFO ] 2024-09-18 10:52:45.127 - [任务 3][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-18 10:52:45.434 - [任务 3][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-09-18 10:52:45.437 - [任务 3][Mysql3306] - Initial sync completed 
[INFO ] 2024-09-18 10:52:45.438 - [任务 3][Mysql3306] - Starting stream read, table list: [testDDL1], offset: {"name":"1c7acfda-a134-4014-b96b-8fc73f5f43f5","offset":{"{\"server\":\"1c7acfda-a134-4014-b96b-8fc73f5f43f5\"}":"{\"file\":\"binlog.000035\",\"pos\":344514785,\"server_id\":1}"}} 
[INFO ] 2024-09-18 10:52:45.540 - [任务 3][Mysql3306] - Starting mysql cdc, server name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5 
[INFO ] 2024-09-18 10:52:45.557 - [任务 3][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 364808844
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5
  database.port: 3306
  threadName: Debezium-Mysql-Connector-1c7acfda-a134-4014-b96b-8fc73f5f43f5
  database.hostname: localhost
  database.password: ********
  name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5
  pdk.offset.string: {"name":"1c7acfda-a134-4014-b96b-8fc73f5f43f5","offset":{"{\"server\":\"1c7acfda-a134-4014-b96b-8fc73f5f43f5\"}":"{\"file\":\"binlog.000035\",\"pos\":344514785,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDDL1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-18 10:52:46.996 - [任务 3][Mysql3306] - Connector Mysql incremental start succeed, tables: [testDDL1], data change syncing 
[INFO ] 2024-09-18 10:53:37.578 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] running status set to false 
[INFO ] 2024-09-18 10:58:38.495 - [任务 3] - Task initialization... 
[INFO ] 2024-09-18 10:58:38.519 - [任务 3] - Start task milestones: 66dabc3a3445221e5bba8c79(任务 3) 
[INFO ] 2024-09-18 10:58:40.109 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 10:58:40.425 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 10:58:41.309 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] start preload schema,table counts: 1 
[INFO ] 2024-09-18 10:58:41.321 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] start preload schema,table counts: 1 
[INFO ] 2024-09-18 10:58:41.323 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 10:58:41.325 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] preload schema finished, cost 2 ms 
[INFO ] 2024-09-18 10:58:43.343 - [任务 3][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-18 10:58:43.369 - [任务 3][DummyTarget] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-09-18 10:58:44.163 - [任务 3][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-09-18 10:58:44.164 - [任务 3][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-09-18 10:58:44.165 - [任务 3][Mysql3306] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-18 10:58:44.177 - [任务 3][Mysql3306] - batch offset found: {},stream offset found: {"name":"1c7acfda-a134-4014-b96b-8fc73f5f43f5","offset":{"{\"server\":\"1c7acfda-a134-4014-b96b-8fc73f5f43f5\"}":"{\"file\":\"binlog.000035\",\"pos\":344514785,\"server_id\":1}"}} 
[INFO ] 2024-09-18 10:58:44.181 - [任务 3][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-18 10:58:44.225 - [任务 3][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-09-18 10:58:44.229 - [任务 3][Mysql3306] - Initial sync completed 
[INFO ] 2024-09-18 10:58:44.230 - [任务 3][Mysql3306] - Starting stream read, table list: [testDDL1], offset: {"name":"1c7acfda-a134-4014-b96b-8fc73f5f43f5","offset":{"{\"server\":\"1c7acfda-a134-4014-b96b-8fc73f5f43f5\"}":"{\"file\":\"binlog.000035\",\"pos\":344514785,\"server_id\":1}"}} 
[INFO ] 2024-09-18 10:58:44.251 - [任务 3][Mysql3306] - Starting mysql cdc, server name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5 
[INFO ] 2024-09-18 10:58:44.254 - [任务 3][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1471506820
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5
  database.port: 3306
  threadName: Debezium-Mysql-Connector-1c7acfda-a134-4014-b96b-8fc73f5f43f5
  database.hostname: localhost
  database.password: ********
  name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5
  pdk.offset.string: {"name":"1c7acfda-a134-4014-b96b-8fc73f5f43f5","offset":{"{\"server\":\"1c7acfda-a134-4014-b96b-8fc73f5f43f5\"}":"{\"file\":\"binlog.000035\",\"pos\":344514785,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDDL1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-18 10:58:45.599 - [任务 3][Mysql3306] - Connector Mysql incremental start succeed, tables: [testDDL1], data change syncing 
[INFO ] 2024-09-18 11:17:12.570 - [任务 3] - Task initialization... 
[INFO ] 2024-09-18 11:17:12.572 - [任务 3] - Start task milestones: 66dabc3a3445221e5bba8c79(任务 3) 
[INFO ] 2024-09-18 11:17:12.832 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 11:17:12.835 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 11:17:12.884 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] start preload schema,table counts: 1 
[INFO ] 2024-09-18 11:17:12.885 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] start preload schema,table counts: 1 
[INFO ] 2024-09-18 11:17:12.885 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 11:17:13.089 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 11:17:13.137 - [任务 3][DummyTarget] - Node(DummyTarget) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-18 11:17:13.137 - [任务 3][DummyTarget] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-09-18 11:17:13.255 - [任务 3][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-09-18 11:17:13.259 - [任务 3][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-09-18 11:17:13.259 - [任务 3][Mysql3306] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-18 11:17:13.263 - [任务 3][Mysql3306] - batch offset found: {},stream offset found: {"name":"1c7acfda-a134-4014-b96b-8fc73f5f43f5","offset":{"{\"server\":\"1c7acfda-a134-4014-b96b-8fc73f5f43f5\"}":"{\"file\":\"binlog.000035\",\"pos\":344514785,\"server_id\":1}"}} 
[INFO ] 2024-09-18 11:17:13.332 - [任务 3][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-18 11:17:13.334 - [任务 3][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-09-18 11:17:13.340 - [任务 3][Mysql3306] - Initial sync completed 
[INFO ] 2024-09-18 11:17:13.340 - [任务 3][Mysql3306] - Starting stream read, table list: [testDDL1], offset: {"name":"1c7acfda-a134-4014-b96b-8fc73f5f43f5","offset":{"{\"server\":\"1c7acfda-a134-4014-b96b-8fc73f5f43f5\"}":"{\"file\":\"binlog.000035\",\"pos\":344514785,\"server_id\":1}"}} 
[INFO ] 2024-09-18 11:17:13.373 - [任务 3][Mysql3306] - Starting mysql cdc, server name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5 
[INFO ] 2024-09-18 11:17:13.375 - [任务 3][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 831546119
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5
  database.port: 3306
  threadName: Debezium-Mysql-Connector-1c7acfda-a134-4014-b96b-8fc73f5f43f5
  database.hostname: localhost
  database.password: ********
  name: 1c7acfda-a134-4014-b96b-8fc73f5f43f5
  pdk.offset.string: {"name":"1c7acfda-a134-4014-b96b-8fc73f5f43f5","offset":{"{\"server\":\"1c7acfda-a134-4014-b96b-8fc73f5f43f5\"}":"{\"file\":\"binlog.000035\",\"pos\":344514785,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDDL1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-18 11:17:13.606 - [任务 3][Mysql3306] - Connector Mysql incremental start succeed, tables: [testDDL1], data change syncing 
[WARN ] 2024-09-18 11:18:59.847 - [任务 3][DummyTarget] - Save to snapshot failed, collection: Task/syncProgress/66dabc3a3445221e5bba8c79, object: {c9ca2cb4-45e8-4e88-a284-8001bfea68fb,5d2c5dbc-3578-478f-a841-0194907dec34=SyncProgress{eventSerialNo=4, syncStage='CDC', batchOffset='{}', streamOffset='MysqlStreamOffset{name='1c7acfda-a134-4014-b96b-8fc73f5f43f5', offset={{"server":"1c7acfda-a134-4014-b96b-8fc73f5f43f5"}={"file":"binlog.000035","pos":344514785,"server_id":1}}}'}}, errors: RestException{uri='http://localhost:3000/api/Task/syncProgress/66dabc3a3445221e5bba8c79?access_token=1129302f8a914f148e5cce80b62d947b2d81f08746c74d53aa41c2d927ee5178', method='POST', param={["c9ca2cb4-45e8-4e88-a284-8001bfea68fb","5d2c5dbc-3578-478f-a841-0194907dec34"]={"offset":null,"eventTime":1726629529626,"eventSerialNo":4,"sourceTime":1726629529626,"syncStage":"CDC","batchOffset":"gAFkABFqYXZhLnV0aWwuSGFzaE1hcKg=\r\n","streamOffset":"_tap_zip_\u001F\b\u0000\u0000\u0000\u0000\u0000\u0000\u0000-Ér£0\u0010@ïTñ/,fª|C\u0003f\u001B$Å éÆX\u0002d{\u0006l¯\u000FÉ¤\u000FÝ^Þë+ìg\rlWpñ£^-\u0012»§$å±Ä¯ZZ\u00035ÓW#¬éà¥v¬¡¾¥6è9\u0019¶¦\\ò6\u0006ZêoªÂ$Û\u0018IÅ}5I\fÌ¯!@?í[¼.ýL*é\u00194;´´n±µF\u000EO¶M\b½~æ5#RN½õ1ÖtÞ¸Ó¡\t\u0012\\¯Ã×1¹3§\u0005mÉ]X°£sÎDsI·fc]!±%ÞÝT\u0005lï/%Ç·B«ÊùùÞVö­p\t1p=é¦0^áOí\u001F9;1þmÞ÷àôÞÈb«üãÈ\u0002>«ÊÛÎø?<Óù®áW²[å}vÈ`$\u0014g\u0011.tk95XO3£\u001CiqÌ(O&Úõ\u0006\u0015ÚáëoÈD\u0006ÕYìÔ\u0017ñA\u0012À·Q09,\u000B2zÔry±õ0#\u0017f´]DìDsm\"qîÝËÝæ³Y¨Jc\u0014k+\u0018C\u001Fv÷å\\«Ê'/\u001E\u0000ðê\u0001\u0000\u0000","type":"NORMAL"}}, code='SystemError', data=null, reqId=748c2aff-2887-4beb-b300-6e963373b560}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[WARN ] 2024-09-18 11:19:03.356 - [任务 3] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=1129302f8a914f148e5cce80b62d947b2d81f08746c74d53aa41c2d927ee5178&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266dabc3a3445221e5bba8c79%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@5c89ea82, CDC=io.tapdata.milestone.entity.MilestoneEntity@524e0f63, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@567181f2, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@13730558, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@3d204e, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@222a123d}, attrs.nodeMilestones={5d2c5dbc-3578-478f-a841-0194907dec34={CDC_WRITE=io.tapdata.milestone.entity.MilestoneEntity@788e699b, NODE=io.tapdata.milestone.entity.MilestoneEntity@735cfa17, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@1a0a8b62, SNAPSHOT_WRITE=io.tapdata.milestone.entity.MilestoneEntity@6843d9e4}, c9ca2cb4-45e8-4e88-a284-8001bfea68fb={OPEN_CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@638aa7c6, NODE=io.tapdata.milestone.entity.MilestoneEntity@771265e2, SNAPSHOT_READ=io.tapdata.milestone.entity.MilestoneEntity@398da1f9, CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@4f77c3d5}}, syncStatus=CDC}}}}, code='SystemError', data=null, reqId=dede37f6-e6c6-4eda-8429-36011a1d283a}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[INFO ] 2024-09-18 11:19:03.668 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] running status set to false 
[INFO ] 2024-09-18 11:19:03.771 - [任务 3][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-18 11:19:03.772 - [任务 3][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-09-18 11:19:03.778 - [任务 3][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-c9ca2cb4-45e8-4e88-a284-8001bfea68fb 
[INFO ] 2024-09-18 11:19:03.779 - [任务 3][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-c9ca2cb4-45e8-4e88-a284-8001bfea68fb 
[INFO ] 2024-09-18 11:19:03.779 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] schema data cleaned 
[INFO ] 2024-09-18 11:19:03.780 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] monitor closed 
[INFO ] 2024-09-18 11:19:03.784 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] close complete, cost 115 ms 
[INFO ] 2024-09-18 11:19:03.784 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] running status set to false 
[WARN ] 2024-09-18 11:19:03.849 - [任务 3][DummyTarget] - Save to snapshot failed, collection: Task/syncProgress/66dabc3a3445221e5bba8c79, object: {c9ca2cb4-45e8-4e88-a284-8001bfea68fb,5d2c5dbc-3578-478f-a841-0194907dec34=SyncProgress{eventSerialNo=4, syncStage='CDC', batchOffset='{}', streamOffset='MysqlStreamOffset{name='1c7acfda-a134-4014-b96b-8fc73f5f43f5', offset={{"server":"1c7acfda-a134-4014-b96b-8fc73f5f43f5"}={"file":"binlog.000035","pos":344514785,"server_id":1}}}'}}, errors: RestException{uri='http://localhost:3000/api/Task/syncProgress/66dabc3a3445221e5bba8c79?access_token=1129302f8a914f148e5cce80b62d947b2d81f08746c74d53aa41c2d927ee5178', method='POST', param={["c9ca2cb4-45e8-4e88-a284-8001bfea68fb","5d2c5dbc-3578-478f-a841-0194907dec34"]={"offset":null,"eventTime":1726629529626,"eventSerialNo":4,"sourceTime":1726629529626,"syncStage":"CDC","batchOffset":"gAFkABFqYXZhLnV0aWwuSGFzaE1hcKg=\r\n","streamOffset":"_tap_zip_\u001F\b\u0000\u0000\u0000\u0000\u0000\u0000\u0000-Ér£0\u0010@ïTñ/,fª|C\u0003f\u001B$Å éÆX\u0002d{\u0006l¯\u000FÉ¤\u000FÝ^Þë+ìg\rlWpñ£^-\u0012»§$å±Ä¯ZZ\u00035ÓW#¬éà¥v¬¡¾¥6è9\u0019¶¦\\ò6\u0006ZêoªÂ$Û\u0018IÅ}5I\fÌ¯!@?í[¼.ýL*é\u00194;´´n±µF\u000EO¶M\b½~æ5#RN½õ1ÖtÞ¸Ó¡\t\u0012\\¯Ã×1¹3§\u0005mÉ]X°£sÎDsI·fc]!±%ÞÝT\u0005lï/%Ç·B«ÊùùÞVö­p\t1p=é¦0^áOí\u001F9;1þmÞ÷àôÞÈb«üãÈ\u0002>«ÊÛÎø?<Óù®áW²[å}vÈ`$\u0014g\u0011.tk95XO3£\u001CiqÌ(O&Úõ\u0006\u0015ÚáëoÈD\u0006ÕYìÔ\u0017ñA\u0012À·Q09,\u000B2zÔry±õ0#\u0017f´]DìDsm\"qîÝËÝæ³Y¨Jc\u0014k+\u0018C\u001Fv÷å\\«Ê'/\u001E\u0000ðê\u0001\u0000\u0000","type":"NORMAL"}}, code='SystemError', data=null, reqId=0bec0556-5d92-41c6-8d30-a1721674d684}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[INFO ] 2024-09-18 11:19:03.850 - [任务 3][DummyTarget] - Stop connector 
[INFO ] 2024-09-18 11:19:03.857 - [任务 3][DummyTarget] - PDK connector node stopped: HazelcastTargetPdkDataNode-5d2c5dbc-3578-478f-a841-0194907dec34 
[INFO ] 2024-09-18 11:19:03.857 - [任务 3][DummyTarget] - PDK connector node released: HazelcastTargetPdkDataNode-5d2c5dbc-3578-478f-a841-0194907dec34 
[INFO ] 2024-09-18 11:19:03.857 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] schema data cleaned 
[INFO ] 2024-09-18 11:19:03.859 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] monitor closed 
[INFO ] 2024-09-18 11:19:03.859 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] close complete, cost 77 ms 
[INFO ] 2024-09-18 11:19:07.124 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 11:19:07.124 - [任务 3] - Stop task milestones: 66dabc3a3445221e5bba8c79(任务 3)  
[WARN ] 2024-09-18 11:19:07.164 - [任务 3] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=1129302f8a914f148e5cce80b62d947b2d81f08746c74d53aa41c2d927ee5178&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266dabc3a3445221e5bba8c79%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@5c89ea82, CDC=io.tapdata.milestone.entity.MilestoneEntity@524e0f63, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@567181f2, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@13730558, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@3d204e, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@222a123d}, attrs.nodeMilestones={5d2c5dbc-3578-478f-a841-0194907dec34={CDC_WRITE=io.tapdata.milestone.entity.MilestoneEntity@788e699b, NODE=io.tapdata.milestone.entity.MilestoneEntity@735cfa17, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@1a0a8b62, SNAPSHOT_WRITE=io.tapdata.milestone.entity.MilestoneEntity@6843d9e4}, c9ca2cb4-45e8-4e88-a284-8001bfea68fb={OPEN_CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@638aa7c6, NODE=io.tapdata.milestone.entity.MilestoneEntity@771265e2, SNAPSHOT_READ=io.tapdata.milestone.entity.MilestoneEntity@398da1f9, CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@4f77c3d5}}, syncStatus=CDC}}}}, code='SystemError', data=null, reqId=349e9495-cecf-4b2c-b5e6-8a9fea8e0e1f}: System error: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>}; nested exception is com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties=<hidden>} 
[INFO ] 2024-09-18 11:19:07.165 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-09-18 11:19:07.165 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-09-18 11:19:07.165 - [任务 3] - Remove memory task client succeed, task: 任务 3[66dabc3a3445221e5bba8c79] 
[INFO ] 2024-09-18 11:19:07.170 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[66dabc3a3445221e5bba8c79] 
[INFO ] 2024-09-18 11:24:02.698 - [任务 3] - Task initialization... 
[INFO ] 2024-09-18 11:24:02.787 - [任务 3] - Start task milestones: 66dabc3a3445221e5bba8c79(任务 3) 
[INFO ] 2024-09-18 11:24:02.904 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-18 11:24:02.949 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-18 11:24:02.991 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] start preload schema,table counts: 1 
[INFO ] 2024-09-18 11:24:02.991 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] start preload schema,table counts: 1 
[INFO ] 2024-09-18 11:24:02.992 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 11:24:02.993 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] preload schema finished, cost 0 ms 
[INFO ] 2024-09-18 11:24:03.088 - [任务 3][DummyTarget] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_5d2c5dbc-3578-478f-a841-0194907dec34 
[INFO ] 2024-09-18 11:24:03.088 - [任务 3][Mysql3306] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3210/562760413@1c6d6713 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
 
[ERROR] 2024-09-18 11:24:03.089 - [任务 3][DummyTarget] - Map name: PdkStateMap_5d2c5dbc-3578-478f-a841-0194907dec34 <-- Error Message -->
Map name: PdkStateMap_5d2c5dbc-3578-478f-a841-0194907dec34

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726629842, "i" : 46 } }, "signature" : { "hash" : { "$binary" : "csTekglhWezeIIxR1nWhOWhCCGs=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726629842, "i" : 46 } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_5d2c5dbc-3578-478f-a841-0194907dec34
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:408)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:171)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 13 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 18 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 19 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1726629842, "i" : 46 } }, "signature" : { "hash" : { "$binary" : "csTekglhWezeIIxR1nWhOWhCCGs=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1726629842, "i" : 46 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 46 more

[INFO ] 2024-09-18 11:24:03.101 - [任务 3][DummyTarget] - Job suspend in error handle 
[ERROR] 2024-09-18 11:24:03.101 - [任务 3][Mysql3306] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3210/562760413@1c6d6713 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3210/562760413@1c6d6713 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more


<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3210/562760413@1c6d6713 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3210/562760413@1c6d6713 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_c9ca2cb4-45e8-4e88-a284-8001bfea68fb
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:271)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

[INFO ] 2024-09-18 11:24:03.105 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] running status set to false 
[INFO ] 2024-09-18 11:24:03.105 - [任务 3][Mysql3306] - PDK connector node stopped: null 
[INFO ] 2024-09-18 11:24:03.105 - [任务 3][Mysql3306] - PDK connector node released: null 
[INFO ] 2024-09-18 11:24:03.105 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] schema data cleaned 
[INFO ] 2024-09-18 11:24:03.110 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] monitor closed 
[INFO ] 2024-09-18 11:24:03.111 - [任务 3][Mysql3306] - Node Mysql3306[c9ca2cb4-45e8-4e88-a284-8001bfea68fb] close complete, cost 6 ms 
[INFO ] 2024-09-18 11:24:03.111 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] running status set to false 
[INFO ] 2024-09-18 11:24:03.111 - [任务 3][DummyTarget] - PDK connector node stopped: null 
[INFO ] 2024-09-18 11:24:03.111 - [任务 3][DummyTarget] - PDK connector node released: null 
[INFO ] 2024-09-18 11:24:03.111 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] schema data cleaned 
[INFO ] 2024-09-18 11:24:03.111 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] monitor closed 
[INFO ] 2024-09-18 11:24:03.317 - [任务 3][DummyTarget] - Node DummyTarget[5d2c5dbc-3578-478f-a841-0194907dec34] close complete, cost 0 ms 
[INFO ] 2024-09-18 11:24:07.968 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-18 11:24:07.972 - [任务 3] - Stop task milestones: 66dabc3a3445221e5bba8c79(任务 3)  
[INFO ] 2024-09-18 11:24:07.984 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-09-18 11:24:07.984 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-09-18 11:24:08.007 - [任务 3] - Remove memory task client succeed, task: 任务 3[66dabc3a3445221e5bba8c79] 
[INFO ] 2024-09-18 11:24:08.007 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[66dabc3a3445221e5bba8c79] 
