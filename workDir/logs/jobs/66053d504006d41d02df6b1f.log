[INFO ] 2024-03-28 17:50:12.973 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:12.973 - [orders(100)][76cf07e4-396c-4a90-b8b4-8c2f86d6cc19] - Node 76cf07e4-396c-4a90-b8b4-8c2f86d6cc19[76cf07e4-396c-4a90-b8b4-8c2f86d6cc19] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:50:12.973 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:12.973 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:12.973 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:12.973 - [orders(100)][76cf07e4-396c-4a90-b8b4-8c2f86d6cc19] - Node 76cf07e4-396c-4a90-b8b4-8c2f86d6cc19[76cf07e4-396c-4a90-b8b4-8c2f86d6cc19] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:13.034 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:50:13.238 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7c84349d error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7c84349d error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7c84349d error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 17:50:13.415 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:50:13.432 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:50:13.432 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:13.432 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:13.432 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:50:13.432 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:50:13.638 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 14 ms 
[INFO ] 2024-03-28 17:50:15.587 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:50:15.587 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:50:15.587 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:50:15.588 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:15.590 - [orders(100)][76cf07e4-396c-4a90-b8b4-8c2f86d6cc19] - Node 76cf07e4-396c-4a90-b8b4-8c2f86d6cc19[76cf07e4-396c-4a90-b8b4-8c2f86d6cc19] running status set to false 
[INFO ] 2024-03-28 17:50:15.590 - [orders(100)][76cf07e4-396c-4a90-b8b4-8c2f86d6cc19] - Node 76cf07e4-396c-4a90-b8b4-8c2f86d6cc19[76cf07e4-396c-4a90-b8b4-8c2f86d6cc19] schema data cleaned 
[INFO ] 2024-03-28 17:50:15.591 - [orders(100)][76cf07e4-396c-4a90-b8b4-8c2f86d6cc19] - Node 76cf07e4-396c-4a90-b8b4-8c2f86d6cc19[76cf07e4-396c-4a90-b8b4-8c2f86d6cc19] monitor closed 
[INFO ] 2024-03-28 17:50:15.591 - [orders(100)][76cf07e4-396c-4a90-b8b4-8c2f86d6cc19] - Node 76cf07e4-396c-4a90-b8b4-8c2f86d6cc19[76cf07e4-396c-4a90-b8b4-8c2f86d6cc19] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:15.791 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-76cf07e4-396c-4a90-b8b4-8c2f86d6cc19 complete, cost 2718ms 
[INFO ] 2024-03-28 17:50:22.491 - [orders(100)][dba4f7a7-1480-48a7-9729-d57d63c1cfe7] - Node dba4f7a7-1480-48a7-9729-d57d63c1cfe7[dba4f7a7-1480-48a7-9729-d57d63c1cfe7] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:50:22.491 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:22.491 - [orders(100)][32400b19-29d7-4bb2-a08f-d0658b127711] - Node 32400b19-29d7-4bb2-a08f-d0658b127711[32400b19-29d7-4bb2-a08f-d0658b127711] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:50:22.491 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:22.491 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:22.491 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:22.491 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:22.491 - [orders(100)][32400b19-29d7-4bb2-a08f-d0658b127711] - Node 32400b19-29d7-4bb2-a08f-d0658b127711[32400b19-29d7-4bb2-a08f-d0658b127711] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:22.491 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:22.491 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:22.491 - [orders(100)][dba4f7a7-1480-48a7-9729-d57d63c1cfe7] - Node dba4f7a7-1480-48a7-9729-d57d63c1cfe7[dba4f7a7-1480-48a7-9729-d57d63c1cfe7] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:22.491 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:22.553 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 17:50:22.553 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:50:22.574 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1221f9c1 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1221f9c1 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1221f9c1 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-28 17:50:22.576 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4259328f error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4259328f error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4259328f error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 17:50:22.721 - [orders(100)][dbd12139-dd77-496f-b99f-c0a19d311089] - Node dbd12139-dd77-496f-b99f-c0a19d311089[dbd12139-dd77-496f-b99f-c0a19d311089] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:50:22.722 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:22.722 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:22.722 - [orders(100)][dbd12139-dd77-496f-b99f-c0a19d311089] - Node dbd12139-dd77-496f-b99f-c0a19d311089[dbd12139-dd77-496f-b99f-c0a19d311089] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:22.722 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:22.739 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 1 ms 
[WARN ] 2024-03-28 17:50:22.739 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:50:22.766 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:50:22.766 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:22.766 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:22.767 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:50:22.769 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:50:22.771 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 22 ms 
[INFO ] 2024-03-28 17:50:22.771 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:50:22.837 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@50c8fb8a error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@50c8fb8a error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@50c8fb8a error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 17:50:22.838 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:22.838 - [orders(100)][*************-4368-9e91-a2e8acbbdf35] - Node *************-4368-9e91-a2e8acbbdf35[*************-4368-9e91-a2e8acbbdf35] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:50:22.838 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:22.838 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:22.838 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:22.848 - [orders(100)][*************-4368-9e91-a2e8acbbdf35] - Node *************-4368-9e91-a2e8acbbdf35[*************-4368-9e91-a2e8acbbdf35] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:22.848 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:50:22.889 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@383e1d58 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@383e1d58 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@383e1d58 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 17:50:22.889 - [orders(100)][b2296e0f-f95e-42c5-8ff6-1112519c8101] - Node b2296e0f-f95e-42c5-8ff6-1112519c8101[b2296e0f-f95e-42c5-8ff6-1112519c8101] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:50:22.889 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:22.889 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:22.889 - [orders(100)][b2296e0f-f95e-42c5-8ff6-1112519c8101] - Node b2296e0f-f95e-42c5-8ff6-1112519c8101[b2296e0f-f95e-42c5-8ff6-1112519c8101] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:22.889 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:22.889 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:22.896 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:50:22.915 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@27b5e28b error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@27b5e28b error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@27b5e28b error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 17:50:22.918 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:50:22.934 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:50:22.934 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:22.934 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:22.934 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:50:22.934 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:50:22.972 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 16 ms 
[WARN ] 2024-03-28 17:50:22.973 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:50:22.986 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:50:22.986 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:22.986 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:22.986 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:50:22.986 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:50:23.132 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 11 ms 
[WARN ] 2024-03-28 17:50:23.132 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:50:23.142 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:50:23.142 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:23.142 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:23.142 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:50:23.143 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:50:23.143 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 8 ms 
[WARN ] 2024-03-28 17:50:23.297 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:50:23.297 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:50:23.303 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:23.303 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:23.303 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:50:23.303 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:50:23.508 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 6 ms 
[INFO ] 2024-03-28 17:50:23.779 - [orders(100)][0e1628c2-512d-4121-93e3-aaf6d37c0951] - Node 0e1628c2-512d-4121-93e3-aaf6d37c0951[0e1628c2-512d-4121-93e3-aaf6d37c0951] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:50:23.779 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:23.779 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:23.780 - [orders(100)][0e1628c2-512d-4121-93e3-aaf6d37c0951] - Node 0e1628c2-512d-4121-93e3-aaf6d37c0951[0e1628c2-512d-4121-93e3-aaf6d37c0951] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:23.780 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:23.780 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:23.792 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:50:23.941 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7111e4a5 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7111e4a5 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7111e4a5 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 17:50:23.941 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:23.941 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:23.941 - [orders(100)][f85b745d-812d-4bc3-8c7d-7988f63d7624] - Node f85b745d-812d-4bc3-8c7d-7988f63d7624[f85b745d-812d-4bc3-8c7d-7988f63d7624] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:50:23.941 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:23.941 - [orders(100)][f85b745d-812d-4bc3-8c7d-7988f63d7624] - Node f85b745d-812d-4bc3-8c7d-7988f63d7624[f85b745d-812d-4bc3-8c7d-7988f63d7624] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:23.941 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:23.997 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 17:50:23.998 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:23.998 - [orders(100)][9d73b6ff-812a-48e4-81ae-5de3f20260a5] - Node 9d73b6ff-812a-48e4-81ae-5de3f20260a5[9d73b6ff-812a-48e4-81ae-5de3f20260a5] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:50:23.998 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:23.998 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:23.998 - [orders(100)][9d73b6ff-812a-48e4-81ae-5de3f20260a5] - Node 9d73b6ff-812a-48e4-81ae-5de3f20260a5[9d73b6ff-812a-48e4-81ae-5de3f20260a5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:23.998 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[WARN ] 2024-03-28 17:50:24.008 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:50:24.008 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[ERROR] 2024-03-28 17:50:24.008 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@76d296fb error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@76d296fb error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@76d296fb error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 17:50:24.008 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 17:50:24.032 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:24.032 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:24.032 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:50:24.038 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:50:24.038 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 27 ms 
[ERROR] 2024-03-28 17:50:24.038 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6b94192c error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6b94192c error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6b94192c error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 17:50:24.188 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:50:24.188 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:50:24.200 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:24.200 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:24.200 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:50:24.200 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:50:24.357 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 14 ms 
[WARN ] 2024-03-28 17:50:24.357 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:50:24.376 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:50:24.376 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:24.376 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:24.377 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:50:24.377 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:50:24.377 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 16 ms 
[INFO ] 2024-03-28 17:50:25.032 - [orders(100)][f6e86244-6bad-42c4-9815-008fff55e559] - Node f6e86244-6bad-42c4-9815-008fff55e559[f6e86244-6bad-42c4-9815-008fff55e559] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:50:25.033 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:25.033 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:25.033 - [orders(100)][f6e86244-6bad-42c4-9815-008fff55e559] - Node f6e86244-6bad-42c4-9815-008fff55e559[f6e86244-6bad-42c4-9815-008fff55e559] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:25.033 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:25.033 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:25.099 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:50:25.099 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:50:25.099 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:50:25.099 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:25.099 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:50:25.099 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:50:25.099 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:50:25.099 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 17:50:25.099 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:25.100 - [orders(100)][32400b19-29d7-4bb2-a08f-d0658b127711] - Node 32400b19-29d7-4bb2-a08f-d0658b127711[32400b19-29d7-4bb2-a08f-d0658b127711] running status set to false 
[INFO ] 2024-03-28 17:50:25.101 - [orders(100)][32400b19-29d7-4bb2-a08f-d0658b127711] - Node 32400b19-29d7-4bb2-a08f-d0658b127711[32400b19-29d7-4bb2-a08f-d0658b127711] schema data cleaned 
[INFO ] 2024-03-28 17:50:25.101 - [orders(100)][32400b19-29d7-4bb2-a08f-d0658b127711] - Node 32400b19-29d7-4bb2-a08f-d0658b127711[32400b19-29d7-4bb2-a08f-d0658b127711] monitor closed 
[INFO ] 2024-03-28 17:50:25.101 - [orders(100)][32400b19-29d7-4bb2-a08f-d0658b127711] - Node 32400b19-29d7-4bb2-a08f-d0658b127711[32400b19-29d7-4bb2-a08f-d0658b127711] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:25.106 - [orders(100)][dba4f7a7-1480-48a7-9729-d57d63c1cfe7] - Node dba4f7a7-1480-48a7-9729-d57d63c1cfe7[dba4f7a7-1480-48a7-9729-d57d63c1cfe7] running status set to false 
[INFO ] 2024-03-28 17:50:25.106 - [orders(100)][dba4f7a7-1480-48a7-9729-d57d63c1cfe7] - Node dba4f7a7-1480-48a7-9729-d57d63c1cfe7[dba4f7a7-1480-48a7-9729-d57d63c1cfe7] schema data cleaned 
[INFO ] 2024-03-28 17:50:25.106 - [orders(100)][dba4f7a7-1480-48a7-9729-d57d63c1cfe7] - Node dba4f7a7-1480-48a7-9729-d57d63c1cfe7[dba4f7a7-1480-48a7-9729-d57d63c1cfe7] monitor closed 
[INFO ] 2024-03-28 17:50:25.106 - [orders(100)][dba4f7a7-1480-48a7-9729-d57d63c1cfe7] - Node dba4f7a7-1480-48a7-9729-d57d63c1cfe7[dba4f7a7-1480-48a7-9729-d57d63c1cfe7] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:25.108 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-dba4f7a7-1480-48a7-9729-d57d63c1cfe7 complete, cost 2741ms 
[INFO ] 2024-03-28 17:50:25.108 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-32400b19-29d7-4bb2-a08f-d0658b127711 complete, cost 2667ms 
[ERROR] 2024-03-28 17:50:25.275 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@54e2219a error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@54e2219a error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@54e2219a error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 17:50:25.281 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:50:25.281 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:50:25.291 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:25.291 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:25.291 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:50:25.291 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:50:25.291 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 12 ms 
[INFO ] 2024-03-28 17:50:25.311 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:50:25.311 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:50:25.311 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:50:25.311 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:25.314 - [orders(100)][dbd12139-dd77-496f-b99f-c0a19d311089] - Node dbd12139-dd77-496f-b99f-c0a19d311089[dbd12139-dd77-496f-b99f-c0a19d311089] running status set to false 
[INFO ] 2024-03-28 17:50:25.314 - [orders(100)][dbd12139-dd77-496f-b99f-c0a19d311089] - Node dbd12139-dd77-496f-b99f-c0a19d311089[dbd12139-dd77-496f-b99f-c0a19d311089] schema data cleaned 
[INFO ] 2024-03-28 17:50:25.314 - [orders(100)][dbd12139-dd77-496f-b99f-c0a19d311089] - Node dbd12139-dd77-496f-b99f-c0a19d311089[dbd12139-dd77-496f-b99f-c0a19d311089] monitor closed 
[INFO ] 2024-03-28 17:50:25.315 - [orders(100)][dbd12139-dd77-496f-b99f-c0a19d311089] - Node dbd12139-dd77-496f-b99f-c0a19d311089[dbd12139-dd77-496f-b99f-c0a19d311089] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:25.315 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-dbd12139-dd77-496f-b99f-c0a19d311089 complete, cost 2639ms 
[INFO ] 2024-03-28 17:50:25.380 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:50:25.380 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:50:25.380 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:50:25.380 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:25.380 - [orders(100)][*************-4368-9e91-a2e8acbbdf35] - Node *************-4368-9e91-a2e8acbbdf35[*************-4368-9e91-a2e8acbbdf35] running status set to false 
[INFO ] 2024-03-28 17:50:25.380 - [orders(100)][*************-4368-9e91-a2e8acbbdf35] - Node *************-4368-9e91-a2e8acbbdf35[*************-4368-9e91-a2e8acbbdf35] schema data cleaned 
[INFO ] 2024-03-28 17:50:25.381 - [orders(100)][*************-4368-9e91-a2e8acbbdf35] - Node *************-4368-9e91-a2e8acbbdf35[*************-4368-9e91-a2e8acbbdf35] monitor closed 
[INFO ] 2024-03-28 17:50:25.381 - [orders(100)][*************-4368-9e91-a2e8acbbdf35] - Node *************-4368-9e91-a2e8acbbdf35[*************-4368-9e91-a2e8acbbdf35] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:25.422 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-*************-4368-9e91-a2e8acbbdf35 complete, cost 2571ms 
[INFO ] 2024-03-28 17:50:25.422 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:50:25.422 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:50:25.422 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:50:25.422 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:25.426 - [orders(100)][b2296e0f-f95e-42c5-8ff6-1112519c8101] - Node b2296e0f-f95e-42c5-8ff6-1112519c8101[b2296e0f-f95e-42c5-8ff6-1112519c8101] running status set to false 
[INFO ] 2024-03-28 17:50:25.426 - [orders(100)][b2296e0f-f95e-42c5-8ff6-1112519c8101] - Node b2296e0f-f95e-42c5-8ff6-1112519c8101[b2296e0f-f95e-42c5-8ff6-1112519c8101] schema data cleaned 
[INFO ] 2024-03-28 17:50:25.426 - [orders(100)][b2296e0f-f95e-42c5-8ff6-1112519c8101] - Node b2296e0f-f95e-42c5-8ff6-1112519c8101[b2296e0f-f95e-42c5-8ff6-1112519c8101] monitor closed 
[INFO ] 2024-03-28 17:50:25.426 - [orders(100)][b2296e0f-f95e-42c5-8ff6-1112519c8101] - Node b2296e0f-f95e-42c5-8ff6-1112519c8101[b2296e0f-f95e-42c5-8ff6-1112519c8101] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:25.631 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-b2296e0f-f95e-42c5-8ff6-1112519c8101 complete, cost 2568ms 
[INFO ] 2024-03-28 17:50:26.325 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:50:26.327 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:50:26.327 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:50:26.327 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 5 ms 
[INFO ] 2024-03-28 17:50:26.329 - [orders(100)][0e1628c2-512d-4121-93e3-aaf6d37c0951] - Node 0e1628c2-512d-4121-93e3-aaf6d37c0951[0e1628c2-512d-4121-93e3-aaf6d37c0951] running status set to false 
[INFO ] 2024-03-28 17:50:26.330 - [orders(100)][0e1628c2-512d-4121-93e3-aaf6d37c0951] - Node 0e1628c2-512d-4121-93e3-aaf6d37c0951[0e1628c2-512d-4121-93e3-aaf6d37c0951] schema data cleaned 
[INFO ] 2024-03-28 17:50:26.332 - [orders(100)][0e1628c2-512d-4121-93e3-aaf6d37c0951] - Node 0e1628c2-512d-4121-93e3-aaf6d37c0951[0e1628c2-512d-4121-93e3-aaf6d37c0951] monitor closed 
[INFO ] 2024-03-28 17:50:26.333 - [orders(100)][0e1628c2-512d-4121-93e3-aaf6d37c0951] - Node 0e1628c2-512d-4121-93e3-aaf6d37c0951[0e1628c2-512d-4121-93e3-aaf6d37c0951] close complete, cost 3 ms 
[INFO ] 2024-03-28 17:50:26.536 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-0e1628c2-512d-4121-93e3-aaf6d37c0951 complete, cost 2600ms 
[INFO ] 2024-03-28 17:50:26.549 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:50:26.549 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:50:26.549 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:50:26.549 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:26.553 - [orders(100)][f85b745d-812d-4bc3-8c7d-7988f63d7624] - Node f85b745d-812d-4bc3-8c7d-7988f63d7624[f85b745d-812d-4bc3-8c7d-7988f63d7624] running status set to false 
[INFO ] 2024-03-28 17:50:26.553 - [orders(100)][f85b745d-812d-4bc3-8c7d-7988f63d7624] - Node f85b745d-812d-4bc3-8c7d-7988f63d7624[f85b745d-812d-4bc3-8c7d-7988f63d7624] schema data cleaned 
[INFO ] 2024-03-28 17:50:26.553 - [orders(100)][f85b745d-812d-4bc3-8c7d-7988f63d7624] - Node f85b745d-812d-4bc3-8c7d-7988f63d7624[f85b745d-812d-4bc3-8c7d-7988f63d7624] monitor closed 
[INFO ] 2024-03-28 17:50:26.553 - [orders(100)][f85b745d-812d-4bc3-8c7d-7988f63d7624] - Node f85b745d-812d-4bc3-8c7d-7988f63d7624[f85b745d-812d-4bc3-8c7d-7988f63d7624] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:26.566 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-f85b745d-812d-4bc3-8c7d-7988f63d7624 complete, cost 2693ms 
[INFO ] 2024-03-28 17:50:26.566 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:50:26.566 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:50:26.566 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:50:26.568 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:26.568 - [orders(100)][9d73b6ff-812a-48e4-81ae-5de3f20260a5] - Node 9d73b6ff-812a-48e4-81ae-5de3f20260a5[9d73b6ff-812a-48e4-81ae-5de3f20260a5] running status set to false 
[INFO ] 2024-03-28 17:50:26.568 - [orders(100)][9d73b6ff-812a-48e4-81ae-5de3f20260a5] - Node 9d73b6ff-812a-48e4-81ae-5de3f20260a5[9d73b6ff-812a-48e4-81ae-5de3f20260a5] schema data cleaned 
[INFO ] 2024-03-28 17:50:26.568 - [orders(100)][9d73b6ff-812a-48e4-81ae-5de3f20260a5] - Node 9d73b6ff-812a-48e4-81ae-5de3f20260a5[9d73b6ff-812a-48e4-81ae-5de3f20260a5] monitor closed 
[INFO ] 2024-03-28 17:50:26.568 - [orders(100)][9d73b6ff-812a-48e4-81ae-5de3f20260a5] - Node 9d73b6ff-812a-48e4-81ae-5de3f20260a5[9d73b6ff-812a-48e4-81ae-5de3f20260a5] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:26.773 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-9d73b6ff-812a-48e4-81ae-5de3f20260a5 complete, cost 2627ms 
[INFO ] 2024-03-28 17:50:27.642 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:50:27.643 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:50:27.643 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:50:27.648 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 2 ms 
[INFO ] 2024-03-28 17:50:27.648 - [orders(100)][f6e86244-6bad-42c4-9815-008fff55e559] - Node f6e86244-6bad-42c4-9815-008fff55e559[f6e86244-6bad-42c4-9815-008fff55e559] running status set to false 
[INFO ] 2024-03-28 17:50:27.649 - [orders(100)][f6e86244-6bad-42c4-9815-008fff55e559] - Node f6e86244-6bad-42c4-9815-008fff55e559[f6e86244-6bad-42c4-9815-008fff55e559] schema data cleaned 
[INFO ] 2024-03-28 17:50:27.649 - [orders(100)][f6e86244-6bad-42c4-9815-008fff55e559] - Node f6e86244-6bad-42c4-9815-008fff55e559[f6e86244-6bad-42c4-9815-008fff55e559] monitor closed 
[INFO ] 2024-03-28 17:50:27.649 - [orders(100)][f6e86244-6bad-42c4-9815-008fff55e559] - Node f6e86244-6bad-42c4-9815-008fff55e559[f6e86244-6bad-42c4-9815-008fff55e559] close complete, cost 2 ms 
[INFO ] 2024-03-28 17:50:27.651 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-f6e86244-6bad-42c4-9815-008fff55e559 complete, cost 2665ms 
[INFO ] 2024-03-28 17:50:30.142 - [orders(100)][13175fec-5e72-422f-96ed-a3b44185e097] - Node 13175fec-5e72-422f-96ed-a3b44185e097[13175fec-5e72-422f-96ed-a3b44185e097] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:50:30.142 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:30.142 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:30.142 - [orders(100)][13175fec-5e72-422f-96ed-a3b44185e097] - Node 13175fec-5e72-422f-96ed-a3b44185e097[13175fec-5e72-422f-96ed-a3b44185e097] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:30.142 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:30.142 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:30.152 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:50:30.152 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@75ad32fb error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@75ad32fb error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@75ad32fb error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 17:50:30.253 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:30.253 - [orders(100)][d1daabc7-2b75-42e5-94de-bd0b577a9763] - Node d1daabc7-2b75-42e5-94de-bd0b577a9763[d1daabc7-2b75-42e5-94de-bd0b577a9763] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:50:30.253 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:30.254 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:30.254 - [orders(100)][d1daabc7-2b75-42e5-94de-bd0b577a9763] - Node d1daabc7-2b75-42e5-94de-bd0b577a9763[d1daabc7-2b75-42e5-94de-bd0b577a9763] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:30.254 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:30.259 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:50:30.322 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1f05aae6 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1f05aae6 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1f05aae6 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 17:50:30.328 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:50:30.328 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:50:30.336 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:30.336 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:30.336 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:50:30.336 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:50:30.491 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 11 ms 
[WARN ] 2024-03-28 17:50:30.491 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:50:30.503 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:50:30.503 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:30.503 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:30.503 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:50:30.504 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:50:30.505 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 10 ms 
[INFO ] 2024-03-28 17:50:32.675 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:50:32.675 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:50:32.675 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:50:32.675 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 1 ms 
[INFO ] 2024-03-28 17:50:32.677 - [orders(100)][13175fec-5e72-422f-96ed-a3b44185e097] - Node 13175fec-5e72-422f-96ed-a3b44185e097[13175fec-5e72-422f-96ed-a3b44185e097] running status set to false 
[INFO ] 2024-03-28 17:50:32.677 - [orders(100)][13175fec-5e72-422f-96ed-a3b44185e097] - Node 13175fec-5e72-422f-96ed-a3b44185e097[13175fec-5e72-422f-96ed-a3b44185e097] schema data cleaned 
[INFO ] 2024-03-28 17:50:32.677 - [orders(100)][13175fec-5e72-422f-96ed-a3b44185e097] - Node 13175fec-5e72-422f-96ed-a3b44185e097[13175fec-5e72-422f-96ed-a3b44185e097] monitor closed 
[INFO ] 2024-03-28 17:50:32.678 - [orders(100)][13175fec-5e72-422f-96ed-a3b44185e097] - Node 13175fec-5e72-422f-96ed-a3b44185e097[13175fec-5e72-422f-96ed-a3b44185e097] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:32.678 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-13175fec-5e72-422f-96ed-a3b44185e097 complete, cost 2562ms 
[INFO ] 2024-03-28 17:50:32.738 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:32.738 - [orders(100)][0a55c38f-1f37-4921-a758-3cb0c0459603] - Node 0a55c38f-1f37-4921-a758-3cb0c0459603[0a55c38f-1f37-4921-a758-3cb0c0459603] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:50:32.738 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:50:32.738 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:32.738 - [orders(100)][0a55c38f-1f37-4921-a758-3cb0c0459603] - Node 0a55c38f-1f37-4921-a758-3cb0c0459603[0a55c38f-1f37-4921-a758-3cb0c0459603] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:32.738 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:50:32.749 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:50:32.788 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@762d8165 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@762d8165 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@762d8165 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 17:50:32.788 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:50:32.788 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:50:32.788 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:50:32.788 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:32.790 - [orders(100)][d1daabc7-2b75-42e5-94de-bd0b577a9763] - Node d1daabc7-2b75-42e5-94de-bd0b577a9763[d1daabc7-2b75-42e5-94de-bd0b577a9763] running status set to false 
[INFO ] 2024-03-28 17:50:32.790 - [orders(100)][d1daabc7-2b75-42e5-94de-bd0b577a9763] - Node d1daabc7-2b75-42e5-94de-bd0b577a9763[d1daabc7-2b75-42e5-94de-bd0b577a9763] schema data cleaned 
[INFO ] 2024-03-28 17:50:32.791 - [orders(100)][d1daabc7-2b75-42e5-94de-bd0b577a9763] - Node d1daabc7-2b75-42e5-94de-bd0b577a9763[d1daabc7-2b75-42e5-94de-bd0b577a9763] monitor closed 
[INFO ] 2024-03-28 17:50:32.791 - [orders(100)][d1daabc7-2b75-42e5-94de-bd0b577a9763] - Node d1daabc7-2b75-42e5-94de-bd0b577a9763[d1daabc7-2b75-42e5-94de-bd0b577a9763] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:50:32.791 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-d1daabc7-2b75-42e5-94de-bd0b577a9763 complete, cost 2562ms 
[WARN ] 2024-03-28 17:50:32.940 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:50:32.940 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:50:32.955 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:32.956 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:50:32.956 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:50:32.957 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:50:32.957 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 18 ms 
[INFO ] 2024-03-28 17:50:35.298 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:50:35.298 - [orders(100)][0a55c38f-1f37-4921-a758-3cb0c0459603] - Node 0a55c38f-1f37-4921-a758-3cb0c0459603[0a55c38f-1f37-4921-a758-3cb0c0459603] running status set to false 
[INFO ] 2024-03-28 17:50:35.298 - [orders(100)][0a55c38f-1f37-4921-a758-3cb0c0459603] - Node 0a55c38f-1f37-4921-a758-3cb0c0459603[0a55c38f-1f37-4921-a758-3cb0c0459603] schema data cleaned 
[INFO ] 2024-03-28 17:50:35.298 - [orders(100)][0a55c38f-1f37-4921-a758-3cb0c0459603] - Node 0a55c38f-1f37-4921-a758-3cb0c0459603[0a55c38f-1f37-4921-a758-3cb0c0459603] monitor closed 
[INFO ] 2024-03-28 17:50:35.298 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:50:35.298 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:50:35.298 - [orders(100)][0a55c38f-1f37-4921-a758-3cb0c0459603] - Node 0a55c38f-1f37-4921-a758-3cb0c0459603[0a55c38f-1f37-4921-a758-3cb0c0459603] close complete, cost 3 ms 
[INFO ] 2024-03-28 17:50:35.298 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 5 ms 
[INFO ] 2024-03-28 17:50:35.299 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-0a55c38f-1f37-4921-a758-3cb0c0459603 complete, cost 2589ms 
[INFO ] 2024-03-28 17:51:05.359 - [orders(100)][037ba6e1-a1f0-4e21-b136-bce5543f01af] - Node 037ba6e1-a1f0-4e21-b136-bce5543f01af[037ba6e1-a1f0-4e21-b136-bce5543f01af] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:51:05.359 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:05.366 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:05.366 - [orders(100)][037ba6e1-a1f0-4e21-b136-bce5543f01af] - Node 037ba6e1-a1f0-4e21-b136-bce5543f01af[037ba6e1-a1f0-4e21-b136-bce5543f01af] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:05.366 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:05.367 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:05.394 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:51:05.395 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@49100c26 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@49100c26 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@49100c26 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 17:51:05.599 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:51:05.620 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:51:05.620 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:05.620 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:05.624 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:51:05.624 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:51:05.624 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 17 ms 
[INFO ] 2024-03-28 17:51:07.944 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:51:07.944 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:51:07.944 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:51:07.944 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 2 ms 
[INFO ] 2024-03-28 17:51:07.947 - [orders(100)][037ba6e1-a1f0-4e21-b136-bce5543f01af] - Node 037ba6e1-a1f0-4e21-b136-bce5543f01af[037ba6e1-a1f0-4e21-b136-bce5543f01af] running status set to false 
[INFO ] 2024-03-28 17:51:07.948 - [orders(100)][037ba6e1-a1f0-4e21-b136-bce5543f01af] - Node 037ba6e1-a1f0-4e21-b136-bce5543f01af[037ba6e1-a1f0-4e21-b136-bce5543f01af] schema data cleaned 
[INFO ] 2024-03-28 17:51:07.948 - [orders(100)][037ba6e1-a1f0-4e21-b136-bce5543f01af] - Node 037ba6e1-a1f0-4e21-b136-bce5543f01af[037ba6e1-a1f0-4e21-b136-bce5543f01af] monitor closed 
[INFO ] 2024-03-28 17:51:07.948 - [orders(100)][037ba6e1-a1f0-4e21-b136-bce5543f01af] - Node 037ba6e1-a1f0-4e21-b136-bce5543f01af[037ba6e1-a1f0-4e21-b136-bce5543f01af] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:51:08.149 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-037ba6e1-a1f0-4e21-b136-bce5543f01af complete, cost 2643ms 
[INFO ] 2024-03-28 17:51:09.963 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:09.963 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:09.963 - [orders(100)][012160fe-da0f-4a3d-969a-48f58cd62645] - Node 012160fe-da0f-4a3d-969a-48f58cd62645[012160fe-da0f-4a3d-969a-48f58cd62645] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:51:09.964 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:09.964 - [orders(100)][012160fe-da0f-4a3d-969a-48f58cd62645] - Node 012160fe-da0f-4a3d-969a-48f58cd62645[012160fe-da0f-4a3d-969a-48f58cd62645] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:09.964 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:10.031 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:51:10.199 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@b0826a5 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@b0826a5 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@b0826a5 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 17:51:10.205 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:51:10.205 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:51:10.214 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:10.214 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:10.214 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:51:10.214 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:51:10.214 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 11 ms 
[INFO ] 2024-03-28 17:51:12.585 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:51:12.589 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:51:12.589 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:51:12.589 - [orders(100)][012160fe-da0f-4a3d-969a-48f58cd62645] - Node 012160fe-da0f-4a3d-969a-48f58cd62645[012160fe-da0f-4a3d-969a-48f58cd62645] running status set to false 
[INFO ] 2024-03-28 17:51:12.589 - [orders(100)][012160fe-da0f-4a3d-969a-48f58cd62645] - Node 012160fe-da0f-4a3d-969a-48f58cd62645[012160fe-da0f-4a3d-969a-48f58cd62645] schema data cleaned 
[INFO ] 2024-03-28 17:51:12.589 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 5 ms 
[INFO ] 2024-03-28 17:51:12.589 - [orders(100)][012160fe-da0f-4a3d-969a-48f58cd62645] - Node 012160fe-da0f-4a3d-969a-48f58cd62645[012160fe-da0f-4a3d-969a-48f58cd62645] monitor closed 
[INFO ] 2024-03-28 17:51:12.589 - [orders(100)][012160fe-da0f-4a3d-969a-48f58cd62645] - Node 012160fe-da0f-4a3d-969a-48f58cd62645[012160fe-da0f-4a3d-969a-48f58cd62645] close complete, cost 1 ms 
[INFO ] 2024-03-28 17:51:12.790 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-012160fe-da0f-4a3d-969a-48f58cd62645 complete, cost 2684ms 
[INFO ] 2024-03-28 17:51:15.505 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:15.505 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:15.505 - [orders(100)][30952c6c-c6b3-4f9f-9713-0681c5fd9bc8] - Node 30952c6c-c6b3-4f9f-9713-0681c5fd9bc8[30952c6c-c6b3-4f9f-9713-0681c5fd9bc8] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:51:15.505 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:15.505 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:15.505 - [orders(100)][30952c6c-c6b3-4f9f-9713-0681c5fd9bc8] - Node 30952c6c-c6b3-4f9f-9713-0681c5fd9bc8[30952c6c-c6b3-4f9f-9713-0681c5fd9bc8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:15.529 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:51:15.529 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1f10af7c error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1f10af7c error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1f10af7c error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 17:51:15.618 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:15.618 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:15.619 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:15.619 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:15.619 - [orders(100)][2061c0b5-6247-4848-8d4d-1f6eaeebc4a1] - Node 2061c0b5-6247-4848-8d4d-1f6eaeebc4a1[2061c0b5-6247-4848-8d4d-1f6eaeebc4a1] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:51:15.625 - [orders(100)][2061c0b5-6247-4848-8d4d-1f6eaeebc4a1] - Node 2061c0b5-6247-4848-8d4d-1f6eaeebc4a1[2061c0b5-6247-4848-8d4d-1f6eaeebc4a1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:15.625 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:51:15.690 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@176e1d65 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@176e1d65 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@176e1d65 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 17:51:15.693 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:51:15.704 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:51:15.705 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:15.705 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:15.705 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:51:15.706 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:51:15.706 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 12 ms 
[WARN ] 2024-03-28 17:51:15.867 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:51:15.867 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:51:15.875 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:15.875 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:15.875 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:51:15.875 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:51:15.875 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 9 ms 
[INFO ] 2024-03-28 17:51:18.052 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:51:18.053 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:51:18.053 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:51:18.056 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 2 ms 
[INFO ] 2024-03-28 17:51:18.056 - [orders(100)][30952c6c-c6b3-4f9f-9713-0681c5fd9bc8] - Node 30952c6c-c6b3-4f9f-9713-0681c5fd9bc8[30952c6c-c6b3-4f9f-9713-0681c5fd9bc8] running status set to false 
[INFO ] 2024-03-28 17:51:18.056 - [orders(100)][30952c6c-c6b3-4f9f-9713-0681c5fd9bc8] - Node 30952c6c-c6b3-4f9f-9713-0681c5fd9bc8[30952c6c-c6b3-4f9f-9713-0681c5fd9bc8] schema data cleaned 
[INFO ] 2024-03-28 17:51:18.056 - [orders(100)][30952c6c-c6b3-4f9f-9713-0681c5fd9bc8] - Node 30952c6c-c6b3-4f9f-9713-0681c5fd9bc8[30952c6c-c6b3-4f9f-9713-0681c5fd9bc8] monitor closed 
[INFO ] 2024-03-28 17:51:18.060 - [orders(100)][30952c6c-c6b3-4f9f-9713-0681c5fd9bc8] - Node 30952c6c-c6b3-4f9f-9713-0681c5fd9bc8[30952c6c-c6b3-4f9f-9713-0681c5fd9bc8] close complete, cost 1 ms 
[INFO ] 2024-03-28 17:51:18.060 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-30952c6c-c6b3-4f9f-9713-0681c5fd9bc8 complete, cost 2586ms 
[INFO ] 2024-03-28 17:51:18.154 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:51:18.154 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:51:18.154 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:51:18.159 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:51:18.159 - [orders(100)][2061c0b5-6247-4848-8d4d-1f6eaeebc4a1] - Node 2061c0b5-6247-4848-8d4d-1f6eaeebc4a1[2061c0b5-6247-4848-8d4d-1f6eaeebc4a1] running status set to false 
[INFO ] 2024-03-28 17:51:18.159 - [orders(100)][2061c0b5-6247-4848-8d4d-1f6eaeebc4a1] - Node 2061c0b5-6247-4848-8d4d-1f6eaeebc4a1[2061c0b5-6247-4848-8d4d-1f6eaeebc4a1] schema data cleaned 
[INFO ] 2024-03-28 17:51:18.159 - [orders(100)][2061c0b5-6247-4848-8d4d-1f6eaeebc4a1] - Node 2061c0b5-6247-4848-8d4d-1f6eaeebc4a1[2061c0b5-6247-4848-8d4d-1f6eaeebc4a1] monitor closed 
[INFO ] 2024-03-28 17:51:18.160 - [orders(100)][2061c0b5-6247-4848-8d4d-1f6eaeebc4a1] - Node 2061c0b5-6247-4848-8d4d-1f6eaeebc4a1[2061c0b5-6247-4848-8d4d-1f6eaeebc4a1] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:51:18.160 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-2061c0b5-6247-4848-8d4d-1f6eaeebc4a1 complete, cost 2572ms 
[INFO ] 2024-03-28 17:51:20.693 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:20.693 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:20.693 - [orders(100)][c2408db2-03cc-4d81-b4e3-d64a8a1dd75e] - Node c2408db2-03cc-4d81-b4e3-d64a8a1dd75e[c2408db2-03cc-4d81-b4e3-d64a8a1dd75e] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:51:20.693 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:20.693 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:20.694 - [orders(100)][c2408db2-03cc-4d81-b4e3-d64a8a1dd75e] - Node c2408db2-03cc-4d81-b4e3-d64a8a1dd75e[c2408db2-03cc-4d81-b4e3-d64a8a1dd75e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:20.724 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:51:20.724 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@36c7b5c9 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@36c7b5c9 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@36c7b5c9 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 17:51:20.890 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:51:20.890 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:51:20.899 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:20.899 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:20.899 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:51:20.899 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:51:20.899 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 10 ms 
[INFO ] 2024-03-28 17:51:23.257 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:51:23.257 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:51:23.257 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:51:23.258 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 2 ms 
[INFO ] 2024-03-28 17:51:23.260 - [orders(100)][c2408db2-03cc-4d81-b4e3-d64a8a1dd75e] - Node c2408db2-03cc-4d81-b4e3-d64a8a1dd75e[c2408db2-03cc-4d81-b4e3-d64a8a1dd75e] running status set to false 
[INFO ] 2024-03-28 17:51:23.260 - [orders(100)][c2408db2-03cc-4d81-b4e3-d64a8a1dd75e] - Node c2408db2-03cc-4d81-b4e3-d64a8a1dd75e[c2408db2-03cc-4d81-b4e3-d64a8a1dd75e] schema data cleaned 
[INFO ] 2024-03-28 17:51:23.260 - [orders(100)][c2408db2-03cc-4d81-b4e3-d64a8a1dd75e] - Node c2408db2-03cc-4d81-b4e3-d64a8a1dd75e[c2408db2-03cc-4d81-b4e3-d64a8a1dd75e] monitor closed 
[INFO ] 2024-03-28 17:51:23.262 - [orders(100)][c2408db2-03cc-4d81-b4e3-d64a8a1dd75e] - Node c2408db2-03cc-4d81-b4e3-d64a8a1dd75e[c2408db2-03cc-4d81-b4e3-d64a8a1dd75e] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:51:23.262 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-c2408db2-03cc-4d81-b4e3-d64a8a1dd75e complete, cost 2625ms 
[INFO ] 2024-03-28 17:51:26.470 - [orders(100)][781e80c3-f934-4c8c-a08d-208a136e7b57] - Node 781e80c3-f934-4c8c-a08d-208a136e7b57[781e80c3-f934-4c8c-a08d-208a136e7b57] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:51:26.471 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:26.471 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:26.471 - [orders(100)][781e80c3-f934-4c8c-a08d-208a136e7b57] - Node 781e80c3-f934-4c8c-a08d-208a136e7b57[781e80c3-f934-4c8c-a08d-208a136e7b57] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:26.471 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:26.471 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:26.484 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:51:26.486 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1ab57a3f error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1ab57a3f error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1ab57a3f error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 17:51:26.683 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:51:26.684 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:51:26.697 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:26.697 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:26.697 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:51:26.698 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:51:26.904 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 17 ms 
[INFO ] 2024-03-28 17:51:29.032 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:51:29.033 - [orders(100)][781e80c3-f934-4c8c-a08d-208a136e7b57] - Node 781e80c3-f934-4c8c-a08d-208a136e7b57[781e80c3-f934-4c8c-a08d-208a136e7b57] running status set to false 
[INFO ] 2024-03-28 17:51:29.033 - [orders(100)][781e80c3-f934-4c8c-a08d-208a136e7b57] - Node 781e80c3-f934-4c8c-a08d-208a136e7b57[781e80c3-f934-4c8c-a08d-208a136e7b57] schema data cleaned 
[INFO ] 2024-03-28 17:51:29.033 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:51:29.033 - [orders(100)][781e80c3-f934-4c8c-a08d-208a136e7b57] - Node 781e80c3-f934-4c8c-a08d-208a136e7b57[781e80c3-f934-4c8c-a08d-208a136e7b57] monitor closed 
[INFO ] 2024-03-28 17:51:29.034 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:51:29.035 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 8 ms 
[INFO ] 2024-03-28 17:51:29.042 - [orders(100)][781e80c3-f934-4c8c-a08d-208a136e7b57] - Node 781e80c3-f934-4c8c-a08d-208a136e7b57[781e80c3-f934-4c8c-a08d-208a136e7b57] close complete, cost 6 ms 
[INFO ] 2024-03-28 17:51:29.042 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-781e80c3-f934-4c8c-a08d-208a136e7b57 complete, cost 2602ms 
[INFO ] 2024-03-28 17:51:29.182 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:29.182 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:29.182 - [orders(100)][21369e33-153a-40d8-8030-3e528339b96d] - Node 21369e33-153a-40d8-8030-3e528339b96d[21369e33-153a-40d8-8030-3e528339b96d] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:51:29.183 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:29.183 - [orders(100)][21369e33-153a-40d8-8030-3e528339b96d] - Node 21369e33-153a-40d8-8030-3e528339b96d[21369e33-153a-40d8-8030-3e528339b96d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:29.183 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:29.256 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:51:29.256 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4cb03697 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4cb03697 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4cb03697 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 17:51:29.425 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:51:29.425 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:51:29.437 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:29.437 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:29.437 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:51:29.438 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:51:29.438 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 14 ms 
[INFO ] 2024-03-28 17:51:31.799 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:51:31.802 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:51:31.802 - [orders(100)][21369e33-153a-40d8-8030-3e528339b96d] - Node 21369e33-153a-40d8-8030-3e528339b96d[21369e33-153a-40d8-8030-3e528339b96d] running status set to false 
[INFO ] 2024-03-28 17:51:31.802 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:51:31.802 - [orders(100)][21369e33-153a-40d8-8030-3e528339b96d] - Node 21369e33-153a-40d8-8030-3e528339b96d[21369e33-153a-40d8-8030-3e528339b96d] schema data cleaned 
[INFO ] 2024-03-28 17:51:31.802 - [orders(100)][21369e33-153a-40d8-8030-3e528339b96d] - Node 21369e33-153a-40d8-8030-3e528339b96d[21369e33-153a-40d8-8030-3e528339b96d] monitor closed 
[INFO ] 2024-03-28 17:51:31.802 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 3 ms 
[INFO ] 2024-03-28 17:51:31.803 - [orders(100)][21369e33-153a-40d8-8030-3e528339b96d] - Node 21369e33-153a-40d8-8030-3e528339b96d[21369e33-153a-40d8-8030-3e528339b96d] close complete, cost 1 ms 
[INFO ] 2024-03-28 17:51:31.943 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-21369e33-153a-40d8-8030-3e528339b96d complete, cost 2661ms 
[INFO ] 2024-03-28 17:51:31.943 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:31.943 - [orders(100)][0328a1d0-930b-4f56-a404-348830bfd10e] - Node 0328a1d0-930b-4f56-a404-348830bfd10e[0328a1d0-930b-4f56-a404-348830bfd10e] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:51:31.943 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:31.943 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 17:51:31.943 - [orders(100)][0328a1d0-930b-4f56-a404-348830bfd10e] - Node 0328a1d0-930b-4f56-a404-348830bfd10e[0328a1d0-930b-4f56-a404-348830bfd10e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:31.943 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:31.970 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:51:32.152 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1b3d528e error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1b3d528e error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1b3d528e error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 17:51:32.152 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:51:32.165 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:51:32.165 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:32.165 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:32.165 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:51:32.165 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:51:32.165 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 10 ms 
[INFO ] 2024-03-28 17:51:34.512 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:51:34.513 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:51:34.513 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:51:34.517 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 3 ms 
[INFO ] 2024-03-28 17:51:34.517 - [orders(100)][0328a1d0-930b-4f56-a404-348830bfd10e] - Node 0328a1d0-930b-4f56-a404-348830bfd10e[0328a1d0-930b-4f56-a404-348830bfd10e] running status set to false 
[INFO ] 2024-03-28 17:51:34.517 - [orders(100)][0328a1d0-930b-4f56-a404-348830bfd10e] - Node 0328a1d0-930b-4f56-a404-348830bfd10e[0328a1d0-930b-4f56-a404-348830bfd10e] schema data cleaned 
[INFO ] 2024-03-28 17:51:34.519 - [orders(100)][0328a1d0-930b-4f56-a404-348830bfd10e] - Node 0328a1d0-930b-4f56-a404-348830bfd10e[0328a1d0-930b-4f56-a404-348830bfd10e] monitor closed 
[INFO ] 2024-03-28 17:51:34.520 - [orders(100)][0328a1d0-930b-4f56-a404-348830bfd10e] - Node 0328a1d0-930b-4f56-a404-348830bfd10e[0328a1d0-930b-4f56-a404-348830bfd10e] close complete, cost 2 ms 
[INFO ] 2024-03-28 17:51:34.724 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-0328a1d0-930b-4f56-a404-348830bfd10e complete, cost 2614ms 
[INFO ] 2024-03-28 17:51:42.220 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:42.220 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:51:42.220 - [orders(100)][73af3fa6-8f16-48bf-ae29-7f52f640e73b] - Node 73af3fa6-8f16-48bf-ae29-7f52f640e73b[73af3fa6-8f16-48bf-ae29-7f52f640e73b] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:51:42.220 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:42.220 - [orders(100)][73af3fa6-8f16-48bf-ae29-7f52f640e73b] - Node 73af3fa6-8f16-48bf-ae29-7f52f640e73b[73af3fa6-8f16-48bf-ae29-7f52f640e73b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:42.220 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:51:42.241 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:51:42.245 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@75c5e05b error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@75c5e05b error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@75c5e05b error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 17:51:42.433 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:51:42.433 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:51:42.434 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:42.434 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:51:42.434 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:51:42.434 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:51:42.434 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 10 ms 
[INFO ] 2024-03-28 17:51:44.776 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:51:44.776 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:51:44.776 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:51:44.777 - [orders(100)][73af3fa6-8f16-48bf-ae29-7f52f640e73b] - Node 73af3fa6-8f16-48bf-ae29-7f52f640e73b[73af3fa6-8f16-48bf-ae29-7f52f640e73b] running status set to false 
[INFO ] 2024-03-28 17:51:44.777 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 4 ms 
[INFO ] 2024-03-28 17:51:44.778 - [orders(100)][73af3fa6-8f16-48bf-ae29-7f52f640e73b] - Node 73af3fa6-8f16-48bf-ae29-7f52f640e73b[73af3fa6-8f16-48bf-ae29-7f52f640e73b] schema data cleaned 
[INFO ] 2024-03-28 17:51:44.779 - [orders(100)][73af3fa6-8f16-48bf-ae29-7f52f640e73b] - Node 73af3fa6-8f16-48bf-ae29-7f52f640e73b[73af3fa6-8f16-48bf-ae29-7f52f640e73b] monitor closed 
[INFO ] 2024-03-28 17:51:44.779 - [orders(100)][73af3fa6-8f16-48bf-ae29-7f52f640e73b] - Node 73af3fa6-8f16-48bf-ae29-7f52f640e73b[73af3fa6-8f16-48bf-ae29-7f52f640e73b] close complete, cost 2 ms 
[INFO ] 2024-03-28 17:51:44.983 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-73af3fa6-8f16-48bf-ae29-7f52f640e73b complete, cost 2609ms 
[INFO ] 2024-03-28 17:53:07.868 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:53:07.868 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 17:53:07.869 - [orders(100)][40f0c125-cc77-45e4-abff-812ab2741a76] - Node 40f0c125-cc77-45e4-abff-812ab2741a76[40f0c125-cc77-45e4-abff-812ab2741a76] start preload schema,table counts: 0 
[INFO ] 2024-03-28 17:53:07.869 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:53:07.869 - [orders(100)][40f0c125-cc77-45e4-abff-812ab2741a76] - Node 40f0c125-cc77-45e4-abff-812ab2741a76[40f0c125-cc77-45e4-abff-812ab2741a76] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:53:07.869 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 17:53:07.907 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 17:53:07.908 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6f017e2e error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6f017e2e error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6f017e2e error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 17:53:08.087 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 17:53:08.087 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] running status set to false 
[INFO ] 2024-03-28 17:53:08.095 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:53:08.096 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0b829b63-3394-4293-8b5d-c50fe6f3df7b 
[INFO ] 2024-03-28 17:53:08.096 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] schema data cleaned 
[INFO ] 2024-03-28 17:53:08.096 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] monitor closed 
[INFO ] 2024-03-28 17:53:08.296 - [orders(100)][Order Details] - Node Order Details[0b829b63-3394-4293-8b5d-c50fe6f3df7b] close complete, cost 11 ms 
[INFO ] 2024-03-28 17:53:10.432 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] running status set to false 
[INFO ] 2024-03-28 17:53:10.432 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] schema data cleaned 
[INFO ] 2024-03-28 17:53:10.432 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] monitor closed 
[INFO ] 2024-03-28 17:53:10.432 - [orders(100)][Order Details] - Node Order Details[3aff2b9d-82ff-4342-8749-f2cd36d4acda] close complete, cost 1 ms 
[INFO ] 2024-03-28 17:53:10.434 - [orders(100)][40f0c125-cc77-45e4-abff-812ab2741a76] - Node 40f0c125-cc77-45e4-abff-812ab2741a76[40f0c125-cc77-45e4-abff-812ab2741a76] running status set to false 
[INFO ] 2024-03-28 17:53:10.435 - [orders(100)][40f0c125-cc77-45e4-abff-812ab2741a76] - Node 40f0c125-cc77-45e4-abff-812ab2741a76[40f0c125-cc77-45e4-abff-812ab2741a76] schema data cleaned 
[INFO ] 2024-03-28 17:53:10.435 - [orders(100)][40f0c125-cc77-45e4-abff-812ab2741a76] - Node 40f0c125-cc77-45e4-abff-812ab2741a76[40f0c125-cc77-45e4-abff-812ab2741a76] monitor closed 
[INFO ] 2024-03-28 17:53:10.435 - [orders(100)][40f0c125-cc77-45e4-abff-812ab2741a76] - Node 40f0c125-cc77-45e4-abff-812ab2741a76[40f0c125-cc77-45e4-abff-812ab2741a76] close complete, cost 0 ms 
[INFO ] 2024-03-28 17:53:10.436 - [orders(100)] - load tapTable task 66053d504006d41d02df6b1f-40f0c125-cc77-45e4-abff-812ab2741a76 complete, cost 2621ms 
