[INFO ] 2024-08-22 18:28:05.710 - [任务 11] - Start task milestones: 66c712a217ade8409258c192(任务 11) 
[INFO ] 2024-08-22 18:28:05.711 - [任务 11] - Task initialization... 
[INFO ] 2024-08-22 18:28:05.744 - [任务 11] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-08-22 18:28:05.965 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-22 18:28:05.974 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] start preload schema,table counts: 37 
[INFO ] 2024-08-22 18:28:05.974 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] start preload schema,table counts: 37 
[INFO ] 2024-08-22 18:28:06.510 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] preload schema finished, cost 524 ms 
[INFO ] 2024-08-22 18:28:06.511 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] preload schema finished, cost 524 ms 
[INFO ] 2024-08-22 18:28:07.250 - [任务 11][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-22 18:28:07.250 - [任务 11][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-22 18:28:07.462 - [任务 11][DB2_132] - Source node "DB2_132" read batch size: 100 
[INFO ] 2024-08-22 18:28:07.463 - [任务 11][DB2_132] - Source node "DB2_132" event queue capacity: 200 
[INFO ] 2024-08-22 18:28:07.463 - [任务 11][DB2_132] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-22 18:28:07.556 - [任务 11][DB2_132] - Table [TEST_DB2_KAFKA, dafasdf0906, Testddlora222, TestCarClaim09062, testoracle, TESTDATE1, ORDERS, TestTimestamp, TestCarClaim0906, HstestMultiKeys_bak, TestColumn, db2target1, Testdbdbdb, Testddlora111, TestIkas1, testCustom11, TestClob] not open CDC 
[INFO ] 2024-08-22 18:28:07.558 - [任务 11][DB2_132] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724322487554} 
[INFO ] 2024-08-22 18:28:07.558 - [任务 11][DB2_132] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-22 18:28:07.723 - [任务 11][DB2_132] - Initial sync started 
[INFO ] 2024-08-22 18:28:07.735 - [任务 11][DB2_132] - Starting batch read, table name: TestCarClaim, offset: null 
[INFO ] 2024-08-22 18:28:07.745 - [任务 11][DB2_132] - Table TestCarClaim is going to be initial synced 
[INFO ] 2024-08-22 18:28:07.948 - [任务 11][DB2_132] - Table [TestCarClaim] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:28:07.985 - [任务 11][DB2_132] - Query table 'TestCarClaim' counts: 19 
[INFO ] 2024-08-22 18:28:07.985 - [任务 11][DB2_132] - Starting batch read, table name: TEST_DB2_KAFKA, offset: null 
[INFO ] 2024-08-22 18:28:08.004 - [任务 11][DB2_132] - Table TEST_DB2_KAFKA is going to be initial synced 
[INFO ] 2024-08-22 18:28:08.004 - [任务 11][DB2_132] - Table [TEST_DB2_KAFKA] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:28:08.004 - [任务 11][DB2_132] - Query table 'TEST_DB2_KAFKA' counts: 3 
[INFO ] 2024-08-22 18:28:08.005 - [任务 11][DB2_132] - Starting batch read, table name: test111, offset: null 
[INFO ] 2024-08-22 18:28:08.005 - [任务 11][DB2_132] - Table test111 is going to be initial synced 
[INFO ] 2024-08-22 18:28:08.025 - [任务 11][DB2_132] - Query table 'test111' counts: 3 
[INFO ] 2024-08-22 18:28:08.025 - [任务 11][DB2_132] - Table [test111] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:28:08.025 - [任务 11][DB2_132] - Starting batch read, table name: dafasdf0906, offset: null 
[INFO ] 2024-08-22 18:28:08.026 - [任务 11][DB2_132] - Table dafasdf0906 is going to be initial synced 
[INFO ] 2024-08-22 18:28:08.047 - [任务 11][DB2_132] - Query table 'dafasdf0906' counts: 23 
[INFO ] 2024-08-22 18:28:08.050 - [任务 11][DB2_132] - Table [dafasdf0906] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:28:08.050 - [任务 11][DB2_132] - Starting batch read, table name: Testddlora222, offset: null 
[INFO ] 2024-08-22 18:28:08.050 - [任务 11][DB2_132] - Table Testddlora222 is going to be initial synced 
[INFO ] 2024-08-22 18:28:08.067 - [任务 11][DB2_132] - Table [Testddlora222] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:28:08.069 - [任务 11][DB2_132] - Query table 'Testddlora222' counts: 1 
[INFO ] 2024-08-22 18:28:08.069 - [任务 11][DB2_132] - Starting batch read, table name: TestCarClaim09062, offset: null 
[INFO ] 2024-08-22 18:28:08.069 - [任务 11][DB2_132] - Table TestCarClaim09062 is going to be initial synced 
[INFO ] 2024-08-22 18:28:08.085 - [任务 11][DB2_132] - Table [TestCarClaim09062] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:28:08.087 - [任务 11][DB2_132] - Query table 'TestCarClaim09062' counts: 24 
[INFO ] 2024-08-22 18:28:08.087 - [任务 11][DB2_132] - Starting batch read, table name: TestDb2, offset: null 
[INFO ] 2024-08-22 18:28:08.087 - [任务 11][DB2_132] - Table TestDb2 is going to be initial synced 
[INFO ] 2024-08-22 18:28:08.148 - [任务 11][DB2_132] - Table [TestDb2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:28:08.149 - [任务 11][DB2_132] - Query table 'TestDb2' counts: 20 
[INFO ] 2024-08-22 18:28:08.149 - [任务 11][DB2_132] - Starting batch read, table name: testoracle, offset: null 
[INFO ] 2024-08-22 18:28:08.165 - [任务 11][DB2_132] - Table testoracle is going to be initial synced 
[INFO ] 2024-08-22 18:28:08.165 - [任务 11][DB2_132] - Table [testoracle] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:28:08.172 - [任务 11][DB2_132] - Query table 'testoracle' counts: 12 
[INFO ] 2024-08-22 18:28:08.172 - [任务 11][DB2_132] - Starting batch read, table name: TESTDATE1, offset: null 
[INFO ] 2024-08-22 18:28:08.186 - [任务 11][DB2_132] - Table TESTDATE1 is going to be initial synced 
[INFO ] 2024-08-22 18:28:08.186 - [任务 11][DB2_132] - Table [TESTDATE1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:28:08.186 - [任务 11][DB2_132] - Query table 'TESTDATE1' counts: 5 
[INFO ] 2024-08-22 18:28:08.186 - [任务 11][DB2_132] - Starting batch read, table name: testTimestamp, offset: null 
[INFO ] 2024-08-22 18:28:08.186 - [任务 11][DB2_132] - Table testTimestamp is going to be initial synced 
[INFO ] 2024-08-22 18:28:08.230 - [任务 11][DB2_132] - Table [testTimestamp] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:28:08.230 - [任务 11][DB2_132] - Query table 'testTimestamp' counts: 7 
[INFO ] 2024-08-22 18:28:08.230 - [任务 11][DB2_132] - Starting batch read, table name: TESTDATE, offset: null 
[INFO ] 2024-08-22 18:28:08.230 - [任务 11][DB2_132] - Table TESTDATE is going to be initial synced 
[INFO ] 2024-08-22 18:28:08.248 - [任务 11][DB2_132] - Query table 'TESTDATE' counts: 5 
[INFO ] 2024-08-22 18:28:08.248 - [任务 11][DB2_132] - Table [TESTDATE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:28:08.248 - [任务 11][DB2_132] - Starting batch read, table name: Test_ikas, offset: null 
[INFO ] 2024-08-22 18:28:08.248 - [任务 11][DB2_132] - Table Test_ikas is going to be initial synced 
[INFO ] 2024-08-22 18:28:08.454 - [任务 11][DB2_132] - Query table 'Test_ikas' counts: 50000 
[INFO ] 2024-08-22 18:28:09.061 - [任务 11][DB2_132] - Table [Test_ikas] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:28:09.062 - [任务 11][DB2_132] - Starting batch read, table name: test222, offset: null 
[INFO ] 2024-08-22 18:28:09.062 - [任务 11][DB2_132] - Table test222 is going to be initial synced 
[INFO ] 2024-08-22 18:28:09.076 - [任务 11][DB2_132] - Table [test222] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:28:09.089 - [任务 11][DB2_132] - Query table 'test222' counts: 1 
[INFO ] 2024-08-22 18:28:09.089 - [任务 11][DB2_132] - Starting batch read, table name: Dummy_TT, offset: null 
[INFO ] 2024-08-22 18:28:09.289 - [任务 11][DB2_132] - Table Dummy_TT is going to be initial synced 
[INFO ] 2024-08-22 18:29:25.874 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] running status set to false 
[INFO ] 2024-08-22 18:29:25.875 - [任务 11] - Stop task milestones: 66c712a217ade8409258c192(任务 11)  
[INFO ] 2024-08-22 18:29:25.889 - [任务 11][DB2_132] - Table [Dummy_TT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:29:25.931 - [任务 11][DB2_132] - Cancel query 'Dummy_TT' snapshot row size with task stopped. 
[INFO ] 2024-08-22 18:29:25.931 - [任务 11][DB2_132] - PDK connector node stopped: HazelcastSourcePdkDataNode-6aae8000-db9d-4b8b-9af3-cd9b576d67bf 
[INFO ] 2024-08-22 18:29:25.932 - [任务 11][DB2_132] - PDK connector node released: HazelcastSourcePdkDataNode-6aae8000-db9d-4b8b-9af3-cd9b576d67bf 
[INFO ] 2024-08-22 18:29:25.932 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] schema data cleaned 
[INFO ] 2024-08-22 18:29:25.932 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] monitor closed 
[INFO ] 2024-08-22 18:29:25.932 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] close complete, cost 63 ms 
[INFO ] 2024-08-22 18:29:25.942 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] running status set to false 
[INFO ] 2024-08-22 18:29:25.944 - [任务 11][TestDummy] - Stop connector 
[INFO ] 2024-08-22 18:29:25.948 - [任务 11][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-bda356cd-7833-4647-abb3-48361475d101 
[INFO ] 2024-08-22 18:29:25.948 - [任务 11][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-bda356cd-7833-4647-abb3-48361475d101 
[INFO ] 2024-08-22 18:29:25.948 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] schema data cleaned 
[INFO ] 2024-08-22 18:29:25.949 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] monitor closed 
[INFO ] 2024-08-22 18:29:25.949 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] close complete, cost 16 ms 
[INFO ] 2024-08-22 18:29:27.009 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-22 18:29:27.009 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-08-22 18:29:27.009 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-08-22 18:29:27.030 - [任务 11] - Remove memory task client succeed, task: 任务 11[66c712a217ade8409258c192] 
[INFO ] 2024-08-22 18:29:27.030 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[66c712a217ade8409258c192] 
[INFO ] 2024-08-22 18:30:59.366 - [任务 11] - Start task milestones: 66c712a217ade8409258c192(任务 11) 
[INFO ] 2024-08-22 18:30:59.369 - [任务 11] - Task initialization... 
[INFO ] 2024-08-22 18:30:59.426 - [任务 11] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-22 18:30:59.632 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-22 18:30:59.838 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] start preload schema,table counts: 37 
[INFO ] 2024-08-22 18:30:59.839 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] start preload schema,table counts: 37 
[INFO ] 2024-08-22 18:31:00.349 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] preload schema finished, cost 679 ms 
[INFO ] 2024-08-22 18:31:00.550 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] preload schema finished, cost 770 ms 
[INFO ] 2024-08-22 18:31:01.202 - [任务 11][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-22 18:31:01.214 - [任务 11][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-22 18:31:01.446 - [任务 11][DB2_132] - Source node "DB2_132" read batch size: 100 
[INFO ] 2024-08-22 18:31:01.447 - [任务 11][DB2_132] - Source node "DB2_132" event queue capacity: 200 
[INFO ] 2024-08-22 18:31:01.448 - [任务 11][DB2_132] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-22 18:31:01.766 - [任务 11][DB2_132] - Table [TEST_DB2_KAFKA, dafasdf0906, Testddlora222, TestCarClaim09062, testoracle, TESTDATE1, ORDERS, TestTimestamp, TestCarClaim0906, HstestMultiKeys_bak, TestColumn, db2target1, Testdbdbdb, Testddlora111, TestIkas1, testCustom11, TestClob] not open CDC 
[INFO ] 2024-08-22 18:31:01.766 - [任务 11][DB2_132] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724322661764} 
[INFO ] 2024-08-22 18:31:01.767 - [任务 11][DB2_132] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-22 18:31:01.916 - [任务 11][DB2_132] - Initial sync started 
[INFO ] 2024-08-22 18:31:01.936 - [任务 11][DB2_132] - Starting batch read, table name: TestCarClaim, offset: null 
[INFO ] 2024-08-22 18:31:01.936 - [任务 11][DB2_132] - Table TestCarClaim is going to be initial synced 
[INFO ] 2024-08-22 18:31:02.074 - [任务 11][DB2_132] - Table [TestCarClaim] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:31:02.075 - [任务 11][DB2_132] - Query table 'TestCarClaim' counts: 19 
[INFO ] 2024-08-22 18:31:02.075 - [任务 11][DB2_132] - Starting batch read, table name: TEST_DB2_KAFKA, offset: null 
[INFO ] 2024-08-22 18:31:02.119 - [任务 11][DB2_132] - Table TEST_DB2_KAFKA is going to be initial synced 
[INFO ] 2024-08-22 18:31:02.119 - [任务 11][DB2_132] - Table [TEST_DB2_KAFKA] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:31:02.119 - [任务 11][DB2_132] - Query table 'TEST_DB2_KAFKA' counts: 3 
[INFO ] 2024-08-22 18:31:02.119 - [任务 11][DB2_132] - Starting batch read, table name: test111, offset: null 
[INFO ] 2024-08-22 18:31:02.136 - [任务 11][DB2_132] - Table test111 is going to be initial synced 
[INFO ] 2024-08-22 18:31:02.136 - [任务 11][DB2_132] - Table [test111] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:31:02.137 - [任务 11][DB2_132] - Query table 'test111' counts: 3 
[INFO ] 2024-08-22 18:31:02.137 - [任务 11][DB2_132] - Starting batch read, table name: dafasdf0906, offset: null 
[INFO ] 2024-08-22 18:31:02.168 - [任务 11][DB2_132] - Table dafasdf0906 is going to be initial synced 
[INFO ] 2024-08-22 18:31:02.169 - [任务 11][DB2_132] - Table [dafasdf0906] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:31:02.169 - [任务 11][DB2_132] - Query table 'dafasdf0906' counts: 23 
[INFO ] 2024-08-22 18:31:02.169 - [任务 11][DB2_132] - Starting batch read, table name: Testddlora222, offset: null 
[INFO ] 2024-08-22 18:31:02.169 - [任务 11][DB2_132] - Table Testddlora222 is going to be initial synced 
[INFO ] 2024-08-22 18:31:02.233 - [任务 11][DB2_132] - Query table 'Testddlora222' counts: 1 
[INFO ] 2024-08-22 18:31:02.233 - [任务 11][DB2_132] - Table [Testddlora222] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:31:02.233 - [任务 11][DB2_132] - Starting batch read, table name: TestCarClaim09062, offset: null 
[INFO ] 2024-08-22 18:31:02.260 - [任务 11][DB2_132] - Table TestCarClaim09062 is going to be initial synced 
[INFO ] 2024-08-22 18:31:02.261 - [任务 11][DB2_132] - Table [TestCarClaim09062] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:31:02.261 - [任务 11][DB2_132] - Query table 'TestCarClaim09062' counts: 24 
[INFO ] 2024-08-22 18:31:02.262 - [任务 11][DB2_132] - Starting batch read, table name: TestDb2, offset: null 
[INFO ] 2024-08-22 18:31:02.262 - [任务 11][DB2_132] - Table TestDb2 is going to be initial synced 
[INFO ] 2024-08-22 18:31:02.309 - [任务 11][DB2_132] - Table [TestDb2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:31:02.309 - [任务 11][DB2_132] - Query table 'TestDb2' counts: 20 
[INFO ] 2024-08-22 18:31:02.310 - [任务 11][DB2_132] - Starting batch read, table name: testoracle, offset: null 
[INFO ] 2024-08-22 18:31:02.310 - [任务 11][DB2_132] - Table testoracle is going to be initial synced 
[INFO ] 2024-08-22 18:31:02.355 - [任务 11][DB2_132] - Table [testoracle] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:31:02.356 - [任务 11][DB2_132] - Query table 'testoracle' counts: 12 
[INFO ] 2024-08-22 18:31:08.000 - [任务 11][DB2_132] - Starting batch read, table name: TESTDATE1, offset: null 
[INFO ] 2024-08-22 18:31:08.007 - [任务 11][DB2_132] - Table TESTDATE1 is going to be initial synced 
[INFO ] 2024-08-22 18:31:08.166 - [任务 11][DB2_132] - Query table 'TESTDATE1' counts: 5 
[INFO ] 2024-08-22 18:31:08.169 - [任务 11][DB2_132] - Table [TESTDATE1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:31:08.169 - [任务 11][DB2_132] - Starting batch read, table name: testTimestamp, offset: null 
[INFO ] 2024-08-22 18:31:08.169 - [任务 11][DB2_132] - Table testTimestamp is going to be initial synced 
[INFO ] 2024-08-22 18:31:08.187 - [任务 11][DB2_132] - Table [testTimestamp] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:31:08.188 - [任务 11][DB2_132] - Query table 'testTimestamp' counts: 7 
[INFO ] 2024-08-22 18:31:08.188 - [任务 11][DB2_132] - Starting batch read, table name: TESTDATE, offset: null 
[INFO ] 2024-08-22 18:31:08.188 - [任务 11][DB2_132] - Table TESTDATE is going to be initial synced 
[INFO ] 2024-08-22 18:31:08.195 - [任务 11][DB2_132] - Query table 'TESTDATE' counts: 5 
[INFO ] 2024-08-22 18:31:08.197 - [任务 11][DB2_132] - Table [TESTDATE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:31:08.197 - [任务 11][DB2_132] - Starting batch read, table name: Test_ikas, offset: null 
[INFO ] 2024-08-22 18:31:08.225 - [任务 11][DB2_132] - Table Test_ikas is going to be initial synced 
[INFO ] 2024-08-22 18:31:08.226 - [任务 11][DB2_132] - Query table 'Test_ikas' counts: 50000 
[INFO ] 2024-08-22 18:35:39.480 - [任务 11] - Stop task milestones: 66c712a217ade8409258c192(任务 11)  
[INFO ] 2024-08-22 18:35:40.295 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] running status set to false 
[INFO ] 2024-08-22 18:35:40.295 - [任务 11][DB2_132] - Table [Test_ikas] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:35:40.304 - [任务 11][DB2_132] - PDK connector node stopped: HazelcastSourcePdkDataNode-6aae8000-db9d-4b8b-9af3-cd9b576d67bf 
[INFO ] 2024-08-22 18:35:40.308 - [任务 11][DB2_132] - PDK connector node released: HazelcastSourcePdkDataNode-6aae8000-db9d-4b8b-9af3-cd9b576d67bf 
[INFO ] 2024-08-22 18:35:40.308 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] schema data cleaned 
[INFO ] 2024-08-22 18:35:40.308 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] monitor closed 
[INFO ] 2024-08-22 18:35:40.308 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] close complete, cost 46 ms 
[INFO ] 2024-08-22 18:35:40.308 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] running status set to false 
[INFO ] 2024-08-22 18:35:40.317 - [任务 11][TestDummy] - Stop connector 
[INFO ] 2024-08-22 18:35:40.317 - [任务 11][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-bda356cd-7833-4647-abb3-48361475d101 
[INFO ] 2024-08-22 18:35:40.317 - [任务 11][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-bda356cd-7833-4647-abb3-48361475d101 
[INFO ] 2024-08-22 18:35:40.317 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] schema data cleaned 
[INFO ] 2024-08-22 18:35:40.317 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] monitor closed 
[INFO ] 2024-08-22 18:35:40.317 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] close complete, cost 11 ms 
[ERROR] 2024-08-22 18:35:40.521 - [任务 11][TestDummy] - Execute PDK method: TARGET_WRITE_RECORD, tableName: Test_ikas <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: Test_ikas

<-- Simple Stack Trace -->
Caused by: java.lang.InterruptedException: sleep interrupted
	java.lang.Thread.sleep(Native Method)
	io.tapdata.dummy.DummyConnector.supportWriteRecord(DummyConnector.java:254)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:828)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: Test_ikas
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:834)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$55(HazelcastTargetPdkDataNode.java:780)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:774)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$24(HazelcastTargetPdkDataNode.java:483)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:483)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:670)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:554)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:510)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:476)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:482)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:528)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at io.tapdata.dummy.DummyConnector.supportWriteRecord(DummyConnector.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:828)
	... 24 more

[INFO ] 2024-08-22 18:35:45.262 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-22 18:35:45.262 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-08-22 18:35:45.263 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-08-22 18:35:45.304 - [任务 11] - Remove memory task client succeed, task: 任务 11[66c712a217ade8409258c192] 
[INFO ] 2024-08-22 18:35:45.304 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[66c712a217ade8409258c192] 
[INFO ] 2024-08-22 18:42:32.459 - [任务 11] - Start task milestones: 66c712a217ade8409258c192(任务 11) 
[INFO ] 2024-08-22 18:42:32.459 - [任务 11] - Task initialization... 
[INFO ] 2024-08-22 18:42:32.558 - [任务 11] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-22 18:42:32.558 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-22 18:42:32.759 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] start preload schema,table counts: 37 
[INFO ] 2024-08-22 18:42:32.760 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] start preload schema,table counts: 37 
[INFO ] 2024-08-22 18:42:33.073 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] preload schema finished, cost 471 ms 
[INFO ] 2024-08-22 18:42:33.073 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] preload schema finished, cost 471 ms 
[INFO ] 2024-08-22 18:42:33.802 - [任务 11][DB2_132] - Source node "DB2_132" read batch size: 100 
[INFO ] 2024-08-22 18:42:33.802 - [任务 11][DB2_132] - Source node "DB2_132" event queue capacity: 200 
[INFO ] 2024-08-22 18:42:33.802 - [任务 11][DB2_132] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-22 18:42:33.878 - [任务 11][DB2_132] - Table [TEST_DB2_KAFKA, dafasdf0906, Testddlora222, TestCarClaim09062, testoracle, TESTDATE1, ORDERS, TestTimestamp, TestCarClaim0906, HstestMultiKeys_bak, TestColumn, db2target1, Testdbdbdb, Testddlora111, TestIkas1, testCustom11, TestClob] not open CDC 
[INFO ] 2024-08-22 18:42:33.879 - [任务 11][DB2_132] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724323353878} 
[INFO ] 2024-08-22 18:42:33.879 - [任务 11][DB2_132] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-22 18:42:33.913 - [任务 11][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-22 18:42:33.991 - [任务 11][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-22 18:42:33.991 - [任务 11][DB2_132] - Initial sync started 
[INFO ] 2024-08-22 18:42:33.991 - [任务 11][DB2_132] - Starting batch read, table name: TestCarClaim, offset: null 
[INFO ] 2024-08-22 18:42:34.178 - [任务 11][DB2_132] - Table TestCarClaim is going to be initial synced 
[INFO ] 2024-08-22 18:42:34.178 - [任务 11][DB2_132] - Table [TestCarClaim] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:42:34.181 - [任务 11][DB2_132] - Query table 'TestCarClaim' counts: 19 
[INFO ] 2024-08-22 18:42:34.182 - [任务 11][DB2_132] - Starting batch read, table name: TEST_DB2_KAFKA, offset: null 
[INFO ] 2024-08-22 18:42:34.214 - [任务 11][DB2_132] - Table TEST_DB2_KAFKA is going to be initial synced 
[INFO ] 2024-08-22 18:42:34.215 - [任务 11][DB2_132] - Table [TEST_DB2_KAFKA] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:42:34.215 - [任务 11][DB2_132] - Query table 'TEST_DB2_KAFKA' counts: 3 
[INFO ] 2024-08-22 18:42:34.215 - [任务 11][DB2_132] - Starting batch read, table name: test111, offset: null 
[INFO ] 2024-08-22 18:42:34.231 - [任务 11][DB2_132] - Table test111 is going to be initial synced 
[INFO ] 2024-08-22 18:42:34.231 - [任务 11][DB2_132] - Query table 'test111' counts: 3 
[INFO ] 2024-08-22 18:42:34.231 - [任务 11][DB2_132] - Table [test111] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:42:34.232 - [任务 11][DB2_132] - Starting batch read, table name: dafasdf0906, offset: null 
[INFO ] 2024-08-22 18:42:34.232 - [任务 11][DB2_132] - Table dafasdf0906 is going to be initial synced 
[INFO ] 2024-08-22 18:42:34.249 - [任务 11][DB2_132] - Table [dafasdf0906] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:42:34.249 - [任务 11][DB2_132] - Query table 'dafasdf0906' counts: 23 
[INFO ] 2024-08-22 18:42:34.250 - [任务 11][DB2_132] - Starting batch read, table name: Testddlora222, offset: null 
[INFO ] 2024-08-22 18:42:34.250 - [任务 11][DB2_132] - Table Testddlora222 is going to be initial synced 
[INFO ] 2024-08-22 18:42:34.258 - [任务 11][DB2_132] - Table [Testddlora222] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:42:34.258 - [任务 11][DB2_132] - Query table 'Testddlora222' counts: 1 
[INFO ] 2024-08-22 18:42:34.258 - [任务 11][DB2_132] - Starting batch read, table name: TestCarClaim09062, offset: null 
[INFO ] 2024-08-22 18:42:34.258 - [任务 11][DB2_132] - Table TestCarClaim09062 is going to be initial synced 
[INFO ] 2024-08-22 18:42:34.277 - [任务 11][DB2_132] - Table [TestCarClaim09062] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:42:34.277 - [任务 11][DB2_132] - Query table 'TestCarClaim09062' counts: 24 
[INFO ] 2024-08-22 18:42:34.277 - [任务 11][DB2_132] - Starting batch read, table name: TestDb2, offset: null 
[INFO ] 2024-08-22 18:42:34.278 - [任务 11][DB2_132] - Table TestDb2 is going to be initial synced 
[INFO ] 2024-08-22 18:42:34.297 - [任务 11][DB2_132] - Table [TestDb2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:42:34.299 - [任务 11][DB2_132] - Query table 'TestDb2' counts: 20 
[INFO ] 2024-08-22 18:42:34.299 - [任务 11][DB2_132] - Starting batch read, table name: testoracle, offset: null 
[INFO ] 2024-08-22 18:42:34.299 - [任务 11][DB2_132] - Table testoracle is going to be initial synced 
[INFO ] 2024-08-22 18:42:34.342 - [任务 11][DB2_132] - Table [testoracle] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:42:34.342 - [任务 11][DB2_132] - Query table 'testoracle' counts: 12 
[INFO ] 2024-08-22 18:42:34.343 - [任务 11][DB2_132] - Starting batch read, table name: TESTDATE1, offset: null 
[INFO ] 2024-08-22 18:42:34.343 - [任务 11][DB2_132] - Table TESTDATE1 is going to be initial synced 
[INFO ] 2024-08-22 18:42:34.359 - [任务 11][DB2_132] - Table [TESTDATE1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:42:34.359 - [任务 11][DB2_132] - Query table 'TESTDATE1' counts: 5 
[INFO ] 2024-08-22 18:42:34.359 - [任务 11][DB2_132] - Starting batch read, table name: testTimestamp, offset: null 
[INFO ] 2024-08-22 18:42:34.359 - [任务 11][DB2_132] - Table testTimestamp is going to be initial synced 
[INFO ] 2024-08-22 18:42:34.370 - [任务 11][DB2_132] - Query table 'testTimestamp' counts: 7 
[INFO ] 2024-08-22 18:42:34.370 - [任务 11][DB2_132] - Table [testTimestamp] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:42:34.370 - [任务 11][DB2_132] - Starting batch read, table name: TESTDATE, offset: null 
[INFO ] 2024-08-22 18:42:34.392 - [任务 11][DB2_132] - Table TESTDATE is going to be initial synced 
[INFO ] 2024-08-22 18:42:34.392 - [任务 11][DB2_132] - Query table 'TESTDATE' counts: 5 
[INFO ] 2024-08-22 18:42:34.401 - [任务 11][DB2_132] - Table [TESTDATE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:42:34.401 - [任务 11][DB2_132] - Starting batch read, table name: Test_ikas, offset: null 
[INFO ] 2024-08-22 18:42:34.452 - [任务 11][DB2_132] - Table Test_ikas is going to be initial synced 
[INFO ] 2024-08-22 18:42:34.452 - [任务 11][DB2_132] - Query table 'Test_ikas' counts: 50000 
[INFO ] 2024-08-22 18:55:46.757 - [任务 11] - Stop task milestones: 66c712a217ade8409258c192(任务 11)  
[INFO ] 2024-08-22 18:55:49.284 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] running status set to false 
[INFO ] 2024-08-22 18:55:49.284 - [任务 11][DB2_132] - Table [Test_ikas] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:55:49.292 - [任务 11][DB2_132] - PDK connector node stopped: HazelcastSourcePdkDataNode-6aae8000-db9d-4b8b-9af3-cd9b576d67bf 
[INFO ] 2024-08-22 18:55:49.292 - [任务 11][DB2_132] - PDK connector node released: HazelcastSourcePdkDataNode-6aae8000-db9d-4b8b-9af3-cd9b576d67bf 
[INFO ] 2024-08-22 18:55:49.292 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] schema data cleaned 
[INFO ] 2024-08-22 18:55:49.292 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] monitor closed 
[INFO ] 2024-08-22 18:55:49.293 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] close complete, cost 38 ms 
[INFO ] 2024-08-22 18:55:49.293 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] running status set to false 
[INFO ] 2024-08-22 18:55:49.300 - [任务 11][TestDummy] - Stop connector 
[ERROR] 2024-08-22 18:55:49.301 - [任务 11][TestDummy] - Execute PDK method: TARGET_WRITE_RECORD, tableName: Test_ikas <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: Test_ikas

<-- Simple Stack Trace -->
Caused by: java.lang.InterruptedException: sleep interrupted
	java.lang.Thread.sleep(Native Method)
	io.tapdata.dummy.DummyConnector.supportWriteRecord(DummyConnector.java:254)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:828)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: Test_ikas
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:834)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$55(HazelcastTargetPdkDataNode.java:780)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:774)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$24(HazelcastTargetPdkDataNode.java:483)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:483)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:670)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:554)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:510)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:476)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:482)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:528)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at io.tapdata.dummy.DummyConnector.supportWriteRecord(DummyConnector.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:828)
	... 24 more

[INFO ] 2024-08-22 18:55:49.306 - [任务 11][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-bda356cd-7833-4647-abb3-48361475d101 
[INFO ] 2024-08-22 18:55:49.306 - [任务 11][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-bda356cd-7833-4647-abb3-48361475d101 
[INFO ] 2024-08-22 18:55:49.306 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] schema data cleaned 
[INFO ] 2024-08-22 18:55:49.306 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] monitor closed 
[INFO ] 2024-08-22 18:55:49.306 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] close complete, cost 13 ms 
[INFO ] 2024-08-22 18:55:51.236 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-22 18:55:51.236 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-08-22 18:55:51.236 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-08-22 18:55:51.280 - [任务 11] - Remove memory task client succeed, task: 任务 11[66c712a217ade8409258c192] 
[INFO ] 2024-08-22 18:55:51.281 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[66c712a217ade8409258c192] 
[INFO ] 2024-08-22 18:56:14.914 - [任务 11] - Start task milestones: 66c712a217ade8409258c192(任务 11) 
[INFO ] 2024-08-22 18:56:14.945 - [任务 11] - Task initialization... 
[INFO ] 2024-08-22 18:56:14.945 - [任务 11] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-22 18:56:15.091 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-22 18:56:15.094 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] start preload schema,table counts: 1 
[INFO ] 2024-08-22 18:56:15.094 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] start preload schema,table counts: 1 
[INFO ] 2024-08-22 18:56:15.139 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] preload schema finished, cost 48 ms 
[INFO ] 2024-08-22 18:56:15.144 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] preload schema finished, cost 48 ms 
[INFO ] 2024-08-22 18:56:15.941 - [任务 11][DB2_132] - Source node "DB2_132" read batch size: 100 
[INFO ] 2024-08-22 18:56:15.945 - [任务 11][DB2_132] - Source node "DB2_132" event queue capacity: 200 
[INFO ] 2024-08-22 18:56:15.945 - [任务 11][DB2_132] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-22 18:56:16.053 - [任务 11][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-22 18:56:16.053 - [任务 11][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-22 18:56:16.257 - [任务 11][DB2_132] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724324176254} 
[INFO ] 2024-08-22 18:56:16.257 - [任务 11][DB2_132] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-22 18:56:16.318 - [任务 11][DB2_132] - Initial sync started 
[INFO ] 2024-08-22 18:56:16.328 - [任务 11][DB2_132] - Starting batch read, table name: test111, offset: null 
[INFO ] 2024-08-22 18:56:16.328 - [任务 11][DB2_132] - Table test111 is going to be initial synced 
[INFO ] 2024-08-22 18:56:16.370 - [任务 11][DB2_132] - Table [test111] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 18:56:16.370 - [任务 11][DB2_132] - Query table 'test111' counts: 3 
[INFO ] 2024-08-22 18:56:16.370 - [任务 11][DB2_132] - Initial sync completed 
[INFO ] 2024-08-22 18:56:16.370 - [任务 11][DB2_132] - Incremental sync starting... 
[INFO ] 2024-08-22 18:56:16.374 - [任务 11][DB2_132] - Initial sync completed 
[INFO ] 2024-08-22 18:56:16.375 - [任务 11][DB2_132] - Starting stream read, table list: [test111], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724324176254} 
[INFO ] 2024-08-22 19:01:10.520 - [任务 11] - Stop task milestones: 66c712a217ade8409258c192(任务 11)  
[INFO ] 2024-08-22 19:01:10.610 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] running status set to false 
[INFO ] 2024-08-22 19:01:10.611 - [任务 11][DB2_132] - Log Miner is shutting down... 
[ERROR] 2024-08-22 19:01:10.816 - [任务 11][DB2_132] - io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted <-- Full Stack Trace -->
java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:567)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:223)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	... 6 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.grpc.Status.asRuntimeException(Status.java:535)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:649)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:175)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.InterruptedException
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.throwIfInterrupted(ClientCalls.java:750)
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.waitAndDrain(ClientCalls.java:733)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.waitForNext(ClientCalls.java:623)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:644)
	... 21 more

[INFO ] 2024-08-22 19:01:12.130 - [任务 11][DB2_132] - PDK connector node stopped: HazelcastSourcePdkDataNode-6aae8000-db9d-4b8b-9af3-cd9b576d67bf 
[INFO ] 2024-08-22 19:01:12.130 - [任务 11][DB2_132] - PDK connector node released: HazelcastSourcePdkDataNode-6aae8000-db9d-4b8b-9af3-cd9b576d67bf 
[INFO ] 2024-08-22 19:01:12.131 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] schema data cleaned 
[INFO ] 2024-08-22 19:01:12.131 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] monitor closed 
[INFO ] 2024-08-22 19:01:12.133 - [任务 11][DB2_132] - Node DB2_132[6aae8000-db9d-4b8b-9af3-cd9b576d67bf] close complete, cost 1524 ms 
[INFO ] 2024-08-22 19:01:12.133 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] running status set to false 
[INFO ] 2024-08-22 19:01:12.148 - [任务 11][TestDummy] - Stop connector 
[INFO ] 2024-08-22 19:01:12.149 - [任务 11][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-bda356cd-7833-4647-abb3-48361475d101 
[INFO ] 2024-08-22 19:01:12.149 - [任务 11][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-bda356cd-7833-4647-abb3-48361475d101 
[INFO ] 2024-08-22 19:01:12.149 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] schema data cleaned 
[INFO ] 2024-08-22 19:01:12.149 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] monitor closed 
[INFO ] 2024-08-22 19:01:12.149 - [任务 11][TestDummy] - Node TestDummy[bda356cd-7833-4647-abb3-48361475d101] close complete, cost 16 ms 
[INFO ] 2024-08-22 19:01:16.573 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-22 19:01:16.574 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-08-22 19:01:16.574 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-08-22 19:01:16.610 - [任务 11] - Remove memory task client succeed, task: 任务 11[66c712a217ade8409258c192] 
[INFO ] 2024-08-22 19:01:16.613 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[66c712a217ade8409258c192] 
