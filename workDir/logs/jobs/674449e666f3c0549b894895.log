[INFO ] 2024-11-25 17:57:13.187 - [任务 26] - Task initialization... 
[INFO ] 2024-11-25 17:57:13.188 - [任务 26] - Start task milestones: 674449e666f3c0549b894895(任务 26) 
[INFO ] 2024-11-25 17:57:13.380 - [任务 26] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-11-25 17:57:13.532 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-25 17:57:13.533 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] start preload schema,table counts: 3 
[INFO ] 2024-11-25 17:57:13.533 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] start preload schema,table counts: 3 
[INFO ] 2024-11-25 17:57:13.533 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] preload schema finished, cost 0 ms 
[INFO ] 2024-11-25 17:57:13.533 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] preload schema finished, cost 1 ms 
[INFO ] 2024-11-25 17:57:13.533 - [任务 26][表编辑] - Node table_rename_processor(表编辑: 4522e48a-1824-4f5a-bf26-cd3540bc7b8d) enable batch process 
[INFO ] 2024-11-25 17:57:13.580 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] start preload schema,table counts: 3 
[INFO ] 2024-11-25 17:57:13.580 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] preload schema finished, cost 1 ms 
[INFO ] 2024-11-25 17:57:14.114 - [任务 26][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-25 17:57:14.114 - [任务 26][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-25 17:57:14.476 - [任务 26][AutoTestMySQL] - Source node "AutoTestMySQL" read batch size: 100 
[INFO ] 2024-11-25 17:57:14.476 - [任务 26][AutoTestMySQL] - Source node "AutoTestMySQL" event queue capacity: 200 
[INFO ] 2024-11-25 17:57:14.476 - [任务 26][AutoTestMySQL] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-25 17:57:14.537 - [任务 26][AutoTestMySQL] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000217","position":868317118,"gtidSet":""} 
[INFO ] 2024-11-25 17:57:14.538 - [任务 26][AutoTestMySQL] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-25 17:57:14.661 - [任务 26][AutoTestMySQL] - Initial sync started 
[INFO ] 2024-11-25 17:57:14.662 - [任务 26][AutoTestMySQL] - Starting batch read, table name: ReplEnhancedJSTable 
[INFO ] 2024-11-25 17:57:14.662 - [任务 26][AutoTestMySQL] - Table ReplEnhancedJSTable is going to be initial synced 
[INFO ] 2024-11-25 17:57:14.703 - [任务 26][AutoTestMySQL] - Table [ReplEnhancedJSTable] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-25 17:57:14.703 - [任务 26][AutoTestMySQL] - Starting batch read, table name: ReplFilterTypeTable 
[INFO ] 2024-11-25 17:57:14.703 - [任务 26][AutoTestMySQL] - Table ReplFilterTypeTable is going to be initial synced 
[INFO ] 2024-11-25 17:57:14.732 - [任务 26][AutoTestMySQL] - Table [ReplFilterTypeTable] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-25 17:57:14.732 - [任务 26][AutoTestMySQL] - Starting batch read, table name: ReplStandardJSTable 
[INFO ] 2024-11-25 17:57:14.732 - [任务 26][AutoTestMySQL] - Table ReplStandardJSTable is going to be initial synced 
[INFO ] 2024-11-25 17:57:14.780 - [任务 26][AutoTestMySQL] - Table [ReplStandardJSTable] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-25 17:57:14.780 - [任务 26][AutoTestMySQL] - Initial sync completed 
[INFO ] 2024-11-25 17:57:14.780 - [任务 26][AutoTestMySQL] - Query snapshot row size completed: AutoTestMySQL(ec8486d1-cb11-4f50-9c46-dbfce3677d9d) 
[INFO ] 2024-11-25 17:57:14.780 - [任务 26][AutoTestMySQL] - Incremental sync starting... 
[INFO ] 2024-11-25 17:57:14.780 - [任务 26][AutoTestMySQL] - Initial sync completed 
[INFO ] 2024-11-25 17:57:14.831 - [任务 26][AutoTestMySQL] - Starting stream read, table list: [ReplEnhancedJSTable, ReplFilterTypeTable, ReplStandardJSTable, _tapdata_heartbeat_table], offset: {"filename":"mysql-bin.000217","position":868317118,"gtidSet":""} 
[INFO ] 2024-11-25 17:57:14.831 - [任务 26][AutoTestMySQL] - Starting mysql cdc, server name: f89774cc-dec7-4c6c-9510-a3c601f295a7 
[INFO ] 2024-11-25 17:57:15.068 - [任务 26][AutoTestMySQL] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1154032184
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f89774cc-dec7-4c6c-9510-a3c601f295a7
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f89774cc-dec7-4c6c-9510-a3c601f295a7
  enable.time.adjuster: false
  database.hostname: *************
  database.password: ********
  name: f89774cc-dec7-4c6c-9510-a3c601f295a7
  pdk.offset.string: {"name":"f89774cc-dec7-4c6c-9510-a3c601f295a7","offset":{"{\"server\":\"f89774cc-dec7-4c6c-9510-a3c601f295a7\"}":"{\"file\":\"mysql-bin.000217\",\"pos\":868317118,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: autoTest.ReplEnhancedJSTable,autoTest.ReplFilterTypeTable,autoTest.ReplStandardJSTable,autoTest._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: autoTest
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-25 17:57:15.070 - [任务 26][AutoTestMySQL] - Connector Mysql incremental start succeed, tables: [ReplEnhancedJSTable, ReplFilterTypeTable, ReplStandardJSTable, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-11-25 17:57:26.911 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] running status set to false 
[INFO ] 2024-11-25 17:57:26.933 - [任务 26][AutoTestMySQL] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-25 17:57:26.933 - [任务 26][AutoTestMySQL] - Mysql binlog reader stopped 
[INFO ] 2024-11-25 17:57:26.952 - [任务 26][AutoTestMySQL] - Incremental sync completed 
[INFO ] 2024-11-25 17:57:26.953 - [任务 26][AutoTestMySQL] - PDK connector node stopped: HazelcastSourcePdkDataNode_ec8486d1-cb11-4f50-9c46-dbfce3677d9d_1732528634100 
[INFO ] 2024-11-25 17:57:26.953 - [任务 26][AutoTestMySQL] - PDK connector node released: HazelcastSourcePdkDataNode_ec8486d1-cb11-4f50-9c46-dbfce3677d9d_1732528634100 
[INFO ] 2024-11-25 17:57:26.953 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] schema data cleaned 
[INFO ] 2024-11-25 17:57:26.954 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] monitor closed 
[INFO ] 2024-11-25 17:57:26.954 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] close complete, cost 64 ms 
[INFO ] 2024-11-25 17:57:26.955 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] running status set to false 
[INFO ] 2024-11-25 17:57:26.955 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] schema data cleaned 
[INFO ] 2024-11-25 17:57:26.955 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] monitor closed 
[INFO ] 2024-11-25 17:57:26.956 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] close complete, cost 0 ms 
[INFO ] 2024-11-25 17:57:26.956 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] running status set to false 
[INFO ] 2024-11-25 17:57:26.972 - [任务 26][Dummy] - Stop connector 
[INFO ] 2024-11-25 17:57:26.972 - [任务 26][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_b0c108a4-b774-442c-8180-f5c9486f66e9_1732528634003 
[INFO ] 2024-11-25 17:57:26.972 - [任务 26][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_b0c108a4-b774-442c-8180-f5c9486f66e9_1732528634003 
[INFO ] 2024-11-25 17:57:26.973 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] schema data cleaned 
[INFO ] 2024-11-25 17:57:26.973 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] monitor closed 
[INFO ] 2024-11-25 17:57:27.178 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] close complete, cost 17 ms 
[INFO ] 2024-11-25 17:57:28.521 - [任务 26] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-25 17:57:28.521 - [任务 26] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@604ea6f2 
[INFO ] 2024-11-25 17:57:28.653 - [任务 26] - Stop task milestones: 674449e666f3c0549b894895(任务 26)  
[INFO ] 2024-11-25 17:57:28.654 - [任务 26] - Stopped task aspect(s) 
[INFO ] 2024-11-25 17:57:28.654 - [任务 26] - Snapshot order controller have been removed 
[INFO ] 2024-11-25 17:57:28.748 - [任务 26] - Remove memory task client succeed, task: 任务 26[674449e666f3c0549b894895] 
[INFO ] 2024-11-25 17:57:28.748 - [任务 26] - Destroy memory task client cache succeed, task: 任务 26[674449e666f3c0549b894895] 
[INFO ] 2024-11-25 17:58:24.770 - [任务 26] - Task initialization... 
[INFO ] 2024-11-25 17:58:24.977 - [任务 26] - Start task milestones: 674449e666f3c0549b894895(任务 26) 
[INFO ] 2024-11-25 17:58:25.036 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-25 17:58:25.195 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-25 17:58:25.199 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] start preload schema,table counts: 3 
[INFO ] 2024-11-25 17:58:25.199 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] start preload schema,table counts: 3 
[INFO ] 2024-11-25 17:58:25.199 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] start preload schema,table counts: 3 
[INFO ] 2024-11-25 17:58:25.199 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] preload schema finished, cost 0 ms 
[INFO ] 2024-11-25 17:58:25.199 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] preload schema finished, cost 0 ms 
[INFO ] 2024-11-25 17:58:25.200 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] preload schema finished, cost 1 ms 
[INFO ] 2024-11-25 17:58:25.200 - [任务 26][表编辑] - Node table_rename_processor(表编辑: 4522e48a-1824-4f5a-bf26-cd3540bc7b8d) enable batch process 
[INFO ] 2024-11-25 17:58:25.371 - [任务 26][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-25 17:58:25.371 - [任务 26][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-25 17:58:25.774 - [任务 26][AutoTestMySQL] - Source node "AutoTestMySQL" read batch size: 100 
[INFO ] 2024-11-25 17:58:25.775 - [任务 26][AutoTestMySQL] - Source node "AutoTestMySQL" event queue capacity: 200 
[INFO ] 2024-11-25 17:58:25.776 - [任务 26][AutoTestMySQL] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-11-25 17:58:25.776 - [任务 26][AutoTestMySQL] - batch offset found: {"ReplEnhancedJSTable":{"batch_read_connector_status":"OVER"},"ReplStandardJSTable":{"batch_read_connector_status":"OVER"},"ReplFilterTypeTable":{"batch_read_connector_status":"OVER"}},stream offset found: {"name":"f89774cc-dec7-4c6c-9510-a3c601f295a7","offset":{"{\"server\":\"f89774cc-dec7-4c6c-9510-a3c601f295a7\"}":"{\"ts_sec\":1732528645,\"file\":\"mysql-bin.000217\",\"pos\":868326356,\"row\":1,\"server_id\":1121,\"event\":2}"}} 
[INFO ] 2024-11-25 17:58:25.776 - [任务 26][AutoTestMySQL] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-25 17:58:25.837 - [任务 26][AutoTestMySQL] - Incremental sync starting... 
[INFO ] 2024-11-25 17:58:25.837 - [任务 26][AutoTestMySQL] - Initial sync completed 
[INFO ] 2024-11-25 17:58:25.844 - [任务 26][AutoTestMySQL] - Starting stream read, table list: [ReplEnhancedJSTable, ReplFilterTypeTable, ReplStandardJSTable, _tapdata_heartbeat_table], offset: {"name":"f89774cc-dec7-4c6c-9510-a3c601f295a7","offset":{"{\"server\":\"f89774cc-dec7-4c6c-9510-a3c601f295a7\"}":"{\"ts_sec\":1732528645,\"file\":\"mysql-bin.000217\",\"pos\":868326356,\"row\":1,\"server_id\":1121,\"event\":2}"}} 
[INFO ] 2024-11-25 17:58:25.846 - [任务 26][AutoTestMySQL] - Starting mysql cdc, server name: f89774cc-dec7-4c6c-9510-a3c601f295a7 
[INFO ] 2024-11-25 17:58:25.985 - [任务 26][AutoTestMySQL] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 807452360
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f89774cc-dec7-4c6c-9510-a3c601f295a7
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f89774cc-dec7-4c6c-9510-a3c601f295a7
  enable.time.adjuster: false
  database.hostname: *************
  database.password: ********
  name: f89774cc-dec7-4c6c-9510-a3c601f295a7
  pdk.offset.string: {"name":"f89774cc-dec7-4c6c-9510-a3c601f295a7","offset":{"{\"server\":\"f89774cc-dec7-4c6c-9510-a3c601f295a7\"}":"{\"ts_sec\":1732528645,\"file\":\"mysql-bin.000217\",\"pos\":868326356,\"row\":1,\"server_id\":1121,\"event\":2}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: autoTest.ReplEnhancedJSTable,autoTest.ReplFilterTypeTable,autoTest.ReplStandardJSTable,autoTest._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: autoTest
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-25 17:58:25.985 - [任务 26][AutoTestMySQL] - Connector Mysql incremental start succeed, tables: [ReplEnhancedJSTable, ReplFilterTypeTable, ReplStandardJSTable, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-11-25 17:58:55.287 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] running status set to false 
[INFO ] 2024-11-25 17:58:55.379 - [任务 26][AutoTestMySQL] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-25 17:58:55.390 - [任务 26][AutoTestMySQL] - Mysql binlog reader stopped 
[INFO ] 2024-11-25 17:58:55.418 - [任务 26][AutoTestMySQL] - Incremental sync completed 
[INFO ] 2024-11-25 17:58:55.418 - [任务 26][AutoTestMySQL] - PDK connector node stopped: HazelcastSourcePdkDataNode_ec8486d1-cb11-4f50-9c46-dbfce3677d9d_1732528705267 
[INFO ] 2024-11-25 17:58:55.419 - [任务 26][AutoTestMySQL] - PDK connector node released: HazelcastSourcePdkDataNode_ec8486d1-cb11-4f50-9c46-dbfce3677d9d_1732528705267 
[INFO ] 2024-11-25 17:58:55.419 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] schema data cleaned 
[INFO ] 2024-11-25 17:58:55.421 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] monitor closed 
[INFO ] 2024-11-25 17:58:55.421 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] close complete, cost 145 ms 
[INFO ] 2024-11-25 17:58:55.422 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] running status set to false 
[INFO ] 2024-11-25 17:58:55.422 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] schema data cleaned 
[INFO ] 2024-11-25 17:58:55.422 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] monitor closed 
[INFO ] 2024-11-25 17:58:55.425 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] close complete, cost 1 ms 
[INFO ] 2024-11-25 17:58:55.426 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] running status set to false 
[INFO ] 2024-11-25 17:58:55.433 - [任务 26][Dummy] - Stop connector 
[INFO ] 2024-11-25 17:58:55.435 - [任务 26][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_b0c108a4-b774-442c-8180-f5c9486f66e9_1732528705267 
[INFO ] 2024-11-25 17:58:55.435 - [任务 26][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_b0c108a4-b774-442c-8180-f5c9486f66e9_1732528705267 
[INFO ] 2024-11-25 17:58:55.435 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] schema data cleaned 
[INFO ] 2024-11-25 17:58:55.436 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] monitor closed 
[INFO ] 2024-11-25 17:58:55.436 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] close complete, cost 13 ms 
[INFO ] 2024-11-25 17:58:59.263 - [任务 26] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-25 17:58:59.263 - [任务 26] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@46937d84 
[INFO ] 2024-11-25 17:58:59.263 - [任务 26] - Stop task milestones: 674449e666f3c0549b894895(任务 26)  
[INFO ] 2024-11-25 17:58:59.278 - [任务 26] - Stopped task aspect(s) 
[INFO ] 2024-11-25 17:58:59.278 - [任务 26] - Snapshot order controller have been removed 
[INFO ] 2024-11-25 17:58:59.299 - [任务 26] - Remove memory task client succeed, task: 任务 26[674449e666f3c0549b894895] 
[INFO ] 2024-11-25 17:58:59.301 - [任务 26] - Destroy memory task client cache succeed, task: 任务 26[674449e666f3c0549b894895] 
[INFO ] 2024-11-25 17:59:21.584 - [任务 26] - Task initialization... 
[INFO ] 2024-11-25 17:59:21.584 - [任务 26] - Start task milestones: 674449e666f3c0549b894895(任务 26) 
[INFO ] 2024-11-25 17:59:21.750 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-25 17:59:21.750 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-25 17:59:21.816 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] start preload schema,table counts: 3 
[INFO ] 2024-11-25 17:59:21.817 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] start preload schema,table counts: 3 
[INFO ] 2024-11-25 17:59:21.821 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] start preload schema,table counts: 3 
[INFO ] 2024-11-25 17:59:21.821 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] preload schema finished, cost 1 ms 
[INFO ] 2024-11-25 17:59:21.821 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] preload schema finished, cost 0 ms 
[INFO ] 2024-11-25 17:59:21.821 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] preload schema finished, cost 0 ms 
[INFO ] 2024-11-25 17:59:21.821 - [任务 26][表编辑] - Node table_rename_processor(表编辑: 4522e48a-1824-4f5a-bf26-cd3540bc7b8d) enable batch process 
[INFO ] 2024-11-25 17:59:22.431 - [任务 26][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-25 17:59:22.431 - [任务 26][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-25 17:59:22.675 - [任务 26][AutoTestMySQL] - Source node "AutoTestMySQL" read batch size: 100 
[INFO ] 2024-11-25 17:59:22.675 - [任务 26][AutoTestMySQL] - Source node "AutoTestMySQL" event queue capacity: 200 
[INFO ] 2024-11-25 17:59:22.676 - [任务 26][AutoTestMySQL] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-25 17:59:22.734 - [任务 26][AutoTestMySQL] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000217","position":868429363,"gtidSet":""} 
[INFO ] 2024-11-25 17:59:22.734 - [任务 26][AutoTestMySQL] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-25 17:59:22.802 - [任务 26][AutoTestMySQL] - Initial sync started 
[INFO ] 2024-11-25 17:59:22.803 - [任务 26][AutoTestMySQL] - Starting batch read, table name: ReplEnhancedJSTable 
[INFO ] 2024-11-25 17:59:22.843 - [任务 26][AutoTestMySQL] - Table ReplEnhancedJSTable is going to be initial synced 
[INFO ] 2024-11-25 17:59:22.843 - [任务 26][AutoTestMySQL] - Table [ReplEnhancedJSTable] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-25 17:59:22.843 - [任务 26][AutoTestMySQL] - Starting batch read, table name: ReplFilterTypeTable 
[INFO ] 2024-11-25 17:59:22.843 - [任务 26][AutoTestMySQL] - Table ReplFilterTypeTable is going to be initial synced 
[INFO ] 2024-11-25 17:59:22.865 - [任务 26][AutoTestMySQL] - Table [ReplFilterTypeTable] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-25 17:59:22.865 - [任务 26][AutoTestMySQL] - Starting batch read, table name: ReplStandardJSTable 
[INFO ] 2024-11-25 17:59:22.865 - [任务 26][AutoTestMySQL] - Table ReplStandardJSTable is going to be initial synced 
[INFO ] 2024-11-25 17:59:22.885 - [任务 26][AutoTestMySQL] - Query snapshot row size completed: AutoTestMySQL(ec8486d1-cb11-4f50-9c46-dbfce3677d9d) 
[INFO ] 2024-11-25 17:59:22.886 - [任务 26][AutoTestMySQL] - Table [ReplStandardJSTable] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-25 17:59:22.886 - [任务 26][AutoTestMySQL] - Initial sync completed 
[INFO ] 2024-11-25 17:59:22.886 - [任务 26][AutoTestMySQL] - Incremental sync starting... 
[INFO ] 2024-11-25 17:59:22.887 - [任务 26][AutoTestMySQL] - Initial sync completed 
[INFO ] 2024-11-25 17:59:22.887 - [任务 26][AutoTestMySQL] - Starting stream read, table list: [ReplEnhancedJSTable, ReplFilterTypeTable, ReplStandardJSTable, _tapdata_heartbeat_table], offset: {"filename":"mysql-bin.000217","position":868429363,"gtidSet":""} 
[INFO ] 2024-11-25 17:59:22.943 - [任务 26][AutoTestMySQL] - Starting mysql cdc, server name: ed45ad8e-5967-49bf-b66c-d140cae5c281 
[INFO ] 2024-11-25 17:59:22.945 - [任务 26][AutoTestMySQL] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1653919265
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ed45ad8e-5967-49bf-b66c-d140cae5c281
  database.port: 3306
  threadName: Debezium-Mysql-Connector-ed45ad8e-5967-49bf-b66c-d140cae5c281
  enable.time.adjuster: false
  database.hostname: *************
  database.password: ********
  name: ed45ad8e-5967-49bf-b66c-d140cae5c281
  pdk.offset.string: {"name":"ed45ad8e-5967-49bf-b66c-d140cae5c281","offset":{"{\"server\":\"ed45ad8e-5967-49bf-b66c-d140cae5c281\"}":"{\"file\":\"mysql-bin.000217\",\"pos\":868429363,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: autoTest.ReplEnhancedJSTable,autoTest.ReplFilterTypeTable,autoTest.ReplStandardJSTable,autoTest._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: autoTest
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-25 17:59:23.148 - [任务 26][AutoTestMySQL] - Connector Mysql incremental start succeed, tables: [ReplEnhancedJSTable, ReplFilterTypeTable, ReplStandardJSTable, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-11-25 18:01:42.093 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] running status set to false 
[INFO ] 2024-11-25 18:01:42.185 - [任务 26][AutoTestMySQL] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-25 18:01:42.185 - [任务 26][AutoTestMySQL] - Mysql binlog reader stopped 
[INFO ] 2024-11-25 18:01:42.187 - [任务 26][AutoTestMySQL] - Incremental sync completed 
[INFO ] 2024-11-25 18:01:42.201 - [任务 26][AutoTestMySQL] - PDK connector node stopped: HazelcastSourcePdkDataNode_ec8486d1-cb11-4f50-9c46-dbfce3677d9d_1732528762369 
[INFO ] 2024-11-25 18:01:42.201 - [任务 26][AutoTestMySQL] - PDK connector node released: HazelcastSourcePdkDataNode_ec8486d1-cb11-4f50-9c46-dbfce3677d9d_1732528762369 
[INFO ] 2024-11-25 18:01:42.201 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] schema data cleaned 
[INFO ] 2024-11-25 18:01:42.201 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] monitor closed 
[INFO ] 2024-11-25 18:01:42.203 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] close complete, cost 149 ms 
[INFO ] 2024-11-25 18:01:42.207 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] running status set to false 
[INFO ] 2024-11-25 18:01:42.207 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] schema data cleaned 
[INFO ] 2024-11-25 18:01:42.211 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] monitor closed 
[INFO ] 2024-11-25 18:01:42.211 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] close complete, cost 0 ms 
[INFO ] 2024-11-25 18:01:42.211 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] running status set to false 
[INFO ] 2024-11-25 18:01:42.229 - [任务 26][Dummy] - Stop connector 
[INFO ] 2024-11-25 18:01:42.229 - [任务 26][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_b0c108a4-b774-442c-8180-f5c9486f66e9_1732528762253 
[INFO ] 2024-11-25 18:01:42.229 - [任务 26][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_b0c108a4-b774-442c-8180-f5c9486f66e9_1732528762253 
[INFO ] 2024-11-25 18:01:42.229 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] schema data cleaned 
[INFO ] 2024-11-25 18:01:42.230 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] monitor closed 
[INFO ] 2024-11-25 18:01:42.230 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] close complete, cost 25 ms 
[INFO ] 2024-11-25 18:01:42.323 - [任务 26] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-25 18:01:42.323 - [任务 26] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7f94822a 
[INFO ] 2024-11-25 18:01:42.525 - [任务 26] - Stop task milestones: 674449e666f3c0549b894895(任务 26)  
[INFO ] 2024-11-25 18:01:42.528 - [任务 26] - Stopped task aspect(s) 
[INFO ] 2024-11-25 18:01:42.528 - [任务 26] - Snapshot order controller have been removed 
[INFO ] 2024-11-25 18:01:42.618 - [任务 26] - Remove memory task client succeed, task: 任务 26[674449e666f3c0549b894895] 
[INFO ] 2024-11-25 18:01:42.619 - [任务 26] - Destroy memory task client cache succeed, task: 任务 26[674449e666f3c0549b894895] 
[INFO ] 2024-11-25 18:01:53.902 - [任务 26] - Task initialization... 
[INFO ] 2024-11-25 18:01:54.103 - [任务 26] - Start task milestones: 674449e666f3c0549b894895(任务 26) 
[INFO ] 2024-11-25 18:01:54.183 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-25 18:01:54.285 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-25 18:01:54.285 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] start preload schema,table counts: 3 
[INFO ] 2024-11-25 18:01:54.285 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] start preload schema,table counts: 3 
[INFO ] 2024-11-25 18:01:54.285 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] preload schema finished, cost 1 ms 
[INFO ] 2024-11-25 18:01:54.285 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] preload schema finished, cost 1 ms 
[INFO ] 2024-11-25 18:01:54.285 - [任务 26][表编辑] - Node table_rename_processor(表编辑: 4522e48a-1824-4f5a-bf26-cd3540bc7b8d) enable batch process 
[INFO ] 2024-11-25 18:01:54.315 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] start preload schema,table counts: 3 
[INFO ] 2024-11-25 18:01:54.315 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] preload schema finished, cost 0 ms 
[INFO ] 2024-11-25 18:01:54.860 - [任务 26][Dummy] - Node(Dummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-25 18:01:54.873 - [任务 26][Dummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-25 18:01:55.298 - [任务 26][AutoTestMySQL] - Source node "AutoTestMySQL" read batch size: 100 
[INFO ] 2024-11-25 18:01:55.298 - [任务 26][AutoTestMySQL] - Source node "AutoTestMySQL" event queue capacity: 200 
[INFO ] 2024-11-25 18:01:55.298 - [任务 26][AutoTestMySQL] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-25 18:01:55.372 - [任务 26][AutoTestMySQL] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000217","position":868648087,"gtidSet":""} 
[INFO ] 2024-11-25 18:01:55.372 - [任务 26][AutoTestMySQL] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-25 18:01:55.441 - [任务 26][AutoTestMySQL] - Initial sync started 
[INFO ] 2024-11-25 18:01:55.470 - [任务 26][AutoTestMySQL] - Starting batch read, table name: ReplEnhancedJSTable 
[INFO ] 2024-11-25 18:01:55.479 - [任务 26][AutoTestMySQL] - Table ReplEnhancedJSTable is going to be initial synced 
[INFO ] 2024-11-25 18:01:55.523 - [任务 26][AutoTestMySQL] - Table [ReplEnhancedJSTable] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-25 18:01:55.523 - [任务 26][AutoTestMySQL] - Starting batch read, table name: ReplFilterTypeTable 
[INFO ] 2024-11-25 18:01:55.523 - [任务 26][AutoTestMySQL] - Table ReplFilterTypeTable is going to be initial synced 
[INFO ] 2024-11-25 18:01:55.594 - [任务 26][AutoTestMySQL] - Table [ReplFilterTypeTable] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-25 18:01:55.594 - [任务 26][AutoTestMySQL] - Starting batch read, table name: ReplStandardJSTable 
[INFO ] 2024-11-25 18:01:55.594 - [任务 26][AutoTestMySQL] - Table ReplStandardJSTable is going to be initial synced 
[INFO ] 2024-11-25 18:01:55.670 - [任务 26][AutoTestMySQL] - Table [ReplStandardJSTable] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-25 18:01:55.671 - [任务 26][AutoTestMySQL] - Initial sync completed 
[INFO ] 2024-11-25 18:01:55.671 - [任务 26][AutoTestMySQL] - Incremental sync starting... 
[INFO ] 2024-11-25 18:01:55.672 - [任务 26][AutoTestMySQL] - Initial sync completed 
[INFO ] 2024-11-25 18:01:55.769 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-11-25 18:01:55.769 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Step 1 - Check connection AutoTestMySQL enable share cdc: true 
[INFO ] 2024-11-25 18:01:55.769 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 26 enable share cdc: true 
[INFO ] 2024-11-25 18:01:55.790 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from AutoTestMySQL 
[INFO ] 2024-11-25 18:01:55.797 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Mongo27018', type='mongodb', uri='mongodb://localhost:27018/ExternalStorage', table='null', ttlDay=0] 
[INFO ] 2024-11-25 18:01:55.797 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-11-25 18:01:55.797 - [任务 26][AutoTestMySQL] - Init share cdc reader completed 
[INFO ] 2024-11-25 18:01:55.797 - [任务 26][AutoTestMySQL] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-11-25 18:01:55.798 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-11-25 18:01:55.798 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Read table count: 3, partition size: 1, read thread number: 3 
[INFO ] 2024-11-25 18:01:55.819 - [任务 26][AutoTestMySQL] - Query snapshot row size completed: AutoTestMySQL(ec8486d1-cb11-4f50-9c46-dbfce3677d9d) 
[INFO ] 2024-11-25 18:01:55.819 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6744486f4150dbb0e2477444, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_ReplEnhancedJSTable, version=v2, tableName=ReplEnhancedJSTable, externalStorageTableName=ExternalStorage_SHARE_CDC_-1759887794, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-25 18:01:55.828 - [任务 26][AutoTestMySQL] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from AutoTestMySQL_ReplEnhancedJSTable_任务 26, external storage name: ExternalStorage_SHARE_CDC_-1759887794 
[INFO ] 2024-11-25 18:01:55.828 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [ReplEnhancedJSTable] 
[INFO ] 2024-11-25 18:01:55.831 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Find sequence in construct(ReplEnhancedJSTable) by timestamp(2024-11-25T10:01:55.298Z): 1 
[INFO ] 2024-11-25 18:01:55.831 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Starting read 'ReplEnhancedJSTable' log, sequence: 1 
[INFO ] 2024-11-25 18:01:55.842 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Find by ReplEnhancedJSTable filter: {sequence=1} 
[INFO ] 2024-11-25 18:01:55.842 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67444b114150dbb0e247fa2d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_ReplFilterTypeTable, version=v2, tableName=ReplFilterTypeTable, externalStorageTableName=ExternalStorage_SHARE_CDC_-1913600357, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-25 18:01:56.008 - [任务 26][AutoTestMySQL] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from AutoTestMySQL_ReplFilterTypeTable_任务 26, external storage name: ExternalStorage_SHARE_CDC_-1913600357 
[INFO ] 2024-11-25 18:01:56.008 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [ReplFilterTypeTable] 
[INFO ] 2024-11-25 18:01:56.009 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Find sequence in construct(ReplFilterTypeTable) by timestamp(2024-11-25T10:01:55.298Z): 0 
[INFO ] 2024-11-25 18:01:56.009 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Starting read 'ReplFilterTypeTable' log, sequence: 0 
[INFO ] 2024-11-25 18:01:56.023 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Find by ReplFilterTypeTable filter: {sequence=0} 
[INFO ] 2024-11-25 18:01:56.023 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67444b114150dbb0e247fa2e, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_ReplStandardJSTable, version=v2, tableName=ReplStandardJSTable, externalStorageTableName=ExternalStorage_SHARE_CDC_2025112871, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-25 18:01:56.167 - [任务 26][AutoTestMySQL] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from AutoTestMySQL_ReplStandardJSTable_任务 26, external storage name: ExternalStorage_SHARE_CDC_2025112871 
[INFO ] 2024-11-25 18:01:56.167 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [ReplStandardJSTable] 
[INFO ] 2024-11-25 18:01:56.168 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Find sequence in construct(ReplStandardJSTable) by timestamp(2024-11-25T10:01:55.298Z): 0 
[INFO ] 2024-11-25 18:01:56.169 - [任务 26][AutoTestMySQL] - Connector Mysql incremental start succeed, tables: [ReplEnhancedJSTable, ReplFilterTypeTable, ReplStandardJSTable], data change syncing 
[INFO ] 2024-11-25 18:01:56.169 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Starting read 'ReplStandardJSTable' log, sequence: 0 
[INFO ] 2024-11-25 18:01:56.370 - [任务 26][AutoTestMySQL] - [Share CDC Task HZ Reader] - Find by ReplStandardJSTable filter: {sequence=0} 
[INFO ] 2024-11-25 18:02:47.904 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] running status set to false 
[INFO ] 2024-11-25 18:02:47.933 - [任务 26][AutoTestMySQL] - Incremental sync completed 
[INFO ] 2024-11-25 18:02:47.933 - [任务 26][AutoTestMySQL] - PDK connector node stopped: HazelcastSourcePdkDataNode_ec8486d1-cb11-4f50-9c46-dbfce3677d9d_1732528914891 
[INFO ] 2024-11-25 18:02:47.933 - [任务 26][AutoTestMySQL] - PDK connector node released: HazelcastSourcePdkDataNode_ec8486d1-cb11-4f50-9c46-dbfce3677d9d_1732528914891 
[INFO ] 2024-11-25 18:02:47.933 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] schema data cleaned 
[INFO ] 2024-11-25 18:02:47.933 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] monitor closed 
[INFO ] 2024-11-25 18:02:47.934 - [任务 26][AutoTestMySQL] - Node AutoTestMySQL[ec8486d1-cb11-4f50-9c46-dbfce3677d9d] close complete, cost 31 ms 
[INFO ] 2024-11-25 18:02:47.934 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] running status set to false 
[INFO ] 2024-11-25 18:02:47.934 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] schema data cleaned 
[INFO ] 2024-11-25 18:02:47.934 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] monitor closed 
[INFO ] 2024-11-25 18:02:47.935 - [任务 26][表编辑] - Node 表编辑[4522e48a-1824-4f5a-bf26-cd3540bc7b8d] close complete, cost 0 ms 
[INFO ] 2024-11-25 18:02:47.935 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] running status set to false 
[INFO ] 2024-11-25 18:02:47.942 - [任务 26][Dummy] - Stop connector 
[INFO ] 2024-11-25 18:02:47.942 - [任务 26][Dummy] - PDK connector node stopped: HazelcastTargetPdkDataNode_b0c108a4-b774-442c-8180-f5c9486f66e9_1732528914763 
[INFO ] 2024-11-25 18:02:47.942 - [任务 26][Dummy] - PDK connector node released: HazelcastTargetPdkDataNode_b0c108a4-b774-442c-8180-f5c9486f66e9_1732528914763 
[INFO ] 2024-11-25 18:02:47.942 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] schema data cleaned 
[INFO ] 2024-11-25 18:02:47.942 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] monitor closed 
[INFO ] 2024-11-25 18:02:48.147 - [任务 26][Dummy] - Node Dummy[b0c108a4-b774-442c-8180-f5c9486f66e9] close complete, cost 7 ms 
[INFO ] 2024-11-25 18:02:51.192 - [任务 26] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-25 18:02:51.316 - [任务 26] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@401e2303 
[INFO ] 2024-11-25 18:02:51.316 - [任务 26] - Stop task milestones: 674449e666f3c0549b894895(任务 26)  
[INFO ] 2024-11-25 18:03:18.833 - [任务 26] - Stopped task aspect(s) 
[INFO ] 2024-11-25 18:03:18.835 - [任务 26] - Snapshot order controller have been removed 
[INFO ] 2024-11-25 18:03:19.077 - [任务 26] - Remove memory task client succeed, task: 任务 26[674449e666f3c0549b894895] 
[INFO ] 2024-11-25 18:03:19.080 - [任务 26] - Destroy memory task client cache succeed, task: 任务 26[674449e666f3c0549b894895] 
