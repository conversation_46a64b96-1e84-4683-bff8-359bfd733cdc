[INFO ] 2024-08-07 10:25:41.627 - [任务 4] - Task initialization... 
[INFO ] 2024-08-07 10:25:41.835 - [任务 4] - Start task milestones: 66b2daf01ba6600c539adc64(任务 4) 
[INFO ] 2024-08-07 10:25:43.791 - [任务 4] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-08-07 10:25:43.791 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-07 10:25:44.365 - [任务 4][Mysql3306] - Node Mysql3306[e8d7bb7f-d2f8-407b-a53c-460adf527115] start preload schema,table counts: 34 
[INFO ] 2024-08-07 10:25:44.368 - [任务 4][LocalKucun] - Node LocalKucun[b94dcb73-ca8b-4d86-be63-32b4a4a9194c] start preload schema,table counts: 34 
[INFO ] 2024-08-07 10:25:44.369 - [任务 4][Mysql3306] - Node Mysql3306[e8d7bb7f-d2f8-407b-a53c-460adf527115] preload schema finished, cost 1 ms 
[INFO ] 2024-08-07 10:25:44.372 - [任务 4][LocalKucun] - Node LocalKucun[b94dcb73-ca8b-4d86-be63-32b4a4a9194c] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 10:25:45.517 - [任务 4][Mysql3306] - Node(Mysql3306) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-07 10:25:45.523 - [任务 4][Mysql3306] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-07 10:25:45.729 - [任务 4][Mysql3306] - Table: 库存汇总记录表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:45.805 - [任务 4][Mysql3306] - Table: Lowes订单 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:45.805 - [任务 4][Mysql3306] - Table: 平台店铺列表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:45.869 - [任务 4][Mysql3306] - Table: Homedepot回款底表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:45.870 - [任务 4][LocalKucun] - Source node "LocalKucun" read batch size: 100 
[INFO ] 2024-08-07 10:25:45.870 - [任务 4][LocalKucun] - Source node "LocalKucun" event queue capacity: 200 
[INFO ] 2024-08-07 10:25:45.872 - [任务 4][LocalKucun] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-07 10:25:45.925 - [任务 4][Mysql3306] - Table: 其他出库表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:45.993 - [任务 4][Mysql3306] - Table: 工厂库存表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:45.993 - [任务 4][LocalKucun] - batch offset found: {},stream offset found: {"filename":"binlog.000003","position":157,"gtidSet":""} 
[INFO ] 2024-08-07 10:25:46.065 - [任务 4][LocalKucun] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-07 10:25:46.065 - [任务 4][Mysql3306] - Table: 生产订单表demo already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:46.175 - [任务 4][Mysql3306] - Table: Amazon回款底表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:46.177 - [任务 4][LocalKucun] - Initial sync started 
[INFO ] 2024-08-07 10:25:46.183 - [任务 4][LocalKucun] - Starting batch read, table name: 库存汇总记录表 
[INFO ] 2024-08-07 10:25:46.200 - [任务 4][LocalKucun] - Table 库存汇总记录表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:46.200 - [任务 4][Mysql3306] - Table: Homedepot订单 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:46.313 - [任务 4][LocalKucun] - Query table '库存汇总记录表' counts: 5 
[INFO ] 2024-08-07 10:25:46.315 - [任务 4][Mysql3306] - Table: WAYFAIR订单 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:46.316 - [任务 4][LocalKucun] - Table [库存汇总记录表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:46.320 - [任务 4][LocalKucun] - Starting batch read, table name: Lowes订单 
[INFO ] 2024-08-07 10:25:46.320 - [任务 4][LocalKucun] - Table Lowes订单 is going to be initial synced 
[INFO ] 2024-08-07 10:25:46.393 - [任务 4][Mysql3306] - Table: Walmart订单 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:46.394 - [任务 4][LocalKucun] - Table [Lowes订单] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:46.395 - [任务 4][LocalKucun] - Query table 'Lowes订单' counts: 0 
[INFO ] 2024-08-07 10:25:46.395 - [任务 4][LocalKucun] - Starting batch read, table name: 平台店铺列表 
[INFO ] 2024-08-07 10:25:46.444 - [任务 4][LocalKucun] - Table 平台店铺列表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:46.447 - [任务 4][LocalKucun] - Table [平台店铺列表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:46.447 - [任务 4][LocalKucun] - Query table '平台店铺列表' counts: 18 
[INFO ] 2024-08-07 10:25:46.447 - [任务 4][LocalKucun] - Starting batch read, table name: Homedepot回款底表 
[INFO ] 2024-08-07 10:25:46.452 - [任务 4][LocalKucun] - Table Homedepot回款底表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:46.452 - [任务 4][Mysql3306] - Table: 产品信息表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:46.509 - [任务 4][Mysql3306] - Table: 销售统计表_月度 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:46.509 - [任务 4][LocalKucun] - Table [Homedepot回款底表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:46.510 - [任务 4][LocalKucun] - Query table 'Homedepot回款底表' counts: 2 
[INFO ] 2024-08-07 10:25:46.511 - [任务 4][LocalKucun] - Starting batch read, table name: 其他出库表 
[INFO ] 2024-08-07 10:25:46.512 - [任务 4][LocalKucun] - Table 其他出库表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:46.546 - [任务 4][Mysql3306] - Table: 工厂库存表demo already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:46.569 - [任务 4][LocalKucun] - Table [其他出库表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:46.569 - [任务 4][LocalKucun] - Query table '其他出库表' counts: 1 
[INFO ] 2024-08-07 10:25:46.570 - [任务 4][LocalKucun] - Starting batch read, table name: 工厂库存表 
[INFO ] 2024-08-07 10:25:46.570 - [任务 4][LocalKucun] - Table 工厂库存表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:46.614 - [任务 4][Mysql3306] - Table: 财务回款表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:46.615 - [任务 4][LocalKucun] - Table [工厂库存表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:46.615 - [任务 4][LocalKucun] - Query table '工厂库存表' counts: 6 
[INFO ] 2024-08-07 10:25:46.615 - [任务 4][LocalKucun] - Starting batch read, table name: 生产订单表demo 
[INFO ] 2024-08-07 10:25:46.640 - [任务 4][LocalKucun] - Table 生产订单表demo is going to be initial synced 
[INFO ] 2024-08-07 10:25:46.640 - [任务 4][Mysql3306] - Table: 尾程账单表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:46.669 - [任务 4][LocalKucun] - Query table '生产订单表demo' counts: 28 
[INFO ] 2024-08-07 10:25:46.670 - [任务 4][LocalKucun] - Table [生产订单表demo] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:46.670 - [任务 4][LocalKucun] - Starting batch read, table name: Amazon回款底表 
[INFO ] 2024-08-07 10:25:46.672 - [任务 4][LocalKucun] - Table Amazon回款底表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:46.728 - [任务 4][Mysql3306] - Table: 海外仓库存表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:46.729 - [任务 4][LocalKucun] - Table [Amazon回款底表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:46.736 - [任务 4][LocalKucun] - Query table 'Amazon回款底表' counts: 2 
[INFO ] 2024-08-07 10:25:46.737 - [任务 4][LocalKucun] - Starting batch read, table name: Homedepot订单 
[INFO ] 2024-08-07 10:25:46.738 - [任务 4][LocalKucun] - Table Homedepot订单 is going to be initial synced 
[INFO ] 2024-08-07 10:25:46.738 - [任务 4][Mysql3306] - Table: Amazon售后表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:46.793 - [任务 4][LocalKucun] - Table [Homedepot订单] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:46.794 - [任务 4][LocalKucun] - Query table 'Homedepot订单' counts: 0 
[INFO ] 2024-08-07 10:25:46.794 - [任务 4][LocalKucun] - Starting batch read, table name: WAYFAIR订单 
[INFO ] 2024-08-07 10:25:46.803 - [任务 4][LocalKucun] - Table WAYFAIR订单 is going to be initial synced 
[INFO ] 2024-08-07 10:25:46.804 - [任务 4][Mysql3306] - Table: Amazon订单 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:46.848 - [任务 4][Mysql3306] - Table: 产品类别表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:46.848 - [任务 4][LocalKucun] - Query table 'WAYFAIR订单' counts: 285 
[INFO ] 2024-08-07 10:25:46.944 - [任务 4][Mysql3306] - Table: 发货明细表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:46.944 - [任务 4][Mysql3306] - Table: 广告数据表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:47.059 - [任务 4][Mysql3306] - Table: 生产订单表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:47.059 - [任务 4][Mysql3306] - Table: 问题运费表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:47.167 - [任务 4][Mysql3306] - Table: 入库明细表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:47.170 - [任务 4][Mysql3306] - Table: demo发货明细表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:47.272 - [任务 4][Mysql3306] - Table: 销售SKU表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:47.272 - [任务 4][Mysql3306] - Table: Wayfair售后表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:47.369 - [任务 4][Mysql3306] - Table: Wayfair回款底表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:47.369 - [任务 4][Mysql3306] - Table: 供应商列表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:47.482 - [任务 4][Mysql3306] - Table: 库存算表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:47.482 - [任务 4][Mysql3306] - Table: 操作记录表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:47.620 - [任务 4][Mysql3306] - Table: Homedepot售后表 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:47.621 - [任务 4][Mysql3306] - Table: Shopify订单 already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-08-07 10:25:47.648 - [任务 4][LocalKucun] - Table [WAYFAIR订单] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:47.648 - [任务 4][LocalKucun] - Starting batch read, table name: Walmart订单 
[INFO ] 2024-08-07 10:25:47.649 - [任务 4][LocalKucun] - Table Walmart订单 is going to be initial synced 
[INFO ] 2024-08-07 10:25:54.468 - [任务 4][LocalKucun] - Table [Walmart订单] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:54.469 - [任务 4][LocalKucun] - Query table 'Walmart订单' counts: 1 
[INFO ] 2024-08-07 10:25:54.469 - [任务 4][LocalKucun] - Starting batch read, table name: 产品信息表 
[INFO ] 2024-08-07 10:25:54.475 - [任务 4][LocalKucun] - Table 产品信息表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:54.574 - [任务 4][LocalKucun] - Query table '产品信息表' counts: 657 
[INFO ] 2024-08-07 10:25:54.575 - [任务 4][LocalKucun] - Table [产品信息表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:54.578 - [任务 4][LocalKucun] - Starting batch read, table name: 销售统计表_月度 
[INFO ] 2024-08-07 10:25:54.578 - [任务 4][LocalKucun] - Table 销售统计表_月度 is going to be initial synced 
[INFO ] 2024-08-07 10:25:54.632 - [任务 4][LocalKucun] - Table [销售统计表_月度] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:54.633 - [任务 4][LocalKucun] - Query table '销售统计表_月度' counts: 2 
[INFO ] 2024-08-07 10:25:54.633 - [任务 4][LocalKucun] - Starting batch read, table name: 工厂库存表demo 
[INFO ] 2024-08-07 10:25:54.633 - [任务 4][LocalKucun] - Table 工厂库存表demo is going to be initial synced 
[INFO ] 2024-08-07 10:25:54.705 - [任务 4][LocalKucun] - Table [工厂库存表demo] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:54.706 - [任务 4][LocalKucun] - Query table '工厂库存表demo' counts: 35 
[INFO ] 2024-08-07 10:25:54.706 - [任务 4][LocalKucun] - Starting batch read, table name: 财务回款表 
[INFO ] 2024-08-07 10:25:54.706 - [任务 4][LocalKucun] - Table 财务回款表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:54.768 - [任务 4][LocalKucun] - Query table '财务回款表' counts: 3 
[INFO ] 2024-08-07 10:25:54.768 - [任务 4][LocalKucun] - Table [财务回款表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:54.768 - [任务 4][LocalKucun] - Starting batch read, table name: 尾程账单表 
[INFO ] 2024-08-07 10:25:54.768 - [任务 4][LocalKucun] - Table 尾程账单表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:54.901 - [任务 4][LocalKucun] - Table [尾程账单表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:54.901 - [任务 4][LocalKucun] - Query table '尾程账单表' counts: 3 
[INFO ] 2024-08-07 10:25:54.902 - [任务 4][LocalKucun] - Starting batch read, table name: 海外仓库存表 
[INFO ] 2024-08-07 10:25:54.902 - [任务 4][LocalKucun] - Table 海外仓库存表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:54.998 - [任务 4][LocalKucun] - Query table '海外仓库存表' counts: 4 
[INFO ] 2024-08-07 10:25:54.999 - [任务 4][LocalKucun] - Table [海外仓库存表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:54.999 - [任务 4][LocalKucun] - Starting batch read, table name: Amazon售后表 
[INFO ] 2024-08-07 10:25:55.090 - [任务 4][LocalKucun] - Table Amazon售后表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:55.090 - [任务 4][LocalKucun] - Table [Amazon售后表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:55.092 - [任务 4][LocalKucun] - Query table 'Amazon售后表' counts: 1 
[INFO ] 2024-08-07 10:25:55.093 - [任务 4][LocalKucun] - Starting batch read, table name: Amazon订单 
[INFO ] 2024-08-07 10:25:55.158 - [任务 4][LocalKucun] - Table Amazon订单 is going to be initial synced 
[INFO ] 2024-08-07 10:25:55.158 - [任务 4][LocalKucun] - Query table 'Amazon订单' counts: 2084 
[INFO ] 2024-08-07 10:25:55.639 - [任务 4][LocalKucun] - Table [Amazon订单] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:55.641 - [任务 4][LocalKucun] - Starting batch read, table name: 产品类别表 
[INFO ] 2024-08-07 10:25:55.641 - [任务 4][LocalKucun] - Table 产品类别表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:55.721 - [任务 4][LocalKucun] - Query table '产品类别表' counts: 43 
[INFO ] 2024-08-07 10:25:55.725 - [任务 4][LocalKucun] - Table [产品类别表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:55.725 - [任务 4][LocalKucun] - Starting batch read, table name: 发货明细表 
[INFO ] 2024-08-07 10:25:55.725 - [任务 4][LocalKucun] - Table 发货明细表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:55.813 - [任务 4][LocalKucun] - Query table '发货明细表' counts: 662 
[INFO ] 2024-08-07 10:25:55.813 - [任务 4][LocalKucun] - Table [发货明细表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:55.814 - [任务 4][LocalKucun] - Starting batch read, table name: 广告数据表 
[INFO ] 2024-08-07 10:25:55.814 - [任务 4][LocalKucun] - Table 广告数据表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:55.883 - [任务 4][LocalKucun] - Table [广告数据表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:55.883 - [任务 4][LocalKucun] - Query table '广告数据表' counts: 2 
[INFO ] 2024-08-07 10:25:55.884 - [任务 4][LocalKucun] - Starting batch read, table name: 生产订单表 
[INFO ] 2024-08-07 10:25:55.884 - [任务 4][LocalKucun] - Table 生产订单表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:55.959 - [任务 4][LocalKucun] - Query table '生产订单表' counts: 807 
[INFO ] 2024-08-07 10:25:55.959 - [任务 4][LocalKucun] - Table [生产订单表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:55.960 - [任务 4][LocalKucun] - Starting batch read, table name: 问题运费表 
[INFO ] 2024-08-07 10:25:55.960 - [任务 4][LocalKucun] - Table 问题运费表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:56.033 - [任务 4][LocalKucun] - Table [问题运费表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:56.033 - [任务 4][LocalKucun] - Query table '问题运费表' counts: 4 
[INFO ] 2024-08-07 10:25:56.034 - [任务 4][LocalKucun] - Starting batch read, table name: 入库明细表 
[INFO ] 2024-08-07 10:25:56.034 - [任务 4][LocalKucun] - Table 入库明细表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:56.119 - [任务 4][LocalKucun] - Table [入库明细表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:56.120 - [任务 4][LocalKucun] - Query table '入库明细表' counts: 12 
[INFO ] 2024-08-07 10:25:56.121 - [任务 4][LocalKucun] - Starting batch read, table name: demo发货明细表 
[INFO ] 2024-08-07 10:25:56.121 - [任务 4][LocalKucun] - Table demo发货明细表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:56.175 - [任务 4][LocalKucun] - Table [demo发货明细表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:56.175 - [任务 4][LocalKucun] - Query table 'demo发货明细表' counts: 2 
[INFO ] 2024-08-07 10:25:56.176 - [任务 4][LocalKucun] - Starting batch read, table name: 销售SKU表 
[INFO ] 2024-08-07 10:25:56.176 - [任务 4][LocalKucun] - Table 销售SKU表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:56.377 - [任务 4][LocalKucun] - Query table '销售SKU表' counts: 2209 
[INFO ] 2024-08-07 10:25:56.416 - [任务 4][LocalKucun] - Table [销售SKU表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:56.416 - [任务 4][LocalKucun] - Starting batch read, table name: Wayfair售后表 
[INFO ] 2024-08-07 10:25:56.416 - [任务 4][LocalKucun] - Table Wayfair售后表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:56.473 - [任务 4][LocalKucun] - Table [Wayfair售后表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:56.473 - [任务 4][LocalKucun] - Query table 'Wayfair售后表' counts: 3 
[INFO ] 2024-08-07 10:25:56.473 - [任务 4][LocalKucun] - Starting batch read, table name: Wayfair回款底表 
[INFO ] 2024-08-07 10:25:56.473 - [任务 4][LocalKucun] - Table Wayfair回款底表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:56.533 - [任务 4][LocalKucun] - Query table 'Wayfair回款底表' counts: 3 
[INFO ] 2024-08-07 10:25:56.534 - [任务 4][LocalKucun] - Table [Wayfair回款底表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:56.534 - [任务 4][LocalKucun] - Starting batch read, table name: 供应商列表 
[INFO ] 2024-08-07 10:25:56.587 - [任务 4][LocalKucun] - Table 供应商列表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:56.588 - [任务 4][LocalKucun] - Table [供应商列表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:56.588 - [任务 4][LocalKucun] - Query table '供应商列表' counts: 24 
[INFO ] 2024-08-07 10:25:56.588 - [任务 4][LocalKucun] - Starting batch read, table name: 库存算表 
[INFO ] 2024-08-07 10:25:56.647 - [任务 4][LocalKucun] - Table 库存算表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:56.647 - [任务 4][LocalKucun] - Query table '库存算表' counts: 78 
[INFO ] 2024-08-07 10:25:56.647 - [任务 4][LocalKucun] - Table [库存算表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:56.647 - [任务 4][LocalKucun] - Starting batch read, table name: 操作记录表 
[INFO ] 2024-08-07 10:25:56.647 - [任务 4][LocalKucun] - Table 操作记录表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:56.719 - [任务 4][LocalKucun] - Table [操作记录表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:56.719 - [任务 4][LocalKucun] - Query table '操作记录表' counts: 435 
[INFO ] 2024-08-07 10:25:56.720 - [任务 4][LocalKucun] - Starting batch read, table name: Homedepot售后表 
[INFO ] 2024-08-07 10:25:56.720 - [任务 4][LocalKucun] - Table Homedepot售后表 is going to be initial synced 
[INFO ] 2024-08-07 10:25:56.777 - [任务 4][LocalKucun] - Table [Homedepot售后表] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:56.778 - [任务 4][LocalKucun] - Query table 'Homedepot售后表' counts: 4 
[INFO ] 2024-08-07 10:25:56.778 - [任务 4][LocalKucun] - Starting batch read, table name: Shopify订单 
[INFO ] 2024-08-07 10:25:56.778 - [任务 4][LocalKucun] - Table Shopify订单 is going to be initial synced 
[INFO ] 2024-08-07 10:25:56.842 - [任务 4][LocalKucun] - Query table 'Shopify订单' counts: 0 
[INFO ] 2024-08-07 10:25:56.842 - [任务 4][LocalKucun] - Table [Shopify订单] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-07 10:25:56.857 - [任务 4][LocalKucun] - Initial sync completed 
[INFO ] 2024-08-07 10:25:56.857 - [任务 4][LocalKucun] - Incremental sync starting... 
[INFO ] 2024-08-07 10:25:56.864 - [任务 4][LocalKucun] - Initial sync completed 
[INFO ] 2024-08-07 10:25:56.867 - [任务 4][LocalKucun] - Starting stream read, table list: [库存汇总记录表, Lowes订单, 平台店铺列表, Homedepot回款底表, 其他出库表, 工厂库存表, 生产订单表demo, Amazon回款底表, Homedepot订单, WAYFAIR订单, Walmart订单, 产品信息表, 销售统计表_月度, 工厂库存表demo, 财务回款表, 尾程账单表, 海外仓库存表, Amazon售后表, Amazon订单, 产品类别表, 发货明细表, 广告数据表, 生产订单表, 问题运费表, 入库明细表, demo发货明细表, 销售SKU表, Wayfair售后表, Wayfair回款底表, 供应商列表, 库存算表, 操作记录表, Homedepot售后表, Shopify订单], offset: {"filename":"binlog.000003","position":157,"gtidSet":""} 
[INFO ] 2024-08-07 10:25:56.991 - [任务 4][LocalKucun] - Starting mysql cdc, server name: 0c7a7cdf-426c-4b73-b82f-78b842c8697d 
[INFO ] 2024-08-07 10:25:56.991 - [任务 4][LocalKucun] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 533395554
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 0c7a7cdf-426c-4b73-b82f-78b842c8697d
  database.port: 3306
  threadName: Debezium-Mysql-Connector-0c7a7cdf-426c-4b73-b82f-78b842c8697d
  database.hostname: ***********
  database.password: ********
  name: 0c7a7cdf-426c-4b73-b82f-78b842c8697d
  pdk.offset.string: {"name":"0c7a7cdf-426c-4b73-b82f-78b842c8697d","offset":{"{\"server\":\"0c7a7cdf-426c-4b73-b82f-78b842c8697d\"}":"{\"file\":\"binlog.000003\",\"pos\":157,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: 库存.库存汇总记录表,库存.Lowes订单,库存.平台店铺列表,库存.Homedepot回款底表,库存.其他出库表,库存.工厂库存表,库存.生产订单表demo,库存.Amazon回款底表,库存.Homedepot订单,库存.WAYFAIR订单,库存.Walmart订单,库存.产品信息表,库存.销售统计表_月度,库存.工厂库存表demo,库存.财务回款表,库存.尾程账单表,库存.海外仓库存表,库存.Amazon售后表,库存.Amazon订单,库存.产品类别表,库存.发货明细表,库存.广告数据表,库存.生产订单表,库存.问题运费表,库存.入库明细表,库存.demo发货明细表,库存.销售SKU表,库存.Wayfair售后表,库存.Wayfair回款底表,库存.供应商列表,库存.库存算表,库存.操作记录表,库存.Homedepot售后表,库存.Shopify订单
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: 库存
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-07 10:25:57.404 - [任务 4][LocalKucun] - Connector Mysql incremental start succeed, tables: [库存汇总记录表, Lowes订单, 平台店铺列表, Homedepot回款底表, 其他出库表, 工厂库存表, 生产订单表demo, Amazon回款底表, Homedepot订单, WAYFAIR订单, Walmart订单, 产品信息表, 销售统计表_月度, 工厂库存表demo, 财务回款表, 尾程账单表, 海外仓库存表, Amazon售后表, Amazon订单, 产品类别表, 发货明细表, 广告数据表, 生产订单表, 问题运费表, 入库明细表, demo发货明细表, 销售SKU表, Wayfair售后表, Wayfair回款底表, 供应商列表, 库存算表, 操作记录表, Homedepot售后表, Shopify订单], data change syncing 
[INFO ] 2024-08-07 10:27:08.898 - [任务 4][LocalKucun] - Node LocalKucun[b94dcb73-ca8b-4d86-be63-32b4a4a9194c] running status set to false 
[INFO ] 2024-08-07 10:27:09.014 - [任务 4][LocalKucun] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-07 10:27:09.014 - [任务 4][LocalKucun] - Mysql binlog reader stopped 
[INFO ] 2024-08-07 10:27:09.016 - [任务 4][LocalKucun] - Incremental sync completed 
[INFO ] 2024-08-07 10:27:09.058 - [任务 4][LocalKucun] - PDK connector node stopped: HazelcastSourcePdkDataNode-b94dcb73-ca8b-4d86-be63-32b4a4a9194c 
[INFO ] 2024-08-07 10:27:09.059 - [任务 4][LocalKucun] - PDK connector node released: HazelcastSourcePdkDataNode-b94dcb73-ca8b-4d86-be63-32b4a4a9194c 
[INFO ] 2024-08-07 10:27:09.062 - [任务 4][LocalKucun] - Node LocalKucun[b94dcb73-ca8b-4d86-be63-32b4a4a9194c] schema data cleaned 
[INFO ] 2024-08-07 10:27:09.062 - [任务 4][LocalKucun] - Node LocalKucun[b94dcb73-ca8b-4d86-be63-32b4a4a9194c] monitor closed 
[INFO ] 2024-08-07 10:27:09.073 - [任务 4][LocalKucun] - Node LocalKucun[b94dcb73-ca8b-4d86-be63-32b4a4a9194c] close complete, cost 193 ms 
[INFO ] 2024-08-07 10:27:09.074 - [任务 4][Mysql3306] - Node Mysql3306[e8d7bb7f-d2f8-407b-a53c-460adf527115] running status set to false 
[INFO ] 2024-08-07 10:27:09.114 - [任务 4][Mysql3306] - PDK connector node stopped: HazelcastTargetPdkDataNode-e8d7bb7f-d2f8-407b-a53c-460adf527115 
[INFO ] 2024-08-07 10:27:09.114 - [任务 4][Mysql3306] - PDK connector node released: HazelcastTargetPdkDataNode-e8d7bb7f-d2f8-407b-a53c-460adf527115 
[INFO ] 2024-08-07 10:27:09.114 - [任务 4][Mysql3306] - Node Mysql3306[e8d7bb7f-d2f8-407b-a53c-460adf527115] schema data cleaned 
[INFO ] 2024-08-07 10:27:09.116 - [任务 4][Mysql3306] - Node Mysql3306[e8d7bb7f-d2f8-407b-a53c-460adf527115] monitor closed 
[INFO ] 2024-08-07 10:27:09.116 - [任务 4][Mysql3306] - Node Mysql3306[e8d7bb7f-d2f8-407b-a53c-460adf527115] close complete, cost 44 ms 
[INFO ] 2024-08-07 10:27:09.436 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-07 10:27:09.559 - [任务 4] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@286a2d5c 
[INFO ] 2024-08-07 10:27:09.559 - [任务 4] - Stop task milestones: 66b2daf01ba6600c539adc64(任务 4)  
[INFO ] 2024-08-07 10:27:09.579 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-08-07 10:27:09.579 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-08-07 10:27:09.666 - [任务 4] - Remove memory task client succeed, task: 任务 4[66b2daf01ba6600c539adc64] 
[INFO ] 2024-08-07 10:27:09.666 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[66b2daf01ba6600c539adc64] 
