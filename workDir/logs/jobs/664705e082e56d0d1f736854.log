[INFO ] 2024-05-17 15:23:41.583 - [任务 9] - Start task milestones: 664705e082e56d0d1f736854(任务 9) 
[INFO ] 2024-05-17 15:23:41.583 - [任务 9] - Task initialization... 
[INFO ] 2024-05-17 15:23:41.652 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 15:23:41.654 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 15:23:41.699 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] start preload schema,table counts: 1 
[INFO ] 2024-05-17 15:23:41.700 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] start preload schema,table counts: 1 
[INFO ] 2024-05-17 15:23:41.728 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] preload schema finished, cost 21 ms 
[INFO ] 2024-05-17 15:23:41.728 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] preload schema finished, cost 28 ms 
[INFO ] 2024-05-17 15:23:42.539 - [任务 9][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 15:23:42.539 - [任务 9][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 15:23:42.539 - [任务 9][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 15:23:42.564 - [任务 9][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FC00001CA00003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 15:23:42.564 - [任务 9][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 15:23:42.607 - [任务 9][my_table] - Initial sync started 
[INFO ] 2024-05-17 15:23:42.612 - [任务 9][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 15:23:42.700 - [任务 9][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 15:23:42.700 - [任务 9][my_table] - Table [my_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-17 15:23:42.723 - [任务 9][my_table] - Query table 'my_table' counts: 4 
[INFO ] 2024-05-17 15:23:42.723 - [任务 9][my_table] - Initial sync completed 
[INFO ] 2024-05-17 15:23:42.725 - [任务 9][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 15:23:42.725 - [任务 9][my_table] - Initial sync completed 
[INFO ] 2024-05-17 15:23:42.906 - [任务 9][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FC00001CA00003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 15:23:42.909 - [任务 9][test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 15:23:43.024 - [任务 9][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 15:23:43.025 - [任务 9][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 15:23:43.026 - [任务 9][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[ERROR] 2024-05-17 15:23:43.555 - [任务 9][test] - Execute PDK method: TARGET_WRITE_RECORD, tableName: test <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test

<-- Simple Stack Trace -->
Caused by: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Stream load failed | Error: {
    "TxnId": 5194,
    "Label": "tapdata_562_test-d33ec607-ff35-4546-a660-e7c36365308d",
    "Comment": "",
    "TwoPhaseCommit": "false",
    "Status": "Fail",
    "Message": "[DATA_QUALITY_ERROR]too many filtered rows",
    "NumberTotalRows": 1,
    "NumberLoadedRows": 0,
    "NumberFilteredRows": 1,
    "NumberUnselectedRows": 0,
    "LoadBytes": 69,
    "LoadTimeMs": 19,
    "BeginTxnTimeMs": 0,
    "StreamLoadPutTimeMs": 4,
    "ReadDataTimeMs": 0,
    "WriteDataTimeMs": 13,
    "CommitAndPublishTimeMs": 0,
    "ErrorURL": "http://192.168.1.184:38040/api/_load_error_log?file=__shard_3/error_log_insert_stmt_e846da87853a09a3-dc5c73f5337eb6bd_e846da87853a09a3_dc5c73f5337eb6bd"
}

	io.tapdata.connector.doris.streamload.DorisStreamLoader.handlePreCommitResponse(DorisStreamLoader.java:195)
	io.tapdata.connector.doris.streamload.DorisStreamLoader.put(DorisStreamLoader.java:175)
	io.tapdata.connector.doris.streamload.DorisStreamLoader.flush(DorisStreamLoader.java:214)
	io.tapdata.connector.doris.streamload.DorisStreamLoader.writeRecord(DorisStreamLoader.java:101)
	io.tapdata.connector.doris.DorisConnector.writeRecord(DorisConnector.java:182)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:834)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:168)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:157)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:157)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$55(HazelcastTargetPdkDataNode.java:780)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:774)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$24(HazelcastTargetPdkDataNode.java:483)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:483)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:557)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:513)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:472)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:485)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:531)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.connector.doris.streamload.exception.DorisRuntimeException: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Call stream load error: Stream load failed | Error: {
    "TxnId": 5194,
    "Label": "tapdata_562_test-d33ec607-ff35-4546-a660-e7c36365308d",
    "Comment": "",
    "TwoPhaseCommit": "false",
    "Status": "Fail",
    "Message": "[DATA_QUALITY_ERROR]too many filtered rows",
    "NumberTotalRows": 1,
    "NumberLoadedRows": 0,
    "NumberFilteredRows": 1,
    "NumberUnselectedRows": 0,
    "LoadBytes": 69,
    "LoadTimeMs": 19,
    "BeginTxnTimeMs": 0,
    "StreamLoadPutTimeMs": 4,
    "ReadDataTimeMs": 0,
    "WriteDataTimeMs": 13,
    "CommitAndPublishTimeMs": 0,
    "ErrorURL": "http://192.168.1.184:38040/api/_load_error_log?file=__shard_3/error_log_insert_stmt_e846da87853a09a3-dc5c73f5337eb6bd_e846da87853a09a3_dc5c73f5337eb6bd"
}

	at io.tapdata.connector.doris.streamload.DorisStreamLoader.flush(DorisStreamLoader.java:224)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.writeRecord(DorisStreamLoader.java:101)
	at io.tapdata.connector.doris.DorisConnector.writeRecord(DorisConnector.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:828)
	... 24 more
Caused by: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Call stream load error: Stream load failed | Error: {
    "TxnId": 5194,
    "Label": "tapdata_562_test-d33ec607-ff35-4546-a660-e7c36365308d",
    "Comment": "",
    "TwoPhaseCommit": "false",
    "Status": "Fail",
    "Message": "[DATA_QUALITY_ERROR]too many filtered rows",
    "NumberTotalRows": 1,
    "NumberLoadedRows": 0,
    "NumberFilteredRows": 1,
    "NumberUnselectedRows": 0,
    "LoadBytes": 69,
    "LoadTimeMs": 19,
    "BeginTxnTimeMs": 0,
    "StreamLoadPutTimeMs": 4,
    "ReadDataTimeMs": 0,
    "WriteDataTimeMs": 13,
    "CommitAndPublishTimeMs": 0,
    "ErrorURL": "http://192.168.1.184:38040/api/_load_error_log?file=__shard_3/error_log_insert_stmt_e846da87853a09a3-dc5c73f5337eb6bd_e846da87853a09a3_dc5c73f5337eb6bd"
}

	at io.tapdata.connector.doris.streamload.DorisStreamLoader.put(DorisStreamLoader.java:179)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.flush(DorisStreamLoader.java:214)
	... 27 more
Caused by: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Stream load failed | Error: {
    "TxnId": 5194,
    "Label": "tapdata_562_test-d33ec607-ff35-4546-a660-e7c36365308d",
    "Comment": "",
    "TwoPhaseCommit": "false",
    "Status": "Fail",
    "Message": "[DATA_QUALITY_ERROR]too many filtered rows",
    "NumberTotalRows": 1,
    "NumberLoadedRows": 0,
    "NumberFilteredRows": 1,
    "NumberUnselectedRows": 0,
    "LoadBytes": 69,
    "LoadTimeMs": 19,
    "BeginTxnTimeMs": 0,
    "StreamLoadPutTimeMs": 4,
    "ReadDataTimeMs": 0,
    "WriteDataTimeMs": 13,
    "CommitAndPublishTimeMs": 0,
    "ErrorURL": "http://192.168.1.184:38040/api/_load_error_log?file=__shard_3/error_log_insert_stmt_e846da87853a09a3-dc5c73f5337eb6bd_e846da87853a09a3_dc5c73f5337eb6bd"
}

	at io.tapdata.connector.doris.streamload.DorisStreamLoader.handlePreCommitResponse(DorisStreamLoader.java:195)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.put(DorisStreamLoader.java:175)
	... 28 more

[INFO ] 2024-05-17 15:23:43.555 - [任务 9][test] - Job suspend in error handle 
[INFO ] 2024-05-17 15:23:43.995 - [任务 9] - Task [任务 9] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-05-17 15:23:44.201 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] running status set to false 
[INFO ] 2024-05-17 15:23:44.585 - [任务 9][my_table] - Incremental sync completed 
[INFO ] 2024-05-17 15:23:47.088 - [任务 9][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-0c79cb64-a3b6-4d48-8108-f73c5411eaf3 
[INFO ] 2024-05-17 15:23:47.088 - [任务 9][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-0c79cb64-a3b6-4d48-8108-f73c5411eaf3 
[INFO ] 2024-05-17 15:23:47.088 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] schema data cleaned 
[INFO ] 2024-05-17 15:23:47.091 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] monitor closed 
[INFO ] 2024-05-17 15:23:47.106 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] close complete, cost 3057 ms 
[INFO ] 2024-05-17 15:23:47.113 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] running status set to false 
[INFO ] 2024-05-17 15:23:47.150 - [任务 9][test] - PDK connector node stopped: HazelcastTargetPdkDataNode-64cbfe72-a9a7-4967-a1e0-99a7a41133a0 
[INFO ] 2024-05-17 15:23:47.152 - [任务 9][test] - PDK connector node released: HazelcastTargetPdkDataNode-64cbfe72-a9a7-4967-a1e0-99a7a41133a0 
[INFO ] 2024-05-17 15:23:47.152 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] schema data cleaned 
[INFO ] 2024-05-17 15:23:47.152 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] monitor closed 
[INFO ] 2024-05-17 15:23:47.360 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] close complete, cost 56 ms 
[INFO ] 2024-05-17 15:23:49.031 - [任务 9] - Task [任务 9] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-05-17 15:23:49.058 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 15:23:49.062 - [任务 9] - Stop task milestones: 664705e082e56d0d1f736854(任务 9)  
[INFO ] 2024-05-17 15:23:49.077 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-05-17 15:23:49.077 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 15:23:49.098 - [任务 9] - Remove memory task client succeed, task: 任务 9[664705e082e56d0d1f736854] 
[INFO ] 2024-05-17 15:23:49.099 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[664705e082e56d0d1f736854] 
[INFO ] 2024-05-17 15:25:16.567 - [任务 9] - Start task milestones: 664705e082e56d0d1f736854(任务 9) 
[INFO ] 2024-05-17 15:25:16.567 - [任务 9] - Task initialization... 
[INFO ] 2024-05-17 15:25:16.567 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 15:25:16.568 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 15:25:16.568 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] start preload schema,table counts: 1 
[INFO ] 2024-05-17 15:25:16.568 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] start preload schema,table counts: 1 
[INFO ] 2024-05-17 15:25:16.568 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] preload schema finished, cost 23 ms 
[INFO ] 2024-05-17 15:25:16.568 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] preload schema finished, cost 26 ms 
[INFO ] 2024-05-17 15:25:17.098 - [任务 9][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 15:25:17.100 - [任务 9][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 15:25:17.101 - [任务 9][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 15:25:17.119 - [任务 9][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FC000021600003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 15:25:17.119 - [任务 9][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 15:25:17.187 - [任务 9][my_table] - Initial sync started 
[INFO ] 2024-05-17 15:25:17.190 - [任务 9][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 15:25:17.250 - [任务 9][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 15:25:17.256 - [任务 9][my_table] - Table [my_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-17 15:25:17.289 - [任务 9][my_table] - Query table 'my_table' counts: 4 
[INFO ] 2024-05-17 15:25:17.289 - [任务 9][my_table] - Initial sync completed 
[INFO ] 2024-05-17 15:25:17.289 - [任务 9][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 15:25:17.290 - [任务 9][my_table] - Initial sync completed 
[INFO ] 2024-05-17 15:25:17.296 - [任务 9][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FC000021600003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 15:25:17.526 - [任务 9][test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 15:25:17.526 - [任务 9][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 15:25:17.581 - [任务 9][test] - The table test has already exist. 
[INFO ] 2024-05-17 15:25:17.640 - [任务 9][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 15:25:17.643 - [任务 9][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[ERROR] 2024-05-17 15:25:34.750 - [任务 9][test] - Execute PDK method: TARGET_WRITE_RECORD, tableName: test <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test

<-- Simple Stack Trace -->
Caused by: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Stream load failed | Error: {
    "TxnId": 5197,
    "Label": "tapdata_604_test-44739f1b-69f1-4193-b6bf-f3d997a0d1a3",
    "Comment": "",
    "TwoPhaseCommit": "false",
    "Status": "Fail",
    "Message": "[DATA_QUALITY_ERROR]too many filtered rows",
    "NumberTotalRows": 1,
    "NumberLoadedRows": 0,
    "NumberFilteredRows": 1,
    "NumberUnselectedRows": 0,
    "LoadBytes": 69,
    "LoadTimeMs": 27,
    "BeginTxnTimeMs": 1,
    "StreamLoadPutTimeMs": 4,
    "ReadDataTimeMs": 0,
    "WriteDataTimeMs": 21,
    "CommitAndPublishTimeMs": 0,
    "ErrorURL": "http://192.168.1.184:38040/api/_load_error_log?file=__shard_4/error_log_insert_stmt_1445a0a0fe7ef47e-584211e54ffdcb5_1445a0a0fe7ef47e_584211e54ffdcb5"
}

	io.tapdata.connector.doris.streamload.DorisStreamLoader.handlePreCommitResponse(DorisStreamLoader.java:195)
	io.tapdata.connector.doris.streamload.DorisStreamLoader.put(DorisStreamLoader.java:175)
	io.tapdata.connector.doris.streamload.DorisStreamLoader.flush(DorisStreamLoader.java:214)
	io.tapdata.connector.doris.streamload.DorisStreamLoader.writeRecord(DorisStreamLoader.java:101)
	io.tapdata.connector.doris.DorisConnector.writeRecord(DorisConnector.java:182)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:834)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:168)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:157)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:157)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$55(HazelcastTargetPdkDataNode.java:780)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:774)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$24(HazelcastTargetPdkDataNode.java:483)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:483)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:557)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:513)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:472)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:485)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:531)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.connector.doris.streamload.exception.DorisRuntimeException: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Call stream load error: Stream load failed | Error: {
    "TxnId": 5197,
    "Label": "tapdata_604_test-44739f1b-69f1-4193-b6bf-f3d997a0d1a3",
    "Comment": "",
    "TwoPhaseCommit": "false",
    "Status": "Fail",
    "Message": "[DATA_QUALITY_ERROR]too many filtered rows",
    "NumberTotalRows": 1,
    "NumberLoadedRows": 0,
    "NumberFilteredRows": 1,
    "NumberUnselectedRows": 0,
    "LoadBytes": 69,
    "LoadTimeMs": 27,
    "BeginTxnTimeMs": 1,
    "StreamLoadPutTimeMs": 4,
    "ReadDataTimeMs": 0,
    "WriteDataTimeMs": 21,
    "CommitAndPublishTimeMs": 0,
    "ErrorURL": "http://192.168.1.184:38040/api/_load_error_log?file=__shard_4/error_log_insert_stmt_1445a0a0fe7ef47e-584211e54ffdcb5_1445a0a0fe7ef47e_584211e54ffdcb5"
}

	at io.tapdata.connector.doris.streamload.DorisStreamLoader.flush(DorisStreamLoader.java:224)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.writeRecord(DorisStreamLoader.java:101)
	at io.tapdata.connector.doris.DorisConnector.writeRecord(DorisConnector.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:828)
	... 24 more
Caused by: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Call stream load error: Stream load failed | Error: {
    "TxnId": 5197,
    "Label": "tapdata_604_test-44739f1b-69f1-4193-b6bf-f3d997a0d1a3",
    "Comment": "",
    "TwoPhaseCommit": "false",
    "Status": "Fail",
    "Message": "[DATA_QUALITY_ERROR]too many filtered rows",
    "NumberTotalRows": 1,
    "NumberLoadedRows": 0,
    "NumberFilteredRows": 1,
    "NumberUnselectedRows": 0,
    "LoadBytes": 69,
    "LoadTimeMs": 27,
    "BeginTxnTimeMs": 1,
    "StreamLoadPutTimeMs": 4,
    "ReadDataTimeMs": 0,
    "WriteDataTimeMs": 21,
    "CommitAndPublishTimeMs": 0,
    "ErrorURL": "http://192.168.1.184:38040/api/_load_error_log?file=__shard_4/error_log_insert_stmt_1445a0a0fe7ef47e-584211e54ffdcb5_1445a0a0fe7ef47e_584211e54ffdcb5"
}

	at io.tapdata.connector.doris.streamload.DorisStreamLoader.put(DorisStreamLoader.java:179)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.flush(DorisStreamLoader.java:214)
	... 27 more
Caused by: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Stream load failed | Error: {
    "TxnId": 5197,
    "Label": "tapdata_604_test-44739f1b-69f1-4193-b6bf-f3d997a0d1a3",
    "Comment": "",
    "TwoPhaseCommit": "false",
    "Status": "Fail",
    "Message": "[DATA_QUALITY_ERROR]too many filtered rows",
    "NumberTotalRows": 1,
    "NumberLoadedRows": 0,
    "NumberFilteredRows": 1,
    "NumberUnselectedRows": 0,
    "LoadBytes": 69,
    "LoadTimeMs": 27,
    "BeginTxnTimeMs": 1,
    "StreamLoadPutTimeMs": 4,
    "ReadDataTimeMs": 0,
    "WriteDataTimeMs": 21,
    "CommitAndPublishTimeMs": 0,
    "ErrorURL": "http://192.168.1.184:38040/api/_load_error_log?file=__shard_4/error_log_insert_stmt_1445a0a0fe7ef47e-584211e54ffdcb5_1445a0a0fe7ef47e_584211e54ffdcb5"
}

	at io.tapdata.connector.doris.streamload.DorisStreamLoader.handlePreCommitResponse(DorisStreamLoader.java:195)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.put(DorisStreamLoader.java:175)
	... 28 more

[INFO ] 2024-05-17 15:25:34.970 - [任务 9][test] - Job suspend in error handle 
[INFO ] 2024-05-17 15:25:35.175 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] running status set to false 
[INFO ] 2024-05-17 15:25:35.788 - [任务 9][my_table] - Incremental sync completed 
[INFO ] 2024-05-17 15:25:38.123 - [任务 9][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-0c79cb64-a3b6-4d48-8108-f73c5411eaf3 
[INFO ] 2024-05-17 15:25:38.129 - [任务 9][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-0c79cb64-a3b6-4d48-8108-f73c5411eaf3 
[INFO ] 2024-05-17 15:25:38.130 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] schema data cleaned 
[INFO ] 2024-05-17 15:25:38.138 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] monitor closed 
[INFO ] 2024-05-17 15:25:38.140 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] close complete, cost 3076 ms 
[INFO ] 2024-05-17 15:25:38.140 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] running status set to false 
[INFO ] 2024-05-17 15:25:38.183 - [任务 9][test] - PDK connector node stopped: HazelcastTargetPdkDataNode-64cbfe72-a9a7-4967-a1e0-99a7a41133a0 
[INFO ] 2024-05-17 15:25:38.183 - [任务 9][test] - PDK connector node released: HazelcastTargetPdkDataNode-64cbfe72-a9a7-4967-a1e0-99a7a41133a0 
[INFO ] 2024-05-17 15:25:38.183 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] schema data cleaned 
[INFO ] 2024-05-17 15:25:38.390 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] monitor closed 
[INFO ] 2024-05-17 15:25:38.393 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] close complete, cost 46 ms 
[INFO ] 2024-05-17 15:25:39.616 - [任务 9] - Task [任务 9] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-05-17 15:25:39.679 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 15:25:39.680 - [任务 9] - Stop task milestones: 664705e082e56d0d1f736854(任务 9)  
[INFO ] 2024-05-17 15:25:39.694 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-05-17 15:25:39.695 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 15:25:39.717 - [任务 9] - Remove memory task client succeed, task: 任务 9[664705e082e56d0d1f736854] 
[INFO ] 2024-05-17 15:25:39.719 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[664705e082e56d0d1f736854] 
[INFO ] 2024-05-17 15:25:50.272 - [任务 9] - Start task milestones: 664705e082e56d0d1f736854(任务 9) 
[INFO ] 2024-05-17 15:25:50.300 - [任务 9] - Task initialization... 
[INFO ] 2024-05-17 15:25:50.391 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 15:25:50.391 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 15:25:50.423 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] start preload schema,table counts: 1 
[INFO ] 2024-05-17 15:25:50.423 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] start preload schema,table counts: 1 
[INFO ] 2024-05-17 15:25:50.446 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] preload schema finished, cost 20 ms 
[INFO ] 2024-05-17 15:25:50.446 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] preload schema finished, cost 19 ms 
[INFO ] 2024-05-17 15:25:51.241 - [任务 9][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 15:25:51.241 - [任务 9][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 15:25:51.241 - [任务 9][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 15:25:51.261 - [任务 9][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FC000023200003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 15:25:51.261 - [任务 9][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 15:25:51.308 - [任务 9][my_table] - Initial sync started 
[INFO ] 2024-05-17 15:25:51.316 - [任务 9][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 15:25:51.317 - [任务 9][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 15:25:51.425 - [任务 9][my_table] - Table [my_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-17 15:25:51.425 - [任务 9][my_table] - Query table 'my_table' counts: 4 
[INFO ] 2024-05-17 15:25:51.426 - [任务 9][my_table] - Initial sync completed 
[INFO ] 2024-05-17 15:25:51.426 - [任务 9][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 15:25:51.426 - [任务 9][my_table] - Initial sync completed 
[INFO ] 2024-05-17 15:25:51.432 - [任务 9][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FC000023200003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 15:25:51.627 - [任务 9][test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 15:25:51.627 - [任务 9][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 15:25:51.753 - [任务 9][test] - The table test has already exist. 
[INFO ] 2024-05-17 15:25:51.754 - [任务 9][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 15:25:51.754 - [任务 9][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[ERROR] 2024-05-17 15:26:46.232 - [任务 9][test] - Execute PDK method: TARGET_WRITE_RECORD, tableName: test <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test

<-- Simple Stack Trace -->
Caused by: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Stream load failed | Error: {
    "TxnId": 5198,
    "Label": "tapdata_660_test-f957fdd9-cb4a-4971-ac32-007ba1f5a6ef",
    "Comment": "",
    "TwoPhaseCommit": "false",
    "Status": "Fail",
    "Message": "[DATA_QUALITY_ERROR]too many filtered rows",
    "NumberTotalRows": 1,
    "NumberLoadedRows": 0,
    "NumberFilteredRows": 1,
    "NumberUnselectedRows": 0,
    "LoadBytes": 69,
    "LoadTimeMs": 36,
    "BeginTxnTimeMs": 1,
    "StreamLoadPutTimeMs": 13,
    "ReadDataTimeMs": 0,
    "WriteDataTimeMs": 21,
    "CommitAndPublishTimeMs": 0,
    "ErrorURL": "http://192.168.1.184:38040/api/_load_error_log?file=__shard_5/error_log_insert_stmt_264ae05cfc43700c-cb04b8bd101e8c8f_264ae05cfc43700c_cb04b8bd101e8c8f"
}

	io.tapdata.connector.doris.streamload.DorisStreamLoader.handlePreCommitResponse(DorisStreamLoader.java:195)
	io.tapdata.connector.doris.streamload.DorisStreamLoader.put(DorisStreamLoader.java:175)
	io.tapdata.connector.doris.streamload.DorisStreamLoader.flush(DorisStreamLoader.java:214)
	io.tapdata.connector.doris.streamload.DorisStreamLoader.writeRecord(DorisStreamLoader.java:101)
	io.tapdata.connector.doris.DorisConnector.writeRecord(DorisConnector.java:182)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:834)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:168)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:157)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:157)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$55(HazelcastTargetPdkDataNode.java:780)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:774)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$24(HazelcastTargetPdkDataNode.java:483)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:483)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:557)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:513)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:472)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:485)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:531)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.connector.doris.streamload.exception.DorisRuntimeException: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Call stream load error: Stream load failed | Error: {
    "TxnId": 5198,
    "Label": "tapdata_660_test-f957fdd9-cb4a-4971-ac32-007ba1f5a6ef",
    "Comment": "",
    "TwoPhaseCommit": "false",
    "Status": "Fail",
    "Message": "[DATA_QUALITY_ERROR]too many filtered rows",
    "NumberTotalRows": 1,
    "NumberLoadedRows": 0,
    "NumberFilteredRows": 1,
    "NumberUnselectedRows": 0,
    "LoadBytes": 69,
    "LoadTimeMs": 36,
    "BeginTxnTimeMs": 1,
    "StreamLoadPutTimeMs": 13,
    "ReadDataTimeMs": 0,
    "WriteDataTimeMs": 21,
    "CommitAndPublishTimeMs": 0,
    "ErrorURL": "http://192.168.1.184:38040/api/_load_error_log?file=__shard_5/error_log_insert_stmt_264ae05cfc43700c-cb04b8bd101e8c8f_264ae05cfc43700c_cb04b8bd101e8c8f"
}

	at io.tapdata.connector.doris.streamload.DorisStreamLoader.flush(DorisStreamLoader.java:224)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.writeRecord(DorisStreamLoader.java:101)
	at io.tapdata.connector.doris.DorisConnector.writeRecord(DorisConnector.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:828)
	... 24 more
Caused by: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Call stream load error: Stream load failed | Error: {
    "TxnId": 5198,
    "Label": "tapdata_660_test-f957fdd9-cb4a-4971-ac32-007ba1f5a6ef",
    "Comment": "",
    "TwoPhaseCommit": "false",
    "Status": "Fail",
    "Message": "[DATA_QUALITY_ERROR]too many filtered rows",
    "NumberTotalRows": 1,
    "NumberLoadedRows": 0,
    "NumberFilteredRows": 1,
    "NumberUnselectedRows": 0,
    "LoadBytes": 69,
    "LoadTimeMs": 36,
    "BeginTxnTimeMs": 1,
    "StreamLoadPutTimeMs": 13,
    "ReadDataTimeMs": 0,
    "WriteDataTimeMs": 21,
    "CommitAndPublishTimeMs": 0,
    "ErrorURL": "http://192.168.1.184:38040/api/_load_error_log?file=__shard_5/error_log_insert_stmt_264ae05cfc43700c-cb04b8bd101e8c8f_264ae05cfc43700c_cb04b8bd101e8c8f"
}

	at io.tapdata.connector.doris.streamload.DorisStreamLoader.put(DorisStreamLoader.java:179)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.flush(DorisStreamLoader.java:214)
	... 27 more
Caused by: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Stream load failed | Error: {
    "TxnId": 5198,
    "Label": "tapdata_660_test-f957fdd9-cb4a-4971-ac32-007ba1f5a6ef",
    "Comment": "",
    "TwoPhaseCommit": "false",
    "Status": "Fail",
    "Message": "[DATA_QUALITY_ERROR]too many filtered rows",
    "NumberTotalRows": 1,
    "NumberLoadedRows": 0,
    "NumberFilteredRows": 1,
    "NumberUnselectedRows": 0,
    "LoadBytes": 69,
    "LoadTimeMs": 36,
    "BeginTxnTimeMs": 1,
    "StreamLoadPutTimeMs": 13,
    "ReadDataTimeMs": 0,
    "WriteDataTimeMs": 21,
    "CommitAndPublishTimeMs": 0,
    "ErrorURL": "http://192.168.1.184:38040/api/_load_error_log?file=__shard_5/error_log_insert_stmt_264ae05cfc43700c-cb04b8bd101e8c8f_264ae05cfc43700c_cb04b8bd101e8c8f"
}

	at io.tapdata.connector.doris.streamload.DorisStreamLoader.handlePreCommitResponse(DorisStreamLoader.java:195)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.put(DorisStreamLoader.java:175)
	... 28 more

[INFO ] 2024-05-17 15:26:46.232 - [任务 9][test] - Job suspend in error handle 
[INFO ] 2024-05-17 15:26:46.430 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] running status set to false 
[INFO ] 2024-05-17 15:26:47.039 - [任务 9][my_table] - Incremental sync completed 
[INFO ] 2024-05-17 15:26:49.461 - [任务 9][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-0c79cb64-a3b6-4d48-8108-f73c5411eaf3 
[INFO ] 2024-05-17 15:26:49.470 - [任务 9][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-0c79cb64-a3b6-4d48-8108-f73c5411eaf3 
[INFO ] 2024-05-17 15:26:49.470 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] schema data cleaned 
[INFO ] 2024-05-17 15:26:49.470 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] monitor closed 
[INFO ] 2024-05-17 15:26:49.471 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] close complete, cost 3049 ms 
[INFO ] 2024-05-17 15:26:49.471 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] running status set to false 
[INFO ] 2024-05-17 15:26:49.520 - [任务 9][test] - PDK connector node stopped: HazelcastTargetPdkDataNode-64cbfe72-a9a7-4967-a1e0-99a7a41133a0 
[INFO ] 2024-05-17 15:26:49.520 - [任务 9][test] - PDK connector node released: HazelcastTargetPdkDataNode-64cbfe72-a9a7-4967-a1e0-99a7a41133a0 
[INFO ] 2024-05-17 15:26:49.521 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] schema data cleaned 
[INFO ] 2024-05-17 15:26:49.521 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] monitor closed 
[INFO ] 2024-05-17 15:26:49.524 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] close complete, cost 54 ms 
[INFO ] 2024-05-17 15:26:50.964 - [任务 9] - Task [任务 9] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-05-17 15:26:50.976 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 15:26:50.977 - [任务 9] - Stop task milestones: 664705e082e56d0d1f736854(任务 9)  
[INFO ] 2024-05-17 15:26:50.996 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-05-17 15:26:50.997 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 15:26:51.019 - [任务 9] - Remove memory task client succeed, task: 任务 9[664705e082e56d0d1f736854] 
[INFO ] 2024-05-17 15:26:51.020 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[664705e082e56d0d1f736854] 
[INFO ] 2024-05-17 15:28:17.830 - [任务 9] - Start task milestones: 664705e082e56d0d1f736854(任务 9) 
[INFO ] 2024-05-17 15:28:17.840 - [任务 9] - Task initialization... 
[INFO ] 2024-05-17 15:28:17.840 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 15:28:17.904 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 15:28:17.942 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] start preload schema,table counts: 1 
[INFO ] 2024-05-17 15:28:17.942 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] start preload schema,table counts: 1 
[INFO ] 2024-05-17 15:28:17.978 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] preload schema finished, cost 36 ms 
[INFO ] 2024-05-17 15:28:17.978 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] preload schema finished, cost 36 ms 
[INFO ] 2024-05-17 15:28:18.869 - [任务 9][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 15:28:18.869 - [任务 9][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 15:28:18.869 - [任务 9][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 15:28:18.924 - [任务 9][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FC00002A700003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 15:28:18.925 - [任务 9][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 15:28:18.983 - [任务 9][my_table] - Initial sync started 
[INFO ] 2024-05-17 15:28:18.988 - [任务 9][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 15:28:19.102 - [任务 9][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 15:28:19.104 - [任务 9][my_table] - Table [my_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-17 15:28:19.152 - [任务 9][my_table] - Query table 'my_table' counts: 4 
[INFO ] 2024-05-17 15:28:19.153 - [任务 9][my_table] - Initial sync completed 
[INFO ] 2024-05-17 15:28:19.153 - [任务 9][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 15:28:19.153 - [任务 9][my_table] - Initial sync completed 
[INFO ] 2024-05-17 15:28:19.162 - [任务 9][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FC00002A700003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 15:28:19.319 - [任务 9][test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 15:28:19.319 - [任务 9][test] - The table test has already exist. 
[INFO ] 2024-05-17 15:28:19.520 - [任务 9][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 15:28:19.540 - [任务 9][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 15:28:19.540 - [任务 9][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[INFO ] 2024-05-17 15:29:11.708 - [任务 9] - Stop task milestones: 664705e082e56d0d1f736854(任务 9)  
[INFO ] 2024-05-17 15:29:12.019 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] running status set to false 
[INFO ] 2024-05-17 15:29:15.060 - [任务 9][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-0c79cb64-a3b6-4d48-8108-f73c5411eaf3 
[INFO ] 2024-05-17 15:29:15.060 - [任务 9][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-0c79cb64-a3b6-4d48-8108-f73c5411eaf3 
[INFO ] 2024-05-17 15:29:15.060 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] schema data cleaned 
[INFO ] 2024-05-17 15:29:15.061 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] monitor closed 
[INFO ] 2024-05-17 15:29:15.063 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] close complete, cost 3054 ms 
[INFO ] 2024-05-17 15:29:15.063 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] running status set to false 
[INFO ] 2024-05-17 15:29:15.146 - [任务 9][test] - PDK connector node stopped: HazelcastTargetPdkDataNode-64cbfe72-a9a7-4967-a1e0-99a7a41133a0 
[INFO ] 2024-05-17 15:29:15.147 - [任务 9][test] - PDK connector node released: HazelcastTargetPdkDataNode-64cbfe72-a9a7-4967-a1e0-99a7a41133a0 
[INFO ] 2024-05-17 15:29:15.147 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] schema data cleaned 
[INFO ] 2024-05-17 15:29:15.147 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] monitor closed 
[INFO ] 2024-05-17 15:29:15.354 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] close complete, cost 84 ms 
[INFO ] 2024-05-17 15:29:16.149 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 15:29:16.149 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-05-17 15:29:16.149 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 15:29:16.208 - [任务 9] - Remove memory task client succeed, task: 任务 9[664705e082e56d0d1f736854] 
[INFO ] 2024-05-17 15:29:16.208 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[664705e082e56d0d1f736854] 
[INFO ] 2024-05-17 15:29:31.045 - [任务 9] - Start task milestones: 664705e082e56d0d1f736854(任务 9) 
[INFO ] 2024-05-17 15:29:31.075 - [任务 9] - Task initialization... 
[INFO ] 2024-05-17 15:29:31.076 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 15:29:31.209 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 15:29:31.209 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] start preload schema,table counts: 1 
[INFO ] 2024-05-17 15:29:31.209 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] start preload schema,table counts: 1 
[INFO ] 2024-05-17 15:29:31.222 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] preload schema finished, cost 12 ms 
[INFO ] 2024-05-17 15:29:31.222 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] preload schema finished, cost 13 ms 
[INFO ] 2024-05-17 15:29:32.198 - [任务 9][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 15:29:32.199 - [任务 9][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 15:29:32.199 - [任务 9][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 15:29:32.227 - [任务 9][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FC00002E400003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 15:29:32.233 - [任务 9][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 15:29:32.233 - [任务 9][test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 15:29:32.305 - [任务 9][my_table] - Initial sync started 
[INFO ] 2024-05-17 15:29:32.306 - [任务 9][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 15:29:32.361 - [任务 9][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 15:29:32.361 - [任务 9][my_table] - Table [my_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-17 15:29:32.395 - [任务 9][my_table] - Query table 'my_table' counts: 4 
[INFO ] 2024-05-17 15:29:32.395 - [任务 9][my_table] - Initial sync completed 
[INFO ] 2024-05-17 15:29:32.395 - [任务 9][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 15:29:32.395 - [任务 9][my_table] - Initial sync completed 
[INFO ] 2024-05-17 15:29:32.600 - [任务 9][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FC00002E400003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 15:29:32.600 - [任务 9][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 15:29:32.704 - [任务 9][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 15:29:32.706 - [任务 9][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[INFO ] 2024-05-17 15:38:19.929 - [任务 9] - Stop task milestones: 664705e082e56d0d1f736854(任务 9)  
[INFO ] 2024-05-17 15:38:19.993 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] running status set to false 
[INFO ] 2024-05-17 15:38:23.044 - [任务 9][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-0c79cb64-a3b6-4d48-8108-f73c5411eaf3 
[INFO ] 2024-05-17 15:38:23.044 - [任务 9][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-0c79cb64-a3b6-4d48-8108-f73c5411eaf3 
[INFO ] 2024-05-17 15:38:23.046 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] schema data cleaned 
[INFO ] 2024-05-17 15:38:23.047 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] monitor closed 
[INFO ] 2024-05-17 15:38:23.049 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] close complete, cost 3063 ms 
[INFO ] 2024-05-17 15:38:23.049 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] running status set to false 
[INFO ] 2024-05-17 15:38:23.119 - [任务 9][test] - PDK connector node stopped: HazelcastTargetPdkDataNode-64cbfe72-a9a7-4967-a1e0-99a7a41133a0 
[INFO ] 2024-05-17 15:38:23.119 - [任务 9][test] - PDK connector node released: HazelcastTargetPdkDataNode-64cbfe72-a9a7-4967-a1e0-99a7a41133a0 
[INFO ] 2024-05-17 15:38:23.120 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] schema data cleaned 
[INFO ] 2024-05-17 15:38:23.120 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] monitor closed 
[INFO ] 2024-05-17 15:38:23.322 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] close complete, cost 72 ms 
[INFO ] 2024-05-17 15:38:24.006 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 15:38:24.006 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-05-17 15:38:24.006 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 15:38:24.035 - [任务 9] - Remove memory task client succeed, task: 任务 9[664705e082e56d0d1f736854] 
[INFO ] 2024-05-17 15:38:24.035 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[664705e082e56d0d1f736854] 
[INFO ] 2024-05-17 15:49:33.041 - [任务 9] - Task initialization... 
[INFO ] 2024-05-17 15:49:33.043 - [任务 9] - Start task milestones: 664705e082e56d0d1f736854(任务 9) 
[INFO ] 2024-05-17 15:49:33.043 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 15:49:33.044 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 15:49:33.368 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] start preload schema,table counts: 1 
[INFO ] 2024-05-17 15:49:33.370 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] start preload schema,table counts: 1 
[INFO ] 2024-05-17 15:49:33.448 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] preload schema finished, cost 73 ms 
[INFO ] 2024-05-17 15:49:33.450 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] preload schema finished, cost 74 ms 
[INFO ] 2024-05-17 15:49:34.872 - [任务 9][test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 15:49:34.897 - [任务 9][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 15:49:34.897 - [任务 9][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 15:49:34.898 - [任务 9][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 15:49:34.975 - [任务 9][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FC000058500001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 15:49:34.980 - [任务 9][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 15:49:35.165 - [任务 9][my_table] - Initial sync started 
[INFO ] 2024-05-17 15:49:35.177 - [任务 9][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 15:49:35.189 - [任务 9][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 15:49:35.397 - [任务 9][my_table] - Table [my_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-17 15:49:35.464 - [任务 9][my_table] - Query table 'my_table' counts: 6 
[INFO ] 2024-05-17 15:49:35.464 - [任务 9][my_table] - Initial sync completed 
[INFO ] 2024-05-17 15:49:35.467 - [任务 9][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 15:49:35.468 - [任务 9][my_table] - Initial sync completed 
[INFO ] 2024-05-17 15:49:35.673 - [任务 9][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FC000058500001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 15:49:35.869 - [任务 9][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 15:49:35.874 - [任务 9][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 15:49:36.078 - [任务 9][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[INFO ] 2024-05-17 15:50:19.445 - [任务 9] - Stop task milestones: 664705e082e56d0d1f736854(任务 9)  
[INFO ] 2024-05-17 15:50:19.853 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] running status set to false 
[INFO ] 2024-05-17 15:50:22.769 - [任务 9][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-0c79cb64-a3b6-4d48-8108-f73c5411eaf3 
[INFO ] 2024-05-17 15:50:22.769 - [任务 9][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-0c79cb64-a3b6-4d48-8108-f73c5411eaf3 
[INFO ] 2024-05-17 15:50:22.772 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] schema data cleaned 
[INFO ] 2024-05-17 15:50:22.772 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] monitor closed 
[INFO ] 2024-05-17 15:50:22.780 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] close complete, cost 3096 ms 
[INFO ] 2024-05-17 15:50:22.780 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] running status set to false 
[INFO ] 2024-05-17 15:50:22.866 - [任务 9][test] - PDK connector node stopped: HazelcastTargetPdkDataNode-64cbfe72-a9a7-4967-a1e0-99a7a41133a0 
[INFO ] 2024-05-17 15:50:22.867 - [任务 9][test] - PDK connector node released: HazelcastTargetPdkDataNode-64cbfe72-a9a7-4967-a1e0-99a7a41133a0 
[INFO ] 2024-05-17 15:50:22.867 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] schema data cleaned 
[INFO ] 2024-05-17 15:50:22.871 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] monitor closed 
[INFO ] 2024-05-17 15:50:22.871 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] close complete, cost 88 ms 
[INFO ] 2024-05-17 15:50:23.846 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 15:50:23.846 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-05-17 15:50:23.847 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 15:50:23.906 - [任务 9] - Remove memory task client succeed, task: 任务 9[664705e082e56d0d1f736854] 
[INFO ] 2024-05-17 15:50:24.109 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[664705e082e56d0d1f736854] 
[INFO ] 2024-05-17 16:12:17.533 - [任务 9] - Task initialization... 
[INFO ] 2024-05-17 16:12:17.535 - [任务 9] - Start task milestones: 664705e082e56d0d1f736854(任务 9) 
[INFO ] 2024-05-17 16:12:17.668 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 16:12:17.669 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 16:12:17.743 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] start preload schema,table counts: 1 
[INFO ] 2024-05-17 16:12:17.744 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] start preload schema,table counts: 1 
[INFO ] 2024-05-17 16:12:17.754 - [任务 9][增强JS] - Node 增强JS[c6525e85-e559-4604-b393-733bf55ecbcd] start preload schema,table counts: 1 
[INFO ] 2024-05-17 16:12:17.778 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] preload schema finished, cost 35 ms 
[INFO ] 2024-05-17 16:12:17.779 - [任务 9][增强JS] - Node 增强JS[c6525e85-e559-4604-b393-733bf55ecbcd] preload schema finished, cost 22 ms 
[INFO ] 2024-05-17 16:12:17.779 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] preload schema finished, cost 34 ms 
[INFO ] 2024-05-17 16:12:18.142 - [任务 9][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 16:12:18.143 - [任务 9][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 16:12:18.143 - [任务 9][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 16:12:18.199 - [任务 9][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FC000091D80003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 16:12:18.199 - [任务 9][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 16:12:18.263 - [任务 9][my_table] - Initial sync started 
[INFO ] 2024-05-17 16:12:18.268 - [任务 9][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 16:12:18.271 - [任务 9][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 16:12:18.810 - [任务 9][my_table] - Table [my_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-17 16:12:18.885 - [任务 9][my_table] - Query table 'my_table' counts: 6 
[INFO ] 2024-05-17 16:12:18.887 - [任务 9][my_table] - Initial sync completed 
[INFO ] 2024-05-17 16:12:18.887 - [任务 9][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 16:12:18.887 - [任务 9][my_table] - Initial sync completed 
[INFO ] 2024-05-17 16:12:18.889 - [任务 9][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FC000091D80003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 16:12:19.092 - [任务 9][test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 16:12:19.549 - [任务 9][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 16:12:19.707 - [任务 9][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 16:12:19.707 - [任务 9][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[INFO ] 2024-05-17 16:21:36.475 - [任务 9] - Stop task milestones: 664705e082e56d0d1f736854(任务 9)  
[INFO ] 2024-05-17 16:21:36.477 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] running status set to false 
[INFO ] 2024-05-17 16:21:39.052 - [任务 9][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-0c79cb64-a3b6-4d48-8108-f73c5411eaf3 
[INFO ] 2024-05-17 16:21:39.053 - [任务 9][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-0c79cb64-a3b6-4d48-8108-f73c5411eaf3 
[INFO ] 2024-05-17 16:21:39.054 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] schema data cleaned 
[INFO ] 2024-05-17 16:21:39.056 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] monitor closed 
[INFO ] 2024-05-17 16:21:39.057 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] close complete, cost 3025 ms 
[INFO ] 2024-05-17 16:21:39.058 - [任务 9][增强JS] - Node 增强JS[c6525e85-e559-4604-b393-733bf55ecbcd] running status set to false 
[INFO ] 2024-05-17 16:21:39.071 - [任务 9][增强JS] - PDK connector node stopped: ScriptExecutor-SourceSqlServer-c670e1d7-b4b6-4d5a-a475-79fa09c0b219 
[INFO ] 2024-05-17 16:21:39.071 - [任务 9][增强JS] - PDK connector node released: ScriptExecutor-SourceSqlServer-c670e1d7-b4b6-4d5a-a475-79fa09c0b219 
[INFO ] 2024-05-17 16:21:39.071 - [任务 9][增强JS] - [ScriptExecutorsManager-664705e082e56d0d1f736854-c6525e85-e559-4604-b393-733bf55ecbcd-6645e5a066673e2fff77dc07] schema data cleaned 
[INFO ] 2024-05-17 16:21:39.091 - [任务 9][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDoris-108c85d1-e99d-421f-823b-a563fdf27bcc 
[INFO ] 2024-05-17 16:21:39.091 - [任务 9][增强JS] - PDK connector node released: ScriptExecutor-SourceDoris-108c85d1-e99d-421f-823b-a563fdf27bcc 
[INFO ] 2024-05-17 16:21:39.091 - [任务 9][增强JS] - [ScriptExecutorsManager-664705e082e56d0d1f736854-c6525e85-e559-4604-b393-733bf55ecbcd-664184e4e7fea472f196681a] schema data cleaned 
[INFO ] 2024-05-17 16:21:39.103 - [任务 9][增强JS] - Node 增强JS[c6525e85-e559-4604-b393-733bf55ecbcd] schema data cleaned 
[INFO ] 2024-05-17 16:21:39.103 - [任务 9][增强JS] - Node 增强JS[c6525e85-e559-4604-b393-733bf55ecbcd] monitor closed 
[INFO ] 2024-05-17 16:21:39.103 - [任务 9][增强JS] - Node 增强JS[c6525e85-e559-4604-b393-733bf55ecbcd] close complete, cost 47 ms 
[INFO ] 2024-05-17 16:21:39.104 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] running status set to false 
[INFO ] 2024-05-17 16:21:39.131 - [任务 9][test] - PDK connector node stopped: HazelcastTargetPdkDataNode-64cbfe72-a9a7-4967-a1e0-99a7a41133a0 
[INFO ] 2024-05-17 16:21:39.132 - [任务 9][test] - PDK connector node released: HazelcastTargetPdkDataNode-64cbfe72-a9a7-4967-a1e0-99a7a41133a0 
[INFO ] 2024-05-17 16:21:39.132 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] schema data cleaned 
[INFO ] 2024-05-17 16:21:39.132 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] monitor closed 
[INFO ] 2024-05-17 16:21:39.132 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] close complete, cost 27 ms 
[INFO ] 2024-05-17 16:21:40.582 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 16:21:40.582 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-05-17 16:21:40.582 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 16:21:40.619 - [任务 9] - Remove memory task client succeed, task: 任务 9[664705e082e56d0d1f736854] 
[INFO ] 2024-05-17 16:21:40.621 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[664705e082e56d0d1f736854] 
[INFO ] 2024-05-17 16:44:24.945 - [任务 9] - Task initialization... 
[INFO ] 2024-05-17 16:44:25.008 - [任务 9] - Start task milestones: 664705e082e56d0d1f736854(任务 9) 
[INFO ] 2024-05-17 16:44:25.008 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 16:44:25.213 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 16:44:25.261 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] start preload schema,table counts: 1 
[INFO ] 2024-05-17 16:44:25.265 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] start preload schema,table counts: 1 
[INFO ] 2024-05-17 16:44:25.265 - [任务 9][增强JS] - Node 增强JS[c6525e85-e559-4604-b393-733bf55ecbcd] start preload schema,table counts: 1 
[INFO ] 2024-05-17 16:44:25.299 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] preload schema finished, cost 38 ms 
[INFO ] 2024-05-17 16:44:25.299 - [任务 9][增强JS] - Node 增强JS[c6525e85-e559-4604-b393-733bf55ecbcd] preload schema finished, cost 37 ms 
[INFO ] 2024-05-17 16:44:25.299 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] preload schema finished, cost 38 ms 
[INFO ] 2024-05-17 16:44:26.031 - [任务 9][my_table] - Source node "my_table" read batch size: 100 
[INFO ] 2024-05-17 16:44:26.035 - [任务 9][my_table] - Source node "my_table" event queue capacity: 200 
[INFO ] 2024-05-17 16:44:26.035 - [任务 9][my_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 16:44:26.059 - [任务 9][my_table] - batch offset found: {},stream offset found: {"currentStartLSN":"000032FC0000F4180003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 16:44:26.059 - [任务 9][my_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 16:44:26.126 - [任务 9][my_table] - Initial sync started 
[INFO ] 2024-05-17 16:44:26.126 - [任务 9][my_table] - Starting batch read, table name: my_table, offset: null 
[INFO ] 2024-05-17 16:44:26.191 - [任务 9][my_table] - Table my_table is going to be initial synced 
[INFO ] 2024-05-17 16:44:26.192 - [任务 9][my_table] - Table [my_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-17 16:44:26.221 - [任务 9][my_table] - Query table 'my_table' counts: 6 
[INFO ] 2024-05-17 16:44:26.224 - [任务 9][my_table] - Initial sync completed 
[INFO ] 2024-05-17 16:44:26.224 - [任务 9][my_table] - Incremental sync starting... 
[INFO ] 2024-05-17 16:44:26.224 - [任务 9][my_table] - Initial sync completed 
[INFO ] 2024-05-17 16:44:26.235 - [任务 9][my_table] - Starting stream read, table list: [my_table], offset: {"currentStartLSN":"000032FC0000F4180003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-05-17 16:44:26.305 - [任务 9][增强JS] - 000000000000EA9A 
[INFO ] 2024-05-17 16:44:26.306 - [任务 9][增强JS] - 000000000000EA9B 
[INFO ] 2024-05-17 16:44:26.311 - [任务 9][增强JS] - 000000000000EA9C 
[INFO ] 2024-05-17 16:44:26.314 - [任务 9][增强JS] - 000000000000EA9D 
[INFO ] 2024-05-17 16:44:26.320 - [任务 9][增强JS] - 000000000000EA9E 
[INFO ] 2024-05-17 16:44:26.321 - [任务 9][增强JS] - 000000000000EA9F 
[INFO ] 2024-05-17 16:44:26.523 - [任务 9][test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-17 16:44:26.523 - [任务 9][my_table] - opened cdc tables: [_tapdata_heartbeat_table, Category, my_table, MyTable, a_test, test001_dummy_test, TEST_LENGTH, table_name, orderitem, Category1, test8, test9, test7, testTable, orders, Supplier, TEST_DDL_001] 
[INFO ] 2024-05-17 16:44:26.647 - [任务 9][my_table] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-05-17 16:44:26.648 - [任务 9][my_table] - Connector SQL Server incremental start succeed, tables: [my_table], data change syncing 
[INFO ] 2024-05-17 16:50:26.027 - [任务 9] - Stop task milestones: 664705e082e56d0d1f736854(任务 9)  
[INFO ] 2024-05-17 16:50:26.229 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] running status set to false 
[INFO ] 2024-05-17 16:50:29.089 - [任务 9][my_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-0c79cb64-a3b6-4d48-8108-f73c5411eaf3 
[INFO ] 2024-05-17 16:50:29.089 - [任务 9][my_table] - PDK connector node released: HazelcastSourcePdkDataNode-0c79cb64-a3b6-4d48-8108-f73c5411eaf3 
[INFO ] 2024-05-17 16:50:29.089 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] schema data cleaned 
[INFO ] 2024-05-17 16:50:29.089 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] monitor closed 
[INFO ] 2024-05-17 16:50:29.090 - [任务 9][my_table] - Node my_table[0c79cb64-a3b6-4d48-8108-f73c5411eaf3] close complete, cost 3027 ms 
[INFO ] 2024-05-17 16:50:29.092 - [任务 9][增强JS] - Node 增强JS[c6525e85-e559-4604-b393-733bf55ecbcd] running status set to false 
[INFO ] 2024-05-17 16:50:29.098 - [任务 9][增强JS] - PDK connector node stopped: ScriptExecutor-SourceSqlServer-66a58796-a760-43b6-a108-a77f818dc386 
[INFO ] 2024-05-17 16:50:29.098 - [任务 9][增强JS] - PDK connector node released: ScriptExecutor-SourceSqlServer-66a58796-a760-43b6-a108-a77f818dc386 
[INFO ] 2024-05-17 16:50:29.098 - [任务 9][增强JS] - [ScriptExecutorsManager-664705e082e56d0d1f736854-c6525e85-e559-4604-b393-733bf55ecbcd-6645e5a066673e2fff77dc07] schema data cleaned 
[INFO ] 2024-05-17 16:50:29.112 - [任务 9][增强JS] - PDK connector node stopped: ScriptExecutor-SourceDoris-8e97d13f-0f66-4e0a-95bc-ac91acbe5ab6 
[INFO ] 2024-05-17 16:50:29.113 - [任务 9][增强JS] - PDK connector node released: ScriptExecutor-SourceDoris-8e97d13f-0f66-4e0a-95bc-ac91acbe5ab6 
[INFO ] 2024-05-17 16:50:29.113 - [任务 9][增强JS] - [ScriptExecutorsManager-664705e082e56d0d1f736854-c6525e85-e559-4604-b393-733bf55ecbcd-664184e4e7fea472f196681a] schema data cleaned 
[INFO ] 2024-05-17 16:50:29.123 - [任务 9][增强JS] - Node 增强JS[c6525e85-e559-4604-b393-733bf55ecbcd] schema data cleaned 
[INFO ] 2024-05-17 16:50:29.124 - [任务 9][增强JS] - Node 增强JS[c6525e85-e559-4604-b393-733bf55ecbcd] monitor closed 
[INFO ] 2024-05-17 16:50:29.125 - [任务 9][增强JS] - Node 增强JS[c6525e85-e559-4604-b393-733bf55ecbcd] close complete, cost 34 ms 
[INFO ] 2024-05-17 16:50:29.125 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] running status set to false 
[INFO ] 2024-05-17 16:50:29.150 - [任务 9][test] - PDK connector node stopped: HazelcastTargetPdkDataNode-64cbfe72-a9a7-4967-a1e0-99a7a41133a0 
[INFO ] 2024-05-17 16:50:29.152 - [任务 9][test] - PDK connector node released: HazelcastTargetPdkDataNode-64cbfe72-a9a7-4967-a1e0-99a7a41133a0 
[INFO ] 2024-05-17 16:50:29.152 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] schema data cleaned 
[INFO ] 2024-05-17 16:50:29.152 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] monitor closed 
[INFO ] 2024-05-17 16:50:29.152 - [任务 9][test] - Node test[64cbfe72-a9a7-4967-a1e0-99a7a41133a0] close complete, cost 27 ms 
[INFO ] 2024-05-17 16:50:32.122 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 16:50:32.123 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-05-17 16:50:32.123 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 16:50:32.173 - [任务 9] - Remove memory task client succeed, task: 任务 9[664705e082e56d0d1f736854] 
[INFO ] 2024-05-17 16:50:32.174 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[664705e082e56d0d1f736854] 
