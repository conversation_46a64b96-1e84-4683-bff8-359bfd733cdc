[INFO ] 2024-07-29 04:48:28.293 - [TestCKEmeu8] - Task initialization... 
[INFO ] 2024-07-29 04:48:28.295 - [TestCKEmeu8] - Start task milestones: 66a6ae1666c385580759c0f6(TestCKEmeu8) 
[INFO ] 2024-07-29 04:48:28.423 - [TestCKEmeu8] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-29 04:48:28.423 - [TestCKEmeu8] - The engine receives TestCKEmeu8 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-29 04:48:28.479 - [TestCKEmeu8][t_enum_nullable] - Node t_enum_nullable[edf7699f-72d0-4ad6-812b-5a2ba2f0a012] start preload schema,table counts: 1 
[INFO ] 2024-07-29 04:48:28.480 - [TestCKEmeu8][targetEnum8] - Node targetEnum8[d3f0df04-0275-4f5b-9cd4-b330afad<PERSON>ea] start preload schema,table counts: 1 
[INFO ] 2024-07-29 04:48:28.480 - [TestCKEmeu8][t_enum_nullable] - Node t_enum_nullable[edf7699f-72d0-4ad6-812b-5a2ba2f0a012] preload schema finished, cost 1 ms 
[INFO ] 2024-07-29 04:48:28.480 - [TestCKEmeu8][targetEnum8] - Node targetEnum8[d3f0df04-0275-4f5b-9cd4-b330afad15ea] preload schema finished, cost 1 ms 
[INFO ] 2024-07-29 04:48:29.439 - [TestCKEmeu8][targetEnum8] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-29 04:48:29.440 - [TestCKEmeu8][t_enum_nullable] - Source node "t_enum_nullable" read batch size: 100 
[INFO ] 2024-07-29 04:48:29.440 - [TestCKEmeu8][t_enum_nullable] - Source node "t_enum_nullable" event queue capacity: 200 
[INFO ] 2024-07-29 04:48:29.440 - [TestCKEmeu8][t_enum_nullable] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-29 04:48:29.440 - [TestCKEmeu8][t_enum_nullable] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-29 04:48:29.440 - [TestCKEmeu8][t_enum_nullable] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-29 04:48:29.482 - [TestCKEmeu8][t_enum_nullable] - Initial sync started 
[INFO ] 2024-07-29 04:48:29.483 - [TestCKEmeu8][t_enum_nullable] - Starting batch read, table name: t_enum_nullable, offset: null 
[INFO ] 2024-07-29 04:48:29.488 - [TestCKEmeu8][t_enum_nullable] - Table t_enum_nullable is going to be initial synced 
[INFO ] 2024-07-29 04:48:29.503 - [TestCKEmeu8][t_enum_nullable] - Table [t_enum_nullable] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-29 04:48:29.503 - [TestCKEmeu8][t_enum_nullable] - Query table 't_enum_nullable' counts: 3 
[INFO ] 2024-07-29 04:48:29.706 - [TestCKEmeu8][t_enum_nullable] - Initial sync completed 
[INFO ] 2024-07-29 04:48:30.021 - [TestCKEmeu8][t_enum_nullable] - Node t_enum_nullable[edf7699f-72d0-4ad6-812b-5a2ba2f0a012] running status set to false 
[INFO ] 2024-07-29 04:48:30.026 - [TestCKEmeu8][targetEnum8] - Node targetEnum8[d3f0df04-0275-4f5b-9cd4-b330afad15ea] running status set to false 
[INFO ] 2024-07-29 04:48:30.027 - [TestCKEmeu8][t_enum_nullable] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-29 04:48:30.037 - [TestCKEmeu8][targetEnum8] - Clickhouse Optimize Table start, tables: ["targetEnum8"] 
[INFO ] 2024-07-29 04:48:30.061 - [TestCKEmeu8][t_enum_nullable] - Clickhouse Optimize Table end 
[WARN ] 2024-07-29 04:48:30.076 - [TestCKEmeu8][targetEnum8] - Clickhouse Optimize Table failed 
[INFO ] 2024-07-29 04:48:30.076 - [TestCKEmeu8][targetEnum8] - PDK connector node stopped: HazelcastTargetPdkDataNode-d3f0df04-0275-4f5b-9cd4-b330afad15ea 
[INFO ] 2024-07-29 04:48:30.076 - [TestCKEmeu8][targetEnum8] - PDK connector node released: HazelcastTargetPdkDataNode-d3f0df04-0275-4f5b-9cd4-b330afad15ea 
[INFO ] 2024-07-29 04:48:30.076 - [TestCKEmeu8][targetEnum8] - Node targetEnum8[d3f0df04-0275-4f5b-9cd4-b330afad15ea] schema data cleaned 
[INFO ] 2024-07-29 04:48:30.076 - [TestCKEmeu8][targetEnum8] - Node targetEnum8[d3f0df04-0275-4f5b-9cd4-b330afad15ea] monitor closed 
[INFO ] 2024-07-29 04:48:30.076 - [TestCKEmeu8][targetEnum8] - Node targetEnum8[d3f0df04-0275-4f5b-9cd4-b330afad15ea] close complete, cost 53 ms 
[INFO ] 2024-07-29 04:48:30.080 - [TestCKEmeu8][t_enum_nullable] - PDK connector node stopped: HazelcastSourcePdkDataNode-edf7699f-72d0-4ad6-812b-5a2ba2f0a012 
[INFO ] 2024-07-29 04:48:30.080 - [TestCKEmeu8][t_enum_nullable] - PDK connector node released: HazelcastSourcePdkDataNode-edf7699f-72d0-4ad6-812b-5a2ba2f0a012 
[INFO ] 2024-07-29 04:48:30.080 - [TestCKEmeu8][t_enum_nullable] - Node t_enum_nullable[edf7699f-72d0-4ad6-812b-5a2ba2f0a012] schema data cleaned 
[INFO ] 2024-07-29 04:48:30.082 - [TestCKEmeu8][t_enum_nullable] - Node t_enum_nullable[edf7699f-72d0-4ad6-812b-5a2ba2f0a012] monitor closed 
[INFO ] 2024-07-29 04:48:30.082 - [TestCKEmeu8][t_enum_nullable] - Node t_enum_nullable[edf7699f-72d0-4ad6-812b-5a2ba2f0a012] close complete, cost 66 ms 
[INFO ] 2024-07-29 04:48:33.556 - [TestCKEmeu8] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-29 04:48:33.676 - [TestCKEmeu8] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1460aa45 
[INFO ] 2024-07-29 04:48:33.676 - [TestCKEmeu8] - Stop task milestones: 66a6ae1666c385580759c0f6(TestCKEmeu8)  
[INFO ] 2024-07-29 04:48:33.699 - [TestCKEmeu8] - Stopped task aspect(s) 
[INFO ] 2024-07-29 04:48:33.699 - [TestCKEmeu8] - Snapshot order controller have been removed 
[INFO ] 2024-07-29 04:48:33.719 - [TestCKEmeu8] - Remove memory task client succeed, task: TestCKEmeu8[66a6ae1666c385580759c0f6] 
[INFO ] 2024-07-29 04:48:33.722 - [TestCKEmeu8] - Destroy memory task client cache succeed, task: TestCKEmeu8[66a6ae1666c385580759c0f6] 
