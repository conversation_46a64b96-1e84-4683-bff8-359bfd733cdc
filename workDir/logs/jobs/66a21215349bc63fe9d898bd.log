[INFO ] 2024-07-25 16:52:04.231 - [任务 29] - Task initialization... 
[INFO ] 2024-07-25 16:52:04.366 - [任务 29] - Start task milestones: 66a21215349bc63fe9d898bd(任务 29) 
[INFO ] 2024-07-25 16:52:04.366 - [任务 29] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 16:52:04.455 - [任务 29] - The engine receives 任务 29 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 16:52:04.540 - [任务 29][testAutoInspect] - Node testAutoInspect[119b0d06-ffab-4d78-8a56-e2a665491728] start preload schema,table counts: 1 
[INFO ] 2024-07-25 16:52:04.540 - [任务 29][testAutoInspect] - Node testAutoInspect[64cab34c-7237-4acd-8dcd-58f15ffdf288] start preload schema,table counts: 1 
[INFO ] 2024-07-25 16:52:04.540 - [任务 29][testAutoInspect] - Node testAutoInspect[64cab34c-7237-4acd-8dcd-58f15ffdf288] preload schema finished, cost 1 ms 
[INFO ] 2024-07-25 16:52:04.540 - [任务 29][testAutoInspect] - Node testAutoInspect[119b0d06-ffab-4d78-8a56-e2a665491728] preload schema finished, cost 2 ms 
[INFO ] 2024-07-25 16:52:05.419 - [任务 29][testAutoInspect] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 16:52:05.611 - [任务 29][testAutoInspect] - Source node "testAutoInspect" read batch size: 100 
[INFO ] 2024-07-25 16:52:05.611 - [任务 29][testAutoInspect] - Source node "testAutoInspect" event queue capacity: 200 
[INFO ] 2024-07-25 16:52:05.611 - [任务 29][testAutoInspect] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 16:52:05.613 - [任务 29][testAutoInspect] - batch offset found: {},stream offset found: {"filename":"binlog.000033","position":59587209,"gtidSet":""} 
[INFO ] 2024-07-25 16:52:05.613 - [任务 29][testAutoInspect] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 16:52:05.681 - [任务 29][testAutoInspect] - Initial sync started 
[INFO ] 2024-07-25 16:52:05.682 - [任务 29][testAutoInspect] - Starting batch read, table name: testAutoInspect, offset: null 
[INFO ] 2024-07-25 16:52:05.682 - [任务 29][testAutoInspect] - Table testAutoInspect is going to be initial synced 
[INFO ] 2024-07-25 16:52:05.705 - [任务 29][testAutoInspect] - Table [testAutoInspect] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:52:05.705 - [任务 29][testAutoInspect] - Query table 'testAutoInspect' counts: 4 
[INFO ] 2024-07-25 16:52:05.705 - [任务 29][testAutoInspect] - Initial sync completed 
[INFO ] 2024-07-25 16:52:05.705 - [任务 29][testAutoInspect] - Incremental sync starting... 
[INFO ] 2024-07-25 16:52:05.705 - [任务 29][testAutoInspect] - Initial sync completed 
[INFO ] 2024-07-25 16:52:05.706 - [任务 29][testAutoInspect] - Starting stream read, table list: [testAutoInspect, _tapdata_heartbeat_table], offset: {"filename":"binlog.000033","position":59587209,"gtidSet":""} 
[INFO ] 2024-07-25 16:52:05.739 - [任务 29][testAutoInspect] - Starting mysql cdc, server name: e12e7a26-b3b7-41c2-8a6f-72ff28b0d269 
[INFO ] 2024-07-25 16:52:05.739 - [任务 29][testAutoInspect] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 27749625
  time.precision.mode: adaptive_time_microseconds
  database.server.name: e12e7a26-b3b7-41c2-8a6f-72ff28b0d269
  database.port: 3306
  threadName: Debezium-Mysql-Connector-e12e7a26-b3b7-41c2-8a6f-72ff28b0d269
  database.hostname: localhost
  database.password: ********
  name: e12e7a26-b3b7-41c2-8a6f-72ff28b0d269
  pdk.offset.string: {"name":"e12e7a26-b3b7-41c2-8a6f-72ff28b0d269","offset":{"{\"server\":\"e12e7a26-b3b7-41c2-8a6f-72ff28b0d269\"}":"{\"file\":\"binlog.000033\",\"pos\":59587209,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testAutoInspect,test._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-25 16:52:05.944 - [任务 29][testAutoInspect] - Connector Mysql incremental start succeed, tables: [testAutoInspect, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-25 17:00:55.298 - [任务 29][testAutoInspect] - Node testAutoInspect[119b0d06-ffab-4d78-8a56-e2a665491728] running status set to false 
[INFO ] 2024-07-25 17:00:55.358 - [任务 29][testAutoInspect] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-25 17:00:55.362 - [任务 29][testAutoInspect] - Mysql binlog reader stopped 
[INFO ] 2024-07-25 17:00:55.368 - [任务 29][testAutoInspect] - Incremental sync completed 
[INFO ] 2024-07-25 17:00:55.385 - [任务 29][testAutoInspect] - PDK connector node stopped: HazelcastSourcePdkDataNode-119b0d06-ffab-4d78-8a56-e2a665491728 
[INFO ] 2024-07-25 17:00:55.385 - [任务 29][testAutoInspect] - PDK connector node released: HazelcastSourcePdkDataNode-119b0d06-ffab-4d78-8a56-e2a665491728 
[INFO ] 2024-07-25 17:00:55.386 - [任务 29][testAutoInspect] - Node testAutoInspect[119b0d06-ffab-4d78-8a56-e2a665491728] schema data cleaned 
[INFO ] 2024-07-25 17:00:55.386 - [任务 29][testAutoInspect] - Node testAutoInspect[119b0d06-ffab-4d78-8a56-e2a665491728] monitor closed 
[INFO ] 2024-07-25 17:00:55.387 - [任务 29][testAutoInspect] - Node testAutoInspect[119b0d06-ffab-4d78-8a56-e2a665491728] close complete, cost 94 ms 
[INFO ] 2024-07-25 17:00:55.390 - [任务 29][testAutoInspect] - Node testAutoInspect[64cab34c-7237-4acd-8dcd-58f15ffdf288] running status set to false 
[INFO ] 2024-07-25 17:00:55.426 - [任务 29][testAutoInspect] - PDK connector node stopped: HazelcastTargetPdkDataNode-64cab34c-7237-4acd-8dcd-58f15ffdf288 
[INFO ] 2024-07-25 17:00:55.440 - [任务 29][testAutoInspect] - PDK connector node released: HazelcastTargetPdkDataNode-64cab34c-7237-4acd-8dcd-58f15ffdf288 
[INFO ] 2024-07-25 17:00:55.443 - [任务 29][testAutoInspect] - Node testAutoInspect[64cab34c-7237-4acd-8dcd-58f15ffdf288] schema data cleaned 
[INFO ] 2024-07-25 17:00:55.446 - [任务 29][testAutoInspect] - Node testAutoInspect[64cab34c-7237-4acd-8dcd-58f15ffdf288] monitor closed 
[INFO ] 2024-07-25 17:00:55.446 - [任务 29][testAutoInspect] - Node testAutoInspect[64cab34c-7237-4acd-8dcd-58f15ffdf288] close complete, cost 40 ms 
[INFO ] 2024-07-25 17:00:58.434 - [任务 29] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 17:00:58.435 - [任务 29] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@45a7d963 
[INFO ] 2024-07-25 17:00:58.542 - [任务 29] - Stop task milestones: 66a21215349bc63fe9d898bd(任务 29)  
[INFO ] 2024-07-25 17:00:58.579 - [任务 29] - Stopped task aspect(s) 
[INFO ] 2024-07-25 17:00:58.579 - [任务 29] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 17:00:58.604 - [任务 29] - Remove memory task client succeed, task: 任务 29[66a21215349bc63fe9d898bd] 
[INFO ] 2024-07-25 17:00:58.607 - [任务 29] - Destroy memory task client cache succeed, task: 任务 29[66a21215349bc63fe9d898bd] 
