[TRACE] 2025-01-22 15:50:25.705 - [任务 7] - Task initialization... 
[TRACE] 2025-01-22 15:50:25.707 - [任务 7] - Start task milestones: 6790a2cc5fb0f31007f79895(任务 7) 
[INFO ] 2025-01-22 15:50:25.922 - [任务 7] - Loading table structure completed 
[TRACE] 2025-01-22 15:50:26.021 - [任务 7] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-01-22 15:50:26.023 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-01-22 15:50:26.096 - [任务 7] - Task started 
[TRACE] 2025-01-22 15:50:26.096 - [任务 7][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] start preload schema,table counts: 1 
[TRACE] 2025-01-22 15:50:26.096 - [任务 7][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] start preload schema,table counts: 1 
[TRACE] 2025-01-22 15:50:26.096 - [任务 7][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] preload schema finished, cost 0 ms 
[TRACE] 2025-01-22 15:50:26.097 - [任务 7][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] preload schema finished, cost 1 ms 
[INFO ] 2025-01-22 15:50:27.005 - [任务 7][Sybase190] - Source connector(Sybase190) initialization completed 
[TRACE] 2025-01-22 15:50:27.008 - [任务 7][Sybase190] - Source node "Sybase190" read batch size: 100 
[TRACE] 2025-01-22 15:50:27.008 - [任务 7][Sybase190] - Source node "Sybase190" event queue capacity: 200 
[TRACE] 2025-01-22 15:50:27.008 - [任务 7][Sybase190] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-01-22 15:50:27.008 - [任务 7][Sybase190] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-01-22 15:50:27.141 - [任务 7][Sybase190] - Starting batch read from 1 tables 
[TRACE] 2025-01-22 15:50:27.141 - [任务 7][Sybase190] - Initial sync started 
[INFO ] 2025-01-22 15:50:27.143 - [任务 7][Sybase190] - Starting batch read from table: td_char0 
[TRACE] 2025-01-22 15:50:27.143 - [任务 7][Sybase190] - Table td_char0 is going to be initial synced 
[TRACE] 2025-01-22 15:50:27.274 - [任务 7][Sybase190] - Query snapshot row size completed: Sybase190(fb5b2d56-0e57-408a-b95f-9f2b020da9f4) 
[INFO ] 2025-01-22 15:50:27.291 - [任务 7][Sybase190] - Table td_char0 has been completed batch read 
[TRACE] 2025-01-22 15:50:27.291 - [任务 7][Sybase190] - Initial sync completed 
[INFO ] 2025-01-22 15:50:27.291 - [任务 7][Sybase190] - Batch read completed. 
[INFO ] 2025-01-22 15:50:27.292 - [任务 7][Sybase190] - Task run completed 
[INFO ] 2025-01-22 15:50:27.309 - [任务 7][PG] - Sink connector(PG) initialization completed 
[TRACE] 2025-01-22 15:50:27.310 - [任务 7][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-01-22 15:50:27.310 - [任务 7][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-01-22 15:50:27.313 - [任务 7][PG] - Apply table structure to target database 
[TRACE] 2025-01-22 15:50:28.429 - [任务 7][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] running status set to false 
[TRACE] 2025-01-22 15:50:28.435 - [任务 7][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] running status set to false 
[TRACE] 2025-01-22 15:50:28.510 - [任务 7][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode_dcd0bd02-c896-45e5-81ff-780f627732d8_1737532227019 
[TRACE] 2025-01-22 15:50:28.510 - [任务 7][PG] - PDK connector node released: HazelcastTargetPdkDataNode_dcd0bd02-c896-45e5-81ff-780f627732d8_1737532227019 
[TRACE] 2025-01-22 15:50:28.510 - [任务 7][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] schema data cleaned 
[TRACE] 2025-01-22 15:50:28.510 - [任务 7][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] monitor closed 
[TRACE] 2025-01-22 15:50:28.534 - [任务 7][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] close complete, cost 91 ms 
[TRACE] 2025-01-22 15:50:28.535 - [任务 7][Sybase190] - PDK connector node stopped: HazelcastSourcePdkDataNode_fb5b2d56-0e57-408a-b95f-9f2b020da9f4_1737532226862 
[TRACE] 2025-01-22 15:50:28.535 - [任务 7][Sybase190] - PDK connector node released: HazelcastSourcePdkDataNode_fb5b2d56-0e57-408a-b95f-9f2b020da9f4_1737532226862 
[TRACE] 2025-01-22 15:50:28.535 - [任务 7][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] schema data cleaned 
[TRACE] 2025-01-22 15:50:28.535 - [任务 7][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] monitor closed 
[TRACE] 2025-01-22 15:50:28.741 - [任务 7][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] close complete, cost 127 ms 
[TRACE] 2025-01-22 15:50:34.219 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-01-22 15:50:34.227 - [任务 7] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@55edf86b 
[TRACE] 2025-01-22 15:50:34.231 - [任务 7] - Stop task milestones: 6790a2cc5fb0f31007f79895(任务 7)  
[TRACE] 2025-01-22 15:50:34.383 - [任务 7] - Stopped task aspect(s) 
[TRACE] 2025-01-22 15:50:34.384 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2025-01-22 15:50:34.384 - [任务 7] - Task stopped. 
[TRACE] 2025-01-22 15:50:34.416 - [任务 7] - Remove memory task client succeed, task: 任务 7[6790a2cc5fb0f31007f79895] 
[TRACE] 2025-01-22 15:50:34.416 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[6790a2cc5fb0f31007f79895] 
[TRACE] 2025-01-22 17:09:26.128 - [测试char0] - Task initialization... 
[TRACE] 2025-01-22 17:09:26.129 - [测试char0] - Start task milestones: 6790a2cc5fb0f31007f79895(测试char0) 
[INFO ] 2025-01-22 17:09:26.227 - [测试char0] - Loading table structure completed 
[TRACE] 2025-01-22 17:09:26.267 - [测试char0] - Node performs snapshot read asynchronously 
[TRACE] 2025-01-22 17:09:26.267 - [测试char0] - The engine receives 测试char0 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-01-22 17:09:26.307 - [测试char0] - Task started 
[TRACE] 2025-01-22 17:09:26.307 - [测试char0][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] start preload schema,table counts: 1 
[TRACE] 2025-01-22 17:09:26.307 - [测试char0][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] start preload schema,table counts: 1 
[TRACE] 2025-01-22 17:09:26.307 - [测试char0][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] preload schema finished, cost 0 ms 
[TRACE] 2025-01-22 17:09:26.308 - [测试char0][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] preload schema finished, cost 0 ms 
[INFO ] 2025-01-22 17:09:27.103 - [测试char0][Sybase190] - Source connector(Sybase190) initialization completed 
[TRACE] 2025-01-22 17:09:27.104 - [测试char0][Sybase190] - Source node "Sybase190" read batch size: 100 
[TRACE] 2025-01-22 17:09:27.104 - [测试char0][Sybase190] - Source node "Sybase190" event queue capacity: 200 
[TRACE] 2025-01-22 17:09:27.104 - [测试char0][Sybase190] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-01-22 17:09:27.104 - [测试char0][Sybase190] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-01-22 17:09:27.148 - [测试char0][Sybase190] - Starting batch read from 1 tables 
[TRACE] 2025-01-22 17:09:27.169 - [测试char0][Sybase190] - Initial sync started 
[INFO ] 2025-01-22 17:09:27.171 - [测试char0][Sybase190] - Starting batch read from table: td_char0 
[TRACE] 2025-01-22 17:09:27.210 - [测试char0][Sybase190] - Table td_char0 is going to be initial synced 
[INFO ] 2025-01-22 17:09:27.210 - [测试char0][PG] - Sink connector(PG) initialization completed 
[TRACE] 2025-01-22 17:09:27.210 - [测试char0][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-01-22 17:09:27.210 - [测试char0][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-01-22 17:09:27.214 - [测试char0][PG] - Apply table structure to target database 
[TRACE] 2025-01-22 17:09:27.272 - [测试char0][Sybase190] - Query snapshot row size completed: Sybase190(fb5b2d56-0e57-408a-b95f-9f2b020da9f4) 
[INFO ] 2025-01-22 17:09:27.272 - [测试char0][Sybase190] - Table td_char0 has been completed batch read 
[TRACE] 2025-01-22 17:09:27.273 - [测试char0][Sybase190] - Initial sync completed 
[INFO ] 2025-01-22 17:09:27.273 - [测试char0][Sybase190] - Batch read completed. 
[INFO ] 2025-01-22 17:09:27.273 - [测试char0][Sybase190] - Task run completed 
[TRACE] 2025-01-22 17:09:28.342 - [测试char0][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] running status set to false 
[TRACE] 2025-01-22 17:09:28.345 - [测试char0][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] running status set to false 
[TRACE] 2025-01-22 17:09:28.430 - [测试char0][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode_dcd0bd02-c896-45e5-81ff-780f627732d8_1737536966914 
[TRACE] 2025-01-22 17:09:28.435 - [测试char0][PG] - PDK connector node released: HazelcastTargetPdkDataNode_dcd0bd02-c896-45e5-81ff-780f627732d8_1737536966914 
[TRACE] 2025-01-22 17:09:28.436 - [测试char0][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] schema data cleaned 
[TRACE] 2025-01-22 17:09:28.436 - [测试char0][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] monitor closed 
[TRACE] 2025-01-22 17:09:28.440 - [测试char0][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] close complete, cost 111 ms 
[TRACE] 2025-01-22 17:09:28.474 - [测试char0][Sybase190] - PDK connector node stopped: HazelcastSourcePdkDataNode_fb5b2d56-0e57-408a-b95f-9f2b020da9f4_1737536967005 
[TRACE] 2025-01-22 17:09:28.474 - [测试char0][Sybase190] - PDK connector node released: HazelcastSourcePdkDataNode_fb5b2d56-0e57-408a-b95f-9f2b020da9f4_1737536967005 
[TRACE] 2025-01-22 17:09:28.475 - [测试char0][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] schema data cleaned 
[TRACE] 2025-01-22 17:09:28.475 - [测试char0][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] monitor closed 
[TRACE] 2025-01-22 17:09:28.679 - [测试char0][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] close complete, cost 153 ms 
[TRACE] 2025-01-22 17:09:33.911 - [测试char0] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-01-22 17:09:33.912 - [测试char0] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@487116d5 
[TRACE] 2025-01-22 17:09:34.074 - [测试char0] - Stop task milestones: 6790a2cc5fb0f31007f79895(测试char0)  
[TRACE] 2025-01-22 17:09:34.075 - [测试char0] - Stopped task aspect(s) 
[TRACE] 2025-01-22 17:09:34.079 - [测试char0] - Snapshot order controller have been removed 
[INFO ] 2025-01-22 17:09:34.080 - [测试char0] - Task stopped. 
[TRACE] 2025-01-22 17:09:34.110 - [测试char0] - Remove memory task client succeed, task: 测试char0[6790a2cc5fb0f31007f79895] 
[TRACE] 2025-01-22 17:09:34.113 - [测试char0] - Destroy memory task client cache succeed, task: 测试char0[6790a2cc5fb0f31007f79895] 
[TRACE] 2025-01-22 17:10:07.339 - [测试char0] - Task initialization... 
[TRACE] 2025-01-22 17:10:07.347 - [测试char0] - Start task milestones: 6790a2cc5fb0f31007f79895(测试char0) 
[INFO ] 2025-01-22 17:10:07.480 - [测试char0] - Loading table structure completed 
[TRACE] 2025-01-22 17:10:07.480 - [测试char0] - Node performs snapshot read asynchronously 
[TRACE] 2025-01-22 17:10:07.534 - [测试char0] - The engine receives 测试char0 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-01-22 17:10:07.534 - [测试char0] - Task started 
[TRACE] 2025-01-22 17:10:07.570 - [测试char0][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] start preload schema,table counts: 1 
[TRACE] 2025-01-22 17:10:07.571 - [测试char0][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] start preload schema,table counts: 1 
[TRACE] 2025-01-22 17:10:07.571 - [测试char0][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] preload schema finished, cost 0 ms 
[TRACE] 2025-01-22 17:10:07.571 - [测试char0][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] preload schema finished, cost 0 ms 
[INFO ] 2025-01-22 17:10:08.591 - [测试char0][Sybase190] - Source connector(Sybase190) initialization completed 
[TRACE] 2025-01-22 17:10:08.591 - [测试char0][Sybase190] - Source node "Sybase190" read batch size: 100 
[TRACE] 2025-01-22 17:10:08.591 - [测试char0][Sybase190] - Source node "Sybase190" event queue capacity: 200 
[TRACE] 2025-01-22 17:10:08.591 - [测试char0][Sybase190] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-01-22 17:10:08.638 - [测试char0][Sybase190] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-01-22 17:10:08.639 - [测试char0][PG] - Sink connector(PG) initialization completed 
[TRACE] 2025-01-22 17:10:08.639 - [测试char0][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-01-22 17:10:08.639 - [测试char0][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-01-22 17:10:08.716 - [测试char0][PG] - Apply table structure to target database 
[INFO ] 2025-01-22 17:10:08.718 - [测试char0][Sybase190] - Starting batch read from 1 tables 
[TRACE] 2025-01-22 17:10:08.748 - [测试char0][Sybase190] - Initial sync started 
[INFO ] 2025-01-22 17:10:08.749 - [测试char0][Sybase190] - Starting batch read from table: td_char0 
[TRACE] 2025-01-22 17:10:08.749 - [测试char0][Sybase190] - Table td_char0 is going to be initial synced 
[TRACE] 2025-01-22 17:10:08.858 - [测试char0][Sybase190] - Query snapshot row size completed: Sybase190(fb5b2d56-0e57-408a-b95f-9f2b020da9f4) 
[INFO ] 2025-01-22 17:10:08.860 - [测试char0][Sybase190] - Table td_char0 has been completed batch read 
[TRACE] 2025-01-22 17:10:08.860 - [测试char0][Sybase190] - Initial sync completed 
[INFO ] 2025-01-22 17:10:08.860 - [测试char0][Sybase190] - Batch read completed. 
[INFO ] 2025-01-22 17:10:08.860 - [测试char0][Sybase190] - Task run completed 
[TRACE] 2025-01-22 17:10:09.755 - [测试char0][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] running status set to false 
[TRACE] 2025-01-22 17:10:09.763 - [测试char0][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] running status set to false 
[TRACE] 2025-01-22 17:10:09.792 - [测试char0][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode_dcd0bd02-c896-45e5-81ff-780f627732d8_1737537008360 
[TRACE] 2025-01-22 17:10:09.792 - [测试char0][PG] - PDK connector node released: HazelcastTargetPdkDataNode_dcd0bd02-c896-45e5-81ff-780f627732d8_1737537008360 
[TRACE] 2025-01-22 17:10:09.792 - [测试char0][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] schema data cleaned 
[TRACE] 2025-01-22 17:10:09.792 - [测试char0][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] monitor closed 
[TRACE] 2025-01-22 17:10:09.938 - [测试char0][PG] - Node PG[dcd0bd02-c896-45e5-81ff-780f627732d8] close complete, cost 40 ms 
[TRACE] 2025-01-22 17:10:09.938 - [测试char0][Sybase190] - PDK connector node stopped: HazelcastSourcePdkDataNode_fb5b2d56-0e57-408a-b95f-9f2b020da9f4_1737537008516 
[TRACE] 2025-01-22 17:10:09.938 - [测试char0][Sybase190] - PDK connector node released: HazelcastSourcePdkDataNode_fb5b2d56-0e57-408a-b95f-9f2b020da9f4_1737537008516 
[TRACE] 2025-01-22 17:10:09.938 - [测试char0][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] schema data cleaned 
[TRACE] 2025-01-22 17:10:09.941 - [测试char0][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] monitor closed 
[TRACE] 2025-01-22 17:10:09.941 - [测试char0][Sybase190] - Node Sybase190[fb5b2d56-0e57-408a-b95f-9f2b020da9f4] close complete, cost 192 ms 
[TRACE] 2025-01-22 17:10:14.161 - [测试char0] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-01-22 17:10:14.162 - [测试char0] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3649dcde 
[TRACE] 2025-01-22 17:10:14.308 - [测试char0] - Stop task milestones: 6790a2cc5fb0f31007f79895(测试char0)  
[TRACE] 2025-01-22 17:10:14.308 - [测试char0] - Stopped task aspect(s) 
[TRACE] 2025-01-22 17:10:14.308 - [测试char0] - Snapshot order controller have been removed 
[INFO ] 2025-01-22 17:10:14.308 - [测试char0] - Task stopped. 
[TRACE] 2025-01-22 17:10:14.357 - [测试char0] - Remove memory task client succeed, task: 测试char0[6790a2cc5fb0f31007f79895] 
[TRACE] 2025-01-22 17:10:14.357 - [测试char0] - Destroy memory task client cache succeed, task: 测试char0[6790a2cc5fb0f31007f79895] 
