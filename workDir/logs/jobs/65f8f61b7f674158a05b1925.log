[INFO ] 2024-03-21 14:19:44.367 - [任务 3] - Task initialization... 
[INFO ] 2024-03-21 14:19:44.381 - [任务 3] - Start task milestones: 65f8f61b7f674158a05b1925(任务 3) 
[INFO ] 2024-03-21 14:19:44.454 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-21 14:19:45.075 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-21 14:19:45.874 - [任务 3][BMSQL_CONFIG] - Node BMSQL_CONFIG[588889c9-4369-413c-bc0c-80ae6763aace] start preload schema,table counts: 1 
[INFO ] 2024-03-21 14:19:45.878 - [任务 3][test22] - Node test22[6e0aa4da-10d9-4c8c-b315-7fb8a7324162] start preload schema,table counts: 1 
[INFO ] 2024-03-21 14:19:46.160 - [任务 3][test22] - Node test22[6e0aa4da-10d9-4c8c-b315-7fb8a7324162] preload schema finished, cost 267 ms 
[INFO ] 2024-03-21 14:19:46.165 - [任务 3][BMSQL_CONFIG] - Node BMSQL_CONFIG[588889c9-4369-413c-bc0c-80ae6763aace] preload schema finished, cost 267 ms 
[INFO ] 2024-03-21 14:19:53.399 - [任务 3][test22] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-21 14:19:53.941 - [任务 3][BMSQL_CONFIG] - Source node "BMSQL_CONFIG" read batch size: 100 
[INFO ] 2024-03-21 14:19:53.948 - [任务 3][BMSQL_CONFIG] - Source node "BMSQL_CONFIG" event queue capacity: 200 
[INFO ] 2024-03-21 14:19:53.954 - [任务 3][BMSQL_CONFIG] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-21 14:19:53.965 - [任务 3][BMSQL_CONFIG] - batch offset found: {"BMSQL_CONFIG":{}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":22694249,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-03-21 14:19:53.966 - [任务 3][BMSQL_CONFIG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-03-21 14:19:54.068 - [任务 3][BMSQL_CONFIG] - Incremental sync starting... 
[INFO ] 2024-03-21 14:19:54.069 - [任务 3][BMSQL_CONFIG] - Initial sync completed 
[INFO ] 2024-03-21 14:19:54.070 - [任务 3][BMSQL_CONFIG] - Incremental sync completed 
[ERROR] 2024-03-21 14:19:54.071 - [任务 3][BMSQL_CONFIG] - java.lang.reflect.InvocationTargetException <-- Error Message -->
java.lang.reflect.InvocationTargetException

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapOssNonsupportFunctionException: null
	io.tapdata.sharecdc.impl.ShareCdcBaseReader.<init>(ShareCdcBaseReader.java:14)
	io.tapdata.sharecdc.impl.ShareCdcHZReader.<init>(ShareCdcHZReader.java:3)
	io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.<init>(ShareCdcPDKTaskReader.java:10)
	sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	...

<-- Full Stack Trace -->
java.lang.reflect.InvocationTargetException
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:611)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:204)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.reflect.InvocationTargetException
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at io.tapdata.flow.engine.V2.sharecdc.impl.ShareCdcFactory.shareCdcReader(ShareCdcFactory.java:30)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doShareCdc(HazelcastSourcePdkDataNode.java:865)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:594)
	... 7 more
Caused by: null
	at io.tapdata.sharecdc.impl.ShareCdcBaseReader.<init>(ShareCdcBaseReader.java:14)
	at io.tapdata.sharecdc.impl.ShareCdcHZReader.<init>(ShareCdcHZReader.java:3)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.<init>(ShareCdcPDKTaskReader.java:10)
	... 14 more

[INFO ] 2024-03-21 14:19:54.076 - [任务 3][BMSQL_CONFIG] - Job suspend in error handle 
[INFO ] 2024-03-21 14:19:54.611 - [任务 3][BMSQL_CONFIG] - Node BMSQL_CONFIG[588889c9-4369-413c-bc0c-80ae6763aace] running status set to false 
[INFO ] 2024-03-21 14:19:54.709 - [任务 3][BMSQL_CONFIG] - PDK connector node stopped: HazelcastSourcePdkDataNode-588889c9-4369-413c-bc0c-80ae6763aace 
[INFO ] 2024-03-21 14:19:54.712 - [任务 3][BMSQL_CONFIG] - PDK connector node released: HazelcastSourcePdkDataNode-588889c9-4369-413c-bc0c-80ae6763aace 
[INFO ] 2024-03-21 14:19:54.712 - [任务 3][BMSQL_CONFIG] - Node BMSQL_CONFIG[588889c9-4369-413c-bc0c-80ae6763aace] schema data cleaned 
[INFO ] 2024-03-21 14:19:54.712 - [任务 3][BMSQL_CONFIG] - Node BMSQL_CONFIG[588889c9-4369-413c-bc0c-80ae6763aace] monitor closed 
[INFO ] 2024-03-21 14:19:54.713 - [任务 3][BMSQL_CONFIG] - Node BMSQL_CONFIG[588889c9-4369-413c-bc0c-80ae6763aace] close complete, cost 142 ms 
[INFO ] 2024-03-21 14:19:54.714 - [任务 3][test22] - Node test22[6e0aa4da-10d9-4c8c-b315-7fb8a7324162] running status set to false 
[INFO ] 2024-03-21 14:19:54.736 - [任务 3][test22] - PDK connector node stopped: HazelcastTargetPdkDataNode-6e0aa4da-10d9-4c8c-b315-7fb8a7324162 
[INFO ] 2024-03-21 14:19:54.736 - [任务 3][test22] - PDK connector node released: HazelcastTargetPdkDataNode-6e0aa4da-10d9-4c8c-b315-7fb8a7324162 
[INFO ] 2024-03-21 14:19:54.737 - [任务 3][test22] - Node test22[6e0aa4da-10d9-4c8c-b315-7fb8a7324162] schema data cleaned 
[INFO ] 2024-03-21 14:19:54.737 - [任务 3][test22] - Node test22[6e0aa4da-10d9-4c8c-b315-7fb8a7324162] monitor closed 
[INFO ] 2024-03-21 14:19:54.737 - [任务 3][test22] - Node test22[6e0aa4da-10d9-4c8c-b315-7fb8a7324162] close complete, cost 23 ms 
[INFO ] 2024-03-21 14:19:58.540 - [任务 3] - Task [任务 3] cannot retry, reason: Task retry service not start 
[INFO ] 2024-03-21 14:19:58.554 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-21 14:19:58.562 - [任务 3] - Stop task milestones: 65f8f61b7f674158a05b1925(任务 3)  
[INFO ] 2024-03-21 14:19:58.580 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-03-21 14:19:58.582 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-03-21 14:19:58.595 - [任务 3] - Remove memory task client succeed, task: 任务 3[65f8f61b7f674158a05b1925] 
[INFO ] 2024-03-21 14:19:58.595 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[65f8f61b7f674158a05b1925] 
