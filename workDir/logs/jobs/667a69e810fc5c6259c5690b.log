[INFO ] 2024-06-25 15:02:53.020 - [任务 30] - Start task milestones: 667a69e810fc5c6259c5690b(任务 30) 
[INFO ] 2024-06-25 15:02:53.021 - [任务 30] - Task initialization... 
[INFO ] 2024-06-25 15:02:53.194 - [任务 30] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-06-25 15:02:53.265 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 15:02:53.316 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] start preload schema,table counts: 3 
[INFO ] 2024-06-25 15:02:53.316 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] start preload schema,table counts: 3 
[INFO ] 2024-06-25 15:02:53.316 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] start preload schema,table counts: 3 
[INFO ] 2024-06-25 15:02:53.317 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:02:53.318 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:02:53.318 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:02:53.824 - [任务 30][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-06-25 15:02:53.824 - [任务 30][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-06-25 15:02:53.824 - [任务 30][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 15:02:53.932 - [任务 30][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719298973,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 15:02:53.932 - [任务 30] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 15:02:54.014 - [任务 30][SourceMongo] - Initial sync started 
[INFO ] 2024-06-25 15:02:54.024 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM2, offset: null 
[INFO ] 2024-06-25 15:02:54.024 - [任务 30][SourceMongo] - Table CLAIM2 is going to be initial synced 
[INFO ] 2024-06-25 15:02:54.042 - [任务 30][SourceMongo] - Table [CLAIM2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:02:54.044 - [任务 30][SourceMongo] - Query table 'CLAIM2' counts: 1 
[INFO ] 2024-06-25 15:02:54.054 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM3, offset: null 
[INFO ] 2024-06-25 15:02:54.054 - [任务 30][SourceMongo] - Table CLAIM3 is going to be initial synced 
[INFO ] 2024-06-25 15:02:54.054 - [任务 30][SourceMongo] - Query table 'CLAIM3' counts: 0 
[INFO ] 2024-06-25 15:02:54.054 - [任务 30][SourceMongo] - Table [CLAIM3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:02:54.070 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-25 15:02:54.070 - [任务 30][SourceMongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-25 15:02:54.070 - [任务 30][SourceMongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-25 15:02:54.070 - [任务 30][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 15:02:54.070 - [任务 30][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 15:02:54.353 - [任务 30][SourceMongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:02:54.353 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:02:54.353 - [任务 30][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-25 15:02:54.353 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:02:54.355 - [任务 30][SourceMongo] - Starting stream read, table list: [CLAIM2, CLAIM3, CLAIM], offset: {"cdcOffset":1719298973,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 15:02:54.558 - [任务 30][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM2, CLAIM3, CLAIM], data change syncing 
[WARN ] 2024-06-25 15:03:40.585 - [任务 30][SouceMysql] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-25 15:03:53.966 - [任务 30][SourceMongo] - Found new table(s): [CLAIM4] 
[INFO ] 2024-06-25 15:03:54.069 - [任务 30][SourceMongo] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-06-25 15:03:54.070 - [任务 30][SourceMongo] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@1c2a0f60: {"table":{"defaultPrimaryKeys":["_id"],"id":"CLAIM4","indexList":[{"indexFields":[{}],"name":"__t__{\"v\": 2, \"key\": {\"_id\": 1}, \"name\": \"_id_\", \"ns\": \"DevSource.CLAIM4\"}"}],"maxPKPos":1,"maxPos":1,"name":"CLAIM4","nameFieldMap":{"_id":{"autoInc":false,"dataType":"OBJECT_ID","name":"_id","nullable":true,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bytes":24,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"_id"}],"indexMap":{"_id":{"fieldAsc":true,"name":"_id"}},"unique":true},"tableAttr":{"size":0,"ns":"DevSource.CLAIM4","capped":false,"storageSize":4096,"shard":{}}},"tableId":"CLAIM4","type":206} 
[INFO ] 2024-06-25 15:03:54.232 - [任务 30][SourceMongo] - Create new table in memory, qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT_CLAIM4_6674feb868ca1e3afc2a0d99_667a69e810fc5c6259c5690b 
[INFO ] 2024-06-25 15:03:54.232 - [任务 30][SourceMongo] - Create new table schema transform finished: TapTable id CLAIM4 name CLAIM4 storageEngine null charset null number of fields 1 
[INFO ] 2024-06-25 15:03:54.388 - [任务 30][SourceMongo] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-06-25 15:03:54.389 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM4, offset: null 
[INFO ] 2024-06-25 15:03:54.389 - [任务 30][SourceMongo] - Table CLAIM4 is going to be initial synced 
[INFO ] 2024-06-25 15:03:54.431 - [任务 30][SourceMongo] - Query table 'CLAIM4' counts: 0 
[INFO ] 2024-06-25 15:03:54.431 - [任务 30][SourceMongo] - Table [CLAIM4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:03:54.439 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:03:54.439 - [任务 30][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-25 15:03:54.440 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:03:54.475 - [任务 30][SourceMongo] - Starting stream read, table list: [CLAIM4, CLAIM2, CLAIM3, CLAIM], offset: {"_data":{"value":"82667A6BD5000000112B022C0100296E5A10046504C9A108CA48DF843909B25C4545DB46465F696400463C6F6964003C36363761363936633164656265636364626230313563303700000004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"string":true,"int32":false,"int64":false,"symbol":false,"binary":false,"double":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-06-25 15:03:54.475 - [任务 30][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM4, CLAIM2, CLAIM3, CLAIM], data change syncing 
[INFO ] 2024-06-25 15:03:54.879 - [任务 30][SourceMongo] - Incremental sync completed 
[WARN ] 2024-06-25 15:04:40.524 - [任务 30][SouceMysql] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-25 15:05:00.128 - [任务 30] - Stop task milestones: 667a69e810fc5c6259c5690b(任务 30)  
[INFO ] 2024-06-25 15:05:00.513 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] running status set to false 
[INFO ] 2024-06-25 15:05:00.522 - [任务 30][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e871a96c-a577-4440-afa5-537887b45e9b 
[INFO ] 2024-06-25 15:05:00.522 - [任务 30][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e871a96c-a577-4440-afa5-537887b45e9b 
[INFO ] 2024-06-25 15:05:00.522 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] schema data cleaned 
[INFO ] 2024-06-25 15:05:00.523 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] monitor closed 
[INFO ] 2024-06-25 15:05:00.524 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] close complete, cost 12 ms 
[INFO ] 2024-06-25 15:05:00.524 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] running status set to false 
[INFO ] 2024-06-25 15:05:00.525 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] schema data cleaned 
[INFO ] 2024-06-25 15:05:00.525 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] monitor closed 
[INFO ] 2024-06-25 15:05:00.525 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] close complete, cost 0 ms 
[INFO ] 2024-06-25 15:05:00.550 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] running status set to false 
[WARN ] 2024-06-25 15:05:00.550 - [任务 30][SouceMysql] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-25 15:05:00.555 - [任务 30][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150 
[INFO ] 2024-06-25 15:05:00.555 - [任务 30][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150 
[INFO ] 2024-06-25 15:05:00.556 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] schema data cleaned 
[INFO ] 2024-06-25 15:05:00.556 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] monitor closed 
[INFO ] 2024-06-25 15:05:00.794 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] close complete, cost 30 ms 
[INFO ] 2024-06-25 15:05:01.056 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 15:05:01.058 - [任务 30] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3e3fa541 
[INFO ] 2024-06-25 15:05:01.058 - [任务 30] - Stopped task aspect(s) 
[INFO ] 2024-06-25 15:05:01.058 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 15:05:01.086 - [任务 30] - Remove memory task client succeed, task: 任务 30[667a69e810fc5c6259c5690b] 
[INFO ] 2024-06-25 15:05:01.087 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[667a69e810fc5c6259c5690b] 
[INFO ] 2024-06-25 15:05:42.052 - [任务 30] - Start task milestones: 667a69e810fc5c6259c5690b(任务 30) 
[INFO ] 2024-06-25 15:05:42.052 - [任务 30] - Task initialization... 
[INFO ] 2024-06-25 15:05:42.299 - [任务 30] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 15:05:42.486 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 15:05:42.486 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:05:42.486 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:05:42.487 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:05:42.487 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] preload schema finished, cost 1 ms 
[INFO ] 2024-06-25 15:05:42.487 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] preload schema finished, cost 1 ms 
[INFO ] 2024-06-25 15:05:42.487 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] preload schema finished, cost 1 ms 
[INFO ] 2024-06-25 15:05:43.166 - [任务 30][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-06-25 15:05:43.166 - [任务 30][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-06-25 15:05:43.167 - [任务 30][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 15:05:43.168 - [任务 30][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 15:05:43.283 - [任务 30][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 15:05:43.284 - [任务 30][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719299143,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 15:05:43.284 - [任务 30] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 15:05:43.345 - [任务 30][SourceMongo] - Initial sync started 
[INFO ] 2024-06-25 15:05:43.346 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM4, offset: null 
[INFO ] 2024-06-25 15:05:43.346 - [任务 30][SourceMongo] - Table CLAIM4 is going to be initial synced 
[INFO ] 2024-06-25 15:05:43.390 - [任务 30][SourceMongo] - Table [CLAIM4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:05:43.390 - [任务 30][SourceMongo] - Query table 'CLAIM4' counts: 1 
[INFO ] 2024-06-25 15:05:43.390 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM2, offset: null 
[INFO ] 2024-06-25 15:05:43.390 - [任务 30][SourceMongo] - Table CLAIM2 is going to be initial synced 
[INFO ] 2024-06-25 15:05:43.392 - [任务 30][SourceMongo] - Query table 'CLAIM2' counts: 1 
[INFO ] 2024-06-25 15:05:43.392 - [任务 30][SourceMongo] - Table [CLAIM2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:05:43.392 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM3, offset: null 
[INFO ] 2024-06-25 15:05:43.392 - [任务 30][SourceMongo] - Table CLAIM3 is going to be initial synced 
[INFO ] 2024-06-25 15:05:43.393 - [任务 30][SourceMongo] - Query table 'CLAIM3' counts: 1 
[INFO ] 2024-06-25 15:05:43.393 - [任务 30][SourceMongo] - Table [CLAIM3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:05:43.394 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-25 15:05:43.394 - [任务 30][SourceMongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-25 15:05:43.464 - [任务 30][SourceMongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-25 15:05:43.465 - [任务 30][SourceMongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:05:43.465 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:05:43.465 - [任务 30][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-25 15:05:43.465 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:05:43.477 - [任务 30][SourceMongo] - Starting stream read, table list: [CLAIM4, CLAIM2, CLAIM3, CLAIM], offset: {"cdcOffset":1719299143,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 15:05:43.477 - [任务 30][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM4, CLAIM2, CLAIM3, CLAIM], data change syncing 
[WARN ] 2024-06-25 15:06:00.775 - [任务 30][SouceMysql] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[WARN ] 2024-06-25 15:07:00.643 - [任务 30][SouceMysql] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-25 15:07:56.773 - [任务 30] - Stop task milestones: 667a69e810fc5c6259c5690b(任务 30)  
[INFO ] 2024-06-25 15:07:56.968 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] running status set to false 
[INFO ] 2024-06-25 15:07:56.993 - [任务 30][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e871a96c-a577-4440-afa5-537887b45e9b 
[INFO ] 2024-06-25 15:07:56.994 - [任务 30][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e871a96c-a577-4440-afa5-537887b45e9b 
[INFO ] 2024-06-25 15:07:56.994 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] schema data cleaned 
[INFO ] 2024-06-25 15:07:56.996 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] monitor closed 
[INFO ] 2024-06-25 15:07:56.997 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] close complete, cost 31 ms 
[INFO ] 2024-06-25 15:07:56.997 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] running status set to false 
[INFO ] 2024-06-25 15:07:56.997 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] schema data cleaned 
[INFO ] 2024-06-25 15:07:56.998 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] monitor closed 
[INFO ] 2024-06-25 15:07:56.999 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] close complete, cost 1 ms 
[INFO ] 2024-06-25 15:07:56.999 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] running status set to false 
[INFO ] 2024-06-25 15:07:57.038 - [任务 30][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150 
[INFO ] 2024-06-25 15:07:57.038 - [任务 30][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150 
[INFO ] 2024-06-25 15:07:57.038 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] schema data cleaned 
[INFO ] 2024-06-25 15:07:57.038 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] monitor closed 
[INFO ] 2024-06-25 15:07:57.039 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] close complete, cost 39 ms 
[WARN ] 2024-06-25 15:08:00.689 - [任务 30][SouceMysql] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 18001, message: Unknown exception occur when operate table: unknown
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-25 15:08:01.286 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 15:08:01.287 - [任务 30] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6c6ae9a0 
[INFO ] 2024-06-25 15:08:01.287 - [任务 30] - Stopped task aspect(s) 
[INFO ] 2024-06-25 15:08:01.287 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 15:08:01.321 - [任务 30] - Remove memory task client succeed, task: 任务 30[667a69e810fc5c6259c5690b] 
[INFO ] 2024-06-25 15:08:01.321 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[667a69e810fc5c6259c5690b] 
[INFO ] 2024-06-25 15:08:55.534 - [任务 30] - Start task milestones: 667a69e810fc5c6259c5690b(任务 30) 
[INFO ] 2024-06-25 15:08:55.534 - [任务 30] - Task initialization... 
[WARN ] 2024-06-25 15:09:02.894 - [任务 30][SouceMysql] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 18001, message: Unknown exception occur when operate table: unknown
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-25 15:09:03.009 - [任务 30] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 15:09:03.009 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 15:09:03.092 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:09:03.092 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:09:03.092 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:09:03.092 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:09:03.092 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:09:03.092 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:09:03.785 - [任务 30][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 15:09:03.785 - [任务 30][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 15:09:03.787 - [任务 30][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-06-25 15:09:03.787 - [任务 30][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-06-25 15:09:03.899 - [任务 30][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 15:09:03.900 - [任务 30][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719299343,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 15:09:03.955 - [任务 30] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 15:09:03.955 - [任务 30][SourceMongo] - Initial sync started 
[INFO ] 2024-06-25 15:09:03.955 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM4, offset: null 
[INFO ] 2024-06-25 15:09:03.961 - [任务 30][SourceMongo] - Table CLAIM4 is going to be initial synced 
[INFO ] 2024-06-25 15:09:03.981 - [任务 30][SourceMongo] - Query table 'CLAIM4' counts: 1 
[INFO ] 2024-06-25 15:09:03.981 - [任务 30][SourceMongo] - Table [CLAIM4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:09:03.981 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM2, offset: null 
[INFO ] 2024-06-25 15:09:03.981 - [任务 30][SourceMongo] - Table CLAIM2 is going to be initial synced 
[INFO ] 2024-06-25 15:09:03.982 - [任务 30][SourceMongo] - Table [CLAIM2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:09:04.021 - [任务 30][SourceMongo] - Query table 'CLAIM2' counts: 1 
[INFO ] 2024-06-25 15:09:04.021 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM3, offset: null 
[INFO ] 2024-06-25 15:09:04.021 - [任务 30][SourceMongo] - Table CLAIM3 is going to be initial synced 
[INFO ] 2024-06-25 15:09:04.028 - [任务 30][SourceMongo] - Query table 'CLAIM3' counts: 1 
[INFO ] 2024-06-25 15:09:04.028 - [任务 30][SourceMongo] - Table [CLAIM3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:09:04.028 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-25 15:09:04.028 - [任务 30][SourceMongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-25 15:09:04.029 - [任务 30][SourceMongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-25 15:09:04.101 - [任务 30][SourceMongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:09:04.102 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:09:04.102 - [任务 30][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-25 15:09:04.102 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:09:04.126 - [任务 30][SourceMongo] - Starting stream read, table list: [CLAIM4, CLAIM2, CLAIM3, CLAIM], offset: {"cdcOffset":1719299343,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 15:09:04.126 - [任务 30][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM4, CLAIM2, CLAIM3, CLAIM], data change syncing 
[INFO ] 2024-06-25 15:09:54.018 - [任务 30] - Stop task milestones: 667a69e810fc5c6259c5690b(任务 30)  
[INFO ] 2024-06-25 15:09:54.020 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] running status set to false 
[INFO ] 2024-06-25 15:09:54.037 - [任务 30][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e871a96c-a577-4440-afa5-537887b45e9b 
[INFO ] 2024-06-25 15:09:54.037 - [任务 30][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e871a96c-a577-4440-afa5-537887b45e9b 
[INFO ] 2024-06-25 15:09:54.037 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] schema data cleaned 
[INFO ] 2024-06-25 15:09:54.038 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] monitor closed 
[INFO ] 2024-06-25 15:09:54.041 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] close complete, cost 32 ms 
[INFO ] 2024-06-25 15:09:54.041 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] running status set to false 
[INFO ] 2024-06-25 15:09:54.041 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] schema data cleaned 
[INFO ] 2024-06-25 15:09:54.041 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] monitor closed 
[INFO ] 2024-06-25 15:09:54.041 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] close complete, cost 0 ms 
[INFO ] 2024-06-25 15:09:54.074 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] running status set to false 
[INFO ] 2024-06-25 15:09:54.075 - [任务 30][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150 
[INFO ] 2024-06-25 15:09:54.075 - [任务 30][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150 
[INFO ] 2024-06-25 15:09:54.075 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] schema data cleaned 
[INFO ] 2024-06-25 15:09:54.075 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] monitor closed 
[INFO ] 2024-06-25 15:09:54.075 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] close complete, cost 32 ms 
[INFO ] 2024-06-25 15:09:54.379 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 15:09:54.379 - [任务 30] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@79be22d1 
[INFO ] 2024-06-25 15:09:54.379 - [任务 30] - Stopped task aspect(s) 
[INFO ] 2024-06-25 15:09:54.379 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 15:09:54.401 - [任务 30] - Remove memory task client succeed, task: 任务 30[667a69e810fc5c6259c5690b] 
[INFO ] 2024-06-25 15:09:54.402 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[667a69e810fc5c6259c5690b] 
[WARN ] 2024-06-25 15:10:02.908 - [任务 30][SouceMysql] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 18001, message: Unknown exception occur when operate table: unknown
 - Remaining retry 8 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-25 15:10:22.602 - [任务 30] - Start task milestones: 667a69e810fc5c6259c5690b(任务 30) 
[INFO ] 2024-06-25 15:10:22.803 - [任务 30] - Task initialization... 
[INFO ] 2024-06-25 15:10:22.803 - [任务 30] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 15:10:22.850 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 15:10:22.929 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:10:22.929 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:10:22.929 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:10:22.929 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:10:22.929 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:10:22.929 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:10:23.578 - [任务 30][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-06-25 15:10:23.578 - [任务 30][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-06-25 15:10:23.578 - [任务 30][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 15:10:23.590 - [任务 30][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 15:10:23.590 - [任务 30][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 15:10:23.723 - [任务 30][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719299423,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 15:10:23.723 - [任务 30] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 15:10:23.785 - [任务 30][SourceMongo] - Initial sync started 
[INFO ] 2024-06-25 15:10:23.785 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM4, offset: null 
[INFO ] 2024-06-25 15:14:05.057 - [任务 30][SourceMongo] - Table CLAIM4 is going to be initial synced 
[INFO ] 2024-06-25 15:14:05.081 - [任务 30][SourceMongo] - Query table 'CLAIM4' counts: 1 
[WARN ] 2024-06-25 15:14:05.083 - [任务 30][SouceMysql] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
 - Remaining retry 7 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-25 15:14:06.928 - [任务 30][SourceMongo] - Table [CLAIM4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:14:07.216 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM2, offset: null 
[INFO ] 2024-06-25 15:14:10.002 - [任务 30][SourceMongo] - Table CLAIM2 is going to be initial synced 
[INFO ] 2024-06-25 15:14:10.026 - [任务 30][SourceMongo] - Query table 'CLAIM2' counts: 1 
[INFO ] 2024-06-25 15:14:10.027 - [任务 30][SourceMongo] - Table [CLAIM2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:14:10.027 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM3, offset: null 
[INFO ] 2024-06-25 15:14:16.035 - [任务 30][SourceMongo] - Table CLAIM3 is going to be initial synced 
[INFO ] 2024-06-25 15:14:16.035 - [任务 30][SourceMongo] - Table [CLAIM3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:14:16.062 - [任务 30][SourceMongo] - Query table 'CLAIM3' counts: 1 
[INFO ] 2024-06-25 15:14:16.063 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-25 15:14:16.070 - [任务 30][SourceMongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-25 15:14:16.281 - [任务 30][SourceMongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-25 15:14:16.336 - [任务 30][SourceMongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:14:16.336 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:14:16.336 - [任务 30][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-25 15:14:16.336 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:14:16.337 - [任务 30][SourceMongo] - Starting stream read, table list: [CLAIM4, CLAIM2, CLAIM3, CLAIM], offset: {"cdcOffset":1719299423,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 15:14:16.544 - [任务 30][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM4, CLAIM2, CLAIM3, CLAIM], data change syncing 
[WARN ] 2024-06-25 15:15:05.140 - [任务 30][SouceMysql] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
 - Remaining retry 6 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-25 15:15:14.378 - [任务 30] - Stop task milestones: 667a69e810fc5c6259c5690b(任务 30)  
[INFO ] 2024-06-25 15:15:14.530 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] running status set to false 
[INFO ] 2024-06-25 15:15:14.541 - [任务 30][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e871a96c-a577-4440-afa5-537887b45e9b 
[INFO ] 2024-06-25 15:15:14.541 - [任务 30][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e871a96c-a577-4440-afa5-537887b45e9b 
[INFO ] 2024-06-25 15:15:14.541 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] schema data cleaned 
[INFO ] 2024-06-25 15:15:14.541 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] monitor closed 
[INFO ] 2024-06-25 15:15:14.542 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] close complete, cost 12 ms 
[INFO ] 2024-06-25 15:15:14.542 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] running status set to false 
[INFO ] 2024-06-25 15:15:14.542 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] schema data cleaned 
[INFO ] 2024-06-25 15:15:14.542 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] monitor closed 
[INFO ] 2024-06-25 15:15:14.542 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] close complete, cost 0 ms 
[INFO ] 2024-06-25 15:15:14.542 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] running status set to false 
[INFO ] 2024-06-25 15:15:14.593 - [任务 30][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150 
[INFO ] 2024-06-25 15:15:14.593 - [任务 30][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150 
[INFO ] 2024-06-25 15:15:14.593 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] schema data cleaned 
[INFO ] 2024-06-25 15:15:14.593 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] monitor closed 
[INFO ] 2024-06-25 15:15:14.794 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] close complete, cost 50 ms 
[INFO ] 2024-06-25 15:15:15.970 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 15:15:15.972 - [任务 30] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@517440fc 
[INFO ] 2024-06-25 15:15:15.972 - [任务 30] - Stopped task aspect(s) 
[INFO ] 2024-06-25 15:15:16.059 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 15:15:16.059 - [任务 30] - Remove memory task client succeed, task: 任务 30[667a69e810fc5c6259c5690b] 
[INFO ] 2024-06-25 15:15:16.059 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[667a69e810fc5c6259c5690b] 
[INFO ] 2024-06-25 15:15:52.153 - [任务 30] - Start task milestones: 667a69e810fc5c6259c5690b(任务 30) 
[INFO ] 2024-06-25 15:15:52.153 - [任务 30] - Task initialization... 
[INFO ] 2024-06-25 15:15:52.270 - [任务 30] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 15:15:52.358 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 15:15:52.359 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:15:52.359 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:15:52.359 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:15:52.359 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:15:52.359 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:15:52.561 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:15:52.908 - [任务 30][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-06-25 15:15:52.908 - [任务 30][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-06-25 15:15:52.908 - [任务 30][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 15:15:53.016 - [任务 30][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719299752,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 15:15:53.016 - [任务 30] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 15:15:53.089 - [任务 30][SourceMongo] - Initial sync started 
[INFO ] 2024-06-25 15:15:53.091 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM4, offset: null 
[INFO ] 2024-06-25 15:15:53.092 - [任务 30][SourceMongo] - Table CLAIM4 is going to be initial synced 
[INFO ] 2024-06-25 15:15:53.097 - [任务 30][SourceMongo] - Table [CLAIM4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:15:53.118 - [任务 30][SourceMongo] - Query table 'CLAIM4' counts: 0 
[INFO ] 2024-06-25 15:15:53.118 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM2, offset: null 
[INFO ] 2024-06-25 15:15:53.131 - [任务 30][SourceMongo] - Table CLAIM2 is going to be initial synced 
[INFO ] 2024-06-25 15:15:53.131 - [任务 30][SourceMongo] - Query table 'CLAIM2' counts: 0 
[INFO ] 2024-06-25 15:15:53.144 - [任务 30][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 15:15:53.144 - [任务 30][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 15:15:53.185 - [任务 30][SourceMongo] - Table [CLAIM2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:15:53.186 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM3, offset: null 
[INFO ] 2024-06-25 15:15:53.186 - [任务 30][SourceMongo] - Table CLAIM3 is going to be initial synced 
[INFO ] 2024-06-25 15:15:53.201 - [任务 30][SourceMongo] - Query table 'CLAIM3' counts: 0 
[INFO ] 2024-06-25 15:15:53.201 - [任务 30][SourceMongo] - Table [CLAIM3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:15:53.201 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-25 15:15:53.204 - [任务 30][SourceMongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-25 15:15:53.233 - [任务 30][SourceMongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-25 15:15:53.485 - [任务 30][SourceMongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:15:53.485 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:15:53.485 - [任务 30][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-25 15:15:53.485 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:15:53.486 - [任务 30][SourceMongo] - Starting stream read, table list: [CLAIM4, CLAIM2, CLAIM3, CLAIM], offset: {"cdcOffset":1719299752,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 15:15:53.689 - [任务 30][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM4, CLAIM2, CLAIM3, CLAIM], data change syncing 
[WARN ] 2024-06-25 15:16:05.281 - [任务 30][SouceMysql] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
 - Remaining retry 5 time(s)
 - Period 60 second(s) 
[WARN ] 2024-06-25 15:17:05.371 - [任务 30][SouceMysql] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
 - Remaining retry 4 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-25 15:17:26.405 - [任务 30] - Stop task milestones: 667a69e810fc5c6259c5690b(任务 30)  
[INFO ] 2024-06-25 15:17:26.441 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] running status set to false 
[INFO ] 2024-06-25 15:17:26.441 - [任务 30][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e871a96c-a577-4440-afa5-537887b45e9b 
[INFO ] 2024-06-25 15:17:26.444 - [任务 30][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e871a96c-a577-4440-afa5-537887b45e9b 
[INFO ] 2024-06-25 15:17:26.444 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] schema data cleaned 
[INFO ] 2024-06-25 15:17:26.457 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] monitor closed 
[INFO ] 2024-06-25 15:17:26.457 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] close complete, cost 23 ms 
[INFO ] 2024-06-25 15:17:26.457 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] running status set to false 
[INFO ] 2024-06-25 15:17:26.457 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] schema data cleaned 
[INFO ] 2024-06-25 15:17:26.457 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] monitor closed 
[INFO ] 2024-06-25 15:17:26.458 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] close complete, cost 4 ms 
[INFO ] 2024-06-25 15:17:26.458 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] running status set to false 
[INFO ] 2024-06-25 15:17:26.469 - [任务 30][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150 
[INFO ] 2024-06-25 15:17:26.469 - [任务 30][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150 
[INFO ] 2024-06-25 15:17:26.469 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] schema data cleaned 
[INFO ] 2024-06-25 15:17:26.469 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] monitor closed 
[INFO ] 2024-06-25 15:17:26.672 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] close complete, cost 12 ms 
[INFO ] 2024-06-25 15:17:31.202 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 15:17:31.203 - [任务 30] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@14d413c0 
[INFO ] 2024-06-25 15:17:31.203 - [任务 30] - Stopped task aspect(s) 
[INFO ] 2024-06-25 15:17:31.237 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 15:17:31.238 - [任务 30] - Remove memory task client succeed, task: 任务 30[667a69e810fc5c6259c5690b] 
[INFO ] 2024-06-25 15:17:31.438 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[667a69e810fc5c6259c5690b] 
[INFO ] 2024-06-25 15:17:35.394 - [任务 30] - Start task milestones: 667a69e810fc5c6259c5690b(任务 30) 
[INFO ] 2024-06-25 15:17:35.602 - [任务 30] - Task initialization... 
[INFO ] 2024-06-25 15:17:35.603 - [任务 30] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 15:17:35.661 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 15:17:35.738 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:17:35.738 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:17:35.738 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:17:35.738 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] preload schema finished, cost 1 ms 
[INFO ] 2024-06-25 15:17:35.738 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] preload schema finished, cost 1 ms 
[INFO ] 2024-06-25 15:17:35.738 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] preload schema finished, cost 1 ms 
[INFO ] 2024-06-25 15:17:36.488 - [任务 30][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-06-25 15:17:36.488 - [任务 30][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-06-25 15:17:36.488 - [任务 30][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 15:17:36.493 - [任务 30][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 15:17:36.493 - [任务 30][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 15:17:36.599 - [任务 30][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719299856,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 15:17:36.599 - [任务 30] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 15:17:36.652 - [任务 30][SourceMongo] - Initial sync started 
[INFO ] 2024-06-25 15:17:36.652 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM4, offset: null 
[INFO ] 2024-06-25 15:17:40.072 - [任务 30][SourceMongo] - Table CLAIM4 is going to be initial synced 
[INFO ] 2024-06-25 15:17:43.331 - [任务 30][SourceMongo] - Table [CLAIM4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:17:50.818 - [任务 30][SourceMongo] - Query table 'CLAIM4' counts: 1 
[INFO ] 2024-06-25 15:17:50.818 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM2, offset: null 
[INFO ] 2024-06-25 15:17:50.890 - [任务 30][SourceMongo] - Table CLAIM2 is going to be initial synced 
[INFO ] 2024-06-25 15:17:50.891 - [任务 30][SourceMongo] - Table [CLAIM2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:17:50.891 - [任务 30][SourceMongo] - Query table 'CLAIM2' counts: 0 
[INFO ] 2024-06-25 15:17:54.988 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM3, offset: null 
[INFO ] 2024-06-25 15:17:56.704 - [任务 30][SourceMongo] - Table CLAIM3 is going to be initial synced 
[INFO ] 2024-06-25 15:17:56.719 - [任务 30][SourceMongo] - Query table 'CLAIM3' counts: 0 
[INFO ] 2024-06-25 15:17:56.719 - [任务 30][SourceMongo] - Table [CLAIM3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:17:56.721 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-25 15:17:59.264 - [任务 30][SourceMongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-25 15:17:59.264 - [任务 30][SourceMongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-25 15:17:59.394 - [任务 30][SourceMongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:17:59.394 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:17:59.394 - [任务 30][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-25 15:17:59.394 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:17:59.396 - [任务 30][SourceMongo] - Starting stream read, table list: [CLAIM4, CLAIM2, CLAIM3, CLAIM], offset: {"cdcOffset":1719299856,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 15:17:59.422 - [任务 30][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM4, CLAIM2, CLAIM3, CLAIM], data change syncing 
[WARN ] 2024-06-25 15:18:05.315 - [任务 30][SouceMysql] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
 - Remaining retry 3 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-25 15:18:19.725 - [任务 30] - Stop task milestones: 667a69e810fc5c6259c5690b(任务 30)  
[INFO ] 2024-06-25 15:18:19.755 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] running status set to false 
[INFO ] 2024-06-25 15:18:19.755 - [任务 30][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e871a96c-a577-4440-afa5-537887b45e9b 
[INFO ] 2024-06-25 15:18:19.755 - [任务 30][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e871a96c-a577-4440-afa5-537887b45e9b 
[INFO ] 2024-06-25 15:18:19.755 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] schema data cleaned 
[INFO ] 2024-06-25 15:18:19.756 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] monitor closed 
[INFO ] 2024-06-25 15:18:19.756 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] close complete, cost 16 ms 
[INFO ] 2024-06-25 15:18:19.756 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] running status set to false 
[INFO ] 2024-06-25 15:18:19.756 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] schema data cleaned 
[INFO ] 2024-06-25 15:18:19.756 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] monitor closed 
[INFO ] 2024-06-25 15:18:19.756 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] close complete, cost 0 ms 
[INFO ] 2024-06-25 15:18:19.768 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] running status set to false 
[INFO ] 2024-06-25 15:18:19.768 - [任务 30][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150 
[INFO ] 2024-06-25 15:18:19.768 - [任务 30][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150 
[INFO ] 2024-06-25 15:18:19.768 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] schema data cleaned 
[INFO ] 2024-06-25 15:18:19.768 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] monitor closed 
[INFO ] 2024-06-25 15:18:19.971 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] close complete, cost 12 ms 
[INFO ] 2024-06-25 15:18:21.645 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 15:18:21.646 - [任务 30] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@138e93b 
[INFO ] 2024-06-25 15:18:21.646 - [任务 30] - Stopped task aspect(s) 
[INFO ] 2024-06-25 15:18:21.646 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 15:18:21.678 - [任务 30] - Remove memory task client succeed, task: 任务 30[667a69e810fc5c6259c5690b] 
[INFO ] 2024-06-25 15:18:21.679 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[667a69e810fc5c6259c5690b] 
[INFO ] 2024-06-25 15:18:25.105 - [任务 30] - Start task milestones: 667a69e810fc5c6259c5690b(任务 30) 
[INFO ] 2024-06-25 15:18:25.267 - [任务 30] - Task initialization... 
[INFO ] 2024-06-25 15:18:25.267 - [任务 30] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 15:18:25.354 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 15:18:25.354 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:18:25.354 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:18:25.354 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:18:25.354 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:18:25.355 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:18:25.355 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:18:26.079 - [任务 30][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-06-25 15:18:26.080 - [任务 30][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-06-25 15:18:26.080 - [任务 30][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 15:18:26.093 - [任务 30][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 15:18:26.093 - [任务 30][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 15:18:26.194 - [任务 30][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719299906,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 15:18:26.194 - [任务 30] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 15:18:26.248 - [任务 30][SourceMongo] - Initial sync started 
[INFO ] 2024-06-25 15:18:26.248 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM4, offset: null 
[INFO ] 2024-06-25 15:18:26.260 - [任务 30][SourceMongo] - Table CLAIM4 is going to be initial synced 
[INFO ] 2024-06-25 15:18:26.260 - [任务 30][SourceMongo] - Table [CLAIM4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:18:26.303 - [任务 30][SourceMongo] - Query table 'CLAIM4' counts: 1 
[INFO ] 2024-06-25 15:18:26.305 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM2, offset: null 
[INFO ] 2024-06-25 15:18:26.309 - [任务 30][SourceMongo] - Table CLAIM2 is going to be initial synced 
[INFO ] 2024-06-25 15:18:26.309 - [任务 30][SourceMongo] - Query table 'CLAIM2' counts: 0 
[INFO ] 2024-06-25 15:18:26.310 - [任务 30][SourceMongo] - Table [CLAIM2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:18:26.311 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM3, offset: null 
[INFO ] 2024-06-25 15:18:26.312 - [任务 30][SourceMongo] - Table CLAIM3 is going to be initial synced 
[INFO ] 2024-06-25 15:18:26.312 - [任务 30][SourceMongo] - Query table 'CLAIM3' counts: 0 
[INFO ] 2024-06-25 15:18:26.313 - [任务 30][SourceMongo] - Table [CLAIM3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:18:26.313 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-25 15:18:26.314 - [任务 30][SourceMongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-25 15:18:26.315 - [任务 30][SourceMongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-25 15:18:26.433 - [任务 30][SourceMongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:18:26.436 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:18:26.436 - [任务 30][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-25 15:18:26.436 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:18:26.438 - [任务 30][SourceMongo] - Starting stream read, table list: [CLAIM4, CLAIM2, CLAIM3, CLAIM], offset: {"cdcOffset":1719299906,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 15:18:26.651 - [任务 30][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM4, CLAIM2, CLAIM3, CLAIM], data change syncing 
[WARN ] 2024-06-25 15:19:05.234 - [任务 30][SouceMysql] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
 - Remaining retry 2 time(s)
 - Period 60 second(s) 
[WARN ] 2024-06-25 15:20:35.047 - [任务 30][SouceMysql] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
 - Remaining retry 1 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-25 15:21:59.484 - [任务 30][SouceMysql] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@36e4f34e: {"after":{"_id":"{\"oid\":\"667a696c1debeccdbb015c07\"}","SETTLED_DATE":"{\"date\":\"2006-12-07T09:07:28.000Z\"}","CLAIM_ID":"2","SETTLED_AMOUNT":325,"CLAIM_REASON":"zH25vvtvdx","POLICY_ID":"WIQZevvvv2Rl","CLAIM_DATE":"2001-10-08 17:22:39.000000","LAST_CHANGE":"{\"date\":\"2012-01-20T14:33:09.000Z\"}"},"containsIllegalDate":false,"referenceTime":1719299018000,"tableId":"WCLAIM3","time":1719299018974,"type":300}, nodeIds=[e871a96c-a577-4440-afa5-537887b45e9b, 4c01a6e2-dda2-4faa-bf16-4b324edd3daf], sourceTime=1719299018000, sourceSerialNo=null} 
[ERROR] 2024-06-25 15:21:59.515 - [任务 30][SouceMysql] - java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@36e4f34e: {"after":{"_id":"{\"oid\":\"667a696c1debeccdbb015c07\"}","SETTLED_DATE":"{\"date\":\"2006-12-07T09:07:28.000Z\"}","CLAIM_ID":"2","SETTLED_AMOUNT":325,"CLAIM_REASON":"zH25vvtvdx","POLICY_ID":"WIQZevvvv2Rl","CLAIM_DATE":"2001-10-08 17:22:39.000000","LAST_CHANGE":"{\"date\":\"2012-01-20T14:33:09.000Z\"}"},"containsIllegalDate":false,"referenceTime":1719299018000,"tableId":"WCLAIM3","time":1719299018974,"type":300}, nodeIds=[e871a96c-a577-4440-afa5-537887b45e9b, 4c01a6e2-dda2-4faa-bf16-4b324edd3daf], sourceTime=1719299018000, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@36e4f34e: {"after":{"_id":"{\"oid\":\"667a696c1debeccdbb015c07\"}","SETTLED_DATE":"{\"date\":\"2006-12-07T09:07:28.000Z\"}","CLAIM_ID":"2","SETTLED_AMOUNT":325,"CLAIM_REASON":"zH25vvtvdx","POLICY_ID":"WIQZevvvv2Rl","CLAIM_DATE":"2001-10-08 17:22:39.000000","LAST_CHANGE":"{\"date\":\"2012-01-20T14:33:09.000Z\"}"},"containsIllegalDate":false,"referenceTime":1719299018000,"tableId":"WCLAIM3","time":1719299018974,"type":300}, nodeIds=[e871a96c-a577-4440-afa5-537887b45e9b, 4c01a6e2-dda2-4faa-bf16-4b324edd3daf], sourceTime=1719299018000, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:52)
	io.tapdata.connector.mysql.dml.MysqlRecordWriter.<init>(MysqlRecordWriter.java:17)
	io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:431)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:851)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:625)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:594)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:524)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:493)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:539)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:497)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:497)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:680)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:617)
	... 12 more
Caused by: Execute PDK method: TARGET_WRITE_RECORD, tableName: WCLAIM3
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:857)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:803)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: java.lang.NullPointerException
	at io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:52)
	at io.tapdata.connector.mysql.dml.MysqlRecordWriter.<init>(MysqlRecordWriter.java:17)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:431)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:851)
	... 28 more

[INFO ] 2024-06-25 15:21:59.526 - [任务 30][SouceMysql] - Job suspend in error handle 
[INFO ] 2024-06-25 15:21:59.845 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] running status set to false 
[INFO ] 2024-06-25 15:21:59.845 - [任务 30][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e871a96c-a577-4440-afa5-537887b45e9b 
[INFO ] 2024-06-25 15:21:59.847 - [任务 30][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e871a96c-a577-4440-afa5-537887b45e9b 
[INFO ] 2024-06-25 15:21:59.847 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] schema data cleaned 
[INFO ] 2024-06-25 15:21:59.853 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] monitor closed 
[INFO ] 2024-06-25 15:21:59.853 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] close complete, cost 44 ms 
[INFO ] 2024-06-25 15:21:59.853 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] running status set to false 
[INFO ] 2024-06-25 15:21:59.853 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] schema data cleaned 
[INFO ] 2024-06-25 15:21:59.854 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] monitor closed 
[INFO ] 2024-06-25 15:21:59.854 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] close complete, cost 1 ms 
[INFO ] 2024-06-25 15:21:59.854 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] running status set to false 
[INFO ] 2024-06-25 15:21:59.899 - [任务 30][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150 
[INFO ] 2024-06-25 15:21:59.899 - [任务 30][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150 
[INFO ] 2024-06-25 15:21:59.900 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] schema data cleaned 
[INFO ] 2024-06-25 15:21:59.900 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] monitor closed 
[INFO ] 2024-06-25 15:22:00.105 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] close complete, cost 46 ms 
[INFO ] 2024-06-25 15:22:00.506 - [任务 30][SourceMongo] - Incremental sync completed 
[INFO ] 2024-06-25 15:22:04.308 - [任务 30] - Task [任务 30] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-06-25 15:22:04.332 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 15:22:04.335 - [任务 30] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5fd7c604 
[INFO ] 2024-06-25 15:22:04.353 - [任务 30] - Stop task milestones: 667a69e810fc5c6259c5690b(任务 30)  
[INFO ] 2024-06-25 15:22:04.354 - [任务 30] - Stopped task aspect(s) 
[INFO ] 2024-06-25 15:22:04.354 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 15:22:04.429 - [任务 30] - Remove memory task client succeed, task: 任务 30[667a69e810fc5c6259c5690b] 
[INFO ] 2024-06-25 15:22:04.431 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[667a69e810fc5c6259c5690b] 
[INFO ] 2024-06-25 15:24:21.372 - [任务 30] - Start task milestones: 667a69e810fc5c6259c5690b(任务 30) 
[INFO ] 2024-06-25 15:24:21.372 - [任务 30] - Task initialization... 
[INFO ] 2024-06-25 15:24:21.525 - [任务 30] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 15:24:21.625 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 15:24:21.625 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:24:21.625 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:24:21.625 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:24:21.625 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:24:21.625 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:24:21.830 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:24:22.142 - [任务 30][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-06-25 15:24:22.142 - [任务 30][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-06-25 15:24:22.142 - [任务 30][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 15:24:22.247 - [任务 30][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719300262,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 15:24:22.247 - [任务 30] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 15:24:22.318 - [任务 30][SourceMongo] - Initial sync started 
[INFO ] 2024-06-25 15:24:22.320 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM4, offset: null 
[INFO ] 2024-06-25 15:24:22.321 - [任务 30][SourceMongo] - Table CLAIM4 is going to be initial synced 
[INFO ] 2024-06-25 15:24:22.339 - [任务 30][SourceMongo] - Table [CLAIM4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:24:22.340 - [任务 30][SourceMongo] - Query table 'CLAIM4' counts: 1 
[INFO ] 2024-06-25 15:24:22.340 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM2, offset: null 
[INFO ] 2024-06-25 15:24:22.340 - [任务 30][SourceMongo] - Table CLAIM2 is going to be initial synced 
[INFO ] 2024-06-25 15:24:22.401 - [任务 30][SourceMongo] - Query table 'CLAIM2' counts: 1 
[INFO ] 2024-06-25 15:24:22.401 - [任务 30][SourceMongo] - Table [CLAIM2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:24:22.401 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM3, offset: null 
[INFO ] 2024-06-25 15:24:22.401 - [任务 30][SourceMongo] - Table CLAIM3 is going to be initial synced 
[INFO ] 2024-06-25 15:24:22.407 - [任务 30][SourceMongo] - Table [CLAIM3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:24:22.407 - [任务 30][SourceMongo] - Query table 'CLAIM3' counts: 1 
[INFO ] 2024-06-25 15:24:22.407 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-25 15:24:22.407 - [任务 30][SourceMongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-25 15:24:22.408 - [任务 30][SourceMongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-25 15:24:22.426 - [任务 30][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 15:24:22.426 - [任务 30][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 15:24:22.768 - [任务 30][SourceMongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:24:22.769 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:24:22.770 - [任务 30][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-25 15:24:22.770 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:24:22.773 - [任务 30][SourceMongo] - Starting stream read, table list: [CLAIM4, CLAIM2, CLAIM3, CLAIM], offset: {"cdcOffset":1719300262,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 15:24:22.978 - [任务 30][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM4, CLAIM2, CLAIM3, CLAIM], data change syncing 
[INFO ] 2024-06-25 15:25:22.300 - [任务 30][SourceMongo] - Found new table(s): [CLAIM5] 
[INFO ] 2024-06-25 15:25:22.300 - [任务 30][SourceMongo] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-06-25 15:25:22.342 - [任务 30][SourceMongo] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@2336eb55: {"table":{"defaultPrimaryKeys":["_id"],"id":"CLAIM5","indexList":[{"indexFields":[{}],"name":"__t__{\"v\": 2, \"key\": {\"_id\": 1}, \"name\": \"_id_\", \"ns\": \"DevSource.CLAIM5\"}"}],"maxPKPos":1,"maxPos":8,"name":"CLAIM5","nameFieldMap":{"_id":{"autoInc":false,"dataType":"OBJECT_ID","name":"_id","nullable":true,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bytes":24,"type":10},"virtual":false},"CLAIM_ID":{"autoInc":false,"dataType":"STRING(100)","name":"CLAIM_ID","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"tapType":{"bytes":100,"type":10},"virtual":false},"SETTLED_AMOUNT":{"autoInc":false,"dataType":"INT32","name":"SETTLED_AMOUNT","nullable":true,"partitionKey":false,"pos":3,"primaryKey":false,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"dataType":"NULL","name":"name","nullable":true,"partitionKey":false,"pos":4,"primaryKey":false,"tapType":{"bytes":9223372036854775807,"type":10},"virtual":false},"CLAIM_REASON":{"autoInc":false,"dataType":"STRING(100)","name":"CLAIM_REASON","nullable":true,"partitionKey":false,"pos":5,"primaryKey":false,"tapType":{"bytes":100,"type":10},"virtual":false},"CLAIM_TYPE":{"autoInc":false,"dataType":"NULL","name":"CLAIM_TYPE","nullable":true,"partitionKey":false,"pos":6,"primaryKey":false,"tapType":{"bytes":9223372036854775807,"type":10},"virtual":false},"POLICY_ID":{"autoInc":false,"dataType":"STRING(100)","name":"POLICY_ID","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":100,"type":10},"virtual":false},"CLAIM_AMOUNT":{"autoInc":false,"dataType":"NULL","name":"CLAIM_AMOUNT","nullable":true,"partitionKey":false,"pos":8,"primaryKey":false,"tapType":{"bytes":9223372036854775807,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"_id"}],"indexMap":{"_id":{"fieldAsc":true,"name":"_id"}},"unique":true},"tableAttr":{"size":147,"ns":"DevSource.CLAIM5","capped":false,"storageSize":4096,"avgObjSize":147,"shard":{}}},"tableId":"CLAIM5","type":206} 
[INFO ] 2024-06-25 15:25:22.343 - [任务 30][SourceMongo] - Create new table in memory, qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT_CLAIM5_6674feb868ca1e3afc2a0d99_667a69e810fc5c6259c5690b 
[INFO ] 2024-06-25 15:25:22.420 - [任务 30][SourceMongo] - Create new table schema transform finished: TapTable id CLAIM5 name CLAIM5 storageEngine null charset null number of fields 8 
[INFO ] 2024-06-25 15:25:22.420 - [任务 30][SourceMongo] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-06-25 15:25:22.584 - [任务 30][SourceMongo] - Starting batch read, table name: CLAIM5, offset: null 
[INFO ] 2024-06-25 15:25:22.584 - [任务 30][SourceMongo] - Table CLAIM5 is going to be initial synced 
[INFO ] 2024-06-25 15:25:22.634 - [任务 30][SourceMongo] - Query table 'CLAIM5' counts: 1 
[INFO ] 2024-06-25 15:25:22.637 - [任务 30][SourceMongo] - Table [CLAIM5] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:25:22.645 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:25:22.645 - [任务 30][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-25 15:25:22.645 - [任务 30][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 15:25:22.646 - [任务 30][SourceMongo] - Starting stream read, table list: [CLAIM4, CLAIM5, CLAIM2, CLAIM3, CLAIM], offset: {"cdcOffset":1719300262,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 15:25:22.851 - [任务 30][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM4, CLAIM5, CLAIM2, CLAIM3, CLAIM], data change syncing 
[INFO ] 2024-06-25 15:25:23.116 - [任务 30][SourceMongo] - Incremental sync completed 
[INFO ] 2024-06-25 15:48:26.504 - [任务 30] - Stop task milestones: 667a69e810fc5c6259c5690b(任务 30)  
[INFO ] 2024-06-25 15:48:26.823 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] running status set to false 
[INFO ] 2024-06-25 15:48:26.842 - [任务 30][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-e871a96c-a577-4440-afa5-537887b45e9b 
[INFO ] 2024-06-25 15:48:26.842 - [任务 30][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-e871a96c-a577-4440-afa5-537887b45e9b 
[INFO ] 2024-06-25 15:48:26.842 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] schema data cleaned 
[INFO ] 2024-06-25 15:48:26.843 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] monitor closed 
[INFO ] 2024-06-25 15:48:26.845 - [任务 30][SourceMongo] - Node SourceMongo[e871a96c-a577-4440-afa5-537887b45e9b] close complete, cost 25 ms 
[INFO ] 2024-06-25 15:48:26.845 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] running status set to false 
[INFO ] 2024-06-25 15:48:26.845 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] schema data cleaned 
[INFO ] 2024-06-25 15:48:26.845 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] monitor closed 
[INFO ] 2024-06-25 15:48:26.845 - [任务 30][表编辑] - Node 表编辑[4c01a6e2-dda2-4faa-bf16-4b324edd3daf] close complete, cost 0 ms 
[INFO ] 2024-06-25 15:48:26.845 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] running status set to false 
[INFO ] 2024-06-25 15:48:26.868 - [任务 30][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150 
[INFO ] 2024-06-25 15:48:26.869 - [任务 30][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150 
[INFO ] 2024-06-25 15:48:26.869 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] schema data cleaned 
[INFO ] 2024-06-25 15:48:26.869 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] monitor closed 
[INFO ] 2024-06-25 15:48:27.071 - [任务 30][SouceMysql] - Node SouceMysql[b98b9e8f-37d1-4dfa-b2c8-9b1edbe3b150] close complete, cost 24 ms 
[INFO ] 2024-06-25 15:48:30.994 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 15:48:30.994 - [任务 30] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@58a1824 
[INFO ] 2024-06-25 15:48:30.994 - [任务 30] - Stopped task aspect(s) 
[INFO ] 2024-06-25 15:48:31.018 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 15:48:31.024 - [任务 30] - Remove memory task client succeed, task: 任务 30[667a69e810fc5c6259c5690b] 
[INFO ] 2024-06-25 15:48:31.025 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[667a69e810fc5c6259c5690b] 
