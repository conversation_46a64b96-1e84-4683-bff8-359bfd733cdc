[INFO ] 2024-07-02 17:30:45.072 - [Heartbeat-SouceMysql] - Start task milestones: 6683c8c4fa7caf4ccea134fb(Heartbeat-SouceMysql) 
[INFO ] 2024-07-02 17:30:45.192 - [Heartbeat-SouceMysql] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:30:45.193 - [Heartbeat-SouceMysql] - The engine receives Heartbeat-SouceMysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 17:30:45.247 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] start preload schema,table counts: 1 
[INFO ] 2024-07-02 17:30:45.248 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] start preload schema,table counts: 1 
[INFO ] 2024-07-02 17:30:45.248 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] preload schema finished, cost 1 ms 
[INFO ] 2024-07-02 17:30:45.248 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] preload schema finished, cost 1 ms 
[INFO ] 2024-07-02 17:30:46.509 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-02 17:30:46.522 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-02 17:30:46.523 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-02 17:30:46.527 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-02 17:30:46.559 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1719912646502,"lastTimes":1719912646502,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-02 17:30:46.615 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-02 17:30:46.624 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-02 17:30:46.655 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-02 17:30:46.661 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-02 17:30:46.662 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-02 17:30:46.676 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1719912646502,"lastTimes":1719912646502,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-02 17:30:46.684 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-02 17:30:46.695 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-02 17:33:17.975 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] running status set to false 
[INFO ] 2024-07-02 17:33:17.982 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-02 17:33:17.989 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-0fec48c8-5a07-483d-aa8f-1a08d3f30d1a 
[INFO ] 2024-07-02 17:33:17.990 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-0fec48c8-5a07-483d-aa8f-1a08d3f30d1a 
[INFO ] 2024-07-02 17:33:17.990 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] schema data cleaned 
[INFO ] 2024-07-02 17:33:17.990 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] monitor closed 
[INFO ] 2024-07-02 17:33:17.992 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] close complete, cost 30 ms 
[INFO ] 2024-07-02 17:33:17.992 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] running status set to false 
[INFO ] 2024-07-02 17:33:18.006 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-6ab7ec53-3115-4c0f-80f3-08ca3fafad48 
[INFO ] 2024-07-02 17:33:18.007 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-6ab7ec53-3115-4c0f-80f3-08ca3fafad48 
[INFO ] 2024-07-02 17:33:18.007 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] schema data cleaned 
[INFO ] 2024-07-02 17:33:18.007 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] monitor closed 
[INFO ] 2024-07-02 17:33:18.210 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] close complete, cost 15 ms 
[INFO ] 2024-07-02 17:33:22.283 - [Heartbeat-SouceMysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:33:22.283 - [Heartbeat-SouceMysql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7e8eeb2a 
[INFO ] 2024-07-02 17:33:22.427 - [Heartbeat-SouceMysql] - Stop task milestones: 6683c8c4fa7caf4ccea134fb(Heartbeat-SouceMysql)  
[INFO ] 2024-07-02 17:33:22.428 - [Heartbeat-SouceMysql] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:33:22.428 - [Heartbeat-SouceMysql] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 17:33:22.492 - [Heartbeat-SouceMysql] - Remove memory task client succeed, task: Heartbeat-SouceMysql[6683c8c4fa7caf4ccea134fb] 
[INFO ] 2024-07-02 17:33:22.493 - [Heartbeat-SouceMysql] - Destroy memory task client cache succeed, task: Heartbeat-SouceMysql[6683c8c4fa7caf4ccea134fb] 
[INFO ] 2024-07-02 17:59:09.268 - [Heartbeat-SouceMysql] - Start task milestones: 6683c8c4fa7caf4ccea134fb(Heartbeat-SouceMysql) 
[INFO ] 2024-07-02 17:59:09.435 - [Heartbeat-SouceMysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:59:09.508 - [Heartbeat-SouceMysql] - The engine receives Heartbeat-SouceMysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 17:59:09.508 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] start preload schema,table counts: 1 
[INFO ] 2024-07-02 17:59:09.509 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] start preload schema,table counts: 1 
[INFO ] 2024-07-02 17:59:09.509 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 17:59:09.509 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 17:59:09.568 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-02 17:59:09.579 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-02 17:59:09.579 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 17:59:09.628 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - batch offset found: {"_tapdata_heartbeat_table":{"batch_read_connector_offset":{"syncStage":"Initial","beginTimes":1719912646621,"lastTimes":1719912646627,"lastTN":1,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1,"updateTotals":0,"deleteTotals":0}}},"batch_read_connector_status":"RUNNING"}},stream offset found: {"syncStage":"Incremental","beginTimes":1719912646502,"lastTimes":1719912797454,"lastTN":137,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":136,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-02 17:59:09.628 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1719912646502,"lastTimes":1719912797454,"lastTN":137,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":136,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-02 17:59:09.629 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-02 17:59:09.629 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-02 17:59:09.832 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-02 17:59:38.230 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] running status set to false 
[INFO ] 2024-07-02 17:59:38.231 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-02 17:59:38.235 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-0fec48c8-5a07-483d-aa8f-1a08d3f30d1a 
[INFO ] 2024-07-02 17:59:38.235 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-0fec48c8-5a07-483d-aa8f-1a08d3f30d1a 
[INFO ] 2024-07-02 17:59:38.235 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] schema data cleaned 
[INFO ] 2024-07-02 17:59:38.235 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] monitor closed 
[INFO ] 2024-07-02 17:59:38.240 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] close complete, cost 22 ms 
[INFO ] 2024-07-02 17:59:38.240 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] running status set to false 
[INFO ] 2024-07-02 17:59:38.255 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-6ab7ec53-3115-4c0f-80f3-08ca3fafad48 
[INFO ] 2024-07-02 17:59:38.255 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-6ab7ec53-3115-4c0f-80f3-08ca3fafad48 
[INFO ] 2024-07-02 17:59:38.256 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] schema data cleaned 
[INFO ] 2024-07-02 17:59:38.256 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] monitor closed 
[INFO ] 2024-07-02 17:59:38.460 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] close complete, cost 20 ms 
[INFO ] 2024-07-02 17:59:43.082 - [Heartbeat-SouceMysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:59:43.085 - [Heartbeat-SouceMysql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@580c21e1 
[INFO ] 2024-07-02 17:59:43.085 - [Heartbeat-SouceMysql] - Stop task milestones: 6683c8c4fa7caf4ccea134fb(Heartbeat-SouceMysql)  
[INFO ] 2024-07-02 17:59:43.218 - [Heartbeat-SouceMysql] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:59:43.219 - [Heartbeat-SouceMysql] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 17:59:43.246 - [Heartbeat-SouceMysql] - Remove memory task client succeed, task: Heartbeat-SouceMysql[6683c8c4fa7caf4ccea134fb] 
[INFO ] 2024-07-02 17:59:43.253 - [Heartbeat-SouceMysql] - Destroy memory task client cache succeed, task: Heartbeat-SouceMysql[6683c8c4fa7caf4ccea134fb] 
[INFO ] 2024-07-02 17:59:51.564 - [Heartbeat-SouceMysql] - Start task milestones: 6683c8c4fa7caf4ccea134fb(Heartbeat-SouceMysql) 
[INFO ] 2024-07-02 17:59:52.628 - [Heartbeat-SouceMysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:59:52.629 - [Heartbeat-SouceMysql] - The engine receives Heartbeat-SouceMysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 17:59:52.670 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] start preload schema,table counts: 1 
[INFO ] 2024-07-02 17:59:52.671 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] start preload schema,table counts: 1 
[INFO ] 2024-07-02 17:59:52.671 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 17:59:52.671 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 17:59:52.701 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-02 17:59:52.701 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-02 17:59:52.703 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 17:59:52.703 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1719912646502,"lastTimes":1719914377708,"lastTN":29,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":165,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-02 17:59:52.751 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1719912646502,"lastTimes":1719914377708,"lastTN":29,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":165,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-02 17:59:52.751 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-02 17:59:52.881 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-02 17:59:52.881 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-02 18:00:06.791 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] running status set to false 
[INFO ] 2024-07-02 18:00:06.793 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-02 18:00:06.797 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-0fec48c8-5a07-483d-aa8f-1a08d3f30d1a 
[INFO ] 2024-07-02 18:00:06.797 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-0fec48c8-5a07-483d-aa8f-1a08d3f30d1a 
[INFO ] 2024-07-02 18:00:06.797 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] schema data cleaned 
[INFO ] 2024-07-02 18:00:06.797 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] monitor closed 
[INFO ] 2024-07-02 18:00:06.801 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] close complete, cost 13 ms 
[INFO ] 2024-07-02 18:00:06.801 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] running status set to false 
[INFO ] 2024-07-02 18:00:06.826 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-6ab7ec53-3115-4c0f-80f3-08ca3fafad48 
[INFO ] 2024-07-02 18:00:06.827 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-6ab7ec53-3115-4c0f-80f3-08ca3fafad48 
[INFO ] 2024-07-02 18:00:06.827 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] schema data cleaned 
[INFO ] 2024-07-02 18:00:06.828 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] monitor closed 
[INFO ] 2024-07-02 18:00:06.829 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] close complete, cost 27 ms 
[INFO ] 2024-07-02 18:00:09.667 - [Heartbeat-SouceMysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 18:00:09.667 - [Heartbeat-SouceMysql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@708bcfe4 
[INFO ] 2024-07-02 18:00:09.667 - [Heartbeat-SouceMysql] - Stop task milestones: 6683c8c4fa7caf4ccea134fb(Heartbeat-SouceMysql)  
[INFO ] 2024-07-02 18:00:09.792 - [Heartbeat-SouceMysql] - Stopped task aspect(s) 
[INFO ] 2024-07-02 18:00:09.792 - [Heartbeat-SouceMysql] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 18:00:09.813 - [Heartbeat-SouceMysql] - Remove memory task client succeed, task: Heartbeat-SouceMysql[6683c8c4fa7caf4ccea134fb] 
[INFO ] 2024-07-02 18:00:09.816 - [Heartbeat-SouceMysql] - Destroy memory task client cache succeed, task: Heartbeat-SouceMysql[6683c8c4fa7caf4ccea134fb] 
[INFO ] 2024-07-02 18:00:15.652 - [Heartbeat-SouceMysql] - Start task milestones: 6683c8c4fa7caf4ccea134fb(Heartbeat-SouceMysql) 
[INFO ] 2024-07-02 18:00:15.759 - [Heartbeat-SouceMysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 18:00:15.760 - [Heartbeat-SouceMysql] - The engine receives Heartbeat-SouceMysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 18:00:15.798 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] start preload schema,table counts: 1 
[INFO ] 2024-07-02 18:00:15.798 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] start preload schema,table counts: 1 
[INFO ] 2024-07-02 18:00:15.798 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 18:00:15.798 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] preload schema finished, cost 1 ms 
[INFO ] 2024-07-02 18:00:15.846 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-02 18:00:15.847 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-02 18:00:15.847 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 18:00:15.909 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1719912646502,"lastTimes":1719914406776,"lastTN":11,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":176,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-02 18:00:15.910 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1719912646502,"lastTimes":1719914406776,"lastTN":11,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":176,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-02 18:00:15.911 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-02 18:00:15.911 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-02 18:00:16.114 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-02 18:00:26.456 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] running status set to false 
[INFO ] 2024-07-02 18:00:26.460 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-02 18:00:26.466 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-0fec48c8-5a07-483d-aa8f-1a08d3f30d1a 
[INFO ] 2024-07-02 18:00:26.466 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-0fec48c8-5a07-483d-aa8f-1a08d3f30d1a 
[INFO ] 2024-07-02 18:00:26.466 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] schema data cleaned 
[INFO ] 2024-07-02 18:00:26.467 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] monitor closed 
[INFO ] 2024-07-02 18:00:26.470 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] close complete, cost 18 ms 
[INFO ] 2024-07-02 18:00:26.471 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] running status set to false 
[INFO ] 2024-07-02 18:00:26.493 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-6ab7ec53-3115-4c0f-80f3-08ca3fafad48 
[INFO ] 2024-07-02 18:00:26.493 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-6ab7ec53-3115-4c0f-80f3-08ca3fafad48 
[INFO ] 2024-07-02 18:00:26.493 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] schema data cleaned 
[INFO ] 2024-07-02 18:00:26.493 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] monitor closed 
[INFO ] 2024-07-02 18:00:26.699 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] close complete, cost 23 ms 
[INFO ] 2024-07-02 18:00:30.651 - [Heartbeat-SouceMysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 18:00:30.652 - [Heartbeat-SouceMysql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@81be05f 
[INFO ] 2024-07-02 18:00:30.789 - [Heartbeat-SouceMysql] - Stop task milestones: 6683c8c4fa7caf4ccea134fb(Heartbeat-SouceMysql)  
[INFO ] 2024-07-02 18:00:30.789 - [Heartbeat-SouceMysql] - Stopped task aspect(s) 
[INFO ] 2024-07-02 18:00:30.789 - [Heartbeat-SouceMysql] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 18:00:30.813 - [Heartbeat-SouceMysql] - Remove memory task client succeed, task: Heartbeat-SouceMysql[6683c8c4fa7caf4ccea134fb] 
[INFO ] 2024-07-02 18:00:30.816 - [Heartbeat-SouceMysql] - Destroy memory task client cache succeed, task: Heartbeat-SouceMysql[6683c8c4fa7caf4ccea134fb] 
[INFO ] 2024-07-02 18:00:36.598 - [Heartbeat-SouceMysql] - Start task milestones: 6683c8c4fa7caf4ccea134fb(Heartbeat-SouceMysql) 
[INFO ] 2024-07-02 18:02:10.964 - [Heartbeat-SouceMysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 18:02:10.964 - [Heartbeat-SouceMysql] - The engine receives Heartbeat-SouceMysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 18:02:11.041 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] start preload schema,table counts: 1 
[INFO ] 2024-07-02 18:02:11.042 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] start preload schema,table counts: 1 
[INFO ] 2024-07-02 18:02:11.042 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 18:02:11.042 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 18:02:11.110 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-02 18:02:11.113 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-02 18:02:11.113 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 18:02:11.113 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1719912646502,"lastTimes":1719914425948,"lastTN":11,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":187,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-02 18:02:11.168 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1719912646502,"lastTimes":1719914425948,"lastTN":11,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":187,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-02 18:02:11.169 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-02 18:02:11.169 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-02 18:02:11.369 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-02 18:09:37.544 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] running status set to false 
[INFO ] 2024-07-02 18:09:37.563 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-02 18:09:37.563 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-0fec48c8-5a07-483d-aa8f-1a08d3f30d1a 
[INFO ] 2024-07-02 18:09:37.564 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-0fec48c8-5a07-483d-aa8f-1a08d3f30d1a 
[INFO ] 2024-07-02 18:09:37.564 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] schema data cleaned 
[INFO ] 2024-07-02 18:09:37.564 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] monitor closed 
[INFO ] 2024-07-02 18:09:37.565 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] close complete, cost 28 ms 
[INFO ] 2024-07-02 18:09:37.565 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] running status set to false 
[INFO ] 2024-07-02 18:09:37.582 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-6ab7ec53-3115-4c0f-80f3-08ca3fafad48 
[INFO ] 2024-07-02 18:09:37.582 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-6ab7ec53-3115-4c0f-80f3-08ca3fafad48 
[INFO ] 2024-07-02 18:09:37.582 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] schema data cleaned 
[INFO ] 2024-07-02 18:09:37.582 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] monitor closed 
[INFO ] 2024-07-02 18:09:37.582 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] close complete, cost 17 ms 
[INFO ] 2024-07-02 18:09:40.404 - [Heartbeat-SouceMysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 18:09:40.404 - [Heartbeat-SouceMysql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@74789301 
[INFO ] 2024-07-02 18:09:40.404 - [Heartbeat-SouceMysql] - Stop task milestones: 6683c8c4fa7caf4ccea134fb(Heartbeat-SouceMysql)  
[INFO ] 2024-07-02 18:09:40.529 - [Heartbeat-SouceMysql] - Stopped task aspect(s) 
[INFO ] 2024-07-02 18:09:40.529 - [Heartbeat-SouceMysql] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 18:09:40.545 - [Heartbeat-SouceMysql] - Remove memory task client succeed, task: Heartbeat-SouceMysql[6683c8c4fa7caf4ccea134fb] 
[INFO ] 2024-07-02 18:09:40.549 - [Heartbeat-SouceMysql] - Destroy memory task client cache succeed, task: Heartbeat-SouceMysql[6683c8c4fa7caf4ccea134fb] 
[INFO ] 2024-07-02 18:10:00.983 - [Heartbeat-SouceMysql] - Start task milestones: 6683c8c4fa7caf4ccea134fb(Heartbeat-SouceMysql) 
[INFO ] 2024-07-02 18:10:01.178 - [Heartbeat-SouceMysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 18:10:01.331 - [Heartbeat-SouceMysql] - The engine receives Heartbeat-SouceMysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 18:10:01.332 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] start preload schema,table counts: 1 
[INFO ] 2024-07-02 18:10:01.332 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] start preload schema,table counts: 1 
[INFO ] 2024-07-02 18:10:01.332 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 18:10:01.332 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 18:10:01.388 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-02 18:10:01.388 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-02 18:10:01.390 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 18:10:01.390 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1719912646502,"lastTimes":1719914977531,"lastTN":446,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":633,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-02 18:10:01.448 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1719912646502,"lastTimes":1719914977531,"lastTN":446,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":633,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-02 18:10:01.448 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-02 18:10:01.448 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-02 18:10:01.574 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-02 18:15:10.357 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] running status set to false 
[INFO ] 2024-07-02 18:15:10.358 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-02 18:15:10.373 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-0fec48c8-5a07-483d-aa8f-1a08d3f30d1a 
[INFO ] 2024-07-02 18:15:10.374 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-0fec48c8-5a07-483d-aa8f-1a08d3f30d1a 
[INFO ] 2024-07-02 18:15:10.374 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] schema data cleaned 
[INFO ] 2024-07-02 18:15:10.376 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] monitor closed 
[INFO ] 2024-07-02 18:15:10.376 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[0fec48c8-5a07-483d-aa8f-1a08d3f30d1a] close complete, cost 22 ms 
[INFO ] 2024-07-02 18:15:10.376 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] running status set to false 
[INFO ] 2024-07-02 18:15:10.409 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-6ab7ec53-3115-4c0f-80f3-08ca3fafad48 
[INFO ] 2024-07-02 18:15:10.409 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-6ab7ec53-3115-4c0f-80f3-08ca3fafad48 
[INFO ] 2024-07-02 18:15:10.409 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] schema data cleaned 
[INFO ] 2024-07-02 18:15:10.409 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] monitor closed 
[INFO ] 2024-07-02 18:15:10.411 - [Heartbeat-SouceMysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6ab7ec53-3115-4c0f-80f3-08ca3fafad48] close complete, cost 34 ms 
[INFO ] 2024-07-02 18:15:11.165 - [Heartbeat-SouceMysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 18:15:11.165 - [Heartbeat-SouceMysql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@778eea0a 
[INFO ] 2024-07-02 18:15:11.289 - [Heartbeat-SouceMysql] - Stop task milestones: 6683c8c4fa7caf4ccea134fb(Heartbeat-SouceMysql)  
[INFO ] 2024-07-02 18:15:11.289 - [Heartbeat-SouceMysql] - Stopped task aspect(s) 
[INFO ] 2024-07-02 18:15:11.289 - [Heartbeat-SouceMysql] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 18:15:11.309 - [Heartbeat-SouceMysql] - Remove memory task client succeed, task: Heartbeat-SouceMysql[6683c8c4fa7caf4ccea134fb] 
[INFO ] 2024-07-02 18:15:11.312 - [Heartbeat-SouceMysql] - Destroy memory task client cache succeed, task: Heartbeat-SouceMysql[6683c8c4fa7caf4ccea134fb] 
