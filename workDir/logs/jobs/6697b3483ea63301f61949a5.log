[INFO ] 2024-07-17 20:05:06.606 - [任务 1] - Start task milestones: 6697b3483ea63301f61949a5(任务 1) 
[INFO ] 2024-07-17 20:05:06.606 - [任务 1] - Task initialization... 
[INFO ] 2024-07-17 20:05:49.664 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-17 20:05:49.847 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 20:05:49.848 - [任务 1][POLICY] - Node POLICY[02e8c06b-7c33-40c7-9ae6-a3cda95497e4] start preload schema,table counts: 1 
[INFO ] 2024-07-17 20:05:49.849 - [任务 1][p] - Node p[700ebd15-33a2-4786-a684-fafdaf5eac2c] start preload schema,table counts: 1 
[INFO ] 2024-07-17 20:05:49.849 - [任务 1][POLICY] - Node POLICY[02e8c06b-7c33-40c7-9ae6-a3cda95497e4] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 20:05:49.850 - [任务 1][p] - Node p[700ebd15-33a2-4786-a684-fafdaf5eac2c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 20:07:15.408 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-17 20:07:15.410 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-17 20:07:15.410 - [任务 1][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 20:07:15.620 - [任务 1][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1721218035,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 20:07:15.621 - [任务 1][POLICY] - Initial sync started 
[INFO ] 2024-07-17 20:07:15.621 - [任务 1][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-17 20:07:15.630 - [任务 1][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-17 20:07:15.661 - [任务 1][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-17 20:07:15.661 - [任务 1][p] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-17 20:07:15.680 - [任务 1][p] - Table "test.p" exists, skip auto create table 
[INFO ] 2024-07-17 20:07:15.682 - [任务 1][p] - The table p has already exist. 
[INFO ] 2024-07-17 20:07:15.784 - [任务 1][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-17 20:07:15.787 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-17 20:07:15.787 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-17 20:07:15.787 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-17 20:07:15.867 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-17 20:07:15.867 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection source enable share cdc: true 
[INFO ] 2024-07-17 20:07:15.868 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 1 enable share cdc: true 
[INFO ] 2024-07-17 20:07:15.891 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自source的共享挖掘任务 
[INFO ] 2024-07-17 20:07:15.907 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav393?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-17 20:07:15.908 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6697b37266ab5ede8accc9a4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697b3373ea63301f6194997_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_666389627, shareCdcTaskId=6697b3723ea63301f61949e2, connectionId=6697b3373ea63301f6194997) 
[INFO ] 2024-07-17 20:07:16.313 - [任务 1][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自source的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav393.ExternalStorage_SHARE_CDC_666389627', head seq: 0, tail seq: -1 
[INFO ] 2024-07-17 20:07:31.163 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-17 20:07:31.164 - [任务 1][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-17 20:07:31.164 - [任务 1][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-17 20:07:31.164 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-17 20:07:31.164 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-17 20:07:31.192 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6697b37266ab5ede8accc9a4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697b3373ea63301f6194997_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_666389627, shareCdcTaskId=6697b3723ea63301f61949e2, connectionId=6697b3373ea63301f6194997) 
[INFO ] 2024-07-17 20:07:31.193 - [任务 1][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自source的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav393.ExternalStorage_SHARE_CDC_666389627', head seq: 0, tail seq: -1 
[INFO ] 2024-07-17 20:07:31.193 - [任务 1][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自source的共享挖掘任务_POLICY_任务 1, external storage name: ExternalStorage_SHARE_CDC_666389627 
[INFO ] 2024-07-17 20:07:31.200 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-17 20:07:31.201 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-17T12:07:15.408Z): 0 
[INFO ] 2024-07-17 20:07:31.203 - [任务 1][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-17 20:07:31.203 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 0 
[INFO ] 2024-07-17 20:07:31.205 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=0} 
