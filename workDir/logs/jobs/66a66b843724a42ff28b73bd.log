[INFO ] 2024-07-29 00:04:23.537 - [任务 1] - Task initialization... 
[INFO ] 2024-07-29 00:04:23.781 - [任务 1] - Start task milestones: 66a66b843724a42ff28b73bd(任务 1) 
[INFO ] 2024-07-29 00:04:24.902 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-29 00:04:25.017 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-29 00:04:25.385 - [任务 1][TESTPO] - Node TESTPO[accabe13-6daa-471d-9b78-4e8bff634153] start preload schema,table counts: 1 
[INFO ] 2024-07-29 00:04:25.386 - [任务 1][PoTest] - Node PoTest[107b3c65-f811-4a02-bdab-37061d00825b] start preload schema,table counts: 1 
[INFO ] 2024-07-29 00:04:25.387 - [任务 1][TESTPO] - Node TESTPO[accabe13-6daa-471d-9b78-4e8bff634153] preload schema finished, cost 1 ms 
[INFO ] 2024-07-29 00:04:25.387 - [任务 1][PoTest] - Node PoTest[107b3c65-f811-4a02-bdab-37061d00825b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 00:04:26.178 - [任务 1][PoTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-29 00:04:26.269 - [任务 1][TESTPO] - Source node "TESTPO" read batch size: 100 
[INFO ] 2024-07-29 00:04:26.269 - [任务 1][TESTPO] - Source node "TESTPO" event queue capacity: 200 
[INFO ] 2024-07-29 00:04:26.269 - [任务 1][TESTPO] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-29 00:04:26.404 - [任务 1][TESTPO] - batch offset found: {},stream offset found: {"cdcOffset":1722182666,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-29 00:04:26.475 - [任务 1][TESTPO] - Initial sync started 
[INFO ] 2024-07-29 00:04:26.477 - [任务 1][TESTPO] - Starting batch read, table name: TESTPO, offset: null 
[INFO ] 2024-07-29 00:04:26.517 - [任务 1][TESTPO] - Table TESTPO is going to be initial synced 
[INFO ] 2024-07-29 00:04:26.518 - [任务 1][TESTPO] - Table [TESTPO] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-29 00:04:26.570 - [任务 1][TESTPO] - Query table 'TESTPO' counts: 600 
[INFO ] 2024-07-29 00:04:26.570 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-29 00:04:26.570 - [任务 1][TESTPO] - Incremental sync starting... 
[INFO ] 2024-07-29 00:04:26.573 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-29 00:04:26.573 - [任务 1][TESTPO] - Starting stream read, table list: [TESTPO, _tapdata_heartbeat_table], offset: {"cdcOffset":1722182666,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-29 00:04:26.776 - [任务 1][TESTPO] - Connector MongoDB incremental start succeed, tables: [TESTPO, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-29 00:09:52.583 - [任务 1][TESTPO] - Node TESTPO[accabe13-6daa-471d-9b78-4e8bff634153] running status set to false 
