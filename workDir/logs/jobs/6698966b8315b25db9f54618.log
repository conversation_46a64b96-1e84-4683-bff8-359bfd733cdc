[INFO ] 2024-07-18 12:13:42.600 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Task initialization... 
[INFO ] 2024-07-18 12:13:42.601 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Start task milestones: 6698966b8315b25db9f54618(t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914) 
[INFO ] 2024-07-18 12:13:42.997 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 12:13:43.202 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - The engine receives t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 12:13:43.210 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[c2ed365c-88e6-497b-843c-21fa4d7b9d52] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:13:43.210 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - Node qa_mongodb_cluster_28017_1717403468657_3537[14afca42-2320-4880-8e25-88c1c0c3177b] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:13:43.210 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[c2ed365c-88e6-497b-843c-21fa4d7b9d52] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:13:43.210 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - Node qa_mongodb_cluster_28017_1717403468657_3537[14afca42-2320-4880-8e25-88c1c0c3177b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:13:43.613 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 12:13:43.613 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Source node "qa_mongodb_cluster_27017_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 12:13:43.613 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Source node "qa_mongodb_cluster_27017_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 12:13:43.613 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 12:13:44.020 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - batch offset found: {},stream offset found: {"cdcOffset":{"rs-s1-v3623":{"seconds":1721276023,"inc":0},"rs-s2-v3623":{"seconds":1721276023,"inc":0},"rs-s3-v3623":{"seconds":1721276023,"inc":0}},"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 12:13:44.023 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 12:13:44.024 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Starting batch read, table name: t27017_482, offset: null 
[INFO ] 2024-07-18 12:13:44.025 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Table t27017_482 is going to be initial synced 
[INFO ] 2024-07-18 12:13:44.229 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Query table 't27017_482' counts: 5000 
[INFO ] 2024-07-18 12:13:48.440 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Table [t27017_482] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 12:13:48.440 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 12:13:48.442 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 12:13:48.443 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 12:13:48.645 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Starting stream read, table list: [t27017_482, _tapdata_heartbeat_table], offset: {"cdcOffset":{"rs-s1-v3623":{"seconds":1721276023,"inc":0},"rs-s2-v3623":{"seconds":1721276023,"inc":0},"rs-s3-v3623":{"seconds":1721276023,"inc":0}},"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 12:13:48.730 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [t27017_482, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 12:13:59.774 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[c2ed365c-88e6-497b-843c-21fa4d7b9d52] running status set to false 
[INFO ] 2024-07-18 12:13:59.774 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 12:14:01.301 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-c2ed365c-88e6-497b-843c-21fa4d7b9d52 
[INFO ] 2024-07-18 12:14:01.301 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-c2ed365c-88e6-497b-843c-21fa4d7b9d52 
[INFO ] 2024-07-18 12:14:01.302 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[c2ed365c-88e6-497b-843c-21fa4d7b9d52] schema data cleaned 
[INFO ] 2024-07-18 12:14:01.304 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[c2ed365c-88e6-497b-843c-21fa4d7b9d52] monitor closed 
[INFO ] 2024-07-18 12:14:01.304 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[c2ed365c-88e6-497b-843c-21fa4d7b9d52] close complete, cost 1533 ms 
[INFO ] 2024-07-18 12:14:01.304 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - Node qa_mongodb_cluster_28017_1717403468657_3537[14afca42-2320-4880-8e25-88c1c0c3177b] running status set to false 
[INFO ] 2024-07-18 12:14:01.363 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-14afca42-2320-4880-8e25-88c1c0c3177b 
[INFO ] 2024-07-18 12:14:01.364 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-14afca42-2320-4880-8e25-88c1c0c3177b 
[INFO ] 2024-07-18 12:14:01.364 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - Node qa_mongodb_cluster_28017_1717403468657_3537[14afca42-2320-4880-8e25-88c1c0c3177b] schema data cleaned 
[INFO ] 2024-07-18 12:14:01.364 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - Node qa_mongodb_cluster_28017_1717403468657_3537[14afca42-2320-4880-8e25-88c1c0c3177b] monitor closed 
[INFO ] 2024-07-18 12:14:01.365 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - Node qa_mongodb_cluster_28017_1717403468657_3537[14afca42-2320-4880-8e25-88c1c0c3177b] close complete, cost 60 ms 
[INFO ] 2024-07-18 12:14:03.479 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 12:14:03.479 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@43bd14f9 
[INFO ] 2024-07-18 12:14:03.592 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Stop task milestones: 6698966b8315b25db9f54618(t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914)  
[INFO ] 2024-07-18 12:14:03.619 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Stopped task aspect(s) 
[INFO ] 2024-07-18 12:14:03.619 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 12:14:03.663 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Remove memory task client succeed, task: t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914[6698966b8315b25db9f54618] 
[INFO ] 2024-07-18 12:14:03.663 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Destroy memory task client cache succeed, task: t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914[6698966b8315b25db9f54618] 
[INFO ] 2024-07-18 12:15:39.914 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Task initialization... 
[INFO ] 2024-07-18 12:15:40.121 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Start task milestones: 6698966b8315b25db9f54618(t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914) 
[INFO ] 2024-07-18 12:15:40.359 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 12:15:40.483 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - The engine receives t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 12:15:40.577 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - Node qa_mongodb_cluster_28017_1717403468657_3537[14afca42-2320-4880-8e25-88c1c0c3177b] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:15:40.577 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[c2ed365c-88e6-497b-843c-21fa4d7b9d52] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:15:40.577 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - Node qa_mongodb_cluster_28017_1717403468657_3537[14afca42-2320-4880-8e25-88c1c0c3177b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:15:40.782 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[c2ed365c-88e6-497b-843c-21fa4d7b9d52] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:15:40.881 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 12:15:40.881 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Source node "qa_mongodb_cluster_27017_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 12:15:40.881 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Source node "qa_mongodb_cluster_27017_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 12:15:40.882 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-18 12:15:41.067 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - batch offset found: {"t27017_482":{"batch_read_connector_offset":{"sortKey":"_id","value":"6698960ce6abd19d4e2ad68f","objectId":true},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":{"rs-s1-v3623":{"seconds":1721276038,"inc":1},"rs-s2-v3623":{"seconds":1721276039,"inc":1},"rs-s3-v3623":{"seconds":1721276032,"inc":1}},"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 12:15:41.067 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 12:15:41.068 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Starting batch read, table name: t27017_482, offset: MongoBatchOffset[sortKey='_id', value=6698960ce6abd19d4e2ad68f, objectId=true] 
[INFO ] 2024-07-18 12:15:41.068 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Table t27017_482 is going to be initial synced 
[INFO ] 2024-07-18 12:15:41.271 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Query table 't27017_482' counts: 7000 
[INFO ] 2024-07-18 12:15:42.585 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Table [t27017_482] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 12:15:42.585 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 12:15:42.585 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 12:15:42.585 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 12:15:42.586 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Starting stream read, table list: [t27017_482, _tapdata_heartbeat_table], offset: {"cdcOffset":{"rs-s1-v3623":{"seconds":1721276038,"inc":1},"rs-s2-v3623":{"seconds":1721276039,"inc":1},"rs-s3-v3623":{"seconds":1721276032,"inc":1}},"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 12:15:42.982 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [t27017_482, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 12:18:22.366 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[c2ed365c-88e6-497b-843c-21fa4d7b9d52] running status set to false 
[INFO ] 2024-07-18 12:18:22.366 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 12:18:23.396 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-c2ed365c-88e6-497b-843c-21fa4d7b9d52 
[INFO ] 2024-07-18 12:18:23.397 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-c2ed365c-88e6-497b-843c-21fa4d7b9d52 
[INFO ] 2024-07-18 12:18:23.397 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[c2ed365c-88e6-497b-843c-21fa4d7b9d52] schema data cleaned 
[INFO ] 2024-07-18 12:18:23.397 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[c2ed365c-88e6-497b-843c-21fa4d7b9d52] monitor closed 
[INFO ] 2024-07-18 12:18:23.401 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[c2ed365c-88e6-497b-843c-21fa4d7b9d52] close complete, cost 1055 ms 
[INFO ] 2024-07-18 12:18:23.401 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - Node qa_mongodb_cluster_28017_1717403468657_3537[14afca42-2320-4880-8e25-88c1c0c3177b] running status set to false 
[INFO ] 2024-07-18 12:18:23.428 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-14afca42-2320-4880-8e25-88c1c0c3177b 
[INFO ] 2024-07-18 12:18:23.428 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-14afca42-2320-4880-8e25-88c1c0c3177b 
[INFO ] 2024-07-18 12:18:23.428 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - Node qa_mongodb_cluster_28017_1717403468657_3537[14afca42-2320-4880-8e25-88c1c0c3177b] schema data cleaned 
[INFO ] 2024-07-18 12:18:23.428 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - Node qa_mongodb_cluster_28017_1717403468657_3537[14afca42-2320-4880-8e25-88c1c0c3177b] monitor closed 
[INFO ] 2024-07-18 12:18:23.429 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914][qa_mongodb_cluster_28017_1717403468657_3537] - Node qa_mongodb_cluster_28017_1717403468657_3537[14afca42-2320-4880-8e25-88c1c0c3177b] close complete, cost 28 ms 
[INFO ] 2024-07-18 12:18:23.867 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 12:18:23.868 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1397c381 
[INFO ] 2024-07-18 12:18:24.031 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Stop task milestones: 6698966b8315b25db9f54618(t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914)  
[INFO ] 2024-07-18 12:18:24.031 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Stopped task aspect(s) 
[INFO ] 2024-07-18 12:18:24.032 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 12:18:24.078 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Remove memory task client succeed, task: t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914[6698966b8315b25db9f54618] 
[INFO ] 2024-07-18 12:18:24.078 - [t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914] - Destroy memory task client cache succeed, task: t_4.8.2-mdb-v3-cluster_v4-cluster_1717403468657_3537-1721275914[6698966b8315b25db9f54618] 
