[INFO ] 2024-07-23 07:40:26.929 - [任务 13] - Task initialization... 
[INFO ] 2024-07-23 07:40:26.930 - [任务 13] - Start task milestones: 669eea892f0fe71c3fdc1f5a(任务 13) 
[INFO ] 2024-07-23 07:40:27.291 - [任务 13] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-23 07:40:27.291 - [任务 13] - The engine receives 任务 13 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-23 07:40:27.344 - [任务 13][POLICY] - Node POLICY[d1cdb495-1b4d-4085-baca-5cfa181a101d] start preload schema,table counts: 1 
[INFO ] 2024-07-23 07:40:27.345 - [任务 13][POLICY] - Node POLICY[17d90224-d253-4fcd-a379-37c471606cf1] start preload schema,table counts: 1 
[INFO ] 2024-07-23 07:40:27.345 - [任务 13][POLICY] - Node POLICY[17d90224-d253-4fcd-a379-37c471606cf1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 07:40:27.345 - [任务 13][POLICY] - Node POLICY[d1cdb495-1b4d-4085-baca-5cfa181a101d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 07:40:28.153 - [任务 13][POLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-23 07:40:28.299 - [任务 13][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-23 07:40:28.301 - [任务 13][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-23 07:40:28.301 - [任务 13][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-23 07:40:28.321 - [任务 13][POLICY] - batch offset found: {},stream offset found: {"filename":"binlog.000033","position":43732968,"gtidSet":""} 
[INFO ] 2024-07-23 07:40:28.321 - [任务 13][POLICY] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-23 07:40:28.384 - [任务 13][POLICY] - Initial sync started 
[INFO ] 2024-07-23 07:40:28.391 - [任务 13][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-23 07:40:28.391 - [任务 13][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-23 07:40:28.530 - [任务 13][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-23 07:40:28.530 - [任务 13][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-23 07:40:28.530 - [任务 13][POLICY] - Initial sync completed 
[INFO ] 2024-07-23 07:40:28.530 - [任务 13][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-23 07:40:28.531 - [任务 13][POLICY] - Initial sync completed 
[INFO ] 2024-07-23 07:40:28.531 - [任务 13][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"filename":"binlog.000033","position":43732968,"gtidSet":""} 
[INFO ] 2024-07-23 07:40:28.558 - [任务 13][POLICY] - Starting mysql cdc, server name: ae887d8e-8ae6-47f8-b20e-9ed5f2c91e47 
[INFO ] 2024-07-23 07:40:28.559 - [任务 13][POLICY] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1089345669
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ae887d8e-8ae6-47f8-b20e-9ed5f2c91e47
  database.port: 3306
  threadName: Debezium-Mysql-Connector-ae887d8e-8ae6-47f8-b20e-9ed5f2c91e47
  database.hostname: localhost
  database.password: ********
  name: ae887d8e-8ae6-47f8-b20e-9ed5f2c91e47
  pdk.offset.string: {"name":"ae887d8e-8ae6-47f8-b20e-9ed5f2c91e47","offset":{"{\"server\":\"ae887d8e-8ae6-47f8-b20e-9ed5f2c91e47\"}":"{\"file\":\"binlog.000033\",\"pos\":43732968,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.POLICY,test._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-23 07:40:28.761 - [任务 13][POLICY] - Connector Mysql incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-23 07:43:49.626 - [任务 13][POLICY] - Node POLICY[17d90224-d253-4fcd-a379-37c471606cf1] running status set to false 
[INFO ] 2024-07-23 07:43:49.723 - [任务 13][POLICY] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-23 07:43:49.733 - [任务 13][POLICY] - Mysql binlog reader stopped 
[INFO ] 2024-07-23 07:43:49.733 - [任务 13][POLICY] - Incremental sync completed 
[INFO ] 2024-07-23 07:43:49.743 - [任务 13][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-17d90224-d253-4fcd-a379-37c471606cf1 
[INFO ] 2024-07-23 07:43:49.743 - [任务 13][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-17d90224-d253-4fcd-a379-37c471606cf1 
[INFO ] 2024-07-23 07:43:49.743 - [任务 13][POLICY] - Node POLICY[17d90224-d253-4fcd-a379-37c471606cf1] schema data cleaned 
[INFO ] 2024-07-23 07:43:49.746 - [任务 13][POLICY] - Node POLICY[17d90224-d253-4fcd-a379-37c471606cf1] monitor closed 
[INFO ] 2024-07-23 07:43:49.746 - [任务 13][POLICY] - Node POLICY[17d90224-d253-4fcd-a379-37c471606cf1] close complete, cost 124 ms 
[INFO ] 2024-07-23 07:43:49.759 - [任务 13][POLICY] - Node POLICY[d1cdb495-1b4d-4085-baca-5cfa181a101d] running status set to false 
[INFO ] 2024-07-23 07:43:49.760 - [任务 13][POLICY] - Clickhouse Optimize Table start, tables: ["POLICY"] 
[WARN ] 2024-07-23 07:43:49.840 - [任务 13][POLICY] - Clickhouse Optimize Table failed 
[INFO ] 2024-07-23 07:43:49.842 - [任务 13][POLICY] - PDK connector node stopped: HazelcastTargetPdkDataNode-d1cdb495-1b4d-4085-baca-5cfa181a101d 
[INFO ] 2024-07-23 07:43:49.842 - [任务 13][POLICY] - PDK connector node released: HazelcastTargetPdkDataNode-d1cdb495-1b4d-4085-baca-5cfa181a101d 
[INFO ] 2024-07-23 07:43:49.842 - [任务 13][POLICY] - Node POLICY[d1cdb495-1b4d-4085-baca-5cfa181a101d] schema data cleaned 
[INFO ] 2024-07-23 07:43:49.842 - [任务 13][POLICY] - Node POLICY[d1cdb495-1b4d-4085-baca-5cfa181a101d] monitor closed 
[INFO ] 2024-07-23 07:43:49.845 - [任务 13][POLICY] - Node POLICY[d1cdb495-1b4d-4085-baca-5cfa181a101d] close complete, cost 97 ms 
[INFO ] 2024-07-23 07:43:52.806 - [任务 13] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-23 07:43:52.816 - [任务 13] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5c50f2ef 
[INFO ] 2024-07-23 07:43:52.978 - [任务 13] - Stop task milestones: 669eea892f0fe71c3fdc1f5a(任务 13)  
[INFO ] 2024-07-23 07:43:52.978 - [任务 13] - Stopped task aspect(s) 
[INFO ] 2024-07-23 07:43:52.978 - [任务 13] - Snapshot order controller have been removed 
[INFO ] 2024-07-23 07:43:52.998 - [任务 13] - Remove memory task client succeed, task: 任务 13[669eea892f0fe71c3fdc1f5a] 
[INFO ] 2024-07-23 07:43:53.000 - [任务 13] - Destroy memory task client cache succeed, task: 任务 13[669eea892f0fe71c3fdc1f5a] 
