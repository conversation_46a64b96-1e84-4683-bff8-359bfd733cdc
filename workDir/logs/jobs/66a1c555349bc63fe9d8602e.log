[INFO ] 2024-07-25 11:26:13.497 - [任务 28] - Task initialization... 
[INFO ] 2024-07-25 11:26:13.700 - [任务 28] - Start task milestones: 66a1c555349bc63fe9d8602e(任务 28) 
[INFO ] 2024-07-25 11:26:14.450 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 11:26:14.450 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 11:26:14.829 - [任务 28][POLICY] - Node POLICY[fc20660c-1c5f-4008-939d-b3f9bcfa42b1] start preload schema,table counts: 1 
[INFO ] 2024-07-25 11:26:14.829 - [任务 28][POLICY] - Node POLICY[fc20660c-1c5f-4008-939d-b3f9bcfa42b1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 11:26:14.868 - [任务 28][POLICY] - Node POLICY[7c9de1c9-1cc7-4329-aa08-1d8b15d1f32a] start preload schema,table counts: 1 
[INFO ] 2024-07-25 11:26:15.070 - [任务 28][POLICY] - Node POLICY[7c9de1c9-1cc7-4329-aa08-1d8b15d1f32a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 11:26:15.816 - [任务 28][POLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 11:26:15.996 - [任务 28][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-25 11:26:15.997 - [任务 28][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-25 11:26:15.997 - [任务 28][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 11:26:16.016 - [任务 28][POLICY] - batch offset found: {},stream offset found: {"filename":"binlog.000033","position":54580277,"gtidSet":""} 
[INFO ] 2024-07-25 11:26:16.094 - [任务 28][POLICY] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 11:26:16.103 - [任务 28][POLICY] - Initial sync started 
[INFO ] 2024-07-25 11:26:16.108 - [任务 28][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-25 11:26:16.125 - [任务 28][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-25 11:26:16.259 - [任务 28][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-25 11:26:16.267 - [任务 28][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 11:26:16.268 - [任务 28][POLICY] - Initial sync completed 
[INFO ] 2024-07-25 11:26:16.268 - [任务 28][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-25 11:26:16.268 - [任务 28][POLICY] - Initial sync completed 
[INFO ] 2024-07-25 11:26:16.268 - [任务 28][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"filename":"binlog.000033","position":54580277,"gtidSet":""} 
[INFO ] 2024-07-25 11:26:16.370 - [任务 28][POLICY] - Starting mysql cdc, server name: 2c423244-e636-4c98-95f7-29b7d305330f 
[INFO ] 2024-07-25 11:26:16.371 - [任务 28][POLICY] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1011659103
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 2c423244-e636-4c98-95f7-29b7d305330f
  database.port: 3306
  threadName: Debezium-Mysql-Connector-2c423244-e636-4c98-95f7-29b7d305330f
  database.hostname: localhost
  database.password: ********
  name: 2c423244-e636-4c98-95f7-29b7d305330f
  pdk.offset.string: {"name":"2c423244-e636-4c98-95f7-29b7d305330f","offset":{"{\"server\":\"2c423244-e636-4c98-95f7-29b7d305330f\"}":"{\"file\":\"binlog.000033\",\"pos\":54580277,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.POLICY,test._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-25 11:26:16.558 - [任务 28][POLICY] - Connector Mysql incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-25 15:26:04.411 - [任务 28][POLICY] - Mysql binlog reader stopped 
[INFO ] 2024-07-25 15:26:04.411 - [任务 28][POLICY] - Incremental sync completed 
[INFO ] 2024-07-25 15:26:04.411 - [任务 28][POLICY] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-25 15:26:04.440 - [任务 28][POLICY] - java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected <-- Error Message -->
java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: BinaryLogClient is already connected
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:566)
	com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected
	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:159)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:370)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:655)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.IllegalStateException: BinaryLogClient is already connected
	at com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:566)
	at com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	... 1 more

[INFO ] 2024-07-25 15:26:04.443 - [任务 28][POLICY] - Job suspend in error handle 
[INFO ] 2024-07-25 15:26:04.706 - [任务 28][POLICY] - Node POLICY[7c9de1c9-1cc7-4329-aa08-1d8b15d1f32a] running status set to false 
[INFO ] 2024-07-25 15:26:04.767 - [任务 28][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-7c9de1c9-1cc7-4329-aa08-1d8b15d1f32a 
[INFO ] 2024-07-25 15:26:04.767 - [任务 28][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-7c9de1c9-1cc7-4329-aa08-1d8b15d1f32a 
[INFO ] 2024-07-25 15:26:04.767 - [任务 28][POLICY] - Node POLICY[7c9de1c9-1cc7-4329-aa08-1d8b15d1f32a] schema data cleaned 
[INFO ] 2024-07-25 15:26:04.767 - [任务 28][POLICY] - Node POLICY[7c9de1c9-1cc7-4329-aa08-1d8b15d1f32a] monitor closed 
[INFO ] 2024-07-25 15:26:04.769 - [任务 28][POLICY] - Node POLICY[7c9de1c9-1cc7-4329-aa08-1d8b15d1f32a] close complete, cost 77 ms 
[INFO ] 2024-07-25 15:26:04.790 - [任务 28][POLICY] - Node POLICY[fc20660c-1c5f-4008-939d-b3f9bcfa42b1] running status set to false 
[INFO ] 2024-07-25 15:26:04.791 - [任务 28][POLICY] - PDK connector node stopped: HazelcastTargetPdkDataNode-fc20660c-1c5f-4008-939d-b3f9bcfa42b1 
[INFO ] 2024-07-25 15:26:04.791 - [任务 28][POLICY] - PDK connector node released: HazelcastTargetPdkDataNode-fc20660c-1c5f-4008-939d-b3f9bcfa42b1 
[INFO ] 2024-07-25 15:26:04.791 - [任务 28][POLICY] - Node POLICY[fc20660c-1c5f-4008-939d-b3f9bcfa42b1] schema data cleaned 
[INFO ] 2024-07-25 15:26:04.796 - [任务 28][POLICY] - Node POLICY[fc20660c-1c5f-4008-939d-b3f9bcfa42b1] monitor closed 
[INFO ] 2024-07-25 15:26:04.802 - [任务 28][POLICY] - Node POLICY[fc20660c-1c5f-4008-939d-b3f9bcfa42b1] close complete, cost 27 ms 
[INFO ] 2024-07-25 15:26:09.141 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-25 15:26:09.152 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 15:26:09.152 - [任务 28] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7359248d 
[INFO ] 2024-07-25 15:26:09.276 - [任务 28] - Stop task milestones: 66a1c555349bc63fe9d8602e(任务 28)  
[INFO ] 2024-07-25 15:26:09.279 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-07-25 15:26:09.279 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 15:26:09.293 - [任务 28] - Remove memory task client succeed, task: 任务 28[66a1c555349bc63fe9d8602e] 
[INFO ] 2024-07-25 15:26:09.295 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[66a1c555349bc63fe9d8602e] 
