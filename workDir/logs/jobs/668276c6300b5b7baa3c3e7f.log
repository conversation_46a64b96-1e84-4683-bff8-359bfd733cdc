[INFO ] 2024-07-01 17:29:02.914 - [任务 39] - Start task milestones: 668276c6300b5b7baa3c3e7f(任务 39) 
[INFO ] 2024-07-01 17:29:03.122 - [任务 39] - Task initialization... 
[INFO ] 2024-07-01 17:29:04.127 - [任务 39] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-01 17:29:04.127 - [任务 39] - The engine receives 任务 39 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-01 17:29:04.484 - [任务 39][SouceMysql] - Node SouceMysql[417225c8-1541-4899-b904-5669e7540f3b] start preload schema,table counts: 1 
[INFO ] 2024-07-01 17:29:04.533 - [任务 39][SouceMysql] - Node SouceMysql[417225c8-1541-4899-b904-5669e7540f3b] preload schema finished, cost 1 ms 
[INFO ] 2024-07-01 17:29:04.534 - [任务 39][SourceMongo] - Node SourceMongo[726f2b7b-97a5-4744-9211-9b04b31f5370] start preload schema,table counts: 1 
[INFO ] 2024-07-01 17:29:04.534 - [任务 39][SourceMongo] - Node SourceMongo[726f2b7b-97a5-4744-9211-9b04b31f5370] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 17:29:05.719 - [任务 39][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-01 17:29:05.720 - [任务 39][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-01 17:29:05.753 - [任务 39][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-07-01 17:29:05.753 - [任务 39][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-07-01 17:29:05.753 - [任务 39][SouceMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-01 17:29:05.774 - [任务 39][SouceMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":4214281,"gtidSet":""} 
[INFO ] 2024-07-01 17:29:05.934 - [任务 39][SouceMysql] - Initial sync started 
[INFO ] 2024-07-01 17:29:05.949 - [任务 39][SouceMysql] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-07-01 17:29:05.960 - [任务 39][SouceMysql] - Table CLAIM is going to be initial synced 
[INFO ] 2024-07-01 17:29:06.170 - [任务 39][SouceMysql] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-07-01 17:29:06.314 - [任务 39][SouceMysql] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 17:29:06.324 - [任务 39][SouceMysql] - Initial sync completed 
[INFO ] 2024-07-01 17:29:06.325 - [任务 39][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-07-01 17:29:06.326 - [任务 39][SouceMysql] - Initial sync completed 
[INFO ] 2024-07-01 17:29:06.336 - [任务 39][SouceMysql] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000032","position":4214281,"gtidSet":""} 
[INFO ] 2024-07-01 17:29:06.437 - [任务 39][SouceMysql] - Starting mysql cdc, server name: 5c0af903-192e-449f-86ff-676029432390 
[INFO ] 2024-07-01 17:29:06.438 - [任务 39][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 454362516
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 5c0af903-192e-449f-86ff-676029432390
  database.port: 3306
  threadName: Debezium-Mysql-Connector-5c0af903-192e-449f-86ff-676029432390
  database.hostname: localhost
  database.password: ********
  name: 5c0af903-192e-449f-86ff-676029432390
  pdk.offset.string: {"name":"5c0af903-192e-449f-86ff-676029432390","offset":{"{\"server\":\"5c0af903-192e-449f-86ff-676029432390\"}":"{\"file\":\"binlog.000032\",\"pos\":4214281,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-01 17:29:06.627 - [任务 39][SouceMysql] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-07-01 18:48:22.088 - [任务 39] - Task initialization... 
[INFO ] 2024-07-01 18:48:22.295 - [任务 39] - Start task milestones: 668276c6300b5b7baa3c3e7f(任务 39) 
[INFO ] 2024-07-01 18:48:23.104 - [任务 39] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-01 18:48:23.521 - [任务 39] - The engine receives 任务 39 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-01 18:48:23.841 - [任务 39][SourceMongo] - Node SourceMongo[726f2b7b-97a5-4744-9211-9b04b31f5370] start preload schema,table counts: 1 
[INFO ] 2024-07-01 18:48:23.842 - [任务 39][SourceMongo] - Node SourceMongo[726f2b7b-97a5-4744-9211-9b04b31f5370] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 18:48:23.871 - [任务 39][SouceMysql] - Node SouceMysql[417225c8-1541-4899-b904-5669e7540f3b] start preload schema,table counts: 1 
[INFO ] 2024-07-01 18:48:24.082 - [任务 39][SouceMysql] - Node SouceMysql[417225c8-1541-4899-b904-5669e7540f3b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 18:48:24.661 - [任务 39][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-01 18:48:24.662 - [任务 39][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-01 18:48:24.935 - [任务 39][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-07-01 18:48:24.946 - [任务 39][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-07-01 18:48:24.947 - [任务 39][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-01 18:48:25.057 - [任务 39][SouceMysql] - batch offset found: {"CLAIM":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"name":"5c0af903-192e-449f-86ff-676029432390","offset":{"{\"server\":\"5c0af903-192e-449f-86ff-676029432390\"}":"{\"ts_sec\":1719826147,\"file\":\"binlog.000032\",\"pos\":4214281,\"server_id\":1}"}} 
[INFO ] 2024-07-01 18:48:25.059 - [任务 39][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-07-01 18:48:25.060 - [任务 39][SouceMysql] - Initial sync completed 
[INFO ] 2024-07-01 18:48:25.064 - [任务 39][SouceMysql] - Starting stream read, table list: [CLAIM], offset: {"name":"5c0af903-192e-449f-86ff-676029432390","offset":{"{\"server\":\"5c0af903-192e-449f-86ff-676029432390\"}":"{\"ts_sec\":1719826147,\"file\":\"binlog.000032\",\"pos\":4214281,\"server_id\":1}"}} 
[INFO ] 2024-07-01 18:48:25.103 - [任务 39][SouceMysql] - Starting mysql cdc, server name: 5c0af903-192e-449f-86ff-676029432390 
[INFO ] 2024-07-01 18:48:25.306 - [任务 39][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 243878337
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 5c0af903-192e-449f-86ff-676029432390
  database.port: 3306
  threadName: Debezium-Mysql-Connector-5c0af903-192e-449f-86ff-676029432390
  database.hostname: localhost
  database.password: ********
  name: 5c0af903-192e-449f-86ff-676029432390
  pdk.offset.string: {"name":"5c0af903-192e-449f-86ff-676029432390","offset":{"{\"server\":\"5c0af903-192e-449f-86ff-676029432390\"}":"{\"ts_sec\":1719826147,\"file\":\"binlog.000032\",\"pos\":4214281,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-01 18:48:25.860 - [任务 39][SouceMysql] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-07-01 18:49:10.009 - [任务 39][SouceMysql] - Node SouceMysql[417225c8-1541-4899-b904-5669e7540f3b] running status set to false 
[INFO ] 2024-07-01 18:49:10.045 - [任务 39][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-01 18:49:10.046 - [任务 39][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-07-01 18:49:10.080 - [任务 39][SouceMysql] - Incremental sync completed 
[INFO ] 2024-07-01 18:49:10.084 - [任务 39][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-417225c8-1541-4899-b904-5669e7540f3b 
[INFO ] 2024-07-01 18:49:10.084 - [任务 39][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-417225c8-1541-4899-b904-5669e7540f3b 
[INFO ] 2024-07-01 18:49:10.086 - [任务 39][SouceMysql] - Node SouceMysql[417225c8-1541-4899-b904-5669e7540f3b] schema data cleaned 
[INFO ] 2024-07-01 18:49:10.099 - [任务 39][SouceMysql] - Node SouceMysql[417225c8-1541-4899-b904-5669e7540f3b] monitor closed 
[INFO ] 2024-07-01 18:49:10.100 - [任务 39][SouceMysql] - Node SouceMysql[417225c8-1541-4899-b904-5669e7540f3b] close complete, cost 211 ms 
[INFO ] 2024-07-01 18:49:10.100 - [任务 39][SourceMongo] - Node SourceMongo[726f2b7b-97a5-4744-9211-9b04b31f5370] running status set to false 
[INFO ] 2024-07-01 18:49:10.152 - [任务 39][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-726f2b7b-97a5-4744-9211-9b04b31f5370 
[INFO ] 2024-07-01 18:49:10.155 - [任务 39][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-726f2b7b-97a5-4744-9211-9b04b31f5370 
[INFO ] 2024-07-01 18:49:10.155 - [任务 39][SourceMongo] - Node SourceMongo[726f2b7b-97a5-4744-9211-9b04b31f5370] schema data cleaned 
[INFO ] 2024-07-01 18:49:10.156 - [任务 39][SourceMongo] - Node SourceMongo[726f2b7b-97a5-4744-9211-9b04b31f5370] monitor closed 
[INFO ] 2024-07-01 18:49:10.156 - [任务 39][SourceMongo] - Node SourceMongo[726f2b7b-97a5-4744-9211-9b04b31f5370] close complete, cost 55 ms 
[INFO ] 2024-07-01 18:49:12.530 - [任务 39] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-01 18:49:12.535 - [任务 39] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5ebe3392 
[INFO ] 2024-07-01 18:49:12.650 - [任务 39] - Stop task milestones: 668276c6300b5b7baa3c3e7f(任务 39)  
[INFO ] 2024-07-01 18:49:12.683 - [任务 39] - Stopped task aspect(s) 
[INFO ] 2024-07-01 18:49:12.683 - [任务 39] - Snapshot order controller have been removed 
[INFO ] 2024-07-01 18:49:12.840 - [任务 39] - Remove memory task client succeed, task: 任务 39[668276c6300b5b7baa3c3e7f] 
[INFO ] 2024-07-01 18:49:12.840 - [任务 39] - Destroy memory task client cache succeed, task: 任务 39[668276c6300b5b7baa3c3e7f] 
