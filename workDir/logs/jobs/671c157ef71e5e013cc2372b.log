[INFO ] 2024-10-26 06:02:58.716 - [任务 14] - Start task milestones: 671c157ef71e5e013cc2372b(任务 14) 
[INFO ] 2024-10-26 06:02:58.927 - [任务 14] - Task initialization... 
[INFO ] 2024-10-26 06:03:01.977 - [任务 14] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-26 06:03:02.036 - [任务 14] - The engine receives 任务 14 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-26 06:03:02.519 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] start preload schema,table counts: 73 
[INFO ] 2024-10-26 06:03:02.520 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] start preload schema,table counts: 73 
[INFO ] 2024-10-26 06:03:02.525 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-26 06:03:02.526 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] preload schema finished, cost 0 ms 
[INFO ] 2024-10-26 06:03:03.497 - [任务 14][MysqlDroagon] - Source node "MysqlDroagon" read batch size: 100 
[INFO ] 2024-10-26 06:03:03.499 - [任务 14][MysqlDroagon] - Source node "MysqlDroagon" event queue capacity: 200 
[INFO ] 2024-10-26 06:03:03.500 - [任务 14][MysqlDroagon] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-10-26 06:03:03.526 - [任务 14][MysqlDroagon] - Call timestamp to stream offset function failed, will stop task after snapshot, type: mysql-io.tapdata-1.0-SNAPSHOT-public, errors: TapDbCdcConfigInvalidEx  You need to do more configuration for the CDC of the data source, solution suggestions: please open mysql binlog config
java.lang.Exception:  Binlog config is close
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:153)
	at io.tapdata.connector.mysql.MysqlConnector.timestampToStreamOffset(MysqlConnector.java:680)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromTime$8(HazelcastSourcePdkBaseNode.java:605)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromTime(HazelcastSourcePdkBaseNode.java:603)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetInitialAndCDC(HazelcastSourcePdkBaseNode.java:583)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:517)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:400)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:292)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:213)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.Exception:  Binlog config is close
	at io.tapdata.connector.mysql.MysqlConnector.timestampToStreamOffset(MysqlConnector.java:679)
	... 21 more
 
[INFO ] 2024-10-26 06:03:03.528 - [任务 14][MysqlDroagon] - batch offset found: {},stream offset not found. 
[INFO ] 2024-10-26 06:03:03.732 - [任务 14][MysqlDroagon] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-26 06:03:03.793 - [任务 14][MysqlDroagon] - Initial sync started 
[INFO ] 2024-10-26 06:03:03.818 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_order, offset: null 
[INFO ] 2024-10-26 06:03:03.820 - [任务 14][MysqlDroagon] - Table eb_store_order is going to be initial synced 
[INFO ] 2024-10-26 06:03:03.916 - [任务 14][MysqlDroagon] - Table [eb_store_order] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:03.933 - [任务 14][MysqlDroagon] - Query table 'eb_store_order' counts: 0 
[INFO ] 2024-10-26 06:03:03.934 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_attr_value, offset: null 
[INFO ] 2024-10-26 06:03:03.935 - [任务 14][MysqlDroagon] - Table eb_store_product_attr_value is going to be initial synced 
[INFO ] 2024-10-26 06:03:04.004 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_attr_value' counts: 38 
[INFO ] 2024-10-26 06:03:04.005 - [任务 14][MysqlDroagon] - Table [eb_store_product_attr_value] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:04.006 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_wechat_callback, offset: null 
[INFO ] 2024-10-26 06:03:04.006 - [任务 14][MysqlDroagon] - Table eb_wechat_callback is going to be initial synced 
[INFO ] 2024-10-26 06:03:04.022 - [任务 14][mysql184] - Node(mysql184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-26 06:03:04.023 - [任务 14][MysqlDroagon] - Table [eb_wechat_callback] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:04.024 - [任务 14][MysqlDroagon] - Query table 'eb_wechat_callback' counts: 0 
[INFO ] 2024-10-26 06:03:04.024 - [任务 14][mysql184] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-26 06:03:04.025 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_shipping_templates_free, offset: null 
[INFO ] 2024-10-26 06:03:04.026 - [任务 14][MysqlDroagon] - Table eb_shipping_templates_free is going to be initial synced 
[INFO ] 2024-10-26 06:03:04.061 - [任务 14][MysqlDroagon] - Query table 'eb_shipping_templates_free' counts: 32 
[INFO ] 2024-10-26 06:03:04.062 - [任务 14][MysqlDroagon] - Table [eb_shipping_templates_free] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:04.062 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_notification, offset: null 
[INFO ] 2024-10-26 06:03:04.062 - [任务 14][MysqlDroagon] - Table eb_system_notification is going to be initial synced 
[INFO ] 2024-10-26 06:03:04.082 - [任务 14][MysqlDroagon] - Query table 'eb_system_notification' counts: 12 
[INFO ] 2024-10-26 06:03:04.083 - [任务 14][MysqlDroagon] - Table [eb_system_notification] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:04.084 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_heal_user, offset: null 
[INFO ] 2024-10-26 06:03:04.099 - [任务 14][MysqlDroagon] - Table eb_store_heal_user is going to be initial synced 
[INFO ] 2024-10-26 06:03:04.128 - [任务 14][MysqlDroagon] - Query table 'eb_store_heal_user' counts: 9 
[INFO ] 2024-10-26 06:03:04.128 - [任务 14][MysqlDroagon] - Table [eb_store_heal_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:04.129 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_level, offset: null 
[INFO ] 2024-10-26 06:03:04.129 - [任务 14][MysqlDroagon] - Table eb_user_level is going to be initial synced 
[INFO ] 2024-10-26 06:03:04.149 - [任务 14][MysqlDroagon] - Table [eb_user_level] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:04.149 - [任务 14][MysqlDroagon] - Query table 'eb_user_level' counts: 1 
[INFO ] 2024-10-26 06:03:04.150 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_token, offset: null 
[INFO ] 2024-10-26 06:03:04.150 - [任务 14][MysqlDroagon] - Table eb_user_token is going to be initial synced 
[INFO ] 2024-10-26 06:03:04.163 - [任务 14][MysqlDroagon] - Query table 'eb_user_token' counts: 3 
[INFO ] 2024-10-26 06:03:04.163 - [任务 14][MysqlDroagon] - Table [eb_user_token] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:04.165 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_group, offset: null 
[INFO ] 2024-10-26 06:03:04.165 - [任务 14][MysqlDroagon] - Table eb_system_group is going to be initial synced 
[INFO ] 2024-10-26 06:03:04.172 - [任务 14][mysql184] - Table "liubai.eb_store_order" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:04.173 - [任务 14][mysql184] - The table eb_store_order has already exist. 
[INFO ] 2024-10-26 06:03:04.190 - [任务 14][MysqlDroagon] - Query table 'eb_system_group' counts: 18 
[INFO ] 2024-10-26 06:03:04.191 - [任务 14][MysqlDroagon] - Table [eb_system_group] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:04.196 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_config, offset: null 
[INFO ] 2024-10-26 06:03:04.197 - [任务 14][MysqlDroagon] - Table eb_system_config is going to be initial synced 
[INFO ] 2024-10-26 06:03:04.229 - [任务 14][MysqlDroagon] - Query table 'eb_system_config' counts: 177 
[INFO ] 2024-10-26 06:03:04.230 - [任务 14][MysqlDroagon] - Table [eb_system_config] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:04.231 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_sign, offset: null 
[INFO ] 2024-10-26 06:03:04.232 - [任务 14][MysqlDroagon] - Table eb_user_sign is going to be initial synced 
[INFO ] 2024-10-26 06:03:04.256 - [任务 14][MysqlDroagon] - Query table 'eb_user_sign' counts: 1 
[INFO ] 2024-10-26 06:03:04.259 - [任务 14][MysqlDroagon] - Table [eb_user_sign] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:04.261 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_cate, offset: null 
[INFO ] 2024-10-26 06:03:04.262 - [任务 14][MysqlDroagon] - Table eb_store_product_cate is going to be initial synced 
[INFO ] 2024-10-26 06:03:04.267 - [任务 14][MysqlDroagon] - Table [eb_store_product_cate] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:04.267 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_cate' counts: 0 
[INFO ] 2024-10-26 06:03:04.275 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_experience_record, offset: null 
[INFO ] 2024-10-26 06:03:04.280 - [任务 14][MysqlDroagon] - Table eb_user_experience_record is going to be initial synced 
[INFO ] 2024-10-26 06:03:04.281 - [任务 14][MysqlDroagon] - Query table 'eb_user_experience_record' counts: 1 
[INFO ] 2024-10-26 06:03:04.287 - [任务 14][MysqlDroagon] - Table [eb_user_experience_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:04.287 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_brand_story, offset: null 
[INFO ] 2024-10-26 06:03:04.288 - [任务 14][MysqlDroagon] - Table eb_store_brand_story is going to be initial synced 
[INFO ] 2024-10-26 06:03:04.298 - [任务 14][MysqlDroagon] - Query table 'eb_store_brand_story' counts: 1 
[INFO ] 2024-10-26 06:03:04.300 - [任务 14][MysqlDroagon] - Table [eb_store_brand_story] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:04.300 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_heal, offset: null 
[INFO ] 2024-10-26 06:03:04.307 - [任务 14][MysqlDroagon] - Table eb_store_heal is going to be initial synced 
[INFO ] 2024-10-26 06:03:04.307 - [任务 14][MysqlDroagon] - Query table 'eb_store_heal' counts: 5 
[INFO ] 2024-10-26 06:03:04.308 - [任务 14][MysqlDroagon] - Table [eb_store_heal] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:04.308 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_description, offset: null 
[INFO ] 2024-10-26 06:03:04.308 - [任务 14][MysqlDroagon] - Table eb_store_product_description is going to be initial synced 
[INFO ] 2024-10-26 06:03:04.314 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_description' counts: 18 
[INFO ] 2024-10-26 06:03:04.351 - [任务 14][mysql184] - Table "liubai.eb_store_product_attr_value" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:04.353 - [任务 14][mysql184] - The table eb_store_product_attr_value has already exist. 
[INFO ] 2024-10-26 06:03:04.457 - [任务 14][mysql184] - Table "liubai.eb_wechat_callback" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:04.458 - [任务 14][mysql184] - The table eb_wechat_callback has already exist. 
[INFO ] 2024-10-26 06:03:04.556 - [任务 14][mysql184] - Table "liubai.eb_shipping_templates_free" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:04.556 - [任务 14][mysql184] - The table eb_shipping_templates_free has already exist. 
[INFO ] 2024-10-26 06:03:04.655 - [任务 14][mysql184] - Table "liubai.eb_system_notification" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:04.656 - [任务 14][mysql184] - The table eb_system_notification has already exist. 
[INFO ] 2024-10-26 06:03:04.764 - [任务 14][mysql184] - Table "liubai.eb_store_heal_user" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:04.765 - [任务 14][mysql184] - The table eb_store_heal_user has already exist. 
[INFO ] 2024-10-26 06:03:04.874 - [任务 14][mysql184] - Table "liubai.eb_user_level" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:04.875 - [任务 14][mysql184] - The table eb_user_level has already exist. 
[INFO ] 2024-10-26 06:03:05.094 - [任务 14][mysql184] - Table "liubai.eb_user_token" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:05.097 - [任务 14][mysql184] - The table eb_user_token has already exist. 
[INFO ] 2024-10-26 06:03:05.243 - [任务 14][mysql184] - Table "liubai.eb_system_group" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:05.245 - [任务 14][mysql184] - The table eb_system_group has already exist. 
[INFO ] 2024-10-26 06:03:05.358 - [任务 14][mysql184] - Table "liubai.eb_system_config" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:05.358 - [任务 14][mysql184] - The table eb_system_config has already exist. 
[INFO ] 2024-10-26 06:03:05.479 - [任务 14][mysql184] - Table "liubai.eb_user_sign" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:05.479 - [任务 14][mysql184] - The table eb_user_sign has already exist. 
[INFO ] 2024-10-26 06:03:05.606 - [任务 14][mysql184] - Table "liubai.eb_store_product_cate" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:05.606 - [任务 14][mysql184] - The table eb_store_product_cate has already exist. 
[INFO ] 2024-10-26 06:03:05.733 - [任务 14][mysql184] - Table "liubai.eb_user_experience_record" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:05.733 - [任务 14][mysql184] - The table eb_user_experience_record has already exist. 
[INFO ] 2024-10-26 06:03:05.864 - [任务 14][mysql184] - Table "liubai.eb_store_brand_story" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:05.865 - [任务 14][mysql184] - The table eb_store_brand_story has already exist. 
[INFO ] 2024-10-26 06:03:05.968 - [任务 14][mysql184] - Table "liubai.eb_store_heal" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:05.968 - [任务 14][mysql184] - The table eb_store_heal has already exist. 
[INFO ] 2024-10-26 06:03:06.077 - [任务 14][mysql184] - Table "liubai.eb_store_product_description" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:06.080 - [任务 14][mysql184] - The table eb_store_product_description has already exist. 
[INFO ] 2024-10-26 06:03:06.202 - [任务 14][mysql184] - Table "liubai.eb_store_product_reply" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:06.206 - [任务 14][mysql184] - The table eb_store_product_reply has already exist. 
[INFO ] 2024-10-26 06:03:06.325 - [任务 14][mysql184] - Table "liubai.eb_user_brokerage_record" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:06.327 - [任务 14][mysql184] - The table eb_user_brokerage_record has already exist. 
[INFO ] 2024-10-26 06:03:06.423 - [任务 14][mysql184] - Table "liubai.eb_system_attachment" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:06.423 - [任务 14][mysql184] - The table eb_system_attachment has already exist. 
[INFO ] 2024-10-26 06:03:06.523 - [任务 14][mysql184] - Table "liubai.eb_system_store_staff" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:06.523 - [任务 14][mysql184] - The table eb_system_store_staff has already exist. 
[INFO ] 2024-10-26 06:03:06.626 - [任务 14][mysql184] - Table "liubai.eb_wechat_reply" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:06.627 - [任务 14][mysql184] - The table eb_wechat_reply has already exist. 
[INFO ] 2024-10-26 06:03:06.744 - [任务 14][mysql184] - Table "liubai.eb_store_bargain_user_help" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:06.744 - [任务 14][mysql184] - The table eb_store_bargain_user_help has already exist. 
[INFO ] 2024-10-26 06:03:06.867 - [任务 14][mysql184] - Table "liubai.eb_store_seckill" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:06.867 - [任务 14][mysql184] - The table eb_store_seckill has already exist. 
[INFO ] 2024-10-26 06:03:06.974 - [任务 14][mysql184] - Table "liubai.eb_store_product_attr_result" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:06.975 - [任务 14][mysql184] - The table eb_store_product_attr_result has already exist. 
[INFO ] 2024-10-26 06:03:07.076 - [任务 14][mysql184] - Table "liubai.eb_shipping_templates" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:07.077 - [任务 14][mysql184] - The table eb_shipping_templates has already exist. 
[INFO ] 2024-10-26 06:03:07.171 - [任务 14][mysql184] - Table "liubai.eb_store_seckill_manger" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:07.171 - [任务 14][mysql184] - The table eb_store_seckill_manger has already exist. 
[INFO ] 2024-10-26 06:03:07.324 - [任务 14][mysql184] - Table "liubai.eb_user" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:07.324 - [任务 14][mysql184] - The table eb_user has already exist. 
[INFO ] 2024-10-26 06:03:07.499 - [任务 14][mysql184] - Table "liubai.eb_store_order_status" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:07.499 - [任务 14][mysql184] - The table eb_store_order_status has already exist. 
[INFO ] 2024-10-26 06:03:07.645 - [任务 14][mysql184] - Table "liubai.eb_system_form_temp" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:07.645 - [任务 14][mysql184] - The table eb_system_form_temp has already exist. 
[INFO ] 2024-10-26 06:03:07.762 - [任务 14][mysql184] - Table "liubai.eb_store_experience_reply" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:07.762 - [任务 14][mysql184] - The table eb_store_experience_reply has already exist. 
[INFO ] 2024-10-26 06:03:07.859 - [任务 14][mysql184] - Table "liubai.eb_store_bargain_user" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:07.861 - [任务 14][mysql184] - The table eb_store_bargain_user has already exist. 
[INFO ] 2024-10-26 06:03:07.964 - [任务 14][mysql184] - Table "liubai.eb_system_store" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:07.964 - [任务 14][mysql184] - The table eb_system_store has already exist. 
[INFO ] 2024-10-26 06:03:08.063 - [任务 14][mysql184] - Table "liubai.eb_shipping_templates_region" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:08.063 - [任务 14][mysql184] - The table eb_shipping_templates_region has already exist. 
[INFO ] 2024-10-26 06:03:08.169 - [任务 14][mysql184] - Table "liubai.eb_user_recharge" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:08.169 - [任务 14][mysql184] - The table eb_user_recharge has already exist. 
[INFO ] 2024-10-26 06:03:08.282 - [任务 14][mysql184] - Table "liubai.eb_store_product_coupon" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:08.282 - [任务 14][mysql184] - The table eb_store_product_coupon has already exist. 
[INFO ] 2024-10-26 06:03:08.381 - [任务 14][mysql184] - Table "liubai.eb_wechat_pay_info" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:08.382 - [任务 14][mysql184] - The table eb_wechat_pay_info has already exist. 
[INFO ] 2024-10-26 06:03:08.495 - [任务 14][mysql184] - Table "liubai.eb_store_combination" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:08.495 - [任务 14][mysql184] - The table eb_store_combination has already exist. 
[INFO ] 2024-10-26 06:03:08.624 - [任务 14][mysql184] - Table "liubai.eb_store_product" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:08.624 - [任务 14][mysql184] - The table eb_store_product has already exist. 
[INFO ] 2024-10-26 06:03:08.758 - [任务 14][mysql184] - Table "liubai.eb_article" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:08.759 - [任务 14][mysql184] - The table eb_article has already exist. 
[INFO ] 2024-10-26 06:03:08.862 - [任务 14][mysql184] - Table "liubai.eb_user_bill" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:08.862 - [任务 14][mysql184] - The table eb_user_bill has already exist. 
[INFO ] 2024-10-26 06:03:08.961 - [任务 14][mysql184] - Table "liubai.eb_store_experience_relation" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:08.961 - [任务 14][mysql184] - The table eb_store_experience_relation has already exist. 
[INFO ] 2024-10-26 06:03:09.084 - [任务 14][mysql184] - Table "liubai.eb_wechat_exceptions" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:09.084 - [任务 14][mysql184] - The table eb_wechat_exceptions has already exist. 
[INFO ] 2024-10-26 06:03:09.191 - [任务 14][mysql184] - Table "liubai.eb_template_message" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:09.191 - [任务 14][mysql184] - The table eb_template_message has already exist. 
[INFO ] 2024-10-26 06:03:09.315 - [任务 14][mysql184] - Table "liubai.eb_user_integral_record" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:09.315 - [任务 14][mysql184] - The table eb_user_integral_record has already exist. 
[INFO ] 2024-10-26 06:03:09.462 - [任务 14][mysql184] - Table "liubai.eb_store_coupon" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:09.462 - [任务 14][mysql184] - The table eb_store_coupon has already exist. 
[INFO ] 2024-10-26 06:03:09.605 - [任务 14][mysql184] - Table "liubai.eb_user_visit_record" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:09.607 - [任务 14][mysql184] - The table eb_user_visit_record has already exist. 
[INFO ] 2024-10-26 06:03:09.728 - [任务 14][mysql184] - Table "liubai.eb_system_role_menu" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:09.728 - [任务 14][mysql184] - The table eb_system_role_menu has already exist. 
[INFO ] 2024-10-26 06:03:09.827 - [任务 14][mysql184] - Table "liubai.eb_store_product_log" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:09.827 - [任务 14][mysql184] - The table eb_store_product_log has already exist. 
[INFO ] 2024-10-26 06:03:09.932 - [任务 14][mysql184] - Table "liubai.eb_store_product_series" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:09.932 - [任务 14][mysql184] - The table eb_store_product_series has already exist. 
[INFO ] 2024-10-26 06:03:10.064 - [任务 14][mysql184] - Table "liubai.eb_store_product_attr" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:10.064 - [任务 14][mysql184] - The table eb_store_product_attr has already exist. 
[INFO ] 2024-10-26 06:03:10.159 - [任务 14][mysql184] - Table "liubai.eb_store_bargain" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:10.159 - [任务 14][mysql184] - The table eb_store_bargain has already exist. 
[INFO ] 2024-10-26 06:03:10.269 - [任务 14][mysql184] - Table "liubai.eb_store_experience_audio" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:10.270 - [任务 14][mysql184] - The table eb_store_experience_audio has already exist. 
[INFO ] 2024-10-26 06:03:10.380 - [任务 14][mysql184] - Table "liubai.eb_system_city" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:10.511 - [任务 14][mysql184] - The table eb_system_city has already exist. 
[INFO ] 2024-10-26 06:03:10.512 - [任务 14][mysql184] - Table "liubai.eb_sms_record" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:10.512 - [任务 14][mysql184] - The table eb_sms_record has already exist. 
[INFO ] 2024-10-26 06:03:10.633 - [任务 14][mysql184] - Table "liubai.eb_user_extract" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:10.633 - [任务 14][mysql184] - The table eb_user_extract has already exist. 
[INFO ] 2024-10-26 06:03:10.759 - [任务 14][mysql184] - Table "liubai.eb_user_group" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:10.760 - [任务 14][mysql184] - The table eb_user_group has already exist. 
[INFO ] 2024-10-26 06:03:10.877 - [任务 14][mysql184] - Table "liubai.eb_store_product_rule" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:10.878 - [任务 14][mysql184] - The table eb_store_product_rule has already exist. 
[INFO ] 2024-10-26 06:03:10.985 - [任务 14][mysql184] - Table "liubai.eb_store_cart" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:10.985 - [任务 14][mysql184] - The table eb_store_cart has already exist. 
[INFO ] 2024-10-26 06:03:11.108 - [任务 14][mysql184] - Table "liubai.eb_system_admin" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:11.108 - [任务 14][mysql184] - The table eb_system_admin has already exist. 
[INFO ] 2024-10-26 06:03:11.217 - [任务 14][mysql184] - Table "liubai.eb_system_menu" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:11.217 - [任务 14][mysql184] - The table eb_system_menu has already exist. 
[INFO ] 2024-10-26 06:03:11.342 - [任务 14][mysql184] - Table "liubai.eb_system_user_level" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:11.343 - [任务 14][mysql184] - The table eb_system_user_level has already exist. 
[INFO ] 2024-10-26 06:03:11.454 - [任务 14][mysql184] - Table "liubai.eb_category" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:11.454 - [任务 14][mysql184] - The table eb_category has already exist. 
[INFO ] 2024-10-26 06:03:11.582 - [任务 14][mysql184] - Table "liubai.eb_sms_template" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:11.583 - [任务 14][mysql184] - The table eb_sms_template has already exist. 
[INFO ] 2024-10-26 06:03:11.979 - [任务 14][mysql184] - Table "liubai.eb_user_tag" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:11.979 - [任务 14][mysql184] - The table eb_user_tag has already exist. 
[INFO ] 2024-10-26 06:03:11.979 - [任务 14][mysql184] - Table "liubai.eb_store_order_info" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:11.979 - [任务 14][mysql184] - The table eb_store_order_info has already exist. 
[INFO ] 2024-10-26 06:03:12.089 - [任务 14][mysql184] - Table "liubai.eb_store_coupon_user" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:12.089 - [任务 14][mysql184] - The table eb_store_coupon_user has already exist. 
[INFO ] 2024-10-26 06:03:12.185 - [任务 14][mysql184] - Table "liubai.eb_system_role" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:12.287 - [任务 14][mysql184] - The table eb_system_role has already exist. 
[INFO ] 2024-10-26 06:03:12.288 - [任务 14][mysql184] - Table "liubai.eb_store_experience_audio_history" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:12.288 - [任务 14][mysql184] - The table eb_store_experience_audio_history has already exist. 
[INFO ] 2024-10-26 06:03:12.403 - [任务 14][mysql184] - Table "liubai.eb_store_pink" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:12.403 - [任务 14][mysql184] - The table eb_store_pink has already exist. 
[INFO ] 2024-10-26 06:03:12.527 - [任务 14][mysql184] - Table "liubai.eb_express" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:12.658 - [任务 14][mysql184] - The table eb_express has already exist. 
[INFO ] 2024-10-26 06:03:12.658 - [任务 14][mysql184] - Table "liubai.eb_store_product_relation" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:12.658 - [任务 14][mysql184] - The table eb_store_product_relation has already exist. 
[INFO ] 2024-10-26 06:03:12.777 - [任务 14][mysql184] - Table "liubai.eb_system_group_data" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:12.898 - [任务 14][mysql184] - The table eb_system_group_data has already exist. 
[INFO ] 2024-10-26 06:03:12.898 - [任务 14][mysql184] - Table "liubai.eb_user_address" exists, skip auto create table 
[INFO ] 2024-10-26 06:03:12.899 - [任务 14][mysql184] - The table eb_user_address has already exist. 
[INFO ] 2024-10-26 06:03:12.968 - [任务 14][MysqlDroagon] - Table [eb_store_product_description] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:12.969 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_reply, offset: null 
[INFO ] 2024-10-26 06:03:12.969 - [任务 14][MysqlDroagon] - Table eb_store_product_reply is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.020 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_reply' counts: 1 
[INFO ] 2024-10-26 06:03:13.022 - [任务 14][MysqlDroagon] - Table [eb_store_product_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.022 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_brokerage_record, offset: null 
[INFO ] 2024-10-26 06:03:13.022 - [任务 14][MysqlDroagon] - Table eb_user_brokerage_record is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.040 - [任务 14][MysqlDroagon] - Table [eb_user_brokerage_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.041 - [任务 14][MysqlDroagon] - Query table 'eb_user_brokerage_record' counts: 0 
[INFO ] 2024-10-26 06:03:13.041 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_attachment, offset: null 
[INFO ] 2024-10-26 06:03:13.041 - [任务 14][MysqlDroagon] - Table eb_system_attachment is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.081 - [任务 14][MysqlDroagon] - Query table 'eb_system_attachment' counts: 288 
[INFO ] 2024-10-26 06:03:13.082 - [任务 14][MysqlDroagon] - Table [eb_system_attachment] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.082 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_store_staff, offset: null 
[INFO ] 2024-10-26 06:03:13.085 - [任务 14][MysqlDroagon] - Table eb_system_store_staff is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.108 - [任务 14][MysqlDroagon] - Table [eb_system_store_staff] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.110 - [任务 14][MysqlDroagon] - Query table 'eb_system_store_staff' counts: 0 
[INFO ] 2024-10-26 06:03:13.110 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_wechat_reply, offset: null 
[INFO ] 2024-10-26 06:03:13.110 - [任务 14][MysqlDroagon] - Table eb_wechat_reply is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.134 - [任务 14][MysqlDroagon] - Query table 'eb_wechat_reply' counts: 1 
[INFO ] 2024-10-26 06:03:13.135 - [任务 14][MysqlDroagon] - Table [eb_wechat_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.135 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_bargain_user_help, offset: null 
[INFO ] 2024-10-26 06:03:13.150 - [任务 14][MysqlDroagon] - Table eb_store_bargain_user_help is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.150 - [任务 14][MysqlDroagon] - Query table 'eb_store_bargain_user_help' counts: 0 
[INFO ] 2024-10-26 06:03:13.151 - [任务 14][MysqlDroagon] - Table [eb_store_bargain_user_help] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.151 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_seckill, offset: null 
[INFO ] 2024-10-26 06:03:13.151 - [任务 14][MysqlDroagon] - Table eb_store_seckill is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.167 - [任务 14][MysqlDroagon] - Query table 'eb_store_seckill' counts: 4 
[INFO ] 2024-10-26 06:03:13.168 - [任务 14][MysqlDroagon] - Table [eb_store_seckill] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.190 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_attr_result, offset: null 
[INFO ] 2024-10-26 06:03:13.195 - [任务 14][MysqlDroagon] - Table eb_store_product_attr_result is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.201 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_attr_result' counts: 0 
[INFO ] 2024-10-26 06:03:13.201 - [任务 14][MysqlDroagon] - Table [eb_store_product_attr_result] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.203 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_shipping_templates, offset: null 
[INFO ] 2024-10-26 06:03:13.203 - [任务 14][MysqlDroagon] - Table eb_shipping_templates is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.216 - [任务 14][MysqlDroagon] - Query table 'eb_shipping_templates' counts: 1 
[INFO ] 2024-10-26 06:03:13.216 - [任务 14][MysqlDroagon] - Table [eb_shipping_templates] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.216 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_seckill_manger, offset: null 
[INFO ] 2024-10-26 06:03:13.216 - [任务 14][MysqlDroagon] - Table eb_store_seckill_manger is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.225 - [任务 14][MysqlDroagon] - Query table 'eb_store_seckill_manger' counts: 3 
[INFO ] 2024-10-26 06:03:13.225 - [任务 14][MysqlDroagon] - Table [eb_store_seckill_manger] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.227 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user, offset: null 
[INFO ] 2024-10-26 06:03:13.227 - [任务 14][MysqlDroagon] - Table eb_user is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.233 - [任务 14][MysqlDroagon] - Query table 'eb_user' counts: 4 
[INFO ] 2024-10-26 06:03:13.235 - [任务 14][MysqlDroagon] - Table [eb_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.235 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_order_status, offset: null 
[INFO ] 2024-10-26 06:03:13.239 - [任务 14][MysqlDroagon] - Table eb_store_order_status is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.239 - [任务 14][MysqlDroagon] - Table [eb_store_order_status] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.239 - [任务 14][MysqlDroagon] - Query table 'eb_store_order_status' counts: 0 
[INFO ] 2024-10-26 06:03:13.240 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_form_temp, offset: null 
[INFO ] 2024-10-26 06:03:13.246 - [任务 14][MysqlDroagon] - Table eb_system_form_temp is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.246 - [任务 14][MysqlDroagon] - Query table 'eb_system_form_temp' counts: 60 
[INFO ] 2024-10-26 06:03:13.247 - [任务 14][MysqlDroagon] - Table [eb_system_form_temp] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.247 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_experience_reply, offset: null 
[INFO ] 2024-10-26 06:03:13.251 - [任务 14][MysqlDroagon] - Table eb_store_experience_reply is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.251 - [任务 14][MysqlDroagon] - Query table 'eb_store_experience_reply' counts: 2 
[INFO ] 2024-10-26 06:03:13.255 - [任务 14][MysqlDroagon] - Table [eb_store_experience_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.255 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_bargain_user, offset: null 
[INFO ] 2024-10-26 06:03:13.262 - [任务 14][MysqlDroagon] - Table eb_store_bargain_user is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.262 - [任务 14][MysqlDroagon] - Table [eb_store_bargain_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.262 - [任务 14][MysqlDroagon] - Query table 'eb_store_bargain_user' counts: 0 
[INFO ] 2024-10-26 06:03:13.263 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_store, offset: null 
[INFO ] 2024-10-26 06:03:13.298 - [任务 14][MysqlDroagon] - Table eb_system_store is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.298 - [任务 14][MysqlDroagon] - Table [eb_system_store] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.298 - [任务 14][MysqlDroagon] - Query table 'eb_system_store' counts: 0 
[INFO ] 2024-10-26 06:03:13.298 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_shipping_templates_region, offset: null 
[INFO ] 2024-10-26 06:03:13.304 - [任务 14][MysqlDroagon] - Table eb_shipping_templates_region is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.304 - [任务 14][MysqlDroagon] - Query table 'eb_shipping_templates_region' counts: 369 
[INFO ] 2024-10-26 06:03:13.323 - [任务 14][MysqlDroagon] - Table [eb_shipping_templates_region] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.323 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_recharge, offset: null 
[INFO ] 2024-10-26 06:03:13.331 - [任务 14][MysqlDroagon] - Table eb_user_recharge is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.331 - [任务 14][MysqlDroagon] - Table [eb_user_recharge] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.331 - [任务 14][MysqlDroagon] - Query table 'eb_user_recharge' counts: 0 
[INFO ] 2024-10-26 06:03:13.331 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_coupon, offset: null 
[INFO ] 2024-10-26 06:03:13.335 - [任务 14][MysqlDroagon] - Table eb_store_product_coupon is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.335 - [任务 14][MysqlDroagon] - Table [eb_store_product_coupon] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.335 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_coupon' counts: 0 
[INFO ] 2024-10-26 06:03:13.335 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_wechat_pay_info, offset: null 
[INFO ] 2024-10-26 06:03:13.339 - [任务 14][MysqlDroagon] - Table eb_wechat_pay_info is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.339 - [任务 14][MysqlDroagon] - Table [eb_wechat_pay_info] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.339 - [任务 14][MysqlDroagon] - Query table 'eb_wechat_pay_info' counts: 0 
[INFO ] 2024-10-26 06:03:13.339 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_combination, offset: null 
[INFO ] 2024-10-26 06:03:13.344 - [任务 14][MysqlDroagon] - Table eb_store_combination is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.345 - [任务 14][MysqlDroagon] - Query table 'eb_store_combination' counts: 4 
[INFO ] 2024-10-26 06:03:13.346 - [任务 14][MysqlDroagon] - Table [eb_store_combination] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.346 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product, offset: null 
[INFO ] 2024-10-26 06:03:13.350 - [任务 14][MysqlDroagon] - Table eb_store_product is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.350 - [任务 14][MysqlDroagon] - Query table 'eb_store_product' counts: 8 
[INFO ] 2024-10-26 06:03:13.351 - [任务 14][MysqlDroagon] - Table [eb_store_product] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.351 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_article, offset: null 
[INFO ] 2024-10-26 06:03:13.355 - [任务 14][MysqlDroagon] - Table eb_article is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.355 - [任务 14][MysqlDroagon] - Table [eb_article] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.356 - [任务 14][MysqlDroagon] - Query table 'eb_article' counts: 0 
[INFO ] 2024-10-26 06:03:13.356 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_bill, offset: null 
[INFO ] 2024-10-26 06:03:13.360 - [任务 14][MysqlDroagon] - Table eb_user_bill is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.361 - [任务 14][MysqlDroagon] - Table [eb_user_bill] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.361 - [任务 14][MysqlDroagon] - Query table 'eb_user_bill' counts: 0 
[INFO ] 2024-10-26 06:03:13.361 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_experience_relation, offset: null 
[INFO ] 2024-10-26 06:03:13.364 - [任务 14][MysqlDroagon] - Table eb_store_experience_relation is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.364 - [任务 14][MysqlDroagon] - Query table 'eb_store_experience_relation' counts: 2 
[INFO ] 2024-10-26 06:03:13.365 - [任务 14][MysqlDroagon] - Table [eb_store_experience_relation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.365 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_wechat_exceptions, offset: null 
[INFO ] 2024-10-26 06:03:13.368 - [任务 14][MysqlDroagon] - Table eb_wechat_exceptions is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.369 - [任务 14][MysqlDroagon] - Query table 'eb_wechat_exceptions' counts: 31 
[INFO ] 2024-10-26 06:03:13.371 - [任务 14][MysqlDroagon] - Table [eb_wechat_exceptions] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.371 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_template_message, offset: null 
[INFO ] 2024-10-26 06:03:13.375 - [任务 14][MysqlDroagon] - Table eb_template_message is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.375 - [任务 14][MysqlDroagon] - Query table 'eb_template_message' counts: 15 
[INFO ] 2024-10-26 06:03:13.375 - [任务 14][MysqlDroagon] - Table [eb_template_message] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.375 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_integral_record, offset: null 
[INFO ] 2024-10-26 06:03:13.381 - [任务 14][MysqlDroagon] - Table eb_user_integral_record is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.382 - [任务 14][MysqlDroagon] - Query table 'eb_user_integral_record' counts: 1 
[INFO ] 2024-10-26 06:03:13.382 - [任务 14][MysqlDroagon] - Table [eb_user_integral_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.382 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_coupon, offset: null 
[INFO ] 2024-10-26 06:03:13.382 - [任务 14][MysqlDroagon] - Table eb_store_coupon is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.386 - [任务 14][MysqlDroagon] - Query table 'eb_store_coupon' counts: 2 
[INFO ] 2024-10-26 06:03:13.387 - [任务 14][MysqlDroagon] - Table [eb_store_coupon] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.389 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_visit_record, offset: null 
[INFO ] 2024-10-26 06:03:13.389 - [任务 14][MysqlDroagon] - Table eb_user_visit_record is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.403 - [任务 14][MysqlDroagon] - Query table 'eb_user_visit_record' counts: 1247 
[INFO ] 2024-10-26 06:03:13.403 - [任务 14][MysqlDroagon] - Table [eb_user_visit_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:13.403 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_role_menu, offset: null 
[INFO ] 2024-10-26 06:03:13.403 - [任务 14][MysqlDroagon] - Table eb_system_role_menu is going to be initial synced 
[INFO ] 2024-10-26 06:03:13.604 - [任务 14][MysqlDroagon] - Query table 'eb_system_role_menu' counts: 595 
[INFO ] 2024-10-26 06:03:14.062 - [任务 14][MysqlDroagon] - Table [eb_system_role_menu] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:14.062 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_log, offset: null 
[INFO ] 2024-10-26 06:03:14.063 - [任务 14][MysqlDroagon] - Table eb_store_product_log is going to be initial synced 
[INFO ] 2024-10-26 06:03:14.082 - [任务 14][MysqlDroagon] - Table [eb_store_product_log] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:14.084 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_log' counts: 0 
[INFO ] 2024-10-26 06:03:14.084 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_series, offset: null 
[INFO ] 2024-10-26 06:03:14.084 - [任务 14][MysqlDroagon] - Table eb_store_product_series is going to be initial synced 
[INFO ] 2024-10-26 06:03:14.099 - [任务 14][MysqlDroagon] - Table [eb_store_product_series] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:14.099 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_series' counts: 5 
[INFO ] 2024-10-26 06:03:14.099 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_attr, offset: null 
[INFO ] 2024-10-26 06:03:14.107 - [任务 14][MysqlDroagon] - Table eb_store_product_attr is going to be initial synced 
[INFO ] 2024-10-26 06:03:14.107 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_attr' counts: 24 
[INFO ] 2024-10-26 06:03:14.107 - [任务 14][MysqlDroagon] - Table [eb_store_product_attr] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:14.107 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_bargain, offset: null 
[INFO ] 2024-10-26 06:03:14.112 - [任务 14][MysqlDroagon] - Table eb_store_bargain is going to be initial synced 
[INFO ] 2024-10-26 06:03:14.112 - [任务 14][MysqlDroagon] - Query table 'eb_store_bargain' counts: 2 
[INFO ] 2024-10-26 06:03:14.113 - [任务 14][MysqlDroagon] - Table [eb_store_bargain] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:14.113 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_experience_audio, offset: null 
[INFO ] 2024-10-26 06:03:14.119 - [任务 14][MysqlDroagon] - Table eb_store_experience_audio is going to be initial synced 
[INFO ] 2024-10-26 06:03:14.121 - [任务 14][MysqlDroagon] - Table [eb_store_experience_audio] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:14.122 - [任务 14][MysqlDroagon] - Query table 'eb_store_experience_audio' counts: 5 
[INFO ] 2024-10-26 06:03:14.122 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_city, offset: null 
[INFO ] 2024-10-26 06:03:14.122 - [任务 14][MysqlDroagon] - Table eb_system_city is going to be initial synced 
[INFO ] 2024-10-26 06:03:14.137 - [任务 14][MysqlDroagon] - Query table 'eb_system_city' counts: 3938 
[INFO ] 2024-10-26 06:03:15.895 - [任务 14][MysqlDroagon] - Table [eb_system_city] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:15.895 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_sms_record, offset: null 
[INFO ] 2024-10-26 06:03:15.907 - [任务 14][MysqlDroagon] - Table eb_sms_record is going to be initial synced 
[INFO ] 2024-10-26 06:03:15.907 - [任务 14][MysqlDroagon] - Table [eb_sms_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:15.908 - [任务 14][MysqlDroagon] - Query table 'eb_sms_record' counts: 0 
[INFO ] 2024-10-26 06:03:15.908 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_extract, offset: null 
[INFO ] 2024-10-26 06:03:15.921 - [任务 14][MysqlDroagon] - Table eb_user_extract is going to be initial synced 
[INFO ] 2024-10-26 06:03:15.922 - [任务 14][MysqlDroagon] - Table [eb_user_extract] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:15.922 - [任务 14][MysqlDroagon] - Query table 'eb_user_extract' counts: 0 
[INFO ] 2024-10-26 06:03:15.922 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_group, offset: null 
[INFO ] 2024-10-26 06:03:15.931 - [任务 14][MysqlDroagon] - Table eb_user_group is going to be initial synced 
[INFO ] 2024-10-26 06:03:15.931 - [任务 14][MysqlDroagon] - Query table 'eb_user_group' counts: 3 
[INFO ] 2024-10-26 06:03:15.934 - [任务 14][MysqlDroagon] - Table [eb_user_group] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:15.934 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_rule, offset: null 
[INFO ] 2024-10-26 06:03:15.942 - [任务 14][MysqlDroagon] - Table eb_store_product_rule is going to be initial synced 
[INFO ] 2024-10-26 06:03:15.942 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_rule' counts: 2 
[INFO ] 2024-10-26 06:03:15.942 - [任务 14][MysqlDroagon] - Table [eb_store_product_rule] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:15.942 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_cart, offset: null 
[INFO ] 2024-10-26 06:03:15.950 - [任务 14][MysqlDroagon] - Table eb_store_cart is going to be initial synced 
[INFO ] 2024-10-26 06:03:15.950 - [任务 14][MysqlDroagon] - Query table 'eb_store_cart' counts: 3 
[INFO ] 2024-10-26 06:03:15.950 - [任务 14][MysqlDroagon] - Table [eb_store_cart] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:15.950 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_admin, offset: null 
[INFO ] 2024-10-26 06:03:15.954 - [任务 14][MysqlDroagon] - Table eb_system_admin is going to be initial synced 
[INFO ] 2024-10-26 06:03:15.954 - [任务 14][MysqlDroagon] - Query table 'eb_system_admin' counts: 2 
[INFO ] 2024-10-26 06:03:15.955 - [任务 14][MysqlDroagon] - Table [eb_system_admin] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:15.955 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_menu, offset: null 
[INFO ] 2024-10-26 06:03:15.960 - [任务 14][MysqlDroagon] - Table eb_system_menu is going to be initial synced 
[INFO ] 2024-10-26 06:03:15.960 - [任务 14][MysqlDroagon] - Query table 'eb_system_menu' counts: 369 
[INFO ] 2024-10-26 06:03:15.972 - [任务 14][MysqlDroagon] - Table [eb_system_menu] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:15.972 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_user_level, offset: null 
[INFO ] 2024-10-26 06:03:15.976 - [任务 14][MysqlDroagon] - Table eb_system_user_level is going to be initial synced 
[INFO ] 2024-10-26 06:03:15.976 - [任务 14][MysqlDroagon] - Query table 'eb_system_user_level' counts: 5 
[INFO ] 2024-10-26 06:03:15.977 - [任务 14][MysqlDroagon] - Table [eb_system_user_level] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:15.977 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_category, offset: null 
[INFO ] 2024-10-26 06:03:15.982 - [任务 14][MysqlDroagon] - Table eb_category is going to be initial synced 
[INFO ] 2024-10-26 06:03:15.982 - [任务 14][MysqlDroagon] - Query table 'eb_category' counts: 295 
[INFO ] 2024-10-26 06:03:15.991 - [任务 14][MysqlDroagon] - Table [eb_category] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:15.991 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_sms_template, offset: null 
[INFO ] 2024-10-26 06:03:15.995 - [任务 14][MysqlDroagon] - Table eb_sms_template is going to be initial synced 
[INFO ] 2024-10-26 06:03:15.995 - [任务 14][MysqlDroagon] - Query table 'eb_sms_template' counts: 7 
[INFO ] 2024-10-26 06:03:15.995 - [任务 14][MysqlDroagon] - Table [eb_sms_template] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:15.995 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_tag, offset: null 
[INFO ] 2024-10-26 06:03:15.998 - [任务 14][MysqlDroagon] - Table eb_user_tag is going to be initial synced 
[INFO ] 2024-10-26 06:03:15.998 - [任务 14][MysqlDroagon] - Query table 'eb_user_tag' counts: 5 
[INFO ] 2024-10-26 06:03:15.999 - [任务 14][MysqlDroagon] - Table [eb_user_tag] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:15.999 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_order_info, offset: null 
[INFO ] 2024-10-26 06:03:16.002 - [任务 14][MysqlDroagon] - Table eb_store_order_info is going to be initial synced 
[INFO ] 2024-10-26 06:03:16.002 - [任务 14][MysqlDroagon] - Table [eb_store_order_info] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:16.003 - [任务 14][MysqlDroagon] - Query table 'eb_store_order_info' counts: 0 
[INFO ] 2024-10-26 06:03:16.003 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_coupon_user, offset: null 
[INFO ] 2024-10-26 06:03:16.006 - [任务 14][MysqlDroagon] - Table eb_store_coupon_user is going to be initial synced 
[INFO ] 2024-10-26 06:03:16.006 - [任务 14][MysqlDroagon] - Table [eb_store_coupon_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:16.006 - [任务 14][MysqlDroagon] - Query table 'eb_store_coupon_user' counts: 0 
[INFO ] 2024-10-26 06:03:16.006 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_role, offset: null 
[INFO ] 2024-10-26 06:03:16.010 - [任务 14][MysqlDroagon] - Table eb_system_role is going to be initial synced 
[INFO ] 2024-10-26 06:03:16.010 - [任务 14][MysqlDroagon] - Query table 'eb_system_role' counts: 2 
[INFO ] 2024-10-26 06:03:16.010 - [任务 14][MysqlDroagon] - Table [eb_system_role] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:16.010 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_experience_audio_history, offset: null 
[INFO ] 2024-10-26 06:03:16.013 - [任务 14][MysqlDroagon] - Table eb_store_experience_audio_history is going to be initial synced 
[INFO ] 2024-10-26 06:03:16.013 - [任务 14][MysqlDroagon] - Query table 'eb_store_experience_audio_history' counts: 4 
[INFO ] 2024-10-26 06:03:16.014 - [任务 14][MysqlDroagon] - Table [eb_store_experience_audio_history] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:16.014 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_pink, offset: null 
[INFO ] 2024-10-26 06:03:16.017 - [任务 14][MysqlDroagon] - Table eb_store_pink is going to be initial synced 
[INFO ] 2024-10-26 06:03:16.017 - [任务 14][MysqlDroagon] - Table [eb_store_pink] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:16.017 - [任务 14][MysqlDroagon] - Query table 'eb_store_pink' counts: 0 
[INFO ] 2024-10-26 06:03:16.017 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_express, offset: null 
[INFO ] 2024-10-26 06:03:16.022 - [任务 14][MysqlDroagon] - Table eb_express is going to be initial synced 
[INFO ] 2024-10-26 06:03:16.022 - [任务 14][MysqlDroagon] - Query table 'eb_express' counts: 1067 
[INFO ] 2024-10-26 06:03:16.079 - [任务 14][MysqlDroagon] - Table [eb_express] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:16.079 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_relation, offset: null 
[INFO ] 2024-10-26 06:03:16.084 - [任务 14][MysqlDroagon] - Table eb_store_product_relation is going to be initial synced 
[INFO ] 2024-10-26 06:03:16.084 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_relation' counts: 0 
[INFO ] 2024-10-26 06:03:16.085 - [任务 14][MysqlDroagon] - Table [eb_store_product_relation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:16.085 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_group_data, offset: null 
[INFO ] 2024-10-26 06:03:16.089 - [任务 14][MysqlDroagon] - Table eb_system_group_data is going to be initial synced 
[INFO ] 2024-10-26 06:03:16.089 - [任务 14][MysqlDroagon] - Query table 'eb_system_group_data' counts: 68 
[INFO ] 2024-10-26 06:03:16.091 - [任务 14][MysqlDroagon] - Table [eb_system_group_data] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:16.091 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_address, offset: null 
[INFO ] 2024-10-26 06:03:16.094 - [任务 14][MysqlDroagon] - Table eb_user_address is going to be initial synced 
[INFO ] 2024-10-26 06:03:16.094 - [任务 14][MysqlDroagon] - Table [eb_user_address] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:03:16.094 - [任务 14][MysqlDroagon] - Query table 'eb_user_address' counts: 0 
[INFO ] 2024-10-26 06:03:16.095 - [任务 14][MysqlDroagon] - Initial sync completed 
[INFO ] 2024-10-26 06:03:18.198 - [任务 14][mysql184] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.Exception:  Binlog config is close
 - TapdataEvent{syncStage=null, tapEvent=null, nodeIds=[4ac5ce09-692f-447a-82f6-00938409fa12], sourceTime=null, sourceSerialNo=null} 
[ERROR] 2024-10-26 06:03:18.245 - [任务 14][mysql184] - java.lang.Exception:  Binlog config is close
 - TapdataEvent{syncStage=null, tapEvent=null, nodeIds=[4ac5ce09-692f-447a-82f6-00938409fa12], sourceTime=null, sourceSerialNo=null} <-- Error Message -->
java.lang.Exception:  Binlog config is close
 - TapdataEvent{syncStage=null, tapEvent=null, nodeIds=[4ac5ce09-692f-447a-82f6-00938409fa12], sourceTime=null, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.lang.Exception:  Binlog config is close
	io.tapdata.connector.mysql.MysqlConnector.timestampToStreamOffset(MysqlConnector.java:679)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromTime$8(HazelcastSourcePdkBaseNode.java:605)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...

<-- Full Stack Trace -->
java.lang.Exception:  Binlog config is close
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvent(HazelcastTargetPdkBaseNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:620)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:572)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:528)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:500)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:546)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.Exception:  Binlog config is close
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:153)
	at io.tapdata.connector.mysql.MysqlConnector.timestampToStreamOffset(MysqlConnector.java:680)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromTime$8(HazelcastSourcePdkBaseNode.java:605)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromTime(HazelcastSourcePdkBaseNode.java:603)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetInitialAndCDC(HazelcastSourcePdkBaseNode.java:583)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:517)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:400)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:292)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:213)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: java.lang.Exception:  Binlog config is close
	at io.tapdata.connector.mysql.MysqlConnector.timestampToStreamOffset(MysqlConnector.java:679)
	... 21 more

[INFO ] 2024-10-26 06:03:18.246 - [任务 14][mysql184] - Job suspend in error handle 
[INFO ] 2024-10-26 06:03:18.650 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] running status set to false 
[INFO ] 2024-10-26 06:03:18.709 - [任务 14][MysqlDroagon] - PDK connector node stopped: HazelcastSourcePdkDataNode-4ac5ce09-692f-447a-82f6-00938409fa12 
[INFO ] 2024-10-26 06:03:18.710 - [任务 14][MysqlDroagon] - PDK connector node released: HazelcastSourcePdkDataNode-4ac5ce09-692f-447a-82f6-00938409fa12 
[INFO ] 2024-10-26 06:03:18.715 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] schema data cleaned 
[INFO ] 2024-10-26 06:03:18.720 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] monitor closed 
[INFO ] 2024-10-26 06:03:18.734 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] close complete, cost 83 ms 
[INFO ] 2024-10-26 06:03:18.735 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] running status set to false 
[INFO ] 2024-10-26 06:03:18.815 - [任务 14][mysql184] - PDK connector node stopped: HazelcastTargetPdkDataNode-f66e37b8-5a42-4581-9be6-f98eb2d1199e 
[INFO ] 2024-10-26 06:03:18.815 - [任务 14][mysql184] - PDK connector node released: HazelcastTargetPdkDataNode-f66e37b8-5a42-4581-9be6-f98eb2d1199e 
[INFO ] 2024-10-26 06:03:18.815 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] schema data cleaned 
[INFO ] 2024-10-26 06:03:18.816 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] monitor closed 
[INFO ] 2024-10-26 06:03:19.025 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] close complete, cost 82 ms 
[INFO ] 2024-10-26 06:03:21.795 - [任务 14] - Task [任务 14] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-26 06:03:21.796 - [任务 14] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-26 06:03:21.973 - [任务 14] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@46f67eed 
[INFO ] 2024-10-26 06:03:21.973 - [任务 14] - Stop task milestones: 671c157ef71e5e013cc2372b(任务 14)  
[INFO ] 2024-10-26 06:03:21.990 - [任务 14] - Stopped task aspect(s) 
[INFO ] 2024-10-26 06:03:21.990 - [任务 14] - Snapshot order controller have been removed 
[INFO ] 2024-10-26 06:03:22.035 - [任务 14] - Remove memory task client succeed, task: 任务 14[671c157ef71e5e013cc2372b] 
[INFO ] 2024-10-26 06:03:22.035 - [任务 14] - Destroy memory task client cache succeed, task: 任务 14[671c157ef71e5e013cc2372b] 
[INFO ] 2024-10-26 06:12:22.805 - [任务 14] - Task initialization... 
[INFO ] 2024-10-26 06:12:22.808 - [任务 14] - Start task milestones: 671c157ef71e5e013cc2372b(任务 14) 
[INFO ] 2024-10-26 06:12:25.394 - [任务 14] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-26 06:12:25.596 - [任务 14] - The engine receives 任务 14 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-26 06:12:25.854 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] start preload schema,table counts: 73 
[INFO ] 2024-10-26 06:12:25.855 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] start preload schema,table counts: 73 
[INFO ] 2024-10-26 06:12:25.855 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] preload schema finished, cost 1 ms 
[INFO ] 2024-10-26 06:12:25.855 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] preload schema finished, cost 1 ms 
[INFO ] 2024-10-26 06:12:26.782 - [任务 14][MysqlDroagon] - Source node "MysqlDroagon" read batch size: 100 
[INFO ] 2024-10-26 06:12:26.785 - [任务 14][MysqlDroagon] - Source node "MysqlDroagon" event queue capacity: 200 
[INFO ] 2024-10-26 06:12:26.785 - [任务 14][MysqlDroagon] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-26 06:12:26.795 - [任务 14][MysqlDroagon] - batch offset found: {},stream offset not found. 
[INFO ] 2024-10-26 06:12:26.796 - [任务 14][MysqlDroagon] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-26 06:12:27.039 - [任务 14][MysqlDroagon] - Initial sync started 
[INFO ] 2024-10-26 06:12:27.063 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_order, offset: null 
[INFO ] 2024-10-26 06:12:27.064 - [任务 14][MysqlDroagon] - Table eb_store_order is going to be initial synced 
[INFO ] 2024-10-26 06:12:27.109 - [任务 14][MysqlDroagon] - Table [eb_store_order] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:27.110 - [任务 14][MysqlDroagon] - Query table 'eb_store_order' counts: 0 
[INFO ] 2024-10-26 06:12:27.111 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_attr_value, offset: null 
[INFO ] 2024-10-26 06:12:27.126 - [任务 14][MysqlDroagon] - Table eb_store_product_attr_value is going to be initial synced 
[INFO ] 2024-10-26 06:12:27.128 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_attr_value' counts: 38 
[INFO ] 2024-10-26 06:12:27.164 - [任务 14][MysqlDroagon] - Table [eb_store_product_attr_value] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:27.167 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_wechat_callback, offset: null 
[INFO ] 2024-10-26 06:12:27.167 - [任务 14][MysqlDroagon] - Table eb_wechat_callback is going to be initial synced 
[INFO ] 2024-10-26 06:12:27.178 - [任务 14][MysqlDroagon] - Table [eb_wechat_callback] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:27.181 - [任务 14][MysqlDroagon] - Query table 'eb_wechat_callback' counts: 0 
[INFO ] 2024-10-26 06:12:27.182 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_shipping_templates_free, offset: null 
[INFO ] 2024-10-26 06:12:27.182 - [任务 14][MysqlDroagon] - Table eb_shipping_templates_free is going to be initial synced 
[INFO ] 2024-10-26 06:12:27.208 - [任务 14][MysqlDroagon] - Query table 'eb_shipping_templates_free' counts: 32 
[INFO ] 2024-10-26 06:12:27.208 - [任务 14][MysqlDroagon] - Table [eb_shipping_templates_free] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:27.209 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_notification, offset: null 
[INFO ] 2024-10-26 06:12:27.210 - [任务 14][MysqlDroagon] - Table eb_system_notification is going to be initial synced 
[INFO ] 2024-10-26 06:12:27.233 - [任务 14][MysqlDroagon] - Query table 'eb_system_notification' counts: 12 
[INFO ] 2024-10-26 06:12:27.236 - [任务 14][MysqlDroagon] - Table [eb_system_notification] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:27.248 - [任务 14][mysql184] - Node(mysql184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-26 06:12:27.253 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_heal_user, offset: null 
[INFO ] 2024-10-26 06:12:27.254 - [任务 14][mysql184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-26 06:12:27.254 - [任务 14][MysqlDroagon] - Table eb_store_heal_user is going to be initial synced 
[INFO ] 2024-10-26 06:12:27.270 - [任务 14][MysqlDroagon] - Query table 'eb_store_heal_user' counts: 11 
[INFO ] 2024-10-26 06:12:27.271 - [任务 14][MysqlDroagon] - Table [eb_store_heal_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:27.271 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_level, offset: null 
[INFO ] 2024-10-26 06:12:27.279 - [任务 14][MysqlDroagon] - Table eb_user_level is going to be initial synced 
[INFO ] 2024-10-26 06:12:27.283 - [任务 14][MysqlDroagon] - Query table 'eb_user_level' counts: 1 
[INFO ] 2024-10-26 06:12:27.293 - [任务 14][MysqlDroagon] - Table [eb_user_level] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:27.295 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_token, offset: null 
[INFO ] 2024-10-26 06:12:27.295 - [任务 14][MysqlDroagon] - Table eb_user_token is going to be initial synced 
[INFO ] 2024-10-26 06:12:27.303 - [任务 14][MysqlDroagon] - Query table 'eb_user_token' counts: 3 
[INFO ] 2024-10-26 06:12:27.304 - [任务 14][MysqlDroagon] - Table [eb_user_token] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:27.317 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_group, offset: null 
[INFO ] 2024-10-26 06:12:27.318 - [任务 14][MysqlDroagon] - Table eb_system_group is going to be initial synced 
[INFO ] 2024-10-26 06:12:27.327 - [任务 14][MysqlDroagon] - Query table 'eb_system_group' counts: 21 
[INFO ] 2024-10-26 06:12:27.327 - [任务 14][MysqlDroagon] - Table [eb_system_group] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:27.330 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_config, offset: null 
[INFO ] 2024-10-26 06:12:27.331 - [任务 14][MysqlDroagon] - Table eb_system_config is going to be initial synced 
[INFO ] 2024-10-26 06:12:27.376 - [任务 14][MysqlDroagon] - Query table 'eb_system_config' counts: 177 
[INFO ] 2024-10-26 06:12:27.377 - [任务 14][MysqlDroagon] - Table [eb_system_config] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:27.380 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_sign, offset: null 
[INFO ] 2024-10-26 06:12:27.380 - [任务 14][MysqlDroagon] - Table eb_user_sign is going to be initial synced 
[INFO ] 2024-10-26 06:12:27.392 - [任务 14][MysqlDroagon] - Query table 'eb_user_sign' counts: 1 
[INFO ] 2024-10-26 06:12:27.393 - [任务 14][MysqlDroagon] - Table [eb_user_sign] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:27.395 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_cate, offset: null 
[INFO ] 2024-10-26 06:12:27.395 - [任务 14][MysqlDroagon] - Table eb_store_product_cate is going to be initial synced 
[INFO ] 2024-10-26 06:12:27.405 - [任务 14][MysqlDroagon] - Table [eb_store_product_cate] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:27.405 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_cate' counts: 0 
[INFO ] 2024-10-26 06:12:27.408 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_experience_record, offset: null 
[INFO ] 2024-10-26 06:12:27.408 - [任务 14][MysqlDroagon] - Table eb_user_experience_record is going to be initial synced 
[INFO ] 2024-10-26 06:12:27.416 - [任务 14][MysqlDroagon] - Query table 'eb_user_experience_record' counts: 1 
[INFO ] 2024-10-26 06:12:27.416 - [任务 14][MysqlDroagon] - Table [eb_user_experience_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:27.422 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_brand_story, offset: null 
[INFO ] 2024-10-26 06:12:27.422 - [任务 14][MysqlDroagon] - Table eb_store_brand_story is going to be initial synced 
[INFO ] 2024-10-26 06:12:27.429 - [任务 14][MysqlDroagon] - Query table 'eb_store_brand_story' counts: 1 
[INFO ] 2024-10-26 06:12:27.429 - [任务 14][MysqlDroagon] - Table [eb_store_brand_story] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:27.429 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_heal, offset: null 
[INFO ] 2024-10-26 06:12:27.431 - [任务 14][MysqlDroagon] - Table eb_store_heal is going to be initial synced 
[INFO ] 2024-10-26 06:12:27.440 - [任务 14][MysqlDroagon] - Query table 'eb_store_heal' counts: 5 
[INFO ] 2024-10-26 06:12:27.441 - [任务 14][MysqlDroagon] - Table [eb_store_heal] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:27.442 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_description, offset: null 
[INFO ] 2024-10-26 06:12:27.442 - [任务 14][MysqlDroagon] - Table eb_store_product_description is going to be initial synced 
[INFO ] 2024-10-26 06:12:27.648 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_description' counts: 18 
[INFO ] 2024-10-26 06:12:46.355 - [任务 14][MysqlDroagon] - Table [eb_store_product_description] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:46.359 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_reply, offset: null 
[INFO ] 2024-10-26 06:12:46.360 - [任务 14][MysqlDroagon] - Table eb_store_product_reply is going to be initial synced 
[INFO ] 2024-10-26 06:12:46.379 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_reply' counts: 1 
[INFO ] 2024-10-26 06:12:46.380 - [任务 14][MysqlDroagon] - Table [eb_store_product_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:46.382 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_brokerage_record, offset: null 
[INFO ] 2024-10-26 06:12:46.382 - [任务 14][MysqlDroagon] - Table eb_user_brokerage_record is going to be initial synced 
[INFO ] 2024-10-26 06:12:46.393 - [任务 14][MysqlDroagon] - Table [eb_user_brokerage_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:46.393 - [任务 14][MysqlDroagon] - Query table 'eb_user_brokerage_record' counts: 0 
[INFO ] 2024-10-26 06:12:46.393 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_attachment, offset: null 
[INFO ] 2024-10-26 06:12:46.393 - [任务 14][MysqlDroagon] - Table eb_system_attachment is going to be initial synced 
[INFO ] 2024-10-26 06:12:46.414 - [任务 14][MysqlDroagon] - Query table 'eb_system_attachment' counts: 253 
[INFO ] 2024-10-26 06:12:46.414 - [任务 14][MysqlDroagon] - Table [eb_system_attachment] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:46.414 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_store_staff, offset: null 
[INFO ] 2024-10-26 06:12:46.433 - [任务 14][MysqlDroagon] - Table eb_system_store_staff is going to be initial synced 
[INFO ] 2024-10-26 06:12:46.435 - [任务 14][MysqlDroagon] - Table [eb_system_store_staff] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:46.435 - [任务 14][MysqlDroagon] - Query table 'eb_system_store_staff' counts: 0 
[INFO ] 2024-10-26 06:12:46.437 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_wechat_reply, offset: null 
[INFO ] 2024-10-26 06:12:46.438 - [任务 14][MysqlDroagon] - Table eb_wechat_reply is going to be initial synced 
[INFO ] 2024-10-26 06:12:46.445 - [任务 14][MysqlDroagon] - Query table 'eb_wechat_reply' counts: 1 
[INFO ] 2024-10-26 06:12:46.446 - [任务 14][MysqlDroagon] - Table [eb_wechat_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:46.447 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_bargain_user_help, offset: null 
[INFO ] 2024-10-26 06:12:46.447 - [任务 14][MysqlDroagon] - Table eb_store_bargain_user_help is going to be initial synced 
[INFO ] 2024-10-26 06:12:46.453 - [任务 14][MysqlDroagon] - Table [eb_store_bargain_user_help] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:46.454 - [任务 14][MysqlDroagon] - Query table 'eb_store_bargain_user_help' counts: 0 
[INFO ] 2024-10-26 06:12:46.455 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_seckill, offset: null 
[INFO ] 2024-10-26 06:12:46.455 - [任务 14][MysqlDroagon] - Table eb_store_seckill is going to be initial synced 
[INFO ] 2024-10-26 06:12:46.467 - [任务 14][MysqlDroagon] - Query table 'eb_store_seckill' counts: 4 
[INFO ] 2024-10-26 06:12:46.468 - [任务 14][MysqlDroagon] - Table [eb_store_seckill] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:46.468 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_attr_result, offset: null 
[INFO ] 2024-10-26 06:12:46.468 - [任务 14][MysqlDroagon] - Table eb_store_product_attr_result is going to be initial synced 
[INFO ] 2024-10-26 06:12:46.478 - [任务 14][MysqlDroagon] - Table [eb_store_product_attr_result] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:46.478 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_attr_result' counts: 0 
[INFO ] 2024-10-26 06:12:46.480 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_shipping_templates, offset: null 
[INFO ] 2024-10-26 06:12:46.480 - [任务 14][MysqlDroagon] - Table eb_shipping_templates is going to be initial synced 
[INFO ] 2024-10-26 06:12:46.485 - [任务 14][MysqlDroagon] - Query table 'eb_shipping_templates' counts: 1 
[INFO ] 2024-10-26 06:12:46.486 - [任务 14][MysqlDroagon] - Table [eb_shipping_templates] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:46.486 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_seckill_manger, offset: null 
[INFO ] 2024-10-26 06:12:46.486 - [任务 14][MysqlDroagon] - Table eb_store_seckill_manger is going to be initial synced 
[INFO ] 2024-10-26 06:12:46.493 - [任务 14][MysqlDroagon] - Query table 'eb_store_seckill_manger' counts: 3 
[INFO ] 2024-10-26 06:12:46.493 - [任务 14][MysqlDroagon] - Table [eb_store_seckill_manger] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:46.494 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user, offset: null 
[INFO ] 2024-10-26 06:12:46.494 - [任务 14][MysqlDroagon] - Table eb_user is going to be initial synced 
[INFO ] 2024-10-26 06:12:46.502 - [任务 14][MysqlDroagon] - Query table 'eb_user' counts: 4 
[INFO ] 2024-10-26 06:12:46.502 - [任务 14][MysqlDroagon] - Table [eb_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:46.507 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_order_status, offset: null 
[INFO ] 2024-10-26 06:12:46.507 - [任务 14][MysqlDroagon] - Table eb_store_order_status is going to be initial synced 
[INFO ] 2024-10-26 06:12:46.511 - [任务 14][MysqlDroagon] - Query table 'eb_store_order_status' counts: 0 
[INFO ] 2024-10-26 06:12:46.511 - [任务 14][MysqlDroagon] - Table [eb_store_order_status] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:46.516 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_form_temp, offset: null 
[INFO ] 2024-10-26 06:12:46.517 - [任务 14][MysqlDroagon] - Table eb_system_form_temp is going to be initial synced 
[INFO ] 2024-10-26 06:12:46.517 - [任务 14][MysqlDroagon] - Query table 'eb_system_form_temp' counts: 63 
[INFO ] 2024-10-26 06:12:46.718 - [任务 14][mysql184] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'expired_time' cannot be null
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@76b248f3: {"after":{"uid":4,"update_time":"2024-09-28 21:51:40.000000","create_time":"2024-09-28 21:51:40.000000","level_id":1,"grade":1,"is_del":0,"discount":100,"id":1,"mark":"尊敬的用户 微信用户, 在2024-09-28 21:51:40您升级为为普通会员会员","status":1,"remind":0},"containsIllegalDate":false,"tableId":"eb_user_level","time":1729894347283,"type":300}, nodeIds=[4ac5ce09-692f-447a-82f6-00938409fa12], sourceTime=1729894346787, sourceSerialNo=null} 
[ERROR] 2024-10-26 06:12:46.743 - [任务 14][mysql184] - java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'expired_time' cannot be null
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@76b248f3: {"after":{"uid":4,"update_time":"2024-09-28 21:51:40.000000","create_time":"2024-09-28 21:51:40.000000","level_id":1,"grade":1,"is_del":0,"discount":100,"id":1,"mark":"尊敬的用户 微信用户, 在2024-09-28 21:51:40您升级为为普通会员会员","status":1,"remind":0},"containsIllegalDate":false,"tableId":"eb_user_level","time":1729894347283,"type":300}, nodeIds=[4ac5ce09-692f-447a-82f6-00938409fa12], sourceTime=1729894346787, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'expired_time' cannot be null
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@76b248f3: {"after":{"uid":4,"update_time":"2024-09-28 21:51:40.000000","create_time":"2024-09-28 21:51:40.000000","level_id":1,"grade":1,"is_del":0,"discount":100,"id":1,"mark":"尊敬的用户 微信用户, 在2024-09-28 21:51:40您升级为为普通会员会员","status":1,"remind":0},"containsIllegalDate":false,"tableId":"eb_user_level","time":1729894347283,"type":300}, nodeIds=[4ac5ce09-692f-447a-82f6-00938409fa12], sourceTime=1729894346787, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'expired_time' cannot be null
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'expired_time' cannot be null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:572)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:528)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:500)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:546)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'expired_time' cannot be null
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:816)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$26(HazelcastTargetPdkDataNode.java:516)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:516)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:624)
	... 11 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'expired_time' cannot be null
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectViolateNull(MysqlExceptionCollector.java:147)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:119)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:455)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$56(HazelcastTargetPdkDataNode.java:870)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$57(HazelcastTargetPdkDataNode.java:822)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 18 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'expired_time' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:93)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:108)
	... 29 more

[INFO ] 2024-10-26 06:12:46.743 - [任务 14][mysql184] - Job suspend in error handle 
[INFO ] 2024-10-26 06:12:47.335 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] running status set to false 
[INFO ] 2024-10-26 06:12:47.348 - [任务 14][MysqlDroagon] - Table [eb_system_form_temp] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:12:47.356 - [任务 14][MysqlDroagon] - Initial sync completed 
[INFO ] 2024-10-26 06:12:47.370 - [任务 14][MysqlDroagon] - PDK connector node stopped: HazelcastSourcePdkDataNode-4ac5ce09-692f-447a-82f6-00938409fa12 
[INFO ] 2024-10-26 06:12:47.370 - [任务 14][MysqlDroagon] - PDK connector node released: HazelcastSourcePdkDataNode-4ac5ce09-692f-447a-82f6-00938409fa12 
[INFO ] 2024-10-26 06:12:47.374 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] schema data cleaned 
[INFO ] 2024-10-26 06:12:47.374 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] monitor closed 
[INFO ] 2024-10-26 06:12:47.388 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] close complete, cost 43 ms 
[INFO ] 2024-10-26 06:12:47.388 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] running status set to false 
[INFO ] 2024-10-26 06:12:47.440 - [任务 14][mysql184] - PDK connector node stopped: HazelcastTargetPdkDataNode-f66e37b8-5a42-4581-9be6-f98eb2d1199e 
[INFO ] 2024-10-26 06:12:47.440 - [任务 14][mysql184] - PDK connector node released: HazelcastTargetPdkDataNode-f66e37b8-5a42-4581-9be6-f98eb2d1199e 
[INFO ] 2024-10-26 06:12:47.440 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] schema data cleaned 
[INFO ] 2024-10-26 06:12:47.440 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] monitor closed 
[INFO ] 2024-10-26 06:12:47.442 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] close complete, cost 55 ms 
[INFO ] 2024-10-26 06:12:51.667 - [任务 14] - Task [任务 14] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-26 06:12:51.727 - [任务 14] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-26 06:12:51.742 - [任务 14] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@745d267d 
[INFO ] 2024-10-26 06:12:51.743 - [任务 14] - Stop task milestones: 671c157ef71e5e013cc2372b(任务 14)  
[INFO ] 2024-10-26 06:12:51.878 - [任务 14] - Stopped task aspect(s) 
[INFO ] 2024-10-26 06:12:51.878 - [任务 14] - Snapshot order controller have been removed 
[INFO ] 2024-10-26 06:12:51.913 - [任务 14] - Remove memory task client succeed, task: 任务 14[671c157ef71e5e013cc2372b] 
[INFO ] 2024-10-26 06:12:51.914 - [任务 14] - Destroy memory task client cache succeed, task: 任务 14[671c157ef71e5e013cc2372b] 
[INFO ] 2024-10-26 06:13:57.281 - [任务 14] - Task initialization... 
[INFO ] 2024-10-26 06:13:57.282 - [任务 14] - Start task milestones: 671c157ef71e5e013cc2372b(任务 14) 
[INFO ] 2024-10-26 06:13:58.119 - [任务 14] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-26 06:13:58.330 - [任务 14] - The engine receives 任务 14 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-26 06:13:58.359 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] start preload schema,table counts: 73 
[INFO ] 2024-10-26 06:13:58.361 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] start preload schema,table counts: 73 
[INFO ] 2024-10-26 06:13:58.361 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] preload schema finished, cost 1 ms 
[INFO ] 2024-10-26 06:13:58.361 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] preload schema finished, cost 1 ms 
[INFO ] 2024-10-26 06:13:59.320 - [任务 14][MysqlDroagon] - Source node "MysqlDroagon" read batch size: 100 
[INFO ] 2024-10-26 06:13:59.321 - [任务 14][MysqlDroagon] - Source node "MysqlDroagon" event queue capacity: 200 
[INFO ] 2024-10-26 06:13:59.321 - [任务 14][MysqlDroagon] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-26 06:13:59.321 - [任务 14][MysqlDroagon] - batch offset found: {},stream offset not found. 
[INFO ] 2024-10-26 06:13:59.535 - [任务 14][MysqlDroagon] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-26 06:13:59.582 - [任务 14][MysqlDroagon] - Initial sync started 
[INFO ] 2024-10-26 06:13:59.582 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_order, offset: null 
[INFO ] 2024-10-26 06:13:59.590 - [任务 14][MysqlDroagon] - Table eb_store_order is going to be initial synced 
[INFO ] 2024-10-26 06:13:59.632 - [任务 14][MysqlDroagon] - Table [eb_store_order] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:13:59.633 - [任务 14][MysqlDroagon] - Query table 'eb_store_order' counts: 0 
[INFO ] 2024-10-26 06:13:59.633 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_attr_value, offset: null 
[INFO ] 2024-10-26 06:13:59.633 - [任务 14][MysqlDroagon] - Table eb_store_product_attr_value is going to be initial synced 
[INFO ] 2024-10-26 06:13:59.648 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_attr_value' counts: 38 
[INFO ] 2024-10-26 06:13:59.656 - [任务 14][MysqlDroagon] - Table [eb_store_product_attr_value] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:13:59.656 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_wechat_callback, offset: null 
[INFO ] 2024-10-26 06:13:59.670 - [任务 14][MysqlDroagon] - Table eb_wechat_callback is going to be initial synced 
[INFO ] 2024-10-26 06:13:59.670 - [任务 14][MysqlDroagon] - Table [eb_wechat_callback] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:13:59.670 - [任务 14][MysqlDroagon] - Query table 'eb_wechat_callback' counts: 0 
[INFO ] 2024-10-26 06:13:59.671 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_shipping_templates_free, offset: null 
[INFO ] 2024-10-26 06:13:59.676 - [任务 14][MysqlDroagon] - Table eb_shipping_templates_free is going to be initial synced 
[INFO ] 2024-10-26 06:13:59.676 - [任务 14][MysqlDroagon] - Query table 'eb_shipping_templates_free' counts: 32 
[INFO ] 2024-10-26 06:13:59.681 - [任务 14][MysqlDroagon] - Table [eb_shipping_templates_free] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:13:59.681 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_notification, offset: null 
[INFO ] 2024-10-26 06:13:59.688 - [任务 14][MysqlDroagon] - Table eb_system_notification is going to be initial synced 
[INFO ] 2024-10-26 06:13:59.688 - [任务 14][MysqlDroagon] - Query table 'eb_system_notification' counts: 12 
[INFO ] 2024-10-26 06:13:59.692 - [任务 14][MysqlDroagon] - Table [eb_system_notification] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:13:59.692 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_heal_user, offset: null 
[INFO ] 2024-10-26 06:13:59.698 - [任务 14][MysqlDroagon] - Table eb_store_heal_user is going to be initial synced 
[INFO ] 2024-10-26 06:13:59.701 - [任务 14][MysqlDroagon] - Query table 'eb_store_heal_user' counts: 11 
[INFO ] 2024-10-26 06:13:59.701 - [任务 14][MysqlDroagon] - Table [eb_store_heal_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:13:59.704 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_level, offset: null 
[INFO ] 2024-10-26 06:13:59.705 - [任务 14][MysqlDroagon] - Table eb_user_level is going to be initial synced 
[INFO ] 2024-10-26 06:13:59.705 - [任务 14][MysqlDroagon] - Query table 'eb_user_level' counts: 1 
[INFO ] 2024-10-26 06:13:59.706 - [任务 14][MysqlDroagon] - Table [eb_user_level] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:13:59.706 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_token, offset: null 
[INFO ] 2024-10-26 06:13:59.718 - [任务 14][MysqlDroagon] - Table eb_user_token is going to be initial synced 
[INFO ] 2024-10-26 06:13:59.718 - [任务 14][MysqlDroagon] - Table [eb_user_token] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:13:59.718 - [任务 14][MysqlDroagon] - Query table 'eb_user_token' counts: 3 
[INFO ] 2024-10-26 06:13:59.718 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_group, offset: null 
[INFO ] 2024-10-26 06:13:59.718 - [任务 14][MysqlDroagon] - Table eb_system_group is going to be initial synced 
[INFO ] 2024-10-26 06:13:59.735 - [任务 14][MysqlDroagon] - Query table 'eb_system_group' counts: 21 
[INFO ] 2024-10-26 06:13:59.735 - [任务 14][MysqlDroagon] - Table [eb_system_group] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:13:59.736 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_config, offset: null 
[INFO ] 2024-10-26 06:13:59.745 - [任务 14][MysqlDroagon] - Table eb_system_config is going to be initial synced 
[INFO ] 2024-10-26 06:13:59.746 - [任务 14][MysqlDroagon] - Query table 'eb_system_config' counts: 177 
[INFO ] 2024-10-26 06:13:59.758 - [任务 14][MysqlDroagon] - Table [eb_system_config] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:13:59.759 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_sign, offset: null 
[INFO ] 2024-10-26 06:13:59.760 - [任务 14][MysqlDroagon] - Table eb_user_sign is going to be initial synced 
[INFO ] 2024-10-26 06:13:59.765 - [任务 14][MysqlDroagon] - Query table 'eb_user_sign' counts: 1 
[INFO ] 2024-10-26 06:13:59.765 - [任务 14][MysqlDroagon] - Table [eb_user_sign] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:13:59.766 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_cate, offset: null 
[INFO ] 2024-10-26 06:13:59.772 - [任务 14][MysqlDroagon] - Table eb_store_product_cate is going to be initial synced 
[INFO ] 2024-10-26 06:13:59.772 - [任务 14][MysqlDroagon] - Table [eb_store_product_cate] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:13:59.772 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_cate' counts: 0 
[INFO ] 2024-10-26 06:13:59.772 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_experience_record, offset: null 
[INFO ] 2024-10-26 06:13:59.778 - [任务 14][MysqlDroagon] - Table eb_user_experience_record is going to be initial synced 
[INFO ] 2024-10-26 06:13:59.779 - [任务 14][MysqlDroagon] - Query table 'eb_user_experience_record' counts: 1 
[INFO ] 2024-10-26 06:13:59.779 - [任务 14][MysqlDroagon] - Table [eb_user_experience_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:13:59.779 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_brand_story, offset: null 
[INFO ] 2024-10-26 06:13:59.779 - [任务 14][MysqlDroagon] - Table eb_store_brand_story is going to be initial synced 
[INFO ] 2024-10-26 06:13:59.786 - [任务 14][MysqlDroagon] - Query table 'eb_store_brand_story' counts: 1 
[INFO ] 2024-10-26 06:13:59.786 - [任务 14][MysqlDroagon] - Table [eb_store_brand_story] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:13:59.787 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_heal, offset: null 
[INFO ] 2024-10-26 06:13:59.787 - [任务 14][MysqlDroagon] - Table eb_store_heal is going to be initial synced 
[INFO ] 2024-10-26 06:13:59.793 - [任务 14][MysqlDroagon] - Query table 'eb_store_heal' counts: 5 
[INFO ] 2024-10-26 06:13:59.795 - [任务 14][MysqlDroagon] - Table [eb_store_heal] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:13:59.795 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_description, offset: null 
[INFO ] 2024-10-26 06:13:59.795 - [任务 14][MysqlDroagon] - Table eb_store_product_description is going to be initial synced 
[INFO ] 2024-10-26 06:13:59.804 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_description' counts: 18 
[INFO ] 2024-10-26 06:13:59.805 - [任务 14][mysql184] - Node(mysql184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-26 06:13:59.805 - [任务 14][mysql184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-26 06:13:59.898 - [任务 14][mysql184] - Table "liubai.eb_store_order" exists, skip auto create table 
[INFO ] 2024-10-26 06:13:59.899 - [任务 14][mysql184] - The table eb_store_order has already exist. 
[INFO ] 2024-10-26 06:14:00.050 - [任务 14][mysql184] - Table "liubai.eb_store_product_attr_value" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:00.050 - [任务 14][mysql184] - The table eb_store_product_attr_value has already exist. 
[INFO ] 2024-10-26 06:14:00.151 - [任务 14][mysql184] - Table "liubai.eb_wechat_callback" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:00.151 - [任务 14][mysql184] - The table eb_wechat_callback has already exist. 
[INFO ] 2024-10-26 06:14:00.256 - [任务 14][mysql184] - Table "liubai.eb_shipping_templates_free" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:00.256 - [任务 14][mysql184] - The table eb_shipping_templates_free has already exist. 
[INFO ] 2024-10-26 06:14:00.366 - [任务 14][mysql184] - Table "liubai.eb_system_notification" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:00.366 - [任务 14][mysql184] - The table eb_system_notification has already exist. 
[INFO ] 2024-10-26 06:14:00.463 - [任务 14][mysql184] - Table "liubai.eb_store_heal_user" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:00.463 - [任务 14][mysql184] - The table eb_store_heal_user has already exist. 
[INFO ] 2024-10-26 06:14:00.567 - [任务 14][mysql184] - Table "liubai.eb_user_level" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:00.567 - [任务 14][mysql184] - The table eb_user_level has already exist. 
[INFO ] 2024-10-26 06:14:00.688 - [任务 14][mysql184] - Table "liubai.eb_user_token" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:00.688 - [任务 14][mysql184] - The table eb_user_token has already exist. 
[INFO ] 2024-10-26 06:14:00.836 - [任务 14][mysql184] - Table "liubai.eb_system_group" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:00.837 - [任务 14][mysql184] - The table eb_system_group has already exist. 
[INFO ] 2024-10-26 06:14:00.972 - [任务 14][mysql184] - Table "liubai.eb_system_config" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:00.972 - [任务 14][mysql184] - The table eb_system_config has already exist. 
[INFO ] 2024-10-26 06:14:01.097 - [任务 14][mysql184] - Table "liubai.eb_user_sign" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:01.102 - [任务 14][mysql184] - The table eb_user_sign has already exist. 
[INFO ] 2024-10-26 06:14:01.213 - [任务 14][mysql184] - Table "liubai.eb_store_product_cate" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:01.213 - [任务 14][mysql184] - The table eb_store_product_cate has already exist. 
[INFO ] 2024-10-26 06:14:01.313 - [任务 14][mysql184] - Table "liubai.eb_user_experience_record" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:01.313 - [任务 14][mysql184] - The table eb_user_experience_record has already exist. 
[INFO ] 2024-10-26 06:14:01.417 - [任务 14][mysql184] - Table "liubai.eb_store_brand_story" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:01.417 - [任务 14][mysql184] - The table eb_store_brand_story has already exist. 
[INFO ] 2024-10-26 06:14:01.540 - [任务 14][mysql184] - Table "liubai.eb_store_heal" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:01.664 - [任务 14][mysql184] - The table eb_store_heal has already exist. 
[INFO ] 2024-10-26 06:14:01.665 - [任务 14][mysql184] - Table "liubai.eb_store_product_description" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:01.665 - [任务 14][mysql184] - The table eb_store_product_description has already exist. 
[INFO ] 2024-10-26 06:14:01.792 - [任务 14][mysql184] - Table "liubai.eb_store_product_reply" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:01.885 - [任务 14][mysql184] - The table eb_store_product_reply has already exist. 
[INFO ] 2024-10-26 06:14:01.886 - [任务 14][mysql184] - Table "liubai.eb_user_brokerage_record" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:01.979 - [任务 14][mysql184] - The table eb_user_brokerage_record has already exist. 
[INFO ] 2024-10-26 06:14:01.980 - [任务 14][mysql184] - Table "liubai.eb_system_attachment" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:02.085 - [任务 14][mysql184] - The table eb_system_attachment has already exist. 
[INFO ] 2024-10-26 06:14:02.085 - [任务 14][mysql184] - Table "liubai.eb_system_store_staff" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:02.085 - [任务 14][mysql184] - The table eb_system_store_staff has already exist. 
[INFO ] 2024-10-26 06:14:02.200 - [任务 14][mysql184] - Table "liubai.eb_wechat_reply" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:02.200 - [任务 14][mysql184] - The table eb_wechat_reply has already exist. 
[INFO ] 2024-10-26 06:14:02.303 - [任务 14][mysql184] - Table "liubai.eb_store_bargain_user_help" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:02.303 - [任务 14][mysql184] - The table eb_store_bargain_user_help has already exist. 
[INFO ] 2024-10-26 06:14:02.404 - [任务 14][mysql184] - Table "liubai.eb_store_seckill" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:02.404 - [任务 14][mysql184] - The table eb_store_seckill has already exist. 
[INFO ] 2024-10-26 06:14:02.517 - [任务 14][mysql184] - Table "liubai.eb_store_product_attr_result" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:02.519 - [任务 14][mysql184] - The table eb_store_product_attr_result has already exist. 
[INFO ] 2024-10-26 06:14:02.624 - [任务 14][mysql184] - Table "liubai.eb_shipping_templates" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:02.624 - [任务 14][mysql184] - The table eb_shipping_templates has already exist. 
[INFO ] 2024-10-26 06:14:02.739 - [任务 14][mysql184] - Table "liubai.eb_store_seckill_manger" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:02.739 - [任务 14][mysql184] - The table eb_store_seckill_manger has already exist. 
[INFO ] 2024-10-26 06:14:02.842 - [任务 14][mysql184] - Table "liubai.eb_user" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:02.842 - [任务 14][mysql184] - The table eb_user has already exist. 
[INFO ] 2024-10-26 06:14:02.965 - [任务 14][mysql184] - Table "liubai.eb_store_order_status" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:02.965 - [任务 14][mysql184] - The table eb_store_order_status has already exist. 
[INFO ] 2024-10-26 06:14:03.078 - [任务 14][mysql184] - Table "liubai.eb_system_form_temp" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:03.078 - [任务 14][mysql184] - The table eb_system_form_temp has already exist. 
[INFO ] 2024-10-26 06:14:03.201 - [任务 14][mysql184] - Table "liubai.eb_store_experience_reply" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:03.201 - [任务 14][mysql184] - The table eb_store_experience_reply has already exist. 
[INFO ] 2024-10-26 06:14:03.331 - [任务 14][mysql184] - Table "liubai.eb_store_bargain_user" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:03.331 - [任务 14][mysql184] - The table eb_store_bargain_user has already exist. 
[INFO ] 2024-10-26 06:14:03.445 - [任务 14][mysql184] - Table "liubai.eb_system_store" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:03.445 - [任务 14][mysql184] - The table eb_system_store has already exist. 
[INFO ] 2024-10-26 06:14:03.572 - [任务 14][mysql184] - Table "liubai.eb_shipping_templates_region" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:03.701 - [任务 14][mysql184] - The table eb_shipping_templates_region has already exist. 
[INFO ] 2024-10-26 06:14:03.702 - [任务 14][mysql184] - Table "liubai.eb_user_recharge" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:03.702 - [任务 14][mysql184] - The table eb_user_recharge has already exist. 
[INFO ] 2024-10-26 06:14:03.831 - [任务 14][mysql184] - Table "liubai.eb_store_product_coupon" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:03.831 - [任务 14][mysql184] - The table eb_store_product_coupon has already exist. 
[INFO ] 2024-10-26 06:14:03.945 - [任务 14][mysql184] - Table "liubai.eb_wechat_pay_info" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:03.945 - [任务 14][mysql184] - The table eb_wechat_pay_info has already exist. 
[INFO ] 2024-10-26 06:14:04.082 - [任务 14][mysql184] - Table "liubai.eb_store_combination" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:04.082 - [任务 14][mysql184] - The table eb_store_combination has already exist. 
[INFO ] 2024-10-26 06:14:04.216 - [任务 14][mysql184] - Table "liubai.eb_store_product" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:04.216 - [任务 14][mysql184] - The table eb_store_product has already exist. 
[INFO ] 2024-10-26 06:14:04.536 - [任务 14][mysql184] - Table "liubai.eb_article" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:04.536 - [任务 14][mysql184] - The table eb_article has already exist. 
[INFO ] 2024-10-26 06:14:04.656 - [任务 14][mysql184] - Table "liubai.eb_user_bill" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:04.656 - [任务 14][mysql184] - The table eb_user_bill has already exist. 
[INFO ] 2024-10-26 06:14:04.765 - [任务 14][mysql184] - Table "liubai.eb_store_experience_relation" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:04.765 - [任务 14][mysql184] - The table eb_store_experience_relation has already exist. 
[INFO ] 2024-10-26 06:14:04.873 - [任务 14][mysql184] - Table "liubai.eb_wechat_exceptions" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:04.873 - [任务 14][mysql184] - The table eb_wechat_exceptions has already exist. 
[INFO ] 2024-10-26 06:14:04.988 - [任务 14][mysql184] - Table "liubai.eb_template_message" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:04.988 - [任务 14][mysql184] - The table eb_template_message has already exist. 
[INFO ] 2024-10-26 06:14:05.096 - [任务 14][mysql184] - Table "liubai.eb_user_integral_record" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:05.096 - [任务 14][mysql184] - The table eb_user_integral_record has already exist. 
[INFO ] 2024-10-26 06:14:05.237 - [任务 14][mysql184] - Table "liubai.eb_store_coupon" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:05.238 - [任务 14][mysql184] - The table eb_store_coupon has already exist. 
[INFO ] 2024-10-26 06:14:05.348 - [任务 14][mysql184] - Table "liubai.eb_user_visit_record" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:05.348 - [任务 14][mysql184] - The table eb_user_visit_record has already exist. 
[INFO ] 2024-10-26 06:14:05.446 - [任务 14][mysql184] - Table "liubai.eb_system_role_menu" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:05.446 - [任务 14][mysql184] - The table eb_system_role_menu has already exist. 
[INFO ] 2024-10-26 06:14:05.560 - [任务 14][mysql184] - Table "liubai.eb_store_product_log" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:05.560 - [任务 14][mysql184] - The table eb_store_product_log has already exist. 
[INFO ] 2024-10-26 06:14:05.672 - [任务 14][mysql184] - Table "liubai.eb_store_product_series" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:05.672 - [任务 14][mysql184] - The table eb_store_product_series has already exist. 
[INFO ] 2024-10-26 06:14:05.793 - [任务 14][mysql184] - Table "liubai.eb_store_product_attr" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:05.934 - [任务 14][mysql184] - The table eb_store_product_attr has already exist. 
[INFO ] 2024-10-26 06:14:05.935 - [任务 14][mysql184] - Table "liubai.eb_store_bargain" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:05.935 - [任务 14][mysql184] - The table eb_store_bargain has already exist. 
[INFO ] 2024-10-26 06:14:06.033 - [任务 14][mysql184] - Table "liubai.eb_store_experience_audio" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:06.134 - [任务 14][mysql184] - The table eb_store_experience_audio has already exist. 
[INFO ] 2024-10-26 06:14:06.134 - [任务 14][mysql184] - Table "liubai.eb_system_city" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:06.134 - [任务 14][mysql184] - The table eb_system_city has already exist. 
[INFO ] 2024-10-26 06:14:06.236 - [任务 14][mysql184] - Table "liubai.eb_sms_record" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:06.344 - [任务 14][mysql184] - The table eb_sms_record has already exist. 
[INFO ] 2024-10-26 06:14:06.345 - [任务 14][mysql184] - Table "liubai.eb_user_extract" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:06.452 - [任务 14][mysql184] - The table eb_user_extract has already exist. 
[INFO ] 2024-10-26 06:14:06.452 - [任务 14][mysql184] - Table "liubai.eb_user_group" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:06.579 - [任务 14][mysql184] - The table eb_user_group has already exist. 
[INFO ] 2024-10-26 06:14:06.579 - [任务 14][mysql184] - Table "liubai.eb_store_product_rule" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:06.579 - [任务 14][mysql184] - The table eb_store_product_rule has already exist. 
[INFO ] 2024-10-26 06:14:06.679 - [任务 14][mysql184] - Table "liubai.eb_store_cart" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:06.679 - [任务 14][mysql184] - The table eb_store_cart has already exist. 
[INFO ] 2024-10-26 06:14:06.785 - [任务 14][mysql184] - Table "liubai.eb_system_admin" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:06.791 - [任务 14][mysql184] - The table eb_system_admin has already exist. 
[INFO ] 2024-10-26 06:14:06.881 - [任务 14][mysql184] - Table "liubai.eb_system_menu" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:06.881 - [任务 14][mysql184] - The table eb_system_menu has already exist. 
[INFO ] 2024-10-26 06:14:06.975 - [任务 14][mysql184] - Table "liubai.eb_system_user_level" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:06.975 - [任务 14][mysql184] - The table eb_system_user_level has already exist. 
[INFO ] 2024-10-26 06:14:07.073 - [任务 14][mysql184] - Table "liubai.eb_category" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:07.074 - [任务 14][mysql184] - The table eb_category has already exist. 
[INFO ] 2024-10-26 06:14:07.199 - [任务 14][mysql184] - Table "liubai.eb_sms_template" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:07.200 - [任务 14][mysql184] - The table eb_sms_template has already exist. 
[INFO ] 2024-10-26 06:14:07.319 - [任务 14][mysql184] - Table "liubai.eb_user_tag" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:07.319 - [任务 14][mysql184] - The table eb_user_tag has already exist. 
[INFO ] 2024-10-26 06:14:07.429 - [任务 14][mysql184] - Table "liubai.eb_store_order_info" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:07.429 - [任务 14][mysql184] - The table eb_store_order_info has already exist. 
[INFO ] 2024-10-26 06:14:07.563 - [任务 14][mysql184] - Table "liubai.eb_store_coupon_user" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:07.564 - [任务 14][mysql184] - The table eb_store_coupon_user has already exist. 
[INFO ] 2024-10-26 06:14:07.678 - [任务 14][mysql184] - Table "liubai.eb_system_role" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:07.678 - [任务 14][mysql184] - The table eb_system_role has already exist. 
[INFO ] 2024-10-26 06:14:07.790 - [任务 14][mysql184] - Table "liubai.eb_store_experience_audio_history" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:07.790 - [任务 14][mysql184] - The table eb_store_experience_audio_history has already exist. 
[INFO ] 2024-10-26 06:14:07.901 - [任务 14][mysql184] - Table "liubai.eb_store_pink" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:07.901 - [任务 14][mysql184] - The table eb_store_pink has already exist. 
[INFO ] 2024-10-26 06:14:08.011 - [任务 14][mysql184] - Table "liubai.eb_express" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:08.011 - [任务 14][mysql184] - The table eb_express has already exist. 
[INFO ] 2024-10-26 06:14:08.131 - [任务 14][mysql184] - Table "liubai.eb_store_product_relation" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:08.257 - [任务 14][mysql184] - The table eb_store_product_relation has already exist. 
[INFO ] 2024-10-26 06:14:08.257 - [任务 14][mysql184] - Table "liubai.eb_system_group_data" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:08.257 - [任务 14][mysql184] - The table eb_system_group_data has already exist. 
[INFO ] 2024-10-26 06:14:08.377 - [任务 14][mysql184] - Table "liubai.eb_user_address" exists, skip auto create table 
[INFO ] 2024-10-26 06:14:08.440 - [任务 14][mysql184] - The table eb_user_address has already exist. 
[INFO ] 2024-10-26 06:14:08.440 - [任务 14][MysqlDroagon] - Table [eb_store_product_description] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:08.460 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_reply, offset: null 
[INFO ] 2024-10-26 06:14:08.460 - [任务 14][MysqlDroagon] - Table eb_store_product_reply is going to be initial synced 
[INFO ] 2024-10-26 06:14:08.486 - [任务 14][MysqlDroagon] - Table [eb_store_product_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:08.487 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_reply' counts: 1 
[INFO ] 2024-10-26 06:14:08.489 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_brokerage_record, offset: null 
[INFO ] 2024-10-26 06:14:08.489 - [任务 14][MysqlDroagon] - Table eb_user_brokerage_record is going to be initial synced 
[INFO ] 2024-10-26 06:14:08.503 - [任务 14][MysqlDroagon] - Table [eb_user_brokerage_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:08.503 - [任务 14][MysqlDroagon] - Query table 'eb_user_brokerage_record' counts: 0 
[INFO ] 2024-10-26 06:14:08.503 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_attachment, offset: null 
[INFO ] 2024-10-26 06:14:08.517 - [任务 14][MysqlDroagon] - Table eb_system_attachment is going to be initial synced 
[INFO ] 2024-10-26 06:14:08.520 - [任务 14][MysqlDroagon] - Query table 'eb_system_attachment' counts: 253 
[INFO ] 2024-10-26 06:14:08.531 - [任务 14][MysqlDroagon] - Table [eb_system_attachment] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:08.531 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_store_staff, offset: null 
[INFO ] 2024-10-26 06:14:08.531 - [任务 14][MysqlDroagon] - Table eb_system_store_staff is going to be initial synced 
[INFO ] 2024-10-26 06:14:08.545 - [任务 14][MysqlDroagon] - Table [eb_system_store_staff] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:08.547 - [任务 14][MysqlDroagon] - Query table 'eb_system_store_staff' counts: 0 
[INFO ] 2024-10-26 06:14:08.547 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_wechat_reply, offset: null 
[INFO ] 2024-10-26 06:14:08.562 - [任务 14][MysqlDroagon] - Table eb_wechat_reply is going to be initial synced 
[INFO ] 2024-10-26 06:14:08.562 - [任务 14][MysqlDroagon] - Query table 'eb_wechat_reply' counts: 1 
[INFO ] 2024-10-26 06:14:08.562 - [任务 14][MysqlDroagon] - Table [eb_wechat_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:08.563 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_bargain_user_help, offset: null 
[INFO ] 2024-10-26 06:14:08.563 - [任务 14][MysqlDroagon] - Table eb_store_bargain_user_help is going to be initial synced 
[INFO ] 2024-10-26 06:14:08.570 - [任务 14][MysqlDroagon] - Table [eb_store_bargain_user_help] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:08.570 - [任务 14][MysqlDroagon] - Query table 'eb_store_bargain_user_help' counts: 0 
[INFO ] 2024-10-26 06:14:08.570 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_seckill, offset: null 
[INFO ] 2024-10-26 06:14:08.571 - [任务 14][MysqlDroagon] - Table eb_store_seckill is going to be initial synced 
[INFO ] 2024-10-26 06:14:08.586 - [任务 14][MysqlDroagon] - Query table 'eb_store_seckill' counts: 4 
[INFO ] 2024-10-26 06:14:08.587 - [任务 14][MysqlDroagon] - Table [eb_store_seckill] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:08.587 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_attr_result, offset: null 
[INFO ] 2024-10-26 06:14:08.591 - [任务 14][MysqlDroagon] - Table eb_store_product_attr_result is going to be initial synced 
[INFO ] 2024-10-26 06:14:08.606 - [任务 14][MysqlDroagon] - Table [eb_store_product_attr_result] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:08.607 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_attr_result' counts: 0 
[INFO ] 2024-10-26 06:14:08.607 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_shipping_templates, offset: null 
[INFO ] 2024-10-26 06:14:08.607 - [任务 14][MysqlDroagon] - Table eb_shipping_templates is going to be initial synced 
[INFO ] 2024-10-26 06:14:08.623 - [任务 14][MysqlDroagon] - Query table 'eb_shipping_templates' counts: 1 
[INFO ] 2024-10-26 06:14:08.624 - [任务 14][MysqlDroagon] - Table [eb_shipping_templates] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:08.624 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_seckill_manger, offset: null 
[INFO ] 2024-10-26 06:14:08.645 - [任务 14][MysqlDroagon] - Table eb_store_seckill_manger is going to be initial synced 
[INFO ] 2024-10-26 06:14:08.645 - [任务 14][MysqlDroagon] - Query table 'eb_store_seckill_manger' counts: 3 
[INFO ] 2024-10-26 06:14:08.645 - [任务 14][MysqlDroagon] - Table [eb_store_seckill_manger] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:08.645 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user, offset: null 
[INFO ] 2024-10-26 06:14:08.646 - [任务 14][MysqlDroagon] - Table eb_user is going to be initial synced 
[INFO ] 2024-10-26 06:14:08.656 - [任务 14][MysqlDroagon] - Query table 'eb_user' counts: 4 
[INFO ] 2024-10-26 06:14:08.656 - [任务 14][MysqlDroagon] - Table [eb_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:08.656 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_order_status, offset: null 
[INFO ] 2024-10-26 06:14:08.656 - [任务 14][MysqlDroagon] - Table eb_store_order_status is going to be initial synced 
[INFO ] 2024-10-26 06:14:08.663 - [任务 14][MysqlDroagon] - Table [eb_store_order_status] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:08.663 - [任务 14][MysqlDroagon] - Query table 'eb_store_order_status' counts: 0 
[INFO ] 2024-10-26 06:14:08.664 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_form_temp, offset: null 
[INFO ] 2024-10-26 06:14:08.664 - [任务 14][MysqlDroagon] - Table eb_system_form_temp is going to be initial synced 
[INFO ] 2024-10-26 06:14:08.775 - [任务 14][MysqlDroagon] - Query table 'eb_system_form_temp' counts: 63 
[INFO ] 2024-10-26 06:14:08.775 - [任务 14][MysqlDroagon] - Table [eb_system_form_temp] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:08.776 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_experience_reply, offset: null 
[INFO ] 2024-10-26 06:14:08.776 - [任务 14][MysqlDroagon] - Table eb_store_experience_reply is going to be initial synced 
[INFO ] 2024-10-26 06:14:08.783 - [任务 14][MysqlDroagon] - Query table 'eb_store_experience_reply' counts: 9 
[INFO ] 2024-10-26 06:14:08.784 - [任务 14][MysqlDroagon] - Table [eb_store_experience_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:08.784 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_bargain_user, offset: null 
[INFO ] 2024-10-26 06:14:08.790 - [任务 14][MysqlDroagon] - Table eb_store_bargain_user is going to be initial synced 
[INFO ] 2024-10-26 06:14:08.790 - [任务 14][MysqlDroagon] - Table [eb_store_bargain_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:08.790 - [任务 14][MysqlDroagon] - Query table 'eb_store_bargain_user' counts: 0 
[INFO ] 2024-10-26 06:14:08.791 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_store, offset: null 
[INFO ] 2024-10-26 06:14:08.791 - [任务 14][MysqlDroagon] - Table eb_system_store is going to be initial synced 
[INFO ] 2024-10-26 06:14:08.796 - [任务 14][MysqlDroagon] - Table [eb_system_store] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:08.796 - [任务 14][MysqlDroagon] - Query table 'eb_system_store' counts: 0 
[INFO ] 2024-10-26 06:14:08.796 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_shipping_templates_region, offset: null 
[INFO ] 2024-10-26 06:14:08.796 - [任务 14][MysqlDroagon] - Table eb_shipping_templates_region is going to be initial synced 
[INFO ] 2024-10-26 06:14:09.000 - [任务 14][MysqlDroagon] - Query table 'eb_shipping_templates_region' counts: 369 
[INFO ] 2024-10-26 06:14:09.619 - [任务 14][MysqlDroagon] - Table [eb_shipping_templates_region] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:09.619 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_recharge, offset: null 
[INFO ] 2024-10-26 06:14:09.627 - [任务 14][MysqlDroagon] - Table eb_user_recharge is going to be initial synced 
[INFO ] 2024-10-26 06:14:09.627 - [任务 14][MysqlDroagon] - Table [eb_user_recharge] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:09.628 - [任务 14][MysqlDroagon] - Query table 'eb_user_recharge' counts: 0 
[INFO ] 2024-10-26 06:14:09.628 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_coupon, offset: null 
[INFO ] 2024-10-26 06:14:09.633 - [任务 14][MysqlDroagon] - Table eb_store_product_coupon is going to be initial synced 
[INFO ] 2024-10-26 06:14:09.633 - [任务 14][MysqlDroagon] - Table [eb_store_product_coupon] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:09.633 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_coupon' counts: 0 
[INFO ] 2024-10-26 06:14:09.633 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_wechat_pay_info, offset: null 
[INFO ] 2024-10-26 06:14:09.638 - [任务 14][MysqlDroagon] - Table eb_wechat_pay_info is going to be initial synced 
[INFO ] 2024-10-26 06:14:09.638 - [任务 14][MysqlDroagon] - Query table 'eb_wechat_pay_info' counts: 0 
[INFO ] 2024-10-26 06:14:09.638 - [任务 14][MysqlDroagon] - Table [eb_wechat_pay_info] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:09.638 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_combination, offset: null 
[INFO ] 2024-10-26 06:14:09.645 - [任务 14][MysqlDroagon] - Table eb_store_combination is going to be initial synced 
[INFO ] 2024-10-26 06:14:09.645 - [任务 14][MysqlDroagon] - Query table 'eb_store_combination' counts: 4 
[INFO ] 2024-10-26 06:14:09.646 - [任务 14][MysqlDroagon] - Table [eb_store_combination] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:09.646 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product, offset: null 
[INFO ] 2024-10-26 06:14:09.651 - [任务 14][MysqlDroagon] - Table eb_store_product is going to be initial synced 
[INFO ] 2024-10-26 06:14:09.651 - [任务 14][MysqlDroagon] - Query table 'eb_store_product' counts: 8 
[INFO ] 2024-10-26 06:14:09.652 - [任务 14][MysqlDroagon] - Table [eb_store_product] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:09.652 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_article, offset: null 
[INFO ] 2024-10-26 06:14:09.659 - [任务 14][MysqlDroagon] - Table eb_article is going to be initial synced 
[INFO ] 2024-10-26 06:14:09.659 - [任务 14][MysqlDroagon] - Query table 'eb_article' counts: 0 
[INFO ] 2024-10-26 06:14:09.659 - [任务 14][MysqlDroagon] - Table [eb_article] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:09.659 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_bill, offset: null 
[INFO ] 2024-10-26 06:14:09.665 - [任务 14][MysqlDroagon] - Table eb_user_bill is going to be initial synced 
[INFO ] 2024-10-26 06:14:09.665 - [任务 14][MysqlDroagon] - Table [eb_user_bill] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:09.665 - [任务 14][MysqlDroagon] - Query table 'eb_user_bill' counts: 0 
[INFO ] 2024-10-26 06:14:09.665 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_experience_relation, offset: null 
[INFO ] 2024-10-26 06:14:09.671 - [任务 14][MysqlDroagon] - Table eb_store_experience_relation is going to be initial synced 
[INFO ] 2024-10-26 06:14:09.671 - [任务 14][MysqlDroagon] - Table [eb_store_experience_relation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:09.671 - [任务 14][MysqlDroagon] - Query table 'eb_store_experience_relation' counts: 5 
[INFO ] 2024-10-26 06:14:09.671 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_wechat_exceptions, offset: null 
[INFO ] 2024-10-26 06:14:09.677 - [任务 14][MysqlDroagon] - Table eb_wechat_exceptions is going to be initial synced 
[INFO ] 2024-10-26 06:14:09.677 - [任务 14][MysqlDroagon] - Query table 'eb_wechat_exceptions' counts: 3 
[INFO ] 2024-10-26 06:14:09.677 - [任务 14][MysqlDroagon] - Table [eb_wechat_exceptions] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:09.677 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_template_message, offset: null 
[INFO ] 2024-10-26 06:14:09.682 - [任务 14][MysqlDroagon] - Table eb_template_message is going to be initial synced 
[INFO ] 2024-10-26 06:14:09.682 - [任务 14][MysqlDroagon] - Query table 'eb_template_message' counts: 15 
[INFO ] 2024-10-26 06:14:09.683 - [任务 14][MysqlDroagon] - Table [eb_template_message] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:09.683 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_integral_record, offset: null 
[INFO ] 2024-10-26 06:14:09.687 - [任务 14][MysqlDroagon] - Table eb_user_integral_record is going to be initial synced 
[INFO ] 2024-10-26 06:14:09.688 - [任务 14][MysqlDroagon] - Query table 'eb_user_integral_record' counts: 1 
[INFO ] 2024-10-26 06:14:09.688 - [任务 14][MysqlDroagon] - Table [eb_user_integral_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:09.688 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_coupon, offset: null 
[INFO ] 2024-10-26 06:14:09.693 - [任务 14][MysqlDroagon] - Table eb_store_coupon is going to be initial synced 
[INFO ] 2024-10-26 06:14:09.693 - [任务 14][MysqlDroagon] - Table [eb_store_coupon] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:09.693 - [任务 14][MysqlDroagon] - Query table 'eb_store_coupon' counts: 2 
[INFO ] 2024-10-26 06:14:09.694 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_visit_record, offset: null 
[INFO ] 2024-10-26 06:14:09.694 - [任务 14][MysqlDroagon] - Table eb_user_visit_record is going to be initial synced 
[INFO ] 2024-10-26 06:14:09.698 - [任务 14][MysqlDroagon] - Query table 'eb_user_visit_record' counts: 1318 
[INFO ] 2024-10-26 06:14:11.359 - [任务 14][MysqlDroagon] - Table [eb_user_visit_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:11.359 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_role_menu, offset: null 
[INFO ] 2024-10-26 06:14:11.380 - [任务 14][MysqlDroagon] - Table eb_system_role_menu is going to be initial synced 
[INFO ] 2024-10-26 06:14:11.383 - [任务 14][MysqlDroagon] - Query table 'eb_system_role_menu' counts: 595 
[INFO ] 2024-10-26 06:14:11.825 - [任务 14][MysqlDroagon] - Table [eb_system_role_menu] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:11.829 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_log, offset: null 
[INFO ] 2024-10-26 06:14:11.829 - [任务 14][MysqlDroagon] - Table eb_store_product_log is going to be initial synced 
[INFO ] 2024-10-26 06:14:11.929 - [任务 14][MysqlDroagon] - Table [eb_store_product_log] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:11.930 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_log' counts: 0 
[INFO ] 2024-10-26 06:14:11.931 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_series, offset: null 
[INFO ] 2024-10-26 06:14:11.931 - [任务 14][MysqlDroagon] - Table eb_store_product_series is going to be initial synced 
[INFO ] 2024-10-26 06:14:11.946 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_series' counts: 5 
[INFO ] 2024-10-26 06:14:11.947 - [任务 14][MysqlDroagon] - Table [eb_store_product_series] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:11.947 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_attr, offset: null 
[INFO ] 2024-10-26 06:14:11.947 - [任务 14][MysqlDroagon] - Table eb_store_product_attr is going to be initial synced 
[INFO ] 2024-10-26 06:14:11.951 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_attr' counts: 24 
[INFO ] 2024-10-26 06:14:11.951 - [任务 14][MysqlDroagon] - Table [eb_store_product_attr] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:11.951 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_bargain, offset: null 
[INFO ] 2024-10-26 06:14:11.951 - [任务 14][MysqlDroagon] - Table eb_store_bargain is going to be initial synced 
[INFO ] 2024-10-26 06:14:11.958 - [任务 14][MysqlDroagon] - Query table 'eb_store_bargain' counts: 2 
[INFO ] 2024-10-26 06:14:11.958 - [任务 14][MysqlDroagon] - Table [eb_store_bargain] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:11.959 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_experience_audio, offset: null 
[INFO ] 2024-10-26 06:14:11.959 - [任务 14][MysqlDroagon] - Table eb_store_experience_audio is going to be initial synced 
[INFO ] 2024-10-26 06:14:11.967 - [任务 14][MysqlDroagon] - Query table 'eb_store_experience_audio' counts: 9 
[INFO ] 2024-10-26 06:14:11.967 - [任务 14][MysqlDroagon] - Table [eb_store_experience_audio] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:11.967 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_city, offset: null 
[INFO ] 2024-10-26 06:14:11.967 - [任务 14][MysqlDroagon] - Table eb_system_city is going to be initial synced 
[INFO ] 2024-10-26 06:14:12.172 - [任务 14][MysqlDroagon] - Query table 'eb_system_city' counts: 3938 
[INFO ] 2024-10-26 06:14:14.808 - [任务 14][MysqlDroagon] - Table [eb_system_city] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:14.809 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_sms_record, offset: null 
[INFO ] 2024-10-26 06:14:14.810 - [任务 14][MysqlDroagon] - Table eb_sms_record is going to be initial synced 
[INFO ] 2024-10-26 06:14:14.821 - [任务 14][MysqlDroagon] - Table [eb_sms_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:14.821 - [任务 14][MysqlDroagon] - Query table 'eb_sms_record' counts: 0 
[INFO ] 2024-10-26 06:14:14.821 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_extract, offset: null 
[INFO ] 2024-10-26 06:14:14.821 - [任务 14][MysqlDroagon] - Table eb_user_extract is going to be initial synced 
[INFO ] 2024-10-26 06:14:14.831 - [任务 14][MysqlDroagon] - Table [eb_user_extract] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:14.831 - [任务 14][MysqlDroagon] - Query table 'eb_user_extract' counts: 0 
[INFO ] 2024-10-26 06:14:14.831 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_group, offset: null 
[INFO ] 2024-10-26 06:14:14.835 - [任务 14][MysqlDroagon] - Table eb_user_group is going to be initial synced 
[INFO ] 2024-10-26 06:14:14.835 - [任务 14][MysqlDroagon] - Query table 'eb_user_group' counts: 3 
[INFO ] 2024-10-26 06:14:14.837 - [任务 14][MysqlDroagon] - Table [eb_user_group] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:14.837 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_rule, offset: null 
[INFO ] 2024-10-26 06:14:14.841 - [任务 14][MysqlDroagon] - Table eb_store_product_rule is going to be initial synced 
[INFO ] 2024-10-26 06:14:14.841 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_rule' counts: 2 
[INFO ] 2024-10-26 06:14:14.842 - [任务 14][MysqlDroagon] - Table [eb_store_product_rule] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:14.842 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_cart, offset: null 
[INFO ] 2024-10-26 06:14:14.848 - [任务 14][MysqlDroagon] - Table eb_store_cart is going to be initial synced 
[INFO ] 2024-10-26 06:14:14.848 - [任务 14][MysqlDroagon] - Table [eb_store_cart] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:14.850 - [任务 14][MysqlDroagon] - Query table 'eb_store_cart' counts: 3 
[INFO ] 2024-10-26 06:14:14.850 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_admin, offset: null 
[INFO ] 2024-10-26 06:14:14.855 - [任务 14][MysqlDroagon] - Table eb_system_admin is going to be initial synced 
[INFO ] 2024-10-26 06:14:14.855 - [任务 14][MysqlDroagon] - Table [eb_system_admin] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:14.859 - [任务 14][MysqlDroagon] - Query table 'eb_system_admin' counts: 2 
[INFO ] 2024-10-26 06:14:14.859 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_menu, offset: null 
[INFO ] 2024-10-26 06:14:14.866 - [任务 14][MysqlDroagon] - Table eb_system_menu is going to be initial synced 
[INFO ] 2024-10-26 06:14:14.866 - [任务 14][MysqlDroagon] - Query table 'eb_system_menu' counts: 369 
[INFO ] 2024-10-26 06:14:15.003 - [任务 14][MysqlDroagon] - Table [eb_system_menu] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:15.003 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_user_level, offset: null 
[INFO ] 2024-10-26 06:14:15.021 - [任务 14][MysqlDroagon] - Table eb_system_user_level is going to be initial synced 
[INFO ] 2024-10-26 06:14:15.021 - [任务 14][MysqlDroagon] - Query table 'eb_system_user_level' counts: 5 
[INFO ] 2024-10-26 06:14:15.022 - [任务 14][MysqlDroagon] - Table [eb_system_user_level] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:15.022 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_category, offset: null 
[INFO ] 2024-10-26 06:14:15.022 - [任务 14][MysqlDroagon] - Table eb_category is going to be initial synced 
[INFO ] 2024-10-26 06:14:15.031 - [任务 14][MysqlDroagon] - Query table 'eb_category' counts: 295 
[INFO ] 2024-10-26 06:14:15.211 - [任务 14][MysqlDroagon] - Table [eb_category] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:15.211 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_sms_template, offset: null 
[INFO ] 2024-10-26 06:14:15.221 - [任务 14][MysqlDroagon] - Table eb_sms_template is going to be initial synced 
[INFO ] 2024-10-26 06:14:15.221 - [任务 14][MysqlDroagon] - Query table 'eb_sms_template' counts: 7 
[INFO ] 2024-10-26 06:14:15.578 - [任务 14][MysqlDroagon] - Table [eb_sms_template] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:15.578 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_tag, offset: null 
[INFO ] 2024-10-26 06:14:15.579 - [任务 14][MysqlDroagon] - Table eb_user_tag is going to be initial synced 
[INFO ] 2024-10-26 06:14:15.603 - [任务 14][MysqlDroagon] - Table [eb_user_tag] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:15.604 - [任务 14][MysqlDroagon] - Query table 'eb_user_tag' counts: 5 
[INFO ] 2024-10-26 06:14:15.604 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_order_info, offset: null 
[INFO ] 2024-10-26 06:14:15.605 - [任务 14][MysqlDroagon] - Table eb_store_order_info is going to be initial synced 
[INFO ] 2024-10-26 06:14:15.617 - [任务 14][MysqlDroagon] - Table [eb_store_order_info] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:15.618 - [任务 14][MysqlDroagon] - Query table 'eb_store_order_info' counts: 0 
[INFO ] 2024-10-26 06:14:15.618 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_coupon_user, offset: null 
[INFO ] 2024-10-26 06:14:15.627 - [任务 14][MysqlDroagon] - Table eb_store_coupon_user is going to be initial synced 
[INFO ] 2024-10-26 06:14:15.627 - [任务 14][MysqlDroagon] - Table [eb_store_coupon_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:15.628 - [任务 14][MysqlDroagon] - Query table 'eb_store_coupon_user' counts: 0 
[INFO ] 2024-10-26 06:14:15.628 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_role, offset: null 
[INFO ] 2024-10-26 06:14:15.635 - [任务 14][MysqlDroagon] - Table eb_system_role is going to be initial synced 
[INFO ] 2024-10-26 06:14:15.636 - [任务 14][MysqlDroagon] - Query table 'eb_system_role' counts: 2 
[INFO ] 2024-10-26 06:14:15.637 - [任务 14][MysqlDroagon] - Table [eb_system_role] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:15.637 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_experience_audio_history, offset: null 
[INFO ] 2024-10-26 06:14:15.638 - [任务 14][MysqlDroagon] - Table eb_store_experience_audio_history is going to be initial synced 
[INFO ] 2024-10-26 06:14:15.646 - [任务 14][MysqlDroagon] - Query table 'eb_store_experience_audio_history' counts: 10 
[INFO ] 2024-10-26 06:14:15.646 - [任务 14][MysqlDroagon] - Table [eb_store_experience_audio_history] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:15.648 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_pink, offset: null 
[INFO ] 2024-10-26 06:14:15.648 - [任务 14][MysqlDroagon] - Table eb_store_pink is going to be initial synced 
[INFO ] 2024-10-26 06:14:15.657 - [任务 14][MysqlDroagon] - Query table 'eb_store_pink' counts: 0 
[INFO ] 2024-10-26 06:14:15.657 - [任务 14][MysqlDroagon] - Table [eb_store_pink] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:15.658 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_express, offset: null 
[INFO ] 2024-10-26 06:14:15.658 - [任务 14][MysqlDroagon] - Table eb_express is going to be initial synced 
[INFO ] 2024-10-26 06:14:15.863 - [任务 14][MysqlDroagon] - Query table 'eb_express' counts: 1067 
[INFO ] 2024-10-26 06:14:16.739 - [任务 14][MysqlDroagon] - Table [eb_express] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:16.742 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_store_product_relation, offset: null 
[INFO ] 2024-10-26 06:14:16.748 - [任务 14][MysqlDroagon] - Table eb_store_product_relation is going to be initial synced 
[INFO ] 2024-10-26 06:14:16.885 - [任务 14][MysqlDroagon] - Table [eb_store_product_relation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:16.886 - [任务 14][MysqlDroagon] - Query table 'eb_store_product_relation' counts: 0 
[INFO ] 2024-10-26 06:14:16.887 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_system_group_data, offset: null 
[INFO ] 2024-10-26 06:14:16.887 - [任务 14][MysqlDroagon] - Table eb_system_group_data is going to be initial synced 
[INFO ] 2024-10-26 06:14:16.898 - [任务 14][MysqlDroagon] - Query table 'eb_system_group_data' counts: 71 
[INFO ] 2024-10-26 06:14:16.898 - [任务 14][MysqlDroagon] - Table [eb_system_group_data] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:16.898 - [任务 14][MysqlDroagon] - Starting batch read, table name: eb_user_address, offset: null 
[INFO ] 2024-10-26 06:14:16.898 - [任务 14][MysqlDroagon] - Table eb_user_address is going to be initial synced 
[INFO ] 2024-10-26 06:14:16.904 - [任务 14][MysqlDroagon] - Table [eb_user_address] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-26 06:14:16.904 - [任务 14][MysqlDroagon] - Query table 'eb_user_address' counts: 0 
[INFO ] 2024-10-26 06:14:17.106 - [任务 14][MysqlDroagon] - Initial sync completed 
[INFO ] 2024-10-26 06:14:17.953 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] running status set to false 
[INFO ] 2024-10-26 06:14:17.990 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] running status set to false 
[INFO ] 2024-10-26 06:14:17.990 - [任务 14][MysqlDroagon] - PDK connector node stopped: HazelcastSourcePdkDataNode-4ac5ce09-692f-447a-82f6-00938409fa12 
[INFO ] 2024-10-26 06:14:18.000 - [任务 14][MysqlDroagon] - PDK connector node released: HazelcastSourcePdkDataNode-4ac5ce09-692f-447a-82f6-00938409fa12 
[INFO ] 2024-10-26 06:14:18.000 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] schema data cleaned 
[INFO ] 2024-10-26 06:14:18.000 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] monitor closed 
[INFO ] 2024-10-26 06:14:18.063 - [任务 14][MysqlDroagon] - Node MysqlDroagon[4ac5ce09-692f-447a-82f6-00938409fa12] close complete, cost 52 ms 
[INFO ] 2024-10-26 06:14:18.064 - [任务 14][mysql184] - PDK connector node stopped: HazelcastTargetPdkDataNode-f66e37b8-5a42-4581-9be6-f98eb2d1199e 
[INFO ] 2024-10-26 06:14:18.064 - [任务 14][mysql184] - PDK connector node released: HazelcastTargetPdkDataNode-f66e37b8-5a42-4581-9be6-f98eb2d1199e 
[INFO ] 2024-10-26 06:14:18.065 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] schema data cleaned 
[INFO ] 2024-10-26 06:14:18.084 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] monitor closed 
[INFO ] 2024-10-26 06:14:18.084 - [任务 14][mysql184] - Node mysql184[f66e37b8-5a42-4581-9be6-f98eb2d1199e] close complete, cost 108 ms 
[INFO ] 2024-10-26 06:14:21.995 - [任务 14] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-26 06:14:21.996 - [任务 14] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4632267e 
[INFO ] 2024-10-26 06:14:21.997 - [任务 14] - Stop task milestones: 671c157ef71e5e013cc2372b(任务 14)  
[INFO ] 2024-10-26 06:14:22.125 - [任务 14] - Stopped task aspect(s) 
[INFO ] 2024-10-26 06:14:22.126 - [任务 14] - Snapshot order controller have been removed 
[INFO ] 2024-10-26 06:14:22.152 - [任务 14] - Remove memory task client succeed, task: 任务 14[671c157ef71e5e013cc2372b] 
[INFO ] 2024-10-26 06:14:22.152 - [任务 14] - Destroy memory task client cache succeed, task: 任务 14[671c157ef71e5e013cc2372b] 
