[INFO ] 2024-07-23 14:28:19.526 - [ClickHouse-Mysql 轮训2] - Task initialization... 
[INFO ] 2024-07-23 14:28:19.649 - [ClickHouse-Mysql 轮训2] - Start task milestones: 669f4d57e29f7d4f8d097370(<PERSON><PERSON><PERSON>ouse-Mysql 轮训2) 
[INFO ] 2024-07-23 14:28:19.649 - [ClickHouse-Mysql 轮训2] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-23 14:28:19.845 - [ClickHouse-Mysql 轮训2] - The engine receives ClickHouse-Mysql 轮训2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-23 14:28:19.845 - [ClickHouse-Mysql 轮训2][dummy_test] - Node dummy_test[cb0c4eba-0208-45da-baf5-97da146cdab0] start preload schema,table counts: 1 
[INFO ] 2024-07-23 14:28:19.856 - [ClickHouse-Mysq<PERSON> 轮训2][testSource2] - Node testSource2[732bb9fd-5671-4c81-b42d-f93f8a136ab0] start preload schema,table counts: 1 
[INFO ] 2024-07-23 14:28:19.862 - [ClickHouse-Mysql 轮训2][dummy_test] - Node dummy_test[cb0c4eba-0208-45da-baf5-97da146cdab0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 14:28:19.865 - [ClickHouse-Mysql 轮训2][testSource2] - Node testSource2[732bb9fd-5671-4c81-b42d-f93f8a136ab0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 14:28:20.692 - [ClickHouse-Mysql 轮训2][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-23 14:28:20.753 - [ClickHouse-Mysql 轮训2][testSource2] - Source node "testSource2" read batch size: 100 
[INFO ] 2024-07-23 14:28:20.753 - [ClickHouse-Mysql 轮训2][testSource2] - Source node "testSource2" event queue capacity: 200 
[INFO ] 2024-07-23 14:28:20.753 - [ClickHouse-Mysql 轮训2][testSource2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-23 14:28:20.753 - [ClickHouse-Mysql 轮训2][testSource2] - batch offset found: {},stream offset found: {} 
[INFO ] 2024-07-23 14:28:20.809 - [ClickHouse-Mysql 轮训2][testSource2] - Initial sync started 
[INFO ] 2024-07-23 14:28:20.810 - [ClickHouse-Mysql 轮训2][testSource2] - Starting batch read, table name: testSource2 
[INFO ] 2024-07-23 14:28:20.810 - [ClickHouse-Mysql 轮训2][testSource2] - Table testSource2 is going to be initial synced 
[INFO ] 2024-07-23 14:29:31.990 - [ClickHouse-Mysql 轮训2][testSource2] - Table [testSource2] has been completed batch read, will skip batch read on the next run 
[WARN ] 2024-07-23 14:29:32.002 - [ClickHouse-Mysql 轮训2][testSource2] - Query 'testSource2' snapshot row size failed: java.lang.RuntimeException: io.tapdata.exception.NodeException: Query table 'testSource2'  count failed: ClickHouse exception, code: 215, host: **************, port: 18123; Code: 215, e.displayText() = DB::Exception: Column `changeTime` is not under aggregate function and not in GROUP BY (version ******** (official build))

java.util.concurrent.CompletionException: java.lang.RuntimeException: io.tapdata.exception.NodeException: Query table 'testSource2'  count failed: ClickHouse exception, code: 215, host: **************, port: 18123; Code: 215, e.displayText() = DB::Exception: Column `changeTime` is not under aggregate function and not in GROUP BY (version ******** (official build))

	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:273)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:280)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1643)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1632)
	at java.util.concurrent.ForkJoinTask.doExec$$$capture(ForkJoinTask.java:289)
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java)
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1056)
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1692)
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:175)
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Query table 'testSource2'  count failed: ClickHouse exception, code: 215, host: **************, port: 18123; Code: 215, e.displayText() = DB::Exception: Column `changeTime` is not under aggregate function and not in GROUP BY (version ******** (official build))

	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doBatchCountFunction(HazelcastSourcePdkDataNode.java:1304)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doAsyncTableCount$41(HazelcastSourcePdkBaseNode.java:1367)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640)
	... 7 more
Caused by: io.tapdata.exception.NodeException: Query table 'testSource2'  count failed: ClickHouse exception, code: 215, host: **************, port: 18123; Code: 215, e.displayText() = DB::Exception: Column `changeTime` is not under aggregate function and not in GROUP BY (version ******** (official build))

	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doBatchCountFunction$56(HazelcastSourcePdkDataNode.java:1307)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 11 more
Caused by: io.tapdata.exception.NodeException: Query table 'testSource2'  count failed: ClickHouse exception, code: 215, host: **************, port: 18123; Code: 215, e.displayText() = DB::Exception: Column `changeTime` is not under aggregate function and not in GROUP BY (version ******** (official build))

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$55(HazelcastSourcePdkDataNode.java:1334)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 17 more
Caused by: ru.yandex.clickhouse.except.ClickHouseException: ClickHouse exception, code: 215, host: **************, port: 18123; Code: 215, e.displayText() = DB::Exception: Column `changeTime` is not under aggregate function and not in GROUP BY (version ******** (official build))

	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:58)
	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:100)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:95)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:90)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:94)
	at io.tapdata.common.CommonDbConnector.countByAdvanceFilter(CommonDbConnector.java:776)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$55(HazelcastSourcePdkDataNode.java:1320)
	... 18 more
Caused by: java.lang.Throwable: Code: 215, e.displayText() = DB::Exception: Column `changeTime` is not under aggregate function and not in GROUP BY (version ******** (official build))

	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	... 30 more
 
[INFO ] 2024-07-23 14:29:32.006 - [ClickHouse-Mysql 轮训2][testSource2] - Initial sync completed 
[INFO ] 2024-07-23 14:29:32.006 - [ClickHouse-Mysql 轮训2][testSource2] - Incremental sync starting... 
[INFO ] 2024-07-23 14:29:32.006 - [ClickHouse-Mysql 轮训2][testSource2] - Initial sync completed 
[INFO ] 2024-07-23 14:29:32.007 - [ClickHouse-Mysql 轮训2][testSource2] - Start run table [testSource2] polling cdc with parameters 
 - Conditional field(s): {testSource2={changeTime=2023-08-08 13:30:00.0}}
 - Loop polling interval: 500 ms
 - Batch size: 1000 
[INFO ] 2024-07-23 14:29:32.008 - [ClickHouse-Mysql 轮训2][testSource2] - Query by advance filter
 - loop time: 1
 - table: testSource2
 - filter: [changeTime>'2023-08-08 13:30:00.0']
 - limit: 1000
 - sort: [changeTime ASC] 
[INFO ] 2024-07-23 14:31:41.822 - [ClickHouse-Mysql 轮训2][testSource2] - Node testSource2[732bb9fd-5671-4c81-b42d-f93f8a136ab0] running status set to false 
[INFO ] 2024-07-23 14:31:41.824 - [ClickHouse-Mysql 轮训2][testSource2] - Incremental sync completed 
[INFO ] 2024-07-23 14:31:41.825 - [ClickHouse-Mysql 轮训2][testSource2] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-23 14:31:41.829 - [ClickHouse-Mysql 轮训2][testSource2] - Clickhouse Optimize Table end 
[WARN ] 2024-07-23 14:31:41.842 - [ClickHouse-Mysql 轮训2][testSource2] - Stop PDK connector node failed: Unknown exception occur when operate table: unknown | Associate id: HazelcastSourcePdkDataNode-732bb9fd-5671-4c81-b42d-f93f8a136ab0 
[INFO ] 2024-07-23 14:31:41.844 - [ClickHouse-Mysql 轮训2][testSource2] - PDK connector node released: HazelcastSourcePdkDataNode-732bb9fd-5671-4c81-b42d-f93f8a136ab0 
[INFO ] 2024-07-23 14:31:41.851 - [ClickHouse-Mysql 轮训2][testSource2] - Node testSource2[732bb9fd-5671-4c81-b42d-f93f8a136ab0] schema data cleaned 
[INFO ] 2024-07-23 14:31:41.851 - [ClickHouse-Mysql 轮训2][testSource2] - Node testSource2[732bb9fd-5671-4c81-b42d-f93f8a136ab0] monitor closed 
[INFO ] 2024-07-23 14:31:41.855 - [ClickHouse-Mysql 轮训2][testSource2] - Node testSource2[732bb9fd-5671-4c81-b42d-f93f8a136ab0] close complete, cost 52 ms 
