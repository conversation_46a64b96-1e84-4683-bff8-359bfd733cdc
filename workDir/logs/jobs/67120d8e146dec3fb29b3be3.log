[INFO ] 2024-10-21 17:29:40.387 - [任务 2] - Task initialization... 
[INFO ] 2024-10-21 17:29:40.593 - [任务 2] - Start task milestones: 67120d8e146dec3fb29b3be3(任务 2) 
[INFO ] 2024-10-21 17:29:40.608 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-21 17:29:40.703 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-21 17:29:40.703 - [任务 2][BMSQL_ITEM] - Node BMSQL_ITEM[d5240504-c7b0-4ea9-8484-9e0afd2635bd] start preload schema,table counts: 1 
[INFO ] 2024-10-21 17:29:40.703 - [任务 2][ITEM] - Node ITEM[51c090bd-bc43-4407-ad73-4b7ff77079f9] start preload schema,table counts: 1 
[INFO ] 2024-10-21 17:29:40.703 - [任务 2][BMSQL_ITEM] - Node BMSQL_ITEM[d5240504-c7b0-4ea9-8484-9e0afd2635bd] preload schema finished, cost 0 ms 
[INFO ] 2024-10-21 17:29:40.904 - [任务 2][ITEM] - Node ITEM[51c090bd-bc43-4407-ad73-4b7ff77079f9] preload schema finished, cost 0 ms 
[INFO ] 2024-10-21 17:29:41.870 - [任务 2][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-10-21 17:29:41.872 - [任务 2][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-10-21 17:29:41.872 - [任务 2][BMSQL_ITEM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-21 17:29:41.974 - [任务 2][BMSQL_ITEM] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":939851,"gtidSet":""} 
[INFO ] 2024-10-21 17:29:41.986 - [任务 2][BMSQL_ITEM] - Initial sync started 
[INFO ] 2024-10-21 17:29:41.986 - [任务 2][BMSQL_ITEM] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-10-21 17:29:42.013 - [任务 2][BMSQL_ITEM] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-10-21 17:29:42.019 - [任务 2][ITEM] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-21 17:29:42.052 - [任务 2][BMSQL_ITEM] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 17:29:42.058 - [任务 2][BMSQL_ITEM] - Query table 'BMSQL_ITEM' counts: 7 
[INFO ] 2024-10-21 17:29:42.058 - [任务 2][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-21 17:29:42.059 - [任务 2][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-10-21 17:29:42.060 - [任务 2][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-21 17:29:42.060 - [任务 2][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: {"filename":"binlog.000036","position":939851,"gtidSet":""} 
[INFO ] 2024-10-21 17:29:42.111 - [任务 2][BMSQL_ITEM] - Starting mysql cdc, server name: f6c57016-63b8-45a3-a43e-2a9ffd386586 
[INFO ] 2024-10-21 17:29:42.111 - [任务 2][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"f6c57016-63b8-45a3-a43e-2a9ffd386586","offset":{"{\"server\":\"f6c57016-63b8-45a3-a43e-2a9ffd386586\"}":"{\"file\":\"binlog.000036\",\"pos\":939851,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1346172246
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f6c57016-63b8-45a3-a43e-2a9ffd386586
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f6c57016-63b8-45a3-a43e-2a9ffd386586
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: f6c57016-63b8-45a3-a43e-2a9ffd386586
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-21 17:29:42.317 - [任务 2][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-10-21 17:49:04.467 - [任务 2][BMSQL_ITEM] - Node BMSQL_ITEM[d5240504-c7b0-4ea9-8484-9e0afd2635bd] running status set to false 
[INFO ] 2024-10-21 17:49:04.586 - [任务 2][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-21 17:49:04.586 - [任务 2][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-10-21 17:49:04.603 - [任务 2][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-10-21 17:49:04.603 - [任务 2][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-d5240504-c7b0-4ea9-8484-9e0afd2635bd 
[INFO ] 2024-10-21 17:49:04.603 - [任务 2][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-d5240504-c7b0-4ea9-8484-9e0afd2635bd 
[INFO ] 2024-10-21 17:49:04.603 - [任务 2][BMSQL_ITEM] - Node BMSQL_ITEM[d5240504-c7b0-4ea9-8484-9e0afd2635bd] schema data cleaned 
[INFO ] 2024-10-21 17:49:04.605 - [任务 2][BMSQL_ITEM] - Node BMSQL_ITEM[d5240504-c7b0-4ea9-8484-9e0afd2635bd] monitor closed 
[INFO ] 2024-10-21 17:49:04.605 - [任务 2][BMSQL_ITEM] - Node BMSQL_ITEM[d5240504-c7b0-4ea9-8484-9e0afd2635bd] close complete, cost 143 ms 
[INFO ] 2024-10-21 17:49:04.637 - [任务 2][ITEM] - Node ITEM[51c090bd-bc43-4407-ad73-4b7ff77079f9] running status set to false 
[INFO ] 2024-10-21 17:49:04.637 - [任务 2][ITEM] - PDK connector node stopped: HazelcastTargetPdkDataNode-51c090bd-bc43-4407-ad73-4b7ff77079f9 
[INFO ] 2024-10-21 17:49:04.637 - [任务 2][ITEM] - PDK connector node released: HazelcastTargetPdkDataNode-51c090bd-bc43-4407-ad73-4b7ff77079f9 
[INFO ] 2024-10-21 17:49:04.638 - [任务 2][ITEM] - Node ITEM[51c090bd-bc43-4407-ad73-4b7ff77079f9] schema data cleaned 
[INFO ] 2024-10-21 17:49:04.638 - [任务 2][ITEM] - Node ITEM[51c090bd-bc43-4407-ad73-4b7ff77079f9] monitor closed 
[INFO ] 2024-10-21 17:49:04.638 - [任务 2][ITEM] - Node ITEM[51c090bd-bc43-4407-ad73-4b7ff77079f9] close complete, cost 33 ms 
[INFO ] 2024-10-21 17:49:08.619 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-21 17:49:08.620 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6c6405ba 
[INFO ] 2024-10-21 17:49:08.762 - [任务 2] - Stop task milestones: 67120d8e146dec3fb29b3be3(任务 2)  
[INFO ] 2024-10-21 17:49:08.762 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-10-21 17:49:08.762 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-10-21 17:49:08.803 - [任务 2] - Remove memory task client succeed, task: 任务 2[67120d8e146dec3fb29b3be3] 
[INFO ] 2024-10-21 17:49:08.804 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[67120d8e146dec3fb29b3be3] 
[INFO ] 2024-10-21 17:49:14.205 - [任务 2] - Task initialization... 
[INFO ] 2024-10-21 17:49:14.207 - [任务 2] - Start task milestones: 67120d8e146dec3fb29b3be3(任务 2) 
[INFO ] 2024-10-21 17:49:14.362 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-21 17:49:14.362 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-21 17:49:14.404 - [任务 2][ITEM] - Node ITEM[51c090bd-bc43-4407-ad73-4b7ff77079f9] start preload schema,table counts: 1 
[INFO ] 2024-10-21 17:49:14.405 - [任务 2][BMSQL_ITEM] - Node BMSQL_ITEM[d5240504-c7b0-4ea9-8484-9e0afd2635bd] start preload schema,table counts: 1 
[INFO ] 2024-10-21 17:49:14.405 - [任务 2][BMSQL_ITEM] - Node BMSQL_ITEM[d5240504-c7b0-4ea9-8484-9e0afd2635bd] preload schema finished, cost 0 ms 
[INFO ] 2024-10-21 17:49:14.405 - [任务 2][ITEM] - Node ITEM[51c090bd-bc43-4407-ad73-4b7ff77079f9] preload schema finished, cost 0 ms 
[INFO ] 2024-10-21 17:49:14.733 - [任务 2][ITEM] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-21 17:49:14.734 - [任务 2][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-10-21 17:49:14.734 - [任务 2][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-10-21 17:49:14.734 - [任务 2][BMSQL_ITEM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-21 17:49:14.804 - [任务 2][BMSQL_ITEM] - batch offset found: {"BMSQL_ITEM":{"batch_read_connector_status":"OVER"}},stream offset found: {"name":"f6c57016-63b8-45a3-a43e-2a9ffd386586","offset":{"{\"server\":\"f6c57016-63b8-45a3-a43e-2a9ffd386586\"}":"{\"ts_sec\":1729503051,\"file\":\"binlog.000036\",\"pos\":940301,\"server_id\":1}"}} 
[INFO ] 2024-10-21 17:49:14.804 - [任务 2][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-10-21 17:49:14.804 - [任务 2][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-21 17:49:14.804 - [任务 2][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: {"name":"f6c57016-63b8-45a3-a43e-2a9ffd386586","offset":{"{\"server\":\"f6c57016-63b8-45a3-a43e-2a9ffd386586\"}":"{\"ts_sec\":1729503051,\"file\":\"binlog.000036\",\"pos\":940301,\"server_id\":1}"}} 
[INFO ] 2024-10-21 17:49:14.843 - [任务 2][BMSQL_ITEM] - Starting mysql cdc, server name: f6c57016-63b8-45a3-a43e-2a9ffd386586 
[INFO ] 2024-10-21 17:49:14.846 - [任务 2][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"f6c57016-63b8-45a3-a43e-2a9ffd386586","offset":{"{\"server\":\"f6c57016-63b8-45a3-a43e-2a9ffd386586\"}":"{\"ts_sec\":1729503051,\"file\":\"binlog.000036\",\"pos\":940301,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2053312712
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f6c57016-63b8-45a3-a43e-2a9ffd386586
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f6c57016-63b8-45a3-a43e-2a9ffd386586
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: f6c57016-63b8-45a3-a43e-2a9ffd386586
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-21 17:49:15.052 - [任务 2][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-10-21 18:21:01.493 - [任务 2][BMSQL_ITEM] - Node BMSQL_ITEM[d5240504-c7b0-4ea9-8484-9e0afd2635bd] running status set to false 
[INFO ] 2024-10-21 18:32:05.924 - [任务 2] - Task initialization... 
[INFO ] 2024-10-21 18:32:06.174 - [任务 2] - Start task milestones: 67120d8e146dec3fb29b3be3(任务 2) 
[INFO ] 2024-10-21 18:32:06.412 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-21 18:32:06.459 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-21 18:32:06.508 - [任务 2][ITEM] - Node ITEM[51c090bd-bc43-4407-ad73-4b7ff77079f9] start preload schema,table counts: 1 
[INFO ] 2024-10-21 18:32:06.509 - [任务 2][BMSQL_ITEM] - Node BMSQL_ITEM[d5240504-c7b0-4ea9-8484-9e0afd2635bd] start preload schema,table counts: 1 
[INFO ] 2024-10-21 18:32:06.523 - [任务 2][ITEM] - Node ITEM[51c090bd-bc43-4407-ad73-4b7ff77079f9] preload schema finished, cost 0 ms 
[INFO ] 2024-10-21 18:32:06.530 - [任务 2][BMSQL_ITEM] - Node BMSQL_ITEM[d5240504-c7b0-4ea9-8484-9e0afd2635bd] preload schema finished, cost 0 ms 
[INFO ] 2024-10-21 18:32:07.270 - [任务 2][BMSQL_ITEM] - Source node "BMSQL_ITEM" read batch size: 100 
[INFO ] 2024-10-21 18:32:07.271 - [任务 2][BMSQL_ITEM] - Source node "BMSQL_ITEM" event queue capacity: 200 
[INFO ] 2024-10-21 18:32:07.272 - [任务 2][BMSQL_ITEM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-21 18:32:07.310 - [任务 2][BMSQL_ITEM] - batch offset found: {},stream offset found: {"name":"f6c57016-63b8-45a3-a43e-2a9ffd386586","offset":{"{\"server\":\"f6c57016-63b8-45a3-a43e-2a9ffd386586\"}":"{\"file\":\"binlog.000036\",\"pos\":943304,\"server_id\":1}"}} 
[INFO ] 2024-10-21 18:32:07.310 - [任务 2][ITEM] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-21 18:32:07.431 - [任务 2][BMSQL_ITEM] - Incremental sync starting... 
[INFO ] 2024-10-21 18:32:07.432 - [任务 2][BMSQL_ITEM] - Initial sync completed 
[INFO ] 2024-10-21 18:32:07.433 - [任务 2][BMSQL_ITEM] - Starting stream read, table list: [BMSQL_ITEM], offset: {"name":"f6c57016-63b8-45a3-a43e-2a9ffd386586","offset":{"{\"server\":\"f6c57016-63b8-45a3-a43e-2a9ffd386586\"}":"{\"file\":\"binlog.000036\",\"pos\":943304,\"server_id\":1}"}} 
[INFO ] 2024-10-21 18:32:07.493 - [任务 2][BMSQL_ITEM] - Starting mysql cdc, server name: f6c57016-63b8-45a3-a43e-2a9ffd386586 
[INFO ] 2024-10-21 18:32:07.576 - [任务 2][BMSQL_ITEM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"f6c57016-63b8-45a3-a43e-2a9ffd386586","offset":{"{\"server\":\"f6c57016-63b8-45a3-a43e-2a9ffd386586\"}":"{\"file\":\"binlog.000036\",\"pos\":943304,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 685371708
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f6c57016-63b8-45a3-a43e-2a9ffd386586
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f6c57016-63b8-45a3-a43e-2a9ffd386586
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: f6c57016-63b8-45a3-a43e-2a9ffd386586
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-21 18:32:09.062 - [任务 2][BMSQL_ITEM] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-10-21 21:06:38.921 - [任务 2][BMSQL_ITEM] - Node BMSQL_ITEM[d5240504-c7b0-4ea9-8484-9e0afd2635bd] running status set to false 
