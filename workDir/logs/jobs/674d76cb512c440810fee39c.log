[INFO ] 2024-12-02 16:59:18.304 - [任务 1] - Task initialization... 
[INFO ] 2024-12-02 16:59:18.512 - [任务 1] - Start task milestones: 674d76cb512c440810fee39c(任务 1) 
[INFO ] 2024-12-02 16:59:19.052 - [任务 1] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-12-02 16:59:19.258 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-02 16:59:19.636 - [任务 1][testCar] - Node testCar[bf19f20f-bc59-4898-9170-414e7ec860df] start preload schema,table counts: 1 
[INFO ] 2024-12-02 16:59:19.638 - [任务 1][CAR_CUSTOMER] - Node CAR_CUSTOMER[a9dca719-204a-415a-b9b6-c7cd1ff68cf9] start preload schema,table counts: 1 
[INFO ] 2024-12-02 16:59:19.639 - [任务 1][testCar] - Node testCar[bf19f20f-bc59-4898-9170-414e7ec860df] preload schema finished, cost 0 ms 
[INFO ] 2024-12-02 16:59:19.641 - [任务 1][CAR_CUSTOMER] - Node CAR_CUSTOMER[a9dca719-204a-415a-b9b6-c7cd1ff68cf9] preload schema finished, cost 0 ms 
[INFO ] 2024-12-02 16:59:20.886 - [任务 1][testCar] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-02 16:59:21.109 - [任务 1][CAR_CUSTOMER] - Source node "CAR_CUSTOMER" read batch size: 100 
[INFO ] 2024-12-02 16:59:21.115 - [任务 1][CAR_CUSTOMER] - Source node "CAR_CUSTOMER" event queue capacity: 200 
[INFO ] 2024-12-02 16:59:21.118 - [任务 1][CAR_CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-02 16:59:21.317 - [任务 1][CAR_CUSTOMER] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":730783557,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-12-02 16:59:21.317 - [任务 1][CAR_CUSTOMER] - Initial sync started 
[INFO ] 2024-12-02 16:59:21.330 - [任务 1][CAR_CUSTOMER] - Starting batch read, table name: CAR_CUSTOMER 
[INFO ] 2024-12-02 16:59:21.331 - [任务 1][CAR_CUSTOMER] - Table CAR_CUSTOMER is going to be initial synced 
[INFO ] 2024-12-02 16:59:21.442 - [任务 1][CAR_CUSTOMER] - Query table 'CAR_CUSTOMER' counts: 675 
[INFO ] 2024-12-02 16:59:21.443 - [任务 1][CAR_CUSTOMER] - Table [CAR_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-02 16:59:21.443 - [任务 1][CAR_CUSTOMER] - Initial sync completed 
[INFO ] 2024-12-02 16:59:21.445 - [任务 1][CAR_CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-12-02 16:59:21.446 - [任务 1][CAR_CUSTOMER] - Initial sync completed 
[INFO ] 2024-12-02 16:59:21.449 - [任务 1][CAR_CUSTOMER] - Starting stream read, table list: [CAR_CUSTOMER], offset: {"sortString":null,"offsetValue":null,"lastScn":730783557,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-12-02 16:59:21.607 - [任务 1][CAR_CUSTOMER] - setting fzs taskId 21444aeca244476b9b625255a69ef946 
[INFO ] 2024-12-02 16:59:21.609 - [任务 1][CAR_CUSTOMER] - fzs set 21444aeca244476b9b625255a69ef946 taskId ok 
[INFO ] 2024-12-02 16:59:21.625 - [任务 1][CAR_CUSTOMER] - set save fzs time ok 
[INFO ] 2024-12-02 16:59:21.626 - [任务 1][CAR_CUSTOMER] - set fzs data format ok 
[INFO ] 2024-12-02 16:59:21.643 - [任务 1][CAR_CUSTOMER] - set disable fzs zip ok 
[INFO ] 2024-12-02 16:59:21.643 - [任务 1][CAR_CUSTOMER] - set fzs socket timeout 100 ok 
[INFO ] 2024-12-02 16:59:21.848 - [任务 1][CAR_CUSTOMER] - fzs set TAPDATA.CAR_CUSTOMER|@2b8edf45 table and bgn scn ok 
[INFO ] 2024-12-02 16:59:22.869 - [任务 1][CAR_CUSTOMER] - fzs service started 
[WARN ] 2024-12-02 17:01:40.130 - [任务 1][CAR_CUSTOMER] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.net.SocketException: Socket closed
	java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:118)
	java.net.SocketOutputStream.write(SocketOutputStream.java:143)
	io.tapdata.connector.oracle.cdc.bridge.BridgeService.pullErrorMsg(BridgeService.java:242)
	io.tapdata.connector.oracle.cdc.bridge.BridgeService.pullRedoLog(BridgeService.java:147)
	io.tapdata.connector.oracle.cdc.bridge.BridgeLogMiner.startMiner(BridgeLogMiner.java:131)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-12-02 17:02:40.279 - [任务 1][CAR_CUSTOMER] - Log Miner is shutting down... 
[INFO ] 2024-12-02 17:02:50.231 - [任务 1][CAR_CUSTOMER] - setting fzs taskId 21444aeca244476b9b625255a69ef946 
[INFO ] 2024-12-02 17:02:50.272 - [任务 1][CAR_CUSTOMER] - fzs set 21444aeca244476b9b625255a69ef946 taskId ok 
[INFO ] 2024-12-02 17:02:50.272 - [任务 1][CAR_CUSTOMER] - set save fzs time ok 
[INFO ] 2024-12-02 17:02:50.294 - [任务 1][CAR_CUSTOMER] - set fzs data format ok 
[INFO ] 2024-12-02 17:02:50.294 - [任务 1][CAR_CUSTOMER] - fzs clear task ok 
[INFO ] 2024-12-02 17:03:00.861 - [任务 1][CAR_CUSTOMER] - setting fzs taskId 21444aeca244476b9b625255a69ef946 
[INFO ] 2024-12-02 17:03:00.861 - [任务 1][CAR_CUSTOMER] - fzs set 21444aeca244476b9b625255a69ef946 taskId ok 
[INFO ] 2024-12-02 17:03:00.891 - [任务 1][CAR_CUSTOMER] - set save fzs time ok 
[INFO ] 2024-12-02 17:03:00.891 - [任务 1][CAR_CUSTOMER] - set fzs data format ok 
[INFO ] 2024-12-02 17:03:00.907 - [任务 1][CAR_CUSTOMER] - set disable fzs zip ok 
[INFO ] 2024-12-02 17:03:00.910 - [任务 1][CAR_CUSTOMER] - set fzs socket timeout 100 ok 
[INFO ] 2024-12-02 17:03:01.117 - [任务 1][CAR_CUSTOMER] - fzs set TAPDATA.CAR_CUSTOMER|@2b8ee078 table and bgn scn ok 
[INFO ] 2024-12-02 17:03:02.127 - [任务 1][CAR_CUSTOMER] - fzs service started 
[INFO ] 2024-12-02 17:03:13.108 - [任务 1][CAR_CUSTOMER] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-12-02 17:10:14.277 - [任务 1][CAR_CUSTOMER] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.net.SocketException: Socket closed
	java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:118)
	java.net.SocketOutputStream.write(SocketOutputStream.java:143)
	io.tapdata.connector.oracle.cdc.bridge.BridgeService.pullErrorMsg(BridgeService.java:242)
	io.tapdata.connector.oracle.cdc.bridge.BridgeService.pullRedoLog(BridgeService.java:147)
	io.tapdata.connector.oracle.cdc.bridge.BridgeLogMiner.startMiner(BridgeLogMiner.java:131)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-12-02 17:11:26.733 - [任务 1][CAR_CUSTOMER] - Log Miner is shutting down... 
[INFO ] 2024-12-02 17:11:42.309 - [任务 1][CAR_CUSTOMER] - setting fzs taskId 21444aeca244476b9b625255a69ef946 
[INFO ] 2024-12-02 17:11:42.326 - [任务 1][CAR_CUSTOMER] - fzs set 21444aeca244476b9b625255a69ef946 taskId ok 
[INFO ] 2024-12-02 17:11:42.327 - [任务 1][CAR_CUSTOMER] - set save fzs time ok 
[INFO ] 2024-12-02 17:11:42.344 - [任务 1][CAR_CUSTOMER] - set fzs data format ok 
[INFO ] 2024-12-02 17:11:42.344 - [任务 1][CAR_CUSTOMER] - fzs clear task ok 
[INFO ] 2024-12-02 17:11:52.834 - [任务 1][CAR_CUSTOMER] - setting fzs taskId 21444aeca244476b9b625255a69ef946 
[INFO ] 2024-12-02 17:11:52.836 - [任务 1][CAR_CUSTOMER] - fzs set 21444aeca244476b9b625255a69ef946 taskId ok 
[INFO ] 2024-12-02 17:11:52.851 - [任务 1][CAR_CUSTOMER] - set save fzs time ok 
[INFO ] 2024-12-02 17:11:52.852 - [任务 1][CAR_CUSTOMER] - set fzs data format ok 
[INFO ] 2024-12-02 17:11:52.864 - [任务 1][CAR_CUSTOMER] - set disable fzs zip ok 
[INFO ] 2024-12-02 17:11:52.865 - [任务 1][CAR_CUSTOMER] - set fzs socket timeout 100 ok 
[INFO ] 2024-12-02 17:11:53.073 - [任务 1][CAR_CUSTOMER] - fzs set TAPDATA.CAR_CUSTOMER|@2b8ee161 table and bgn scn ok 
[INFO ] 2024-12-02 17:11:53.885 - [任务 1][CAR_CUSTOMER] - fzs service started 
[INFO ] 2024-12-02 17:12:37.982 - [任务 1][CAR_CUSTOMER] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-12-02 17:13:50.059 - [任务 1][CAR_CUSTOMER] - Node CAR_CUSTOMER[a9dca719-204a-415a-b9b6-c7cd1ff68cf9] running status set to false 
[INFO ] 2024-12-02 17:13:50.266 - [任务 1][CAR_CUSTOMER] - Log Miner is shutting down... 
[INFO ] 2024-12-02 17:13:51.083 - [任务 1][CAR_CUSTOMER] - Incremental sync completed 
[INFO ] 2024-12-02 17:14:00.093 - [任务 1][CAR_CUSTOMER] - setting fzs taskId 21444aeca244476b9b625255a69ef946 
[INFO ] 2024-12-02 17:14:00.121 - [任务 1][CAR_CUSTOMER] - fzs set 21444aeca244476b9b625255a69ef946 taskId ok 
[INFO ] 2024-12-02 17:14:00.121 - [任务 1][CAR_CUSTOMER] - set save fzs time ok 
[INFO ] 2024-12-02 17:14:00.148 - [任务 1][CAR_CUSTOMER] - set fzs data format ok 
[INFO ] 2024-12-02 17:14:00.149 - [任务 1][CAR_CUSTOMER] - fzs clear task ok 
[INFO ] 2024-12-02 17:14:10.193 - [任务 1][CAR_CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-a9dca719-204a-415a-b9b6-c7cd1ff68cf9 
[INFO ] 2024-12-02 17:14:10.195 - [任务 1][CAR_CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-a9dca719-204a-415a-b9b6-c7cd1ff68cf9 
[INFO ] 2024-12-02 17:14:10.196 - [任务 1][CAR_CUSTOMER] - Node CAR_CUSTOMER[a9dca719-204a-415a-b9b6-c7cd1ff68cf9] schema data cleaned 
[INFO ] 2024-12-02 17:14:10.212 - [任务 1][CAR_CUSTOMER] - Node CAR_CUSTOMER[a9dca719-204a-415a-b9b6-c7cd1ff68cf9] monitor closed 
[INFO ] 2024-12-02 17:14:10.212 - [任务 1][CAR_CUSTOMER] - Node CAR_CUSTOMER[a9dca719-204a-415a-b9b6-c7cd1ff68cf9] close complete, cost 20152 ms 
[INFO ] 2024-12-02 17:14:10.244 - [任务 1][testCar] - Node testCar[bf19f20f-bc59-4898-9170-414e7ec860df] running status set to false 
[INFO ] 2024-12-02 17:14:10.245 - [任务 1][testCar] - PDK connector node stopped: HazelcastTargetPdkDataNode-bf19f20f-bc59-4898-9170-414e7ec860df 
[INFO ] 2024-12-02 17:14:10.245 - [任务 1][testCar] - PDK connector node released: HazelcastTargetPdkDataNode-bf19f20f-bc59-4898-9170-414e7ec860df 
[INFO ] 2024-12-02 17:14:10.245 - [任务 1][testCar] - Node testCar[bf19f20f-bc59-4898-9170-414e7ec860df] schema data cleaned 
[INFO ] 2024-12-02 17:14:10.246 - [任务 1][testCar] - Node testCar[bf19f20f-bc59-4898-9170-414e7ec860df] monitor closed 
[INFO ] 2024-12-02 17:14:10.455 - [任务 1][testCar] - Node testCar[bf19f20f-bc59-4898-9170-414e7ec860df] close complete, cost 34 ms 
[INFO ] 2024-12-02 17:14:12.457 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-02 17:14:12.458 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@243803b5 
[INFO ] 2024-12-02 17:14:12.589 - [任务 1] - Stop task milestones: 674d76cb512c440810fee39c(任务 1)  
[INFO ] 2024-12-02 17:14:12.589 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-12-02 17:14:12.624 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-12-02 17:14:12.625 - [任务 1] - Remove memory task client succeed, task: 任务 1[674d76cb512c440810fee39c] 
[INFO ] 2024-12-02 17:14:12.625 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[674d76cb512c440810fee39c] 
