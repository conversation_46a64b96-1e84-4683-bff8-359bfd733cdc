[INFO ] 2024-03-28 15:10:38.409 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:10:38.410 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:10:38.410 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:10:38.410 - [orders(100)][b6d7adb6-6c12-4d6b-a766-3c184984c2bf] - Node b6d7adb6-6c12-4d6b-a766-3c184984c2bf[b6d7adb6-6c12-4d6b-a766-3c184984c2bf] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:10:38.410 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:10:38.411 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:10:38.412 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:10:38.412 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:10:38.412 - [orders(100)][b6d7adb6-6c12-4d6b-a766-3c184984c2bf] - Node b6d7adb6-6c12-4d6b-a766-3c184984c2bf[b6d7adb6-6c12-4d6b-a766-3c184984c2bf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:10:38.467 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:10:38.471 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:10:38.471 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3a8473e2 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3a8473e2 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3a8473e2 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:10:39.015 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:10:39.048 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] running status set to false 
[INFO ] 2024-03-28 15:10:39.050 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] running status set to false 
[INFO ] 2024-03-28 15:10:39.050 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] running status set to false 
[INFO ] 2024-03-28 15:10:39.050 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:10:39.050 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:10:39.050 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] schema data cleaned 
[INFO ] 2024-03-28 15:10:39.050 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] monitor closed 
[INFO ] 2024-03-28 15:10:39.050 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] close complete, cost 30 ms 
[INFO ] 2024-03-28 15:10:39.124 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] schema data cleaned 
[INFO ] 2024-03-28 15:10:39.124 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] schema data cleaned 
[INFO ] 2024-03-28 15:10:39.124 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] monitor closed 
[INFO ] 2024-03-28 15:10:39.124 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] monitor closed 
[INFO ] 2024-03-28 15:10:39.124 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] close complete, cost 82 ms 
[INFO ] 2024-03-28 15:10:39.124 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] close complete, cost 77 ms 
[INFO ] 2024-03-28 15:10:41.004 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] running status set to false 
[INFO ] 2024-03-28 15:10:41.004 - [orders(100)][b6d7adb6-6c12-4d6b-a766-3c184984c2bf] - Node b6d7adb6-6c12-4d6b-a766-3c184984c2bf[b6d7adb6-6c12-4d6b-a766-3c184984c2bf] running status set to false 
[INFO ] 2024-03-28 15:10:41.009 - [orders(100)][b6d7adb6-6c12-4d6b-a766-3c184984c2bf] - Node b6d7adb6-6c12-4d6b-a766-3c184984c2bf[b6d7adb6-6c12-4d6b-a766-3c184984c2bf] schema data cleaned 
[INFO ] 2024-03-28 15:10:41.010 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] schema data cleaned 
[INFO ] 2024-03-28 15:10:41.010 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] monitor closed 
[INFO ] 2024-03-28 15:10:41.010 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] close complete, cost 12 ms 
[INFO ] 2024-03-28 15:10:41.010 - [orders(100)][b6d7adb6-6c12-4d6b-a766-3c184984c2bf] - Node b6d7adb6-6c12-4d6b-a766-3c184984c2bf[b6d7adb6-6c12-4d6b-a766-3c184984c2bf] monitor closed 
[INFO ] 2024-03-28 15:10:41.011 - [orders(100)][b6d7adb6-6c12-4d6b-a766-3c184984c2bf] - Node b6d7adb6-6c12-4d6b-a766-3c184984c2bf[b6d7adb6-6c12-4d6b-a766-3c184984c2bf] close complete, cost 10 ms 
[INFO ] 2024-03-28 15:10:41.014 - [orders(100)] - load tapTable task 660517eea90a2b08fd2ae4bd-b6d7adb6-6c12-4d6b-a766-3c184984c2bf complete, cost 2690ms 
[INFO ] 2024-03-28 15:11:01.862 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:01.862 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:01.862 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:01.864 - [orders(100)][97200490-94ad-42d4-97d8-db48a67150dd] - Node 97200490-94ad-42d4-97d8-db48a67150dd[97200490-94ad-42d4-97d8-db48a67150dd] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:11:01.864 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:01.864 - [orders(100)][97200490-94ad-42d4-97d8-db48a67150dd] - Node 97200490-94ad-42d4-97d8-db48a67150dd[97200490-94ad-42d4-97d8-db48a67150dd] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:01.865 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:01.865 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:01.872 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:01.910 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:01.910 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:01.910 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:01.910 - [orders(100)][9dfd2f62-69a1-4ea9-a299-80fa2259b95b] - Node 9dfd2f62-69a1-4ea9-a299-80fa2259b95b[9dfd2f62-69a1-4ea9-a299-80fa2259b95b] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:11:01.910 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:01.910 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:01.911 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:01.911 - [orders(100)][9dfd2f62-69a1-4ea9-a299-80fa2259b95b] - Node 9dfd2f62-69a1-4ea9-a299-80fa2259b95b[9dfd2f62-69a1-4ea9-a299-80fa2259b95b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:01.924 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:01.924 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:01.924 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:01.924 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:01.925 - [orders(100)][e586e5fa-0a90-4964-a873-3e21633f6ec0] - Node e586e5fa-0a90-4964-a873-3e21633f6ec0[e586e5fa-0a90-4964-a873-3e21633f6ec0] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:11:01.929 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:01.929 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:01.929 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:01.929 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:01.929 - [orders(100)][e586e5fa-0a90-4964-a873-3e21633f6ec0] - Node e586e5fa-0a90-4964-a873-3e21633f6ec0[e586e5fa-0a90-4964-a873-3e21633f6ec0] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:01.929 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:01.929 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:01.929 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:01.936 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 15:11:01.936 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 15:11:01.963 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:11:01.965 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@97796ae error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@97796ae error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@97796ae error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-28 15:11:01.966 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3591da97 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3591da97 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3591da97 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-28 15:11:01.966 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@34266ff1 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@34266ff1 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@34266ff1 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:11:02.138 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:11:02.163 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] running status set to false 
[INFO ] 2024-03-28 15:11:02.163 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] running status set to false 
[INFO ] 2024-03-28 15:11:02.168 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] running status set to false 
[INFO ] 2024-03-28 15:11:02.172 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:02.172 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:02.172 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] schema data cleaned 
[INFO ] 2024-03-28 15:11:02.172 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] monitor closed 
[INFO ] 2024-03-28 15:11:02.173 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] close complete, cost 32 ms 
[INFO ] 2024-03-28 15:11:02.235 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] schema data cleaned 
[INFO ] 2024-03-28 15:11:02.235 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] schema data cleaned 
[INFO ] 2024-03-28 15:11:02.235 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] monitor closed 
[INFO ] 2024-03-28 15:11:02.235 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] monitor closed 
[INFO ] 2024-03-28 15:11:02.236 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] close complete, cost 88 ms 
[INFO ] 2024-03-28 15:11:02.284 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] close complete, cost 72 ms 
[WARN ] 2024-03-28 15:11:02.284 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:11:02.303 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] running status set to false 
[INFO ] 2024-03-28 15:11:02.303 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:02.303 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:02.303 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] schema data cleaned 
[INFO ] 2024-03-28 15:11:02.308 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] monitor closed 
[INFO ] 2024-03-28 15:11:02.308 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] close complete, cost 17 ms 
[INFO ] 2024-03-28 15:11:02.308 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] running status set to false 
[INFO ] 2024-03-28 15:11:02.308 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] running status set to false 
[INFO ] 2024-03-28 15:11:02.404 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] schema data cleaned 
[INFO ] 2024-03-28 15:11:02.404 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] schema data cleaned 
[INFO ] 2024-03-28 15:11:02.404 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] monitor closed 
[INFO ] 2024-03-28 15:11:02.404 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] monitor closed 
[INFO ] 2024-03-28 15:11:02.404 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] close complete, cost 95 ms 
[INFO ] 2024-03-28 15:11:02.404 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] close complete, cost 97 ms 
[WARN ] 2024-03-28 15:11:02.476 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:11:02.500 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] running status set to false 
[INFO ] 2024-03-28 15:11:02.505 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:02.505 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] running status set to false 
[INFO ] 2024-03-28 15:11:02.505 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] running status set to false 
[INFO ] 2024-03-28 15:11:02.506 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:02.506 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] schema data cleaned 
[INFO ] 2024-03-28 15:11:02.506 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] monitor closed 
[INFO ] 2024-03-28 15:11:02.581 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] close complete, cost 35 ms 
[INFO ] 2024-03-28 15:11:02.581 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] schema data cleaned 
[INFO ] 2024-03-28 15:11:02.581 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] schema data cleaned 
[INFO ] 2024-03-28 15:11:02.581 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] monitor closed 
[INFO ] 2024-03-28 15:11:02.583 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] monitor closed 
[INFO ] 2024-03-28 15:11:02.583 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] close complete, cost 81 ms 
[INFO ] 2024-03-28 15:11:02.583 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] close complete, cost 77 ms 
[INFO ] 2024-03-28 15:11:02.696 - [orders(100)][168a7c9d-cf2c-4a60-bc07-55ff612a2353] - Node 168a7c9d-cf2c-4a60-bc07-55ff612a2353[168a7c9d-cf2c-4a60-bc07-55ff612a2353] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:11:02.696 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:02.696 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:02.696 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:02.696 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:02.696 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:02.696 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:02.696 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:02.696 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:02.696 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:02.696 - [orders(100)][eab881e5-0838-402a-9deb-cf91496915fc] - Node eab881e5-0838-402a-9deb-cf91496915fc[eab881e5-0838-402a-9deb-cf91496915fc] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:11:02.697 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:02.697 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:02.697 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:02.697 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:02.697 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:02.700 - [orders(100)][168a7c9d-cf2c-4a60-bc07-55ff612a2353] - Node 168a7c9d-cf2c-4a60-bc07-55ff612a2353[168a7c9d-cf2c-4a60-bc07-55ff612a2353] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:02.700 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:02.700 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:02.700 - [orders(100)][eab881e5-0838-402a-9deb-cf91496915fc] - Node eab881e5-0838-402a-9deb-cf91496915fc[eab881e5-0838-402a-9deb-cf91496915fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:02.798 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 15:11:02.800 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 15:11:02.808 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:02.808 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:02.819 - [orders(100)][9621eea2-135d-481a-8ed3-989b03b196ad] - Node 9621eea2-135d-481a-8ed3-989b03b196ad[9621eea2-135d-481a-8ed3-989b03b196ad] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:11:02.819 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:02.819 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:02.819 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:02.819 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:02.819 - [orders(100)][9621eea2-135d-481a-8ed3-989b03b196ad] - Node 9621eea2-135d-481a-8ed3-989b03b196ad[9621eea2-135d-481a-8ed3-989b03b196ad] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:02.819 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] preload schema finished, cost 2 ms 
[INFO ] 2024-03-28 15:11:02.847 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 15:11:02.847 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:11:02.855 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1133871a error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1133871a error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1133871a error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-28 15:11:02.857 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2ca041f7 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2ca041f7 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2ca041f7 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-28 15:11:03.021 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@51710326 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@51710326 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@51710326 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:11:03.022 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:11:03.029 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] running status set to false 
[INFO ] 2024-03-28 15:11:03.029 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] running status set to false 
[INFO ] 2024-03-28 15:11:03.030 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] running status set to false 
[INFO ] 2024-03-28 15:11:03.049 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:03.049 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:03.050 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] schema data cleaned 
[INFO ] 2024-03-28 15:11:03.050 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] monitor closed 
[INFO ] 2024-03-28 15:11:03.262 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] close complete, cost 25 ms 
[INFO ] 2024-03-28 15:11:03.294 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] schema data cleaned 
[INFO ] 2024-03-28 15:11:03.294 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] schema data cleaned 
[INFO ] 2024-03-28 15:11:03.294 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] monitor closed 
[INFO ] 2024-03-28 15:11:03.294 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] monitor closed 
[INFO ] 2024-03-28 15:11:03.295 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] close complete, cost 266 ms 
[INFO ] 2024-03-28 15:11:03.295 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] close complete, cost 264 ms 
[WARN ] 2024-03-28 15:11:03.360 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:11:03.360 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] running status set to false 
[INFO ] 2024-03-28 15:11:03.396 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] running status set to false 
[INFO ] 2024-03-28 15:11:03.396 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] running status set to false 
[INFO ] 2024-03-28 15:11:03.403 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:03.404 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:03.406 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] schema data cleaned 
[INFO ] 2024-03-28 15:11:03.406 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] monitor closed 
[INFO ] 2024-03-28 15:11:03.407 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] close complete, cost 48 ms 
[INFO ] 2024-03-28 15:11:03.476 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] schema data cleaned 
[INFO ] 2024-03-28 15:11:03.476 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] schema data cleaned 
[INFO ] 2024-03-28 15:11:03.476 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] monitor closed 
[INFO ] 2024-03-28 15:11:03.476 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] monitor closed 
[INFO ] 2024-03-28 15:11:03.480 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] close complete, cost 101 ms 
[INFO ] 2024-03-28 15:11:03.480 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] close complete, cost 108 ms 
[WARN ] 2024-03-28 15:11:03.539 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:11:03.539 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] running status set to false 
[INFO ] 2024-03-28 15:11:03.566 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] running status set to false 
[INFO ] 2024-03-28 15:11:03.566 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] running status set to false 
[INFO ] 2024-03-28 15:11:03.568 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:03.570 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:03.570 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] schema data cleaned 
[INFO ] 2024-03-28 15:11:03.570 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] monitor closed 
[INFO ] 2024-03-28 15:11:03.571 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] close complete, cost 34 ms 
[INFO ] 2024-03-28 15:11:03.619 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] schema data cleaned 
[INFO ] 2024-03-28 15:11:03.619 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] schema data cleaned 
[INFO ] 2024-03-28 15:11:03.619 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] monitor closed 
[INFO ] 2024-03-28 15:11:03.619 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] monitor closed 
[INFO ] 2024-03-28 15:11:03.619 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] close complete, cost 57 ms 
[INFO ] 2024-03-28 15:11:03.821 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] close complete, cost 72 ms 
[INFO ] 2024-03-28 15:11:03.844 - [orders(100)][eb8ab19c-0ce5-4840-9768-b526576dbfe9] - Node eb8ab19c-0ce5-4840-9768-b526576dbfe9[eb8ab19c-0ce5-4840-9768-b526576dbfe9] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:11:03.844 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:03.844 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:03.844 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:03.844 - [orders(100)][eb8ab19c-0ce5-4840-9768-b526576dbfe9] - Node eb8ab19c-0ce5-4840-9768-b526576dbfe9[eb8ab19c-0ce5-4840-9768-b526576dbfe9] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 15:11:03.844 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:03.844 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:03.844 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:03.844 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:03.844 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:03.897 - [orders(100)][e48f1039-c929-457e-97c0-2d4d45e759c4] - Node e48f1039-c929-457e-97c0-2d4d45e759c4[e48f1039-c929-457e-97c0-2d4d45e759c4] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:11:03.898 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:03.898 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:03.898 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:03.898 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:03.898 - [orders(100)][e48f1039-c929-457e-97c0-2d4d45e759c4] - Node e48f1039-c929-457e-97c0-2d4d45e759c4[e48f1039-c929-457e-97c0-2d4d45e759c4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:03.898 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:03.898 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:03.898 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:03.898 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:03.978 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 15:11:03.979 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:11:03.979 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@642ca59e error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@642ca59e error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@642ca59e error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-28 15:11:03.979 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@cf61c1d error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@cf61c1d error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@cf61c1d error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 15:11:03.996 - [orders(100)][bf7c7d74-4d01-4061-b267-06c64381497f] - Node bf7c7d74-4d01-4061-b267-06c64381497f[bf7c7d74-4d01-4061-b267-06c64381497f] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:11:03.996 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:03.996 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:03.996 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:03.996 - [orders(100)][bf7c7d74-4d01-4061-b267-06c64381497f] - Node bf7c7d74-4d01-4061-b267-06c64381497f[bf7c7d74-4d01-4061-b267-06c64381497f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:03.996 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:03.996 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:03.996 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:03.996 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:04.003 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:04.003 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:11:04.111 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@18ef48dd error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@18ef48dd error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@18ef48dd error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:11:04.112 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:11:04.130 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] running status set to false 
[INFO ] 2024-03-28 15:11:04.130 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] running status set to false 
[INFO ] 2024-03-28 15:11:04.133 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] running status set to false 
[INFO ] 2024-03-28 15:11:04.133 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:04.134 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:04.134 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] schema data cleaned 
[INFO ] 2024-03-28 15:11:04.134 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] monitor closed 
[INFO ] 2024-03-28 15:11:04.134 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] close complete, cost 19 ms 
[INFO ] 2024-03-28 15:11:04.176 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] schema data cleaned 
[INFO ] 2024-03-28 15:11:04.176 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] monitor closed 
[INFO ] 2024-03-28 15:11:04.176 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] schema data cleaned 
[INFO ] 2024-03-28 15:11:04.176 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] close complete, cost 45 ms 
[INFO ] 2024-03-28 15:11:04.176 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] monitor closed 
[INFO ] 2024-03-28 15:11:04.176 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] close complete, cost 50 ms 
[WARN ] 2024-03-28 15:11:04.284 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:11:04.284 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] running status set to false 
[INFO ] 2024-03-28 15:11:04.297 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] running status set to false 
[INFO ] 2024-03-28 15:11:04.297 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] running status set to false 
[INFO ] 2024-03-28 15:11:04.297 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:04.299 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:04.299 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] schema data cleaned 
[INFO ] 2024-03-28 15:11:04.299 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] monitor closed 
[INFO ] 2024-03-28 15:11:04.374 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] close complete, cost 16 ms 
[INFO ] 2024-03-28 15:11:04.374 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] schema data cleaned 
[INFO ] 2024-03-28 15:11:04.374 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] schema data cleaned 
[INFO ] 2024-03-28 15:11:04.374 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] monitor closed 
[INFO ] 2024-03-28 15:11:04.374 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] monitor closed 
[INFO ] 2024-03-28 15:11:04.375 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] close complete, cost 80 ms 
[INFO ] 2024-03-28 15:11:04.375 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] close complete, cost 87 ms 
[WARN ] 2024-03-28 15:11:04.453 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:11:04.453 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] running status set to false 
[INFO ] 2024-03-28 15:11:04.467 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] running status set to false 
[INFO ] 2024-03-28 15:11:04.467 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:04.467 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] running status set to false 
[INFO ] 2024-03-28 15:11:04.468 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:04.468 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] schema data cleaned 
[INFO ] 2024-03-28 15:11:04.468 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] monitor closed 
[INFO ] 2024-03-28 15:11:04.525 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] close complete, cost 18 ms 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] schema data cleaned 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] schema data cleaned 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] monitor closed 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] monitor closed 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] close complete, cost 54 ms 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] close complete, cost 70 ms 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] running status set to false 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] schema data cleaned 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] monitor closed 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] running status set to false 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] close complete, cost 0 ms 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] schema data cleaned 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] monitor closed 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] close complete, cost 0 ms 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] running status set to false 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] schema data cleaned 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] monitor closed 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] close complete, cost 0 ms 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][97200490-94ad-42d4-97d8-db48a67150dd] - Node 97200490-94ad-42d4-97d8-db48a67150dd[97200490-94ad-42d4-97d8-db48a67150dd] running status set to false 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][97200490-94ad-42d4-97d8-db48a67150dd] - Node 97200490-94ad-42d4-97d8-db48a67150dd[97200490-94ad-42d4-97d8-db48a67150dd] schema data cleaned 
[INFO ] 2024-03-28 15:11:04.531 - [orders(100)][97200490-94ad-42d4-97d8-db48a67150dd] - Node 97200490-94ad-42d4-97d8-db48a67150dd[97200490-94ad-42d4-97d8-db48a67150dd] monitor closed 
[INFO ] 2024-03-28 15:11:04.532 - [orders(100)][9dfd2f62-69a1-4ea9-a299-80fa2259b95b] - Node 9dfd2f62-69a1-4ea9-a299-80fa2259b95b[9dfd2f62-69a1-4ea9-a299-80fa2259b95b] running status set to false 
[INFO ] 2024-03-28 15:11:04.532 - [orders(100)][e586e5fa-0a90-4964-a873-3e21633f6ec0] - Node e586e5fa-0a90-4964-a873-3e21633f6ec0[e586e5fa-0a90-4964-a873-3e21633f6ec0] running status set to false 
[INFO ] 2024-03-28 15:11:04.532 - [orders(100)][9dfd2f62-69a1-4ea9-a299-80fa2259b95b] - Node 9dfd2f62-69a1-4ea9-a299-80fa2259b95b[9dfd2f62-69a1-4ea9-a299-80fa2259b95b] schema data cleaned 
[INFO ] 2024-03-28 15:11:04.533 - [orders(100)][e586e5fa-0a90-4964-a873-3e21633f6ec0] - Node e586e5fa-0a90-4964-a873-3e21633f6ec0[e586e5fa-0a90-4964-a873-3e21633f6ec0] schema data cleaned 
[INFO ] 2024-03-28 15:11:04.533 - [orders(100)][9dfd2f62-69a1-4ea9-a299-80fa2259b95b] - Node 9dfd2f62-69a1-4ea9-a299-80fa2259b95b[9dfd2f62-69a1-4ea9-a299-80fa2259b95b] monitor closed 
[INFO ] 2024-03-28 15:11:04.533 - [orders(100)][e586e5fa-0a90-4964-a873-3e21633f6ec0] - Node e586e5fa-0a90-4964-a873-3e21633f6ec0[e586e5fa-0a90-4964-a873-3e21633f6ec0] monitor closed 
[INFO ] 2024-03-28 15:11:04.533 - [orders(100)][97200490-94ad-42d4-97d8-db48a67150dd] - Node 97200490-94ad-42d4-97d8-db48a67150dd[97200490-94ad-42d4-97d8-db48a67150dd] close complete, cost 1 ms 
[INFO ] 2024-03-28 15:11:04.533 - [orders(100)][e586e5fa-0a90-4964-a873-3e21633f6ec0] - Node e586e5fa-0a90-4964-a873-3e21633f6ec0[e586e5fa-0a90-4964-a873-3e21633f6ec0] close complete, cost 0 ms 
[INFO ] 2024-03-28 15:11:04.541 - [orders(100)][9dfd2f62-69a1-4ea9-a299-80fa2259b95b] - Node 9dfd2f62-69a1-4ea9-a299-80fa2259b95b[9dfd2f62-69a1-4ea9-a299-80fa2259b95b] close complete, cost 2 ms 
[INFO ] 2024-03-28 15:11:04.541 - [orders(100)] - load tapTable task 660517eea90a2b08fd2ae4bd-9dfd2f62-69a1-4ea9-a299-80fa2259b95b complete, cost 2713ms 
[INFO ] 2024-03-28 15:11:04.542 - [orders(100)] - load tapTable task 660517eea90a2b08fd2ae4bd-97200490-94ad-42d4-97d8-db48a67150dd complete, cost 2917ms 
[INFO ] 2024-03-28 15:11:04.542 - [orders(100)] - load tapTable task 660517eea90a2b08fd2ae4bd-e586e5fa-0a90-4964-a873-3e21633f6ec0 complete, cost 2685ms 
[INFO ] 2024-03-28 15:11:05.438 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] running status set to false 
[INFO ] 2024-03-28 15:11:05.438 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] running status set to false 
[INFO ] 2024-03-28 15:11:05.438 - [orders(100)][168a7c9d-cf2c-4a60-bc07-55ff612a2353] - Node 168a7c9d-cf2c-4a60-bc07-55ff612a2353[168a7c9d-cf2c-4a60-bc07-55ff612a2353] running status set to false 
[INFO ] 2024-03-28 15:11:05.440 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] schema data cleaned 
[INFO ] 2024-03-28 15:11:05.440 - [orders(100)][eab881e5-0838-402a-9deb-cf91496915fc] - Node eab881e5-0838-402a-9deb-cf91496915fc[eab881e5-0838-402a-9deb-cf91496915fc] running status set to false 
[INFO ] 2024-03-28 15:11:05.440 - [orders(100)][168a7c9d-cf2c-4a60-bc07-55ff612a2353] - Node 168a7c9d-cf2c-4a60-bc07-55ff612a2353[168a7c9d-cf2c-4a60-bc07-55ff612a2353] schema data cleaned 
[INFO ] 2024-03-28 15:11:05.440 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] schema data cleaned 
[INFO ] 2024-03-28 15:11:05.440 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] running status set to false 
[INFO ] 2024-03-28 15:11:05.440 - [orders(100)][9621eea2-135d-481a-8ed3-989b03b196ad] - Node 9621eea2-135d-481a-8ed3-989b03b196ad[9621eea2-135d-481a-8ed3-989b03b196ad] running status set to false 
[INFO ] 2024-03-28 15:11:05.440 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] monitor closed 
[INFO ] 2024-03-28 15:11:05.440 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] monitor closed 
[INFO ] 2024-03-28 15:11:05.440 - [orders(100)][eab881e5-0838-402a-9deb-cf91496915fc] - Node eab881e5-0838-402a-9deb-cf91496915fc[eab881e5-0838-402a-9deb-cf91496915fc] schema data cleaned 
[INFO ] 2024-03-28 15:11:05.441 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] schema data cleaned 
[INFO ] 2024-03-28 15:11:05.441 - [orders(100)][9621eea2-135d-481a-8ed3-989b03b196ad] - Node 9621eea2-135d-481a-8ed3-989b03b196ad[9621eea2-135d-481a-8ed3-989b03b196ad] schema data cleaned 
[INFO ] 2024-03-28 15:11:05.441 - [orders(100)][168a7c9d-cf2c-4a60-bc07-55ff612a2353] - Node 168a7c9d-cf2c-4a60-bc07-55ff612a2353[168a7c9d-cf2c-4a60-bc07-55ff612a2353] monitor closed 
[INFO ] 2024-03-28 15:11:05.441 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] monitor closed 
[INFO ] 2024-03-28 15:11:05.441 - [orders(100)][9621eea2-135d-481a-8ed3-989b03b196ad] - Node 9621eea2-135d-481a-8ed3-989b03b196ad[9621eea2-135d-481a-8ed3-989b03b196ad] monitor closed 
[INFO ] 2024-03-28 15:11:05.441 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] close complete, cost 15 ms 
[INFO ] 2024-03-28 15:11:05.441 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] close complete, cost 15 ms 
[INFO ] 2024-03-28 15:11:05.441 - [orders(100)][eab881e5-0838-402a-9deb-cf91496915fc] - Node eab881e5-0838-402a-9deb-cf91496915fc[eab881e5-0838-402a-9deb-cf91496915fc] monitor closed 
[INFO ] 2024-03-28 15:11:05.441 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] close complete, cost 16 ms 
[INFO ] 2024-03-28 15:11:05.441 - [orders(100)][9621eea2-135d-481a-8ed3-989b03b196ad] - Node 9621eea2-135d-481a-8ed3-989b03b196ad[9621eea2-135d-481a-8ed3-989b03b196ad] close complete, cost 11 ms 
[INFO ] 2024-03-28 15:11:05.441 - [orders(100)][168a7c9d-cf2c-4a60-bc07-55ff612a2353] - Node 168a7c9d-cf2c-4a60-bc07-55ff612a2353[168a7c9d-cf2c-4a60-bc07-55ff612a2353] close complete, cost 15 ms 
[INFO ] 2024-03-28 15:11:05.441 - [orders(100)][eab881e5-0838-402a-9deb-cf91496915fc] - Node eab881e5-0838-402a-9deb-cf91496915fc[eab881e5-0838-402a-9deb-cf91496915fc] close complete, cost 12 ms 
[INFO ] 2024-03-28 15:11:05.443 - [orders(100)] - load tapTable task 660517eea90a2b08fd2ae4bd-9621eea2-135d-481a-8ed3-989b03b196ad complete, cost 2738ms 
[INFO ] 2024-03-28 15:11:05.443 - [orders(100)] - load tapTable task 660517eea90a2b08fd2ae4bd-eab881e5-0838-402a-9deb-cf91496915fc complete, cost 2852ms 
[INFO ] 2024-03-28 15:11:05.443 - [orders(100)] - load tapTable task 660517eea90a2b08fd2ae4bd-168a7c9d-cf2c-4a60-bc07-55ff612a2353 complete, cost 2854ms 
[INFO ] 2024-03-28 15:11:06.506 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] running status set to false 
[INFO ] 2024-03-28 15:11:06.515 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] running status set to false 
[INFO ] 2024-03-28 15:11:06.515 - [orders(100)][eb8ab19c-0ce5-4840-9768-b526576dbfe9] - Node eb8ab19c-0ce5-4840-9768-b526576dbfe9[eb8ab19c-0ce5-4840-9768-b526576dbfe9] running status set to false 
[INFO ] 2024-03-28 15:11:06.517 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] schema data cleaned 
[INFO ] 2024-03-28 15:11:06.518 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] schema data cleaned 
[INFO ] 2024-03-28 15:11:06.518 - [orders(100)][eb8ab19c-0ce5-4840-9768-b526576dbfe9] - Node eb8ab19c-0ce5-4840-9768-b526576dbfe9[eb8ab19c-0ce5-4840-9768-b526576dbfe9] schema data cleaned 
[INFO ] 2024-03-28 15:11:06.518 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] monitor closed 
[INFO ] 2024-03-28 15:11:06.518 - [orders(100)][e48f1039-c929-457e-97c0-2d4d45e759c4] - Node e48f1039-c929-457e-97c0-2d4d45e759c4[e48f1039-c929-457e-97c0-2d4d45e759c4] running status set to false 
[INFO ] 2024-03-28 15:11:06.518 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] close complete, cost 15 ms 
[INFO ] 2024-03-28 15:11:06.518 - [orders(100)][e48f1039-c929-457e-97c0-2d4d45e759c4] - Node e48f1039-c929-457e-97c0-2d4d45e759c4[e48f1039-c929-457e-97c0-2d4d45e759c4] schema data cleaned 
[INFO ] 2024-03-28 15:11:06.518 - [orders(100)][eb8ab19c-0ce5-4840-9768-b526576dbfe9] - Node eb8ab19c-0ce5-4840-9768-b526576dbfe9[eb8ab19c-0ce5-4840-9768-b526576dbfe9] monitor closed 
[INFO ] 2024-03-28 15:11:06.518 - [orders(100)][e48f1039-c929-457e-97c0-2d4d45e759c4] - Node e48f1039-c929-457e-97c0-2d4d45e759c4[e48f1039-c929-457e-97c0-2d4d45e759c4] monitor closed 
[INFO ] 2024-03-28 15:11:06.518 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] monitor closed 
[INFO ] 2024-03-28 15:11:06.518 - [orders(100)][e48f1039-c929-457e-97c0-2d4d45e759c4] - Node e48f1039-c929-457e-97c0-2d4d45e759c4[e48f1039-c929-457e-97c0-2d4d45e759c4] close complete, cost 1 ms 
[INFO ] 2024-03-28 15:11:06.519 - [orders(100)][eb8ab19c-0ce5-4840-9768-b526576dbfe9] - Node eb8ab19c-0ce5-4840-9768-b526576dbfe9[eb8ab19c-0ce5-4840-9768-b526576dbfe9] close complete, cost 15 ms 
[INFO ] 2024-03-28 15:11:06.519 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] close complete, cost 16 ms 
[INFO ] 2024-03-28 15:11:06.519 - [orders(100)] - load tapTable task 660517eea90a2b08fd2ae4bd-e48f1039-c929-457e-97c0-2d4d45e759c4 complete, cost 2674ms 
[INFO ] 2024-03-28 15:11:06.520 - [orders(100)] - load tapTable task 660517eea90a2b08fd2ae4bd-eb8ab19c-0ce5-4840-9768-b526576dbfe9 complete, cost 2727ms 
[INFO ] 2024-03-28 15:11:06.558 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] running status set to false 
[INFO ] 2024-03-28 15:11:06.558 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] schema data cleaned 
[INFO ] 2024-03-28 15:11:06.559 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] monitor closed 
[INFO ] 2024-03-28 15:11:06.559 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] close complete, cost 0 ms 
[INFO ] 2024-03-28 15:11:06.560 - [orders(100)][bf7c7d74-4d01-4061-b267-06c64381497f] - Node bf7c7d74-4d01-4061-b267-06c64381497f[bf7c7d74-4d01-4061-b267-06c64381497f] running status set to false 
[INFO ] 2024-03-28 15:11:06.560 - [orders(100)][bf7c7d74-4d01-4061-b267-06c64381497f] - Node bf7c7d74-4d01-4061-b267-06c64381497f[bf7c7d74-4d01-4061-b267-06c64381497f] schema data cleaned 
[INFO ] 2024-03-28 15:11:06.560 - [orders(100)][bf7c7d74-4d01-4061-b267-06c64381497f] - Node bf7c7d74-4d01-4061-b267-06c64381497f[bf7c7d74-4d01-4061-b267-06c64381497f] monitor closed 
[INFO ] 2024-03-28 15:11:06.560 - [orders(100)][bf7c7d74-4d01-4061-b267-06c64381497f] - Node bf7c7d74-4d01-4061-b267-06c64381497f[bf7c7d74-4d01-4061-b267-06c64381497f] close complete, cost 0 ms 
[INFO ] 2024-03-28 15:11:06.761 - [orders(100)] - load tapTable task 660517eea90a2b08fd2ae4bd-bf7c7d74-4d01-4061-b267-06c64381497f complete, cost 2600ms 
[INFO ] 2024-03-28 15:11:10.573 - [orders(100)][055f8cf9-6855-4780-bf92-8991a24a60dd] - Node 055f8cf9-6855-4780-bf92-8991a24a60dd[055f8cf9-6855-4780-bf92-8991a24a60dd] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:11:10.573 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:10.573 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:10.573 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:10.573 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:10.573 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:10.573 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:10.573 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:10.574 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:10.574 - [orders(100)][055f8cf9-6855-4780-bf92-8991a24a60dd] - Node 055f8cf9-6855-4780-bf92-8991a24a60dd[055f8cf9-6855-4780-bf92-8991a24a60dd] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:10.627 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:11:10.662 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5aa726ba error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5aa726ba error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5aa726ba error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 15:11:10.663 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:10.663 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:10.663 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:10.663 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:10.664 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:10.664 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:10.664 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:10.664 - [orders(100)][c85b3dd8-1b0b-4e11-820b-cda2d273ad61] - Node c85b3dd8-1b0b-4e11-820b-cda2d273ad61[c85b3dd8-1b0b-4e11-820b-cda2d273ad61] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:11:10.664 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:10.664 - [orders(100)][c85b3dd8-1b0b-4e11-820b-cda2d273ad61] - Node c85b3dd8-1b0b-4e11-820b-cda2d273ad61[c85b3dd8-1b0b-4e11-820b-cda2d273ad61] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:10.677 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:11:10.677 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@353d084b error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@353d084b error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@353d084b error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:11:10.819 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:11:10.819 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] running status set to false 
[INFO ] 2024-03-28 15:11:10.835 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] running status set to false 
[INFO ] 2024-03-28 15:11:10.835 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] running status set to false 
[INFO ] 2024-03-28 15:11:10.842 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:10.843 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:10.843 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] schema data cleaned 
[INFO ] 2024-03-28 15:11:10.843 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] monitor closed 
[INFO ] 2024-03-28 15:11:10.922 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] close complete, cost 28 ms 
[INFO ] 2024-03-28 15:11:10.922 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] schema data cleaned 
[INFO ] 2024-03-28 15:11:10.922 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] schema data cleaned 
[INFO ] 2024-03-28 15:11:10.922 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] monitor closed 
[INFO ] 2024-03-28 15:11:10.922 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] monitor closed 
[INFO ] 2024-03-28 15:11:10.923 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] close complete, cost 102 ms 
[INFO ] 2024-03-28 15:11:10.923 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] close complete, cost 104 ms 
[WARN ] 2024-03-28 15:11:10.969 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:11:10.969 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] running status set to false 
[INFO ] 2024-03-28 15:11:10.978 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] running status set to false 
[INFO ] 2024-03-28 15:11:10.979 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:10.979 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:10.979 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] schema data cleaned 
[INFO ] 2024-03-28 15:11:10.979 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] monitor closed 
[INFO ] 2024-03-28 15:11:10.979 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] close complete, cost 10 ms 
[INFO ] 2024-03-28 15:11:11.027 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] running status set to false 
[INFO ] 2024-03-28 15:11:11.032 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] schema data cleaned 
[INFO ] 2024-03-28 15:11:11.032 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] schema data cleaned 
[INFO ] 2024-03-28 15:11:11.032 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] monitor closed 
[INFO ] 2024-03-28 15:11:11.032 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] monitor closed 
[INFO ] 2024-03-28 15:11:11.032 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] close complete, cost 49 ms 
[INFO ] 2024-03-28 15:11:11.032 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] close complete, cost 45 ms 
[INFO ] 2024-03-28 15:11:13.224 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] running status set to false 
[INFO ] 2024-03-28 15:11:13.226 - [orders(100)][055f8cf9-6855-4780-bf92-8991a24a60dd] - Node 055f8cf9-6855-4780-bf92-8991a24a60dd[055f8cf9-6855-4780-bf92-8991a24a60dd] running status set to false 
[INFO ] 2024-03-28 15:11:13.226 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] running status set to false 
[INFO ] 2024-03-28 15:11:13.226 - [orders(100)][c85b3dd8-1b0b-4e11-820b-cda2d273ad61] - Node c85b3dd8-1b0b-4e11-820b-cda2d273ad61[c85b3dd8-1b0b-4e11-820b-cda2d273ad61] running status set to false 
[INFO ] 2024-03-28 15:11:13.226 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] schema data cleaned 
[INFO ] 2024-03-28 15:11:13.226 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] schema data cleaned 
[INFO ] 2024-03-28 15:11:13.226 - [orders(100)][055f8cf9-6855-4780-bf92-8991a24a60dd] - Node 055f8cf9-6855-4780-bf92-8991a24a60dd[055f8cf9-6855-4780-bf92-8991a24a60dd] schema data cleaned 
[INFO ] 2024-03-28 15:11:13.226 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] monitor closed 
[INFO ] 2024-03-28 15:11:13.226 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] monitor closed 
[INFO ] 2024-03-28 15:11:13.226 - [orders(100)][c85b3dd8-1b0b-4e11-820b-cda2d273ad61] - Node c85b3dd8-1b0b-4e11-820b-cda2d273ad61[c85b3dd8-1b0b-4e11-820b-cda2d273ad61] schema data cleaned 
[INFO ] 2024-03-28 15:11:13.226 - [orders(100)][055f8cf9-6855-4780-bf92-8991a24a60dd] - Node 055f8cf9-6855-4780-bf92-8991a24a60dd[055f8cf9-6855-4780-bf92-8991a24a60dd] monitor closed 
[INFO ] 2024-03-28 15:11:13.227 - [orders(100)][c85b3dd8-1b0b-4e11-820b-cda2d273ad61] - Node c85b3dd8-1b0b-4e11-820b-cda2d273ad61[c85b3dd8-1b0b-4e11-820b-cda2d273ad61] monitor closed 
[INFO ] 2024-03-28 15:11:13.227 - [orders(100)][c85b3dd8-1b0b-4e11-820b-cda2d273ad61] - Node c85b3dd8-1b0b-4e11-820b-cda2d273ad61[c85b3dd8-1b0b-4e11-820b-cda2d273ad61] close complete, cost 23 ms 
[INFO ] 2024-03-28 15:11:13.228 - [orders(100)][055f8cf9-6855-4780-bf92-8991a24a60dd] - Node 055f8cf9-6855-4780-bf92-8991a24a60dd[055f8cf9-6855-4780-bf92-8991a24a60dd] close complete, cost 29 ms 
[INFO ] 2024-03-28 15:11:13.228 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] close complete, cost 27 ms 
[INFO ] 2024-03-28 15:11:13.231 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] close complete, cost 34 ms 
[INFO ] 2024-03-28 15:11:13.232 - [orders(100)] - load tapTable task 660517eea90a2b08fd2ae4bd-055f8cf9-6855-4780-bf92-8991a24a60dd complete, cost 2713ms 
[INFO ] 2024-03-28 15:11:13.438 - [orders(100)] - load tapTable task 660517eea90a2b08fd2ae4bd-c85b3dd8-1b0b-4e11-820b-cda2d273ad61 complete, cost 2607ms 
[INFO ] 2024-03-28 15:11:15.145 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:15.145 - [orders(100)][0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76] - Node 0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76[0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:11:15.145 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:15.145 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:15.145 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:15.147 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 15:11:15.147 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 15:11:15.147 - [orders(100)][0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76] - Node 0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76[0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:15.147 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:15.147 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 15:11:15.181 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:15.181 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:15.181 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:15.181 - [orders(100)][239b8137-4111-4b6b-8d2e-5ded3af9a21c] - Node 239b8137-4111-4b6b-8d2e-5ded3af9a21c[239b8137-4111-4b6b-8d2e-5ded3af9a21c] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:11:15.181 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:15.181 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:15.182 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:15.182 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:15.182 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:15.182 - [orders(100)][239b8137-4111-4b6b-8d2e-5ded3af9a21c] - Node 239b8137-4111-4b6b-8d2e-5ded3af9a21c[239b8137-4111-4b6b-8d2e-5ded3af9a21c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:15.222 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 15:11:15.225 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:11:15.253 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@76eb10e3 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@76eb10e3 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@76eb10e3 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-28 15:11:15.254 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5211d5f7 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5211d5f7 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5211d5f7 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 15:11:15.272 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:15.272 - [orders(100)][cb10439a-8f43-48c9-9e27-b574c24626cf] - Node cb10439a-8f43-48c9-9e27-b574c24626cf[cb10439a-8f43-48c9-9e27-b574c24626cf] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:11:15.272 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:15.273 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:15.273 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:15.273 - [orders(100)][cb10439a-8f43-48c9-9e27-b574c24626cf] - Node cb10439a-8f43-48c9-9e27-b574c24626cf[cb10439a-8f43-48c9-9e27-b574c24626cf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:15.273 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:11:15.273 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:15.273 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:15.279 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:11:15.279 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:11:15.415 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@790786aa error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@790786aa error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@790786aa error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:11:15.416 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:11:15.430 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] running status set to false 
[INFO ] 2024-03-28 15:11:15.436 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] running status set to false 
[INFO ] 2024-03-28 15:11:15.446 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] running status set to false 
[INFO ] 2024-03-28 15:11:15.446 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:15.446 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:15.447 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] schema data cleaned 
[INFO ] 2024-03-28 15:11:15.447 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] monitor closed 
[INFO ] 2024-03-28 15:11:15.447 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] close complete, cost 28 ms 
[INFO ] 2024-03-28 15:11:15.492 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] schema data cleaned 
[INFO ] 2024-03-28 15:11:15.492 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] monitor closed 
[INFO ] 2024-03-28 15:11:15.492 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] close complete, cost 70 ms 
[INFO ] 2024-03-28 15:11:15.492 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] schema data cleaned 
[INFO ] 2024-03-28 15:11:15.492 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] monitor closed 
[INFO ] 2024-03-28 15:11:15.492 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] close complete, cost 51 ms 
[WARN ] 2024-03-28 15:11:15.569 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:11:15.569 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] running status set to false 
[INFO ] 2024-03-28 15:11:15.570 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] running status set to false 
[INFO ] 2024-03-28 15:11:15.581 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:15.582 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] running status set to false 
[INFO ] 2024-03-28 15:11:15.582 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:15.582 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] schema data cleaned 
[INFO ] 2024-03-28 15:11:15.582 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] monitor closed 
[INFO ] 2024-03-28 15:11:15.582 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] close complete, cost 15 ms 
[INFO ] 2024-03-28 15:11:15.625 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] schema data cleaned 
[INFO ] 2024-03-28 15:11:15.625 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] monitor closed 
[INFO ] 2024-03-28 15:11:15.625 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] close complete, cost 56 ms 
[INFO ] 2024-03-28 15:11:15.626 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] schema data cleaned 
[INFO ] 2024-03-28 15:11:15.626 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] monitor closed 
[INFO ] 2024-03-28 15:11:15.626 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] close complete, cost 45 ms 
[WARN ] 2024-03-28 15:11:15.748 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:11:15.748 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] running status set to false 
[INFO ] 2024-03-28 15:11:15.770 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] running status set to false 
[INFO ] 2024-03-28 15:11:15.772 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:15.772 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] running status set to false 
[INFO ] 2024-03-28 15:11:15.772 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc 
[INFO ] 2024-03-28 15:11:15.772 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] schema data cleaned 
[INFO ] 2024-03-28 15:11:15.772 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] monitor closed 
[INFO ] 2024-03-28 15:11:15.840 - [orders(100)][Order Details] - Node Order Details[cd6c4bdd-f4c9-4eb8-98c3-3ca0688bc1dc] close complete, cost 27 ms 
[INFO ] 2024-03-28 15:11:15.841 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] schema data cleaned 
[INFO ] 2024-03-28 15:11:15.841 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] schema data cleaned 
[INFO ] 2024-03-28 15:11:15.841 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] monitor closed 
[INFO ] 2024-03-28 15:11:15.841 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] monitor closed 
[INFO ] 2024-03-28 15:11:15.841 - [orders(100)][Rename Order Details] - Node Rename Order Details[19832abb-42a5-469c-b078-75cb94259aa8] close complete, cost 80 ms 
[INFO ] 2024-03-28 15:11:15.841 - [orders(100)][Delete Order Details] - Node Delete Order Details[4b4ce99e-1ec3-4212-a3b6-d96c33639d9c] close complete, cost 90 ms 
[INFO ] 2024-03-28 15:11:17.791 - [orders(100)][0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76] - Node 0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76[0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76] running status set to false 
[INFO ] 2024-03-28 15:11:17.792 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] running status set to false 
[INFO ] 2024-03-28 15:11:17.796 - [orders(100)][0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76] - Node 0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76[0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76] schema data cleaned 
[INFO ] 2024-03-28 15:11:17.796 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] schema data cleaned 
[INFO ] 2024-03-28 15:11:17.796 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] running status set to false 
[INFO ] 2024-03-28 15:11:17.796 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] monitor closed 
[INFO ] 2024-03-28 15:11:17.796 - [orders(100)][0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76] - Node 0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76[0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76] monitor closed 
[INFO ] 2024-03-28 15:11:17.796 - [orders(100)][239b8137-4111-4b6b-8d2e-5ded3af9a21c] - Node 239b8137-4111-4b6b-8d2e-5ded3af9a21c[239b8137-4111-4b6b-8d2e-5ded3af9a21c] running status set to false 
[INFO ] 2024-03-28 15:11:17.796 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] schema data cleaned 
[INFO ] 2024-03-28 15:11:17.796 - [orders(100)][239b8137-4111-4b6b-8d2e-5ded3af9a21c] - Node 239b8137-4111-4b6b-8d2e-5ded3af9a21c[239b8137-4111-4b6b-8d2e-5ded3af9a21c] schema data cleaned 
[INFO ] 2024-03-28 15:11:17.797 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] close complete, cost 7 ms 
[INFO ] 2024-03-28 15:11:17.797 - [orders(100)][0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76] - Node 0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76[0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76] close complete, cost 7 ms 
[INFO ] 2024-03-28 15:11:17.797 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] monitor closed 
[INFO ] 2024-03-28 15:11:17.797 - [orders(100)][239b8137-4111-4b6b-8d2e-5ded3af9a21c] - Node 239b8137-4111-4b6b-8d2e-5ded3af9a21c[239b8137-4111-4b6b-8d2e-5ded3af9a21c] monitor closed 
[INFO ] 2024-03-28 15:11:17.797 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] close complete, cost 7 ms 
[INFO ] 2024-03-28 15:11:17.797 - [orders(100)][239b8137-4111-4b6b-8d2e-5ded3af9a21c] - Node 239b8137-4111-4b6b-8d2e-5ded3af9a21c[239b8137-4111-4b6b-8d2e-5ded3af9a21c] close complete, cost 3 ms 
[INFO ] 2024-03-28 15:11:17.798 - [orders(100)] - load tapTable task 660517eea90a2b08fd2ae4bd-239b8137-4111-4b6b-8d2e-5ded3af9a21c complete, cost 2649ms 
[INFO ] 2024-03-28 15:11:17.799 - [orders(100)] - load tapTable task 660517eea90a2b08fd2ae4bd-0bbd1aac-91a1-4e00-b5d6-3d317b1c4a76 complete, cost 2720ms 
[INFO ] 2024-03-28 15:11:17.806 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] running status set to false 
[INFO ] 2024-03-28 15:11:17.806 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] schema data cleaned 
[INFO ] 2024-03-28 15:11:17.806 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] monitor closed 
[INFO ] 2024-03-28 15:11:17.810 - [orders(100)][Order Details] - Node Order Details[0e4a8a1f-a1ac-4a9d-bdba-13cbe52988e5] close complete, cost 1 ms 
[INFO ] 2024-03-28 15:11:17.811 - [orders(100)][cb10439a-8f43-48c9-9e27-b574c24626cf] - Node cb10439a-8f43-48c9-9e27-b574c24626cf[cb10439a-8f43-48c9-9e27-b574c24626cf] running status set to false 
[INFO ] 2024-03-28 15:11:17.813 - [orders(100)][cb10439a-8f43-48c9-9e27-b574c24626cf] - Node cb10439a-8f43-48c9-9e27-b574c24626cf[cb10439a-8f43-48c9-9e27-b574c24626cf] schema data cleaned 
[INFO ] 2024-03-28 15:11:17.813 - [orders(100)][cb10439a-8f43-48c9-9e27-b574c24626cf] - Node cb10439a-8f43-48c9-9e27-b574c24626cf[cb10439a-8f43-48c9-9e27-b574c24626cf] monitor closed 
[INFO ] 2024-03-28 15:11:17.814 - [orders(100)][cb10439a-8f43-48c9-9e27-b574c24626cf] - Node cb10439a-8f43-48c9-9e27-b574c24626cf[cb10439a-8f43-48c9-9e27-b574c24626cf] close complete, cost 3 ms 
[INFO ] 2024-03-28 15:11:17.815 - [orders(100)] - load tapTable task 660517eea90a2b08fd2ae4bd-cb10439a-8f43-48c9-9e27-b574c24626cf complete, cost 2569ms 
