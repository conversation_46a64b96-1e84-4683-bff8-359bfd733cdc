[INFO ] 2025-05-08 00:00:12.025 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 58 ms 
[INFO ] 2025-05-08 00:00:12.030 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:00:14.062 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 00:00:14.062 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:00:16.103 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:00:16.103 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:00:18.105 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:00:18.105 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:00:28.145 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 00:00:28.145 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:00:30.188 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 00:00:30.188 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:00:32.228 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:00:32.228 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:00:34.269 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:00:34.269 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:00:36.326 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 54 ms 
[INFO ] 2025-05-08 00:00:36.327 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:00:38.333 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:00:38.333 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:00:48.381 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 45 ms 
[INFO ] 2025-05-08 00:00:48.382 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:00:50.426 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 00:00:50.426 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:00:52.451 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 24 ms 
[INFO ] 2025-05-08 00:00:52.451 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:00:54.498 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 00:00:54.499 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:00:56.556 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 53 ms 
[INFO ] 2025-05-08 00:00:56.557 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:00:58.562 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:00:58.562 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:01:08.610 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 45 ms 
[INFO ] 2025-05-08 00:01:08.610 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:01:10.665 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 50 ms 
[INFO ] 2025-05-08 00:01:10.665 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:01:12.714 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 47 ms 
[INFO ] 2025-05-08 00:01:12.714 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:01:39.041 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 22655 ms 
[INFO ] 2025-05-08 00:01:39.041 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:01:49.243 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 8070 ms 
[INFO ] 2025-05-08 00:01:49.244 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:01:51.249 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:01:51.249 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:02:23.776 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 20713 ms 
[INFO ] 2025-05-08 00:02:23.777 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:02:29.136 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 2943 ms 
[INFO ] 2025-05-08 00:02:29.136 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:02:34.838 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 3200 ms 
[INFO ] 2025-05-08 00:02:34.838 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:04:07.881 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 89454 ms 
[INFO ] 2025-05-08 00:04:07.881 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:05:02.031 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 48966 ms 
[INFO ] 2025-05-08 00:05:02.031 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:05:04.032 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:05:04.032 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:05:14.102 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 64 ms 
[INFO ] 2025-05-08 00:05:14.102 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:05:16.120 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 15 ms 
[INFO ] 2025-05-08 00:05:16.120 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:05:18.205 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 83 ms 
[INFO ] 2025-05-08 00:05:18.205 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:05:20.251 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:05:20.251 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:05:22.292 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:05:22.292 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:05:24.295 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:05:24.295 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:05:34.372 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 73 ms 
[INFO ] 2025-05-08 00:05:34.373 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:05:36.414 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 00:05:36.415 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:05:38.453 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 00:05:38.453 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:05:40.523 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 65 ms 
[INFO ] 2025-05-08 00:05:40.524 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:05:42.577 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 49 ms 
[INFO ] 2025-05-08 00:05:42.577 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:05:44.584 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:05:44.584 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:05:54.648 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 60 ms 
[INFO ] 2025-05-08 00:05:54.649 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:05:56.710 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 57 ms 
[INFO ] 2025-05-08 00:05:56.711 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:05:58.787 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 70 ms 
[INFO ] 2025-05-08 00:05:58.787 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:06:00.823 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 00:06:00.823 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:06:02.861 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 00:06:02.861 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:06:04.864 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:06:04.864 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:06:14.930 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 61 ms 
[INFO ] 2025-05-08 00:06:14.930 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:06:16.974 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 00:06:16.974 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:06:19.016 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 00:06:19.017 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:06:21.060 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 00:06:21.060 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:06:23.083 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 22 ms 
[INFO ] 2025-05-08 00:06:23.083 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:06:25.088 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:06:25.089 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:06:35.135 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 00:06:35.135 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:06:37.187 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 47 ms 
[INFO ] 2025-05-08 00:06:37.187 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:06:39.229 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 00:06:39.229 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:06:41.276 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 00:06:41.276 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:06:43.310 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 00:06:43.310 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:06:45.316 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:06:45.316 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:06:55.365 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 48 ms 
[INFO ] 2025-05-08 00:06:55.366 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:06:57.412 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 00:06:57.412 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:06:59.449 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:06:59.449 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:07:01.490 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:07:01.490 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:07:03.540 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 46 ms 
[INFO ] 2025-05-08 00:07:03.541 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:07:05.546 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:07:05.547 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:07:15.606 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 54 ms 
[INFO ] 2025-05-08 00:07:15.606 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:07:17.673 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 63 ms 
[INFO ] 2025-05-08 00:07:17.674 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:07:19.748 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 68 ms 
[INFO ] 2025-05-08 00:07:19.749 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:07:21.805 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 53 ms 
[INFO ] 2025-05-08 00:07:21.805 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:07:23.874 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 66 ms 
[INFO ] 2025-05-08 00:07:23.874 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:07:25.881 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:07:25.881 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:07:35.930 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 44 ms 
[INFO ] 2025-05-08 00:07:35.930 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:07:37.973 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 00:07:37.973 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:07:40.027 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 49 ms 
[INFO ] 2025-05-08 00:07:40.028 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:07:42.076 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 45 ms 
[INFO ] 2025-05-08 00:07:42.077 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:07:44.132 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 51 ms 
[INFO ] 2025-05-08 00:07:44.132 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:07:46.137 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:07:46.138 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:07:56.187 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 44 ms 
[INFO ] 2025-05-08 00:07:56.187 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:07:58.248 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 55 ms 
[INFO ] 2025-05-08 00:07:58.249 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:08:00.326 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 75 ms 
[INFO ] 2025-05-08 00:08:00.327 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:08:02.388 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 55 ms 
[INFO ] 2025-05-08 00:08:02.388 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:08:04.440 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 47 ms 
[INFO ] 2025-05-08 00:08:04.441 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:08:06.448 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:08:06.449 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:08:16.510 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 56 ms 
[INFO ] 2025-05-08 00:08:16.510 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:08:18.562 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 48 ms 
[INFO ] 2025-05-08 00:08:18.563 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:08:20.594 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 26 ms 
[INFO ] 2025-05-08 00:08:20.594 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:08:22.633 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:08:22.633 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:08:24.679 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 00:08:24.681 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:08:26.682 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:08:26.888 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:08:36.754 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 67 ms 
[INFO ] 2025-05-08 00:08:36.755 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:08:38.830 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 72 ms 
[INFO ] 2025-05-08 00:08:38.830 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:08:40.886 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 51 ms 
[INFO ] 2025-05-08 00:08:40.886 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:08:42.951 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 61 ms 
[INFO ] 2025-05-08 00:08:42.952 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:08:45.014 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 54 ms 
[INFO ] 2025-05-08 00:08:45.014 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:08:47.022 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:08:47.023 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:08:57.047 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 22 ms 
[INFO ] 2025-05-08 00:08:57.048 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:08:59.095 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 00:08:59.095 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:09:01.134 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 00:09:01.134 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:09:03.157 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 17 ms 
[INFO ] 2025-05-08 00:09:03.157 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:09:05.205 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 45 ms 
[INFO ] 2025-05-08 00:09:05.205 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:09:07.215 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:09:07.216 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:09:17.373 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 63 ms 
[INFO ] 2025-05-08 00:09:17.374 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:09:19.358 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 66 ms 
[INFO ] 2025-05-08 00:09:19.358 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:09:21.597 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 57 ms 
[INFO ] 2025-05-08 00:09:21.598 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:09:23.479 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 55 ms 
[INFO ] 2025-05-08 00:09:23.479 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:09:25.545 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 59 ms 
[INFO ] 2025-05-08 00:09:25.545 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:09:27.552 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:09:27.552 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:10:08.442 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 30331 ms 
[INFO ] 2025-05-08 00:10:08.442 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:10:10.494 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 47 ms 
[INFO ] 2025-05-08 00:10:10.495 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:10:12.558 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 61 ms 
[INFO ] 2025-05-08 00:10:12.558 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:10:14.593 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 29 ms 
[INFO ] 2025-05-08 00:10:14.593 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:10:16.686 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 90 ms 
[INFO ] 2025-05-08 00:10:16.686 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:10:18.691 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:10:18.692 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:10:28.749 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 51 ms 
[INFO ] 2025-05-08 00:10:28.750 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:10:30.797 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 00:10:30.798 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:10:32.869 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 69 ms 
[INFO ] 2025-05-08 00:10:32.869 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:10:34.909 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 00:10:34.910 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:10:36.974 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 57 ms 
[INFO ] 2025-05-08 00:10:36.975 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:10:38.976 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:10:38.977 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:10:49.022 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:10:49.023 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:10:51.066 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 00:10:51.066 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:10:53.123 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 50 ms 
[INFO ] 2025-05-08 00:10:53.123 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:10:55.170 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 00:10:55.170 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:10:57.228 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 57 ms 
[INFO ] 2025-05-08 00:10:57.229 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:10:59.232 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:10:59.232 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:11:09.274 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 00:11:09.274 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:11:11.315 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 00:11:11.316 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:11:13.368 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 49 ms 
[INFO ] 2025-05-08 00:11:13.369 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:11:15.435 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 60 ms 
[INFO ] 2025-05-08 00:11:15.436 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:11:17.495 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 55 ms 
[INFO ] 2025-05-08 00:11:17.495 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:11:19.500 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:11:19.501 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:11:29.551 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 46 ms 
[INFO ] 2025-05-08 00:11:29.552 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:11:31.578 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 23 ms 
[INFO ] 2025-05-08 00:11:31.578 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:11:33.816 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 51 ms 
[INFO ] 2025-05-08 00:11:33.816 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:11:35.705 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 63 ms 
[INFO ] 2025-05-08 00:11:35.705 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:11:37.767 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 58 ms 
[INFO ] 2025-05-08 00:11:37.768 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:11:39.772 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:11:39.772 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:11:49.813 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 00:11:49.813 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:11:51.840 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 22 ms 
[INFO ] 2025-05-08 00:11:51.840 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:11:53.879 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 00:11:53.879 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:11:55.936 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 51 ms 
[INFO ] 2025-05-08 00:11:55.936 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:11:57.983 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 43 ms 
[INFO ] 2025-05-08 00:11:57.983 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:11:59.988 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:11:59.988 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:12:10.040 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 47 ms 
[INFO ] 2025-05-08 00:12:10.041 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:12:12.084 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:12:12.084 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:12:14.119 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 00:12:14.119 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:12:16.156 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:12:16.156 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:12:18.188 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 00:12:18.189 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:12:20.193 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:12:20.193 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:12:30.250 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 54 ms 
[INFO ] 2025-05-08 00:12:30.251 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:12:32.323 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 64 ms 
[INFO ] 2025-05-08 00:12:32.324 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:12:34.388 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 61 ms 
[INFO ] 2025-05-08 00:12:34.388 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:12:36.467 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 73 ms 
[INFO ] 2025-05-08 00:12:36.467 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:12:38.525 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 53 ms 
[INFO ] 2025-05-08 00:12:38.525 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:12:40.531 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:12:40.531 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:12:50.572 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 00:12:50.572 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:12:52.651 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 72 ms 
[INFO ] 2025-05-08 00:12:52.651 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:12:54.713 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 57 ms 
[INFO ] 2025-05-08 00:12:54.714 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:12:56.771 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 53 ms 
[INFO ] 2025-05-08 00:12:56.771 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:12:58.813 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 00:12:58.814 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:13:00.818 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:13:00.819 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:13:10.869 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 44 ms 
[INFO ] 2025-05-08 00:13:10.869 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:13:12.912 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 00:13:12.912 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:13:14.967 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 51 ms 
[INFO ] 2025-05-08 00:13:14.967 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:13:17.005 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 00:13:17.005 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:13:19.040 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 00:13:19.040 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:13:21.043 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:13:21.043 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:13:31.078 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 00:13:31.078 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:13:33.116 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 00:13:33.117 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:13:35.175 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 57 ms 
[INFO ] 2025-05-08 00:13:35.175 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:13:37.213 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 00:13:37.213 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:13:39.248 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 00:13:39.248 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:13:41.249 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:13:41.249 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:13:51.283 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 00:13:51.283 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:13:53.316 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 00:13:53.316 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:13:55.350 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 00:13:55.350 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:13:57.385 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 00:13:57.385 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:13:59.419 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 00:13:59.420 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:14:01.423 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:14:01.423 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:14:11.461 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 00:14:11.461 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:14:13.518 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 50 ms 
[INFO ] 2025-05-08 00:14:13.519 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:14:15.572 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 52 ms 
[INFO ] 2025-05-08 00:14:15.572 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:14:17.633 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 55 ms 
[INFO ] 2025-05-08 00:14:17.633 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:14:19.692 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 55 ms 
[INFO ] 2025-05-08 00:14:19.692 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:14:21.698 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:14:21.698 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:14:31.733 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 00:14:31.733 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:14:33.767 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 28 ms 
[INFO ] 2025-05-08 00:14:33.767 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:14:35.803 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 00:14:35.803 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:14:37.853 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 44 ms 
[INFO ] 2025-05-08 00:14:37.853 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:14:39.907 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 49 ms 
[INFO ] 2025-05-08 00:14:39.907 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:14:41.911 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:14:41.911 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[INFO ] 2025-05-08 00:14:51.949 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:14:51.949 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:14:53.981 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 29 ms 
[INFO ] 2025-05-08 00:14:53.981 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:14:56.016 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:14:56.016 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:14:58.067 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 45 ms 
[INFO ] 2025-05-08 00:14:58.067 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:15:00.147 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 72 ms 
[INFO ] 2025-05-08 00:15:00.147 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:15:02.151 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:15:02.151 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525072, rowId: 15 
[TRACE] 2025-05-08 00:15:11.046 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] running status set to false 
[INFO ] 2025-05-08 00:15:11.069 - [Sybase ~pg时间测试][LocalSybase] - Log Miner is shutting down... 
[INFO ] 2025-05-08 00:15:11.069 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 15, size: 1, cost: 22 ms 
[INFO ] 2025-05-08 00:15:11.069 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[TRACE] 2025-05-08 00:15:11.071 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746633127322 
[TRACE] 2025-05-08 00:15:11.071 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node released: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746633127322 
[TRACE] 2025-05-08 00:15:11.071 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] schema data cleaned 
[TRACE] 2025-05-08 00:15:11.072 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] monitor closed 
[TRACE] 2025-05-08 00:15:11.072 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] close complete, cost 26 ms 
[TRACE] 2025-05-08 00:15:11.072 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] running status set to false 
[TRACE] 2025-05-08 00:15:11.110 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node stopped: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746633127120 
[TRACE] 2025-05-08 00:15:11.110 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node released: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746633127120 
[TRACE] 2025-05-08 00:15:11.110 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] schema data cleaned 
[TRACE] 2025-05-08 00:15:11.110 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] monitor closed 
[TRACE] 2025-05-08 00:15:11.311 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] close complete, cost 38 ms 
[INFO ] 2025-05-08 00:15:13.113 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 15, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 00:15:13.113 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[TRACE] 2025-05-08 00:15:13.658 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:15:13.659 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4619f085 
[TRACE] 2025-05-08 00:15:13.801 - [Sybase ~pg时间测试] - Stop task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试)  
[TRACE] 2025-05-08 00:15:13.801 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[TRACE] 2025-05-08 00:15:13.802 - [Sybase ~pg时间测试] - Snapshot order controller have been removed 
[INFO ] 2025-05-08 00:15:13.802 - [Sybase ~pg时间测试] - Task stopped. 
[INFO ] 2025-05-08 00:15:15.183 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 15, size: 1, cost: 63 ms 
[INFO ] 2025-05-08 00:15:15.184 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:15:17.226 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 15, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 00:15:17.226 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[TRACE] 2025-05-08 00:15:18.818 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:15:18.819 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4619f085 
[TRACE] 2025-05-08 00:15:18.819 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[INFO ] 2025-05-08 00:15:18.897 - [Sybase ~pg时间测试] - Task stopped. 
[TRACE] 2025-05-08 00:15:18.898 - [Sybase ~pg时间测试] - Remove memory task client succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:15:18.898 - [Sybase ~pg时间测试] - Destroy memory task client cache succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[INFO ] 2025-05-08 00:15:19.285 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 15, size: 1, cost: 57 ms 
[INFO ] 2025-05-08 00:15:19.286 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:15:21.293 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[TRACE] 2025-05-08 00:17:12.758 - [Sybase ~pg时间测试] - Task initialization... 
[TRACE] 2025-05-08 00:17:12.965 - [Sybase ~pg时间测试] - Start task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试) 
[INFO ] 2025-05-08 00:17:13.171 - [Sybase ~pg时间测试] - Loading table structure completed 
[TRACE] 2025-05-08 00:17:13.288 - [Sybase ~pg时间测试] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-08 00:17:13.411 - [Sybase ~pg时间测试] - The engine receives Sybase ~pg时间测试 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-08 00:17:13.412 - [Sybase ~pg时间测试] - Task started 
[TRACE] 2025-05-08 00:17:13.486 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:17:13.486 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:17:13.486 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 00:17:13.486 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] preload schema finished, cost 0 ms 
[INFO ] 2025-05-08 00:17:14.215 - [Sybase ~pg时间测试][PG - Copy] - Sink connector(PG - Copy) initialization completed 
[TRACE] 2025-05-08 00:17:14.215 - [Sybase ~pg时间测试][PG - Copy] - Node(PG - Copy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-08 00:17:14.215 - [Sybase ~pg时间测试][PG - Copy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-08 00:17:14.244 - [Sybase ~pg时间测试][LocalSybase] - Source connector(LocalSybase) initialization completed 
[TRACE] 2025-05-08 00:17:14.247 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" read batch size: 100 
[TRACE] 2025-05-08 00:17:14.247 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" event queue capacity: 200 
[TRACE] 2025-05-08 00:17:14.247 - [Sybase ~pg时间测试][LocalSybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-08 00:17:14.247 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:17:14.421 - [Sybase ~pg时间测试][LocalSybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-05-08 00:17:14.421 - [Sybase ~pg时间测试][PG - Copy] - Apply table structure to target database 
[INFO ] 2025-05-08 00:17:14.626 - [Sybase ~pg时间测试][LocalSybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:17:14.781 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from 2 tables 
[TRACE] 2025-05-08 00:17:14.812 - [Sybase ~pg时间测试][LocalSybase] - Initial sync started 
[INFO ] 2025-05-08 00:17:14.812 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: testrslt 
[TRACE] 2025-05-08 00:17:14.812 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt is going to be initial synced 
[INFO ] 2025-05-08 00:17:14.878 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt has been completed batch read 
[INFO ] 2025-05-08 00:17:14.878 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: time_test2 
[TRACE] 2025-05-08 00:17:14.878 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 is going to be initial synced 
[TRACE] 2025-05-08 00:17:14.902 - [Sybase ~pg时间测试][LocalSybase] - Query snapshot row size completed: LocalSybase(ecf2fe24-1395-49b6-9448-da59d864cc1e) 
[TRACE] 2025-05-08 00:17:14.902 - [Sybase ~pg时间测试][LocalSybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {}, error: Text '2025-05-07' could not be parsed at index 10 
[ERROR] 2025-05-08 00:17:14.951 - [Sybase ~pg时间测试][LocalSybase] - java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {}, error: Text '2025-05-07' could not be parsed at index 10 <-- Error Message -->
java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {}, error: Text '2025-05-07' could not be parsed at index 10

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {}, error: Text '2025-05-07' could not be parsed at index 10
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:49)
	io.tapdata.sybase.SybaseConnectorV2.batchReadV2(SybaseConnectorV2.java:357)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {}, error: Text '2025-05-07' could not be parsed at index 10
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:789)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:51)
	at io.tapdata.sybase.SybaseConnectorV2.batchReadV2(SybaseConnectorV2.java:357)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:501)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:493)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:415)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:359)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {}, error: Text '2025-05-07' could not be parsed at index 10
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:49)
	... 22 more

[TRACE] 2025-05-08 00:17:14.952 - [Sybase ~pg时间测试][LocalSybase] - Job suspend in error handle 
[TRACE] 2025-05-08 00:17:14.952 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[INFO ] 2025-05-08 00:17:14.952 - [Sybase ~pg时间测试][LocalSybase] - Batch read completed. 
[TRACE] 2025-05-08 00:17:14.952 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync starting... 
[TRACE] 2025-05-08 00:17:14.952 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync completed 
[TRACE] 2025-05-08 00:17:15.051 - [Sybase ~pg时间测试][LocalSybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Cannot invoke "java.util.concurrent.atomic.AtomicInteger.intValue()" because "sourceInitialCounter" is null 
[TRACE] 2025-05-08 00:17:15.051 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] running status set to false 
[TRACE] 2025-05-08 00:17:15.118 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746634634181 
[TRACE] 2025-05-08 00:17:15.118 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node released: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746634634181 
[TRACE] 2025-05-08 00:17:15.118 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] schema data cleaned 
[TRACE] 2025-05-08 00:17:15.118 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] monitor closed 
[TRACE] 2025-05-08 00:17:15.119 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] close complete, cost 68 ms 
[TRACE] 2025-05-08 00:17:15.119 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] running status set to false 
[TRACE] 2025-05-08 00:17:15.121 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node stopped: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746634634057 
[TRACE] 2025-05-08 00:17:15.121 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node released: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746634634057 
[TRACE] 2025-05-08 00:17:15.121 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] schema data cleaned 
[TRACE] 2025-05-08 00:17:15.121 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] monitor closed 
[TRACE] 2025-05-08 00:17:15.323 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] close complete, cost 2 ms 
[INFO ] 2025-05-08 00:17:18.997 - [Sybase ~pg时间测试] - Task [Sybase ~pg时间测试] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 00:17:19.009 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:17:19.009 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@76db1dd8 
[TRACE] 2025-05-08 00:17:19.146 - [Sybase ~pg时间测试] - Stop task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试)  
[TRACE] 2025-05-08 00:17:19.146 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[TRACE] 2025-05-08 00:17:19.146 - [Sybase ~pg时间测试] - Snapshot order controller have been removed 
[INFO ] 2025-05-08 00:17:19.147 - [Sybase ~pg时间测试] - Task stopped. 
[INFO ] 2025-05-08 00:17:24.170 - [Sybase ~pg时间测试] - Task [Sybase ~pg时间测试] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 00:17:24.171 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:17:24.175 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@76db1dd8 
[TRACE] 2025-05-08 00:17:24.175 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[INFO ] 2025-05-08 00:17:24.175 - [Sybase ~pg时间测试] - Task stopped. 
[TRACE] 2025-05-08 00:17:24.245 - [Sybase ~pg时间测试] - Remove memory task client succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:17:24.245 - [Sybase ~pg时间测试] - Destroy memory task client cache succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:18:29.787 - [Sybase ~pg时间测试] - Task initialization... 
[TRACE] 2025-05-08 00:18:29.993 - [Sybase ~pg时间测试] - Start task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试) 
[INFO ] 2025-05-08 00:18:30.061 - [Sybase ~pg时间测试] - Loading table structure completed 
[TRACE] 2025-05-08 00:18:30.472 - [Sybase ~pg时间测试] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-08 00:18:30.559 - [Sybase ~pg时间测试] - The engine receives Sybase ~pg时间测试 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-08 00:18:30.621 - [Sybase ~pg时间测试] - Task started 
[TRACE] 2025-05-08 00:18:30.621 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:18:30.621 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:18:30.621 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 00:18:30.621 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] preload schema finished, cost 0 ms 
[INFO ] 2025-05-08 00:18:31.450 - [Sybase ~pg时间测试][LocalSybase] - Source connector(LocalSybase) initialization completed 
[TRACE] 2025-05-08 00:18:31.452 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" read batch size: 100 
[TRACE] 2025-05-08 00:18:31.452 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" event queue capacity: 200 
[TRACE] 2025-05-08 00:18:31.452 - [Sybase ~pg时间测试][LocalSybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-08 00:18:31.452 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:18:31.494 - [Sybase ~pg时间测试][LocalSybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-05-08 00:18:31.494 - [Sybase ~pg时间测试][LocalSybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:18:31.495 - [Sybase ~pg时间测试][PG - Copy] - Sink connector(PG - Copy) initialization completed 
[TRACE] 2025-05-08 00:18:31.495 - [Sybase ~pg时间测试][PG - Copy] - Node(PG - Copy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-08 00:18:31.495 - [Sybase ~pg时间测试][PG - Copy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-08 00:18:31.708 - [Sybase ~pg时间测试][PG - Copy] - Apply table structure to target database 
[INFO ] 2025-05-08 00:18:31.911 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from 2 tables 
[TRACE] 2025-05-08 00:18:31.911 - [Sybase ~pg时间测试][LocalSybase] - Initial sync started 
[INFO ] 2025-05-08 00:18:31.911 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: testrslt 
[TRACE] 2025-05-08 00:18:31.911 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt is going to be initial synced 
[INFO ] 2025-05-08 00:18:31.969 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt has been completed batch read 
[INFO ] 2025-05-08 00:18:31.969 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: time_test2 
[TRACE] 2025-05-08 00:18:31.994 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 is going to be initial synced 
[TRACE] 2025-05-08 00:19:18.991 - [Sybase ~pg时间测试][LocalSybase] - Query snapshot row size completed: LocalSybase(ecf2fe24-1395-49b6-9448-da59d864cc1e) 
[TRACE] 2025-05-08 00:19:19.038 - [Sybase ~pg时间测试][LocalSybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {}, error: Text '2025-05-07' could not be parsed at index 10 
[ERROR] 2025-05-08 00:19:19.040 - [Sybase ~pg时间测试][LocalSybase] - java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {}, error: Text '2025-05-07' could not be parsed at index 10 <-- Error Message -->
java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {}, error: Text '2025-05-07' could not be parsed at index 10

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {}, error: Text '2025-05-07' could not be parsed at index 10
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:49)
	io.tapdata.sybase.SybaseConnectorV2.batchReadV2(SybaseConnectorV2.java:357)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {}, error: Text '2025-05-07' could not be parsed at index 10
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:789)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:51)
	at io.tapdata.sybase.SybaseConnectorV2.batchReadV2(SybaseConnectorV2.java:357)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:501)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:493)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:415)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:359)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {}, error: Text '2025-05-07' could not be parsed at index 10
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:49)
	... 22 more

[TRACE] 2025-05-08 00:19:19.040 - [Sybase ~pg时间测试][LocalSybase] - Job suspend in error handle 
[TRACE] 2025-05-08 00:19:19.040 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[INFO ] 2025-05-08 00:19:19.040 - [Sybase ~pg时间测试][LocalSybase] - Batch read completed. 
[TRACE] 2025-05-08 00:19:19.040 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync starting... 
[TRACE] 2025-05-08 00:19:19.040 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync completed 
[TRACE] 2025-05-08 00:19:19.246 - [Sybase ~pg时间测试][LocalSybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Cannot invoke "java.util.concurrent.atomic.AtomicInteger.intValue()" because "sourceInitialCounter" is null 
[TRACE] 2025-05-08 00:19:19.324 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] running status set to false 
[TRACE] 2025-05-08 00:19:19.343 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746634711362 
[TRACE] 2025-05-08 00:19:19.343 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node released: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746634711362 
[TRACE] 2025-05-08 00:19:19.343 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] schema data cleaned 
[TRACE] 2025-05-08 00:19:19.343 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] monitor closed 
[TRACE] 2025-05-08 00:19:19.346 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] close complete, cost 65 ms 
[TRACE] 2025-05-08 00:19:19.346 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] running status set to false 
[TRACE] 2025-05-08 00:19:19.357 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node stopped: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746634711272 
[TRACE] 2025-05-08 00:19:19.357 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node released: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746634711272 
[TRACE] 2025-05-08 00:19:19.357 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] schema data cleaned 
[TRACE] 2025-05-08 00:19:19.357 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] monitor closed 
[TRACE] 2025-05-08 00:19:19.357 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] close complete, cost 11 ms 
[INFO ] 2025-05-08 00:19:23.996 - [Sybase ~pg时间测试] - Task [Sybase ~pg时间测试] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 00:19:23.996 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:19:23.997 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4a49e99c 
[TRACE] 2025-05-08 00:19:24.115 - [Sybase ~pg时间测试] - Stop task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试)  
[TRACE] 2025-05-08 00:19:24.115 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[TRACE] 2025-05-08 00:19:24.115 - [Sybase ~pg时间测试] - Snapshot order controller have been removed 
[INFO ] 2025-05-08 00:19:24.115 - [Sybase ~pg时间测试] - Task stopped. 
[INFO ] 2025-05-08 00:19:29.121 - [Sybase ~pg时间测试] - Task [Sybase ~pg时间测试] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 00:19:29.121 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:19:29.121 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4a49e99c 
[TRACE] 2025-05-08 00:19:29.121 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[INFO ] 2025-05-08 00:19:29.136 - [Sybase ~pg时间测试] - Task stopped. 
[TRACE] 2025-05-08 00:19:29.138 - [Sybase ~pg时间测试] - Remove memory task client succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:19:29.139 - [Sybase ~pg时间测试] - Destroy memory task client cache succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:21:36.011 - [Sybase ~pg时间测试] - Task initialization... 
[TRACE] 2025-05-08 00:21:36.014 - [Sybase ~pg时间测试] - Start task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试) 
[INFO ] 2025-05-08 00:21:36.216 - [Sybase ~pg时间测试] - Loading table structure completed 
[TRACE] 2025-05-08 00:21:36.258 - [Sybase ~pg时间测试] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-08 00:21:36.258 - [Sybase ~pg时间测试] - The engine receives Sybase ~pg时间测试 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-08 00:21:36.287 - [Sybase ~pg时间测试] - Task started 
[TRACE] 2025-05-08 00:21:36.287 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:21:36.287 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:21:36.287 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 00:21:36.287 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] preload schema finished, cost 0 ms 
[INFO ] 2025-05-08 00:21:37.055 - [Sybase ~pg时间测试][PG - Copy] - Sink connector(PG - Copy) initialization completed 
[TRACE] 2025-05-08 00:21:37.055 - [Sybase ~pg时间测试][PG - Copy] - Node(PG - Copy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-08 00:21:37.055 - [Sybase ~pg时间测试][PG - Copy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-08 00:21:37.121 - [Sybase ~pg时间测试][PG - Copy] - Apply table structure to target database 
[INFO ] 2025-05-08 00:21:37.125 - [Sybase ~pg时间测试][LocalSybase] - Source connector(LocalSybase) initialization completed 
[TRACE] 2025-05-08 00:21:37.125 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" read batch size: 100 
[TRACE] 2025-05-08 00:21:37.125 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" event queue capacity: 200 
[TRACE] 2025-05-08 00:21:37.125 - [Sybase ~pg时间测试][LocalSybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-08 00:21:37.192 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:21:37.192 - [Sybase ~pg时间测试][LocalSybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-05-08 00:21:37.392 - [Sybase ~pg时间测试][LocalSybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:21:37.440 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from 2 tables 
[TRACE] 2025-05-08 00:21:37.459 - [Sybase ~pg时间测试][LocalSybase] - Initial sync started 
[INFO ] 2025-05-08 00:21:37.461 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: testrslt 
[TRACE] 2025-05-08 00:21:37.461 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt is going to be initial synced 
[INFO ] 2025-05-08 00:21:37.563 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt has been completed batch read 
[INFO ] 2025-05-08 00:21:37.563 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: time_test2 
[TRACE] 2025-05-08 00:21:37.563 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 is going to be initial synced 
[TRACE] 2025-05-08 00:21:37.594 - [Sybase ~pg时间测试][LocalSybase] - Query snapshot row size completed: LocalSybase(ecf2fe24-1395-49b6-9448-da59d864cc1e) 
[TRACE] 2025-05-08 00:21:37.594 - [Sybase ~pg时间测试][LocalSybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10 
[ERROR] 2025-05-08 00:21:37.599 - [Sybase ~pg时间测试][LocalSybase] - java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10 <-- Error Message -->
java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:49)
	io.tapdata.sybase.SybaseConnectorV2.batchReadV2(SybaseConnectorV2.java:357)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:789)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:51)
	at io.tapdata.sybase.SybaseConnectorV2.batchReadV2(SybaseConnectorV2.java:357)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:501)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:493)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:415)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:359)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:49)
	... 22 more

[TRACE] 2025-05-08 00:21:37.602 - [Sybase ~pg时间测试][LocalSybase] - Job suspend in error handle 
[TRACE] 2025-05-08 00:21:37.602 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[INFO ] 2025-05-08 00:21:37.602 - [Sybase ~pg时间测试][LocalSybase] - Batch read completed. 
[TRACE] 2025-05-08 00:21:37.602 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync starting... 
[TRACE] 2025-05-08 00:21:37.603 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync completed 
[TRACE] 2025-05-08 00:21:37.646 - [Sybase ~pg时间测试][LocalSybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Cannot invoke "java.util.concurrent.atomic.AtomicInteger.intValue()" because "sourceInitialCounter" is null 
[TRACE] 2025-05-08 00:21:37.646 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] running status set to false 
[TRACE] 2025-05-08 00:21:37.671 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746634897011 
[TRACE] 2025-05-08 00:21:37.671 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node released: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746634897011 
[TRACE] 2025-05-08 00:21:37.671 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] schema data cleaned 
[TRACE] 2025-05-08 00:21:37.672 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] monitor closed 
[TRACE] 2025-05-08 00:21:37.673 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] close complete, cost 25 ms 
[TRACE] 2025-05-08 00:21:37.673 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] running status set to false 
[TRACE] 2025-05-08 00:21:37.679 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node stopped: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746634896883 
[TRACE] 2025-05-08 00:21:37.679 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node released: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746634896883 
[TRACE] 2025-05-08 00:21:37.679 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] schema data cleaned 
[TRACE] 2025-05-08 00:21:37.679 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] monitor closed 
[TRACE] 2025-05-08 00:21:37.885 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] close complete, cost 8 ms 
[INFO ] 2025-05-08 00:21:39.241 - [Sybase ~pg时间测试] - Task [Sybase ~pg时间测试] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 00:21:39.252 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:21:39.253 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7f1d52c8 
[TRACE] 2025-05-08 00:21:39.385 - [Sybase ~pg时间测试] - Stop task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试)  
[TRACE] 2025-05-08 00:21:39.385 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[TRACE] 2025-05-08 00:21:39.385 - [Sybase ~pg时间测试] - Snapshot order controller have been removed 
[INFO ] 2025-05-08 00:21:39.386 - [Sybase ~pg时间测试] - Task stopped. 
[INFO ] 2025-05-08 00:21:44.417 - [Sybase ~pg时间测试] - Task [Sybase ~pg时间测试] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 00:21:44.417 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:21:44.417 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7f1d52c8 
[TRACE] 2025-05-08 00:21:44.417 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[INFO ] 2025-05-08 00:21:44.454 - [Sybase ~pg时间测试] - Task stopped. 
[TRACE] 2025-05-08 00:21:44.458 - [Sybase ~pg时间测试] - Remove memory task client succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:21:44.458 - [Sybase ~pg时间测试] - Destroy memory task client cache succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:22:11.496 - [Sybase ~pg时间测试] - Task initialization... 
[TRACE] 2025-05-08 00:22:11.499 - [Sybase ~pg时间测试] - Start task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试) 
[INFO ] 2025-05-08 00:22:11.705 - [Sybase ~pg时间测试] - Loading table structure completed 
[TRACE] 2025-05-08 00:22:11.901 - [Sybase ~pg时间测试] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-08 00:22:11.905 - [Sybase ~pg时间测试] - The engine receives Sybase ~pg时间测试 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-08 00:22:11.990 - [Sybase ~pg时间测试] - Task started 
[TRACE] 2025-05-08 00:22:11.990 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:22:11.990 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:22:11.990 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 00:22:11.990 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] preload schema finished, cost 0 ms 
[INFO ] 2025-05-08 00:22:12.884 - [Sybase ~pg时间测试][LocalSybase] - Source connector(LocalSybase) initialization completed 
[TRACE] 2025-05-08 00:22:12.885 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" read batch size: 100 
[TRACE] 2025-05-08 00:22:12.885 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" event queue capacity: 200 
[TRACE] 2025-05-08 00:22:12.885 - [Sybase ~pg时间测试][LocalSybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-08 00:22:12.886 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:22:12.915 - [Sybase ~pg时间测试][PG - Copy] - Sink connector(PG - Copy) initialization completed 
[TRACE] 2025-05-08 00:22:12.915 - [Sybase ~pg时间测试][PG - Copy] - Node(PG - Copy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-08 00:22:12.915 - [Sybase ~pg时间测试][PG - Copy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-08 00:22:12.936 - [Sybase ~pg时间测试][LocalSybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-05-08 00:22:12.936 - [Sybase ~pg时间测试][LocalSybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:22:12.998 - [Sybase ~pg时间测试][PG - Copy] - Apply table structure to target database 
[INFO ] 2025-05-08 00:22:12.998 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from 2 tables 
[TRACE] 2025-05-08 00:22:13.005 - [Sybase ~pg时间测试][LocalSybase] - Initial sync started 
[INFO ] 2025-05-08 00:22:13.005 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: testrslt 
[TRACE] 2025-05-08 00:22:13.005 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt is going to be initial synced 
[INFO ] 2025-05-08 00:22:36.927 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt has been completed batch read 
[INFO ] 2025-05-08 00:22:36.943 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: time_test2 
[TRACE] 2025-05-08 00:22:36.944 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 is going to be initial synced 
[TRACE] 2025-05-08 00:22:36.978 - [Sybase ~pg时间测试][LocalSybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10 
[TRACE] 2025-05-08 00:22:36.979 - [Sybase ~pg时间测试][LocalSybase] - Query snapshot row size completed: LocalSybase(ecf2fe24-1395-49b6-9448-da59d864cc1e) 
[ERROR] 2025-05-08 00:22:36.990 - [Sybase ~pg时间测试][LocalSybase] - java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10 <-- Error Message -->
java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:49)
	io.tapdata.sybase.SybaseConnectorV2.batchReadV2(SybaseConnectorV2.java:357)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:789)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:51)
	at io.tapdata.sybase.SybaseConnectorV2.batchReadV2(SybaseConnectorV2.java:357)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:501)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:493)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:415)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:359)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:49)
	... 22 more

[TRACE] 2025-05-08 00:22:36.993 - [Sybase ~pg时间测试][LocalSybase] - Job suspend in error handle 
[TRACE] 2025-05-08 00:22:36.995 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[INFO ] 2025-05-08 00:22:36.998 - [Sybase ~pg时间测试][LocalSybase] - Batch read completed. 
[TRACE] 2025-05-08 00:22:36.998 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync starting... 
[TRACE] 2025-05-08 00:22:36.998 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync completed 
[TRACE] 2025-05-08 00:22:37.069 - [Sybase ~pg时间测试][LocalSybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Cannot invoke "java.util.concurrent.atomic.AtomicInteger.intValue()" because "sourceInitialCounter" is null 
[TRACE] 2025-05-08 00:22:37.070 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] running status set to false 
[TRACE] 2025-05-08 00:22:37.142 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746634932790 
[TRACE] 2025-05-08 00:22:37.142 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node released: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746634932790 
[TRACE] 2025-05-08 00:22:37.142 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] schema data cleaned 
[TRACE] 2025-05-08 00:22:37.142 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] monitor closed 
[TRACE] 2025-05-08 00:22:37.142 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] close complete, cost 73 ms 
[TRACE] 2025-05-08 00:22:37.143 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] running status set to false 
[TRACE] 2025-05-08 00:22:37.146 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node stopped: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746634932699 
[TRACE] 2025-05-08 00:22:37.146 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node released: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746634932699 
[TRACE] 2025-05-08 00:22:37.146 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] schema data cleaned 
[TRACE] 2025-05-08 00:22:37.146 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] monitor closed 
[TRACE] 2025-05-08 00:22:37.352 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] close complete, cost 4 ms 
[INFO ] 2025-05-08 00:22:41.892 - [Sybase ~pg时间测试] - Task [Sybase ~pg时间测试] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 00:22:41.901 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:22:41.901 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@657d3df8 
[TRACE] 2025-05-08 00:22:42.025 - [Sybase ~pg时间测试] - Stop task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试)  
[TRACE] 2025-05-08 00:22:42.025 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[TRACE] 2025-05-08 00:22:42.025 - [Sybase ~pg时间测试] - Snapshot order controller have been removed 
[INFO ] 2025-05-08 00:22:42.025 - [Sybase ~pg时间测试] - Task stopped. 
[INFO ] 2025-05-08 00:22:47.039 - [Sybase ~pg时间测试] - Task [Sybase ~pg时间测试] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 00:22:47.039 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:22:47.039 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@657d3df8 
[TRACE] 2025-05-08 00:22:47.040 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[INFO ] 2025-05-08 00:22:47.040 - [Sybase ~pg时间测试] - Task stopped. 
[TRACE] 2025-05-08 00:22:47.061 - [Sybase ~pg时间测试] - Remove memory task client succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:22:47.063 - [Sybase ~pg时间测试] - Destroy memory task client cache succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:24:31.056 - [Sybase ~pg时间测试] - Task initialization... 
[TRACE] 2025-05-08 00:24:31.221 - [Sybase ~pg时间测试] - Start task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试) 
[INFO ] 2025-05-08 00:24:31.221 - [Sybase ~pg时间测试] - Loading table structure completed 
[TRACE] 2025-05-08 00:24:31.288 - [Sybase ~pg时间测试] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-08 00:24:31.352 - [Sybase ~pg时间测试] - The engine receives Sybase ~pg时间测试 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-08 00:24:31.352 - [Sybase ~pg时间测试] - Task started 
[TRACE] 2025-05-08 00:24:31.375 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:24:31.375 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:24:31.375 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 00:24:31.376 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] preload schema finished, cost 0 ms 
[INFO ] 2025-05-08 00:24:32.540 - [Sybase ~pg时间测试][LocalSybase] - Source connector(LocalSybase) initialization completed 
[TRACE] 2025-05-08 00:24:32.543 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" read batch size: 100 
[TRACE] 2025-05-08 00:24:32.543 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" event queue capacity: 200 
[TRACE] 2025-05-08 00:24:32.543 - [Sybase ~pg时间测试][LocalSybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-08 00:24:32.551 - [Sybase ~pg时间测试][PG - Copy] - Sink connector(PG - Copy) initialization completed 
[TRACE] 2025-05-08 00:24:32.551 - [Sybase ~pg时间测试][PG - Copy] - Node(PG - Copy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-08 00:24:32.551 - [Sybase ~pg时间测试][PG - Copy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-08 00:24:32.556 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:24:32.616 - [Sybase ~pg时间测试][PG - Copy] - Apply table structure to target database 
[INFO ] 2025-05-08 00:24:32.617 - [Sybase ~pg时间测试][LocalSybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-05-08 00:24:32.784 - [Sybase ~pg时间测试][LocalSybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:24:32.787 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from 2 tables 
[TRACE] 2025-05-08 00:24:32.859 - [Sybase ~pg时间测试][LocalSybase] - Initial sync started 
[INFO ] 2025-05-08 00:24:32.860 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: testrslt 
[TRACE] 2025-05-08 00:24:32.988 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt is going to be initial synced 
[INFO ] 2025-05-08 00:24:32.994 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt has been completed batch read 
[INFO ] 2025-05-08 00:24:32.994 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: time_test2 
[TRACE] 2025-05-08 00:24:32.994 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 is going to be initial synced 
[TRACE] 2025-05-08 00:24:59.773 - [Sybase ~pg时间测试][LocalSybase] - Query snapshot row size completed: LocalSybase(ecf2fe24-1395-49b6-9448-da59d864cc1e) 
[TRACE] 2025-05-08 00:25:02.568 - [Sybase ~pg时间测试][LocalSybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10 
[ERROR] 2025-05-08 00:25:02.572 - [Sybase ~pg时间测试][LocalSybase] - java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10 <-- Error Message -->
java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:49)
	io.tapdata.sybase.SybaseConnectorV2.batchReadV2(SybaseConnectorV2.java:357)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:789)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:51)
	at io.tapdata.sybase.SybaseConnectorV2.batchReadV2(SybaseConnectorV2.java:357)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:501)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:493)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:415)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:359)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:49)
	... 22 more

[TRACE] 2025-05-08 00:25:02.580 - [Sybase ~pg时间测试][LocalSybase] - Job suspend in error handle 
[TRACE] 2025-05-08 00:25:02.580 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[INFO ] 2025-05-08 00:25:02.580 - [Sybase ~pg时间测试][LocalSybase] - Batch read completed. 
[TRACE] 2025-05-08 00:25:02.580 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync starting... 
[TRACE] 2025-05-08 00:25:02.580 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync completed 
[TRACE] 2025-05-08 00:25:02.634 - [Sybase ~pg时间测试][LocalSybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Cannot invoke "java.util.concurrent.atomic.AtomicInteger.intValue()" because "sourceInitialCounter" is null 
[TRACE] 2025-05-08 00:25:02.634 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] running status set to false 
[TRACE] 2025-05-08 00:25:02.696 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746635072396 
[TRACE] 2025-05-08 00:25:02.696 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node released: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746635072396 
[TRACE] 2025-05-08 00:25:02.697 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] schema data cleaned 
[TRACE] 2025-05-08 00:25:02.697 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] monitor closed 
[TRACE] 2025-05-08 00:25:02.700 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] close complete, cost 65 ms 
[TRACE] 2025-05-08 00:25:02.700 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] running status set to false 
[TRACE] 2025-05-08 00:25:02.715 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node stopped: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746635072262 
[TRACE] 2025-05-08 00:25:02.715 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node released: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746635072262 
[TRACE] 2025-05-08 00:25:02.715 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] schema data cleaned 
[TRACE] 2025-05-08 00:25:02.715 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] monitor closed 
[TRACE] 2025-05-08 00:25:02.788 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] close complete, cost 16 ms 
[INFO ] 2025-05-08 00:25:02.788 - [Sybase ~pg时间测试] - Task [Sybase ~pg时间测试] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 00:25:02.792 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:25:02.792 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3791ef36 
[TRACE] 2025-05-08 00:25:02.910 - [Sybase ~pg时间测试] - Stop task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试)  
[TRACE] 2025-05-08 00:25:02.910 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[TRACE] 2025-05-08 00:25:02.910 - [Sybase ~pg时间测试] - Snapshot order controller have been removed 
[INFO ] 2025-05-08 00:25:03.115 - [Sybase ~pg时间测试] - Task stopped. 
[INFO ] 2025-05-08 00:25:07.919 - [Sybase ~pg时间测试] - Task [Sybase ~pg时间测试] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 00:25:07.921 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:25:07.921 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3791ef36 
[TRACE] 2025-05-08 00:25:07.922 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[INFO ] 2025-05-08 00:25:07.922 - [Sybase ~pg时间测试] - Task stopped. 
[TRACE] 2025-05-08 00:25:08.127 - [Sybase ~pg时间测试] - Remove memory task client succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:25:08.128 - [Sybase ~pg时间测试] - Destroy memory task client cache succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:25:46.697 - [Sybase ~pg时间测试] - Task initialization... 
[TRACE] 2025-05-08 00:25:46.697 - [Sybase ~pg时间测试] - Start task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试) 
[INFO ] 2025-05-08 00:25:46.900 - [Sybase ~pg时间测试] - Loading table structure completed 
[TRACE] 2025-05-08 00:25:46.900 - [Sybase ~pg时间测试] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-08 00:25:46.946 - [Sybase ~pg时间测试] - The engine receives Sybase ~pg时间测试 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-08 00:25:46.946 - [Sybase ~pg时间测试] - Task started 
[TRACE] 2025-05-08 00:25:46.972 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:25:46.972 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 00:25:46.973 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:25:46.973 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] preload schema finished, cost 0 ms 
[INFO ] 2025-05-08 00:25:47.658 - [Sybase ~pg时间测试][PG - Copy] - Sink connector(PG - Copy) initialization completed 
[TRACE] 2025-05-08 00:25:47.658 - [Sybase ~pg时间测试][PG - Copy] - Node(PG - Copy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-08 00:25:47.658 - [Sybase ~pg时间测试][PG - Copy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-08 00:25:47.670 - [Sybase ~pg时间测试][PG - Copy] - Apply table structure to target database 
[INFO ] 2025-05-08 00:25:47.682 - [Sybase ~pg时间测试][LocalSybase] - Source connector(LocalSybase) initialization completed 
[TRACE] 2025-05-08 00:25:47.686 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" read batch size: 100 
[TRACE] 2025-05-08 00:25:47.686 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" event queue capacity: 200 
[TRACE] 2025-05-08 00:25:47.686 - [Sybase ~pg时间测试][LocalSybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-08 00:25:47.686 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:25:47.733 - [Sybase ~pg时间测试][LocalSybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-05-08 00:25:47.734 - [Sybase ~pg时间测试][LocalSybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:25:47.853 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from 2 tables 
[TRACE] 2025-05-08 00:25:47.853 - [Sybase ~pg时间测试][LocalSybase] - Initial sync started 
[INFO ] 2025-05-08 00:25:47.854 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: testrslt 
[TRACE] 2025-05-08 00:25:47.855 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt is going to be initial synced 
[INFO ] 2025-05-08 00:25:47.944 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt has been completed batch read 
[INFO ] 2025-05-08 00:25:47.950 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: time_test2 
[TRACE] 2025-05-08 00:25:47.952 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 is going to be initial synced 
[TRACE] 2025-05-08 00:25:52.286 - [Sybase ~pg时间测试][LocalSybase] - Query snapshot row size completed: LocalSybase(ecf2fe24-1395-49b6-9448-da59d864cc1e) 
[TRACE] 2025-05-08 00:27:23.755 - [Sybase ~pg时间测试][LocalSybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10 
[ERROR] 2025-05-08 00:27:23.757 - [Sybase ~pg时间测试][LocalSybase] - java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10 <-- Error Message -->
java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:49)
	io.tapdata.sybase.SybaseConnectorV2.batchReadV2(SybaseConnectorV2.java:357)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:789)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:51)
	at io.tapdata.sybase.SybaseConnectorV2.batchReadV2(SybaseConnectorV2.java:357)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:580)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:501)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:493)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:415)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:359)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: Batch read failed, table name: time_test2, sql: SELECT  "id" , "col_date" , "col_time" , "col_datetime" , "col_smalldatetime" , "col_bigdatetime" , "col_bigtime"  FROM  s1.dbo.time_test2 , error msg: Read column value failed, column name: col_date, type: DATE, data: {col_date=2025-05-07T00:00}, error: Text '2025-05-07' could not be parsed at index 10
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:49)
	... 22 more

[TRACE] 2025-05-08 00:27:23.757 - [Sybase ~pg时间测试][LocalSybase] - Job suspend in error handle 
[TRACE] 2025-05-08 00:27:23.761 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[INFO ] 2025-05-08 00:27:23.762 - [Sybase ~pg时间测试][LocalSybase] - Batch read completed. 
[TRACE] 2025-05-08 00:27:23.762 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync starting... 
[TRACE] 2025-05-08 00:27:23.762 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync completed 
[TRACE] 2025-05-08 00:27:23.853 - [Sybase ~pg时间测试][LocalSybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Cannot invoke "java.util.concurrent.atomic.AtomicInteger.intValue()" because "sourceInitialCounter" is null 
[TRACE] 2025-05-08 00:27:23.853 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] running status set to false 
[TRACE] 2025-05-08 00:27:23.919 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746635147624 
[TRACE] 2025-05-08 00:27:23.919 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node released: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746635147624 
[TRACE] 2025-05-08 00:27:23.919 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] schema data cleaned 
[TRACE] 2025-05-08 00:27:23.919 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] monitor closed 
[TRACE] 2025-05-08 00:27:23.919 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] close complete, cost 66 ms 
[TRACE] 2025-05-08 00:27:23.920 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] running status set to false 
[TRACE] 2025-05-08 00:27:23.931 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node stopped: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746635147513 
[TRACE] 2025-05-08 00:27:23.931 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node released: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746635147513 
[TRACE] 2025-05-08 00:27:23.931 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] schema data cleaned 
[TRACE] 2025-05-08 00:27:23.931 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] monitor closed 
[TRACE] 2025-05-08 00:27:24.133 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] close complete, cost 11 ms 
[INFO ] 2025-05-08 00:27:28.742 - [Sybase ~pg时间测试] - Task [Sybase ~pg时间测试] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 00:27:28.745 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:27:28.745 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1939564b 
[TRACE] 2025-05-08 00:27:28.862 - [Sybase ~pg时间测试] - Stop task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试)  
[TRACE] 2025-05-08 00:27:28.863 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[TRACE] 2025-05-08 00:27:28.863 - [Sybase ~pg时间测试] - Snapshot order controller have been removed 
[INFO ] 2025-05-08 00:27:28.863 - [Sybase ~pg时间测试] - Task stopped. 
[INFO ] 2025-05-08 00:27:33.867 - [Sybase ~pg时间测试] - Task [Sybase ~pg时间测试] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 00:27:33.867 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:27:33.867 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1939564b 
[TRACE] 2025-05-08 00:27:33.867 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[INFO ] 2025-05-08 00:27:33.867 - [Sybase ~pg时间测试] - Task stopped. 
[TRACE] 2025-05-08 00:27:33.883 - [Sybase ~pg时间测试] - Remove memory task client succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:27:33.886 - [Sybase ~pg时间测试] - Destroy memory task client cache succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:28:53.859 - [Sybase ~pg时间测试] - Task initialization... 
[TRACE] 2025-05-08 00:28:53.861 - [Sybase ~pg时间测试] - Start task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试) 
[INFO ] 2025-05-08 00:28:54.281 - [Sybase ~pg时间测试] - Loading table structure completed 
[TRACE] 2025-05-08 00:28:54.376 - [Sybase ~pg时间测试] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-08 00:28:54.383 - [Sybase ~pg时间测试] - The engine receives Sybase ~pg时间测试 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-08 00:28:54.511 - [Sybase ~pg时间测试] - Task started 
[TRACE] 2025-05-08 00:28:54.514 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:28:54.514 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:28:54.514 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 00:28:54.514 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] preload schema finished, cost 0 ms 
[INFO ] 2025-05-08 00:28:55.425 - [Sybase ~pg时间测试][LocalSybase] - Source connector(LocalSybase) initialization completed 
[TRACE] 2025-05-08 00:28:55.428 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" read batch size: 100 
[TRACE] 2025-05-08 00:28:55.428 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" event queue capacity: 200 
[TRACE] 2025-05-08 00:28:55.428 - [Sybase ~pg时间测试][LocalSybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-08 00:28:55.436 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:28:55.532 - [Sybase ~pg时间测试][LocalSybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-05-08 00:28:55.533 - [Sybase ~pg时间测试][LocalSybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:28:55.721 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from 2 tables 
[TRACE] 2025-05-08 00:28:55.722 - [Sybase ~pg时间测试][LocalSybase] - Initial sync started 
[INFO ] 2025-05-08 00:28:55.722 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: testrslt 
[TRACE] 2025-05-08 00:28:55.797 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt is going to be initial synced 
[INFO ] 2025-05-08 00:28:55.802 - [Sybase ~pg时间测试][PG - Copy] - Sink connector(PG - Copy) initialization completed 
[TRACE] 2025-05-08 00:28:55.802 - [Sybase ~pg时间测试][PG - Copy] - Node(PG - Copy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-08 00:28:55.802 - [Sybase ~pg时间测试][PG - Copy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-08 00:28:55.828 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt has been completed batch read 
[INFO ] 2025-05-08 00:28:55.828 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: time_test2 
[INFO ] 2025-05-08 00:28:55.831 - [Sybase ~pg时间测试][PG - Copy] - Apply table structure to target database 
[TRACE] 2025-05-08 00:28:55.832 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 is going to be initial synced 
[TRACE] 2025-05-08 00:28:55.869 - [Sybase ~pg时间测试][LocalSybase] - Query snapshot row size completed: LocalSybase(ecf2fe24-1395-49b6-9448-da59d864cc1e) 
[INFO ] 2025-05-08 00:28:55.870 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 has been completed batch read 
[TRACE] 2025-05-08 00:28:55.870 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[INFO ] 2025-05-08 00:28:55.870 - [Sybase ~pg时间测试][LocalSybase] - Batch read completed. 
[TRACE] 2025-05-08 00:28:55.870 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync starting... 
[TRACE] 2025-05-08 00:28:55.870 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[TRACE] 2025-05-08 00:28:55.871 - [Sybase ~pg时间测试][LocalSybase] - Starting stream read, table list: [testrslt, time_test2], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:28:55.904 - [Sybase ~pg时间测试][LocalSybase] - Starting incremental sync using database log parser 
[INFO ] 2025-05-08 00:28:55.906 - [Sybase ~pg时间测试][LocalSybase] - startRid: 525072, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:28:55.906 - [Sybase ~pg时间测试][LocalSybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:28:55.906 - [Sybase ~pg时间测试][LocalSybase] - sybase offset in database is: startRid: 525072, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-05-08 00:28:55.906 - [Sybase ~pg时间测试][LocalSybase] - we will use offset in database, how ever, this is safe: startRid: 525072, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:28:56.123 - [Sybase ~pg时间测试][LocalSybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-05-08 00:28:56.137 - [Sybase ~pg时间测试][LocalSybase] - sp_config_rep_agent disabled, database: s1 
[INFO ] 2025-05-08 00:28:56.137 - [Sybase ~pg时间测试][LocalSybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-05-08 00:28:56.157 - [Sybase ~pg时间测试][LocalSybase] - opened cdc for tables: {dbo=[testrslt, time_test2]} 
[INFO ] 2025-05-08 00:28:56.157 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:28:56.244 - [Sybase ~pg时间测试][LocalSybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-05-08 00:28:56.245 - [Sybase ~pg时间测试][LocalSybase] - sybase cdc debug log is disabled 
[INFO ] 2025-05-08 00:28:56.294 - [Sybase ~pg时间测试][LocalSybase] - trans timestamp offset: 0 
[INFO ] 2025-05-08 00:28:56.299 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525072, rowId: 0 
[INFO ] 2025-05-08 00:28:56.415 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525072, 0, size: 7, cost: 114 ms 
[INFO ] 2025-05-08 00:28:56.417 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 32, will rescan it until it's enough: 999, or loop: 1 to 5 
[TRACE] 2025-05-08 00:28:56.447 - [Sybase ~pg时间测试][PG - Copy] - Process after table "time_test2" initial sync finished, cost: 0 ms 
[TRACE] 2025-05-08 00:28:56.447 - [Sybase ~pg时间测试][PG - Copy] - Process after table "testrslt" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-08 00:28:56.448 - [Sybase ~pg时间测试][PG - Copy] - Process after all table(s) initial sync are finished，table number: 2 
[INFO ] 2025-05-08 00:28:58.580 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525072, 0, size: 7, cost: 144 ms 
[INFO ] 2025-05-08 00:28:58.586 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 32, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:29:00.757 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525072, 0, size: 7, cost: 153 ms 
[INFO ] 2025-05-08 00:29:00.762 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 32, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:29:02.930 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525072, 0, size: 7, cost: 150 ms 
[INFO ] 2025-05-08 00:29:02.935 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 32, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:29:05.045 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525072, 0, size: 7, cost: 97 ms 
[INFO ] 2025-05-08 00:29:05.057 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 32, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:29:07.062 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:29:07.137 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525073, rowId: 5 
[INFO ] 2025-05-08 00:29:07.137 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525073, 5, size: 1, cost: 56 ms 
[INFO ] 2025-05-08 00:29:07.137 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:29:09.290 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525073, 5, size: 1, cost: 127 ms 
[INFO ] 2025-05-08 00:29:09.295 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:29:11.421 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525073, 5, size: 1, cost: 111 ms 
[INFO ] 2025-05-08 00:29:11.426 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:29:13.570 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525073, 5, size: 1, cost: 124 ms 
[INFO ] 2025-05-08 00:29:13.577 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:29:15.717 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525073, 5, size: 1, cost: 130 ms 
[INFO ] 2025-05-08 00:29:15.723 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:29:17.771 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:29:17.777 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525073, rowId: 5 
[INFO ] 2025-05-08 00:29:27.954 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525073, 5, size: 1, cost: 163 ms 
[INFO ] 2025-05-08 00:29:27.960 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:29:30.116 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525073, 5, size: 1, cost: 138 ms 
[INFO ] 2025-05-08 00:29:30.122 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:29:32.280 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525073, 5, size: 1, cost: 138 ms 
[INFO ] 2025-05-08 00:29:32.285 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:29:34.431 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525073, 5, size: 1, cost: 133 ms 
[INFO ] 2025-05-08 00:29:34.436 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:29:36.553 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525073, 5, size: 1, cost: 105 ms 
[INFO ] 2025-05-08 00:29:36.557 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:29:38.599 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:29:38.603 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525073, rowId: 5 
[INFO ] 2025-05-08 00:29:48.709 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525073, 5, size: 1, cost: 109 ms 
[INFO ] 2025-05-08 00:29:48.709 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:29:50.783 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525073, 5, size: 1, cost: 71 ms 
[INFO ] 2025-05-08 00:29:50.783 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:29:52.951 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525073, 5, size: 1, cost: 136 ms 
[INFO ] 2025-05-08 00:29:52.955 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[TRACE] 2025-05-08 00:29:53.493 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] running status set to false 
[INFO ] 2025-05-08 00:29:53.561 - [Sybase ~pg时间测试][LocalSybase] - Log Miner is shutting down... 
[INFO ] 2025-05-08 00:29:53.567 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525073, 5, size: 1, cost: 58 ms 
[INFO ] 2025-05-08 00:29:53.569 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[TRACE] 2025-05-08 00:29:53.586 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746635335223 
[TRACE] 2025-05-08 00:29:53.586 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node released: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746635335223 
[TRACE] 2025-05-08 00:29:53.586 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] schema data cleaned 
[TRACE] 2025-05-08 00:29:53.590 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] monitor closed 
[TRACE] 2025-05-08 00:29:53.590 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] close complete, cost 97 ms 
[TRACE] 2025-05-08 00:29:53.621 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] running status set to false 
[TRACE] 2025-05-08 00:29:53.622 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node stopped: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746635335492 
[TRACE] 2025-05-08 00:29:53.622 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node released: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746635335492 
[TRACE] 2025-05-08 00:29:53.622 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] schema data cleaned 
[TRACE] 2025-05-08 00:29:53.623 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] monitor closed 
[TRACE] 2025-05-08 00:29:53.623 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] close complete, cost 32 ms 
[TRACE] 2025-05-08 00:29:54.071 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:29:54.073 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@cba6537 
[TRACE] 2025-05-08 00:29:54.078 - [Sybase ~pg时间测试] - Stop task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试)  
[TRACE] 2025-05-08 00:29:54.213 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[TRACE] 2025-05-08 00:29:54.213 - [Sybase ~pg时间测试] - Snapshot order controller have been removed 
[INFO ] 2025-05-08 00:29:54.428 - [Sybase ~pg时间测试] - Task stopped. 
[INFO ] 2025-05-08 00:29:55.711 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525073, 5, size: 1, cost: 131 ms 
[INFO ] 2025-05-08 00:29:55.716 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:29:57.752 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[TRACE] 2025-05-08 00:29:59.254 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:29:59.254 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@cba6537 
[TRACE] 2025-05-08 00:29:59.255 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[INFO ] 2025-05-08 00:29:59.255 - [Sybase ~pg时间测试] - Task stopped. 
[TRACE] 2025-05-08 00:29:59.462 - [Sybase ~pg时间测试] - Remove memory task client succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:29:59.462 - [Sybase ~pg时间测试] - Destroy memory task client cache succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:30:22.119 - [Sybase ~pg时间测试] - Task initialization... 
[TRACE] 2025-05-08 00:30:22.331 - [Sybase ~pg时间测试] - Start task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试) 
[INFO ] 2025-05-08 00:30:22.486 - [Sybase ~pg时间测试] - Loading table structure completed 
[TRACE] 2025-05-08 00:30:23.036 - [Sybase ~pg时间测试] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-08 00:30:23.039 - [Sybase ~pg时间测试] - The engine receives Sybase ~pg时间测试 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-08 00:30:23.173 - [Sybase ~pg时间测试] - Task started 
[TRACE] 2025-05-08 00:30:23.318 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:30:23.342 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:30:23.346 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 00:30:23.354 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] preload schema finished, cost 0 ms 
[INFO ] 2025-05-08 00:30:24.436 - [Sybase ~pg时间测试][LocalSybase] - Source connector(LocalSybase) initialization completed 
[TRACE] 2025-05-08 00:30:24.452 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" read batch size: 100 
[TRACE] 2025-05-08 00:30:24.457 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" event queue capacity: 200 
[TRACE] 2025-05-08 00:30:24.466 - [Sybase ~pg时间测试][LocalSybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-08 00:30:24.470 - [Sybase ~pg时间测试][PG - Copy] - Sink connector(PG - Copy) initialization completed 
[INFO ] 2025-05-08 00:30:24.475 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[TRACE] 2025-05-08 00:30:24.480 - [Sybase ~pg时间测试][PG - Copy] - Node(PG - Copy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-08 00:30:24.484 - [Sybase ~pg时间测试][PG - Copy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-08 00:30:24.571 - [Sybase ~pg时间测试][PG - Copy] - Apply table structure to target database 
[INFO ] 2025-05-08 00:30:24.576 - [Sybase ~pg时间测试][LocalSybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-05-08 00:30:24.730 - [Sybase ~pg时间测试][LocalSybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:30:24.731 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from 2 tables 
[TRACE] 2025-05-08 00:30:24.746 - [Sybase ~pg时间测试][LocalSybase] - Initial sync started 
[INFO ] 2025-05-08 00:30:24.747 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: testrslt 
[TRACE] 2025-05-08 00:30:24.750 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt is going to be initial synced 
[INFO ] 2025-05-08 00:30:24.819 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt has been completed batch read 
[INFO ] 2025-05-08 00:30:24.827 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: time_test2 
[TRACE] 2025-05-08 00:30:24.830 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 is going to be initial synced 
[TRACE] 2025-05-08 00:31:05.912 - [Sybase ~pg时间测试][LocalSybase] - Query snapshot row size completed: LocalSybase(ecf2fe24-1395-49b6-9448-da59d864cc1e) 
[INFO ] 2025-05-08 00:31:31.425 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 has been completed batch read 
[TRACE] 2025-05-08 00:31:31.444 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[INFO ] 2025-05-08 00:31:31.448 - [Sybase ~pg时间测试][LocalSybase] - Batch read completed. 
[TRACE] 2025-05-08 00:31:31.464 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync starting... 
[TRACE] 2025-05-08 00:31:34.394 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[TRACE] 2025-05-08 00:31:34.547 - [Sybase ~pg时间测试][LocalSybase] - Starting stream read, table list: [testrslt, time_test2], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:31:34.552 - [Sybase ~pg时间测试][LocalSybase] - Starting incremental sync using database log parser 
[INFO ] 2025-05-08 00:31:52.040 - [Sybase ~pg时间测试][LocalSybase] - startRid: 525073, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:31:52.045 - [Sybase ~pg时间测试][LocalSybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:31:52.049 - [Sybase ~pg时间测试][LocalSybase] - sybase offset in database is: startRid: 525073, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-05-08 00:31:52.063 - [Sybase ~pg时间测试][LocalSybase] - we will use offset in database, how ever, this is safe: startRid: 525073, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:31:52.204 - [Sybase ~pg时间测试][LocalSybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-05-08 00:31:52.209 - [Sybase ~pg时间测试][LocalSybase] - sp_config_rep_agent disabled, database: s1 
[INFO ] 2025-05-08 00:31:52.241 - [Sybase ~pg时间测试][LocalSybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-05-08 00:31:52.244 - [Sybase ~pg时间测试][LocalSybase] - opened cdc for tables: {dbo=[testrslt, time_test2]} 
[INFO ] 2025-05-08 00:31:52.247 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:31:52.288 - [Sybase ~pg时间测试][LocalSybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-05-08 00:31:52.343 - [Sybase ~pg时间测试][LocalSybase] - sybase cdc debug log is disabled 
[INFO ] 2025-05-08 00:31:52.345 - [Sybase ~pg时间测试][LocalSybase] - trans timestamp offset: 0 
[INFO ] 2025-05-08 00:31:52.347 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525073, rowId: 0 
[INFO ] 2025-05-08 00:31:52.396 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525073, 0, size: 5, cost: 51 ms 
[INFO ] 2025-05-08 00:31:52.396 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 25, will rescan it until it's enough: 999, or loop: 1 to 5 
[TRACE] 2025-05-08 00:32:39.675 - [Sybase ~pg时间测试][PG - Copy] - Process after table "time_test2" initial sync finished, cost: 0 ms 
[TRACE] 2025-05-08 00:32:39.690 - [Sybase ~pg时间测试][PG - Copy] - Process after table "testrslt" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-08 00:32:39.693 - [Sybase ~pg时间测试][PG - Copy] - Process after all table(s) initial sync are finished，table number: 2 
[INFO ] 2025-05-08 00:32:39.734 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525073, 0, size: 5, cost: 2011 ms 
[INFO ] 2025-05-08 00:32:39.734 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 25, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:32:41.802 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525073, 0, size: 5, cost: 60 ms 
[INFO ] 2025-05-08 00:32:41.802 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 25, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:32:43.895 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525073, 0, size: 5, cost: 88 ms 
[INFO ] 2025-05-08 00:32:43.898 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 25, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:32:46.037 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525073, 0, size: 5, cost: 132 ms 
[INFO ] 2025-05-08 00:32:46.040 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 25, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:32:48.062 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:32:48.072 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525073, rowId: 24 
[INFO ] 2025-05-08 00:32:48.145 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525073, 24, size: 1, cost: 84 ms 
[INFO ] 2025-05-08 00:32:48.145 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:32:50.211 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525073, 24, size: 1, cost: 48 ms 
[INFO ] 2025-05-08 00:32:50.426 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:32:52.274 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525073, 24, size: 1, cost: 48 ms 
[INFO ] 2025-05-08 00:32:52.274 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:32:54.409 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525073, 24, size: 1, cost: 86 ms 
[INFO ] 2025-05-08 00:32:54.410 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:32:56.550 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525073, 24, size: 1, cost: 125 ms 
[INFO ] 2025-05-08 00:32:56.552 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:32:58.555 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:32:58.556 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525073, rowId: 24 
[INFO ] 2025-05-08 00:33:08.637 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525073, 24, size: 1, cost: 70 ms 
[INFO ] 2025-05-08 00:33:08.638 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:33:10.705 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525073, 24, size: 1, cost: 61 ms 
[INFO ] 2025-05-08 00:33:10.705 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:33:12.806 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525073, 24, size: 1, cost: 88 ms 
[INFO ] 2025-05-08 00:33:12.807 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:33:14.846 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525073, 24, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 00:33:14.847 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:33:16.920 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525073, 24, size: 1, cost: 60 ms 
[INFO ] 2025-05-08 00:33:17.126 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:33:18.938 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:33:18.938 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525073, rowId: 24 
[INFO ] 2025-05-08 00:33:29.004 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525073, 24, size: 1, cost: 55 ms 
[INFO ] 2025-05-08 00:33:29.005 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:33:31.075 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525073, 24, size: 1, cost: 65 ms 
[INFO ] 2025-05-08 00:33:31.076 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:33:33.132 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525073, 24, size: 1, cost: 50 ms 
[INFO ] 2025-05-08 00:33:33.132 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:33:35.207 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525073, 24, size: 1, cost: 70 ms 
[INFO ] 2025-05-08 00:33:35.208 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:33:37.263 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525073, 24, size: 1, cost: 51 ms 
[INFO ] 2025-05-08 00:33:37.264 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:33:39.268 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:33:39.270 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525073, rowId: 24 
[INFO ] 2025-05-08 00:33:49.396 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525073, 24, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:33:49.396 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:33:51.344 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525073, 24, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 00:33:51.344 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:33:53.403 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525073, 24, size: 1, cost: 52 ms 
[INFO ] 2025-05-08 00:33:53.404 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:33:55.428 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525073, 24, size: 1, cost: 19 ms 
[INFO ] 2025-05-08 00:33:55.428 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:33:57.500 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525073, 24, size: 1, cost: 61 ms 
[INFO ] 2025-05-08 00:33:57.705 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:33:59.517 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:33:59.518 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525073, rowId: 24 
[TRACE] 2025-05-08 00:34:08.662 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] running status set to false 
[INFO ] 2025-05-08 00:34:08.663 - [Sybase ~pg时间测试][LocalSybase] - Log Miner is shutting down... 
[INFO ] 2025-05-08 00:34:08.717 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525073, 24, size: 1, cost: 53 ms 
[INFO ] 2025-05-08 00:34:08.717 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[TRACE] 2025-05-08 00:34:08.773 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746635424345 
[TRACE] 2025-05-08 00:34:08.773 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node released: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746635424345 
[TRACE] 2025-05-08 00:34:08.774 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] schema data cleaned 
[TRACE] 2025-05-08 00:34:08.774 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] monitor closed 
[TRACE] 2025-05-08 00:34:08.777 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] close complete, cost 116 ms 
[TRACE] 2025-05-08 00:34:08.777 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] running status set to false 
[TRACE] 2025-05-08 00:34:08.796 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node stopped: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746635424215 
[TRACE] 2025-05-08 00:34:08.796 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node released: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746635424215 
[TRACE] 2025-05-08 00:34:08.796 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] schema data cleaned 
[TRACE] 2025-05-08 00:34:08.799 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] monitor closed 
[TRACE] 2025-05-08 00:34:08.799 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] close complete, cost 21 ms 
[TRACE] 2025-05-08 00:34:09.644 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:34:09.659 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5ca27bf6 
[TRACE] 2025-05-08 00:34:09.660 - [Sybase ~pg时间测试] - Stop task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试)  
[TRACE] 2025-05-08 00:34:09.820 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[TRACE] 2025-05-08 00:34:09.829 - [Sybase ~pg时间测试] - Snapshot order controller have been removed 
[INFO ] 2025-05-08 00:34:09.830 - [Sybase ~pg时间测试] - Task stopped. 
[INFO ] 2025-05-08 00:34:10.812 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525073, 24, size: 1, cost: 67 ms 
[INFO ] 2025-05-08 00:34:10.813 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:34:12.843 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525073, 24, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:34:12.843 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[TRACE] 2025-05-08 00:34:14.869 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:34:14.873 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5ca27bf6 
[TRACE] 2025-05-08 00:34:14.873 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[INFO ] 2025-05-08 00:34:14.873 - [Sybase ~pg时间测试] - Task stopped. 
[TRACE] 2025-05-08 00:34:14.921 - [Sybase ~pg时间测试] - Remove memory task client succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:34:14.924 - [Sybase ~pg时间测试] - Destroy memory task client cache succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[INFO ] 2025-05-08 00:34:14.925 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525073, 24, size: 1, cost: 72 ms 
[INFO ] 2025-05-08 00:34:14.925 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:34:17.031 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525073, 24, size: 1, cost: 100 ms 
[INFO ] 2025-05-08 00:34:17.031 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:34:19.070 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[TRACE] 2025-05-08 00:34:50.676 - [Sybase ~pg时间测试] - Task initialization... 
[TRACE] 2025-05-08 00:34:50.888 - [Sybase ~pg时间测试] - Start task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试) 
[INFO ] 2025-05-08 00:34:51.126 - [Sybase ~pg时间测试] - Loading table structure completed 
[TRACE] 2025-05-08 00:34:51.228 - [Sybase ~pg时间测试] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-08 00:34:51.228 - [Sybase ~pg时间测试] - The engine receives Sybase ~pg时间测试 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-08 00:34:51.315 - [Sybase ~pg时间测试] - Task started 
[TRACE] 2025-05-08 00:34:51.316 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:34:51.316 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:34:51.316 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 00:34:51.316 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] preload schema finished, cost 0 ms 
[INFO ] 2025-05-08 00:34:52.083 - [Sybase ~pg时间测试][LocalSybase] - Source connector(LocalSybase) initialization completed 
[TRACE] 2025-05-08 00:34:52.087 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" read batch size: 100 
[TRACE] 2025-05-08 00:34:52.090 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" event queue capacity: 200 
[TRACE] 2025-05-08 00:34:52.090 - [Sybase ~pg时间测试][LocalSybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-08 00:34:52.090 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:34:52.138 - [Sybase ~pg时间测试][LocalSybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-05-08 00:34:52.138 - [Sybase ~pg时间测试][LocalSybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:34:52.207 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from 2 tables 
[TRACE] 2025-05-08 00:34:52.207 - [Sybase ~pg时间测试][LocalSybase] - Initial sync started 
[INFO ] 2025-05-08 00:34:52.208 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: testrslt 
[TRACE] 2025-05-08 00:34:52.208 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt is going to be initial synced 
[INFO ] 2025-05-08 00:34:52.245 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt has been completed batch read 
[INFO ] 2025-05-08 00:34:52.245 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: time_test2 
[TRACE] 2025-05-08 00:34:52.245 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 is going to be initial synced 
[TRACE] 2025-05-08 00:34:52.256 - [Sybase ~pg时间测试][LocalSybase] - Query snapshot row size completed: LocalSybase(ecf2fe24-1395-49b6-9448-da59d864cc1e) 
[INFO ] 2025-05-08 00:34:56.270 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 has been completed batch read 
[TRACE] 2025-05-08 00:34:56.271 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[INFO ] 2025-05-08 00:34:56.271 - [Sybase ~pg时间测试][LocalSybase] - Batch read completed. 
[TRACE] 2025-05-08 00:34:56.271 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync starting... 
[TRACE] 2025-05-08 00:34:56.271 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[TRACE] 2025-05-08 00:34:56.272 - [Sybase ~pg时间测试][LocalSybase] - Starting stream read, table list: [testrslt, time_test2], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:34:56.276 - [Sybase ~pg时间测试][LocalSybase] - Starting incremental sync using database log parser 
[INFO ] 2025-05-08 00:34:56.312 - [Sybase ~pg时间测试][LocalSybase] - startRid: 525074, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:34:56.312 - [Sybase ~pg时间测试][LocalSybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:34:56.312 - [Sybase ~pg时间测试][LocalSybase] - sybase offset in database is: startRid: 525074, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-05-08 00:34:56.316 - [Sybase ~pg时间测试][LocalSybase] - we will use offset in database, how ever, this is safe: startRid: 525074, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:34:56.364 - [Sybase ~pg时间测试][LocalSybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-05-08 00:34:56.364 - [Sybase ~pg时间测试][LocalSybase] - sp_config_rep_agent disabled, database: s1 
[INFO ] 2025-05-08 00:34:56.369 - [Sybase ~pg时间测试][LocalSybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-05-08 00:34:56.369 - [Sybase ~pg时间测试][PG - Copy] - Sink connector(PG - Copy) initialization completed 
[TRACE] 2025-05-08 00:34:56.369 - [Sybase ~pg时间测试][PG - Copy] - Node(PG - Copy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-08 00:34:56.370 - [Sybase ~pg时间测试][PG - Copy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-08 00:34:56.385 - [Sybase ~pg时间测试][LocalSybase] - opened cdc for tables: {dbo=[testrslt, time_test2]} 
[INFO ] 2025-05-08 00:34:56.386 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:34:56.403 - [Sybase ~pg时间测试][PG - Copy] - Apply table structure to target database 
[INFO ] 2025-05-08 00:34:56.403 - [Sybase ~pg时间测试][LocalSybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-05-08 00:34:56.436 - [Sybase ~pg时间测试][LocalSybase] - sybase cdc debug log is disabled 
[INFO ] 2025-05-08 00:34:56.436 - [Sybase ~pg时间测试][LocalSybase] - trans timestamp offset: 0 
[INFO ] 2025-05-08 00:34:56.436 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525074, rowId: 0 
[INFO ] 2025-05-08 00:34:56.453 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525074, 0, size: 3, cost: 16 ms 
[INFO ] 2025-05-08 00:34:56.453 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:36:02.929 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525074, 0, size: 3, cost: 63650 ms 
[INFO ] 2025-05-08 00:36:02.930 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 2 to 5 
[TRACE] 2025-05-08 00:36:03.412 - [Sybase ~pg时间测试][PG - Copy] - Process after table "time_test2" initial sync finished, cost: 0 ms 
[TRACE] 2025-05-08 00:36:03.413 - [Sybase ~pg时间测试][PG - Copy] - Process after table "testrslt" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-08 00:36:03.413 - [Sybase ~pg时间测试][PG - Copy] - Process after all table(s) initial sync are finished，table number: 2 
[INFO ] 2025-05-08 00:36:04.990 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525074, 0, size: 3, cost: 55 ms 
[INFO ] 2025-05-08 00:36:04.990 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:36:07.040 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525074, 0, size: 3, cost: 44 ms 
[INFO ] 2025-05-08 00:36:07.040 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:36:09.104 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525074, 0, size: 3, cost: 58 ms 
[INFO ] 2025-05-08 00:36:09.105 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:36:11.110 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:36:11.161 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525074, rowId: 16 
[INFO ] 2025-05-08 00:36:11.161 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525074, 16, size: 1, cost: 49 ms 
[INFO ] 2025-05-08 00:36:11.161 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:36:13.207 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525074, 16, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:36:13.208 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:36:15.268 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525074, 16, size: 1, cost: 56 ms 
[INFO ] 2025-05-08 00:36:15.268 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:36:17.337 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525074, 16, size: 1, cost: 63 ms 
[INFO ] 2025-05-08 00:36:17.337 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:36:19.401 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525074, 16, size: 1, cost: 59 ms 
[INFO ] 2025-05-08 00:36:19.402 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:36:21.405 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:36:21.405 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525074, rowId: 16 
[TRACE] 2025-05-08 00:36:27.462 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] running status set to false 
[INFO ] 2025-05-08 00:36:27.463 - [Sybase ~pg时间测试][LocalSybase] - Log Miner is shutting down... 
[INFO ] 2025-05-08 00:36:27.530 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525074, 16, size: 1, cost: 67 ms 
[INFO ] 2025-05-08 00:36:27.530 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[TRACE] 2025-05-08 00:36:27.540 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746635691997 
[TRACE] 2025-05-08 00:36:27.540 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node released: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746635691997 
[TRACE] 2025-05-08 00:36:27.540 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] schema data cleaned 
[TRACE] 2025-05-08 00:36:27.541 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] monitor closed 
[TRACE] 2025-05-08 00:36:27.543 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] close complete, cost 85 ms 
[TRACE] 2025-05-08 00:36:27.544 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] running status set to false 
[TRACE] 2025-05-08 00:36:27.561 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node stopped: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746635692224 
[TRACE] 2025-05-08 00:36:27.561 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node released: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746635692224 
[TRACE] 2025-05-08 00:36:27.561 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] schema data cleaned 
[TRACE] 2025-05-08 00:36:27.561 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] monitor closed 
[TRACE] 2025-05-08 00:36:27.765 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] close complete, cost 19 ms 
[TRACE] 2025-05-08 00:36:27.865 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:36:27.866 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4d252b07 
[TRACE] 2025-05-08 00:36:27.995 - [Sybase ~pg时间测试] - Stop task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试)  
[TRACE] 2025-05-08 00:36:27.995 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[TRACE] 2025-05-08 00:36:27.995 - [Sybase ~pg时间测试] - Snapshot order controller have been removed 
[INFO ] 2025-05-08 00:36:27.997 - [Sybase ~pg时间测试] - Task stopped. 
[INFO ] 2025-05-08 00:36:29.612 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525074, 16, size: 1, cost: 75 ms 
[INFO ] 2025-05-08 00:36:29.613 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:36:31.674 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525074, 16, size: 1, cost: 57 ms 
[INFO ] 2025-05-08 00:36:31.674 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[TRACE] 2025-05-08 00:36:33.024 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:36:33.025 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4d252b07 
[TRACE] 2025-05-08 00:36:33.025 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[INFO ] 2025-05-08 00:36:33.025 - [Sybase ~pg时间测试] - Task stopped. 
[TRACE] 2025-05-08 00:36:33.061 - [Sybase ~pg时间测试] - Remove memory task client succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:36:33.066 - [Sybase ~pg时间测试] - Destroy memory task client cache succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[INFO ] 2025-05-08 00:36:33.741 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525074, 16, size: 1, cost: 60 ms 
[INFO ] 2025-05-08 00:36:33.742 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:36:35.835 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525074, 16, size: 1, cost: 73 ms 
[INFO ] 2025-05-08 00:36:35.836 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:36:37.848 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[TRACE] 2025-05-08 00:36:41.328 - [Sybase ~pg时间测试] - Task initialization... 
[TRACE] 2025-05-08 00:36:41.531 - [Sybase ~pg时间测试] - Start task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试) 
[INFO ] 2025-05-08 00:36:41.634 - [Sybase ~pg时间测试] - Loading table structure completed 
[TRACE] 2025-05-08 00:36:41.733 - [Sybase ~pg时间测试] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-08 00:36:41.733 - [Sybase ~pg时间测试] - The engine receives Sybase ~pg时间测试 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-08 00:36:41.780 - [Sybase ~pg时间测试] - Task started 
[TRACE] 2025-05-08 00:36:41.780 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:36:41.780 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 00:36:41.780 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:36:41.780 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] preload schema finished, cost 0 ms 
[INFO ] 2025-05-08 00:36:42.459 - [Sybase ~pg时间测试][LocalSybase] - Source connector(LocalSybase) initialization completed 
[TRACE] 2025-05-08 00:36:42.463 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" read batch size: 100 
[TRACE] 2025-05-08 00:36:42.463 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" event queue capacity: 200 
[TRACE] 2025-05-08 00:36:42.463 - [Sybase ~pg时间测试][LocalSybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-08 00:36:42.463 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:36:42.540 - [Sybase ~pg时间测试][LocalSybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-05-08 00:36:42.541 - [Sybase ~pg时间测试][LocalSybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:36:42.635 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from 2 tables 
[TRACE] 2025-05-08 00:36:42.635 - [Sybase ~pg时间测试][LocalSybase] - Initial sync started 
[INFO ] 2025-05-08 00:36:42.635 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: testrslt 
[TRACE] 2025-05-08 00:36:42.635 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt is going to be initial synced 
[INFO ] 2025-05-08 00:36:42.696 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt has been completed batch read 
[INFO ] 2025-05-08 00:36:42.697 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: time_test2 
[TRACE] 2025-05-08 00:36:42.698 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 is going to be initial synced 
[TRACE] 2025-05-08 00:36:45.810 - [Sybase ~pg时间测试][LocalSybase] - Query snapshot row size completed: LocalSybase(ecf2fe24-1395-49b6-9448-da59d864cc1e) 
[INFO ] 2025-05-08 00:36:47.006 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 has been completed batch read 
[TRACE] 2025-05-08 00:36:47.009 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[INFO ] 2025-05-08 00:36:47.009 - [Sybase ~pg时间测试][LocalSybase] - Batch read completed. 
[TRACE] 2025-05-08 00:36:47.010 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync starting... 
[TRACE] 2025-05-08 00:36:47.014 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[TRACE] 2025-05-08 00:36:47.014 - [Sybase ~pg时间测试][LocalSybase] - Starting stream read, table list: [testrslt, time_test2], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:36:47.014 - [Sybase ~pg时间测试][LocalSybase] - Starting incremental sync using database log parser 
[INFO ] 2025-05-08 00:36:47.040 - [Sybase ~pg时间测试][PG - Copy] - Sink connector(PG - Copy) initialization completed 
[TRACE] 2025-05-08 00:36:47.042 - [Sybase ~pg时间测试][PG - Copy] - Node(PG - Copy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-08 00:36:47.042 - [Sybase ~pg时间测试][PG - Copy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-08 00:36:47.043 - [Sybase ~pg时间测试][LocalSybase] - startRid: 525074, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:36:47.046 - [Sybase ~pg时间测试][LocalSybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:36:47.047 - [Sybase ~pg时间测试][LocalSybase] - sybase offset in database is: startRid: 525074, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-05-08 00:36:47.047 - [Sybase ~pg时间测试][LocalSybase] - we will use offset in database, how ever, this is safe: startRid: 525074, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:36:47.053 - [Sybase ~pg时间测试][LocalSybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-05-08 00:36:47.054 - [Sybase ~pg时间测试][PG - Copy] - Apply table structure to target database 
[INFO ] 2025-05-08 00:36:47.091 - [Sybase ~pg时间测试][LocalSybase] - sp_config_rep_agent disabled, database: s1 
[INFO ] 2025-05-08 00:36:47.091 - [Sybase ~pg时间测试][LocalSybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-05-08 00:36:47.108 - [Sybase ~pg时间测试][LocalSybase] - opened cdc for tables: {dbo=[testrslt, time_test2]} 
[INFO ] 2025-05-08 00:36:47.109 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:36:47.148 - [Sybase ~pg时间测试][LocalSybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-05-08 00:36:47.148 - [Sybase ~pg时间测试][LocalSybase] - sybase cdc debug log is disabled 
[INFO ] 2025-05-08 00:36:47.161 - [Sybase ~pg时间测试][LocalSybase] - trans timestamp offset: 0 
[INFO ] 2025-05-08 00:36:47.161 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525074, rowId: 0 
[INFO ] 2025-05-08 00:36:47.182 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525074, 0, size: 7, cost: 20 ms 
[INFO ] 2025-05-08 00:36:47.182 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 33, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:37:17.091 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525074, 0, size: 7, cost: 14431 ms 
[INFO ] 2025-05-08 00:37:17.871 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 33, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:37:45.697 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525074, 0, size: 7, cost: 24837 ms 
[INFO ] 2025-05-08 00:37:45.697 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 33, will rescan it until it's enough: 999, or loop: 3 to 5 
[TRACE] 2025-05-08 00:38:20.573 - [Sybase ~pg时间测试][PG - Copy] - Process after table "testrslt" initial sync finished, cost: 0 ms 
[TRACE] 2025-05-08 00:38:20.574 - [Sybase ~pg时间测试][PG - Copy] - Process after table "time_test2" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-08 00:38:20.574 - [Sybase ~pg时间测试][PG - Copy] - Process after all table(s) initial sync are finished，table number: 2 
[INFO ] 2025-05-08 00:38:25.125 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525074, 0, size: 7, cost: 36373 ms 
[INFO ] 2025-05-08 00:38:25.125 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 33, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:38:27.196 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525074, 0, size: 7, cost: 65 ms 
[INFO ] 2025-05-08 00:38:27.196 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 33, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:38:29.205 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:38:29.211 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525075, rowId: 5 
[INFO ] 2025-05-08 00:38:29.250 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525075, 5, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 00:38:29.250 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:38:31.364 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525075, 5, size: 1, cost: 94 ms 
[INFO ] 2025-05-08 00:38:31.369 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:38:33.399 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525075, 5, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 00:38:33.400 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:38:35.436 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525075, 5, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 00:38:35.436 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:38:37.479 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525075, 5, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 00:38:37.479 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:38:39.484 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:38:39.484 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525075, rowId: 5 
[INFO ] 2025-05-08 00:38:49.517 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525075, 5, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:38:49.517 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:38:51.552 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525075, 5, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:38:51.553 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:38:53.589 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525075, 5, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 00:38:53.590 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:38:55.628 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525075, 5, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 00:38:55.628 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:38:57.661 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525075, 5, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 00:38:57.661 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:38:59.663 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:38:59.663 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525075, rowId: 5 
[INFO ] 2025-05-08 00:39:09.697 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525075, 5, size: 1, cost: 28 ms 
[INFO ] 2025-05-08 00:39:09.698 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:39:11.777 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525075, 5, size: 1, cost: 73 ms 
[INFO ] 2025-05-08 00:39:11.777 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:39:13.824 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525075, 5, size: 1, cost: 44 ms 
[INFO ] 2025-05-08 00:39:13.824 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:39:15.873 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525075, 5, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:39:15.874 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:39:17.923 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525075, 5, size: 1, cost: 48 ms 
[INFO ] 2025-05-08 00:39:17.924 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:39:19.927 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:39:19.927 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525075, rowId: 5 
[INFO ] 2025-05-08 00:39:29.996 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525075, 5, size: 1, cost: 63 ms 
[INFO ] 2025-05-08 00:39:29.996 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[TRACE] 2025-05-08 00:39:31.294 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] running status set to false 
[INFO ] 2025-05-08 00:39:31.295 - [Sybase ~pg时间测试][LocalSybase] - Log Miner is shutting down... 
[INFO ] 2025-05-08 00:39:31.358 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525075, 5, size: 1, cost: 63 ms 
[INFO ] 2025-05-08 00:39:31.358 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[TRACE] 2025-05-08 00:39:31.373 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746635802377 
[TRACE] 2025-05-08 00:39:31.374 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node released: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746635802377 
[TRACE] 2025-05-08 00:39:31.374 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] schema data cleaned 
[TRACE] 2025-05-08 00:39:31.376 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] monitor closed 
[TRACE] 2025-05-08 00:39:31.380 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] close complete, cost 86 ms 
[TRACE] 2025-05-08 00:39:31.381 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] running status set to false 
[TRACE] 2025-05-08 00:39:31.393 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node stopped: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746635802609 
[TRACE] 2025-05-08 00:39:31.393 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node released: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746635802609 
[TRACE] 2025-05-08 00:39:31.393 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] schema data cleaned 
[TRACE] 2025-05-08 00:39:31.394 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] monitor closed 
[TRACE] 2025-05-08 00:39:31.394 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] close complete, cost 17 ms 
[INFO ] 2025-05-08 00:39:33.423 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525075, 5, size: 1, cost: 60 ms 
[INFO ] 2025-05-08 00:39:33.424 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[TRACE] 2025-05-08 00:39:35.168 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:39:35.183 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4fa03cdc 
[TRACE] 2025-05-08 00:39:35.183 - [Sybase ~pg时间测试] - Stop task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试)  
[TRACE] 2025-05-08 00:39:35.312 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[TRACE] 2025-05-08 00:39:35.313 - [Sybase ~pg时间测试] - Snapshot order controller have been removed 
[INFO ] 2025-05-08 00:39:35.313 - [Sybase ~pg时间测试] - Task stopped. 
[INFO ] 2025-05-08 00:39:35.506 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525075, 5, size: 1, cost: 76 ms 
[INFO ] 2025-05-08 00:39:35.507 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:39:37.577 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525075, 5, size: 1, cost: 67 ms 
[INFO ] 2025-05-08 00:39:37.578 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:39:39.584 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[TRACE] 2025-05-08 00:39:40.348 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:39:40.348 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4fa03cdc 
[TRACE] 2025-05-08 00:39:40.348 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[INFO ] 2025-05-08 00:39:40.349 - [Sybase ~pg时间测试] - Task stopped. 
[TRACE] 2025-05-08 00:39:40.383 - [Sybase ~pg时间测试] - Remove memory task client succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:39:40.385 - [Sybase ~pg时间测试] - Destroy memory task client cache succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:39:55.143 - [Sybase ~pg时间测试] - Task initialization... 
[TRACE] 2025-05-08 00:39:55.144 - [Sybase ~pg时间测试] - Start task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试) 
[INFO ] 2025-05-08 00:39:55.412 - [Sybase ~pg时间测试] - Loading table structure completed 
[TRACE] 2025-05-08 00:39:55.413 - [Sybase ~pg时间测试] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-08 00:39:55.488 - [Sybase ~pg时间测试] - The engine receives Sybase ~pg时间测试 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-08 00:39:55.488 - [Sybase ~pg时间测试] - Task started 
[TRACE] 2025-05-08 00:39:55.520 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:39:55.521 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:39:55.521 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 00:39:55.521 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] preload schema finished, cost 0 ms 
[INFO ] 2025-05-08 00:39:56.192 - [Sybase ~pg时间测试][LocalSybase] - Source connector(LocalSybase) initialization completed 
[TRACE] 2025-05-08 00:39:56.193 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" read batch size: 100 
[TRACE] 2025-05-08 00:39:56.193 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" event queue capacity: 200 
[TRACE] 2025-05-08 00:39:56.195 - [Sybase ~pg时间测试][LocalSybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-08 00:39:56.197 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:39:56.302 - [Sybase ~pg时间测试][LocalSybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-05-08 00:39:56.305 - [Sybase ~pg时间测试][LocalSybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:39:56.370 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from 2 tables 
[TRACE] 2025-05-08 00:39:56.370 - [Sybase ~pg时间测试][LocalSybase] - Initial sync started 
[INFO ] 2025-05-08 00:39:56.371 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: testrslt 
[TRACE] 2025-05-08 00:39:56.435 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt is going to be initial synced 
[INFO ] 2025-05-08 00:39:56.435 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt has been completed batch read 
[INFO ] 2025-05-08 00:39:56.435 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: time_test2 
[TRACE] 2025-05-08 00:39:56.436 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 is going to be initial synced 
[INFO ] 2025-05-08 00:39:56.451 - [Sybase ~pg时间测试][PG - Copy] - Sink connector(PG - Copy) initialization completed 
[TRACE] 2025-05-08 00:39:56.452 - [Sybase ~pg时间测试][PG - Copy] - Node(PG - Copy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-08 00:39:56.452 - [Sybase ~pg时间测试][PG - Copy] - Write batch size: 100, max wait ms per batch: 500 
[TRACE] 2025-05-08 00:39:56.467 - [Sybase ~pg时间测试][LocalSybase] - Query snapshot row size completed: LocalSybase(ecf2fe24-1395-49b6-9448-da59d864cc1e) 
[INFO ] 2025-05-08 00:39:56.468 - [Sybase ~pg时间测试][PG - Copy] - Apply table structure to target database 
[INFO ] 2025-05-08 00:39:56.511 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 has been completed batch read 
[TRACE] 2025-05-08 00:39:56.512 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[INFO ] 2025-05-08 00:39:56.517 - [Sybase ~pg时间测试][LocalSybase] - Batch read completed. 
[TRACE] 2025-05-08 00:39:56.517 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync starting... 
[TRACE] 2025-05-08 00:39:56.518 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[TRACE] 2025-05-08 00:39:56.518 - [Sybase ~pg时间测试][LocalSybase] - Starting stream read, table list: [testrslt, time_test2], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:39:56.518 - [Sybase ~pg时间测试][LocalSybase] - Starting incremental sync using database log parser 
[INFO ] 2025-05-08 00:39:56.539 - [Sybase ~pg时间测试][LocalSybase] - startRid: 525075, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:39:56.539 - [Sybase ~pg时间测试][LocalSybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:39:56.539 - [Sybase ~pg时间测试][LocalSybase] - sybase offset in database is: startRid: 525075, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-05-08 00:39:56.540 - [Sybase ~pg时间测试][LocalSybase] - we will use offset in database, how ever, this is safe: startRid: 525075, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:39:56.572 - [Sybase ~pg时间测试][LocalSybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-05-08 00:39:56.616 - [Sybase ~pg时间测试][LocalSybase] - sp_config_rep_agent disabled, database: s1 
[INFO ] 2025-05-08 00:39:56.616 - [Sybase ~pg时间测试][LocalSybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-05-08 00:39:56.634 - [Sybase ~pg时间测试][LocalSybase] - opened cdc for tables: {dbo=[testrslt, time_test2]} 
[INFO ] 2025-05-08 00:39:56.635 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:39:56.666 - [Sybase ~pg时间测试][LocalSybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-05-08 00:39:56.666 - [Sybase ~pg时间测试][LocalSybase] - sybase cdc debug log is disabled 
[INFO ] 2025-05-08 00:39:56.690 - [Sybase ~pg时间测试][LocalSybase] - trans timestamp offset: 0 
[INFO ] 2025-05-08 00:39:56.690 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525075, rowId: 0 
[INFO ] 2025-05-08 00:39:56.720 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525075, 0, size: 5, cost: 29 ms 
[INFO ] 2025-05-08 00:39:56.721 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 25, will rescan it until it's enough: 999, or loop: 1 to 5 
[WARN ] 2025-05-08 00:39:57.034 - [Sybase ~pg时间测试][PG - Copy] - writeRecord failed, dismantle them, size: 1 
[TRACE] 2025-05-08 00:39:57.060 - [Sybase ~pg时间测试][PG - Copy] - Exception skipping - The current exception does not match the skip exception strategy, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: time_test2 
[ERROR] 2025-05-08 00:39:57.062 - [Sybase ~pg时间测试][PG - Copy] - Execute PDK method: TARGET_WRITE_RECORD, tableName: time_test2 <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: time_test2

<-- Simple Stack Trace -->
Caused by: org.postgresql.util.PSQLException: ERROR: column "col_date" is of type date but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：144
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: time_test2
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1127)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:948)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:866)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:815)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:775)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:661)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:747)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:799)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:746)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: Error occurred when retrying write record: io.tapdata.entity.event.dml.TapInsertRecordEvent@1159f377: {"after":{"col_date":"\"2025-05-07\"","col_bigtime":"23:59:59.999999","col_time":"23:59:59.996","col_smalldatetime":"2025-05-07T23:59:00","col_bigdatetime":"2025-05-07T23:59:59.999999000","id":19,"col_datetime":"2025-05-07T23:59:59.996"},"containsIllegalDate":false,"database":"dbo","pdkGroup":"io.tapdata","pdkId":"sybase","pdkVersion":"1.0-SNAPSHOT","referenceTime":1746635996466,"tableId":"time_test2","time":1746635996466,"type":300}
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:164)
	at io.tapdata.common.dml.NormalRecordWriter.lambda$writePart$1(NormalRecordWriter.java:168)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:168)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:90)
	at io.tapdata.connector.postgres.PostgresConnector.writeRecord(PostgresConnector.java:485)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	... 26 more
Caused by: java.sql.BatchUpdateException: Batch entry 0 INSERT INTO "wim"."time_test2" ("id", "col_date", "col_time", "col_datetime", "col_smalldatetime", "col_bigdatetime", "col_bigtime") VALUES(('19'::int4),('"2025-05-07"'),('23:59:59.996'::time),('2025-05-07 23:59:59.996'::timestamp),('2025-05-07 23:59:00'::timestamp),('2025-05-07 23:59:59.999999'::timestamp),('23:59:59.999999'::time))  was aborted: ERROR: column "col_date" is of type date but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：144  Call getNextException to see other errors in the batch.
	at org.postgresql.jdbc.BatchResultHandler.handleError(BatchResultHandler.java:165)
	at org.postgresql.core.ResultHandlerDelegate.handleError(ResultHandlerDelegate.java:52)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2421)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	at org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	at org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	at org.postgresql.jdbc.PgPreparedStatement.executeBatch(PgPreparedStatement.java:1739)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:132)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:150)
	... 34 more
Caused by: org.postgresql.util.PSQLException: ERROR: column "col_date" is of type date but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：144
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	... 42 more

[TRACE] 2025-05-08 00:39:57.063 - [Sybase ~pg时间测试][PG - Copy] - Job suspend in error handle 
[TRACE] 2025-05-08 00:39:57.090 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] running status set to false 
[INFO ] 2025-05-08 00:39:57.090 - [Sybase ~pg时间测试][LocalSybase] - Log Miner is shutting down... 
[INFO ] 2025-05-08 00:39:57.137 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525075, 0, size: 5, cost: 47 ms 
[INFO ] 2025-05-08 00:39:57.137 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 25, will rescan it until it's enough: 999, or loop: 2 to 5 
[TRACE] 2025-05-08 00:39:57.138 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746635996119 
[TRACE] 2025-05-08 00:39:57.139 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node released: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746635996119 
[TRACE] 2025-05-08 00:39:57.139 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] schema data cleaned 
[TRACE] 2025-05-08 00:39:57.140 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] monitor closed 
[TRACE] 2025-05-08 00:39:57.141 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] close complete, cost 52 ms 
[TRACE] 2025-05-08 00:39:57.141 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] running status set to false 
[TRACE] 2025-05-08 00:39:57.159 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node stopped: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746635996301 
[TRACE] 2025-05-08 00:39:57.159 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node released: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746635996301 
[TRACE] 2025-05-08 00:39:57.159 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] schema data cleaned 
[TRACE] 2025-05-08 00:39:57.159 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] monitor closed 
[TRACE] 2025-05-08 00:39:57.159 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] close complete, cost 18 ms 
[INFO ] 2025-05-08 00:39:59.207 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525075, 0, size: 5, cost: 63 ms 
[INFO ] 2025-05-08 00:39:59.207 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 25, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:40:00.403 - [Sybase ~pg时间测试] - Task [Sybase ~pg时间测试] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 00:40:00.408 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:40:00.408 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2b9f05df 
[TRACE] 2025-05-08 00:40:00.531 - [Sybase ~pg时间测试] - Stop task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试)  
[TRACE] 2025-05-08 00:40:00.531 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[TRACE] 2025-05-08 00:40:00.531 - [Sybase ~pg时间测试] - Snapshot order controller have been removed 
[INFO ] 2025-05-08 00:40:00.531 - [Sybase ~pg时间测试] - Task stopped. 
[INFO ] 2025-05-08 00:40:01.273 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525075, 0, size: 5, cost: 59 ms 
[INFO ] 2025-05-08 00:40:01.273 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 25, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:40:03.342 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525075, 0, size: 5, cost: 67 ms 
[INFO ] 2025-05-08 00:40:03.342 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 25, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:40:05.370 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:40:05.537 - [Sybase ~pg时间测试] - Task [Sybase ~pg时间测试] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 00:40:05.537 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:40:05.538 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2b9f05df 
[TRACE] 2025-05-08 00:40:05.538 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[INFO ] 2025-05-08 00:40:05.561 - [Sybase ~pg时间测试] - Task stopped. 
[TRACE] 2025-05-08 00:40:05.563 - [Sybase ~pg时间测试] - Remove memory task client succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:40:05.563 - [Sybase ~pg时间测试] - Destroy memory task client cache succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:40:35.841 - [Sybase ~pg时间测试] - Task initialization... 
[TRACE] 2025-05-08 00:40:35.842 - [Sybase ~pg时间测试] - Start task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试) 
[INFO ] 2025-05-08 00:40:36.043 - [Sybase ~pg时间测试] - Loading table structure completed 
[TRACE] 2025-05-08 00:40:36.108 - [Sybase ~pg时间测试] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-08 00:40:36.108 - [Sybase ~pg时间测试] - The engine receives Sybase ~pg时间测试 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-08 00:40:36.171 - [Sybase ~pg时间测试] - Task started 
[TRACE] 2025-05-08 00:40:36.172 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:40:36.172 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:40:36.172 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 00:40:36.172 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] preload schema finished, cost 0 ms 
[INFO ] 2025-05-08 00:40:36.850 - [Sybase ~pg时间测试][LocalSybase] - Source connector(LocalSybase) initialization completed 
[TRACE] 2025-05-08 00:40:36.855 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" read batch size: 100 
[TRACE] 2025-05-08 00:40:36.855 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" event queue capacity: 200 
[TRACE] 2025-05-08 00:40:36.855 - [Sybase ~pg时间测试][LocalSybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-08 00:40:36.855 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:40:36.956 - [Sybase ~pg时间测试][LocalSybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-05-08 00:40:36.961 - [Sybase ~pg时间测试][LocalSybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:40:37.039 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from 2 tables 
[TRACE] 2025-05-08 00:40:37.044 - [Sybase ~pg时间测试][LocalSybase] - Initial sync started 
[INFO ] 2025-05-08 00:40:37.044 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: testrslt 
[TRACE] 2025-05-08 00:40:37.044 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt is going to be initial synced 
[INFO ] 2025-05-08 00:40:37.082 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt has been completed batch read 
[INFO ] 2025-05-08 00:40:37.085 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: time_test2 
[TRACE] 2025-05-08 00:40:37.085 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 is going to be initial synced 
[TRACE] 2025-05-08 00:40:38.480 - [Sybase ~pg时间测试][LocalSybase] - Query snapshot row size completed: LocalSybase(ecf2fe24-1395-49b6-9448-da59d864cc1e) 
[INFO ] 2025-05-08 00:40:39.448 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 has been completed batch read 
[TRACE] 2025-05-08 00:40:39.448 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[INFO ] 2025-05-08 00:40:39.448 - [Sybase ~pg时间测试][LocalSybase] - Batch read completed. 
[TRACE] 2025-05-08 00:40:39.448 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync starting... 
[TRACE] 2025-05-08 00:40:39.448 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[TRACE] 2025-05-08 00:40:39.448 - [Sybase ~pg时间测试][LocalSybase] - Starting stream read, table list: [testrslt, time_test2], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:40:39.491 - [Sybase ~pg时间测试][LocalSybase] - Starting incremental sync using database log parser 
[INFO ] 2025-05-08 00:40:39.492 - [Sybase ~pg时间测试][LocalSybase] - startRid: 525076, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:40:39.492 - [Sybase ~pg时间测试][LocalSybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:40:39.492 - [Sybase ~pg时间测试][LocalSybase] - sybase offset in database is: startRid: 525076, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-05-08 00:40:39.492 - [Sybase ~pg时间测试][LocalSybase] - we will use offset in database, how ever, this is safe: startRid: 525076, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:40:39.540 - [Sybase ~pg时间测试][LocalSybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-05-08 00:40:39.540 - [Sybase ~pg时间测试][LocalSybase] - sp_config_rep_agent disabled, database: s1 
[INFO ] 2025-05-08 00:40:39.556 - [Sybase ~pg时间测试][LocalSybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-05-08 00:40:39.556 - [Sybase ~pg时间测试][LocalSybase] - opened cdc for tables: {dbo=[testrslt, time_test2]} 
[INFO ] 2025-05-08 00:40:39.574 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:40:39.574 - [Sybase ~pg时间测试][LocalSybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-05-08 00:40:39.587 - [Sybase ~pg时间测试][LocalSybase] - sybase cdc debug log is disabled 
[INFO ] 2025-05-08 00:40:39.587 - [Sybase ~pg时间测试][PG - Copy] - Sink connector(PG - Copy) initialization completed 
[TRACE] 2025-05-08 00:40:39.587 - [Sybase ~pg时间测试][PG - Copy] - Node(PG - Copy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-08 00:40:39.587 - [Sybase ~pg时间测试][PG - Copy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-08 00:40:39.596 - [Sybase ~pg时间测试][LocalSybase] - trans timestamp offset: 0 
[INFO ] 2025-05-08 00:40:39.596 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525076, rowId: 0 
[INFO ] 2025-05-08 00:40:39.612 - [Sybase ~pg时间测试][PG - Copy] - Apply table structure to target database 
[INFO ] 2025-05-08 00:40:39.612 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525076, 0, size: 3, cost: 15 ms 
[INFO ] 2025-05-08 00:40:39.612 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:42:36.557 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525076, 0, size: 3, cost: 108611 ms 
[INFO ] 2025-05-08 00:42:36.557 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 2 to 5 
[WARN ] 2025-05-08 00:42:37.053 - [Sybase ~pg时间测试][PG - Copy] - writeRecord failed, dismantle them, size: 1 
[TRACE] 2025-05-08 00:42:37.053 - [Sybase ~pg时间测试][PG - Copy] - Exception skipping - The current exception does not match the skip exception strategy, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: time_test2 
[ERROR] 2025-05-08 00:42:37.060 - [Sybase ~pg时间测试][PG - Copy] - Execute PDK method: TARGET_WRITE_RECORD, tableName: time_test2 <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: time_test2

<-- Simple Stack Trace -->
Caused by: org.postgresql.util.PSQLException: ERROR: column "col_date" is of type date but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：144
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: time_test2
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1127)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:948)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:866)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:815)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:775)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:661)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:747)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:799)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:746)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: Error occurred when retrying write record: io.tapdata.entity.event.dml.TapInsertRecordEvent@1bf02787: {"after":{"col_date":"\"2025-05-07\"","col_bigtime":"23:59:59.999999","col_time":"23:59:59.996","col_smalldatetime":"2025-05-07T23:59:00","col_bigdatetime":"2025-05-07T23:59:59.999999000","id":19,"col_datetime":"2025-05-07T23:59:59.996"},"containsIllegalDate":false,"database":"dbo","pdkGroup":"io.tapdata","pdkId":"sybase","pdkVersion":"1.0-SNAPSHOT","referenceTime":1746636038489,"tableId":"time_test2","time":1746636038489,"type":300}
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:164)
	at io.tapdata.common.dml.NormalRecordWriter.lambda$writePart$1(NormalRecordWriter.java:168)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:168)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:90)
	at io.tapdata.connector.postgres.PostgresConnector.writeRecord(PostgresConnector.java:485)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	... 26 more
Caused by: java.sql.BatchUpdateException: Batch entry 0 INSERT INTO "wim"."time_test2" ("id", "col_date", "col_time", "col_datetime", "col_smalldatetime", "col_bigdatetime", "col_bigtime") VALUES(('19'::int4),('"2025-05-07"'),('23:59:59.996'::time),('2025-05-07 23:59:59.996'::timestamp),('2025-05-07 23:59:00'::timestamp),('2025-05-07 23:59:59.999999'::timestamp),('23:59:59.999999'::time))  was aborted: ERROR: column "col_date" is of type date but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：144  Call getNextException to see other errors in the batch.
	at org.postgresql.jdbc.BatchResultHandler.handleError(BatchResultHandler.java:165)
	at org.postgresql.core.ResultHandlerDelegate.handleError(ResultHandlerDelegate.java:52)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2421)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	at org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	at org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	at org.postgresql.jdbc.PgPreparedStatement.executeBatch(PgPreparedStatement.java:1739)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:132)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:150)
	... 34 more
Caused by: org.postgresql.util.PSQLException: ERROR: column "col_date" is of type date but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：144
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	... 42 more

[TRACE] 2025-05-08 00:42:37.062 - [Sybase ~pg时间测试][PG - Copy] - Job suspend in error handle 
[TRACE] 2025-05-08 00:42:37.134 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] running status set to false 
[INFO ] 2025-05-08 00:42:37.134 - [Sybase ~pg时间测试][LocalSybase] - Log Miner is shutting down... 
[INFO ] 2025-05-08 00:42:37.182 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525076, 0, size: 3, cost: 47 ms 
[INFO ] 2025-05-08 00:42:37.182 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 3 to 5 
[TRACE] 2025-05-08 00:42:37.183 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746636036784 
[TRACE] 2025-05-08 00:42:37.183 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node released: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746636036784 
[TRACE] 2025-05-08 00:42:37.183 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] schema data cleaned 
[TRACE] 2025-05-08 00:42:37.183 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] monitor closed 
[TRACE] 2025-05-08 00:42:37.184 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] close complete, cost 50 ms 
[TRACE] 2025-05-08 00:42:37.184 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] running status set to false 
[TRACE] 2025-05-08 00:42:37.202 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node stopped: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746636039425 
[TRACE] 2025-05-08 00:42:37.202 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node released: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746636039425 
[TRACE] 2025-05-08 00:42:37.203 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] schema data cleaned 
[TRACE] 2025-05-08 00:42:37.203 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] monitor closed 
[TRACE] 2025-05-08 00:42:37.204 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] close complete, cost 19 ms 
[INFO ] 2025-05-08 00:42:39.236 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525076, 0, size: 3, cost: 51 ms 
[INFO ] 2025-05-08 00:42:39.236 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:42:41.288 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525076, 0, size: 3, cost: 46 ms 
[INFO ] 2025-05-08 00:42:41.288 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:42:41.499 - [Sybase ~pg时间测试] - Task [Sybase ~pg时间测试] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 00:42:41.499 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:42:41.499 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@44a43b9f 
[TRACE] 2025-05-08 00:42:41.502 - [Sybase ~pg时间测试] - Stop task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试)  
[TRACE] 2025-05-08 00:42:41.628 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[TRACE] 2025-05-08 00:42:41.628 - [Sybase ~pg时间测试] - Snapshot order controller have been removed 
[INFO ] 2025-05-08 00:42:41.628 - [Sybase ~pg时间测试] - Task stopped. 
[INFO ] 2025-05-08 00:42:43.296 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:42:46.640 - [Sybase ~pg时间测试] - Task [Sybase ~pg时间测试] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 00:42:46.640 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 00:42:46.640 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@44a43b9f 
[TRACE] 2025-05-08 00:42:46.641 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[INFO ] 2025-05-08 00:42:46.641 - [Sybase ~pg时间测试] - Task stopped. 
[TRACE] 2025-05-08 00:42:46.677 - [Sybase ~pg时间测试] - Remove memory task client succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:42:46.678 - [Sybase ~pg时间测试] - Destroy memory task client cache succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 00:44:16.006 - [Sybase ~pg时间测试] - Task initialization... 
[TRACE] 2025-05-08 00:44:16.213 - [Sybase ~pg时间测试] - Start task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试) 
[INFO ] 2025-05-08 00:44:16.488 - [Sybase ~pg时间测试] - Loading table structure completed 
[TRACE] 2025-05-08 00:44:16.488 - [Sybase ~pg时间测试] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-08 00:44:16.554 - [Sybase ~pg时间测试] - The engine receives Sybase ~pg时间测试 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-08 00:44:16.554 - [Sybase ~pg时间测试] - Task started 
[TRACE] 2025-05-08 00:44:16.585 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:44:16.585 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] start preload schema,table counts: 2 
[TRACE] 2025-05-08 00:44:16.585 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 00:44:16.585 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] preload schema finished, cost 0 ms 
[INFO ] 2025-05-08 00:44:17.454 - [Sybase ~pg时间测试][PG - Copy] - Sink connector(PG - Copy) initialization completed 
[TRACE] 2025-05-08 00:44:17.454 - [Sybase ~pg时间测试][PG - Copy] - Node(PG - Copy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-08 00:44:17.467 - [Sybase ~pg时间测试][PG - Copy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-08 00:44:17.467 - [Sybase ~pg时间测试][PG - Copy] - Apply table structure to target database 
[INFO ] 2025-05-08 00:44:17.479 - [Sybase ~pg时间测试][LocalSybase] - Source connector(LocalSybase) initialization completed 
[TRACE] 2025-05-08 00:44:17.482 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" read batch size: 100 
[TRACE] 2025-05-08 00:44:17.482 - [Sybase ~pg时间测试][LocalSybase] - Source node "LocalSybase" event queue capacity: 200 
[TRACE] 2025-05-08 00:44:17.482 - [Sybase ~pg时间测试][LocalSybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-08 00:44:17.482 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:44:17.554 - [Sybase ~pg时间测试][LocalSybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-05-08 00:44:17.554 - [Sybase ~pg时间测试][LocalSybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:44:17.616 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from 2 tables 
[TRACE] 2025-05-08 00:44:17.616 - [Sybase ~pg时间测试][LocalSybase] - Initial sync started 
[INFO ] 2025-05-08 00:44:17.616 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: testrslt 
[TRACE] 2025-05-08 00:44:17.616 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt is going to be initial synced 
[INFO ] 2025-05-08 00:44:17.666 - [Sybase ~pg时间测试][LocalSybase] - Table testrslt has been completed batch read 
[INFO ] 2025-05-08 00:44:17.666 - [Sybase ~pg时间测试][LocalSybase] - Starting batch read from table: time_test2 
[TRACE] 2025-05-08 00:44:17.666 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 is going to be initial synced 
[TRACE] 2025-05-08 00:44:17.696 - [Sybase ~pg时间测试][LocalSybase] - Query snapshot row size completed: LocalSybase(ecf2fe24-1395-49b6-9448-da59d864cc1e) 
[INFO ] 2025-05-08 00:44:17.696 - [Sybase ~pg时间测试][LocalSybase] - Table time_test2 has been completed batch read 
[TRACE] 2025-05-08 00:44:17.696 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[INFO ] 2025-05-08 00:44:17.696 - [Sybase ~pg时间测试][LocalSybase] - Batch read completed. 
[TRACE] 2025-05-08 00:44:17.696 - [Sybase ~pg时间测试][LocalSybase] - Incremental sync starting... 
[TRACE] 2025-05-08 00:44:17.696 - [Sybase ~pg时间测试][LocalSybase] - Initial sync completed 
[TRACE] 2025-05-08 00:44:17.697 - [Sybase ~pg时间测试][LocalSybase] - Starting stream read, table list: [testrslt, time_test2], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-05-08 00:44:17.697 - [Sybase ~pg时间测试][LocalSybase] - Starting incremental sync using database log parser 
[INFO ] 2025-05-08 00:44:17.716 - [Sybase ~pg时间测试][LocalSybase] - startRid: 525076, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:44:17.716 - [Sybase ~pg时间测试][LocalSybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:44:17.716 - [Sybase ~pg时间测试][LocalSybase] - sybase offset in database is: startRid: 525076, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-05-08 00:44:17.717 - [Sybase ~pg时间测试][LocalSybase] - we will use offset in database, how ever, this is safe: startRid: 525076, rowId: 0, h: 0, l: 0 
[INFO ] 2025-05-08 00:44:17.812 - [Sybase ~pg时间测试][LocalSybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-05-08 00:44:17.812 - [Sybase ~pg时间测试][LocalSybase] - sp_config_rep_agent disabled, database: s1 
[INFO ] 2025-05-08 00:44:17.835 - [Sybase ~pg时间测试][LocalSybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-05-08 00:44:17.835 - [Sybase ~pg时间测试][LocalSybase] - opened cdc for tables: {dbo=[testrslt, time_test2]} 
[INFO ] 2025-05-08 00:44:17.853 - [Sybase ~pg时间测试][LocalSybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='s1') 
[INFO ] 2025-05-08 00:44:17.854 - [Sybase ~pg时间测试][LocalSybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-05-08 00:44:17.897 - [Sybase ~pg时间测试][LocalSybase] - sybase cdc debug log is disabled 
[INFO ] 2025-05-08 00:44:17.897 - [Sybase ~pg时间测试][LocalSybase] - trans timestamp offset: 0 
[INFO ] 2025-05-08 00:44:17.897 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525076, rowId: 0 
[INFO ] 2025-05-08 00:44:17.936 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525076, 0, size: 7, cost: 38 ms 
[INFO ] 2025-05-08 00:44:17.936 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 33, will rescan it until it's enough: 999, or loop: 1 to 5 
[TRACE] 2025-05-08 00:44:17.985 - [Sybase ~pg时间测试][PG - Copy] - Process after table "testrslt" initial sync finished, cost: 0 ms 
[TRACE] 2025-05-08 00:44:17.985 - [Sybase ~pg时间测试][PG - Copy] - Process after table "time_test2" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-08 00:44:17.985 - [Sybase ~pg时间测试][PG - Copy] - Process after all table(s) initial sync are finished，table number: 2 
[INFO ] 2025-05-08 00:44:20.025 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525076, 0, size: 7, cost: 85 ms 
[INFO ] 2025-05-08 00:44:20.026 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 33, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:44:22.089 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525076, 0, size: 7, cost: 58 ms 
[INFO ] 2025-05-08 00:44:22.089 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 33, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:44:24.212 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525076, 0, size: 7, cost: 117 ms 
[INFO ] 2025-05-08 00:44:24.213 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 33, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:44:26.304 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525076, 0, size: 7, cost: 85 ms 
[INFO ] 2025-05-08 00:44:26.304 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 33, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:44:28.310 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:44:28.365 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525077, rowId: 5 
[INFO ] 2025-05-08 00:44:28.366 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525077, 5, size: 1, cost: 43 ms 
[INFO ] 2025-05-08 00:44:28.366 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:44:30.457 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525077, 5, size: 1, cost: 72 ms 
[INFO ] 2025-05-08 00:44:30.457 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:44:32.496 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525077, 5, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:44:32.497 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:44:34.596 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525077, 5, size: 1, cost: 91 ms 
[INFO ] 2025-05-08 00:44:34.596 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:44:36.644 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525077, 5, size: 1, cost: 46 ms 
[INFO ] 2025-05-08 00:44:36.645 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:44:38.653 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:44:38.661 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525077, rowId: 5 
[INFO ] 2025-05-08 00:44:48.744 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525077, 5, size: 1, cost: 84 ms 
[INFO ] 2025-05-08 00:44:48.744 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:44:50.838 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525077, 5, size: 1, cost: 86 ms 
[INFO ] 2025-05-08 00:44:50.838 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:44:52.917 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525077, 5, size: 1, cost: 75 ms 
[INFO ] 2025-05-08 00:44:52.919 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:44:54.982 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525077, 5, size: 1, cost: 56 ms 
[INFO ] 2025-05-08 00:44:54.983 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:44:57.039 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525077, 5, size: 1, cost: 56 ms 
[INFO ] 2025-05-08 00:44:57.040 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:44:59.049 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:44:59.051 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525077, rowId: 5 
[INFO ] 2025-05-08 00:45:09.157 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525077, 5, size: 5, cost: 107 ms 
[INFO ] 2025-05-08 00:45:09.157 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 29, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:45:11.241 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525077, 5, size: 5, cost: 80 ms 
[INFO ] 2025-05-08 00:45:11.241 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 29, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:45:13.369 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525077, 5, size: 5, cost: 119 ms 
[INFO ] 2025-05-08 00:45:13.369 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 29, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:45:15.489 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525077, 5, size: 5, cost: 113 ms 
[INFO ] 2025-05-08 00:45:15.489 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 29, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:45:17.593 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525077, 5, size: 5, cost: 98 ms 
[INFO ] 2025-05-08 00:45:17.593 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 29, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:45:19.616 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:45:19.617 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525081, rowId: 8 
[INFO ] 2025-05-08 00:45:19.683 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525081, 8, size: 1, cost: 67 ms 
[INFO ] 2025-05-08 00:45:19.684 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:45:21.751 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525081, 8, size: 1, cost: 60 ms 
[INFO ] 2025-05-08 00:45:21.751 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:45:23.855 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525081, 8, size: 1, cost: 98 ms 
[INFO ] 2025-05-08 00:45:23.855 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:45:25.926 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525081, 8, size: 1, cost: 62 ms 
[INFO ] 2025-05-08 00:45:25.930 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:45:28.017 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525081, 8, size: 1, cost: 84 ms 
[INFO ] 2025-05-08 00:45:28.018 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:45:30.026 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:45:30.031 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525081, rowId: 8 
[INFO ] 2025-05-08 00:45:40.094 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525081, 8, size: 1, cost: 61 ms 
[INFO ] 2025-05-08 00:45:40.095 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:45:42.207 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525081, 8, size: 3, cost: 106 ms 
[INFO ] 2025-05-08 00:45:42.208 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:45:44.281 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525081, 8, size: 3, cost: 70 ms 
[INFO ] 2025-05-08 00:45:44.282 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:45:46.352 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525081, 8, size: 3, cost: 66 ms 
[INFO ] 2025-05-08 00:45:46.353 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:45:48.421 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525081, 8, size: 3, cost: 61 ms 
[INFO ] 2025-05-08 00:45:48.422 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:45:50.429 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:45:50.432 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525082, rowId: 2 
[INFO ] 2025-05-08 00:45:50.520 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525082, 2, size: 1, cost: 89 ms 
[INFO ] 2025-05-08 00:45:50.520 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:45:52.586 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525082, 2, size: 1, cost: 61 ms 
[INFO ] 2025-05-08 00:45:52.586 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:45:54.660 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525082, 2, size: 1, cost: 68 ms 
[INFO ] 2025-05-08 00:45:54.661 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:45:56.730 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525082, 2, size: 1, cost: 64 ms 
[INFO ] 2025-05-08 00:45:56.730 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:45:58.809 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525082, 2, size: 1, cost: 73 ms 
[INFO ] 2025-05-08 00:45:58.810 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:46:00.845 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:46:00.845 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525082, rowId: 2 
[INFO ] 2025-05-08 00:46:10.895 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525082, 2, size: 1, cost: 68 ms 
[INFO ] 2025-05-08 00:46:10.895 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:46:12.958 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525082, 2, size: 1, cost: 59 ms 
[INFO ] 2025-05-08 00:46:13.167 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:46:15.051 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525082, 2, size: 1, cost: 85 ms 
[INFO ] 2025-05-08 00:46:15.051 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:46:17.084 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525082, 2, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 00:46:17.084 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:46:19.173 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525082, 2, size: 1, cost: 82 ms 
[INFO ] 2025-05-08 00:46:19.174 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:46:21.180 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:46:21.183 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525082, rowId: 2 
[INFO ] 2025-05-08 00:46:31.260 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525082, 2, size: 4, cost: 69 ms 
[INFO ] 2025-05-08 00:46:31.261 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:46:33.373 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525082, 2, size: 4, cost: 86 ms 
[INFO ] 2025-05-08 00:46:33.373 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:46:35.464 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525082, 2, size: 4, cost: 87 ms 
[INFO ] 2025-05-08 00:46:35.464 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:46:37.559 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525082, 2, size: 4, cost: 86 ms 
[INFO ] 2025-05-08 00:46:37.559 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:46:39.606 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525082, 2, size: 4, cost: 46 ms 
[INFO ] 2025-05-08 00:46:39.607 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:46:41.615 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:46:41.616 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525082, rowId: 5 
[INFO ] 2025-05-08 00:46:41.820 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525082, 5, size: 3, cost: 74 ms 
[INFO ] 2025-05-08 00:46:41.820 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:46:43.763 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525082, 5, size: 3, cost: 69 ms 
[INFO ] 2025-05-08 00:46:43.763 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:46:45.830 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525082, 5, size: 3, cost: 60 ms 
[INFO ] 2025-05-08 00:46:45.830 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:46:47.906 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525082, 5, size: 3, cost: 72 ms 
[INFO ] 2025-05-08 00:46:47.907 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:46:50.013 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525082, 5, size: 3, cost: 102 ms 
[INFO ] 2025-05-08 00:46:50.013 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:46:52.025 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:46:52.026 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525082, rowId: 8 
[INFO ] 2025-05-08 00:46:52.063 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525082, 8, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:46:52.064 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:46:54.136 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525082, 8, size: 1, cost: 66 ms 
[INFO ] 2025-05-08 00:46:54.137 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:46:56.211 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525082, 8, size: 1, cost: 68 ms 
[INFO ] 2025-05-08 00:46:56.211 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:46:58.290 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525082, 8, size: 1, cost: 65 ms 
[INFO ] 2025-05-08 00:46:58.297 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:47:00.354 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525082, 8, size: 1, cost: 59 ms 
[INFO ] 2025-05-08 00:47:00.354 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:47:02.361 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:47:02.362 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525082, rowId: 8 
[INFO ] 2025-05-08 00:47:12.404 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525082, 8, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 00:47:12.405 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:47:14.473 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525082, 8, size: 1, cost: 63 ms 
[INFO ] 2025-05-08 00:47:14.474 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:47:16.559 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525082, 8, size: 1, cost: 78 ms 
[INFO ] 2025-05-08 00:47:16.562 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:47:18.629 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525082, 8, size: 1, cost: 64 ms 
[INFO ] 2025-05-08 00:47:18.630 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:47:20.714 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525082, 8, size: 1, cost: 78 ms 
[INFO ] 2025-05-08 00:47:20.716 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:47:22.720 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:47:22.720 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525082, rowId: 8 
[INFO ] 2025-05-08 00:47:32.806 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525082, 8, size: 4, cost: 76 ms 
[INFO ] 2025-05-08 00:47:32.806 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:47:34.868 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525082, 8, size: 4, cost: 58 ms 
[INFO ] 2025-05-08 00:47:34.869 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:47:36.931 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525082, 8, size: 4, cost: 60 ms 
[INFO ] 2025-05-08 00:47:36.931 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:47:39.024 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525082, 8, size: 4, cost: 85 ms 
[INFO ] 2025-05-08 00:47:39.024 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:47:41.060 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525082, 8, size: 6, cost: 28 ms 
[INFO ] 2025-05-08 00:47:41.060 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 7, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:47:43.064 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:47:43.064 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525082, rowId: 14 
[INFO ] 2025-05-08 00:47:43.109 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525082, 14, size: 1, cost: 45 ms 
[INFO ] 2025-05-08 00:47:43.110 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:47:45.198 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525082, 14, size: 1, cost: 80 ms 
[INFO ] 2025-05-08 00:47:45.198 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:47:47.308 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525082, 14, size: 1, cost: 99 ms 
[INFO ] 2025-05-08 00:47:47.308 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:47:49.377 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525082, 14, size: 1, cost: 62 ms 
[INFO ] 2025-05-08 00:47:49.377 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:47:51.457 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525082, 14, size: 1, cost: 75 ms 
[INFO ] 2025-05-08 00:47:51.457 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:47:53.462 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:47:53.463 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525082, rowId: 14 
[INFO ] 2025-05-08 00:48:03.488 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525082, 14, size: 1, cost: 23 ms 
[INFO ] 2025-05-08 00:48:03.489 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:48:05.559 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525082, 14, size: 1, cost: 62 ms 
[INFO ] 2025-05-08 00:48:05.559 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:48:07.645 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525082, 14, size: 1, cost: 79 ms 
[INFO ] 2025-05-08 00:48:07.645 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:48:09.721 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525082, 14, size: 1, cost: 70 ms 
[INFO ] 2025-05-08 00:48:09.722 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:48:11.810 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525082, 14, size: 1, cost: 83 ms 
[INFO ] 2025-05-08 00:48:11.810 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:48:13.817 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:48:13.817 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525082, rowId: 14 
[INFO ] 2025-05-08 00:48:23.901 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525082, 14, size: 4, cost: 77 ms 
[INFO ] 2025-05-08 00:48:23.902 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:48:25.970 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525082, 14, size: 4, cost: 63 ms 
[INFO ] 2025-05-08 00:48:25.971 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:48:28.034 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525082, 14, size: 4, cost: 59 ms 
[INFO ] 2025-05-08 00:48:28.035 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:48:30.109 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525082, 14, size: 4, cost: 70 ms 
[INFO ] 2025-05-08 00:48:30.110 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:48:32.163 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525082, 14, size: 4, cost: 49 ms 
[INFO ] 2025-05-08 00:48:32.164 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:48:34.170 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:48:34.257 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525082, rowId: 17 
[INFO ] 2025-05-08 00:48:34.258 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525082, 17, size: 1, cost: 85 ms 
[INFO ] 2025-05-08 00:48:34.258 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:48:36.352 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525082, 17, size: 1, cost: 89 ms 
[INFO ] 2025-05-08 00:48:36.352 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:48:38.411 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525082, 17, size: 1, cost: 54 ms 
[INFO ] 2025-05-08 00:48:38.411 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:48:40.462 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525082, 17, size: 3, cost: 45 ms 
[INFO ] 2025-05-08 00:48:40.463 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:48:42.484 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525082, 17, size: 3, cost: 16 ms 
[INFO ] 2025-05-08 00:48:42.484 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:48:44.504 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:48:44.504 - [Sybase ~pg时间测试][LocalSybase] - continue normal rescan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:48:44.524 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 20 ms 
[INFO ] 2025-05-08 00:48:44.524 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:48:46.587 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 56 ms 
[INFO ] 2025-05-08 00:48:46.587 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:48:48.664 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 71 ms 
[INFO ] 2025-05-08 00:48:48.664 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:48:50.739 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 69 ms 
[INFO ] 2025-05-08 00:48:50.740 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:48:52.806 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 62 ms 
[INFO ] 2025-05-08 00:48:52.806 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:48:54.809 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:48:54.810 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:49:04.868 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 55 ms 
[INFO ] 2025-05-08 00:49:04.869 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:49:06.901 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:49:06.901 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:49:08.966 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 61 ms 
[INFO ] 2025-05-08 00:49:08.967 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:49:11.025 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 53 ms 
[INFO ] 2025-05-08 00:49:11.025 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:49:13.087 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 59 ms 
[INFO ] 2025-05-08 00:49:13.087 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:49:15.090 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:49:15.090 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:49:25.157 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 61 ms 
[INFO ] 2025-05-08 00:49:25.158 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:49:27.196 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:49:27.196 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:49:29.243 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 44 ms 
[INFO ] 2025-05-08 00:49:29.243 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:49:31.319 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 69 ms 
[INFO ] 2025-05-08 00:49:31.320 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:49:33.389 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 64 ms 
[INFO ] 2025-05-08 00:49:33.390 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:49:35.408 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:49:35.409 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:49:45.493 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 73 ms 
[INFO ] 2025-05-08 00:49:45.495 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:49:47.575 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 76 ms 
[INFO ] 2025-05-08 00:49:47.576 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:49:49.640 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 62 ms 
[INFO ] 2025-05-08 00:49:49.641 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:49:51.719 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 73 ms 
[INFO ] 2025-05-08 00:49:51.720 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:49:53.815 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 87 ms 
[INFO ] 2025-05-08 00:49:53.815 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:49:55.827 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:49:55.831 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:50:05.911 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 76 ms 
[INFO ] 2025-05-08 00:50:05.911 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:50:08.042 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 125 ms 
[INFO ] 2025-05-08 00:50:08.042 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:50:10.111 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 63 ms 
[INFO ] 2025-05-08 00:50:10.112 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:50:12.177 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 60 ms 
[INFO ] 2025-05-08 00:50:12.177 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:50:14.244 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 55 ms 
[INFO ] 2025-05-08 00:50:14.244 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:50:16.247 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:50:16.247 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:50:26.301 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 47 ms 
[INFO ] 2025-05-08 00:50:26.302 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:50:28.349 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 43 ms 
[INFO ] 2025-05-08 00:50:28.349 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:50:30.403 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 49 ms 
[INFO ] 2025-05-08 00:50:30.404 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:50:32.433 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 27 ms 
[INFO ] 2025-05-08 00:50:32.434 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:50:34.487 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 50 ms 
[INFO ] 2025-05-08 00:50:34.487 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:50:36.494 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:50:36.494 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:50:46.535 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 00:50:46.538 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:50:48.594 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 52 ms 
[INFO ] 2025-05-08 00:50:48.595 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:50:50.670 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 66 ms 
[INFO ] 2025-05-08 00:50:50.670 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:50:52.736 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 57 ms 
[INFO ] 2025-05-08 00:50:52.940 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:50:54.807 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 65 ms 
[INFO ] 2025-05-08 00:50:54.808 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:50:56.814 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:50:56.815 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:51:06.886 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 52 ms 
[INFO ] 2025-05-08 00:51:06.890 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:51:08.981 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 81 ms 
[INFO ] 2025-05-08 00:51:08.984 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:51:11.021 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:51:11.025 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:51:13.079 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 52 ms 
[INFO ] 2025-05-08 00:51:13.079 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:51:15.126 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 43 ms 
[INFO ] 2025-05-08 00:51:15.126 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:51:17.134 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:51:17.341 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:51:27.191 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 47 ms 
[INFO ] 2025-05-08 00:51:27.191 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:51:29.288 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 90 ms 
[INFO ] 2025-05-08 00:51:29.288 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:51:31.356 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 63 ms 
[INFO ] 2025-05-08 00:51:31.356 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:51:33.445 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 82 ms 
[INFO ] 2025-05-08 00:51:33.445 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:51:35.515 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 65 ms 
[INFO ] 2025-05-08 00:51:35.516 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:51:37.522 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:51:37.522 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:51:47.592 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 66 ms 
[INFO ] 2025-05-08 00:51:47.592 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:51:49.683 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 81 ms 
[INFO ] 2025-05-08 00:51:49.686 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:51:51.731 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 45 ms 
[INFO ] 2025-05-08 00:51:51.732 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:51:53.802 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 56 ms 
[INFO ] 2025-05-08 00:51:53.804 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:51:55.898 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 82 ms 
[INFO ] 2025-05-08 00:51:55.899 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:51:57.907 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:51:57.907 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:52:07.980 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 64 ms 
[INFO ] 2025-05-08 00:52:07.981 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:52:10.041 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 55 ms 
[INFO ] 2025-05-08 00:52:10.042 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:52:12.075 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 00:52:12.075 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:52:14.150 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 68 ms 
[INFO ] 2025-05-08 00:52:14.151 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:52:16.202 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 50 ms 
[INFO ] 2025-05-08 00:52:16.203 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:52:18.209 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:52:18.210 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:52:28.280 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 64 ms 
[INFO ] 2025-05-08 00:52:28.281 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:52:30.333 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 49 ms 
[INFO ] 2025-05-08 00:52:30.334 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:52:32.465 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 126 ms 
[INFO ] 2025-05-08 00:52:32.466 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:52:34.525 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 56 ms 
[INFO ] 2025-05-08 00:52:34.525 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:52:36.584 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 54 ms 
[INFO ] 2025-05-08 00:52:36.584 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:52:38.592 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:52:38.593 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:52:48.625 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:52:48.630 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:52:50.674 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 44 ms 
[INFO ] 2025-05-08 00:52:50.674 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:52:52.715 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:52:52.716 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:52:54.754 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:52:54.755 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:52:56.792 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 00:52:56.792 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:52:58.798 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:52:58.798 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:53:08.843 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 00:53:08.843 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:53:10.882 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 00:53:10.883 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:53:12.919 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 00:53:12.919 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:53:14.960 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:53:14.960 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:53:17.004 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 00:53:17.005 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:53:19.013 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:53:19.013 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:53:29.058 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 00:53:29.058 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:53:31.098 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 00:53:31.098 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:53:33.142 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:53:33.143 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:53:35.188 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 00:53:35.188 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:53:37.234 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 00:53:37.234 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:53:39.243 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:53:39.244 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:53:49.286 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 00:53:49.286 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:53:51.324 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:53:51.324 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:53:53.372 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 00:53:53.373 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:53:55.415 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 00:53:55.415 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:53:57.454 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:53:57.454 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:53:59.464 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:53:59.464 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:54:09.512 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 00:54:09.513 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:54:11.559 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 00:54:11.559 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:54:13.607 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 00:54:13.607 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:54:15.651 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 00:54:15.651 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:54:17.698 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 00:54:17.699 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:54:19.704 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:54:19.704 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:54:29.745 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 00:54:29.746 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:54:31.791 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:54:31.792 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:54:33.832 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 00:54:33.832 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:54:35.873 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 00:54:35.874 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:54:37.915 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:54:37.915 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:54:39.924 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:54:39.925 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:54:49.973 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 00:54:49.973 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:54:52.014 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 00:54:52.015 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:54:54.064 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:54:54.064 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:54:56.098 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:54:56.098 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:54:58.148 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:54:58.148 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:55:00.155 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:55:00.156 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:55:10.203 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 00:55:10.204 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:55:12.240 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:55:12.240 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:55:14.281 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:55:14.281 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:55:16.329 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:55:16.329 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:55:18.369 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 00:55:18.369 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:55:20.381 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:55:20.381 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:55:30.432 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 00:55:30.432 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:55:32.478 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 00:55:32.478 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:55:34.502 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 19 ms 
[INFO ] 2025-05-08 00:55:34.502 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:55:36.552 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 00:55:36.552 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:55:38.685 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 103 ms 
[INFO ] 2025-05-08 00:55:38.691 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:55:40.680 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:55:40.681 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:55:50.713 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 00:55:50.713 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:55:52.736 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 20 ms 
[INFO ] 2025-05-08 00:55:52.737 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:55:54.778 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:55:54.778 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:55:56.812 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:55:56.812 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:55:58.854 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 00:55:58.855 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:56:00.861 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:56:00.861 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:56:10.991 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 97 ms 
[INFO ] 2025-05-08 00:56:10.996 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:56:13.019 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:56:13.019 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:56:15.062 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:56:15.062 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:56:17.098 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 00:56:17.099 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:56:19.147 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 00:56:19.147 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:56:21.155 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:56:21.361 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:56:31.200 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 00:56:31.200 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:56:33.247 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 00:56:33.247 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:56:35.287 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:56:35.287 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:56:37.335 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 00:56:37.335 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:56:39.376 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 00:56:39.376 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:56:41.384 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:56:41.384 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:56:51.431 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 00:56:51.432 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:56:53.474 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 00:56:53.475 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:56:55.512 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 00:56:55.512 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:56:57.586 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 65 ms 
[INFO ] 2025-05-08 00:56:57.587 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:56:59.626 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:56:59.627 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:57:01.637 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:57:01.637 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:57:11.675 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 00:57:11.676 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:57:13.726 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:57:13.726 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:57:15.774 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 00:57:15.775 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:57:17.818 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 00:57:17.818 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:57:19.858 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:57:19.859 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:57:21.860 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:57:21.860 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:57:31.908 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:57:31.908 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:57:33.948 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 00:57:33.949 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:57:35.983 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:57:35.984 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:57:38.015 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 00:57:38.015 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:57:40.054 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 00:57:40.055 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:57:42.057 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:57:42.057 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:57:52.090 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 23 ms 
[INFO ] 2025-05-08 00:57:52.090 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:57:54.135 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:57:54.135 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:57:56.172 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:57:56.172 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:57:58.228 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:57:58.228 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:58:00.256 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:58:00.256 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:58:02.257 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:58:02.257 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:58:12.288 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 00:58:12.289 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:58:14.339 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 00:58:14.339 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:58:16.378 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:58:16.378 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:58:18.413 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:58:18.414 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:58:20.456 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 00:58:20.457 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:58:22.466 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:58:22.467 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:58:32.511 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 00:58:32.512 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:58:34.554 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 00:58:34.555 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:58:36.615 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 57 ms 
[INFO ] 2025-05-08 00:58:36.615 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:58:38.671 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 45 ms 
[INFO ] 2025-05-08 00:58:38.671 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:58:40.743 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 62 ms 
[INFO ] 2025-05-08 00:58:40.744 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:58:42.748 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:58:42.957 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:58:52.794 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 00:58:52.794 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:58:54.841 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 00:58:54.841 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:58:56.884 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:58:56.884 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:58:58.925 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:58:58.926 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:59:00.972 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 00:59:00.972 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:59:02.982 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:59:02.983 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:59:13.030 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 00:59:13.030 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:59:15.074 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 00:59:15.074 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:59:17.118 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 00:59:17.118 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:59:19.161 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 00:59:19.161 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:59:21.212 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 00:59:21.213 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:59:23.216 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:59:23.217 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:59:33.257 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 00:59:33.257 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:59:35.315 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 47 ms 
[INFO ] 2025-05-08 00:59:35.315 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:59:37.358 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 00:59:37.358 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:59:39.403 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 00:59:39.403 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 00:59:41.447 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 00:59:41.447 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 00:59:43.458 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 00:59:43.458 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 00:59:53.505 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 00:59:53.505 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 00:59:55.546 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 00:59:55.547 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 00:59:57.581 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 00:59:57.582 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 00:59:59.626 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 00:59:59.627 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:00:01.672 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:00:01.672 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:00:03.678 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:00:03.679 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:00:13.715 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:00:13.715 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:00:15.739 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 20 ms 
[INFO ] 2025-05-08 01:00:15.740 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:00:17.794 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 44 ms 
[INFO ] 2025-05-08 01:00:17.794 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:00:19.850 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 48 ms 
[INFO ] 2025-05-08 01:00:19.850 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:00:21.896 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:00:21.896 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:00:23.908 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:00:23.909 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:00:33.950 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:00:33.950 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:00:35.988 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:00:35.989 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:00:38.029 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:00:38.030 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:00:40.064 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:00:40.064 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:00:42.110 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:00:42.110 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:00:44.119 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:00:44.119 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:00:54.160 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:00:54.160 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:00:56.203 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:00:56.203 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:00:58.246 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:00:58.246 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:01:00.301 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 45 ms 
[INFO ] 2025-05-08 01:01:00.302 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:01:02.351 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:01:02.351 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:01:04.357 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:01:04.357 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:01:14.391 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:01:14.391 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:01:16.431 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:01:16.431 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:01:18.476 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:01:18.476 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:01:20.525 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:01:20.525 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:01:22.570 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:01:22.570 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:01:24.579 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:01:24.580 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:01:34.621 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:01:34.622 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:01:36.664 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:01:36.664 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:01:38.708 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:01:38.708 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:01:40.752 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:01:40.752 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:01:42.794 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:01:42.794 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:01:44.798 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:01:44.798 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:01:54.842 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:01:54.843 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:01:56.889 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:01:56.889 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:01:58.931 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:01:58.932 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:02:00.965 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:02:00.966 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:02:02.995 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 20 ms 
[INFO ] 2025-05-08 01:02:02.995 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:02:05.005 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:02:05.006 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:02:15.043 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:02:15.044 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:02:17.078 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 27 ms 
[INFO ] 2025-05-08 01:02:17.078 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:02:19.124 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:02:19.124 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:02:21.168 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:02:21.168 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:02:23.206 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:02:23.206 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:02:25.211 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:02:25.211 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:02:35.256 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:02:35.256 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:02:37.298 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:02:37.298 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:02:39.339 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:02:39.339 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:02:41.383 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:02:41.383 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:02:43.424 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:02:43.424 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:02:45.434 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:02:45.434 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:02:55.478 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:02:55.478 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:02:57.518 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:02:57.518 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:02:59.558 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:02:59.559 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:03:01.597 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:03:01.597 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:03:03.633 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:03:03.633 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:03:05.642 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:03:05.642 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:03:15.683 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:03:15.684 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:03:17.718 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:03:17.719 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:03:19.764 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:03:19.764 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:03:21.804 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:03:21.804 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:03:23.850 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:03:23.850 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:03:25.860 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:03:25.861 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:03:35.904 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:03:35.904 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:03:37.952 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:03:37.952 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:03:39.999 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:03:39.999 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:03:42.050 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:03:42.050 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:03:44.091 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:03:44.091 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:03:46.099 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:03:46.100 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:03:56.142 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:03:56.142 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:03:58.185 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:03:58.186 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:04:00.230 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:04:00.230 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:04:02.271 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:04:02.271 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:04:04.309 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:04:04.309 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:04:06.321 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:04:06.322 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:04:16.360 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:04:16.360 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:04:18.400 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:04:18.400 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:04:20.447 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 01:04:20.448 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:04:22.488 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:04:22.488 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:04:24.533 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:04:24.533 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:04:26.541 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:04:26.541 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:04:36.583 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:04:36.583 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:04:38.626 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:04:38.626 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:04:40.713 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 76 ms 
[INFO ] 2025-05-08 01:04:40.713 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:04:42.762 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 44 ms 
[INFO ] 2025-05-08 01:04:42.762 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:04:44.809 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:04:44.809 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:04:46.814 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:04:46.814 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:04:56.859 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:04:56.859 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:04:58.905 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:04:58.905 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:05:00.947 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:05:00.947 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:05:02.997 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:05:02.998 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:05:05.044 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:05:05.047 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:05:07.052 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:05:07.053 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:05:17.080 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 17 ms 
[INFO ] 2025-05-08 01:05:17.080 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:05:19.121 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:05:19.121 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:05:21.165 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:05:21.165 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:05:23.196 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 21 ms 
[INFO ] 2025-05-08 01:05:23.196 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:05:25.244 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:05:25.244 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:05:27.255 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:05:27.255 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:05:37.302 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 01:05:37.303 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:05:39.340 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 29 ms 
[INFO ] 2025-05-08 01:05:39.341 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:05:41.384 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:05:41.384 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:05:43.426 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:05:43.427 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:05:45.473 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:05:45.473 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:05:47.483 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:05:47.483 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:05:57.526 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:05:57.526 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:05:59.563 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 29 ms 
[INFO ] 2025-05-08 01:05:59.563 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:06:01.613 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:06:01.613 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:06:03.649 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:06:03.650 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:06:05.694 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:06:05.694 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:06:07.705 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:06:07.705 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:06:17.748 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:06:17.748 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:06:19.792 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:06:19.792 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:06:21.838 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:06:21.839 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:06:23.873 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:06:23.873 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:06:25.915 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:06:25.915 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:06:27.925 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:06:27.925 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:06:37.968 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:06:37.969 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:06:40.013 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:06:40.013 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:06:42.045 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 26 ms 
[INFO ] 2025-05-08 01:06:42.045 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:06:44.088 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:06:44.089 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:06:46.131 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:06:46.131 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:06:48.142 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:06:48.142 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:06:58.187 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:06:58.188 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:07:00.230 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:07:00.230 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:07:02.273 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:07:02.274 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:07:04.309 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:07:04.309 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:07:06.350 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:07:06.351 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:07:08.361 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:07:08.361 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:07:18.403 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:07:18.403 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:07:20.441 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:07:20.442 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:07:22.516 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 64 ms 
[INFO ] 2025-05-08 01:07:22.516 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:07:24.583 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 63 ms 
[INFO ] 2025-05-08 01:07:24.584 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:07:26.637 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 43 ms 
[INFO ] 2025-05-08 01:07:26.637 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:07:28.644 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:07:28.644 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:07:38.702 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 52 ms 
[INFO ] 2025-05-08 01:07:38.702 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:07:40.742 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:07:40.742 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:07:42.777 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 29 ms 
[INFO ] 2025-05-08 01:07:42.778 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:07:44.819 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:07:44.819 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:07:46.857 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:07:46.857 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:07:48.859 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:07:49.063 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:07:58.906 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:07:58.906 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:08:00.942 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:08:00.942 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:08:02.987 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:08:02.988 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:08:05.033 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:08:05.033 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:08:07.059 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 17 ms 
[INFO ] 2025-05-08 01:08:07.059 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:08:09.070 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:08:09.070 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:08:19.114 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:08:19.114 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:08:21.155 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:08:21.157 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:08:23.188 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 24 ms 
[INFO ] 2025-05-08 01:08:23.188 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:08:25.236 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:08:25.236 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:08:27.278 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:08:27.278 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:08:29.285 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:08:29.285 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:08:39.324 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:08:39.324 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:08:41.368 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:08:41.369 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:08:43.410 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:08:43.410 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:08:45.455 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:08:45.455 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:08:47.498 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:08:47.498 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:08:49.500 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:08:49.500 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:08:59.543 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:08:59.543 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:09:01.581 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:09:01.582 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:09:03.622 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:09:03.623 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:09:05.661 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:09:05.661 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:09:07.701 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:09:07.701 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:09:09.709 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:09:09.709 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:09:19.744 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 29 ms 
[INFO ] 2025-05-08 01:09:19.745 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:09:21.784 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:09:21.784 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:09:23.846 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 50 ms 
[INFO ] 2025-05-08 01:09:23.847 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:09:25.883 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:09:25.884 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:09:27.917 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:09:27.918 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:09:29.927 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:09:29.928 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:09:39.975 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:09:39.975 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:09:42.008 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:09:42.008 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:09:44.049 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:09:44.050 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:09:46.087 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 29 ms 
[INFO ] 2025-05-08 01:09:46.088 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:09:48.134 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:09:48.134 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:09:50.136 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:09:50.136 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:10:00.171 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:10:00.171 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:10:02.210 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:10:02.210 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:10:04.246 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:10:04.247 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:10:06.291 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:10:06.292 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:10:08.333 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:10:08.333 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:10:10.336 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:10:10.336 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:10:20.454 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 91 ms 
[INFO ] 2025-05-08 01:10:20.459 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:10:22.493 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:10:22.494 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:10:24.538 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:10:24.538 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:10:26.581 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:10:26.581 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:10:28.620 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:10:28.621 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:10:30.632 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:10:30.633 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:10:40.673 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:10:40.674 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:10:42.712 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:10:42.712 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:10:44.761 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:10:44.762 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:10:46.831 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 60 ms 
[INFO ] 2025-05-08 01:10:46.831 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:10:48.877 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 01:10:48.878 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:10:50.880 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:10:50.881 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:11:00.919 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:11:00.919 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:11:02.964 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:11:02.965 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:11:05.010 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:11:05.011 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:11:07.043 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 28 ms 
[INFO ] 2025-05-08 01:11:07.043 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:11:09.090 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:11:09.090 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:11:11.100 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:11:11.100 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:11:21.150 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:11:21.152 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:11:23.199 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:11:23.199 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:11:25.247 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:11:25.247 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:11:27.284 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:11:27.284 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:11:29.322 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 29 ms 
[INFO ] 2025-05-08 01:11:29.322 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:11:31.328 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:11:31.328 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:11:41.374 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 01:11:41.375 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:11:43.402 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 17 ms 
[INFO ] 2025-05-08 01:11:43.402 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:11:45.444 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:11:45.444 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:11:47.492 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:11:47.495 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:11:49.532 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:11:49.532 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:11:51.544 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:11:51.544 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:12:01.592 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:12:01.592 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:12:03.637 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:12:03.637 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:12:05.687 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 01:12:05.687 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:12:07.736 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:12:07.736 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:12:09.784 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:12:09.784 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:12:11.788 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:12:11.789 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:12:21.828 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:12:21.829 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:12:23.873 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:12:23.873 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:12:25.915 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:12:25.915 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:12:27.955 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:12:27.955 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:12:30.000 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:12:30.000 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:12:32.010 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:12:32.010 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:12:42.045 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 27 ms 
[INFO ] 2025-05-08 01:12:42.045 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:12:44.085 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:12:44.085 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:12:46.126 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:12:46.127 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:12:48.171 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:12:48.171 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:12:50.215 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:12:50.215 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:12:52.226 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:12:52.227 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:13:02.264 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:13:02.264 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:13:04.308 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:13:04.308 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:13:06.356 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:13:06.356 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:13:08.396 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:13:08.396 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:13:10.439 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:13:10.439 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:13:12.443 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:13:12.444 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:13:22.492 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:13:22.492 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:13:24.531 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:13:24.532 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:13:26.579 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:13:26.579 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:13:28.624 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:13:28.624 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:13:30.666 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:13:30.667 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:13:32.677 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:13:32.678 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:13:42.721 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:13:42.721 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:13:44.761 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:13:44.761 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:13:46.809 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:13:46.809 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:13:48.852 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:13:48.852 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:13:50.896 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:13:50.896 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:13:52.898 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:13:52.898 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:14:03.138 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:14:03.139 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:14:04.992 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 01:14:04.992 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:14:07.037 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:14:07.037 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:14:09.114 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 70 ms 
[INFO ] 2025-05-08 01:14:09.115 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:14:11.162 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 01:14:11.162 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:14:13.169 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:14:13.169 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:14:23.214 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 01:14:23.214 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:14:25.251 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:14:25.251 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:14:27.288 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:14:27.289 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:14:29.323 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 26 ms 
[INFO ] 2025-05-08 01:14:29.324 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:14:31.372 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 46 ms 
[INFO ] 2025-05-08 01:14:31.372 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:14:33.382 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:14:33.382 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:14:43.422 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:14:43.423 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:14:45.464 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:14:45.464 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:14:47.509 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:14:47.509 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:14:49.549 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 29 ms 
[INFO ] 2025-05-08 01:14:49.549 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:14:51.589 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:14:51.589 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:14:53.599 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:14:53.599 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:15:03.625 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 19 ms 
[INFO ] 2025-05-08 01:15:03.625 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:15:05.671 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:15:05.671 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:15:07.711 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:15:07.711 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:15:09.748 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:15:09.748 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:15:11.785 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:15:11.785 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:15:13.794 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:15:13.794 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:15:23.837 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:15:23.838 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:15:25.881 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:15:25.882 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:15:27.922 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:15:27.922 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:15:29.970 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:15:29.970 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:15:32.009 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:15:32.009 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:15:34.019 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:15:34.019 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:15:44.064 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:15:44.064 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:15:46.105 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:15:46.106 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:15:48.155 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:15:48.155 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:15:50.195 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:15:50.195 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:15:52.238 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 01:15:52.238 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:15:54.248 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:15:54.249 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:16:04.311 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 61 ms 
[INFO ] 2025-05-08 01:16:04.311 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:16:06.374 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 52 ms 
[INFO ] 2025-05-08 01:16:06.374 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:16:08.419 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:16:08.419 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:16:10.465 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:16:10.465 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:16:12.506 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:16:12.506 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:16:14.517 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:16:14.518 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:16:24.559 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:16:24.559 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:16:26.599 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:16:26.600 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:16:28.636 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:16:28.636 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:16:30.683 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:16:30.684 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:16:32.727 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:16:32.727 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:16:34.735 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:16:34.735 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:16:44.790 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 44 ms 
[INFO ] 2025-05-08 01:16:44.790 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:16:46.839 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 43 ms 
[INFO ] 2025-05-08 01:16:46.840 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:16:48.878 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:16:48.878 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:16:50.918 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:16:50.919 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:16:52.967 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 01:16:52.967 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:16:54.973 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:16:54.974 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:17:05.015 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:17:05.016 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:17:07.039 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 18 ms 
[INFO ] 2025-05-08 01:17:07.040 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:17:09.073 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 25 ms 
[INFO ] 2025-05-08 01:17:09.074 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:17:11.112 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:17:11.112 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:17:13.153 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:17:13.154 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:17:15.162 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:17:15.368 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:17:25.199 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:17:25.199 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:17:27.238 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:17:27.239 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:17:29.279 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:17:29.280 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:17:31.324 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:17:31.324 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:17:33.374 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:17:33.374 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:17:35.385 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:17:35.387 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:17:45.426 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:17:45.426 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:17:47.467 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:17:47.468 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:17:49.507 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:17:49.507 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:17:51.542 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:17:51.572 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:17:53.613 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 62 ms 
[INFO ] 2025-05-08 01:17:53.613 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:17:55.618 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:17:55.619 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:18:05.657 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:18:05.658 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:18:07.704 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:18:07.704 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:18:09.745 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:18:09.745 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:18:11.790 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:18:11.790 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:18:13.828 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:18:13.828 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:18:15.833 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:18:15.833 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:18:25.870 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:18:25.870 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:18:27.909 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:18:27.909 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:18:29.956 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:18:29.957 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:18:31.992 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 29 ms 
[INFO ] 2025-05-08 01:18:31.993 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:18:34.029 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:18:34.029 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:18:36.034 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:18:36.035 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:18:46.072 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:18:46.073 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:18:48.115 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:18:48.115 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:18:50.155 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:18:50.155 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:18:52.194 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:18:52.194 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:18:54.234 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:18:54.234 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:18:56.236 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:18:56.236 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:19:06.273 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:19:06.483 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:19:08.321 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:19:08.322 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:19:10.364 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:19:10.367 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:19:12.398 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 29 ms 
[INFO ] 2025-05-08 01:19:12.399 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:19:14.440 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:19:14.441 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:19:16.449 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:19:16.449 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:19:26.496 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:19:26.496 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:19:28.542 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:19:28.542 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:19:30.579 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:19:30.579 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:19:32.618 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:19:32.619 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:19:34.679 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 51 ms 
[INFO ] 2025-05-08 01:19:34.679 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:19:36.690 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:19:36.694 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:19:46.727 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 29 ms 
[INFO ] 2025-05-08 01:19:46.727 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:19:48.766 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:19:48.766 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:19:50.798 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 25 ms 
[INFO ] 2025-05-08 01:19:50.798 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:19:52.850 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 46 ms 
[INFO ] 2025-05-08 01:19:52.850 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:19:54.893 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:19:54.893 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:19:56.899 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:19:56.899 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:20:06.936 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 29 ms 
[INFO ] 2025-05-08 01:20:06.936 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:20:08.982 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:20:08.982 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:20:11.027 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:20:11.027 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:20:13.073 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:20:13.073 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:20:15.119 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:20:15.119 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:20:17.127 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:20:17.128 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:20:27.177 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 01:20:27.178 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:20:29.224 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:20:29.224 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:20:31.264 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:20:31.264 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:20:33.311 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:20:33.312 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:20:35.354 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:20:35.354 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:20:37.362 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:20:37.362 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:20:47.409 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:20:47.410 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:20:49.459 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:20:49.459 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:20:51.500 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:20:51.500 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:20:53.542 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:20:53.542 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:20:55.583 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:20:55.583 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:20:57.590 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:20:57.590 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:21:07.633 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:21:07.633 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:21:09.677 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:21:09.678 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:21:11.724 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:21:11.724 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:21:13.755 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 21 ms 
[INFO ] 2025-05-08 01:21:13.755 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:21:15.797 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:21:15.797 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:21:17.803 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:21:17.804 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:21:27.849 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:21:27.849 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:21:29.885 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:21:29.885 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:21:31.925 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:21:31.925 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:21:33.970 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:21:33.971 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:21:36.013 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:21:36.013 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:21:38.023 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:21:38.023 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:21:48.068 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:21:48.068 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:21:50.109 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:21:50.109 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:21:52.151 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:21:52.152 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:21:54.192 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:21:54.192 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:21:56.236 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:21:56.237 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:21:58.247 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:21:58.248 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:22:08.293 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:22:08.294 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:22:10.334 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:22:10.541 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:22:12.377 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:22:12.377 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:22:14.424 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:22:14.424 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:22:16.467 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:22:16.467 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:22:18.478 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:22:18.478 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:22:28.516 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:22:28.516 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:22:30.552 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:22:30.553 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:22:32.596 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:22:32.596 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:22:34.648 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 44 ms 
[INFO ] 2025-05-08 01:22:34.649 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:22:36.691 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:22:36.695 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:22:38.697 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:22:38.698 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:22:48.741 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:22:48.742 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:22:50.784 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:22:50.785 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:22:52.831 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:22:52.831 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:22:54.869 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:22:54.869 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:22:56.912 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:22:56.912 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:22:58.921 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:22:58.921 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:23:08.967 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:23:08.967 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:23:11.006 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:23:11.006 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:23:13.047 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:23:13.047 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:23:15.085 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:23:15.085 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:23:17.127 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:23:17.127 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:23:19.138 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:23:19.139 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:23:29.179 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:23:29.179 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:23:31.219 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:23:31.219 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:23:33.265 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:23:33.266 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:23:35.303 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:23:35.303 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:23:37.340 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:23:37.340 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:23:39.346 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:23:39.346 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:23:49.391 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:23:49.391 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:23:51.440 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:23:51.441 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:23:53.486 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:23:53.486 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:23:55.526 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:23:55.527 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:23:57.574 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:23:57.575 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:23:59.578 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:23:59.579 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:24:09.629 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 40 ms 
[INFO ] 2025-05-08 01:24:09.630 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:24:11.677 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:24:11.677 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:24:13.720 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:24:13.720 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:24:15.767 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 38 ms 
[INFO ] 2025-05-08 01:24:15.768 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:24:17.808 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 35 ms 
[INFO ] 2025-05-08 01:24:17.809 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:24:19.818 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:24:19.818 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:24:29.860 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:24:29.860 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:24:31.905 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:24:31.905 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:24:33.932 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 16 ms 
[INFO ] 2025-05-08 01:24:33.932 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:24:35.974 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:24:35.974 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:24:38.022 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:24:38.022 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:24:40.031 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:24:40.031 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:24:50.106 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 66 ms 
[INFO ] 2025-05-08 01:24:50.107 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:24:52.148 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:24:52.149 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:24:54.184 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:24:54.185 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:24:56.225 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:24:56.225 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:24:58.310 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 77 ms 
[INFO ] 2025-05-08 01:24:58.311 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:25:00.322 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:25:00.527 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:25:10.370 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 43 ms 
[INFO ] 2025-05-08 01:25:10.370 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:25:12.408 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:25:12.408 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:25:14.451 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:25:14.451 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:25:16.491 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:25:16.492 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:25:18.524 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:25:18.524 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:25:20.531 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:25:20.532 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:25:30.573 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:25:30.573 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:25:32.613 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:25:32.613 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:25:34.648 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 29 ms 
[INFO ] 2025-05-08 01:25:34.648 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:25:36.695 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:25:36.698 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:25:38.737 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 36 ms 
[INFO ] 2025-05-08 01:25:38.737 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:25:40.741 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:25:40.741 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:25:50.776 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:25:50.776 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:25:52.811 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:25:52.811 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:25:54.855 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:25:54.856 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:25:56.897 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:25:56.898 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:25:58.936 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:25:58.936 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:26:00.942 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:26:00.943 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:26:10.984 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:26:10.985 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:26:13.021 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:26:13.021 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:26:15.062 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:26:15.062 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:26:17.101 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:26:17.101 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:26:19.137 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:26:19.137 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:26:21.143 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:26:21.143 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:26:31.182 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:26:31.183 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:26:33.220 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:26:33.220 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:26:35.259 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:26:35.259 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:26:37.293 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:26:37.294 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:26:39.332 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:26:39.332 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:26:41.337 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:26:41.337 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:26:51.375 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:26:51.375 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:26:53.413 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:26:53.414 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:26:55.450 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:26:55.451 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:26:57.486 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:26:57.486 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:26:59.523 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:26:59.523 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:27:01.530 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:27:01.530 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:27:11.570 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:27:11.570 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:27:13.608 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 30 ms 
[INFO ] 2025-05-08 01:27:13.608 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:27:15.642 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 31 ms 
[INFO ] 2025-05-08 01:27:15.643 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:27:17.691 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 37 ms 
[INFO ] 2025-05-08 01:27:17.692 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:27:19.723 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 32 ms 
[INFO ] 2025-05-08 01:27:19.723 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:27:21.729 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:27:21.729 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:27:31.773 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:27:31.774 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-05-08 01:27:33.818 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 34 ms 
[INFO ] 2025-05-08 01:27:33.819 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-05-08 01:27:35.868 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 01:27:35.868 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-05-08 01:27:38.165 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 142 ms 
[INFO ] 2025-05-08 01:27:38.165 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:27:40.213 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 42 ms 
[INFO ] 2025-05-08 01:27:40.214 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-05-08 01:27:42.218 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
[INFO ] 2025-05-08 01:27:42.219 - [Sybase ~pg时间测试][LocalSybase] - normal rescan, will sleep 1s, and scan from startRid: 525083, rowId: 1 
[INFO ] 2025-05-08 01:27:52.260 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 1, 525083, 1, size: 1, cost: 39 ms 
[INFO ] 2025-05-08 01:27:52.260 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[TRACE] 2025-05-08 01:27:53.189 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] running status set to false 
[INFO ] 2025-05-08 01:27:53.224 - [Sybase ~pg时间测试][LocalSybase] - Log Miner is shutting down... 
[INFO ] 2025-05-08 01:27:53.224 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 2, 525083, 1, size: 1, cost: 33 ms 
[INFO ] 2025-05-08 01:27:53.224 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[TRACE] 2025-05-08 01:27:53.266 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746636257406 
[TRACE] 2025-05-08 01:27:53.266 - [Sybase ~pg时间测试][LocalSybase] - PDK connector node released: HazelcastSourcePdkDataNode_ecf2fe24-1395-49b6-9448-da59d864cc1e_1746636257406 
[TRACE] 2025-05-08 01:27:53.266 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] schema data cleaned 
[TRACE] 2025-05-08 01:27:53.266 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] monitor closed 
[TRACE] 2025-05-08 01:27:53.270 - [Sybase ~pg时间测试][LocalSybase] - Node LocalSybase[ecf2fe24-1395-49b6-9448-da59d864cc1e] close complete, cost 81 ms 
[TRACE] 2025-05-08 01:27:53.270 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] running status set to false 
[TRACE] 2025-05-08 01:27:53.875 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node stopped: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746636257273 
[TRACE] 2025-05-08 01:27:53.876 - [Sybase ~pg时间测试][PG - Copy] - PDK connector node released: HazelcastTargetPdkDataNode_8512ae32-5586-472d-9be0-9a10feed51db_1746636257273 
[TRACE] 2025-05-08 01:27:53.876 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] schema data cleaned 
[TRACE] 2025-05-08 01:27:53.876 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] monitor closed 
[TRACE] 2025-05-08 01:27:54.077 - [Sybase ~pg时间测试][PG - Copy] - Node PG - Copy[8512ae32-5586-472d-9be0-9a10feed51db] close complete, cost 606 ms 
[TRACE] 2025-05-08 01:27:55.181 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 01:27:55.186 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@495a98fc 
[TRACE] 2025-05-08 01:27:55.186 - [Sybase ~pg时间测试] - Stop task milestones: 6819adaabec2de0faadb1cd6(Sybase ~pg时间测试)  
[INFO ] 2025-05-08 01:27:55.268 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 3, 525083, 1, size: 1, cost: 41 ms 
[INFO ] 2025-05-08 01:27:55.268 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[TRACE] 2025-05-08 01:27:55.473 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[TRACE] 2025-05-08 01:27:55.473 - [Sybase ~pg时间测试] - Snapshot order controller have been removed 
[INFO ] 2025-05-08 01:27:55.473 - [Sybase ~pg时间测试] - Task stopped. 
[INFO ] 2025-05-08 01:27:57.322 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 4, 525083, 1, size: 1, cost: 48 ms 
[INFO ] 2025-05-08 01:27:57.322 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-05-08 01:27:59.408 - [Sybase ~pg时间测试][LocalSybase] - scan online t: 5, 525083, 1, size: 1, cost: 83 ms 
[INFO ] 2025-05-08 01:27:59.408 - [Sybase ~pg时间测试][LocalSybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[TRACE] 2025-05-08 01:28:00.493 - [Sybase ~pg时间测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 01:28:00.494 - [Sybase ~pg时间测试] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@495a98fc 
[TRACE] 2025-05-08 01:28:00.494 - [Sybase ~pg时间测试] - Stopped task aspect(s) 
[INFO ] 2025-05-08 01:28:00.596 - [Sybase ~pg时间测试] - Task stopped. 
[TRACE] 2025-05-08 01:28:00.596 - [Sybase ~pg时间测试] - Remove memory task client succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[TRACE] 2025-05-08 01:28:00.596 - [Sybase ~pg时间测试] - Destroy memory task client cache succeed, task: Sybase ~pg时间测试[6819adaabec2de0faadb1cd6] 
[INFO ] 2025-05-08 01:28:01.414 - [Sybase ~pg时间测试][LocalSybase] - uncommit trans size: 0 
