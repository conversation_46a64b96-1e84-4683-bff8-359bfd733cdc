[INFO ] 2025-02-13 10:51:07.355 - [任务 2] - Start task milestones: 67ad5e06ebea6a00371b07b9(任务 2) 
[INFO ] 2025-02-13 10:51:07.357 - [任务 2] - Task initialization... 
[INFO ] 2025-02-13 10:51:07.759 - [任务 2] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2025-02-13 10:51:07.851 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-13 10:51:07.852 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] start preload schema,table counts: 1 
[INFO ] 2025-02-13 10:51:07.852 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] start preload schema,table counts: 1 
[INFO ] 2025-02-13 10:51:07.856 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] preload schema finished, cost 0 ms 
[INFO ] 2025-02-13 10:51:07.856 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] preload schema finished, cost 0 ms 
[INFO ] 2025-02-13 10:51:08.715 - [任务 2][MysqlSource] - Node(MysqlSource) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2025-02-13 10:51:08.716 - [任务 2][MysqlSource] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-13 10:51:08.909 - [任务 2][MongoSource] - Source node "MongoSource" read batch size: 100 
[INFO ] 2025-02-13 10:51:08.910 - [任务 2][MongoSource] - Source node "MongoSource" event queue capacity: 200 
[INFO ] 2025-02-13 10:51:08.911 - [任务 2][MongoSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-13 10:51:09.051 - [任务 2][MongoSource] - batch offset found: {},stream offset found: {"cdcOffset":1739415068,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-02-13 10:51:09.124 - [任务 2][MongoSource] - Initial sync started 
[INFO ] 2025-02-13 10:51:09.143 - [任务 2][MongoSource] - Starting batch read, table name: CAR.CLAIM, offset: null 
[INFO ] 2025-02-13 10:51:09.143 - [任务 2][MongoSource] - Table CAR.CLAIM is going to be initial synced 
[INFO ] 2025-02-13 10:51:09.344 - [任务 2][MongoSource] - Query table 'CAR.CLAIM' counts: 1075 
[INFO ] 2025-02-13 10:51:09.429 - [任务 2][MongoSource] - Table [CAR.CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2025-02-13 10:51:09.434 - [任务 2][MongoSource] - Initial sync completed 
[INFO ] 2025-02-13 10:51:09.434 - [任务 2][MongoSource] - Incremental sync starting... 
[INFO ] 2025-02-13 10:51:09.435 - [任务 2][MongoSource] - Initial sync completed 
[INFO ] 2025-02-13 10:51:09.436 - [任务 2][MongoSource] - Starting stream read, table list: [CAR.CLAIM], offset: {"cdcOffset":1739415068,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-02-13 10:51:09.640 - [任务 2][MongoSource] - Connector MongoDB incremental start succeed, tables: [CAR.CLAIM], data change syncing 
[INFO ] 2025-02-13 10:51:23.204 - [任务 2] - Stop task milestones: 67ad5e06ebea6a00371b07b9(任务 2)  
[INFO ] 2025-02-13 10:51:23.207 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] running status set to false 
[INFO ] 2025-02-13 10:51:23.244 - [任务 2][MongoSource] - PDK connector node stopped: HazelcastSourcePdkDataNode-2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a 
[INFO ] 2025-02-13 10:51:23.245 - [任务 2][MongoSource] - PDK connector node released: HazelcastSourcePdkDataNode-2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a 
[INFO ] 2025-02-13 10:51:23.246 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] schema data cleaned 
[INFO ] 2025-02-13 10:51:23.249 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] monitor closed 
[INFO ] 2025-02-13 10:51:23.265 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] close complete, cost 48 ms 
[INFO ] 2025-02-13 10:51:23.266 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] running status set to false 
[INFO ] 2025-02-13 10:51:23.313 - [任务 2][MysqlSource] - PDK connector node stopped: HazelcastTargetPdkDataNode-dfb4b0b7-cafc-43b0-a844-74177a0a7a9e 
[INFO ] 2025-02-13 10:51:23.314 - [任务 2][MysqlSource] - PDK connector node released: HazelcastTargetPdkDataNode-dfb4b0b7-cafc-43b0-a844-74177a0a7a9e 
[INFO ] 2025-02-13 10:51:23.314 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] schema data cleaned 
[INFO ] 2025-02-13 10:51:23.314 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] monitor closed 
[INFO ] 2025-02-13 10:51:23.520 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] close complete, cost 50 ms 
[INFO ] 2025-02-13 10:51:26.127 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2025-02-13 10:51:26.131 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2025-02-13 10:51:26.131 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2025-02-13 10:51:26.217 - [任务 2] - Remove memory task client succeed, task: 任务 2[67ad5e06ebea6a00371b07b9] 
[INFO ] 2025-02-13 10:51:26.217 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[67ad5e06ebea6a00371b07b9] 
[INFO ] 2025-02-13 14:51:07.030 - [任务 2] - Task initialization... 
[INFO ] 2025-02-13 14:51:07.238 - [任务 2] - Start task milestones: 67ad5e06ebea6a00371b07b9(任务 2) 
[INFO ] 2025-02-13 14:51:07.285 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2025-02-13 14:51:07.490 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-13 14:51:07.498 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] start preload schema,table counts: 1 
[INFO ] 2025-02-13 14:51:07.513 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] start preload schema,table counts: 1 
[INFO ] 2025-02-13 14:51:07.516 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] preload schema finished, cost 0 ms 
[INFO ] 2025-02-13 14:51:07.517 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] preload schema finished, cost 1 ms 
[INFO ] 2025-02-13 14:51:08.372 - [任务 2][MongoSource] - Source node "MongoSource" read batch size: 100 
[INFO ] 2025-02-13 14:51:08.372 - [任务 2][MongoSource] - Source node "MongoSource" event queue capacity: 200 
[INFO ] 2025-02-13 14:51:08.373 - [任务 2][MongoSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-13 14:51:08.532 - [任务 2][MongoSource] - batch offset found: {},stream offset found: {"cdcOffset":1739429468,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-02-13 14:51:08.615 - [任务 2][MongoSource] - Initial sync started 
[INFO ] 2025-02-13 14:51:08.619 - [任务 2][MongoSource] - Starting batch read, table name: testYan, offset: null 
[INFO ] 2025-02-13 14:51:08.619 - [任务 2][MongoSource] - Table testYan is going to be initial synced 
[INFO ] 2025-02-13 14:51:08.685 - [任务 2][MongoSource] - Table [testYan] has been completed batch read, will skip batch read on the next run 
[INFO ] 2025-02-13 14:51:08.686 - [任务 2][MysqlSource] - Node(MysqlSource) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2025-02-13 14:51:08.686 - [任务 2][MysqlSource] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-13 14:51:08.700 - [任务 2][MongoSource] - Query table 'testYan' counts: 3 
[INFO ] 2025-02-13 14:51:08.700 - [任务 2][MongoSource] - Initial sync completed 
[INFO ] 2025-02-13 14:51:08.700 - [任务 2][MongoSource] - Incremental sync starting... 
[INFO ] 2025-02-13 14:51:08.700 - [任务 2][MongoSource] - Initial sync completed 
[INFO ] 2025-02-13 14:51:08.706 - [任务 2][MongoSource] - Starting stream read, table list: [testYan], offset: {"cdcOffset":1739429468,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-02-13 14:51:08.756 - [任务 2][MongoSource] - Connector MongoDB incremental start succeed, tables: [testYan], data change syncing 
[INFO ] 2025-02-13 14:51:08.757 - [任务 2][MysqlSource] - Exception skipping - The current exception does not match the skip exception strategy, message: Missing privileges when write data on mysql. 
 - Executing operation: createTable
 - Table name: [testYan]
 - Missing privileges: [] 
[ERROR] 2025-02-13 14:51:08.793 - [任务 2][MysqlSource] - Missing privileges when write data on mysql. 
 - Executing operation: createTable
 - Table name: [testYan]
 - Missing privileges: [] <-- Error Message -->
Missing privileges when write data on mysql. 
 - Executing operation: createTable
 - Table name: [testYan]
 - Missing privileges: []

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column '_id' used in key specification without a key length
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: BLOB/TEXT column '_id' used in key specification without a key length
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectWritePrivileges(MysqlExceptionCollector.java:69)
	at io.tapdata.connector.mysql.MysqlConnector.createTableV2(MysqlConnector.java:391)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$6(HazelcastTargetPdkBaseNode.java:291)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createTable$7(HazelcastTargetPdkBaseNode.java:289)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:284)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:203)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:149)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column '_id' used in key specification without a key length
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.connector.mysql.MysqlConnector.createTableV2(MysqlConnector.java:386)
	... 30 more

[INFO ] 2025-02-13 14:51:08.794 - [任务 2][MysqlSource] - Job suspend in error handle 
[INFO ] 2025-02-13 14:51:09.244 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] running status set to false 
[INFO ] 2025-02-13 14:51:09.245 - [任务 2][MongoSource] - PDK connector node stopped: HazelcastSourcePdkDataNode-2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a 
[INFO ] 2025-02-13 14:51:09.245 - [任务 2][MongoSource] - PDK connector node released: HazelcastSourcePdkDataNode-2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a 
[INFO ] 2025-02-13 14:51:09.245 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] schema data cleaned 
[INFO ] 2025-02-13 14:51:09.247 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] monitor closed 
[INFO ] 2025-02-13 14:51:09.251 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] close complete, cost 31 ms 
[INFO ] 2025-02-13 14:51:09.252 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] running status set to false 
[INFO ] 2025-02-13 14:51:09.272 - [任务 2][MysqlSource] - PDK connector node stopped: HazelcastTargetPdkDataNode-dfb4b0b7-cafc-43b0-a844-74177a0a7a9e 
[INFO ] 2025-02-13 14:51:09.272 - [任务 2][MysqlSource] - PDK connector node released: HazelcastTargetPdkDataNode-dfb4b0b7-cafc-43b0-a844-74177a0a7a9e 
[INFO ] 2025-02-13 14:51:09.272 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] schema data cleaned 
[INFO ] 2025-02-13 14:51:09.274 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] monitor closed 
[INFO ] 2025-02-13 14:51:09.274 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] close complete, cost 23 ms 
[INFO ] 2025-02-13 14:51:09.737 - [任务 2][MongoSource] - Incremental sync completed 
[INFO ] 2025-02-13 14:51:11.032 - [任务 2] - Task [任务 2] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-02-13 14:51:11.032 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2025-02-13 14:51:11.060 - [任务 2] - Stop task milestones: 67ad5e06ebea6a00371b07b9(任务 2)  
[INFO ] 2025-02-13 14:51:11.061 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2025-02-13 14:51:11.061 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2025-02-13 14:51:11.078 - [任务 2] - Remove memory task client succeed, task: 任务 2[67ad5e06ebea6a00371b07b9] 
[INFO ] 2025-02-13 14:51:11.078 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[67ad5e06ebea6a00371b07b9] 
[INFO ] 2025-02-13 14:51:49.283 - [任务 2] - Task initialization... 
[INFO ] 2025-02-13 14:51:49.489 - [任务 2] - Start task milestones: 67ad5e06ebea6a00371b07b9(任务 2) 
[INFO ] 2025-02-13 14:51:49.489 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2025-02-13 14:51:49.618 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-13 14:51:49.701 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] start preload schema,table counts: 1 
[INFO ] 2025-02-13 14:51:49.701 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] start preload schema,table counts: 1 
[INFO ] 2025-02-13 14:51:49.701 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] preload schema finished, cost 0 ms 
[INFO ] 2025-02-13 14:51:49.702 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] preload schema finished, cost 0 ms 
[INFO ] 2025-02-13 14:51:50.440 - [任务 2][MongoSource] - Source node "MongoSource" read batch size: 100 
[INFO ] 2025-02-13 14:51:50.440 - [任务 2][MongoSource] - Source node "MongoSource" event queue capacity: 200 
[INFO ] 2025-02-13 14:51:50.440 - [任务 2][MongoSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-13 14:51:50.645 - [任务 2][MongoSource] - batch offset found: {},stream offset found: {"cdcOffset":1739429510,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-02-13 14:51:50.661 - [任务 2][MongoSource] - Initial sync started 
[INFO ] 2025-02-13 14:51:50.662 - [任务 2][MongoSource] - Starting batch read, table name: testYan, offset: null 
[INFO ] 2025-02-13 14:51:50.691 - [任务 2][MongoSource] - Table testYan is going to be initial synced 
[INFO ] 2025-02-13 14:51:50.692 - [任务 2][MongoSource] - Query table 'testYan' counts: 3 
[INFO ] 2025-02-13 14:51:50.692 - [任务 2][MongoSource] - Table [testYan] has been completed batch read, will skip batch read on the next run 
[INFO ] 2025-02-13 14:51:50.692 - [任务 2][MongoSource] - Initial sync completed 
[INFO ] 2025-02-13 14:51:50.693 - [任务 2][MongoSource] - Incremental sync starting... 
[INFO ] 2025-02-13 14:51:50.693 - [任务 2][MongoSource] - Initial sync completed 
[INFO ] 2025-02-13 14:51:50.711 - [任务 2][MongoSource] - Starting stream read, table list: [testYan], offset: {"cdcOffset":1739429510,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-02-13 14:51:50.712 - [任务 2][MongoSource] - Connector MongoDB incremental start succeed, tables: [testYan], data change syncing 
[INFO ] 2025-02-13 14:51:50.734 - [任务 2][MysqlSource] - Node(MysqlSource) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2025-02-13 14:51:50.735 - [任务 2][MysqlSource] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-13 14:51:50.759 - [任务 2][MysqlSource] - Exception skipping - The current exception does not match the skip exception strategy, message: Missing privileges when write data on mysql. 
 - Executing operation: createTable
 - Table name: [testYan]
 - Missing privileges: [] 
[ERROR] 2025-02-13 14:51:50.787 - [任务 2][MysqlSource] - Missing privileges when write data on mysql. 
 - Executing operation: createTable
 - Table name: [testYan]
 - Missing privileges: [] <-- Error Message -->
Missing privileges when write data on mysql. 
 - Executing operation: createTable
 - Table name: [testYan]
 - Missing privileges: []

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column '_id' used in key specification without a key length
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: BLOB/TEXT column '_id' used in key specification without a key length
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectWritePrivileges(MysqlExceptionCollector.java:69)
	at io.tapdata.connector.mysql.MysqlConnector.createTableV2(MysqlConnector.java:391)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$6(HazelcastTargetPdkBaseNode.java:291)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createTable$7(HazelcastTargetPdkBaseNode.java:289)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:284)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:203)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:149)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column '_id' used in key specification without a key length
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.connector.mysql.MysqlConnector.createTableV2(MysqlConnector.java:386)
	... 30 more

[INFO ] 2025-02-13 14:51:50.787 - [任务 2][MysqlSource] - Job suspend in error handle 
[INFO ] 2025-02-13 14:51:51.110 - [任务 2] - Task [任务 2] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-02-13 14:51:51.207 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] running status set to false 
[INFO ] 2025-02-13 14:51:51.219 - [任务 2][MongoSource] - PDK connector node stopped: HazelcastSourcePdkDataNode-2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a 
[INFO ] 2025-02-13 14:51:51.219 - [任务 2][MongoSource] - PDK connector node released: HazelcastSourcePdkDataNode-2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a 
[INFO ] 2025-02-13 14:51:51.219 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] schema data cleaned 
[INFO ] 2025-02-13 14:51:51.221 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] monitor closed 
[INFO ] 2025-02-13 14:51:51.222 - [任务 2][MongoSource] - Node MongoSource[2158261a-5d8b-4d5d-8ff5-1b01e3f81d4a] close complete, cost 13 ms 
[INFO ] 2025-02-13 14:51:51.235 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] running status set to false 
[INFO ] 2025-02-13 14:51:51.235 - [任务 2][MysqlSource] - PDK connector node stopped: HazelcastTargetPdkDataNode-dfb4b0b7-cafc-43b0-a844-74177a0a7a9e 
[INFO ] 2025-02-13 14:51:51.235 - [任务 2][MysqlSource] - PDK connector node released: HazelcastTargetPdkDataNode-dfb4b0b7-cafc-43b0-a844-74177a0a7a9e 
[INFO ] 2025-02-13 14:51:51.235 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] schema data cleaned 
[INFO ] 2025-02-13 14:51:51.236 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] monitor closed 
[INFO ] 2025-02-13 14:51:51.239 - [任务 2][MysqlSource] - Node MysqlSource[dfb4b0b7-cafc-43b0-a844-74177a0a7a9e] close complete, cost 14 ms 
[INFO ] 2025-02-13 14:51:51.729 - [任务 2][MongoSource] - Incremental sync completed 
[INFO ] 2025-02-13 14:51:56.133 - [任务 2] - Task [任务 2] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-02-13 14:51:56.134 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2025-02-13 14:51:56.164 - [任务 2] - Stop task milestones: 67ad5e06ebea6a00371b07b9(任务 2)  
[INFO ] 2025-02-13 14:51:56.165 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2025-02-13 14:51:56.165 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2025-02-13 14:51:56.186 - [任务 2] - Remove memory task client succeed, task: 任务 2[67ad5e06ebea6a00371b07b9] 
[INFO ] 2025-02-13 14:51:56.189 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[67ad5e06ebea6a00371b07b9] 
