[INFO ] 2024-03-27 11:25:11.041 - [任务 20] - Start task milestones: 660391608b5bca60f72decbe(任务 20) 
[INFO ] 2024-03-27 11:25:11.042 - [任务 20] - Task initialization... 
[INFO ] 2024-03-27 11:25:11.042 - [任务 20] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 11:25:11.042 - [任务 20] - The engine receives 任务 20 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 11:25:11.042 - [任务 20][test9] - Node test9[395450af-a05a-417a-bffe-447ea1459b05] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:25:11.043 - [任务 20][CLAIM] - Node CLAIM[ad3a73a9-84ef-4dd6-8e23-cd39ad1af2dd] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:25:11.061 - [任务 20][test9] - Node test9[395450af-a05a-417a-bffe-447ea1459b05] preload schema finished, cost 26 ms 
[INFO ] 2024-03-27 11:25:11.061 - [任务 20][CLAIM] - Node CLAIM[ad3a73a9-84ef-4dd6-8e23-cd39ad1af2dd] preload schema finished, cost 26 ms 
[INFO ] 2024-03-27 11:25:11.961 - [任务 20][test9] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 11:25:11.993 - [任务 20][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 11:25:11.995 - [任务 20][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 11:25:11.995 - [任务 20][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 11:25:12.011 - [任务 20][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":144728890,"gtidSet":""} 
[INFO ] 2024-03-27 11:25:12.111 - [任务 20][CLAIM] - Initial sync started 
[INFO ] 2024-03-27 11:25:12.118 - [任务 20][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-27 11:25:12.119 - [任务 20][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-27 11:25:12.201 - [任务 20][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-27 11:25:12.303 - [任务 20][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 11:25:12.330 - [任务 20][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 11:25:12.335 - [任务 20][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 11:25:12.336 - [任务 20][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":144728890,"gtidSet":""} 
[INFO ] 2024-03-27 11:25:12.392 - [任务 20][CLAIM] - Starting mysql cdc, server name: c67c73e3-cf2c-4a62-ab4a-7d858f355d4c 
[INFO ] 2024-03-27 11:25:12.405 - [任务 20][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1724682773
  time.precision.mode: adaptive_time_microseconds
  database.server.name: c67c73e3-cf2c-4a62-ab4a-7d858f355d4c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-c67c73e3-cf2c-4a62-ab4a-7d858f355d4c
  database.hostname: 127.0.0.1
  database.password: ********
  name: c67c73e3-cf2c-4a62-ab4a-7d858f355d4c
  pdk.offset.string: {"name":"c67c73e3-cf2c-4a62-ab4a-7d858f355d4c","offset":{"{\"server\":\"c67c73e3-cf2c-4a62-ab4a-7d858f355d4c\"}":"{\"file\":\"binlog.000020\",\"pos\":144728890,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 11:25:12.468 - [任务 20][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 11:25:53.965 - [任务 20][CLAIM] - Read DDL: alter table CLAIM rename column `name1` to `name`, about to be packaged as some event(s) 
[INFO ] 2024-03-27 11:25:53.966 - [任务 20][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldNameEvent
  - Offset: MysqlStreamOffset{name='c67c73e3-cf2c-4a62-ab4a-7d858f355d4c', offset={{"server":"c67c73e3-cf2c-4a62-ab4a-7d858f355d4c"}={"ts_sec":1711509953,"file":"binlog.000020","pos":144729151,"server_id":1}}} 
[INFO ] 2024-03-27 11:25:53.966 - [任务 20][CLAIM] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='CLAIM', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@5d724750} 
[INFO ] 2024-03-27 11:25:53.972 - [任务 20][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660391608b5bca60f72decbe 
[INFO ] 2024-03-27 11:25:54.028 - [任务 20][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 12:25:21.964 - [任务 20] - Task initialization... 
[INFO ] 2024-03-27 12:25:21.965 - [任务 20] - Start task milestones: 660391608b5bca60f72decbe(任务 20) 
[INFO ] 2024-03-27 12:25:21.965 - [任务 20] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 12:25:21.965 - [任务 20] - The engine receives 任务 20 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 12:25:21.965 - [任务 20][CLAIM] - Node CLAIM[ad3a73a9-84ef-4dd6-8e23-cd39ad1af2dd] start preload schema,table counts: 1 
[INFO ] 2024-03-27 12:25:21.966 - [任务 20][test9] - Node test9[395450af-a05a-417a-bffe-447ea1459b05] start preload schema,table counts: 1 
[INFO ] 2024-03-27 12:25:21.979 - [任务 20][test9] - Node test9[395450af-a05a-417a-bffe-447ea1459b05] preload schema finished, cost 17 ms 
[INFO ] 2024-03-27 12:25:21.981 - [任务 20][CLAIM] - Node CLAIM[ad3a73a9-84ef-4dd6-8e23-cd39ad1af2dd] preload schema finished, cost 22 ms 
[INFO ] 2024-03-27 12:25:22.255 - [任务 20][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 12:25:22.258 - [任务 20][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 12:25:22.274 - [任务 20][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-27 12:25:22.299 - [任务 20][CLAIM] - batch offset found: {"CLAIM":{}},stream offset found: {"name":"c67c73e3-cf2c-4a62-ab4a-7d858f355d4c","offset":{"{\"server\":\"c67c73e3-cf2c-4a62-ab4a-7d858f355d4c\"}":"{\"ts_sec\":1711509953,\"file\":\"binlog.000020\",\"pos\":144899281,\"server_id\":1}"}} 
[INFO ] 2024-03-27 12:25:22.321 - [任务 20][test9] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 12:25:22.416 - [任务 20][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 12:25:22.420 - [任务 20][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 12:25:22.420 - [任务 20][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"c67c73e3-cf2c-4a62-ab4a-7d858f355d4c","offset":{"{\"server\":\"c67c73e3-cf2c-4a62-ab4a-7d858f355d4c\"}":"{\"ts_sec\":1711509953,\"file\":\"binlog.000020\",\"pos\":144899281,\"server_id\":1}"}} 
[INFO ] 2024-03-27 12:25:22.462 - [任务 20][CLAIM] - Starting mysql cdc, server name: c67c73e3-cf2c-4a62-ab4a-7d858f355d4c 
[INFO ] 2024-03-27 12:25:22.506 - [任务 20][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 369309093
  time.precision.mode: adaptive_time_microseconds
  database.server.name: c67c73e3-cf2c-4a62-ab4a-7d858f355d4c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-c67c73e3-cf2c-4a62-ab4a-7d858f355d4c
  database.hostname: 127.0.0.1
  database.password: ********
  name: c67c73e3-cf2c-4a62-ab4a-7d858f355d4c
  pdk.offset.string: {"name":"c67c73e3-cf2c-4a62-ab4a-7d858f355d4c","offset":{"{\"server\":\"c67c73e3-cf2c-4a62-ab4a-7d858f355d4c\"}":"{\"ts_sec\":1711509953,\"file\":\"binlog.000020\",\"pos\":144899281,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 12:25:23.640 - [任务 20][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 14:38:18.512 - [任务 20][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-27 14:38:18.515 - [任务 20][CLAIM] - Incremental sync completed 
[ERROR] 2024-03-27 14:38:18.518 - [任务 20][CLAIM] - java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected <-- Error Message -->
java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: BinaryLogClient is already connected
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:566)
	com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected
	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:142)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:333)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:458)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.IllegalStateException: BinaryLogClient is already connected
	at com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:566)
	at com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	... 1 more

[INFO ] 2024-03-27 14:38:18.518 - [任务 20][CLAIM] - Job suspend in error handle 
[INFO ] 2024-03-27 14:38:18.518 - [任务 20][CLAIM] - Node CLAIM[ad3a73a9-84ef-4dd6-8e23-cd39ad1af2dd] running status set to false 
[INFO ] 2024-03-27 14:38:18.518 - [任务 20][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-ad3a73a9-84ef-4dd6-8e23-cd39ad1af2dd 
[INFO ] 2024-03-27 14:38:18.518 - [任务 20][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-ad3a73a9-84ef-4dd6-8e23-cd39ad1af2dd 
[INFO ] 2024-03-27 14:38:18.518 - [任务 20][CLAIM] - Node CLAIM[ad3a73a9-84ef-4dd6-8e23-cd39ad1af2dd] schema data cleaned 
[INFO ] 2024-03-27 14:38:18.518 - [任务 20][CLAIM] - Node CLAIM[ad3a73a9-84ef-4dd6-8e23-cd39ad1af2dd] monitor closed 
[INFO ] 2024-03-27 14:38:18.519 - [任务 20][CLAIM] - Node CLAIM[ad3a73a9-84ef-4dd6-8e23-cd39ad1af2dd] close complete, cost 34 ms 
[INFO ] 2024-03-27 14:38:18.519 - [任务 20][test9] - Node test9[395450af-a05a-417a-bffe-447ea1459b05] running status set to false 
[INFO ] 2024-03-27 14:38:18.542 - [任务 20][test9] - PDK connector node stopped: HazelcastTargetPdkDataNode-395450af-a05a-417a-bffe-447ea1459b05 
[INFO ] 2024-03-27 14:38:18.554 - [任务 20][test9] - PDK connector node released: HazelcastTargetPdkDataNode-395450af-a05a-417a-bffe-447ea1459b05 
[INFO ] 2024-03-27 14:38:18.555 - [任务 20][test9] - Node test9[395450af-a05a-417a-bffe-447ea1459b05] schema data cleaned 
[INFO ] 2024-03-27 14:38:18.555 - [任务 20][test9] - Node test9[395450af-a05a-417a-bffe-447ea1459b05] monitor closed 
[INFO ] 2024-03-27 14:38:18.555 - [任务 20][test9] - Node test9[395450af-a05a-417a-bffe-447ea1459b05] close complete, cost 32 ms 
[WARN ] 2024-03-27 14:38:18.593 - [任务 20] - Save milestone failed: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/update": Broken pipe (Write failed); nested exception is java.net.SocketException: Broken pipe (Write failed). 
[INFO ] 2024-03-27 14:38:22.901 - [任务 20] - Task [任务 20] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-27 14:38:22.919 - [任务 20] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 14:38:22.941 - [任务 20] - Stop task milestones: 660391608b5bca60f72decbe(任务 20)  
[INFO ] 2024-03-27 14:38:22.950 - [任务 20] - Stopped task aspect(s) 
[INFO ] 2024-03-27 14:38:22.950 - [任务 20] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 14:38:22.977 - [任务 20] - Remove memory task client succeed, task: 任务 20[660391608b5bca60f72decbe] 
[INFO ] 2024-03-27 14:38:22.982 - [任务 20] - Destroy memory task client cache succeed, task: 任务 20[660391608b5bca60f72decbe] 
