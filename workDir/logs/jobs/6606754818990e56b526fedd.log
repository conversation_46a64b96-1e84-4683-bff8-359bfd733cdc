[INFO ] 2024-03-29 16:01:35.334 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:35.336 - [suppliers_import_import_import_import(100)][7a3defba-3917-4d9b-865c-c36ce30d8c4c] - Node 7a3defba-3917-4d9b-865c-c36ce30d8c4c[7a3defba-3917-4d9b-865c-c36ce30d8c4c] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:01:35.336 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:35.336 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:35.336 - [suppliers_import_import_import_import(100)][7a3defba-3917-4d9b-865c-c36ce30d8c4c] - Node 7a3defba-3917-4d9b-865c-c36ce30d8c4c[7a3defba-3917-4d9b-865c-c36ce30d8c4c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:35.336 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:35.375 - [suppliers_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:01:35.475 - [suppliers_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@475d5d24 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@475d5d24 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@475d5d24 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 16:01:35.479 - [suppliers_import_import_import_import(100)][bb256c6a-68bc-4786-b41d-fcdfea17685f] - Node bb256c6a-68bc-4786-b41d-fcdfea17685f[bb256c6a-68bc-4786-b41d-fcdfea17685f] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:01:35.479 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:35.479 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:35.479 - [suppliers_import_import_import_import(100)][bb256c6a-68bc-4786-b41d-fcdfea17685f] - Node bb256c6a-68bc-4786-b41d-fcdfea17685f[bb256c6a-68bc-4786-b41d-fcdfea17685f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:35.479 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:35.479 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:35.533 - [suppliers_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:01:35.534 - [suppliers_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@48a6fc91 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@48a6fc91 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@48a6fc91 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:01:35.936 - [suppliers_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:01:35.950 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] running status set to false 
[INFO ] 2024-03-29 16:01:35.950 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:35.950 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:35.950 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] schema data cleaned 
[INFO ] 2024-03-29 16:01:35.951 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] monitor closed 
[INFO ] 2024-03-29 16:01:35.951 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] close complete, cost 13 ms 
[WARN ] 2024-03-29 16:01:36.094 - [suppliers_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:01:36.094 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] running status set to false 
[INFO ] 2024-03-29 16:01:36.099 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:36.099 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:36.100 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] schema data cleaned 
[INFO ] 2024-03-29 16:01:36.100 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] monitor closed 
[INFO ] 2024-03-29 16:01:36.302 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] close complete, cost 6 ms 
[INFO ] 2024-03-29 16:01:37.987 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] running status set to false 
[INFO ] 2024-03-29 16:01:37.999 - [suppliers_import_import_import_import(100)][7a3defba-3917-4d9b-865c-c36ce30d8c4c] - Node 7a3defba-3917-4d9b-865c-c36ce30d8c4c[7a3defba-3917-4d9b-865c-c36ce30d8c4c] running status set to false 
[INFO ] 2024-03-29 16:01:38.000 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] schema data cleaned 
[INFO ] 2024-03-29 16:01:38.000 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] monitor closed 
[INFO ] 2024-03-29 16:01:38.000 - [suppliers_import_import_import_import(100)][7a3defba-3917-4d9b-865c-c36ce30d8c4c] - Node 7a3defba-3917-4d9b-865c-c36ce30d8c4c[7a3defba-3917-4d9b-865c-c36ce30d8c4c] schema data cleaned 
[INFO ] 2024-03-29 16:01:38.000 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] close complete, cost 23 ms 
[INFO ] 2024-03-29 16:01:38.000 - [suppliers_import_import_import_import(100)][7a3defba-3917-4d9b-865c-c36ce30d8c4c] - Node 7a3defba-3917-4d9b-865c-c36ce30d8c4c[7a3defba-3917-4d9b-865c-c36ce30d8c4c] monitor closed 
[INFO ] 2024-03-29 16:01:38.006 - [suppliers_import_import_import_import(100)][7a3defba-3917-4d9b-865c-c36ce30d8c4c] - Node 7a3defba-3917-4d9b-865c-c36ce30d8c4c[7a3defba-3917-4d9b-865c-c36ce30d8c4c] close complete, cost 21 ms 
[INFO ] 2024-03-29 16:01:38.006 - [suppliers_import_import_import_import(100)] - load tapTable task 6606754818990e56b526fedd-7a3defba-3917-4d9b-865c-c36ce30d8c4c complete, cost 2731ms 
[INFO ] 2024-03-29 16:01:38.068 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] running status set to false 
[INFO ] 2024-03-29 16:01:38.069 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] schema data cleaned 
[INFO ] 2024-03-29 16:01:38.069 - [suppliers_import_import_import_import(100)][bb256c6a-68bc-4786-b41d-fcdfea17685f] - Node bb256c6a-68bc-4786-b41d-fcdfea17685f[bb256c6a-68bc-4786-b41d-fcdfea17685f] running status set to false 
[INFO ] 2024-03-29 16:01:38.069 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] monitor closed 
[INFO ] 2024-03-29 16:01:38.069 - [suppliers_import_import_import_import(100)][bb256c6a-68bc-4786-b41d-fcdfea17685f] - Node bb256c6a-68bc-4786-b41d-fcdfea17685f[bb256c6a-68bc-4786-b41d-fcdfea17685f] schema data cleaned 
[INFO ] 2024-03-29 16:01:38.069 - [suppliers_import_import_import_import(100)][bb256c6a-68bc-4786-b41d-fcdfea17685f] - Node bb256c6a-68bc-4786-b41d-fcdfea17685f[bb256c6a-68bc-4786-b41d-fcdfea17685f] monitor closed 
[INFO ] 2024-03-29 16:01:38.069 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] close complete, cost 9 ms 
[INFO ] 2024-03-29 16:01:38.069 - [suppliers_import_import_import_import(100)][bb256c6a-68bc-4786-b41d-fcdfea17685f] - Node bb256c6a-68bc-4786-b41d-fcdfea17685f[bb256c6a-68bc-4786-b41d-fcdfea17685f] close complete, cost 6 ms 
[INFO ] 2024-03-29 16:01:38.278 - [suppliers_import_import_import_import(100)] - load tapTable task 6606754818990e56b526fedd-bb256c6a-68bc-4786-b41d-fcdfea17685f complete, cost 2655ms 
[INFO ] 2024-03-29 16:01:41.261 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:41.261 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:41.262 - [suppliers_import_import_import_import(100)][ff1ced20-2fdd-4dab-80ff-a57927719446] - Node ff1ced20-2fdd-4dab-80ff-a57927719446[ff1ced20-2fdd-4dab-80ff-a57927719446] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:01:41.262 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:41.262 - [suppliers_import_import_import_import(100)][ff1ced20-2fdd-4dab-80ff-a57927719446] - Node ff1ced20-2fdd-4dab-80ff-a57927719446[ff1ced20-2fdd-4dab-80ff-a57927719446] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:41.307 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:41.308 - [suppliers_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:01:41.481 - [suppliers_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3fcb9c62 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3fcb9c62 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3fcb9c62 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:01:41.494 - [suppliers_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:01:41.500 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] running status set to false 
[INFO ] 2024-03-29 16:01:41.505 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:41.506 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:41.506 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] schema data cleaned 
[INFO ] 2024-03-29 16:01:41.507 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] monitor closed 
[INFO ] 2024-03-29 16:01:41.507 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] close complete, cost 22 ms 
[INFO ] 2024-03-29 16:01:42.116 - [suppliers_import_import_import_import(100)][37fb2e73-44d4-4c91-a6e5-a5ad30ee9211] - Node 37fb2e73-44d4-4c91-a6e5-a5ad30ee9211[37fb2e73-44d4-4c91-a6e5-a5ad30ee9211] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:01:42.116 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:42.116 - [suppliers_import_import_import_import(100)][37fb2e73-44d4-4c91-a6e5-a5ad30ee9211] - Node 37fb2e73-44d4-4c91-a6e5-a5ad30ee9211[37fb2e73-44d4-4c91-a6e5-a5ad30ee9211] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 16:01:42.116 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:42.116 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:42.116 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:42.181 - [suppliers_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:01:42.369 - [suppliers_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1f9b9b2d error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1f9b9b2d error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1f9b9b2d error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:01:42.380 - [suppliers_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:01:42.381 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] running status set to false 
[INFO ] 2024-03-29 16:01:42.384 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:42.384 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:42.386 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] schema data cleaned 
[INFO ] 2024-03-29 16:01:42.386 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] monitor closed 
[INFO ] 2024-03-29 16:01:42.587 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] close complete, cost 17 ms 
[INFO ] 2024-03-29 16:01:43.872 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] running status set to false 
[INFO ] 2024-03-29 16:01:43.875 - [suppliers_import_import_import_import(100)][ff1ced20-2fdd-4dab-80ff-a57927719446] - Node ff1ced20-2fdd-4dab-80ff-a57927719446[ff1ced20-2fdd-4dab-80ff-a57927719446] running status set to false 
[INFO ] 2024-03-29 16:01:43.875 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] schema data cleaned 
[INFO ] 2024-03-29 16:01:43.876 - [suppliers_import_import_import_import(100)][ff1ced20-2fdd-4dab-80ff-a57927719446] - Node ff1ced20-2fdd-4dab-80ff-a57927719446[ff1ced20-2fdd-4dab-80ff-a57927719446] schema data cleaned 
[INFO ] 2024-03-29 16:01:43.876 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] monitor closed 
[INFO ] 2024-03-29 16:01:43.877 - [suppliers_import_import_import_import(100)][ff1ced20-2fdd-4dab-80ff-a57927719446] - Node ff1ced20-2fdd-4dab-80ff-a57927719446[ff1ced20-2fdd-4dab-80ff-a57927719446] monitor closed 
[INFO ] 2024-03-29 16:01:43.878 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] close complete, cost 13 ms 
[INFO ] 2024-03-29 16:01:43.878 - [suppliers_import_import_import_import(100)][ff1ced20-2fdd-4dab-80ff-a57927719446] - Node ff1ced20-2fdd-4dab-80ff-a57927719446[ff1ced20-2fdd-4dab-80ff-a57927719446] close complete, cost 13 ms 
[INFO ] 2024-03-29 16:01:44.084 - [suppliers_import_import_import_import(100)] - load tapTable task 6606754818990e56b526fedd-ff1ced20-2fdd-4dab-80ff-a57927719446 complete, cost 2671ms 
[INFO ] 2024-03-29 16:01:44.764 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] running status set to false 
[INFO ] 2024-03-29 16:01:44.764 - [suppliers_import_import_import_import(100)][37fb2e73-44d4-4c91-a6e5-a5ad30ee9211] - Node 37fb2e73-44d4-4c91-a6e5-a5ad30ee9211[37fb2e73-44d4-4c91-a6e5-a5ad30ee9211] running status set to false 
[INFO ] 2024-03-29 16:01:44.765 - [suppliers_import_import_import_import(100)][37fb2e73-44d4-4c91-a6e5-a5ad30ee9211] - Node 37fb2e73-44d4-4c91-a6e5-a5ad30ee9211[37fb2e73-44d4-4c91-a6e5-a5ad30ee9211] schema data cleaned 
[INFO ] 2024-03-29 16:01:44.766 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] schema data cleaned 
[INFO ] 2024-03-29 16:01:44.766 - [suppliers_import_import_import_import(100)][37fb2e73-44d4-4c91-a6e5-a5ad30ee9211] - Node 37fb2e73-44d4-4c91-a6e5-a5ad30ee9211[37fb2e73-44d4-4c91-a6e5-a5ad30ee9211] monitor closed 
[INFO ] 2024-03-29 16:01:44.767 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] monitor closed 
[INFO ] 2024-03-29 16:01:44.767 - [suppliers_import_import_import_import(100)][37fb2e73-44d4-4c91-a6e5-a5ad30ee9211] - Node 37fb2e73-44d4-4c91-a6e5-a5ad30ee9211[37fb2e73-44d4-4c91-a6e5-a5ad30ee9211] close complete, cost 4 ms 
[INFO ] 2024-03-29 16:01:44.773 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] close complete, cost 8 ms 
[INFO ] 2024-03-29 16:01:44.773 - [suppliers_import_import_import_import(100)] - load tapTable task 6606754818990e56b526fedd-37fb2e73-44d4-4c91-a6e5-a5ad30ee9211 complete, cost 2745ms 
[INFO ] 2024-03-29 16:01:49.277 - [suppliers_import_import_import_import(100)][df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb] - Node df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb[df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:01:49.277 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:49.277 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:49.278 - [suppliers_import_import_import_import(100)][df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb] - Node df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb[df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:49.278 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:49.302 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:49.303 - [suppliers_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:01:49.510 - [suppliers_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@46fd8df6 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@46fd8df6 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@46fd8df6 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:01:49.519 - [suppliers_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:01:49.519 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] running status set to false 
[INFO ] 2024-03-29 16:01:49.541 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:49.541 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:49.542 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] schema data cleaned 
[INFO ] 2024-03-29 16:01:49.542 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] monitor closed 
[INFO ] 2024-03-29 16:01:49.744 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] close complete, cost 24 ms 
[INFO ] 2024-03-29 16:01:50.357 - [suppliers_import_import_import_import(100)][704c569f-8fcc-4140-9896-990186c0eab2] - Node 704c569f-8fcc-4140-9896-990186c0eab2[704c569f-8fcc-4140-9896-990186c0eab2] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:01:50.357 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:50.358 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:50.358 - [suppliers_import_import_import_import(100)][704c569f-8fcc-4140-9896-990186c0eab2] - Node 704c569f-8fcc-4140-9896-990186c0eab2[704c569f-8fcc-4140-9896-990186c0eab2] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:50.359 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:50.359 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:50.465 - [suppliers_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:01:50.465 - [suppliers_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@d0772e8 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@d0772e8 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@d0772e8 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 16:01:50.468 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:50.468 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:50.469 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:50.473 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:50.474 - [suppliers_import_import_import_import(100)][2c2a4880-e47a-4bad-8753-a4af26e22f3d] - Node 2c2a4880-e47a-4bad-8753-a4af26e22f3d[2c2a4880-e47a-4bad-8753-a4af26e22f3d] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:01:50.475 - [suppliers_import_import_import_import(100)][2c2a4880-e47a-4bad-8753-a4af26e22f3d] - Node 2c2a4880-e47a-4bad-8753-a4af26e22f3d[2c2a4880-e47a-4bad-8753-a4af26e22f3d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:50.504 - [suppliers_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:01:50.505 - [suppliers_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6ccbf785 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6ccbf785 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6ccbf785 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:01:50.626 - [suppliers_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:01:50.626 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] running status set to false 
[INFO ] 2024-03-29 16:01:50.634 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:50.634 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:50.635 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] schema data cleaned 
[INFO ] 2024-03-29 16:01:50.635 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] monitor closed 
[INFO ] 2024-03-29 16:01:50.765 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] close complete, cost 12 ms 
[WARN ] 2024-03-29 16:01:50.765 - [suppliers_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:01:50.773 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] running status set to false 
[INFO ] 2024-03-29 16:01:50.773 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:50.774 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:50.774 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] schema data cleaned 
[INFO ] 2024-03-29 16:01:50.776 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] monitor closed 
[INFO ] 2024-03-29 16:01:50.776 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] close complete, cost 6 ms 
[INFO ] 2024-03-29 16:01:51.493 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:51.493 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:51.494 - [suppliers_import_import_import_import(100)][b532839c-c788-4860-a435-2b9fc35e63aa] - Node b532839c-c788-4860-a435-2b9fc35e63aa[b532839c-c788-4860-a435-2b9fc35e63aa] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:01:51.494 - [suppliers_import_import_import_import(100)][b532839c-c788-4860-a435-2b9fc35e63aa] - Node b532839c-c788-4860-a435-2b9fc35e63aa[b532839c-c788-4860-a435-2b9fc35e63aa] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:51.494 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:51.495 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] preload schema finished, cost 2 ms 
[INFO ] 2024-03-29 16:01:51.565 - [suppliers_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:01:51.747 - [suppliers_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@696ddba error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@696ddba error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@696ddba error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:01:51.747 - [suppliers_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:01:51.761 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] running status set to false 
[INFO ] 2024-03-29 16:01:51.761 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:51.762 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:51.762 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] schema data cleaned 
[INFO ] 2024-03-29 16:01:51.762 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] monitor closed 
[INFO ] 2024-03-29 16:01:51.762 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] close complete, cost 12 ms 
[INFO ] 2024-03-29 16:01:51.849 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] running status set to false 
[INFO ] 2024-03-29 16:01:51.849 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] schema data cleaned 
[INFO ] 2024-03-29 16:01:51.849 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] monitor closed 
[INFO ] 2024-03-29 16:01:51.849 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] close complete, cost 0 ms 
[INFO ] 2024-03-29 16:01:51.855 - [suppliers_import_import_import_import(100)][df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb] - Node df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb[df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb] running status set to false 
[INFO ] 2024-03-29 16:01:51.855 - [suppliers_import_import_import_import(100)][df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb] - Node df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb[df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb] schema data cleaned 
[INFO ] 2024-03-29 16:01:51.855 - [suppliers_import_import_import_import(100)][df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb] - Node df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb[df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb] monitor closed 
[INFO ] 2024-03-29 16:01:51.855 - [suppliers_import_import_import_import(100)][df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb] - Node df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb[df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb] close complete, cost 1 ms 
[INFO ] 2024-03-29 16:01:52.061 - [suppliers_import_import_import_import(100)] - load tapTable task 6606754818990e56b526fedd-df8b3b14-9587-4eeb-b9b0-1c715bbbfbdb complete, cost 2611ms 
[INFO ] 2024-03-29 16:01:52.989 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] running status set to false 
[INFO ] 2024-03-29 16:01:52.990 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] schema data cleaned 
[INFO ] 2024-03-29 16:01:52.990 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] monitor closed 
[INFO ] 2024-03-29 16:01:52.998 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] close complete, cost 1 ms 
[INFO ] 2024-03-29 16:01:52.998 - [suppliers_import_import_import_import(100)][704c569f-8fcc-4140-9896-990186c0eab2] - Node 704c569f-8fcc-4140-9896-990186c0eab2[704c569f-8fcc-4140-9896-990186c0eab2] running status set to false 
[INFO ] 2024-03-29 16:01:52.998 - [suppliers_import_import_import_import(100)][704c569f-8fcc-4140-9896-990186c0eab2] - Node 704c569f-8fcc-4140-9896-990186c0eab2[704c569f-8fcc-4140-9896-990186c0eab2] schema data cleaned 
[INFO ] 2024-03-29 16:01:52.998 - [suppliers_import_import_import_import(100)][704c569f-8fcc-4140-9896-990186c0eab2] - Node 704c569f-8fcc-4140-9896-990186c0eab2[704c569f-8fcc-4140-9896-990186c0eab2] monitor closed 
[INFO ] 2024-03-29 16:01:52.998 - [suppliers_import_import_import_import(100)][704c569f-8fcc-4140-9896-990186c0eab2] - Node 704c569f-8fcc-4140-9896-990186c0eab2[704c569f-8fcc-4140-9896-990186c0eab2] close complete, cost 3 ms 
[INFO ] 2024-03-29 16:01:52.999 - [suppliers_import_import_import_import(100)] - load tapTable task 6606754818990e56b526fedd-704c569f-8fcc-4140-9896-990186c0eab2 complete, cost 2691ms 
[INFO ] 2024-03-29 16:01:53.032 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] running status set to false 
[INFO ] 2024-03-29 16:01:53.032 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] schema data cleaned 
[INFO ] 2024-03-29 16:01:53.032 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] monitor closed 
[INFO ] 2024-03-29 16:01:53.032 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] close complete, cost 0 ms 
[INFO ] 2024-03-29 16:01:53.037 - [suppliers_import_import_import_import(100)][2c2a4880-e47a-4bad-8753-a4af26e22f3d] - Node 2c2a4880-e47a-4bad-8753-a4af26e22f3d[2c2a4880-e47a-4bad-8753-a4af26e22f3d] running status set to false 
[INFO ] 2024-03-29 16:01:53.037 - [suppliers_import_import_import_import(100)][2c2a4880-e47a-4bad-8753-a4af26e22f3d] - Node 2c2a4880-e47a-4bad-8753-a4af26e22f3d[2c2a4880-e47a-4bad-8753-a4af26e22f3d] schema data cleaned 
[INFO ] 2024-03-29 16:01:53.037 - [suppliers_import_import_import_import(100)][2c2a4880-e47a-4bad-8753-a4af26e22f3d] - Node 2c2a4880-e47a-4bad-8753-a4af26e22f3d[2c2a4880-e47a-4bad-8753-a4af26e22f3d] monitor closed 
[INFO ] 2024-03-29 16:01:53.038 - [suppliers_import_import_import_import(100)][2c2a4880-e47a-4bad-8753-a4af26e22f3d] - Node 2c2a4880-e47a-4bad-8753-a4af26e22f3d[2c2a4880-e47a-4bad-8753-a4af26e22f3d] close complete, cost 1 ms 
[INFO ] 2024-03-29 16:01:53.242 - [suppliers_import_import_import_import(100)] - load tapTable task 6606754818990e56b526fedd-2c2a4880-e47a-4bad-8753-a4af26e22f3d complete, cost 2604ms 
[INFO ] 2024-03-29 16:01:54.128 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] running status set to false 
[INFO ] 2024-03-29 16:01:54.129 - [suppliers_import_import_import_import(100)][b532839c-c788-4860-a435-2b9fc35e63aa] - Node b532839c-c788-4860-a435-2b9fc35e63aa[b532839c-c788-4860-a435-2b9fc35e63aa] running status set to false 
[INFO ] 2024-03-29 16:01:54.130 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] schema data cleaned 
[INFO ] 2024-03-29 16:01:54.130 - [suppliers_import_import_import_import(100)][b532839c-c788-4860-a435-2b9fc35e63aa] - Node b532839c-c788-4860-a435-2b9fc35e63aa[b532839c-c788-4860-a435-2b9fc35e63aa] schema data cleaned 
[INFO ] 2024-03-29 16:01:54.137 - [suppliers_import_import_import_import(100)][b532839c-c788-4860-a435-2b9fc35e63aa] - Node b532839c-c788-4860-a435-2b9fc35e63aa[b532839c-c788-4860-a435-2b9fc35e63aa] monitor closed 
[INFO ] 2024-03-29 16:01:54.150 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] monitor closed 
[INFO ] 2024-03-29 16:01:54.151 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] close complete, cost 10 ms 
[INFO ] 2024-03-29 16:01:54.151 - [suppliers_import_import_import_import(100)][b532839c-c788-4860-a435-2b9fc35e63aa] - Node b532839c-c788-4860-a435-2b9fc35e63aa[b532839c-c788-4860-a435-2b9fc35e63aa] close complete, cost 9 ms 
[INFO ] 2024-03-29 16:01:54.152 - [suppliers_import_import_import_import(100)] - load tapTable task 6606754818990e56b526fedd-b532839c-c788-4860-a435-2b9fc35e63aa complete, cost 2742ms 
[INFO ] 2024-03-29 16:01:55.189 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:55.189 - [suppliers_import_import_import_import(100)][b8402298-2bc0-44b9-8355-bec5dbb59577] - Node b8402298-2bc0-44b9-8355-bec5dbb59577[b8402298-2bc0-44b9-8355-bec5dbb59577] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:01:55.189 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:55.189 - [suppliers_import_import_import_import(100)][b8402298-2bc0-44b9-8355-bec5dbb59577] - Node b8402298-2bc0-44b9-8355-bec5dbb59577[b8402298-2bc0-44b9-8355-bec5dbb59577] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:55.189 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:55.190 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:55.231 - [suppliers_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:01:55.231 - [suppliers_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2b5a6c46 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2b5a6c46 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2b5a6c46 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:01:55.400 - [suppliers_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:01:55.400 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] running status set to false 
[INFO ] 2024-03-29 16:01:55.412 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:55.414 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:55.414 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] schema data cleaned 
[INFO ] 2024-03-29 16:01:55.414 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] monitor closed 
[INFO ] 2024-03-29 16:01:55.420 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] close complete, cost 16 ms 
[INFO ] 2024-03-29 16:01:56.564 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:56.564 - [suppliers_import_import_import_import(100)][3285a910-a668-4c83-ab9e-7e8bc4e56ba7] - Node 3285a910-a668-4c83-ab9e-7e8bc4e56ba7[3285a910-a668-4c83-ab9e-7e8bc4e56ba7] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:01:56.565 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:56.565 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:56.565 - [suppliers_import_import_import_import(100)][3285a910-a668-4c83-ab9e-7e8bc4e56ba7] - Node 3285a910-a668-4c83-ab9e-7e8bc4e56ba7[3285a910-a668-4c83-ab9e-7e8bc4e56ba7] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 16:01:56.566 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 16:01:56.643 - [suppliers_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:01:56.669 - [suppliers_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@426ffec5 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@426ffec5 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@426ffec5 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 16:01:56.669 - [suppliers_import_import_import_import(100)][d6579c88-fd4f-47bb-b446-a5b1af073294] - Node d6579c88-fd4f-47bb-b446-a5b1af073294[d6579c88-fd4f-47bb-b446-a5b1af073294] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:01:56.669 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:56.669 - [suppliers_import_import_import_import(100)][d6579c88-fd4f-47bb-b446-a5b1af073294] - Node d6579c88-fd4f-47bb-b446-a5b1af073294[d6579c88-fd4f-47bb-b446-a5b1af073294] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:56.669 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:56.669 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:56.679 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:56.679 - [suppliers_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:01:56.809 - [suppliers_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@15b9191b error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@15b9191b error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@15b9191b error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:01:56.809 - [suppliers_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:01:56.821 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] running status set to false 
[INFO ] 2024-03-29 16:01:56.821 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:56.822 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:56.822 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] schema data cleaned 
[INFO ] 2024-03-29 16:01:56.823 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] monitor closed 
[INFO ] 2024-03-29 16:01:56.823 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] close complete, cost 12 ms 
[WARN ] 2024-03-29 16:01:56.971 - [suppliers_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:01:56.977 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] running status set to false 
[INFO ] 2024-03-29 16:01:56.984 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:56.985 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:56.986 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] schema data cleaned 
[INFO ] 2024-03-29 16:01:56.986 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] monitor closed 
[INFO ] 2024-03-29 16:01:57.187 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] close complete, cost 17 ms 
[INFO ] 2024-03-29 16:01:57.772 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] running status set to false 
[INFO ] 2024-03-29 16:01:57.778 - [suppliers_import_import_import_import(100)][b8402298-2bc0-44b9-8355-bec5dbb59577] - Node b8402298-2bc0-44b9-8355-bec5dbb59577[b8402298-2bc0-44b9-8355-bec5dbb59577] running status set to false 
[INFO ] 2024-03-29 16:01:57.778 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] schema data cleaned 
[INFO ] 2024-03-29 16:01:57.778 - [suppliers_import_import_import_import(100)][b8402298-2bc0-44b9-8355-bec5dbb59577] - Node b8402298-2bc0-44b9-8355-bec5dbb59577[b8402298-2bc0-44b9-8355-bec5dbb59577] schema data cleaned 
[INFO ] 2024-03-29 16:01:57.778 - [suppliers_import_import_import_import(100)][b8402298-2bc0-44b9-8355-bec5dbb59577] - Node b8402298-2bc0-44b9-8355-bec5dbb59577[b8402298-2bc0-44b9-8355-bec5dbb59577] monitor closed 
[INFO ] 2024-03-29 16:01:57.779 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] monitor closed 
[INFO ] 2024-03-29 16:01:57.783 - [suppliers_import_import_import_import(100)][b8402298-2bc0-44b9-8355-bec5dbb59577] - Node b8402298-2bc0-44b9-8355-bec5dbb59577[b8402298-2bc0-44b9-8355-bec5dbb59577] close complete, cost 11 ms 
[INFO ] 2024-03-29 16:01:57.783 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] close complete, cost 15 ms 
[INFO ] 2024-03-29 16:01:57.991 - [suppliers_import_import_import_import(100)] - load tapTable task 6606754818990e56b526fedd-b8402298-2bc0-44b9-8355-bec5dbb59577 complete, cost 2640ms 
[INFO ] 2024-03-29 16:01:58.139 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:58.139 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:01:58.140 - [suppliers_import_import_import_import(100)][559b1981-1e77-4ff0-8991-a6c15a2472f5] - Node 559b1981-1e77-4ff0-8991-a6c15a2472f5[559b1981-1e77-4ff0-8991-a6c15a2472f5] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:01:58.140 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:58.145 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:58.145 - [suppliers_import_import_import_import(100)][559b1981-1e77-4ff0-8991-a6c15a2472f5] - Node 559b1981-1e77-4ff0-8991-a6c15a2472f5[559b1981-1e77-4ff0-8991-a6c15a2472f5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:01:58.207 - [suppliers_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:01:58.388 - [suppliers_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2051ecfe error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2051ecfe error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2051ecfe error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:01:58.392 - [suppliers_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:01:58.401 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] running status set to false 
[INFO ] 2024-03-29 16:01:58.401 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:58.401 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:01:58.401 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] schema data cleaned 
[INFO ] 2024-03-29 16:01:58.402 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] monitor closed 
[INFO ] 2024-03-29 16:01:58.402 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] close complete, cost 11 ms 
[INFO ] 2024-03-29 16:01:59.207 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] running status set to false 
[INFO ] 2024-03-29 16:01:59.207 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] schema data cleaned 
[INFO ] 2024-03-29 16:01:59.207 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] monitor closed 
[INFO ] 2024-03-29 16:01:59.209 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] close complete, cost 1 ms 
[INFO ] 2024-03-29 16:01:59.209 - [suppliers_import_import_import_import(100)][3285a910-a668-4c83-ab9e-7e8bc4e56ba7] - Node 3285a910-a668-4c83-ab9e-7e8bc4e56ba7[3285a910-a668-4c83-ab9e-7e8bc4e56ba7] running status set to false 
[INFO ] 2024-03-29 16:01:59.209 - [suppliers_import_import_import_import(100)][3285a910-a668-4c83-ab9e-7e8bc4e56ba7] - Node 3285a910-a668-4c83-ab9e-7e8bc4e56ba7[3285a910-a668-4c83-ab9e-7e8bc4e56ba7] schema data cleaned 
[INFO ] 2024-03-29 16:01:59.209 - [suppliers_import_import_import_import(100)][3285a910-a668-4c83-ab9e-7e8bc4e56ba7] - Node 3285a910-a668-4c83-ab9e-7e8bc4e56ba7[3285a910-a668-4c83-ab9e-7e8bc4e56ba7] monitor closed 
[INFO ] 2024-03-29 16:01:59.211 - [suppliers_import_import_import_import(100)][3285a910-a668-4c83-ab9e-7e8bc4e56ba7] - Node 3285a910-a668-4c83-ab9e-7e8bc4e56ba7[3285a910-a668-4c83-ab9e-7e8bc4e56ba7] close complete, cost 0 ms 
[INFO ] 2024-03-29 16:01:59.211 - [suppliers_import_import_import_import(100)] - load tapTable task 6606754818990e56b526fedd-3285a910-a668-4c83-ab9e-7e8bc4e56ba7 complete, cost 2746ms 
[INFO ] 2024-03-29 16:01:59.228 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] running status set to false 
[INFO ] 2024-03-29 16:01:59.228 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] schema data cleaned 
[INFO ] 2024-03-29 16:01:59.228 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] monitor closed 
[INFO ] 2024-03-29 16:01:59.228 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] close complete, cost 0 ms 
[INFO ] 2024-03-29 16:01:59.230 - [suppliers_import_import_import_import(100)][d6579c88-fd4f-47bb-b446-a5b1af073294] - Node d6579c88-fd4f-47bb-b446-a5b1af073294[d6579c88-fd4f-47bb-b446-a5b1af073294] running status set to false 
[INFO ] 2024-03-29 16:01:59.230 - [suppliers_import_import_import_import(100)][d6579c88-fd4f-47bb-b446-a5b1af073294] - Node d6579c88-fd4f-47bb-b446-a5b1af073294[d6579c88-fd4f-47bb-b446-a5b1af073294] schema data cleaned 
[INFO ] 2024-03-29 16:01:59.230 - [suppliers_import_import_import_import(100)][d6579c88-fd4f-47bb-b446-a5b1af073294] - Node d6579c88-fd4f-47bb-b446-a5b1af073294[d6579c88-fd4f-47bb-b446-a5b1af073294] monitor closed 
[INFO ] 2024-03-29 16:01:59.231 - [suppliers_import_import_import_import(100)][d6579c88-fd4f-47bb-b446-a5b1af073294] - Node d6579c88-fd4f-47bb-b446-a5b1af073294[d6579c88-fd4f-47bb-b446-a5b1af073294] close complete, cost 0 ms 
[INFO ] 2024-03-29 16:01:59.436 - [suppliers_import_import_import_import(100)] - load tapTable task 6606754818990e56b526fedd-d6579c88-fd4f-47bb-b446-a5b1af073294 complete, cost 2594ms 
[INFO ] 2024-03-29 16:02:00.800 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] running status set to false 
[INFO ] 2024-03-29 16:02:00.800 - [suppliers_import_import_import_import(100)][559b1981-1e77-4ff0-8991-a6c15a2472f5] - Node 559b1981-1e77-4ff0-8991-a6c15a2472f5[559b1981-1e77-4ff0-8991-a6c15a2472f5] running status set to false 
[INFO ] 2024-03-29 16:02:00.802 - [suppliers_import_import_import_import(100)][559b1981-1e77-4ff0-8991-a6c15a2472f5] - Node 559b1981-1e77-4ff0-8991-a6c15a2472f5[559b1981-1e77-4ff0-8991-a6c15a2472f5] schema data cleaned 
[INFO ] 2024-03-29 16:02:00.802 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] schema data cleaned 
[INFO ] 2024-03-29 16:02:00.803 - [suppliers_import_import_import_import(100)][559b1981-1e77-4ff0-8991-a6c15a2472f5] - Node 559b1981-1e77-4ff0-8991-a6c15a2472f5[559b1981-1e77-4ff0-8991-a6c15a2472f5] monitor closed 
[INFO ] 2024-03-29 16:02:00.803 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] monitor closed 
[INFO ] 2024-03-29 16:02:00.804 - [suppliers_import_import_import_import(100)][559b1981-1e77-4ff0-8991-a6c15a2472f5] - Node 559b1981-1e77-4ff0-8991-a6c15a2472f5[559b1981-1e77-4ff0-8991-a6c15a2472f5] close complete, cost 50 ms 
[INFO ] 2024-03-29 16:02:00.804 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] close complete, cost 51 ms 
[INFO ] 2024-03-29 16:02:01.010 - [suppliers_import_import_import_import(100)] - load tapTable task 6606754818990e56b526fedd-559b1981-1e77-4ff0-8991-a6c15a2472f5 complete, cost 2733ms 
[INFO ] 2024-03-29 16:02:03.175 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:02:03.175 - [suppliers_import_import_import_import(100)][3068f919-fffb-43b4-bcd3-cd568e9d7f40] - Node 3068f919-fffb-43b4-bcd3-cd568e9d7f40[3068f919-fffb-43b4-bcd3-cd568e9d7f40] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:02:03.175 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:02:03.176 - [suppliers_import_import_import_import(100)][3068f919-fffb-43b4-bcd3-cd568e9d7f40] - Node 3068f919-fffb-43b4-bcd3-cd568e9d7f40[3068f919-fffb-43b4-bcd3-cd568e9d7f40] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:02:03.176 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:02:03.176 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:02:03.236 - [suppliers_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:02:03.239 - [suppliers_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@62d6f4e error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@62d6f4e error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@62d6f4e error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 16:02:03.324 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:02:03.324 - [suppliers_import_import_import_import(100)][054c394b-df54-4cc7-9da3-2a9b67452def] - Node 054c394b-df54-4cc7-9da3-2a9b67452def[054c394b-df54-4cc7-9da3-2a9b67452def] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:02:03.324 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:02:03.324 - [suppliers_import_import_import_import(100)][054c394b-df54-4cc7-9da3-2a9b67452def] - Node 054c394b-df54-4cc7-9da3-2a9b67452def[054c394b-df54-4cc7-9da3-2a9b67452def] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:02:03.324 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:02:03.324 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:02:03.366 - [suppliers_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:02:03.414 - [suppliers_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4e6ccaf6 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4e6ccaf6 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4e6ccaf6 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:02:03.418 - [suppliers_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:02:03.418 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] running status set to false 
[INFO ] 2024-03-29 16:02:03.430 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:02:03.431 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:02:03.431 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] schema data cleaned 
[INFO ] 2024-03-29 16:02:03.431 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] monitor closed 
[INFO ] 2024-03-29 16:02:03.431 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] close complete, cost 14 ms 
[WARN ] 2024-03-29 16:02:03.571 - [suppliers_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:02:03.571 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] running status set to false 
[INFO ] 2024-03-29 16:02:03.576 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:02:03.576 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:02:03.576 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] schema data cleaned 
[INFO ] 2024-03-29 16:02:03.577 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] monitor closed 
[INFO ] 2024-03-29 16:02:03.782 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] close complete, cost 6 ms 
[INFO ] 2024-03-29 16:02:05.787 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] running status set to false 
[INFO ] 2024-03-29 16:02:05.788 - [suppliers_import_import_import_import(100)][3068f919-fffb-43b4-bcd3-cd568e9d7f40] - Node 3068f919-fffb-43b4-bcd3-cd568e9d7f40[3068f919-fffb-43b4-bcd3-cd568e9d7f40] running status set to false 
[INFO ] 2024-03-29 16:02:05.788 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] schema data cleaned 
[INFO ] 2024-03-29 16:02:05.788 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] monitor closed 
[INFO ] 2024-03-29 16:02:05.788 - [suppliers_import_import_import_import(100)][3068f919-fffb-43b4-bcd3-cd568e9d7f40] - Node 3068f919-fffb-43b4-bcd3-cd568e9d7f40[3068f919-fffb-43b4-bcd3-cd568e9d7f40] schema data cleaned 
[INFO ] 2024-03-29 16:02:05.788 - [suppliers_import_import_import_import(100)][3068f919-fffb-43b4-bcd3-cd568e9d7f40] - Node 3068f919-fffb-43b4-bcd3-cd568e9d7f40[3068f919-fffb-43b4-bcd3-cd568e9d7f40] monitor closed 
[INFO ] 2024-03-29 16:02:05.788 - [suppliers_import_import_import_import(100)][3068f919-fffb-43b4-bcd3-cd568e9d7f40] - Node 3068f919-fffb-43b4-bcd3-cd568e9d7f40[3068f919-fffb-43b4-bcd3-cd568e9d7f40] close complete, cost 2 ms 
[INFO ] 2024-03-29 16:02:05.788 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] close complete, cost 6 ms 
[INFO ] 2024-03-29 16:02:05.934 - [suppliers_import_import_import_import(100)] - load tapTable task 6606754818990e56b526fedd-3068f919-fffb-43b4-bcd3-cd568e9d7f40 complete, cost 2656ms 
[INFO ] 2024-03-29 16:02:05.935 - [suppliers_import_import_import_import(100)][054c394b-df54-4cc7-9da3-2a9b67452def] - Node 054c394b-df54-4cc7-9da3-2a9b67452def[054c394b-df54-4cc7-9da3-2a9b67452def] running status set to false 
[INFO ] 2024-03-29 16:02:05.935 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] running status set to false 
[INFO ] 2024-03-29 16:02:05.935 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] schema data cleaned 
[INFO ] 2024-03-29 16:02:05.935 - [suppliers_import_import_import_import(100)][054c394b-df54-4cc7-9da3-2a9b67452def] - Node 054c394b-df54-4cc7-9da3-2a9b67452def[054c394b-df54-4cc7-9da3-2a9b67452def] schema data cleaned 
[INFO ] 2024-03-29 16:02:05.935 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] monitor closed 
[INFO ] 2024-03-29 16:02:05.935 - [suppliers_import_import_import_import(100)][054c394b-df54-4cc7-9da3-2a9b67452def] - Node 054c394b-df54-4cc7-9da3-2a9b67452def[054c394b-df54-4cc7-9da3-2a9b67452def] monitor closed 
[INFO ] 2024-03-29 16:02:05.936 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] close complete, cost 12 ms 
[INFO ] 2024-03-29 16:02:05.936 - [suppliers_import_import_import_import(100)][054c394b-df54-4cc7-9da3-2a9b67452def] - Node 054c394b-df54-4cc7-9da3-2a9b67452def[054c394b-df54-4cc7-9da3-2a9b67452def] close complete, cost 8 ms 
[INFO ] 2024-03-29 16:02:05.939 - [suppliers_import_import_import_import(100)] - load tapTable task 6606754818990e56b526fedd-054c394b-df54-4cc7-9da3-2a9b67452def complete, cost 2678ms 
[INFO ] 2024-03-29 16:02:08.842 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:02:08.842 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:02:08.842 - [suppliers_import_import_import_import(100)][e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25] - Node e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25[e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:02:08.842 - [suppliers_import_import_import_import(100)][e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25] - Node e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25[e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:02:08.842 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:02:08.843 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:02:08.904 - [suppliers_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:02:08.950 - [suppliers_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1fba5cb6 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1fba5cb6 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1fba5cb6 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 16:02:08.951 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:02:08.951 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:02:08.951 - [suppliers_import_import_import_import(100)][87806eca-4d91-427a-b2bb-1ab39f5a7074] - Node 87806eca-4d91-427a-b2bb-1ab39f5a7074[87806eca-4d91-427a-b2bb-1ab39f5a7074] start preload schema,table counts: 0 
[INFO ] 2024-03-29 16:02:08.951 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:02:08.951 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:02:08.951 - [suppliers_import_import_import_import(100)][87806eca-4d91-427a-b2bb-1ab39f5a7074] - Node 87806eca-4d91-427a-b2bb-1ab39f5a7074[87806eca-4d91-427a-b2bb-1ab39f5a7074] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 16:02:08.962 - [suppliers_import_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 16:02:09.087 - [suppliers_import_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@55749f35 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@55749f35 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@55749f35 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-29 16:02:09.091 - [suppliers_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:02:09.091 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] running status set to false 
[INFO ] 2024-03-29 16:02:09.101 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:02:09.101 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:02:09.101 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] schema data cleaned 
[INFO ] 2024-03-29 16:02:09.101 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] monitor closed 
[INFO ] 2024-03-29 16:02:09.102 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] close complete, cost 11 ms 
[WARN ] 2024-03-29 16:02:09.237 - [suppliers_import_import_import_import(100)][Shippers] - Source table is empty, trying to mock data 
[INFO ] 2024-03-29 16:02:09.237 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] running status set to false 
[INFO ] 2024-03-29 16:02:09.249 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:02:09.249 - [suppliers_import_import_import_import(100)][Shippers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-71885ed1-d35e-4cf1-a818-a4fb67d61682 
[INFO ] 2024-03-29 16:02:09.249 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] schema data cleaned 
[INFO ] 2024-03-29 16:02:09.249 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] monitor closed 
[INFO ] 2024-03-29 16:02:09.455 - [suppliers_import_import_import_import(100)][Shippers] - Node Shippers[71885ed1-d35e-4cf1-a818-a4fb67d61682] close complete, cost 13 ms 
[INFO ] 2024-03-29 16:02:11.521 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] running status set to false 
[INFO ] 2024-03-29 16:02:11.523 - [suppliers_import_import_import_import(100)][e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25] - Node e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25[e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25] running status set to false 
[INFO ] 2024-03-29 16:02:11.524 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] running status set to false 
[INFO ] 2024-03-29 16:02:11.524 - [suppliers_import_import_import_import(100)][87806eca-4d91-427a-b2bb-1ab39f5a7074] - Node 87806eca-4d91-427a-b2bb-1ab39f5a7074[87806eca-4d91-427a-b2bb-1ab39f5a7074] running status set to false 
[INFO ] 2024-03-29 16:02:11.524 - [suppliers_import_import_import_import(100)][87806eca-4d91-427a-b2bb-1ab39f5a7074] - Node 87806eca-4d91-427a-b2bb-1ab39f5a7074[87806eca-4d91-427a-b2bb-1ab39f5a7074] schema data cleaned 
[INFO ] 2024-03-29 16:02:11.524 - [suppliers_import_import_import_import(100)][e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25] - Node e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25[e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25] schema data cleaned 
[INFO ] 2024-03-29 16:02:11.524 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] schema data cleaned 
[INFO ] 2024-03-29 16:02:11.524 - [suppliers_import_import_import_import(100)][87806eca-4d91-427a-b2bb-1ab39f5a7074] - Node 87806eca-4d91-427a-b2bb-1ab39f5a7074[87806eca-4d91-427a-b2bb-1ab39f5a7074] monitor closed 
[INFO ] 2024-03-29 16:02:11.524 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] monitor closed 
[INFO ] 2024-03-29 16:02:11.524 - [suppliers_import_import_import_import(100)][e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25] - Node e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25[e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25] monitor closed 
[INFO ] 2024-03-29 16:02:11.525 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] schema data cleaned 
[INFO ] 2024-03-29 16:02:11.526 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] close complete, cost 39 ms 
[INFO ] 2024-03-29 16:02:11.526 - [suppliers_import_import_import_import(100)][87806eca-4d91-427a-b2bb-1ab39f5a7074] - Node 87806eca-4d91-427a-b2bb-1ab39f5a7074[87806eca-4d91-427a-b2bb-1ab39f5a7074] close complete, cost 4 ms 
[INFO ] 2024-03-29 16:02:11.526 - [suppliers_import_import_import_import(100)][e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25] - Node e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25[e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25] close complete, cost 44 ms 
[INFO ] 2024-03-29 16:02:11.526 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] monitor closed 
[INFO ] 2024-03-29 16:02:11.532 - [suppliers_import_import_import_import(100)][Suppliers] - Node Suppliers[a644f9ff-517d-480d-9411-0ae775694f48] close complete, cost 20 ms 
[INFO ] 2024-03-29 16:02:11.532 - [suppliers_import_import_import_import(100)] - load tapTable task 6606754818990e56b526fedd-e4d2a2d7-c4b0-4a9a-81fa-9bee19f16e25 complete, cost 2733ms 
[INFO ] 2024-03-29 16:02:11.737 - [suppliers_import_import_import_import(100)] - load tapTable task 6606754818990e56b526fedd-87806eca-4d91-427a-b2bb-1ab39f5a7074 complete, cost 2609ms 
