[TRACE] 2025-02-18 15:58:19.801 - [任务 9] - Task initialization... 
[TRACE] 2025-02-18 15:58:19.802 - [任务 9] - Start task milestones: 67b43d53cc3e3c6b7743cd18(任务 9) 
[INFO ] 2025-02-18 15:58:19.931 - [任务 9] - Loading table structure completed 
[TRACE] 2025-02-18 15:58:19.931 - [任务 9] - <PERSON>de performs snapshot read asynchronously 
[TRACE] 2025-02-18 15:58:19.982 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-18 15:58:19.982 - [任务 9] - Task started 
[TRACE] 2025-02-18 15:58:20.015 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] start preload schema,table counts: 1 
[TRACE] 2025-02-18 15:58:20.015 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] start preload schema,table counts: 1 
[TRACE] 2025-02-18 15:58:20.015 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] start preload schema,table counts: 1 
[TRACE] 2025-02-18 15:58:20.015 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 15:58:20.015 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 15:58:20.015 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] preload schema finished, cost 1 ms 
[TRACE] 2025-02-18 15:58:20.015 - [任务 9][表编辑] - Node table_rename_processor(表编辑: 7a31a691-ba8e-408c-b625-b0f4734361bd) enable batch process 
[INFO ] 2025-02-18 15:58:20.221 - [任务 9][PG] - Enable partition table support for source database 
[INFO ] 2025-02-18 15:58:20.737 - [任务 9][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-02-18 15:58:20.739 - [任务 9][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-18 15:58:20.739 - [任务 9][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-18 15:58:20.739 - [任务 9][Sybase] - Apply table structure to target database 
[TRACE] 2025-02-18 15:58:20.940 - [任务 9][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Table name: pg_w_td_fact_secio.tapdata.entity.event.ddl.table.TapDropTableEvent@3e9560a5: {"tableId":"pg_w_td_fact_sec","time":1739865500739,"type":208}
 
[ERROR] 2025-02-18 15:58:20.985 - [任务 9][Sybase] - Table name: pg_w_td_fact_secio.tapdata.entity.event.ddl.table.TapDropTableEvent@3e9560a5: {"tableId":"pg_w_td_fact_sec","time":1739865500739,"type":208}
 <-- Error Message -->
Table name: pg_w_td_fact_secio.tapdata.entity.event.ddl.table.TapDropTableEvent@3e9560a5: {"tableId":"pg_w_td_fact_sec","time":1739865500739,"type":208}


<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.tapdata.common.CommonDbConnector.dropTable(CommonDbConnector.java:364)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$26(HazelcastTargetPdkDataNode.java:534)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...

<-- Full Stack Trace -->
Table name: pg_w_td_fact_sec
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.dropTable(HazelcastTargetPdkDataNode.java:544)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:224)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$3(HazelcastTargetPdkDataNode.java:174)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:160)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:111)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.NullPointerException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.dropTable(HazelcastTargetPdkDataNode.java:528)
	... 19 more
Caused by: java.lang.NullPointerException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:77)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$dropTable$27(HazelcastTargetPdkDataNode.java:534)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 21 more
Caused by: java.lang.NullPointerException
	at io.tapdata.common.CommonDbConnector.dropTable(CommonDbConnector.java:364)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$26(HazelcastTargetPdkDataNode.java:534)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 28 more

[TRACE] 2025-02-18 15:58:21.121 - [任务 9][Sybase] - Job suspend in error handle 
[INFO ] 2025-02-18 15:58:21.122 - [任务 9][PG] - Source connector(PG) initialization completed 
[TRACE] 2025-02-18 15:58:21.122 - [任务 9][PG] - Source node "PG" read batch size: 100 
[TRACE] 2025-02-18 15:58:21.122 - [任务 9][PG] - Source node "PG" event queue capacity: 200 
[TRACE] 2025-02-18 15:58:21.122 - [任务 9][PG] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-02-18 15:58:21.440 - [任务 9][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-18 15:58:21.440 - [任务 9][PG] - new logical replication slot created, slotName:tapdata_cdc_72448009_24a8_4cb4_ae3e_719a024f156b 
[INFO ] 2025-02-18 15:58:21.463 - [任务 9][PG] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-18 15:58:21.463 - [任务 9][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-18 15:58:21.554 - [任务 9][PG] - Starting batch read from 1 tables 
[TRACE] 2025-02-18 15:58:21.618 - [任务 9][PG] - Initial sync started 
[INFO ] 2025-02-18 15:58:21.623 - [任务 9][PG] - Starting batch read from table: td_fact_sec 
[TRACE] 2025-02-18 15:58:21.623 - [任务 9][PG] - Table td_fact_sec is going to be initial synced 
[TRACE] 2025-02-18 15:58:21.653 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] running status set to false 
[TRACE] 2025-02-18 15:58:21.655 - [任务 9][PG] - Query snapshot row size completed: PG(616acfed-be0a-4001-993e-ed81de7614e1) 
[INFO ] 2025-02-18 15:58:21.656 - [任务 9] - Task [任务 9] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-02-18 15:58:21.672 - [任务 9][PG] - Initial sync completed 
[TRACE] 2025-02-18 15:58:21.678 - [任务 9][PG] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-24) has been closed. 
[TRACE] 2025-02-18 15:58:21.678 - [任务 9][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_616acfed-be0a-4001-993e-ed81de7614e1_1739865500798 
[TRACE] 2025-02-18 15:58:21.678 - [任务 9][PG] - PDK connector node released: HazelcastSourcePdkDataNode_616acfed-be0a-4001-993e-ed81de7614e1_1739865500798 
[TRACE] 2025-02-18 15:58:21.678 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] schema data cleaned 
[TRACE] 2025-02-18 15:58:21.679 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] monitor closed 
[TRACE] 2025-02-18 15:58:21.680 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] close complete, cost 50 ms 
[TRACE] 2025-02-18 15:58:21.681 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] running status set to false 
[TRACE] 2025-02-18 15:58:21.681 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] schema data cleaned 
[TRACE] 2025-02-18 15:58:21.681 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] monitor closed 
[TRACE] 2025-02-18 15:58:21.681 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] close complete, cost 0 ms 
[TRACE] 2025-02-18 15:58:21.681 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] running status set to false 
[ERROR] 2025-02-18 15:58:21.804 - [任务 9][PG] - java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-24) has been closed. <-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-24) has been closed.
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.handleThrowable(HazelcastSourcePdkDataNode.java:445)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:414)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:356)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:269)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-24) has been closed.
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:490)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:412)
	... 10 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-24) has been closed.
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:498)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 13 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-24) has been closed.
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:146)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutHashSplit(CommonDbConnector.java:596)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:590)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:577)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 19 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-24) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 24 more

[TRACE] 2025-02-18 15:58:21.805 - [任务 9][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_4912cc31-36b0-40ce-9f1d-a4b54429c9dc_1739865500632 
[TRACE] 2025-02-18 15:58:21.805 - [任务 9][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_4912cc31-36b0-40ce-9f1d-a4b54429c9dc_1739865500632 
[TRACE] 2025-02-18 15:58:21.805 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] schema data cleaned 
[TRACE] 2025-02-18 15:58:21.805 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] monitor closed 
[TRACE] 2025-02-18 15:58:21.807 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] close complete, cost 126 ms 
[INFO ] 2025-02-18 15:58:26.667 - [任务 9] - Task [任务 9] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-02-18 15:58:26.686 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-18 15:58:26.687 - [任务 9] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@34279036 
[TRACE] 2025-02-18 15:58:26.835 - [任务 9] - Stop task milestones: 67b43d53cc3e3c6b7743cd18(任务 9)  
[TRACE] 2025-02-18 15:58:26.836 - [任务 9] - Stopped task aspect(s) 
[TRACE] 2025-02-18 15:58:26.836 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2025-02-18 15:58:26.836 - [任务 9] - Task stopped. 
[TRACE] 2025-02-18 15:58:26.865 - [任务 9] - Remove memory task client succeed, task: 任务 9[67b43d53cc3e3c6b7743cd18] 
[TRACE] 2025-02-18 15:58:26.865 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[67b43d53cc3e3c6b7743cd18] 
[TRACE] 2025-02-18 15:58:55.603 - [任务 9] - Task initialization... 
[TRACE] 2025-02-18 15:58:55.607 - [任务 9] - Start task milestones: 67b43d53cc3e3c6b7743cd18(任务 9) 
[INFO ] 2025-02-18 15:58:55.738 - [任务 9] - Loading table structure completed 
[TRACE] 2025-02-18 15:58:55.868 - [任务 9] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-18 15:58:55.871 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-18 15:58:55.945 - [任务 9] - Task started 
[TRACE] 2025-02-18 15:58:55.946 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] start preload schema,table counts: 1 
[TRACE] 2025-02-18 15:58:55.946 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] start preload schema,table counts: 1 
[TRACE] 2025-02-18 15:58:55.946 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] start preload schema,table counts: 1 
[TRACE] 2025-02-18 15:58:55.946 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 15:58:55.947 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 15:58:55.947 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] preload schema finished, cost 0 ms 
[INFO ] 2025-02-18 15:58:55.947 - [任务 9][PG] - Enable partition table support for source database 
[TRACE] 2025-02-18 15:58:55.947 - [任务 9][表编辑] - Node table_rename_processor(表编辑: 7a31a691-ba8e-408c-b625-b0f4734361bd) enable batch process 
[INFO ] 2025-02-18 15:58:56.648 - [任务 9][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-02-18 15:58:56.651 - [任务 9][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-18 15:58:56.651 - [任务 9][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-18 15:58:56.651 - [任务 9][Sybase] - Apply table structure to target database 
[INFO ] 2025-02-18 15:58:57.011 - [任务 9][PG] - Source connector(PG) initialization completed 
[TRACE] 2025-02-18 15:58:57.011 - [任务 9][PG] - Source node "PG" read batch size: 100 
[TRACE] 2025-02-18 15:58:57.011 - [任务 9][PG] - Source node "PG" event queue capacity: 200 
[TRACE] 2025-02-18 15:58:57.011 - [任务 9][PG] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-02-18 15:58:57.967 - [任务 9][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-18 15:58:57.971 - [任务 9][PG] - new logical replication slot created, slotName:tapdata_cdc_99636eda_d75d_4cbf_81cc_89033b8574ae 
[INFO ] 2025-02-18 15:58:57.974 - [任务 9][PG] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-18 15:58:57.976 - [任务 9][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-18 15:58:58.089 - [任务 9][PG] - Starting batch read from 1 tables 
[TRACE] 2025-02-18 15:58:58.103 - [任务 9][PG] - Initial sync started 
[INFO ] 2025-02-18 15:58:58.103 - [任务 9][PG] - Starting batch read from table: td_fact_sec 
[TRACE] 2025-02-18 15:58:58.105 - [任务 9][PG] - Table td_fact_sec is going to be initial synced 
[TRACE] 2025-02-18 15:58:58.364 - [任务 9][PG] - Query snapshot row size completed: PG(616acfed-be0a-4001-993e-ed81de7614e1) 
[INFO ] 2025-02-18 15:58:58.377 - [任务 9][PG] - Table td_fact_sec has been completed batch read 
[TRACE] 2025-02-18 15:58:58.378 - [任务 9][PG] - Initial sync completed 
[INFO ] 2025-02-18 15:58:58.378 - [任务 9][PG] - Batch read completed. 
[TRACE] 2025-02-18 15:58:58.378 - [任务 9][PG] - Incremental sync starting... 
[TRACE] 2025-02-18 15:58:58.378 - [任务 9][PG] - Initial sync completed 
[TRACE] 2025-02-18 15:58:58.379 - [任务 9][PG] - Starting stream read, table list: [td_fact_sec], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-02-18 15:58:58.379 - [任务 9][PG] - Starting incremental sync using database log parser 
[WARN ] 2025-02-18 15:58:58.587 - [任务 9][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-18 15:58:58.696 - [任务 9][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_99636eda_d75d_4cbf_81cc_89033b8574ae 
[TRACE] 2025-02-18 15:58:59.704 - [任务 9][PG] - Connector PostgreSQL incremental start succeed, tables: [td_fact_sec], data change syncing 
[TRACE] 2025-02-18 16:08:17.267 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] running status set to false 
[TRACE] 2025-02-18 16:08:17.606 - [任务 9][PG] - Incremental sync completed 
[TRACE] 2025-02-18 16:08:17.619 - [任务 9][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_616acfed-be0a-4001-993e-ed81de7614e1_1739865536691 
[TRACE] 2025-02-18 16:08:17.619 - [任务 9][PG] - PDK connector node released: HazelcastSourcePdkDataNode_616acfed-be0a-4001-993e-ed81de7614e1_1739865536691 
[TRACE] 2025-02-18 16:08:17.620 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] schema data cleaned 
[TRACE] 2025-02-18 16:08:17.620 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] monitor closed 
[TRACE] 2025-02-18 16:08:17.623 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] close complete, cost 437 ms 
[TRACE] 2025-02-18 16:08:17.623 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] running status set to false 
[TRACE] 2025-02-18 16:08:17.623 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] schema data cleaned 
[TRACE] 2025-02-18 16:08:17.623 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] monitor closed 
[TRACE] 2025-02-18 16:08:17.624 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] close complete, cost 1 ms 
[TRACE] 2025-02-18 16:08:17.624 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] running status set to false 
[TRACE] 2025-02-18 16:08:17.746 - [任务 9][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_4912cc31-36b0-40ce-9f1d-a4b54429c9dc_1739865536568 
[TRACE] 2025-02-18 16:08:17.749 - [任务 9][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_4912cc31-36b0-40ce-9f1d-a4b54429c9dc_1739865536568 
[TRACE] 2025-02-18 16:08:17.749 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] schema data cleaned 
[TRACE] 2025-02-18 16:08:17.749 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] monitor closed 
[TRACE] 2025-02-18 16:08:17.959 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] close complete, cost 124 ms 
[TRACE] 2025-02-18 16:08:22.417 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-18 16:08:22.424 - [任务 9] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2c8ebf9 
[TRACE] 2025-02-18 16:08:22.424 - [任务 9] - Stop task milestones: 67b43d53cc3e3c6b7743cd18(任务 9)  
[TRACE] 2025-02-18 16:08:22.558 - [任务 9] - Stopped task aspect(s) 
[TRACE] 2025-02-18 16:08:22.559 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2025-02-18 16:08:22.559 - [任务 9] - Task stopped. 
[TRACE] 2025-02-18 16:08:22.583 - [任务 9] - Remove memory task client succeed, task: 任务 9[67b43d53cc3e3c6b7743cd18] 
[TRACE] 2025-02-18 16:08:22.586 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[67b43d53cc3e3c6b7743cd18] 
[TRACE] 2025-02-18 16:08:48.210 - [任务 9] - Task initialization... 
[TRACE] 2025-02-18 16:08:48.212 - [任务 9] - Start task milestones: 67b43d53cc3e3c6b7743cd18(任务 9) 
[INFO ] 2025-02-18 16:08:48.321 - [任务 9] - Loading table structure completed 
[TRACE] 2025-02-18 16:08:48.321 - [任务 9] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-18 16:08:48.368 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-18 16:08:48.411 - [任务 9] - Task started 
[TRACE] 2025-02-18 16:08:48.411 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] start preload schema,table counts: 1 
[TRACE] 2025-02-18 16:08:48.412 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] start preload schema,table counts: 1 
[TRACE] 2025-02-18 16:08:48.412 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] start preload schema,table counts: 1 
[TRACE] 2025-02-18 16:08:48.412 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 16:08:48.412 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 16:08:48.412 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 16:08:48.412 - [任务 9][表编辑] - Node table_rename_processor(表编辑: 7a31a691-ba8e-408c-b625-b0f4734361bd) enable batch process 
[INFO ] 2025-02-18 16:08:48.412 - [任务 9][PG] - Enable partition table support for source database 
[INFO ] 2025-02-18 16:08:49.243 - [任务 9][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-02-18 16:08:49.244 - [任务 9][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-18 16:08:49.244 - [任务 9][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-18 16:08:49.245 - [任务 9][Sybase] - Apply table structure to target database 
[INFO ] 2025-02-18 16:08:49.269 - [任务 9][PG] - Source connector(PG) initialization completed 
[TRACE] 2025-02-18 16:08:49.269 - [任务 9][PG] - Source node "PG" read batch size: 100 
[TRACE] 2025-02-18 16:08:49.269 - [任务 9][PG] - Source node "PG" event queue capacity: 200 
[TRACE] 2025-02-18 16:08:49.269 - [任务 9][PG] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-02-18 16:08:49.585 - [任务 9][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-18 16:08:49.591 - [任务 9][PG] - new logical replication slot created, slotName:tapdata_cdc_6b8cad5f_4ea6_4d20_a785_620e0f465403 
[INFO ] 2025-02-18 16:08:49.611 - [任务 9][PG] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-18 16:08:49.616 - [任务 9][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-18 16:08:49.690 - [任务 9][PG] - Starting batch read from 1 tables 
[TRACE] 2025-02-18 16:08:49.701 - [任务 9][PG] - Initial sync started 
[INFO ] 2025-02-18 16:08:49.701 - [任务 9][PG] - Starting batch read from table: td_fact_sec 
[TRACE] 2025-02-18 16:08:49.702 - [任务 9][PG] - Table td_fact_sec is going to be initial synced 
[TRACE] 2025-02-18 16:08:49.747 - [任务 9][PG] - Query snapshot row size completed: PG(616acfed-be0a-4001-993e-ed81de7614e1) 
[INFO ] 2025-02-18 16:08:49.747 - [任务 9][PG] - Table td_fact_sec has been completed batch read 
[TRACE] 2025-02-18 16:08:49.747 - [任务 9][PG] - Initial sync completed 
[INFO ] 2025-02-18 16:08:49.747 - [任务 9][PG] - Batch read completed. 
[TRACE] 2025-02-18 16:08:49.747 - [任务 9][PG] - Incremental sync starting... 
[TRACE] 2025-02-18 16:08:49.747 - [任务 9][PG] - Initial sync completed 
[TRACE] 2025-02-18 16:08:49.748 - [任务 9][PG] - Starting stream read, table list: [td_fact_sec], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-02-18 16:08:49.748 - [任务 9][PG] - Starting incremental sync using database log parser 
[WARN ] 2025-02-18 16:08:49.748 - [任务 9][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-18 16:08:49.949 - [任务 9][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_6b8cad5f_4ea6_4d20_a785_620e0f465403 
[TRACE] 2025-02-18 16:08:50.095 - [任务 9][PG] - Connector PostgreSQL incremental start succeed, tables: [td_fact_sec], data change syncing 
[TRACE] 2025-02-18 16:14:37.376 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] running status set to false 
[TRACE] 2025-02-18 16:14:37.935 - [任务 9][PG] - Incremental sync completed 
[TRACE] 2025-02-18 16:14:37.935 - [任务 9][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_616acfed-be0a-4001-993e-ed81de7614e1_1739866128966 
[TRACE] 2025-02-18 16:14:37.935 - [任务 9][PG] - PDK connector node released: HazelcastSourcePdkDataNode_616acfed-be0a-4001-993e-ed81de7614e1_1739866128966 
[TRACE] 2025-02-18 16:14:37.936 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] schema data cleaned 
[TRACE] 2025-02-18 16:14:37.936 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] monitor closed 
[TRACE] 2025-02-18 16:14:37.937 - [任务 9][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] close complete, cost 561 ms 
[TRACE] 2025-02-18 16:14:37.937 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] running status set to false 
[TRACE] 2025-02-18 16:14:37.937 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] schema data cleaned 
[TRACE] 2025-02-18 16:14:37.937 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] monitor closed 
[TRACE] 2025-02-18 16:14:37.938 - [任务 9][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] close complete, cost 0 ms 
[TRACE] 2025-02-18 16:14:37.938 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] running status set to false 
[TRACE] 2025-02-18 16:14:38.044 - [任务 9][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_4912cc31-36b0-40ce-9f1d-a4b54429c9dc_1739866129177 
[TRACE] 2025-02-18 16:14:38.045 - [任务 9][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_4912cc31-36b0-40ce-9f1d-a4b54429c9dc_1739866129177 
[TRACE] 2025-02-18 16:14:38.045 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] schema data cleaned 
[TRACE] 2025-02-18 16:14:38.045 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] monitor closed 
[TRACE] 2025-02-18 16:14:38.246 - [任务 9][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] close complete, cost 107 ms 
[TRACE] 2025-02-18 16:14:42.959 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-18 16:14:42.960 - [任务 9] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@29dc952c 
[TRACE] 2025-02-18 16:14:43.111 - [任务 9] - Stop task milestones: 67b43d53cc3e3c6b7743cd18(任务 9)  
[TRACE] 2025-02-18 16:14:43.111 - [任务 9] - Stopped task aspect(s) 
[TRACE] 2025-02-18 16:14:43.112 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2025-02-18 16:14:43.112 - [任务 9] - Task stopped. 
[TRACE] 2025-02-18 16:14:43.160 - [任务 9] - Remove memory task client succeed, task: 任务 9[67b43d53cc3e3c6b7743cd18] 
[TRACE] 2025-02-18 16:14:43.160 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[67b43d53cc3e3c6b7743cd18] 
[TRACE] 2025-02-18 16:26:05.699 - [验证PG再到sybase] - Task initialization... 
[TRACE] 2025-02-18 16:26:05.700 - [验证PG再到sybase] - Start task milestones: 67b43d53cc3e3c6b7743cd18(验证PG再到sybase) 
[INFO ] 2025-02-18 16:26:05.885 - [验证PG再到sybase] - Loading table structure completed 
[TRACE] 2025-02-18 16:26:05.885 - [验证PG再到sybase] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-18 16:26:05.962 - [验证PG再到sybase] - The engine receives 验证PG再到sybase task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-18 16:26:05.962 - [验证PG再到sybase] - Task started 
[TRACE] 2025-02-18 16:26:05.997 - [验证PG再到sybase][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] start preload schema,table counts: 1 
[TRACE] 2025-02-18 16:26:05.997 - [验证PG再到sybase][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] start preload schema,table counts: 1 
[TRACE] 2025-02-18 16:26:05.997 - [验证PG再到sybase][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] start preload schema,table counts: 1 
[TRACE] 2025-02-18 16:26:05.997 - [验证PG再到sybase][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 16:26:05.998 - [验证PG再到sybase][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 16:26:05.998 - [验证PG再到sybase][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 16:26:05.998 - [验证PG再到sybase][表编辑] - Node table_rename_processor(表编辑: 7a31a691-ba8e-408c-b625-b0f4734361bd) enable batch process 
[INFO ] 2025-02-18 16:26:05.998 - [验证PG再到sybase][PG] - Enable partition table support for source database 
[INFO ] 2025-02-18 16:26:06.689 - [验证PG再到sybase][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-02-18 16:26:06.690 - [验证PG再到sybase][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-18 16:26:06.690 - [验证PG再到sybase][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-18 16:26:06.896 - [验证PG再到sybase][Sybase] - Apply table structure to target database 
[INFO ] 2025-02-18 16:26:07.064 - [验证PG再到sybase][PG] - Source connector(PG) initialization completed 
[TRACE] 2025-02-18 16:26:07.067 - [验证PG再到sybase][PG] - Source node "PG" read batch size: 100 
[TRACE] 2025-02-18 16:26:07.067 - [验证PG再到sybase][PG] - Source node "PG" event queue capacity: 200 
[TRACE] 2025-02-18 16:26:07.067 - [验证PG再到sybase][PG] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-02-18 16:26:07.291 - [验证PG再到sybase][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-18 16:26:07.311 - [验证PG再到sybase][PG] - new logical replication slot created, slotName:tapdata_cdc_1e206dc9_4bb0_4a92_99d1_f6eba122577d 
[INFO ] 2025-02-18 16:26:07.311 - [验证PG再到sybase][PG] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-18 16:26:07.311 - [验证PG再到sybase][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-18 16:26:07.378 - [验证PG再到sybase][PG] - Starting batch read from 1 tables 
[TRACE] 2025-02-18 16:26:07.387 - [验证PG再到sybase][PG] - Initial sync started 
[INFO ] 2025-02-18 16:26:07.388 - [验证PG再到sybase][PG] - Starting batch read from table: td_fact_sec 
[TRACE] 2025-02-18 16:26:07.389 - [验证PG再到sybase][PG] - Table td_fact_sec is going to be initial synced 
[TRACE] 2025-02-18 16:26:07.441 - [验证PG再到sybase][PG] - Query snapshot row size completed: PG(616acfed-be0a-4001-993e-ed81de7614e1) 
[INFO ] 2025-02-18 16:26:07.442 - [验证PG再到sybase][PG] - Table td_fact_sec has been completed batch read 
[TRACE] 2025-02-18 16:26:07.442 - [验证PG再到sybase][PG] - Initial sync completed 
[INFO ] 2025-02-18 16:26:07.442 - [验证PG再到sybase][PG] - Batch read completed. 
[TRACE] 2025-02-18 16:26:07.442 - [验证PG再到sybase][PG] - Incremental sync starting... 
[TRACE] 2025-02-18 16:26:07.443 - [验证PG再到sybase][PG] - Initial sync completed 
[TRACE] 2025-02-18 16:26:07.444 - [验证PG再到sybase][PG] - Starting stream read, table list: [td_fact_sec], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-02-18 16:26:07.444 - [验证PG再到sybase][PG] - Starting incremental sync using database log parser 
[WARN ] 2025-02-18 16:26:07.453 - [验证PG再到sybase][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-18 16:26:07.458 - [验证PG再到sybase][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_1e206dc9_4bb0_4a92_99d1_f6eba122577d 
[TRACE] 2025-02-18 16:26:07.746 - [验证PG再到sybase][PG] - Connector PostgreSQL incremental start succeed, tables: [td_fact_sec], data change syncing 
[TRACE] 2025-02-18 16:34:07.603 - [验证PG再到sybase][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] running status set to false 
[TRACE] 2025-02-18 16:34:08.047 - [验证PG再到sybase][PG] - Incremental sync completed 
[TRACE] 2025-02-18 16:34:08.048 - [验证PG再到sybase][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_616acfed-be0a-4001-993e-ed81de7614e1_1739867166723 
[TRACE] 2025-02-18 16:34:08.048 - [验证PG再到sybase][PG] - PDK connector node released: HazelcastSourcePdkDataNode_616acfed-be0a-4001-993e-ed81de7614e1_1739867166723 
[TRACE] 2025-02-18 16:34:08.048 - [验证PG再到sybase][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] schema data cleaned 
[TRACE] 2025-02-18 16:34:08.048 - [验证PG再到sybase][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] monitor closed 
[TRACE] 2025-02-18 16:34:08.053 - [验证PG再到sybase][PG] - Node PG[616acfed-be0a-4001-993e-ed81de7614e1] close complete, cost 453 ms 
[TRACE] 2025-02-18 16:34:08.053 - [验证PG再到sybase][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] running status set to false 
[TRACE] 2025-02-18 16:34:08.053 - [验证PG再到sybase][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] schema data cleaned 
[TRACE] 2025-02-18 16:34:08.053 - [验证PG再到sybase][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] monitor closed 
[TRACE] 2025-02-18 16:34:08.053 - [验证PG再到sybase][表编辑] - Node 表编辑[7a31a691-ba8e-408c-b625-b0f4734361bd] close complete, cost 2 ms 
[TRACE] 2025-02-18 16:34:08.053 - [验证PG再到sybase][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] running status set to false 
[TRACE] 2025-02-18 16:34:08.185 - [验证PG再到sybase][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_4912cc31-36b0-40ce-9f1d-a4b54429c9dc_1739867166612 
[TRACE] 2025-02-18 16:34:08.185 - [验证PG再到sybase][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_4912cc31-36b0-40ce-9f1d-a4b54429c9dc_1739867166612 
[TRACE] 2025-02-18 16:34:08.185 - [验证PG再到sybase][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] schema data cleaned 
[TRACE] 2025-02-18 16:34:08.186 - [验证PG再到sybase][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] monitor closed 
[TRACE] 2025-02-18 16:34:08.186 - [验证PG再到sybase][Sybase] - Node Sybase[4912cc31-36b0-40ce-9f1d-a4b54429c9dc] close complete, cost 132 ms 
[TRACE] 2025-02-18 16:34:09.165 - [验证PG再到sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-18 16:34:09.169 - [验证PG再到sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2d219418 
[TRACE] 2025-02-18 16:34:09.169 - [验证PG再到sybase] - Stop task milestones: 67b43d53cc3e3c6b7743cd18(验证PG再到sybase)  
[TRACE] 2025-02-18 16:34:09.307 - [验证PG再到sybase] - Stopped task aspect(s) 
[TRACE] 2025-02-18 16:34:09.308 - [验证PG再到sybase] - Snapshot order controller have been removed 
[INFO ] 2025-02-18 16:34:09.308 - [验证PG再到sybase] - Task stopped. 
[TRACE] 2025-02-18 16:34:09.351 - [验证PG再到sybase] - Remove memory task client succeed, task: 验证PG再到sybase[67b43d53cc3e3c6b7743cd18] 
[TRACE] 2025-02-18 16:34:09.351 - [验证PG再到sybase] - Destroy memory task client cache succeed, task: 验证PG再到sybase[67b43d53cc3e3c6b7743cd18] 
