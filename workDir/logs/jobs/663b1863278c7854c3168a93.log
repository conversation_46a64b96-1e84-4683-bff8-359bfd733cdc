[WARN ] 2024-05-09 01:33:33.215 - [任务 3][SourceOralce] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-09 01:55:52.940 - [任务 3][SourceOralce] - Log Miner is shutting down... 
[INFO ] 2024-05-09 01:55:52.940 - [任务 3][SourceOralce] - Log Miner has been closed! 
[INFO ] 2024-05-09 01:55:53.443 - [任务 3][SourceOralce] - Checking whether archived log exists... 
[INFO ] 2024-05-09 01:55:53.654 - [任务 3][SourceOralce] - Redo Log Miner is starting... 
[WARN ] 2024-05-09 04:33:20.067 - [任务 3][SourceOralce] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-09 05:10:37.601 - [任务 3][SourceOralce] - Log Miner is shutting down... 
[INFO ] 2024-05-09 05:10:37.601 - [任务 3][SourceOralce] - Log Miner has been closed! 
[INFO ] 2024-05-09 05:11:06.095 - [任务 3][SourceOralce] - Checking whether archived log exists... 
[INFO ] 2024-05-09 05:11:06.095 - [任务 3][SourceOralce] - Redo Log Miner is starting... 
[WARN ] 2024-05-09 05:13:40.584 - [任务 3][SourceOralce] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-09 05:54:55.790 - [任务 3][SourceOralce] - Log Miner is shutting down... 
[INFO ] 2024-05-09 05:54:55.791 - [任务 3][SourceOralce] - Log Miner has been closed! 
[INFO ] 2024-05-09 05:54:56.338 - [任务 3][SourceOralce] - Checking whether archived log exists... 
[INFO ] 2024-05-09 05:54:56.338 - [任务 3][SourceOralce] - Redo Log Miner is starting... 
[WARN ] 2024-05-09 08:38:33.793 - [任务 3][SourceOralce] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-09 09:00:21.644 - [任务 3][SourceOralce] - Log Miner is shutting down... 
[INFO ] 2024-05-09 09:00:21.644 - [任务 3][SourceOralce] - Log Miner has been closed! 
[WARN ] 2024-05-09 09:18:15.461 - [任务 3][SourceOralce] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): oracle.net.ns.NetException: Got minus one from a read call
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-09 09:58:52.200 - [任务 3][SourceOralce] - Log Miner is shutting down... 
[INFO ] 2024-05-09 09:58:52.200 - [任务 3][SourceOralce] - Log Miner has been closed! 
[WARN ] 2024-05-09 09:58:52.200 - [任务 3][SourceOralce] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.io.IOException: Connection reset by peer
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-09 10:05:45.624 - [任务 3][SourceOralce] - Log Miner is shutting down... 
[INFO ] 2024-05-09 10:05:45.625 - [任务 3][SourceOralce] - Log Miner has been closed! 
[INFO ] 2024-05-09 10:05:52.137 - [任务 3][SourceOralce] - Checking whether archived log exists... 
[INFO ] 2024-05-09 10:05:52.339 - [任务 3][SourceOralce] - Redo Log Miner is starting... 
[INFO ] 2024-05-09 10:06:43.775 - [任务 3][SourceOralce] - Redo Log Miner has been started... 
[INFO ] 2024-05-09 10:08:02.254 - [任务 3][SourceOralce] - Node SourceOralce[e4a5f7c4-e3de-4afe-aaf4-13bdfd446e0d] running status set to false 
[INFO ] 2024-05-09 10:08:02.256 - [任务 3][SourceOralce] - Log Miner is shutting down... 
[INFO ] 2024-05-09 10:08:02.256 - [任务 3][SourceOralce] - Auto redo oracle log miner result set closed 
[INFO ] 2024-05-09 10:08:02.256 - [任务 3][SourceOralce] - Log Miner has been closed! 
[INFO ] 2024-05-09 10:08:02.400 - [任务 3][SourceOralce] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-09 10:08:02.400 - [任务 3][SourceOralce] - Incremental sync completed 
[INFO ] 2024-05-09 10:09:58.469 - [任务 3] - Task initialization... 
[INFO ] 2024-05-09 10:09:58.519 - [任务 3] - Start task milestones: 663b1863278c7854c3168a93(任务 3) 
[INFO ] 2024-05-09 10:09:58.520 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-09 10:09:58.726 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-09 10:09:59.024 - [任务 3][TestMysql] - Node TestMysql[68e11400-7057-4b71-a5da-3b65d5b40470] start preload schema,table counts: 1 
[INFO ] 2024-05-09 10:09:59.025 - [任务 3][SourceOralce] - Node SourceOralce[e4a5f7c4-e3de-4afe-aaf4-13bdfd446e0d] start preload schema,table counts: 1 
[INFO ] 2024-05-09 10:09:59.185 - [任务 3][TestMysql] - Node TestMysql[68e11400-7057-4b71-a5da-3b65d5b40470] preload schema finished, cost 163 ms 
[INFO ] 2024-05-09 10:09:59.186 - [任务 3][SourceOralce] - Node SourceOralce[e4a5f7c4-e3de-4afe-aaf4-13bdfd446e0d] preload schema finished, cost 159 ms 
[INFO ] 2024-05-09 10:10:00.097 - [任务 3][SourceOralce] - Source node "SourceOralce" read batch size: 100 
[INFO ] 2024-05-09 10:10:00.102 - [任务 3][SourceOralce] - Source node "SourceOralce" event queue capacity: 200 
[INFO ] 2024-05-09 10:10:00.102 - [任务 3][SourceOralce] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-09 10:10:00.335 - [任务 3][SourceOralce] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":719036192,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-05-09 10:10:00.336 - [任务 3][SourceOralce] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-09 10:10:00.402 - [任务 3][TestMysql] - Node(TestMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-05-09 10:10:00.402 - [任务 3][TestMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-09 10:10:00.703 - [任务 3][SourceOralce] - Initial sync started 
[INFO ] 2024-05-09 10:10:00.714 - [任务 3][SourceOralce] - Starting batch read, table name: Test, offset: null 
[INFO ] 2024-05-09 10:10:00.760 - [任务 3][SourceOralce] - Table Test is going to be initial synced 
[INFO ] 2024-05-09 10:10:00.760 - [任务 3][SourceOralce] - Query table 'Test' counts: 13 
[INFO ] 2024-05-09 10:10:00.777 - [任务 3][SourceOralce] - Table [Test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-09 10:10:00.777 - [任务 3][SourceOralce] - Initial sync completed 
[INFO ] 2024-05-09 10:10:00.780 - [任务 3][SourceOralce] - Incremental sync starting... 
[INFO ] 2024-05-09 10:10:00.780 - [任务 3][SourceOralce] - Initial sync completed 
[INFO ] 2024-05-09 10:10:00.862 - [任务 3][SourceOralce] - Starting stream read, table list: [Test], offset: {"sortString":null,"offsetValue":null,"lastScn":719036192,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-05-09 10:10:00.863 - [任务 3][SourceOralce] - Checking whether archived log exists... 
[INFO ] 2024-05-09 10:10:05.043 - [任务 3][SourceOralce] - building new log file... 
[INFO ] 2024-05-09 10:10:11.093 - [任务 3][SourceOralce] - Redo Log Miner is starting... 
[INFO ] 2024-05-09 10:10:59.357 - [任务 3][SourceOralce] - Redo Log Miner has been started... 
[INFO ] 2024-05-09 10:10:59.359 - [任务 3][SourceOralce] - Connector Oracle incremental start succeed, tables: [Test], data change syncing 
[WARN ] 2024-05-09 10:32:32.306 - [任务 3][SourceOralce] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-09 10:33:32.398 - [任务 3][SourceOralce] - Log Miner is shutting down... 
[INFO ] 2024-05-09 10:33:32.608 - [任务 3][SourceOralce] - Log Miner has been closed! 
[INFO ] 2024-05-09 10:33:32.950 - [任务 3][SourceOralce] - Checking whether archived log exists... 
[INFO ] 2024-05-09 10:33:32.950 - [任务 3][SourceOralce] - building new log file... 
[INFO ] 2024-05-09 10:33:35.386 - [任务 3][SourceOralce] - Redo Log Miner is starting... 
[INFO ] 2024-05-09 10:34:27.477 - [任务 3][SourceOralce] - Redo Log Miner has been started... 
[INFO ] 2024-05-09 10:44:11.261 - [任务 3] - Stop task milestones: 663b1863278c7854c3168a93(任务 3)  
[INFO ] 2024-05-09 10:44:11.307 - [任务 3][SourceOralce] - Node SourceOralce[e4a5f7c4-e3de-4afe-aaf4-13bdfd446e0d] running status set to false 
[INFO ] 2024-05-09 10:44:11.308 - [任务 3][SourceOralce] - Log Miner is shutting down... 
[INFO ] 2024-05-09 10:44:11.322 - [任务 3][SourceOralce] - Log Miner has been closed! 
[INFO ] 2024-05-09 10:44:11.323 - [任务 3][SourceOralce] - Auto redo oracle log miner result set closed 
[INFO ] 2024-05-09 10:44:11.358 - [任务 3][SourceOralce] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-09 10:44:11.360 - [任务 3][SourceOralce] - PDK connector node stopped: HazelcastSourcePdkDataNode-e4a5f7c4-e3de-4afe-aaf4-13bdfd446e0d 
[INFO ] 2024-05-09 10:44:11.363 - [任务 3][SourceOralce] - PDK connector node released: HazelcastSourcePdkDataNode-e4a5f7c4-e3de-4afe-aaf4-13bdfd446e0d 
[INFO ] 2024-05-09 10:44:11.363 - [任务 3][SourceOralce] - Node SourceOralce[e4a5f7c4-e3de-4afe-aaf4-13bdfd446e0d] schema data cleaned 
[INFO ] 2024-05-09 10:44:11.368 - [任务 3][SourceOralce] - Node SourceOralce[e4a5f7c4-e3de-4afe-aaf4-13bdfd446e0d] monitor closed 
[INFO ] 2024-05-09 10:44:11.368 - [任务 3][SourceOralce] - Node SourceOralce[e4a5f7c4-e3de-4afe-aaf4-13bdfd446e0d] close complete, cost 66 ms 
[INFO ] 2024-05-09 10:44:11.387 - [任务 3][TestMysql] - Node TestMysql[68e11400-7057-4b71-a5da-3b65d5b40470] running status set to false 
[INFO ] 2024-05-09 10:44:11.387 - [任务 3][TestMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-68e11400-7057-4b71-a5da-3b65d5b40470 
[INFO ] 2024-05-09 10:44:11.387 - [任务 3][TestMysql] - PDK connector node released: HazelcastTargetPdkDataNode-68e11400-7057-4b71-a5da-3b65d5b40470 
[INFO ] 2024-05-09 10:44:11.389 - [任务 3][TestMysql] - Node TestMysql[68e11400-7057-4b71-a5da-3b65d5b40470] schema data cleaned 
[INFO ] 2024-05-09 10:44:11.391 - [任务 3][TestMysql] - Node TestMysql[68e11400-7057-4b71-a5da-3b65d5b40470] monitor closed 
[INFO ] 2024-05-09 10:44:11.391 - [任务 3][TestMysql] - Node TestMysql[68e11400-7057-4b71-a5da-3b65d5b40470] close complete, cost 22 ms 
[INFO ] 2024-05-09 10:44:12.480 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-09 10:44:12.487 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-05-09 10:44:12.487 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-05-09 10:44:12.536 - [任务 3] - Remove memory task client succeed, task: 任务 3[663b1863278c7854c3168a93] 
[INFO ] 2024-05-09 10:44:12.538 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[663b1863278c7854c3168a93] 
