[INFO ] 2024-06-28 00:24:14.665 - [任务 6(100)][标准JS] - Node 标准JS[60dabb1d-4a23-4b5a-8261-748399fbe0a9] start preload schema,table counts: 1 
[INFO ] 2024-06-28 00:24:14.666 - [任务 6(100)][c8220912-e8cd-496c-a020-e404849cf1f2] - Node c8220912-e8cd-496c-a020-e404849cf1f2[c8220912-e8cd-496c-a020-e404849cf1f2] start preload schema,table counts: 0 
[INFO ] 2024-06-28 00:24:14.667 - [任务 6(100)][标准JS] - Node 标准JS[60dabb1d-4a23-4b5a-8261-748399fbe0a9] preload schema finished, cost 0 ms 
[INFO ] 2024-06-28 00:24:14.667 - [任务 6(100)][Amazon订单] - Node Amazon订单[edbfe2bd-ba85-4689-a0b4-1370392c4dc4] start preload schema,table counts: 1 
[INFO ] 2024-06-28 00:24:14.667 - [任务 6(100)][c8220912-e8cd-496c-a020-e404849cf1f2] - Node c8220912-e8cd-496c-a020-e404849cf1f2[c8220912-e8cd-496c-a020-e404849cf1f2] preload schema finished, cost 0 ms 
[INFO ] 2024-06-28 00:24:14.667 - [任务 6(100)][Amazon订单] - Node Amazon订单[edbfe2bd-ba85-4689-a0b4-1370392c4dc4] preload schema finished, cost 0 ms 
[INFO ] 2024-06-28 00:24:15.829 - [任务 6(100)][Amazon订单] - Node Amazon订单[edbfe2bd-ba85-4689-a0b4-1370392c4dc4] running status set to false 
[INFO ] 2024-06-28 00:24:15.831 - [任务 6(100)][Amazon订单] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-edbfe2bd-ba85-4689-a0b4-1370392c4dc4 
[INFO ] 2024-06-28 00:24:15.831 - [任务 6(100)][Amazon订单] - PDK connector node released: HazelcastSampleSourcePdkDataNode-edbfe2bd-ba85-4689-a0b4-1370392c4dc4 
[INFO ] 2024-06-28 00:24:15.832 - [任务 6(100)][Amazon订单] - Node Amazon订单[edbfe2bd-ba85-4689-a0b4-1370392c4dc4] schema data cleaned 
[INFO ] 2024-06-28 00:24:15.832 - [任务 6(100)][Amazon订单] - Node Amazon订单[edbfe2bd-ba85-4689-a0b4-1370392c4dc4] monitor closed 
[INFO ] 2024-06-28 00:24:15.870 - [任务 6(100)][Amazon订单] - Node Amazon订单[edbfe2bd-ba85-4689-a0b4-1370392c4dc4] close complete, cost 101 ms 
[INFO ] 2024-06-28 00:24:15.872 - [任务 6(100)][标准JS] - Node 标准JS[60dabb1d-4a23-4b5a-8261-748399fbe0a9] running status set to false 
[INFO ] 2024-06-28 00:24:15.872 - [任务 6(100)][c8220912-e8cd-496c-a020-e404849cf1f2] - Node c8220912-e8cd-496c-a020-e404849cf1f2[c8220912-e8cd-496c-a020-e404849cf1f2] running status set to false 
[INFO ] 2024-06-28 00:24:15.872 - [任务 6(100)][c8220912-e8cd-496c-a020-e404849cf1f2] - Node c8220912-e8cd-496c-a020-e404849cf1f2[c8220912-e8cd-496c-a020-e404849cf1f2] schema data cleaned 
[INFO ] 2024-06-28 00:24:15.872 - [任务 6(100)][c8220912-e8cd-496c-a020-e404849cf1f2] - Node c8220912-e8cd-496c-a020-e404849cf1f2[c8220912-e8cd-496c-a020-e404849cf1f2] monitor closed 
[INFO ] 2024-06-28 00:24:15.872 - [任务 6(100)][c8220912-e8cd-496c-a020-e404849cf1f2] - Node c8220912-e8cd-496c-a020-e404849cf1f2[c8220912-e8cd-496c-a020-e404849cf1f2] close complete, cost 4 ms 
[INFO ] 2024-06-28 00:24:15.877 - [任务 6(100)][标准JS] - Node 标准JS[60dabb1d-4a23-4b5a-8261-748399fbe0a9] schema data cleaned 
[INFO ] 2024-06-28 00:24:15.879 - [任务 6(100)][标准JS] - Node 标准JS[60dabb1d-4a23-4b5a-8261-748399fbe0a9] monitor closed 
[INFO ] 2024-06-28 00:24:15.881 - [任务 6(100)][标准JS] - Node 标准JS[60dabb1d-4a23-4b5a-8261-748399fbe0a9] close complete, cost 17 ms 
[INFO ] 2024-06-28 00:24:17.619 - [任务 6(100)][标准JS] - Node 标准JS[60dabb1d-4a23-4b5a-8261-748399fbe0a9] start preload schema,table counts: 1 
[INFO ] 2024-06-28 00:24:17.619 - [任务 6(100)][Amazon订单] - Node Amazon订单[edbfe2bd-ba85-4689-a0b4-1370392c4dc4] start preload schema,table counts: 1 
[INFO ] 2024-06-28 00:24:17.620 - [任务 6(100)][6b24c96c-6fdd-4465-9c64-b22c006c36bd] - Node 6b24c96c-6fdd-4465-9c64-b22c006c36bd[6b24c96c-6fdd-4465-9c64-b22c006c36bd] start preload schema,table counts: 0 
[INFO ] 2024-06-28 00:24:17.621 - [任务 6(100)][Amazon订单] - Node Amazon订单[edbfe2bd-ba85-4689-a0b4-1370392c4dc4] preload schema finished, cost 0 ms 
[INFO ] 2024-06-28 00:24:17.629 - [任务 6(100)][标准JS] - Node 标准JS[60dabb1d-4a23-4b5a-8261-748399fbe0a9] preload schema finished, cost 0 ms 
[INFO ] 2024-06-28 00:24:17.629 - [任务 6(100)][6b24c96c-6fdd-4465-9c64-b22c006c36bd] - Node 6b24c96c-6fdd-4465-9c64-b22c006c36bd[6b24c96c-6fdd-4465-9c64-b22c006c36bd] preload schema finished, cost 0 ms 
[INFO ] 2024-06-28 00:24:18.354 - [任务 6(100)][Amazon订单] - Node Amazon订单[edbfe2bd-ba85-4689-a0b4-1370392c4dc4] running status set to false 
[INFO ] 2024-06-28 00:24:18.354 - [任务 6(100)][标准JS] - Node 标准JS[60dabb1d-4a23-4b5a-8261-748399fbe0a9] running status set to false 
[INFO ] 2024-06-28 00:24:18.355 - [任务 6(100)][6b24c96c-6fdd-4465-9c64-b22c006c36bd] - Node 6b24c96c-6fdd-4465-9c64-b22c006c36bd[6b24c96c-6fdd-4465-9c64-b22c006c36bd] running status set to false 
[INFO ] 2024-06-28 00:24:18.355 - [任务 6(100)][Amazon订单] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-edbfe2bd-ba85-4689-a0b4-1370392c4dc4 
[INFO ] 2024-06-28 00:24:18.355 - [任务 6(100)][6b24c96c-6fdd-4465-9c64-b22c006c36bd] - Node 6b24c96c-6fdd-4465-9c64-b22c006c36bd[6b24c96c-6fdd-4465-9c64-b22c006c36bd] schema data cleaned 
[INFO ] 2024-06-28 00:24:18.356 - [任务 6(100)][6b24c96c-6fdd-4465-9c64-b22c006c36bd] - Node 6b24c96c-6fdd-4465-9c64-b22c006c36bd[6b24c96c-6fdd-4465-9c64-b22c006c36bd] monitor closed 
[INFO ] 2024-06-28 00:24:18.356 - [任务 6(100)][Amazon订单] - PDK connector node released: HazelcastSampleSourcePdkDataNode-edbfe2bd-ba85-4689-a0b4-1370392c4dc4 
[INFO ] 2024-06-28 00:24:18.356 - [任务 6(100)][Amazon订单] - Node Amazon订单[edbfe2bd-ba85-4689-a0b4-1370392c4dc4] schema data cleaned 
[INFO ] 2024-06-28 00:24:18.356 - [任务 6(100)][6b24c96c-6fdd-4465-9c64-b22c006c36bd] - Node 6b24c96c-6fdd-4465-9c64-b22c006c36bd[6b24c96c-6fdd-4465-9c64-b22c006c36bd] close complete, cost 13 ms 
[INFO ] 2024-06-28 00:24:18.356 - [任务 6(100)][标准JS] - Node 标准JS[60dabb1d-4a23-4b5a-8261-748399fbe0a9] schema data cleaned 
[INFO ] 2024-06-28 00:24:18.357 - [任务 6(100)][Amazon订单] - Node Amazon订单[edbfe2bd-ba85-4689-a0b4-1370392c4dc4] monitor closed 
[INFO ] 2024-06-28 00:24:18.358 - [任务 6(100)][标准JS] - Node 标准JS[60dabb1d-4a23-4b5a-8261-748399fbe0a9] monitor closed 
[INFO ] 2024-06-28 00:24:18.358 - [任务 6(100)][Amazon订单] - Node Amazon订单[edbfe2bd-ba85-4689-a0b4-1370392c4dc4] close complete, cost 74 ms 
[INFO ] 2024-06-28 00:24:18.360 - [任务 6(100)][标准JS] - Node 标准JS[60dabb1d-4a23-4b5a-8261-748399fbe0a9] close complete, cost 32 ms 
