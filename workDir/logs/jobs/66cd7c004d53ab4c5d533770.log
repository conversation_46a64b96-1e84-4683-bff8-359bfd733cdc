[INFO ] 2024-08-27 15:16:40.256 - [任务 2] - Start task milestones: 66cd7c004d53ab4c5d533770(任务 2) 
[INFO ] 2024-08-27 15:16:40.256 - [任务 2] - Task initialization... 
[INFO ] 2024-08-27 15:16:40.603 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 15:16:40.603 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 15:16:40.649 - [任务 2][Mongo] - Node Mongo[2b74bbdf-70b1-4afc-bdd9-00cd292481ea] start preload schema,table counts: 9 
[INFO ] 2024-08-27 15:16:40.649 - [任务 2][Oracle] - Node Oracle[04d9edaf-7448-4cfd-91a1-6d9bc90e7146] start preload schema,table counts: 9 
[INFO ] 2024-08-27 15:16:40.649 - [任务 2][Oracle] - Node Oracle[04d9edaf-7448-4cfd-91a1-6d9bc90e7146] preload schema finished, cost 1 ms 
[INFO ] 2024-08-27 15:16:40.854 - [任务 2][Mongo] - Node Mongo[2b74bbdf-70b1-4afc-bdd9-00cd292481ea] preload schema finished, cost 1 ms 
[INFO ] 2024-08-27 15:16:41.331 - [任务 2][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 15:16:41.332 - [任务 2][Mongo] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-08-27 15:16:41.849 - [任务 2][Oracle] - Source node "Oracle" read batch size: 100 
[INFO ] 2024-08-27 15:16:41.850 - [任务 2][Oracle] - Source node "Oracle" event queue capacity: 200 
[INFO ] 2024-08-27 15:16:41.850 - [任务 2][Oracle] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-27 15:16:42.179 - [任务 2][Oracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":102756285,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-27 15:16:42.252 - [任务 2] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 15:16:42.252 - [任务 2][Oracle] - Initial sync started 
[INFO ] 2024-08-27 15:16:42.257 - [任务 2][Oracle] - Starting batch read, table name: AA_0516_share 
[INFO ] 2024-08-27 15:16:42.257 - [任务 2][Oracle] - Table AA_0516_share is going to be initial synced 
[INFO ] 2024-08-27 15:16:42.301 - [任务 2][Oracle] - Query table 'AA_0516_share' counts: 8 
[INFO ] 2024-08-27 15:16:42.301 - [任务 2][Oracle] - Table [AA_0516_share] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 15:16:42.301 - [任务 2][Oracle] - Starting batch read, table name: AA_0322 
[INFO ] 2024-08-27 15:16:42.302 - [任务 2][Oracle] - Table AA_0322 is going to be initial synced 
[INFO ] 2024-08-27 15:16:42.503 - [任务 2][Oracle] - Query table 'AA_0322' counts: 10032 
[INFO ] 2024-08-27 15:16:42.996 - [任务 2][Oracle] - Table [AA_0322] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 15:16:43.007 - [任务 2][Oracle] - Starting batch read, table name: AA_0514 
[INFO ] 2024-08-27 15:16:43.007 - [任务 2][Oracle] - Table AA_0514 is going to be initial synced 
[INFO ] 2024-08-27 15:16:43.065 - [任务 2][Oracle] - Query table 'AA_0514' counts: 20 
[INFO ] 2024-08-27 15:16:43.067 - [任务 2][Oracle] - Table [AA_0514] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 15:16:43.067 - [任务 2][Oracle] - Starting batch read, table name: AA_0516 
[INFO ] 2024-08-27 15:16:43.067 - [任务 2][Oracle] - Table AA_0516 is going to be initial synced 
[INFO ] 2024-08-27 15:16:43.113 - [任务 2][Oracle] - Table [AA_0516] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 15:16:43.117 - [任务 2][Oracle] - Query table 'AA_0516' counts: 1 
[INFO ] 2024-08-27 15:16:43.117 - [任务 2][Oracle] - Starting batch read, table name: AA_0328 
[INFO ] 2024-08-27 15:16:43.117 - [任务 2][Oracle] - Table AA_0328 is going to be initial synced 
[INFO ] 2024-08-27 15:16:43.180 - [任务 2][Oracle] - Table [AA_0328] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 15:16:43.184 - [任务 2][Oracle] - Query table 'AA_0328' counts: 0 
[INFO ] 2024-08-27 15:16:43.184 - [任务 2][Oracle] - Starting batch read, table name: AA_0516_T 
[INFO ] 2024-08-27 15:16:43.184 - [任务 2][Oracle] - Table AA_0516_T is going to be initial synced 
[INFO ] 2024-08-27 15:16:43.212 - [任务 2][Oracle] - Table [AA_0516_T] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 15:16:43.212 - [任务 2][Oracle] - Query table 'AA_0516_T' counts: 8 
[INFO ] 2024-08-27 15:16:43.213 - [任务 2][Oracle] - Starting batch read, table name: AA_0530_T2 
[INFO ] 2024-08-27 15:16:43.216 - [任务 2][Oracle] - Table AA_0530_T2 is going to be initial synced 
[INFO ] 2024-08-27 15:16:43.393 - [任务 2][Oracle] - Query table 'AA_0530_T2' counts: 56 
[INFO ] 2024-08-27 15:16:43.393 - [任务 2][Oracle] - Table [AA_0530_T2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 15:16:43.393 - [任务 2][Oracle] - Starting batch read, table name: AA_0530_T1 
[INFO ] 2024-08-27 15:16:43.393 - [任务 2][Oracle] - Table AA_0530_T1 is going to be initial synced 
[INFO ] 2024-08-27 15:16:43.416 - [任务 2][Oracle] - Query table 'AA_0530_T1' counts: 56 
[INFO ] 2024-08-27 15:16:43.454 - [任务 2][Oracle] - Table [AA_0530_T1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 15:16:43.454 - [任务 2][Oracle] - Starting batch read, table name: AA_0508 
[INFO ] 2024-08-27 15:16:43.454 - [任务 2][Oracle] - Table AA_0508 is going to be initial synced 
[INFO ] 2024-08-27 15:16:43.481 - [任务 2][Oracle] - Query table 'AA_0508' counts: 3 
[INFO ] 2024-08-27 15:16:43.483 - [任务 2][Oracle] - Table [AA_0508] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 15:16:43.483 - [任务 2][Oracle] - Initial sync completed 
[INFO ] 2024-08-27 15:16:43.483 - [任务 2][Oracle] - Incremental sync starting... 
[INFO ] 2024-08-27 15:16:43.483 - [任务 2][Oracle] - Initial sync completed 
[INFO ] 2024-08-27 15:16:43.484 - [任务 2][Oracle] - Starting stream read, table list: [AA_0516_share, AA_0322, AA_0514, AA_0516, AA_0328, AA_0516_T, AA_0530_T2, AA_0530_T1, AA_0508], offset: {"sortString":null,"offsetValue":null,"lastScn":102756285,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-27 15:16:44.146 - [任务 2][Oracle] - total start mining scn: 102756285 
[INFO ] 2024-08-27 15:16:45.562 - [任务 2][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-27 15:38:42.369 - [任务 2][Oracle] - Found new table(s): [AA_T3] 
[INFO ] 2024-08-27 15:38:43.145 - [任务 2][Oracle] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-08-27 15:38:43.182 - [任务 2][Oracle] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@1862365e: {"table":{"id":"AA_T3","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"SYS_C0066706","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"AA_T3","nameFieldMap":{"ID":{"autoInc":false,"dataType":"NUMBER(*,0)","length":22,"name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"NUMBER","scale":0,"tapType":{"fixed":true,"maxValue":99999999999999999999,"minValue":-99999999999999999999,"precision":20,"scale":0,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR2(255)","length":255,"name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"VARCHAR2","tapType":{"bytes":255,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true},"tableAttr":{"capacity":0}},"tableId":"AA_T3","type":206} 
[INFO ] 2024-08-27 15:38:43.182 - [任务 2][Oracle] - Create new table in memory, qualified name: T_oracle_io_tapdata_1_0-SNAPSHOT_AA_T3_66cd7bf24d53ab4c5d533765_66cd7c004d53ab4c5d533770 
[INFO ] 2024-08-27 15:38:43.240 - [任务 2][Oracle] - Create new table schema transform finished: TapTable id AA_T3 name AA_T3 storageEngine null charset null number of fields 2 
[INFO ] 2024-08-27 15:38:43.240 - [任务 2][Oracle] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-08-27 15:38:43.409 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 15:38:43.409 - [任务 2][Oracle] - Log Miner has been closed! 
[WARN ] 2024-08-27 15:38:43.611 - [任务 2][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 1306, message: Caused by: oracle.jdbc.OracleDatabaseException: ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 15:38:44.005 - [任务 2][Oracle] - Starting batch read, table name: AA_T3 
[INFO ] 2024-08-27 15:38:44.079 - [任务 2][Oracle] - Table AA_T3 is going to be initial synced 
[INFO ] 2024-08-27 15:38:44.079 - [任务 2][Mongo] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@1862365e: {"table":{"id":"AA_T3","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"SYS_C0066706","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"AA_T3","nameFieldMap":{"ID":{"autoInc":false,"dataType":"NUMBER(20)","length":22,"name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"NUMBER","scale":0,"tapType":{"maxValue":99999999999999999999,"minValue":-99999999999999999999,"precision":20,"scale":0,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR2(255)","length":255,"name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"VARCHAR2","tapType":{"bytes":255,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true},"tableAttr":{"capacity":0}},"tableId":"AA_T3","type":206}). Wait for all previous events to be processed 
[INFO ] 2024-08-27 15:38:44.273 - [任务 2][Oracle] - Query table 'AA_T3' counts: 0 
[INFO ] 2024-08-27 15:38:44.325 - [任务 2][Oracle] - Table [AA_T3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 15:38:44.325 - [任务 2][Oracle] - Initial sync completed 
[INFO ] 2024-08-27 15:38:44.326 - [任务 2][Oracle] - Incremental sync starting... 
[INFO ] 2024-08-27 15:38:44.326 - [任务 2][Oracle] - Initial sync completed 
[INFO ] 2024-08-27 15:38:44.326 - [任务 2][Oracle] - Starting stream read, table list: [AA_0516_share, AA_0322, AA_0514, AA_0516, AA_0328, AA_0516_T, AA_0530_T2, AA_0530_T1, AA_T3, AA_0508], offset: {"sortString":null,"offsetValue":null,"lastScn":102761313,"pendingScn":102761316,"timestamp":1724744311000,"hexScn":null,"fno":0} 
[INFO ] 2024-08-27 15:38:44.705 - [任务 2][Mongo] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@1862365e: {"table":{"id":"AA_T3","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"SYS_C0066706","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"AA_T3","nameFieldMap":{"ID":{"autoInc":false,"dataType":"NUMBER(20)","length":22,"name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"NUMBER","scale":0,"tapType":{"maxValue":99999999999999999999,"minValue":-99999999999999999999,"precision":20,"scale":0,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR2(255)","length":255,"name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"VARCHAR2","tapType":{"bytes":255,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true},"tableAttr":{"capacity":0}},"tableId":"AA_T3","type":206}) 
[INFO ] 2024-08-27 15:38:45.088 - [任务 2][Oracle] - total start mining scn: 102761313 
[INFO ] 2024-08-27 15:38:46.228 - [任务 2][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-27 15:39:43.462 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 15:39:43.464 - [任务 2][Oracle] - Log Miner has been closed! 
[WARN ] 2024-08-27 15:39:43.672 - [任务 2][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 15:40:43.491 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 15:40:43.491 - [任务 2][Oracle] - Log Miner has been closed! 
[WARN ] 2024-08-27 15:40:43.496 - [任务 2][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 15:41:43.519 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 15:41:43.523 - [任务 2][Oracle] - Log Miner has been closed! 
[WARN ] 2024-08-27 15:41:43.524 - [任务 2][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 15:42:43.546 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 15:42:43.546 - [任务 2][Oracle] - Log Miner has been closed! 
[WARN ] 2024-08-27 15:42:43.549 - [任务 2][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	...
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 15:43:43.578 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 15:43:43.579 - [任务 2][Oracle] - Log Miner has been closed! 
[WARN ] 2024-08-27 15:43:43.782 - [任务 2][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 15:44:43.602 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 15:44:43.603 - [任务 2][Oracle] - Log Miner has been closed! 
[WARN ] 2024-08-27 15:44:43.604 - [任务 2][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	...
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 15:45:43.620 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 15:45:43.625 - [任务 2][Oracle] - Log Miner has been closed! 
[WARN ] 2024-08-27 15:45:43.626 - [任务 2][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	...
 - Remaining retry 8 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 15:46:43.675 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 15:46:43.676 - [任务 2][Oracle] - Log Miner has been closed! 
[WARN ] 2024-08-27 15:46:43.678 - [任务 2][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	...
 - Remaining retry 7 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 15:47:43.696 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 15:47:43.697 - [任务 2][Oracle] - Log Miner has been closed! 
[WARN ] 2024-08-27 15:47:43.697 - [任务 2][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	...
 - Remaining retry 6 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 15:48:43.727 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 15:48:43.727 - [任务 2][Oracle] - Log Miner has been closed! 
[WARN ] 2024-08-27 15:48:43.929 - [任务 2][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	...
 - Remaining retry 5 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 15:49:43.755 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 15:49:43.758 - [任务 2][Oracle] - Log Miner has been closed! 
[WARN ] 2024-08-27 15:49:43.759 - [任务 2][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	...
 - Remaining retry 4 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 15:50:43.787 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 15:50:43.788 - [任务 2][Oracle] - Log Miner has been closed! 
[WARN ] 2024-08-27 15:50:43.800 - [任务 2][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	...
 - Remaining retry 3 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 15:51:43.826 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 15:51:43.826 - [任务 2][Oracle] - Log Miner has been closed! 
[WARN ] 2024-08-27 15:51:43.829 - [任务 2][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	...
 - Remaining retry 2 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 15:52:43.875 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 15:52:43.875 - [任务 2][Oracle] - Log Miner has been closed! 
[WARN ] 2024-08-27 15:52:43.879 - [任务 2][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	...
 - Remaining retry 1 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 15:53:43.902 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 15:53:43.926 - [任务 2][Oracle] - Log Miner has been closed! 
[INFO ] 2024-08-27 15:53:43.927 - [任务 2][Oracle] - Incremental sync completed 
[INFO ] 2024-08-27 15:53:43.942 - [任务 2][Oracle] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed. 
[ERROR] 2024-08-27 15:53:43.943 - [任务 2][Oracle] - java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed. <-- Error Message -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	...

<-- Full Stack Trace -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:437)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:790)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-40) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 24 more

[INFO ] 2024-08-27 15:53:44.155 - [任务 2][Oracle] - Job suspend in error handle 
[INFO ] 2024-08-27 15:53:44.400 - [任务 2][Oracle] - Node Oracle[04d9edaf-7448-4cfd-91a1-6d9bc90e7146] running status set to false 
[INFO ] 2024-08-27 15:53:44.491 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 15:53:44.492 - [任务 2][Oracle] - Log Miner has been closed! 
[INFO ] 2024-08-27 15:53:44.587 - [任务 2][Oracle] - Incremental sync completed 
[INFO ] 2024-08-27 15:53:44.587 - [任务 2][Oracle] - PDK connector node stopped: HazelcastSourcePdkDataNode-04d9edaf-7448-4cfd-91a1-6d9bc90e7146 
[INFO ] 2024-08-27 15:53:44.587 - [任务 2][Oracle] - PDK connector node released: HazelcastSourcePdkDataNode-04d9edaf-7448-4cfd-91a1-6d9bc90e7146 
[INFO ] 2024-08-27 15:53:44.587 - [任务 2][Oracle] - Node Oracle[04d9edaf-7448-4cfd-91a1-6d9bc90e7146] schema data cleaned 
[INFO ] 2024-08-27 15:53:44.591 - [任务 2][Oracle] - Node Oracle[04d9edaf-7448-4cfd-91a1-6d9bc90e7146] monitor closed 
[INFO ] 2024-08-27 15:53:44.595 - [任务 2][Oracle] - Node Oracle[04d9edaf-7448-4cfd-91a1-6d9bc90e7146] close complete, cost 198 ms 
[INFO ] 2024-08-27 15:53:44.595 - [任务 2][Mongo] - Node Mongo[2b74bbdf-70b1-4afc-bdd9-00cd292481ea] running status set to false 
[INFO ] 2024-08-27 15:53:44.616 - [任务 2][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-2b74bbdf-70b1-4afc-bdd9-00cd292481ea 
[INFO ] 2024-08-27 15:53:44.616 - [任务 2][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-2b74bbdf-70b1-4afc-bdd9-00cd292481ea 
[INFO ] 2024-08-27 15:53:44.616 - [任务 2][Mongo] - Node Mongo[2b74bbdf-70b1-4afc-bdd9-00cd292481ea] schema data cleaned 
[INFO ] 2024-08-27 15:53:44.617 - [任务 2][Mongo] - Node Mongo[2b74bbdf-70b1-4afc-bdd9-00cd292481ea] monitor closed 
[INFO ] 2024-08-27 15:53:44.617 - [任务 2][Mongo] - Node Mongo[2b74bbdf-70b1-4afc-bdd9-00cd292481ea] close complete, cost 22 ms 
[INFO ] 2024-08-27 15:53:45.225 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-27 15:53:45.226 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@781fb793 
[INFO ] 2024-08-27 15:53:45.358 - [任务 2] - Stop task milestones: 66cd7c004d53ab4c5d533770(任务 2)  
[INFO ] 2024-08-27 15:53:45.358 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-08-27 15:53:45.358 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-08-27 15:53:45.359 - [任务 2] - Remove memory task client succeed, task: 任务 2[66cd7c004d53ab4c5d533770] 
[INFO ] 2024-08-27 15:53:45.360 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[66cd7c004d53ab4c5d533770] 
[INFO ] 2024-08-27 15:53:45.374 - [任务 2] - Resume task[任务 2] 
[INFO ] 2024-08-27 15:53:45.408 - [任务 2] - Start task milestones: 66cd7c004d53ab4c5d533770(任务 2) 
[INFO ] 2024-08-27 15:53:45.408 - [任务 2] - Task initialization... 
[INFO ] 2024-08-27 15:53:45.705 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 15:53:45.705 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 15:53:45.760 - [任务 2][Mongo] - Node Mongo[2b74bbdf-70b1-4afc-bdd9-00cd292481ea] start preload schema,table counts: 10 
[INFO ] 2024-08-27 15:53:45.760 - [任务 2][Oracle] - Node Oracle[04d9edaf-7448-4cfd-91a1-6d9bc90e7146] start preload schema,table counts: 10 
[INFO ] 2024-08-27 15:53:45.760 - [任务 2][Mongo] - Node Mongo[2b74bbdf-70b1-4afc-bdd9-00cd292481ea] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 15:53:45.760 - [任务 2][Oracle] - Node Oracle[04d9edaf-7448-4cfd-91a1-6d9bc90e7146] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 15:53:45.880 - [任务 2][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 15:53:45.880 - [任务 2][Mongo] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-08-27 15:53:46.230 - [任务 2][Oracle] - Source node "Oracle" read batch size: 100 
[INFO ] 2024-08-27 15:53:46.230 - [任务 2][Oracle] - Source node "Oracle" event queue capacity: 200 
[INFO ] 2024-08-27 15:53:46.231 - [任务 2][Oracle] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-27 15:53:46.232 - [任务 2][Oracle] - batch offset found: {"AA_0516_share":{"batch_read_connector_status":"OVER"},"AA_0322":{"batch_read_connector_status":"OVER"},"AA_0514":{"batch_read_connector_status":"OVER"},"AA_0516":{"batch_read_connector_status":"OVER"},"AA_0328":{"batch_read_connector_status":"OVER"},"AA_0516_T":{"batch_read_connector_status":"OVER"},"AA_0530_T2":{"batch_read_connector_status":"OVER"},"AA_0530_T1":{"batch_read_connector_status":"OVER"},"AA_T3":{"batch_read_connector_status":"OVER"},"AA_0508":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":102763817,"pendingScn":102763817,"timestamp":1724745176000,"hexScn":null,"fno":0} 
[INFO ] 2024-08-27 15:53:46.233 - [任务 2] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 15:53:46.301 - [任务 2][Oracle] - Incremental sync starting... 
[INFO ] 2024-08-27 15:53:46.301 - [任务 2][Oracle] - Initial sync completed 
[INFO ] 2024-08-27 15:53:46.301 - [任务 2][Oracle] - Starting stream read, table list: [AA_0516_share, AA_0322, AA_0514, AA_0516, AA_0328, AA_0516_T, AA_0530_T2, AA_0530_T1, AA_T3, AA_0508], offset: {"sortString":null,"offsetValue":null,"lastScn":102763817,"pendingScn":102763817,"timestamp":1724745176000,"hexScn":null,"fno":0} 
[INFO ] 2024-08-27 15:53:47.039 - [任务 2][Oracle] - total start mining scn: 102763817 
[INFO ] 2024-08-27 15:53:48.175 - [任务 2][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-27 15:55:01.303 - [任务 2][Oracle] - Node Oracle[04d9edaf-7448-4cfd-91a1-6d9bc90e7146] running status set to false 
[INFO ] 2024-08-27 15:55:01.343 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 16:00:03.248 - [任务 2][Oracle] - Log Miner has been closed! 
[INFO ] 2024-08-27 16:00:03.268 - [任务 2] - Start task milestones: 66cd7c004d53ab4c5d533770(任务 2) 
[INFO ] 2024-08-27 16:00:03.475 - [任务 2] - Task initialization... 
[INFO ] 2024-08-27 16:00:04.703 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 16:00:04.909 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 16:00:05.233 - [任务 2][Oracle] - Node Oracle[04d9edaf-7448-4cfd-91a1-6d9bc90e7146] start preload schema,table counts: 10 
[INFO ] 2024-08-27 16:00:05.235 - [任务 2][Mongo] - Node Mongo[2b74bbdf-70b1-4afc-bdd9-00cd292481ea] start preload schema,table counts: 10 
[INFO ] 2024-08-27 16:00:05.236 - [任务 2][Oracle] - Node Oracle[04d9edaf-7448-4cfd-91a1-6d9bc90e7146] preload schema finished, cost 1 ms 
[INFO ] 2024-08-27 16:00:05.236 - [任务 2][Mongo] - Node Mongo[2b74bbdf-70b1-4afc-bdd9-00cd292481ea] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 16:00:05.949 - [任务 2][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 16:00:05.953 - [任务 2][Mongo] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-08-27 16:00:06.630 - [任务 2][Oracle] - Source node "Oracle" read batch size: 100 
[INFO ] 2024-08-27 16:00:06.634 - [任务 2][Oracle] - Source node "Oracle" event queue capacity: 200 
[INFO ] 2024-08-27 16:00:06.635 - [任务 2][Oracle] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-27 16:00:06.667 - [任务 2][Oracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":102764032,"pendingScn":102764038,"timestamp":1724745255000,"hexScn":null,"fno":0} 
[INFO ] 2024-08-27 16:00:06.680 - [任务 2] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 16:00:06.771 - [任务 2][Oracle] - Incremental sync starting... 
[INFO ] 2024-08-27 16:00:06.772 - [任务 2][Oracle] - Initial sync completed 
[INFO ] 2024-08-27 16:00:06.787 - [任务 2][Oracle] - Starting stream read, table list: [AA_0516_share, AA_0322, AA_0514, AA_0516, AA_0328, AA_0516_T, AA_0530_T2, AA_0530_T1, AA_T3, AA_0508], offset: {"sortString":null,"offsetValue":null,"lastScn":102764032,"pendingScn":102764038,"timestamp":1724745255000,"hexScn":null,"fno":0} 
[INFO ] 2024-08-27 16:00:07.802 - [任务 2][Oracle] - total start mining scn: 102764032 
[INFO ] 2024-08-27 16:00:09.023 - [任务 2][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-27 18:05:30.149 - [任务 2] - Start task milestones: 66cd7c004d53ab4c5d533770(任务 2) 
[INFO ] 2024-08-27 18:05:30.355 - [任务 2] - Task initialization... 
[INFO ] 2024-08-27 18:05:32.675 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 18:05:32.880 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 18:05:33.129 - [任务 2][Mongo] - Node Mongo[2b74bbdf-70b1-4afc-bdd9-00cd292481ea] start preload schema,table counts: 10 
[INFO ] 2024-08-27 18:05:33.159 - [任务 2][Mongo] - Node Mongo[2b74bbdf-70b1-4afc-bdd9-00cd292481ea] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 18:05:33.160 - [任务 2][Oracle] - Node Oracle[04d9edaf-7448-4cfd-91a1-6d9bc90e7146] start preload schema,table counts: 10 
[INFO ] 2024-08-27 18:05:33.162 - [任务 2][Oracle] - Node Oracle[04d9edaf-7448-4cfd-91a1-6d9bc90e7146] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 18:05:33.950 - [任务 2][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 18:05:33.951 - [任务 2][Mongo] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-08-27 18:05:34.704 - [任务 2][Oracle] - Source node "Oracle" read batch size: 100 
[INFO ] 2024-08-27 18:05:34.711 - [任务 2][Oracle] - Source node "Oracle" event queue capacity: 200 
[INFO ] 2024-08-27 18:05:34.738 - [任务 2][Oracle] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-27 18:05:34.739 - [任务 2][Oracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":102773859,"pendingScn":102773859,"timestamp":1724748610000,"hexScn":null,"fno":0} 
[INFO ] 2024-08-27 18:05:38.104 - [任务 2] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 18:05:38.390 - [任务 2][Oracle] - Incremental sync starting... 
[INFO ] 2024-08-27 18:05:38.391 - [任务 2][Oracle] - Initial sync completed 
[INFO ] 2024-08-27 18:05:38.397 - [任务 2][Oracle] - Starting stream read, table list: [AA_0516_share, AA_0322, AA_0514, AA_0516, AA_0328, AA_0516_T, AA_0530_T2, AA_0530_T1, AA_T3, AA_0508], offset: {"sortString":null,"offsetValue":null,"lastScn":102773859,"pendingScn":102773859,"timestamp":1724748610000,"hexScn":null,"fno":0} 
[INFO ] 2024-08-27 18:05:39.353 - [任务 2][Oracle] - total start mining scn: 102773859 
[INFO ] 2024-08-27 18:05:40.773 - [任务 2][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-27 19:16:13.077 - [任务 2][Oracle] - Node Oracle[04d9edaf-7448-4cfd-91a1-6d9bc90e7146] running status set to false 
[INFO ] 2024-08-27 19:16:13.096 - [任务 2][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-27 19:16:13.107 - [任务 2][Oracle] - Log Miner has been closed! 
[INFO ] 2024-08-27 19:16:13.108 - [任务 2][Oracle] - Incremental sync completed 
[INFO ] 2024-08-27 19:16:13.148 - [任务 2][Oracle] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-08-27 19:16:13.150 - [任务 2][Oracle] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:444)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:790)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-08-27 19:16:13.165 - [任务 2][Oracle] - PDK connector node stopped: HazelcastSourcePdkDataNode-04d9edaf-7448-4cfd-91a1-6d9bc90e7146 
[INFO ] 2024-08-27 19:16:13.166 - [任务 2][Oracle] - PDK connector node released: HazelcastSourcePdkDataNode-04d9edaf-7448-4cfd-91a1-6d9bc90e7146 
[INFO ] 2024-08-27 19:16:13.168 - [任务 2][Oracle] - Node Oracle[04d9edaf-7448-4cfd-91a1-6d9bc90e7146] schema data cleaned 
[INFO ] 2024-08-27 19:16:13.169 - [任务 2][Oracle] - Node Oracle[04d9edaf-7448-4cfd-91a1-6d9bc90e7146] monitor closed 
[INFO ] 2024-08-27 19:16:13.180 - [任务 2][Oracle] - Node Oracle[04d9edaf-7448-4cfd-91a1-6d9bc90e7146] close complete, cost 111 ms 
[INFO ] 2024-08-27 19:16:13.181 - [任务 2][Mongo] - Node Mongo[2b74bbdf-70b1-4afc-bdd9-00cd292481ea] running status set to false 
[INFO ] 2024-08-27 19:16:13.221 - [任务 2][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-2b74bbdf-70b1-4afc-bdd9-00cd292481ea 
[INFO ] 2024-08-27 19:16:13.225 - [任务 2][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-2b74bbdf-70b1-4afc-bdd9-00cd292481ea 
[INFO ] 2024-08-27 19:16:13.225 - [任务 2][Mongo] - Node Mongo[2b74bbdf-70b1-4afc-bdd9-00cd292481ea] schema data cleaned 
[INFO ] 2024-08-27 19:16:13.226 - [任务 2][Mongo] - Node Mongo[2b74bbdf-70b1-4afc-bdd9-00cd292481ea] monitor closed 
[INFO ] 2024-08-27 19:16:13.226 - [任务 2][Mongo] - Node Mongo[2b74bbdf-70b1-4afc-bdd9-00cd292481ea] close complete, cost 49 ms 
[INFO ] 2024-08-27 19:16:16.884 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-27 19:16:16.884 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6061d7d4 
[INFO ] 2024-08-27 19:16:17.059 - [任务 2] - Stop task milestones: 66cd7c004d53ab4c5d533770(任务 2)  
[INFO ] 2024-08-27 19:16:17.060 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-08-27 19:16:17.061 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-08-27 19:16:17.158 - [任务 2] - Remove memory task client succeed, task: 任务 2[66cd7c004d53ab4c5d533770] 
[INFO ] 2024-08-27 19:16:17.158 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[66cd7c004d53ab4c5d533770] 
