[INFO ] 2024-03-29 18:38:24.524 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:24.527 - [suppliers(100)][cde43abc-a3f7-4791-92b8-b9775b27bc7c] - Node cde43abc-a3f7-4791-92b8-b9775b27bc7c[cde43abc-a3f7-4791-92b8-b9775b27bc7c] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:38:24.531 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:24.533 - [suppliers(100)][cde43abc-a3f7-4791-92b8-b9775b27bc7c] - Node cde43abc-a3f7-4791-92b8-b9775b27bc7c[cde43abc-a3f7-4791-92b8-b9775b27bc7c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:24.533 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:24.535 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:24.946 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:38:25.174 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@688f461 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@688f461 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@688f461 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:38:26.237 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:38:26.305 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:38:26.306 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:38:26.308 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:38:26.309 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:38:26.315 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 79 ms 
[INFO ] 2024-03-29 18:38:27.708 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:38:27.709 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:38:27.711 - [suppliers(100)][cde43abc-a3f7-4791-92b8-b9775b27bc7c] - Node cde43abc-a3f7-4791-92b8-b9775b27bc7c[cde43abc-a3f7-4791-92b8-b9775b27bc7c] running status set to false 
[INFO ] 2024-03-29 18:38:27.711 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:38:27.711 - [suppliers(100)][cde43abc-a3f7-4791-92b8-b9775b27bc7c] - Node cde43abc-a3f7-4791-92b8-b9775b27bc7c[cde43abc-a3f7-4791-92b8-b9775b27bc7c] schema data cleaned 
[INFO ] 2024-03-29 18:38:27.711 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 6 ms 
[INFO ] 2024-03-29 18:38:27.711 - [suppliers(100)][cde43abc-a3f7-4791-92b8-b9775b27bc7c] - Node cde43abc-a3f7-4791-92b8-b9775b27bc7c[cde43abc-a3f7-4791-92b8-b9775b27bc7c] monitor closed 
[INFO ] 2024-03-29 18:38:27.714 - [suppliers(100)][cde43abc-a3f7-4791-92b8-b9775b27bc7c] - Node cde43abc-a3f7-4791-92b8-b9775b27bc7c[cde43abc-a3f7-4791-92b8-b9775b27bc7c] close complete, cost 4 ms 
[INFO ] 2024-03-29 18:38:27.753 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-cde43abc-a3f7-4791-92b8-b9775b27bc7c complete, cost 4537ms 
[INFO ] 2024-03-29 18:39:13.595 - [suppliers(100)][9489fbfe-ef28-4598-95f4-b74f5895f8da] - Node 9489fbfe-ef28-4598-95f4-b74f5895f8da[9489fbfe-ef28-4598-95f4-b74f5895f8da] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:13.595 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:13.598 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:13.607 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:39:13.607 - [suppliers(100)][9489fbfe-ef28-4598-95f4-b74f5895f8da] - Node 9489fbfe-ef28-4598-95f4-b74f5895f8da[9489fbfe-ef28-4598-95f4-b74f5895f8da] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:13.609 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:13.745 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:13.751 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@53e34d74 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@53e34d74 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@53e34d74 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:13.770 - [suppliers(100)][1dd9938e-cb5c-48df-9842-4001148193b6] - Node 1dd9938e-cb5c-48df-9842-4001148193b6[1dd9938e-cb5c-48df-9842-4001148193b6] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:13.770 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:13.770 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:13.770 - [suppliers(100)][1dd9938e-cb5c-48df-9842-4001148193b6] - Node 1dd9938e-cb5c-48df-9842-4001148193b6[1dd9938e-cb5c-48df-9842-4001148193b6] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:13.770 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:13.770 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:13.785 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:13.786 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:13.786 - [suppliers(100)][71c415f1-47c8-494e-bf40-a8e25b78c9b7] - Node 71c415f1-47c8-494e-bf40-a8e25b78c9b7[71c415f1-47c8-494e-bf40-a8e25b78c9b7] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:13.787 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:13.787 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:13.787 - [suppliers(100)][71c415f1-47c8-494e-bf40-a8e25b78c9b7] - Node 71c415f1-47c8-494e-bf40-a8e25b78c9b7[71c415f1-47c8-494e-bf40-a8e25b78c9b7] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:13.799 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:39:13.799 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:13.824 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3c966ca8 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3c966ca8 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3c966ca8 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 18:39:13.825 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2573eaa2 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2573eaa2 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2573eaa2 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:13.950 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:13.963 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:13.963 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:13.963 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:13.964 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:13.964 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 54 ms 
[INFO ] 2024-03-29 18:39:14.488 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:14.524 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:14.524 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:14.525 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:14.525 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:14.635 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 39 ms 
[INFO ] 2024-03-29 18:39:14.637 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:14.637 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:14.637 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:14.638 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:14.638 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:14.638 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 39 ms 
[INFO ] 2024-03-29 18:39:14.653 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:14.653 - [suppliers(100)][fb2de2be-8bcd-4ac3-81e8-bc714de4d556] - Node fb2de2be-8bcd-4ac3-81e8-bc714de4d556[fb2de2be-8bcd-4ac3-81e8-bc714de4d556] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:14.653 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:14.653 - [suppliers(100)][fb2de2be-8bcd-4ac3-81e8-bc714de4d556] - Node fb2de2be-8bcd-4ac3-81e8-bc714de4d556[fb2de2be-8bcd-4ac3-81e8-bc714de4d556] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:14.653 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:14.653 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:14.777 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:14.778 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@36ed067d error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@36ed067d error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@36ed067d error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:14.964 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:14.965 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:14.965 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:14.965 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:14.965 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:14.966 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 23 ms 
[INFO ] 2024-03-29 18:39:15.674 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:15.675 - [suppliers(100)][40a6b357-8c8c-4e8b-942b-10093e9a1306] - Node 40a6b357-8c8c-4e8b-942b-10093e9a1306[40a6b357-8c8c-4e8b-942b-10093e9a1306] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:15.675 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:15.676 - [suppliers(100)][40a6b357-8c8c-4e8b-942b-10093e9a1306] - Node 40a6b357-8c8c-4e8b-942b-10093e9a1306[40a6b357-8c8c-4e8b-942b-10093e9a1306] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:15.676 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:15.676 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:39:15.728 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:15.910 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@54647a9 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@54647a9 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@54647a9 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:15.910 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:15.925 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:15.925 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:15.925 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:15.925 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:15.925 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 16 ms 
[INFO ] 2024-03-29 18:39:16.098 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:16.098 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:16.099 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:16.099 - [suppliers(100)][bcc516b9-5879-4459-8f11-7f9cdb314690] - Node bcc516b9-5879-4459-8f11-7f9cdb314690[bcc516b9-5879-4459-8f11-7f9cdb314690] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:16.099 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:16.099 - [suppliers(100)][bcc516b9-5879-4459-8f11-7f9cdb314690] - Node bcc516b9-5879-4459-8f11-7f9cdb314690[bcc516b9-5879-4459-8f11-7f9cdb314690] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:16.156 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:16.156 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@574fc251 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@574fc251 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@574fc251 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:16.295 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:16.295 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:16.302 - [suppliers(100)][9489fbfe-ef28-4598-95f4-b74f5895f8da] - Node 9489fbfe-ef28-4598-95f4-b74f5895f8da[9489fbfe-ef28-4598-95f4-b74f5895f8da] running status set to false 
[INFO ] 2024-03-29 18:39:16.302 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:16.303 - [suppliers(100)][9489fbfe-ef28-4598-95f4-b74f5895f8da] - Node 9489fbfe-ef28-4598-95f4-b74f5895f8da[9489fbfe-ef28-4598-95f4-b74f5895f8da] schema data cleaned 
[INFO ] 2024-03-29 18:39:16.303 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 6 ms 
[INFO ] 2024-03-29 18:39:16.303 - [suppliers(100)][9489fbfe-ef28-4598-95f4-b74f5895f8da] - Node 9489fbfe-ef28-4598-95f4-b74f5895f8da[9489fbfe-ef28-4598-95f4-b74f5895f8da] monitor closed 
[INFO ] 2024-03-29 18:39:16.304 - [suppliers(100)][9489fbfe-ef28-4598-95f4-b74f5895f8da] - Node 9489fbfe-ef28-4598-95f4-b74f5895f8da[9489fbfe-ef28-4598-95f4-b74f5895f8da] close complete, cost 8 ms 
[INFO ] 2024-03-29 18:39:16.305 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-9489fbfe-ef28-4598-95f4-b74f5895f8da complete, cost 2796ms 
[INFO ] 2024-03-29 18:39:16.334 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:16.334 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:16.334 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:16.334 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:16.334 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:16.335 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 18 ms 
[INFO ] 2024-03-29 18:39:16.371 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:16.373 - [suppliers(100)][1dd9938e-cb5c-48df-9842-4001148193b6] - Node 1dd9938e-cb5c-48df-9842-4001148193b6[1dd9938e-cb5c-48df-9842-4001148193b6] running status set to false 
[INFO ] 2024-03-29 18:39:16.373 - [suppliers(100)][71c415f1-47c8-494e-bf40-a8e25b78c9b7] - Node 71c415f1-47c8-494e-bf40-a8e25b78c9b7[71c415f1-47c8-494e-bf40-a8e25b78c9b7] running status set to false 
[INFO ] 2024-03-29 18:39:16.373 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:16.373 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:16.373 - [suppliers(100)][1dd9938e-cb5c-48df-9842-4001148193b6] - Node 1dd9938e-cb5c-48df-9842-4001148193b6[1dd9938e-cb5c-48df-9842-4001148193b6] schema data cleaned 
[INFO ] 2024-03-29 18:39:16.373 - [suppliers(100)][71c415f1-47c8-494e-bf40-a8e25b78c9b7] - Node 71c415f1-47c8-494e-bf40-a8e25b78c9b7[71c415f1-47c8-494e-bf40-a8e25b78c9b7] schema data cleaned 
[INFO ] 2024-03-29 18:39:16.373 - [suppliers(100)][71c415f1-47c8-494e-bf40-a8e25b78c9b7] - Node 71c415f1-47c8-494e-bf40-a8e25b78c9b7[71c415f1-47c8-494e-bf40-a8e25b78c9b7] monitor closed 
[INFO ] 2024-03-29 18:39:16.373 - [suppliers(100)][1dd9938e-cb5c-48df-9842-4001148193b6] - Node 1dd9938e-cb5c-48df-9842-4001148193b6[1dd9938e-cb5c-48df-9842-4001148193b6] monitor closed 
[INFO ] 2024-03-29 18:39:16.373 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:16.373 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:16.373 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:16.373 - [suppliers(100)][1dd9938e-cb5c-48df-9842-4001148193b6] - Node 1dd9938e-cb5c-48df-9842-4001148193b6[1dd9938e-cb5c-48df-9842-4001148193b6] close complete, cost 11 ms 
[INFO ] 2024-03-29 18:39:16.373 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 11 ms 
[INFO ] 2024-03-29 18:39:16.373 - [suppliers(100)][71c415f1-47c8-494e-bf40-a8e25b78c9b7] - Node 71c415f1-47c8-494e-bf40-a8e25b78c9b7[71c415f1-47c8-494e-bf40-a8e25b78c9b7] close complete, cost 6 ms 
[INFO ] 2024-03-29 18:39:16.373 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 6 ms 
[INFO ] 2024-03-29 18:39:16.582 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-71c415f1-47c8-494e-bf40-a8e25b78c9b7 complete, cost 2646ms 
[INFO ] 2024-03-29 18:39:16.582 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-1dd9938e-cb5c-48df-9842-4001148193b6 complete, cost 2678ms 
[INFO ] 2024-03-29 18:39:16.992 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:16.993 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:16.995 - [suppliers(100)][02084291-c769-4e82-90b9-a0d06f1eb57d] - Node 02084291-c769-4e82-90b9-a0d06f1eb57d[02084291-c769-4e82-90b9-a0d06f1eb57d] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:16.995 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:16.995 - [suppliers(100)][02084291-c769-4e82-90b9-a0d06f1eb57d] - Node 02084291-c769-4e82-90b9-a0d06f1eb57d[02084291-c769-4e82-90b9-a0d06f1eb57d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:16.995 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:17.063 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:39:17.114 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:17.114 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:17.118 - [suppliers(100)][7c8e2242-aba5-4414-bc93-67e6ab972be0] - Node 7c8e2242-aba5-4414-bc93-67e6ab972be0[7c8e2242-aba5-4414-bc93-67e6ab972be0] start preload schema,table counts: 0 
[ERROR] 2024-03-29 18:39:17.118 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5b3acc76 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5b3acc76 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5b3acc76 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:17.120 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:17.120 - [suppliers(100)][7c8e2242-aba5-4414-bc93-67e6ab972be0] - Node 7c8e2242-aba5-4414-bc93-67e6ab972be0[7c8e2242-aba5-4414-bc93-67e6ab972be0] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:17.120 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:17.167 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:17.168 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@55c6206e error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@55c6206e error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@55c6206e error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:17.370 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:17.375 - [suppliers(100)][fb2de2be-8bcd-4ac3-81e8-bc714de4d556] - Node fb2de2be-8bcd-4ac3-81e8-bc714de4d556[fb2de2be-8bcd-4ac3-81e8-bc714de4d556] running status set to false 
[INFO ] 2024-03-29 18:39:17.375 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:17.375 - [suppliers(100)][fb2de2be-8bcd-4ac3-81e8-bc714de4d556] - Node fb2de2be-8bcd-4ac3-81e8-bc714de4d556[fb2de2be-8bcd-4ac3-81e8-bc714de4d556] schema data cleaned 
[INFO ] 2024-03-29 18:39:17.376 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:17.376 - [suppliers(100)][fb2de2be-8bcd-4ac3-81e8-bc714de4d556] - Node fb2de2be-8bcd-4ac3-81e8-bc714de4d556[fb2de2be-8bcd-4ac3-81e8-bc714de4d556] monitor closed 
[INFO ] 2024-03-29 18:39:17.378 - [suppliers(100)][fb2de2be-8bcd-4ac3-81e8-bc714de4d556] - Node fb2de2be-8bcd-4ac3-81e8-bc714de4d556[fb2de2be-8bcd-4ac3-81e8-bc714de4d556] close complete, cost 49 ms 
[INFO ] 2024-03-29 18:39:17.378 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 57 ms 
[INFO ] 2024-03-29 18:39:17.385 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-fb2de2be-8bcd-4ac3-81e8-bc714de4d556 complete, cost 2836ms 
[INFO ] 2024-03-29 18:39:17.390 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:17.390 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:17.390 - [suppliers(100)][67b2456e-d9ba-42ac-ba1d-4ad6019be501] - Node 67b2456e-d9ba-42ac-ba1d-4ad6019be501[67b2456e-d9ba-42ac-ba1d-4ad6019be501] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:17.390 - [suppliers(100)][67b2456e-d9ba-42ac-ba1d-4ad6019be501] - Node 67b2456e-d9ba-42ac-ba1d-4ad6019be501[67b2456e-d9ba-42ac-ba1d-4ad6019be501] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:17.390 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:17.458 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:17.458 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:39:17.509 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:17.509 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:17.510 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:17.511 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:17.511 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:17.541 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 39 ms 
[ERROR] 2024-03-29 18:39:17.541 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6d872a9c error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6d872a9c error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6d872a9c error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:17.559 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:17.559 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:17.559 - [suppliers(100)][a135873d-8719-4a7b-8833-e21c424b695e] - Node a135873d-8719-4a7b-8833-e21c424b695e[a135873d-8719-4a7b-8833-e21c424b695e] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:17.559 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:17.559 - [suppliers(100)][a135873d-8719-4a7b-8833-e21c424b695e] - Node a135873d-8719-4a7b-8833-e21c424b695e[a135873d-8719-4a7b-8833-e21c424b695e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:17.559 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:17.613 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:17.619 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@70f13981 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@70f13981 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@70f13981 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:17.621 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:17.650 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:17.651 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:17.651 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:17.651 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:17.651 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 32 ms 
[INFO ] 2024-03-29 18:39:17.718 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:17.718 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:17.718 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:17.718 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:17.719 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:17.719 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 27 ms 
[INFO ] 2024-03-29 18:39:17.897 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:17.897 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:17.897 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:17.897 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:17.897 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:18.100 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 28 ms 
[INFO ] 2024-03-29 18:39:18.143 - [suppliers(100)][66991883-c167-4923-a756-e4c994d8ecd4] - Node 66991883-c167-4923-a756-e4c994d8ecd4[66991883-c167-4923-a756-e4c994d8ecd4] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:18.143 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:18.143 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:18.143 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:18.143 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:18.199 - [suppliers(100)][66991883-c167-4923-a756-e4c994d8ecd4] - Node 66991883-c167-4923-a756-e4c994d8ecd4[66991883-c167-4923-a756-e4c994d8ecd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:18.199 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:18.359 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4e72f177 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4e72f177 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4e72f177 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:18.360 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:18.360 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:18.360 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:18.360 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:39:18.362 - [suppliers(100)][40a6b357-8c8c-4e8b-942b-10093e9a1306] - Node 40a6b357-8c8c-4e8b-942b-10093e9a1306[40a6b357-8c8c-4e8b-942b-10093e9a1306] running status set to false 
[INFO ] 2024-03-29 18:39:18.362 - [suppliers(100)][40a6b357-8c8c-4e8b-942b-10093e9a1306] - Node 40a6b357-8c8c-4e8b-942b-10093e9a1306[40a6b357-8c8c-4e8b-942b-10093e9a1306] schema data cleaned 
[INFO ] 2024-03-29 18:39:18.362 - [suppliers(100)][40a6b357-8c8c-4e8b-942b-10093e9a1306] - Node 40a6b357-8c8c-4e8b-942b-10093e9a1306[40a6b357-8c8c-4e8b-942b-10093e9a1306] monitor closed 
[INFO ] 2024-03-29 18:39:18.362 - [suppliers(100)][40a6b357-8c8c-4e8b-942b-10093e9a1306] - Node 40a6b357-8c8c-4e8b-942b-10093e9a1306[40a6b357-8c8c-4e8b-942b-10093e9a1306] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:39:18.377 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-40a6b357-8c8c-4e8b-942b-10093e9a1306 complete, cost 2766ms 
[INFO ] 2024-03-29 18:39:18.377 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:18.396 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:18.396 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:18.397 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:18.397 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:18.603 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 21 ms 
[INFO ] 2024-03-29 18:39:18.689 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:18.691 - [suppliers(100)][bcc516b9-5879-4459-8f11-7f9cdb314690] - Node bcc516b9-5879-4459-8f11-7f9cdb314690[bcc516b9-5879-4459-8f11-7f9cdb314690] running status set to false 
[INFO ] 2024-03-29 18:39:18.691 - [suppliers(100)][bcc516b9-5879-4459-8f11-7f9cdb314690] - Node bcc516b9-5879-4459-8f11-7f9cdb314690[bcc516b9-5879-4459-8f11-7f9cdb314690] schema data cleaned 
[INFO ] 2024-03-29 18:39:18.697 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:18.697 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:18.697 - [suppliers(100)][bcc516b9-5879-4459-8f11-7f9cdb314690] - Node bcc516b9-5879-4459-8f11-7f9cdb314690[bcc516b9-5879-4459-8f11-7f9cdb314690] monitor closed 
[INFO ] 2024-03-29 18:39:18.697 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 9 ms 
[INFO ] 2024-03-29 18:39:18.697 - [suppliers(100)][bcc516b9-5879-4459-8f11-7f9cdb314690] - Node bcc516b9-5879-4459-8f11-7f9cdb314690[bcc516b9-5879-4459-8f11-7f9cdb314690] close complete, cost 6 ms 
[INFO ] 2024-03-29 18:39:18.697 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-bcc516b9-5879-4459-8f11-7f9cdb314690 complete, cost 2631ms 
[INFO ] 2024-03-29 18:39:18.871 - [suppliers(100)][bf3ac110-ea59-4500-8cd5-b06e25b3587d] - Node bf3ac110-ea59-4500-8cd5-b06e25b3587d[bf3ac110-ea59-4500-8cd5-b06e25b3587d] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:18.871 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:18.871 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:18.872 - [suppliers(100)][bf3ac110-ea59-4500-8cd5-b06e25b3587d] - Node bf3ac110-ea59-4500-8cd5-b06e25b3587d[bf3ac110-ea59-4500-8cd5-b06e25b3587d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:39:18.872 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:39:18.872 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 2 ms 
[INFO ] 2024-03-29 18:39:18.954 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:39:18.970 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:18.970 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:18.970 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:18.970 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:18.972 - [suppliers(100)][af84f9b3-f840-4fb4-9831-5bbafbefc69a] - Node af84f9b3-f840-4fb4-9831-5bbafbefc69a[af84f9b3-f840-4fb4-9831-5bbafbefc69a] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:18.972 - [suppliers(100)][af84f9b3-f840-4fb4-9831-5bbafbefc69a] - Node af84f9b3-f840-4fb4-9831-5bbafbefc69a[af84f9b3-f840-4fb4-9831-5bbafbefc69a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:18.984 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:18.986 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5a6814b3 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5a6814b3 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5a6814b3 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 18:39:19.150 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ed02eda error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ed02eda error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ed02eda error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:19.150 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:19.175 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:19.175 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:19.175 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:19.176 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:19.176 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 27 ms 
[INFO ] 2024-03-29 18:39:19.384 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:19.407 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:19.408 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:19.409 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:19.409 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:19.410 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 46 ms 
[INFO ] 2024-03-29 18:39:19.685 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:19.687 - [suppliers(100)][02084291-c769-4e82-90b9-a0d06f1eb57d] - Node 02084291-c769-4e82-90b9-a0d06f1eb57d[02084291-c769-4e82-90b9-a0d06f1eb57d] running status set to false 
[INFO ] 2024-03-29 18:39:19.687 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:19.687 - [suppliers(100)][02084291-c769-4e82-90b9-a0d06f1eb57d] - Node 02084291-c769-4e82-90b9-a0d06f1eb57d[02084291-c769-4e82-90b9-a0d06f1eb57d] schema data cleaned 
[INFO ] 2024-03-29 18:39:19.687 - [suppliers(100)][02084291-c769-4e82-90b9-a0d06f1eb57d] - Node 02084291-c769-4e82-90b9-a0d06f1eb57d[02084291-c769-4e82-90b9-a0d06f1eb57d] monitor closed 
[INFO ] 2024-03-29 18:39:19.687 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:19.688 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:19.688 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:19.688 - [suppliers(100)][7c8e2242-aba5-4414-bc93-67e6ab972be0] - Node 7c8e2242-aba5-4414-bc93-67e6ab972be0[7c8e2242-aba5-4414-bc93-67e6ab972be0] running status set to false 
[INFO ] 2024-03-29 18:39:19.688 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:19.688 - [suppliers(100)][7c8e2242-aba5-4414-bc93-67e6ab972be0] - Node 7c8e2242-aba5-4414-bc93-67e6ab972be0[7c8e2242-aba5-4414-bc93-67e6ab972be0] schema data cleaned 
[INFO ] 2024-03-29 18:39:19.688 - [suppliers(100)][7c8e2242-aba5-4414-bc93-67e6ab972be0] - Node 7c8e2242-aba5-4414-bc93-67e6ab972be0[7c8e2242-aba5-4414-bc93-67e6ab972be0] monitor closed 
[INFO ] 2024-03-29 18:39:19.688 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 10 ms 
[INFO ] 2024-03-29 18:39:19.688 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 4 ms 
[INFO ] 2024-03-29 18:39:19.688 - [suppliers(100)][02084291-c769-4e82-90b9-a0d06f1eb57d] - Node 02084291-c769-4e82-90b9-a0d06f1eb57d[02084291-c769-4e82-90b9-a0d06f1eb57d] close complete, cost 6 ms 
[INFO ] 2024-03-29 18:39:19.688 - [suppliers(100)][7c8e2242-aba5-4414-bc93-67e6ab972be0] - Node 7c8e2242-aba5-4414-bc93-67e6ab972be0[7c8e2242-aba5-4414-bc93-67e6ab972be0] close complete, cost 2 ms 
[INFO ] 2024-03-29 18:39:19.691 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-02084291-c769-4e82-90b9-a0d06f1eb57d complete, cost 2775ms 
[INFO ] 2024-03-29 18:39:19.691 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-7c8e2242-aba5-4414-bc93-67e6ab972be0 complete, cost 2648ms 
[INFO ] 2024-03-29 18:39:20.063 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:20.063 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:20.063 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:20.063 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:39:20.065 - [suppliers(100)][67b2456e-d9ba-42ac-ba1d-4ad6019be501] - Node 67b2456e-d9ba-42ac-ba1d-4ad6019be501[67b2456e-d9ba-42ac-ba1d-4ad6019be501] running status set to false 
[INFO ] 2024-03-29 18:39:20.066 - [suppliers(100)][67b2456e-d9ba-42ac-ba1d-4ad6019be501] - Node 67b2456e-d9ba-42ac-ba1d-4ad6019be501[67b2456e-d9ba-42ac-ba1d-4ad6019be501] schema data cleaned 
[INFO ] 2024-03-29 18:39:20.066 - [suppliers(100)][67b2456e-d9ba-42ac-ba1d-4ad6019be501] - Node 67b2456e-d9ba-42ac-ba1d-4ad6019be501[67b2456e-d9ba-42ac-ba1d-4ad6019be501] monitor closed 
[INFO ] 2024-03-29 18:39:20.067 - [suppliers(100)][67b2456e-d9ba-42ac-ba1d-4ad6019be501] - Node 67b2456e-d9ba-42ac-ba1d-4ad6019be501[67b2456e-d9ba-42ac-ba1d-4ad6019be501] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:39:20.067 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-67b2456e-d9ba-42ac-ba1d-4ad6019be501 complete, cost 2919ms 
[INFO ] 2024-03-29 18:39:20.138 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:20.138 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:20.138 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:20.138 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:39:20.142 - [suppliers(100)][a135873d-8719-4a7b-8833-e21c424b695e] - Node a135873d-8719-4a7b-8833-e21c424b695e[a135873d-8719-4a7b-8833-e21c424b695e] running status set to false 
[INFO ] 2024-03-29 18:39:20.142 - [suppliers(100)][a135873d-8719-4a7b-8833-e21c424b695e] - Node a135873d-8719-4a7b-8833-e21c424b695e[a135873d-8719-4a7b-8833-e21c424b695e] schema data cleaned 
[INFO ] 2024-03-29 18:39:20.142 - [suppliers(100)][a135873d-8719-4a7b-8833-e21c424b695e] - Node a135873d-8719-4a7b-8833-e21c424b695e[a135873d-8719-4a7b-8833-e21c424b695e] monitor closed 
[INFO ] 2024-03-29 18:39:20.142 - [suppliers(100)][a135873d-8719-4a7b-8833-e21c424b695e] - Node a135873d-8719-4a7b-8833-e21c424b695e[a135873d-8719-4a7b-8833-e21c424b695e] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:39:20.345 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-a135873d-8719-4a7b-8833-e21c424b695e complete, cost 2682ms 
[INFO ] 2024-03-29 18:39:20.629 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:20.630 - [suppliers(100)][93cd36e6-a5d2-4b42-8364-d2d63a783e55] - Node 93cd36e6-a5d2-4b42-8364-d2d63a783e55[93cd36e6-a5d2-4b42-8364-d2d63a783e55] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:20.630 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:20.631 - [suppliers(100)][93cd36e6-a5d2-4b42-8364-d2d63a783e55] - Node 93cd36e6-a5d2-4b42-8364-d2d63a783e55[93cd36e6-a5d2-4b42-8364-d2d63a783e55] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:20.631 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:20.631 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:20.651 - [suppliers(100)][fd1b71c0-777c-4e44-97dd-a7652b9c377b] - Node fd1b71c0-777c-4e44-97dd-a7652b9c377b[fd1b71c0-777c-4e44-97dd-a7652b9c377b] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:20.651 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:20.651 - [suppliers(100)][fd1b71c0-777c-4e44-97dd-a7652b9c377b] - Node fd1b71c0-777c-4e44-97dd-a7652b9c377b[fd1b71c0-777c-4e44-97dd-a7652b9c377b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:20.652 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:20.652 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:20.652 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:20.699 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:39:20.699 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:20.748 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4f0b4244 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4f0b4244 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4f0b4244 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 18:39:20.749 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@8ddfcbc error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@8ddfcbc error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@8ddfcbc error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:20.763 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:20.763 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:20.763 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:20.764 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:39:20.764 - [suppliers(100)][66991883-c167-4923-a756-e4c994d8ecd4] - Node 66991883-c167-4923-a756-e4c994d8ecd4[66991883-c167-4923-a756-e4c994d8ecd4] running status set to false 
[INFO ] 2024-03-29 18:39:20.764 - [suppliers(100)][66991883-c167-4923-a756-e4c994d8ecd4] - Node 66991883-c167-4923-a756-e4c994d8ecd4[66991883-c167-4923-a756-e4c994d8ecd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:20.764 - [suppliers(100)][66991883-c167-4923-a756-e4c994d8ecd4] - Node 66991883-c167-4923-a756-e4c994d8ecd4[66991883-c167-4923-a756-e4c994d8ecd4] monitor closed 
[INFO ] 2024-03-29 18:39:20.764 - [suppliers(100)][66991883-c167-4923-a756-e4c994d8ecd4] - Node 66991883-c167-4923-a756-e4c994d8ecd4[66991883-c167-4923-a756-e4c994d8ecd4] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:39:20.859 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-66991883-c167-4923-a756-e4c994d8ecd4 complete, cost 2680ms 
[INFO ] 2024-03-29 18:39:20.859 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:20.859 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:20.859 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:20.859 - [suppliers(100)][6f08ee26-d468-4495-b534-1e0a2b64d4bb] - Node 6f08ee26-d468-4495-b534-1e0a2b64d4bb[6f08ee26-d468-4495-b534-1e0a2b64d4bb] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:20.859 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:20.885 - [suppliers(100)][6f08ee26-d468-4495-b534-1e0a2b64d4bb] - Node 6f08ee26-d468-4495-b534-1e0a2b64d4bb[6f08ee26-d468-4495-b534-1e0a2b64d4bb] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:20.885 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:39:20.885 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:20.912 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:20.912 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:20.913 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:20.913 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:20.932 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 30 ms 
[ERROR] 2024-03-29 18:39:20.932 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@46676bcf error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@46676bcf error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@46676bcf error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:21.089 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:21.089 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:21.089 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:21.089 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:21.089 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:21.089 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 14 ms 
[INFO ] 2024-03-29 18:39:21.147 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:21.147 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:21.148 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:21.148 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:21.148 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:21.148 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 23 ms 
[INFO ] 2024-03-29 18:39:21.515 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:21.516 - [suppliers(100)][af84f9b3-f840-4fb4-9831-5bbafbefc69a] - Node af84f9b3-f840-4fb4-9831-5bbafbefc69a[af84f9b3-f840-4fb4-9831-5bbafbefc69a] running status set to false 
[INFO ] 2024-03-29 18:39:21.516 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:21.516 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:21.516 - [suppliers(100)][af84f9b3-f840-4fb4-9831-5bbafbefc69a] - Node af84f9b3-f840-4fb4-9831-5bbafbefc69a[af84f9b3-f840-4fb4-9831-5bbafbefc69a] schema data cleaned 
[INFO ] 2024-03-29 18:39:21.516 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:39:21.516 - [suppliers(100)][af84f9b3-f840-4fb4-9831-5bbafbefc69a] - Node af84f9b3-f840-4fb4-9831-5bbafbefc69a[af84f9b3-f840-4fb4-9831-5bbafbefc69a] monitor closed 
[INFO ] 2024-03-29 18:39:21.516 - [suppliers(100)][af84f9b3-f840-4fb4-9831-5bbafbefc69a] - Node af84f9b3-f840-4fb4-9831-5bbafbefc69a[af84f9b3-f840-4fb4-9831-5bbafbefc69a] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:39:21.517 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-af84f9b3-f840-4fb4-9831-5bbafbefc69a complete, cost 2591ms 
[INFO ] 2024-03-29 18:39:21.517 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:21.517 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:21.517 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:21.521 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:39:21.521 - [suppliers(100)][bf3ac110-ea59-4500-8cd5-b06e25b3587d] - Node bf3ac110-ea59-4500-8cd5-b06e25b3587d[bf3ac110-ea59-4500-8cd5-b06e25b3587d] running status set to false 
[INFO ] 2024-03-29 18:39:21.521 - [suppliers(100)][bf3ac110-ea59-4500-8cd5-b06e25b3587d] - Node bf3ac110-ea59-4500-8cd5-b06e25b3587d[bf3ac110-ea59-4500-8cd5-b06e25b3587d] schema data cleaned 
[INFO ] 2024-03-29 18:39:21.521 - [suppliers(100)][bf3ac110-ea59-4500-8cd5-b06e25b3587d] - Node bf3ac110-ea59-4500-8cd5-b06e25b3587d[bf3ac110-ea59-4500-8cd5-b06e25b3587d] monitor closed 
[INFO ] 2024-03-29 18:39:21.522 - [suppliers(100)][bf3ac110-ea59-4500-8cd5-b06e25b3587d] - Node bf3ac110-ea59-4500-8cd5-b06e25b3587d[bf3ac110-ea59-4500-8cd5-b06e25b3587d] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:39:21.522 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-bf3ac110-ea59-4500-8cd5-b06e25b3587d complete, cost 2753ms 
[INFO ] 2024-03-29 18:39:22.076 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:22.077 - [suppliers(100)][6862ff66-23d3-4069-a4fa-7b6a5f871eaf] - Node 6862ff66-23d3-4069-a4fa-7b6a5f871eaf[6862ff66-23d3-4069-a4fa-7b6a5f871eaf] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:22.077 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:22.077 - [suppliers(100)][6862ff66-23d3-4069-a4fa-7b6a5f871eaf] - Node 6862ff66-23d3-4069-a4fa-7b6a5f871eaf[6862ff66-23d3-4069-a4fa-7b6a5f871eaf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:22.077 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:22.077 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:39:22.152 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:22.153 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3a7f24ef error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3a7f24ef error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3a7f24ef error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:22.319 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:22.319 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:22.319 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:22.320 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:22.320 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:22.320 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 11 ms 
[INFO ] 2024-03-29 18:39:23.290 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:23.291 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:23.291 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:23.291 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:23.291 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:23.291 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:23.291 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 9 ms 
[INFO ] 2024-03-29 18:39:23.291 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 9 ms 
[INFO ] 2024-03-29 18:39:23.292 - [suppliers(100)][fd1b71c0-777c-4e44-97dd-a7652b9c377b] - Node fd1b71c0-777c-4e44-97dd-a7652b9c377b[fd1b71c0-777c-4e44-97dd-a7652b9c377b] running status set to false 
[INFO ] 2024-03-29 18:39:23.292 - [suppliers(100)][fd1b71c0-777c-4e44-97dd-a7652b9c377b] - Node fd1b71c0-777c-4e44-97dd-a7652b9c377b[fd1b71c0-777c-4e44-97dd-a7652b9c377b] schema data cleaned 
[INFO ] 2024-03-29 18:39:23.292 - [suppliers(100)][fd1b71c0-777c-4e44-97dd-a7652b9c377b] - Node fd1b71c0-777c-4e44-97dd-a7652b9c377b[fd1b71c0-777c-4e44-97dd-a7652b9c377b] monitor closed 
[INFO ] 2024-03-29 18:39:23.292 - [suppliers(100)][93cd36e6-a5d2-4b42-8364-d2d63a783e55] - Node 93cd36e6-a5d2-4b42-8364-d2d63a783e55[93cd36e6-a5d2-4b42-8364-d2d63a783e55] running status set to false 
[INFO ] 2024-03-29 18:39:23.292 - [suppliers(100)][fd1b71c0-777c-4e44-97dd-a7652b9c377b] - Node fd1b71c0-777c-4e44-97dd-a7652b9c377b[fd1b71c0-777c-4e44-97dd-a7652b9c377b] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:39:23.292 - [suppliers(100)][93cd36e6-a5d2-4b42-8364-d2d63a783e55] - Node 93cd36e6-a5d2-4b42-8364-d2d63a783e55[93cd36e6-a5d2-4b42-8364-d2d63a783e55] schema data cleaned 
[INFO ] 2024-03-29 18:39:23.292 - [suppliers(100)][93cd36e6-a5d2-4b42-8364-d2d63a783e55] - Node 93cd36e6-a5d2-4b42-8364-d2d63a783e55[93cd36e6-a5d2-4b42-8364-d2d63a783e55] monitor closed 
[INFO ] 2024-03-29 18:39:23.292 - [suppliers(100)][93cd36e6-a5d2-4b42-8364-d2d63a783e55] - Node 93cd36e6-a5d2-4b42-8364-d2d63a783e55[93cd36e6-a5d2-4b42-8364-d2d63a783e55] close complete, cost 2 ms 
[INFO ] 2024-03-29 18:39:23.292 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-93cd36e6-a5d2-4b42-8364-d2d63a783e55 complete, cost 2741ms 
[INFO ] 2024-03-29 18:39:23.293 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-fd1b71c0-777c-4e44-97dd-a7652b9c377b complete, cost 2685ms 
[INFO ] 2024-03-29 18:39:23.458 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:23.458 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:23.458 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:23.458 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:39:23.460 - [suppliers(100)][6f08ee26-d468-4495-b534-1e0a2b64d4bb] - Node 6f08ee26-d468-4495-b534-1e0a2b64d4bb[6f08ee26-d468-4495-b534-1e0a2b64d4bb] running status set to false 
[INFO ] 2024-03-29 18:39:23.460 - [suppliers(100)][6f08ee26-d468-4495-b534-1e0a2b64d4bb] - Node 6f08ee26-d468-4495-b534-1e0a2b64d4bb[6f08ee26-d468-4495-b534-1e0a2b64d4bb] schema data cleaned 
[INFO ] 2024-03-29 18:39:23.460 - [suppliers(100)][6f08ee26-d468-4495-b534-1e0a2b64d4bb] - Node 6f08ee26-d468-4495-b534-1e0a2b64d4bb[6f08ee26-d468-4495-b534-1e0a2b64d4bb] monitor closed 
[INFO ] 2024-03-29 18:39:23.460 - [suppliers(100)][6f08ee26-d468-4495-b534-1e0a2b64d4bb] - Node 6f08ee26-d468-4495-b534-1e0a2b64d4bb[6f08ee26-d468-4495-b534-1e0a2b64d4bb] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:39:23.662 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-6f08ee26-d468-4495-b534-1e0a2b64d4bb complete, cost 2656ms 
[INFO ] 2024-03-29 18:39:24.570 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:24.571 - [suppliers(100)][6330d072-6cb8-419e-a8eb-3e32f0332f06] - Node 6330d072-6cb8-419e-a8eb-3e32f0332f06[6330d072-6cb8-419e-a8eb-3e32f0332f06] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:24.571 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:24.571 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:39:24.571 - [suppliers(100)][6330d072-6cb8-419e-a8eb-3e32f0332f06] - Node 6330d072-6cb8-419e-a8eb-3e32f0332f06[6330d072-6cb8-419e-a8eb-3e32f0332f06] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:24.571 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:24.624 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:24.691 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3b3d92ba error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3b3d92ba error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3b3d92ba error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:24.695 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:24.695 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:24.695 - [suppliers(100)][6862ff66-23d3-4069-a4fa-7b6a5f871eaf] - Node 6862ff66-23d3-4069-a4fa-7b6a5f871eaf[6862ff66-23d3-4069-a4fa-7b6a5f871eaf] running status set to false 
[INFO ] 2024-03-29 18:39:24.695 - [suppliers(100)][6862ff66-23d3-4069-a4fa-7b6a5f871eaf] - Node 6862ff66-23d3-4069-a4fa-7b6a5f871eaf[6862ff66-23d3-4069-a4fa-7b6a5f871eaf] schema data cleaned 
[INFO ] 2024-03-29 18:39:24.695 - [suppliers(100)][6862ff66-23d3-4069-a4fa-7b6a5f871eaf] - Node 6862ff66-23d3-4069-a4fa-7b6a5f871eaf[6862ff66-23d3-4069-a4fa-7b6a5f871eaf] monitor closed 
[INFO ] 2024-03-29 18:39:24.695 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:24.695 - [suppliers(100)][6862ff66-23d3-4069-a4fa-7b6a5f871eaf] - Node 6862ff66-23d3-4069-a4fa-7b6a5f871eaf[6862ff66-23d3-4069-a4fa-7b6a5f871eaf] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:39:24.695 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 6 ms 
[INFO ] 2024-03-29 18:39:24.800 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-6862ff66-23d3-4069-a4fa-7b6a5f871eaf complete, cost 2726ms 
[INFO ] 2024-03-29 18:39:24.800 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:24.811 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:24.811 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:24.812 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:24.812 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:25.017 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 13 ms 
[INFO ] 2024-03-29 18:39:27.187 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:27.188 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:27.188 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:27.193 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 2 ms 
[INFO ] 2024-03-29 18:39:27.193 - [suppliers(100)][6330d072-6cb8-419e-a8eb-3e32f0332f06] - Node 6330d072-6cb8-419e-a8eb-3e32f0332f06[6330d072-6cb8-419e-a8eb-3e32f0332f06] running status set to false 
[INFO ] 2024-03-29 18:39:27.193 - [suppliers(100)][6330d072-6cb8-419e-a8eb-3e32f0332f06] - Node 6330d072-6cb8-419e-a8eb-3e32f0332f06[6330d072-6cb8-419e-a8eb-3e32f0332f06] schema data cleaned 
[INFO ] 2024-03-29 18:39:27.193 - [suppliers(100)][6330d072-6cb8-419e-a8eb-3e32f0332f06] - Node 6330d072-6cb8-419e-a8eb-3e32f0332f06[6330d072-6cb8-419e-a8eb-3e32f0332f06] monitor closed 
[INFO ] 2024-03-29 18:39:27.193 - [suppliers(100)][6330d072-6cb8-419e-a8eb-3e32f0332f06] - Node 6330d072-6cb8-419e-a8eb-3e32f0332f06[6330d072-6cb8-419e-a8eb-3e32f0332f06] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:39:27.397 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-6330d072-6cb8-419e-a8eb-3e32f0332f06 complete, cost 2722ms 
[INFO ] 2024-03-29 18:39:27.950 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:27.950 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:27.950 - [suppliers(100)][4ae8a7de-3d36-40aa-a5df-5d330307eb0b] - Node 4ae8a7de-3d36-40aa-a5df-5d330307eb0b[4ae8a7de-3d36-40aa-a5df-5d330307eb0b] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:27.950 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:27.950 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:27.950 - [suppliers(100)][4ae8a7de-3d36-40aa-a5df-5d330307eb0b] - Node 4ae8a7de-3d36-40aa-a5df-5d330307eb0b[4ae8a7de-3d36-40aa-a5df-5d330307eb0b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:27.987 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:27.987 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@179202de error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@179202de error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@179202de error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:28.170 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:28.170 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:28.170 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:28.171 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:28.171 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:28.373 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 9 ms 
[INFO ] 2024-03-29 18:39:29.558 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:29.559 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:29.559 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:29.559 - [suppliers(100)][a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70] - Node a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70[a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:29.559 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:29.559 - [suppliers(100)][a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70] - Node a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70[a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:29.593 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:29.593 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@22a8388 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@22a8388 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@22a8388 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:29.773 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:29.773 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:29.774 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:29.774 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:29.774 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:29.975 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 8 ms 
[INFO ] 2024-03-29 18:39:30.519 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:30.520 - [suppliers(100)][4ae8a7de-3d36-40aa-a5df-5d330307eb0b] - Node 4ae8a7de-3d36-40aa-a5df-5d330307eb0b[4ae8a7de-3d36-40aa-a5df-5d330307eb0b] running status set to false 
[INFO ] 2024-03-29 18:39:30.520 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:30.520 - [suppliers(100)][4ae8a7de-3d36-40aa-a5df-5d330307eb0b] - Node 4ae8a7de-3d36-40aa-a5df-5d330307eb0b[4ae8a7de-3d36-40aa-a5df-5d330307eb0b] schema data cleaned 
[INFO ] 2024-03-29 18:39:30.520 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:30.521 - [suppliers(100)][4ae8a7de-3d36-40aa-a5df-5d330307eb0b] - Node 4ae8a7de-3d36-40aa-a5df-5d330307eb0b[4ae8a7de-3d36-40aa-a5df-5d330307eb0b] monitor closed 
[INFO ] 2024-03-29 18:39:30.522 - [suppliers(100)][4ae8a7de-3d36-40aa-a5df-5d330307eb0b] - Node 4ae8a7de-3d36-40aa-a5df-5d330307eb0b[4ae8a7de-3d36-40aa-a5df-5d330307eb0b] close complete, cost 3 ms 
[INFO ] 2024-03-29 18:39:30.522 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 10 ms 
[INFO ] 2024-03-29 18:39:30.725 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-4ae8a7de-3d36-40aa-a5df-5d330307eb0b complete, cost 2608ms 
[INFO ] 2024-03-29 18:39:32.147 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:32.151 - [suppliers(100)][a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70] - Node a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70[a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70] running status set to false 
[INFO ] 2024-03-29 18:39:32.151 - [suppliers(100)][a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70] - Node a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70[a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70] schema data cleaned 
[INFO ] 2024-03-29 18:39:32.151 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:32.151 - [suppliers(100)][a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70] - Node a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70[a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70] monitor closed 
[INFO ] 2024-03-29 18:39:32.151 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:32.151 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 22 ms 
[INFO ] 2024-03-29 18:39:32.151 - [suppliers(100)][a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70] - Node a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70[a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70] close complete, cost 20 ms 
[INFO ] 2024-03-29 18:39:32.356 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-a50446e7-1ccc-4ea3-80e9-2e4fc39f1c70 complete, cost 2624ms 
[INFO ] 2024-03-29 18:39:33.376 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:33.377 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:33.377 - [suppliers(100)][ff0c1462-5c9d-4d5a-a241-922a6586fca7] - Node ff0c1462-5c9d-4d5a-a241-922a6586fca7[ff0c1462-5c9d-4d5a-a241-922a6586fca7] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:33.377 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:33.377 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:33.378 - [suppliers(100)][ff0c1462-5c9d-4d5a-a241-922a6586fca7] - Node ff0c1462-5c9d-4d5a-a241-922a6586fca7[ff0c1462-5c9d-4d5a-a241-922a6586fca7] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:33.459 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:33.673 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@545897b2 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@545897b2 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@545897b2 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:33.674 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:33.687 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:33.687 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:33.688 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:33.688 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:33.688 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 28 ms 
[INFO ] 2024-03-29 18:39:35.394 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:35.394 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:35.395 - [suppliers(100)][61735c01-3be4-4c48-ae35-9fc7263a0eb6] - Node 61735c01-3be4-4c48-ae35-9fc7263a0eb6[61735c01-3be4-4c48-ae35-9fc7263a0eb6] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:35.395 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:35.395 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:35.395 - [suppliers(100)][61735c01-3be4-4c48-ae35-9fc7263a0eb6] - Node 61735c01-3be4-4c48-ae35-9fc7263a0eb6[61735c01-3be4-4c48-ae35-9fc7263a0eb6] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:35.436 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:35.607 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1d76c783 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1d76c783 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1d76c783 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:35.607 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:35.614 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:35.614 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:35.615 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:35.615 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:35.820 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 7 ms 
[INFO ] 2024-03-29 18:39:36.022 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:36.022 - [suppliers(100)][ff0c1462-5c9d-4d5a-a241-922a6586fca7] - Node ff0c1462-5c9d-4d5a-a241-922a6586fca7[ff0c1462-5c9d-4d5a-a241-922a6586fca7] running status set to false 
[INFO ] 2024-03-29 18:39:36.022 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:36.022 - [suppliers(100)][ff0c1462-5c9d-4d5a-a241-922a6586fca7] - Node ff0c1462-5c9d-4d5a-a241-922a6586fca7[ff0c1462-5c9d-4d5a-a241-922a6586fca7] schema data cleaned 
[INFO ] 2024-03-29 18:39:36.022 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:36.022 - [suppliers(100)][ff0c1462-5c9d-4d5a-a241-922a6586fca7] - Node ff0c1462-5c9d-4d5a-a241-922a6586fca7[ff0c1462-5c9d-4d5a-a241-922a6586fca7] monitor closed 
[INFO ] 2024-03-29 18:39:36.022 - [suppliers(100)][ff0c1462-5c9d-4d5a-a241-922a6586fca7] - Node ff0c1462-5c9d-4d5a-a241-922a6586fca7[ff0c1462-5c9d-4d5a-a241-922a6586fca7] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:39:36.022 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:39:36.023 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-ff0c1462-5c9d-4d5a-a241-922a6586fca7 complete, cost 2705ms 
[INFO ] 2024-03-29 18:39:37.982 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:37.982 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:37.982 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:37.982 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:39:37.986 - [suppliers(100)][61735c01-3be4-4c48-ae35-9fc7263a0eb6] - Node 61735c01-3be4-4c48-ae35-9fc7263a0eb6[61735c01-3be4-4c48-ae35-9fc7263a0eb6] running status set to false 
[INFO ] 2024-03-29 18:39:37.986 - [suppliers(100)][61735c01-3be4-4c48-ae35-9fc7263a0eb6] - Node 61735c01-3be4-4c48-ae35-9fc7263a0eb6[61735c01-3be4-4c48-ae35-9fc7263a0eb6] schema data cleaned 
[INFO ] 2024-03-29 18:39:37.986 - [suppliers(100)][61735c01-3be4-4c48-ae35-9fc7263a0eb6] - Node 61735c01-3be4-4c48-ae35-9fc7263a0eb6[61735c01-3be4-4c48-ae35-9fc7263a0eb6] monitor closed 
[INFO ] 2024-03-29 18:39:37.987 - [suppliers(100)][61735c01-3be4-4c48-ae35-9fc7263a0eb6] - Node 61735c01-3be4-4c48-ae35-9fc7263a0eb6[61735c01-3be4-4c48-ae35-9fc7263a0eb6] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:39:38.191 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-61735c01-3be4-4c48-ae35-9fc7263a0eb6 complete, cost 2638ms 
[INFO ] 2024-03-29 18:39:38.196 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:38.196 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:38.196 - [suppliers(100)][f5c60b36-c9ac-45e3-ac0d-beab65820463] - Node f5c60b36-c9ac-45e3-ac0d-beab65820463[f5c60b36-c9ac-45e3-ac0d-beab65820463] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:38.196 - [suppliers(100)][f5c60b36-c9ac-45e3-ac0d-beab65820463] - Node f5c60b36-c9ac-45e3-ac0d-beab65820463[f5c60b36-c9ac-45e3-ac0d-beab65820463] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:38.196 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:38.196 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:38.227 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:38.227 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7eccea90 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7eccea90 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7eccea90 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:38.408 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:38.408 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:38.408 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:38.409 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:38.409 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:38.611 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 8 ms 
[INFO ] 2024-03-29 18:39:40.795 - [suppliers(100)][f5c60b36-c9ac-45e3-ac0d-beab65820463] - Node f5c60b36-c9ac-45e3-ac0d-beab65820463[f5c60b36-c9ac-45e3-ac0d-beab65820463] running status set to false 
[INFO ] 2024-03-29 18:39:40.800 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:40.800 - [suppliers(100)][f5c60b36-c9ac-45e3-ac0d-beab65820463] - Node f5c60b36-c9ac-45e3-ac0d-beab65820463[f5c60b36-c9ac-45e3-ac0d-beab65820463] schema data cleaned 
[INFO ] 2024-03-29 18:39:40.800 - [suppliers(100)][f5c60b36-c9ac-45e3-ac0d-beab65820463] - Node f5c60b36-c9ac-45e3-ac0d-beab65820463[f5c60b36-c9ac-45e3-ac0d-beab65820463] monitor closed 
[INFO ] 2024-03-29 18:39:40.801 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:40.801 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:40.801 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 33 ms 
[INFO ] 2024-03-29 18:39:40.801 - [suppliers(100)][f5c60b36-c9ac-45e3-ac0d-beab65820463] - Node f5c60b36-c9ac-45e3-ac0d-beab65820463[f5c60b36-c9ac-45e3-ac0d-beab65820463] close complete, cost 34 ms 
[INFO ] 2024-03-29 18:39:41.005 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-f5c60b36-c9ac-45e3-ac0d-beab65820463 complete, cost 2661ms 
[INFO ] 2024-03-29 18:39:42.845 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:42.846 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:42.846 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:42.846 - [suppliers(100)][37cc6cd6-7a2f-418c-91ea-2adbe75da6b7] - Node 37cc6cd6-7a2f-418c-91ea-2adbe75da6b7[37cc6cd6-7a2f-418c-91ea-2adbe75da6b7] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:42.846 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:42.846 - [suppliers(100)][37cc6cd6-7a2f-418c-91ea-2adbe75da6b7] - Node 37cc6cd6-7a2f-418c-91ea-2adbe75da6b7[37cc6cd6-7a2f-418c-91ea-2adbe75da6b7] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:42.922 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:42.923 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@579daf13 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@579daf13 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@579daf13 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:43.089 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:43.089 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:43.089 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:43.089 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:43.089 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:43.089 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 15 ms 
[INFO ] 2024-03-29 18:39:45.445 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:45.446 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:45.446 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:45.447 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:39:45.447 - [suppliers(100)][37cc6cd6-7a2f-418c-91ea-2adbe75da6b7] - Node 37cc6cd6-7a2f-418c-91ea-2adbe75da6b7[37cc6cd6-7a2f-418c-91ea-2adbe75da6b7] running status set to false 
[INFO ] 2024-03-29 18:39:45.447 - [suppliers(100)][37cc6cd6-7a2f-418c-91ea-2adbe75da6b7] - Node 37cc6cd6-7a2f-418c-91ea-2adbe75da6b7[37cc6cd6-7a2f-418c-91ea-2adbe75da6b7] schema data cleaned 
[INFO ] 2024-03-29 18:39:45.447 - [suppliers(100)][37cc6cd6-7a2f-418c-91ea-2adbe75da6b7] - Node 37cc6cd6-7a2f-418c-91ea-2adbe75da6b7[37cc6cd6-7a2f-418c-91ea-2adbe75da6b7] monitor closed 
[INFO ] 2024-03-29 18:39:45.448 - [suppliers(100)][37cc6cd6-7a2f-418c-91ea-2adbe75da6b7] - Node 37cc6cd6-7a2f-418c-91ea-2adbe75da6b7[37cc6cd6-7a2f-418c-91ea-2adbe75da6b7] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:39:45.448 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-37cc6cd6-7a2f-418c-91ea-2adbe75da6b7 complete, cost 2655ms 
[INFO ] 2024-03-29 18:39:46.972 - [suppliers(100)][d6e854a0-1403-4fbd-bee0-767068fce1d7] - Node d6e854a0-1403-4fbd-bee0-767068fce1d7[d6e854a0-1403-4fbd-bee0-767068fce1d7] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:39:46.972 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:46.972 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:46.972 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:46.972 - [suppliers(100)][d6e854a0-1403-4fbd-bee0-767068fce1d7] - Node d6e854a0-1403-4fbd-bee0-767068fce1d7[d6e854a0-1403-4fbd-bee0-767068fce1d7] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:46.972 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:39:46.989 - [suppliers(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:39:47.160 - [suppliers(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6c986b2f error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6c986b2f error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6c986b2f error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:39:47.161 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:39:47.170 - [suppliers(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:47.170 - [suppliers(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:39:47.170 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:39:47.170 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:39:47.372 - [suppliers(100)][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 10 ms 
[INFO ] 2024-03-29 18:39:49.533 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:39:49.534 - [suppliers(100)][d6e854a0-1403-4fbd-bee0-767068fce1d7] - Node d6e854a0-1403-4fbd-bee0-767068fce1d7[d6e854a0-1403-4fbd-bee0-767068fce1d7] running status set to false 
[INFO ] 2024-03-29 18:39:49.534 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:39:49.534 - [suppliers(100)][d6e854a0-1403-4fbd-bee0-767068fce1d7] - Node d6e854a0-1403-4fbd-bee0-767068fce1d7[d6e854a0-1403-4fbd-bee0-767068fce1d7] schema data cleaned 
[INFO ] 2024-03-29 18:39:49.534 - [suppliers(100)][d6e854a0-1403-4fbd-bee0-767068fce1d7] - Node d6e854a0-1403-4fbd-bee0-767068fce1d7[d6e854a0-1403-4fbd-bee0-767068fce1d7] monitor closed 
[INFO ] 2024-03-29 18:39:49.534 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:39:49.534 - [suppliers(100)][d6e854a0-1403-4fbd-bee0-767068fce1d7] - Node d6e854a0-1403-4fbd-bee0-767068fce1d7[d6e854a0-1403-4fbd-bee0-767068fce1d7] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:39:49.534 - [suppliers(100)][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 6 ms 
[INFO ] 2024-03-29 18:39:49.738 - [suppliers(100)] - load tapTable task 66069a1b0aba7657dd9a0e9a-d6e854a0-1403-4fbd-bee0-767068fce1d7 complete, cost 2600ms 
