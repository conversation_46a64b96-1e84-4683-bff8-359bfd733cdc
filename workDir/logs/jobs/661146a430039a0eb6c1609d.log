[INFO ] 2024-04-06 20:57:38.165 - [任务 55] - Task initialization... 
[INFO ] 2024-04-06 20:57:38.171 - [任务 55] - Start task milestones: 661146a430039a0eb6c1609d(任务 55) 
[INFO ] 2024-04-06 20:57:38.172 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-06 20:57:38.172 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-06 20:57:38.172 - [任务 55][CLAIM] - Node CLAIM[1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5] start preload schema,table counts: 1 
[INFO ] 2024-04-06 20:57:38.173 - [任务 55][test] - Node test[aaa901b4-c68e-40c5-91be-798d284467e2] start preload schema,table counts: 1 
[INFO ] 2024-04-06 20:57:38.193 - [任务 55][CLAIM] - Node CLAIM[1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5] preload schema finished, cost 27 ms 
[INFO ] 2024-04-06 20:57:38.258 - [任务 55][test] - Node test[aaa901b4-c68e-40c5-91be-798d284467e2] preload schema finished, cost 24 ms 
[ERROR] 2024-04-06 20:57:38.259 - [任务 55][CLAIM] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3832/623277798@6dbd748e failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:194)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 11 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 16 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 17 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3832/623277798@6dbd748e failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:194)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 11 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 16 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 17 more


<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3832/623277798@6dbd748e failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:194)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 11 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 16 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 17 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$3832/623277798@6dbd748e failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:194)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 11 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 16 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 17 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:191)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:129)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:194)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 11 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 16 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 17 more

[ERROR] 2024-04-06 20:57:38.277 - [任务 55][test] - Map name: PdkStateMap_aaa901b4-c68e-40c5-91be-798d284467e2 <-- Error Message -->
Map name: PdkStateMap_aaa901b4-c68e-40c5-91be-798d284467e2

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_aaa901b4-c68e-40c5-91be-798d284467e2
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:371)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:167)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1712408258, "i" : 2 } }, "signature" : { "hash" : { "$binary" : "s/GcdliiIw6id8tROm9lzMjQlWc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7289292068113874950" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

[INFO ] 2024-04-06 20:57:38.285 - [任务 55][CLAIM] - Job suspend in error handle 
[INFO ] 2024-04-06 20:57:38.286 - [任务 55][CLAIM] - Node CLAIM[1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5] running status set to false 
[INFO ] 2024-04-06 20:57:38.286 - [任务 55][CLAIM] - PDK connector node stopped: null 
[INFO ] 2024-04-06 20:57:38.287 - [任务 55][CLAIM] - PDK connector node released: null 
[INFO ] 2024-04-06 20:57:38.287 - [任务 55][CLAIM] - Node CLAIM[1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5] schema data cleaned 
[INFO ] 2024-04-06 20:57:38.292 - [任务 55][CLAIM] - Node CLAIM[1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5] monitor closed 
[INFO ] 2024-04-06 20:57:38.292 - [任务 55][CLAIM] - Node CLAIM[1d70558c-9c5e-42f9-9f4a-1d5c6cb82eb5] close complete, cost 8 ms 
[INFO ] 2024-04-06 20:57:38.294 - [任务 55][test] - Node test[aaa901b4-c68e-40c5-91be-798d284467e2] running status set to false 
[INFO ] 2024-04-06 20:57:38.294 - [任务 55][test] - PDK connector node stopped: null 
[INFO ] 2024-04-06 20:57:38.295 - [任务 55][test] - PDK connector node released: null 
[INFO ] 2024-04-06 20:57:38.295 - [任务 55][test] - Node test[aaa901b4-c68e-40c5-91be-798d284467e2] schema data cleaned 
[INFO ] 2024-04-06 20:57:38.296 - [任务 55][test] - Node test[aaa901b4-c68e-40c5-91be-798d284467e2] monitor closed 
[INFO ] 2024-04-06 20:57:38.296 - [任务 55][test] - Node test[aaa901b4-c68e-40c5-91be-798d284467e2] close complete, cost 3 ms 
[INFO ] 2024-04-06 20:57:39.008 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-06 20:57:39.029 - [任务 55] - Stop task milestones: 661146a430039a0eb6c1609d(任务 55)  
[INFO ] 2024-04-06 20:57:39.029 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-04-06 20:57:39.044 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-04-06 20:57:39.045 - [任务 55] - Remove memory task client succeed, task: 任务 55[661146a430039a0eb6c1609d] 
[INFO ] 2024-04-06 20:57:39.045 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[661146a430039a0eb6c1609d] 
