[INFO ] 2024-08-14 12:26:59.516 - [任务 1 - Copy - Copy] - Task initialization... 
[INFO ] 2024-08-14 12:26:59.517 - [任务 1 - Copy - Copy] - Start task milestones: 66bc31bd2e7f73266c77eebd(任务 1 - Copy - Copy) 
[INFO ] 2024-08-14 12:26:59.551 - [任务 1 - Copy - Copy] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-08-14 12:26:59.697 - [任务 1 - Copy - Copy] - The engine receives 任务 1 - Copy - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-14 12:26:59.698 - [任务 1 - Copy - Copy][Pg] - Node Pg[b6d593e0-bde6-4f18-84c6-f59ffa2ed754] start preload schema,table counts: 9 
[INFO ] 2024-08-14 12:26:59.698 - [任务 1 - Copy - Copy][Mysql] - Node <PERSON>sql[2bb7cd82-2b5b-46e6-9aff-8e232ed9eeb5] start preload schema,table counts: 9 
[INFO ] 2024-08-14 12:26:59.823 - [任务 1 - Copy - Copy][Mysql] - Node Mysql[2bb7cd82-2b5b-46e6-9aff-8e232ed9eeb5] preload schema finished, cost 110 ms 
[INFO ] 2024-08-14 12:27:00.028 - [任务 1 - Copy - Copy][Pg] - Node Pg[b6d593e0-bde6-4f18-84c6-f59ffa2ed754] preload schema finished, cost 113 ms 
[INFO ] 2024-08-14 12:27:00.657 - [任务 1 - Copy - Copy][Mysql] - Source node "Mysql" read batch size: 500 
[INFO ] 2024-08-14 12:27:00.658 - [任务 1 - Copy - Copy][Mysql] - Source node "Mysql" event queue capacity: 1000 
[INFO ] 2024-08-14 12:27:00.658 - [任务 1 - Copy - Copy][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-14 12:27:00.663 - [任务 1 - Copy - Copy][Mysql] - batch offset found: {},stream offset found: {"filename":"binlog.000034","position":308235516,"gtidSet":""} 
[INFO ] 2024-08-14 12:27:00.664 - [任务 1 - Copy - Copy][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-14 12:27:00.776 - [任务 1 - Copy - Copy] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-14 12:27:00.776 - [任务 1 - Copy - Copy][Mysql] - Initial sync started 
[INFO ] 2024-08-14 12:27:00.776 - [任务 1 - Copy - Copy][Mysql] - Starting batch read, table name: BMSQL_STOCK, offset: null 
[INFO ] 2024-08-14 12:27:00.793 - [任务 1 - Copy - Copy][Mysql] - Table BMSQL_STOCK is going to be initial synced 
[INFO ] 2024-08-14 12:27:00.845 - [任务 1 - Copy - Copy][Mysql] - Query table 'BMSQL_STOCK' counts: 7899 
[INFO ] 2024-08-14 12:27:00.846 - [任务 1 - Copy - Copy][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-14 12:27:00.874 - [任务 1 - Copy - Copy][Pg] - Write batch size: 100, max wait ms per batch: 5000 
[INFO ] 2024-08-14 12:27:00.876 - [任务 1 - Copy - Copy][Pg] - The table BMSQL_STOCK has already exist. 
[INFO ] 2024-08-14 12:27:00.984 - [任务 1 - Copy - Copy][Pg] - The table BMSQL_NEW_ORDER has already exist. 
[INFO ] 2024-08-14 12:27:00.985 - [任务 1 - Copy - Copy][Pg] - The table BMSQL_ITEM has already exist. 
[INFO ] 2024-08-14 12:27:01.102 - [任务 1 - Copy - Copy][Pg] - The table BMSQL_ORDER_LINE has already exist. 
[INFO ] 2024-08-14 12:27:01.102 - [任务 1 - Copy - Copy][Pg] - The table BMSQL_DISTRICT has already exist. 
[INFO ] 2024-08-14 12:27:01.170 - [任务 1 - Copy - Copy][Pg] - The table BMSQL_CONFIG has already exist. 
[INFO ] 2024-08-14 12:27:01.170 - [任务 1 - Copy - Copy][Pg] - The table BMSQL_CUSTOMER has already exist. 
[INFO ] 2024-08-14 12:27:01.371 - [任务 1 - Copy - Copy][Pg] - The table BMSQL_OORDER has already exist. 
[INFO ] 2024-08-14 12:27:01.800 - [任务 1 - Copy - Copy][Mysql] - Table [BMSQL_STOCK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 12:27:01.800 - [任务 1 - Copy - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_STOCK 
[INFO ] 2024-08-14 12:27:01.801 - [任务 1 - Copy - Copy][Mysql] - Starting batch read, table name: BMSQL_NEW_ORDER, offset: null 
[INFO ] 2024-08-14 12:27:01.801 - [任务 1 - Copy - Copy][Mysql] - Table BMSQL_NEW_ORDER is going to be initial synced 
[INFO ] 2024-08-14 12:27:01.871 - [任务 1 - Copy - Copy][Mysql] - Query table 'BMSQL_NEW_ORDER' counts: 2899 
[INFO ] 2024-08-14 12:27:01.872 - [任务 1 - Copy - Copy][Pg] - handle CompleteTableSnapshotBMSQL_STOCK 
[INFO ] 2024-08-14 12:27:01.902 - [任务 1 - Copy - Copy][Mysql] - Table [BMSQL_NEW_ORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 12:27:01.902 - [任务 1 - Copy - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_NEW_ORDER 
[INFO ] 2024-08-14 12:27:01.902 - [任务 1 - Copy - Copy][Mysql] - Starting batch read, table name: BMSQL_WAREHOUSE, offset: null 
[INFO ] 2024-08-14 12:27:01.902 - [任务 1 - Copy - Copy][Mysql] - Table BMSQL_WAREHOUSE is going to be initial synced 
[INFO ] 2024-08-14 12:27:01.905 - [任务 1 - Copy - Copy][Mysql] - Table [BMSQL_WAREHOUSE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 12:27:01.907 - [任务 1 - Copy - Copy][Mysql] - Query table 'BMSQL_WAREHOUSE' counts: 0 
[INFO ] 2024-08-14 12:27:01.907 - [任务 1 - Copy - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_WAREHOUSE 
[INFO ] 2024-08-14 12:27:01.907 - [任务 1 - Copy - Copy][Mysql] - Starting batch read, table name: BMSQL_ITEM, offset: null 
[INFO ] 2024-08-14 12:27:01.907 - [任务 1 - Copy - Copy][Mysql] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-08-14 12:27:01.911 - [任务 1 - Copy - Copy][Mysql] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 12:27:01.911 - [任务 1 - Copy - Copy][Mysql] - Query table 'BMSQL_ITEM' counts: 0 
[INFO ] 2024-08-14 12:27:01.912 - [任务 1 - Copy - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_ITEM 
[INFO ] 2024-08-14 12:27:01.912 - [任务 1 - Copy - Copy][Mysql] - Starting batch read, table name: BMSQL_ORDER_LINE, offset: null 
[INFO ] 2024-08-14 12:27:01.914 - [任务 1 - Copy - Copy][Mysql] - Table BMSQL_ORDER_LINE is going to be initial synced 
[INFO ] 2024-08-14 12:27:01.914 - [任务 1 - Copy - Copy][Mysql] - Table [BMSQL_ORDER_LINE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 12:27:01.914 - [任务 1 - Copy - Copy][Mysql] - Query table 'BMSQL_ORDER_LINE' counts: 0 
[INFO ] 2024-08-14 12:27:01.915 - [任务 1 - Copy - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_ORDER_LINE 
[INFO ] 2024-08-14 12:27:01.915 - [任务 1 - Copy - Copy][Mysql] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-14 12:27:01.915 - [任务 1 - Copy - Copy][Mysql] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-14 12:27:01.921 - [任务 1 - Copy - Copy][Mysql] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-14 12:27:01.921 - [任务 1 - Copy - Copy][Mysql] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 12:27:01.922 - [任务 1 - Copy - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_DISTRICT 
[INFO ] 2024-08-14 12:27:01.922 - [任务 1 - Copy - Copy][Mysql] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-14 12:27:01.924 - [任务 1 - Copy - Copy][Mysql] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-14 12:27:01.924 - [任务 1 - Copy - Copy][Mysql] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 12:27:01.924 - [任务 1 - Copy - Copy][Mysql] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-14 12:27:01.924 - [任务 1 - Copy - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_CONFIG 
[INFO ] 2024-08-14 12:27:01.925 - [任务 1 - Copy - Copy][Mysql] - Starting batch read, table name: BMSQL_CUSTOMER, offset: null 
[INFO ] 2024-08-14 12:27:01.925 - [任务 1 - Copy - Copy][Mysql] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-08-14 12:27:01.927 - [任务 1 - Copy - Copy][Pg] - handle CompleteTableSnapshotBMSQL_NEW_ORDER 
[INFO ] 2024-08-14 12:27:01.927 - [任务 1 - Copy - Copy][Pg] - handle CompleteTableSnapshotBMSQL_WAREHOUSE 
[INFO ] 2024-08-14 12:27:01.928 - [任务 1 - Copy - Copy][Mysql] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 12:27:01.928 - [任务 1 - Copy - Copy][Mysql] - Query table 'BMSQL_CUSTOMER' counts: 0 
[INFO ] 2024-08-14 12:27:01.928 - [任务 1 - Copy - Copy][Pg] - handle CompleteTableSnapshotBMSQL_ITEM 
[INFO ] 2024-08-14 12:27:01.928 - [任务 1 - Copy - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_CUSTOMER 
[INFO ] 2024-08-14 12:27:01.928 - [任务 1 - Copy - Copy][Pg] - handle CompleteTableSnapshotBMSQL_ORDER_LINE 
[INFO ] 2024-08-14 12:27:01.928 - [任务 1 - Copy - Copy][Mysql] - Starting batch read, table name: BMSQL_OORDER, offset: null 
[INFO ] 2024-08-14 12:27:01.930 - [任务 1 - Copy - Copy][Mysql] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-08-14 12:27:01.930 - [任务 1 - Copy - Copy][Mysql] - Query table 'BMSQL_OORDER' counts: 0 
[INFO ] 2024-08-14 12:27:01.931 - [任务 1 - Copy - Copy][Mysql] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 12:27:01.931 - [任务 1 - Copy - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_OORDER 
[INFO ] 2024-08-14 12:27:01.931 - [任务 1 - Copy - Copy][Mysql] - enqueue TapDataCompleteSnapshotEvent 
[INFO ] 2024-08-14 12:27:01.931 - [任务 1 - Copy - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 12:27:01.931 - [任务 1 - Copy - Copy][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-14 12:27:01.931 - [任务 1 - Copy - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 12:27:01.973 - [任务 1 - Copy - Copy][Mysql] - Starting stream read, table list: [BMSQL_STOCK, BMSQL_NEW_ORDER, BMSQL_WAREHOUSE, BMSQL_ITEM, BMSQL_ORDER_LINE, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER], offset: {"filename":"binlog.000034","position":308235516,"gtidSet":""} 
[INFO ] 2024-08-14 12:27:01.974 - [任务 1 - Copy - Copy][Mysql] - Starting mysql cdc, server name: 98d2e9f5-f60b-4dd7-8402-52682bf8b392 
[INFO ] 2024-08-14 12:27:02.012 - [任务 1 - Copy - Copy][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 394516147
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 98d2e9f5-f60b-4dd7-8402-52682bf8b392
  database.port: 3306
  threadName: Debezium-Mysql-Connector-98d2e9f5-f60b-4dd7-8402-52682bf8b392
  database.hostname: localhost
  database.password: ********
  name: 98d2e9f5-f60b-4dd7-8402-52682bf8b392
  pdk.offset.string: {"name":"98d2e9f5-f60b-4dd7-8402-52682bf8b392","offset":{"{\"server\":\"98d2e9f5-f60b-4dd7-8402-52682bf8b392\"}":"{\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_STOCK,test.BMSQL_NEW_ORDER,test.BMSQL_WAREHOUSE,test.BMSQL_ITEM,test.BMSQL_ORDER_LINE,test.BMSQL_DISTRICT,test.BMSQL_CONFIG,test.BMSQL_CUSTOMER,test.BMSQL_OORDER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-14 12:27:02.013 - [任务 1 - Copy - Copy][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_STOCK, BMSQL_NEW_ORDER, BMSQL_WAREHOUSE, BMSQL_ITEM, BMSQL_ORDER_LINE, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER], data change syncing 
[INFO ] 2024-08-14 12:27:06.979 - [任务 1 - Copy - Copy][Pg] - handle CompleteTableSnapshotBMSQL_DISTRICT 
[INFO ] 2024-08-14 12:27:06.992 - [任务 1 - Copy - Copy][Pg] - handle CompleteTableSnapshotBMSQL_CONFIG 
[INFO ] 2024-08-14 12:27:06.992 - [任务 1 - Copy - Copy][Pg] - handle CompleteTableSnapshotBMSQL_CUSTOMER 
[INFO ] 2024-08-14 12:27:07.192 - [任务 1 - Copy - Copy][Pg] - handle CompleteTableSnapshotBMSQL_OORDER 
[INFO ] 2024-08-14 12:29:00.750 - [任务 1 - Copy - Copy][Mysql] - Found new table(s): [BMSQL_OORDER_BACK] 
[INFO ] 2024-08-14 12:29:00.753 - [任务 1 - Copy - Copy][Mysql] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-08-14 12:29:00.923 - [任务 1 - Copy - Copy][Mysql] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@42651a56: {"table":{"comment":"","id":"BMSQL_OORDER_BACK","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"}],"name":"PRIMARY","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"BMSQL_OORDER_BACK","nameFieldMap":{"id":{"autoInc":false,"comment":"","dataType":"int","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"comment":"","dataType":"varchar(20)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"}},"unique":true}},"tableId":"BMSQL_OORDER_BACK","type":206} 
[INFO ] 2024-08-14 12:29:00.924 - [任务 1 - Copy - Copy][Mysql] - Create new table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_BMSQL_OORDER_BACK_66b9b562916df264a848f35a_66bc31bd2e7f73266c77eebd 
[INFO ] 2024-08-14 12:29:01.704 - [任务 1 - Copy - Copy][Mysql] - Create new table schema transform finished: TapTable id BMSQL_OORDER_BACK name BMSQL_OORDER_BACK storageEngine null charset null number of fields 2 
[INFO ] 2024-08-14 12:29:01.797 - [任务 1 - Copy - Copy][Mysql] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-08-14 12:29:01.797 - [任务 1 - Copy - Copy][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-14 12:29:01.801 - [任务 1 - Copy - Copy][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-14 12:29:01.801 - [任务 1 - Copy - Copy][Mysql] - Incremental sync completed 
[INFO ] 2024-08-14 12:29:02.046 - [任务 1 - Copy - Copy][Mysql] - Starting batch read, table name: BMSQL_OORDER_BACK, offset: null 
[INFO ] 2024-08-14 12:29:02.049 - [任务 1 - Copy - Copy][Mysql] - Table BMSQL_OORDER_BACK is going to be initial synced 
[INFO ] 2024-08-14 12:29:02.052 - [任务 1 - Copy - Copy][Mysql] - Query table 'BMSQL_OORDER_BACK' counts: 1 
[INFO ] 2024-08-14 12:29:02.054 - [任务 1 - Copy - Copy][Mysql] - Table [BMSQL_OORDER_BACK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 12:29:02.058 - [任务 1 - Copy - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_OORDER_BACK 
[INFO ] 2024-08-14 12:29:02.058 - [任务 1 - Copy - Copy][Mysql] - enqueue TapDataCompleteSnapshotEvent 
[INFO ] 2024-08-14 12:29:02.060 - [任务 1 - Copy - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 12:29:02.060 - [任务 1 - Copy - Copy][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-14 12:29:02.061 - [任务 1 - Copy - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 12:29:02.062 - [任务 1 - Copy - Copy][Mysql] - Starting stream read, table list: [BMSQL_OORDER_BACK, BMSQL_STOCK, BMSQL_NEW_ORDER, BMSQL_WAREHOUSE, BMSQL_ITEM, BMSQL_ORDER_LINE, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER], offset: {"name":"98d2e9f5-f60b-4dd7-8402-52682bf8b392","offset":{"{\"server\":\"98d2e9f5-f60b-4dd7-8402-52682bf8b392\"}":"{\"ts_sec\":1723609622,\"file\":\"binlog.000034\",\"pos\":308235886,\"server_id\":1}"}} 
[INFO ] 2024-08-14 12:29:02.080 - [任务 1 - Copy - Copy][Mysql] - Starting mysql cdc, server name: 98d2e9f5-f60b-4dd7-8402-52682bf8b392 
[INFO ] 2024-08-14 12:29:02.081 - [任务 1 - Copy - Copy][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 648549466
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 98d2e9f5-f60b-4dd7-8402-52682bf8b392
  database.port: 3306
  threadName: Debezium-Mysql-Connector-98d2e9f5-f60b-4dd7-8402-52682bf8b392
  database.hostname: localhost
  database.password: ********
  name: 98d2e9f5-f60b-4dd7-8402-52682bf8b392
  pdk.offset.string: {"name":"98d2e9f5-f60b-4dd7-8402-52682bf8b392","offset":{"{\"server\":\"98d2e9f5-f60b-4dd7-8402-52682bf8b392\"}":"{\"ts_sec\":1723609622,\"file\":\"binlog.000034\",\"pos\":308235886,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_OORDER_BACK,test.BMSQL_STOCK,test.BMSQL_NEW_ORDER,test.BMSQL_WAREHOUSE,test.BMSQL_ITEM,test.BMSQL_ORDER_LINE,test.BMSQL_DISTRICT,test.BMSQL_CONFIG,test.BMSQL_CUSTOMER,test.BMSQL_OORDER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-14 12:29:02.217 - [任务 1 - Copy - Copy][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_OORDER_BACK, BMSQL_STOCK, BMSQL_NEW_ORDER, BMSQL_WAREHOUSE, BMSQL_ITEM, BMSQL_ORDER_LINE, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER], data change syncing 
[INFO ] 2024-08-14 12:29:02.218 - [任务 1 - Copy - Copy][Pg] - handle CompleteTableSnapshotBMSQL_OORDER_BACK 
[INFO ] 2024-08-14 12:33:00.720 - [任务 1 - Copy - Copy][Mysql] - Found new table(s): [BMSQL_OORDER_BACK2] 
[INFO ] 2024-08-14 12:33:00.740 - [任务 1 - Copy - Copy][Mysql] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-08-14 12:33:00.741 - [任务 1 - Copy - Copy][Mysql] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@63267d1a: {"table":{"comment":"","id":"BMSQL_OORDER_BACK2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"}],"name":"PRIMARY","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"BMSQL_OORDER_BACK2","nameFieldMap":{"id":{"autoInc":false,"comment":"","dataType":"int","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"comment":"","dataType":"varchar(20)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"}},"unique":true}},"tableId":"BMSQL_OORDER_BACK2","type":206} 
[INFO ] 2024-08-14 12:33:00.945 - [任务 1 - Copy - Copy][Mysql] - Create new table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_BMSQL_OORDER_BACK2_66b9b562916df264a848f35a_66bc31bd2e7f73266c77eebd 
[INFO ] 2024-08-14 12:33:00.946 - [任务 1 - Copy - Copy][Mysql] - Create new table schema transform finished: TapTable id BMSQL_OORDER_BACK2 name BMSQL_OORDER_BACK2 storageEngine null charset null number of fields 2 
[INFO ] 2024-08-14 12:33:00.948 - [任务 1 - Copy - Copy][Mysql] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-08-14 12:33:01.060 - [任务 1 - Copy - Copy][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-14 12:33:01.060 - [任务 1 - Copy - Copy][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-14 12:33:01.062 - [任务 1 - Copy - Copy][Mysql] - Incremental sync completed 
[INFO ] 2024-08-14 12:33:53.231 - [任务 1 - Copy - Copy][Mysql] - Starting batch read, table name: BMSQL_OORDER_BACK2, offset: null 
[INFO ] 2024-08-14 12:33:53.261 - [任务 1 - Copy - Copy][Mysql] - Table BMSQL_OORDER_BACK2 is going to be initial synced 
[INFO ] 2024-08-14 12:33:53.320 - [任务 1 - Copy - Copy][Mysql] - Query table 'BMSQL_OORDER_BACK2' counts: 1 
[INFO ] 2024-08-14 12:33:53.321 - [任务 1 - Copy - Copy][Mysql] - Table [BMSQL_OORDER_BACK2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-14 12:33:53.321 - [任务 1 - Copy - Copy][Mysql] - enqueue TapDataCompleteTableSnapshotEvent tableName is BMSQL_OORDER_BACK2 
[INFO ] 2024-08-14 12:33:53.322 - [任务 1 - Copy - Copy][Mysql] - enqueue TapDataCompleteSnapshotEvent 
[INFO ] 2024-08-14 12:33:53.323 - [任务 1 - Copy - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 12:33:53.323 - [任务 1 - Copy - Copy][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-14 12:33:53.323 - [任务 1 - Copy - Copy][Mysql] - Initial sync completed 
[INFO ] 2024-08-14 12:33:53.347 - [任务 1 - Copy - Copy][Mysql] - Starting stream read, table list: [BMSQL_OORDER_BACK, BMSQL_STOCK, BMSQL_NEW_ORDER, BMSQL_WAREHOUSE, BMSQL_ITEM, BMSQL_ORDER_LINE, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER, BMSQL_OORDER_BACK2], offset: {"name":"98d2e9f5-f60b-4dd7-8402-52682bf8b392","offset":{"{\"server\":\"98d2e9f5-f60b-4dd7-8402-52682bf8b392\"}":"{\"ts_sec\":1723609732,\"file\":\"binlog.000034\",\"pos\":308236187,\"server_id\":1}"}} 
[INFO ] 2024-08-14 12:33:53.348 - [任务 1 - Copy - Copy][Mysql] - Starting mysql cdc, server name: 98d2e9f5-f60b-4dd7-8402-52682bf8b392 
[INFO ] 2024-08-14 12:33:53.501 - [任务 1 - Copy - Copy][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 436332339
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 98d2e9f5-f60b-4dd7-8402-52682bf8b392
  database.port: 3306
  threadName: Debezium-Mysql-Connector-98d2e9f5-f60b-4dd7-8402-52682bf8b392
  database.hostname: localhost
  database.password: ********
  name: 98d2e9f5-f60b-4dd7-8402-52682bf8b392
  pdk.offset.string: {"name":"98d2e9f5-f60b-4dd7-8402-52682bf8b392","offset":{"{\"server\":\"98d2e9f5-f60b-4dd7-8402-52682bf8b392\"}":"{\"ts_sec\":1723609732,\"file\":\"binlog.000034\",\"pos\":308236187,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_OORDER_BACK,test.BMSQL_STOCK,test.BMSQL_NEW_ORDER,test.BMSQL_WAREHOUSE,test.BMSQL_ITEM,test.BMSQL_ORDER_LINE,test.BMSQL_DISTRICT,test.BMSQL_CONFIG,test.BMSQL_CUSTOMER,test.BMSQL_OORDER,test.BMSQL_OORDER_BACK2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-14 12:33:53.513 - [任务 1 - Copy - Copy][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_OORDER_BACK, BMSQL_STOCK, BMSQL_NEW_ORDER, BMSQL_WAREHOUSE, BMSQL_ITEM, BMSQL_ORDER_LINE, BMSQL_DISTRICT, BMSQL_CONFIG, BMSQL_CUSTOMER, BMSQL_OORDER, BMSQL_OORDER_BACK2], data change syncing 
[INFO ] 2024-08-14 12:33:58.499 - [任务 1 - Copy - Copy][Pg] - handle CompleteTableSnapshotBMSQL_OORDER_BACK2 
[INFO ] 2024-08-14 12:36:09.665 - [任务 1 - Copy - Copy] - Stop task milestones: 66bc31bd2e7f73266c77eebd(任务 1 - Copy - Copy)  
[INFO ] 2024-08-14 12:36:09.665 - [任务 1 - Copy - Copy][Mysql] - Node Mysql[2bb7cd82-2b5b-46e6-9aff-8e232ed9eeb5] running status set to false 
[INFO ] 2024-08-14 12:36:09.754 - [任务 1 - Copy - Copy][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-14 12:36:09.755 - [任务 1 - Copy - Copy][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-14 12:36:09.763 - [任务 1 - Copy - Copy][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-2bb7cd82-2b5b-46e6-9aff-8e232ed9eeb5 
[INFO ] 2024-08-14 12:36:09.764 - [任务 1 - Copy - Copy][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-2bb7cd82-2b5b-46e6-9aff-8e232ed9eeb5 
[INFO ] 2024-08-14 12:36:09.764 - [任务 1 - Copy - Copy][Mysql] - Node Mysql[2bb7cd82-2b5b-46e6-9aff-8e232ed9eeb5] schema data cleaned 
[INFO ] 2024-08-14 12:36:09.770 - [任务 1 - Copy - Copy][Mysql] - Node Mysql[2bb7cd82-2b5b-46e6-9aff-8e232ed9eeb5] monitor closed 
[INFO ] 2024-08-14 12:36:09.771 - [任务 1 - Copy - Copy][Mysql] - Node Mysql[2bb7cd82-2b5b-46e6-9aff-8e232ed9eeb5] close complete, cost 110 ms 
[INFO ] 2024-08-14 12:36:09.771 - [任务 1 - Copy - Copy][Pg] - Node Pg[b6d593e0-bde6-4f18-84c6-f59ffa2ed754] running status set to false 
[INFO ] 2024-08-14 12:36:09.790 - [任务 1 - Copy - Copy][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-b6d593e0-bde6-4f18-84c6-f59ffa2ed754 
[INFO ] 2024-08-14 12:36:09.793 - [任务 1 - Copy - Copy][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-b6d593e0-bde6-4f18-84c6-f59ffa2ed754 
[INFO ] 2024-08-14 12:36:09.793 - [任务 1 - Copy - Copy][Pg] - Node Pg[b6d593e0-bde6-4f18-84c6-f59ffa2ed754] schema data cleaned 
[INFO ] 2024-08-14 12:36:09.793 - [任务 1 - Copy - Copy][Pg] - Node Pg[b6d593e0-bde6-4f18-84c6-f59ffa2ed754] monitor closed 
[INFO ] 2024-08-14 12:36:09.793 - [任务 1 - Copy - Copy][Pg] - Node Pg[b6d593e0-bde6-4f18-84c6-f59ffa2ed754] close complete, cost 19 ms 
[INFO ] 2024-08-14 12:36:13.167 - [任务 1 - Copy - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-14 12:36:13.167 - [任务 1 - Copy - Copy] - Stopped task aspect(s) 
[INFO ] 2024-08-14 12:36:13.167 - [任务 1 - Copy - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-08-14 12:36:13.206 - [任务 1 - Copy - Copy] - Remove memory task client succeed, task: 任务 1 - Copy - Copy[66bc31bd2e7f73266c77eebd] 
[INFO ] 2024-08-14 12:36:13.206 - [任务 1 - Copy - Copy] - Destroy memory task client cache succeed, task: 任务 1 - Copy - Copy[66bc31bd2e7f73266c77eebd] 
