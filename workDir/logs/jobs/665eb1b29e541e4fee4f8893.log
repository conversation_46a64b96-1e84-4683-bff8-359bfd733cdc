[INFO ] 2024-06-04 15:36:58.257 - [任务 1(100)][294f8821-8537-4a1c-ac3b-b2a63c93e881] - Node 294f8821-8537-4a1c-ac3b-b2a63c93e881[294f8821-8537-4a1c-ac3b-b2a63c93e881] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:36:58.261 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:36:58.264 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:36:58.265 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:36:58.265 - [任务 1(100)][294f8821-8537-4a1c-ac3b-b2a63c93e881] - Node 294f8821-8537-4a1c-ac3b-b2a63c93e881[294f8821-8537-4a1c-ac3b-b2a63c93e881] preload schema finished, cost 1 ms 
[INFO ] 2024-06-04 15:36:58.265 - [任务 1(100)][8371c563-ae50-4ad8-b48e-bc0372b56d8d] - Node 8371c563-ae50-4ad8-b48e-bc0372b56d8d[8371c563-ae50-4ad8-b48e-bc0372b56d8d] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:36:58.266 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:36:58.266 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:36:58.266 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 1 ms 
[INFO ] 2024-06-04 15:36:58.266 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:36:58.267 - [任务 1(100)][8371c563-ae50-4ad8-b48e-bc0372b56d8d] - Node 8371c563-ae50-4ad8-b48e-bc0372b56d8d[8371c563-ae50-4ad8-b48e-bc0372b56d8d] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:36:58.267 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:36:58.267 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:36:58.267 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:36:58.268 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:36:58.268 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:36:59.381 - [任务 1(100)][agg_products] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to create pdk connector node, database type: postgres-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[ERROR] 2024-06-04 15:36:59.590 - [任务 1(100)][agg_products] - start source runner failed: Failed to create pdk connector node, database type: postgres-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: postgres-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: postgres-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:685)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:571)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:213)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: postgres-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:221)
	... 16 more

[INFO ] 2024-06-04 15:37:00.773 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:37:00.774 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:37:00.785 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:00.787 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:00.787 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:37:00.799 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:37:00.807 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 52 ms 
[INFO ] 2024-06-04 15:37:00.864 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:37:00.873 - [任务 1(100)][294f8821-8537-4a1c-ac3b-b2a63c93e881] - Node 294f8821-8537-4a1c-ac3b-b2a63c93e881[294f8821-8537-4a1c-ac3b-b2a63c93e881] running status set to false 
[INFO ] 2024-06-04 15:37:00.873 - [任务 1(100)][294f8821-8537-4a1c-ac3b-b2a63c93e881] - Node 294f8821-8537-4a1c-ac3b-b2a63c93e881[294f8821-8537-4a1c-ac3b-b2a63c93e881] schema data cleaned 
[INFO ] 2024-06-04 15:37:00.873 - [任务 1(100)][294f8821-8537-4a1c-ac3b-b2a63c93e881] - Node 294f8821-8537-4a1c-ac3b-b2a63c93e881[294f8821-8537-4a1c-ac3b-b2a63c93e881] monitor closed 
[INFO ] 2024-06-04 15:37:00.874 - [任务 1(100)][294f8821-8537-4a1c-ac3b-b2a63c93e881] - Node 294f8821-8537-4a1c-ac3b-b2a63c93e881[294f8821-8537-4a1c-ac3b-b2a63c93e881] close complete, cost 19 ms 
[INFO ] 2024-06-04 15:37:00.874 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-625bbfc8-94fd-4bd2-8cdf-dfdb469c57e2 
[INFO ] 2024-06-04 15:37:00.876 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-625bbfc8-94fd-4bd2-8cdf-dfdb469c57e2 
[INFO ] 2024-06-04 15:37:00.883 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:37:00.884 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:37:00.884 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:37:00.929 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 43 ms 
[INFO ] 2024-06-04 15:37:00.933 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:37:00.933 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:37:00.933 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 158 ms 
[INFO ] 2024-06-04 15:37:00.940 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-294f8821-8537-4a1c-ac3b-b2a63c93e881 complete, cost 20093ms 
[INFO ] 2024-06-04 15:37:02.085 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:37:02.086 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:37:02.091 - [任务 1(100)][agg_products] - PDK connector node stopped: null 
[INFO ] 2024-06-04 15:37:02.119 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:37:02.123 - [任务 1(100)][agg_products] - PDK connector node released: null 
[INFO ] 2024-06-04 15:37:02.123 - [任务 1(100)][8371c563-ae50-4ad8-b48e-bc0372b56d8d] - Node 8371c563-ae50-4ad8-b48e-bc0372b56d8d[8371c563-ae50-4ad8-b48e-bc0372b56d8d] running status set to false 
[INFO ] 2024-06-04 15:37:02.137 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:37:02.138 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-749720d2-3614-4b67-8442-b8a79caf7298 
[INFO ] 2024-06-04 15:37:02.138 - [任务 1(100)][8371c563-ae50-4ad8-b48e-bc0372b56d8d] - Node 8371c563-ae50-4ad8-b48e-bc0372b56d8d[8371c563-ae50-4ad8-b48e-bc0372b56d8d] schema data cleaned 
[INFO ] 2024-06-04 15:37:02.138 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:37:02.138 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-749720d2-3614-4b67-8442-b8a79caf7298 
[INFO ] 2024-06-04 15:37:02.140 - [任务 1(100)][8371c563-ae50-4ad8-b48e-bc0372b56d8d] - Node 8371c563-ae50-4ad8-b48e-bc0372b56d8d[8371c563-ae50-4ad8-b48e-bc0372b56d8d] monitor closed 
[INFO ] 2024-06-04 15:37:02.140 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 60 ms 
[INFO ] 2024-06-04 15:37:02.140 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:37:02.140 - [任务 1(100)][8371c563-ae50-4ad8-b48e-bc0372b56d8d] - Node 8371c563-ae50-4ad8-b48e-bc0372b56d8d[8371c563-ae50-4ad8-b48e-bc0372b56d8d] close complete, cost 43 ms 
[INFO ] 2024-06-04 15:37:02.150 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:37:02.150 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:37:02.245 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 63 ms 
[INFO ] 2024-06-04 15:37:02.248 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:37:02.249 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:37:02.250 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 161 ms 
[INFO ] 2024-06-04 15:37:02.250 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-8371c563-ae50-4ad8-b48e-bc0372b56d8d complete, cost 5062ms 
[INFO ] 2024-06-04 15:37:03.349 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:03.351 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:03.351 - [任务 1(100)][3b8fe1ce-4cd5-446f-ab72-05efc53a55a3] - Node 3b8fe1ce-4cd5-446f-ab72-05efc53a55a3[3b8fe1ce-4cd5-446f-ab72-05efc53a55a3] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:37:03.351 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:03.367 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:03.371 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:03.373 - [任务 1(100)][3b8fe1ce-4cd5-446f-ab72-05efc53a55a3] - Node 3b8fe1ce-4cd5-446f-ab72-05efc53a55a3[3b8fe1ce-4cd5-446f-ab72-05efc53a55a3] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:03.376 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:03.656 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:37:03.657 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:37:03.692 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:03.693 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:03.698 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:37:03.706 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:37:03.706 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 61 ms 
[INFO ] 2024-06-04 15:37:03.731 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:37:03.732 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:37:03.732 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 80 ms 
[INFO ] 2024-06-04 15:37:03.792 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:37:03.798 - [任务 1(100)][3b8fe1ce-4cd5-446f-ab72-05efc53a55a3] - Node 3b8fe1ce-4cd5-446f-ab72-05efc53a55a3[3b8fe1ce-4cd5-446f-ab72-05efc53a55a3] running status set to false 
[INFO ] 2024-06-04 15:37:03.799 - [任务 1(100)][3b8fe1ce-4cd5-446f-ab72-05efc53a55a3] - Node 3b8fe1ce-4cd5-446f-ab72-05efc53a55a3[3b8fe1ce-4cd5-446f-ab72-05efc53a55a3] schema data cleaned 
[INFO ] 2024-06-04 15:37:03.799 - [任务 1(100)][3b8fe1ce-4cd5-446f-ab72-05efc53a55a3] - Node 3b8fe1ce-4cd5-446f-ab72-05efc53a55a3[3b8fe1ce-4cd5-446f-ab72-05efc53a55a3] monitor closed 
[INFO ] 2024-06-04 15:37:03.799 - [任务 1(100)][3b8fe1ce-4cd5-446f-ab72-05efc53a55a3] - Node 3b8fe1ce-4cd5-446f-ab72-05efc53a55a3[3b8fe1ce-4cd5-446f-ab72-05efc53a55a3] close complete, cost 4 ms 
[INFO ] 2024-06-04 15:37:03.799 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-68fe30db-a55d-42a4-9b50-ac7161840ac4 
[INFO ] 2024-06-04 15:37:03.799 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-68fe30db-a55d-42a4-9b50-ac7161840ac4 
[INFO ] 2024-06-04 15:37:03.799 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:37:03.801 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:37:03.801 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:37:03.802 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 19 ms 
[INFO ] 2024-06-04 15:37:04.008 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-3b8fe1ce-4cd5-446f-ab72-05efc53a55a3 complete, cost 527ms 
[INFO ] 2024-06-04 15:37:09.630 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:09.631 - [任务 1(100)][a67cba50-672a-455d-995f-b711e2594a08] - Node a67cba50-672a-455d-995f-b711e2594a08[a67cba50-672a-455d-995f-b711e2594a08] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:37:09.631 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:09.632 - [任务 1(100)][a67cba50-672a-455d-995f-b711e2594a08] - Node a67cba50-672a-455d-995f-b711e2594a08[a67cba50-672a-455d-995f-b711e2594a08] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:09.632 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:09.632 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:09.641 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:09.641 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 1 ms 
[INFO ] 2024-06-04 15:37:09.875 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:37:09.875 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:37:09.875 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:09.876 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:09.877 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:37:09.878 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:37:09.878 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 17 ms 
[INFO ] 2024-06-04 15:37:09.979 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:37:09.985 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:37:09.985 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 109 ms 
[INFO ] 2024-06-04 15:37:10.034 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:37:10.034 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-ba4e7321-4fc6-48a2-b992-da135b925f60 
[INFO ] 2024-06-04 15:37:10.035 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-ba4e7321-4fc6-48a2-b992-da135b925f60 
[INFO ] 2024-06-04 15:37:10.035 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:37:10.037 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:37:10.037 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:37:10.087 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 24 ms 
[INFO ] 2024-06-04 15:37:10.091 - [任务 1(100)][a67cba50-672a-455d-995f-b711e2594a08] - Node a67cba50-672a-455d-995f-b711e2594a08[a67cba50-672a-455d-995f-b711e2594a08] running status set to false 
[INFO ] 2024-06-04 15:37:10.093 - [任务 1(100)][a67cba50-672a-455d-995f-b711e2594a08] - Node a67cba50-672a-455d-995f-b711e2594a08[a67cba50-672a-455d-995f-b711e2594a08] schema data cleaned 
[INFO ] 2024-06-04 15:37:10.096 - [任务 1(100)][a67cba50-672a-455d-995f-b711e2594a08] - Node a67cba50-672a-455d-995f-b711e2594a08[a67cba50-672a-455d-995f-b711e2594a08] monitor closed 
[INFO ] 2024-06-04 15:37:10.096 - [任务 1(100)][a67cba50-672a-455d-995f-b711e2594a08] - Node a67cba50-672a-455d-995f-b711e2594a08[a67cba50-672a-455d-995f-b711e2594a08] close complete, cost 5 ms 
[INFO ] 2024-06-04 15:37:10.096 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-a67cba50-672a-455d-995f-b711e2594a08 complete, cost 534ms 
[INFO ] 2024-06-04 15:37:21.832 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:21.834 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:21.838 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:21.840 - [任务 1(100)][56c30ee1-a08f-4ad6-af72-a73d1143c2cc] - Node 56c30ee1-a08f-4ad6-af72-a73d1143c2cc[56c30ee1-a08f-4ad6-af72-a73d1143c2cc] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:37:21.841 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:21.845 - [任务 1(100)][56c30ee1-a08f-4ad6-af72-a73d1143c2cc] - Node 56c30ee1-a08f-4ad6-af72-a73d1143c2cc[56c30ee1-a08f-4ad6-af72-a73d1143c2cc] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:21.847 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 1 ms 
[INFO ] 2024-06-04 15:37:21.847 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:22.150 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:37:22.151 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:37:22.155 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:22.155 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:22.159 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:37:22.159 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:37:22.159 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 15 ms 
[INFO ] 2024-06-04 15:37:22.210 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:37:22.212 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:37:22.212 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 67 ms 
[INFO ] 2024-06-04 15:37:22.236 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:37:22.240 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-2b34b9f6-16ee-4797-9868-f134d5e6396f 
[INFO ] 2024-06-04 15:37:22.240 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-2b34b9f6-16ee-4797-9868-f134d5e6396f 
[INFO ] 2024-06-04 15:37:22.240 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:37:22.240 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:37:22.244 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:37:22.248 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 16 ms 
[INFO ] 2024-06-04 15:37:22.254 - [任务 1(100)][56c30ee1-a08f-4ad6-af72-a73d1143c2cc] - Node 56c30ee1-a08f-4ad6-af72-a73d1143c2cc[56c30ee1-a08f-4ad6-af72-a73d1143c2cc] running status set to false 
[INFO ] 2024-06-04 15:37:22.256 - [任务 1(100)][56c30ee1-a08f-4ad6-af72-a73d1143c2cc] - Node 56c30ee1-a08f-4ad6-af72-a73d1143c2cc[56c30ee1-a08f-4ad6-af72-a73d1143c2cc] schema data cleaned 
[INFO ] 2024-06-04 15:37:22.256 - [任务 1(100)][56c30ee1-a08f-4ad6-af72-a73d1143c2cc] - Node 56c30ee1-a08f-4ad6-af72-a73d1143c2cc[56c30ee1-a08f-4ad6-af72-a73d1143c2cc] monitor closed 
[INFO ] 2024-06-04 15:37:22.256 - [任务 1(100)][56c30ee1-a08f-4ad6-af72-a73d1143c2cc] - Node 56c30ee1-a08f-4ad6-af72-a73d1143c2cc[56c30ee1-a08f-4ad6-af72-a73d1143c2cc] close complete, cost 2 ms 
[INFO ] 2024-06-04 15:37:22.466 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-56c30ee1-a08f-4ad6-af72-a73d1143c2cc complete, cost 544ms 
[INFO ] 2024-06-04 15:37:34.420 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:34.420 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:34.421 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:34.422 - [任务 1(100)][b9854666-555e-4a74-b9aa-6177dfe3361f] - Node b9854666-555e-4a74-b9aa-6177dfe3361f[b9854666-555e-4a74-b9aa-6177dfe3361f] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:37:34.422 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:34.425 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:34.425 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:34.428 - [任务 1(100)][b9854666-555e-4a74-b9aa-6177dfe3361f] - Node b9854666-555e-4a74-b9aa-6177dfe3361f[b9854666-555e-4a74-b9aa-6177dfe3361f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:34.639 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:37:34.643 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:37:34.647 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:34.648 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:34.648 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:37:34.649 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:37:34.649 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 18 ms 
[INFO ] 2024-06-04 15:37:34.745 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:37:34.746 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:37:34.746 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 111 ms 
[INFO ] 2024-06-04 15:37:34.821 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:37:34.824 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-2594a1f4-4eb9-4444-a361-8602ec03561b 
[INFO ] 2024-06-04 15:37:34.824 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-2594a1f4-4eb9-4444-a361-8602ec03561b 
[INFO ] 2024-06-04 15:37:34.824 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:37:34.825 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:37:34.825 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:37:34.848 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 32 ms 
[INFO ] 2024-06-04 15:37:34.849 - [任务 1(100)][b9854666-555e-4a74-b9aa-6177dfe3361f] - Node b9854666-555e-4a74-b9aa-6177dfe3361f[b9854666-555e-4a74-b9aa-6177dfe3361f] running status set to false 
[INFO ] 2024-06-04 15:37:34.849 - [任务 1(100)][b9854666-555e-4a74-b9aa-6177dfe3361f] - Node b9854666-555e-4a74-b9aa-6177dfe3361f[b9854666-555e-4a74-b9aa-6177dfe3361f] schema data cleaned 
[INFO ] 2024-06-04 15:37:34.850 - [任务 1(100)][b9854666-555e-4a74-b9aa-6177dfe3361f] - Node b9854666-555e-4a74-b9aa-6177dfe3361f[b9854666-555e-4a74-b9aa-6177dfe3361f] monitor closed 
[INFO ] 2024-06-04 15:37:34.853 - [任务 1(100)][b9854666-555e-4a74-b9aa-6177dfe3361f] - Node b9854666-555e-4a74-b9aa-6177dfe3361f[b9854666-555e-4a74-b9aa-6177dfe3361f] close complete, cost 7 ms 
[INFO ] 2024-06-04 15:37:34.854 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-b9854666-555e-4a74-b9aa-6177dfe3361f complete, cost 508ms 
[INFO ] 2024-06-04 15:37:42.365 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:42.367 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:42.367 - [任务 1(100)][7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc] - Node 7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc[7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:37:42.367 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:42.368 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:42.368 - [任务 1(100)][7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc] - Node 7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc[7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:42.368 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:42.368 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:42.609 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:37:42.610 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:37:42.610 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:42.610 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:42.610 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:37:42.611 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:37:42.660 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 11 ms 
[INFO ] 2024-06-04 15:37:42.661 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:37:42.661 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:37:42.661 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 52 ms 
[INFO ] 2024-06-04 15:37:42.725 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:37:42.726 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-7b6b446d-cbd8-409f-81f1-1844f2f18d2d 
[INFO ] 2024-06-04 15:37:42.726 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-7b6b446d-cbd8-409f-81f1-1844f2f18d2d 
[INFO ] 2024-06-04 15:37:42.727 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:37:42.729 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:37:42.730 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:37:42.749 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 21 ms 
[WARN ] 2024-06-04 15:37:42.750 - [任务 1(100)][7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc] - field total_sales already exists in table agg_products 
[INFO ] 2024-06-04 15:37:42.751 - [任务 1(100)][7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc] - Node 7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc[7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc] running status set to false 
[INFO ] 2024-06-04 15:37:42.751 - [任务 1(100)][7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc] - Node 7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc[7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc] schema data cleaned 
[INFO ] 2024-06-04 15:37:42.752 - [任务 1(100)][7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc] - Node 7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc[7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc] monitor closed 
[INFO ] 2024-06-04 15:37:42.752 - [任务 1(100)][7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc] - Node 7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc[7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc] close complete, cost 1 ms 
[INFO ] 2024-06-04 15:37:42.962 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-7ccd6bcb-81bd-4f8a-8bcf-44cdce10b7dc complete, cost 439ms 
[INFO ] 2024-06-04 15:37:46.831 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:46.831 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:46.831 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:46.832 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:46.833 - [任务 1(100)][aa65646a-bec7-4472-b327-ae6d875ad982] - Node aa65646a-bec7-4472-b327-ae6d875ad982[aa65646a-bec7-4472-b327-ae6d875ad982] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:37:46.834 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:46.834 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:46.834 - [任务 1(100)][aa65646a-bec7-4472-b327-ae6d875ad982] - Node aa65646a-bec7-4472-b327-ae6d875ad982[aa65646a-bec7-4472-b327-ae6d875ad982] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:46.903 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:46.903 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:46.903 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:46.903 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:46.903 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:46.906 - [任务 1(100)][87a12e0c-05e8-4b15-91f6-318ad932ed7e] - Node 87a12e0c-05e8-4b15-91f6-318ad932ed7e[87a12e0c-05e8-4b15-91f6-318ad932ed7e] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:37:46.906 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:46.906 - [任务 1(100)][87a12e0c-05e8-4b15-91f6-318ad932ed7e] - Node 87a12e0c-05e8-4b15-91f6-318ad932ed7e[87a12e0c-05e8-4b15-91f6-318ad932ed7e] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:47.154 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:37:47.168 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:37:47.169 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:47.169 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:47.169 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:37:47.175 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:37:47.175 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 22 ms 
[INFO ] 2024-06-04 15:37:47.229 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:37:47.229 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:37:47.330 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 74 ms 
[INFO ] 2024-06-04 15:37:47.331 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:37:47.342 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-be8c6277-9d32-4660-a4fc-2a0b37e234aa 
[INFO ] 2024-06-04 15:37:47.343 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-be8c6277-9d32-4660-a4fc-2a0b37e234aa 
[INFO ] 2024-06-04 15:37:47.344 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:37:47.345 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:37:47.345 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:37:47.372 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 20 ms 
[WARN ] 2024-06-04 15:37:47.372 - [任务 1(100)][aa65646a-bec7-4472-b327-ae6d875ad982] - field total_sales already exists in table agg_products 
[INFO ] 2024-06-04 15:37:47.373 - [任务 1(100)][aa65646a-bec7-4472-b327-ae6d875ad982] - Node aa65646a-bec7-4472-b327-ae6d875ad982[aa65646a-bec7-4472-b327-ae6d875ad982] running status set to false 
[INFO ] 2024-06-04 15:37:47.373 - [任务 1(100)][aa65646a-bec7-4472-b327-ae6d875ad982] - Node aa65646a-bec7-4472-b327-ae6d875ad982[aa65646a-bec7-4472-b327-ae6d875ad982] schema data cleaned 
[INFO ] 2024-06-04 15:37:47.374 - [任务 1(100)][aa65646a-bec7-4472-b327-ae6d875ad982] - Node aa65646a-bec7-4472-b327-ae6d875ad982[aa65646a-bec7-4472-b327-ae6d875ad982] monitor closed 
[INFO ] 2024-06-04 15:37:47.374 - [任务 1(100)][aa65646a-bec7-4472-b327-ae6d875ad982] - Node aa65646a-bec7-4472-b327-ae6d875ad982[aa65646a-bec7-4472-b327-ae6d875ad982] close complete, cost 1 ms 
[INFO ] 2024-06-04 15:37:47.543 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-aa65646a-bec7-4472-b327-ae6d875ad982 complete, cost 632ms 
[INFO ] 2024-06-04 15:37:47.550 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:37:47.551 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:37:47.567 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:47.604 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:47.636 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:37:47.636 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:37:47.637 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-f03798a7-179e-4e6a-9579-ac5edeab413e 
[INFO ] 2024-06-04 15:37:47.637 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-f03798a7-179e-4e6a-9579-ac5edeab413e 
[INFO ] 2024-06-04 15:37:47.637 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:37:47.638 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:37:47.638 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 109 ms 
[INFO ] 2024-06-04 15:37:47.638 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:37:47.638 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:37:47.638 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 68 ms 
[WARN ] 2024-06-04 15:37:47.652 - [任务 1(100)][87a12e0c-05e8-4b15-91f6-318ad932ed7e] - field total_sales already exists in table agg_products 
[INFO ] 2024-06-04 15:37:47.652 - [任务 1(100)][87a12e0c-05e8-4b15-91f6-318ad932ed7e] - Node 87a12e0c-05e8-4b15-91f6-318ad932ed7e[87a12e0c-05e8-4b15-91f6-318ad932ed7e] running status set to false 
[INFO ] 2024-06-04 15:37:47.653 - [任务 1(100)][87a12e0c-05e8-4b15-91f6-318ad932ed7e] - Node 87a12e0c-05e8-4b15-91f6-318ad932ed7e[87a12e0c-05e8-4b15-91f6-318ad932ed7e] schema data cleaned 
[INFO ] 2024-06-04 15:37:47.653 - [任务 1(100)][87a12e0c-05e8-4b15-91f6-318ad932ed7e] - Node 87a12e0c-05e8-4b15-91f6-318ad932ed7e[87a12e0c-05e8-4b15-91f6-318ad932ed7e] monitor closed 
[INFO ] 2024-06-04 15:37:47.710 - [任务 1(100)][87a12e0c-05e8-4b15-91f6-318ad932ed7e] - Node 87a12e0c-05e8-4b15-91f6-318ad932ed7e[87a12e0c-05e8-4b15-91f6-318ad932ed7e] close complete, cost 2 ms 
[INFO ] 2024-06-04 15:37:47.777 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:37:47.779 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:37:47.779 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 191 ms 
[INFO ] 2024-06-04 15:37:47.991 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-87a12e0c-05e8-4b15-91f6-318ad932ed7e complete, cost 938ms 
[INFO ] 2024-06-04 15:37:57.851 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:57.851 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:57.855 - [任务 1(100)][87110dd6-6848-48d1-a2ba-e829d1493e04] - Node 87110dd6-6848-48d1-a2ba-e829d1493e04[87110dd6-6848-48d1-a2ba-e829d1493e04] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:37:57.855 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:37:57.855 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:57.856 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:57.856 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:57.856 - [任务 1(100)][87110dd6-6848-48d1-a2ba-e829d1493e04] - Node 87110dd6-6848-48d1-a2ba-e829d1493e04[87110dd6-6848-48d1-a2ba-e829d1493e04] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:37:58.200 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:37:58.201 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:37:58.206 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:58.208 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:37:58.208 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:37:58.209 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:37:58.209 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 22 ms 
[INFO ] 2024-06-04 15:37:58.255 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:37:58.257 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-b71afb6e-7b57-4b3a-be4c-90dd181ddef2 
[INFO ] 2024-06-04 15:37:58.257 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-b71afb6e-7b57-4b3a-be4c-90dd181ddef2 
[INFO ] 2024-06-04 15:37:58.257 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:37:58.264 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:37:58.264 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:37:58.287 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 23 ms 
[WARN ] 2024-06-04 15:37:58.287 - [任务 1(100)][87110dd6-6848-48d1-a2ba-e829d1493e04] - field total_sales already exists in table agg_products 
[INFO ] 2024-06-04 15:37:58.288 - [任务 1(100)][87110dd6-6848-48d1-a2ba-e829d1493e04] - Node 87110dd6-6848-48d1-a2ba-e829d1493e04[87110dd6-6848-48d1-a2ba-e829d1493e04] running status set to false 
[INFO ] 2024-06-04 15:37:58.289 - [任务 1(100)][87110dd6-6848-48d1-a2ba-e829d1493e04] - Node 87110dd6-6848-48d1-a2ba-e829d1493e04[87110dd6-6848-48d1-a2ba-e829d1493e04] schema data cleaned 
[INFO ] 2024-06-04 15:37:58.289 - [任务 1(100)][87110dd6-6848-48d1-a2ba-e829d1493e04] - Node 87110dd6-6848-48d1-a2ba-e829d1493e04[87110dd6-6848-48d1-a2ba-e829d1493e04] monitor closed 
[INFO ] 2024-06-04 15:37:58.289 - [任务 1(100)][87110dd6-6848-48d1-a2ba-e829d1493e04] - Node 87110dd6-6848-48d1-a2ba-e829d1493e04[87110dd6-6848-48d1-a2ba-e829d1493e04] close complete, cost 1 ms 
[INFO ] 2024-06-04 15:37:58.324 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:37:58.326 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:37:58.326 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 136 ms 
[INFO ] 2024-06-04 15:37:58.535 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-87110dd6-6848-48d1-a2ba-e829d1493e04 complete, cost 592ms 
[INFO ] 2024-06-04 15:38:01.461 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:38:01.461 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:38:01.462 - [任务 1(100)][79d9edd1-5955-4706-ab3f-e5777c51bbbe] - Node 79d9edd1-5955-4706-ab3f-e5777c51bbbe[79d9edd1-5955-4706-ab3f-e5777c51bbbe] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:38:01.462 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:38:01.468 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:38:01.469 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:38:01.471 - [任务 1(100)][79d9edd1-5955-4706-ab3f-e5777c51bbbe] - Node 79d9edd1-5955-4706-ab3f-e5777c51bbbe[79d9edd1-5955-4706-ab3f-e5777c51bbbe] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:38:01.471 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:38:01.499 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:38:01.499 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:38:01.499 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:38:01.499 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:38:01.500 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:38:01.500 - [任务 1(100)][727f8f51-f488-45c7-8f4e-be832b2ad1ca] - Node 727f8f51-f488-45c7-8f4e-be832b2ad1ca[727f8f51-f488-45c7-8f4e-be832b2ad1ca] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:38:01.500 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:38:01.500 - [任务 1(100)][727f8f51-f488-45c7-8f4e-be832b2ad1ca] - Node 727f8f51-f488-45c7-8f4e-be832b2ad1ca[727f8f51-f488-45c7-8f4e-be832b2ad1ca] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:38:01.733 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:38:01.741 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:38:01.742 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:38:01.742 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:38:01.742 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:38:01.743 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:38:01.743 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 18 ms 
[INFO ] 2024-06-04 15:38:01.794 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:38:01.794 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:38:01.794 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 64 ms 
[INFO ] 2024-06-04 15:38:01.961 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:38:01.984 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-f2c3e699-1bce-41a6-93fe-871414fbaf1e 
[INFO ] 2024-06-04 15:38:01.985 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-f2c3e699-1bce-41a6-93fe-871414fbaf1e 
[INFO ] 2024-06-04 15:38:01.985 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:38:01.987 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:38:01.987 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:38:01.987 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 37 ms 
[WARN ] 2024-06-04 15:38:02.029 - [任务 1(100)][79d9edd1-5955-4706-ab3f-e5777c51bbbe] - field total_sales already exists in table agg_products 
[INFO ] 2024-06-04 15:38:02.029 - [任务 1(100)][79d9edd1-5955-4706-ab3f-e5777c51bbbe] - Node 79d9edd1-5955-4706-ab3f-e5777c51bbbe[79d9edd1-5955-4706-ab3f-e5777c51bbbe] running status set to false 
[INFO ] 2024-06-04 15:38:02.029 - [任务 1(100)][79d9edd1-5955-4706-ab3f-e5777c51bbbe] - Node 79d9edd1-5955-4706-ab3f-e5777c51bbbe[79d9edd1-5955-4706-ab3f-e5777c51bbbe] schema data cleaned 
[INFO ] 2024-06-04 15:38:02.030 - [任务 1(100)][79d9edd1-5955-4706-ab3f-e5777c51bbbe] - Node 79d9edd1-5955-4706-ab3f-e5777c51bbbe[79d9edd1-5955-4706-ab3f-e5777c51bbbe] monitor closed 
[INFO ] 2024-06-04 15:38:02.030 - [任务 1(100)][79d9edd1-5955-4706-ab3f-e5777c51bbbe] - Node 79d9edd1-5955-4706-ab3f-e5777c51bbbe[79d9edd1-5955-4706-ab3f-e5777c51bbbe] close complete, cost 0 ms 
[INFO ] 2024-06-04 15:38:02.031 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-79d9edd1-5955-4706-ab3f-e5777c51bbbe complete, cost 646ms 
[INFO ] 2024-06-04 15:38:02.329 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:38:02.330 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:38:02.349 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:38:02.350 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:38:02.350 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:38:02.350 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:38:02.352 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 44 ms 
[INFO ] 2024-06-04 15:38:02.383 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:38:02.385 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-99379210-1b1e-473a-9352-8d84dd9f3d11 
[INFO ] 2024-06-04 15:38:02.389 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-99379210-1b1e-473a-9352-8d84dd9f3d11 
[INFO ] 2024-06-04 15:38:02.390 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:38:02.395 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:38:02.395 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:38:02.424 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 38 ms 
[WARN ] 2024-06-04 15:38:02.425 - [任务 1(100)][727f8f51-f488-45c7-8f4e-be832b2ad1ca] - field total_sales already exists in table agg_products 
[INFO ] 2024-06-04 15:38:02.426 - [任务 1(100)][727f8f51-f488-45c7-8f4e-be832b2ad1ca] - Node 727f8f51-f488-45c7-8f4e-be832b2ad1ca[727f8f51-f488-45c7-8f4e-be832b2ad1ca] running status set to false 
[INFO ] 2024-06-04 15:38:02.427 - [任务 1(100)][727f8f51-f488-45c7-8f4e-be832b2ad1ca] - Node 727f8f51-f488-45c7-8f4e-be832b2ad1ca[727f8f51-f488-45c7-8f4e-be832b2ad1ca] schema data cleaned 
[INFO ] 2024-06-04 15:38:02.428 - [任务 1(100)][727f8f51-f488-45c7-8f4e-be832b2ad1ca] - Node 727f8f51-f488-45c7-8f4e-be832b2ad1ca[727f8f51-f488-45c7-8f4e-be832b2ad1ca] monitor closed 
[INFO ] 2024-06-04 15:38:02.428 - [任务 1(100)][727f8f51-f488-45c7-8f4e-be832b2ad1ca] - Node 727f8f51-f488-45c7-8f4e-be832b2ad1ca[727f8f51-f488-45c7-8f4e-be832b2ad1ca] close complete, cost 2 ms 
[INFO ] 2024-06-04 15:38:02.485 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:38:02.487 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:38:02.488 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 174 ms 
[INFO ] 2024-06-04 15:38:02.695 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-727f8f51-f488-45c7-8f4e-be832b2ad1ca complete, cost 1050ms 
[INFO ] 2024-06-04 15:38:11.641 - [任务 1(100)][763fa95b-9753-4f99-80e8-fade29008618] - Node 763fa95b-9753-4f99-80e8-fade29008618[763fa95b-9753-4f99-80e8-fade29008618] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:38:11.641 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:38:11.641 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:38:11.643 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:38:11.643 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:38:11.643 - [任务 1(100)][763fa95b-9753-4f99-80e8-fade29008618] - Node 763fa95b-9753-4f99-80e8-fade29008618[763fa95b-9753-4f99-80e8-fade29008618] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:38:11.643 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:38:11.846 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:38:11.904 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:38:11.904 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:38:11.913 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:38:11.913 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:38:11.913 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:38:11.913 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:38:11.914 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 19 ms 
[INFO ] 2024-06-04 15:38:11.969 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:38:11.975 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:38:11.976 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 76 ms 
[INFO ] 2024-06-04 15:38:11.990 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:38:12.003 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-581700be-b856-4b17-8c81-786fd724eb97 
[INFO ] 2024-06-04 15:38:12.003 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-581700be-b856-4b17-8c81-786fd724eb97 
[INFO ] 2024-06-04 15:38:12.004 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:38:12.009 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:38:12.009 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:38:12.009 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 22 ms 
[WARN ] 2024-06-04 15:38:12.023 - [任务 1(100)][763fa95b-9753-4f99-80e8-fade29008618] - field total_sales already exists in table agg_products 
[INFO ] 2024-06-04 15:38:12.025 - [任务 1(100)][763fa95b-9753-4f99-80e8-fade29008618] - Node 763fa95b-9753-4f99-80e8-fade29008618[763fa95b-9753-4f99-80e8-fade29008618] running status set to false 
[INFO ] 2024-06-04 15:38:12.025 - [任务 1(100)][763fa95b-9753-4f99-80e8-fade29008618] - Node 763fa95b-9753-4f99-80e8-fade29008618[763fa95b-9753-4f99-80e8-fade29008618] schema data cleaned 
[INFO ] 2024-06-04 15:38:12.025 - [任务 1(100)][763fa95b-9753-4f99-80e8-fade29008618] - Node 763fa95b-9753-4f99-80e8-fade29008618[763fa95b-9753-4f99-80e8-fade29008618] monitor closed 
[INFO ] 2024-06-04 15:38:12.025 - [任务 1(100)][763fa95b-9753-4f99-80e8-fade29008618] - Node 763fa95b-9753-4f99-80e8-fade29008618[763fa95b-9753-4f99-80e8-fade29008618] close complete, cost 1 ms 
[INFO ] 2024-06-04 15:38:12.237 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-763fa95b-9753-4f99-80e8-fade29008618 complete, cost 528ms 
[INFO ] 2024-06-04 15:39:50.816 - [任务 1(100)][5a139c7a-a149-490f-be49-7ea6bf82270a] - Node 5a139c7a-a149-490f-be49-7ea6bf82270a[5a139c7a-a149-490f-be49-7ea6bf82270a] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:39:50.816 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:39:50.816 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:39:50.824 - [任务 1(100)][5a139c7a-a149-490f-be49-7ea6bf82270a] - Node 5a139c7a-a149-490f-be49-7ea6bf82270a[5a139c7a-a149-490f-be49-7ea6bf82270a] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:39:50.825 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:39:50.825 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:39:50.825 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:39:50.826 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:39:51.212 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:39:51.212 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:39:51.222 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:39:51.222 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:39:51.225 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:39:51.225 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:39:51.300 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 24 ms 
[INFO ] 2024-06-04 15:39:51.300 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:39:51.313 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-fdbc9f9a-ade7-4846-a944-7b9d318858c8 
[INFO ] 2024-06-04 15:39:51.313 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-fdbc9f9a-ade7-4846-a944-7b9d318858c8 
[INFO ] 2024-06-04 15:39:51.313 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:39:51.315 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:39:51.316 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:39:51.316 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 19 ms 
[INFO ] 2024-06-04 15:39:51.325 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:39:51.326 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:39:51.326 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 114 ms 
[WARN ] 2024-06-04 15:39:51.330 - [任务 1(100)][5a139c7a-a149-490f-be49-7ea6bf82270a] - field total_sales already exists in table agg_products 
[INFO ] 2024-06-04 15:39:51.330 - [任务 1(100)][5a139c7a-a149-490f-be49-7ea6bf82270a] - Node 5a139c7a-a149-490f-be49-7ea6bf82270a[5a139c7a-a149-490f-be49-7ea6bf82270a] running status set to false 
[INFO ] 2024-06-04 15:39:51.330 - [任务 1(100)][5a139c7a-a149-490f-be49-7ea6bf82270a] - Node 5a139c7a-a149-490f-be49-7ea6bf82270a[5a139c7a-a149-490f-be49-7ea6bf82270a] schema data cleaned 
[INFO ] 2024-06-04 15:39:51.330 - [任务 1(100)][5a139c7a-a149-490f-be49-7ea6bf82270a] - Node 5a139c7a-a149-490f-be49-7ea6bf82270a[5a139c7a-a149-490f-be49-7ea6bf82270a] monitor closed 
[INFO ] 2024-06-04 15:39:51.330 - [任务 1(100)][5a139c7a-a149-490f-be49-7ea6bf82270a] - Node 5a139c7a-a149-490f-be49-7ea6bf82270a[5a139c7a-a149-490f-be49-7ea6bf82270a] close complete, cost 0 ms 
[INFO ] 2024-06-04 15:39:51.537 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-5a139c7a-a149-490f-be49-7ea6bf82270a complete, cost 621ms 
[INFO ] 2024-06-04 15:39:52.601 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:39:52.602 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:39:52.602 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:39:52.603 - [任务 1(100)][7608249d-3aa7-4d04-8c49-97e267d41cb0] - Node 7608249d-3aa7-4d04-8c49-97e267d41cb0[7608249d-3aa7-4d04-8c49-97e267d41cb0] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:39:52.603 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:39:52.604 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 1 ms 
[INFO ] 2024-06-04 15:39:52.604 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:39:52.604 - [任务 1(100)][7608249d-3aa7-4d04-8c49-97e267d41cb0] - Node 7608249d-3aa7-4d04-8c49-97e267d41cb0[7608249d-3aa7-4d04-8c49-97e267d41cb0] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:39:52.848 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:39:52.856 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:39:52.860 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:39:52.860 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:39:52.860 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:39:52.860 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:39:52.914 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 15 ms 
[INFO ] 2024-06-04 15:39:52.914 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:39:52.914 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:39:52.914 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 67 ms 
[INFO ] 2024-06-04 15:39:52.967 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:39:52.976 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-f158b8f7-0f7e-4517-a6d0-6c6064b06154 
[INFO ] 2024-06-04 15:39:52.977 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-f158b8f7-0f7e-4517-a6d0-6c6064b06154 
[INFO ] 2024-06-04 15:39:52.977 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:39:52.979 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:39:52.979 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:39:52.997 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 14 ms 
[WARN ] 2024-06-04 15:39:52.998 - [任务 1(100)][7608249d-3aa7-4d04-8c49-97e267d41cb0] - field total_sales already exists in table agg_products 
[INFO ] 2024-06-04 15:39:52.998 - [任务 1(100)][7608249d-3aa7-4d04-8c49-97e267d41cb0] - Node 7608249d-3aa7-4d04-8c49-97e267d41cb0[7608249d-3aa7-4d04-8c49-97e267d41cb0] running status set to false 
[INFO ] 2024-06-04 15:39:52.998 - [任务 1(100)][7608249d-3aa7-4d04-8c49-97e267d41cb0] - Node 7608249d-3aa7-4d04-8c49-97e267d41cb0[7608249d-3aa7-4d04-8c49-97e267d41cb0] schema data cleaned 
[INFO ] 2024-06-04 15:39:52.998 - [任务 1(100)][7608249d-3aa7-4d04-8c49-97e267d41cb0] - Node 7608249d-3aa7-4d04-8c49-97e267d41cb0[7608249d-3aa7-4d04-8c49-97e267d41cb0] monitor closed 
[INFO ] 2024-06-04 15:39:52.998 - [任务 1(100)][7608249d-3aa7-4d04-8c49-97e267d41cb0] - Node 7608249d-3aa7-4d04-8c49-97e267d41cb0[7608249d-3aa7-4d04-8c49-97e267d41cb0] close complete, cost 1 ms 
[INFO ] 2024-06-04 15:39:53.204 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-7608249d-3aa7-4d04-8c49-97e267d41cb0 complete, cost 467ms 
[INFO ] 2024-06-04 15:40:06.841 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:40:06.846 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:40:06.847 - [任务 1(100)][6203f3e8-3938-4c82-803e-bbfdfe5553cf] - Node 6203f3e8-3938-4c82-803e-bbfdfe5553cf[6203f3e8-3938-4c82-803e-bbfdfe5553cf] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:40:06.847 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:40:06.847 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:40:06.847 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:40:06.849 - [任务 1(100)][6203f3e8-3938-4c82-803e-bbfdfe5553cf] - Node 6203f3e8-3938-4c82-803e-bbfdfe5553cf[6203f3e8-3938-4c82-803e-bbfdfe5553cf] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:40:06.849 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:40:07.088 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:40:07.099 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:40:07.100 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:40:07.104 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:40:07.104 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:40:07.105 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:40:07.105 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 18 ms 
[INFO ] 2024-06-04 15:40:07.212 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:40:07.215 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:40:07.216 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 118 ms 
[INFO ] 2024-06-04 15:40:07.263 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:40:07.285 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-69c4996a-7528-4a59-bae2-c951db26355f 
[INFO ] 2024-06-04 15:40:07.285 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-69c4996a-7528-4a59-bae2-c951db26355f 
[INFO ] 2024-06-04 15:40:07.289 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:40:07.290 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:40:07.290 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:40:07.290 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 39 ms 
[INFO ] 2024-06-04 15:40:07.391 - [任务 1(100)][6203f3e8-3938-4c82-803e-bbfdfe5553cf] - Node 6203f3e8-3938-4c82-803e-bbfdfe5553cf[6203f3e8-3938-4c82-803e-bbfdfe5553cf] running status set to false 
[INFO ] 2024-06-04 15:40:07.391 - [任务 1(100)][6203f3e8-3938-4c82-803e-bbfdfe5553cf] - Node 6203f3e8-3938-4c82-803e-bbfdfe5553cf[6203f3e8-3938-4c82-803e-bbfdfe5553cf] schema data cleaned 
[INFO ] 2024-06-04 15:40:07.391 - [任务 1(100)][6203f3e8-3938-4c82-803e-bbfdfe5553cf] - Node 6203f3e8-3938-4c82-803e-bbfdfe5553cf[6203f3e8-3938-4c82-803e-bbfdfe5553cf] monitor closed 
[INFO ] 2024-06-04 15:40:07.391 - [任务 1(100)][6203f3e8-3938-4c82-803e-bbfdfe5553cf] - Node 6203f3e8-3938-4c82-803e-bbfdfe5553cf[6203f3e8-3938-4c82-803e-bbfdfe5553cf] close complete, cost 8 ms 
[INFO ] 2024-06-04 15:40:07.393 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-6203f3e8-3938-4c82-803e-bbfdfe5553cf complete, cost 620ms 
[INFO ] 2024-06-04 15:40:07.439 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:40:07.439 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:40:07.439 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:40:07.439 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:40:07.439 - [任务 1(100)][044763e0-3d06-4e5c-890c-4c8b882561f0] - Node 044763e0-3d06-4e5c-890c-4c8b882561f0[044763e0-3d06-4e5c-890c-4c8b882561f0] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:40:07.439 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:40:07.440 - [任务 1(100)][044763e0-3d06-4e5c-890c-4c8b882561f0] - Node 044763e0-3d06-4e5c-890c-4c8b882561f0[044763e0-3d06-4e5c-890c-4c8b882561f0] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:40:07.440 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:40:07.654 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:40:07.662 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:40:07.662 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:40:07.662 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:40:07.662 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:40:07.662 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:40:07.663 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 10 ms 
[INFO ] 2024-06-04 15:40:07.866 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:40:07.868 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:40:07.869 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 208 ms 
[INFO ] 2024-06-04 15:40:07.939 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:40:07.966 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-f3b9ee03-9614-48ab-a008-c5156a3b416f 
[INFO ] 2024-06-04 15:40:07.967 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-f3b9ee03-9614-48ab-a008-c5156a3b416f 
[INFO ] 2024-06-04 15:40:07.967 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:40:07.967 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:40:07.967 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:40:07.968 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 36 ms 
[INFO ] 2024-06-04 15:40:07.988 - [任务 1(100)][044763e0-3d06-4e5c-890c-4c8b882561f0] - Node 044763e0-3d06-4e5c-890c-4c8b882561f0[044763e0-3d06-4e5c-890c-4c8b882561f0] running status set to false 
[INFO ] 2024-06-04 15:40:07.988 - [任务 1(100)][044763e0-3d06-4e5c-890c-4c8b882561f0] - Node 044763e0-3d06-4e5c-890c-4c8b882561f0[044763e0-3d06-4e5c-890c-4c8b882561f0] schema data cleaned 
[INFO ] 2024-06-04 15:40:07.988 - [任务 1(100)][044763e0-3d06-4e5c-890c-4c8b882561f0] - Node 044763e0-3d06-4e5c-890c-4c8b882561f0[044763e0-3d06-4e5c-890c-4c8b882561f0] monitor closed 
[INFO ] 2024-06-04 15:40:07.988 - [任务 1(100)][044763e0-3d06-4e5c-890c-4c8b882561f0] - Node 044763e0-3d06-4e5c-890c-4c8b882561f0[044763e0-3d06-4e5c-890c-4c8b882561f0] close complete, cost 4 ms 
[INFO ] 2024-06-04 15:40:08.004 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-044763e0-3d06-4e5c-890c-4c8b882561f0 complete, cost 627ms 
[INFO ] 2024-06-04 15:40:12.322 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:40:12.322 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:40:12.322 - [任务 1(100)][dda683b8-0009-4e14-a62c-dd0211f97f89] - Node dda683b8-0009-4e14-a62c-dd0211f97f89[dda683b8-0009-4e14-a62c-dd0211f97f89] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:40:12.322 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:40:12.325 - [任务 1(100)][dda683b8-0009-4e14-a62c-dd0211f97f89] - Node dda683b8-0009-4e14-a62c-dd0211f97f89[dda683b8-0009-4e14-a62c-dd0211f97f89] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:40:12.326 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:40:12.326 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:40:12.326 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:40:12.564 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:40:12.564 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:40:12.564 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:40:12.564 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:40:12.565 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:40:12.565 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:40:12.610 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 9 ms 
[INFO ] 2024-06-04 15:40:12.610 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:40:12.610 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:40:12.610 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 48 ms 
[INFO ] 2024-06-04 15:40:12.701 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:40:12.702 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-35244e4c-db64-4952-9601-f2add3fd8b86 
[INFO ] 2024-06-04 15:40:12.702 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-35244e4c-db64-4952-9601-f2add3fd8b86 
[INFO ] 2024-06-04 15:40:12.704 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:40:12.704 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:40:12.705 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:40:12.705 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 15 ms 
[INFO ] 2024-06-04 15:40:12.722 - [任务 1(100)][dda683b8-0009-4e14-a62c-dd0211f97f89] - Node dda683b8-0009-4e14-a62c-dd0211f97f89[dda683b8-0009-4e14-a62c-dd0211f97f89] running status set to false 
[INFO ] 2024-06-04 15:40:12.722 - [任务 1(100)][dda683b8-0009-4e14-a62c-dd0211f97f89] - Node dda683b8-0009-4e14-a62c-dd0211f97f89[dda683b8-0009-4e14-a62c-dd0211f97f89] schema data cleaned 
[INFO ] 2024-06-04 15:40:12.722 - [任务 1(100)][dda683b8-0009-4e14-a62c-dd0211f97f89] - Node dda683b8-0009-4e14-a62c-dd0211f97f89[dda683b8-0009-4e14-a62c-dd0211f97f89] monitor closed 
[INFO ] 2024-06-04 15:40:12.722 - [任务 1(100)][dda683b8-0009-4e14-a62c-dd0211f97f89] - Node dda683b8-0009-4e14-a62c-dd0211f97f89[dda683b8-0009-4e14-a62c-dd0211f97f89] close complete, cost 1 ms 
[INFO ] 2024-06-04 15:40:12.934 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-dda683b8-0009-4e14-a62c-dd0211f97f89 complete, cost 460ms 
[INFO ] 2024-06-04 15:40:18.351 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:40:18.351 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:40:18.351 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:40:18.354 - [任务 1(100)][f9f442cf-912a-4da5-8954-b21e2689cea5] - Node f9f442cf-912a-4da5-8954-b21e2689cea5[f9f442cf-912a-4da5-8954-b21e2689cea5] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:40:18.355 - [任务 1(100)][f9f442cf-912a-4da5-8954-b21e2689cea5] - Node f9f442cf-912a-4da5-8954-b21e2689cea5[f9f442cf-912a-4da5-8954-b21e2689cea5] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:40:18.355 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 1 ms 
[INFO ] 2024-06-04 15:40:18.355 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:40:18.357 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 1 ms 
[INFO ] 2024-06-04 15:40:18.846 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:40:18.849 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:40:18.876 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:40:18.893 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:40:18.893 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:40:18.893 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:40:18.893 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:40:18.894 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 42 ms 
[INFO ] 2024-06-04 15:40:18.897 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-362f2424-4c69-4ef5-a618-705e63280c6b 
[INFO ] 2024-06-04 15:40:18.898 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-362f2424-4c69-4ef5-a618-705e63280c6b 
[INFO ] 2024-06-04 15:40:18.898 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:40:18.902 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:40:18.902 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:40:18.932 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 30 ms 
[INFO ] 2024-06-04 15:40:18.933 - [任务 1(100)][f9f442cf-912a-4da5-8954-b21e2689cea5] - Node f9f442cf-912a-4da5-8954-b21e2689cea5[f9f442cf-912a-4da5-8954-b21e2689cea5] running status set to false 
[INFO ] 2024-06-04 15:40:18.933 - [任务 1(100)][f9f442cf-912a-4da5-8954-b21e2689cea5] - Node f9f442cf-912a-4da5-8954-b21e2689cea5[f9f442cf-912a-4da5-8954-b21e2689cea5] schema data cleaned 
[INFO ] 2024-06-04 15:40:18.933 - [任务 1(100)][f9f442cf-912a-4da5-8954-b21e2689cea5] - Node f9f442cf-912a-4da5-8954-b21e2689cea5[f9f442cf-912a-4da5-8954-b21e2689cea5] monitor closed 
[INFO ] 2024-06-04 15:40:18.933 - [任务 1(100)][f9f442cf-912a-4da5-8954-b21e2689cea5] - Node f9f442cf-912a-4da5-8954-b21e2689cea5[f9f442cf-912a-4da5-8954-b21e2689cea5] close complete, cost 3 ms 
[INFO ] 2024-06-04 15:40:18.982 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:40:18.983 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:40:18.983 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 149 ms 
[INFO ] 2024-06-04 15:40:19.194 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-f9f442cf-912a-4da5-8954-b21e2689cea5 complete, cost 744ms 
[INFO ] 2024-06-04 15:41:11.105 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:41:11.105 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:41:11.107 - [任务 1(100)][5fb5446d-8837-4582-9ce5-a8662191299a] - Node 5fb5446d-8837-4582-9ce5-a8662191299a[5fb5446d-8837-4582-9ce5-a8662191299a] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:41:11.107 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:41:11.111 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 2 ms 
[INFO ] 2024-06-04 15:41:11.111 - [任务 1(100)][5fb5446d-8837-4582-9ce5-a8662191299a] - Node 5fb5446d-8837-4582-9ce5-a8662191299a[5fb5446d-8837-4582-9ce5-a8662191299a] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:41:11.112 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:41:11.112 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:41:11.592 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:41:11.592 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:41:11.592 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:41:11.592 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:41:11.593 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:41:11.593 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:41:11.593 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 12 ms 
[INFO ] 2024-06-04 15:41:11.611 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:41:11.631 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-2fd91510-b1e2-4248-a8a2-7e7df967c2c1 
[INFO ] 2024-06-04 15:41:11.631 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-2fd91510-b1e2-4248-a8a2-7e7df967c2c1 
[INFO ] 2024-06-04 15:41:11.631 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:41:11.635 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:41:11.635 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:41:11.653 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 26 ms 
[INFO ] 2024-06-04 15:41:11.653 - [任务 1(100)][5fb5446d-8837-4582-9ce5-a8662191299a] - Node 5fb5446d-8837-4582-9ce5-a8662191299a[5fb5446d-8837-4582-9ce5-a8662191299a] running status set to false 
[INFO ] 2024-06-04 15:41:11.653 - [任务 1(100)][5fb5446d-8837-4582-9ce5-a8662191299a] - Node 5fb5446d-8837-4582-9ce5-a8662191299a[5fb5446d-8837-4582-9ce5-a8662191299a] schema data cleaned 
[INFO ] 2024-06-04 15:41:11.653 - [任务 1(100)][5fb5446d-8837-4582-9ce5-a8662191299a] - Node 5fb5446d-8837-4582-9ce5-a8662191299a[5fb5446d-8837-4582-9ce5-a8662191299a] monitor closed 
[INFO ] 2024-06-04 15:41:11.654 - [任务 1(100)][5fb5446d-8837-4582-9ce5-a8662191299a] - Node 5fb5446d-8837-4582-9ce5-a8662191299a[5fb5446d-8837-4582-9ce5-a8662191299a] close complete, cost 2 ms 
[INFO ] 2024-06-04 15:41:11.714 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:41:11.718 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:41:11.718 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 126 ms 
[INFO ] 2024-06-04 15:41:11.929 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-5fb5446d-8837-4582-9ce5-a8662191299a complete, cost 710ms 
[INFO ] 2024-06-04 15:41:36.794 - [任务 1(100)][bdc04fc8-236f-471a-a3a3-65f938dd295c] - Node bdc04fc8-236f-471a-a3a3-65f938dd295c[bdc04fc8-236f-471a-a3a3-65f938dd295c] start preload schema,table counts: 0 
[INFO ] 2024-06-04 15:41:36.794 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:41:36.794 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:41:36.795 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:41:36.796 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] preload schema finished, cost 1 ms 
[INFO ] 2024-06-04 15:41:36.796 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:41:36.796 - [任务 1(100)][bdc04fc8-236f-471a-a3a3-65f938dd295c] - Node bdc04fc8-236f-471a-a3a3-65f938dd295c[bdc04fc8-236f-471a-a3a3-65f938dd295c] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:41:36.796 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-04 15:41:37.074 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] running status set to false 
[INFO ] 2024-06-04 15:41:37.074 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] running status set to false 
[INFO ] 2024-06-04 15:41:37.087 - [任务 1(100)][agg_products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:41:37.087 - [任务 1(100)][agg_products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8da4fb78-2c13-4352-945f-fc7b8f90055f 
[INFO ] 2024-06-04 15:41:37.087 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] schema data cleaned 
[INFO ] 2024-06-04 15:41:37.088 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] monitor closed 
[INFO ] 2024-06-04 15:41:37.136 - [任务 1(100)][agg_products] - Node agg_products[8da4fb78-2c13-4352-945f-fc7b8f90055f] close complete, cost 16 ms 
[INFO ] 2024-06-04 15:41:37.136 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] schema data cleaned 
[INFO ] 2024-06-04 15:41:37.136 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] monitor closed 
[INFO ] 2024-06-04 15:41:37.136 - [任务 1(100)][增删字段] - Node 增删字段[1adf806e-6bff-4747-9b7f-beec0788899f] close complete, cost 63 ms 
[INFO ] 2024-06-04 15:41:37.180 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] running status set to false 
[INFO ] 2024-06-04 15:41:37.186 - [任务 1(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-015e58ea-9377-4054-92f5-1a3ca1201f4f 
[INFO ] 2024-06-04 15:41:37.187 - [任务 1(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-015e58ea-9377-4054-92f5-1a3ca1201f4f 
[INFO ] 2024-06-04 15:41:37.187 - [任务 1(100)][增强JS] - [ScriptExecutorsManager-665eb1b29e541e4fee4f8893-54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6-665eb04d9e541e4fee4f87c5] schema data cleaned 
[INFO ] 2024-06-04 15:41:37.191 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] schema data cleaned 
[INFO ] 2024-06-04 15:41:37.191 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] monitor closed 
[INFO ] 2024-06-04 15:41:37.204 - [任务 1(100)][增强JS] - Node 增强JS[54f1a1a6-22ee-4af1-bf0a-e1fe75c29ca6] close complete, cost 12 ms 
[INFO ] 2024-06-04 15:41:37.204 - [任务 1(100)][bdc04fc8-236f-471a-a3a3-65f938dd295c] - Node bdc04fc8-236f-471a-a3a3-65f938dd295c[bdc04fc8-236f-471a-a3a3-65f938dd295c] running status set to false 
[INFO ] 2024-06-04 15:41:37.204 - [任务 1(100)][bdc04fc8-236f-471a-a3a3-65f938dd295c] - Node bdc04fc8-236f-471a-a3a3-65f938dd295c[bdc04fc8-236f-471a-a3a3-65f938dd295c] schema data cleaned 
[INFO ] 2024-06-04 15:41:37.204 - [任务 1(100)][bdc04fc8-236f-471a-a3a3-65f938dd295c] - Node bdc04fc8-236f-471a-a3a3-65f938dd295c[bdc04fc8-236f-471a-a3a3-65f938dd295c] monitor closed 
[INFO ] 2024-06-04 15:41:37.206 - [任务 1(100)][bdc04fc8-236f-471a-a3a3-65f938dd295c] - Node bdc04fc8-236f-471a-a3a3-65f938dd295c[bdc04fc8-236f-471a-a3a3-65f938dd295c] close complete, cost 2 ms 
[INFO ] 2024-06-04 15:41:37.206 - [任务 1(100)] - load tapTable task 665eb1b29e541e4fee4f8893-bdc04fc8-236f-471a-a3a3-65f938dd295c complete, cost 518ms 
