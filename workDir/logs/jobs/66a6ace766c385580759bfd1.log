[INFO ] 2024-07-29 04:42:02.633 - [TestCKEnum] - Task initialization... 
[INFO ] 2024-07-29 04:42:02.634 - [TestCKEnum] - Start task milestones: 66a6ace766c385580759bfd1(TestCKEnum) 
[INFO ] 2024-07-29 04:42:02.768 - [TestCKEnum] - No<PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-29 04:42:02.768 - [TestCKEnum] - The engine receives TestCKEnum task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-29 04:42:02.802 - [TestCKEnum][t_enum] - Node t_enum[366706c4-2c2d-4ea1-93de-ee0a0e605836] start preload schema,table counts: 1 
[INFO ] 2024-07-29 04:42:02.802 - [TestCKEnum][T_Enum] - Node T_Enum[7a756bf5-2380-4e0e-a737-b5ddf828e4a8] start preload schema,table counts: 1 
[INFO ] 2024-07-29 04:42:02.803 - [TestCKEnum][T_Enum] - Node T_Enum[7a756bf5-2380-4e0e-a737-b5ddf828e4a8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 04:42:02.803 - [TestCKEnum][t_enum] - Node t_enum[366706c4-2c2d-4ea1-93de-ee0a0e605836] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 04:42:03.549 - [TestCKEnum][t_enum] - Source node "t_enum" read batch size: 100 
[INFO ] 2024-07-29 04:42:03.551 - [TestCKEnum][t_enum] - Source node "t_enum" event queue capacity: 200 
[INFO ] 2024-07-29 04:42:03.551 - [TestCKEnum][t_enum] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-29 04:42:03.551 - [TestCKEnum][t_enum] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-29 04:42:03.551 - [TestCKEnum][t_enum] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-29 04:42:03.634 - [TestCKEnum][t_enum] - Initial sync started 
[INFO ] 2024-07-29 04:42:03.634 - [TestCKEnum][t_enum] - Starting batch read, table name: t_enum, offset: null 
[INFO ] 2024-07-29 04:42:03.636 - [TestCKEnum][t_enum] - Table t_enum is going to be initial synced 
[INFO ] 2024-07-29 04:42:03.665 - [TestCKEnum][t_enum] - Table [t_enum] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-29 04:42:03.665 - [TestCKEnum][t_enum] - Query table 't_enum' counts: 3 
[INFO ] 2024-07-29 04:42:03.804 - [TestCKEnum][t_enum] - Initial sync completed 
[INFO ] 2024-07-29 04:42:03.804 - [TestCKEnum][T_Enum] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-29 04:42:03.874 - [TestCKEnum][T_Enum] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: T_Enum 
[ERROR] 2024-07-29 04:42:03.876 - [TestCKEnum][T_Enum] - Unknown exception occur when operate table: T_Enum <-- Error Message -->
Unknown exception occur when operate table: T_Enum

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'x' used in key specification without a key length
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'x' used in key specification without a key length
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$createTargetIndex$7(HazelcastTargetPdkDataNode.java:269)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTargetIndex(HazelcastTargetPdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:149)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'x' used in key specification without a key length
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$6(HazelcastTargetPdkDataNode.java:271)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 28 more

[INFO ] 2024-07-29 04:42:04.079 - [TestCKEnum][T_Enum] - Job suspend in error handle 
[INFO ] 2024-07-29 04:42:04.169 - [TestCKEnum][t_enum] - Node t_enum[366706c4-2c2d-4ea1-93de-ee0a0e605836] running status set to false 
[INFO ] 2024-07-29 04:42:04.200 - [TestCKEnum][t_enum] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-29 04:42:04.200 - [TestCKEnum][t_enum] - Clickhouse Optimize Table end 
[INFO ] 2024-07-29 04:42:04.231 - [TestCKEnum][t_enum] - PDK connector node stopped: HazelcastSourcePdkDataNode-366706c4-2c2d-4ea1-93de-ee0a0e605836 
[INFO ] 2024-07-29 04:42:04.231 - [TestCKEnum][t_enum] - PDK connector node released: HazelcastSourcePdkDataNode-366706c4-2c2d-4ea1-93de-ee0a0e605836 
[INFO ] 2024-07-29 04:42:04.231 - [TestCKEnum][t_enum] - Node t_enum[366706c4-2c2d-4ea1-93de-ee0a0e605836] schema data cleaned 
[INFO ] 2024-07-29 04:42:04.231 - [TestCKEnum][t_enum] - Node t_enum[366706c4-2c2d-4ea1-93de-ee0a0e605836] monitor closed 
[INFO ] 2024-07-29 04:42:04.232 - [TestCKEnum][t_enum] - Node t_enum[366706c4-2c2d-4ea1-93de-ee0a0e605836] close complete, cost 63 ms 
[INFO ] 2024-07-29 04:42:04.244 - [TestCKEnum][T_Enum] - Node T_Enum[7a756bf5-2380-4e0e-a737-b5ddf828e4a8] running status set to false 
[INFO ] 2024-07-29 04:42:04.244 - [TestCKEnum][T_Enum] - PDK connector node stopped: HazelcastTargetPdkDataNode-7a756bf5-2380-4e0e-a737-b5ddf828e4a8 
[INFO ] 2024-07-29 04:42:04.244 - [TestCKEnum][T_Enum] - PDK connector node released: HazelcastTargetPdkDataNode-7a756bf5-2380-4e0e-a737-b5ddf828e4a8 
[INFO ] 2024-07-29 04:42:04.244 - [TestCKEnum][T_Enum] - Node T_Enum[7a756bf5-2380-4e0e-a737-b5ddf828e4a8] schema data cleaned 
[INFO ] 2024-07-29 04:42:04.244 - [TestCKEnum][T_Enum] - Node T_Enum[7a756bf5-2380-4e0e-a737-b5ddf828e4a8] monitor closed 
[INFO ] 2024-07-29 04:42:04.244 - [TestCKEnum][T_Enum] - Node T_Enum[7a756bf5-2380-4e0e-a737-b5ddf828e4a8] close complete, cost 11 ms 
[INFO ] 2024-07-29 04:42:07.837 - [TestCKEnum] - Task [TestCKEnum] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-29 04:42:07.844 - [TestCKEnum] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-29 04:42:07.860 - [TestCKEnum] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@dfd9edb 
[INFO ] 2024-07-29 04:42:07.979 - [TestCKEnum] - Stop task milestones: 66a6ace766c385580759bfd1(TestCKEnum)  
[INFO ] 2024-07-29 04:42:07.979 - [TestCKEnum] - Stopped task aspect(s) 
[INFO ] 2024-07-29 04:42:07.979 - [TestCKEnum] - Snapshot order controller have been removed 
[INFO ] 2024-07-29 04:42:07.992 - [TestCKEnum] - Remove memory task client succeed, task: TestCKEnum[66a6ace766c385580759bfd1] 
[INFO ] 2024-07-29 04:42:07.995 - [TestCKEnum] - Destroy memory task client cache succeed, task: TestCKEnum[66a6ace766c385580759bfd1] 
[INFO ] 2024-07-29 04:43:28.023 - [TestCKEnum] - Task initialization... 
[INFO ] 2024-07-29 04:43:28.088 - [TestCKEnum] - Start task milestones: 66a6ace766c385580759bfd1(TestCKEnum) 
[INFO ] 2024-07-29 04:43:28.088 - [TestCKEnum] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-29 04:43:28.145 - [TestCKEnum] - The engine receives TestCKEnum task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-29 04:43:28.145 - [TestCKEnum][t_enum] - Node t_enum[366706c4-2c2d-4ea1-93de-ee0a0e605836] start preload schema,table counts: 1 
[INFO ] 2024-07-29 04:43:28.145 - [TestCKEnum][TargetEnum] - Node TargetEnum[c7528103-21ef-4b70-9e31-826e592de9fe] start preload schema,table counts: 1 
[INFO ] 2024-07-29 04:43:28.146 - [TestCKEnum][t_enum] - Node t_enum[366706c4-2c2d-4ea1-93de-ee0a0e605836] preload schema finished, cost 1 ms 
[INFO ] 2024-07-29 04:43:28.347 - [TestCKEnum][TargetEnum] - Node TargetEnum[c7528103-21ef-4b70-9e31-826e592de9fe] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 04:43:28.725 - [TestCKEnum][t_enum] - Source node "t_enum" read batch size: 100 
[INFO ] 2024-07-29 04:43:28.726 - [TestCKEnum][t_enum] - Source node "t_enum" event queue capacity: 200 
[INFO ] 2024-07-29 04:43:28.726 - [TestCKEnum][t_enum] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-29 04:43:28.726 - [TestCKEnum][t_enum] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-29 04:43:28.726 - [TestCKEnum][t_enum] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-29 04:43:28.773 - [TestCKEnum][t_enum] - Initial sync started 
[INFO ] 2024-07-29 04:43:28.781 - [TestCKEnum][t_enum] - Starting batch read, table name: t_enum, offset: null 
[INFO ] 2024-07-29 04:43:28.781 - [TestCKEnum][t_enum] - Table t_enum is going to be initial synced 
[INFO ] 2024-07-29 04:43:28.795 - [TestCKEnum][t_enum] - Table [t_enum] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-29 04:43:28.796 - [TestCKEnum][t_enum] - Query table 't_enum' counts: 3 
[INFO ] 2024-07-29 04:43:28.857 - [TestCKEnum][t_enum] - Initial sync completed 
[INFO ] 2024-07-29 04:43:28.857 - [TestCKEnum][TargetEnum] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-29 04:43:29.827 - [TestCKEnum][t_enum] - Node t_enum[366706c4-2c2d-4ea1-93de-ee0a0e605836] running status set to false 
[INFO ] 2024-07-29 04:43:29.831 - [TestCKEnum][TargetEnum] - Node TargetEnum[c7528103-21ef-4b70-9e31-826e592de9fe] running status set to false 
[INFO ] 2024-07-29 04:43:29.832 - [TestCKEnum][t_enum] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-29 04:43:29.869 - [TestCKEnum][TargetEnum] - Clickhouse Optimize Table start, tables: ["TargetEnum"] 
[INFO ] 2024-07-29 04:43:29.871 - [TestCKEnum][t_enum] - Clickhouse Optimize Table end 
[WARN ] 2024-07-29 04:43:29.895 - [TestCKEnum][TargetEnum] - Clickhouse Optimize Table failed 
[INFO ] 2024-07-29 04:43:29.896 - [TestCKEnum][TargetEnum] - PDK connector node stopped: HazelcastTargetPdkDataNode-c7528103-21ef-4b70-9e31-826e592de9fe 
[INFO ] 2024-07-29 04:43:29.896 - [TestCKEnum][TargetEnum] - PDK connector node released: HazelcastTargetPdkDataNode-c7528103-21ef-4b70-9e31-826e592de9fe 
[INFO ] 2024-07-29 04:43:29.896 - [TestCKEnum][t_enum] - PDK connector node stopped: HazelcastSourcePdkDataNode-366706c4-2c2d-4ea1-93de-ee0a0e605836 
[INFO ] 2024-07-29 04:43:29.896 - [TestCKEnum][TargetEnum] - Node TargetEnum[c7528103-21ef-4b70-9e31-826e592de9fe] schema data cleaned 
[INFO ] 2024-07-29 04:43:29.896 - [TestCKEnum][t_enum] - PDK connector node released: HazelcastSourcePdkDataNode-366706c4-2c2d-4ea1-93de-ee0a0e605836 
[INFO ] 2024-07-29 04:43:29.896 - [TestCKEnum][TargetEnum] - Node TargetEnum[c7528103-21ef-4b70-9e31-826e592de9fe] monitor closed 
[INFO ] 2024-07-29 04:43:29.896 - [TestCKEnum][t_enum] - Node t_enum[366706c4-2c2d-4ea1-93de-ee0a0e605836] schema data cleaned 
[INFO ] 2024-07-29 04:43:29.897 - [TestCKEnum][t_enum] - Node t_enum[366706c4-2c2d-4ea1-93de-ee0a0e605836] monitor closed 
[INFO ] 2024-07-29 04:43:29.898 - [TestCKEnum][TargetEnum] - Node TargetEnum[c7528103-21ef-4b70-9e31-826e592de9fe] close complete, cost 78 ms 
[INFO ] 2024-07-29 04:43:29.900 - [TestCKEnum][t_enum] - Node t_enum[366706c4-2c2d-4ea1-93de-ee0a0e605836] close complete, cost 88 ms 
[INFO ] 2024-07-29 04:43:33.091 - [TestCKEnum] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-29 04:43:33.092 - [TestCKEnum] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@14097619 
[INFO ] 2024-07-29 04:43:33.222 - [TestCKEnum] - Stop task milestones: 66a6ace766c385580759bfd1(TestCKEnum)  
[INFO ] 2024-07-29 04:43:33.226 - [TestCKEnum] - Stopped task aspect(s) 
[INFO ] 2024-07-29 04:43:33.226 - [TestCKEnum] - Snapshot order controller have been removed 
[INFO ] 2024-07-29 04:43:33.243 - [TestCKEnum] - Remove memory task client succeed, task: TestCKEnum[66a6ace766c385580759bfd1] 
[INFO ] 2024-07-29 04:43:33.245 - [TestCKEnum] - Destroy memory task client cache succeed, task: TestCKEnum[66a6ace766c385580759bfd1] 
