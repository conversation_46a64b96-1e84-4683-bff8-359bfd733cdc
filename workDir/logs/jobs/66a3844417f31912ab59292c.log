[INFO ] 2024-07-26 19:11:54.594 - [任务 40] - Task initialization... 
[INFO ] 2024-07-26 19:11:58.034 - [任务 40] - Start task milestones: 66a3844417f31912ab59292c(任务 40) 
[INFO ] 2024-07-26 19:11:58.769 - [任务 40] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:11:58.867 - [任务 40] - The engine receives 任务 40 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 19:11:59.468 - [任务 40][dummy_test] - Node dummy_test[f0365a49-4be7-4d47-afa0-27561491cade] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:11:59.469 - [任务 40][POLICY] - Node POLICY[6a161c73-2964-4eb7-ab67-3883187d37eb] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:11:59.471 - [任务 40][dummy_test] - Node dummy_test[f0365a49-4be7-4d47-afa0-27561491cade] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 19:11:59.474 - [任务 40][POLICY] - Node POLICY[6a161c73-2964-4eb7-ab67-3883187d37eb] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 19:12:00.528 - [任务 40][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 19:12:00.756 - [任务 40][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-26 19:12:00.757 - [任务 40][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-26 19:12:00.757 - [任务 40][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 19:12:00.940 - [任务 40][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1721992320,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 19:12:01.013 - [任务 40][POLICY] - Initial sync started 
[INFO ] 2024-07-26 19:12:01.030 - [任务 40][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-26 19:12:01.030 - [任务 40][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-26 19:12:01.058 - [任务 40][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-26 19:12:01.142 - [任务 40][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 19:12:01.144 - [任务 40][POLICY] - Initial sync completed 
[INFO ] 2024-07-26 19:12:01.144 - [任务 40][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-26 19:12:01.145 - [任务 40][POLICY] - Initial sync completed 
[INFO ] 2024-07-26 19:12:01.172 - [任务 40][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721992320,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 19:12:01.388 - [任务 40][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 19:12:23.894 - [任务 40][POLICY] - Node POLICY[6a161c73-2964-4eb7-ab67-3883187d37eb] running status set to false 
[INFO ] 2024-07-26 19:12:23.901 - [任务 40][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-6a161c73-2964-4eb7-ab67-3883187d37eb 
[INFO ] 2024-07-26 19:12:23.901 - [任务 40][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-6a161c73-2964-4eb7-ab67-3883187d37eb 
[INFO ] 2024-07-26 19:12:23.901 - [任务 40][POLICY] - Node POLICY[6a161c73-2964-4eb7-ab67-3883187d37eb] schema data cleaned 
[INFO ] 2024-07-26 19:12:23.903 - [任务 40][POLICY] - Node POLICY[6a161c73-2964-4eb7-ab67-3883187d37eb] monitor closed 
[INFO ] 2024-07-26 19:12:23.905 - [任务 40][POLICY] - Node POLICY[6a161c73-2964-4eb7-ab67-3883187d37eb] close complete, cost 29 ms 
[INFO ] 2024-07-26 19:12:23.905 - [任务 40][dummy_test] - Node dummy_test[f0365a49-4be7-4d47-afa0-27561491cade] running status set to false 
[INFO ] 2024-07-26 19:12:23.925 - [任务 40][dummy_test] - Stop connector 
[INFO ] 2024-07-26 19:12:23.926 - [任务 40][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-f0365a49-4be7-4d47-afa0-27561491cade 
[INFO ] 2024-07-26 19:12:23.928 - [任务 40][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-f0365a49-4be7-4d47-afa0-27561491cade 
[INFO ] 2024-07-26 19:12:23.928 - [任务 40][dummy_test] - Node dummy_test[f0365a49-4be7-4d47-afa0-27561491cade] schema data cleaned 
[INFO ] 2024-07-26 19:12:23.928 - [任务 40][dummy_test] - Node dummy_test[f0365a49-4be7-4d47-afa0-27561491cade] monitor closed 
[INFO ] 2024-07-26 19:12:24.129 - [任务 40][dummy_test] - Node dummy_test[f0365a49-4be7-4d47-afa0-27561491cade] close complete, cost 24 ms 
[INFO ] 2024-07-26 19:12:24.330 - [任务 40][POLICY] - Incremental sync completed 
[INFO ] 2024-07-26 19:12:28.330 - [任务 40] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 19:12:28.466 - [任务 40] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4e09cc03 
[INFO ] 2024-07-26 19:12:28.467 - [任务 40] - Stop task milestones: 66a3844417f31912ab59292c(任务 40)  
[INFO ] 2024-07-26 19:12:28.482 - [任务 40] - Stopped task aspect(s) 
[INFO ] 2024-07-26 19:12:28.483 - [任务 40] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 19:12:28.503 - [任务 40] - Remove memory task client succeed, task: 任务 40[66a3844417f31912ab59292c] 
[INFO ] 2024-07-26 19:12:28.504 - [任务 40] - Destroy memory task client cache succeed, task: 任务 40[66a3844417f31912ab59292c] 
