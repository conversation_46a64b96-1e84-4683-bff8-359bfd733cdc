[INFO ] 2024-07-18 12:00:21.432 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188] - load tapTable task 669893538315b25db9f54457-a10a586a-4de7-484b-845b-9df18904eb8a complete, cost 1560ms 
[INFO ] 2024-07-18 12:00:23.470 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188] - load tapTable task 669893538315b25db9f54457-3e487d51-a29a-486d-bfa2-b86782b30137 complete, cost 3407ms 
[INFO ] 2024-07-18 12:00:30.691 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188] - Task initialization... 
[INFO ] 2024-07-18 12:00:30.691 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188] - Start task milestones: 669893538315b25db9f54458(t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188) 
[INFO ] 2024-07-18 12:00:31.735 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188] - load tapTable task 669893538315b25db9f54457-946ccad3-b829-435b-bcda-2074cc299a0e complete, cost 864ms 
[INFO ] 2024-07-18 12:00:32.148 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 12:00:32.149 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188] - The engine receives t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 12:00:32.415 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:00:32.415 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:00:32.416 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[2ea80f45-854a-4801-80dd-0fe54f0a0c51] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:00:32.416 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[2ea80f45-854a-4801-80dd-0fe54f0a0c51] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:00:32.416 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:00:32.416 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:00:32.829 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 12:00:32.909 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Source node "qa_mongodb_repl_6040_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 12:00:32.909 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Source node "qa_mongodb_repl_6040_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 12:00:32.909 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 12:00:33.315 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - batch offset found: {},stream offset found: {"cdcOffset":1721275233,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 12:00:33.506 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 12:00:33.507 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Starting batch read, table name: t_delete, offset: null 
[INFO ] 2024-07-18 12:00:33.526 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Table t_delete is going to be initial synced 
[INFO ] 2024-07-18 12:00:33.526 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Table [t_delete] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 12:00:33.530 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Query table 't_delete' counts: 1 
[INFO ] 2024-07-18 12:00:33.530 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 12:00:33.530 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 12:00:33.530 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 12:00:33.531 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Starting stream read, table list: [t_delete, _tapdata_heartbeat_table], offset: {"cdcOffset":1721275233,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 12:00:33.735 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [t_delete, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 12:02:40.836 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] running status set to false 
[INFO ] 2024-07-18 12:02:40.872 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-9337afde-7d6e-436d-85f9-e8450d109f75 
[INFO ] 2024-07-18 12:02:40.873 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-9337afde-7d6e-436d-85f9-e8450d109f75 
[INFO ] 2024-07-18 12:02:40.873 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] schema data cleaned 
[INFO ] 2024-07-18 12:02:40.873 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] monitor closed 
[INFO ] 2024-07-18 12:02:40.875 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9337afde-7d6e-436d-85f9-e8450d109f75] close complete, cost 56 ms 
[INFO ] 2024-07-18 12:02:40.875 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] running status set to false 
[INFO ] 2024-07-18 12:02:40.886 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][JS] - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_6040_1717403468657_3537-f4092f53-4081-43fa-a495-db4cbccede6b 
[INFO ] 2024-07-18 12:02:40.886 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][JS] - PDK connector node released: ScriptExecutor-qa_mongodb_repl_6040_1717403468657_3537-f4092f53-4081-43fa-a495-db4cbccede6b 
[INFO ] 2024-07-18 12:02:40.887 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][JS] - [ScriptExecutorsManager-669893538315b25db9f54458-1168de21-27e5-42a3-a5b8-c29383206f39-6697a5dfb92eda1a86f5248f] schema data cleaned 
[INFO ] 2024-07-18 12:02:40.897 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][JS] - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_6040_1717403468657_3537-f20de7e5-04a4-48ae-9e91-5fab3aa7f400 
[INFO ] 2024-07-18 12:02:40.897 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][JS] - PDK connector node released: ScriptExecutor-qa_mongodb_repl_6040_1717403468657_3537-f20de7e5-04a4-48ae-9e91-5fab3aa7f400 
[INFO ] 2024-07-18 12:02:40.898 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][JS] - [ScriptExecutorsManager-669893538315b25db9f54458-1168de21-27e5-42a3-a5b8-c29383206f39-6697a5dfb92eda1a86f5248f] schema data cleaned 
[INFO ] 2024-07-18 12:02:40.919 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] schema data cleaned 
[INFO ] 2024-07-18 12:02:40.919 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] monitor closed 
[INFO ] 2024-07-18 12:02:40.920 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][JS] - Node JS[1168de21-27e5-42a3-a5b8-c29383206f39] close complete, cost 45 ms 
[INFO ] 2024-07-18 12:02:40.920 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[2ea80f45-854a-4801-80dd-0fe54f0a0c51] running status set to false 
[INFO ] 2024-07-18 12:02:40.956 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-2ea80f45-854a-4801-80dd-0fe54f0a0c51 
[INFO ] 2024-07-18 12:02:40.956 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-2ea80f45-854a-4801-80dd-0fe54f0a0c51 
[INFO ] 2024-07-18 12:02:40.956 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[2ea80f45-854a-4801-80dd-0fe54f0a0c51] schema data cleaned 
[INFO ] 2024-07-18 12:02:40.957 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[2ea80f45-854a-4801-80dd-0fe54f0a0c51] monitor closed 
[INFO ] 2024-07-18 12:02:40.958 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[2ea80f45-854a-4801-80dd-0fe54f0a0c51] close complete, cost 37 ms 
[INFO ] 2024-07-18 12:02:41.381 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188][qa_mongodb_repl_6040_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 12:02:42.299 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 12:02:42.299 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4ea245eb 
[INFO ] 2024-07-18 12:02:42.442 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188] - Stop task milestones: 669893538315b25db9f54458(t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188)  
[INFO ] 2024-07-18 12:02:42.442 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188] - Stopped task aspect(s) 
[INFO ] 2024-07-18 12:02:42.442 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 12:02:42.497 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188] - Remove memory task client succeed, task: t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188[669893538315b25db9f54458] 
[INFO ] 2024-07-18 12:02:42.497 - [t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188] - Destroy memory task client cache succeed, task: t_4.3.1-mdb-v6.0.4_to_mdb_delete_check_1717403468657_3537-1721275188[669893538315b25db9f54458] 
