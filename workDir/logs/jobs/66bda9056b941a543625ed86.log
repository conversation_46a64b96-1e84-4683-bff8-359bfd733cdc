[INFO ] 2024-08-15 15:07:41.167 - [任务 5] - Start task milestones: 66bda9056b941a543625ed86(任务 5) 
[INFO ] 2024-08-15 15:07:41.299 - [任务 5] - Task initialization... 
[INFO ] 2024-08-15 15:07:41.299 - [任务 5] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-08-15 15:07:41.501 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-15 15:07:41.667 - [任务 5][DB2TEST] - Node DB2TEST[b7c29387-ec7c-40af-a95e-70e28d93a8ee] start preload schema,table counts: 1 
[INFO ] 2024-08-15 15:07:41.667 - [任务 5][Mysql] - Node Mysql[4eb32234-4124-4020-a5c7-af691e3bae47] start preload schema,table counts: 1 
[INFO ] 2024-08-15 15:07:41.782 - [任务 5][DB2TEST] - Node DB2TEST[b7c29387-ec7c-40af-a95e-70e28d93a8ee] preload schema finished, cost 101 ms 
[INFO ] 2024-08-15 15:07:41.782 - [任务 5][Mysql] - Node Mysql[4eb32234-4124-4020-a5c7-af691e3bae47] preload schema finished, cost 101 ms 
[INFO ] 2024-08-15 15:07:42.782 - [任务 5][DB2TEST] - Source node "DB2TEST" read batch size: 100 
[INFO ] 2024-08-15 15:07:42.784 - [任务 5][DB2TEST] - Source node "DB2TEST" event queue capacity: 200 
[INFO ] 2024-08-15 15:07:42.786 - [任务 5][DB2TEST] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-15 15:07:42.987 - [任务 5][DB2TEST] - Table [CUSTOMERS1] not open CDC 
[INFO ] 2024-08-15 15:07:42.996 - [任务 5][DB2TEST] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1723705662975} 
[INFO ] 2024-08-15 15:07:42.996 - [任务 5][DB2TEST] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-15 15:07:43.017 - [任务 5][Mysql] - Node(Mysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-15 15:07:43.017 - [任务 5][Mysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-15 15:07:43.202 - [任务 5][DB2TEST] - Initial sync started 
[INFO ] 2024-08-15 15:07:43.221 - [任务 5][DB2TEST] - Starting batch read, table name: CUSTOMERS1, offset: null 
[INFO ] 2024-08-15 15:07:43.222 - [任务 5][DB2TEST] - Table CUSTOMERS1 is going to be initial synced 
[INFO ] 2024-08-15 15:07:43.300 - [任务 5][DB2TEST] - Query table 'CUSTOMERS1' counts: 1 
[INFO ] 2024-08-15 15:07:43.302 - [任务 5][DB2TEST] - Table [CUSTOMERS1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-15 15:07:43.306 - [任务 5][DB2TEST] - Initial sync completed 
[INFO ] 2024-08-15 15:07:43.306 - [任务 5][DB2TEST] - Incremental sync starting... 
[INFO ] 2024-08-15 15:07:43.306 - [任务 5][DB2TEST] - Initial sync completed 
[INFO ] 2024-08-15 15:07:43.312 - [任务 5][DB2TEST] - Starting stream read, table list: [CUSTOMERS1], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1723705662975} 
[INFO ] 2024-08-15 15:31:00.008 - [任务 5] - Stop task milestones: 66bda9056b941a543625ed86(任务 5)  
[INFO ] 2024-08-15 15:31:00.010 - [任务 5][DB2TEST] - Node DB2TEST[b7c29387-ec7c-40af-a95e-70e28d93a8ee] running status set to false 
[INFO ] 2024-08-15 15:31:00.013 - [任务 5][DB2TEST] - Log Miner is shutting down... 
[ERROR] 2024-08-15 15:31:00.220 - [任务 5][DB2TEST] - io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted <-- Full Stack Trace -->
java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:567)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:223)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	... 6 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.grpc.Status.asRuntimeException(Status.java:535)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:649)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:175)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.InterruptedException
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.throwIfInterrupted(ClientCalls.java:750)
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.waitAndDrain(ClientCalls.java:733)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.waitForNext(ClientCalls.java:623)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:644)
	... 21 more

[INFO ] 2024-08-15 15:31:01.990 - [任务 5][DB2TEST] - PDK connector node stopped: HazelcastSourcePdkDataNode-b7c29387-ec7c-40af-a95e-70e28d93a8ee 
[INFO ] 2024-08-15 15:31:01.993 - [任务 5][DB2TEST] - PDK connector node released: HazelcastSourcePdkDataNode-b7c29387-ec7c-40af-a95e-70e28d93a8ee 
[INFO ] 2024-08-15 15:31:01.993 - [任务 5][DB2TEST] - Node DB2TEST[b7c29387-ec7c-40af-a95e-70e28d93a8ee] schema data cleaned 
[INFO ] 2024-08-15 15:31:02.000 - [任务 5][DB2TEST] - Node DB2TEST[b7c29387-ec7c-40af-a95e-70e28d93a8ee] monitor closed 
[INFO ] 2024-08-15 15:31:02.001 - [任务 5][DB2TEST] - Node DB2TEST[b7c29387-ec7c-40af-a95e-70e28d93a8ee] close complete, cost 1994 ms 
[INFO ] 2024-08-15 15:31:02.034 - [任务 5][Mysql] - Node Mysql[4eb32234-4124-4020-a5c7-af691e3bae47] running status set to false 
[INFO ] 2024-08-15 15:31:02.034 - [任务 5][Mysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-4eb32234-4124-4020-a5c7-af691e3bae47 
[INFO ] 2024-08-15 15:31:02.034 - [任务 5][Mysql] - PDK connector node released: HazelcastTargetPdkDataNode-4eb32234-4124-4020-a5c7-af691e3bae47 
[INFO ] 2024-08-15 15:31:02.034 - [任务 5][Mysql] - Node Mysql[4eb32234-4124-4020-a5c7-af691e3bae47] schema data cleaned 
[INFO ] 2024-08-15 15:31:02.034 - [任务 5][Mysql] - Node Mysql[4eb32234-4124-4020-a5c7-af691e3bae47] monitor closed 
[INFO ] 2024-08-15 15:31:02.035 - [任务 5][Mysql] - Node Mysql[4eb32234-4124-4020-a5c7-af691e3bae47] close complete, cost 34 ms 
[INFO ] 2024-08-15 15:31:06.232 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-15 15:31:06.232 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-08-15 15:31:06.261 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-08-15 15:31:06.261 - [任务 5] - Remove memory task client succeed, task: 任务 5[66bda9056b941a543625ed86] 
[INFO ] 2024-08-15 15:31:06.468 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[66bda9056b941a543625ed86] 
[INFO ] 2024-08-15 15:55:03.826 - [任务 5] - Start task milestones: 66bda9056b941a543625ed86(任务 5) 
[INFO ] 2024-08-15 15:55:03.826 - [任务 5] - Task initialization... 
[INFO ] 2024-08-15 15:55:03.891 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-15 15:55:03.892 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-15 15:55:03.951 - [任务 5][DB2TEST] - Node DB2TEST[b7c29387-ec7c-40af-a95e-70e28d93a8ee] start preload schema,table counts: 1 
[INFO ] 2024-08-15 15:55:03.951 - [任务 5][Mysql] - Node Mysql[4eb32234-4124-4020-a5c7-af691e3bae47] start preload schema,table counts: 1 
[INFO ] 2024-08-15 15:55:03.980 - [任务 5][DB2TEST] - Node DB2TEST[b7c29387-ec7c-40af-a95e-70e28d93a8ee] preload schema finished, cost 30 ms 
[INFO ] 2024-08-15 15:55:03.981 - [任务 5][Mysql] - Node Mysql[4eb32234-4124-4020-a5c7-af691e3bae47] preload schema finished, cost 25 ms 
[INFO ] 2024-08-15 15:55:04.760 - [任务 5][DB2TEST] - Source node "DB2TEST" read batch size: 100 
[INFO ] 2024-08-15 15:55:04.761 - [任务 5][DB2TEST] - Source node "DB2TEST" event queue capacity: 200 
[INFO ] 2024-08-15 15:55:04.767 - [任务 5][DB2TEST] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-15 15:55:04.959 - [任务 5][Mysql] - Node(Mysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-15 15:55:04.959 - [任务 5][Mysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-15 15:55:04.971 - [任务 5][Mysql] - Table "test.CUSTOMERS1" exists, skip auto create table 
[INFO ] 2024-08-15 15:55:04.971 - [任务 5][Mysql] - The table CUSTOMERS1 has already exist. 
[WARN ] 2024-08-15 15:55:05.082 - [任务 5][DB2TEST] - Table [CUSTOMERS1] not open CDC 
[INFO ] 2024-08-15 15:55:05.085 - [任务 5][DB2TEST] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1723708505081} 
[INFO ] 2024-08-15 15:55:05.086 - [任务 5][DB2TEST] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-15 15:55:05.130 - [任务 5][DB2TEST] - Initial sync started 
[INFO ] 2024-08-15 15:55:05.135 - [任务 5][DB2TEST] - Starting batch read, table name: CUSTOMERS1, offset: null 
[INFO ] 2024-08-15 15:55:05.182 - [任务 5][DB2TEST] - Table CUSTOMERS1 is going to be initial synced 
[INFO ] 2024-08-15 15:55:05.182 - [任务 5][DB2TEST] - Table [CUSTOMERS1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-15 15:55:05.193 - [任务 5][DB2TEST] - Query table 'CUSTOMERS1' counts: 2 
[INFO ] 2024-08-15 15:55:05.194 - [任务 5][DB2TEST] - Initial sync completed 
[INFO ] 2024-08-15 15:55:05.194 - [任务 5][DB2TEST] - Incremental sync starting... 
[INFO ] 2024-08-15 15:55:05.195 - [任务 5][DB2TEST] - Initial sync completed 
[INFO ] 2024-08-15 15:55:05.195 - [任务 5][DB2TEST] - Starting stream read, table list: [CUSTOMERS1], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1723708505081} 
[INFO ] 2024-08-15 15:57:54.527 - [任务 5] - Stop task milestones: 66bda9056b941a543625ed86(任务 5)  
[INFO ] 2024-08-15 15:57:54.527 - [任务 5][DB2TEST] - Node DB2TEST[b7c29387-ec7c-40af-a95e-70e28d93a8ee] running status set to false 
[INFO ] 2024-08-15 15:57:54.527 - [任务 5][DB2TEST] - Log Miner is shutting down... 
[ERROR] 2024-08-15 15:57:54.540 - [任务 5][DB2TEST] - io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted <-- Full Stack Trace -->
java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:567)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:223)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	... 6 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.grpc.Status.asRuntimeException(Status.java:535)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:649)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:175)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.InterruptedException
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.throwIfInterrupted(ClientCalls.java:750)
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.waitAndDrain(ClientCalls.java:733)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.waitForNext(ClientCalls.java:623)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:644)
	... 21 more

[INFO ] 2024-08-15 15:57:56.145 - [任务 5][DB2TEST] - PDK connector node stopped: HazelcastSourcePdkDataNode-b7c29387-ec7c-40af-a95e-70e28d93a8ee 
[INFO ] 2024-08-15 15:57:56.145 - [任务 5][DB2TEST] - PDK connector node released: HazelcastSourcePdkDataNode-b7c29387-ec7c-40af-a95e-70e28d93a8ee 
[INFO ] 2024-08-15 15:57:56.145 - [任务 5][DB2TEST] - Node DB2TEST[b7c29387-ec7c-40af-a95e-70e28d93a8ee] schema data cleaned 
[INFO ] 2024-08-15 15:57:56.147 - [任务 5][DB2TEST] - Node DB2TEST[b7c29387-ec7c-40af-a95e-70e28d93a8ee] monitor closed 
[INFO ] 2024-08-15 15:57:56.154 - [任务 5][DB2TEST] - Node DB2TEST[b7c29387-ec7c-40af-a95e-70e28d93a8ee] close complete, cost 1622 ms 
[INFO ] 2024-08-15 15:57:56.155 - [任务 5][Mysql] - Node Mysql[4eb32234-4124-4020-a5c7-af691e3bae47] running status set to false 
[INFO ] 2024-08-15 15:57:56.167 - [任务 5][Mysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-4eb32234-4124-4020-a5c7-af691e3bae47 
[INFO ] 2024-08-15 15:57:56.170 - [任务 5][Mysql] - PDK connector node released: HazelcastTargetPdkDataNode-4eb32234-4124-4020-a5c7-af691e3bae47 
[INFO ] 2024-08-15 15:57:56.171 - [任务 5][Mysql] - Node Mysql[4eb32234-4124-4020-a5c7-af691e3bae47] schema data cleaned 
[INFO ] 2024-08-15 15:57:56.174 - [任务 5][Mysql] - Node Mysql[4eb32234-4124-4020-a5c7-af691e3bae47] monitor closed 
[INFO ] 2024-08-15 15:57:56.178 - [任务 5][Mysql] - Node Mysql[4eb32234-4124-4020-a5c7-af691e3bae47] close complete, cost 23 ms 
[INFO ] 2024-08-15 15:57:57.431 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-15 15:57:57.431 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-08-15 15:57:57.431 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-08-15 15:57:57.465 - [任务 5] - Remove memory task client succeed, task: 任务 5[66bda9056b941a543625ed86] 
[INFO ] 2024-08-15 15:57:57.668 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[66bda9056b941a543625ed86] 
