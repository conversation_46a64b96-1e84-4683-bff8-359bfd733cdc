[INFO ] 2024-06-05 11:47:31.633 - [任务 1] - Task initialization... 
[INFO ] 2024-06-05 11:47:31.656 - [任务 1] - Start task milestones: 665fda9a0ea2021850b59b9a(任务 1) 
[INFO ] 2024-06-05 11:47:31.672 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-05 11:47:31.695 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-05 11:47:31.930 - [任务 1][test] - Node test[4ed297c0-5d11-4f96-96d2-18f119746b05] start preload schema,table counts: 1 
[INFO ] 2024-06-05 11:47:31.932 - [任务 1][IMAGE_TABLE] - Node IMAGE_TABLE[15268197-c098-4c6a-8668-e2156c95d886] start preload schema,table counts: 1 
[INFO ] 2024-06-05 11:47:32.028 - [任务 1][IMAGE_TABLE] - Node IMAGE_TABLE[15268197-c098-4c6a-8668-e2156c95d886] preload schema finished, cost 93 ms 
[INFO ] 2024-06-05 11:47:32.235 - [任务 1][test] - Node test[4ed297c0-5d11-4f96-96d2-18f119746b05] preload schema finished, cost 94 ms 
[INFO ] 2024-06-05 11:47:33.471 - [任务 1][IMAGE_TABLE] - Source node "IMAGE_TABLE" read batch size: 100 
[INFO ] 2024-06-05 11:47:33.472 - [任务 1][IMAGE_TABLE] - Source node "IMAGE_TABLE" event queue capacity: 200 
[INFO ] 2024-06-05 11:47:33.476 - [任务 1][IMAGE_TABLE] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-05 11:47:33.674 - [任务 1][test] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-06-05 11:47:33.674 - [任务 1][IMAGE_TABLE] - Found pending transaction, please check if there are any earlier transactions to prevent data loss, the earliest startScn: 53769735 
[INFO ] 2024-06-05 11:47:33.924 - [任务 1][IMAGE_TABLE] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53769734,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-05 11:47:33.924 - [任务 1][IMAGE_TABLE] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-05 11:47:34.155 - [任务 1][IMAGE_TABLE] - Initial sync started 
[INFO ] 2024-06-05 11:47:34.176 - [任务 1][IMAGE_TABLE] - Starting batch read, table name: IMAGE_TABLE, offset: null 
[INFO ] 2024-06-05 11:47:34.176 - [任务 1][IMAGE_TABLE] - Table IMAGE_TABLE is going to be initial synced 
[INFO ] 2024-06-05 11:47:34.279 - [任务 1][IMAGE_TABLE] - Query table 'IMAGE_TABLE' counts: 1 
[INFO ] 2024-06-05 11:47:34.279 - [任务 1][IMAGE_TABLE] - Table [IMAGE_TABLE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-05 11:47:34.284 - [任务 1][IMAGE_TABLE] - Initial sync completed 
[INFO ] 2024-06-05 11:47:34.284 - [任务 1][IMAGE_TABLE] - Incremental sync starting... 
[INFO ] 2024-06-05 11:47:34.284 - [任务 1][IMAGE_TABLE] - Initial sync completed 
[INFO ] 2024-06-05 11:47:34.290 - [任务 1][IMAGE_TABLE] - Starting stream read, table list: [IMAGE_TABLE], offset: {"sortString":null,"offsetValue":null,"lastScn":53769734,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-05 11:47:34.491 - [任务 1][IMAGE_TABLE] - total start mining scn: 53769734 
[INFO ] 2024-06-05 11:47:37.532 - [任务 1][IMAGE_TABLE] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-06-05 11:51:04.184 - [任务 1] - Stop task milestones: 665fda9a0ea2021850b59b9a(任务 1)  
[INFO ] 2024-06-05 11:51:04.489 - [任务 1][IMAGE_TABLE] - Node IMAGE_TABLE[15268197-c098-4c6a-8668-e2156c95d886] running status set to false 
[INFO ] 2024-06-05 11:51:04.518 - [任务 1][IMAGE_TABLE] - Log Miner is shutting down... 
[INFO ] 2024-06-05 11:51:04.518 - [任务 1][IMAGE_TABLE] - Log Miner has been closed! 
[INFO ] 2024-06-05 11:51:04.594 - [任务 1][IMAGE_TABLE] - PDK connector node stopped: HazelcastSourcePdkDataNode-15268197-c098-4c6a-8668-e2156c95d886 
[ERROR] 2024-06-05 11:51:04.596 - [任务 1][IMAGE_TABLE] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-06-05 11:51:04.597 - [任务 1][IMAGE_TABLE] - PDK connector node released: HazelcastSourcePdkDataNode-15268197-c098-4c6a-8668-e2156c95d886 
[INFO ] 2024-06-05 11:51:04.598 - [任务 1][IMAGE_TABLE] - Node IMAGE_TABLE[15268197-c098-4c6a-8668-e2156c95d886] schema data cleaned 
[INFO ] 2024-06-05 11:51:04.600 - [任务 1][IMAGE_TABLE] - Node IMAGE_TABLE[15268197-c098-4c6a-8668-e2156c95d886] monitor closed 
[INFO ] 2024-06-05 11:51:04.604 - [任务 1][IMAGE_TABLE] - Node IMAGE_TABLE[15268197-c098-4c6a-8668-e2156c95d886] close complete, cost 128 ms 
[INFO ] 2024-06-05 11:51:04.605 - [任务 1][test] - Node test[4ed297c0-5d11-4f96-96d2-18f119746b05] running status set to false 
[INFO ] 2024-06-05 11:51:04.684 - [任务 1][test] - PDK connector node stopped: HazelcastTargetPdkDataNode-4ed297c0-5d11-4f96-96d2-18f119746b05 
[INFO ] 2024-06-05 11:51:04.684 - [任务 1][test] - PDK connector node released: HazelcastTargetPdkDataNode-4ed297c0-5d11-4f96-96d2-18f119746b05 
[INFO ] 2024-06-05 11:51:04.684 - [任务 1][test] - Node test[4ed297c0-5d11-4f96-96d2-18f119746b05] schema data cleaned 
[INFO ] 2024-06-05 11:51:04.687 - [任务 1][test] - Node test[4ed297c0-5d11-4f96-96d2-18f119746b05] monitor closed 
[INFO ] 2024-06-05 11:51:04.894 - [任务 1][test] - Node test[4ed297c0-5d11-4f96-96d2-18f119746b05] close complete, cost 83 ms 
[INFO ] 2024-06-05 11:51:07.662 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-05 11:51:07.662 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-06-05 11:51:07.762 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-06-05 11:51:07.763 - [任务 1] - Remove memory task client succeed, task: 任务 1[665fda9a0ea2021850b59b9a] 
[INFO ] 2024-06-05 11:51:07.967 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[665fda9a0ea2021850b59b9a] 
[INFO ] 2024-06-05 12:25:36.600 - [任务 1] - Task initialization... 
[INFO ] 2024-06-05 12:25:36.602 - [任务 1] - Start task milestones: 665fda9a0ea2021850b59b9a(任务 1) 
[INFO ] 2024-06-05 12:25:36.610 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-05 12:25:36.708 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-05 12:25:36.708 - [任务 1][IMAGE_TABLE] - Node IMAGE_TABLE[15268197-c098-4c6a-8668-e2156c95d886] start preload schema,table counts: 1 
[INFO ] 2024-06-05 12:25:36.708 - [任务 1][test] - Node test[4ed297c0-5d11-4f96-96d2-18f119746b05] start preload schema,table counts: 1 
[INFO ] 2024-06-05 12:25:36.738 - [任务 1][test] - Node test[4ed297c0-5d11-4f96-96d2-18f119746b05] preload schema finished, cost 29 ms 
[INFO ] 2024-06-05 12:25:36.738 - [任务 1][IMAGE_TABLE] - Node IMAGE_TABLE[15268197-c098-4c6a-8668-e2156c95d886] preload schema finished, cost 30 ms 
[INFO ] 2024-06-05 12:25:37.744 - [任务 1][test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-05 12:25:37.808 - [任务 1][IMAGE_TABLE] - Source node "IMAGE_TABLE" read batch size: 100 
[INFO ] 2024-06-05 12:25:37.808 - [任务 1][IMAGE_TABLE] - Source node "IMAGE_TABLE" event queue capacity: 200 
[INFO ] 2024-06-05 12:25:37.809 - [任务 1][IMAGE_TABLE] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-05 12:25:38.040 - [任务 1][IMAGE_TABLE] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53782053,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-05 12:25:38.040 - [任务 1][IMAGE_TABLE] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-05 12:25:38.090 - [任务 1][test] - The table test has already exist. 
[INFO ] 2024-06-05 12:25:38.097 - [任务 1][IMAGE_TABLE] - Initial sync started 
[INFO ] 2024-06-05 12:25:38.099 - [任务 1][IMAGE_TABLE] - Starting batch read, table name: IMAGE_TABLE, offset: null 
[INFO ] 2024-06-05 12:25:38.100 - [任务 1][IMAGE_TABLE] - Table IMAGE_TABLE is going to be initial synced 
[INFO ] 2024-06-05 12:25:38.204 - [任务 1][IMAGE_TABLE] - Table [IMAGE_TABLE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-05 12:25:38.204 - [任务 1][IMAGE_TABLE] - Query table 'IMAGE_TABLE' counts: 1 
[INFO ] 2024-06-05 12:25:38.205 - [任务 1][IMAGE_TABLE] - Initial sync completed 
[INFO ] 2024-06-05 12:25:38.205 - [任务 1][IMAGE_TABLE] - Incremental sync starting... 
[INFO ] 2024-06-05 12:25:38.205 - [任务 1][IMAGE_TABLE] - Initial sync completed 
[INFO ] 2024-06-05 12:25:38.205 - [任务 1][IMAGE_TABLE] - Starting stream read, table list: [IMAGE_TABLE], offset: {"sortString":null,"offsetValue":null,"lastScn":53782053,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-05 12:25:38.406 - [任务 1][IMAGE_TABLE] - total start mining scn: 53782053 
[INFO ] 2024-06-05 12:25:39.622 - [任务 1][IMAGE_TABLE] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-06-05 12:27:04.999 - [任务 1] - Stop task milestones: 665fda9a0ea2021850b59b9a(任务 1)  
[INFO ] 2024-06-05 12:27:05.184 - [任务 1][IMAGE_TABLE] - Node IMAGE_TABLE[15268197-c098-4c6a-8668-e2156c95d886] running status set to false 
[INFO ] 2024-06-05 12:27:05.211 - [任务 1][IMAGE_TABLE] - Log Miner is shutting down... 
[INFO ] 2024-06-05 12:27:05.212 - [任务 1][IMAGE_TABLE] - Log Miner has been closed! 
[ERROR] 2024-06-05 12:27:05.270 - [任务 1][IMAGE_TABLE] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-06-05 12:27:05.271 - [任务 1][IMAGE_TABLE] - PDK connector node stopped: HazelcastSourcePdkDataNode-15268197-c098-4c6a-8668-e2156c95d886 
[INFO ] 2024-06-05 12:27:05.271 - [任务 1][IMAGE_TABLE] - PDK connector node released: HazelcastSourcePdkDataNode-15268197-c098-4c6a-8668-e2156c95d886 
[INFO ] 2024-06-05 12:27:05.271 - [任务 1][IMAGE_TABLE] - Node IMAGE_TABLE[15268197-c098-4c6a-8668-e2156c95d886] schema data cleaned 
[INFO ] 2024-06-05 12:27:05.272 - [任务 1][IMAGE_TABLE] - Node IMAGE_TABLE[15268197-c098-4c6a-8668-e2156c95d886] monitor closed 
[INFO ] 2024-06-05 12:27:05.273 - [任务 1][IMAGE_TABLE] - Node IMAGE_TABLE[15268197-c098-4c6a-8668-e2156c95d886] close complete, cost 94 ms 
[INFO ] 2024-06-05 12:27:05.273 - [任务 1][test] - Node test[4ed297c0-5d11-4f96-96d2-18f119746b05] running status set to false 
[INFO ] 2024-06-05 12:27:05.330 - [任务 1][test] - PDK connector node stopped: HazelcastTargetPdkDataNode-4ed297c0-5d11-4f96-96d2-18f119746b05 
[INFO ] 2024-06-05 12:27:05.330 - [任务 1][test] - PDK connector node released: HazelcastTargetPdkDataNode-4ed297c0-5d11-4f96-96d2-18f119746b05 
[INFO ] 2024-06-05 12:27:05.331 - [任务 1][test] - Node test[4ed297c0-5d11-4f96-96d2-18f119746b05] schema data cleaned 
[INFO ] 2024-06-05 12:27:05.331 - [任务 1][test] - Node test[4ed297c0-5d11-4f96-96d2-18f119746b05] monitor closed 
[INFO ] 2024-06-05 12:27:05.534 - [任务 1][test] - Node test[4ed297c0-5d11-4f96-96d2-18f119746b05] close complete, cost 58 ms 
[INFO ] 2024-06-05 12:27:09.795 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-05 12:27:09.796 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-06-05 12:27:09.796 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-06-05 12:27:09.847 - [任务 1] - Remove memory task client succeed, task: 任务 1[665fda9a0ea2021850b59b9a] 
[INFO ] 2024-06-05 12:27:09.847 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[665fda9a0ea2021850b59b9a] 
