[INFO ] 2024-07-19 07:02:41.104 - [任务 1] - Task initialization... 
[INFO ] 2024-07-19 07:02:41.273 - [任务 1] - Start task milestones: 6695b92f6d76494ed53f3959(任务 1) 
[INFO ] 2024-07-19 07:02:42.887 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-19 07:02:43.047 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-19 07:02:43.632 - [任务 1][POLICY] - Node POLICY[e6402b3d-a9e8-4bd5-bcd3-0763ddbea750] start preload schema,table counts: 1 
[INFO ] 2024-07-19 07:02:43.639 - [任务 1][TESTPOLICY] - Node TESTPOLICY[969da717-2d89-4fde-9654-55546d220d9f] start preload schema,table counts: 1 
[INFO ] 2024-07-19 07:02:43.639 - [任务 1][POLICY] - Node POLICY[e6402b3d-a9e8-4bd5-bcd3-0763ddbea750] preload schema finished, cost 0 ms 
[INFO ] 2024-07-19 07:02:43.646 - [任务 1][TESTPOLICY] - Node TESTPOLICY[969da717-2d89-4fde-9654-55546d220d9f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-19 07:02:45.772 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-19 07:02:45.772 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-19 07:02:45.796 - [任务 1][POLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-19 07:02:45.862 - [任务 1][POLICY] - batch offset found: {},stream offset found: {"sequenceMap":{"POLICY":2,"_tapdata_heartbeat_table":44452},"streamOffset":{"_data":{"value":"82669771A9000000012B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F696400646695B95266AB5EDE8A9DCD3E0004","bsonType":"STRING","boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"double":false,"binary":false,"int32":false,"int64":false,"string":true,"symbol":false,"array":false,"null":false,"number":false}}} 
[INFO ] 2024-07-19 07:02:45.961 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-19 07:02:45.962 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-19 07:02:46.100 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-19 07:02:46.102 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection Mongo enable share cdc: true 
[INFO ] 2024-07-19 07:02:46.102 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 1 enable share cdc: true 
[INFO ] 2024-07-19 07:02:46.158 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自Mongo的共享挖掘任务 
[INFO ] 2024-07-19 07:02:46.191 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav392?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-19 07:02:46.192 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695b94e66ab5ede8a9dc8b0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6695b8966d76494ed53f3874_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-384100775, shareCdcTaskId=6695b94e6d76494ed53f39a0, connectionId=6695b8966d76494ed53f3874) 
[INFO ] 2024-07-19 07:02:46.238 - [任务 1][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_-384100775', head seq: 0, tail seq: 1 
[INFO ] 2024-07-19 07:02:46.243 - [任务 1][TESTPOLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-19 07:02:46.269 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695bb1d66ab5ede8a9de506, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6695b8966d76494ed53f3874__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_297978497, shareCdcTaskId=6695b94e6d76494ed53f39a0, connectionId=6695b8966d76494ed53f3874) 
[INFO ] 2024-07-19 07:02:46.279 - [任务 1][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table_任务 1', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_297978497', head seq: 0, tail seq: 44453 
[INFO ] 2024-07-19 07:02:46.284 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-19 07:02:46.284 - [任务 1][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-19 07:02:46.285 - [任务 1][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-19 07:02:46.286 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-19 07:02:46.287 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Read table count: 2, partition size: 1, read thread number: 2 
[INFO ] 2024-07-19 07:02:46.299 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695b94e66ab5ede8a9dc8b0, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6695b8966d76494ed53f3874_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-384100775, shareCdcTaskId=6695b94e6d76494ed53f39a0, connectionId=6695b8966d76494ed53f3874) 
[INFO ] 2024-07-19 07:02:46.300 - [任务 1][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_-384100775', head seq: 0, tail seq: 1 
[INFO ] 2024-07-19 07:02:46.303 - [任务 1][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 1, external storage name: ExternalStorage_SHARE_CDC_-384100775 
[INFO ] 2024-07-19 07:02:46.303 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-19 07:02:46.312 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 2 
[INFO ] 2024-07-19 07:02:46.334 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695bb1d66ab5ede8a9de506, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6695b8966d76494ed53f3874__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_297978497, shareCdcTaskId=6695b94e6d76494ed53f39a0, connectionId=6695b8966d76494ed53f3874) 
[INFO ] 2024-07-19 07:02:46.334 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=2} 
[INFO ] 2024-07-19 07:02:46.366 - [任务 1][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table_任务 1', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_297978497', head seq: 0, tail seq: 44453 
[INFO ] 2024-07-19 07:02:46.370 - [任务 1][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自Mongo的共享挖掘任务__tapdata_heartbeat_table_任务 1, external storage name: ExternalStorage_SHARE_CDC_297978497 
[INFO ] 2024-07-19 07:02:46.387 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [_tapdata_heartbeat_table] 
[INFO ] 2024-07-19 07:02:46.403 - [任务 1][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-19 07:02:46.405 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read '_tapdata_heartbeat_table' log, sequence: 44452 
[INFO ] 2024-07-19 07:02:46.406 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find by _tapdata_heartbeat_table filter: {sequence=44452} 
[INFO ] 2024-07-19 07:02:46.448 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=_tapdata_heartbeat_table, timestamp=1721201066000, date=Wed Jul 17 15:24:26 CST 2024, before=Document{{}}, after=Document{{_id=6695b95266ab5ede8a9dcd3e, id=6695b8966d76494ed53f3874, ts=Wed Jul 17 15:24:24 CST 2024}}, op=u, offsetString=gAFkABVvcmcuYnNvbi5Cc29uRG9jdW1lbnQBFAAFX2RhdGEBAgATb3JnLmJzb24uQnNvblN0cmlu
ZwAAAdR7ImFycmF5IjpmYWxzZSwiYmluYXJ5IjpmYWxzZSwiYm9vbGVhbiI6ZmFsc2UsImJzb25U
eXBlIjoiU1RSSU5HIiwiZEJQb2ludGVyIjpmYWxzZSwiZGF0ZVRpbWUiOmZhbHNlLCJkZWNpbWFs
MTI4IjpmYWxzZSwiZG9jdW1lbnQiOmZhbHNlLCJkb3VibGUiOmZhbHNlLCJpbnQzMiI6ZmFsc2Us
ImludDY0IjpmYWxzZSwiamF2YVNjcmlwdCI6ZmFsc2UsImphdmFTY3JpcHRXaXRoU2NvcGUiOmZh
bHNlLCJudWxsIjpmYWxzZSwibnVtYmVyIjpmYWxzZSwib2JqZWN0SWQiOmZhbHNlLCJyZWd1bGFy
RXhwcmVzc2lvbiI6ZmFsc2UsInN0cmluZyI6dHJ1ZSwic3ltYm9sIjpmYWxzZSwidGltZXN0YW1w
IjpmYWxzZSwidmFsdWUiOiI4MjY2OTc3MUFBMDAwMDAwMDEyQjAyMkMwMTAwMjk2RTVBMTAwNDEx
NDM0QkVCRDBCMzRDMUNCOTBEQTM2QjQ5MEYxQ0U1NDY2NDVGNjk2NDAwNjQ2Njk1Qjk1MjY2QUI1
RURFOEE5RENEM0UwMDA0In2o
, type=DATA, connectionId=6695b8966d76494ed53f3874, isReplaceEvent=false, _ts=1721201067}} 
[INFO ] 2024-07-19 07:02:49.416 - [任务 1][POLICY] - Incremental sync completed 
[INFO ] 2024-07-19 07:02:49.417 - [任务 1][POLICY] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-19 07:02:49.430 - [任务 1][POLICY] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:397)
	io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:346)
	io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObject(ObjectSerializableImplV2.java:300)
	io.tapdata.flow.engine.V2.util.PdkUtil.decodeOffset(PdkUtil.java:138)
	io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.tapEventWrapper(ShareCdcPDKTaskReader.java:683)
	...

<-- Full Stack Trace -->
java.lang.Exception: An internal error occurred, will close; Error: Reader occur unknown error
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:272)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:137)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:180)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doShareCdc$47(HazelcastSourcePdkDataNode.java:911)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doShareCdc(HazelcastSourcePdkDataNode.java:894)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:617)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.Exception: An internal error occurred, will close; Error: Reader occur unknown error
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.listen(ShareCdcPDKTaskReader.java:372)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$46(HazelcastSourcePdkDataNode.java:909)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:167)
	... 12 more
Caused by: java.lang.Exception: Reader occur unknown error
	at io.tapdata.sharecdc.impl.ShareCdcBaseReader.poll(ShareCdcBaseReader.java:117)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.listen(ShareCdcPDKTaskReader.java:363)
	... 14 more
Caused by: java.lang.Throwable: Reader occur unknown error
	at io.tapdata.sharecdc.impl.ShareCdcBaseReader.handleFailed(ShareCdcBaseReader.java:93)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader$ReadRunner.read(ShareCdcPDKTaskReader.java:591)
	... 6 more
Caused by: java.lang.RuntimeException: Decode offset string failed: gAFkABVvcmcuYnNvbi5Cc29uRG9jdW1lbnQBFAAFX2RhdGEBAgATb3JnLmJzb24uQnNvblN0cmlu
ZwAAAdR7ImFycmF5IjpmYWxzZSwiYmluYXJ5IjpmYWxzZSwiYm9vbGVhbiI6ZmFsc2UsImJzb25U
eXBlIjoiU1RSSU5HIiwiZEJQb2ludGVyIjpmYWxzZSwiZGF0ZVRpbWUiOmZhbHNlLCJkZWNpbWFs
MTI4IjpmYWxzZSwiZG9jdW1lbnQiOmZhbHNlLCJkb3VibGUiOmZhbHNlLCJpbnQzMiI6ZmFsc2Us
ImludDY0IjpmYWxzZSwiamF2YVNjcmlwdCI6ZmFsc2UsImphdmFTY3JpcHRXaXRoU2NvcGUiOmZh
bHNlLCJudWxsIjpmYWxzZSwibnVtYmVyIjpmYWxzZSwib2JqZWN0SWQiOmZhbHNlLCJyZWd1bGFy
RXhwcmVzc2lvbiI6ZmFsc2UsInN0cmluZyI6dHJ1ZSwic3ltYm9sIjpmYWxzZSwidGltZXN0YW1w
IjpmYWxzZSwidmFsdWUiOiI4MjY2OTc3MUFBMDAwMDAwMDEyQjAyMkMwMTAwMjk2RTVBMTAwNDEx
NDM0QkVCRDBCMzRDMUNCOTBEQTM2QjQ5MEYxQ0U1NDY2NDVGNjk2NDAwNjQ2Njk1Qjk1MjY2QUI1
RURFOEE5RENEM0UwMDA0In2o
; Error: null
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.tapEventWrapper(ShareCdcPDKTaskReader.java:686)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader$ReadRunner.lambda$null$6(ShareCdcPDKTaskReader.java:578)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader$ReadRunner.lambda$read$7(ShareCdcPDKTaskReader.java:578)
	at io.tapdata.flow.engine.V2.util.SkipIdleProcessor.process(SkipIdleProcessor.java:95)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader$ReadRunner.read(ShareCdcPDKTaskReader.java:532)
	... 6 more
Caused by: java.lang.NullPointerException
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:397)
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObjectPrivate(ObjectSerializableImplV2.java:346)
	at io.tapdata.pdk.core.api.impl.serialize.ObjectSerializableImplV2.toObject(ObjectSerializableImplV2.java:300)
	at io.tapdata.flow.engine.V2.util.PdkUtil.decodeOffset(PdkUtil.java:138)
	at io.tapdata.sharecdc.impl.ShareCdcPDKTaskReader.tapEventWrapper(ShareCdcPDKTaskReader.java:683)
	... 11 more

[INFO ] 2024-07-19 07:02:49.430 - [任务 1][POLICY] - Job suspend in error handle 
[INFO ] 2024-07-19 07:02:49.685 - [任务 1] - Task [任务 1] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-19 07:02:49.925 - [任务 1][POLICY] - Node POLICY[e6402b3d-a9e8-4bd5-bcd3-0763ddbea750] running status set to false 
[INFO ] 2024-07-19 07:02:49.943 - [任务 1][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-e6402b3d-a9e8-4bd5-bcd3-0763ddbea750 
[INFO ] 2024-07-19 07:02:49.943 - [任务 1][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-e6402b3d-a9e8-4bd5-bcd3-0763ddbea750 
[INFO ] 2024-07-19 07:02:49.944 - [任务 1][POLICY] - Node POLICY[e6402b3d-a9e8-4bd5-bcd3-0763ddbea750] schema data cleaned 
[INFO ] 2024-07-19 07:02:49.945 - [任务 1][POLICY] - Node POLICY[e6402b3d-a9e8-4bd5-bcd3-0763ddbea750] monitor closed 
[INFO ] 2024-07-19 07:02:49.948 - [任务 1][POLICY] - Node POLICY[e6402b3d-a9e8-4bd5-bcd3-0763ddbea750] close complete, cost 41 ms 
[INFO ] 2024-07-19 07:02:49.948 - [任务 1][TESTPOLICY] - Node TESTPOLICY[969da717-2d89-4fde-9654-55546d220d9f] running status set to false 
[INFO ] 2024-07-19 07:02:49.971 - [任务 1][TESTPOLICY] - PDK connector node stopped: HazelcastTargetPdkDataNode-969da717-2d89-4fde-9654-55546d220d9f 
[INFO ] 2024-07-19 07:02:49.972 - [任务 1][TESTPOLICY] - PDK connector node released: HazelcastTargetPdkDataNode-969da717-2d89-4fde-9654-55546d220d9f 
[INFO ] 2024-07-19 07:02:49.972 - [任务 1][TESTPOLICY] - Node TESTPOLICY[969da717-2d89-4fde-9654-55546d220d9f] schema data cleaned 
[INFO ] 2024-07-19 07:02:49.972 - [任务 1][TESTPOLICY] - Node TESTPOLICY[969da717-2d89-4fde-9654-55546d220d9f] monitor closed 
[INFO ] 2024-07-19 07:02:50.015 - [任务 1][TESTPOLICY] - Node TESTPOLICY[969da717-2d89-4fde-9654-55546d220d9f] close complete, cost 24 ms 
[INFO ] 2024-07-19 07:02:56.560 - [任务 1] - Task [任务 1] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-19 07:02:56.582 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-19 07:02:56.742 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@59cee575 
[INFO ] 2024-07-19 07:02:56.744 - [任务 1] - Stop task milestones: 6695b92f6d76494ed53f3959(任务 1)  
[INFO ] 2024-07-19 07:02:56.745 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-19 07:02:56.746 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-19 07:02:56.778 - [任务 1] - Remove memory task client succeed, task: 任务 1[6695b92f6d76494ed53f3959] 
[INFO ] 2024-07-19 07:02:56.779 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6695b92f6d76494ed53f3959] 
