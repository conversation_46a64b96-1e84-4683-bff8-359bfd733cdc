[INFO ] 2024-07-18 11:27:37.804 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238] - Task initialization... 
[INFO ] 2024-07-18 11:27:38.012 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238] - Start task milestones: 66988b9e8315b25db9f53cf1(t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238) 
[INFO ] 2024-07-18 11:27:38.617 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 11:27:39.010 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238] - The engine receives t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 11:27:39.498 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][TableRename] - Node TableRename[ba07b5f1-2174-4daa-bc80-c8b04c4c22fb] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:27:39.499 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[c495e4dc-26f0-4add-b35f-2cef16d337b8] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:27:39.499 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[fd0a4579-a3bb-4eed-9ad6-4ab21b3a90bc] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:27:39.499 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[c495e4dc-26f0-4add-b35f-2cef16d337b8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:27:39.501 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][TableRename] - Node TableRename[ba07b5f1-2174-4daa-bc80-c8b04c4c22fb] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:27:39.501 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[fd0a4579-a3bb-4eed-9ad6-4ab21b3a90bc] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:27:41.680 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_t_1717403468657_3537] - Node(qa_mysql_repl_33306_t_1717403468657_3537) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-18 11:27:41.680 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_t_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 11:27:42.156 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Source node "qa_mysql_repl_33306_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 11:27:42.157 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Source node "qa_mysql_repl_33306_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 11:27:42.157 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 11:27:42.561 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000304","position":463560778,"gtidSet":""} 
[INFO ] 2024-07-18 11:27:42.561 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-18 11:27:42.769 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-07-18 11:27:42.994 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 11:27:42.994 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Starting batch read, table name: t_3_6_1_ddl, offset: null 
[INFO ] 2024-07-18 11:27:43.057 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Table t_3_6_1_ddl is going to be initial synced 
[INFO ] 2024-07-18 11:27:43.057 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Table [t_3_6_1_ddl] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:27:43.071 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Query table 't_3_6_1_ddl' counts: 1 
[INFO ] 2024-07-18 11:27:43.071 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:27:43.071 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 11:27:43.071 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:27:43.195 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Starting stream read, table list: [t_3_6_1_ddl, _tapdata_heartbeat_table], offset: {"filename":"mysql-bin.000304","position":463560778,"gtidSet":""} 
[INFO ] 2024-07-18 11:27:43.195 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Starting mysql cdc, server name: 10df3f56-cbcd-44c1-81da-ffa18900b086 
[INFO ] 2024-07-18 11:27:43.368 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 751702623
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 10df3f56-cbcd-44c1-81da-ffa18900b086
  database.port: 3306
  threadName: Debezium-Mysql-Connector-10df3f56-cbcd-44c1-81da-ffa18900b086
  database.hostname: *************
  database.password: ********
  name: 10df3f56-cbcd-44c1-81da-ffa18900b086
  pdk.offset.string: {"name":"10df3f56-cbcd-44c1-81da-ffa18900b086","offset":{"{\"server\":\"10df3f56-cbcd-44c1-81da-ffa18900b086\"}":"{\"file\":\"mysql-bin.000304\",\"pos\":463560778,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: t0.t_3_6_1_ddl,t0._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: t0
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-18 11:27:43.368 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Connector Mysql incremental start succeed, tables: [t_3_6_1_ddl, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 11:27:55.370 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Read DDL: alter table t0.t_3_6_1_ddl add column c2 int default 123, about to be packaged as some event(s) 
[INFO ] 2024-07-18 11:27:55.371 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - DDL event  - Table: t_3_6_1_ddl
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='10df3f56-cbcd-44c1-81da-ffa18900b086', offset={{"server":"10df3f56-cbcd-44c1-81da-ffa18900b086"}={"ts_sec":1721273274,"file":"mysql-bin.000304","pos":463792461,"server_id":1121}}} 
[INFO ] 2024-07-18 11:27:55.418 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@8f92ef5: {"newFields":[{"autoInc":false,"dataType":"int","defaultValue":"123","name":"c2","nullable":true,"partitionKey":false,"pos":4,"primaryKey":false,"virtual":false}],"originDDL":"alter table t0.t_3_6_1_ddl add column c2 int default 123","referenceTime":1721273274936,"tableId":"t_3_6_1_ddl","time":1721273275367,"type":209} 
[INFO ] 2024-07-18 11:27:55.419 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_t_3_6_1_ddl_6697a4cfb92eda1a86f5244d_66988b9e8315b25db9f53cf1 
[INFO ] 2024-07-18 11:27:55.626 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Alter table schema transform finished 
[INFO ] 2024-07-18 11:30:17.859 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[c495e4dc-26f0-4add-b35f-2cef16d337b8] running status set to false 
[INFO ] 2024-07-18 11:30:17.895 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-18 11:30:17.895 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Mysql binlog reader stopped 
[INFO ] 2024-07-18 11:30:17.896 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 11:30:17.903 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-c495e4dc-26f0-4add-b35f-2cef16d337b8 
[INFO ] 2024-07-18 11:30:17.904 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-c495e4dc-26f0-4add-b35f-2cef16d337b8 
[INFO ] 2024-07-18 11:30:17.904 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[c495e4dc-26f0-4add-b35f-2cef16d337b8] schema data cleaned 
[INFO ] 2024-07-18 11:30:17.906 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[c495e4dc-26f0-4add-b35f-2cef16d337b8] monitor closed 
[INFO ] 2024-07-18 11:30:17.906 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[c495e4dc-26f0-4add-b35f-2cef16d337b8] close complete, cost 61 ms 
[INFO ] 2024-07-18 11:30:17.906 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][TableRename] - Node TableRename[ba07b5f1-2174-4daa-bc80-c8b04c4c22fb] running status set to false 
[INFO ] 2024-07-18 11:30:17.906 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][TableRename] - Node TableRename[ba07b5f1-2174-4daa-bc80-c8b04c4c22fb] schema data cleaned 
[INFO ] 2024-07-18 11:30:17.906 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][TableRename] - Node TableRename[ba07b5f1-2174-4daa-bc80-c8b04c4c22fb] monitor closed 
[INFO ] 2024-07-18 11:30:17.906 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][TableRename] - Node TableRename[ba07b5f1-2174-4daa-bc80-c8b04c4c22fb] close complete, cost 0 ms 
[INFO ] 2024-07-18 11:30:17.932 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[fd0a4579-a3bb-4eed-9ad6-4ab21b3a90bc] running status set to false 
[INFO ] 2024-07-18 11:30:17.932 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_t_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-fd0a4579-a3bb-4eed-9ad6-4ab21b3a90bc 
[INFO ] 2024-07-18 11:30:17.932 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_t_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-fd0a4579-a3bb-4eed-9ad6-4ab21b3a90bc 
[INFO ] 2024-07-18 11:30:17.932 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[fd0a4579-a3bb-4eed-9ad6-4ab21b3a90bc] schema data cleaned 
[INFO ] 2024-07-18 11:30:17.933 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[fd0a4579-a3bb-4eed-9ad6-4ab21b3a90bc] monitor closed 
[INFO ] 2024-07-18 11:30:18.133 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[fd0a4579-a3bb-4eed-9ad6-4ab21b3a90bc] close complete, cost 27 ms 
[INFO ] 2024-07-18 11:30:18.174 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 11:30:18.294 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@68a1fce 
[INFO ] 2024-07-18 11:30:18.295 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238] - Stop task milestones: 66988b9e8315b25db9f53cf1(t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238)  
[INFO ] 2024-07-18 11:30:18.316 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238] - Stopped task aspect(s) 
[INFO ] 2024-07-18 11:30:18.316 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 11:30:18.373 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238] - Remove memory task client succeed, task: t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238[66988b9e8315b25db9f53cf1] 
[INFO ] 2024-07-18 11:30:18.373 - [t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238] - Destroy memory task client cache succeed, task: t_3.6.1-mysql_to_mysql_ddl_new_1717403468657_3537-1721273238[66988b9e8315b25db9f53cf1] 
