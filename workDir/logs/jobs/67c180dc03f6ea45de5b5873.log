[TRACE] 2025-02-28 17:51:40.162 - [PG~ sybase 反向(100)][3ae73a4d-888a-406a-97c3-cb4edfe499ba] - Node 3ae73a4d-888a-406a-97c3-cb4edfe499ba[3ae73a4d-888a-406a-97c3-cb4edfe499ba] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:51:40.165 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:51:40.165 - [PG~ sybase 反向(100)][3ae73a4d-888a-406a-97c3-cb4edfe499ba] - Node 3ae73a4d-888a-406a-97c3-cb4edfe499ba[3ae73a4d-888a-406a-97c3-cb4edfe499ba] preload schema finished, cost 1 ms 
[TRACE] 2025-02-28 17:51:40.165 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] preload schema finished, cost 2 ms 
[TRACE] 2025-02-28 17:51:40.165 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 23463d9f-6591-4914-8978-c7e93ad536b0) enable batch process 
[TRACE] 2025-02-28 17:51:40.165 - [PG~ sybase 反向(100)][3ae73a4d-888a-406a-97c3-cb4edfe499ba] - Exception skipping - The current exception does not match the skip exception strategy, message: HazelcastSchemaTargetNode only allows one predecessor node 
[TRACE] 2025-02-28 17:51:40.165 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] running status set to false 
[TRACE] 2025-02-28 17:51:40.171 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] schema data cleaned 
[TRACE] 2025-02-28 17:51:40.172 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] monitor closed 
[TRACE] 2025-02-28 17:51:40.172 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] close complete, cost 5 ms 
[ERROR] 2025-02-28 17:51:40.178 - [PG~ sybase 反向(100)][3ae73a4d-888a-406a-97c3-cb4edfe499ba] - java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node <-- Error Message -->
java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node
	io.tapdata.flow.engine.V2.node.hazelcast.data.HazelcastSchemaTargetNode.doInit(HazelcastSchemaTargetNode.java:112)
	io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	...

<-- Full Stack Trace -->
java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:782)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:664)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:657)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node
	at io.tapdata.flow.engine.V2.node.hazelcast.data.HazelcastSchemaTargetNode.doInit(HazelcastSchemaTargetNode.java:112)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	... 12 more

[TRACE] 2025-02-28 17:51:41.045 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] start preload schema,table counts: 1 
[TRACE] 2025-02-28 17:51:41.047 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] start preload schema,table counts: 1 
[TRACE] 2025-02-28 17:51:41.047 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:41.047 - [PG~ sybase 反向(100)][1a9f9d2f-fc84-451e-ba6f-2febd436990e] - Node 1a9f9d2f-fc84-451e-ba6f-2febd436990e[1a9f9d2f-fc84-451e-ba6f-2febd436990e] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:51:41.047 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:41.047 - [PG~ sybase 反向(100)][1a9f9d2f-fc84-451e-ba6f-2febd436990e] - Node 1a9f9d2f-fc84-451e-ba6f-2febd436990e[1a9f9d2f-fc84-451e-ba6f-2febd436990e] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:41.247 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 23463d9f-6591-4914-8978-c7e93ad536b0) enable batch process 
[WARN ] 2025-02-28 17:51:41.334 - [PG~ sybase 反向(100)][Pg] - Source table is empty, trying to mock data 
[TRACE] 2025-02-28 17:51:41.351 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] running status set to false 
[TRACE] 2025-02-28 17:51:41.352 - [PG~ sybase 反向(100)][Pg] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736301092 
[TRACE] 2025-02-28 17:51:41.352 - [PG~ sybase 反向(100)][Pg] - PDK connector node released: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736301092 
[TRACE] 2025-02-28 17:51:41.354 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] schema data cleaned 
[TRACE] 2025-02-28 17:51:41.354 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] monitor closed 
[TRACE] 2025-02-28 17:51:41.568 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] close complete, cost 18 ms 
[TRACE] 2025-02-28 17:51:42.184 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] running status set to false 
[TRACE] 2025-02-28 17:51:42.186 - [PG~ sybase 反向(100)][1a9f9d2f-fc84-451e-ba6f-2febd436990e] - Node 1a9f9d2f-fc84-451e-ba6f-2febd436990e[1a9f9d2f-fc84-451e-ba6f-2febd436990e] running status set to false 
[TRACE] 2025-02-28 17:51:42.186 - [PG~ sybase 反向(100)][1a9f9d2f-fc84-451e-ba6f-2febd436990e] - Node 1a9f9d2f-fc84-451e-ba6f-2febd436990e[1a9f9d2f-fc84-451e-ba6f-2febd436990e] schema data cleaned 
[TRACE] 2025-02-28 17:51:42.186 - [PG~ sybase 反向(100)][1a9f9d2f-fc84-451e-ba6f-2febd436990e] - Node 1a9f9d2f-fc84-451e-ba6f-2febd436990e[1a9f9d2f-fc84-451e-ba6f-2febd436990e] monitor closed 
[TRACE] 2025-02-28 17:51:42.186 - [PG~ sybase 反向(100)][1a9f9d2f-fc84-451e-ba6f-2febd436990e] - Node 1a9f9d2f-fc84-451e-ba6f-2febd436990e[1a9f9d2f-fc84-451e-ba6f-2febd436990e] close complete, cost 27 ms 
[INFO ] 2025-02-28 17:51:42.197 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Pg-b5bdf320-6143-4d1a-be89-bde3949d7d60 
[INFO ] 2025-02-28 17:51:42.199 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Pg-b5bdf320-6143-4d1a-be89-bde3949d7d60 
[INFO ] 2025-02-28 17:51:42.200 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67c180dc03f6ea45de5b5873-23463d9f-6591-4914-8978-c7e93ad536b0-67b4d8043d2c63001046bc9c] schema data cleaned 
[TRACE] 2025-02-28 17:51:42.213 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] schema data cleaned 
[TRACE] 2025-02-28 17:51:42.214 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] monitor closed 
[TRACE] 2025-02-28 17:51:42.214 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] close complete, cost 78 ms 
[TRACE] 2025-02-28 17:51:42.226 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:51:42.226 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:51:42.233 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
[TRACE] 2025-02-28 17:51:42.396 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] start preload schema,table counts: 1 
[TRACE] 2025-02-28 17:51:42.397 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] start preload schema,table counts: 1 
[TRACE] 2025-02-28 17:51:42.397 - [PG~ sybase 反向(100)][381044b9-604a-4d3a-9490-fb01aa55d01e] - Node 381044b9-604a-4d3a-9490-fb01aa55d01e[381044b9-604a-4d3a-9490-fb01aa55d01e] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:51:42.397 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:42.402 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:42.404 - [PG~ sybase 反向(100)][381044b9-604a-4d3a-9490-fb01aa55d01e] - Node 381044b9-604a-4d3a-9490-fb01aa55d01e[381044b9-604a-4d3a-9490-fb01aa55d01e] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:42.404 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 23463d9f-6591-4914-8978-c7e93ad536b0) enable batch process 
[TRACE] 2025-02-28 17:51:42.732 - [PG~ sybase 反向(100)][3ae73a4d-888a-406a-97c3-cb4edfe499ba] - Node 3ae73a4d-888a-406a-97c3-cb4edfe499ba[3ae73a4d-888a-406a-97c3-cb4edfe499ba] running status set to false 
[TRACE] 2025-02-28 17:51:42.733 - [PG~ sybase 反向(100)][3ae73a4d-888a-406a-97c3-cb4edfe499ba] - Node 3ae73a4d-888a-406a-97c3-cb4edfe499ba[3ae73a4d-888a-406a-97c3-cb4edfe499ba] schema data cleaned 
[TRACE] 2025-02-28 17:51:42.733 - [PG~ sybase 反向(100)][3ae73a4d-888a-406a-97c3-cb4edfe499ba] - Node 3ae73a4d-888a-406a-97c3-cb4edfe499ba[3ae73a4d-888a-406a-97c3-cb4edfe499ba] monitor closed 
[TRACE] 2025-02-28 17:51:42.733 - [PG~ sybase 反向(100)][3ae73a4d-888a-406a-97c3-cb4edfe499ba] - Node 3ae73a4d-888a-406a-97c3-cb4edfe499ba[3ae73a4d-888a-406a-97c3-cb4edfe499ba] close complete, cost 11 ms 
[TRACE] 2025-02-28 17:51:42.733 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:51:42.733 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:51:42.740 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
[WARN ] 2025-02-28 17:51:42.741 - [PG~ sybase 反向(100)][Pg] - Source table is empty, trying to mock data 
[TRACE] 2025-02-28 17:51:42.768 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] running status set to false 
[TRACE] 2025-02-28 17:51:42.768 - [PG~ sybase 反向(100)][Pg] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736302449 
[TRACE] 2025-02-28 17:51:42.768 - [PG~ sybase 反向(100)][Pg] - PDK connector node released: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736302449 
[TRACE] 2025-02-28 17:51:42.769 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] schema data cleaned 
[TRACE] 2025-02-28 17:51:42.770 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] monitor closed 
[TRACE] 2025-02-28 17:51:42.973 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] close complete, cost 25 ms 
[TRACE] 2025-02-28 17:51:43.005 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] start preload schema,table counts: 1 
[TRACE] 2025-02-28 17:51:43.005 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] start preload schema,table counts: 1 
[TRACE] 2025-02-28 17:51:43.006 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:43.006 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:43.006 - [PG~ sybase 反向(100)][d3138601-8c7e-4aa2-8d0d-1116a3e2a396] - Node d3138601-8c7e-4aa2-8d0d-1116a3e2a396[d3138601-8c7e-4aa2-8d0d-1116a3e2a396] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:51:43.009 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 23463d9f-6591-4914-8978-c7e93ad536b0) enable batch process 
[TRACE] 2025-02-28 17:51:43.010 - [PG~ sybase 反向(100)][d3138601-8c7e-4aa2-8d0d-1116a3e2a396] - Node d3138601-8c7e-4aa2-8d0d-1116a3e2a396[d3138601-8c7e-4aa2-8d0d-1116a3e2a396] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:43.116 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] running status set to false 
[TRACE] 2025-02-28 17:51:43.116 - [PG~ sybase 反向(100)][381044b9-604a-4d3a-9490-fb01aa55d01e] - Node 381044b9-604a-4d3a-9490-fb01aa55d01e[381044b9-604a-4d3a-9490-fb01aa55d01e] running status set to false 
[TRACE] 2025-02-28 17:51:43.116 - [PG~ sybase 反向(100)][381044b9-604a-4d3a-9490-fb01aa55d01e] - Node 381044b9-604a-4d3a-9490-fb01aa55d01e[381044b9-604a-4d3a-9490-fb01aa55d01e] schema data cleaned 
[TRACE] 2025-02-28 17:51:43.116 - [PG~ sybase 反向(100)][381044b9-604a-4d3a-9490-fb01aa55d01e] - Node 381044b9-604a-4d3a-9490-fb01aa55d01e[381044b9-604a-4d3a-9490-fb01aa55d01e] monitor closed 
[TRACE] 2025-02-28 17:51:43.116 - [PG~ sybase 反向(100)][381044b9-604a-4d3a-9490-fb01aa55d01e] - Node 381044b9-604a-4d3a-9490-fb01aa55d01e[381044b9-604a-4d3a-9490-fb01aa55d01e] close complete, cost 1 ms 
[INFO ] 2025-02-28 17:51:43.128 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Pg-252ad287-c84f-42d6-9aaa-9c8b7ea3c6ba 
[INFO ] 2025-02-28 17:51:43.129 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Pg-252ad287-c84f-42d6-9aaa-9c8b7ea3c6ba 
[INFO ] 2025-02-28 17:51:43.129 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67c180dc03f6ea45de5b5873-23463d9f-6591-4914-8978-c7e93ad536b0-67b4d8043d2c63001046bc9c] schema data cleaned 
[TRACE] 2025-02-28 17:51:43.131 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] schema data cleaned 
[TRACE] 2025-02-28 17:51:43.131 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] monitor closed 
[TRACE] 2025-02-28 17:51:43.133 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] close complete, cost 18 ms 
[TRACE] 2025-02-28 17:51:43.133 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:51:43.133 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:51:43.344 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
[WARN ] 2025-02-28 17:51:43.361 - [PG~ sybase 反向(100)][Pg] - Source table is empty, trying to mock data 
[TRACE] 2025-02-28 17:51:43.361 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] running status set to false 
[TRACE] 2025-02-28 17:51:43.393 - [PG~ sybase 反向(100)][Pg] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736303029 
[TRACE] 2025-02-28 17:51:43.394 - [PG~ sybase 反向(100)][Pg] - PDK connector node released: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736303029 
[TRACE] 2025-02-28 17:51:43.394 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] schema data cleaned 
[TRACE] 2025-02-28 17:51:43.394 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] monitor closed 
[TRACE] 2025-02-28 17:51:43.394 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] close complete, cost 36 ms 
[TRACE] 2025-02-28 17:51:43.748 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] running status set to false 
[TRACE] 2025-02-28 17:51:43.756 - [PG~ sybase 反向(100)][d3138601-8c7e-4aa2-8d0d-1116a3e2a396] - Node d3138601-8c7e-4aa2-8d0d-1116a3e2a396[d3138601-8c7e-4aa2-8d0d-1116a3e2a396] running status set to false 
[TRACE] 2025-02-28 17:51:43.757 - [PG~ sybase 反向(100)][d3138601-8c7e-4aa2-8d0d-1116a3e2a396] - Node d3138601-8c7e-4aa2-8d0d-1116a3e2a396[d3138601-8c7e-4aa2-8d0d-1116a3e2a396] schema data cleaned 
[TRACE] 2025-02-28 17:51:43.758 - [PG~ sybase 反向(100)][d3138601-8c7e-4aa2-8d0d-1116a3e2a396] - Node d3138601-8c7e-4aa2-8d0d-1116a3e2a396[d3138601-8c7e-4aa2-8d0d-1116a3e2a396] monitor closed 
[INFO ] 2025-02-28 17:51:43.758 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Pg-31746148-f1d5-45d5-bb5c-0b8acd395b5b 
[TRACE] 2025-02-28 17:51:43.758 - [PG~ sybase 反向(100)][d3138601-8c7e-4aa2-8d0d-1116a3e2a396] - Node d3138601-8c7e-4aa2-8d0d-1116a3e2a396[d3138601-8c7e-4aa2-8d0d-1116a3e2a396] close complete, cost 5 ms 
[INFO ] 2025-02-28 17:51:43.759 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Pg-31746148-f1d5-45d5-bb5c-0b8acd395b5b 
[INFO ] 2025-02-28 17:51:43.760 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67c180dc03f6ea45de5b5873-23463d9f-6591-4914-8978-c7e93ad536b0-67b4d8043d2c63001046bc9c] schema data cleaned 
[TRACE] 2025-02-28 17:51:43.762 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] schema data cleaned 
[TRACE] 2025-02-28 17:51:43.763 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] monitor closed 
[TRACE] 2025-02-28 17:51:43.765 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] close complete, cost 17 ms 
[TRACE] 2025-02-28 17:51:43.765 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:51:43.765 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:51:43.973 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
[TRACE] 2025-02-28 17:51:44.300 - [PG~ sybase 反向(100)][3193a299-6b05-4253-9c08-b7d60f5724b6] - Node 3193a299-6b05-4253-9c08-b7d60f5724b6[3193a299-6b05-4253-9c08-b7d60f5724b6] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:51:44.300 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] start preload schema,table counts: 1 
[TRACE] 2025-02-28 17:51:44.300 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] start preload schema,table counts: 1 
[TRACE] 2025-02-28 17:51:44.300 - [PG~ sybase 反向(100)][3193a299-6b05-4253-9c08-b7d60f5724b6] - Node 3193a299-6b05-4253-9c08-b7d60f5724b6[3193a299-6b05-4253-9c08-b7d60f5724b6] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:44.300 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:44.300 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:44.300 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 23463d9f-6591-4914-8978-c7e93ad536b0) enable batch process 
[WARN ] 2025-02-28 17:51:44.580 - [PG~ sybase 反向(100)][Pg] - Source table is empty, trying to mock data 
[TRACE] 2025-02-28 17:51:44.581 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] running status set to false 
[TRACE] 2025-02-28 17:51:44.589 - [PG~ sybase 反向(100)][Pg] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736304326 
[TRACE] 2025-02-28 17:51:44.590 - [PG~ sybase 反向(100)][Pg] - PDK connector node released: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736304326 
[TRACE] 2025-02-28 17:51:44.590 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] schema data cleaned 
[TRACE] 2025-02-28 17:51:44.590 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] monitor closed 
[TRACE] 2025-02-28 17:51:44.590 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] close complete, cost 11 ms 
[TRACE] 2025-02-28 17:51:44.882 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] running status set to false 
[TRACE] 2025-02-28 17:51:44.882 - [PG~ sybase 反向(100)][3193a299-6b05-4253-9c08-b7d60f5724b6] - Node 3193a299-6b05-4253-9c08-b7d60f5724b6[3193a299-6b05-4253-9c08-b7d60f5724b6] running status set to false 
[TRACE] 2025-02-28 17:51:44.882 - [PG~ sybase 反向(100)][3193a299-6b05-4253-9c08-b7d60f5724b6] - Node 3193a299-6b05-4253-9c08-b7d60f5724b6[3193a299-6b05-4253-9c08-b7d60f5724b6] schema data cleaned 
[TRACE] 2025-02-28 17:51:44.882 - [PG~ sybase 反向(100)][3193a299-6b05-4253-9c08-b7d60f5724b6] - Node 3193a299-6b05-4253-9c08-b7d60f5724b6[3193a299-6b05-4253-9c08-b7d60f5724b6] monitor closed 
[TRACE] 2025-02-28 17:51:44.887 - [PG~ sybase 反向(100)][3193a299-6b05-4253-9c08-b7d60f5724b6] - Node 3193a299-6b05-4253-9c08-b7d60f5724b6[3193a299-6b05-4253-9c08-b7d60f5724b6] close complete, cost 4 ms 
[INFO ] 2025-02-28 17:51:44.891 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Pg-af3c9b33-1187-430f-865f-bb77d9a80d80 
[INFO ] 2025-02-28 17:51:44.891 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Pg-af3c9b33-1187-430f-865f-bb77d9a80d80 
[INFO ] 2025-02-28 17:51:44.894 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67c180dc03f6ea45de5b5873-23463d9f-6591-4914-8978-c7e93ad536b0-67b4d8043d2c63001046bc9c] schema data cleaned 
[TRACE] 2025-02-28 17:51:44.894 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] schema data cleaned 
[TRACE] 2025-02-28 17:51:44.894 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] monitor closed 
[TRACE] 2025-02-28 17:51:44.895 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] close complete, cost 19 ms 
[TRACE] 2025-02-28 17:51:44.897 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:51:44.897 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:51:45.110 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
[TRACE] 2025-02-28 17:51:50.425 - [PG~ sybase 反向(100)][a447fff8-7225-4316-a63a-0bb87dae18ae] - Node a447fff8-7225-4316-a63a-0bb87dae18ae[a447fff8-7225-4316-a63a-0bb87dae18ae] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:51:50.425 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] start preload schema,table counts: 1 
[TRACE] 2025-02-28 17:51:50.427 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] start preload schema,table counts: 1 
[TRACE] 2025-02-28 17:51:50.427 - [PG~ sybase 反向(100)][a447fff8-7225-4316-a63a-0bb87dae18ae] - Node a447fff8-7225-4316-a63a-0bb87dae18ae[a447fff8-7225-4316-a63a-0bb87dae18ae] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:50.427 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:50.427 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:50.428 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 23463d9f-6591-4914-8978-c7e93ad536b0) enable batch process 
[WARN ] 2025-02-28 17:51:50.710 - [PG~ sybase 反向(100)][Pg] - Source table is empty, trying to mock data 
[TRACE] 2025-02-28 17:51:50.710 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] running status set to false 
[TRACE] 2025-02-28 17:51:50.722 - [PG~ sybase 反向(100)][Pg] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736310444 
[TRACE] 2025-02-28 17:51:50.722 - [PG~ sybase 反向(100)][Pg] - PDK connector node released: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736310444 
[TRACE] 2025-02-28 17:51:50.722 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] schema data cleaned 
[TRACE] 2025-02-28 17:51:50.723 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] monitor closed 
[TRACE] 2025-02-28 17:51:50.723 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] close complete, cost 16 ms 
[TRACE] 2025-02-28 17:51:51.101 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] running status set to false 
[TRACE] 2025-02-28 17:51:51.114 - [PG~ sybase 反向(100)][a447fff8-7225-4316-a63a-0bb87dae18ae] - Node a447fff8-7225-4316-a63a-0bb87dae18ae[a447fff8-7225-4316-a63a-0bb87dae18ae] running status set to false 
[TRACE] 2025-02-28 17:51:51.114 - [PG~ sybase 反向(100)][a447fff8-7225-4316-a63a-0bb87dae18ae] - Node a447fff8-7225-4316-a63a-0bb87dae18ae[a447fff8-7225-4316-a63a-0bb87dae18ae] schema data cleaned 
[TRACE] 2025-02-28 17:51:51.120 - [PG~ sybase 反向(100)][a447fff8-7225-4316-a63a-0bb87dae18ae] - Node a447fff8-7225-4316-a63a-0bb87dae18ae[a447fff8-7225-4316-a63a-0bb87dae18ae] monitor closed 
[INFO ] 2025-02-28 17:51:51.120 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Pg-8522f9a9-cfbf-4107-a0f5-1756d1e15c99 
[TRACE] 2025-02-28 17:51:51.129 - [PG~ sybase 反向(100)][a447fff8-7225-4316-a63a-0bb87dae18ae] - Node a447fff8-7225-4316-a63a-0bb87dae18ae[a447fff8-7225-4316-a63a-0bb87dae18ae] close complete, cost 18 ms 
[INFO ] 2025-02-28 17:51:51.130 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Pg-8522f9a9-cfbf-4107-a0f5-1756d1e15c99 
[INFO ] 2025-02-28 17:51:51.130 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67c180dc03f6ea45de5b5873-23463d9f-6591-4914-8978-c7e93ad536b0-67b4d8043d2c63001046bc9c] schema data cleaned 
[TRACE] 2025-02-28 17:51:51.130 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] schema data cleaned 
[TRACE] 2025-02-28 17:51:51.130 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] monitor closed 
[TRACE] 2025-02-28 17:51:51.131 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] close complete, cost 40 ms 
[TRACE] 2025-02-28 17:51:51.132 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:51:51.132 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:51:51.334 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
[TRACE] 2025-02-28 17:51:56.195 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] start preload schema,table counts: 1 
[TRACE] 2025-02-28 17:51:56.196 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] start preload schema,table counts: 1 
[TRACE] 2025-02-28 17:51:56.197 - [PG~ sybase 反向(100)][41ad8c08-e626-42ba-9549-d441ed84d5d6] - Node 41ad8c08-e626-42ba-9549-d441ed84d5d6[41ad8c08-e626-42ba-9549-d441ed84d5d6] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:51:56.197 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:56.197 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:56.197 - [PG~ sybase 反向(100)][41ad8c08-e626-42ba-9549-d441ed84d5d6] - Node 41ad8c08-e626-42ba-9549-d441ed84d5d6[41ad8c08-e626-42ba-9549-d441ed84d5d6] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:51:56.197 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 23463d9f-6591-4914-8978-c7e93ad536b0) enable batch process 
[WARN ] 2025-02-28 17:51:56.481 - [PG~ sybase 反向(100)][Pg] - Source table is empty, trying to mock data 
[TRACE] 2025-02-28 17:51:56.497 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] running status set to false 
[TRACE] 2025-02-28 17:51:56.497 - [PG~ sybase 反向(100)][Pg] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736316240 
[TRACE] 2025-02-28 17:51:56.497 - [PG~ sybase 反向(100)][Pg] - PDK connector node released: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736316240 
[TRACE] 2025-02-28 17:51:56.497 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] schema data cleaned 
[TRACE] 2025-02-28 17:51:56.497 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] monitor closed 
[TRACE] 2025-02-28 17:51:56.498 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] close complete, cost 13 ms 
[TRACE] 2025-02-28 17:51:56.775 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] running status set to false 
[INFO ] 2025-02-28 17:51:56.786 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Pg-4420e39a-b665-43f5-af0f-42aaf5516ad6 
[INFO ] 2025-02-28 17:51:56.786 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Pg-4420e39a-b665-43f5-af0f-42aaf5516ad6 
[INFO ] 2025-02-28 17:51:56.786 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67c180dc03f6ea45de5b5873-23463d9f-6591-4914-8978-c7e93ad536b0-67b4d8043d2c63001046bc9c] schema data cleaned 
[TRACE] 2025-02-28 17:51:56.790 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] schema data cleaned 
[TRACE] 2025-02-28 17:51:56.790 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] monitor closed 
[TRACE] 2025-02-28 17:51:56.790 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] close complete, cost 15 ms 
[TRACE] 2025-02-28 17:51:56.861 - [PG~ sybase 反向(100)][41ad8c08-e626-42ba-9549-d441ed84d5d6] - Node 41ad8c08-e626-42ba-9549-d441ed84d5d6[41ad8c08-e626-42ba-9549-d441ed84d5d6] running status set to false 
[TRACE] 2025-02-28 17:51:56.863 - [PG~ sybase 反向(100)][41ad8c08-e626-42ba-9549-d441ed84d5d6] - Node 41ad8c08-e626-42ba-9549-d441ed84d5d6[41ad8c08-e626-42ba-9549-d441ed84d5d6] schema data cleaned 
[TRACE] 2025-02-28 17:51:56.863 - [PG~ sybase 反向(100)][41ad8c08-e626-42ba-9549-d441ed84d5d6] - Node 41ad8c08-e626-42ba-9549-d441ed84d5d6[41ad8c08-e626-42ba-9549-d441ed84d5d6] monitor closed 
[TRACE] 2025-02-28 17:51:56.863 - [PG~ sybase 反向(100)][41ad8c08-e626-42ba-9549-d441ed84d5d6] - Node 41ad8c08-e626-42ba-9549-d441ed84d5d6[41ad8c08-e626-42ba-9549-d441ed84d5d6] close complete, cost 5 ms 
[TRACE] 2025-02-28 17:51:56.868 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:51:56.868 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:51:56.868 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
[TRACE] 2025-02-28 17:52:06.664 - [PG~ sybase 反向(100)][2323e91f-5125-469a-a396-73180b98e3de] - Node 2323e91f-5125-469a-a396-73180b98e3de[2323e91f-5125-469a-a396-73180b98e3de] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:52:06.665 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] start preload schema,table counts: 1 
[TRACE] 2025-02-28 17:52:06.665 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] start preload schema,table counts: 1 
[TRACE] 2025-02-28 17:52:06.665 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] preload schema finished, cost 1 ms 
[TRACE] 2025-02-28 17:52:06.666 - [PG~ sybase 反向(100)][2323e91f-5125-469a-a396-73180b98e3de] - Node 2323e91f-5125-469a-a396-73180b98e3de[2323e91f-5125-469a-a396-73180b98e3de] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:52:06.666 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] preload schema finished, cost 2 ms 
[TRACE] 2025-02-28 17:52:06.666 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 23463d9f-6591-4914-8978-c7e93ad536b0) enable batch process 
[WARN ] 2025-02-28 17:52:06.981 - [PG~ sybase 反向(100)][Pg] - Source table is empty, trying to mock data 
[TRACE] 2025-02-28 17:52:06.981 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] running status set to false 
[TRACE] 2025-02-28 17:52:06.992 - [PG~ sybase 反向(100)][Pg] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736326691 
[TRACE] 2025-02-28 17:52:06.992 - [PG~ sybase 反向(100)][Pg] - PDK connector node released: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736326691 
[TRACE] 2025-02-28 17:52:06.992 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] schema data cleaned 
[TRACE] 2025-02-28 17:52:06.992 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] monitor closed 
[TRACE] 2025-02-28 17:52:06.994 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] close complete, cost 12 ms 
[TRACE] 2025-02-28 17:52:07.307 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] running status set to false 
[INFO ] 2025-02-28 17:52:07.320 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Pg-c8ecfb81-b67b-4a5c-8037-cabc90b215bf 
[INFO ] 2025-02-28 17:52:07.320 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Pg-c8ecfb81-b67b-4a5c-8037-cabc90b215bf 
[INFO ] 2025-02-28 17:52:07.320 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67c180dc03f6ea45de5b5873-23463d9f-6591-4914-8978-c7e93ad536b0-67b4d8043d2c63001046bc9c] schema data cleaned 
[TRACE] 2025-02-28 17:52:07.323 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] schema data cleaned 
[TRACE] 2025-02-28 17:52:07.323 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] monitor closed 
[TRACE] 2025-02-28 17:52:07.343 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] close complete, cost 18 ms 
[TRACE] 2025-02-28 17:52:07.344 - [PG~ sybase 反向(100)][2323e91f-5125-469a-a396-73180b98e3de] - Node 2323e91f-5125-469a-a396-73180b98e3de[2323e91f-5125-469a-a396-73180b98e3de] running status set to false 
[TRACE] 2025-02-28 17:52:07.344 - [PG~ sybase 反向(100)][2323e91f-5125-469a-a396-73180b98e3de] - Node 2323e91f-5125-469a-a396-73180b98e3de[2323e91f-5125-469a-a396-73180b98e3de] schema data cleaned 
[TRACE] 2025-02-28 17:52:07.344 - [PG~ sybase 反向(100)][2323e91f-5125-469a-a396-73180b98e3de] - Node 2323e91f-5125-469a-a396-73180b98e3de[2323e91f-5125-469a-a396-73180b98e3de] monitor closed 
[TRACE] 2025-02-28 17:52:07.344 - [PG~ sybase 反向(100)][2323e91f-5125-469a-a396-73180b98e3de] - Node 2323e91f-5125-469a-a396-73180b98e3de[2323e91f-5125-469a-a396-73180b98e3de] close complete, cost 3 ms 
[TRACE] 2025-02-28 17:52:07.347 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:52:07.347 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:52:07.558 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
[TRACE] 2025-02-28 17:53:04.700 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:53:04.701 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:53:04.703 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:53:04.704 - [PG~ sybase 反向(100)][e8240b17-76ce-47fc-a9b5-7a9f49e28e59] - Node e8240b17-76ce-47fc-a9b5-7a9f49e28e59[e8240b17-76ce-47fc-a9b5-7a9f49e28e59] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:53:04.704 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:53:04.704 - [PG~ sybase 反向(100)][e8240b17-76ce-47fc-a9b5-7a9f49e28e59] - Node e8240b17-76ce-47fc-a9b5-7a9f49e28e59[e8240b17-76ce-47fc-a9b5-7a9f49e28e59] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:53:04.704 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 23463d9f-6591-4914-8978-c7e93ad536b0) enable batch process 
[WARN ] 2025-02-28 17:53:04.995 - [PG~ sybase 反向(100)][Pg] - Source table is empty, trying to mock data 
[TRACE] 2025-02-28 17:53:05.014 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] running status set to false 
[TRACE] 2025-02-28 17:53:05.014 - [PG~ sybase 反向(100)][Pg] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736384727 
[TRACE] 2025-02-28 17:53:05.014 - [PG~ sybase 反向(100)][Pg] - PDK connector node released: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736384727 
[TRACE] 2025-02-28 17:53:05.016 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] schema data cleaned 
[TRACE] 2025-02-28 17:53:05.017 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] monitor closed 
[TRACE] 2025-02-28 17:53:05.017 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] close complete, cost 17 ms 
[TRACE] 2025-02-28 17:53:05.426 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] running status set to false 
[INFO ] 2025-02-28 17:53:05.439 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Pg-46f306af-1154-40b4-a284-72b324b94af3 
[INFO ] 2025-02-28 17:53:05.439 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Pg-46f306af-1154-40b4-a284-72b324b94af3 
[INFO ] 2025-02-28 17:53:05.439 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67c180dc03f6ea45de5b5873-23463d9f-6591-4914-8978-c7e93ad536b0-67b4d8043d2c63001046bc9c] schema data cleaned 
[TRACE] 2025-02-28 17:53:05.440 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] schema data cleaned 
[TRACE] 2025-02-28 17:53:05.440 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] monitor closed 
[TRACE] 2025-02-28 17:53:05.441 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] close complete, cost 43 ms 
[TRACE] 2025-02-28 17:53:05.452 - [PG~ sybase 反向(100)][e8240b17-76ce-47fc-a9b5-7a9f49e28e59] - Node e8240b17-76ce-47fc-a9b5-7a9f49e28e59[e8240b17-76ce-47fc-a9b5-7a9f49e28e59] running status set to false 
[TRACE] 2025-02-28 17:53:05.452 - [PG~ sybase 反向(100)][e8240b17-76ce-47fc-a9b5-7a9f49e28e59] - Node e8240b17-76ce-47fc-a9b5-7a9f49e28e59[e8240b17-76ce-47fc-a9b5-7a9f49e28e59] schema data cleaned 
[TRACE] 2025-02-28 17:53:05.452 - [PG~ sybase 反向(100)][e8240b17-76ce-47fc-a9b5-7a9f49e28e59] - Node e8240b17-76ce-47fc-a9b5-7a9f49e28e59[e8240b17-76ce-47fc-a9b5-7a9f49e28e59] monitor closed 
[TRACE] 2025-02-28 17:53:05.454 - [PG~ sybase 反向(100)][e8240b17-76ce-47fc-a9b5-7a9f49e28e59] - Node e8240b17-76ce-47fc-a9b5-7a9f49e28e59[e8240b17-76ce-47fc-a9b5-7a9f49e28e59] close complete, cost 0 ms 
[TRACE] 2025-02-28 17:53:05.454 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:53:05.454 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:53:05.657 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
[TRACE] 2025-02-28 17:55:33.338 - [PG~ sybase 反向(100)][a223bc21-1554-4335-9c12-917e0183a6e9] - Node a223bc21-1554-4335-9c12-917e0183a6e9[a223bc21-1554-4335-9c12-917e0183a6e9] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:55:33.338 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:55:33.338 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:55:33.339 - [PG~ sybase 反向(100)][a223bc21-1554-4335-9c12-917e0183a6e9] - Node a223bc21-1554-4335-9c12-917e0183a6e9[a223bc21-1554-4335-9c12-917e0183a6e9] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:55:33.339 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:55:33.339 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] preload schema finished, cost 1 ms 
[TRACE] 2025-02-28 17:55:33.539 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 23463d9f-6591-4914-8978-c7e93ad536b0) enable batch process 
[WARN ] 2025-02-28 17:55:33.603 - [PG~ sybase 反向(100)][Pg] - Source table is empty, trying to mock data 
[TRACE] 2025-02-28 17:55:33.603 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] running status set to false 
[TRACE] 2025-02-28 17:55:33.618 - [PG~ sybase 反向(100)][Pg] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736533355 
[TRACE] 2025-02-28 17:55:33.618 - [PG~ sybase 反向(100)][Pg] - PDK connector node released: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736533355 
[TRACE] 2025-02-28 17:55:33.618 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] schema data cleaned 
[TRACE] 2025-02-28 17:55:33.618 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] monitor closed 
[TRACE] 2025-02-28 17:55:33.618 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] close complete, cost 18 ms 
[TRACE] 2025-02-28 17:55:33.999 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] running status set to false 
[INFO ] 2025-02-28 17:55:34.014 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Pg-504ca9d2-b0df-4c61-9ae8-255b0207e054 
[INFO ] 2025-02-28 17:55:34.014 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Pg-504ca9d2-b0df-4c61-9ae8-255b0207e054 
[INFO ] 2025-02-28 17:55:34.014 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67c180dc03f6ea45de5b5873-23463d9f-6591-4914-8978-c7e93ad536b0-67b4d8043d2c63001046bc9c] schema data cleaned 
[TRACE] 2025-02-28 17:55:34.019 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] schema data cleaned 
[TRACE] 2025-02-28 17:55:34.019 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] monitor closed 
[TRACE] 2025-02-28 17:55:34.019 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] close complete, cost 34 ms 
[TRACE] 2025-02-28 17:55:34.034 - [PG~ sybase 反向(100)][a223bc21-1554-4335-9c12-917e0183a6e9] - Node a223bc21-1554-4335-9c12-917e0183a6e9[a223bc21-1554-4335-9c12-917e0183a6e9] running status set to false 
[TRACE] 2025-02-28 17:55:34.034 - [PG~ sybase 反向(100)][a223bc21-1554-4335-9c12-917e0183a6e9] - Node a223bc21-1554-4335-9c12-917e0183a6e9[a223bc21-1554-4335-9c12-917e0183a6e9] schema data cleaned 
[TRACE] 2025-02-28 17:55:34.034 - [PG~ sybase 反向(100)][a223bc21-1554-4335-9c12-917e0183a6e9] - Node a223bc21-1554-4335-9c12-917e0183a6e9[a223bc21-1554-4335-9c12-917e0183a6e9] monitor closed 
[TRACE] 2025-02-28 17:55:34.034 - [PG~ sybase 反向(100)][a223bc21-1554-4335-9c12-917e0183a6e9] - Node a223bc21-1554-4335-9c12-917e0183a6e9[a223bc21-1554-4335-9c12-917e0183a6e9] close complete, cost 2 ms 
[TRACE] 2025-02-28 17:55:34.036 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:55:34.036 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:55:34.037 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
[TRACE] 2025-02-28 17:55:35.528 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:55:35.528 - [PG~ sybase 反向(100)][7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd] - Node 7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd[7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:55:35.528 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:55:35.528 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:55:35.528 - [PG~ sybase 反向(100)][7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd] - Node 7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd[7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:55:35.528 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:55:35.529 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 23463d9f-6591-4914-8978-c7e93ad536b0) enable batch process 
[WARN ] 2025-02-28 17:55:35.786 - [PG~ sybase 反向(100)][Pg] - Source table is empty, trying to mock data 
[TRACE] 2025-02-28 17:55:35.795 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] running status set to false 
[TRACE] 2025-02-28 17:55:35.795 - [PG~ sybase 反向(100)][Pg] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736535558 
[TRACE] 2025-02-28 17:55:35.795 - [PG~ sybase 反向(100)][Pg] - PDK connector node released: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736535558 
[TRACE] 2025-02-28 17:55:35.795 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] schema data cleaned 
[TRACE] 2025-02-28 17:55:35.795 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] monitor closed 
[TRACE] 2025-02-28 17:55:35.796 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] close complete, cost 7 ms 
[TRACE] 2025-02-28 17:55:36.081 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] running status set to false 
[INFO ] 2025-02-28 17:55:36.091 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Pg-8d931cf2-9631-4f32-b123-0e919461af84 
[INFO ] 2025-02-28 17:55:36.091 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Pg-8d931cf2-9631-4f32-b123-0e919461af84 
[INFO ] 2025-02-28 17:55:36.091 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67c180dc03f6ea45de5b5873-23463d9f-6591-4914-8978-c7e93ad536b0-67b4d8043d2c63001046bc9c] schema data cleaned 
[TRACE] 2025-02-28 17:55:36.092 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] schema data cleaned 
[TRACE] 2025-02-28 17:55:36.093 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] monitor closed 
[TRACE] 2025-02-28 17:55:36.112 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] close complete, cost 13 ms 
[TRACE] 2025-02-28 17:55:36.113 - [PG~ sybase 反向(100)][7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd] - Node 7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd[7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd] running status set to false 
[TRACE] 2025-02-28 17:55:36.113 - [PG~ sybase 反向(100)][7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd] - Node 7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd[7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd] schema data cleaned 
[TRACE] 2025-02-28 17:55:36.113 - [PG~ sybase 反向(100)][7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd] - Node 7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd[7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd] monitor closed 
[TRACE] 2025-02-28 17:55:36.113 - [PG~ sybase 反向(100)][7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd] - Node 7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd[7cd7fbb9-6f4c-43ef-a6e9-8e0cdb5069dd] close complete, cost 2 ms 
[TRACE] 2025-02-28 17:55:36.114 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:55:36.115 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:55:36.115 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
[TRACE] 2025-02-28 17:55:37.367 - [PG~ sybase 反向(100)][b4036609-0773-46a7-b445-4addf316612b] - Node b4036609-0773-46a7-b445-4addf316612b[b4036609-0773-46a7-b445-4addf316612b] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:55:37.367 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:55:37.368 - [PG~ sybase 反向(100)][b4036609-0773-46a7-b445-4addf316612b] - Node b4036609-0773-46a7-b445-4addf316612b[b4036609-0773-46a7-b445-4addf316612b] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:55:37.368 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:55:37.368 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:55:37.369 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:55:37.369 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 23463d9f-6591-4914-8978-c7e93ad536b0) enable batch process 
[WARN ] 2025-02-28 17:55:37.624 - [PG~ sybase 反向(100)][Pg] - Source table is empty, trying to mock data 
[TRACE] 2025-02-28 17:55:37.635 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] running status set to false 
[TRACE] 2025-02-28 17:55:37.635 - [PG~ sybase 反向(100)][Pg] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736537402 
[TRACE] 2025-02-28 17:55:37.635 - [PG~ sybase 反向(100)][Pg] - PDK connector node released: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736537402 
[TRACE] 2025-02-28 17:55:37.635 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] schema data cleaned 
[TRACE] 2025-02-28 17:55:37.635 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] monitor closed 
[TRACE] 2025-02-28 17:55:37.839 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] close complete, cost 9 ms 
[TRACE] 2025-02-28 17:55:37.952 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] running status set to false 
[INFO ] 2025-02-28 17:55:37.952 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Pg-7212bb33-8096-4aa6-b6f1-73c2cfb1fd4d 
[INFO ] 2025-02-28 17:55:37.952 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Pg-7212bb33-8096-4aa6-b6f1-73c2cfb1fd4d 
[INFO ] 2025-02-28 17:55:37.952 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67c180dc03f6ea45de5b5873-23463d9f-6591-4914-8978-c7e93ad536b0-67b4d8043d2c63001046bc9c] schema data cleaned 
[TRACE] 2025-02-28 17:55:37.954 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] schema data cleaned 
[TRACE] 2025-02-28 17:55:37.955 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] monitor closed 
[TRACE] 2025-02-28 17:55:37.955 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] close complete, cost 15 ms 
[TRACE] 2025-02-28 17:55:37.980 - [PG~ sybase 反向(100)][b4036609-0773-46a7-b445-4addf316612b] - Node b4036609-0773-46a7-b445-4addf316612b[b4036609-0773-46a7-b445-4addf316612b] running status set to false 
[TRACE] 2025-02-28 17:55:37.981 - [PG~ sybase 反向(100)][b4036609-0773-46a7-b445-4addf316612b] - Node b4036609-0773-46a7-b445-4addf316612b[b4036609-0773-46a7-b445-4addf316612b] schema data cleaned 
[TRACE] 2025-02-28 17:55:37.981 - [PG~ sybase 反向(100)][b4036609-0773-46a7-b445-4addf316612b] - Node b4036609-0773-46a7-b445-4addf316612b[b4036609-0773-46a7-b445-4addf316612b] monitor closed 
[TRACE] 2025-02-28 17:55:37.981 - [PG~ sybase 反向(100)][b4036609-0773-46a7-b445-4addf316612b] - Node b4036609-0773-46a7-b445-4addf316612b[b4036609-0773-46a7-b445-4addf316612b] close complete, cost 2 ms 
[TRACE] 2025-02-28 17:55:37.983 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:55:37.983 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:55:38.193 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
[TRACE] 2025-02-28 17:55:53.974 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:55:53.974 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:55:53.974 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:55:53.975 - [PG~ sybase 反向(100)][93f5f594-051a-4e92-a7d4-3200a5390e64] - Node 93f5f594-051a-4e92-a7d4-3200a5390e64[93f5f594-051a-4e92-a7d4-3200a5390e64] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:55:53.975 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:55:53.975 - [PG~ sybase 反向(100)][93f5f594-051a-4e92-a7d4-3200a5390e64] - Node 93f5f594-051a-4e92-a7d4-3200a5390e64[93f5f594-051a-4e92-a7d4-3200a5390e64] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:55:53.976 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 23463d9f-6591-4914-8978-c7e93ad536b0) enable batch process 
[WARN ] 2025-02-28 17:55:54.276 - [PG~ sybase 反向(100)][Pg] - Source table is empty, trying to mock data 
[TRACE] 2025-02-28 17:55:54.294 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] running status set to false 
[TRACE] 2025-02-28 17:55:54.295 - [PG~ sybase 反向(100)][Pg] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736553995 
[TRACE] 2025-02-28 17:55:54.295 - [PG~ sybase 反向(100)][Pg] - PDK connector node released: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736553995 
[TRACE] 2025-02-28 17:55:54.295 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] schema data cleaned 
[TRACE] 2025-02-28 17:55:54.295 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] monitor closed 
[TRACE] 2025-02-28 17:55:54.502 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] close complete, cost 19 ms 
[TRACE] 2025-02-28 17:55:54.735 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] running status set to false 
[INFO ] 2025-02-28 17:55:54.736 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Pg-9cb88d4d-2a4c-4bac-8420-cd752c5714c9 
[INFO ] 2025-02-28 17:55:54.736 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Pg-9cb88d4d-2a4c-4bac-8420-cd752c5714c9 
[INFO ] 2025-02-28 17:55:54.737 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67c180dc03f6ea45de5b5873-23463d9f-6591-4914-8978-c7e93ad536b0-67b4d8043d2c63001046bc9c] schema data cleaned 
[TRACE] 2025-02-28 17:55:54.741 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] schema data cleaned 
[TRACE] 2025-02-28 17:55:54.742 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] monitor closed 
[TRACE] 2025-02-28 17:55:54.742 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] close complete, cost 35 ms 
[TRACE] 2025-02-28 17:55:54.760 - [PG~ sybase 反向(100)][93f5f594-051a-4e92-a7d4-3200a5390e64] - Node 93f5f594-051a-4e92-a7d4-3200a5390e64[93f5f594-051a-4e92-a7d4-3200a5390e64] running status set to false 
[TRACE] 2025-02-28 17:55:54.760 - [PG~ sybase 反向(100)][93f5f594-051a-4e92-a7d4-3200a5390e64] - Node 93f5f594-051a-4e92-a7d4-3200a5390e64[93f5f594-051a-4e92-a7d4-3200a5390e64] schema data cleaned 
[TRACE] 2025-02-28 17:55:54.760 - [PG~ sybase 反向(100)][93f5f594-051a-4e92-a7d4-3200a5390e64] - Node 93f5f594-051a-4e92-a7d4-3200a5390e64[93f5f594-051a-4e92-a7d4-3200a5390e64] monitor closed 
[TRACE] 2025-02-28 17:55:54.762 - [PG~ sybase 反向(100)][93f5f594-051a-4e92-a7d4-3200a5390e64] - Node 93f5f594-051a-4e92-a7d4-3200a5390e64[93f5f594-051a-4e92-a7d4-3200a5390e64] close complete, cost 1 ms 
[TRACE] 2025-02-28 17:55:54.763 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:55:54.763 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:55:54.964 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
[TRACE] 2025-02-28 17:55:56.340 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:55:56.340 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:55:56.340 - [PG~ sybase 反向(100)][9efbe5cb-aad0-4573-ae39-3e1fe7864325] - Node 9efbe5cb-aad0-4573-ae39-3e1fe7864325[9efbe5cb-aad0-4573-ae39-3e1fe7864325] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:55:56.340 - [PG~ sybase 反向(100)][9efbe5cb-aad0-4573-ae39-3e1fe7864325] - Node 9efbe5cb-aad0-4573-ae39-3e1fe7864325[9efbe5cb-aad0-4573-ae39-3e1fe7864325] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:55:56.340 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] preload schema finished, cost 1 ms 
[TRACE] 2025-02-28 17:55:56.340 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] preload schema finished, cost 1 ms 
[TRACE] 2025-02-28 17:55:56.340 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 23463d9f-6591-4914-8978-c7e93ad536b0) enable batch process 
[WARN ] 2025-02-28 17:55:56.679 - [PG~ sybase 反向(100)][Pg] - Source table is empty, trying to mock data 
[TRACE] 2025-02-28 17:55:56.679 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] running status set to false 
[TRACE] 2025-02-28 17:55:56.696 - [PG~ sybase 反向(100)][Pg] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736556376 
[TRACE] 2025-02-28 17:55:56.696 - [PG~ sybase 反向(100)][Pg] - PDK connector node released: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736556376 
[TRACE] 2025-02-28 17:55:56.704 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] schema data cleaned 
[TRACE] 2025-02-28 17:55:56.705 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] monitor closed 
[TRACE] 2025-02-28 17:55:56.913 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] close complete, cost 27 ms 
[TRACE] 2025-02-28 17:55:57.106 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] running status set to false 
[INFO ] 2025-02-28 17:55:57.111 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Pg-941a8081-ac62-4b59-b5bd-6c8a8b6f6f8b 
[INFO ] 2025-02-28 17:55:57.115 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Pg-941a8081-ac62-4b59-b5bd-6c8a8b6f6f8b 
[INFO ] 2025-02-28 17:55:57.115 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67c180dc03f6ea45de5b5873-23463d9f-6591-4914-8978-c7e93ad536b0-67b4d8043d2c63001046bc9c] schema data cleaned 
[TRACE] 2025-02-28 17:55:57.115 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] schema data cleaned 
[TRACE] 2025-02-28 17:55:57.116 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] monitor closed 
[TRACE] 2025-02-28 17:55:57.116 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] close complete, cost 35 ms 
[TRACE] 2025-02-28 17:55:57.138 - [PG~ sybase 反向(100)][9efbe5cb-aad0-4573-ae39-3e1fe7864325] - Node 9efbe5cb-aad0-4573-ae39-3e1fe7864325[9efbe5cb-aad0-4573-ae39-3e1fe7864325] running status set to false 
[TRACE] 2025-02-28 17:55:57.138 - [PG~ sybase 反向(100)][9efbe5cb-aad0-4573-ae39-3e1fe7864325] - Node 9efbe5cb-aad0-4573-ae39-3e1fe7864325[9efbe5cb-aad0-4573-ae39-3e1fe7864325] schema data cleaned 
[TRACE] 2025-02-28 17:55:57.139 - [PG~ sybase 反向(100)][9efbe5cb-aad0-4573-ae39-3e1fe7864325] - Node 9efbe5cb-aad0-4573-ae39-3e1fe7864325[9efbe5cb-aad0-4573-ae39-3e1fe7864325] monitor closed 
[TRACE] 2025-02-28 17:55:57.139 - [PG~ sybase 反向(100)][9efbe5cb-aad0-4573-ae39-3e1fe7864325] - Node 9efbe5cb-aad0-4573-ae39-3e1fe7864325[9efbe5cb-aad0-4573-ae39-3e1fe7864325] close complete, cost 5 ms 
[TRACE] 2025-02-28 17:55:57.141 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:55:57.141 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:55:57.141 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
[TRACE] 2025-02-28 17:57:05.150 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:57:05.150 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:57:05.155 - [PG~ sybase 反向(100)][b9fa582a-f721-4dfe-b683-6484abf9de32] - Node b9fa582a-f721-4dfe-b683-6484abf9de32[b9fa582a-f721-4dfe-b683-6484abf9de32] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:57:05.155 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:57:05.155 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:57:05.155 - [PG~ sybase 反向(100)][b9fa582a-f721-4dfe-b683-6484abf9de32] - Node b9fa582a-f721-4dfe-b683-6484abf9de32[b9fa582a-f721-4dfe-b683-6484abf9de32] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:57:05.155 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 23463d9f-6591-4914-8978-c7e93ad536b0) enable batch process 
[WARN ] 2025-02-28 17:57:05.465 - [PG~ sybase 反向(100)][Pg] - Source table is empty, trying to mock data 
[TRACE] 2025-02-28 17:57:05.483 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] running status set to false 
[TRACE] 2025-02-28 17:57:05.483 - [PG~ sybase 反向(100)][Pg] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736625201 
[TRACE] 2025-02-28 17:57:05.483 - [PG~ sybase 反向(100)][Pg] - PDK connector node released: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736625201 
[TRACE] 2025-02-28 17:57:05.483 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] schema data cleaned 
[TRACE] 2025-02-28 17:57:05.483 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] monitor closed 
[TRACE] 2025-02-28 17:57:05.483 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] close complete, cost 14 ms 
[TRACE] 2025-02-28 17:57:05.802 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] running status set to false 
[INFO ] 2025-02-28 17:57:05.803 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Pg-33312e0f-dff4-4a52-9ebc-631c17d21c0b 
[INFO ] 2025-02-28 17:57:05.803 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Pg-33312e0f-dff4-4a52-9ebc-631c17d21c0b 
[INFO ] 2025-02-28 17:57:05.803 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67c180dc03f6ea45de5b5873-23463d9f-6591-4914-8978-c7e93ad536b0-67b4d8043d2c63001046bc9c] schema data cleaned 
[TRACE] 2025-02-28 17:57:05.805 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] schema data cleaned 
[TRACE] 2025-02-28 17:57:05.806 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] monitor closed 
[TRACE] 2025-02-28 17:57:05.806 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] close complete, cost 22 ms 
[TRACE] 2025-02-28 17:57:05.819 - [PG~ sybase 反向(100)][b9fa582a-f721-4dfe-b683-6484abf9de32] - Node b9fa582a-f721-4dfe-b683-6484abf9de32[b9fa582a-f721-4dfe-b683-6484abf9de32] running status set to false 
[TRACE] 2025-02-28 17:57:05.819 - [PG~ sybase 反向(100)][b9fa582a-f721-4dfe-b683-6484abf9de32] - Node b9fa582a-f721-4dfe-b683-6484abf9de32[b9fa582a-f721-4dfe-b683-6484abf9de32] schema data cleaned 
[TRACE] 2025-02-28 17:57:05.819 - [PG~ sybase 反向(100)][b9fa582a-f721-4dfe-b683-6484abf9de32] - Node b9fa582a-f721-4dfe-b683-6484abf9de32[b9fa582a-f721-4dfe-b683-6484abf9de32] monitor closed 
[TRACE] 2025-02-28 17:57:05.821 - [PG~ sybase 反向(100)][b9fa582a-f721-4dfe-b683-6484abf9de32] - Node b9fa582a-f721-4dfe-b683-6484abf9de32[b9fa582a-f721-4dfe-b683-6484abf9de32] close complete, cost 1 ms 
[TRACE] 2025-02-28 17:57:05.821 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:57:05.821 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:57:06.029 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
[TRACE] 2025-02-28 17:58:01.428 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:58:01.428 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:58:01.429 - [PG~ sybase 反向(100)][05fd694f-7887-4eeb-953b-2c02b7353cf2] - Node 05fd694f-7887-4eeb-953b-2c02b7353cf2[05fd694f-7887-4eeb-953b-2c02b7353cf2] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:58:01.429 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:58:01.429 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:58:01.429 - [PG~ sybase 反向(100)][05fd694f-7887-4eeb-953b-2c02b7353cf2] - Node 05fd694f-7887-4eeb-953b-2c02b7353cf2[05fd694f-7887-4eeb-953b-2c02b7353cf2] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:58:01.630 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 23463d9f-6591-4914-8978-c7e93ad536b0) enable batch process 
[WARN ] 2025-02-28 17:58:01.700 - [PG~ sybase 反向(100)][Pg] - Source table is empty, trying to mock data 
[TRACE] 2025-02-28 17:58:01.700 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] running status set to false 
[TRACE] 2025-02-28 17:58:01.710 - [PG~ sybase 反向(100)][Pg] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736681445 
[TRACE] 2025-02-28 17:58:01.710 - [PG~ sybase 反向(100)][Pg] - PDK connector node released: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736681445 
[TRACE] 2025-02-28 17:58:01.710 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] schema data cleaned 
[TRACE] 2025-02-28 17:58:01.710 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] monitor closed 
[TRACE] 2025-02-28 17:58:01.917 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] close complete, cost 11 ms 
[TRACE] 2025-02-28 17:58:02.110 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] running status set to false 
[INFO ] 2025-02-28 17:58:02.111 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Pg-df0f57d5-4921-4ada-b275-929f27c33809 
[INFO ] 2025-02-28 17:58:02.111 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Pg-df0f57d5-4921-4ada-b275-929f27c33809 
[INFO ] 2025-02-28 17:58:02.112 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67c180dc03f6ea45de5b5873-23463d9f-6591-4914-8978-c7e93ad536b0-67b4d8043d2c63001046bc9c] schema data cleaned 
[TRACE] 2025-02-28 17:58:02.113 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] schema data cleaned 
[TRACE] 2025-02-28 17:58:02.114 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] monitor closed 
[TRACE] 2025-02-28 17:58:02.114 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[23463d9f-6591-4914-8978-c7e93ad536b0] close complete, cost 35 ms 
[TRACE] 2025-02-28 17:58:02.134 - [PG~ sybase 反向(100)][05fd694f-7887-4eeb-953b-2c02b7353cf2] - Node 05fd694f-7887-4eeb-953b-2c02b7353cf2[05fd694f-7887-4eeb-953b-2c02b7353cf2] running status set to false 
[TRACE] 2025-02-28 17:58:02.134 - [PG~ sybase 反向(100)][05fd694f-7887-4eeb-953b-2c02b7353cf2] - Node 05fd694f-7887-4eeb-953b-2c02b7353cf2[05fd694f-7887-4eeb-953b-2c02b7353cf2] schema data cleaned 
[TRACE] 2025-02-28 17:58:02.134 - [PG~ sybase 反向(100)][05fd694f-7887-4eeb-953b-2c02b7353cf2] - Node 05fd694f-7887-4eeb-953b-2c02b7353cf2[05fd694f-7887-4eeb-953b-2c02b7353cf2] monitor closed 
[TRACE] 2025-02-28 17:58:02.134 - [PG~ sybase 反向(100)][05fd694f-7887-4eeb-953b-2c02b7353cf2] - Node 05fd694f-7887-4eeb-953b-2c02b7353cf2[05fd694f-7887-4eeb-953b-2c02b7353cf2] close complete, cost 0 ms 
[TRACE] 2025-02-28 17:58:02.136 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:58:02.136 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:58:02.136 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
[TRACE] 2025-02-28 17:58:56.875 - [PG~ sybase 反向(100)][7edc3e5c-0716-4f5b-9890-35e36de98e0e] - Node 7edc3e5c-0716-4f5b-9890-35e36de98e0e[7edc3e5c-0716-4f5b-9890-35e36de98e0e] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:58:56.875 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:58:56.875 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[91d5f22e-62c0-4521-89b3-ce6cdd278f40] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:58:56.875 - [PG~ sybase 反向(100)][7edc3e5c-0716-4f5b-9890-35e36de98e0e] - Node 7edc3e5c-0716-4f5b-9890-35e36de98e0e[7edc3e5c-0716-4f5b-9890-35e36de98e0e] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:58:56.875 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:58:56.875 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[91d5f22e-62c0-4521-89b3-ce6cdd278f40] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:58:57.076 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 91d5f22e-62c0-4521-89b3-ce6cdd278f40) enable batch process 
[WARN ] 2025-02-28 17:58:57.202 - [PG~ sybase 反向(100)][Pg] - Source table is empty, trying to mock data 
[TRACE] 2025-02-28 17:58:57.203 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] running status set to false 
[TRACE] 2025-02-28 17:58:57.220 - [PG~ sybase 反向(100)][Pg] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736736897 
[TRACE] 2025-02-28 17:58:57.220 - [PG~ sybase 反向(100)][Pg] - PDK connector node released: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736736897 
[TRACE] 2025-02-28 17:58:57.220 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] schema data cleaned 
[TRACE] 2025-02-28 17:58:57.220 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] monitor closed 
[TRACE] 2025-02-28 17:58:57.220 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] close complete, cost 20 ms 
[TRACE] 2025-02-28 17:58:58.089 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:58:58.089 - [PG~ sybase 反向(100)][361cb63c-9f86-48eb-b93a-9da5edbbb92c] - Node 361cb63c-9f86-48eb-b93a-9da5edbbb92c[361cb63c-9f86-48eb-b93a-9da5edbbb92c] start preload schema,table counts: 0 
[TRACE] 2025-02-28 17:58:58.090 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[91d5f22e-62c0-4521-89b3-ce6cdd278f40] start preload schema,table counts: 2 
[TRACE] 2025-02-28 17:58:58.090 - [PG~ sybase 反向(100)][361cb63c-9f86-48eb-b93a-9da5edbbb92c] - Node 361cb63c-9f86-48eb-b93a-9da5edbbb92c[361cb63c-9f86-48eb-b93a-9da5edbbb92c] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:58:58.090 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] preload schema finished, cost 1 ms 
[TRACE] 2025-02-28 17:58:58.090 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[91d5f22e-62c0-4521-89b3-ce6cdd278f40] preload schema finished, cost 0 ms 
[TRACE] 2025-02-28 17:58:58.095 - [PG~ sybase 反向(100)][增强JS] - Node migrate_js_processor(增强JS: 91d5f22e-62c0-4521-89b3-ce6cdd278f40) enable batch process 
[TRACE] 2025-02-28 17:58:58.166 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[91d5f22e-62c0-4521-89b3-ce6cdd278f40] running status set to false 
[TRACE] 2025-02-28 17:58:58.186 - [PG~ sybase 反向(100)][7edc3e5c-0716-4f5b-9890-35e36de98e0e] - Node 7edc3e5c-0716-4f5b-9890-35e36de98e0e[7edc3e5c-0716-4f5b-9890-35e36de98e0e] running status set to false 
[TRACE] 2025-02-28 17:58:58.186 - [PG~ sybase 反向(100)][7edc3e5c-0716-4f5b-9890-35e36de98e0e] - Node 7edc3e5c-0716-4f5b-9890-35e36de98e0e[7edc3e5c-0716-4f5b-9890-35e36de98e0e] schema data cleaned 
[TRACE] 2025-02-28 17:58:58.186 - [PG~ sybase 反向(100)][7edc3e5c-0716-4f5b-9890-35e36de98e0e] - Node 7edc3e5c-0716-4f5b-9890-35e36de98e0e[7edc3e5c-0716-4f5b-9890-35e36de98e0e] monitor closed 
[TRACE] 2025-02-28 17:58:58.186 - [PG~ sybase 反向(100)][7edc3e5c-0716-4f5b-9890-35e36de98e0e] - Node 7edc3e5c-0716-4f5b-9890-35e36de98e0e[7edc3e5c-0716-4f5b-9890-35e36de98e0e] close complete, cost 4 ms 
[INFO ] 2025-02-28 17:58:58.198 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Pg-265d6907-f0fc-45a0-be50-1fdbad74620d 
[INFO ] 2025-02-28 17:58:58.201 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Pg-265d6907-f0fc-45a0-be50-1fdbad74620d 
[INFO ] 2025-02-28 17:58:58.201 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67c180dc03f6ea45de5b5873-91d5f22e-62c0-4521-89b3-ce6cdd278f40-67b4d8043d2c63001046bc9c] schema data cleaned 
[TRACE] 2025-02-28 17:58:58.201 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[91d5f22e-62c0-4521-89b3-ce6cdd278f40] schema data cleaned 
[TRACE] 2025-02-28 17:58:58.201 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[91d5f22e-62c0-4521-89b3-ce6cdd278f40] monitor closed 
[TRACE] 2025-02-28 17:58:58.204 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[91d5f22e-62c0-4521-89b3-ce6cdd278f40] close complete, cost 54 ms 
[TRACE] 2025-02-28 17:58:58.205 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:58:58.205 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:58:58.205 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
[WARN ] 2025-02-28 17:58:58.483 - [PG~ sybase 反向(100)][Pg] - Source table is empty, trying to mock data 
[TRACE] 2025-02-28 17:58:58.498 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] running status set to false 
[TRACE] 2025-02-28 17:58:58.498 - [PG~ sybase 反向(100)][Pg] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736738199 
[TRACE] 2025-02-28 17:58:58.498 - [PG~ sybase 反向(100)][Pg] - PDK connector node released: HazelcastSampleSourcePdkDataNode_1927eca2-aaff-443b-a794-ed3faf6ea38f_1740736738199 
[TRACE] 2025-02-28 17:58:58.498 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] schema data cleaned 
[TRACE] 2025-02-28 17:58:58.498 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] monitor closed 
[TRACE] 2025-02-28 17:58:58.499 - [PG~ sybase 反向(100)][Pg] - Node Pg[1927eca2-aaff-443b-a794-ed3faf6ea38f] close complete, cost 11 ms 
[TRACE] 2025-02-28 17:58:58.884 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[91d5f22e-62c0-4521-89b3-ce6cdd278f40] running status set to false 
[TRACE] 2025-02-28 17:58:58.891 - [PG~ sybase 反向(100)][361cb63c-9f86-48eb-b93a-9da5edbbb92c] - Node 361cb63c-9f86-48eb-b93a-9da5edbbb92c[361cb63c-9f86-48eb-b93a-9da5edbbb92c] running status set to false 
[TRACE] 2025-02-28 17:58:58.891 - [PG~ sybase 反向(100)][361cb63c-9f86-48eb-b93a-9da5edbbb92c] - Node 361cb63c-9f86-48eb-b93a-9da5edbbb92c[361cb63c-9f86-48eb-b93a-9da5edbbb92c] schema data cleaned 
[TRACE] 2025-02-28 17:58:58.891 - [PG~ sybase 反向(100)][361cb63c-9f86-48eb-b93a-9da5edbbb92c] - Node 361cb63c-9f86-48eb-b93a-9da5edbbb92c[361cb63c-9f86-48eb-b93a-9da5edbbb92c] monitor closed 
[TRACE] 2025-02-28 17:58:58.891 - [PG~ sybase 反向(100)][361cb63c-9f86-48eb-b93a-9da5edbbb92c] - Node 361cb63c-9f86-48eb-b93a-9da5edbbb92c[361cb63c-9f86-48eb-b93a-9da5edbbb92c] close complete, cost 0 ms 
[INFO ] 2025-02-28 17:58:58.894 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-Pg-04980e0c-4384-47cf-9e07-702de30aeddf 
[INFO ] 2025-02-28 17:58:58.895 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-Pg-04980e0c-4384-47cf-9e07-702de30aeddf 
[INFO ] 2025-02-28 17:58:58.895 - [PG~ sybase 反向(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-67c180dc03f6ea45de5b5873-91d5f22e-62c0-4521-89b3-ce6cdd278f40-67b4d8043d2c63001046bc9c] schema data cleaned 
[TRACE] 2025-02-28 17:58:58.897 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[91d5f22e-62c0-4521-89b3-ce6cdd278f40] schema data cleaned 
[TRACE] 2025-02-28 17:58:58.897 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[91d5f22e-62c0-4521-89b3-ce6cdd278f40] monitor closed 
[TRACE] 2025-02-28 17:58:58.899 - [PG~ sybase 反向(100)][增强JS] - Node 增强JS[91d5f22e-62c0-4521-89b3-ce6cdd278f40] close complete, cost 14 ms 
[TRACE] 2025-02-28 17:58:58.899 - [PG~ sybase 反向(100)] - Closed task monitor(s)
null 
[TRACE] 2025-02-28 17:58:58.899 - [PG~ sybase 反向(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-02-28 17:58:58.900 - [PG~ sybase 反向(100)] - Stopped task aspect(s) 
