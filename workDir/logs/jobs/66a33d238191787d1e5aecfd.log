[INFO ] 2024-07-26 14:08:23.284 - [dummy 写入ck] - Task initialization... 
[INFO ] 2024-07-26 14:08:23.285 - [dummy 写入ck] - Start task milestones: 66a33d238191787d1e5aecfd(dummy 写入ck) 
[INFO ] 2024-07-26 14:08:23.494 - [dummy 写入ck] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 14:08:23.564 - [dummy 写入ck] - The engine receives dummy 写入ck task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 14:08:23.564 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] start preload schema,table counts: 1 
[INFO ] 2024-07-26 14:08:23.564 - [dummy 写入ck][dummy_test] - Node dummy_test[5b15e46b-0f84-4f63-b202-09bacb9371b8] start preload schema,table counts: 1 
[INFO ] 2024-07-26 14:08:23.564 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 14:08:23.564 - [dummy 写入ck][dummy_test] - Node dummy_test[5b15e46b-0f84-4f63-b202-09bacb9371b8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 14:08:24.383 - [dummy 写入ck][DummyTest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 14:08:24.384 - [dummy 写入ck][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-07-26 14:08:24.384 - [dummy 写入ck][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-07-26 14:08:24.384 - [dummy 写入ck][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 14:08:24.385 - [dummy 写入ck][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721974104384,"lastTimes":1721974104384,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 14:08:24.385 - [dummy 写入ck][dummy_test] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-26 14:08:24.434 - [dummy 写入ck][dummy_test] - Initial sync started 
[INFO ] 2024-07-26 14:08:24.436 - [dummy 写入ck][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-07-26 14:08:24.437 - [dummy 写入ck][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-26 14:08:24.437 - [dummy 写入ck][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-26 14:08:24.454 - [dummy 写入ck][dummy_test] - Query table 'dummy_test' counts: 10000 
[INFO ] 2024-07-26 14:08:25.649 - [dummy 写入ck][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-26 14:08:25.652 - [dummy 写入ck][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 14:08:25.652 - [dummy 写入ck][dummy_test] - Initial sync completed 
[INFO ] 2024-07-26 14:08:25.652 - [dummy 写入ck][dummy_test] - Incremental sync starting... 
[INFO ] 2024-07-26 14:08:25.652 - [dummy 写入ck][dummy_test] - Initial sync completed 
[INFO ] 2024-07-26 14:08:25.653 - [dummy 写入ck][dummy_test] - Starting stream read, table list: [dummy_test], offset: {"syncStage":null,"beginTimes":1721974104384,"lastTimes":1721974104384,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 14:08:25.654 - [dummy 写入ck][dummy_test] - Start [dummy_test] stream read 
[INFO ] 2024-07-26 14:08:25.654 - [dummy 写入ck][dummy_test] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[INFO ] 2024-07-26 14:08:47.227 - [dummy 写入ck][dummy_test] - Node dummy_test[5b15e46b-0f84-4f63-b202-09bacb9371b8] running status set to false 
[INFO ] 2024-07-26 14:08:47.235 - [dummy 写入ck][dummy_test] - Stop connector 
[INFO ] 2024-07-26 14:08:47.235 - [dummy 写入ck][dummy_test] - Incremental sync completed 
[INFO ] 2024-07-26 14:08:47.243 - [dummy 写入ck][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-5b15e46b-0f84-4f63-b202-09bacb9371b8 
[INFO ] 2024-07-26 14:08:47.243 - [dummy 写入ck][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-5b15e46b-0f84-4f63-b202-09bacb9371b8 
[INFO ] 2024-07-26 14:08:47.251 - [dummy 写入ck][dummy_test] - Node dummy_test[5b15e46b-0f84-4f63-b202-09bacb9371b8] schema data cleaned 
[INFO ] 2024-07-26 14:08:47.251 - [dummy 写入ck][dummy_test] - Node dummy_test[5b15e46b-0f84-4f63-b202-09bacb9371b8] monitor closed 
[INFO ] 2024-07-26 14:08:47.251 - [dummy 写入ck][dummy_test] - Node dummy_test[5b15e46b-0f84-4f63-b202-09bacb9371b8] close complete, cost 27 ms 
[INFO ] 2024-07-26 14:08:47.251 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] running status set to false 
[INFO ] 2024-07-26 14:08:47.268 - [dummy 写入ck][DummyTest1] - Clickhouse Optimize Table start, tables: ["DummyTest1"] 
[WARN ] 2024-07-26 14:08:47.268 - [dummy 写入ck][DummyTest1] - Clickhouse Optimize Table failed 
[INFO ] 2024-07-26 14:08:47.283 - [dummy 写入ck][DummyTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-36994896-a680-4bcb-8ca0-c27db27f72aa 
[INFO ] 2024-07-26 14:08:47.284 - [dummy 写入ck][DummyTest1] - PDK connector node released: HazelcastTargetPdkDataNode-36994896-a680-4bcb-8ca0-c27db27f72aa 
[INFO ] 2024-07-26 14:08:47.284 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] schema data cleaned 
[INFO ] 2024-07-26 14:08:47.285 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] monitor closed 
[INFO ] 2024-07-26 14:08:47.523 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] close complete, cost 37 ms 
[INFO ] 2024-07-26 14:08:49.730 - [dummy 写入ck] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 14:08:49.731 - [dummy 写入ck] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2ab35992 
[INFO ] 2024-07-26 14:08:49.735 - [dummy 写入ck] - Stop task milestones: 66a33d238191787d1e5aecfd(dummy 写入ck)  
[INFO ] 2024-07-26 14:08:49.862 - [dummy 写入ck] - Stopped task aspect(s) 
[INFO ] 2024-07-26 14:08:49.862 - [dummy 写入ck] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 14:08:49.895 - [dummy 写入ck] - Remove memory task client succeed, task: dummy 写入ck[66a33d238191787d1e5aecfd] 
[INFO ] 2024-07-26 14:08:49.901 - [dummy 写入ck] - Destroy memory task client cache succeed, task: dummy 写入ck[66a33d238191787d1e5aecfd] 
[INFO ] 2024-07-26 14:09:42.433 - [dummy 写入ck] - Task initialization... 
[INFO ] 2024-07-26 14:09:42.435 - [dummy 写入ck] - Start task milestones: 66a33d238191787d1e5aecfd(dummy 写入ck) 
[INFO ] 2024-07-26 14:09:42.512 - [dummy 写入ck] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 14:09:42.570 - [dummy 写入ck] - The engine receives dummy 写入ck task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 14:09:42.570 - [dummy 写入ck][dummy_test] - Node dummy_test[5b15e46b-0f84-4f63-b202-09bacb9371b8] start preload schema,table counts: 1 
[INFO ] 2024-07-26 14:09:42.570 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] start preload schema,table counts: 1 
[INFO ] 2024-07-26 14:09:42.570 - [dummy 写入ck][dummy_test] - Node dummy_test[5b15e46b-0f84-4f63-b202-09bacb9371b8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 14:09:42.570 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 14:09:43.256 - [dummy 写入ck][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-07-26 14:09:43.259 - [dummy 写入ck][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-07-26 14:09:43.259 - [dummy 写入ck][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 14:09:43.259 - [dummy 写入ck][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721974183254,"lastTimes":1721974183254,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 14:09:43.259 - [dummy 写入ck][dummy_test] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-26 14:09:43.299 - [dummy 写入ck][dummy_test] - Initial sync started 
[INFO ] 2024-07-26 14:09:43.299 - [dummy 写入ck][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-07-26 14:09:43.300 - [dummy 写入ck][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-26 14:09:43.300 - [dummy 写入ck][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-26 14:09:43.317 - [dummy 写入ck][dummy_test] - Query table 'dummy_test' counts: 10000 
[INFO ] 2024-07-26 14:09:43.317 - [dummy 写入ck][DummyTest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 14:09:45.481 - [dummy 写入ck][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-26 14:09:45.481 - [dummy 写入ck][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 14:09:45.483 - [dummy 写入ck][dummy_test] - Initial sync completed 
[INFO ] 2024-07-26 14:09:45.483 - [dummy 写入ck][dummy_test] - Incremental sync starting... 
[INFO ] 2024-07-26 14:09:45.483 - [dummy 写入ck][dummy_test] - Initial sync completed 
[INFO ] 2024-07-26 14:09:45.484 - [dummy 写入ck][dummy_test] - Starting stream read, table list: [dummy_test], offset: {"syncStage":null,"beginTimes":1721974183254,"lastTimes":1721974183254,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 14:09:45.485 - [dummy 写入ck][dummy_test] - Start [dummy_test] stream read 
[INFO ] 2024-07-26 14:09:45.485 - [dummy 写入ck][dummy_test] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[INFO ] 2024-07-26 14:10:14.975 - [dummy 写入ck][dummy_test] - Node dummy_test[5b15e46b-0f84-4f63-b202-09bacb9371b8] running status set to false 
[INFO ] 2024-07-26 14:10:14.975 - [dummy 写入ck][dummy_test] - Stop connector 
[INFO ] 2024-07-26 14:10:14.976 - [dummy 写入ck][dummy_test] - Compile [dummy_test] batch read 
[INFO ] 2024-07-26 14:10:14.977 - [dummy 写入ck][dummy_test] - Incremental sync completed 
[INFO ] 2024-07-26 14:10:14.982 - [dummy 写入ck][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-5b15e46b-0f84-4f63-b202-09bacb9371b8 
[INFO ] 2024-07-26 14:10:14.982 - [dummy 写入ck][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-5b15e46b-0f84-4f63-b202-09bacb9371b8 
[INFO ] 2024-07-26 14:10:14.982 - [dummy 写入ck][dummy_test] - Node dummy_test[5b15e46b-0f84-4f63-b202-09bacb9371b8] schema data cleaned 
[INFO ] 2024-07-26 14:10:14.984 - [dummy 写入ck][dummy_test] - Node dummy_test[5b15e46b-0f84-4f63-b202-09bacb9371b8] monitor closed 
[INFO ] 2024-07-26 14:10:14.985 - [dummy 写入ck][dummy_test] - Node dummy_test[5b15e46b-0f84-4f63-b202-09bacb9371b8] close complete, cost 24 ms 
[INFO ] 2024-07-26 14:10:14.985 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] running status set to false 
[INFO ] 2024-07-26 14:10:15.009 - [dummy 写入ck][DummyTest1] - Clickhouse Optimize Table start, tables: ["DummyTest1"] 
[WARN ] 2024-07-26 14:10:15.012 - [dummy 写入ck][DummyTest1] - Clickhouse Optimize Table failed 
[INFO ] 2024-07-26 14:10:15.038 - [dummy 写入ck][DummyTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-36994896-a680-4bcb-8ca0-c27db27f72aa 
[INFO ] 2024-07-26 14:10:15.038 - [dummy 写入ck][DummyTest1] - PDK connector node released: HazelcastTargetPdkDataNode-36994896-a680-4bcb-8ca0-c27db27f72aa 
[INFO ] 2024-07-26 14:10:15.040 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] schema data cleaned 
[INFO ] 2024-07-26 14:10:15.041 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] monitor closed 
[INFO ] 2024-07-26 14:10:15.041 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] close complete, cost 56 ms 
[INFO ] 2024-07-26 14:10:15.264 - [dummy 写入ck] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 14:10:15.267 - [dummy 写入ck] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6aa0722e 
[INFO ] 2024-07-26 14:10:15.267 - [dummy 写入ck] - Stop task milestones: 66a33d238191787d1e5aecfd(dummy 写入ck)  
[INFO ] 2024-07-26 14:10:15.421 - [dummy 写入ck] - Stopped task aspect(s) 
[INFO ] 2024-07-26 14:10:15.421 - [dummy 写入ck] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 14:10:15.444 - [dummy 写入ck] - Remove memory task client succeed, task: dummy 写入ck[66a33d238191787d1e5aecfd] 
[INFO ] 2024-07-26 14:10:15.445 - [dummy 写入ck] - Destroy memory task client cache succeed, task: dummy 写入ck[66a33d238191787d1e5aecfd] 
[INFO ] 2024-07-26 14:17:35.911 - [dummy 写入ck] - Task initialization... 
[INFO ] 2024-07-26 14:17:35.912 - [dummy 写入ck] - Start task milestones: 66a33d238191787d1e5aecfd(dummy 写入ck) 
[INFO ] 2024-07-26 14:17:36.041 - [dummy 写入ck] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 14:17:36.041 - [dummy 写入ck] - The engine receives dummy 写入ck task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 14:17:36.072 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] start preload schema,table counts: 1 
[INFO ] 2024-07-26 14:17:36.074 - [dummy 写入ck][dummy_test] - Node dummy_test[1679342d-7169-4cae-855a-5fb1b89d0212] start preload schema,table counts: 1 
[INFO ] 2024-07-26 14:17:36.074 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 14:17:36.074 - [dummy 写入ck][dummy_test] - Node dummy_test[1679342d-7169-4cae-855a-5fb1b89d0212] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 14:17:36.838 - [dummy 写入ck][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-07-26 14:17:36.839 - [dummy 写入ck][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-07-26 14:17:36.839 - [dummy 写入ck][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 14:17:36.840 - [dummy 写入ck][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721974656839,"lastTimes":1721974656839,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 14:17:36.840 - [dummy 写入ck][dummy_test] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-26 14:17:36.910 - [dummy 写入ck][dummy_test] - Initial sync started 
[INFO ] 2024-07-26 14:17:36.910 - [dummy 写入ck][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-07-26 14:17:36.912 - [dummy 写入ck][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-26 14:17:36.912 - [dummy 写入ck][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-26 14:17:36.946 - [dummy 写入ck][DummyTest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 14:17:36.946 - [dummy 写入ck][dummy_test] - Query table 'dummy_test' counts: 50000000 
[INFO ] 2024-07-26 14:18:08.997 - [dummy 写入ck][dummy_test] - Node dummy_test[1679342d-7169-4cae-855a-5fb1b89d0212] running status set to false 
[INFO ] 2024-07-26 14:18:09.002 - [dummy 写入ck][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-26 14:18:09.002 - [dummy 写入ck][dummy_test] - Stop connector 
[INFO ] 2024-07-26 14:18:09.003 - [dummy 写入ck][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 14:18:09.003 - [dummy 写入ck][dummy_test] - Initial sync completed 
[INFO ] 2024-07-26 14:18:09.003 - [dummy 写入ck][dummy_test] - Incremental sync starting... 
[INFO ] 2024-07-26 14:18:09.003 - [dummy 写入ck][dummy_test] - Incremental sync completed 
[INFO ] 2024-07-26 14:18:09.007 - [dummy 写入ck][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-1679342d-7169-4cae-855a-5fb1b89d0212 
[INFO ] 2024-07-26 14:18:09.007 - [dummy 写入ck][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-1679342d-7169-4cae-855a-5fb1b89d0212 
[INFO ] 2024-07-26 14:18:09.007 - [dummy 写入ck][dummy_test] - Node dummy_test[1679342d-7169-4cae-855a-5fb1b89d0212] schema data cleaned 
[INFO ] 2024-07-26 14:18:09.007 - [dummy 写入ck][dummy_test] - Node dummy_test[1679342d-7169-4cae-855a-5fb1b89d0212] monitor closed 
[INFO ] 2024-07-26 14:18:09.010 - [dummy 写入ck][dummy_test] - Node dummy_test[1679342d-7169-4cae-855a-5fb1b89d0212] close complete, cost 22 ms 
[INFO ] 2024-07-26 14:18:09.010 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] running status set to false 
[INFO ] 2024-07-26 14:18:09.212 - [dummy 写入ck][DummyTest1] - Clickhouse Optimize Table start, tables: ["DummyTest1"] 
[WARN ] 2024-07-26 14:18:09.357 - [dummy 写入ck][DummyTest1] - Clickhouse Optimize Table failed 
[INFO ] 2024-07-26 14:18:09.374 - [dummy 写入ck][DummyTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-36994896-a680-4bcb-8ca0-c27db27f72aa 
[INFO ] 2024-07-26 14:18:09.374 - [dummy 写入ck][DummyTest1] - PDK connector node released: HazelcastTargetPdkDataNode-36994896-a680-4bcb-8ca0-c27db27f72aa 
[INFO ] 2024-07-26 14:18:09.374 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] schema data cleaned 
[INFO ] 2024-07-26 14:18:09.375 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] monitor closed 
[INFO ] 2024-07-26 14:18:09.579 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] close complete, cost 365 ms 
[INFO ] 2024-07-26 14:18:11.051 - [dummy 写入ck] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 14:18:11.054 - [dummy 写入ck] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2976ee86 
[INFO ] 2024-07-26 14:18:11.055 - [dummy 写入ck] - Stop task milestones: 66a33d238191787d1e5aecfd(dummy 写入ck)  
[INFO ] 2024-07-26 14:18:11.185 - [dummy 写入ck] - Stopped task aspect(s) 
[INFO ] 2024-07-26 14:18:11.185 - [dummy 写入ck] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 14:18:11.209 - [dummy 写入ck] - Remove memory task client succeed, task: dummy 写入ck[66a33d238191787d1e5aecfd] 
[INFO ] 2024-07-26 14:18:11.211 - [dummy 写入ck] - Destroy memory task client cache succeed, task: dummy 写入ck[66a33d238191787d1e5aecfd] 
[INFO ] 2024-07-26 14:18:58.113 - [dummy 写入ck] - Task initialization... 
[INFO ] 2024-07-26 14:18:58.114 - [dummy 写入ck] - Start task milestones: 66a33d238191787d1e5aecfd(dummy 写入ck) 
[INFO ] 2024-07-26 14:18:58.244 - [dummy 写入ck] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 14:18:58.340 - [dummy 写入ck] - The engine receives dummy 写入ck task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 14:18:58.340 - [dummy 写入ck][dummy_test] - Node dummy_test[1679342d-7169-4cae-855a-5fb1b89d0212] start preload schema,table counts: 1 
[INFO ] 2024-07-26 14:18:58.340 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] start preload schema,table counts: 1 
[INFO ] 2024-07-26 14:18:58.340 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 14:18:58.341 - [dummy 写入ck][dummy_test] - Node dummy_test[1679342d-7169-4cae-855a-5fb1b89d0212] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 14:18:58.911 - [dummy 写入ck][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-07-26 14:18:58.911 - [dummy 写入ck][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-07-26 14:18:58.911 - [dummy 写入ck][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 14:18:58.918 - [dummy 写入ck][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721974738913,"lastTimes":1721974738913,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 14:18:58.918 - [dummy 写入ck][dummy_test] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-26 14:18:58.957 - [dummy 写入ck][dummy_test] - Initial sync started 
[INFO ] 2024-07-26 14:18:58.957 - [dummy 写入ck][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-07-26 14:18:58.958 - [dummy 写入ck][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-26 14:18:58.958 - [dummy 写入ck][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-26 14:18:59.163 - [dummy 写入ck][dummy_test] - Query table 'dummy_test' counts: 10000 
[INFO ] 2024-07-26 14:18:59.771 - [dummy 写入ck][DummyTest1] - Write batch size: 10000, max wait ms per batch: 500 
[INFO ] 2024-07-26 14:19:00.861 - [dummy 写入ck][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-26 14:19:00.861 - [dummy 写入ck][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 14:19:00.862 - [dummy 写入ck][dummy_test] - Initial sync completed 
[INFO ] 2024-07-26 14:19:00.862 - [dummy 写入ck][dummy_test] - Incremental sync starting... 
[INFO ] 2024-07-26 14:19:00.862 - [dummy 写入ck][dummy_test] - Initial sync completed 
[INFO ] 2024-07-26 14:19:00.863 - [dummy 写入ck][dummy_test] - Starting stream read, table list: [dummy_test], offset: {"syncStage":null,"beginTimes":1721974738913,"lastTimes":1721974738913,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 14:19:00.863 - [dummy 写入ck][dummy_test] - Start [dummy_test] stream read 
[INFO ] 2024-07-26 14:19:01.150 - [dummy 写入ck][dummy_test] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[INFO ] 2024-07-26 14:19:13.534 - [dummy 写入ck][DummyTest1] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: DummyTest1
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@aed84e8: {"after":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"05aac263-856c-47c7-8f92-c269d2e1aa33","name2":"name2","name1":"name1"},"before":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"05aac263-856c-47c7-8f92-c269d2e1aa33","name2":"name2","name1":"name1"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721974741186,"tableId":"dummy_test","time":1721974741186,"type":302}, nodeIds=[1679342d-7169-4cae-855a-5fb1b89d0212], sourceTime=1721974741186, sourceSerialNo=null} 
[ERROR] 2024-07-26 14:19:13.547 - [dummy 写入ck][DummyTest1] - java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: DummyTest1
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@aed84e8: {"after":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"05aac263-856c-47c7-8f92-c269d2e1aa33","name2":"name2","name1":"name1"},"before":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"05aac263-856c-47c7-8f92-c269d2e1aa33","name2":"name2","name1":"name1"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721974741186,"tableId":"dummy_test","time":1721974741186,"type":302}, nodeIds=[1679342d-7169-4cae-855a-5fb1b89d0212], sourceTime=1721974741186, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: DummyTest1
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@aed84e8: {"after":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"05aac263-856c-47c7-8f92-c269d2e1aa33","name2":"name2","name1":"name1"},"before":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"05aac263-856c-47c7-8f92-c269d2e1aa33","name2":"name2","name1":"name1"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721974741186,"tableId":"dummy_test","time":1721974741186,"type":302}, nodeIds=[1679342d-7169-4cae-855a-5fb1b89d0212], sourceTime=1721974741186, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.lang.Throwable: Code: 420, e.displayText() = DB::Exception: Cannot UPDATE key column `id` (version ******** (official build))

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeUpdate(ClickHouseStatementImpl.java:215)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: DummyTest1
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:633)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:602)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:583)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:532)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:495)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:501)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:547)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: DummyTest1
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:799)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:499)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:499)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:688)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:625)
	... 12 more
Caused by: Execute PDK method: TARGET_WRITE_RECORD, tableName: DummyTest1
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:859)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:805)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: java.lang.RuntimeException: ru.yandex.clickhouse.except.ClickHouseException: ClickHouse exception, code: 420, host: **************, port: 18123; Code: 420, e.displayText() = DB::Exception: Cannot UPDATE key column `id` (version ******** (official build))

	at io.tapdata.common.exception.AbstractExceptionCollector.collectViolateUnique(AbstractExceptionCollector.java:53)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:124)
	at io.tapdata.connector.clickhouse.ClickhouseConnector.writeRecord(ClickhouseConnector.java:314)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:853)
	... 26 more
Caused by: ru.yandex.clickhouse.except.ClickHouseException: ClickHouse exception, code: 420, host: **************, port: 18123; Code: 420, e.displayText() = DB::Exception: Cannot UPDATE key column `id` (version ******** (official build))

	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:58)
	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeUpdate(ClickHouseStatementImpl.java:215)
	at ru.yandex.clickhouse.ClickHousePreparedStatementImpl.executeUpdate(ClickHousePreparedStatementImpl.java:130)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at io.tapdata.connector.clickhouse.dml.ClickhouseWriteRecorder.addUpdateBatch(ClickhouseWriteRecorder.java:53)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:98)
	... 28 more
Caused by: java.lang.Throwable: Code: 420, e.displayText() = DB::Exception: Cannot UPDATE key column `id` (version ******** (official build))

	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	... 37 more

[INFO ] 2024-07-26 14:19:13.547 - [dummy 写入ck][DummyTest1] - Job suspend in error handle 
[INFO ] 2024-07-26 14:19:14.557 - [dummy 写入ck][dummy_test] - Node dummy_test[1679342d-7169-4cae-855a-5fb1b89d0212] running status set to false 
[INFO ] 2024-07-26 14:19:14.557 - [dummy 写入ck][dummy_test] - Stop connector 
[INFO ] 2024-07-26 14:19:14.560 - [dummy 写入ck][dummy_test] - Compile [dummy_test] batch read 
[INFO ] 2024-07-26 14:19:14.560 - [dummy 写入ck][dummy_test] - Incremental sync completed 
[INFO ] 2024-07-26 14:19:14.567 - [dummy 写入ck][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-1679342d-7169-4cae-855a-5fb1b89d0212 
[INFO ] 2024-07-26 14:19:14.567 - [dummy 写入ck][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-1679342d-7169-4cae-855a-5fb1b89d0212 
[INFO ] 2024-07-26 14:19:14.568 - [dummy 写入ck][dummy_test] - Node dummy_test[1679342d-7169-4cae-855a-5fb1b89d0212] schema data cleaned 
[INFO ] 2024-07-26 14:19:14.568 - [dummy 写入ck][dummy_test] - Node dummy_test[1679342d-7169-4cae-855a-5fb1b89d0212] monitor closed 
[INFO ] 2024-07-26 14:19:14.569 - [dummy 写入ck][dummy_test] - Node dummy_test[1679342d-7169-4cae-855a-5fb1b89d0212] close complete, cost 21 ms 
[INFO ] 2024-07-26 14:19:14.569 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] running status set to false 
[INFO ] 2024-07-26 14:19:14.770 - [dummy 写入ck][DummyTest1] - Clickhouse Optimize Table start, tables: ["DummyTest1"] 
[WARN ] 2024-07-26 14:19:14.962 - [dummy 写入ck][DummyTest1] - Clickhouse Optimize Table failed 
[INFO ] 2024-07-26 14:19:14.999 - [dummy 写入ck][DummyTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-36994896-a680-4bcb-8ca0-c27db27f72aa 
[INFO ] 2024-07-26 14:19:14.999 - [dummy 写入ck][DummyTest1] - PDK connector node released: HazelcastTargetPdkDataNode-36994896-a680-4bcb-8ca0-c27db27f72aa 
[INFO ] 2024-07-26 14:19:14.999 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] schema data cleaned 
[INFO ] 2024-07-26 14:19:14.999 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] monitor closed 
[INFO ] 2024-07-26 14:19:15.200 - [dummy 写入ck][DummyTest1] - Node DummyTest1[36994896-a680-4bcb-8ca0-c27db27f72aa] close complete, cost 430 ms 
[INFO ] 2024-07-26 14:19:16.745 - [dummy 写入ck] - Task [dummy 写入ck] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-26 14:19:16.755 - [dummy 写入ck] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 14:19:16.756 - [dummy 写入ck] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6dee4dca 
[INFO ] 2024-07-26 14:19:16.757 - [dummy 写入ck] - Stop task milestones: 66a33d238191787d1e5aecfd(dummy 写入ck)  
[INFO ] 2024-07-26 14:19:16.898 - [dummy 写入ck] - Stopped task aspect(s) 
[INFO ] 2024-07-26 14:19:16.898 - [dummy 写入ck] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 14:19:16.933 - [dummy 写入ck] - Remove memory task client succeed, task: dummy 写入ck[66a33d238191787d1e5aecfd] 
[INFO ] 2024-07-26 14:19:16.934 - [dummy 写入ck] - Destroy memory task client cache succeed, task: dummy 写入ck[66a33d238191787d1e5aecfd] 
