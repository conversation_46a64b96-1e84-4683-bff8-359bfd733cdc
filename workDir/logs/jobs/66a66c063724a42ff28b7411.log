[INFO ] 2024-07-29 00:04:23.719 - [Heartbeat-<PERSON><PERSON>] - Start task milestones: 66a66c063724a42ff28b7411(Heartbeat-Mon<PERSON>) 
[INFO ] 2024-07-29 00:04:24.902 - [Heartbeat-<PERSON><PERSON>] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-29 00:04:24.904 - [Heartbeat-<PERSON><PERSON>] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-29 00:04:24.907 - [Heartbeat-Mongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5324cc89 
[INFO ] 2024-07-29 00:04:25.020 - [Heartbeat-<PERSON>go] - Stop task milestones: 66a66c063724a42ff28b7411(Heartbeat-Mongo)  
[INFO ] 2024-07-29 00:04:25.033 - [Heartbeat-Mongo] - Stopped task aspect(s) 
[INFO ] 2024-07-29 00:04:25.034 - [Heartbeat-Mon<PERSON>] - Snapshot order controller have been removed 
[ERROR] 2024-07-29 00:04:25.042 - [Heartbeat-<PERSON><PERSON>] - Node [id 4508bfd4-c1ee-4392-ba36-15a109d7c3ef, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 4508bfd4-c1ee-4392-ba36-15a109d7c3ef, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

