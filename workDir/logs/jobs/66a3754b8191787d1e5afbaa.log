[INFO ] 2024-07-26 18:07:38.826 - [任务 39] - Task initialization... 
[INFO ] 2024-07-26 18:07:38.827 - [任务 39] - Start task milestones: 66a3754b8191787d1e5afbaa(任务 39) 
[INFO ] 2024-07-26 18:07:39.046 - [任务 39] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 18:07:39.071 - [任务 39] - The engine receives 任务 39 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 18:07:39.116 - [任务 39][CUSTOMER] - Node CUSTOMER[ac12948e-2011-4ffa-a85b-d9a9b70deda7] start preload schema,table counts: 1 
[INFO ] 2024-07-26 18:07:39.117 - [任务 39][Test1] - Node Test1[28547ec9-f9eb-4a45-9041-e270b041c898] start preload schema,table counts: 1 
[INFO ] 2024-07-26 18:07:39.117 - [任务 39][CUSTOMER] - Node CUSTOMER[ac12948e-2011-4ffa-a85b-d9a9b70deda7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 18:07:39.117 - [任务 39][Test1] - Node Test1[28547ec9-f9eb-4a45-9041-e270b041c898] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 18:07:39.777 - [任务 39][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-26 18:07:39.777 - [任务 39][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-26 18:07:39.777 - [任务 39][CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 18:07:39.815 - [任务 39][Test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 18:07:39.983 - [任务 39][CUSTOMER] - batch offset found: {},stream offset found: {"cdcOffset":1721988459,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 18:07:39.983 - [任务 39][CUSTOMER] - Initial sync started 
[INFO ] 2024-07-26 18:07:39.985 - [任务 39][CUSTOMER] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-26 18:07:39.985 - [任务 39][CUSTOMER] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-26 18:07:40.009 - [任务 39][CUSTOMER] - Query table 'CUSTOMER' counts: 676 
[INFO ] 2024-07-26 18:07:40.012 - [任务 39][CUSTOMER] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 18:07:40.013 - [任务 39][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 18:07:40.013 - [任务 39][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-26 18:07:40.013 - [任务 39][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 18:07:40.039 - [任务 39][CUSTOMER] - Starting stream read, table list: [CUSTOMER, _tapdata_heartbeat_table], offset: {"cdcOffset":1721988459,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 18:07:40.039 - [任务 39][CUSTOMER] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 18:07:46.913 - [任务 39][CUSTOMER] - Node CUSTOMER[ac12948e-2011-4ffa-a85b-d9a9b70deda7] running status set to false 
[INFO ] 2024-07-26 18:07:46.914 - [任务 39][CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-ac12948e-2011-4ffa-a85b-d9a9b70deda7 
[INFO ] 2024-07-26 18:07:46.914 - [任务 39][CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-ac12948e-2011-4ffa-a85b-d9a9b70deda7 
[INFO ] 2024-07-26 18:07:46.914 - [任务 39][CUSTOMER] - Node CUSTOMER[ac12948e-2011-4ffa-a85b-d9a9b70deda7] schema data cleaned 
[INFO ] 2024-07-26 18:07:46.914 - [任务 39][CUSTOMER] - Node CUSTOMER[ac12948e-2011-4ffa-a85b-d9a9b70deda7] monitor closed 
[INFO ] 2024-07-26 18:07:46.915 - [任务 39][CUSTOMER] - Node CUSTOMER[ac12948e-2011-4ffa-a85b-d9a9b70deda7] close complete, cost 13 ms 
[INFO ] 2024-07-26 18:07:46.915 - [任务 39][Test1] - Node Test1[28547ec9-f9eb-4a45-9041-e270b041c898] running status set to false 
[INFO ] 2024-07-26 18:07:46.939 - [任务 39][Test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28547ec9-f9eb-4a45-9041-e270b041c898 
[INFO ] 2024-07-26 18:07:46.939 - [任务 39][Test1] - PDK connector node released: HazelcastTargetPdkDataNode-28547ec9-f9eb-4a45-9041-e270b041c898 
[INFO ] 2024-07-26 18:07:46.940 - [任务 39][Test1] - Node Test1[28547ec9-f9eb-4a45-9041-e270b041c898] schema data cleaned 
[INFO ] 2024-07-26 18:07:46.940 - [任务 39][Test1] - Node Test1[28547ec9-f9eb-4a45-9041-e270b041c898] monitor closed 
[INFO ] 2024-07-26 18:07:47.145 - [任务 39][Test1] - Node Test1[28547ec9-f9eb-4a45-9041-e270b041c898] close complete, cost 25 ms 
[INFO ] 2024-07-26 18:07:47.350 - [任务 39][CUSTOMER] - Incremental sync completed 
[INFO ] 2024-07-26 18:07:49.026 - [任务 39] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:07:49.027 - [任务 39] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@32ce34eb 
[INFO ] 2024-07-26 18:07:49.155 - [任务 39] - Stop task milestones: 66a3754b8191787d1e5afbaa(任务 39)  
[INFO ] 2024-07-26 18:07:49.155 - [任务 39] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:07:49.156 - [任务 39] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 18:07:49.179 - [任务 39] - Remove memory task client succeed, task: 任务 39[66a3754b8191787d1e5afbaa] 
[INFO ] 2024-07-26 18:07:49.182 - [任务 39] - Destroy memory task client cache succeed, task: 任务 39[66a3754b8191787d1e5afbaa] 
[INFO ] 2024-07-26 18:08:23.147 - [任务 39] - Task initialization... 
[INFO ] 2024-07-26 18:08:23.147 - [任务 39] - Start task milestones: 66a3754b8191787d1e5afbaa(任务 39) 
[INFO ] 2024-07-26 18:08:23.267 - [任务 39] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 18:08:23.390 - [任务 39] - The engine receives 任务 39 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 18:08:23.390 - [任务 39][CUSTOMER] - Node CUSTOMER[ac12948e-2011-4ffa-a85b-d9a9b70deda7] start preload schema,table counts: 1 
[INFO ] 2024-07-26 18:08:23.390 - [任务 39][Test1] - Node Test1[28547ec9-f9eb-4a45-9041-e270b041c898] start preload schema,table counts: 1 
[INFO ] 2024-07-26 18:08:23.390 - [任务 39][Test1] - Node Test1[28547ec9-f9eb-4a45-9041-e270b041c898] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 18:08:23.390 - [任务 39][CUSTOMER] - Node CUSTOMER[ac12948e-2011-4ffa-a85b-d9a9b70deda7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 18:08:24.417 - [任务 39][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-26 18:08:24.419 - [任务 39][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-26 18:08:24.419 - [任务 39][CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 18:08:24.599 - [任务 39][CUSTOMER] - batch offset found: {},stream offset found: {"cdcOffset":1721988504,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 18:08:24.625 - [任务 39][CUSTOMER] - Initial sync started 
[INFO ] 2024-07-26 18:08:24.626 - [任务 39][CUSTOMER] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-26 18:08:24.626 - [任务 39][CUSTOMER] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-26 18:08:24.683 - [任务 39][CUSTOMER] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 18:08:24.738 - [任务 39][CUSTOMER] - Query table 'CUSTOMER' counts: 676 
[INFO ] 2024-07-26 18:08:25.407 - [任务 39][Test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 18:08:25.444 - [任务 39][Test1] - Table "test.Test1" exists, skip auto create table 
[INFO ] 2024-07-26 18:08:25.449 - [任务 39][Test1] - The table Test1 has already exist. 
[INFO ] 2024-07-26 18:08:25.541 - [任务 39][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 18:08:25.541 - [任务 39][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-26 18:08:25.541 - [任务 39][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 18:08:25.665 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-26 18:08:25.666 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Step 1 - Check connection TestMongoHeartBeat - Copy enable share cdc: true 
[INFO ] 2024-07-26 18:08:25.666 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 39 enable share cdc: true 
[INFO ] 2024-07-26 18:08:25.713 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自TestMongoHeartBeat - Copy的共享挖掘任务 
[INFO ] 2024-07-26 18:08:25.713 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav392?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-26 18:08:25.741 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66a37596feea88265d2b65df, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66a374968191787d1e5afa66_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_-484709193, shareCdcTaskId=66a375968191787d1e5afc98, connectionId=66a374968191787d1e5afa66) 
[INFO ] 2024-07-26 18:08:25.743 - [任务 39][CUSTOMER] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TestMongoHeartBeat - Copy的共享挖掘任务_CUSTOMER_任务 39', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_-484709193', head seq: 0, tail seq: 0 
[INFO ] 2024-07-26 18:08:25.872 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66a37596feea88265d2b65d9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66a374968191787d1e5afa66__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_1364349939, shareCdcTaskId=66a375968191787d1e5afc98, connectionId=66a374968191787d1e5afa66) 
[INFO ] 2024-07-26 18:08:25.873 - [任务 39][CUSTOMER] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TestMongoHeartBeat - Copy的共享挖掘任务__tapdata_heartbeat_table_任务 39', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_1364349939', head seq: 0, tail seq: 1 
[INFO ] 2024-07-26 18:08:25.878 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-26 18:08:25.878 - [任务 39][CUSTOMER] - Init share cdc reader completed 
[INFO ] 2024-07-26 18:08:25.878 - [任务 39][CUSTOMER] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-26 18:08:25.878 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-26 18:08:25.878 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Read table count: 2, partition size: 1, read thread number: 2 
[INFO ] 2024-07-26 18:08:25.890 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66a37596feea88265d2b65df, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66a374968191787d1e5afa66_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_-484709193, shareCdcTaskId=66a375968191787d1e5afc98, connectionId=66a374968191787d1e5afa66) 
[INFO ] 2024-07-26 18:08:25.894 - [任务 39][CUSTOMER] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自TestMongoHeartBeat - Copy的共享挖掘任务_CUSTOMER_任务 39', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_-484709193', head seq: 0, tail seq: 0 
[INFO ] 2024-07-26 18:08:25.894 - [任务 39][CUSTOMER] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自TestMongoHeartBeat - Copy的共享挖掘任务_CUSTOMER_任务 39, external storage name: ExternalStorage_SHARE_CDC_-484709193 
[INFO ] 2024-07-26 18:08:25.901 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CUSTOMER] 
[INFO ] 2024-07-26 18:08:25.901 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66a37596feea88265d2b65d9, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66a374968191787d1e5afa66__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_1364349939, shareCdcTaskId=66a375968191787d1e5afc98, connectionId=66a374968191787d1e5afa66) 
[INFO ] 2024-07-26 18:08:25.902 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Find sequence in construct(CUSTOMER) by timestamp(2024-07-26T10:08:24.417Z): 1 
[INFO ] 2024-07-26 18:08:25.902 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Starting read 'CUSTOMER' log, sequence: 1 
[INFO ] 2024-07-26 18:08:25.906 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Find by CUSTOMER filter: {sequence=1} 
[INFO ] 2024-07-26 18:08:25.906 - [任务 39][CUSTOMER] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自TestMongoHeartBeat - Copy的共享挖掘任务__tapdata_heartbeat_table_任务 39', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_1364349939', head seq: 0, tail seq: 1 
[INFO ] 2024-07-26 18:08:25.906 - [任务 39][CUSTOMER] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自TestMongoHeartBeat - Copy的共享挖掘任务__tapdata_heartbeat_table_任务 39, external storage name: ExternalStorage_SHARE_CDC_1364349939 
[INFO ] 2024-07-26 18:08:25.907 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [_tapdata_heartbeat_table] 
[INFO ] 2024-07-26 18:08:25.910 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Find sequence in construct(_tapdata_heartbeat_table) by timestamp(2024-07-26T10:08:24.417Z): 2 
[INFO ] 2024-07-26 18:08:25.911 - [任务 39][CUSTOMER] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 18:08:25.911 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Starting read '_tapdata_heartbeat_table' log, sequence: 2 
[INFO ] 2024-07-26 18:08:25.911 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Find by _tapdata_heartbeat_table filter: {sequence=2} 
[INFO ] 2024-07-26 18:08:27.536 - [任务 39][CUSTOMER] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=_tapdata_heartbeat_table, timestamp=1721988505000, date=Fri Jul 26 18:08:25 CST 2024, before=Document{{}}, after=Document{{_id=6695b95266ab5ede8a9dcd3e, id=6695b8966d76494ed53f3874, ts=Fri Jul 26 18:08:24 CST 2024}}, op=u, offsetString=gAFkABVvcmcuYnNvbi5Cc29uRG9jdW1lbnQBFAAFX2RhdGEBAgATb3JnLmJzb24uQnNvblN0cmlu
ZwAAAdR7ImFycmF5IjpmYWxzZSwiYmluYXJ5IjpmYWxzZSwiYm9vbGVhbiI6ZmFsc2UsImJzb25U
eXBlIjoiU1RSSU5HIiwiZEJQb2ludGVyIjpmYWxzZSwiZGF0ZVRpbWUiOmZhbHNlLCJkZWNpbWFs
MTI4IjpmYWxzZSwiZG9jdW1lbnQiOmZhbHNlLCJkb3VibGUiOmZhbHNlLCJpbnQzMiI6ZmFsc2Us
ImludDY0IjpmYWxzZSwiamF2YVNjcmlwdCI6ZmFsc2UsImphdmFTY3JpcHRXaXRoU2NvcGUiOmZh
bHNlLCJudWxsIjpmYWxzZSwibnVtYmVyIjpmYWxzZSwib2JqZWN0SWQiOmZhbHNlLCJyZWd1bGFy
RXhwcmVzc2lvbiI6ZmFsc2UsInN0cmluZyI6dHJ1ZSwic3ltYm9sIjpmYWxzZSwidGltZXN0YW1w
IjpmYWxzZSwidmFsdWUiOiI4MjY2QTM3NTk5MDAwMDAwM0MyQjAyMkMwMTAwMjk2RTVBMTAwNDEx
NDM0QkVCRDBCMzRDMUNCOTBEQTM2QjQ5MEYxQ0U1NDY2NDVGNjk2NDAwNjQ2Njk1Qjk1MjY2QUI1
RURFOEE5RENEM0UwMDA0In2o
, type=DATA, connectionId=66a374968191787d1e5afa66, isReplaceEvent=false, _ts=1721988506}} 
[INFO ] 2024-07-26 18:10:14.852 - [任务 39][CUSTOMER] - Node CUSTOMER[ac12948e-2011-4ffa-a85b-d9a9b70deda7] running status set to false 
[INFO ] 2024-07-26 18:10:14.870 - [任务 39][CUSTOMER] - Incremental sync completed 
[INFO ] 2024-07-26 18:10:14.870 - [任务 39][CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-ac12948e-2011-4ffa-a85b-d9a9b70deda7 
[INFO ] 2024-07-26 18:10:14.870 - [任务 39][CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-ac12948e-2011-4ffa-a85b-d9a9b70deda7 
[INFO ] 2024-07-26 18:10:14.870 - [任务 39][CUSTOMER] - Node CUSTOMER[ac12948e-2011-4ffa-a85b-d9a9b70deda7] schema data cleaned 
[INFO ] 2024-07-26 18:10:14.871 - [任务 39][CUSTOMER] - Node CUSTOMER[ac12948e-2011-4ffa-a85b-d9a9b70deda7] monitor closed 
[INFO ] 2024-07-26 18:10:14.872 - [任务 39][CUSTOMER] - Node CUSTOMER[ac12948e-2011-4ffa-a85b-d9a9b70deda7] close complete, cost 49 ms 
[INFO ] 2024-07-26 18:10:14.872 - [任务 39][Test1] - Node Test1[28547ec9-f9eb-4a45-9041-e270b041c898] running status set to false 
[INFO ] 2024-07-26 18:10:14.889 - [任务 39][Test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28547ec9-f9eb-4a45-9041-e270b041c898 
[INFO ] 2024-07-26 18:10:14.890 - [任务 39][Test1] - PDK connector node released: HazelcastTargetPdkDataNode-28547ec9-f9eb-4a45-9041-e270b041c898 
[INFO ] 2024-07-26 18:10:14.890 - [任务 39][Test1] - Node Test1[28547ec9-f9eb-4a45-9041-e270b041c898] schema data cleaned 
[INFO ] 2024-07-26 18:10:14.890 - [任务 39][Test1] - Node Test1[28547ec9-f9eb-4a45-9041-e270b041c898] monitor closed 
[INFO ] 2024-07-26 18:10:14.890 - [任务 39][Test1] - Node Test1[28547ec9-f9eb-4a45-9041-e270b041c898] close complete, cost 18 ms 
[INFO ] 2024-07-26 18:10:15.814 - [任务 39] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:10:15.814 - [任务 39] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@64118e75 
[INFO ] 2024-07-26 18:10:15.821 - [任务 39] - Stop task milestones: 66a3754b8191787d1e5afbaa(任务 39)  
[INFO ] 2024-07-26 18:10:15.941 - [任务 39] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:10:15.941 - [任务 39] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 18:10:15.962 - [任务 39] - Remove memory task client succeed, task: 任务 39[66a3754b8191787d1e5afbaa] 
[INFO ] 2024-07-26 18:10:15.966 - [任务 39] - Destroy memory task client cache succeed, task: 任务 39[66a3754b8191787d1e5afbaa] 
