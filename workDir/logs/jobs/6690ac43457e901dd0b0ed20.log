[INFO ] 2024-07-12 12:09:07.488 - [任务 58] - Task initialization... 
[INFO ] 2024-07-12 12:09:07.506 - [任务 58] - Start task milestones: 6690ac43457e901dd0b0ed20(任务 58) 
[INFO ] 2024-07-12 12:09:08.505 - [任务 58] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-12 12:09:08.506 - [任务 58] - The engine receives 任务 58 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-12 12:09:12.603 - [任务 58][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[2204cc47-efba-43f9-9f39-45f44d05e174] start preload schema,table counts: 1 
[INFO ] 2024-07-12 12:09:12.603 - [任务 58][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[2204cc47-efba-43f9-9f39-45f44d05e174] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 12:09:12.614 - [任务 58][Mongo555] - Node Mongo555[0c3d751a-c515-4ca0-962b-70784c4d0506] start preload schema,table counts: 1 
[INFO ] 2024-07-12 12:09:12.615 - [任务 58][Mongo555] - Node Mongo555[0c3d751a-c515-4ca0-962b-70784c4d0506] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 12:09:14.038 - [任务 58][Mongo555] - Node(Mongo555) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-12 12:09:14.042 - [任务 58][Mongo555] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-12 12:09:14.269 - [任务 58][SourceMysqlTestHeartBeat] - Source node "SourceMysqlTestHeartBeat" read batch size: 100 
[INFO ] 2024-07-12 12:09:14.270 - [任务 58][SourceMysqlTestHeartBeat] - Source node "SourceMysqlTestHeartBeat" event queue capacity: 200 
[INFO ] 2024-07-12 12:09:14.280 - [任务 58][SourceMysqlTestHeartBeat] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-12 12:09:14.296 - [任务 58][SourceMysqlTestHeartBeat] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":94143183,"gtidSet":""} 
[INFO ] 2024-07-12 12:09:14.371 - [任务 58][SourceMysqlTestHeartBeat] - Initial sync started 
[INFO ] 2024-07-12 12:09:14.389 - [任务 58][SourceMysqlTestHeartBeat] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-12 12:09:14.396 - [任务 58][SourceMysqlTestHeartBeat] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-12 12:09:14.484 - [任务 58][SourceMysqlTestHeartBeat] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-12 12:09:14.749 - [任务 58][SourceMysqlTestHeartBeat] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-12 12:09:14.750 - [任务 58][SourceMysqlTestHeartBeat] - Initial sync completed 
[INFO ] 2024-07-12 12:09:14.751 - [任务 58][SourceMysqlTestHeartBeat] - Incremental sync starting... 
[INFO ] 2024-07-12 12:09:14.757 - [任务 58][SourceMysqlTestHeartBeat] - Initial sync completed 
[INFO ] 2024-07-12 12:09:14.860 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-12 12:09:14.875 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceMysqlTestHeartBeat enable share cdc: true 
[INFO ] 2024-07-12 12:09:14.877 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 58 enable share cdc: true 
[INFO ] 2024-07-12 12:09:14.923 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceMysqlTestHeartBeat的共享挖掘任务 
[INFO ] 2024-07-12 12:09:14.925 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-12 12:09:15.058 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690ac6266ab5ede8a624f21, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66909af97c91bf6e9824898b_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_933891356, shareCdcTaskId=6690ac62457e901dd0b0ed5e, connectionId=66909af97c91bf6e9824898b) 
[INFO ] 2024-07-12 12:09:15.060 - [任务 58][SourceMysqlTestHeartBeat] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMysqlTestHeartBeat的共享挖掘任务_POLICY_任务 58', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_933891356', head seq: 0, tail seq: 0 
[INFO ] 2024-07-12 12:09:15.122 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-12 12:09:15.124 - [任务 58][SourceMysqlTestHeartBeat] - Init share cdc reader completed 
[INFO ] 2024-07-12 12:09:15.124 - [任务 58][SourceMysqlTestHeartBeat] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-12 12:09:15.124 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-12 12:09:15.124 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-12 12:09:15.165 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690ac6266ab5ede8a624f21, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66909af97c91bf6e9824898b_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_933891356, shareCdcTaskId=6690ac62457e901dd0b0ed5e, connectionId=66909af97c91bf6e9824898b) 
[INFO ] 2024-07-12 12:09:15.165 - [任务 58][SourceMysqlTestHeartBeat] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMysqlTestHeartBeat的共享挖掘任务_POLICY_任务 58', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_933891356', head seq: 0, tail seq: 0 
[INFO ] 2024-07-12 12:09:15.165 - [任务 58][SourceMysqlTestHeartBeat] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMysqlTestHeartBeat的共享挖掘任务_POLICY_任务 58, external storage name: ExternalStorage_SHARE_CDC_933891356 
[INFO ] 2024-07-12 12:09:15.171 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-12 12:09:15.182 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-12T04:09:14.275Z): 1 
[INFO ] 2024-07-12 12:09:15.182 - [任务 58][SourceMysqlTestHeartBeat] - Connector Mysql incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-12 12:09:15.189 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 1 
[INFO ] 2024-07-12 12:09:15.189 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=1} 
[INFO ] 2024-07-12 12:09:47.751 - [任务 58][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[2204cc47-efba-43f9-9f39-45f44d05e174] running status set to false 
[INFO ] 2024-07-12 12:09:47.769 - [任务 58][SourceMysqlTestHeartBeat] - Incremental sync completed 
[INFO ] 2024-07-12 12:09:47.769 - [任务 58][SourceMysqlTestHeartBeat] - PDK connector node stopped: HazelcastSourcePdkDataNode-2204cc47-efba-43f9-9f39-45f44d05e174 
[INFO ] 2024-07-12 12:09:47.771 - [任务 58][SourceMysqlTestHeartBeat] - PDK connector node released: HazelcastSourcePdkDataNode-2204cc47-efba-43f9-9f39-45f44d05e174 
[INFO ] 2024-07-12 12:09:47.772 - [任务 58][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[2204cc47-efba-43f9-9f39-45f44d05e174] schema data cleaned 
[INFO ] 2024-07-12 12:09:47.777 - [任务 58][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[2204cc47-efba-43f9-9f39-45f44d05e174] monitor closed 
[INFO ] 2024-07-12 12:09:47.778 - [任务 58][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[2204cc47-efba-43f9-9f39-45f44d05e174] close complete, cost 33 ms 
[INFO ] 2024-07-12 12:09:47.800 - [任务 58][Mongo555] - Node Mongo555[0c3d751a-c515-4ca0-962b-70784c4d0506] running status set to false 
[INFO ] 2024-07-12 12:09:47.801 - [任务 58][Mongo555] - PDK connector node stopped: HazelcastTargetPdkDataNode-0c3d751a-c515-4ca0-962b-70784c4d0506 
[INFO ] 2024-07-12 12:09:47.802 - [任务 58][Mongo555] - PDK connector node released: HazelcastTargetPdkDataNode-0c3d751a-c515-4ca0-962b-70784c4d0506 
[INFO ] 2024-07-12 12:09:47.803 - [任务 58][Mongo555] - Node Mongo555[0c3d751a-c515-4ca0-962b-70784c4d0506] schema data cleaned 
[INFO ] 2024-07-12 12:09:47.803 - [任务 58][Mongo555] - Node Mongo555[0c3d751a-c515-4ca0-962b-70784c4d0506] monitor closed 
[INFO ] 2024-07-12 12:09:47.803 - [任务 58][Mongo555] - Node Mongo555[0c3d751a-c515-4ca0-962b-70784c4d0506] close complete, cost 25 ms 
[INFO ] 2024-07-12 12:09:52.243 - [任务 58] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-12 12:09:52.244 - [任务 58] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2d642f77 
[INFO ] 2024-07-12 12:09:52.248 - [任务 58] - Stop task milestones: 6690ac43457e901dd0b0ed20(任务 58)  
[INFO ] 2024-07-12 12:09:52.372 - [任务 58] - Stopped task aspect(s) 
[INFO ] 2024-07-12 12:09:52.373 - [任务 58] - Snapshot order controller have been removed 
[INFO ] 2024-07-12 12:09:52.411 - [任务 58] - Remove memory task client succeed, task: 任务 58[6690ac43457e901dd0b0ed20] 
[INFO ] 2024-07-12 12:09:52.412 - [任务 58] - Destroy memory task client cache succeed, task: 任务 58[6690ac43457e901dd0b0ed20] 
[INFO ] 2024-07-12 12:09:55.051 - [任务 58] - Task initialization... 
[INFO ] 2024-07-12 12:09:55.051 - [任务 58] - Start task milestones: 6690ac43457e901dd0b0ed20(任务 58) 
[INFO ] 2024-07-12 12:09:55.507 - [任务 58] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-12 12:09:55.507 - [任务 58] - The engine receives 任务 58 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-12 12:09:55.563 - [任务 58][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[2204cc47-efba-43f9-9f39-45f44d05e174] start preload schema,table counts: 1 
[INFO ] 2024-07-12 12:09:55.566 - [任务 58][Mongo555] - Node Mongo555[0c3d751a-c515-4ca0-962b-70784c4d0506] start preload schema,table counts: 1 
[INFO ] 2024-07-12 12:09:55.567 - [任务 58][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[2204cc47-efba-43f9-9f39-45f44d05e174] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 12:09:55.575 - [任务 58][Mongo555] - Node Mongo555[0c3d751a-c515-4ca0-962b-70784c4d0506] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 12:09:55.853 - [任务 58][Mongo555] - Node(Mongo555) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-12 12:09:55.854 - [任务 58][Mongo555] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-12 12:09:55.956 - [任务 58][SourceMysqlTestHeartBeat] - Source node "SourceMysqlTestHeartBeat" read batch size: 100 
[INFO ] 2024-07-12 12:09:55.956 - [任务 58][SourceMysqlTestHeartBeat] - Source node "SourceMysqlTestHeartBeat" event queue capacity: 200 
[INFO ] 2024-07-12 12:09:55.957 - [任务 58][SourceMysqlTestHeartBeat] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-12 12:09:55.969 - [任务 58][SourceMysqlTestHeartBeat] - batch offset found: {"POLICY":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"filename":"binlog.000032","position":94143183,"gtidSet":""} 
[INFO ] 2024-07-12 12:09:56.039 - [任务 58][SourceMysqlTestHeartBeat] - Incremental sync starting... 
[INFO ] 2024-07-12 12:09:56.055 - [任务 58][SourceMysqlTestHeartBeat] - Initial sync completed 
[INFO ] 2024-07-12 12:09:56.133 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-12 12:09:56.138 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceMysqlTestHeartBeat enable share cdc: true 
[INFO ] 2024-07-12 12:09:56.225 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 58 enable share cdc: true 
[INFO ] 2024-07-12 12:09:56.249 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceMysqlTestHeartBeat的共享挖掘任务 
[INFO ] 2024-07-12 12:09:56.254 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-12 12:09:56.266 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690ac6266ab5ede8a624f21, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66909af97c91bf6e9824898b_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_933891356, shareCdcTaskId=6690ac62457e901dd0b0ed5e, connectionId=66909af97c91bf6e9824898b) 
[INFO ] 2024-07-12 12:09:56.267 - [任务 58][SourceMysqlTestHeartBeat] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMysqlTestHeartBeat的共享挖掘任务_POLICY_任务 58', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_933891356', head seq: 0, tail seq: 0 
[INFO ] 2024-07-12 12:09:56.465 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690ac9266ab5ede8a6260cc, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66909af97c91bf6e9824898b__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_2072557188, shareCdcTaskId=6690ac62457e901dd0b0ed5e, connectionId=66909af97c91bf6e9824898b) 
[INFO ] 2024-07-12 12:09:56.466 - [任务 58][SourceMysqlTestHeartBeat] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMysqlTestHeartBeat的共享挖掘任务__tapdata_heartbeat_table_任务 58', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_2072557188', head seq: 0, tail seq: -1 
[INFO ] 2024-07-12 12:10:24.574 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-12 12:10:24.574 - [任务 58][SourceMysqlTestHeartBeat] - Init share cdc reader completed 
[INFO ] 2024-07-12 12:10:24.574 - [任务 58][SourceMysqlTestHeartBeat] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-12 12:10:24.575 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-12 12:10:24.576 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Read table count: 2, partition size: 1, read thread number: 2 
[INFO ] 2024-07-12 12:10:24.596 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690ac6266ab5ede8a624f21, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66909af97c91bf6e9824898b_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_933891356, shareCdcTaskId=6690ac62457e901dd0b0ed5e, connectionId=66909af97c91bf6e9824898b) 
[INFO ] 2024-07-12 12:10:24.596 - [任务 58][SourceMysqlTestHeartBeat] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMysqlTestHeartBeat的共享挖掘任务_POLICY_任务 58', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_933891356', head seq: 0, tail seq: 0 
[INFO ] 2024-07-12 12:10:24.599 - [任务 58][SourceMysqlTestHeartBeat] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMysqlTestHeartBeat的共享挖掘任务_POLICY_任务 58, external storage name: ExternalStorage_SHARE_CDC_933891356 
[INFO ] 2024-07-12 12:10:24.599 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-12 12:10:24.607 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-12T04:09:14.275Z): 1 
[INFO ] 2024-07-12 12:10:24.608 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690ac9266ab5ede8a6260cc, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66909af97c91bf6e9824898b__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_2072557188, shareCdcTaskId=6690ac62457e901dd0b0ed5e, connectionId=66909af97c91bf6e9824898b) 
[INFO ] 2024-07-12 12:10:24.612 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 1 
[INFO ] 2024-07-12 12:10:24.614 - [任务 58][SourceMysqlTestHeartBeat] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMysqlTestHeartBeat的共享挖掘任务__tapdata_heartbeat_table_任务 58', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_2072557188', head seq: 0, tail seq: 28 
[INFO ] 2024-07-12 12:10:24.614 - [任务 58][SourceMysqlTestHeartBeat] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMysqlTestHeartBeat的共享挖掘任务__tapdata_heartbeat_table_任务 58, external storage name: ExternalStorage_SHARE_CDC_2072557188 
[INFO ] 2024-07-12 12:10:24.615 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [_tapdata_heartbeat_table] 
[INFO ] 2024-07-12 12:10:24.624 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=1} 
[INFO ] 2024-07-12 12:10:24.624 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Find sequence in construct(_tapdata_heartbeat_table) by timestamp(2024-07-12T04:09:14.275Z): 0 
[INFO ] 2024-07-12 12:10:24.625 - [任务 58][SourceMysqlTestHeartBeat] - Connector Mysql incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-12 12:10:24.625 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Starting read '_tapdata_heartbeat_table' log, sequence: 0 
[INFO ] 2024-07-12 12:10:24.628 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Find by _tapdata_heartbeat_table filter: {sequence=0} 
[INFO ] 2024-07-12 12:10:24.629 - [任务 58][SourceMysqlTestHeartBeat] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=_tapdata_heartbeat_table, timestamp=1720757397000, date=Fri Jul 12 12:09:57 CST 2024, before=Document{{id=66909af97c91bf6e9824898b, ts=Fri Jul 12 11:23:16 CST 2024}}, after=Document{{id=66909af97c91bf6e9824898b, ts=Fri Jul 12 12:09:56 CST 2024}}, op=u, offsetString=gAEUAoxfdGFwX3ppcF8fwosIwIDAgMCAwIDAgMCAwIAtUMOZbsKrMBB9R8OiX1hKwqU8wpolYMKK
wp3Chjhgw7sNTBvCs8KYwqTClyTCgMK/w77DkmUeZsKkwqPCmTnDiwVEw77Cv8KDBXzCilcQw6Jb
wr16FsKjfsOPaC4zwoXCn8K1w7IGw6bDpk/DkXrDtw/CisKXOsOwwoZ6w4xvTcOSPwgdwrQoF8OS
JMOpw4BKw7vDnTTCuMOiwprDk8K8wr3Cri7CrcKQwoPDpQUCw5DDj8OAwo89WcKXw4VGwpPCokrD
rR12w5pNwqzDtMOGw4zDhsKWUMODwoPDqzsCwoA/w7/Dri3AgHbDiDTDvjbDrSYewqbDjMKywqUI
w68vIjnCgjRGKw/Ciw4TZGXDpMKowrHCjsOuOMKObMK8w5otUmnCn8KRRsKiDnoHw6J3TBXCkm3D
nkwDw7jDuy9GwrnDjMOGw4LCqsOKw7lxworDt8K6worCtsKvw6DCnDhTEsOuVcKYwoMZBMO5BcK8
w5XDsU7DssKIw4sfw6d9DwLCml/ChSp0FcOvJsKew4jDmTTDnjfCjcK/wrV3woPCnwnCv8Kbw5/A
gMKwfMKsw6nDpiJ1OMKFw63CoU07wqTCjjbDk2fCjQPDi0JaLBnDiSUmw6LDjsK6wrTDpcKBw6XC
mgYjcD3CkH5mw453wpJRw7tJwo/DoETCp8K2ScOwwqdwwooOdsOXBWs4Y312wpE+w5tZwpDCqsKq
XMKGDW/DmcKWw5/CpnfDjDowwqMQaMOTQC3CnMOgw6g/w4UKXw/DpMK4YALDncKNb8ODw7AgRj7C
iGF3w6MBfEXDlH4xwo3Dv8OxwrPCocKeAgLAgMCA
, type=DATA, connectionId=66909af97c91bf6e9824898b, isReplaceEvent=false, _ts=1720757422}} 
[INFO ] 2024-07-12 15:18:57.950 - [任务 58][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[2204cc47-efba-43f9-9f39-45f44d05e174] running status set to false 
[INFO ] 2024-07-12 15:18:57.984 - [任务 58][SourceMysqlTestHeartBeat] - Incremental sync completed 
[INFO ] 2024-07-12 15:18:57.984 - [任务 58][SourceMysqlTestHeartBeat] - PDK connector node stopped: HazelcastSourcePdkDataNode-2204cc47-efba-43f9-9f39-45f44d05e174 
[INFO ] 2024-07-12 15:18:57.985 - [任务 58][SourceMysqlTestHeartBeat] - PDK connector node released: HazelcastSourcePdkDataNode-2204cc47-efba-43f9-9f39-45f44d05e174 
[INFO ] 2024-07-12 15:18:57.985 - [任务 58][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[2204cc47-efba-43f9-9f39-45f44d05e174] schema data cleaned 
[INFO ] 2024-07-12 15:18:57.988 - [任务 58][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[2204cc47-efba-43f9-9f39-45f44d05e174] monitor closed 
[INFO ] 2024-07-12 15:18:57.988 - [任务 58][SourceMysqlTestHeartBeat] - Node SourceMysqlTestHeartBeat[2204cc47-efba-43f9-9f39-45f44d05e174] close complete, cost 38 ms 
[INFO ] 2024-07-12 15:18:58.015 - [任务 58][Mongo555] - Node Mongo555[0c3d751a-c515-4ca0-962b-70784c4d0506] running status set to false 
[INFO ] 2024-07-12 15:18:58.016 - [任务 58][Mongo555] - PDK connector node stopped: HazelcastTargetPdkDataNode-0c3d751a-c515-4ca0-962b-70784c4d0506 
[INFO ] 2024-07-12 15:18:58.016 - [任务 58][Mongo555] - PDK connector node released: HazelcastTargetPdkDataNode-0c3d751a-c515-4ca0-962b-70784c4d0506 
[INFO ] 2024-07-12 15:18:58.018 - [任务 58][Mongo555] - Node Mongo555[0c3d751a-c515-4ca0-962b-70784c4d0506] schema data cleaned 
[INFO ] 2024-07-12 15:18:58.020 - [任务 58][Mongo555] - Node Mongo555[0c3d751a-c515-4ca0-962b-70784c4d0506] monitor closed 
[INFO ] 2024-07-12 15:18:58.023 - [任务 58][Mongo555] - Node Mongo555[0c3d751a-c515-4ca0-962b-70784c4d0506] close complete, cost 31 ms 
[INFO ] 2024-07-12 15:18:59.528 - [任务 58] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-12 15:18:59.529 - [任务 58] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@78815d7d 
[INFO ] 2024-07-12 15:18:59.676 - [任务 58] - Stop task milestones: 6690ac43457e901dd0b0ed20(任务 58)  
[INFO ] 2024-07-12 15:18:59.676 - [任务 58] - Stopped task aspect(s) 
[INFO ] 2024-07-12 15:18:59.677 - [任务 58] - Snapshot order controller have been removed 
[INFO ] 2024-07-12 15:18:59.703 - [任务 58] - Remove memory task client succeed, task: 任务 58[6690ac43457e901dd0b0ed20] 
[INFO ] 2024-07-12 15:18:59.708 - [任务 58] - Destroy memory task client cache succeed, task: 任务 58[6690ac43457e901dd0b0ed20] 
