[INFO ] 2024-05-27 15:16:08.695 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:08.695 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:08.695 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:08.695 - [任务 17(100)][53be07d8-5b3e-4706-91d9-ceb6c3e0a44e] - Node 53be07d8-5b3e-4706-91d9-ceb6c3e0a44e[53be07d8-5b3e-4706-91d9-ceb6c3e0a44e] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:16:08.695 - [任务 17(100)][53be07d8-5b3e-4706-91d9-ceb6c3e0a44e] - Node 53be07d8-5b3e-4706-91d9-ceb6c3e0a44e[53be07d8-5b3e-4706-91d9-ceb6c3e0a44e] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:08.695 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:08.695 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 3 ms 
[INFO ] 2024-05-27 15:16:08.696 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:08.862 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:16:08.862 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:16:08.865 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:16:08.865 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:08.865 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:16:08.865 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:08.865 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 6 ms 
[INFO ] 2024-05-27 15:16:08.867 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:16:08.881 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:16:08.881 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:08.881 - [任务 17(100)][5aa2fb59-3433-4e26-9f1e-e599769e6831] - Node 5aa2fb59-3433-4e26-9f1e-e599769e6831[5aa2fb59-3433-4e26-9f1e-e599769e6831] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:16:08.881 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:08.882 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:08.882 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 29 ms 
[INFO ] 2024-05-27 15:16:08.882 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:08.882 - [任务 17(100)][5aa2fb59-3433-4e26-9f1e-e599769e6831] - Node 5aa2fb59-3433-4e26-9f1e-e599769e6831[5aa2fb59-3433-4e26-9f1e-e599769e6831] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:08.882 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:08.882 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:08.925 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:16:08.925 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-949da6d2-973a-4fd5-ac40-aadc78e20638 
[INFO ] 2024-05-27 15:16:08.926 - [任务 17(100)][53be07d8-5b3e-4706-91d9-ceb6c3e0a44e] - Node 53be07d8-5b3e-4706-91d9-ceb6c3e0a44e[53be07d8-5b3e-4706-91d9-ceb6c3e0a44e] running status set to false 
[INFO ] 2024-05-27 15:16:08.926 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-949da6d2-973a-4fd5-ac40-aadc78e20638 
[INFO ] 2024-05-27 15:16:08.926 - [任务 17(100)][53be07d8-5b3e-4706-91d9-ceb6c3e0a44e] - Node 53be07d8-5b3e-4706-91d9-ceb6c3e0a44e[53be07d8-5b3e-4706-91d9-ceb6c3e0a44e] schema data cleaned 
[INFO ] 2024-05-27 15:16:08.926 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:16:08.926 - [任务 17(100)][53be07d8-5b3e-4706-91d9-ceb6c3e0a44e] - Node 53be07d8-5b3e-4706-91d9-ceb6c3e0a44e[53be07d8-5b3e-4706-91d9-ceb6c3e0a44e] monitor closed 
[INFO ] 2024-05-27 15:16:08.926 - [任务 17(100)][53be07d8-5b3e-4706-91d9-ceb6c3e0a44e] - Node 53be07d8-5b3e-4706-91d9-ceb6c3e0a44e[53be07d8-5b3e-4706-91d9-ceb6c3e0a44e] close complete, cost 3 ms 
[INFO ] 2024-05-27 15:16:08.930 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:16:08.930 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:16:08.930 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 20 ms 
[INFO ] 2024-05-27 15:16:09.047 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-53be07d8-5b3e-4706-91d9-ceb6c3e0a44e complete, cost 788ms 
[INFO ] 2024-05-27 15:16:09.048 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:16:09.048 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:16:09.048 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:16:09.048 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:16:09.049 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 2 ms 
[INFO ] 2024-05-27 15:16:09.050 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:09.054 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:09.055 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:16:09.055 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:16:09.055 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 8 ms 
[INFO ] 2024-05-27 15:16:09.158 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:16:09.160 - [任务 17(100)][5aa2fb59-3433-4e26-9f1e-e599769e6831] - Node 5aa2fb59-3433-4e26-9f1e-e599769e6831[5aa2fb59-3433-4e26-9f1e-e599769e6831] running status set to false 
[INFO ] 2024-05-27 15:16:09.160 - [任务 17(100)][5aa2fb59-3433-4e26-9f1e-e599769e6831] - Node 5aa2fb59-3433-4e26-9f1e-e599769e6831[5aa2fb59-3433-4e26-9f1e-e599769e6831] schema data cleaned 
[INFO ] 2024-05-27 15:16:09.160 - [任务 17(100)][5aa2fb59-3433-4e26-9f1e-e599769e6831] - Node 5aa2fb59-3433-4e26-9f1e-e599769e6831[5aa2fb59-3433-4e26-9f1e-e599769e6831] monitor closed 
[INFO ] 2024-05-27 15:16:09.160 - [任务 17(100)][5aa2fb59-3433-4e26-9f1e-e599769e6831] - Node 5aa2fb59-3433-4e26-9f1e-e599769e6831[5aa2fb59-3433-4e26-9f1e-e599769e6831] close complete, cost 13 ms 
[INFO ] 2024-05-27 15:16:09.166 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-88b596d1-fe56-4194-a124-77a235b40c4d 
[INFO ] 2024-05-27 15:16:09.167 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-88b596d1-fe56-4194-a124-77a235b40c4d 
[INFO ] 2024-05-27 15:16:09.167 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:16:09.168 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:16:09.168 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:16:09.168 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 25 ms 
[INFO ] 2024-05-27 15:16:09.374 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-5aa2fb59-3433-4e26-9f1e-e599769e6831 complete, cost 374ms 
[INFO ] 2024-05-27 15:16:12.382 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:12.382 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:12.383 - [任务 17(100)][1f7218a3-1952-4ebb-8986-bd9326d76643] - Node 1f7218a3-1952-4ebb-8986-bd9326d76643[1f7218a3-1952-4ebb-8986-bd9326d76643] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:16:12.383 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:12.383 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:12.384 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:12.384 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:12.384 - [任务 17(100)][1f7218a3-1952-4ebb-8986-bd9326d76643] - Node 1f7218a3-1952-4ebb-8986-bd9326d76643[1f7218a3-1952-4ebb-8986-bd9326d76643] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:12.526 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:16:12.529 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:12.529 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:12.529 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:16:12.529 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:16:12.530 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 4 ms 
[INFO ] 2024-05-27 15:16:12.530 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:16:12.530 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:16:12.531 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:16:12.531 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:16:12.589 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:16:12.589 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-123fb248-690c-4b3b-b0c8-0b5fa2f1517d 
[INFO ] 2024-05-27 15:16:12.589 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-123fb248-690c-4b3b-b0c8-0b5fa2f1517d 
[INFO ] 2024-05-27 15:16:12.589 - [任务 17(100)][1f7218a3-1952-4ebb-8986-bd9326d76643] - Node 1f7218a3-1952-4ebb-8986-bd9326d76643[1f7218a3-1952-4ebb-8986-bd9326d76643] running status set to false 
[INFO ] 2024-05-27 15:16:12.590 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:16:12.590 - [任务 17(100)][1f7218a3-1952-4ebb-8986-bd9326d76643] - Node 1f7218a3-1952-4ebb-8986-bd9326d76643[1f7218a3-1952-4ebb-8986-bd9326d76643] schema data cleaned 
[INFO ] 2024-05-27 15:16:12.590 - [任务 17(100)][1f7218a3-1952-4ebb-8986-bd9326d76643] - Node 1f7218a3-1952-4ebb-8986-bd9326d76643[1f7218a3-1952-4ebb-8986-bd9326d76643] monitor closed 
[INFO ] 2024-05-27 15:16:12.591 - [任务 17(100)][1f7218a3-1952-4ebb-8986-bd9326d76643] - Node 1f7218a3-1952-4ebb-8986-bd9326d76643[1f7218a3-1952-4ebb-8986-bd9326d76643] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:16:12.591 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:16:12.591 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:16:12.591 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 5 ms 
[INFO ] 2024-05-27 15:16:12.796 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-1f7218a3-1952-4ebb-8986-bd9326d76643 complete, cost 291ms 
[INFO ] 2024-05-27 15:16:23.147 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:23.148 - [任务 17(100)][0896ddd8-b85b-4ae2-bcae-da69b76f6713] - Node 0896ddd8-b85b-4ae2-bcae-da69b76f6713[0896ddd8-b85b-4ae2-bcae-da69b76f6713] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:16:23.148 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:23.148 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:23.149 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 1 ms 
[INFO ] 2024-05-27 15:16:23.149 - [任务 17(100)][0896ddd8-b85b-4ae2-bcae-da69b76f6713] - Node 0896ddd8-b85b-4ae2-bcae-da69b76f6713[0896ddd8-b85b-4ae2-bcae-da69b76f6713] preload schema finished, cost 1 ms 
[INFO ] 2024-05-27 15:16:23.149 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 1 ms 
[INFO ] 2024-05-27 15:16:23.238 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 3 ms 
[INFO ] 2024-05-27 15:16:23.238 - [任务 17(100)][d76a0e23-e2ce-413c-a94b-bebfa814ca02] - Node d76a0e23-e2ce-413c-a94b-bebfa814ca02[d76a0e23-e2ce-413c-a94b-bebfa814ca02] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:16:23.238 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:23.238 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:23.238 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:23.238 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:23.238 - [任务 17(100)][d76a0e23-e2ce-413c-a94b-bebfa814ca02] - Node d76a0e23-e2ce-413c-a94b-bebfa814ca02[d76a0e23-e2ce-413c-a94b-bebfa814ca02] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:23.238 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:23.238 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:23.314 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:16:23.314 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:16:23.314 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:16:23.314 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:16:23.315 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 1 ms 
[INFO ] 2024-05-27 15:16:23.318 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:23.318 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:23.318 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:16:23.319 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:16:23.319 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 8 ms 
[INFO ] 2024-05-27 15:16:23.370 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:16:23.370 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-9aa78ae7-9282-4e85-a1c4-47b84ab2d4a6 
[INFO ] 2024-05-27 15:16:23.370 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-9aa78ae7-9282-4e85-a1c4-47b84ab2d4a6 
[INFO ] 2024-05-27 15:16:23.370 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:16:23.371 - [任务 17(100)][0896ddd8-b85b-4ae2-bcae-da69b76f6713] - Node 0896ddd8-b85b-4ae2-bcae-da69b76f6713[0896ddd8-b85b-4ae2-bcae-da69b76f6713] running status set to false 
[INFO ] 2024-05-27 15:16:23.372 - [任务 17(100)][0896ddd8-b85b-4ae2-bcae-da69b76f6713] - Node 0896ddd8-b85b-4ae2-bcae-da69b76f6713[0896ddd8-b85b-4ae2-bcae-da69b76f6713] schema data cleaned 
[INFO ] 2024-05-27 15:16:23.372 - [任务 17(100)][0896ddd8-b85b-4ae2-bcae-da69b76f6713] - Node 0896ddd8-b85b-4ae2-bcae-da69b76f6713[0896ddd8-b85b-4ae2-bcae-da69b76f6713] monitor closed 
[INFO ] 2024-05-27 15:16:23.372 - [任务 17(100)][0896ddd8-b85b-4ae2-bcae-da69b76f6713] - Node 0896ddd8-b85b-4ae2-bcae-da69b76f6713[0896ddd8-b85b-4ae2-bcae-da69b76f6713] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:16:23.372 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:16:23.372 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:16:23.372 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 5 ms 
[INFO ] 2024-05-27 15:16:23.422 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-0896ddd8-b85b-4ae2-bcae-da69b76f6713 complete, cost 489ms 
[INFO ] 2024-05-27 15:16:23.422 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:16:23.423 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:23.423 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:23.423 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:16:23.423 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:16:23.424 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 2 ms 
[INFO ] 2024-05-27 15:16:23.424 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:16:23.425 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:16:23.425 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:16:23.425 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:16:23.447 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:16:23.449 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-01896c2a-a109-462b-bced-ca9c49ec0878 
[INFO ] 2024-05-27 15:16:23.450 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-01896c2a-a109-462b-bced-ca9c49ec0878 
[INFO ] 2024-05-27 15:16:23.450 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:16:23.450 - [任务 17(100)][d76a0e23-e2ce-413c-a94b-bebfa814ca02] - Node d76a0e23-e2ce-413c-a94b-bebfa814ca02[d76a0e23-e2ce-413c-a94b-bebfa814ca02] running status set to false 
[INFO ] 2024-05-27 15:16:23.457 - [任务 17(100)][d76a0e23-e2ce-413c-a94b-bebfa814ca02] - Node d76a0e23-e2ce-413c-a94b-bebfa814ca02[d76a0e23-e2ce-413c-a94b-bebfa814ca02] schema data cleaned 
[INFO ] 2024-05-27 15:16:23.457 - [任务 17(100)][d76a0e23-e2ce-413c-a94b-bebfa814ca02] - Node d76a0e23-e2ce-413c-a94b-bebfa814ca02[d76a0e23-e2ce-413c-a94b-bebfa814ca02] monitor closed 
[INFO ] 2024-05-27 15:16:23.457 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:16:23.457 - [任务 17(100)][d76a0e23-e2ce-413c-a94b-bebfa814ca02] - Node d76a0e23-e2ce-413c-a94b-bebfa814ca02[d76a0e23-e2ce-413c-a94b-bebfa814ca02] close complete, cost 6 ms 
[INFO ] 2024-05-27 15:16:23.457 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:16:23.457 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 11 ms 
[INFO ] 2024-05-27 15:16:23.665 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-d76a0e23-e2ce-413c-a94b-bebfa814ca02 complete, cost 300ms 
[INFO ] 2024-05-27 15:16:26.789 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:26.789 - [任务 17(100)][a8e62b9d-8198-4a99-9921-6514e91bbba9] - Node a8e62b9d-8198-4a99-9921-6514e91bbba9[a8e62b9d-8198-4a99-9921-6514e91bbba9] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:16:26.789 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:26.789 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:26.789 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:26.789 - [任务 17(100)][a8e62b9d-8198-4a99-9921-6514e91bbba9] - Node a8e62b9d-8198-4a99-9921-6514e91bbba9[a8e62b9d-8198-4a99-9921-6514e91bbba9] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:26.789 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:26.789 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:26.936 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:16:26.936 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:16:26.938 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:16:26.938 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:26.938 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:16:26.938 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:26.938 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:16:26.938 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:16:26.938 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 2 ms 
[INFO ] 2024-05-27 15:16:26.938 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 7 ms 
[INFO ] 2024-05-27 15:16:26.989 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:16:26.989 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-e08a615a-4b01-4abe-995f-1841746de1ed 
[INFO ] 2024-05-27 15:16:26.989 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-e08a615a-4b01-4abe-995f-1841746de1ed 
[INFO ] 2024-05-27 15:16:26.989 - [任务 17(100)][a8e62b9d-8198-4a99-9921-6514e91bbba9] - Node a8e62b9d-8198-4a99-9921-6514e91bbba9[a8e62b9d-8198-4a99-9921-6514e91bbba9] running status set to false 
[INFO ] 2024-05-27 15:16:26.990 - [任务 17(100)][a8e62b9d-8198-4a99-9921-6514e91bbba9] - Node a8e62b9d-8198-4a99-9921-6514e91bbba9[a8e62b9d-8198-4a99-9921-6514e91bbba9] schema data cleaned 
[INFO ] 2024-05-27 15:16:26.990 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:16:26.990 - [任务 17(100)][a8e62b9d-8198-4a99-9921-6514e91bbba9] - Node a8e62b9d-8198-4a99-9921-6514e91bbba9[a8e62b9d-8198-4a99-9921-6514e91bbba9] monitor closed 
[INFO ] 2024-05-27 15:16:26.990 - [任务 17(100)][a8e62b9d-8198-4a99-9921-6514e91bbba9] - Node a8e62b9d-8198-4a99-9921-6514e91bbba9[a8e62b9d-8198-4a99-9921-6514e91bbba9] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:16:26.991 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:16:26.991 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:16:26.991 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 5 ms 
[INFO ] 2024-05-27 15:16:27.195 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-a8e62b9d-8198-4a99-9921-6514e91bbba9 complete, cost 304ms 
[INFO ] 2024-05-27 15:16:36.002 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:36.003 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:36.003 - [任务 17(100)][d4d22959-a7a7-4d54-93b9-c4be5c947c87] - Node d4d22959-a7a7-4d54-93b9-c4be5c947c87[d4d22959-a7a7-4d54-93b9-c4be5c947c87] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:16:36.003 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:36.003 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:36.003 - [任务 17(100)][d4d22959-a7a7-4d54-93b9-c4be5c947c87] - Node d4d22959-a7a7-4d54-93b9-c4be5c947c87[d4d22959-a7a7-4d54-93b9-c4be5c947c87] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:36.003 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:36.115 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:36.115 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:16:36.116 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:16:36.116 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:16:36.116 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:36.116 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:16:36.116 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:36.116 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:16:36.116 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:16:36.116 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:16:36.117 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 3 ms 
[INFO ] 2024-05-27 15:16:36.202 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:16:36.205 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-a61b952d-37fe-4d76-9ffc-660c6fd366d2 
[INFO ] 2024-05-27 15:16:36.205 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-a61b952d-37fe-4d76-9ffc-660c6fd366d2 
[INFO ] 2024-05-27 15:16:36.210 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:16:36.210 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:16:36.210 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:16:36.256 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 9 ms 
[INFO ] 2024-05-27 15:16:36.256 - [任务 17(100)][d4d22959-a7a7-4d54-93b9-c4be5c947c87] - Node d4d22959-a7a7-4d54-93b9-c4be5c947c87[d4d22959-a7a7-4d54-93b9-c4be5c947c87] running status set to false 
[INFO ] 2024-05-27 15:16:36.256 - [任务 17(100)][d4d22959-a7a7-4d54-93b9-c4be5c947c87] - Node d4d22959-a7a7-4d54-93b9-c4be5c947c87[d4d22959-a7a7-4d54-93b9-c4be5c947c87] schema data cleaned 
[INFO ] 2024-05-27 15:16:36.256 - [任务 17(100)][d4d22959-a7a7-4d54-93b9-c4be5c947c87] - Node d4d22959-a7a7-4d54-93b9-c4be5c947c87[d4d22959-a7a7-4d54-93b9-c4be5c947c87] monitor closed 
[INFO ] 2024-05-27 15:16:36.256 - [任务 17(100)][d4d22959-a7a7-4d54-93b9-c4be5c947c87] - Node d4d22959-a7a7-4d54-93b9-c4be5c947c87[d4d22959-a7a7-4d54-93b9-c4be5c947c87] close complete, cost 4 ms 
[INFO ] 2024-05-27 15:16:36.261 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-d4d22959-a7a7-4d54-93b9-c4be5c947c87 complete, cost 319ms 
[INFO ] 2024-05-27 15:16:44.516 - [任务 17(100)][543cd633-b56c-4de8-8b47-41074b0afdf7] - Node 543cd633-b56c-4de8-8b47-41074b0afdf7[543cd633-b56c-4de8-8b47-41074b0afdf7] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:16:44.516 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:44.517 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:44.518 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:44.518 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 1 ms 
[INFO ] 2024-05-27 15:16:44.518 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 1 ms 
[INFO ] 2024-05-27 15:16:44.518 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 1 ms 
[INFO ] 2024-05-27 15:16:44.518 - [任务 17(100)][543cd633-b56c-4de8-8b47-41074b0afdf7] - Node 543cd633-b56c-4de8-8b47-41074b0afdf7[543cd633-b56c-4de8-8b47-41074b0afdf7] preload schema finished, cost 3 ms 
[INFO ] 2024-05-27 15:16:44.663 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:16:44.666 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:44.666 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:44.667 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:16:44.667 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:16:44.668 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:16:44.669 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 5 ms 
[INFO ] 2024-05-27 15:16:44.669 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:16:44.669 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:16:44.669 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:16:44.711 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:16:44.711 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-815c01dd-641c-411a-9554-797bf085e2bd 
[INFO ] 2024-05-27 15:16:44.711 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-815c01dd-641c-411a-9554-797bf085e2bd 
[INFO ] 2024-05-27 15:16:44.711 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:16:44.713 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:16:44.713 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:16:44.728 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 5 ms 
[INFO ] 2024-05-27 15:16:44.728 - [任务 17(100)][543cd633-b56c-4de8-8b47-41074b0afdf7] - Node 543cd633-b56c-4de8-8b47-41074b0afdf7[543cd633-b56c-4de8-8b47-41074b0afdf7] running status set to false 
[INFO ] 2024-05-27 15:16:44.728 - [任务 17(100)][543cd633-b56c-4de8-8b47-41074b0afdf7] - Node 543cd633-b56c-4de8-8b47-41074b0afdf7[543cd633-b56c-4de8-8b47-41074b0afdf7] schema data cleaned 
[INFO ] 2024-05-27 15:16:44.728 - [任务 17(100)][543cd633-b56c-4de8-8b47-41074b0afdf7] - Node 543cd633-b56c-4de8-8b47-41074b0afdf7[543cd633-b56c-4de8-8b47-41074b0afdf7] monitor closed 
[INFO ] 2024-05-27 15:16:44.934 - [任务 17(100)][543cd633-b56c-4de8-8b47-41074b0afdf7] - Node 543cd633-b56c-4de8-8b47-41074b0afdf7[543cd633-b56c-4de8-8b47-41074b0afdf7] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:16:44.934 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-543cd633-b56c-4de8-8b47-41074b0afdf7 complete, cost 299ms 
[INFO ] 2024-05-27 15:16:46.551 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:46.551 - [任务 17(100)][6dbe58d2-d9e9-46a6-8a70-cba58072c9bd] - Node 6dbe58d2-d9e9-46a6-8a70-cba58072c9bd[6dbe58d2-d9e9-46a6-8a70-cba58072c9bd] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:16:46.551 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:46.551 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:46.551 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:46.551 - [任务 17(100)][6dbe58d2-d9e9-46a6-8a70-cba58072c9bd] - Node 6dbe58d2-d9e9-46a6-8a70-cba58072c9bd[6dbe58d2-d9e9-46a6-8a70-cba58072c9bd] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:46.551 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:46.552 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:46.637 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:16:46.637 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:16:46.637 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:16:46.638 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:16:46.638 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:16:46.639 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:46.639 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:46.639 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:16:46.639 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:16:46.640 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 3 ms 
[INFO ] 2024-05-27 15:16:46.707 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:16:46.707 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-006aaf0a-3e30-4ea4-b8b9-ca19cfb5f52a 
[INFO ] 2024-05-27 15:16:46.707 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-006aaf0a-3e30-4ea4-b8b9-ca19cfb5f52a 
[INFO ] 2024-05-27 15:16:46.707 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:16:46.708 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:16:46.708 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:16:46.708 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 3 ms 
[INFO ] 2024-05-27 15:16:46.736 - [任务 17(100)][6dbe58d2-d9e9-46a6-8a70-cba58072c9bd] - Node 6dbe58d2-d9e9-46a6-8a70-cba58072c9bd[6dbe58d2-d9e9-46a6-8a70-cba58072c9bd] running status set to false 
[INFO ] 2024-05-27 15:16:46.736 - [任务 17(100)][6dbe58d2-d9e9-46a6-8a70-cba58072c9bd] - Node 6dbe58d2-d9e9-46a6-8a70-cba58072c9bd[6dbe58d2-d9e9-46a6-8a70-cba58072c9bd] schema data cleaned 
[INFO ] 2024-05-27 15:16:46.736 - [任务 17(100)][6dbe58d2-d9e9-46a6-8a70-cba58072c9bd] - Node 6dbe58d2-d9e9-46a6-8a70-cba58072c9bd[6dbe58d2-d9e9-46a6-8a70-cba58072c9bd] monitor closed 
[INFO ] 2024-05-27 15:16:46.736 - [任务 17(100)][6dbe58d2-d9e9-46a6-8a70-cba58072c9bd] - Node 6dbe58d2-d9e9-46a6-8a70-cba58072c9bd[6dbe58d2-d9e9-46a6-8a70-cba58072c9bd] close complete, cost 2 ms 
[INFO ] 2024-05-27 15:16:46.946 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-6dbe58d2-d9e9-46a6-8a70-cba58072c9bd complete, cost 225ms 
[INFO ] 2024-05-27 15:16:48.311 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:48.311 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:48.312 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:48.312 - [任务 17(100)][9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa] - Node 9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa[9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:16:48.313 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:48.313 - [任务 17(100)][9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa] - Node 9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa[9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:48.313 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:48.464 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:48.464 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:16:48.464 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:16:48.464 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:16:48.464 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:16:48.465 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 2 ms 
[INFO ] 2024-05-27 15:16:48.467 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:48.467 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:48.467 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:16:48.467 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:16:48.467 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 6 ms 
[INFO ] 2024-05-27 15:16:48.519 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:16:48.520 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-260e9c0f-2f36-448e-97b6-01f569825075 
[INFO ] 2024-05-27 15:16:48.520 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-260e9c0f-2f36-448e-97b6-01f569825075 
[INFO ] 2024-05-27 15:16:48.520 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:16:48.522 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:16:48.522 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:16:48.522 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 5 ms 
[INFO ] 2024-05-27 15:16:48.531 - [任务 17(100)][9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa] - Node 9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa[9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa] running status set to false 
[INFO ] 2024-05-27 15:16:48.531 - [任务 17(100)][9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa] - Node 9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa[9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa] schema data cleaned 
[INFO ] 2024-05-27 15:16:48.531 - [任务 17(100)][9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa] - Node 9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa[9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa] monitor closed 
[INFO ] 2024-05-27 15:16:48.532 - [任务 17(100)][9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa] - Node 9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa[9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:16:48.532 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-9d3aeb14-6bac-4c6e-a1d5-8a8ddc0870fa complete, cost 307ms 
[INFO ] 2024-05-27 15:16:54.047 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:54.047 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:54.047 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:16:54.053 - [任务 17(100)][c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5] - Node c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5[c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:16:54.054 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:54.054 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:54.054 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:54.054 - [任务 17(100)][c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5] - Node c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5[c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:16:54.152 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:16:54.152 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:16:54.152 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:16:54.152 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:16:54.152 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:16:54.154 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:54.155 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:16:54.155 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:16:54.155 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:16:54.155 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 5 ms 
[INFO ] 2024-05-27 15:16:54.255 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:16:54.256 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-89984bb7-a63c-4f06-9135-03fae9b1c225 
[INFO ] 2024-05-27 15:16:54.256 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-89984bb7-a63c-4f06-9135-03fae9b1c225 
[INFO ] 2024-05-27 15:16:54.258 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:16:54.261 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:16:54.261 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:16:54.277 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 16 ms 
[INFO ] 2024-05-27 15:16:54.278 - [任务 17(100)][c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5] - Node c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5[c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5] running status set to false 
[INFO ] 2024-05-27 15:16:54.278 - [任务 17(100)][c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5] - Node c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5[c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5] schema data cleaned 
[INFO ] 2024-05-27 15:16:54.278 - [任务 17(100)][c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5] - Node c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5[c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5] monitor closed 
[INFO ] 2024-05-27 15:16:54.278 - [任务 17(100)][c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5] - Node c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5[c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5] close complete, cost 2 ms 
[INFO ] 2024-05-27 15:16:54.280 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-c8aeda7f-7f5a-4c18-b7a5-a4aca69e4ea5 complete, cost 280ms 
[INFO ] 2024-05-27 15:18:20.470 - [任务 17(100)][fe1224be-3371-4073-90c1-2089c506a266] - Node fe1224be-3371-4073-90c1-2089c506a266[fe1224be-3371-4073-90c1-2089c506a266] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:18:20.471 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:18:20.471 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:18:20.471 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:18:20.471 - [任务 17(100)][fe1224be-3371-4073-90c1-2089c506a266] - Node fe1224be-3371-4073-90c1-2089c506a266[fe1224be-3371-4073-90c1-2089c506a266] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:18:20.471 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:18:20.472 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 2 ms 
[INFO ] 2024-05-27 15:18:20.472 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:18:20.780 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:18:20.782 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:18:20.782 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:18:20.782 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:18:20.783 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 2 ms 
[INFO ] 2024-05-27 15:18:20.785 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:18:20.785 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:18:20.786 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:18:20.788 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:18:20.805 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 23 ms 
[INFO ] 2024-05-27 15:18:20.805 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:18:20.807 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-8b08b7b0-74e1-4942-9368-d758640d3333 
[INFO ] 2024-05-27 15:18:20.807 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-8b08b7b0-74e1-4942-9368-d758640d3333 
[INFO ] 2024-05-27 15:18:20.808 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:18:20.809 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:18:20.809 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:18:20.837 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 4 ms 
[INFO ] 2024-05-27 15:18:20.837 - [任务 17(100)][fe1224be-3371-4073-90c1-2089c506a266] - Node fe1224be-3371-4073-90c1-2089c506a266[fe1224be-3371-4073-90c1-2089c506a266] running status set to false 
[INFO ] 2024-05-27 15:18:20.837 - [任务 17(100)][fe1224be-3371-4073-90c1-2089c506a266] - Node fe1224be-3371-4073-90c1-2089c506a266[fe1224be-3371-4073-90c1-2089c506a266] schema data cleaned 
[INFO ] 2024-05-27 15:18:20.837 - [任务 17(100)][fe1224be-3371-4073-90c1-2089c506a266] - Node fe1224be-3371-4073-90c1-2089c506a266[fe1224be-3371-4073-90c1-2089c506a266] monitor closed 
[INFO ] 2024-05-27 15:18:20.837 - [任务 17(100)][fe1224be-3371-4073-90c1-2089c506a266] - Node fe1224be-3371-4073-90c1-2089c506a266[fe1224be-3371-4073-90c1-2089c506a266] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:18:21.042 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-fe1224be-3371-4073-90c1-2089c506a266 complete, cost 538ms 
[INFO ] 2024-05-27 15:18:54.445 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:18:54.445 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:18:54.449 - [任务 17(100)][6703a429-694a-4aee-961b-2810d0c9fbed] - Node 6703a429-694a-4aee-961b-2810d0c9fbed[6703a429-694a-4aee-961b-2810d0c9fbed] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:18:54.450 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:18:54.450 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 4 ms 
[INFO ] 2024-05-27 15:18:54.451 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 5 ms 
[INFO ] 2024-05-27 15:18:54.451 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:18:54.451 - [任务 17(100)][6703a429-694a-4aee-961b-2810d0c9fbed] - Node 6703a429-694a-4aee-961b-2810d0c9fbed[6703a429-694a-4aee-961b-2810d0c9fbed] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:18:54.611 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:18:54.612 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:18:54.612 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:18:54.612 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:18:54.612 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 3 ms 
[INFO ] 2024-05-27 15:18:54.615 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:18:54.615 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:18:54.615 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:18:54.615 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:18:54.651 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 12 ms 
[INFO ] 2024-05-27 15:18:54.651 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:18:54.653 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-534ae97c-131e-4ea7-9d6a-87d02342665a 
[INFO ] 2024-05-27 15:18:54.653 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-534ae97c-131e-4ea7-9d6a-87d02342665a 
[INFO ] 2024-05-27 15:18:54.653 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:18:54.657 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:18:54.657 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:18:54.671 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 6 ms 
[INFO ] 2024-05-27 15:18:54.671 - [任务 17(100)][6703a429-694a-4aee-961b-2810d0c9fbed] - Node 6703a429-694a-4aee-961b-2810d0c9fbed[6703a429-694a-4aee-961b-2810d0c9fbed] running status set to false 
[INFO ] 2024-05-27 15:18:54.671 - [任务 17(100)][6703a429-694a-4aee-961b-2810d0c9fbed] - Node 6703a429-694a-4aee-961b-2810d0c9fbed[6703a429-694a-4aee-961b-2810d0c9fbed] schema data cleaned 
[INFO ] 2024-05-27 15:18:54.671 - [任务 17(100)][6703a429-694a-4aee-961b-2810d0c9fbed] - Node 6703a429-694a-4aee-961b-2810d0c9fbed[6703a429-694a-4aee-961b-2810d0c9fbed] monitor closed 
[INFO ] 2024-05-27 15:18:54.673 - [任务 17(100)][6703a429-694a-4aee-961b-2810d0c9fbed] - Node 6703a429-694a-4aee-961b-2810d0c9fbed[6703a429-694a-4aee-961b-2810d0c9fbed] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:18:54.673 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-6703a429-694a-4aee-961b-2810d0c9fbed complete, cost 356ms 
[INFO ] 2024-05-27 15:19:01.854 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:01.854 - [任务 17(100)][2c10e616-ee03-41a6-9a2b-2f41b26adda2] - Node 2c10e616-ee03-41a6-9a2b-2f41b26adda2[2c10e616-ee03-41a6-9a2b-2f41b26adda2] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:19:01.854 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:01.854 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:01.952 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:01.952 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:01.952 - [任务 17(100)][2c10e616-ee03-41a6-9a2b-2f41b26adda2] - Node 2c10e616-ee03-41a6-9a2b-2f41b26adda2[2c10e616-ee03-41a6-9a2b-2f41b26adda2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:01.952 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:01.952 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:19:01.952 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:19:01.953 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:19:01.953 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:19:01.954 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 8 ms 
[INFO ] 2024-05-27 15:19:01.955 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:19:01.955 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:19:01.955 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:19:01.955 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:19:01.956 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 11 ms 
[INFO ] 2024-05-27 15:19:02.059 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:19:02.066 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-46d43cb0-99ca-4062-a728-8692255574ea 
[INFO ] 2024-05-27 15:19:02.066 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-46d43cb0-99ca-4062-a728-8692255574ea 
[INFO ] 2024-05-27 15:19:02.067 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:19:02.068 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:19:02.068 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:19:02.068 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 14 ms 
[INFO ] 2024-05-27 15:19:02.080 - [任务 17(100)][2c10e616-ee03-41a6-9a2b-2f41b26adda2] - Node 2c10e616-ee03-41a6-9a2b-2f41b26adda2[2c10e616-ee03-41a6-9a2b-2f41b26adda2] running status set to false 
[INFO ] 2024-05-27 15:19:02.081 - [任务 17(100)][2c10e616-ee03-41a6-9a2b-2f41b26adda2] - Node 2c10e616-ee03-41a6-9a2b-2f41b26adda2[2c10e616-ee03-41a6-9a2b-2f41b26adda2] schema data cleaned 
[INFO ] 2024-05-27 15:19:02.081 - [任务 17(100)][2c10e616-ee03-41a6-9a2b-2f41b26adda2] - Node 2c10e616-ee03-41a6-9a2b-2f41b26adda2[2c10e616-ee03-41a6-9a2b-2f41b26adda2] monitor closed 
[INFO ] 2024-05-27 15:19:02.081 - [任务 17(100)][2c10e616-ee03-41a6-9a2b-2f41b26adda2] - Node 2c10e616-ee03-41a6-9a2b-2f41b26adda2[2c10e616-ee03-41a6-9a2b-2f41b26adda2] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:19:02.287 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-2c10e616-ee03-41a6-9a2b-2f41b26adda2 complete, cost 301ms 
[INFO ] 2024-05-27 15:19:13.470 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:13.470 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:13.470 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:13.471 - [任务 17(100)][d3362366-1f5b-471e-acf9-7bcb579a174f] - Node d3362366-1f5b-471e-acf9-7bcb579a174f[d3362366-1f5b-471e-acf9-7bcb579a174f] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:19:13.471 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:13.471 - [任务 17(100)][d3362366-1f5b-471e-acf9-7bcb579a174f] - Node d3362366-1f5b-471e-acf9-7bcb579a174f[d3362366-1f5b-471e-acf9-7bcb579a174f] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:13.471 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:13.471 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:13.614 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:19:13.614 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:19:13.615 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:19:13.615 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:19:13.616 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 2 ms 
[INFO ] 2024-05-27 15:19:13.618 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:19:13.618 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:19:13.618 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:19:13.618 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:19:13.661 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 6 ms 
[INFO ] 2024-05-27 15:19:13.661 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:19:13.663 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-1b37c072-4274-4fe8-824c-198b676c509e 
[INFO ] 2024-05-27 15:19:13.663 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-1b37c072-4274-4fe8-824c-198b676c509e 
[INFO ] 2024-05-27 15:19:13.663 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:19:13.664 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:19:13.664 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:19:13.664 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 4 ms 
[INFO ] 2024-05-27 15:19:13.674 - [任务 17(100)][d3362366-1f5b-471e-acf9-7bcb579a174f] - Node d3362366-1f5b-471e-acf9-7bcb579a174f[d3362366-1f5b-471e-acf9-7bcb579a174f] running status set to false 
[INFO ] 2024-05-27 15:19:13.674 - [任务 17(100)][d3362366-1f5b-471e-acf9-7bcb579a174f] - Node d3362366-1f5b-471e-acf9-7bcb579a174f[d3362366-1f5b-471e-acf9-7bcb579a174f] schema data cleaned 
[INFO ] 2024-05-27 15:19:13.674 - [任务 17(100)][d3362366-1f5b-471e-acf9-7bcb579a174f] - Node d3362366-1f5b-471e-acf9-7bcb579a174f[d3362366-1f5b-471e-acf9-7bcb579a174f] monitor closed 
[INFO ] 2024-05-27 15:19:13.675 - [任务 17(100)][d3362366-1f5b-471e-acf9-7bcb579a174f] - Node d3362366-1f5b-471e-acf9-7bcb579a174f[d3362366-1f5b-471e-acf9-7bcb579a174f] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:19:13.880 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-d3362366-1f5b-471e-acf9-7bcb579a174f complete, cost 283ms 
[INFO ] 2024-05-27 15:19:24.438 - [任务 17(100)][ac1670b7-3668-433e-a694-476332727931] - Node ac1670b7-3668-433e-a694-476332727931[ac1670b7-3668-433e-a694-476332727931] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:19:24.439 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:24.439 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:24.439 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:24.439 - [任务 17(100)][ac1670b7-3668-433e-a694-476332727931] - Node ac1670b7-3668-433e-a694-476332727931[ac1670b7-3668-433e-a694-476332727931] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:24.442 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:24.443 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:24.443 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:24.622 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:19:24.622 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:19:24.622 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:19:24.622 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:19:24.622 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 7 ms 
[INFO ] 2024-05-27 15:19:24.623 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:19:24.623 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:19:24.623 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:19:24.624 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:19:24.743 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 16 ms 
[INFO ] 2024-05-27 15:19:24.743 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:19:24.752 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-216f5097-9a71-4d09-9b29-5eae784c2bbf 
[INFO ] 2024-05-27 15:19:24.752 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-216f5097-9a71-4d09-9b29-5eae784c2bbf 
[INFO ] 2024-05-27 15:19:24.752 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:19:24.754 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:19:24.754 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:19:24.780 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 22 ms 
[INFO ] 2024-05-27 15:19:24.781 - [任务 17(100)][ac1670b7-3668-433e-a694-476332727931] - Node ac1670b7-3668-433e-a694-476332727931[ac1670b7-3668-433e-a694-476332727931] running status set to false 
[INFO ] 2024-05-27 15:19:24.781 - [任务 17(100)][ac1670b7-3668-433e-a694-476332727931] - Node ac1670b7-3668-433e-a694-476332727931[ac1670b7-3668-433e-a694-476332727931] schema data cleaned 
[INFO ] 2024-05-27 15:19:24.781 - [任务 17(100)][ac1670b7-3668-433e-a694-476332727931] - Node ac1670b7-3668-433e-a694-476332727931[ac1670b7-3668-433e-a694-476332727931] monitor closed 
[INFO ] 2024-05-27 15:19:24.781 - [任务 17(100)][ac1670b7-3668-433e-a694-476332727931] - Node ac1670b7-3668-433e-a694-476332727931[ac1670b7-3668-433e-a694-476332727931] close complete, cost 1 ms 
[INFO ] 2024-05-27 15:19:24.983 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-ac1670b7-3668-433e-a694-476332727931 complete, cost 446ms 
[INFO ] 2024-05-27 15:19:27.655 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:27.655 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:27.655 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:27.655 - [任务 17(100)][d457a2f1-95e2-4522-a9a1-e484ddf15561] - Node d457a2f1-95e2-4522-a9a1-e484ddf15561[d457a2f1-95e2-4522-a9a1-e484ddf15561] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:19:27.655 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:27.655 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:27.655 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:27.655 - [任务 17(100)][d457a2f1-95e2-4522-a9a1-e484ddf15561] - Node d457a2f1-95e2-4522-a9a1-e484ddf15561[d457a2f1-95e2-4522-a9a1-e484ddf15561] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:27.751 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:19:27.752 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:19:27.752 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:19:27.752 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:19:27.752 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:19:27.754 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:19:27.754 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:19:27.754 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:19:27.755 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:19:27.755 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 4 ms 
[INFO ] 2024-05-27 15:19:27.839 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:19:27.839 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-15f95b93-3894-48eb-9d35-32547f1bc0c6 
[INFO ] 2024-05-27 15:19:27.840 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-15f95b93-3894-48eb-9d35-32547f1bc0c6 
[INFO ] 2024-05-27 15:19:27.840 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:19:27.841 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:19:27.841 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:19:27.860 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 17 ms 
[INFO ] 2024-05-27 15:19:27.860 - [任务 17(100)][d457a2f1-95e2-4522-a9a1-e484ddf15561] - Node d457a2f1-95e2-4522-a9a1-e484ddf15561[d457a2f1-95e2-4522-a9a1-e484ddf15561] running status set to false 
[INFO ] 2024-05-27 15:19:27.861 - [任务 17(100)][d457a2f1-95e2-4522-a9a1-e484ddf15561] - Node d457a2f1-95e2-4522-a9a1-e484ddf15561[d457a2f1-95e2-4522-a9a1-e484ddf15561] schema data cleaned 
[INFO ] 2024-05-27 15:19:27.861 - [任务 17(100)][d457a2f1-95e2-4522-a9a1-e484ddf15561] - Node d457a2f1-95e2-4522-a9a1-e484ddf15561[d457a2f1-95e2-4522-a9a1-e484ddf15561] monitor closed 
[INFO ] 2024-05-27 15:19:27.861 - [任务 17(100)][d457a2f1-95e2-4522-a9a1-e484ddf15561] - Node d457a2f1-95e2-4522-a9a1-e484ddf15561[d457a2f1-95e2-4522-a9a1-e484ddf15561] close complete, cost 1 ms 
[INFO ] 2024-05-27 15:19:28.071 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-d457a2f1-95e2-4522-a9a1-e484ddf15561 complete, cost 257ms 
[INFO ] 2024-05-27 15:19:31.850 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:31.851 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:31.851 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:31.852 - [任务 17(100)][accbb4cb-df3d-4b24-a88b-260ac3e044e0] - Node accbb4cb-df3d-4b24-a88b-260ac3e044e0[accbb4cb-df3d-4b24-a88b-260ac3e044e0] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:19:31.852 - [任务 17(100)][accbb4cb-df3d-4b24-a88b-260ac3e044e0] - Node accbb4cb-df3d-4b24-a88b-260ac3e044e0[accbb4cb-df3d-4b24-a88b-260ac3e044e0] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:31.852 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 1 ms 
[INFO ] 2024-05-27 15:19:31.852 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:31.852 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 2 ms 
[INFO ] 2024-05-27 15:19:31.985 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:19:31.985 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:19:31.986 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:19:31.986 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:19:31.989 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 2 ms 
[INFO ] 2024-05-27 15:19:31.989 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:19:31.989 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:19:31.989 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:19:31.989 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:19:31.989 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 10 ms 
[INFO ] 2024-05-27 15:19:32.038 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:19:32.038 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-a8897bf2-66e9-47df-80eb-7574fb5e678a 
[INFO ] 2024-05-27 15:19:32.038 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-a8897bf2-66e9-47df-80eb-7574fb5e678a 
[INFO ] 2024-05-27 15:19:32.038 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:19:32.039 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:19:32.039 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:19:32.039 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 5 ms 
[INFO ] 2024-05-27 15:19:32.048 - [任务 17(100)][accbb4cb-df3d-4b24-a88b-260ac3e044e0] - Node accbb4cb-df3d-4b24-a88b-260ac3e044e0[accbb4cb-df3d-4b24-a88b-260ac3e044e0] running status set to false 
[INFO ] 2024-05-27 15:19:32.048 - [任务 17(100)][accbb4cb-df3d-4b24-a88b-260ac3e044e0] - Node accbb4cb-df3d-4b24-a88b-260ac3e044e0[accbb4cb-df3d-4b24-a88b-260ac3e044e0] schema data cleaned 
[INFO ] 2024-05-27 15:19:32.048 - [任务 17(100)][accbb4cb-df3d-4b24-a88b-260ac3e044e0] - Node accbb4cb-df3d-4b24-a88b-260ac3e044e0[accbb4cb-df3d-4b24-a88b-260ac3e044e0] monitor closed 
[INFO ] 2024-05-27 15:19:32.050 - [任务 17(100)][accbb4cb-df3d-4b24-a88b-260ac3e044e0] - Node accbb4cb-df3d-4b24-a88b-260ac3e044e0[accbb4cb-df3d-4b24-a88b-260ac3e044e0] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:19:32.050 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-accbb4cb-df3d-4b24-a88b-260ac3e044e0 complete, cost 271ms 
[INFO ] 2024-05-27 15:19:37.233 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:37.233 - [任务 17(100)][71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb] - Node 71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb[71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:19:37.234 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:37.234 - [任务 17(100)][71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb] - Node 71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb[71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:37.234 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:37.234 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:37.234 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:37.312 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:37.312 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:19:37.314 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:19:37.314 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:19:37.314 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:19:37.314 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:19:37.315 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 3 ms 
[INFO ] 2024-05-27 15:19:37.315 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:19:37.316 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:19:37.316 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:19:37.382 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:19:37.383 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:19:37.387 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-4176380e-ba52-49f7-bf22-5dee076e0d75 
[INFO ] 2024-05-27 15:19:37.387 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-4176380e-ba52-49f7-bf22-5dee076e0d75 
[INFO ] 2024-05-27 15:19:37.387 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:19:37.391 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:19:37.391 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:19:37.411 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 10 ms 
[INFO ] 2024-05-27 15:19:37.412 - [任务 17(100)][71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb] - Node 71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb[71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb] running status set to false 
[INFO ] 2024-05-27 15:19:37.412 - [任务 17(100)][71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb] - Node 71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb[71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb] schema data cleaned 
[INFO ] 2024-05-27 15:19:37.412 - [任务 17(100)][71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb] - Node 71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb[71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb] monitor closed 
[INFO ] 2024-05-27 15:19:37.414 - [任务 17(100)][71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb] - Node 71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb[71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb] close complete, cost 3 ms 
[INFO ] 2024-05-27 15:19:37.414 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-71c9cfa2-5dbf-4f70-b17e-ed3978e50cfb complete, cost 225ms 
[INFO ] 2024-05-27 15:19:41.159 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:41.160 - [任务 17(100)][9dd60ebf-759b-4424-8438-fb8ec027348a] - Node 9dd60ebf-759b-4424-8438-fb8ec027348a[9dd60ebf-759b-4424-8438-fb8ec027348a] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:19:41.160 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:41.161 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:41.161 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:41.161 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:41.161 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:41.161 - [任务 17(100)][9dd60ebf-759b-4424-8438-fb8ec027348a] - Node 9dd60ebf-759b-4424-8438-fb8ec027348a[9dd60ebf-759b-4424-8438-fb8ec027348a] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:41.316 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:19:41.316 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:19:41.316 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:19:41.316 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:19:41.320 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 3 ms 
[INFO ] 2024-05-27 15:19:41.320 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:19:41.321 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:19:41.321 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:19:41.321 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:19:41.321 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 10 ms 
[INFO ] 2024-05-27 15:19:41.366 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:19:41.366 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-a375ccbc-53d1-4ea1-ba19-724030ccd3fe 
[INFO ] 2024-05-27 15:19:41.366 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-a375ccbc-53d1-4ea1-ba19-724030ccd3fe 
[INFO ] 2024-05-27 15:19:41.366 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:19:41.367 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:19:41.367 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:19:41.367 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 4 ms 
[INFO ] 2024-05-27 15:19:41.382 - [任务 17(100)][9dd60ebf-759b-4424-8438-fb8ec027348a] - Node 9dd60ebf-759b-4424-8438-fb8ec027348a[9dd60ebf-759b-4424-8438-fb8ec027348a] running status set to false 
[INFO ] 2024-05-27 15:19:41.382 - [任务 17(100)][9dd60ebf-759b-4424-8438-fb8ec027348a] - Node 9dd60ebf-759b-4424-8438-fb8ec027348a[9dd60ebf-759b-4424-8438-fb8ec027348a] schema data cleaned 
[INFO ] 2024-05-27 15:19:41.382 - [任务 17(100)][9dd60ebf-759b-4424-8438-fb8ec027348a] - Node 9dd60ebf-759b-4424-8438-fb8ec027348a[9dd60ebf-759b-4424-8438-fb8ec027348a] monitor closed 
[INFO ] 2024-05-27 15:19:41.383 - [任务 17(100)][9dd60ebf-759b-4424-8438-fb8ec027348a] - Node 9dd60ebf-759b-4424-8438-fb8ec027348a[9dd60ebf-759b-4424-8438-fb8ec027348a] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:19:41.584 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-9dd60ebf-759b-4424-8438-fb8ec027348a complete, cost 316ms 
[INFO ] 2024-05-27 15:19:53.942 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:53.943 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:53.943 - [任务 17(100)][aa32f7dd-39c5-4f5f-92e5-e507f03407cf] - Node aa32f7dd-39c5-4f5f-92e5-e507f03407cf[aa32f7dd-39c5-4f5f-92e5-e507f03407cf] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:19:53.943 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:19:53.943 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:53.943 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:53.943 - [任务 17(100)][aa32f7dd-39c5-4f5f-92e5-e507f03407cf] - Node aa32f7dd-39c5-4f5f-92e5-e507f03407cf[aa32f7dd-39c5-4f5f-92e5-e507f03407cf] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:53.943 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:19:54.026 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:19:54.026 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:19:54.026 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:19:54.026 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:19:54.027 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:19:54.029 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:19:54.029 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:19:54.029 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:19:54.029 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:19:54.029 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 3 ms 
[INFO ] 2024-05-27 15:19:54.110 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:19:54.110 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-b80c56e1-4ae5-407f-8078-1fbc8f79d7e3 
[INFO ] 2024-05-27 15:19:54.110 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-b80c56e1-4ae5-407f-8078-1fbc8f79d7e3 
[INFO ] 2024-05-27 15:19:54.112 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:19:54.112 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:19:54.112 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:19:54.112 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 10 ms 
[INFO ] 2024-05-27 15:19:54.125 - [任务 17(100)][aa32f7dd-39c5-4f5f-92e5-e507f03407cf] - Node aa32f7dd-39c5-4f5f-92e5-e507f03407cf[aa32f7dd-39c5-4f5f-92e5-e507f03407cf] running status set to false 
[INFO ] 2024-05-27 15:19:54.125 - [任务 17(100)][aa32f7dd-39c5-4f5f-92e5-e507f03407cf] - Node aa32f7dd-39c5-4f5f-92e5-e507f03407cf[aa32f7dd-39c5-4f5f-92e5-e507f03407cf] schema data cleaned 
[INFO ] 2024-05-27 15:19:54.125 - [任务 17(100)][aa32f7dd-39c5-4f5f-92e5-e507f03407cf] - Node aa32f7dd-39c5-4f5f-92e5-e507f03407cf[aa32f7dd-39c5-4f5f-92e5-e507f03407cf] monitor closed 
[INFO ] 2024-05-27 15:19:54.125 - [任务 17(100)][aa32f7dd-39c5-4f5f-92e5-e507f03407cf] - Node aa32f7dd-39c5-4f5f-92e5-e507f03407cf[aa32f7dd-39c5-4f5f-92e5-e507f03407cf] close complete, cost 1 ms 
[INFO ] 2024-05-27 15:19:54.332 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-aa32f7dd-39c5-4f5f-92e5-e507f03407cf complete, cost 228ms 
[INFO ] 2024-05-27 15:29:13.626 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:29:13.629 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:29:13.631 - [任务 17(100)][36e4976d-0192-43a2-a0d0-3bd02896d978] - Node 36e4976d-0192-43a2-a0d0-3bd02896d978[36e4976d-0192-43a2-a0d0-3bd02896d978] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:29:13.631 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:29:13.632 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 1 ms 
[INFO ] 2024-05-27 15:29:13.632 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:29:13.632 - [任务 17(100)][36e4976d-0192-43a2-a0d0-3bd02896d978] - Node 36e4976d-0192-43a2-a0d0-3bd02896d978[36e4976d-0192-43a2-a0d0-3bd02896d978] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:29:13.632 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:29:14.825 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:29:14.843 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:29:14.855 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:29:14.867 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:29:14.868 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 25 ms 
[INFO ] 2024-05-27 15:29:14.917 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:29:14.919 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:29:14.920 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:29:14.921 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:29:14.922 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:29:14.923 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 113 ms 
[INFO ] 2024-05-27 15:29:14.937 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-d9c4a96d-204e-4b13-8b5a-82481440953f 
[INFO ] 2024-05-27 15:29:14.939 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-d9c4a96d-204e-4b13-8b5a-82481440953f 
[INFO ] 2024-05-27 15:29:14.940 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:29:14.949 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:29:14.949 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:29:15.021 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 30 ms 
[INFO ] 2024-05-27 15:29:15.026 - [任务 17(100)][36e4976d-0192-43a2-a0d0-3bd02896d978] - Node 36e4976d-0192-43a2-a0d0-3bd02896d978[36e4976d-0192-43a2-a0d0-3bd02896d978] running status set to false 
[INFO ] 2024-05-27 15:29:15.026 - [任务 17(100)][36e4976d-0192-43a2-a0d0-3bd02896d978] - Node 36e4976d-0192-43a2-a0d0-3bd02896d978[36e4976d-0192-43a2-a0d0-3bd02896d978] schema data cleaned 
[INFO ] 2024-05-27 15:29:15.026 - [任务 17(100)][36e4976d-0192-43a2-a0d0-3bd02896d978] - Node 36e4976d-0192-43a2-a0d0-3bd02896d978[36e4976d-0192-43a2-a0d0-3bd02896d978] monitor closed 
[INFO ] 2024-05-27 15:29:15.033 - [任务 17(100)][36e4976d-0192-43a2-a0d0-3bd02896d978] - Node 36e4976d-0192-43a2-a0d0-3bd02896d978[36e4976d-0192-43a2-a0d0-3bd02896d978] close complete, cost 21 ms 
[INFO ] 2024-05-27 15:29:15.239 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-36e4976d-0192-43a2-a0d0-3bd02896d978 complete, cost 2020ms 
[INFO ] 2024-05-27 15:30:12.670 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:30:12.673 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:30:12.674 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:30:12.674 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:30:12.674 - [任务 17(100)][7bbf94f8-4e19-47f9-b65b-13a2899f068b] - Node 7bbf94f8-4e19-47f9-b65b-13a2899f068b[7bbf94f8-4e19-47f9-b65b-13a2899f068b] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:30:12.686 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:30:12.686 - [任务 17(100)][7bbf94f8-4e19-47f9-b65b-13a2899f068b] - Node 7bbf94f8-4e19-47f9-b65b-13a2899f068b[7bbf94f8-4e19-47f9-b65b-13a2899f068b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:30:12.686 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:30:12.886 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:30:12.888 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:30:12.889 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:30:12.889 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:30:12.890 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 14 ms 
[INFO ] 2024-05-27 15:30:12.893 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:30:12.895 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:30:12.895 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:30:12.895 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:30:12.970 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 35 ms 
[INFO ] 2024-05-27 15:30:12.972 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:30:12.981 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-c34d7834-b217-4ea5-b84c-f2b2899f5bce 
[INFO ] 2024-05-27 15:30:12.981 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-c34d7834-b217-4ea5-b84c-f2b2899f5bce 
[INFO ] 2024-05-27 15:30:12.981 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:30:12.986 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:30:12.987 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:30:12.987 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 28 ms 
[INFO ] 2024-05-27 15:30:13.004 - [任务 17(100)][7bbf94f8-4e19-47f9-b65b-13a2899f068b] - Node 7bbf94f8-4e19-47f9-b65b-13a2899f068b[7bbf94f8-4e19-47f9-b65b-13a2899f068b] running status set to false 
[INFO ] 2024-05-27 15:30:13.006 - [任务 17(100)][7bbf94f8-4e19-47f9-b65b-13a2899f068b] - Node 7bbf94f8-4e19-47f9-b65b-13a2899f068b[7bbf94f8-4e19-47f9-b65b-13a2899f068b] schema data cleaned 
[INFO ] 2024-05-27 15:30:13.006 - [任务 17(100)][7bbf94f8-4e19-47f9-b65b-13a2899f068b] - Node 7bbf94f8-4e19-47f9-b65b-13a2899f068b[7bbf94f8-4e19-47f9-b65b-13a2899f068b] monitor closed 
[INFO ] 2024-05-27 15:30:13.006 - [任务 17(100)][7bbf94f8-4e19-47f9-b65b-13a2899f068b] - Node 7bbf94f8-4e19-47f9-b65b-13a2899f068b[7bbf94f8-4e19-47f9-b65b-13a2899f068b] close complete, cost 2 ms 
[INFO ] 2024-05-27 15:30:13.009 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-7bbf94f8-4e19-47f9-b65b-13a2899f068b complete, cost 466ms 
[INFO ] 2024-05-27 15:30:15.390 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:30:15.390 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:30:15.391 - [任务 17(100)][f88603b0-b300-499d-9a3e-bde6ad299ce7] - Node f88603b0-b300-499d-9a3e-bde6ad299ce7[f88603b0-b300-499d-9a3e-bde6ad299ce7] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:30:15.392 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:30:15.395 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:30:15.395 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:30:15.399 - [任务 17(100)][f88603b0-b300-499d-9a3e-bde6ad299ce7] - Node f88603b0-b300-499d-9a3e-bde6ad299ce7[f88603b0-b300-499d-9a3e-bde6ad299ce7] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:30:15.401 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:30:15.693 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:30:15.694 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:30:15.695 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:30:15.695 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:30:15.696 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:30:15.696 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 34 ms 
[INFO ] 2024-05-27 15:30:15.715 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:30:15.715 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-db229900-1f99-422e-998a-ceb73fdf37d5 
[INFO ] 2024-05-27 15:30:15.716 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:30:15.717 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-db229900-1f99-422e-998a-ceb73fdf37d5 
[INFO ] 2024-05-27 15:30:15.717 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:30:15.717 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:30:15.717 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:30:15.718 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 60 ms 
[INFO ] 2024-05-27 15:30:15.718 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:30:15.719 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:30:15.719 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 23 ms 
[INFO ] 2024-05-27 15:30:15.733 - [任务 17(100)][f88603b0-b300-499d-9a3e-bde6ad299ce7] - Node f88603b0-b300-499d-9a3e-bde6ad299ce7[f88603b0-b300-499d-9a3e-bde6ad299ce7] running status set to false 
[INFO ] 2024-05-27 15:30:15.734 - [任务 17(100)][f88603b0-b300-499d-9a3e-bde6ad299ce7] - Node f88603b0-b300-499d-9a3e-bde6ad299ce7[f88603b0-b300-499d-9a3e-bde6ad299ce7] schema data cleaned 
[INFO ] 2024-05-27 15:30:15.734 - [任务 17(100)][f88603b0-b300-499d-9a3e-bde6ad299ce7] - Node f88603b0-b300-499d-9a3e-bde6ad299ce7[f88603b0-b300-499d-9a3e-bde6ad299ce7] monitor closed 
[INFO ] 2024-05-27 15:30:15.734 - [任务 17(100)][f88603b0-b300-499d-9a3e-bde6ad299ce7] - Node f88603b0-b300-499d-9a3e-bde6ad299ce7[f88603b0-b300-499d-9a3e-bde6ad299ce7] close complete, cost 2 ms 
[INFO ] 2024-05-27 15:30:15.942 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-f88603b0-b300-499d-9a3e-bde6ad299ce7 complete, cost 409ms 
[INFO ] 2024-05-27 15:31:27.601 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:31:27.605 - [任务 17(100)][65cfbd7e-9289-4339-9de0-62554cbb7625] - Node 65cfbd7e-9289-4339-9de0-62554cbb7625[65cfbd7e-9289-4339-9de0-62554cbb7625] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:31:27.605 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:31:27.613 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:31:27.614 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:31:27.616 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 2 ms 
[INFO ] 2024-05-27 15:31:27.617 - [任务 17(100)][65cfbd7e-9289-4339-9de0-62554cbb7625] - Node 65cfbd7e-9289-4339-9de0-62554cbb7625[65cfbd7e-9289-4339-9de0-62554cbb7625] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:31:27.618 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:31:27.835 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:31:27.836 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:31:27.836 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:31:27.836 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:31:27.838 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 10 ms 
[INFO ] 2024-05-27 15:31:27.845 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:31:27.846 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:31:27.846 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:31:27.846 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:31:27.921 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 31 ms 
[INFO ] 2024-05-27 15:31:27.922 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:31:27.931 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-97ee145c-c8b7-4f4a-afdb-72f2f6d3d491 
[INFO ] 2024-05-27 15:31:27.931 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-97ee145c-c8b7-4f4a-afdb-72f2f6d3d491 
[INFO ] 2024-05-27 15:31:27.932 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:31:27.934 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:31:27.935 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:31:27.935 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 28 ms 
[INFO ] 2024-05-27 15:31:27.956 - [任务 17(100)][65cfbd7e-9289-4339-9de0-62554cbb7625] - Node 65cfbd7e-9289-4339-9de0-62554cbb7625[65cfbd7e-9289-4339-9de0-62554cbb7625] running status set to false 
[INFO ] 2024-05-27 15:31:27.956 - [任务 17(100)][65cfbd7e-9289-4339-9de0-62554cbb7625] - Node 65cfbd7e-9289-4339-9de0-62554cbb7625[65cfbd7e-9289-4339-9de0-62554cbb7625] schema data cleaned 
[INFO ] 2024-05-27 15:31:27.956 - [任务 17(100)][65cfbd7e-9289-4339-9de0-62554cbb7625] - Node 65cfbd7e-9289-4339-9de0-62554cbb7625[65cfbd7e-9289-4339-9de0-62554cbb7625] monitor closed 
[INFO ] 2024-05-27 15:31:27.956 - [任务 17(100)][65cfbd7e-9289-4339-9de0-62554cbb7625] - Node 65cfbd7e-9289-4339-9de0-62554cbb7625[65cfbd7e-9289-4339-9de0-62554cbb7625] close complete, cost 4 ms 
[INFO ] 2024-05-27 15:31:27.961 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-65cfbd7e-9289-4339-9de0-62554cbb7625 complete, cost 456ms 
[INFO ] 2024-05-27 15:31:35.389 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:31:35.389 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:31:35.389 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] start preload schema,table counts: 2 
[INFO ] 2024-05-27 15:31:35.393 - [任务 17(100)][055ab1ab-41ad-45e2-bc59-886df844d8ca] - Node 055ab1ab-41ad-45e2-bc59-886df844d8ca[055ab1ab-41ad-45e2-bc59-886df844d8ca] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:31:35.393 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:31:35.399 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:31:35.399 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:31:35.399 - [任务 17(100)][055ab1ab-41ad-45e2-bc59-886df844d8ca] - Node 055ab1ab-41ad-45e2-bc59-886df844d8ca[055ab1ab-41ad-45e2-bc59-886df844d8ca] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:31:35.667 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] running status set to false 
[INFO ] 2024-05-27 15:31:35.670 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] running status set to false 
[INFO ] 2024-05-27 15:31:35.670 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] schema data cleaned 
[INFO ] 2024-05-27 15:31:35.670 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] monitor closed 
[INFO ] 2024-05-27 15:31:35.670 - [任务 17(100)][字段编辑] - Node 字段编辑[ff92e4c1-0154-4c73-b23f-07d1c787e616] close complete, cost 10 ms 
[INFO ] 2024-05-27 15:31:35.705 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] running status set to false 
[INFO ] 2024-05-27 15:31:35.726 - [任务 17(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-fb5809aa-59b1-4c1e-b11c-f011649d968f 
[INFO ] 2024-05-27 15:31:35.726 - [任务 17(100)][SourceMongo] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:31:35.726 - [任务 17(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-fb5809aa-59b1-4c1e-b11c-f011649d968f 
[INFO ] 2024-05-27 15:31:35.726 - [任务 17(100)][SourceMongo] - PDK connector node released: HazelcastSampleSourcePdkDataNode-74ff2936-7d2e-4e4d-8d16-7bb42681b2d2 
[INFO ] 2024-05-27 15:31:35.726 - [任务 17(100)][增强JS] - [ScriptExecutorsManager-66543282cd8f0a1c163c37e4-382f222e-d573-4491-b8d3-304fc790b86b-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:31:35.726 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] schema data cleaned 
[INFO ] 2024-05-27 15:31:35.727 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] monitor closed 
[INFO ] 2024-05-27 15:31:35.727 - [任务 17(100)][SourceMongo] - Node SourceMongo[74ff2936-7d2e-4e4d-8d16-7bb42681b2d2] close complete, cost 79 ms 
[INFO ] 2024-05-27 15:31:35.728 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] schema data cleaned 
[INFO ] 2024-05-27 15:31:35.728 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] monitor closed 
[INFO ] 2024-05-27 15:31:35.728 - [任务 17(100)][增强JS] - Node 增强JS[382f222e-d573-4491-b8d3-304fc790b86b] close complete, cost 35 ms 
[INFO ] 2024-05-27 15:31:35.752 - [任务 17(100)][055ab1ab-41ad-45e2-bc59-886df844d8ca] - Node 055ab1ab-41ad-45e2-bc59-886df844d8ca[055ab1ab-41ad-45e2-bc59-886df844d8ca] running status set to false 
[INFO ] 2024-05-27 15:31:35.752 - [任务 17(100)][055ab1ab-41ad-45e2-bc59-886df844d8ca] - Node 055ab1ab-41ad-45e2-bc59-886df844d8ca[055ab1ab-41ad-45e2-bc59-886df844d8ca] schema data cleaned 
[INFO ] 2024-05-27 15:31:35.753 - [任务 17(100)][055ab1ab-41ad-45e2-bc59-886df844d8ca] - Node 055ab1ab-41ad-45e2-bc59-886df844d8ca[055ab1ab-41ad-45e2-bc59-886df844d8ca] monitor closed 
[INFO ] 2024-05-27 15:31:35.753 - [任务 17(100)][055ab1ab-41ad-45e2-bc59-886df844d8ca] - Node 055ab1ab-41ad-45e2-bc59-886df844d8ca[055ab1ab-41ad-45e2-bc59-886df844d8ca] close complete, cost 5 ms 
[INFO ] 2024-05-27 15:31:35.754 - [任务 17(100)] - load MigrateJsResultVos task 66543282cd8f0a1c163c37e4-055ab1ab-41ad-45e2-bc59-886df844d8ca complete, cost 421ms 
