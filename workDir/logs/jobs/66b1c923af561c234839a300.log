[INFO ] 2024-08-07 05:39:56.723 - [任务 3(100)][526c7760-1733-44aa-9a75-a046924d2bb0] - Node 526c7760-1733-44aa-9a75-a046924d2bb0[526c7760-1733-44aa-9a75-a046924d2bb0] start preload schema,table counts: 0 
[INFO ] 2024-08-07 05:39:56.726 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] start preload schema,table counts: 9 
[INFO ] 2024-08-07 05:39:56.727 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] start preload schema,table counts: 9 
[INFO ] 2024-08-07 05:39:56.728 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:39:56.729 - [任务 3(100)][526c7760-1733-44aa-9a75-a046924d2bb0] - Node 526c7760-1733-44aa-9a75-a046924d2bb0[526c7760-1733-44aa-9a75-a046924d2bb0] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:39:56.735 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:39:56.736 - [任务 3(100)][增强JS] - Node migrate_js_processor(增强JS: 24de22ff-e70f-48f1-a31d-fd04f4910417) enable batch process 
[INFO ] 2024-08-07 05:39:57.226 - [任务 3(100)][adab0f13-3b1a-4335-b09e-0752ee778f10] - Node adab0f13-3b1a-4335-b09e-0752ee778f10[adab0f13-3b1a-4335-b09e-0752ee778f10] start preload schema,table counts: 0 
[INFO ] 2024-08-07 05:39:57.226 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] start preload schema,table counts: 9 
[INFO ] 2024-08-07 05:39:57.228 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] start preload schema,table counts: 9 
[INFO ] 2024-08-07 05:39:57.228 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:39:57.228 - [任务 3(100)][adab0f13-3b1a-4335-b09e-0752ee778f10] - Node adab0f13-3b1a-4335-b09e-0752ee778f10[adab0f13-3b1a-4335-b09e-0752ee778f10] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:39:57.229 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:39:57.233 - [任务 3(100)][增强JS] - Node migrate_js_processor(增强JS: 24de22ff-e70f-48f1-a31d-fd04f4910417) enable batch process 
[INFO ] 2024-08-07 05:39:57.386 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] running status set to false 
[INFO ] 2024-08-07 05:39:57.388 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] running status set to false 
[INFO ] 2024-08-07 05:39:57.389 - [任务 3(100)][526c7760-1733-44aa-9a75-a046924d2bb0] - Node 526c7760-1733-44aa-9a75-a046924d2bb0[526c7760-1733-44aa-9a75-a046924d2bb0] running status set to false 
[INFO ] 2024-08-07 05:39:57.389 - [任务 3(100)][526c7760-1733-44aa-9a75-a046924d2bb0] - Node 526c7760-1733-44aa-9a75-a046924d2bb0[526c7760-1733-44aa-9a75-a046924d2bb0] schema data cleaned 
[INFO ] 2024-08-07 05:39:57.389 - [任务 3(100)][526c7760-1733-44aa-9a75-a046924d2bb0] - Node 526c7760-1733-44aa-9a75-a046924d2bb0[526c7760-1733-44aa-9a75-a046924d2bb0] monitor closed 
[INFO ] 2024-08-07 05:39:57.410 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] schema data cleaned 
[INFO ] 2024-08-07 05:39:57.415 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] monitor closed 
[INFO ] 2024-08-07 05:39:57.416 - [任务 3(100)][526c7760-1733-44aa-9a75-a046924d2bb0] - Node 526c7760-1733-44aa-9a75-a046924d2bb0[526c7760-1733-44aa-9a75-a046924d2bb0] close complete, cost 6 ms 
[INFO ] 2024-08-07 05:39:57.416 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] close complete, cost 26 ms 
[INFO ] 2024-08-07 05:39:57.491 - [任务 3(100)][Mysql] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf 
[INFO ] 2024-08-07 05:39:57.495 - [任务 3(100)][Mysql] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf 
[INFO ] 2024-08-07 05:39:57.495 - [任务 3(100)][Mysql] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf 
[INFO ] 2024-08-07 05:39:57.497 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] schema data cleaned 
[INFO ] 2024-08-07 05:39:57.497 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] monitor closed 
[INFO ] 2024-08-07 05:39:57.510 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] close complete, cost 124 ms 
[INFO ] 2024-08-07 05:39:57.512 - [任务 3(100)] - Closed task monitor(s)
null 
[INFO ] 2024-08-07 05:39:57.514 - [任务 3(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-08-07 05:39:57.530 - [任务 3(100)] - Stopped task aspect(s) 
[ERROR] 2024-08-07 05:39:57.531 - [任务 3(100)][Mysql] - start source runner failed: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:61)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:208)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.initNode(HazelcastSampleSourcePdkDataNode.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:85)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:221)
	... 17 more

[INFO ] 2024-08-07 05:40:00.088 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] running status set to false 
[INFO ] 2024-08-07 05:40:00.092 - [任务 3(100)][Mysql] - PDK connector node stopped: null 
[INFO ] 2024-08-07 05:40:00.093 - [任务 3(100)][Mysql] - PDK connector node released: null 
[INFO ] 2024-08-07 05:40:00.097 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] schema data cleaned 
[INFO ] 2024-08-07 05:40:00.097 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] monitor closed 
[INFO ] 2024-08-07 05:40:00.102 - [任务 3(100)][adab0f13-3b1a-4335-b09e-0752ee778f10] - Node adab0f13-3b1a-4335-b09e-0752ee778f10[adab0f13-3b1a-4335-b09e-0752ee778f10] running status set to false 
[INFO ] 2024-08-07 05:40:00.103 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] running status set to false 
[INFO ] 2024-08-07 05:40:00.104 - [任务 3(100)][adab0f13-3b1a-4335-b09e-0752ee778f10] - Node adab0f13-3b1a-4335-b09e-0752ee778f10[adab0f13-3b1a-4335-b09e-0752ee778f10] schema data cleaned 
[INFO ] 2024-08-07 05:40:00.107 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] close complete, cost 6 ms 
[INFO ] 2024-08-07 05:40:00.107 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] schema data cleaned 
[INFO ] 2024-08-07 05:40:00.107 - [任务 3(100)][adab0f13-3b1a-4335-b09e-0752ee778f10] - Node adab0f13-3b1a-4335-b09e-0752ee778f10[adab0f13-3b1a-4335-b09e-0752ee778f10] monitor closed 
[INFO ] 2024-08-07 05:40:00.114 - [任务 3(100)][adab0f13-3b1a-4335-b09e-0752ee778f10] - Node adab0f13-3b1a-4335-b09e-0752ee778f10[adab0f13-3b1a-4335-b09e-0752ee778f10] close complete, cost 16 ms 
[INFO ] 2024-08-07 05:40:00.116 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] monitor closed 
[INFO ] 2024-08-07 05:40:00.116 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] close complete, cost 23 ms 
[INFO ] 2024-08-07 05:40:00.116 - [任务 3(100)] - Closed task monitor(s)
null 
[INFO ] 2024-08-07 05:40:00.120 - [任务 3(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-08-07 05:40:00.120 - [任务 3(100)] - Stopped task aspect(s) 
[INFO ] 2024-08-07 05:40:14.445 - [任务 3(100)][03616f59-c985-4696-839c-370ce4efd4b5] - Node 03616f59-c985-4696-839c-370ce4efd4b5[03616f59-c985-4696-839c-370ce4efd4b5] start preload schema,table counts: 0 
[INFO ] 2024-08-07 05:40:14.449 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] start preload schema,table counts: 9 
[INFO ] 2024-08-07 05:40:14.449 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] start preload schema,table counts: 9 
[INFO ] 2024-08-07 05:40:14.454 - [任务 3(100)][03616f59-c985-4696-839c-370ce4efd4b5] - Node 03616f59-c985-4696-839c-370ce4efd4b5[03616f59-c985-4696-839c-370ce4efd4b5] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:40:14.455 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:40:14.455 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:40:14.456 - [任务 3(100)][增强JS] - Node migrate_js_processor(增强JS: 24de22ff-e70f-48f1-a31d-fd04f4910417) enable batch process 
[INFO ] 2024-08-07 05:40:14.685 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] running status set to false 
[INFO ] 2024-08-07 05:40:14.696 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] running status set to false 
[INFO ] 2024-08-07 05:40:14.698 - [任务 3(100)][03616f59-c985-4696-839c-370ce4efd4b5] - Node 03616f59-c985-4696-839c-370ce4efd4b5[03616f59-c985-4696-839c-370ce4efd4b5] running status set to false 
[INFO ] 2024-08-07 05:40:14.698 - [任务 3(100)][Mysql] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf 
[INFO ] 2024-08-07 05:40:14.698 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] schema data cleaned 
[INFO ] 2024-08-07 05:40:14.698 - [任务 3(100)][03616f59-c985-4696-839c-370ce4efd4b5] - Node 03616f59-c985-4696-839c-370ce4efd4b5[03616f59-c985-4696-839c-370ce4efd4b5] schema data cleaned 
[INFO ] 2024-08-07 05:40:14.699 - [任务 3(100)][Mysql] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf 
[INFO ] 2024-08-07 05:40:14.699 - [任务 3(100)][03616f59-c985-4696-839c-370ce4efd4b5] - Node 03616f59-c985-4696-839c-370ce4efd4b5[03616f59-c985-4696-839c-370ce4efd4b5] monitor closed 
[INFO ] 2024-08-07 05:40:14.699 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] monitor closed 
[INFO ] 2024-08-07 05:40:14.699 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] schema data cleaned 
[INFO ] 2024-08-07 05:40:14.699 - [任务 3(100)][03616f59-c985-4696-839c-370ce4efd4b5] - Node 03616f59-c985-4696-839c-370ce4efd4b5[03616f59-c985-4696-839c-370ce4efd4b5] close complete, cost 9 ms 
[INFO ] 2024-08-07 05:40:14.699 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] close complete, cost 14 ms 
[INFO ] 2024-08-07 05:40:14.699 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] monitor closed 
[INFO ] 2024-08-07 05:40:14.701 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] close complete, cost 20 ms 
[INFO ] 2024-08-07 05:40:14.701 - [任务 3(100)] - Closed task monitor(s)
null 
[INFO ] 2024-08-07 05:40:14.701 - [任务 3(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-08-07 05:40:14.913 - [任务 3(100)] - Stopped task aspect(s) 
[INFO ] 2024-08-07 05:40:15.072 - [任务 3(100)][28cf9000-4d76-4e78-93c0-dd7e0f9845d5] - Node 28cf9000-4d76-4e78-93c0-dd7e0f9845d5[28cf9000-4d76-4e78-93c0-dd7e0f9845d5] start preload schema,table counts: 0 
[INFO ] 2024-08-07 05:40:15.072 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] start preload schema,table counts: 9 
[INFO ] 2024-08-07 05:40:15.072 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] start preload schema,table counts: 9 
[INFO ] 2024-08-07 05:40:15.082 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:40:15.083 - [任务 3(100)][28cf9000-4d76-4e78-93c0-dd7e0f9845d5] - Node 28cf9000-4d76-4e78-93c0-dd7e0f9845d5[28cf9000-4d76-4e78-93c0-dd7e0f9845d5] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:40:15.086 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:40:15.292 - [任务 3(100)][增强JS] - Node migrate_js_processor(增强JS: 24de22ff-e70f-48f1-a31d-fd04f4910417) enable batch process 
[INFO ] 2024-08-07 05:40:15.321 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] running status set to false 
[INFO ] 2024-08-07 05:40:15.327 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] running status set to false 
[INFO ] 2024-08-07 05:40:15.329 - [任务 3(100)][Mysql] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf 
[INFO ] 2024-08-07 05:40:15.330 - [任务 3(100)][28cf9000-4d76-4e78-93c0-dd7e0f9845d5] - Node 28cf9000-4d76-4e78-93c0-dd7e0f9845d5[28cf9000-4d76-4e78-93c0-dd7e0f9845d5] running status set to false 
[INFO ] 2024-08-07 05:40:15.330 - [任务 3(100)][Mysql] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf 
[INFO ] 2024-08-07 05:40:15.331 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] schema data cleaned 
[INFO ] 2024-08-07 05:40:15.331 - [任务 3(100)][28cf9000-4d76-4e78-93c0-dd7e0f9845d5] - Node 28cf9000-4d76-4e78-93c0-dd7e0f9845d5[28cf9000-4d76-4e78-93c0-dd7e0f9845d5] schema data cleaned 
[INFO ] 2024-08-07 05:40:15.332 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] schema data cleaned 
[INFO ] 2024-08-07 05:40:15.332 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] monitor closed 
[INFO ] 2024-08-07 05:40:15.332 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] monitor closed 
[INFO ] 2024-08-07 05:40:15.333 - [任务 3(100)][28cf9000-4d76-4e78-93c0-dd7e0f9845d5] - Node 28cf9000-4d76-4e78-93c0-dd7e0f9845d5[28cf9000-4d76-4e78-93c0-dd7e0f9845d5] monitor closed 
[INFO ] 2024-08-07 05:40:15.333 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] close complete, cost 23 ms 
[INFO ] 2024-08-07 05:40:15.333 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] close complete, cost 23 ms 
[INFO ] 2024-08-07 05:40:15.333 - [任务 3(100)][28cf9000-4d76-4e78-93c0-dd7e0f9845d5] - Node 28cf9000-4d76-4e78-93c0-dd7e0f9845d5[28cf9000-4d76-4e78-93c0-dd7e0f9845d5] close complete, cost 20 ms 
[INFO ] 2024-08-07 05:40:15.334 - [任务 3(100)] - Closed task monitor(s)
null 
[INFO ] 2024-08-07 05:40:15.334 - [任务 3(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-08-07 05:40:15.334 - [任务 3(100)] - Stopped task aspect(s) 
[INFO ] 2024-08-07 05:40:20.284 - [任务 3(100)][403a394b-9c3f-43f9-8dc0-b9ede107a662] - Node 403a394b-9c3f-43f9-8dc0-b9ede107a662[403a394b-9c3f-43f9-8dc0-b9ede107a662] start preload schema,table counts: 0 
[INFO ] 2024-08-07 05:40:20.285 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] start preload schema,table counts: 9 
[INFO ] 2024-08-07 05:40:20.285 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] start preload schema,table counts: 9 
[INFO ] 2024-08-07 05:40:20.285 - [任务 3(100)][403a394b-9c3f-43f9-8dc0-b9ede107a662] - Node 403a394b-9c3f-43f9-8dc0-b9ede107a662[403a394b-9c3f-43f9-8dc0-b9ede107a662] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:40:20.285 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:40:20.285 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] preload schema finished, cost 1 ms 
[INFO ] 2024-08-07 05:40:20.285 - [任务 3(100)][增强JS] - Node migrate_js_processor(增强JS: 24de22ff-e70f-48f1-a31d-fd04f4910417) enable batch process 
[INFO ] 2024-08-07 05:40:20.780 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] running status set to false 
[INFO ] 2024-08-07 05:40:20.790 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] running status set to false 
[INFO ] 2024-08-07 05:40:20.790 - [任务 3(100)][Mysql] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf 
[INFO ] 2024-08-07 05:40:20.791 - [任务 3(100)][403a394b-9c3f-43f9-8dc0-b9ede107a662] - Node 403a394b-9c3f-43f9-8dc0-b9ede107a662[403a394b-9c3f-43f9-8dc0-b9ede107a662] running status set to false 
[INFO ] 2024-08-07 05:40:20.791 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] schema data cleaned 
[INFO ] 2024-08-07 05:40:20.791 - [任务 3(100)][403a394b-9c3f-43f9-8dc0-b9ede107a662] - Node 403a394b-9c3f-43f9-8dc0-b9ede107a662[403a394b-9c3f-43f9-8dc0-b9ede107a662] schema data cleaned 
[INFO ] 2024-08-07 05:40:20.792 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] monitor closed 
[INFO ] 2024-08-07 05:40:20.792 - [任务 3(100)][Mysql] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf 
[INFO ] 2024-08-07 05:40:20.793 - [任务 3(100)][403a394b-9c3f-43f9-8dc0-b9ede107a662] - Node 403a394b-9c3f-43f9-8dc0-b9ede107a662[403a394b-9c3f-43f9-8dc0-b9ede107a662] monitor closed 
[INFO ] 2024-08-07 05:40:20.793 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] close complete, cost 5 ms 
[INFO ] 2024-08-07 05:40:20.793 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] schema data cleaned 
[INFO ] 2024-08-07 05:40:20.793 - [任务 3(100)][403a394b-9c3f-43f9-8dc0-b9ede107a662] - Node 403a394b-9c3f-43f9-8dc0-b9ede107a662[403a394b-9c3f-43f9-8dc0-b9ede107a662] close complete, cost 3 ms 
[INFO ] 2024-08-07 05:40:20.794 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] monitor closed 
[INFO ] 2024-08-07 05:40:20.794 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] close complete, cost 15 ms 
[INFO ] 2024-08-07 05:40:20.796 - [任务 3(100)] - Closed task monitor(s)
null 
[INFO ] 2024-08-07 05:40:20.798 - [任务 3(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-08-07 05:40:20.798 - [任务 3(100)] - Stopped task aspect(s) 
[INFO ] 2024-08-07 05:40:23.071 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] start preload schema,table counts: 9 
[INFO ] 2024-08-07 05:40:23.076 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] start preload schema,table counts: 9 
[INFO ] 2024-08-07 05:40:23.078 - [任务 3(100)][7eec1120-6092-4478-84b7-91d77829d199] - Node 7eec1120-6092-4478-84b7-91d77829d199[7eec1120-6092-4478-84b7-91d77829d199] start preload schema,table counts: 0 
[INFO ] 2024-08-07 05:40:23.078 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:40:23.078 - [任务 3(100)][7eec1120-6092-4478-84b7-91d77829d199] - Node 7eec1120-6092-4478-84b7-91d77829d199[7eec1120-6092-4478-84b7-91d77829d199] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:40:23.078 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:40:23.078 - [任务 3(100)][增强JS] - Node migrate_js_processor(增强JS: 24de22ff-e70f-48f1-a31d-fd04f4910417) enable batch process 
[INFO ] 2024-08-07 05:40:23.281 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] running status set to false 
[INFO ] 2024-08-07 05:40:23.289 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] running status set to false 
[INFO ] 2024-08-07 05:40:23.289 - [任务 3(100)][Mysql] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf 
[INFO ] 2024-08-07 05:40:23.289 - [任务 3(100)][7eec1120-6092-4478-84b7-91d77829d199] - Node 7eec1120-6092-4478-84b7-91d77829d199[7eec1120-6092-4478-84b7-91d77829d199] running status set to false 
[INFO ] 2024-08-07 05:40:23.289 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] schema data cleaned 
[INFO ] 2024-08-07 05:40:23.289 - [任务 3(100)][Mysql] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf 
[INFO ] 2024-08-07 05:40:23.290 - [任务 3(100)][7eec1120-6092-4478-84b7-91d77829d199] - Node 7eec1120-6092-4478-84b7-91d77829d199[7eec1120-6092-4478-84b7-91d77829d199] schema data cleaned 
[INFO ] 2024-08-07 05:40:23.290 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] monitor closed 
[INFO ] 2024-08-07 05:40:23.290 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] schema data cleaned 
[INFO ] 2024-08-07 05:40:23.290 - [任务 3(100)][7eec1120-6092-4478-84b7-91d77829d199] - Node 7eec1120-6092-4478-84b7-91d77829d199[7eec1120-6092-4478-84b7-91d77829d199] monitor closed 
[INFO ] 2024-08-07 05:40:23.291 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] close complete, cost 4 ms 
[INFO ] 2024-08-07 05:40:23.296 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] monitor closed 
[INFO ] 2024-08-07 05:40:23.296 - [任务 3(100)][7eec1120-6092-4478-84b7-91d77829d199] - Node 7eec1120-6092-4478-84b7-91d77829d199[7eec1120-6092-4478-84b7-91d77829d199] close complete, cost 2 ms 
[INFO ] 2024-08-07 05:40:23.298 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] close complete, cost 19 ms 
[INFO ] 2024-08-07 05:40:23.304 - [任务 3(100)] - Closed task monitor(s)
null 
[INFO ] 2024-08-07 05:40:23.305 - [任务 3(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-08-07 05:40:23.305 - [任务 3(100)] - Stopped task aspect(s) 
[INFO ] 2024-08-07 05:40:25.432 - [任务 3(100)][fd7854f2-fb67-4b2a-a660-8874fe84ce16] - Node fd7854f2-fb67-4b2a-a660-8874fe84ce16[fd7854f2-fb67-4b2a-a660-8874fe84ce16] start preload schema,table counts: 0 
[INFO ] 2024-08-07 05:40:25.433 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] start preload schema,table counts: 9 
[INFO ] 2024-08-07 05:40:25.433 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] start preload schema,table counts: 9 
[INFO ] 2024-08-07 05:40:25.433 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:40:25.433 - [任务 3(100)][fd7854f2-fb67-4b2a-a660-8874fe84ce16] - Node fd7854f2-fb67-4b2a-a660-8874fe84ce16[fd7854f2-fb67-4b2a-a660-8874fe84ce16] preload schema finished, cost 1 ms 
[INFO ] 2024-08-07 05:40:25.433 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] preload schema finished, cost 0 ms 
[INFO ] 2024-08-07 05:40:25.436 - [任务 3(100)][增强JS] - Node migrate_js_processor(增强JS: 24de22ff-e70f-48f1-a31d-fd04f4910417) enable batch process 
[INFO ] 2024-08-07 05:40:25.673 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] running status set to false 
[INFO ] 2024-08-07 05:40:25.673 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] running status set to false 
[INFO ] 2024-08-07 05:40:25.673 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] schema data cleaned 
[INFO ] 2024-08-07 05:40:25.673 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] monitor closed 
[INFO ] 2024-08-07 05:40:25.676 - [任务 3(100)][fd7854f2-fb67-4b2a-a660-8874fe84ce16] - Node fd7854f2-fb67-4b2a-a660-8874fe84ce16[fd7854f2-fb67-4b2a-a660-8874fe84ce16] running status set to false 
[INFO ] 2024-08-07 05:40:25.676 - [任务 3(100)][增强JS] - Node 增强JS[24de22ff-e70f-48f1-a31d-fd04f4910417] close complete, cost 5 ms 
[INFO ] 2024-08-07 05:40:25.676 - [任务 3(100)][fd7854f2-fb67-4b2a-a660-8874fe84ce16] - Node fd7854f2-fb67-4b2a-a660-8874fe84ce16[fd7854f2-fb67-4b2a-a660-8874fe84ce16] schema data cleaned 
[INFO ] 2024-08-07 05:40:25.681 - [任务 3(100)][fd7854f2-fb67-4b2a-a660-8874fe84ce16] - Node fd7854f2-fb67-4b2a-a660-8874fe84ce16[fd7854f2-fb67-4b2a-a660-8874fe84ce16] monitor closed 
[INFO ] 2024-08-07 05:40:25.687 - [任务 3(100)][fd7854f2-fb67-4b2a-a660-8874fe84ce16] - Node fd7854f2-fb67-4b2a-a660-8874fe84ce16[fd7854f2-fb67-4b2a-a660-8874fe84ce16] close complete, cost 6 ms 
[INFO ] 2024-08-07 05:40:25.688 - [任务 3(100)][Mysql] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf 
[INFO ] 2024-08-07 05:40:25.688 - [任务 3(100)][Mysql] - PDK connector node released: HazelcastSampleSourcePdkDataNode-2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf 
[INFO ] 2024-08-07 05:40:25.688 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] schema data cleaned 
[INFO ] 2024-08-07 05:40:25.689 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] monitor closed 
[INFO ] 2024-08-07 05:40:25.689 - [任务 3(100)][Mysql] - Node Mysql[2af7a9f3-78e2-47ce-bb4a-5f4f03c61fdf] close complete, cost 24 ms 
[INFO ] 2024-08-07 05:40:25.689 - [任务 3(100)] - Closed task monitor(s)
null 
[INFO ] 2024-08-07 05:40:25.690 - [任务 3(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-08-07 05:40:25.690 - [任务 3(100)] - Stopped task aspect(s) 
