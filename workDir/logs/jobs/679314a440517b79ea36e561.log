[TRACE] 2025-01-27 10:22:50.318 - [任务 10] - Task initialization... 
[TRACE] 2025-01-27 10:22:50.339 - [任务 10] - Start task milestones: 679314a440517b79ea36e561(任务 10) 
[INFO ] 2025-01-27 10:22:50.537 - [任务 10] - Loading table structure completed 
[TRACE] 2025-01-27 10:22:50.614 - [任务 10] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-01-27 10:22:50.683 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-01-27 10:22:50.683 - [任务 10] - Task started 
[TRACE] 2025-01-27 10:22:50.719 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] start preload schema,table counts: 2 
[TRACE] 2025-01-27 10:22:50.719 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] start preload schema,table counts: 2 
[TRACE] 2025-01-27 10:22:50.719 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] preload schema finished, cost 0 ms 
[TRACE] 2025-01-27 10:22:50.719 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] preload schema finished, cost 0 ms 
[INFO ] 2025-01-27 10:22:51.360 - [任务 10][COM_DB] - Source connector(COM_DB) initialization completed 
[TRACE] 2025-01-27 10:22:51.360 - [任务 10][COM_DB] - Source node "COM_DB" read batch size: 100 
[TRACE] 2025-01-27 10:22:51.361 - [任务 10][COM_DB] - Source node "COM_DB" event queue capacity: 200 
[TRACE] 2025-01-27 10:22:51.361 - [任务 10][COM_DB] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-01-27 10:22:51.439 - [任务 10][COM_DB] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-01-27 10:22:51.439 - [任务 10][COM_DB] - Starting batch read from 2 tables 
[TRACE] 2025-01-27 10:22:51.448 - [任务 10][COM_DB] - Initial sync started 
[INFO ] 2025-01-27 10:22:51.449 - [任务 10][COM_DB] - Starting batch read from table: ccc_big5 
[TRACE] 2025-01-27 10:22:51.449 - [任务 10][COM_DB] - Table ccc_big5 is going to be initial synced 
[INFO ] 2025-01-27 10:22:51.682 - [任务 10][COM_DB] - Table ccc_big5 has been completed batch read 
[INFO ] 2025-01-27 10:22:51.682 - [任务 10][COM_DB] - Starting batch read from table: ccc_big5_20170927 
[TRACE] 2025-01-27 10:22:51.682 - [任务 10][COM_DB] - Table ccc_big5_20170927 is going to be initial synced 
[INFO ] 2025-01-27 10:22:51.830 - [任务 10][wimPg] - Sink connector(wimPg) initialization completed 
[TRACE] 2025-01-27 10:22:51.830 - [任务 10][wimPg] - Node(wimPg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-01-27 10:22:51.830 - [任务 10][wimPg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-01-27 10:22:51.934 - [任务 10][wimPg] - Apply table structure to target database 
[TRACE] 2025-01-27 10:22:51.934 - [任务 10][COM_DB] - Query snapshot row size completed: COM_DB(85889a58-08e9-4937-a6a5-566f7d5e75e4) 
[INFO ] 2025-01-27 10:22:51.970 - [任务 10][COM_DB] - Table ccc_big5_20170927 has been completed batch read 
[TRACE] 2025-01-27 10:22:51.971 - [任务 10][COM_DB] - Initial sync completed 
[INFO ] 2025-01-27 10:22:51.971 - [任务 10][COM_DB] - Batch read completed. 
[INFO ] 2025-01-27 10:22:51.971 - [任务 10][COM_DB] - Task run completed 
[TRACE] 2025-01-27 10:25:25.705 - [任务 10][wimPg] - Table: ccc_big5 will create Index: TapIndex name ccc_big5_NEW_idx1 indexFields: [TapIndexField name big_five fieldAsc true indexType null; ] 
[TRACE] 2025-01-27 10:25:25.839 - [任务 10][wimPg] - Table: ccc_big5 create Index: ccc_big5_NEW_idx1 successfully, cost 1610ms 
[TRACE] 2025-01-27 10:25:25.840 - [任务 10][wimPg] - Table: ccc_big5 synchronize indexes completed, cost 28012ms totally 
[TRACE] 2025-01-27 10:26:40.047 - [任务 10][wimPg] - Table: ccc_big5_20170927 will create Index: TapIndex name ccc_big5_NEW_idx1 indexFields: [TapIndexField name big_five fieldAsc true indexType null; ] 
[TRACE] 2025-01-27 10:26:40.206 - [任务 10][wimPg] - Exception skipping - The current exception does not match the skip exception strategy, message: Table name: ccc_big5_20170927io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@6dc7d0a1: {"indexList":[{"cluster":false,"indexFields":[{"fieldAsc":true,"name":"big_five"}],"name":"ccc_big5_NEW_idx1","primary":false,"unique":false}],"tableId":"ccc_big5_20170927","time":1737944799994,"type":101}
 
[ERROR] 2025-01-27 10:26:40.208 - [任务 10][wimPg] - Table name: ccc_big5_20170927io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@6dc7d0a1: {"indexList":[{"cluster":false,"indexFields":[{"fieldAsc":true,"name":"big_five"}],"name":"ccc_big5_NEW_idx1","primary":false,"unique":false}],"tableId":"ccc_big5_20170927","time":1737944799994,"type":101}
 <-- Error Message -->
Table name: ccc_big5_20170927io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@6dc7d0a1: {"indexList":[{"cluster":false,"indexFields":[{"fieldAsc":true,"name":"big_five"}],"name":"ccc_big5_NEW_idx1","primary":false,"unique":false}],"tableId":"ccc_big5_20170927","time":1737944799994,"type":101}


<-- Simple Stack Trace -->
Caused by: org.postgresql.util.PSQLException: ERROR: relation "ccc_big5_NEW_idx1" already exists
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...

<-- Full Stack Trace -->
Table name: ccc_big5_20170927
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:424)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:231)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$3(HazelcastTargetPdkDataNode.java:174)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:160)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:111)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: org.postgresql.util.PSQLException: ERROR: relation "ccc_big5_NEW_idx1" already exists
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$syncIndex$15(HazelcastTargetPdkDataNode.java:412)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:408)
	... 19 more
Caused by: org.postgresql.util.PSQLException: ERROR: relation "ccc_big5_NEW_idx1" already exists
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:77)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$14(HazelcastTargetPdkDataNode.java:417)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 23 more
Caused by: org.postgresql.util.PSQLException: ERROR: relation "ccc_big5_NEW_idx1" already exists
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:329)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:315)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:291)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:286)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:416)
	at io.tapdata.connector.postgres.PostgresConnector.createIndex(PostgresConnector.java:806)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$13(HazelcastTargetPdkDataNode.java:419)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 30 more

[TRACE] 2025-01-27 10:26:40.317 - [任务 10][wimPg] - Job suspend in error handle 
[TRACE] 2025-01-27 10:26:40.318 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] running status set to false 
[TRACE] 2025-01-27 10:26:40.547 - [任务 10][COM_DB] - PDK connector node stopped: HazelcastSourcePdkDataNode_85889a58-08e9-4937-a6a5-566f7d5e75e4_1737944571242 
[TRACE] 2025-01-27 10:26:40.550 - [任务 10][COM_DB] - PDK connector node released: HazelcastSourcePdkDataNode_85889a58-08e9-4937-a6a5-566f7d5e75e4_1737944571242 
[TRACE] 2025-01-27 10:26:40.551 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] schema data cleaned 
[TRACE] 2025-01-27 10:26:40.551 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] monitor closed 
[TRACE] 2025-01-27 10:26:40.551 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] close complete, cost 241 ms 
[TRACE] 2025-01-27 10:26:40.551 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] running status set to false 
[TRACE] 2025-01-27 10:26:40.560 - [任务 10][wimPg] - PDK connector node stopped: HazelcastTargetPdkDataNode_ef5d7176-109d-4c29-8b16-e0d19fb47212_1737944571366 
[TRACE] 2025-01-27 10:26:40.560 - [任务 10][wimPg] - PDK connector node released: HazelcastTargetPdkDataNode_ef5d7176-109d-4c29-8b16-e0d19fb47212_1737944571366 
[TRACE] 2025-01-27 10:26:40.560 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] schema data cleaned 
[TRACE] 2025-01-27 10:26:40.560 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] monitor closed 
[TRACE] 2025-01-27 10:26:40.763 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] close complete, cost 10 ms 
[INFO ] 2025-01-27 10:26:44.973 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-01-27 10:26:44.974 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-01-27 10:26:44.974 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@e975571 
[TRACE] 2025-01-27 10:26:44.985 - [任务 10] - Stop task milestones: 679314a440517b79ea36e561(任务 10)  
[TRACE] 2025-01-27 10:26:45.115 - [任务 10] - Stopped task aspect(s) 
[TRACE] 2025-01-27 10:26:45.115 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2025-01-27 10:26:45.115 - [任务 10] - Task stopped. 
[TRACE] 2025-01-27 10:26:45.133 - [任务 10] - Remove memory task client succeed, task: 任务 10[679314a440517b79ea36e561] 
[TRACE] 2025-01-27 10:26:45.136 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[679314a440517b79ea36e561] 
[TRACE] 2025-01-27 12:07:45.539 - [任务 10] - Task initialization... 
[TRACE] 2025-01-27 12:07:45.553 - [任务 10] - Start task milestones: 679314a440517b79ea36e561(任务 10) 
[INFO ] 2025-01-27 12:07:45.724 - [任务 10] - Loading table structure completed 
[TRACE] 2025-01-27 12:07:45.888 - [任务 10] - Node performs snapshot read asynchronously 
[TRACE] 2025-01-27 12:07:45.890 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-01-27 12:07:45.959 - [任务 10] - Task started 
[TRACE] 2025-01-27 12:07:45.959 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] start preload schema,table counts: 2 
[TRACE] 2025-01-27 12:07:45.959 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] start preload schema,table counts: 2 
[TRACE] 2025-01-27 12:07:45.959 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] preload schema finished, cost 0 ms 
[TRACE] 2025-01-27 12:07:45.960 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] preload schema finished, cost 1 ms 
[INFO ] 2025-01-27 12:07:46.687 - [任务 10][COM_DB] - Source connector(COM_DB) initialization completed 
[TRACE] 2025-01-27 12:07:46.689 - [任务 10][COM_DB] - Source node "COM_DB" read batch size: 100 
[TRACE] 2025-01-27 12:07:46.690 - [任务 10][COM_DB] - Source node "COM_DB" event queue capacity: 200 
[TRACE] 2025-01-27 12:07:46.690 - [任务 10][COM_DB] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-01-27 12:07:46.690 - [任务 10][COM_DB] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-01-27 12:07:46.786 - [任务 10][COM_DB] - Starting batch read from 2 tables 
[TRACE] 2025-01-27 12:07:46.786 - [任务 10][COM_DB] - Initial sync started 
[INFO ] 2025-01-27 12:07:46.786 - [任务 10][COM_DB] - Starting batch read from table: ccc_big5 
[TRACE] 2025-01-27 12:07:46.949 - [任务 10][COM_DB] - Table ccc_big5 is going to be initial synced 
[INFO ] 2025-01-27 12:07:46.949 - [任务 10][wimPg] - Sink connector(wimPg) initialization completed 
[TRACE] 2025-01-27 12:07:46.949 - [任务 10][wimPg] - Node(wimPg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-01-27 12:07:46.949 - [任务 10][wimPg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-01-27 12:07:47.062 - [任务 10][wimPg] - Apply table structure to target database 
[INFO ] 2025-01-27 12:07:47.063 - [任务 10][COM_DB] - Table ccc_big5 has been completed batch read 
[INFO ] 2025-01-27 12:07:47.063 - [任务 10][COM_DB] - Starting batch read from table: ccc_big5_20170927 
[TRACE] 2025-01-27 12:07:47.063 - [任务 10][COM_DB] - Table ccc_big5_20170927 is going to be initial synced 
[INFO ] 2025-01-27 12:07:47.315 - [任务 10][COM_DB] - Table ccc_big5_20170927 has been completed batch read 
[TRACE] 2025-01-27 12:07:47.319 - [任务 10][COM_DB] - Initial sync completed 
[INFO ] 2025-01-27 12:07:47.319 - [任务 10][COM_DB] - Batch read completed. 
[INFO ] 2025-01-27 12:07:47.319 - [任务 10][COM_DB] - Task run completed 
[TRACE] 2025-01-27 12:07:47.534 - [任务 10][COM_DB] - Query snapshot row size completed: COM_DB(85889a58-08e9-4937-a6a5-566f7d5e75e4) 
[WARN ] 2025-01-27 12:07:48.498 - [任务 10][wimPg] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=6797069337418c00b8013134, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[85889a58-08e9-4937-a6a5-566f7d5e75e4], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-01-27 12:07:48.555 - [任务 10][wimPg] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=6797069337418c00b8013135, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[85889a58-08e9-4937-a6a5-566f7d5e75e4], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-01-27 12:07:48.557 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] running status set to false 
[TRACE] 2025-01-27 12:07:48.599 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] running status set to false 
[TRACE] 2025-01-27 12:07:48.599 - [任务 10][wimPg] - PDK connector node stopped: HazelcastTargetPdkDataNode_ef5d7176-109d-4c29-8b16-e0d19fb47212_1737950866492 
[TRACE] 2025-01-27 12:07:48.599 - [任务 10][wimPg] - PDK connector node released: HazelcastTargetPdkDataNode_ef5d7176-109d-4c29-8b16-e0d19fb47212_1737950866492 
[TRACE] 2025-01-27 12:07:48.599 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] schema data cleaned 
[TRACE] 2025-01-27 12:07:48.599 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] monitor closed 
[TRACE] 2025-01-27 12:07:48.805 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] close complete, cost 26 ms 
[TRACE] 2025-01-27 12:07:48.820 - [任务 10][COM_DB] - PDK connector node stopped: HazelcastSourcePdkDataNode_85889a58-08e9-4937-a6a5-566f7d5e75e4_1737950866617 
[TRACE] 2025-01-27 12:07:48.820 - [任务 10][COM_DB] - PDK connector node released: HazelcastSourcePdkDataNode_85889a58-08e9-4937-a6a5-566f7d5e75e4_1737950866617 
[TRACE] 2025-01-27 12:07:48.820 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] schema data cleaned 
[TRACE] 2025-01-27 12:07:48.821 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] monitor closed 
[TRACE] 2025-01-27 12:07:49.024 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] close complete, cost 280 ms 
[TRACE] 2025-01-27 12:07:51.046 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-01-27 12:07:51.061 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@52ccd534 
[TRACE] 2025-01-27 12:07:51.062 - [任务 10] - Stop task milestones: 679314a440517b79ea36e561(任务 10)  
[TRACE] 2025-01-27 12:07:51.193 - [任务 10] - Stopped task aspect(s) 
[TRACE] 2025-01-27 12:07:51.193 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2025-01-27 12:07:51.193 - [任务 10] - Task stopped. 
[TRACE] 2025-01-27 12:07:51.217 - [任务 10] - Remove memory task client succeed, task: 任务 10[679314a440517b79ea36e561] 
[TRACE] 2025-01-27 12:07:51.217 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[679314a440517b79ea36e561] 
[TRACE] 2025-01-27 12:09:12.100 - [任务 10] - Task initialization... 
[TRACE] 2025-01-27 12:09:12.111 - [任务 10] - Start task milestones: 679314a440517b79ea36e561(任务 10) 
[INFO ] 2025-01-27 12:09:12.293 - [任务 10] - Loading table structure completed 
[TRACE] 2025-01-27 12:09:12.293 - [任务 10] - Node performs snapshot read asynchronously 
[TRACE] 2025-01-27 12:09:12.447 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-01-27 12:09:12.447 - [任务 10] - Task started 
[TRACE] 2025-01-27 12:09:12.480 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] start preload schema,table counts: 1 
[TRACE] 2025-01-27 12:09:12.481 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] start preload schema,table counts: 1 
[TRACE] 2025-01-27 12:09:12.481 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] preload schema finished, cost 0 ms 
[TRACE] 2025-01-27 12:09:12.481 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] preload schema finished, cost 0 ms 
[INFO ] 2025-01-27 12:09:13.173 - [任务 10][COM_DB] - Source connector(COM_DB) initialization completed 
[TRACE] 2025-01-27 12:09:13.176 - [任务 10][COM_DB] - Source node "COM_DB" read batch size: 100 
[TRACE] 2025-01-27 12:09:13.176 - [任务 10][COM_DB] - Source node "COM_DB" event queue capacity: 200 
[TRACE] 2025-01-27 12:09:13.176 - [任务 10][COM_DB] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-01-27 12:09:13.176 - [任务 10][COM_DB] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-01-27 12:09:13.239 - [任务 10][COM_DB] - Starting batch read from 1 tables 
[TRACE] 2025-01-27 12:09:13.249 - [任务 10][COM_DB] - Initial sync started 
[INFO ] 2025-01-27 12:09:13.250 - [任务 10][COM_DB] - Starting batch read from table: ccc_big5_20170927 
[TRACE] 2025-01-27 12:09:13.446 - [任务 10][COM_DB] - Table ccc_big5_20170927 is going to be initial synced 
[INFO ] 2025-01-27 12:09:13.447 - [任务 10][wimPg] - Sink connector(wimPg) initialization completed 
[TRACE] 2025-01-27 12:09:13.447 - [任务 10][wimPg] - Node(wimPg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-01-27 12:09:13.447 - [任务 10][wimPg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-01-27 12:09:13.472 - [任务 10][wimPg] - Apply table structure to target database 
[TRACE] 2025-01-27 12:09:13.472 - [任务 10][COM_DB] - Query snapshot row size completed: COM_DB(85889a58-08e9-4937-a6a5-566f7d5e75e4) 
[INFO ] 2025-01-27 12:09:13.485 - [任务 10][COM_DB] - Table ccc_big5_20170927 has been completed batch read 
[TRACE] 2025-01-27 12:09:13.485 - [任务 10][COM_DB] - Initial sync completed 
[INFO ] 2025-01-27 12:09:13.485 - [任务 10][COM_DB] - Batch read completed. 
[INFO ] 2025-01-27 12:09:13.485 - [任务 10][COM_DB] - Task run completed 
[TRACE] 2025-01-27 12:09:14.063 - [任务 10][wimPg] - Table: ccc_big5_20170927 will create Index: TapIndex name ccc_big5_NEW_idx1 indexFields: [TapIndexField name big_five fieldAsc true indexType null; ] 
[TRACE] 2025-01-27 12:09:14.063 - [任务 10][wimPg] - Table: ccc_big5_20170927 create Index: ccc_big5_NEW_idx1 successfully, cost 100ms 
[TRACE] 2025-01-27 12:09:14.063 - [任务 10][wimPg] - Table: ccc_big5_20170927 synchronize indexes completed, cost 196ms totally 
[WARN ] 2025-01-27 12:09:14.958 - [任务 10][wimPg] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=679706e937418c00b8013140, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[85889a58-08e9-4937-a6a5-566f7d5e75e4], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-01-27 12:09:15.084 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] running status set to false 
[TRACE] 2025-01-27 12:09:15.088 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] running status set to false 
[TRACE] 2025-01-27 12:09:15.124 - [任务 10][wimPg] - PDK connector node stopped: HazelcastTargetPdkDataNode_ef5d7176-109d-4c29-8b16-e0d19fb47212_1737950953011 
[TRACE] 2025-01-27 12:09:15.124 - [任务 10][wimPg] - PDK connector node released: HazelcastTargetPdkDataNode_ef5d7176-109d-4c29-8b16-e0d19fb47212_1737950953011 
[TRACE] 2025-01-27 12:09:15.124 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] schema data cleaned 
[TRACE] 2025-01-27 12:09:15.128 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] monitor closed 
[TRACE] 2025-01-27 12:09:15.128 - [任务 10][wimPg] - Node wimPg[ef5d7176-109d-4c29-8b16-e0d19fb47212] close complete, cost 80 ms 
[TRACE] 2025-01-27 12:09:15.355 - [任务 10][COM_DB] - PDK connector node stopped: HazelcastSourcePdkDataNode_85889a58-08e9-4937-a6a5-566f7d5e75e4_1737950953111 
[TRACE] 2025-01-27 12:09:15.355 - [任务 10][COM_DB] - PDK connector node released: HazelcastSourcePdkDataNode_85889a58-08e9-4937-a6a5-566f7d5e75e4_1737950953111 
[TRACE] 2025-01-27 12:09:15.355 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] schema data cleaned 
[TRACE] 2025-01-27 12:09:15.355 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] monitor closed 
[TRACE] 2025-01-27 12:09:15.561 - [任务 10][COM_DB] - Node COM_DB[85889a58-08e9-4937-a6a5-566f7d5e75e4] close complete, cost 312 ms 
[TRACE] 2025-01-27 12:09:21.332 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-01-27 12:09:21.360 - [任务 10] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4801e08e 
[TRACE] 2025-01-27 12:09:21.362 - [任务 10] - Stop task milestones: 679314a440517b79ea36e561(任务 10)  
[TRACE] 2025-01-27 12:09:21.541 - [任务 10] - Stopped task aspect(s) 
[TRACE] 2025-01-27 12:09:21.541 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2025-01-27 12:09:21.562 - [任务 10] - Task stopped. 
[TRACE] 2025-01-27 12:09:21.565 - [任务 10] - Remove memory task client succeed, task: 任务 10[679314a440517b79ea36e561] 
[TRACE] 2025-01-27 12:09:21.565 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[679314a440517b79ea36e561] 
