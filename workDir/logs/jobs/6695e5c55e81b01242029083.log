[INFO ] 2024-07-26 13:16:49.862 - [Heartbeat-Mysql] - Start task milestones: 6695e5c55e81b01242029083(Heartbeat-Mysql) 
[INFO ] 2024-07-26 13:16:50.035 - [Heartbeat-Mysql] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 13:16:50.075 - [Heartbeat-Mysql] - The engine receives Heartbeat-Mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 13:16:50.402 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b5b215b5-b911-4a65-9ebc-9053e174202e] start preload schema,table counts: 1 
[INFO ] 2024-07-26 13:16:50.404 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b5b215b5-b911-4a65-9ebc-9053e174202e] preload schema finished, cost 1 ms 
[INFO ] 2024-07-26 13:16:50.408 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[61644a43-78e7-411a-8ae2-aa558d6db665] start preload schema,table counts: 1 
[INFO ] 2024-07-26 13:16:50.409 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[61644a43-78e7-411a-8ae2-aa558d6db665] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 13:16:50.720 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-26 13:16:50.720 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-26 13:16:50.720 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-26 13:16:50.725 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1721099737134,"lastTimes":1721904183315,"lastTN":3290,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":206744,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-26 13:16:50.725 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-26 13:16:50.779 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721099737134,"lastTimes":1721904183315,"lastTN":3290,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":206744,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-26 13:16:50.782 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-26 13:16:50.782 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 13:16:50.984 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-26 18:10:36.959 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[61644a43-78e7-411a-8ae2-aa558d6db665] running status set to false 
[INFO ] 2024-07-26 18:10:36.959 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-26 18:10:36.965 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-61644a43-78e7-411a-8ae2-aa558d6db665 
[INFO ] 2024-07-26 18:10:36.965 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-61644a43-78e7-411a-8ae2-aa558d6db665 
[INFO ] 2024-07-26 18:10:36.965 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[61644a43-78e7-411a-8ae2-aa558d6db665] schema data cleaned 
[INFO ] 2024-07-26 18:10:36.965 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[61644a43-78e7-411a-8ae2-aa558d6db665] monitor closed 
[INFO ] 2024-07-26 18:10:36.990 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[61644a43-78e7-411a-8ae2-aa558d6db665] close complete, cost 34 ms 
[INFO ] 2024-07-26 18:10:36.990 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b5b215b5-b911-4a65-9ebc-9053e174202e] running status set to false 
[INFO ] 2024-07-26 18:10:37.007 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-b5b215b5-b911-4a65-9ebc-9053e174202e 
[INFO ] 2024-07-26 18:10:37.007 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-b5b215b5-b911-4a65-9ebc-9053e174202e 
[INFO ] 2024-07-26 18:10:37.007 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b5b215b5-b911-4a65-9ebc-9053e174202e] schema data cleaned 
[INFO ] 2024-07-26 18:10:37.007 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b5b215b5-b911-4a65-9ebc-9053e174202e] monitor closed 
[INFO ] 2024-07-26 18:10:37.208 - [Heartbeat-Mysql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b5b215b5-b911-4a65-9ebc-9053e174202e] close complete, cost 17 ms 
[INFO ] 2024-07-26 18:10:47.222 - [Heartbeat-Mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:10:47.223 - [Heartbeat-Mysql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2ff7f10b 
[INFO ] 2024-07-26 18:10:47.223 - [Heartbeat-Mysql] - Stop task milestones: 6695e5c55e81b01242029083(Heartbeat-Mysql)  
[INFO ] 2024-07-26 18:10:47.340 - [Heartbeat-Mysql] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:10:47.340 - [Heartbeat-Mysql] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 18:10:47.365 - [Heartbeat-Mysql] - Remove memory task client succeed, task: Heartbeat-Mysql[6695e5c55e81b01242029083] 
[INFO ] 2024-07-26 18:10:47.368 - [Heartbeat-Mysql] - Destroy memory task client cache succeed, task: Heartbeat-Mysql[6695e5c55e81b01242029083] 
