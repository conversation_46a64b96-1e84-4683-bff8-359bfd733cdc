[INFO ] 2024-07-26 14:23:20.721 - [任务 34] - Task initialization... 
[INFO ] 2024-07-26 14:23:20.722 - [任务 34] - Start task milestones: 66a340478191787d1e5aef4b(任务 34) 
[INFO ] 2024-07-26 14:23:21.042 - [任务 34] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 14:23:21.044 - [任务 34] - The engine receives 任务 34 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 14:23:21.084 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] start preload schema,table counts: 1 
[INFO ] 2024-07-26 14:23:21.084 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] start preload schema,table counts: 1 
[INFO ] 2024-07-26 14:23:21.084 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 14:23:21.084 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 14:23:21.899 - [任务 34][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-26 14:23:21.899 - [任务 34][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-26 14:23:21.899 - [任务 34][CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 14:23:22.053 - [任务 34][CUSTOMER] - batch offset found: {},stream offset found: {"cdcOffset":1721975001,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 14:23:22.126 - [任务 34][CUSTOMER] - Initial sync started 
[INFO ] 2024-07-26 14:23:22.127 - [任务 34][CUSTOMER] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-26 14:23:22.208 - [任务 34][CUSTOMER] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-26 14:23:22.208 - [任务 34][CUSTOMER] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 14:23:22.208 - [任务 34][CUSTOMER] - Query table 'CUSTOMER' counts: 676 
[INFO ] 2024-07-26 14:23:22.412 - [任务 34][Customer] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 14:23:22.672 - [任务 34][Customer] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: Customer 
[ERROR] 2024-07-26 14:23:22.673 - [任务 34][Customer] - Unknown exception occur when operate table: Customer <-- Error Message -->
Unknown exception occur when operate table: Customer

<-- Simple Stack Trace -->
Caused by: java.lang.Throwable: Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Create Table Customer Failed! ClickHouse exception, code: 69, host: **************, port: 18123; Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createTable$7(HazelcastTargetPdkBaseNode.java:298)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:292)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:203)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:149)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Create Table Customer Failed! ClickHouse exception, code: 69, host: **************, port: 18123; Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at io.tapdata.connector.clickhouse.ClickhouseConnector.createTableV2(ClickhouseConnector.java:285)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$6(HazelcastTargetPdkBaseNode.java:300)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 28 more
Caused by: ru.yandex.clickhouse.except.ClickHouseException: ClickHouse exception, code: 69, host: **************, port: 18123; Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:58)
	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:100)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:95)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:90)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.execute(ClickHouseStatementImpl.java:226)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.connector.clickhouse.ClickhouseConnector.createTableV2(ClickhouseConnector.java:282)
	... 30 more
Caused by: java.lang.Throwable: Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	... 42 more

[INFO ] 2024-07-26 14:23:22.718 - [任务 34][Customer] - Job suspend in error handle 
[INFO ] 2024-07-26 14:23:22.720 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] running status set to false 
[INFO ] 2024-07-26 14:23:22.720 - [任务 34][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 14:23:22.720 - [任务 34][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-26 14:23:22.720 - [任务 34][CUSTOMER] - Incremental sync completed 
[INFO ] 2024-07-26 14:23:22.758 - [任务 34][CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-47318a23-0a44-4a56-8a67-9ecc0d715a5f 
[INFO ] 2024-07-26 14:23:22.758 - [任务 34][CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-47318a23-0a44-4a56-8a67-9ecc0d715a5f 
[INFO ] 2024-07-26 14:23:22.758 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] schema data cleaned 
[INFO ] 2024-07-26 14:23:22.758 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] monitor closed 
[INFO ] 2024-07-26 14:23:22.760 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] close complete, cost 63 ms 
[INFO ] 2024-07-26 14:23:22.760 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] running status set to false 
[INFO ] 2024-07-26 14:23:22.761 - [任务 34][Customer] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-26 14:23:22.761 - [任务 34][Customer] - Clickhouse Optimize Table end 
[INFO ] 2024-07-26 14:23:22.782 - [任务 34][Customer] - PDK connector node stopped: HazelcastTargetPdkDataNode-e0f767ef-b7c2-4f9d-8501-ab13c27aee4a 
[INFO ] 2024-07-26 14:23:22.782 - [任务 34][Customer] - PDK connector node released: HazelcastTargetPdkDataNode-e0f767ef-b7c2-4f9d-8501-ab13c27aee4a 
[INFO ] 2024-07-26 14:23:22.782 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] schema data cleaned 
[INFO ] 2024-07-26 14:23:22.782 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] monitor closed 
[INFO ] 2024-07-26 14:23:22.856 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] close complete, cost 22 ms 
[INFO ] 2024-07-26 14:23:22.856 - [任务 34] - Task [任务 34] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-26 14:23:22.860 - [任务 34] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 14:23:22.860 - [任务 34] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7468826a 
[INFO ] 2024-07-26 14:23:22.860 - [任务 34] - Stop task milestones: 66a340478191787d1e5aef4b(任务 34)  
[INFO ] 2024-07-26 14:23:22.985 - [任务 34] - Stopped task aspect(s) 
[INFO ] 2024-07-26 14:23:22.985 - [任务 34] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 14:23:23.016 - [任务 34] - Remove memory task client succeed, task: 任务 34[66a340478191787d1e5aef4b] 
[INFO ] 2024-07-26 14:23:23.023 - [任务 34] - Destroy memory task client cache succeed, task: 任务 34[66a340478191787d1e5aef4b] 
[INFO ] 2024-07-26 15:09:09.282 - [任务 34] - Task initialization... 
[INFO ] 2024-07-26 15:09:09.283 - [任务 34] - Start task milestones: 66a340478191787d1e5aef4b(任务 34) 
[INFO ] 2024-07-26 15:09:09.489 - [任务 34] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 15:09:09.552 - [任务 34] - The engine receives 任务 34 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 15:09:09.555 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] start preload schema,table counts: 1 
[INFO ] 2024-07-26 15:09:09.555 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] start preload schema,table counts: 1 
[INFO ] 2024-07-26 15:09:09.555 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] preload schema finished, cost 1 ms 
[INFO ] 2024-07-26 15:09:09.555 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] preload schema finished, cost 1 ms 
[INFO ] 2024-07-26 15:09:10.616 - [任务 34][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-26 15:09:10.616 - [任务 34][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-26 15:09:10.616 - [任务 34][CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 15:09:10.765 - [任务 34][CUSTOMER] - batch offset found: {},stream offset found: {"cdcOffset":1721977750,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 15:09:10.858 - [任务 34][CUSTOMER] - Initial sync started 
[INFO ] 2024-07-26 15:09:10.859 - [任务 34][CUSTOMER] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-26 15:09:10.859 - [任务 34][CUSTOMER] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-26 15:09:10.892 - [任务 34][CUSTOMER] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 15:09:10.892 - [任务 34][CUSTOMER] - Query table 'CUSTOMER' counts: 676 
[INFO ] 2024-07-26 15:09:11.094 - [任务 34][Customer] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 15:11:56.900 - [任务 34][Customer] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: Customer 
[ERROR] 2024-07-26 15:11:56.947 - [任务 34][Customer] - Unknown exception occur when operate table: Customer <-- Error Message -->
Unknown exception occur when operate table: Customer

<-- Simple Stack Trace -->
Caused by: java.lang.Throwable: Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Create Table Customer Failed! ClickHouse exception, code: 69, host: **************, port: 18123; Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createTable$7(HazelcastTargetPdkBaseNode.java:298)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:292)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:203)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:149)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Create Table Customer Failed! ClickHouse exception, code: 69, host: **************, port: 18123; Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at io.tapdata.connector.clickhouse.ClickhouseConnector.createTableV2(ClickhouseConnector.java:285)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$6(HazelcastTargetPdkBaseNode.java:300)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 28 more
Caused by: ru.yandex.clickhouse.except.ClickHouseException: ClickHouse exception, code: 69, host: **************, port: 18123; Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:58)
	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:100)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:95)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:90)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.execute(ClickHouseStatementImpl.java:226)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.connector.clickhouse.ClickhouseConnector.createTableV2(ClickhouseConnector.java:282)
	... 30 more
Caused by: java.lang.Throwable: Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	... 42 more

[INFO ] 2024-07-26 15:11:56.953 - [任务 34][Customer] - Job suspend in error handle 
[INFO ] 2024-07-26 15:11:56.957 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] running status set to false 
[INFO ] 2024-07-26 15:11:56.957 - [任务 34][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 15:11:56.957 - [任务 34][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-26 15:11:56.957 - [任务 34][CUSTOMER] - Incremental sync completed 
[INFO ] 2024-07-26 15:11:57.005 - [任务 34][CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-47318a23-0a44-4a56-8a67-9ecc0d715a5f 
[INFO ] 2024-07-26 15:11:57.005 - [任务 34][CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-47318a23-0a44-4a56-8a67-9ecc0d715a5f 
[INFO ] 2024-07-26 15:11:57.005 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] schema data cleaned 
[INFO ] 2024-07-26 15:11:57.006 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] monitor closed 
[INFO ] 2024-07-26 15:11:57.008 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] close complete, cost 58 ms 
[INFO ] 2024-07-26 15:11:57.008 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] running status set to false 
[INFO ] 2024-07-26 15:11:57.008 - [任务 34][Customer] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-26 15:11:57.008 - [任务 34][Customer] - Clickhouse Optimize Table end 
[INFO ] 2024-07-26 15:11:57.039 - [任务 34][Customer] - PDK connector node stopped: HazelcastTargetPdkDataNode-e0f767ef-b7c2-4f9d-8501-ab13c27aee4a 
[INFO ] 2024-07-26 15:11:57.040 - [任务 34][Customer] - PDK connector node released: HazelcastTargetPdkDataNode-e0f767ef-b7c2-4f9d-8501-ab13c27aee4a 
[INFO ] 2024-07-26 15:11:57.040 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] schema data cleaned 
[INFO ] 2024-07-26 15:11:57.040 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] monitor closed 
[INFO ] 2024-07-26 15:11:57.040 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] close complete, cost 32 ms 
[INFO ] 2024-07-26 15:12:01.219 - [任务 34] - Task [任务 34] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-26 15:12:01.219 - [任务 34] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 15:12:01.219 - [任务 34] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@74358341 
[INFO ] 2024-07-26 15:12:01.366 - [任务 34] - Stop task milestones: 66a340478191787d1e5aef4b(任务 34)  
[INFO ] 2024-07-26 15:12:01.366 - [任务 34] - Stopped task aspect(s) 
[INFO ] 2024-07-26 15:12:01.366 - [任务 34] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 15:12:01.420 - [任务 34] - Remove memory task client succeed, task: 任务 34[66a340478191787d1e5aef4b] 
[INFO ] 2024-07-26 15:12:01.423 - [任务 34] - Destroy memory task client cache succeed, task: 任务 34[66a340478191787d1e5aef4b] 
[INFO ] 2024-07-26 15:13:47.515 - [任务 34] - Task initialization... 
[INFO ] 2024-07-26 15:13:47.516 - [任务 34] - Start task milestones: 66a340478191787d1e5aef4b(任务 34) 
[INFO ] 2024-07-26 15:13:47.768 - [任务 34] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 15:13:47.846 - [任务 34] - The engine receives 任务 34 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 15:13:47.847 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] start preload schema,table counts: 1 
[INFO ] 2024-07-26 15:13:47.848 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] start preload schema,table counts: 1 
[INFO ] 2024-07-26 15:13:47.848 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] start preload schema,table counts: 1 
[INFO ] 2024-07-26 15:13:47.850 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 15:13:47.850 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 15:13:48.051 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 15:13:48.484 - [任务 34][Customer] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 15:13:48.555 - [任务 34][Customer] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: Customer 
[ERROR] 2024-07-26 15:13:48.556 - [任务 34][Customer] - Unknown exception occur when operate table: Customer <-- Error Message -->
Unknown exception occur when operate table: Customer

<-- Simple Stack Trace -->
Caused by: java.lang.Throwable: Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Create Table Customer Failed! ClickHouse exception, code: 69, host: **************, port: 18123; Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createTable$7(HazelcastTargetPdkBaseNode.java:298)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:292)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:203)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:149)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Create Table Customer Failed! ClickHouse exception, code: 69, host: **************, port: 18123; Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at io.tapdata.connector.clickhouse.ClickhouseConnector.createTableV2(ClickhouseConnector.java:285)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$6(HazelcastTargetPdkBaseNode.java:300)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 28 more
Caused by: ru.yandex.clickhouse.except.ClickHouseException: ClickHouse exception, code: 69, host: **************, port: 18123; Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:58)
	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:100)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:95)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:90)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.execute(ClickHouseStatementImpl.java:226)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.connector.clickhouse.ClickhouseConnector.createTableV2(ClickhouseConnector.java:282)
	... 30 more
Caused by: java.lang.Throwable: Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	... 42 more

[INFO ] 2024-07-26 15:13:48.609 - [任务 34][Customer] - Job suspend in error handle 
[INFO ] 2024-07-26 15:13:48.609 - [任务 34][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-26 15:13:48.609 - [任务 34][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-26 15:13:48.609 - [任务 34][CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 15:13:48.737 - [任务 34][CUSTOMER] - batch offset found: {},stream offset found: {"cdcOffset":1721978028,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 15:13:48.815 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] running status set to false 
[INFO ] 2024-07-26 15:13:48.815 - [任务 34][CUSTOMER] - Initial sync started 
[INFO ] 2024-07-26 15:13:48.815 - [任务 34][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 15:13:48.816 - [任务 34][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-26 15:13:48.844 - [任务 34][CUSTOMER] - Incremental sync completed 
[INFO ] 2024-07-26 15:13:48.844 - [任务 34][CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-47318a23-0a44-4a56-8a67-9ecc0d715a5f 
[INFO ] 2024-07-26 15:13:48.844 - [任务 34][CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-47318a23-0a44-4a56-8a67-9ecc0d715a5f 
[INFO ] 2024-07-26 15:13:48.844 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] schema data cleaned 
[INFO ] 2024-07-26 15:13:48.845 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] monitor closed 
[INFO ] 2024-07-26 15:13:48.846 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] close complete, cost 32 ms 
[INFO ] 2024-07-26 15:13:48.847 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] running status set to false 
[INFO ] 2024-07-26 15:13:48.954 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] schema data cleaned 
[INFO ] 2024-07-26 15:13:48.954 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] monitor closed 
[INFO ] 2024-07-26 15:13:48.957 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] close complete, cost 109 ms 
[INFO ] 2024-07-26 15:13:48.958 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] running status set to false 
[INFO ] 2024-07-26 15:13:48.958 - [任务 34][Customer] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-26 15:13:48.993 - [任务 34][Customer] - Clickhouse Optimize Table end 
[INFO ] 2024-07-26 15:13:48.993 - [任务 34][Customer] - PDK connector node stopped: HazelcastTargetPdkDataNode-e0f767ef-b7c2-4f9d-8501-ab13c27aee4a 
[INFO ] 2024-07-26 15:13:48.993 - [任务 34][Customer] - PDK connector node released: HazelcastTargetPdkDataNode-e0f767ef-b7c2-4f9d-8501-ab13c27aee4a 
[INFO ] 2024-07-26 15:13:48.993 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] schema data cleaned 
[INFO ] 2024-07-26 15:13:48.994 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] monitor closed 
[INFO ] 2024-07-26 15:13:48.995 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] close complete, cost 37 ms 
[INFO ] 2024-07-26 15:13:51.762 - [任务 34] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 15:13:51.762 - [任务 34] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2e848174 
[INFO ] 2024-07-26 15:13:51.764 - [任务 34] - Stop task milestones: 66a340478191787d1e5aef4b(任务 34)  
[INFO ] 2024-07-26 15:13:51.898 - [任务 34] - Stopped task aspect(s) 
[INFO ] 2024-07-26 15:13:51.898 - [任务 34] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 15:13:51.916 - [任务 34] - Remove memory task client succeed, task: 任务 34[66a340478191787d1e5aef4b] 
[INFO ] 2024-07-26 15:13:51.918 - [任务 34] - Destroy memory task client cache succeed, task: 任务 34[66a340478191787d1e5aef4b] 
[INFO ] 2024-07-26 15:14:45.523 - [任务 34] - Task initialization... 
[INFO ] 2024-07-26 15:14:45.524 - [任务 34] - Start task milestones: 66a340478191787d1e5aef4b(任务 34) 
[INFO ] 2024-07-26 15:14:45.707 - [任务 34] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 15:14:45.707 - [任务 34] - The engine receives 任务 34 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 15:14:45.777 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] start preload schema,table counts: 1 
[INFO ] 2024-07-26 15:14:45.777 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] start preload schema,table counts: 1 
[INFO ] 2024-07-26 15:14:45.777 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] start preload schema,table counts: 1 
[INFO ] 2024-07-26 15:14:45.778 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 15:14:45.778 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 15:14:45.778 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 15:14:46.464 - [任务 34][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-26 15:14:46.464 - [任务 34][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-26 15:14:46.464 - [任务 34][CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 15:14:46.585 - [任务 34][CUSTOMER] - batch offset found: {},stream offset found: {"cdcOffset":1721978086,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 15:14:46.643 - [任务 34][CUSTOMER] - Initial sync started 
[INFO ] 2024-07-26 15:14:46.643 - [任务 34][CUSTOMER] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-26 15:14:46.687 - [任务 34][CUSTOMER] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-26 15:14:46.687 - [任务 34][CUSTOMER] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 15:14:46.876 - [任务 34][CUSTOMER] - Query table 'CUSTOMER' counts: 676 
[INFO ] 2024-07-26 15:14:46.876 - [任务 34][Customer] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 15:14:47.234 - [任务 34][Customer] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: Customer 
[ERROR] 2024-07-26 15:14:47.250 - [任务 34][Customer] - Unknown exception occur when operate table: Customer <-- Error Message -->
Unknown exception occur when operate table: Customer

<-- Simple Stack Trace -->
Caused by: java.lang.Throwable: Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Create Table Customer Failed! ClickHouse exception, code: 69, host: **************, port: 18123; Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createTable$7(HazelcastTargetPdkBaseNode.java:298)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:292)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:203)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:149)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Create Table Customer Failed! ClickHouse exception, code: 69, host: **************, port: 18123; Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at io.tapdata.connector.clickhouse.ClickhouseConnector.createTableV2(ClickhouseConnector.java:285)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$6(HazelcastTargetPdkBaseNode.java:300)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 28 more
Caused by: ru.yandex.clickhouse.except.ClickHouseException: ClickHouse exception, code: 69, host: **************, port: 18123; Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:58)
	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:100)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:95)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:90)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.execute(ClickHouseStatementImpl.java:226)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.connector.clickhouse.ClickhouseConnector.createTableV2(ClickhouseConnector.java:282)
	... 30 more
Caused by: java.lang.Throwable: Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	... 42 more

[INFO ] 2024-07-26 15:14:47.251 - [任务 34][Customer] - Job suspend in error handle 
[INFO ] 2024-07-26 15:14:47.261 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] running status set to false 
[INFO ] 2024-07-26 15:14:47.261 - [任务 34][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 15:14:47.261 - [任务 34][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-26 15:14:47.261 - [任务 34][CUSTOMER] - Incremental sync completed 
[INFO ] 2024-07-26 15:14:47.289 - [任务 34][CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-47318a23-0a44-4a56-8a67-9ecc0d715a5f 
[INFO ] 2024-07-26 15:14:47.289 - [任务 34][CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-47318a23-0a44-4a56-8a67-9ecc0d715a5f 
[INFO ] 2024-07-26 15:14:47.289 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] schema data cleaned 
[INFO ] 2024-07-26 15:14:47.289 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] monitor closed 
[INFO ] 2024-07-26 15:14:47.290 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] close complete, cost 31 ms 
[INFO ] 2024-07-26 15:14:47.290 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] running status set to false 
[INFO ] 2024-07-26 15:14:47.336 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] schema data cleaned 
[INFO ] 2024-07-26 15:14:47.336 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] monitor closed 
[INFO ] 2024-07-26 15:14:47.337 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] close complete, cost 46 ms 
[INFO ] 2024-07-26 15:14:47.337 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] running status set to false 
[INFO ] 2024-07-26 15:14:47.337 - [任务 34][Customer] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-26 15:14:47.337 - [任务 34][Customer] - Clickhouse Optimize Table end 
[INFO ] 2024-07-26 15:14:47.359 - [任务 34][Customer] - PDK connector node stopped: HazelcastTargetPdkDataNode-e0f767ef-b7c2-4f9d-8501-ab13c27aee4a 
[INFO ] 2024-07-26 15:14:47.359 - [任务 34][Customer] - PDK connector node released: HazelcastTargetPdkDataNode-e0f767ef-b7c2-4f9d-8501-ab13c27aee4a 
[INFO ] 2024-07-26 15:14:47.359 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] schema data cleaned 
[INFO ] 2024-07-26 15:14:47.359 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] monitor closed 
[INFO ] 2024-07-26 15:14:47.359 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] close complete, cost 22 ms 
[INFO ] 2024-07-26 15:14:52.215 - [任务 34] - Task [任务 34] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-26 15:14:52.215 - [任务 34] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 15:14:52.215 - [任务 34] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4a485b41 
[INFO ] 2024-07-26 15:14:52.217 - [任务 34] - Stop task milestones: 66a340478191787d1e5aef4b(任务 34)  
[INFO ] 2024-07-26 15:14:52.347 - [任务 34] - Stopped task aspect(s) 
[INFO ] 2024-07-26 15:14:52.347 - [任务 34] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 15:14:52.371 - [任务 34] - Remove memory task client succeed, task: 任务 34[66a340478191787d1e5aef4b] 
[INFO ] 2024-07-26 15:14:52.371 - [任务 34] - Destroy memory task client cache succeed, task: 任务 34[66a340478191787d1e5aef4b] 
[INFO ] 2024-07-26 15:15:09.155 - [任务 34] - Task initialization... 
[INFO ] 2024-07-26 15:15:09.156 - [任务 34] - Start task milestones: 66a340478191787d1e5aef4b(任务 34) 
[INFO ] 2024-07-26 15:15:09.356 - [任务 34] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 15:15:09.356 - [任务 34] - The engine receives 任务 34 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 15:15:09.418 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] start preload schema,table counts: 1 
[INFO ] 2024-07-26 15:15:09.418 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] start preload schema,table counts: 1 
[INFO ] 2024-07-26 15:15:09.419 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] start preload schema,table counts: 1 
[INFO ] 2024-07-26 15:15:09.419 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 15:15:09.419 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 15:15:09.419 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 15:15:10.260 - [任务 34][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-26 15:15:10.260 - [任务 34][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-26 15:15:10.260 - [任务 34][CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 15:15:16.977 - [任务 34][Customer] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 15:15:17.305 - [任务 34][CUSTOMER] - batch offset found: {},stream offset found: {"cdcOffset":1721978117,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 15:15:17.308 - [任务 34][CUSTOMER] - Initial sync started 
[INFO ] 2024-07-26 15:15:17.308 - [任务 34][CUSTOMER] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-26 15:15:17.310 - [任务 34][CUSTOMER] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-26 15:15:44.526 - [任务 34][CUSTOMER] - Query table 'CUSTOMER' counts: 676 
[INFO ] 2024-07-26 15:15:44.567 - [任务 34][CUSTOMER] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 15:15:44.570 - [任务 34][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 15:15:44.570 - [任务 34][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-26 15:15:44.570 - [任务 34][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 15:15:44.773 - [任务 34][Customer] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: Customer 
[ERROR] 2024-07-26 15:15:44.811 - [任务 34][Customer] - Unknown exception occur when operate table: Customer <-- Error Message -->
Unknown exception occur when operate table: Customer

<-- Simple Stack Trace -->
Caused by: java.lang.Throwable: Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Create Table Customer Failed! ClickHouse exception, code: 69, host: **************, port: 18123; Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createTable$7(HazelcastTargetPdkBaseNode.java:298)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:292)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:203)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:149)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Create Table Customer Failed! ClickHouse exception, code: 69, host: **************, port: 18123; Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at io.tapdata.connector.clickhouse.ClickhouseConnector.createTableV2(ClickhouseConnector.java:285)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$6(HazelcastTargetPdkBaseNode.java:300)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 28 more
Caused by: ru.yandex.clickhouse.except.ClickHouseException: ClickHouse exception, code: 69, host: **************, port: 18123; Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:58)
	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:100)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:95)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:90)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.execute(ClickHouseStatementImpl.java:226)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.connector.clickhouse.ClickhouseConnector.createTableV2(ClickhouseConnector.java:282)
	... 30 more
Caused by: java.lang.Throwable: Code: 69, e.displayText() = DB::Exception: Wrong precision (version ******** (official build))

	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	... 42 more

[INFO ] 2024-07-26 15:15:44.811 - [任务 34][Customer] - Job suspend in error handle 
[INFO ] 2024-07-26 15:15:44.835 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] running status set to false 
[INFO ] 2024-07-26 15:15:44.836 - [任务 34][CUSTOMER] - Incremental sync completed 
[INFO ] 2024-07-26 15:15:44.865 - [任务 34][CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-47318a23-0a44-4a56-8a67-9ecc0d715a5f 
[INFO ] 2024-07-26 15:15:44.868 - [任务 34][CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-47318a23-0a44-4a56-8a67-9ecc0d715a5f 
[INFO ] 2024-07-26 15:15:44.868 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] schema data cleaned 
[INFO ] 2024-07-26 15:15:44.868 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] monitor closed 
[INFO ] 2024-07-26 15:15:44.868 - [任务 34][CUSTOMER] - Node CUSTOMER[47318a23-0a44-4a56-8a67-9ecc0d715a5f] close complete, cost 41 ms 
[INFO ] 2024-07-26 15:15:44.868 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] running status set to false 
[INFO ] 2024-07-26 15:15:44.995 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] schema data cleaned 
[INFO ] 2024-07-26 15:15:44.999 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] monitor closed 
[INFO ] 2024-07-26 15:15:45.000 - [任务 34][增删字段] - Node 增删字段[bcaff70c-4260-41ef-92a0-274d086c6916] close complete, cost 130 ms 
[INFO ] 2024-07-26 15:15:45.000 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] running status set to false 
[INFO ] 2024-07-26 15:15:45.002 - [任务 34][Customer] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-26 15:15:45.002 - [任务 34][Customer] - Clickhouse Optimize Table end 
[INFO ] 2024-07-26 15:15:45.046 - [任务 34][Customer] - PDK connector node stopped: HazelcastTargetPdkDataNode-e0f767ef-b7c2-4f9d-8501-ab13c27aee4a 
[INFO ] 2024-07-26 15:15:45.046 - [任务 34][Customer] - PDK connector node released: HazelcastTargetPdkDataNode-e0f767ef-b7c2-4f9d-8501-ab13c27aee4a 
[INFO ] 2024-07-26 15:15:45.046 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] schema data cleaned 
[INFO ] 2024-07-26 15:15:45.046 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] monitor closed 
[INFO ] 2024-07-26 15:15:45.048 - [任务 34][Customer] - Node Customer[e0f767ef-b7c2-4f9d-8501-ab13c27aee4a] close complete, cost 49 ms 
[INFO ] 2024-07-26 15:15:49.215 - [任务 34] - Task [任务 34] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-26 15:15:49.215 - [任务 34] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 15:15:49.218 - [任务 34] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4bfe5bc4 
[INFO ] 2024-07-26 15:15:49.218 - [任务 34] - Stop task milestones: 66a340478191787d1e5aef4b(任务 34)  
[INFO ] 2024-07-26 15:15:49.357 - [任务 34] - Stopped task aspect(s) 
[INFO ] 2024-07-26 15:15:49.357 - [任务 34] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 15:15:49.375 - [任务 34] - Remove memory task client succeed, task: 任务 34[66a340478191787d1e5aef4b] 
[INFO ] 2024-07-26 15:15:49.377 - [任务 34] - Destroy memory task client cache succeed, task: 任务 34[66a340478191787d1e5aef4b] 
