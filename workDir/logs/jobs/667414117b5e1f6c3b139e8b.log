[INFO ] 2024-06-21 10:58:02.848 - [任务 1] - Task initialization... 
[INFO ] 2024-06-21 10:58:03.072 - [任务 1] - Start task milestones: 667414117b5e1f6c3b139e8b(任务 1) 
[INFO ] 2024-06-21 10:58:04.294 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-21 10:58:04.392 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-21 10:58:05.036 - [任务 1][testReference] - Node testReference[0cdbdf9c-194d-4fb1-97e2-952fb9be5840] start preload schema,table counts: 1 
[INFO ] 2024-06-21 10:58:05.037 - [任务 1][testReference] - Node testReference[0cdbdf9c-194d-4fb1-97e2-952fb9be5840] preload schema finished, cost 1 ms 
[INFO ] 2024-06-21 10:58:05.057 - [任务 1][dummy_test] - Node dummy_test[d329a686-c851-4679-8d64-0f7569ba6f98] start preload schema,table counts: 1 
[INFO ] 2024-06-21 10:58:05.057 - [任务 1][dummy_test] - Node dummy_test[d329a686-c851-4679-8d64-0f7569ba6f98] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 10:58:05.734 - [任务 1][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-21 10:58:06.194 - [任务 1][testReference] - Source node "testReference" read batch size: 100 
[INFO ] 2024-06-21 10:58:06.200 - [任务 1][testReference] - Source node "testReference" event queue capacity: 200 
[INFO ] 2024-06-21 10:58:06.203 - [任务 1][testReference] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-21 10:58:06.227 - [任务 1][testReference] - batch offset found: {},stream offset found: {"name":"a1a9f5c7-165b-4e50-a547-48e3bbc45326","offset":{"{\"server\":\"a1a9f5c7-165b-4e50-a547-48e3bbc45326\"}":"{\"ts_sec\":1718884058,\"file\":\"binlog.000031\",\"pos\":1057953529,\"server_id\":1}"}} 
[INFO ] 2024-06-21 10:58:06.349 - [任务 1][testReference] - Incremental sync starting... 
[INFO ] 2024-06-21 10:58:06.349 - [任务 1][testReference] - Initial sync completed 
[INFO ] 2024-06-21 10:58:06.389 - [任务 1][testReference] - Starting stream read, table list: [testReference], offset: {"name":"a1a9f5c7-165b-4e50-a547-48e3bbc45326","offset":{"{\"server\":\"a1a9f5c7-165b-4e50-a547-48e3bbc45326\"}":"{\"ts_sec\":1718884058,\"file\":\"binlog.000031\",\"pos\":1057953529,\"server_id\":1}"}} 
[INFO ] 2024-06-21 10:58:06.390 - [任务 1][testReference] - Starting mysql cdc, server name: a1a9f5c7-165b-4e50-a547-48e3bbc45326 
[INFO ] 2024-06-21 10:58:06.602 - [任务 1][testReference] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 456121699
  time.precision.mode: adaptive_time_microseconds
  database.server.name: a1a9f5c7-165b-4e50-a547-48e3bbc45326
  database.port: 3306
  threadName: Debezium-Mysql-Connector-a1a9f5c7-165b-4e50-a547-48e3bbc45326
  database.hostname: localhost
  database.password: ********
  name: a1a9f5c7-165b-4e50-a547-48e3bbc45326
  pdk.offset.string: {"name":"a1a9f5c7-165b-4e50-a547-48e3bbc45326","offset":{"{\"server\":\"a1a9f5c7-165b-4e50-a547-48e3bbc45326\"}":"{\"ts_sec\":1718884058,\"file\":\"binlog.000031\",\"pos\":1057953529,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testReference
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-21 10:58:07.150 - [任务 1][testReference] - Connector Mysql incremental start succeed, tables: [testReference], data change syncing 
[INFO ] 2024-06-21 11:18:35.971 - [任务 1] - Stop task milestones: 667414117b5e1f6c3b139e8b(任务 1)  
[INFO ] 2024-06-21 11:18:36.172 - [任务 1][testReference] - Node testReference[0cdbdf9c-194d-4fb1-97e2-952fb9be5840] running status set to false 
[INFO ] 2024-06-21 11:18:36.287 - [任务 1][testReference] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-21 11:18:36.288 - [任务 1][testReference] - Mysql binlog reader stopped 
[INFO ] 2024-06-21 11:18:36.316 - [任务 1][testReference] - PDK connector node stopped: HazelcastSourcePdkDataNode-0cdbdf9c-194d-4fb1-97e2-952fb9be5840 
[INFO ] 2024-06-21 11:18:36.317 - [任务 1][testReference] - PDK connector node released: HazelcastSourcePdkDataNode-0cdbdf9c-194d-4fb1-97e2-952fb9be5840 
[INFO ] 2024-06-21 11:18:36.319 - [任务 1][testReference] - Node testReference[0cdbdf9c-194d-4fb1-97e2-952fb9be5840] schema data cleaned 
[INFO ] 2024-06-21 11:18:36.319 - [任务 1][testReference] - Node testReference[0cdbdf9c-194d-4fb1-97e2-952fb9be5840] monitor closed 
[INFO ] 2024-06-21 11:18:36.326 - [任务 1][testReference] - Node testReference[0cdbdf9c-194d-4fb1-97e2-952fb9be5840] close complete, cost 156 ms 
[INFO ] 2024-06-21 11:18:36.326 - [任务 1][dummy_test] - Node dummy_test[d329a686-c851-4679-8d64-0f7569ba6f98] running status set to false 
[INFO ] 2024-06-21 11:18:36.379 - [任务 1][dummy_test] - Stop connector 
[INFO ] 2024-06-21 11:18:36.405 - [任务 1][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-d329a686-c851-4679-8d64-0f7569ba6f98 
[INFO ] 2024-06-21 11:18:36.405 - [任务 1][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-d329a686-c851-4679-8d64-0f7569ba6f98 
[INFO ] 2024-06-21 11:18:36.408 - [任务 1][dummy_test] - Node dummy_test[d329a686-c851-4679-8d64-0f7569ba6f98] schema data cleaned 
[INFO ] 2024-06-21 11:18:36.408 - [任务 1][dummy_test] - Node dummy_test[d329a686-c851-4679-8d64-0f7569ba6f98] monitor closed 
[INFO ] 2024-06-21 11:18:36.614 - [任务 1][dummy_test] - Node dummy_test[d329a686-c851-4679-8d64-0f7569ba6f98] close complete, cost 84 ms 
[INFO ] 2024-06-21 11:18:39.841 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-21 11:18:39.841 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-06-21 11:18:39.842 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-06-21 11:18:39.911 - [任务 1] - Remove memory task client succeed, task: 任务 1[667414117b5e1f6c3b139e8b] 
[INFO ] 2024-06-21 11:18:39.912 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[667414117b5e1f6c3b139e8b] 
