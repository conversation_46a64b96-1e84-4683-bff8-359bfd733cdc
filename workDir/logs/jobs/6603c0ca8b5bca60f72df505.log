[INFO ] 2024-03-27 14:47:18.498 - [任务 24] - Task initialization... 
[INFO ] 2024-03-27 14:47:18.502 - [任务 24] - Start task milestones: 6603c0ca8b5bca60f72df505(任务 24) 
[INFO ] 2024-03-27 14:47:18.503 - [任务 24] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 14:47:18.504 - [任务 24] - The engine receives 任务 24 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 14:47:18.504 - [任务 24][Test1] - Node Test1[6d0790e2-0cf7-42bc-b750-3465dbd06824] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:47:18.504 - [任务 24][CLAIM] - Node CLAIM[00ac92b3-6971-4ed1-a5fa-850062e3b37d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:47:18.565 - [任务 24][CLAIM] - Node CLAIM[00ac92b3-6971-4ed1-a5fa-850062e3b37d] preload schema finished, cost 72 ms 
[INFO ] 2024-03-27 14:47:18.569 - [任务 24][Test1] - Node Test1[6d0790e2-0cf7-42bc-b750-3465dbd06824] preload schema finished, cost 95 ms 
[INFO ] 2024-03-27 14:47:19.229 - [任务 24][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 14:47:19.231 - [任务 24][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 14:47:19.231 - [任务 24][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 14:47:19.250 - [任务 24][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145161794,"gtidSet":""} 
[INFO ] 2024-03-27 14:47:19.252 - [任务 24][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-03-27 14:47:19.310 - [任务 24][CLAIM] - Initial sync started 
[INFO ] 2024-03-27 14:47:19.312 - [任务 24][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-27 14:47:19.317 - [任务 24][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-27 14:47:19.373 - [任务 24][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-27 14:47:19.407 - [任务 24][Test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 14:47:19.554 - [任务 24][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 14:47:19.556 - [任务 24][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 14:47:19.556 - [任务 24][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 14:47:19.558 - [任务 24][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":145161794,"gtidSet":""} 
[INFO ] 2024-03-27 14:47:19.597 - [任务 24][CLAIM] - Starting mysql cdc, server name: 278131d5-fd8d-4e85-a739-3020873b33cf 
[INFO ] 2024-03-27 14:47:19.603 - [任务 24][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 241630907
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 278131d5-fd8d-4e85-a739-3020873b33cf
  database.port: 3306
  threadName: Debezium-Mysql-Connector-278131d5-fd8d-4e85-a739-3020873b33cf
  database.hostname: 127.0.0.1
  database.password: ********
  name: 278131d5-fd8d-4e85-a739-3020873b33cf
  pdk.offset.string: {"name":"278131d5-fd8d-4e85-a739-3020873b33cf","offset":{"{\"server\":\"278131d5-fd8d-4e85-a739-3020873b33cf\"}":"{\"file\":\"binlog.000020\",\"pos\":145161794,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 14:47:19.676 - [任务 24][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 14:48:41.256 - [任务 24][CLAIM] - Read DDL: alter table CLAIM rename column `name` to `name1`, about to be packaged as some event(s) 
[INFO ] 2024-03-27 14:48:41.261 - [任务 24][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldNameEvent
  - Offset: MysqlStreamOffset{name='278131d5-fd8d-4e85-a739-3020873b33cf', offset={{"server":"278131d5-fd8d-4e85-a739-3020873b33cf"}={"ts_sec":1711522120,"file":"binlog.000020","pos":145162055,"server_id":1}}} 
[INFO ] 2024-03-27 14:48:41.261 - [任务 24][CLAIM] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='CLAIM', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@5aa85530} 
[WARN ] 2024-03-27 14:48:41.263 - [任务 24][CLAIM] - DDL events are filtered
 - Event: TapAlterFieldNameEvent{tableId='CLAIM', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@5aa85530}
 - Filter: {} 
[INFO ] 2024-03-27 14:48:54.038 - [任务 24] - Stop task milestones: 6603c0ca8b5bca60f72df505(任务 24)  
[INFO ] 2024-03-27 14:48:54.471 - [任务 24][CLAIM] - Node CLAIM[00ac92b3-6971-4ed1-a5fa-850062e3b37d] running status set to false 
[INFO ] 2024-03-27 14:48:54.482 - [任务 24][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-27 14:48:54.483 - [任务 24][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-27 14:48:54.498 - [任务 24][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-00ac92b3-6971-4ed1-a5fa-850062e3b37d 
[INFO ] 2024-03-27 14:48:54.498 - [任务 24][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-00ac92b3-6971-4ed1-a5fa-850062e3b37d 
[INFO ] 2024-03-27 14:48:54.498 - [任务 24][CLAIM] - Node CLAIM[00ac92b3-6971-4ed1-a5fa-850062e3b37d] schema data cleaned 
[INFO ] 2024-03-27 14:48:54.498 - [任务 24][CLAIM] - Node CLAIM[00ac92b3-6971-4ed1-a5fa-850062e3b37d] monitor closed 
[INFO ] 2024-03-27 14:48:54.498 - [任务 24][CLAIM] - Node CLAIM[00ac92b3-6971-4ed1-a5fa-850062e3b37d] close complete, cost 31 ms 
[INFO ] 2024-03-27 14:48:54.498 - [任务 24][Test1] - Node Test1[6d0790e2-0cf7-42bc-b750-3465dbd06824] running status set to false 
[INFO ] 2024-03-27 14:48:54.513 - [任务 24][Test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-6d0790e2-0cf7-42bc-b750-3465dbd06824 
[INFO ] 2024-03-27 14:48:54.513 - [任务 24][Test1] - PDK connector node released: HazelcastTargetPdkDataNode-6d0790e2-0cf7-42bc-b750-3465dbd06824 
[INFO ] 2024-03-27 14:48:54.513 - [任务 24][Test1] - Node Test1[6d0790e2-0cf7-42bc-b750-3465dbd06824] schema data cleaned 
[INFO ] 2024-03-27 14:48:54.514 - [任务 24][Test1] - Node Test1[6d0790e2-0cf7-42bc-b750-3465dbd06824] monitor closed 
[INFO ] 2024-03-27 14:48:54.514 - [任务 24][Test1] - Node Test1[6d0790e2-0cf7-42bc-b750-3465dbd06824] close complete, cost 16 ms 
[INFO ] 2024-03-27 14:48:58.544 - [任务 24] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 14:48:58.547 - [任务 24] - Stopped task aspect(s) 
[INFO ] 2024-03-27 14:48:58.549 - [任务 24] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 14:48:58.573 - [任务 24] - Remove memory task client succeed, task: 任务 24[6603c0ca8b5bca60f72df505] 
[INFO ] 2024-03-27 14:48:58.573 - [任务 24] - Destroy memory task client cache succeed, task: 任务 24[6603c0ca8b5bca60f72df505] 
[INFO ] 2024-03-27 14:49:11.897 - [任务 24] - Task initialization... 
[INFO ] 2024-03-27 14:49:11.898 - [任务 24] - Start task milestones: 6603c0ca8b5bca60f72df505(任务 24) 
[INFO ] 2024-03-27 14:49:11.916 - [任务 24] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 14:49:11.986 - [任务 24] - The engine receives 任务 24 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 14:49:12.051 - [任务 24][CLAIM] - Node CLAIM[00ac92b3-6971-4ed1-a5fa-850062e3b37d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:49:12.051 - [任务 24][Test1] - Node Test1[6d0790e2-0cf7-42bc-b750-3465dbd06824] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:49:12.089 - [任务 24][CLAIM] - Node CLAIM[00ac92b3-6971-4ed1-a5fa-850062e3b37d] preload schema finished, cost 32 ms 
[INFO ] 2024-03-27 14:49:12.095 - [任务 24][Test1] - Node Test1[6d0790e2-0cf7-42bc-b750-3465dbd06824] preload schema finished, cost 31 ms 
[INFO ] 2024-03-27 14:49:12.367 - [任务 24][Test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 14:49:12.495 - [任务 24][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 14:49:12.495 - [任务 24][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 14:49:12.495 - [任务 24][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-27 14:49:12.498 - [任务 24][CLAIM] - batch offset found: {"CLAIM":{}},stream offset found: {"name":"278131d5-fd8d-4e85-a739-3020873b33cf","offset":{"{\"server\":\"278131d5-fd8d-4e85-a739-3020873b33cf\"}":"{\"ts_sec\":1711522039,\"file\":\"binlog.000020\",\"pos\":145161794,\"server_id\":1}"}} 
[INFO ] 2024-03-27 14:49:12.499 - [任务 24][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-03-27 14:49:12.538 - [任务 24][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 14:49:12.539 - [任务 24][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 14:49:12.543 - [任务 24][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"278131d5-fd8d-4e85-a739-3020873b33cf","offset":{"{\"server\":\"278131d5-fd8d-4e85-a739-3020873b33cf\"}":"{\"ts_sec\":1711522039,\"file\":\"binlog.000020\",\"pos\":145161794,\"server_id\":1}"}} 
[INFO ] 2024-03-27 14:49:12.554 - [任务 24][CLAIM] - Starting mysql cdc, server name: 278131d5-fd8d-4e85-a739-3020873b33cf 
[INFO ] 2024-03-27 14:49:12.559 - [任务 24][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 86179621
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 278131d5-fd8d-4e85-a739-3020873b33cf
  database.port: 3306
  threadName: Debezium-Mysql-Connector-278131d5-fd8d-4e85-a739-3020873b33cf
  database.hostname: 127.0.0.1
  database.password: ********
  name: 278131d5-fd8d-4e85-a739-3020873b33cf
  pdk.offset.string: {"name":"278131d5-fd8d-4e85-a739-3020873b33cf","offset":{"{\"server\":\"278131d5-fd8d-4e85-a739-3020873b33cf\"}":"{\"ts_sec\":1711522039,\"file\":\"binlog.000020\",\"pos\":145161794,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 14:49:12.598 - [任务 24][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 14:49:13.097 - [任务 24][CLAIM] - Read DDL: alter table CLAIM rename column `name` to `name1`, about to be packaged as some event(s) 
[INFO ] 2024-03-27 14:49:13.097 - [任务 24][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldNameEvent
  - Offset: MysqlStreamOffset{name='278131d5-fd8d-4e85-a739-3020873b33cf', offset={{"server":"278131d5-fd8d-4e85-a739-3020873b33cf"}={"ts_sec":1711522152,"file":"binlog.000020","pos":145162055,"server_id":1}}} 
[INFO ] 2024-03-27 14:49:13.098 - [任务 24][CLAIM] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='CLAIM', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@73f151e1} 
[INFO ] 2024-03-27 15:10:55.004 - [任务 24][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_6603c0ca8b5bca60f72df505 
[INFO ] 2024-03-27 15:11:31.996 - [任务 24][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 15:42:36.441 - [任务 24][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 15:46:40.582 - [任务 24][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_6603c0ca8b5bca60f72df505 
[INFO ] 2024-03-27 15:47:40.872 - [任务 24][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 16:15:06.602 - [任务 24][CLAIM] - Read DDL: alter table CLAIM add column name varchar(50), about to be packaged as some event(s) 
[INFO ] 2024-03-27 16:15:06.603 - [任务 24][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='278131d5-fd8d-4e85-a739-3020873b33cf', offset={{"server":"278131d5-fd8d-4e85-a739-3020873b33cf"}={"ts_sec":1711527306,"file":"binlog.000020","pos":145162560,"server_id":1}}} 
[INFO ] 2024-03-27 16:15:06.603 - [任务 24][CLAIM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@6fc9d394: {"newFields":[{"autoInc":false,"dataType":"varchar(50)","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false}],"referenceTime":1711527306111,"tableId":"CLAIM","time":1711527306597,"type":209} 
[INFO ] 2024-03-27 16:15:06.609 - [任务 24][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_6603c0ca8b5bca60f72df505 
[INFO ] 2024-03-27 16:15:06.631 - [任务 24][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 16:17:59.666 - [任务 24][CLAIM] - Read DDL: alter table CLAIM rename column `name` to `name1`, about to be packaged as some event(s) 
[INFO ] 2024-03-27 16:17:59.677 - [任务 24][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldNameEvent
  - Offset: MysqlStreamOffset{name='278131d5-fd8d-4e85-a739-3020873b33cf', offset={{"server":"278131d5-fd8d-4e85-a739-3020873b33cf"}={"ts_sec":1711527414,"file":"binlog.000020","pos":145162821,"server_id":1}}} 
[INFO ] 2024-03-27 16:18:06.523 - [任务 24][CLAIM] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='CLAIM', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@2a3f2c32} 
[INFO ] 2024-03-27 16:18:06.523 - [任务 24][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_6603c0ca8b5bca60f72df505 
[INFO ] 2024-03-27 16:18:06.733 - [任务 24][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 16:21:14.767 - [任务 24][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-27 16:21:14.767 - [任务 24][CLAIM] - Incremental sync completed 
[ERROR] 2024-03-27 16:21:14.767 - [任务 24][CLAIM] - java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms <-- Error Message -->
java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms

<-- Simple Stack Trace -->
Caused by: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:986)
	com.github.shyiko.mysql.binlog.BinaryLogClient$SpawnKeepAliveThread.run(BinaryLogClient.java:907)
	java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	java.util.concurrent.FutureTask.run(FutureTask.java)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:142)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:333)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:458)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	at com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:986)
	at com.github.shyiko.mysql.binlog.BinaryLogClient$SpawnKeepAliveThread.run(BinaryLogClient.java:907)
	... 6 more

[INFO ] 2024-03-27 16:21:14.767 - [任务 24][CLAIM] - Job suspend in error handle 
[INFO ] 2024-03-27 16:21:15.115 - [任务 24][CLAIM] - Node CLAIM[00ac92b3-6971-4ed1-a5fa-850062e3b37d] running status set to false 
[INFO ] 2024-03-27 16:21:15.141 - [任务 24][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-00ac92b3-6971-4ed1-a5fa-850062e3b37d 
[INFO ] 2024-03-27 16:21:15.141 - [任务 24][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-00ac92b3-6971-4ed1-a5fa-850062e3b37d 
[INFO ] 2024-03-27 16:21:15.141 - [任务 24][CLAIM] - Node CLAIM[00ac92b3-6971-4ed1-a5fa-850062e3b37d] schema data cleaned 
[INFO ] 2024-03-27 16:21:15.141 - [任务 24][CLAIM] - Node CLAIM[00ac92b3-6971-4ed1-a5fa-850062e3b37d] monitor closed 
[INFO ] 2024-03-27 16:21:15.143 - [任务 24][CLAIM] - Node CLAIM[00ac92b3-6971-4ed1-a5fa-850062e3b37d] close complete, cost 33 ms 
[INFO ] 2024-03-27 16:21:15.143 - [任务 24][Test1] - Node Test1[6d0790e2-0cf7-42bc-b750-3465dbd06824] running status set to false 
[INFO ] 2024-03-27 16:21:15.157 - [任务 24][Test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-6d0790e2-0cf7-42bc-b750-3465dbd06824 
[INFO ] 2024-03-27 16:21:15.158 - [任务 24][Test1] - PDK connector node released: HazelcastTargetPdkDataNode-6d0790e2-0cf7-42bc-b750-3465dbd06824 
[INFO ] 2024-03-27 16:21:15.158 - [任务 24][Test1] - Node Test1[6d0790e2-0cf7-42bc-b750-3465dbd06824] schema data cleaned 
[INFO ] 2024-03-27 16:21:15.159 - [任务 24][Test1] - Node Test1[6d0790e2-0cf7-42bc-b750-3465dbd06824] monitor closed 
[INFO ] 2024-03-27 16:21:15.159 - [任务 24][Test1] - Node Test1[6d0790e2-0cf7-42bc-b750-3465dbd06824] close complete, cost 16 ms 
[INFO ] 2024-03-27 16:21:19.147 - [任务 24] - Task [任务 24] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-27 16:21:19.150 - [任务 24] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 16:21:19.157 - [任务 24] - Stop task milestones: 6603c0ca8b5bca60f72df505(任务 24)  
[INFO ] 2024-03-27 16:21:19.173 - [任务 24] - Stopped task aspect(s) 
[INFO ] 2024-03-27 16:21:19.173 - [任务 24] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 16:21:19.209 - [任务 24] - Remove memory task client succeed, task: 任务 24[6603c0ca8b5bca60f72df505] 
[INFO ] 2024-03-27 16:21:19.210 - [任务 24] - Destroy memory task client cache succeed, task: 任务 24[6603c0ca8b5bca60f72df505] 
