[INFO ] 2024-10-21 10:24:07.433 - [任务 4] - Task initialization... 
[INFO ] 2024-10-21 10:24:07.435 - [任务 4] - Start task milestones: 6715bb308d7fb11f2d81215d(任务 4) 
[INFO ] 2024-10-21 10:24:07.845 - [任务 4] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-10-21 10:24:07.845 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-21 10:24:07.918 - [任务 4][TestMysql] - Node TestMysql[cb349ac5-9ebd-4870-9793-b37f59b651bb] start preload schema,table counts: 1 
[INFO ] 2024-10-21 10:24:07.919 - [任务 4][Mysql3307] - Node Mysql3307[1b6f72c0-e016-48ba-bd97-5d01405f7ce5] start preload schema,table counts: 1 
[INFO ] 2024-10-21 10:24:07.919 - [任务 4][TestMysql] - Node TestMysql[cb349ac5-9ebd-4870-9793-b37f59b651bb] preload schema finished, cost 0 ms 
[INFO ] 2024-10-21 10:24:07.919 - [任务 4][Mysql3307] - Node Mysql3307[1b6f72c0-e016-48ba-bd97-5d01405f7ce5] preload schema finished, cost 0 ms 
[INFO ] 2024-10-21 10:24:08.791 - [任务 4][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-21 10:24:08.798 - [任务 4][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-21 10:24:08.824 - [任务 4][Mysql3307] - Table "test2.TESTPer" exists, skip auto create table 
[INFO ] 2024-10-21 10:24:08.957 - [任务 4][Mysql3307] - The table TESTPer has already exist. 
[INFO ] 2024-10-21 10:24:08.958 - [任务 4][TestMysql] - Source node "TestMysql" read batch size: 100 
[INFO ] 2024-10-21 10:24:08.958 - [任务 4][TestMysql] - Source node "TestMysql" event queue capacity: 200 
[INFO ] 2024-10-21 10:24:08.958 - [任务 4][TestMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-21 10:24:08.962 - [任务 4][TestMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":938080,"gtidSet":""} 
[INFO ] 2024-10-21 10:24:08.963 - [任务 4][TestMysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-21 10:24:09.017 - [任务 4][TestMysql] - Initial sync started 
[INFO ] 2024-10-21 10:24:09.018 - [任务 4][TestMysql] - Starting batch read, table name: TESTPer 
[INFO ] 2024-10-21 10:24:09.222 - [任务 4][TestMysql] - Table TESTPer is going to be initial synced 
[INFO ] 2024-10-21 10:24:09.224 - [任务 4][TestMysql] - Query table 'TESTPer' counts: 5000000 
[INFO ] 2024-10-21 10:25:00.253 - [任务 4][Mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'ID' in 'field list'
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@75f11ff2: {"after":{"CREATED":"2024-05-16 11:50:37.000000","ID":1864800,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729477500175,"type":300}, nodeIds=[cb349ac5-9ebd-4870-9793-b37f59b651bb], sourceTime=1729477448958, sourceSerialNo=null} 
[ERROR] 2024-10-21 10:25:00.280 - [任务 4][Mysql3307] - java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'ID' in 'field list'
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@75f11ff2: {"after":{"CREATED":"2024-05-16 11:50:37.000000","ID":1864800,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729477500175,"type":300}, nodeIds=[cb349ac5-9ebd-4870-9793-b37f59b651bb], sourceTime=1729477448958, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'ID' in 'field list'
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@75f11ff2: {"after":{"CREATED":"2024-05-16 11:50:37.000000","ID":1864800,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729477500175,"type":300}, nodeIds=[cb349ac5-9ebd-4870-9793-b37f59b651bb], sourceTime=1729477448958, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'ID' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'ID' in 'field list'
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:663)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:623)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:516)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:595)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:647)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:594)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'ID' in 'field list'
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:816)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$26(HazelcastTargetPdkDataNode.java:516)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:516)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:781)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:714)
	... 12 more
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'ID' in 'field list'
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:128)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:457)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:871)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:867)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$59(HazelcastTargetPdkDataNode.java:822)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'ID' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:94)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:108)
	... 30 more

[INFO ] 2024-10-21 10:25:00.282 - [任务 4][Mysql3307] - Job suspend in error handle 
[INFO ] 2024-10-21 10:25:00.488 - [任务 4] - Task [任务 4] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-10-21 10:25:01.238 - [任务 4][TestMysql] - Node TestMysql[cb349ac5-9ebd-4870-9793-b37f59b651bb] running status set to false 
[INFO ] 2024-10-21 10:25:01.277 - [任务 4][TestMysql] - Initial sync completed 
[INFO ] 2024-10-21 10:25:01.277 - [任务 4][TestMysql] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:68)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 
[INFO ] 2024-10-21 10:25:01.278 - [任务 4][TestMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-cb349ac5-9ebd-4870-9793-b37f59b651bb 
[INFO ] 2024-10-21 10:25:01.278 - [任务 4][TestMysql] - PDK connector node released: HazelcastSourcePdkDataNode-cb349ac5-9ebd-4870-9793-b37f59b651bb 
[INFO ] 2024-10-21 10:25:01.278 - [任务 4][TestMysql] - Node TestMysql[cb349ac5-9ebd-4870-9793-b37f59b651bb] schema data cleaned 
[INFO ] 2024-10-21 10:25:01.282 - [任务 4][TestMysql] - Node TestMysql[cb349ac5-9ebd-4870-9793-b37f59b651bb] monitor closed 
[INFO ] 2024-10-21 10:25:01.282 - [任务 4][TestMysql] - Node TestMysql[cb349ac5-9ebd-4870-9793-b37f59b651bb] close complete, cost 63 ms 
[INFO ] 2024-10-21 10:25:01.288 - [任务 4][Mysql3307] - Node Mysql3307[1b6f72c0-e016-48ba-bd97-5d01405f7ce5] running status set to false 
[ERROR] 2024-10-21 10:25:01.310 - [任务 4][TestMysql] - java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:68)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 <-- Error Message -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:68)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **



<-- Simple Stack Trace -->
Caused by: java.io.IOException: Stream closed.
	java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	java.net.SocketInputStream.available(SocketInputStream.java:259)
	com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:68)
	com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:68)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.handleThrowable(HazelcastSourcePdkDataNode.java:354)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:323)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:68)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	... 10 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:68)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 13 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:68)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:213)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 19 more
Caused by: java.sql.SQLException: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:68)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:131)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1952)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	... 23 more
Caused by: java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:68)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	... 26 more

[INFO ] 2024-10-21 10:25:01.321 - [任务 4][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-1b6f72c0-e016-48ba-bd97-5d01405f7ce5 
[INFO ] 2024-10-21 10:25:01.321 - [任务 4][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-1b6f72c0-e016-48ba-bd97-5d01405f7ce5 
[INFO ] 2024-10-21 10:25:01.321 - [任务 4][Mysql3307] - Node Mysql3307[1b6f72c0-e016-48ba-bd97-5d01405f7ce5] schema data cleaned 
[INFO ] 2024-10-21 10:25:01.321 - [任务 4][Mysql3307] - Node Mysql3307[1b6f72c0-e016-48ba-bd97-5d01405f7ce5] monitor closed 
[INFO ] 2024-10-21 10:25:01.321 - [任务 4][Mysql3307] - Node Mysql3307[1b6f72c0-e016-48ba-bd97-5d01405f7ce5] close complete, cost 39 ms 
[INFO ] 2024-10-21 10:25:05.325 - [任务 4] - Task [任务 4] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-10-21 10:25:05.330 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-21 10:25:05.330 - [任务 4] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3e5af8e9 
[INFO ] 2024-10-21 10:25:05.452 - [任务 4] - Stop task milestones: 6715bb308d7fb11f2d81215d(任务 4)  
[INFO ] 2024-10-21 10:25:05.453 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-10-21 10:25:05.453 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-10-21 10:25:05.469 - [任务 4] - Remove memory task client succeed, task: 任务 4[6715bb308d7fb11f2d81215d] 
[INFO ] 2024-10-21 10:25:05.473 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[6715bb308d7fb11f2d81215d] 
