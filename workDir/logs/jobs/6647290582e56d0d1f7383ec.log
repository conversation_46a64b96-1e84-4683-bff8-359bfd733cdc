[INFO ] 2024-05-21 11:25:15.211 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] start preload schema,table counts: 1 
[INFO ] 2024-05-21 11:25:15.230 - [任务 10(100)][8a73b3a7-6913-43ee-8199-23cac259266c] - Node 8a73b3a7-6913-43ee-8199-23cac259266c[8a73b3a7-6913-43ee-8199-23cac259266c] start preload schema,table counts: 0 
[INFO ] 2024-05-21 11:25:15.231 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] start preload schema,table counts: 1 
[INFO ] 2024-05-21 11:25:15.233 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] start preload schema,table counts: 1 
[INFO ] 2024-05-21 11:25:15.235 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] preload schema finished, cost 0 ms 
[INFO ] 2024-05-21 11:25:15.235 - [任务 10(100)][c38a1596-1ee4-4184-98f4-8b7dc8a2253e] - Node c38a1596-1ee4-4184-98f4-8b7dc8a2253e[c38a1596-1ee4-4184-98f4-8b7dc8a2253e] start preload schema,table counts: 0 
[INFO ] 2024-05-21 11:25:15.235 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] start preload schema,table counts: 1 
[INFO ] 2024-05-21 11:25:15.235 - [任务 10(100)][8a73b3a7-6913-43ee-8199-23cac259266c] - Node 8a73b3a7-6913-43ee-8199-23cac259266c[8a73b3a7-6913-43ee-8199-23cac259266c] preload schema finished, cost 0 ms 
[INFO ] 2024-05-21 11:25:15.236 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] preload schema finished, cost 0 ms 
[INFO ] 2024-05-21 11:25:15.236 - [任务 10(100)][c38a1596-1ee4-4184-98f4-8b7dc8a2253e] - Node c38a1596-1ee4-4184-98f4-8b7dc8a2253e[c38a1596-1ee4-4184-98f4-8b7dc8a2253e] preload schema finished, cost 0 ms 
[INFO ] 2024-05-21 11:25:15.236 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] preload schema finished, cost 0 ms 
[INFO ] 2024-05-21 11:25:15.236 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] preload schema finished, cost 0 ms 
[INFO ] 2024-05-21 11:25:15.743 - [任务 10(100)][1ee7db0f-0752-4d86-9f31-79058fe7157d] - Node 1ee7db0f-0752-4d86-9f31-79058fe7157d[1ee7db0f-0752-4d86-9f31-79058fe7157d] start preload schema,table counts: 0 
[INFO ] 2024-05-21 11:25:15.743 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] start preload schema,table counts: 1 
[INFO ] 2024-05-21 11:25:15.745 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] start preload schema,table counts: 1 
[INFO ] 2024-05-21 11:25:15.745 - [任务 10(100)][1ee7db0f-0752-4d86-9f31-79058fe7157d] - Node 1ee7db0f-0752-4d86-9f31-79058fe7157d[1ee7db0f-0752-4d86-9f31-79058fe7157d] preload schema finished, cost 0 ms 
[INFO ] 2024-05-21 11:25:15.746 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] preload schema finished, cost 0 ms 
[INFO ] 2024-05-21 11:25:15.748 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] preload schema finished, cost 0 ms 
[ERROR] 2024-05-21 11:25:16.760 - [任务 10(100)][testtime1] - start source runner failed: Failed to create pdk connector node, database type: postgres-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: postgres-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: postgres-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:567)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:212)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: postgres-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:194)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:221)
	... 16 more

[INFO ] 2024-05-21 11:25:16.829 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] running status set to false 
[INFO ] 2024-05-21 11:25:16.830 - [任务 10(100)][testtime1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e 
[INFO ] 2024-05-21 11:25:16.830 - [任务 10(100)][testtime1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e 
[INFO ] 2024-05-21 11:25:16.830 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] schema data cleaned 
[INFO ] 2024-05-21 11:25:16.830 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] monitor closed 
[INFO ] 2024-05-21 11:25:16.830 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] close complete, cost 59 ms 
[INFO ] 2024-05-21 11:25:17.205 - [任务 10(100)][增强JS] - true 
[INFO ] 2024-05-21 11:25:17.227 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] running status set to false 
[INFO ] 2024-05-21 11:25:17.227 - [任务 10(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePG-6dab5cfc-3f94-4cea-986b-44ec379c6763 
[INFO ] 2024-05-21 11:25:17.227 - [任务 10(100)][c38a1596-1ee4-4184-98f4-8b7dc8a2253e] - Node c38a1596-1ee4-4184-98f4-8b7dc8a2253e[c38a1596-1ee4-4184-98f4-8b7dc8a2253e] running status set to false 
[INFO ] 2024-05-21 11:25:17.227 - [任务 10(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePG-6dab5cfc-3f94-4cea-986b-44ec379c6763 
[INFO ] 2024-05-21 11:25:17.229 - [任务 10(100)][c38a1596-1ee4-4184-98f4-8b7dc8a2253e] - Node c38a1596-1ee4-4184-98f4-8b7dc8a2253e[c38a1596-1ee4-4184-98f4-8b7dc8a2253e] schema data cleaned 
[INFO ] 2024-05-21 11:25:17.229 - [任务 10(100)][c38a1596-1ee4-4184-98f4-8b7dc8a2253e] - Node c38a1596-1ee4-4184-98f4-8b7dc8a2253e[c38a1596-1ee4-4184-98f4-8b7dc8a2253e] monitor closed 
[INFO ] 2024-05-21 11:25:17.230 - [任务 10(100)][增强JS] - [ScriptExecutorsManager-6647290582e56d0d1f7383ec-b1c23729-0ca2-4c00-8f92-8f25a40d3990-6647213882e56d0d1f73814f] schema data cleaned 
[INFO ] 2024-05-21 11:25:17.230 - [任务 10(100)][c38a1596-1ee4-4184-98f4-8b7dc8a2253e] - Node c38a1596-1ee4-4184-98f4-8b7dc8a2253e[c38a1596-1ee4-4184-98f4-8b7dc8a2253e] close complete, cost 8 ms 
[INFO ] 2024-05-21 11:25:17.259 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] running status set to false 
[INFO ] 2024-05-21 11:25:17.259 - [任务 10(100)][testtime1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e 
[INFO ] 2024-05-21 11:25:17.259 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] schema data cleaned 
[INFO ] 2024-05-21 11:25:17.259 - [任务 10(100)][testtime1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e 
[INFO ] 2024-05-21 11:25:17.259 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] monitor closed 
[INFO ] 2024-05-21 11:25:17.261 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] schema data cleaned 
[INFO ] 2024-05-21 11:25:17.262 - [任务 10(100)][增强JS] - true 
[INFO ] 2024-05-21 11:25:17.264 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] close complete, cost 51 ms 
[INFO ] 2024-05-21 11:25:17.264 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] monitor closed 
[INFO ] 2024-05-21 11:25:17.266 - [任务 10(100)][8a73b3a7-6913-43ee-8199-23cac259266c] - Node 8a73b3a7-6913-43ee-8199-23cac259266c[8a73b3a7-6913-43ee-8199-23cac259266c] running status set to false 
[INFO ] 2024-05-21 11:25:17.266 - [任务 10(100)][8a73b3a7-6913-43ee-8199-23cac259266c] - Node 8a73b3a7-6913-43ee-8199-23cac259266c[8a73b3a7-6913-43ee-8199-23cac259266c] schema data cleaned 
[INFO ] 2024-05-21 11:25:17.266 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] running status set to false 
[INFO ] 2024-05-21 11:25:17.267 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] close complete, cost 30 ms 
[INFO ] 2024-05-21 11:25:17.267 - [任务 10(100)][8a73b3a7-6913-43ee-8199-23cac259266c] - Node 8a73b3a7-6913-43ee-8199-23cac259266c[8a73b3a7-6913-43ee-8199-23cac259266c] monitor closed 
[INFO ] 2024-05-21 11:25:17.267 - [任务 10(100)][8a73b3a7-6913-43ee-8199-23cac259266c] - Node 8a73b3a7-6913-43ee-8199-23cac259266c[8a73b3a7-6913-43ee-8199-23cac259266c] close complete, cost 2 ms 
[INFO ] 2024-05-21 11:25:17.281 - [任务 10(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePG-50c62d65-bf91-4c83-a715-ff96e2691a3f 
[INFO ] 2024-05-21 11:25:17.282 - [任务 10(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePG-50c62d65-bf91-4c83-a715-ff96e2691a3f 
[INFO ] 2024-05-21 11:25:17.283 - [任务 10(100)][增强JS] - [ScriptExecutorsManager-6647290582e56d0d1f7383ec-b1c23729-0ca2-4c00-8f92-8f25a40d3990-6647213882e56d0d1f73814f] schema data cleaned 
[INFO ] 2024-05-21 11:25:17.285 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] schema data cleaned 
[INFO ] 2024-05-21 11:25:17.285 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] monitor closed 
[INFO ] 2024-05-21 11:25:17.286 - [任务 10(100)] - load tapTable task 6647290582e56d0d1f7383ec-c38a1596-1ee4-4184-98f4-8b7dc8a2253e complete, cost 2658ms 
[INFO ] 2024-05-21 11:25:17.286 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] close complete, cost 20 ms 
[INFO ] 2024-05-21 11:25:17.494 - [任务 10(100)] - load tapTable task 6647290582e56d0d1f7383ec-8a73b3a7-6913-43ee-8199-23cac259266c complete, cost 2707ms 
[INFO ] 2024-05-21 11:25:19.237 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] running status set to false 
[INFO ] 2024-05-21 11:25:19.238 - [任务 10(100)][1ee7db0f-0752-4d86-9f31-79058fe7157d] - Node 1ee7db0f-0752-4d86-9f31-79058fe7157d[1ee7db0f-0752-4d86-9f31-79058fe7157d] running status set to false 
[INFO ] 2024-05-21 11:25:19.238 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] running status set to false 
[INFO ] 2024-05-21 11:25:19.254 - [任务 10(100)][testtime1] - PDK connector node stopped: null 
[INFO ] 2024-05-21 11:25:19.254 - [任务 10(100)][1ee7db0f-0752-4d86-9f31-79058fe7157d] - Node 1ee7db0f-0752-4d86-9f31-79058fe7157d[1ee7db0f-0752-4d86-9f31-79058fe7157d] schema data cleaned 
[INFO ] 2024-05-21 11:25:19.257 - [任务 10(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePG-7c1a944f-52c1-4f1c-a753-d09b48576ca6 
[INFO ] 2024-05-21 11:25:19.258 - [任务 10(100)][testtime1] - PDK connector node released: null 
[INFO ] 2024-05-21 11:25:19.261 - [任务 10(100)][1ee7db0f-0752-4d86-9f31-79058fe7157d] - Node 1ee7db0f-0752-4d86-9f31-79058fe7157d[1ee7db0f-0752-4d86-9f31-79058fe7157d] monitor closed 
[INFO ] 2024-05-21 11:25:19.261 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] schema data cleaned 
[INFO ] 2024-05-21 11:25:19.261 - [任务 10(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePG-7c1a944f-52c1-4f1c-a753-d09b48576ca6 
[INFO ] 2024-05-21 11:25:19.268 - [任务 10(100)][1ee7db0f-0752-4d86-9f31-79058fe7157d] - Node 1ee7db0f-0752-4d86-9f31-79058fe7157d[1ee7db0f-0752-4d86-9f31-79058fe7157d] close complete, cost 29 ms 
[INFO ] 2024-05-21 11:25:19.268 - [任务 10(100)][增强JS] - [ScriptExecutorsManager-6647290582e56d0d1f7383ec-b1c23729-0ca2-4c00-8f92-8f25a40d3990-6647213882e56d0d1f73814f] schema data cleaned 
[INFO ] 2024-05-21 11:25:19.268 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] monitor closed 
[INFO ] 2024-05-21 11:25:19.274 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] close complete, cost 43 ms 
[INFO ] 2024-05-21 11:25:19.274 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] schema data cleaned 
[INFO ] 2024-05-21 11:25:19.275 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] monitor closed 
[INFO ] 2024-05-21 11:25:19.276 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] close complete, cost 48 ms 
[INFO ] 2024-05-21 11:25:19.479 - [任务 10(100)] - load tapTable task 6647290582e56d0d1f7383ec-1ee7db0f-0752-4d86-9f31-79058fe7157d complete, cost 3587ms 
[INFO ] 2024-05-21 16:53:21.357 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] start preload schema,table counts: 1 
[INFO ] 2024-05-21 16:53:21.358 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] start preload schema,table counts: 1 
[INFO ] 2024-05-21 16:53:21.359 - [任务 10(100)][7f4cec68-949d-4c84-903c-102a791a768e] - Node 7f4cec68-949d-4c84-903c-102a791a768e[7f4cec68-949d-4c84-903c-102a791a768e] start preload schema,table counts: 0 
[INFO ] 2024-05-21 16:53:21.359 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] preload schema finished, cost 1 ms 
[INFO ] 2024-05-21 16:53:21.359 - [任务 10(100)][7f4cec68-949d-4c84-903c-102a791a768e] - Node 7f4cec68-949d-4c84-903c-102a791a768e[7f4cec68-949d-4c84-903c-102a791a768e] preload schema finished, cost 1 ms 
[INFO ] 2024-05-21 16:53:21.359 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] preload schema finished, cost 4 ms 
[INFO ] 2024-05-21 16:53:21.813 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] start preload schema,table counts: 1 
[INFO ] 2024-05-21 16:53:21.814 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] start preload schema,table counts: 1 
[INFO ] 2024-05-21 16:53:21.814 - [任务 10(100)][d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86] - Node d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86[d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86] start preload schema,table counts: 0 
[INFO ] 2024-05-21 16:53:21.820 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] preload schema finished, cost 1 ms 
[INFO ] 2024-05-21 16:53:21.822 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] preload schema finished, cost 0 ms 
[INFO ] 2024-05-21 16:53:21.822 - [任务 10(100)][d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86] - Node d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86[d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86] preload schema finished, cost 0 ms 
[ERROR] 2024-05-21 16:53:22.837 - [任务 10(100)][testtime1] - start source runner failed: Failed to create pdk connector node, database type: postgres-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: postgres-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: postgres-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:567)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:212)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: postgres-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:198)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId postgres not found for associateId HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:221)
	... 16 more

[INFO ] 2024-05-21 16:53:22.851 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] running status set to false 
[INFO ] 2024-05-21 16:53:22.862 - [任务 10(100)][testtime1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e 
[INFO ] 2024-05-21 16:53:22.862 - [任务 10(100)][testtime1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-7e3580f2-9861-492e-9dfc-9d2656579d5e 
[INFO ] 2024-05-21 16:53:22.863 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] schema data cleaned 
[INFO ] 2024-05-21 16:53:22.867 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] monitor closed 
[INFO ] 2024-05-21 16:53:23.075 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] close complete, cost 41 ms 
[INFO ] 2024-05-21 16:53:23.187 - [任务 10(100)][增强JS] - true 
[INFO ] 2024-05-21 16:53:23.195 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] running status set to false 
[INFO ] 2024-05-21 16:53:23.197 - [任务 10(100)][7f4cec68-949d-4c84-903c-102a791a768e] - Node 7f4cec68-949d-4c84-903c-102a791a768e[7f4cec68-949d-4c84-903c-102a791a768e] running status set to false 
[INFO ] 2024-05-21 16:53:23.198 - [任务 10(100)][7f4cec68-949d-4c84-903c-102a791a768e] - Node 7f4cec68-949d-4c84-903c-102a791a768e[7f4cec68-949d-4c84-903c-102a791a768e] schema data cleaned 
[INFO ] 2024-05-21 16:53:23.204 - [任务 10(100)][7f4cec68-949d-4c84-903c-102a791a768e] - Node 7f4cec68-949d-4c84-903c-102a791a768e[7f4cec68-949d-4c84-903c-102a791a768e] monitor closed 
[INFO ] 2024-05-21 16:53:23.205 - [任务 10(100)][7f4cec68-949d-4c84-903c-102a791a768e] - Node 7f4cec68-949d-4c84-903c-102a791a768e[7f4cec68-949d-4c84-903c-102a791a768e] close complete, cost 18 ms 
[INFO ] 2024-05-21 16:53:23.208 - [任务 10(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePG-abf221c4-94fd-46e0-a168-ad1ffdcec6b7 
[INFO ] 2024-05-21 16:53:23.210 - [任务 10(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePG-abf221c4-94fd-46e0-a168-ad1ffdcec6b7 
[INFO ] 2024-05-21 16:53:23.210 - [任务 10(100)][增强JS] - [ScriptExecutorsManager-6647290582e56d0d1f7383ec-b1c23729-0ca2-4c00-8f92-8f25a40d3990-6647213882e56d0d1f73814f] schema data cleaned 
[INFO ] 2024-05-21 16:53:23.215 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] schema data cleaned 
[INFO ] 2024-05-21 16:53:23.220 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] monitor closed 
[INFO ] 2024-05-21 16:53:23.221 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] close complete, cost 33 ms 
[INFO ] 2024-05-21 16:53:23.426 - [任务 10(100)] - load tapTable task 6647290582e56d0d1f7383ec-7f4cec68-949d-4c84-903c-102a791a768e complete, cost 2471ms 
[INFO ] 2024-05-21 16:53:25.432 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] running status set to false 
[INFO ] 2024-05-21 16:53:25.436 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] running status set to false 
[INFO ] 2024-05-21 16:53:25.449 - [任务 10(100)][d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86] - Node d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86[d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86] running status set to false 
[INFO ] 2024-05-21 16:53:25.449 - [任务 10(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePG-3888244d-5e4f-4035-a0a8-b379dbda142b 
[INFO ] 2024-05-21 16:53:25.449 - [任务 10(100)][testtime1] - PDK connector node stopped: null 
[INFO ] 2024-05-21 16:53:25.450 - [任务 10(100)][d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86] - Node d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86[d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86] schema data cleaned 
[INFO ] 2024-05-21 16:53:25.451 - [任务 10(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePG-3888244d-5e4f-4035-a0a8-b379dbda142b 
[INFO ] 2024-05-21 16:53:25.461 - [任务 10(100)][testtime1] - PDK connector node released: null 
[INFO ] 2024-05-21 16:53:25.461 - [任务 10(100)][d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86] - Node d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86[d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86] monitor closed 
[INFO ] 2024-05-21 16:53:25.462 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] schema data cleaned 
[INFO ] 2024-05-21 16:53:25.464 - [任务 10(100)][增强JS] - [ScriptExecutorsManager-6647290582e56d0d1f7383ec-b1c23729-0ca2-4c00-8f92-8f25a40d3990-6647213882e56d0d1f73814f] schema data cleaned 
[INFO ] 2024-05-21 16:53:25.469 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] monitor closed 
[INFO ] 2024-05-21 16:53:25.469 - [任务 10(100)][d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86] - Node d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86[d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86] close complete, cost 46 ms 
[INFO ] 2024-05-21 16:53:25.469 - [任务 10(100)][testtime1] - Node testtime1[7e3580f2-9861-492e-9dfc-9d2656579d5e] close complete, cost 55 ms 
[INFO ] 2024-05-21 16:53:25.473 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] schema data cleaned 
[INFO ] 2024-05-21 16:53:25.474 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] monitor closed 
[INFO ] 2024-05-21 16:53:25.478 - [任务 10(100)][增强JS] - Node 增强JS[b1c23729-0ca2-4c00-8f92-8f25a40d3990] close complete, cost 70 ms 
[INFO ] 2024-05-21 16:53:25.479 - [任务 10(100)] - load tapTable task 6647290582e56d0d1f7383ec-d80cd350-9d56-42ca-a6b2-4b1c3c7a7e86 complete, cost 3720ms 
