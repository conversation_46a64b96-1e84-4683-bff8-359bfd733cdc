[INFO ] 2024-10-23 11:48:31.759 - [任务 1] - Start task milestones: 67186c3a4347332a7f43e7ec(任务 1) 
[INFO ] 2024-10-23 11:48:31.965 - [任务 1] - Task initialization... 
[INFO ] 2024-10-23 11:48:32.592 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-23 11:48:32.652 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-23 11:48:33.246 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] start preload schema,table counts: 1 
[INFO ] 2024-10-23 11:48:33.248 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] start preload schema,table counts: 1 
[INFO ] 2024-10-23 11:48:33.249 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 11:48:33.249 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] preload schema finished, cost 2 ms 
[INFO ] 2024-10-23 11:48:33.264 - [任务 1][增删字段] - Node 增删字段[3125dcc3-546a-4b94-9521-81df0aa05b8b] start preload schema,table counts: 1 
[INFO ] 2024-10-23 11:48:33.265 - [任务 1][增删字段] - Node 增删字段[3125dcc3-546a-4b94-9521-81df0aa05b8b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 11:48:33.270 - [任务 1][增删字段] - Node field_add_del_processor(增删字段: 3125dcc3-546a-4b94-9521-81df0aa05b8b) enable batch process 
[INFO ] 2024-10-23 11:48:37.614 - [任务 1][cloudTaskTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-23 11:48:37.617 - [任务 1][FDM_Net_Task] - Source node "FDM_Net_Task" read batch size: 100 
[INFO ] 2024-10-23 11:48:37.618 - [任务 1][FDM_Net_Task] - Source node "FDM_Net_Task" event queue capacity: 200 
[INFO ] 2024-10-23 11:48:37.618 - [任务 1][FDM_Net_Task] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-23 11:48:37.823 - [任务 1][FDM_Net_Task] - batch offset found: {},stream offset found: {"cdcOffset":1729655317,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 11:48:37.928 - [任务 1][FDM_Net_Task] - Initial sync started 
[INFO ] 2024-10-23 11:48:37.943 - [任务 1][FDM_Net_Task] - Starting batch read, table name: FDM_Net_Task 
[INFO ] 2024-10-23 11:48:37.944 - [任务 1][FDM_Net_Task] - Table FDM_Net_Task is going to be initial synced 
[INFO ] 2024-10-23 11:48:38.150 - [任务 1][FDM_Net_Task] - Query table 'FDM_Net_Task' counts: 26412 
[INFO ] 2024-10-23 11:48:40.835 - [任务 1][FDM_Net_Task] - Table [FDM_Net_Task] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-23 11:48:40.837 - [任务 1][FDM_Net_Task] - Initial sync completed 
[INFO ] 2024-10-23 11:48:40.840 - [任务 1][FDM_Net_Task] - Incremental sync starting... 
[INFO ] 2024-10-23 11:48:40.843 - [任务 1][FDM_Net_Task] - Initial sync completed 
[INFO ] 2024-10-23 11:48:40.914 - [任务 1][FDM_Net_Task] - Starting stream read, table list: [FDM_Net_Task], offset: {"cdcOffset":1729655317,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 11:48:40.916 - [任务 1][FDM_Net_Task] - Connector MongoDB incremental start succeed, tables: [FDM_Net_Task], data change syncing 
[INFO ] 2024-10-23 11:49:13.882 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] running status set to false 
[INFO ] 2024-10-23 11:49:13.883 - [任务 1][FDM_Net_Task] - PDK connector node stopped: HazelcastSourcePdkDataNode-565ff66c-f44f-4407-8b8d-07106a91e9e1 
[INFO ] 2024-10-23 11:49:13.883 - [任务 1][FDM_Net_Task] - PDK connector node released: HazelcastSourcePdkDataNode-565ff66c-f44f-4407-8b8d-07106a91e9e1 
[INFO ] 2024-10-23 11:49:13.883 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] schema data cleaned 
[INFO ] 2024-10-23 11:49:13.899 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] monitor closed 
[INFO ] 2024-10-23 11:49:13.900 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] close complete, cost 57 ms 
[INFO ] 2024-10-23 11:49:13.900 - [任务 1][增删字段] - Node 增删字段[3125dcc3-546a-4b94-9521-81df0aa05b8b] running status set to false 
[INFO ] 2024-10-23 11:49:14.040 - [任务 1][增删字段] - Node 增删字段[3125dcc3-546a-4b94-9521-81df0aa05b8b] schema data cleaned 
[INFO ] 2024-10-23 11:49:14.043 - [任务 1][增删字段] - Node 增删字段[3125dcc3-546a-4b94-9521-81df0aa05b8b] monitor closed 
[INFO ] 2024-10-23 11:49:14.046 - [任务 1][增删字段] - Node 增删字段[3125dcc3-546a-4b94-9521-81df0aa05b8b] close complete, cost 144 ms 
[INFO ] 2024-10-23 11:49:14.046 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] running status set to false 
[INFO ] 2024-10-23 11:49:14.088 - [任务 1][cloudTaskTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-437b75c0-292e-41c6-af31-0d4aca49c6f6 
[INFO ] 2024-10-23 11:49:14.088 - [任务 1][cloudTaskTest] - PDK connector node released: HazelcastTargetPdkDataNode-437b75c0-292e-41c6-af31-0d4aca49c6f6 
[INFO ] 2024-10-23 11:49:14.088 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] schema data cleaned 
[INFO ] 2024-10-23 11:49:14.089 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] monitor closed 
[INFO ] 2024-10-23 11:49:14.089 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] close complete, cost 44 ms 
[INFO ] 2024-10-23 11:49:14.520 - [任务 1][FDM_Net_Task] - Incremental sync completed 
[INFO ] 2024-10-23 11:49:17.290 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-23 11:49:17.411 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6831b10 
[INFO ] 2024-10-23 11:49:17.412 - [任务 1] - Stop task milestones: 67186c3a4347332a7f43e7ec(任务 1)  
[INFO ] 2024-10-23 11:49:17.444 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-10-23 11:49:17.446 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-10-23 11:49:17.558 - [任务 1] - Remove memory task client succeed, task: 任务 1[67186c3a4347332a7f43e7ec] 
[INFO ] 2024-10-23 11:49:17.559 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[67186c3a4347332a7f43e7ec] 
[INFO ] 2024-10-23 11:50:05.785 - [任务 1] - Start task milestones: 67186c3a4347332a7f43e7ec(任务 1) 
[INFO ] 2024-10-23 11:50:05.785 - [任务 1] - Task initialization... 
[INFO ] 2024-10-23 11:50:06.321 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-23 11:50:06.322 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-23 11:50:06.390 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] start preload schema,table counts: 1 
[INFO ] 2024-10-23 11:50:06.391 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] start preload schema,table counts: 1 
[INFO ] 2024-10-23 11:50:06.393 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 11:50:06.394 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 11:50:07.559 - [任务 1][cloudTaskTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-23 11:50:07.560 - [任务 1][FDM_Net_Task] - Source node "FDM_Net_Task" read batch size: 100 
[INFO ] 2024-10-23 11:50:07.562 - [任务 1][FDM_Net_Task] - Source node "FDM_Net_Task" event queue capacity: 200 
[INFO ] 2024-10-23 11:50:07.563 - [任务 1][FDM_Net_Task] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-23 11:50:07.764 - [任务 1][FDM_Net_Task] - batch offset found: {},stream offset found: {"cdcOffset":1729655407,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 11:50:07.849 - [任务 1][FDM_Net_Task] - Initial sync started 
[INFO ] 2024-10-23 11:50:07.851 - [任务 1][FDM_Net_Task] - Starting batch read, table name: FDM_Net_Task 
[INFO ] 2024-10-23 11:50:07.939 - [任务 1][FDM_Net_Task] - Table FDM_Net_Task is going to be initial synced 
[INFO ] 2024-10-23 11:50:07.939 - [任务 1][FDM_Net_Task] - Query table 'FDM_Net_Task' counts: 26412 
[INFO ] 2024-10-23 11:50:10.690 - [任务 1][FDM_Net_Task] - Table [FDM_Net_Task] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-23 11:50:10.694 - [任务 1][FDM_Net_Task] - Initial sync completed 
[INFO ] 2024-10-23 11:50:10.694 - [任务 1][FDM_Net_Task] - Incremental sync starting... 
[INFO ] 2024-10-23 11:50:10.694 - [任务 1][FDM_Net_Task] - Initial sync completed 
[INFO ] 2024-10-23 11:50:10.694 - [任务 1][FDM_Net_Task] - Starting stream read, table list: [FDM_Net_Task], offset: {"cdcOffset":1729655407,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 11:50:10.701 - [任务 1][FDM_Net_Task] - Connector MongoDB incremental start succeed, tables: [FDM_Net_Task], data change syncing 
[INFO ] 2024-10-23 11:59:34.318 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] running status set to false 
[INFO ] 2024-10-23 11:59:34.325 - [任务 1][FDM_Net_Task] - PDK connector node stopped: HazelcastSourcePdkDataNode-565ff66c-f44f-4407-8b8d-07106a91e9e1 
[INFO ] 2024-10-23 11:59:34.325 - [任务 1][FDM_Net_Task] - PDK connector node released: HazelcastSourcePdkDataNode-565ff66c-f44f-4407-8b8d-07106a91e9e1 
[INFO ] 2024-10-23 11:59:34.325 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] schema data cleaned 
[INFO ] 2024-10-23 11:59:34.325 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] monitor closed 
[INFO ] 2024-10-23 11:59:34.328 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] close complete, cost 40 ms 
[INFO ] 2024-10-23 11:59:34.328 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] running status set to false 
[INFO ] 2024-10-23 11:59:34.346 - [任务 1][cloudTaskTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-437b75c0-292e-41c6-af31-0d4aca49c6f6 
[INFO ] 2024-10-23 11:59:34.347 - [任务 1][cloudTaskTest] - PDK connector node released: HazelcastTargetPdkDataNode-437b75c0-292e-41c6-af31-0d4aca49c6f6 
[INFO ] 2024-10-23 11:59:34.347 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] schema data cleaned 
[INFO ] 2024-10-23 11:59:34.348 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] monitor closed 
[INFO ] 2024-10-23 11:59:34.349 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] close complete, cost 21 ms 
[INFO ] 2024-10-23 11:59:35.164 - [任务 1][FDM_Net_Task] - Incremental sync completed 
[INFO ] 2024-10-23 11:59:38.168 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-23 11:59:38.173 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@a5d85ef 
[INFO ] 2024-10-23 11:59:38.293 - [任务 1] - Stop task milestones: 67186c3a4347332a7f43e7ec(任务 1)  
[INFO ] 2024-10-23 11:59:38.307 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-10-23 11:59:38.307 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-10-23 11:59:38.327 - [任务 1] - Remove memory task client succeed, task: 任务 1[67186c3a4347332a7f43e7ec] 
[INFO ] 2024-10-23 11:59:38.334 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[67186c3a4347332a7f43e7ec] 
[INFO ] 2024-10-23 12:11:28.003 - [任务 1] - Start task milestones: 67186c3a4347332a7f43e7ec(任务 1) 
[INFO ] 2024-10-23 12:11:28.160 - [任务 1] - Task initialization... 
[INFO ] 2024-10-23 12:11:28.160 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-23 12:11:28.245 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-23 12:11:28.245 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] start preload schema,table counts: 1 
[INFO ] 2024-10-23 12:11:28.245 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] start preload schema,table counts: 1 
[INFO ] 2024-10-23 12:11:28.246 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] preload schema finished, cost 1 ms 
[INFO ] 2024-10-23 12:11:28.246 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] preload schema finished, cost 0 ms 
[INFO ] 2024-10-23 12:11:29.035 - [任务 1][FDM_Net_Task] - Source node "FDM_Net_Task" read batch size: 100 
[INFO ] 2024-10-23 12:11:29.037 - [任务 1][FDM_Net_Task] - Source node "FDM_Net_Task" event queue capacity: 200 
[INFO ] 2024-10-23 12:11:29.037 - [任务 1][FDM_Net_Task] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-23 12:11:29.240 - [任务 1][cloudTaskTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-23 12:11:29.342 - [任务 1][FDM_Net_Task] - batch offset found: {},stream offset found: {"cdcOffset":1729656689,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 12:11:29.398 - [任务 1][FDM_Net_Task] - Initial sync started 
[INFO ] 2024-10-23 12:11:29.398 - [任务 1][FDM_Net_Task] - Starting batch read, table name: FDM_Net_Task 
[INFO ] 2024-10-23 12:11:29.509 - [任务 1][FDM_Net_Task] - Table FDM_Net_Task is going to be initial synced 
[INFO ] 2024-10-23 12:11:29.509 - [任务 1][FDM_Net_Task] - Query table 'FDM_Net_Task' counts: 26412 
[INFO ] 2024-10-23 12:11:34.538 - [任务 1][FDM_Net_Task] - Table [FDM_Net_Task] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-23 12:11:34.556 - [任务 1][FDM_Net_Task] - Initial sync completed 
[INFO ] 2024-10-23 12:11:34.557 - [任务 1][FDM_Net_Task] - Incremental sync starting... 
[INFO ] 2024-10-23 12:11:34.557 - [任务 1][FDM_Net_Task] - Initial sync completed 
[INFO ] 2024-10-23 12:11:34.558 - [任务 1][FDM_Net_Task] - Starting stream read, table list: [FDM_Net_Task], offset: {"cdcOffset":1729656689,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-10-23 12:11:34.574 - [任务 1][FDM_Net_Task] - Connector MongoDB incremental start succeed, tables: [FDM_Net_Task], data change syncing 
[INFO ] 2024-10-23 12:16:11.976 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] running status set to false 
[INFO ] 2024-10-23 12:16:11.977 - [任务 1][FDM_Net_Task] - PDK connector node stopped: HazelcastSourcePdkDataNode-565ff66c-f44f-4407-8b8d-07106a91e9e1 
[INFO ] 2024-10-23 12:16:11.977 - [任务 1][FDM_Net_Task] - PDK connector node released: HazelcastSourcePdkDataNode-565ff66c-f44f-4407-8b8d-07106a91e9e1 
[INFO ] 2024-10-23 12:16:11.977 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] schema data cleaned 
[INFO ] 2024-10-23 12:16:11.981 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] monitor closed 
[INFO ] 2024-10-23 12:16:11.984 - [任务 1][FDM_Net_Task] - Node FDM_Net_Task[565ff66c-f44f-4407-8b8d-07106a91e9e1] close complete, cost 53 ms 
[INFO ] 2024-10-23 12:16:11.984 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] running status set to false 
[INFO ] 2024-10-23 12:16:12.002 - [任务 1][cloudTaskTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-437b75c0-292e-41c6-af31-0d4aca49c6f6 
[INFO ] 2024-10-23 12:16:12.002 - [任务 1][cloudTaskTest] - PDK connector node released: HazelcastTargetPdkDataNode-437b75c0-292e-41c6-af31-0d4aca49c6f6 
[INFO ] 2024-10-23 12:16:12.002 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] schema data cleaned 
[INFO ] 2024-10-23 12:16:12.003 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] monitor closed 
[INFO ] 2024-10-23 12:16:12.147 - [任务 1][cloudTaskTest] - Node cloudTaskTest[437b75c0-292e-41c6-af31-0d4aca49c6f6] close complete, cost 20 ms 
[INFO ] 2024-10-23 12:16:12.148 - [任务 1][FDM_Net_Task] - Incremental sync completed 
[INFO ] 2024-10-23 12:16:14.487 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-23 12:16:14.595 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@9edc8eb 
[INFO ] 2024-10-23 12:16:14.596 - [任务 1] - Stop task milestones: 67186c3a4347332a7f43e7ec(任务 1)  
[INFO ] 2024-10-23 12:16:14.614 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-10-23 12:16:14.614 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-10-23 12:16:14.647 - [任务 1] - Remove memory task client succeed, task: 任务 1[67186c3a4347332a7f43e7ec] 
[INFO ] 2024-10-23 12:16:14.647 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[67186c3a4347332a7f43e7ec] 
