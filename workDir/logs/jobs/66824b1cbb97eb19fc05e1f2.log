[INFO ] 2024-07-01 14:23:57.706 - [Task 1] - Start task milestones: 66824b1cbb97eb19fc05e1f2(Task 1) 
[INFO ] 2024-07-01 14:23:57.912 - [Task 1] - Task initialization... 
[INFO ] 2024-07-01 14:23:57.951 - [Task 1] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-01 14:23:57.952 - [Task 1] - The engine receives Task 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-01 14:23:58.047 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] start preload schema,table counts: 1 
[INFO ] 2024-07-01 14:23:58.048 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] start preload schema,table counts: 1 
[INFO ] 2024-07-01 14:23:58.048 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 14:23:58.048 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 14:23:58.924 - [Task 1][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-01 14:23:58.925 - [Task 1][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-01 14:23:58.948 - [Task 1][SouceMysql] - Table "test2.CLAIM" exists, skip auto create table 
[INFO ] 2024-07-01 14:23:58.950 - [Task 1][SouceMysql] - The table CLAIM has already exist. 
[INFO ] 2024-07-01 14:23:58.999 - [Task 1][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-07-01 14:23:58.999 - [Task 1][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-07-01 14:23:58.999 - [Task 1][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-01 14:23:59.138 - [Task 1][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719815039,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 14:23:59.199 - [Task 1][SourceMongo] - Initial sync started 
[INFO ] 2024-07-01 14:23:59.199 - [Task 1][SourceMongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-07-01 14:24:05.334 - [Task 1][SourceMongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-07-01 14:24:06.277 - [Task 1][SourceMongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-07-01 14:24:06.910 - [Task 1][SourceMongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 14:24:06.911 - [Task 1][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-01 14:24:06.912 - [Task 1][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-01 14:24:06.913 - [Task 1][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-01 14:24:07.103 - [Task 1][SourceMongo] - Starting stream read, table list: [CLAIM], offset: {"cdcOffset":1719815039,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 14:24:07.106 - [Task 1][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-07-01 14:53:57.842 - [Task 1] - Stop task milestones: 66824b1cbb97eb19fc05e1f2(Task 1)  
[INFO ] 2024-07-01 14:53:58.316 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] running status set to false 
[INFO ] 2024-07-01 14:53:58.316 - [Task 1][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-136577b4-0085-458c-9209-6d43bc577309 
[INFO ] 2024-07-01 14:53:58.316 - [Task 1][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-136577b4-0085-458c-9209-6d43bc577309 
[INFO ] 2024-07-01 14:53:58.316 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] schema data cleaned 
[INFO ] 2024-07-01 14:53:58.317 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] monitor closed 
[INFO ] 2024-07-01 14:53:58.317 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] close complete, cost 44 ms 
[INFO ] 2024-07-01 14:53:58.317 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] running status set to false 
[INFO ] 2024-07-01 14:53:58.344 - [Task 1][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-e81feca2-e254-48de-83eb-20f2b5b23e9b 
[INFO ] 2024-07-01 14:53:58.344 - [Task 1][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-e81feca2-e254-48de-83eb-20f2b5b23e9b 
[INFO ] 2024-07-01 14:53:58.344 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] schema data cleaned 
[INFO ] 2024-07-01 14:53:58.344 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] monitor closed 
[INFO ] 2024-07-01 14:53:58.348 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] close complete, cost 27 ms 
[INFO ] 2024-07-01 14:54:03.007 - [Task 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-01 14:54:03.010 - [Task 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3ba66c0d 
[INFO ] 2024-07-01 14:54:03.011 - [Task 1] - Stopped task aspect(s) 
[INFO ] 2024-07-01 14:54:03.014 - [Task 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-01 14:54:03.032 - [Task 1] - Remove memory task client succeed, task: Task 1[66824b1cbb97eb19fc05e1f2] 
[INFO ] 2024-07-01 14:54:03.034 - [Task 1] - Destroy memory task client cache succeed, task: Task 1[66824b1cbb97eb19fc05e1f2] 
[INFO ] 2024-07-01 22:45:19.929 - [Task 1] - Task initialization... 
[INFO ] 2024-07-01 22:45:19.931 - [Task 1] - Start task milestones: 66824b1cbb97eb19fc05e1f2(Task 1) 
[INFO ] 2024-07-01 22:45:20.644 - [Task 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-01 22:45:20.645 - [Task 1] - The engine receives Task 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-01 22:45:21.044 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] start preload schema,table counts: 1 
[INFO ] 2024-07-01 22:45:21.046 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] start preload schema,table counts: 1 
[INFO ] 2024-07-01 22:45:21.049 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 22:45:21.049 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 22:45:22.291 - [Task 1][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-01 22:45:22.294 - [Task 1][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-01 22:45:22.306 - [Task 1][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-07-01 22:45:22.306 - [Task 1][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-07-01 22:45:22.307 - [Task 1][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-01 22:45:22.381 - [Task 1][SouceMysql] - Table "test2.CLAIM" exists, skip auto create table 
[INFO ] 2024-07-01 22:45:22.384 - [Task 1][SouceMysql] - The table CLAIM has already exist. 
[INFO ] 2024-07-01 22:45:22.517 - [Task 1][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719845122,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 22:45:22.624 - [Task 1][SourceMongo] - Initial sync started 
[INFO ] 2024-07-01 22:45:22.649 - [Task 1][SourceMongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-07-01 22:45:22.649 - [Task 1][SourceMongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-07-01 22:45:22.863 - [Task 1][SourceMongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-07-01 22:45:22.961 - [Task 1][SouceMysql] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: CLAIM 
[ERROR] 2024-07-01 22:45:23.007 - [Task 1][SouceMysql] - java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: CLAIM <-- Error Message -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: CLAIM

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	java.util.HashMap.merge(HashMap.java:1226)
	java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	java.util.Iterator.forEachRemaining(Iterator.java:116)
	java.util.Spliterators$IteratorSpliterator.forEachRemaining(Spliterators.java:1801)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: CLAIM
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:626)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:566)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:522)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:488)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:540)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: CLAIM
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:798)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:498)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:498)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:681)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:618)
	... 11 more
Caused by: Execute PDK method: TARGET_WRITE_RECORD, tableName: CLAIM
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:858)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 18 more
Caused by: java.lang.NullPointerException
	at java.util.HashMap.merge(HashMap.java:1226)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.Iterator.forEachRemaining(Iterator.java:116)
	at java.util.Spliterators$IteratorSpliterator.forEachRemaining(Spliterators.java:1801)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.common.dml.NormalWriteRecorder.<init>(NormalWriteRecorder.java:71)
	at io.tapdata.connector.mysql.dml.MysqlWriteRecorder.<init>(MysqlWriteRecorder.java:22)
	at io.tapdata.connector.mysql.dml.MysqlRecordWriter.<init>(MysqlRecordWriter.java:20)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:431)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:852)
	... 25 more

[INFO ] 2024-07-01 22:45:23.007 - [Task 1][SouceMysql] - Job suspend in error handle 
[INFO ] 2024-07-01 22:45:23.974 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] running status set to false 
[INFO ] 2024-07-01 22:45:23.988 - [Task 1][SourceMongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 22:45:23.991 - [Task 1][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-01 22:45:23.994 - [Task 1][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-01 22:45:23.996 - [Task 1][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-01 22:45:23.996 - [Task 1][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-136577b4-0085-458c-9209-6d43bc577309 
[INFO ] 2024-07-01 22:45:23.996 - [Task 1][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-136577b4-0085-458c-9209-6d43bc577309 
[INFO ] 2024-07-01 22:45:24.001 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] schema data cleaned 
[INFO ] 2024-07-01 22:45:24.001 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] monitor closed 
[INFO ] 2024-07-01 22:45:24.010 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] close complete, cost 59 ms 
[INFO ] 2024-07-01 22:45:24.010 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] running status set to false 
[INFO ] 2024-07-01 22:45:24.027 - [Task 1][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-e81feca2-e254-48de-83eb-20f2b5b23e9b 
[INFO ] 2024-07-01 22:45:24.027 - [Task 1][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-e81feca2-e254-48de-83eb-20f2b5b23e9b 
[INFO ] 2024-07-01 22:45:24.027 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] schema data cleaned 
[INFO ] 2024-07-01 22:45:24.029 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] monitor closed 
[INFO ] 2024-07-01 22:45:24.029 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] close complete, cost 21 ms 
[INFO ] 2024-07-01 22:45:26.508 - [Task 1] - Task [Task 1] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-01 22:45:26.546 - [Task 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-01 22:45:26.546 - [Task 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@49123644 
[INFO ] 2024-07-01 22:45:26.681 - [Task 1] - Stop task milestones: 66824b1cbb97eb19fc05e1f2(Task 1)  
[INFO ] 2024-07-01 22:45:26.682 - [Task 1] - Stopped task aspect(s) 
[INFO ] 2024-07-01 22:45:26.733 - [Task 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-01 22:45:26.734 - [Task 1] - Remove memory task client succeed, task: Task 1[66824b1cbb97eb19fc05e1f2] 
[INFO ] 2024-07-01 22:45:26.736 - [Task 1] - Destroy memory task client cache succeed, task: Task 1[66824b1cbb97eb19fc05e1f2] 
[INFO ] 2024-07-01 22:45:43.553 - [Task 1] - Task initialization... 
[INFO ] 2024-07-01 22:45:43.553 - [Task 1] - Start task milestones: 66824b1cbb97eb19fc05e1f2(Task 1) 
[INFO ] 2024-07-01 22:45:43.720 - [Task 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-01 22:45:43.720 - [Task 1] - The engine receives Task 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-01 22:45:43.805 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] start preload schema,table counts: 1 
[INFO ] 2024-07-01 22:45:43.805 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] start preload schema,table counts: 1 
[INFO ] 2024-07-01 22:45:43.807 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 22:45:43.807 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 22:45:44.637 - [Task 1][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-07-01 22:45:44.640 - [Task 1][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-07-01 22:45:44.640 - [Task 1][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-01 22:45:44.790 - [Task 1][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719845144,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 22:45:44.876 - [Task 1][SourceMongo] - Initial sync started 
[INFO ] 2024-07-01 22:45:44.884 - [Task 1][SourceMongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-07-01 22:45:44.884 - [Task 1][SourceMongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-07-01 22:45:44.919 - [Task 1][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-01 22:45:44.919 - [Task 1][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-01 22:45:44.932 - [Task 1][SouceMysql] - Table "test2.CLAIM" exists, skip auto create table 
[INFO ] 2024-07-01 22:45:44.933 - [Task 1][SouceMysql] - The table CLAIM has already exist. 
[INFO ] 2024-07-01 22:45:45.138 - [Task 1][SourceMongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-07-01 22:45:45.161 - [Task 1][SouceMysql] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: CLAIM 
[ERROR] 2024-07-01 22:45:45.176 - [Task 1][SouceMysql] - java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: CLAIM <-- Error Message -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: CLAIM

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	java.util.HashMap.merge(HashMap.java:1226)
	java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	java.util.Iterator.forEachRemaining(Iterator.java:116)
	java.util.Spliterators$IteratorSpliterator.forEachRemaining(Spliterators.java:1801)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: CLAIM
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:626)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:566)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:522)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:488)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:540)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: CLAIM
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:798)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:498)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:498)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:681)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:618)
	... 11 more
Caused by: Execute PDK method: TARGET_WRITE_RECORD, tableName: CLAIM
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:858)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 18 more
Caused by: java.lang.NullPointerException
	at java.util.HashMap.merge(HashMap.java:1226)
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.Iterator.forEachRemaining(Iterator.java:116)
	at java.util.Spliterators$IteratorSpliterator.forEachRemaining(Spliterators.java:1801)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.common.dml.NormalWriteRecorder.<init>(NormalWriteRecorder.java:71)
	at io.tapdata.connector.mysql.dml.MysqlWriteRecorder.<init>(MysqlWriteRecorder.java:22)
	at io.tapdata.connector.mysql.dml.MysqlRecordWriter.<init>(MysqlRecordWriter.java:20)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:431)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:852)
	... 25 more

[INFO ] 2024-07-01 22:45:45.177 - [Task 1][SouceMysql] - Job suspend in error handle 
[INFO ] 2024-07-01 22:45:46.200 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] running status set to false 
[INFO ] 2024-07-01 22:45:46.206 - [Task 1][SourceMongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 22:45:46.208 - [Task 1][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-01 22:45:46.208 - [Task 1][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-01 22:45:46.208 - [Task 1][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-01 22:45:46.211 - [Task 1][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-136577b4-0085-458c-9209-6d43bc577309 
[INFO ] 2024-07-01 22:45:46.211 - [Task 1][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-136577b4-0085-458c-9209-6d43bc577309 
[INFO ] 2024-07-01 22:45:46.213 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] schema data cleaned 
[INFO ] 2024-07-01 22:45:46.213 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] monitor closed 
[INFO ] 2024-07-01 22:45:46.215 - [Task 1][SourceMongo] - Node SourceMongo[136577b4-0085-458c-9209-6d43bc577309] close complete, cost 62 ms 
[INFO ] 2024-07-01 22:45:46.215 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] running status set to false 
[INFO ] 2024-07-01 22:45:46.224 - [Task 1][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-e81feca2-e254-48de-83eb-20f2b5b23e9b 
[INFO ] 2024-07-01 22:45:46.224 - [Task 1][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-e81feca2-e254-48de-83eb-20f2b5b23e9b 
[INFO ] 2024-07-01 22:45:46.225 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] schema data cleaned 
[INFO ] 2024-07-01 22:45:46.225 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] monitor closed 
[INFO ] 2024-07-01 22:45:46.436 - [Task 1][SouceMysql] - Node SouceMysql[e81feca2-e254-48de-83eb-20f2b5b23e9b] close complete, cost 12 ms 
[INFO ] 2024-07-01 22:45:46.820 - [Task 1] - Task [Task 1] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-01 22:45:46.821 - [Task 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-01 22:45:46.942 - [Task 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@17fcdb96 
[INFO ] 2024-07-01 22:45:46.942 - [Task 1] - Stop task milestones: 66824b1cbb97eb19fc05e1f2(Task 1)  
[INFO ] 2024-07-01 22:45:46.957 - [Task 1] - Stopped task aspect(s) 
[INFO ] 2024-07-01 22:45:46.957 - [Task 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-01 22:45:46.976 - [Task 1] - Remove memory task client succeed, task: Task 1[66824b1cbb97eb19fc05e1f2] 
[INFO ] 2024-07-01 22:45:46.976 - [Task 1] - Destroy memory task client cache succeed, task: Task 1[66824b1cbb97eb19fc05e1f2] 
