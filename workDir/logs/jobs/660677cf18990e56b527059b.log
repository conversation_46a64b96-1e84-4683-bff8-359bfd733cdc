[INFO ] 2024-03-29 16:21:00.461 - [categories_import_import_import_import_import_import_import] - Task initialization... 
[INFO ] 2024-03-29 16:21:00.510 - [categories_import_import_import_import_import_import_import] - Start task milestones: 660677cf18990e56b527059b(categories_import_import_import_import_import_import_import) 
[INFO ] 2024-03-29 16:21:00.518 - [categories_import_import_import_import_import_import_import] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-03-29 16:21:00.720 - [categories_import_import_import_import_import_import_import] - The engine receives categories_import_import_import_import_import_import_import task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 16:21:00.772 - [categories_import_import_import_import_import_import_import][Categories] - Node Categories[6e59ec62-513e-49bf-a8c8-fd2f588ff01c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:21:00.772 - [categories_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[4bd87d81-0c6c-47a7-872b-7fa3dc533158] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:21:00.773 - [categories_import_import_import_import_import_import_import][merge] - Node merge[3ebb6139-826b-47db-8144-e0c6ce0bb9d6] start preload schema,table counts: 3 
[INFO ] 2024-03-29 16:21:00.773 - [categories_import_import_import_import_import_import_import][Products] - Node Products[f6505c6d-026e-4756-adc1-1f497fd70d5e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:21:00.773 - [categories_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[44605532-6889-4183-9aac-428b66868a8f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:21:00.773 - [categories_import_import_import_import_import_import_import][Delete Categories] - Node Delete Categories[b4ca8171-14f7-4049-a687-70278eb56046] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:21:00.773 - [categories_import_import_import_import_import_import_import][categories] - Node categories[ad220733-7360-4eb6-97ea-0e46a1748485] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:21:00.794 - [categories_import_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[29a5a5aa-bb00-419c-b6d7-7b2fb0253daf] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:21:00.829 - [categories_import_import_import_import_import_import_import][categories] - Node categories[ad220733-7360-4eb6-97ea-0e46a1748485] preload schema finished, cost 57 ms 
[INFO ] 2024-03-29 16:21:00.829 - [categories_import_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[29a5a5aa-bb00-419c-b6d7-7b2fb0253daf] preload schema finished, cost 34 ms 
[INFO ] 2024-03-29 16:21:00.829 - [categories_import_import_import_import_import_import_import][Delete Categories] - Node Delete Categories[b4ca8171-14f7-4049-a687-70278eb56046] preload schema finished, cost 58 ms 
[INFO ] 2024-03-29 16:21:00.829 - [categories_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[4bd87d81-0c6c-47a7-872b-7fa3dc533158] preload schema finished, cost 57 ms 
[INFO ] 2024-03-29 16:21:00.829 - [categories_import_import_import_import_import_import_import][Products] - Node Products[f6505c6d-026e-4756-adc1-1f497fd70d5e] preload schema finished, cost 58 ms 
[INFO ] 2024-03-29 16:21:00.832 - [categories_import_import_import_import_import_import_import][Categories] - Node Categories[6e59ec62-513e-49bf-a8c8-fd2f588ff01c] preload schema finished, cost 57 ms 
[INFO ] 2024-03-29 16:21:00.833 - [categories_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[44605532-6889-4183-9aac-428b66868a8f] preload schema finished, cost 58 ms 
[INFO ] 2024-03-29 16:21:00.883 - [categories_import_import_import_import_import_import_import][merge] - Node merge[3ebb6139-826b-47db-8144-e0c6ce0bb9d6] preload schema finished, cost 111 ms 
[INFO ] 2024-03-29 16:21:00.883 - [categories_import_import_import_import_import_import_import][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 16:21:01.087 - [categories_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Categories(b4ca8171-14f7-4049-a687-70278eb56046)
    ->Products(4bd87d81-0c6c-47a7-872b-7fa3dc533158)
} 
[INFO ] 2024-03-29 16:21:01.163 - [categories_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Products_4bd87d81-0c6c-47a7-872b-7fa3dc533158__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 16:21:01.165 - [categories_import_import_import_import_import_import_import][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 16:21:01.165 - [categories_import_import_import_import_import_import_import][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 16:21:01.861 - [categories_import_import_import_import_import_import_import][Products] - Source node "Products" read batch size: 500 
[INFO ] 2024-03-29 16:21:01.861 - [categories_import_import_import_import_import_import_import][Products] - Source node "Products" event queue capacity: 1000 
[INFO ] 2024-03-29 16:21:01.861 - [categories_import_import_import_import_import_import_import][Products] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:21:01.863 - [categories_import_import_import_import_import_import_import][Products] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6371734,"gtidSet":""} 
[INFO ] 2024-03-29 16:21:01.937 - [categories_import_import_import_import_import_import_import][categories] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 16:21:01.940 - [categories_import_import_import_import_import_import_import] - Node[Products] is waiting for running 
[INFO ] 2024-03-29 16:21:02.067 - [categories_import_import_import_import_import_import_import][Categories] - Source node "Categories" read batch size: 500 
[INFO ] 2024-03-29 16:21:02.067 - [categories_import_import_import_import_import_import_import][Categories] - Source node "Categories" event queue capacity: 1000 
[INFO ] 2024-03-29 16:21:02.067 - [categories_import_import_import_import_import_import_import][Categories] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:21:02.114 - [categories_import_import_import_import_import_import_import][Categories] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6371734,"gtidSet":""} 
[INFO ] 2024-03-29 16:21:02.114 - [categories_import_import_import_import_import_import_import][Categories] - Initial sync started 
[INFO ] 2024-03-29 16:21:02.114 - [categories_import_import_import_import_import_import_import][Categories] - Starting batch read, table name: Categories, offset: null 
[INFO ] 2024-03-29 16:21:02.120 - [categories_import_import_import_import_import_import_import][Categories] - Table Categories is going to be initial synced 
[INFO ] 2024-03-29 16:21:02.157 - [categories_import_import_import_import_import_import_import][Categories] - Query table 'Categories' counts: 1 
[INFO ] 2024-03-29 16:21:02.157 - [categories_import_import_import_import_import_import_import][Categories] - Initial sync completed 
[INFO ] 2024-03-29 16:21:03.520 - [categories_import_import_import_import_import_import_import] - Node[Categories] finish, notify next layer to run 
[INFO ] 2024-03-29 16:21:03.589 - [categories_import_import_import_import_import_import_import] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 16:21:03.591 - [categories_import_import_import_import_import_import_import][Products] - Initial sync started 
[INFO ] 2024-03-29 16:21:03.591 - [categories_import_import_import_import_import_import_import][Products] - Starting batch read, table name: Products, offset: null 
[INFO ] 2024-03-29 16:21:03.595 - [categories_import_import_import_import_import_import_import][Products] - Table Products is going to be initial synced 
[INFO ] 2024-03-29 16:21:03.625 - [categories_import_import_import_import_import_import_import][Products] - Query table 'Products' counts: 1 
[INFO ] 2024-03-29 16:21:03.627 - [categories_import_import_import_import_import_import_import][Products] - Initial sync completed 
[INFO ] 2024-03-29 16:21:04.163 - [categories_import_import_import_import_import_import_import][Categories] - Incremental sync starting... 
[INFO ] 2024-03-29 16:21:04.163 - [categories_import_import_import_import_import_import_import][Categories] - Initial sync completed 
[INFO ] 2024-03-29 16:21:04.164 - [categories_import_import_import_import_import_import_import][Categories] - Starting stream read, table list: [Categories], offset: {"filename":"binlog.000008","position":6371734,"gtidSet":""} 
[INFO ] 2024-03-29 16:21:04.194 - [categories_import_import_import_import_import_import_import][Categories] - Starting mysql cdc, server name: b1669261-264e-4833-971d-006073da1b65 
[INFO ] 2024-03-29 16:21:04.194 - [categories_import_import_import_import_import_import_import][Categories] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 30497961
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b1669261-264e-4833-971d-006073da1b65
  database.port: 3307
  threadName: Debezium-Mysql-Connector-b1669261-264e-4833-971d-006073da1b65
  database.hostname: 127.0.0.1
  database.password: ********
  name: b1669261-264e-4833-971d-006073da1b65
  pdk.offset.string: {"name":"b1669261-264e-4833-971d-006073da1b65","offset":{"{\"server\":\"b1669261-264e-4833-971d-006073da1b65\"}":"{\"file\":\"binlog.000008\",\"pos\":6371734,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Categories
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 16:21:04.404 - [categories_import_import_import_import_import_import_import][Categories] - Connector Mysql incremental start succeed, tables: [Categories], data change syncing 
[INFO ] 2024-03-29 16:21:04.637 - [categories_import_import_import_import_import_import_import][Products] - Incremental sync starting... 
[INFO ] 2024-03-29 16:21:04.637 - [categories_import_import_import_import_import_import_import][Products] - Initial sync completed 
[INFO ] 2024-03-29 16:21:04.639 - [categories_import_import_import_import_import_import_import][Products] - Starting stream read, table list: [Products], offset: {"filename":"binlog.000008","position":6371734,"gtidSet":""} 
[INFO ] 2024-03-29 16:21:04.698 - [categories_import_import_import_import_import_import_import][Products] - Starting mysql cdc, server name: d43c81a7-a789-4bfd-b3ae-c28cfee43a82 
[INFO ] 2024-03-29 16:21:04.698 - [categories_import_import_import_import_import_import_import][Products] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1661263190
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d43c81a7-a789-4bfd-b3ae-c28cfee43a82
  database.port: 3307
  threadName: Debezium-Mysql-Connector-d43c81a7-a789-4bfd-b3ae-c28cfee43a82
  database.hostname: 127.0.0.1
  database.password: ********
  name: d43c81a7-a789-4bfd-b3ae-c28cfee43a82
  pdk.offset.string: {"name":"d43c81a7-a789-4bfd-b3ae-c28cfee43a82","offset":{"{\"server\":\"d43c81a7-a789-4bfd-b3ae-c28cfee43a82\"}":"{\"file\":\"binlog.000008\",\"pos\":6371734,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Products
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 16:21:04.901 - [categories_import_import_import_import_import_import_import][Products] - Connector Mysql incremental start succeed, tables: [Products], data change syncing 
[INFO ] 2024-03-29 16:31:36.171 - [categories_import_import_import_import_import_import_import] - Stop task milestones: 660677cf18990e56b527059b(categories_import_import_import_import_import_import_import)  
[INFO ] 2024-03-29 16:31:36.292 - [categories_import_import_import_import_import_import_import][Categories] - Node Categories[6e59ec62-513e-49bf-a8c8-fd2f588ff01c] running status set to false 
[INFO ] 2024-03-29 16:31:36.335 - [categories_import_import_import_import_import_import_import][Categories] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 16:31:36.335 - [categories_import_import_import_import_import_import_import][Categories] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 16:31:36.339 - [categories_import_import_import_import_import_import_import][Categories] - PDK connector node stopped: HazelcastSourcePdkDataNode-6e59ec62-513e-49bf-a8c8-fd2f588ff01c 
[INFO ] 2024-03-29 16:31:36.340 - [categories_import_import_import_import_import_import_import][Categories] - PDK connector node released: HazelcastSourcePdkDataNode-6e59ec62-513e-49bf-a8c8-fd2f588ff01c 
[INFO ] 2024-03-29 16:31:36.341 - [categories_import_import_import_import_import_import_import][Categories] - Node Categories[6e59ec62-513e-49bf-a8c8-fd2f588ff01c] schema data cleaned 
[INFO ] 2024-03-29 16:31:36.341 - [categories_import_import_import_import_import_import_import][Categories] - Node Categories[6e59ec62-513e-49bf-a8c8-fd2f588ff01c] monitor closed 
[INFO ] 2024-03-29 16:31:36.341 - [categories_import_import_import_import_import_import_import][Categories] - Node Categories[6e59ec62-513e-49bf-a8c8-fd2f588ff01c] close complete, cost 50 ms 
[INFO ] 2024-03-29 16:31:36.341 - [categories_import_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[29a5a5aa-bb00-419c-b6d7-7b2fb0253daf] running status set to false 
[INFO ] 2024-03-29 16:31:36.432 - [categories_import_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[29a5a5aa-bb00-419c-b6d7-7b2fb0253daf] schema data cleaned 
[INFO ] 2024-03-29 16:31:36.434 - [categories_import_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[29a5a5aa-bb00-419c-b6d7-7b2fb0253daf] monitor closed 
[INFO ] 2024-03-29 16:31:36.434 - [categories_import_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[29a5a5aa-bb00-419c-b6d7-7b2fb0253daf] close complete, cost 91 ms 
[INFO ] 2024-03-29 16:31:36.434 - [categories_import_import_import_import_import_import_import][Delete Categories] - Node Delete Categories[b4ca8171-14f7-4049-a687-70278eb56046] running status set to false 
[INFO ] 2024-03-29 16:31:36.492 - [categories_import_import_import_import_import_import_import][Delete Categories] - Node Delete Categories[b4ca8171-14f7-4049-a687-70278eb56046] schema data cleaned 
[INFO ] 2024-03-29 16:31:36.492 - [categories_import_import_import_import_import_import_import][Delete Categories] - Node Delete Categories[b4ca8171-14f7-4049-a687-70278eb56046] monitor closed 
[INFO ] 2024-03-29 16:31:36.493 - [categories_import_import_import_import_import_import_import][Delete Categories] - Node Delete Categories[b4ca8171-14f7-4049-a687-70278eb56046] close complete, cost 57 ms 
[INFO ] 2024-03-29 16:31:36.493 - [categories_import_import_import_import_import_import_import][Products] - Node Products[f6505c6d-026e-4756-adc1-1f497fd70d5e] running status set to false 
[INFO ] 2024-03-29 16:31:36.586 - [categories_import_import_import_import_import_import_import][Products] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 16:31:36.594 - [categories_import_import_import_import_import_import_import][Products] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 16:31:36.595 - [categories_import_import_import_import_import_import_import][Products] - PDK connector node stopped: HazelcastSourcePdkDataNode-f6505c6d-026e-4756-adc1-1f497fd70d5e 
[INFO ] 2024-03-29 16:31:36.595 - [categories_import_import_import_import_import_import_import][Products] - PDK connector node released: HazelcastSourcePdkDataNode-f6505c6d-026e-4756-adc1-1f497fd70d5e 
[INFO ] 2024-03-29 16:31:36.596 - [categories_import_import_import_import_import_import_import][Products] - Node Products[f6505c6d-026e-4756-adc1-1f497fd70d5e] schema data cleaned 
[INFO ] 2024-03-29 16:31:36.596 - [categories_import_import_import_import_import_import_import][Products] - Node Products[f6505c6d-026e-4756-adc1-1f497fd70d5e] monitor closed 
[INFO ] 2024-03-29 16:31:36.596 - [categories_import_import_import_import_import_import_import][Products] - Node Products[f6505c6d-026e-4756-adc1-1f497fd70d5e] close complete, cost 103 ms 
[INFO ] 2024-03-29 16:31:36.635 - [categories_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[44605532-6889-4183-9aac-428b66868a8f] running status set to false 
[INFO ] 2024-03-29 16:31:36.635 - [categories_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[44605532-6889-4183-9aac-428b66868a8f] schema data cleaned 
[INFO ] 2024-03-29 16:31:36.635 - [categories_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[44605532-6889-4183-9aac-428b66868a8f] monitor closed 
[INFO ] 2024-03-29 16:31:36.635 - [categories_import_import_import_import_import_import_import][Rename Products] - Node Rename Products[44605532-6889-4183-9aac-428b66868a8f] close complete, cost 39 ms 
[INFO ] 2024-03-29 16:31:36.635 - [categories_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[4bd87d81-0c6c-47a7-872b-7fa3dc533158] running status set to false 
[INFO ] 2024-03-29 16:31:36.675 - [categories_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[4bd87d81-0c6c-47a7-872b-7fa3dc533158] schema data cleaned 
[INFO ] 2024-03-29 16:31:36.675 - [categories_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[4bd87d81-0c6c-47a7-872b-7fa3dc533158] monitor closed 
[INFO ] 2024-03-29 16:31:36.675 - [categories_import_import_import_import_import_import_import][Delete Products] - Node Delete Products[4bd87d81-0c6c-47a7-872b-7fa3dc533158] close complete, cost 39 ms 
[INFO ] 2024-03-29 16:31:36.675 - [categories_import_import_import_import_import_import_import][merge] - Node merge[3ebb6139-826b-47db-8144-e0c6ce0bb9d6] running status set to false 
[INFO ] 2024-03-29 16:31:36.688 - [categories_import_import_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Products_4bd87d81-0c6c-47a7-872b-7fa3dc533158__TPORIG 
[INFO ] 2024-03-29 16:31:36.688 - [categories_import_import_import_import_import_import_import][merge] - Node merge[3ebb6139-826b-47db-8144-e0c6ce0bb9d6] schema data cleaned 
[INFO ] 2024-03-29 16:31:36.688 - [categories_import_import_import_import_import_import_import][merge] - Node merge[3ebb6139-826b-47db-8144-e0c6ce0bb9d6] monitor closed 
[INFO ] 2024-03-29 16:31:36.688 - [categories_import_import_import_import_import_import_import][merge] - Node merge[3ebb6139-826b-47db-8144-e0c6ce0bb9d6] close complete, cost 13 ms 
[INFO ] 2024-03-29 16:31:36.703 - [categories_import_import_import_import_import_import_import][categories] - Node categories[ad220733-7360-4eb6-97ea-0e46a1748485] running status set to false 
[INFO ] 2024-03-29 16:31:36.703 - [categories_import_import_import_import_import_import_import][categories] - PDK connector node stopped: HazelcastTargetPdkDataNode-ad220733-7360-4eb6-97ea-0e46a1748485 
[INFO ] 2024-03-29 16:31:36.703 - [categories_import_import_import_import_import_import_import][categories] - PDK connector node released: HazelcastTargetPdkDataNode-ad220733-7360-4eb6-97ea-0e46a1748485 
[INFO ] 2024-03-29 16:31:36.703 - [categories_import_import_import_import_import_import_import][categories] - Node categories[ad220733-7360-4eb6-97ea-0e46a1748485] schema data cleaned 
[INFO ] 2024-03-29 16:31:36.703 - [categories_import_import_import_import_import_import_import][categories] - Node categories[ad220733-7360-4eb6-97ea-0e46a1748485] monitor closed 
[INFO ] 2024-03-29 16:31:36.703 - [categories_import_import_import_import_import_import_import][categories] - Node categories[ad220733-7360-4eb6-97ea-0e46a1748485] close complete, cost 14 ms 
[INFO ] 2024-03-29 16:31:37.909 - [categories_import_import_import_import_import_import_import] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 16:31:37.911 - [categories_import_import_import_import_import_import_import] - Stopped task aspect(s) 
[INFO ] 2024-03-29 16:31:37.911 - [categories_import_import_import_import_import_import_import] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 16:31:37.931 - [categories_import_import_import_import_import_import_import] - Remove memory task client succeed, task: categories_import_import_import_import_import_import_import[660677cf18990e56b527059b] 
[INFO ] 2024-03-29 16:31:37.934 - [categories_import_import_import_import_import_import_import] - Destroy memory task client cache succeed, task: categories_import_import_import_import_import_import_import[660677cf18990e56b527059b] 
