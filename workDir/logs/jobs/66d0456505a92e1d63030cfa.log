[INFO ] 2024-08-30 17:18:39.439 - [任务 15] - Task initialization... 
[INFO ] 2024-08-30 17:18:39.465 - [任务 15] - Start task milestones: 66d0456505a92e1d63030cfa(任务 15) 
[INFO ] 2024-08-30 17:18:39.497 - [任务 15] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-08-30 17:18:39.702 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-30 17:18:40.198 - [任务 15][Mysql3306] - Node Mysql3306[70fb267b-cf52-4517-b5b4-2ca06954de01] start preload schema,table counts: 1 
[INFO ] 2024-08-30 17:18:40.200 - [任务 15][字段编辑] - Node 字段编辑[a7b80c44-c031-4a97-8cda-6fb024872ac9] start preload schema,table counts: 1 
[INFO ] 2024-08-30 17:18:40.341 - [任务 15][Oracle] - Node Oracle[4d78221e-a081-4f20-beee-c80e8ca78e02] start preload schema,table counts: 1 
[INFO ] 2024-08-30 17:18:40.342 - [任务 15][Oracle] - Node Oracle[4d78221e-a081-4f20-beee-c80e8ca78e02] preload schema finished, cost 122 ms 
[INFO ] 2024-08-30 17:18:40.351 - [任务 15][Mysql3306] - Node Mysql3306[70fb267b-cf52-4517-b5b4-2ca06954de01] preload schema finished, cost 144 ms 
[INFO ] 2024-08-30 17:18:40.354 - [任务 15][字段编辑] - Node 字段编辑[a7b80c44-c031-4a97-8cda-6fb024872ac9] preload schema finished, cost 143 ms 
[ERROR] 2024-08-30 17:18:40.484 - [任务 15][Mysql3306] - Map name: PdkStateMap_70fb267b-cf52-4517-b5b4-2ca06954de01 <-- Error Message -->
Map name: PdkStateMap_70fb267b-cf52-4517-b5b4-2ca06954de01

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_70fb267b-cf52-4517-b5b4-2ca06954de01
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:402)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:170)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 13 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 18 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 19 more

[ERROR] 2024-08-30 17:18:40.487 - [任务 15][Oracle] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2583/985529946@3680255e failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_4d78221e-a081-4f20-beee-c80e8ca78e02
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_4d78221e-a081-4f20-beee-c80e8ca78e02
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_4d78221e-a081-4f20-beee-c80e8ca78e02
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2583/985529946@3680255e failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_4d78221e-a081-4f20-beee-c80e8ca78e02
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_4d78221e-a081-4f20-beee-c80e8ca78e02
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_4d78221e-a081-4f20-beee-c80e8ca78e02
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more


<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2583/985529946@3680255e failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_4d78221e-a081-4f20-beee-c80e8ca78e02
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_4d78221e-a081-4f20-beee-c80e8ca78e02
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_4d78221e-a081-4f20-beee-c80e8ca78e02
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:212)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2583/985529946@3680255e failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_4d78221e-a081-4f20-beee-c80e8ca78e02
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_4d78221e-a081-4f20-beee-c80e8ca78e02
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_4d78221e-a081-4f20-beee-c80e8ca78e02
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:193)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_4d78221e-a081-4f20-beee-c80e8ca78e02
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_4d78221e-a081-4f20-beee-c80e8ca78e02
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:202)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_4d78221e-a081-4f20-beee-c80e8ca78e02
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:196)
	... 7 more
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. java.lang.IllegalStateException: state should be: server session pool is open
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: state should be: server session pool is open
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 17 more
Caused by: java.lang.IllegalStateException: state should be: server session pool is open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:70)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:79)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 18 more

[INFO ] 2024-08-30 17:18:40.517 - [任务 15][Oracle] - Job suspend in error handle 
[INFO ] 2024-08-30 17:18:40.521 - [任务 15][Mysql3306] - Job suspend in error handle 
[INFO ] 2024-08-30 17:18:40.547 - [任务 15][Oracle] - Node Oracle[4d78221e-a081-4f20-beee-c80e8ca78e02] running status set to false 
[INFO ] 2024-08-30 17:18:40.547 - [任务 15][Oracle] - PDK connector node stopped: null 
[INFO ] 2024-08-30 17:18:40.549 - [任务 15][Oracle] - PDK connector node released: null 
[INFO ] 2024-08-30 17:18:40.549 - [任务 15][Oracle] - Node Oracle[4d78221e-a081-4f20-beee-c80e8ca78e02] schema data cleaned 
[INFO ] 2024-08-30 17:18:40.551 - [任务 15][Oracle] - Node Oracle[4d78221e-a081-4f20-beee-c80e8ca78e02] monitor closed 
[INFO ] 2024-08-30 17:18:40.560 - [任务 15][Oracle] - Node Oracle[4d78221e-a081-4f20-beee-c80e8ca78e02] close complete, cost 18 ms 
[INFO ] 2024-08-30 17:18:40.562 - [任务 15][字段编辑] - Node 字段编辑[a7b80c44-c031-4a97-8cda-6fb024872ac9] running status set to false 
[INFO ] 2024-08-30 17:18:40.563 - [任务 15][字段编辑] - Node 字段编辑[a7b80c44-c031-4a97-8cda-6fb024872ac9] schema data cleaned 
[INFO ] 2024-08-30 17:18:40.566 - [任务 15][字段编辑] - Node 字段编辑[a7b80c44-c031-4a97-8cda-6fb024872ac9] monitor closed 
[INFO ] 2024-08-30 17:18:40.568 - [任务 15][字段编辑] - Node 字段编辑[a7b80c44-c031-4a97-8cda-6fb024872ac9] close complete, cost 5 ms 
[INFO ] 2024-08-30 17:18:40.572 - [任务 15][Mysql3306] - Node Mysql3306[70fb267b-cf52-4517-b5b4-2ca06954de01] running status set to false 
[INFO ] 2024-08-30 17:18:40.572 - [任务 15][Mysql3306] - PDK connector node stopped: null 
[INFO ] 2024-08-30 17:18:40.572 - [任务 15][Mysql3306] - PDK connector node released: null 
[INFO ] 2024-08-30 17:18:40.573 - [任务 15][Mysql3306] - Node Mysql3306[70fb267b-cf52-4517-b5b4-2ca06954de01] schema data cleaned 
[INFO ] 2024-08-30 17:18:40.573 - [任务 15][Mysql3306] - Node Mysql3306[70fb267b-cf52-4517-b5b4-2ca06954de01] monitor closed 
[INFO ] 2024-08-30 17:18:40.780 - [任务 15][Mysql3306] - Node Mysql3306[70fb267b-cf52-4517-b5b4-2ca06954de01] close complete, cost 5 ms 
[INFO ] 2024-08-30 17:18:44.737 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-30 17:18:44.783 - [任务 15] - Stop task milestones: 66d0456505a92e1d63030cfa(任务 15)  
[INFO ] 2024-08-30 17:18:44.784 - [任务 15] - Stopped task aspect(s) 
[INFO ] 2024-08-30 17:18:44.839 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2024-08-30 17:18:44.840 - [任务 15] - Remove memory task client succeed, task: 任务 15[66d0456505a92e1d63030cfa] 
[INFO ] 2024-08-30 17:18:44.841 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[66d0456505a92e1d63030cfa] 
