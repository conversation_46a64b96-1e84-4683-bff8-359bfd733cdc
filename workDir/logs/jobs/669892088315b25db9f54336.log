[INFO ] 2024-07-18 11:54:49.619 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537] - Start task milestones: 669892088315b25db9f54336(Heartbeat-qa_mongodb_repl_42240_1717403468657_3537) 
[INFO ] 2024-07-18 11:54:49.826 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 11:54:49.976 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537] - The engine receives Heartbeat-qa_mongodb_repl_42240_1717403468657_3537 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 11:54:50.025 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[1ae05773-797d-412f-a80b-5fd3de7066cc] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:54:50.025 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3eaa5b34-8f13-45ae-b75c-fde83306918b] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:54:50.025 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[1ae05773-797d-412f-a80b-5fd3de7066cc] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:54:50.025 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3eaa5b34-8f13-45ae-b75c-fde83306918b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:54:50.229 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-18 11:54:50.229 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-18 11:54:50.229 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 11:54:50.229 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721274890229,"lastTimes":1721274890229,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-18 11:54:50.364 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-18 11:54:50.364 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-18 11:54:50.376 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-18 11:54:50.376 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:54:50.376 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-18 11:54:50.377 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721274890229,"lastTimes":1721274890229,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-18 11:54:50.377 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-18 11:54:50.377 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 11:54:50.550 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 12:33:07.495 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3eaa5b34-8f13-45ae-b75c-fde83306918b] running status set to false 
[INFO ] 2024-07-18 12:33:07.496 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-18 12:33:07.498 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-3eaa5b34-8f13-45ae-b75c-fde83306918b 
[INFO ] 2024-07-18 12:33:07.498 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-3eaa5b34-8f13-45ae-b75c-fde83306918b 
[INFO ] 2024-07-18 12:33:07.498 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3eaa5b34-8f13-45ae-b75c-fde83306918b] schema data cleaned 
[INFO ] 2024-07-18 12:33:07.498 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3eaa5b34-8f13-45ae-b75c-fde83306918b] monitor closed 
[INFO ] 2024-07-18 12:33:07.499 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3eaa5b34-8f13-45ae-b75c-fde83306918b] close complete, cost 4 ms 
[INFO ] 2024-07-18 12:33:07.499 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[1ae05773-797d-412f-a80b-5fd3de7066cc] running status set to false 
[INFO ] 2024-07-18 12:33:07.521 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-1ae05773-797d-412f-a80b-5fd3de7066cc 
[INFO ] 2024-07-18 12:33:07.521 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-1ae05773-797d-412f-a80b-5fd3de7066cc 
[INFO ] 2024-07-18 12:33:07.521 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[1ae05773-797d-412f-a80b-5fd3de7066cc] schema data cleaned 
[INFO ] 2024-07-18 12:33:07.521 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[1ae05773-797d-412f-a80b-5fd3de7066cc] monitor closed 
[INFO ] 2024-07-18 12:33:07.726 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[1ae05773-797d-412f-a80b-5fd3de7066cc] close complete, cost 22 ms 
[INFO ] 2024-07-18 12:33:11.641 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 12:33:11.641 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@466b13c7 
[INFO ] 2024-07-18 12:33:11.745 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537] - Stop task milestones: 669892088315b25db9f54336(Heartbeat-qa_mongodb_repl_42240_1717403468657_3537)  
[INFO ] 2024-07-18 12:33:11.791 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537] - Stopped task aspect(s) 
[INFO ] 2024-07-18 12:33:11.791 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 12:33:11.843 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537] - Remove memory task client succeed, task: Heartbeat-qa_mongodb_repl_42240_1717403468657_3537[669892088315b25db9f54336] 
[INFO ] 2024-07-18 12:33:11.849 - [Heartbeat-qa_mongodb_repl_42240_1717403468657_3537] - Destroy memory task client cache succeed, task: Heartbeat-qa_mongodb_repl_42240_1717403468657_3537[669892088315b25db9f54336] 
