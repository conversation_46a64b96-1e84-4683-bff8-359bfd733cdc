[INFO ] 2024-06-21 11:38:14.475 - [研祥任务测试] - Task initialization... 
[INFO ] 2024-06-21 11:38:14.476 - [研祥任务测试] - Start task milestones: 6672a09dc432b64620be6e23(研祥任务测试) 
[INFO ] 2024-06-21 11:38:14.477 - [研祥任务测试] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-21 11:38:14.477 - [研祥任务测试] - The engine receives 研祥任务测试 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-21 11:38:14.477 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:38:14.477 - [研祥任务测试][TEST_001] - Node TEST_001[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:38:14.477 - [研祥任务测试][TEST_001] - Node TEST_001[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:38:14.477 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] preload schema finished, cost 1 ms 
[INFO ] 2024-06-21 11:38:15.298 - [研祥任务测试][TEST_001] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-21 11:38:15.476 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Source node "IdentityServerIdentityResourceProperties" read batch size: 100 
[INFO ] 2024-06-21 11:38:15.477 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Source node "IdentityServerIdentityResourceProperties" event queue capacity: 200 
[INFO ] 2024-06-21 11:38:15.477 - [研祥任务测试][IdentityServerIdentityResourceProperties] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-21 11:38:15.499 - [研祥任务测试][IdentityServerIdentityResourceProperties] - batch offset found: {},stream offset found: {"currentStartLSN":"000036A10000BD600001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-06-21 11:38:15.499 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-21 11:38:15.547 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Initial sync started 
[INFO ] 2024-06-21 11:38:15.552 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Starting batch read, table name: IdentityServerIdentityResourceProperties, offset: null 
[INFO ] 2024-06-21 11:38:15.655 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Table IdentityServerIdentityResourceProperties is going to be initial synced 
[INFO ] 2024-06-21 11:38:15.655 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Query table 'IdentityServerIdentityResourceProperties' counts: 1 
[INFO ] 2024-06-21 11:38:15.792 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Table [IdentityServerIdentityResourceProperties] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-21 11:38:15.792 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Initial sync completed 
[INFO ] 2024-06-21 11:38:15.794 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Incremental sync starting... 
[INFO ] 2024-06-21 11:38:15.794 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Initial sync completed 
[INFO ] 2024-06-21 11:38:15.996 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Starting stream read, table list: [IdentityServerIdentityResourceProperties], offset: {"currentStartLSN":"000036A10000BD600001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-06-21 11:38:16.203 - [研祥任务测试][IdentityServerIdentityResourceProperties] - opened cdc tables: [Category_test, Category, ChildTable, a_test, test001_dummy_test, dummy612, Product, TEST_LENGTH, table_name, IdentityServerIdentityResourceProperties, orderitem, Category1, testTable, Supplier, SupplierInfo, TEST_DDL_001, reimbursement_approval, _tapdata_heartbeat_table, my_table, MyTable, t_9_1_3, ParentTable, t_9_1_1, testdate, test8, test9, Category1_test, test7, orders] 
[INFO ] 2024-06-21 11:38:16.326 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-06-21 11:38:16.327 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Connector SQL Server incremental start succeed, tables: [IdentityServerIdentityResourceProperties], data change syncing 
[INFO ] 2024-06-21 11:41:31.242 - [研祥任务测试] - Stop task milestones: 6672a09dc432b64620be6e23(研祥任务测试)  
[INFO ] 2024-06-21 11:41:31.448 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] running status set to false 
[INFO ] 2024-06-21 11:41:34.316 - [研祥任务测试][IdentityServerIdentityResourceProperties] - PDK connector node stopped: HazelcastSourcePdkDataNode-46487505-053d-4a15-92a3-555e88315d55 
[INFO ] 2024-06-21 11:41:34.317 - [研祥任务测试][IdentityServerIdentityResourceProperties] - PDK connector node released: HazelcastSourcePdkDataNode-46487505-053d-4a15-92a3-555e88315d55 
[INFO ] 2024-06-21 11:41:34.319 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] schema data cleaned 
[INFO ] 2024-06-21 11:41:34.319 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] monitor closed 
[INFO ] 2024-06-21 11:41:34.321 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] close complete, cost 3060 ms 
[INFO ] 2024-06-21 11:41:34.321 - [研祥任务测试][TEST_001] - Node TEST_001[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] running status set to false 
[INFO ] 2024-06-21 11:41:34.355 - [研祥任务测试][TEST_001] - PDK connector node stopped: HazelcastTargetPdkDataNode-42b8cbf1-84b8-4a4d-8c00-5d428eb43686 
[INFO ] 2024-06-21 11:41:34.355 - [研祥任务测试][TEST_001] - PDK connector node released: HazelcastTargetPdkDataNode-42b8cbf1-84b8-4a4d-8c00-5d428eb43686 
[INFO ] 2024-06-21 11:41:34.355 - [研祥任务测试][TEST_001] - Node TEST_001[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] schema data cleaned 
[INFO ] 2024-06-21 11:41:34.356 - [研祥任务测试][TEST_001] - Node TEST_001[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] monitor closed 
[INFO ] 2024-06-21 11:41:34.567 - [研祥任务测试][TEST_001] - Node TEST_001[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] close complete, cost 36 ms 
[INFO ] 2024-06-21 11:41:36.185 - [研祥任务测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-21 11:41:36.185 - [研祥任务测试] - Stopped task aspect(s) 
[INFO ] 2024-06-21 11:41:36.185 - [研祥任务测试] - Snapshot order controller have been removed 
[INFO ] 2024-06-21 11:41:36.235 - [研祥任务测试] - Remove memory task client succeed, task: 研祥任务测试[6672a09dc432b64620be6e23] 
[INFO ] 2024-06-21 11:41:36.235 - [研祥任务测试] - Destroy memory task client cache succeed, task: 研祥任务测试[6672a09dc432b64620be6e23] 
[INFO ] 2024-06-21 11:42:03.171 - [研祥任务测试] - Task initialization... 
[INFO ] 2024-06-21 11:42:03.172 - [研祥任务测试] - Start task milestones: 6672a09dc432b64620be6e23(研祥任务测试) 
[INFO ] 2024-06-21 11:42:03.379 - [研祥任务测试] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-21 11:42:03.511 - [研祥任务测试] - The engine receives 研祥任务测试 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-21 11:42:03.511 - [研祥任务测试][TEST_001] - Node Category1[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:42:03.511 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:42:03.511 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:42:03.512 - [研祥任务测试][TEST_001] - Node Category1[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:42:05.268 - [研祥任务测试][TEST_001] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-21 11:42:05.270 - [研祥任务测试][TEST_001] - The table Category1 has already exist. 
[INFO ] 2024-06-21 11:42:05.614 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Source node "IdentityServerIdentityResourceProperties" read batch size: 100 
[INFO ] 2024-06-21 11:42:05.614 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Source node "IdentityServerIdentityResourceProperties" event queue capacity: 200 
[INFO ] 2024-06-21 11:42:05.614 - [研祥任务测试][IdentityServerIdentityResourceProperties] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-21 11:42:05.643 - [研祥任务测试][IdentityServerIdentityResourceProperties] - batch offset found: {},stream offset found: {"currentStartLSN":"000036A10000BFB80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-06-21 11:42:05.718 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-21 11:42:05.718 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Initial sync started 
[INFO ] 2024-06-21 11:42:05.718 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Starting batch read, table name: IdentityServerIdentityResourceProperties, offset: null 
[INFO ] 2024-06-21 11:42:05.724 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Table IdentityServerIdentityResourceProperties is going to be initial synced 
[INFO ] 2024-06-21 11:42:05.803 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Table [IdentityServerIdentityResourceProperties] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-21 11:42:05.803 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Query table 'IdentityServerIdentityResourceProperties' counts: 1 
[INFO ] 2024-06-21 11:42:05.805 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Initial sync completed 
[INFO ] 2024-06-21 11:42:05.805 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Incremental sync starting... 
[INFO ] 2024-06-21 11:42:05.806 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Initial sync completed 
[INFO ] 2024-06-21 11:42:05.806 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Starting stream read, table list: [IdentityServerIdentityResourceProperties], offset: {"currentStartLSN":"000036A10000BFB80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-06-21 11:42:05.845 - [研祥任务测试][TEST_001] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: Category1 
[ERROR] 2024-06-21 11:42:05.848 - [研祥任务测试][TEST_001] - Unknown exception occur when operate table: Category1 <-- Error Message -->
Unknown exception occur when operate table: Category1

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Execute create index sql(s) failed: Column name 'IdentityResourceId' does not exist in the target table or view.
 Sql(s): CREATE NONCLUSTERED INDEX [5bfeb725-8a50-4379-8995-e3c83f4b278a] ON [dbo].[Category1] ([IdentityResourceId] ASC)
	io.tapdata.connector.mssql.MssqlConnector.createIndex(MssqlConnector.java:290)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$6(HazelcastTargetPdkDataNode.java:271)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Execute create index sql(s) failed: Column name 'IdentityResourceId' does not exist in the target table or view.
 Sql(s): CREATE NONCLUSTERED INDEX [5bfeb725-8a50-4379-8995-e3c83f4b278a] ON [dbo].[Category1] ([IdentityResourceId] ASC)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$createTargetIndex$7(HazelcastTargetPdkDataNode.java:269)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTargetIndex(HazelcastTargetPdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:149)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute create index sql(s) failed: Column name 'IdentityResourceId' does not exist in the target table or view.
 Sql(s): CREATE NONCLUSTERED INDEX [5bfeb725-8a50-4379-8995-e3c83f4b278a] ON [dbo].[Category1] ([IdentityResourceId] ASC)
	at io.tapdata.connector.mssql.MssqlConnector.createIndex(MssqlConnector.java:290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$6(HazelcastTargetPdkDataNode.java:271)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 28 more

[INFO ] 2024-06-21 11:42:06.055 - [研祥任务测试][TEST_001] - Job suspend in error handle 
[INFO ] 2024-06-21 11:42:06.193 - [研祥任务测试][IdentityServerIdentityResourceProperties] - opened cdc tables: [Category_test, Category, ChildTable, a_test, test001_dummy_test, dummy612, Product, TEST_LENGTH, table_name, IdentityServerIdentityResourceProperties, orderitem, Category1, testTable, Supplier, SupplierInfo, TEST_DDL_001, reimbursement_approval, _tapdata_heartbeat_table, my_table, MyTable, t_9_1_3, ParentTable, t_9_1_1, testdate, test8, test9, Category1_test, test7, orders] 
[INFO ] 2024-06-21 11:42:06.194 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-06-21 11:42:06.194 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Connector SQL Server incremental start succeed, tables: [IdentityServerIdentityResourceProperties], data change syncing 
[INFO ] 2024-06-21 11:42:06.195 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] running status set to false 
[INFO ] 2024-06-21 11:42:06.231 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Incremental sync completed 
[INFO ] 2024-06-21 11:42:06.437 - [研祥任务测试] - Task [研祥任务测试] cannot retry, reason: Task retry service not start 
[INFO ] 2024-06-21 11:42:09.238 - [研祥任务测试][IdentityServerIdentityResourceProperties] - PDK connector node stopped: HazelcastSourcePdkDataNode-46487505-053d-4a15-92a3-555e88315d55 
[INFO ] 2024-06-21 11:42:09.239 - [研祥任务测试][IdentityServerIdentityResourceProperties] - PDK connector node released: HazelcastSourcePdkDataNode-46487505-053d-4a15-92a3-555e88315d55 
[INFO ] 2024-06-21 11:42:09.239 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] schema data cleaned 
[INFO ] 2024-06-21 11:42:09.239 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] monitor closed 
[INFO ] 2024-06-21 11:42:09.245 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] close complete, cost 3053 ms 
[INFO ] 2024-06-21 11:42:09.265 - [研祥任务测试][TEST_001] - Node Category1[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] running status set to false 
[INFO ] 2024-06-21 11:42:09.266 - [研祥任务测试][TEST_001] - PDK connector node stopped: HazelcastTargetPdkDataNode-42b8cbf1-84b8-4a4d-8c00-5d428eb43686 
[INFO ] 2024-06-21 11:42:09.266 - [研祥任务测试][TEST_001] - PDK connector node released: HazelcastTargetPdkDataNode-42b8cbf1-84b8-4a4d-8c00-5d428eb43686 
[INFO ] 2024-06-21 11:42:09.266 - [研祥任务测试][TEST_001] - Node Category1[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] schema data cleaned 
[INFO ] 2024-06-21 11:42:09.267 - [研祥任务测试][TEST_001] - Node Category1[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] monitor closed 
[INFO ] 2024-06-21 11:42:09.470 - [研祥任务测试][TEST_001] - Node Category1[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] close complete, cost 22 ms 
[INFO ] 2024-06-21 11:42:11.270 - [研祥任务测试] - Task [研祥任务测试] cannot retry, reason: Task retry service not start 
[INFO ] 2024-06-21 11:42:11.308 - [研祥任务测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-21 11:42:11.308 - [研祥任务测试] - Stop task milestones: 6672a09dc432b64620be6e23(研祥任务测试)  
[INFO ] 2024-06-21 11:42:11.334 - [研祥任务测试] - Stopped task aspect(s) 
[INFO ] 2024-06-21 11:42:11.335 - [研祥任务测试] - Snapshot order controller have been removed 
[INFO ] 2024-06-21 11:42:11.366 - [研祥任务测试] - Remove memory task client succeed, task: 研祥任务测试[6672a09dc432b64620be6e23] 
[INFO ] 2024-06-21 11:42:11.368 - [研祥任务测试] - Destroy memory task client cache succeed, task: 研祥任务测试[6672a09dc432b64620be6e23] 
[INFO ] 2024-06-21 11:43:24.303 - [研祥任务测试] - Task initialization... 
[INFO ] 2024-06-21 11:43:24.316 - [研祥任务测试] - Start task milestones: 6672a09dc432b64620be6e23(研祥任务测试) 
[INFO ] 2024-06-21 11:43:24.471 - [研祥任务测试] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-21 11:43:24.545 - [研祥任务测试] - The engine receives 研祥任务测试 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-21 11:43:24.614 - [研祥任务测试][TEST_001] - Node test[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:43:24.614 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:43:24.614 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:43:24.617 - [研祥任务测试][TEST_001] - Node test[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:43:25.330 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Source node "IdentityServerIdentityResourceProperties" read batch size: 100 
[INFO ] 2024-06-21 11:43:25.331 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Source node "IdentityServerIdentityResourceProperties" event queue capacity: 200 
[INFO ] 2024-06-21 11:43:25.331 - [研祥任务测试][IdentityServerIdentityResourceProperties] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-21 11:43:25.352 - [研祥任务测试][IdentityServerIdentityResourceProperties] - batch offset found: {},stream offset found: {"currentStartLSN":"000036A10000BFB80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-06-21 11:43:25.392 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-21 11:43:25.392 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Initial sync started 
[INFO ] 2024-06-21 11:43:25.392 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Starting batch read, table name: IdentityServerIdentityResourceProperties, offset: null 
[INFO ] 2024-06-21 11:43:25.401 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Table IdentityServerIdentityResourceProperties is going to be initial synced 
[INFO ] 2024-06-21 11:43:25.605 - [研祥任务测试][TEST_001] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-21 11:43:25.647 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Table [IdentityServerIdentityResourceProperties] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-21 11:43:25.647 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Query table 'IdentityServerIdentityResourceProperties' counts: 1 
[INFO ] 2024-06-21 11:43:25.648 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Initial sync completed 
[INFO ] 2024-06-21 11:43:25.648 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Incremental sync starting... 
[INFO ] 2024-06-21 11:43:25.648 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Initial sync completed 
[INFO ] 2024-06-21 11:43:25.860 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Starting stream read, table list: [IdentityServerIdentityResourceProperties], offset: {"currentStartLSN":"000036A10000BFB80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-06-21 11:43:26.015 - [研祥任务测试][IdentityServerIdentityResourceProperties] - opened cdc tables: [Category_test, Category, ChildTable, a_test, test001_dummy_test, dummy612, Product, TEST_LENGTH, table_name, IdentityServerIdentityResourceProperties, orderitem, Category1, testTable, Supplier, SupplierInfo, TEST_DDL_001, reimbursement_approval, _tapdata_heartbeat_table, my_table, MyTable, t_9_1_3, ParentTable, t_9_1_1, testdate, test8, test9, Category1_test, test7, orders] 
[INFO ] 2024-06-21 11:43:26.015 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-06-21 11:43:26.015 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Connector SQL Server incremental start succeed, tables: [IdentityServerIdentityResourceProperties], data change syncing 
[INFO ] 2024-06-21 11:44:13.030 - [研祥任务测试] - Stop task milestones: 6672a09dc432b64620be6e23(研祥任务测试)  
[INFO ] 2024-06-21 11:44:13.436 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] running status set to false 
[INFO ] 2024-06-21 11:44:16.484 - [研祥任务测试][IdentityServerIdentityResourceProperties] - PDK connector node stopped: HazelcastSourcePdkDataNode-46487505-053d-4a15-92a3-555e88315d55 
[INFO ] 2024-06-21 11:44:16.487 - [研祥任务测试][IdentityServerIdentityResourceProperties] - PDK connector node released: HazelcastSourcePdkDataNode-46487505-053d-4a15-92a3-555e88315d55 
[INFO ] 2024-06-21 11:44:16.487 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] schema data cleaned 
[INFO ] 2024-06-21 11:44:16.491 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] monitor closed 
[INFO ] 2024-06-21 11:44:16.491 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] close complete, cost 3085 ms 
[INFO ] 2024-06-21 11:44:16.491 - [研祥任务测试][TEST_001] - Node test[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] running status set to false 
[INFO ] 2024-06-21 11:44:16.533 - [研祥任务测试][TEST_001] - PDK connector node stopped: HazelcastTargetPdkDataNode-42b8cbf1-84b8-4a4d-8c00-5d428eb43686 
[INFO ] 2024-06-21 11:44:16.539 - [研祥任务测试][TEST_001] - PDK connector node released: HazelcastTargetPdkDataNode-42b8cbf1-84b8-4a4d-8c00-5d428eb43686 
[INFO ] 2024-06-21 11:44:16.547 - [研祥任务测试][TEST_001] - Node test[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] schema data cleaned 
[INFO ] 2024-06-21 11:44:16.550 - [研祥任务测试][TEST_001] - Node test[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] monitor closed 
[INFO ] 2024-06-21 11:44:16.561 - [研祥任务测试][TEST_001] - Node test[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] close complete, cost 45 ms 
[INFO ] 2024-06-21 11:44:21.529 - [研祥任务测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-21 11:44:21.530 - [研祥任务测试] - Stopped task aspect(s) 
[INFO ] 2024-06-21 11:44:21.530 - [研祥任务测试] - Snapshot order controller have been removed 
[INFO ] 2024-06-21 11:44:21.556 - [研祥任务测试] - Remove memory task client succeed, task: 研祥任务测试[6672a09dc432b64620be6e23] 
[INFO ] 2024-06-21 11:44:21.761 - [研祥任务测试] - Destroy memory task client cache succeed, task: 研祥任务测试[6672a09dc432b64620be6e23] 
[INFO ] 2024-06-21 11:46:16.951 - [研祥任务测试] - Task initialization... 
[INFO ] 2024-06-21 11:46:16.954 - [研祥任务测试] - Start task milestones: 6672a09dc432b64620be6e23(研祥任务测试) 
[INFO ] 2024-06-21 11:46:16.954 - [研祥任务测试] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-21 11:46:16.954 - [研祥任务测试] - The engine receives 研祥任务测试 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-21 11:46:16.954 - [研祥任务测试][TEST_001] - Node CLAIM[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:46:16.954 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:46:16.954 - [研祥任务测试][TEST_001] - Node CLAIM[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:46:16.954 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:46:17.493 - [研祥任务测试][TEST_001] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-21 11:46:17.495 - [研祥任务测试][TEST_001] - The table CLAIM has already exist. 
[INFO ] 2024-06-21 11:46:17.758 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Source node "IdentityServerIdentityResourceProperties" read batch size: 100 
[INFO ] 2024-06-21 11:46:17.758 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Source node "IdentityServerIdentityResourceProperties" event queue capacity: 200 
[INFO ] 2024-06-21 11:46:17.778 - [研祥任务测试][IdentityServerIdentityResourceProperties] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-21 11:46:17.778 - [研祥任务测试][IdentityServerIdentityResourceProperties] - batch offset found: {},stream offset found: {"currentStartLSN":"000036A10000C1C00001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-06-21 11:46:17.856 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-21 11:46:17.858 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Initial sync started 
[INFO ] 2024-06-21 11:46:17.858 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Starting batch read, table name: IdentityServerIdentityResourceProperties, offset: null 
[INFO ] 2024-06-21 11:46:17.858 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Table IdentityServerIdentityResourceProperties is going to be initial synced 
[INFO ] 2024-06-21 11:46:17.945 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Table [IdentityServerIdentityResourceProperties] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-21 11:46:17.946 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Query table 'IdentityServerIdentityResourceProperties' counts: 1 
[INFO ] 2024-06-21 11:46:17.947 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Initial sync completed 
[INFO ] 2024-06-21 11:46:17.947 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Incremental sync starting... 
[INFO ] 2024-06-21 11:46:17.949 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Initial sync completed 
[INFO ] 2024-06-21 11:46:17.950 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Starting stream read, table list: [IdentityServerIdentityResourceProperties], offset: {"currentStartLSN":"000036A10000C1C00001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-06-21 11:46:18.006 - [研祥任务测试][TEST_001] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: CLAIM 
[ERROR] 2024-06-21 11:46:18.009 - [研祥任务测试][TEST_001] - Unknown exception occur when operate table: CLAIM <-- Error Message -->
Unknown exception occur when operate table: CLAIM

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Execute create index sql(s) failed: Column name 'IdentityResourceId' does not exist in the target table or view.
 Sql(s): CREATE NONCLUSTERED INDEX [6e83b7cb-c6f1-44b2-8c4f-5d7e3078caa8] ON [dbo].[CLAIM] ([IdentityResourceId] ASC)
	io.tapdata.connector.mssql.MssqlConnector.createIndex(MssqlConnector.java:290)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$6(HazelcastTargetPdkDataNode.java:271)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Execute create index sql(s) failed: Column name 'IdentityResourceId' does not exist in the target table or view.
 Sql(s): CREATE NONCLUSTERED INDEX [6e83b7cb-c6f1-44b2-8c4f-5d7e3078caa8] ON [dbo].[CLAIM] ([IdentityResourceId] ASC)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$createTargetIndex$7(HazelcastTargetPdkDataNode.java:269)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTargetIndex(HazelcastTargetPdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:149)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute create index sql(s) failed: Column name 'IdentityResourceId' does not exist in the target table or view.
 Sql(s): CREATE NONCLUSTERED INDEX [6e83b7cb-c6f1-44b2-8c4f-5d7e3078caa8] ON [dbo].[CLAIM] ([IdentityResourceId] ASC)
	at io.tapdata.connector.mssql.MssqlConnector.createIndex(MssqlConnector.java:290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$6(HazelcastTargetPdkDataNode.java:271)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 28 more

[INFO ] 2024-06-21 11:46:18.179 - [研祥任务测试][TEST_001] - Job suspend in error handle 
[INFO ] 2024-06-21 11:46:18.179 - [研祥任务测试][IdentityServerIdentityResourceProperties] - opened cdc tables: [Category_test, Category, ChildTable, a_test, test001_dummy_test, dummy612, Product, TEST_LENGTH, table_name, IdentityServerIdentityResourceProperties, orderitem, Category1, testTable, Supplier, SupplierInfo, TEST_DDL_001, reimbursement_approval, _tapdata_heartbeat_table, my_table, MyTable, t_9_1_3, ParentTable, t_9_1_1, testdate, test8, test9, Category1_test, test7, orders] 
[INFO ] 2024-06-21 11:46:18.280 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-06-21 11:46:18.285 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Connector SQL Server incremental start succeed, tables: [IdentityServerIdentityResourceProperties], data change syncing 
[INFO ] 2024-06-21 11:46:18.285 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] running status set to false 
[INFO ] 2024-06-21 11:46:18.491 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Incremental sync completed 
[INFO ] 2024-06-21 11:46:21.320 - [研祥任务测试][IdentityServerIdentityResourceProperties] - PDK connector node stopped: HazelcastSourcePdkDataNode-46487505-053d-4a15-92a3-555e88315d55 
[INFO ] 2024-06-21 11:46:21.320 - [研祥任务测试][IdentityServerIdentityResourceProperties] - PDK connector node released: HazelcastSourcePdkDataNode-46487505-053d-4a15-92a3-555e88315d55 
[INFO ] 2024-06-21 11:46:21.321 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] schema data cleaned 
[INFO ] 2024-06-21 11:46:21.321 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] monitor closed 
[INFO ] 2024-06-21 11:46:21.324 - [研祥任务测试][IdentityServerIdentityResourceProperties] - Node IdentityServerIdentityResourceProperties[46487505-053d-4a15-92a3-555e88315d55] close complete, cost 3039 ms 
[INFO ] 2024-06-21 11:46:21.325 - [研祥任务测试][TEST_001] - Node CLAIM[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] running status set to false 
[INFO ] 2024-06-21 11:46:21.345 - [研祥任务测试][TEST_001] - PDK connector node stopped: HazelcastTargetPdkDataNode-42b8cbf1-84b8-4a4d-8c00-5d428eb43686 
[INFO ] 2024-06-21 11:46:21.345 - [研祥任务测试][TEST_001] - PDK connector node released: HazelcastTargetPdkDataNode-42b8cbf1-84b8-4a4d-8c00-5d428eb43686 
[INFO ] 2024-06-21 11:46:21.346 - [研祥任务测试][TEST_001] - Node CLAIM[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] schema data cleaned 
[INFO ] 2024-06-21 11:46:21.348 - [研祥任务测试][TEST_001] - Node CLAIM[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] monitor closed 
[INFO ] 2024-06-21 11:46:21.348 - [研祥任务测试][TEST_001] - Node CLAIM[42b8cbf1-84b8-4a4d-8c00-5d428eb43686] close complete, cost 23 ms 
[INFO ] 2024-06-21 11:46:21.663 - [研祥任务测试] - Task [研祥任务测试] cannot retry, reason: Task retry service not start 
[INFO ] 2024-06-21 11:46:21.691 - [研祥任务测试] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-21 11:46:21.694 - [研祥任务测试] - Stop task milestones: 6672a09dc432b64620be6e23(研祥任务测试)  
[INFO ] 2024-06-21 11:46:21.715 - [研祥任务测试] - Stopped task aspect(s) 
[INFO ] 2024-06-21 11:46:21.715 - [研祥任务测试] - Snapshot order controller have been removed 
[INFO ] 2024-06-21 11:46:21.737 - [研祥任务测试] - Remove memory task client succeed, task: 研祥任务测试[6672a09dc432b64620be6e23] 
[INFO ] 2024-06-21 11:46:21.737 - [研祥任务测试] - Destroy memory task client cache succeed, task: 研祥任务测试[6672a09dc432b64620be6e23] 
