[INFO ] 2024-07-16 08:03:59.028 - [Heartbeat-Mysql] - Start task milestones: 6695b8ee6d76494ed53f38d7(Heartbeat-Mysql) 
[INFO ] 2024-07-16 08:03:59.769 - [Heartbeat-Mysql] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-16 08:03:59.769 - [Heartbeat-Mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-16 08:03:59.770 - [Heartbeat-Mysql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7407114d 
[INFO ] 2024-07-16 08:03:59.771 - [Heartbeat-Mysql] - Stop task milestones: 6695b8ee6d76494ed53f38d7(Heartbeat-Mysql)  
[INFO ] 2024-07-16 08:03:59.909 - [Heartbeat-Mysql] - Stopped task aspect(s) 
[INFO ] 2024-07-16 08:03:59.913 - [Heartbeat-Mysql] - Snapshot order controller have been removed 
[ERROR] 2024-07-16 08:03:59.916 - [Heartbeat-Mysql] - Node [id 2995919f-59e3-4d38-bb72-5cfd49254e2a, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 2995919f-59e3-4d38-bb72-5cfd49254e2a, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

