[INFO ] 2024-07-11 02:06:30.348 - [任务 48] - Start task milestones: 668a8361c830314239d1b276(任务 48) 
[INFO ] 2024-07-11 02:06:30.548 - [任务 48] - Task initialization... 
[INFO ] 2024-07-11 02:06:33.819 - [任务 48] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-11 02:06:33.899 - [任务 48] - The engine receives 任务 48 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-11 02:11:48.354 - [任务 48] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-11 02:11:48.355 - [任务 48] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1d3b79e7 
[INFO ] 2024-07-11 02:11:48.356 - [任务 48] - Stop task milestones: 668a8361c830314239d1b276(任务 48)  
[INFO ] 2024-07-11 02:11:48.492 - [任务 48] - Stopped task aspect(s) 
[INFO ] 2024-07-11 02:11:48.523 - [任务 48] - Snapshot order controller have been removed 
[INFO ] 2024-07-11 02:11:48.524 - [任务 48] - Remove memory task client succeed, task: 任务 48[668a8361c830314239d1b276] 
[INFO ] 2024-07-11 02:11:48.524 - [任务 48] - Destroy memory task client cache succeed, task: 任务 48[668a8361c830314239d1b276] 
[INFO ] 2024-07-11 02:11:53.815 - [任务 48] - Start task milestones: 668a8361c830314239d1b276(任务 48) 
[INFO ] 2024-07-11 02:11:54.023 - [任务 48] - Task initialization... 
[INFO ] 2024-07-11 02:11:54.532 - [任务 48] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-11 02:12:40.224 - [任务 48] - The engine receives 任务 48 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-11 02:12:45.267 - [任务 48] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-11 02:12:45.272 - [任务 48] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@36d17aab 
[INFO ] 2024-07-11 02:12:45.273 - [任务 48] - Stop task milestones: 668a8361c830314239d1b276(任务 48)  
[INFO ] 2024-07-11 02:12:45.426 - [任务 48] - Stopped task aspect(s) 
[INFO ] 2024-07-11 02:12:45.427 - [任务 48] - Snapshot order controller have been removed 
[INFO ] 2024-07-11 02:12:45.460 - [任务 48] - Remove memory task client succeed, task: 任务 48[668a8361c830314239d1b276] 
[INFO ] 2024-07-11 02:12:45.462 - [任务 48] - Destroy memory task client cache succeed, task: 任务 48[668a8361c830314239d1b276] 
