[INFO ] 2024-06-30 15:53:32.688 - [任务 35] - Task initialization... 
[INFO ] 2024-06-30 15:53:32.859 - [任务 35] - Start task milestones: 66810ed36fad3a13e50cb846(任务 35) 
[INFO ] 2024-06-30 15:53:32.859 - [任务 35] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-06-30 15:53:32.957 - [任务 35] - The engine receives 任务 35 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-30 15:53:32.958 - [任务 35][表编辑] - Node 表编辑[b7cbc264-165e-42f1-bdb7-8dfb22161c25] start preload schema,table counts: 2 
[INFO ] 2024-06-30 15:53:32.958 - [任务 35][SouceMysql] - Node SouceMysql[45ab0094-7b85-478b-ba1f-8cf9dc7f826f] start preload schema,table counts: 2 
[INFO ] 2024-06-30 15:53:32.958 - [任务 35][表编辑] - Node 表编辑[b7cbc264-165e-42f1-bdb7-8dfb22161c25] preload schema finished, cost 0 ms 
[INFO ] 2024-06-30 15:53:32.958 - [任务 35][SouceMysql] - Node SouceMysql[45ab0094-7b85-478b-ba1f-8cf9dc7f826f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-30 15:53:33.004 - [任务 35][SourceMongo] - Node SourceMongo[34be6ee4-a6e8-4284-94e1-e1ff1c32492a] start preload schema,table counts: 2 
[INFO ] 2024-06-30 15:53:33.004 - [任务 35][SourceMongo] - Node SourceMongo[34be6ee4-a6e8-4284-94e1-e1ff1c32492a] preload schema finished, cost 0 ms 
[INFO ] 2024-06-30 15:53:33.720 - [任务 35][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-06-30 15:53:33.720 - [任务 35][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-06-30 15:53:33.720 - [任务 35][SouceMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-30 15:53:33.721 - [任务 35][SouceMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":157,"gtidSet":""} 
[INFO ] 2024-06-30 15:53:33.786 - [任务 35][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-30 15:53:33.790 - [任务 35][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-30 15:53:33.791 - [任务 35][SouceMysql] - Initial sync started 
[INFO ] 2024-06-30 15:53:33.792 - [任务 35][SouceMysql] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-06-30 15:53:33.883 - [任务 35][SouceMysql] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-06-30 15:53:33.883 - [任务 35][SouceMysql] - Query table 'CUSTOMER' counts: 674 
[INFO ] 2024-06-30 15:53:34.005 - [任务 35][SouceMysql] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-30 15:53:34.006 - [任务 35][SouceMysql] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-30 15:53:34.006 - [任务 35][SouceMysql] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-30 15:53:34.016 - [任务 35][SouceMysql] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-30 15:53:34.214 - [任务 35][SouceMysql] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-30 15:53:34.225 - [任务 35][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-30 15:53:34.226 - [任务 35][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-30 15:53:34.226 - [任务 35][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-30 15:53:34.226 - [任务 35][SouceMysql] - Starting stream read, table list: [CUSTOMER, CLAIM], offset: {"filename":"binlog.000032","position":157,"gtidSet":""} 
[INFO ] 2024-06-30 15:53:34.284 - [任务 35][SouceMysql] - Starting mysql cdc, server name: 6f90562b-6a29-4252-b120-83283ee2fbb9 
[INFO ] 2024-06-30 15:53:34.286 - [任务 35][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1141357020
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 6f90562b-6a29-4252-b120-83283ee2fbb9
  database.port: 3306
  threadName: Debezium-Mysql-Connector-6f90562b-6a29-4252-b120-83283ee2fbb9
  database.hostname: localhost
  database.password: ********
  name: 6f90562b-6a29-4252-b120-83283ee2fbb9
  pdk.offset.string: {"name":"6f90562b-6a29-4252-b120-83283ee2fbb9","offset":{"{\"server\":\"6f90562b-6a29-4252-b120-83283ee2fbb9\"}":"{\"file\":\"binlog.000032\",\"pos\":157,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CUSTOMER,test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-30 15:53:34.492 - [任务 35][SouceMysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER, CLAIM], data change syncing 
[INFO ] 2024-06-30 15:54:48.712 - [任务 35] - Stop task milestones: 66810ed36fad3a13e50cb846(任务 35)  
[INFO ] 2024-06-30 15:54:48.924 - [任务 35][SouceMysql] - Node SouceMysql[45ab0094-7b85-478b-ba1f-8cf9dc7f826f] running status set to false 
[INFO ] 2024-06-30 15:54:48.927 - [任务 35][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-30 15:54:48.944 - [任务 35][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-06-30 15:54:48.945 - [任务 35][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-45ab0094-7b85-478b-ba1f-8cf9dc7f826f 
[INFO ] 2024-06-30 15:54:48.945 - [任务 35][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-45ab0094-7b85-478b-ba1f-8cf9dc7f826f 
[INFO ] 2024-06-30 15:54:48.945 - [任务 35][SouceMysql] - Node SouceMysql[45ab0094-7b85-478b-ba1f-8cf9dc7f826f] schema data cleaned 
[INFO ] 2024-06-30 15:54:48.945 - [任务 35][SouceMysql] - Node SouceMysql[45ab0094-7b85-478b-ba1f-8cf9dc7f826f] monitor closed 
[INFO ] 2024-06-30 15:54:48.945 - [任务 35][SouceMysql] - Node SouceMysql[45ab0094-7b85-478b-ba1f-8cf9dc7f826f] close complete, cost 135 ms 
[INFO ] 2024-06-30 15:54:48.946 - [任务 35][表编辑] - Node 表编辑[b7cbc264-165e-42f1-bdb7-8dfb22161c25] running status set to false 
[INFO ] 2024-06-30 15:54:48.960 - [任务 35][表编辑] - Node 表编辑[b7cbc264-165e-42f1-bdb7-8dfb22161c25] schema data cleaned 
[INFO ] 2024-06-30 15:54:48.961 - [任务 35][表编辑] - Node 表编辑[b7cbc264-165e-42f1-bdb7-8dfb22161c25] monitor closed 
[INFO ] 2024-06-30 15:54:48.961 - [任务 35][表编辑] - Node 表编辑[b7cbc264-165e-42f1-bdb7-8dfb22161c25] close complete, cost 14 ms 
[INFO ] 2024-06-30 15:54:48.981 - [任务 35][SourceMongo] - Node SourceMongo[34be6ee4-a6e8-4284-94e1-e1ff1c32492a] running status set to false 
[INFO ] 2024-06-30 15:54:48.982 - [任务 35][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-34be6ee4-a6e8-4284-94e1-e1ff1c32492a 
[INFO ] 2024-06-30 15:54:48.982 - [任务 35][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-34be6ee4-a6e8-4284-94e1-e1ff1c32492a 
[INFO ] 2024-06-30 15:54:48.982 - [任务 35][SourceMongo] - Node SourceMongo[34be6ee4-a6e8-4284-94e1-e1ff1c32492a] schema data cleaned 
[INFO ] 2024-06-30 15:54:48.982 - [任务 35][SourceMongo] - Node SourceMongo[34be6ee4-a6e8-4284-94e1-e1ff1c32492a] monitor closed 
[INFO ] 2024-06-30 15:54:49.190 - [任务 35][SourceMongo] - Node SourceMongo[34be6ee4-a6e8-4284-94e1-e1ff1c32492a] close complete, cost 21 ms 
[INFO ] 2024-06-30 15:54:51.229 - [任务 35] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-30 15:54:51.232 - [任务 35] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7e72bb0f 
[INFO ] 2024-06-30 15:54:51.232 - [任务 35] - Stopped task aspect(s) 
[INFO ] 2024-06-30 15:54:51.232 - [任务 35] - Snapshot order controller have been removed 
[INFO ] 2024-06-30 15:54:51.260 - [任务 35] - Remove memory task client succeed, task: 任务 35[66810ed36fad3a13e50cb846] 
[INFO ] 2024-06-30 15:54:51.260 - [任务 35] - Destroy memory task client cache succeed, task: 任务 35[66810ed36fad3a13e50cb846] 
