[INFO ] 2024-07-23 14:21:36.181 - [任务 15] - Task initialization... 
[INFO ] 2024-07-23 14:21:36.187 - [任务 15] - Start task milestones: 669f4bcee29f7d4f8d09721a(任务 15) 
[INFO ] 2024-07-23 14:21:36.592 - [任务 15] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-23 14:21:36.704 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-23 14:21:36.704 - [任务 15][POLICY] - Node POLICY[ba7948fc-905e-4d8c-92d1-152b8ed0d3c6] start preload schema,table counts: 1 
[INFO ] 2024-07-23 14:21:36.706 - [任务 15][dummy_test] - Node dummy_test[29de226c-552a-4f6c-99fe-e2f0b2bf7a30] start preload schema,table counts: 1 
[INFO ] 2024-07-23 14:21:36.707 - [任务 15][POLICY] - Node POLICY[ba7948fc-905e-4d8c-92d1-152b8ed0d3c6] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 14:21:36.708 - [任务 15][dummy_test] - Node dummy_test[29de226c-552a-4f6c-99fe-e2f0b2bf7a30] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 14:21:37.514 - [任务 15][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-23 14:21:37.711 - [任务 15][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-23 14:21:37.712 - [任务 15][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-23 14:21:37.712 - [任务 15][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-23 14:21:37.776 - [任务 15][POLICY] - batch offset found: {},stream offset found: {} 
[INFO ] 2024-07-23 14:21:37.776 - [任务 15][POLICY] - Initial sync started 
[INFO ] 2024-07-23 14:21:37.786 - [任务 15][POLICY] - Starting batch read, table name: POLICY 
[INFO ] 2024-07-23 14:21:37.787 - [任务 15][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-23 14:21:37.931 - [任务 15][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-23 14:21:37.936 - [任务 15][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-23 14:21:37.937 - [任务 15][POLICY] - Initial sync completed 
[INFO ] 2024-07-23 14:21:37.940 - [任务 15][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-23 14:21:37.940 - [任务 15][POLICY] - Initial sync completed 
[INFO ] 2024-07-23 14:21:37.941 - [任务 15][POLICY] - Start run table [POLICY] polling cdc with parameters 
 - Conditional field(s): {POLICY={LAST_CHANGE=DateTime nano 100000000 seconds 1562300023 timeZone null}}
 - Loop polling interval: 500 ms
 - Batch size: 1000 
[INFO ] 2024-07-23 14:21:37.942 - [任务 15][POLICY] - Query by advance filter
 - loop time: 1
 - table: POLICY
 - filter: [LAST_CHANGE>'DateTime nano 100000000 seconds 1562300023 timeZone null']
 - limit: 1000
 - sort: [LAST_CHANGE ASC] 
[INFO ] 2024-07-23 14:24:18.049 - [任务 15][POLICY] - Node POLICY[ba7948fc-905e-4d8c-92d1-152b8ed0d3c6] running status set to false 
[INFO ] 2024-07-23 14:24:18.050 - [任务 15][POLICY] - Incremental sync completed 
[INFO ] 2024-07-23 14:24:18.067 - [任务 15][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-ba7948fc-905e-4d8c-92d1-152b8ed0d3c6 
[INFO ] 2024-07-23 14:24:18.074 - [任务 15][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-ba7948fc-905e-4d8c-92d1-152b8ed0d3c6 
[INFO ] 2024-07-23 14:24:18.075 - [任务 15][POLICY] - Node POLICY[ba7948fc-905e-4d8c-92d1-152b8ed0d3c6] schema data cleaned 
[INFO ] 2024-07-23 14:24:18.076 - [任务 15][POLICY] - Node POLICY[ba7948fc-905e-4d8c-92d1-152b8ed0d3c6] monitor closed 
[INFO ] 2024-07-23 14:24:18.085 - [任务 15][POLICY] - Node POLICY[ba7948fc-905e-4d8c-92d1-152b8ed0d3c6] close complete, cost 57 ms 
[INFO ] 2024-07-23 14:24:18.085 - [任务 15][dummy_test] - Node dummy_test[29de226c-552a-4f6c-99fe-e2f0b2bf7a30] running status set to false 
[INFO ] 2024-07-23 14:24:18.098 - [任务 15][dummy_test] - Stop connector: first 1721715697879 44ms, last 1721715779442 63866ms, counts: 696/145429ms, min: 44, max: 63866, QPS: 4/s 
[INFO ] 2024-07-23 14:24:18.099 - [任务 15][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-29de226c-552a-4f6c-99fe-e2f0b2bf7a30 
[INFO ] 2024-07-23 14:24:18.099 - [任务 15][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-29de226c-552a-4f6c-99fe-e2f0b2bf7a30 
[INFO ] 2024-07-23 14:24:18.100 - [任务 15][dummy_test] - Node dummy_test[29de226c-552a-4f6c-99fe-e2f0b2bf7a30] schema data cleaned 
[INFO ] 2024-07-23 14:24:18.100 - [任务 15][dummy_test] - Node dummy_test[29de226c-552a-4f6c-99fe-e2f0b2bf7a30] monitor closed 
[INFO ] 2024-07-23 14:24:18.303 - [任务 15][dummy_test] - Node dummy_test[29de226c-552a-4f6c-99fe-e2f0b2bf7a30] close complete, cost 19 ms 
[INFO ] 2024-07-23 14:24:22.739 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-23 14:24:22.739 - [任务 15] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2300dcf0 
[INFO ] 2024-07-23 14:24:22.863 - [任务 15] - Stop task milestones: 669f4bcee29f7d4f8d09721a(任务 15)  
[INFO ] 2024-07-23 14:24:22.863 - [任务 15] - Stopped task aspect(s) 
[INFO ] 2024-07-23 14:24:22.888 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2024-07-23 14:24:22.890 - [任务 15] - Remove memory task client succeed, task: 任务 15[669f4bcee29f7d4f8d09721a] 
[INFO ] 2024-07-23 14:24:22.890 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[669f4bcee29f7d4f8d09721a] 
