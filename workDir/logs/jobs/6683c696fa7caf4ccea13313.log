[INFO ] 2024-07-02 17:23:04.911 - [任务 42] - Start task milestones: 6683c696fa7caf4ccea13313(任务 42) 
[INFO ] 2024-07-02 17:23:04.914 - [任务 42] - Task initialization... 
[INFO ] 2024-07-02 17:23:05.127 - [任务 42] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:23:05.187 - [任务 42] - The engine receives 任务 42 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 17:23:05.218 - [任务 42][POLICY] - Node POLICY[29dd7f9e-3a2b-4a4b-8471-3cd55724a42f] start preload schema,table counts: 1 
[INFO ] 2024-07-02 17:23:05.219 - [任务 42][POLICY] - Node POLICY[29dd7f9e-3a2b-4a4b-8471-3cd55724a42f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 17:23:05.219 - [任务 42][POLICY] - Node POLICY[5435c356-3faa-43d6-8281-2c362ea213c8] start preload schema,table counts: 1 
[INFO ] 2024-07-02 17:23:05.219 - [任务 42][POLICY] - Node POLICY[5435c356-3faa-43d6-8281-2c362ea213c8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 17:23:05.862 - [任务 42][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-02 17:23:05.864 - [任务 42][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-02 17:23:05.864 - [任务 42][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-02 17:23:05.974 - [任务 42][POLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 17:23:05.974 - [任务 42][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1719912185,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 17:23:06.013 - [任务 42][POLICY] - Initial sync started 
[INFO ] 2024-07-02 17:23:06.013 - [任务 42][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-02 17:23:06.066 - [任务 42][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-02 17:23:06.066 - [任务 42][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-02 17:23:06.106 - [任务 42][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-02 17:23:06.108 - [任务 42][POLICY] - Initial sync completed 
[INFO ] 2024-07-02 17:23:06.108 - [任务 42][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-02 17:23:06.108 - [任务 42][POLICY] - Initial sync completed 
[INFO ] 2024-07-02 17:23:06.157 - [任务 42][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-02 17:23:06.157 - [任务 42][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceMongo enable share cdc: true 
[INFO ] 2024-07-02 17:23:06.167 - [任务 42][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 42 enable share cdc: true 
[INFO ] 2024-07-02 17:23:06.168 - [任务 42][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceMongo的共享挖掘任务 
[INFO ] 2024-07-02 17:23:06.184 - [任务 42][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-02 17:23:06.184 - [任务 42][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 17:23:06.263 - [任务 42][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 42', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 2 
[INFO ] 2024-07-02 17:23:06.263 - [任务 42][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-02 17:23:06.263 - [任务 42][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-02 17:23:06.263 - [任务 42][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-02 17:23:06.265 - [任务 42][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-02 17:23:06.265 - [任务 42][POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-02 17:23:06.289 - [任务 42][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 17:23:06.289 - [任务 42][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 42', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 2 
[INFO ] 2024-07-02 17:23:06.291 - [任务 42][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 42, external storage name: ExternalStorage_SHARE_CDC_553058525 
[INFO ] 2024-07-02 17:23:06.291 - [任务 42][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-02 17:23:06.297 - [任务 42][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-02T09:23:05.862Z): 3 
[INFO ] 2024-07-02 17:23:06.298 - [任务 42][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 17:23:06.298 - [任务 42][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 3 
[INFO ] 2024-07-02 17:23:06.506 - [任务 42][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=3} 
[INFO ] 2024-07-02 17:23:34.657 - [任务 42][POLICY] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=POLICY, timestamp=1719912211000, date=Tue Jul 02 17:23:31 CST 2024, before=Document{{}}, after=Document{{_id=[B@242ef0ad, POLICY_ID=PC_000000007, CAR_MODEL=Volklf, COVER_START=Fri Jul 31 16:57:37 CST 2009, CUSTOMER_ID=C000022027, LAST_ANN_PREMIUM_GROSS=11.94, LAST_CHANGE=Fri Jul 05 09:13:42 CST 2019, MAX_COVERED=100000.0}}, op=u, offsetString=gAFkABVvcmcuYnNvbi5Cc29uRG9jdW1lbnQBFAAFX2RhdGEBAgATb3JnLmJzb24uQnNvblN0cmlu
ZwAAAdR7ImFycmF5IjpmYWxzZSwiYmluYXJ5IjpmYWxzZSwiYm9vbGVhbiI6ZmFsc2UsImJzb25U
eXBlIjoiU1RSSU5HIiwiZEJQb2ludGVyIjpmYWxzZSwiZGF0ZVRpbWUiOmZhbHNlLCJkZWNpbWFs
MTI4IjpmYWxzZSwiZG9jdW1lbnQiOmZhbHNlLCJkb3VibGUiOmZhbHNlLCJpbnQzMiI6ZmFsc2Us
ImludDY0IjpmYWxzZSwiamF2YVNjcmlwdCI6ZmFsc2UsImphdmFTY3JpcHRXaXRoU2NvcGUiOmZh
bHNlLCJudWxsIjpmYWxzZSwibnVtYmVyIjpmYWxzZSwib2JqZWN0SWQiOmZhbHNlLCJyZWd1bGFy
RXhwcmVzc2lvbiI6ZmFsc2UsInN0cmluZyI6dHJ1ZSwic3ltYm9sIjpmYWxzZSwidGltZXN0YW1w
IjpmYWxzZSwidmFsdWUiOiI4MjY2ODNDNzEzMDAwMDAwMDIyQjAyMkMwMTAwMjk2RTVBMTAwNDE5
QUMyQ0U0NUFGMjRGRDRCNDI1NjlBQzE4QzZGOEM3NDY2NDVGNjk2NDAwNjQ2NTEwRjc0Q0EyNzBB
MUNGNTUzM0NFRjkwMDA0In2o
, type=DATA, connectionId=6674feb868ca1e3afc2a0d99, isReplaceEvent=false, _ts=1719912212}} 
[INFO ] 2024-07-02 17:27:03.581 - [任务 42][POLICY] - Node POLICY[5435c356-3faa-43d6-8281-2c362ea213c8] running status set to false 
[INFO ] 2024-07-02 17:27:03.581 - [任务 42][POLICY] - Incremental sync completed 
[INFO ] 2024-07-02 17:27:03.593 - [任务 42][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-5435c356-3faa-43d6-8281-2c362ea213c8 
[INFO ] 2024-07-02 17:27:03.593 - [任务 42][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-5435c356-3faa-43d6-8281-2c362ea213c8 
[INFO ] 2024-07-02 17:27:03.593 - [任务 42][POLICY] - Node POLICY[5435c356-3faa-43d6-8281-2c362ea213c8] schema data cleaned 
[INFO ] 2024-07-02 17:27:03.595 - [任务 42][POLICY] - Node POLICY[5435c356-3faa-43d6-8281-2c362ea213c8] monitor closed 
[INFO ] 2024-07-02 17:27:03.596 - [任务 42][POLICY] - Node POLICY[5435c356-3faa-43d6-8281-2c362ea213c8] close complete, cost 45 ms 
[INFO ] 2024-07-02 17:27:03.608 - [任务 42][POLICY] - Node POLICY[29dd7f9e-3a2b-4a4b-8471-3cd55724a42f] running status set to false 
[INFO ] 2024-07-02 17:27:03.610 - [任务 42][POLICY] - PDK connector node stopped: HazelcastTargetPdkDataNode-29dd7f9e-3a2b-4a4b-8471-3cd55724a42f 
[INFO ] 2024-07-02 17:27:03.610 - [任务 42][POLICY] - PDK connector node released: HazelcastTargetPdkDataNode-29dd7f9e-3a2b-4a4b-8471-3cd55724a42f 
[INFO ] 2024-07-02 17:27:03.610 - [任务 42][POLICY] - Node POLICY[29dd7f9e-3a2b-4a4b-8471-3cd55724a42f] schema data cleaned 
[INFO ] 2024-07-02 17:27:03.611 - [任务 42][POLICY] - Node POLICY[29dd7f9e-3a2b-4a4b-8471-3cd55724a42f] monitor closed 
[INFO ] 2024-07-02 17:27:03.813 - [任务 42][POLICY] - Node POLICY[29dd7f9e-3a2b-4a4b-8471-3cd55724a42f] close complete, cost 15 ms 
[INFO ] 2024-07-02 17:27:04.570 - [任务 42] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:27:04.573 - [任务 42] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2e4a3e19 
[INFO ] 2024-07-02 17:27:04.573 - [任务 42] - Stop task milestones: 6683c696fa7caf4ccea13313(任务 42)  
[INFO ] 2024-07-02 17:27:04.751 - [任务 42] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:27:04.751 - [任务 42] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 17:27:04.791 - [任务 42] - Remove memory task client succeed, task: 任务 42[6683c696fa7caf4ccea13313] 
[INFO ] 2024-07-02 17:27:04.791 - [任务 42] - Destroy memory task client cache succeed, task: 任务 42[6683c696fa7caf4ccea13313] 
