[INFO ] 2024-07-12 11:03:39.370 - [任务 58] - Task initialization... 
[INFO ] 2024-07-12 11:03:39.573 - [任务 58] - Start task milestones: 66909c4e7c91bf6e98248af8(任务 58) 
[INFO ] 2024-07-12 11:03:39.630 - [任务 58] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-12 11:03:39.750 - [任务 58] - The engine receives 任务 58 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-12 11:03:39.751 - [任务 58][PolTest] - Node PolTest[edf45e97-7273-40a2-8834-fb846e397c67] start preload schema,table counts: 1 
[INFO ] 2024-07-12 11:03:39.754 - [任务 58][POLICY] - Node POLICY[430bfa22-252a-437c-9480-86c7c504678f] start preload schema,table counts: 1 
[INFO ] 2024-07-12 11:03:39.756 - [任务 58][POLICY] - Node POLICY[430bfa22-252a-437c-9480-86c7c504678f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 11:03:39.756 - [任务 58][PolTest] - Node PolTest[edf45e97-7273-40a2-8834-fb846e397c67] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 11:03:40.700 - [任务 58][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-12 11:03:40.702 - [任务 58][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-12 11:03:40.702 - [任务 58][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-12 11:03:40.706 - [任务 58][POLICY] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":92865303,"gtidSet":""} 
[INFO ] 2024-07-12 11:03:40.755 - [任务 58][POLICY] - Initial sync started 
[INFO ] 2024-07-12 11:03:40.755 - [任务 58][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-12 11:03:40.808 - [任务 58][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-12 11:03:40.809 - [任务 58][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-12 11:03:40.980 - [任务 58][PolTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-12 11:03:41.199 - [任务 58][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-12 11:03:41.205 - [任务 58][POLICY] - Initial sync completed 
[INFO ] 2024-07-12 11:03:41.230 - [任务 58][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-12 11:03:41.231 - [任务 58][POLICY] - Initial sync completed 
[INFO ] 2024-07-12 11:03:41.236 - [任务 58][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"filename":"binlog.000032","position":92865303,"gtidSet":""} 
[INFO ] 2024-07-12 11:03:41.291 - [任务 58][POLICY] - Starting mysql cdc, server name: ab283255-7838-4672-84d3-ea5658caeae2 
[INFO ] 2024-07-12 11:03:41.300 - [任务 58][POLICY] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1070483718
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ab283255-7838-4672-84d3-ea5658caeae2
  database.port: 3306
  threadName: Debezium-Mysql-Connector-ab283255-7838-4672-84d3-ea5658caeae2
  database.hostname: localhost
  database.password: ********
  name: ab283255-7838-4672-84d3-ea5658caeae2
  pdk.offset.string: {"name":"ab283255-7838-4672-84d3-ea5658caeae2","offset":{"{\"server\":\"ab283255-7838-4672-84d3-ea5658caeae2\"}":"{\"file\":\"binlog.000032\",\"pos\":92865303,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.POLICY,test._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-12 11:03:41.507 - [任务 58][POLICY] - Connector Mysql incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-12 11:03:59.995 - [任务 58][POLICY] - Node POLICY[430bfa22-252a-437c-9480-86c7c504678f] running status set to false 
[INFO ] 2024-07-12 11:03:59.995 - [任务 58][POLICY] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-12 11:03:59.997 - [任务 58][POLICY] - Mysql binlog reader stopped 
[INFO ] 2024-07-12 11:03:59.997 - [任务 58][POLICY] - Incremental sync completed 
[INFO ] 2024-07-12 11:04:00.006 - [任务 58][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-430bfa22-252a-437c-9480-86c7c504678f 
[INFO ] 2024-07-12 11:04:00.006 - [任务 58][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-430bfa22-252a-437c-9480-86c7c504678f 
[INFO ] 2024-07-12 11:04:00.006 - [任务 58][POLICY] - Node POLICY[430bfa22-252a-437c-9480-86c7c504678f] schema data cleaned 
[INFO ] 2024-07-12 11:04:00.009 - [任务 58][POLICY] - Node POLICY[430bfa22-252a-437c-9480-86c7c504678f] monitor closed 
[INFO ] 2024-07-12 11:04:00.009 - [任务 58][POLICY] - Node POLICY[430bfa22-252a-437c-9480-86c7c504678f] close complete, cost 149 ms 
[INFO ] 2024-07-12 11:04:00.010 - [任务 58][PolTest] - Node PolTest[edf45e97-7273-40a2-8834-fb846e397c67] running status set to false 
[INFO ] 2024-07-12 11:04:00.029 - [任务 58][PolTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-edf45e97-7273-40a2-8834-fb846e397c67 
[INFO ] 2024-07-12 11:04:00.030 - [任务 58][PolTest] - PDK connector node released: HazelcastTargetPdkDataNode-edf45e97-7273-40a2-8834-fb846e397c67 
[INFO ] 2024-07-12 11:04:00.031 - [任务 58][PolTest] - Node PolTest[edf45e97-7273-40a2-8834-fb846e397c67] schema data cleaned 
[INFO ] 2024-07-12 11:04:00.031 - [任务 58][PolTest] - Node PolTest[edf45e97-7273-40a2-8834-fb846e397c67] monitor closed 
[INFO ] 2024-07-12 11:04:00.236 - [任务 58][PolTest] - Node PolTest[edf45e97-7273-40a2-8834-fb846e397c67] close complete, cost 22 ms 
[INFO ] 2024-07-12 11:04:02.999 - [任务 58] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-12 11:04:02.999 - [任务 58] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@37cb8121 
[INFO ] 2024-07-12 11:04:03.120 - [任务 58] - Stop task milestones: 66909c4e7c91bf6e98248af8(任务 58)  
[INFO ] 2024-07-12 11:04:03.156 - [任务 58] - Stopped task aspect(s) 
[INFO ] 2024-07-12 11:04:03.156 - [任务 58] - Snapshot order controller have been removed 
[INFO ] 2024-07-12 11:04:03.183 - [任务 58] - Remove memory task client succeed, task: 任务 58[66909c4e7c91bf6e98248af8] 
[INFO ] 2024-07-12 11:04:03.184 - [任务 58] - Destroy memory task client cache succeed, task: 任务 58[66909c4e7c91bf6e98248af8] 
