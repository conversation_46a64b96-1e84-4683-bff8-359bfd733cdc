[INFO ] 2024-07-18 11:54:49.733 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844] - Task initialization... 
[INFO ] 2024-07-18 11:54:49.826 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844] - Start task milestones: 669891fe8315b25db9f5431d(t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844) 
[INFO ] 2024-07-18 11:54:49.974 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-18 11:54:50.032 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844] - The engine receives t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 11:54:50.074 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[a9447e65-c5e8-4e38-b121-6363bc1955be] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:54:50.074 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[cc5a23a3-a6a0-4974-877f-10e247b718f9] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:54:50.074 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[a9447e65-c5e8-4e38-b121-6363bc1955be] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:54:50.228 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[cc5a23a3-a6a0-4974-877f-10e247b718f9] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:54:50.550 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_6040_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 11:54:50.668 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Source node "qa_mongodb_repl_42240_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 11:54:50.668 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Source node "qa_mongodb_repl_42240_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 11:54:50.668 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 11:54:50.911 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - batch offset found: {},stream offset found: {"cdcOffset":1721274890,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 11:54:51.034 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 11:54:51.035 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Starting batch read, table name: t42240, offset: null 
[INFO ] 2024-07-18 11:54:51.058 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Table t42240 is going to be initial synced 
[INFO ] 2024-07-18 11:54:51.058 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Table [t42240] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:54:51.058 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Query table 't42240' counts: 1 
[INFO ] 2024-07-18 11:54:51.058 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:54:51.059 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 11:54:51.059 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:54:51.105 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Starting stream read, table list: [t42240, _tapdata_heartbeat_table], offset: {"cdcOffset":1721274890,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 11:54:51.105 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [t42240, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 11:56:58.609 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[cc5a23a3-a6a0-4974-877f-10e247b718f9] running status set to false 
[INFO ] 2024-07-18 11:56:58.638 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-cc5a23a3-a6a0-4974-877f-10e247b718f9 
[INFO ] 2024-07-18 11:56:58.638 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-cc5a23a3-a6a0-4974-877f-10e247b718f9 
[INFO ] 2024-07-18 11:56:58.638 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[cc5a23a3-a6a0-4974-877f-10e247b718f9] schema data cleaned 
[INFO ] 2024-07-18 11:56:58.638 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[cc5a23a3-a6a0-4974-877f-10e247b718f9] monitor closed 
[INFO ] 2024-07-18 11:56:58.641 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[cc5a23a3-a6a0-4974-877f-10e247b718f9] close complete, cost 64 ms 
[INFO ] 2024-07-18 11:56:58.672 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[a9447e65-c5e8-4e38-b121-6363bc1955be] running status set to false 
[INFO ] 2024-07-18 11:56:58.673 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-a9447e65-c5e8-4e38-b121-6363bc1955be 
[INFO ] 2024-07-18 11:56:58.673 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-a9447e65-c5e8-4e38-b121-6363bc1955be 
[INFO ] 2024-07-18 11:56:58.673 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[a9447e65-c5e8-4e38-b121-6363bc1955be] schema data cleaned 
[INFO ] 2024-07-18 11:56:58.673 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[a9447e65-c5e8-4e38-b121-6363bc1955be] monitor closed 
[INFO ] 2024-07-18 11:56:58.673 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[a9447e65-c5e8-4e38-b121-6363bc1955be] close complete, cost 32 ms 
[INFO ] 2024-07-18 11:56:59.103 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844][qa_mongodb_repl_42240_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 11:57:01.692 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 11:57:01.692 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@c0060f3 
[INFO ] 2024-07-18 11:57:01.821 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844] - Stop task milestones: 669891fe8315b25db9f5431d(t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844)  
[INFO ] 2024-07-18 11:57:01.854 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844] - Stopped task aspect(s) 
[INFO ] 2024-07-18 11:57:01.854 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 11:57:01.906 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844] - Remove memory task client succeed, task: t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844[669891fe8315b25db9f5431d] 
[INFO ] 2024-07-18 11:57:01.906 - [t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844] - Destroy memory task client cache succeed, task: t_4.2.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721274844[669891fe8315b25db9f5431d] 
