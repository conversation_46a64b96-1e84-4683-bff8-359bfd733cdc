[INFO ] 2024-11-13 11:40:36.431 - [任务 1] - Start task milestones: 67341f4058765d7645ee3f0b(任务 1) 
[INFO ] 2024-11-13 11:40:36.431 - [任务 1] - Task initialization... 
[INFO ] 2024-11-13 11:40:37.066 - [任务 1] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-11-13 11:40:37.272 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-13 11:40:37.648 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] start preload schema,table counts: 1 
[INFO ] 2024-11-13 11:40:37.648 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] start preload schema,table counts: 1 
[INFO ] 2024-11-13 11:40:37.650 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-13 11:40:37.650 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] preload schema finished, cost 0 ms 
[INFO ] 2024-11-13 11:40:38.838 - [任务 1][testDateTime] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-13 11:40:38.838 - [任务 1][testDateTime] - Source node "testDateTime" read batch size: 100 
[INFO ] 2024-11-13 11:40:38.838 - [任务 1][testDateTime] - Source node "testDateTime" event queue capacity: 200 
[INFO ] 2024-11-13 11:40:38.839 - [任务 1][testDateTime] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-13 11:40:38.860 - [任务 1][testDateTime] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":32659,"gtidSet":""} 
[INFO ] 2024-11-13 11:40:39.002 - [任务 1][testDateTime] - Initial sync started 
[INFO ] 2024-11-13 11:40:39.002 - [任务 1][testDateTime] - Starting batch read, table name: testDateTime, offset: null 
[INFO ] 2024-11-13 11:40:39.081 - [任务 1][testDateTime] - Table testDateTime is going to be initial synced 
[INFO ] 2024-11-13 11:40:39.081 - [任务 1][testDateTime] - Query table 'testDateTime' counts: 1 
[INFO ] 2024-11-13 11:40:39.084 - [任务 1][testDateTime] - Table [testDateTime] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-13 11:40:39.085 - [任务 1][testDateTime] - Initial sync completed 
[INFO ] 2024-11-13 11:40:39.087 - [任务 1][testDateTime] - Incremental sync starting... 
[INFO ] 2024-11-13 11:40:39.087 - [任务 1][testDateTime] - Initial sync completed 
[INFO ] 2024-11-13 11:40:39.130 - [任务 1][testDateTime] - Starting stream read, table list: [testDateTime], offset: {"filename":"binlog.000038","position":32659,"gtidSet":""} 
[INFO ] 2024-11-13 11:40:39.131 - [任务 1][testDateTime] - Starting mysql cdc, server name: f319a9a6-f15f-4969-9c61-93792f03fc74 
[INFO ] 2024-11-13 11:40:39.334 - [任务 1][testDateTime] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 579466104
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f319a9a6-f15f-4969-9c61-93792f03fc74
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f319a9a6-f15f-4969-9c61-93792f03fc74
  enable.time.adjuster: false
  database.hostname: localhost
  database.password: ********
  name: f319a9a6-f15f-4969-9c61-93792f03fc74
  pdk.offset.string: {"name":"f319a9a6-f15f-4969-9c61-93792f03fc74","offset":{"{\"server\":\"f319a9a6-f15f-4969-9c61-93792f03fc74\"}":"{\"file\":\"binlog.000038\",\"pos\":32659,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDateTime
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-13 11:40:39.426 - [任务 1][testDateTime] - Connector Mysql incremental start succeed, tables: [testDateTime], data change syncing 
[INFO ] 2024-11-13 11:41:14.084 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] running status set to false 
[INFO ] 2024-11-13 11:41:14.086 - [任务 1][testDateTime] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-13 11:41:14.089 - [任务 1][testDateTime] - Mysql binlog reader stopped 
[INFO ] 2024-11-13 11:41:14.098 - [任务 1][testDateTime] - Incremental sync completed 
[INFO ] 2024-11-13 11:41:14.108 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 11:41:14.112 - [任务 1][testDateTime] - PDK connector node released: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 11:41:14.112 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] schema data cleaned 
[INFO ] 2024-11-13 11:41:14.123 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] monitor closed 
[INFO ] 2024-11-13 11:41:14.124 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] close complete, cost 103 ms 
[INFO ] 2024-11-13 11:41:14.153 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] running status set to false 
[INFO ] 2024-11-13 11:41:14.153 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 11:41:14.154 - [任务 1][testDateTime] - PDK connector node released: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 11:41:14.154 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] schema data cleaned 
[INFO ] 2024-11-13 11:41:14.155 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] monitor closed 
[INFO ] 2024-11-13 11:41:14.155 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] close complete, cost 31 ms 
[INFO ] 2024-11-13 11:41:16.511 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-13 11:41:16.513 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5ac5f9e8 
[INFO ] 2024-11-13 11:41:16.525 - [任务 1] - Stop task milestones: 67341f4058765d7645ee3f0b(任务 1)  
[INFO ] 2024-11-13 11:41:16.682 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-11-13 11:41:16.683 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-11-13 11:41:16.893 - [任务 1] - Remove memory task client succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
[INFO ] 2024-11-13 11:41:16.894 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
[INFO ] 2024-11-13 11:41:48.887 - [任务 1] - Start task milestones: 67341f4058765d7645ee3f0b(任务 1) 
[INFO ] 2024-11-13 11:41:48.888 - [任务 1] - Task initialization... 
[INFO ] 2024-11-13 11:41:49.024 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-13 11:41:49.129 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-13 11:41:49.129 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] start preload schema,table counts: 1 
[INFO ] 2024-11-13 11:41:49.133 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] start preload schema,table counts: 1 
[INFO ] 2024-11-13 11:41:49.133 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-13 11:41:49.334 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] preload schema finished, cost 0 ms 
[INFO ] 2024-11-13 11:41:49.963 - [任务 1][testDateTime] - Source node "testDateTime" read batch size: 100 
[INFO ] 2024-11-13 11:41:49.963 - [任务 1][testDateTime] - Source node "testDateTime" event queue capacity: 200 
[INFO ] 2024-11-13 11:41:49.964 - [任务 1][testDateTime] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-13 11:41:49.971 - [任务 1][testDateTime] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":32659,"gtidSet":""} 
[INFO ] 2024-11-13 11:41:50.018 - [任务 1][testDateTime] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-13 11:41:50.019 - [任务 1][testDateTime] - Initial sync started 
[INFO ] 2024-11-13 11:41:50.026 - [任务 1][testDateTime] - Starting batch read, table name: testDateTime, offset: null 
[INFO ] 2024-11-13 11:41:50.028 - [任务 1][testDateTime] - Table testDateTime is going to be initial synced 
[INFO ] 2024-11-13 11:41:50.089 - [任务 1][testDateTime] - Query table 'testDateTime' counts: 1 
[INFO ] 2024-11-13 11:41:50.090 - [任务 1][testDateTime] - Table [testDateTime] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-13 11:41:50.090 - [任务 1][testDateTime] - Initial sync completed 
[INFO ] 2024-11-13 11:41:50.090 - [任务 1][testDateTime] - Incremental sync starting... 
[INFO ] 2024-11-13 11:41:50.090 - [任务 1][testDateTime] - Initial sync completed 
[INFO ] 2024-11-13 11:41:50.090 - [任务 1][testDateTime] - Starting stream read, table list: [testDateTime], offset: {"filename":"binlog.000038","position":32659,"gtidSet":""} 
[INFO ] 2024-11-13 11:41:50.127 - [任务 1][testDateTime] - Starting mysql cdc, server name: 2fc2b9b8-26a7-4ee1-a5e6-c5585e6e2596 
[INFO ] 2024-11-13 11:41:50.129 - [任务 1][testDateTime] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1556675978
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 2fc2b9b8-26a7-4ee1-a5e6-c5585e6e2596
  database.port: 3306
  threadName: Debezium-Mysql-Connector-2fc2b9b8-26a7-4ee1-a5e6-c5585e6e2596
  enable.time.adjuster: false
  database.hostname: localhost
  database.password: ********
  name: 2fc2b9b8-26a7-4ee1-a5e6-c5585e6e2596
  pdk.offset.string: {"name":"2fc2b9b8-26a7-4ee1-a5e6-c5585e6e2596","offset":{"{\"server\":\"2fc2b9b8-26a7-4ee1-a5e6-c5585e6e2596\"}":"{\"file\":\"binlog.000038\",\"pos\":32659,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDateTime
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-13 11:41:50.336 - [任务 1][testDateTime] - Connector Mysql incremental start succeed, tables: [testDateTime], data change syncing 
[INFO ] 2024-11-13 11:42:29.879 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] running status set to false 
[INFO ] 2024-11-13 11:42:29.972 - [任务 1][testDateTime] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-13 11:42:29.973 - [任务 1][testDateTime] - Mysql binlog reader stopped 
[INFO ] 2024-11-13 11:42:29.984 - [任务 1][testDateTime] - Incremental sync completed 
[INFO ] 2024-11-13 11:42:29.992 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 11:42:29.993 - [任务 1][testDateTime] - PDK connector node released: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 11:42:29.993 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] schema data cleaned 
[INFO ] 2024-11-13 11:42:29.998 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] monitor closed 
[INFO ] 2024-11-13 11:42:30.001 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] close complete, cost 127 ms 
[INFO ] 2024-11-13 11:42:30.002 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] running status set to false 
[INFO ] 2024-11-13 11:42:30.044 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 11:42:30.047 - [任务 1][testDateTime] - PDK connector node released: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 11:42:30.047 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] schema data cleaned 
[INFO ] 2024-11-13 11:42:30.054 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] monitor closed 
[INFO ] 2024-11-13 11:42:30.054 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] close complete, cost 51 ms 
[INFO ] 2024-11-13 11:42:31.994 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-13 11:42:32.018 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@49169eb3 
[INFO ] 2024-11-13 11:42:32.018 - [任务 1] - Stop task milestones: 67341f4058765d7645ee3f0b(任务 1)  
[INFO ] 2024-11-13 11:42:32.176 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-11-13 11:42:32.177 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-11-13 11:42:32.219 - [任务 1] - Remove memory task client succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
[INFO ] 2024-11-13 11:42:32.220 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
[INFO ] 2024-11-13 11:48:31.029 - [任务 1] - Start task milestones: 67341f4058765d7645ee3f0b(任务 1) 
[INFO ] 2024-11-13 11:48:31.190 - [任务 1] - Task initialization... 
[INFO ] 2024-11-13 11:48:31.191 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-13 11:48:31.277 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-13 11:48:31.277 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] start preload schema,table counts: 1 
[INFO ] 2024-11-13 11:48:31.277 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] start preload schema,table counts: 1 
[INFO ] 2024-11-13 11:48:31.277 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] preload schema finished, cost 0 ms 
[INFO ] 2024-11-13 11:48:31.277 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-13 11:48:32.117 - [任务 1][testDateTime] - Source node "testDateTime" read batch size: 100 
[INFO ] 2024-11-13 11:48:32.117 - [任务 1][testDateTime] - Source node "testDateTime" event queue capacity: 200 
[INFO ] 2024-11-13 11:48:32.118 - [任务 1][testDateTime] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-13 11:48:32.119 - [任务 1][testDateTime] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":32659,"gtidSet":""} 
[INFO ] 2024-11-13 11:48:32.186 - [任务 1][testDateTime] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-13 11:48:32.186 - [任务 1][testDateTime] - Initial sync started 
[INFO ] 2024-11-13 11:48:32.186 - [任务 1][testDateTime] - Starting batch read, table name: testDateTime, offset: null 
[INFO ] 2024-11-13 11:48:32.194 - [任务 1][testDateTime] - Table testDateTime is going to be initial synced 
[INFO ] 2024-11-13 11:48:32.241 - [任务 1][testDateTime] - Table [testDateTime] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-13 11:48:32.242 - [任务 1][testDateTime] - Query table 'testDateTime' counts: 1 
[INFO ] 2024-11-13 11:48:32.242 - [任务 1][testDateTime] - Initial sync completed 
[INFO ] 2024-11-13 11:48:32.245 - [任务 1][testDateTime] - Incremental sync starting... 
[INFO ] 2024-11-13 11:48:32.248 - [任务 1][testDateTime] - Initial sync completed 
[INFO ] 2024-11-13 11:48:32.251 - [任务 1][testDateTime] - Starting stream read, table list: [testDateTime], offset: {"filename":"binlog.000038","position":32659,"gtidSet":""} 
[INFO ] 2024-11-13 11:48:32.278 - [任务 1][testDateTime] - Starting mysql cdc, server name: c05c4b90-ebad-4e37-979c-0034f1be013a 
[INFO ] 2024-11-13 11:48:32.279 - [任务 1][testDateTime] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1224162398
  time.precision.mode: adaptive_time_microseconds
  database.server.name: c05c4b90-ebad-4e37-979c-0034f1be013a
  database.port: 3306
  threadName: Debezium-Mysql-Connector-c05c4b90-ebad-4e37-979c-0034f1be013a
  enable.time.adjuster: false
  database.hostname: localhost
  database.password: ********
  name: c05c4b90-ebad-4e37-979c-0034f1be013a
  pdk.offset.string: {"name":"c05c4b90-ebad-4e37-979c-0034f1be013a","offset":{"{\"server\":\"c05c4b90-ebad-4e37-979c-0034f1be013a\"}":"{\"file\":\"binlog.000038\",\"pos\":32659,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDateTime
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-13 11:48:32.489 - [任务 1][testDateTime] - Connector Mysql incremental start succeed, tables: [testDateTime], data change syncing 
[INFO ] 2024-11-13 12:04:13.139 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] running status set to false 
[INFO ] 2024-11-13 12:04:13.185 - [任务 1][testDateTime] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-13 12:04:13.185 - [任务 1][testDateTime] - Mysql binlog reader stopped 
[INFO ] 2024-11-13 12:04:13.185 - [任务 1][testDateTime] - Incremental sync completed 
[INFO ] 2024-11-13 12:04:13.200 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 12:04:13.202 - [任务 1][testDateTime] - PDK connector node released: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 12:04:13.202 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] schema data cleaned 
[INFO ] 2024-11-13 12:04:13.206 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] monitor closed 
[INFO ] 2024-11-13 12:04:13.207 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] close complete, cost 147 ms 
[INFO ] 2024-11-13 12:04:13.207 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] running status set to false 
[INFO ] 2024-11-13 12:04:13.230 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 12:04:13.230 - [任务 1][testDateTime] - PDK connector node released: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 12:04:13.230 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] schema data cleaned 
[INFO ] 2024-11-13 12:04:13.232 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] monitor closed 
[INFO ] 2024-11-13 12:04:13.232 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] close complete, cost 25 ms 
[INFO ] 2024-11-13 12:04:13.395 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-13 12:04:13.396 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@755a7644 
[INFO ] 2024-11-13 12:04:13.403 - [任务 1] - Stop task milestones: 67341f4058765d7645ee3f0b(任务 1)  
[INFO ] 2024-11-13 12:04:13.549 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-11-13 12:04:13.549 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-11-13 12:04:13.568 - [任务 1] - Remove memory task client succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
[INFO ] 2024-11-13 12:04:13.571 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
[INFO ] 2024-11-13 12:04:32.334 - [任务 1] - Start task milestones: 67341f4058765d7645ee3f0b(任务 1) 
[INFO ] 2024-11-13 12:04:32.335 - [任务 1] - Task initialization... 
[INFO ] 2024-11-13 12:04:32.454 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-13 12:04:32.454 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-13 12:04:32.493 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] start preload schema,table counts: 1 
[INFO ] 2024-11-13 12:04:32.493 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-13 12:04:32.494 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] start preload schema,table counts: 1 
[INFO ] 2024-11-13 12:04:32.695 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] preload schema finished, cost 0 ms 
[INFO ] 2024-11-13 12:04:33.332 - [任务 1][testDateTime] - Source node "testDateTime" read batch size: 100 
[INFO ] 2024-11-13 12:04:33.336 - [任务 1][testDateTime] - Source node "testDateTime" event queue capacity: 200 
[INFO ] 2024-11-13 12:04:33.346 - [任务 1][testDateTime] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-13 12:04:33.349 - [任务 1][testDateTime] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":32659,"gtidSet":""} 
[INFO ] 2024-11-13 12:04:33.411 - [任务 1][testDateTime] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-13 12:04:33.411 - [任务 1][testDateTime] - Initial sync started 
[INFO ] 2024-11-13 12:04:33.411 - [任务 1][testDateTime] - Starting batch read, table name: testDateTime, offset: null 
[INFO ] 2024-11-13 12:04:33.416 - [任务 1][testDateTime] - Table testDateTime is going to be initial synced 
[INFO ] 2024-11-13 12:04:33.457 - [任务 1][testDateTime] - Table [testDateTime] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-13 12:04:33.457 - [任务 1][testDateTime] - Query table 'testDateTime' counts: 1 
[INFO ] 2024-11-13 12:04:33.458 - [任务 1][testDateTime] - Initial sync completed 
[INFO ] 2024-11-13 12:04:33.458 - [任务 1][testDateTime] - Incremental sync starting... 
[INFO ] 2024-11-13 12:04:33.465 - [任务 1][testDateTime] - Initial sync completed 
[INFO ] 2024-11-13 12:04:33.465 - [任务 1][testDateTime] - Starting stream read, table list: [testDateTime], offset: {"filename":"binlog.000038","position":32659,"gtidSet":""} 
[INFO ] 2024-11-13 12:04:33.491 - [任务 1][testDateTime] - Starting mysql cdc, server name: 708934cb-71ce-45c3-8e80-564faae95810 
[INFO ] 2024-11-13 12:04:33.492 - [任务 1][testDateTime] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 877266176
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 708934cb-71ce-45c3-8e80-564faae95810
  database.port: 3306
  threadName: Debezium-Mysql-Connector-708934cb-71ce-45c3-8e80-564faae95810
  enable.time.adjuster: false
  database.hostname: localhost
  database.password: ********
  name: 708934cb-71ce-45c3-8e80-564faae95810
  pdk.offset.string: {"name":"708934cb-71ce-45c3-8e80-564faae95810","offset":{"{\"server\":\"708934cb-71ce-45c3-8e80-564faae95810\"}":"{\"file\":\"binlog.000038\",\"pos\":32659,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDateTime
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-13 12:04:33.702 - [任务 1][testDateTime] - Connector Mysql incremental start succeed, tables: [testDateTime], data change syncing 
[INFO ] 2024-11-13 12:04:50.311 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] running status set to false 
[INFO ] 2024-11-13 12:04:50.433 - [任务 1][testDateTime] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-13 12:04:50.433 - [任务 1][testDateTime] - Mysql binlog reader stopped 
[INFO ] 2024-11-13 12:04:50.434 - [任务 1][testDateTime] - Incremental sync completed 
[INFO ] 2024-11-13 12:04:50.439 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 12:04:50.439 - [任务 1][testDateTime] - PDK connector node released: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 12:04:50.440 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] schema data cleaned 
[INFO ] 2024-11-13 12:04:50.440 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] monitor closed 
[INFO ] 2024-11-13 12:04:50.445 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] close complete, cost 146 ms 
[INFO ] 2024-11-13 12:04:50.445 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] running status set to false 
[INFO ] 2024-11-13 12:04:50.472 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 12:04:50.473 - [任务 1][testDateTime] - PDK connector node released: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 12:04:50.473 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] schema data cleaned 
[INFO ] 2024-11-13 12:04:50.473 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] monitor closed 
[INFO ] 2024-11-13 12:04:50.474 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] close complete, cost 30 ms 
[INFO ] 2024-11-13 12:04:53.626 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-13 12:04:53.633 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4c568ff9 
[INFO ] 2024-11-13 12:04:53.633 - [任务 1] - Stop task milestones: 67341f4058765d7645ee3f0b(任务 1)  
[INFO ] 2024-11-13 12:04:53.758 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-11-13 12:04:53.761 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-11-13 12:04:53.791 - [任务 1] - Remove memory task client succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
[INFO ] 2024-11-13 12:04:53.792 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
[INFO ] 2024-11-13 12:05:04.803 - [任务 1] - Start task milestones: 67341f4058765d7645ee3f0b(任务 1) 
[INFO ] 2024-11-13 12:05:04.887 - [任务 1] - Task initialization... 
[INFO ] 2024-11-13 12:05:04.888 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-13 12:05:04.955 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-13 12:05:04.955 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] start preload schema,table counts: 1 
[INFO ] 2024-11-13 12:05:04.955 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] start preload schema,table counts: 1 
[INFO ] 2024-11-13 12:05:04.955 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] preload schema finished, cost 0 ms 
[INFO ] 2024-11-13 12:05:05.156 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-13 12:05:05.715 - [任务 1][testDateTime] - Source node "testDateTime" read batch size: 100 
[INFO ] 2024-11-13 12:05:05.715 - [任务 1][testDateTime] - Source node "testDateTime" event queue capacity: 200 
[INFO ] 2024-11-13 12:05:05.715 - [任务 1][testDateTime] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-13 12:05:05.718 - [任务 1][testDateTime] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":32659,"gtidSet":""} 
[INFO ] 2024-11-13 12:05:05.794 - [任务 1][testDateTime] - Initial sync started 
[INFO ] 2024-11-13 12:05:05.795 - [任务 1][testDateTime] - Starting batch read, table name: testDateTime, offset: null 
[INFO ] 2024-11-13 12:05:05.804 - [任务 1][testDateTime] - Table testDateTime is going to be initial synced 
[INFO ] 2024-11-13 12:05:05.876 - [任务 1][testDateTime] - Table [testDateTime] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-13 12:05:05.882 - [任务 1][testDateTime] - Query table 'testDateTime' counts: 1 
[INFO ] 2024-11-13 12:05:05.882 - [任务 1][testDateTime] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-13 12:05:05.882 - [任务 1][testDateTime] - Initial sync completed 
[INFO ] 2024-11-13 12:05:05.882 - [任务 1][testDateTime] - Incremental sync starting... 
[INFO ] 2024-11-13 12:05:05.894 - [任务 1][testDateTime] - Initial sync completed 
[INFO ] 2024-11-13 12:05:05.894 - [任务 1][testDateTime] - Starting stream read, table list: [testDateTime], offset: {"filename":"binlog.000038","position":32659,"gtidSet":""} 
[INFO ] 2024-11-13 12:05:05.926 - [任务 1][testDateTime] - Starting mysql cdc, server name: cde9c0c8-8ea7-428d-8c06-a8ae7c8f76a3 
[INFO ] 2024-11-13 12:05:05.927 - [任务 1][testDateTime] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1071589717
  time.precision.mode: adaptive_time_microseconds
  database.server.name: cde9c0c8-8ea7-428d-8c06-a8ae7c8f76a3
  database.port: 3306
  threadName: Debezium-Mysql-Connector-cde9c0c8-8ea7-428d-8c06-a8ae7c8f76a3
  enable.time.adjuster: false
  database.hostname: localhost
  database.password: ********
  name: cde9c0c8-8ea7-428d-8c06-a8ae7c8f76a3
  pdk.offset.string: {"name":"cde9c0c8-8ea7-428d-8c06-a8ae7c8f76a3","offset":{"{\"server\":\"cde9c0c8-8ea7-428d-8c06-a8ae7c8f76a3\"}":"{\"file\":\"binlog.000038\",\"pos\":32659,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDateTime
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-13 12:05:06.130 - [任务 1][testDateTime] - Connector Mysql incremental start succeed, tables: [testDateTime], data change syncing 
[INFO ] 2024-11-13 12:06:09.202 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] running status set to false 
[INFO ] 2024-11-13 12:06:09.259 - [任务 1][testDateTime] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-13 12:06:09.261 - [任务 1][testDateTime] - Mysql binlog reader stopped 
[INFO ] 2024-11-13 12:06:09.271 - [任务 1][testDateTime] - Incremental sync completed 
[INFO ] 2024-11-13 12:06:09.278 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 12:06:09.279 - [任务 1][testDateTime] - PDK connector node released: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 12:06:09.279 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] schema data cleaned 
[INFO ] 2024-11-13 12:06:09.285 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] monitor closed 
[INFO ] 2024-11-13 12:06:09.285 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] close complete, cost 99 ms 
[INFO ] 2024-11-13 12:06:09.286 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] running status set to false 
[INFO ] 2024-11-13 12:06:09.306 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 12:06:09.307 - [任务 1][testDateTime] - PDK connector node released: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 12:06:09.308 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] schema data cleaned 
[INFO ] 2024-11-13 12:06:09.309 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] monitor closed 
[INFO ] 2024-11-13 12:06:09.309 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] close complete, cost 23 ms 
[INFO ] 2024-11-13 12:06:14.098 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-13 12:06:14.099 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@56cebd87 
[INFO ] 2024-11-13 12:06:14.120 - [任务 1] - Stop task milestones: 67341f4058765d7645ee3f0b(任务 1)  
[INFO ] 2024-11-13 12:06:14.253 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-11-13 12:06:14.253 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-11-13 12:06:14.281 - [任务 1] - Remove memory task client succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
[INFO ] 2024-11-13 12:06:14.281 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
[INFO ] 2024-11-13 12:06:39.578 - [任务 1] - Start task milestones: 67341f4058765d7645ee3f0b(任务 1) 
[INFO ] 2024-11-13 12:06:39.579 - [任务 1] - Task initialization... 
[INFO ] 2024-11-13 12:06:39.757 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-13 12:06:39.757 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-13 12:06:39.818 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] start preload schema,table counts: 1 
[INFO ] 2024-11-13 12:06:39.818 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] start preload schema,table counts: 1 
[INFO ] 2024-11-13 12:06:39.818 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-13 12:06:39.818 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] preload schema finished, cost 0 ms 
[INFO ] 2024-11-13 12:06:44.151 - [任务 1][testDateTime] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-13 12:06:44.410 - [任务 1][testDateTime] - Source node "testDateTime" read batch size: 100 
[INFO ] 2024-11-13 12:06:44.410 - [任务 1][testDateTime] - Source node "testDateTime" event queue capacity: 200 
[INFO ] 2024-11-13 12:06:44.410 - [任务 1][testDateTime] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-13 12:06:44.412 - [任务 1][testDateTime] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":32659,"gtidSet":""} 
[INFO ] 2024-11-13 12:06:44.474 - [任务 1][testDateTime] - Initial sync started 
[INFO ] 2024-11-13 12:06:44.475 - [任务 1][testDateTime] - Starting batch read, table name: testDateTime, offset: null 
[INFO ] 2024-11-13 12:06:44.498 - [任务 1][testDateTime] - Table testDateTime is going to be initial synced 
[INFO ] 2024-11-13 12:06:44.498 - [任务 1][testDateTime] - Table [testDateTime] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-13 12:06:44.503 - [任务 1][testDateTime] - Query table 'testDateTime' counts: 1 
[INFO ] 2024-11-13 12:06:44.503 - [任务 1][testDateTime] - Initial sync completed 
[INFO ] 2024-11-13 12:06:44.503 - [任务 1][testDateTime] - Incremental sync starting... 
[INFO ] 2024-11-13 12:06:44.504 - [任务 1][testDateTime] - Initial sync completed 
[INFO ] 2024-11-13 12:06:44.504 - [任务 1][testDateTime] - Starting stream read, table list: [testDateTime], offset: {"filename":"binlog.000038","position":32659,"gtidSet":""} 
[INFO ] 2024-11-13 12:06:44.536 - [任务 1][testDateTime] - Starting mysql cdc, server name: f1cef25e-8619-4ed1-a015-b6e9dc89ad69 
[INFO ] 2024-11-13 12:06:44.536 - [任务 1][testDateTime] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 770154585
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f1cef25e-8619-4ed1-a015-b6e9dc89ad69
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f1cef25e-8619-4ed1-a015-b6e9dc89ad69
  enable.time.adjuster: false
  database.hostname: localhost
  database.password: ********
  name: f1cef25e-8619-4ed1-a015-b6e9dc89ad69
  pdk.offset.string: {"name":"f1cef25e-8619-4ed1-a015-b6e9dc89ad69","offset":{"{\"server\":\"f1cef25e-8619-4ed1-a015-b6e9dc89ad69\"}":"{\"file\":\"binlog.000038\",\"pos\":32659,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDateTime
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-13 12:07:21.869 - [任务 1][testDateTime] - Connector Mysql incremental start succeed, tables: [testDateTime], data change syncing 
[INFO ] 2024-11-13 12:08:00.026 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] running status set to false 
[INFO ] 2024-11-13 12:08:00.078 - [任务 1][testDateTime] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-13 12:08:00.078 - [任务 1][testDateTime] - Mysql binlog reader stopped 
[INFO ] 2024-11-13 12:08:00.079 - [任务 1][testDateTime] - Incremental sync completed 
[INFO ] 2024-11-13 12:08:00.091 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 12:08:00.092 - [任务 1][testDateTime] - PDK connector node released: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 12:08:00.092 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] schema data cleaned 
[INFO ] 2024-11-13 12:08:00.092 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] monitor closed 
[INFO ] 2024-11-13 12:08:00.095 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] close complete, cost 109 ms 
[INFO ] 2024-11-13 12:08:00.095 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] running status set to false 
[INFO ] 2024-11-13 12:08:00.111 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 12:08:00.111 - [任务 1][testDateTime] - PDK connector node released: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 12:08:00.112 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] schema data cleaned 
[INFO ] 2024-11-13 12:08:00.112 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] monitor closed 
[INFO ] 2024-11-13 12:08:00.113 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] close complete, cost 18 ms 
[INFO ] 2024-11-13 12:08:03.338 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-13 12:08:03.339 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2bd4f666 
[INFO ] 2024-11-13 12:08:03.492 - [任务 1] - Stop task milestones: 67341f4058765d7645ee3f0b(任务 1)  
[INFO ] 2024-11-13 12:08:03.493 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-11-13 12:08:03.535 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-11-13 12:08:03.535 - [任务 1] - Remove memory task client succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
[INFO ] 2024-11-13 12:08:03.737 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
[INFO ] 2024-11-13 12:08:15.731 - [任务 1] - Start task milestones: 67341f4058765d7645ee3f0b(任务 1) 
[INFO ] 2024-11-13 12:08:15.732 - [任务 1] - Task initialization... 
[INFO ] 2024-11-13 12:08:15.831 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-13 12:08:15.901 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-13 12:08:15.901 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] start preload schema,table counts: 1 
[INFO ] 2024-11-13 12:08:15.901 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] start preload schema,table counts: 1 
[INFO ] 2024-11-13 12:08:15.902 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] preload schema finished, cost 0 ms 
[INFO ] 2024-11-13 12:08:16.107 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-13 12:08:34.272 - [任务 1][testDateTime] - Source node "testDateTime" read batch size: 100 
[INFO ] 2024-11-13 12:08:34.279 - [任务 1][testDateTime] - Source node "testDateTime" event queue capacity: 200 
[INFO ] 2024-11-13 12:08:34.280 - [任务 1][testDateTime] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-13 12:08:34.280 - [任务 1][testDateTime] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":32659,"gtidSet":""} 
[INFO ] 2024-11-13 12:08:39.775 - [任务 1][testDateTime] - Initial sync started 
[INFO ] 2024-11-13 12:08:39.816 - [任务 1][testDateTime] - Starting batch read, table name: testDateTime, offset: null 
[INFO ] 2024-11-13 12:08:39.816 - [任务 1][testDateTime] - Table testDateTime is going to be initial synced 
[INFO ] 2024-11-13 12:08:39.885 - [任务 1][testDateTime] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-13 12:08:39.885 - [任务 1][testDateTime] - Table [testDateTime] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-13 12:08:39.887 - [任务 1][testDateTime] - Query table 'testDateTime' counts: 1 
[INFO ] 2024-11-13 12:08:39.887 - [任务 1][testDateTime] - Initial sync completed 
[INFO ] 2024-11-13 12:08:39.887 - [任务 1][testDateTime] - Incremental sync starting... 
[INFO ] 2024-11-13 12:08:39.892 - [任务 1][testDateTime] - Initial sync completed 
[INFO ] 2024-11-13 12:08:39.892 - [任务 1][testDateTime] - Starting stream read, table list: [testDateTime], offset: {"filename":"binlog.000038","position":32659,"gtidSet":""} 
[INFO ] 2024-11-13 12:08:39.923 - [任务 1][testDateTime] - Starting mysql cdc, server name: b796eee2-5a71-4fd4-8726-1861cbb346a5 
[INFO ] 2024-11-13 12:08:39.923 - [任务 1][testDateTime] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 133371648
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b796eee2-5a71-4fd4-8726-1861cbb346a5
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b796eee2-5a71-4fd4-8726-1861cbb346a5
  enable.time.adjuster: false
  database.hostname: localhost
  database.password: ********
  name: b796eee2-5a71-4fd4-8726-1861cbb346a5
  pdk.offset.string: {"name":"b796eee2-5a71-4fd4-8726-1861cbb346a5","offset":{"{\"server\":\"b796eee2-5a71-4fd4-8726-1861cbb346a5\"}":"{\"file\":\"binlog.000038\",\"pos\":32659,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDateTime
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-13 12:08:40.126 - [任务 1][testDateTime] - Connector Mysql incremental start succeed, tables: [testDateTime], data change syncing 
[INFO ] 2024-11-13 12:09:42.164 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] running status set to false 
[INFO ] 2024-11-13 12:09:42.212 - [任务 1][testDateTime] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-13 12:09:42.212 - [任务 1][testDateTime] - Mysql binlog reader stopped 
[INFO ] 2024-11-13 12:09:42.213 - [任务 1][testDateTime] - Incremental sync completed 
[INFO ] 2024-11-13 12:09:42.223 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 12:09:42.223 - [任务 1][testDateTime] - PDK connector node released: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 12:09:42.224 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] schema data cleaned 
[INFO ] 2024-11-13 12:09:42.226 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] monitor closed 
[INFO ] 2024-11-13 12:09:42.230 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] close complete, cost 66 ms 
[INFO ] 2024-11-13 12:09:42.230 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] running status set to false 
[INFO ] 2024-11-13 12:09:42.243 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 12:09:42.243 - [任务 1][testDateTime] - PDK connector node released: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 12:09:42.244 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] schema data cleaned 
[INFO ] 2024-11-13 12:09:42.244 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] monitor closed 
[INFO ] 2024-11-13 12:09:42.450 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] close complete, cost 15 ms 
[INFO ] 2024-11-13 12:09:44.829 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-13 12:09:44.829 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@13e5df5 
[INFO ] 2024-11-13 12:09:44.836 - [任务 1] - Stop task milestones: 67341f4058765d7645ee3f0b(任务 1)  
[INFO ] 2024-11-13 12:09:44.955 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-11-13 12:09:44.955 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-11-13 12:09:44.982 - [任务 1] - Remove memory task client succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
[INFO ] 2024-11-13 12:09:44.983 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
[INFO ] 2024-11-13 12:10:10.029 - [任务 1] - Start task milestones: 67341f4058765d7645ee3f0b(任务 1) 
[INFO ] 2024-11-13 12:10:10.029 - [任务 1] - Task initialization... 
[INFO ] 2024-11-13 12:10:10.189 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-13 12:10:10.240 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-13 12:10:10.293 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] start preload schema,table counts: 1 
[INFO ] 2024-11-13 12:10:10.293 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] start preload schema,table counts: 1 
[INFO ] 2024-11-13 12:10:10.293 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] preload schema finished, cost 0 ms 
[INFO ] 2024-11-13 12:10:10.293 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] preload schema finished, cost 0 ms 
[INFO ] 2024-11-13 12:10:11.086 - [任务 1][testDateTime] - Source node "testDateTime" read batch size: 100 
[INFO ] 2024-11-13 12:10:11.086 - [任务 1][testDateTime] - Source node "testDateTime" event queue capacity: 200 
[INFO ] 2024-11-13 12:10:11.086 - [任务 1][testDateTime] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-13 12:10:11.186 - [任务 1][testDateTime] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":32659,"gtidSet":""} 
[INFO ] 2024-11-13 12:10:11.187 - [任务 1][testDateTime] - Initial sync started 
[INFO ] 2024-11-13 12:10:11.187 - [任务 1][testDateTime] - Starting batch read, table name: testDateTime, offset: null 
[INFO ] 2024-11-13 12:10:11.194 - [任务 1][testDateTime] - Table testDateTime is going to be initial synced 
[INFO ] 2024-11-13 12:11:21.284 - [任务 1][testDateTime] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-13 12:11:21.284 - [任务 1][testDateTime] - Table [testDateTime] has been completed batch read, will skip batch read on the next run 
[WARN ] 2024-11-13 12:11:21.313 - [任务 1][testDateTime] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: java.sql.SQLTransientConnectionException: HikariPool-21 - Connection is not available, request timed out after 69977ms.
	com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:128)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-13 12:11:52.354 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] running status set to false 
[INFO ] 2024-11-13 12:11:52.390 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 12:11:52.391 - [任务 1][testDateTime] - PDK connector node released: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 12:11:52.391 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] schema data cleaned 
[INFO ] 2024-11-13 12:11:52.391 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] monitor closed 
[INFO ] 2024-11-13 12:11:52.393 - [任务 1][testDateTime] - Cancel query 'testDateTime' snapshot row size with task stopped. 
[INFO ] 2024-11-13 12:11:52.393 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] close complete, cost 83 ms 
[INFO ] 2024-11-13 12:11:52.394 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] running status set to false 
[INFO ] 2024-11-13 12:11:52.399 - [任务 1][testDateTime] - Incremental sync starting... 
[INFO ] 2024-11-13 12:11:52.409 - [任务 1][testDateTime] - Incremental sync completed 
[INFO ] 2024-11-13 12:11:52.409 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 12:11:52.410 - [任务 1][testDateTime] - PDK connector node released: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 12:11:52.410 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] schema data cleaned 
[INFO ] 2024-11-13 12:11:52.410 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] monitor closed 
[INFO ] 2024-11-13 12:11:52.614 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] close complete, cost 16 ms 
[INFO ] 2024-11-13 12:11:56.184 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-13 12:11:56.186 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1eae7478 
[INFO ] 2024-11-13 12:11:56.225 - [任务 1] - Stop task milestones: 67341f4058765d7645ee3f0b(任务 1)  
[INFO ] 2024-11-13 12:11:56.342 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-11-13 12:11:56.342 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-11-13 12:11:56.363 - [任务 1] - Remove memory task client succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
[INFO ] 2024-11-13 12:11:56.365 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
[INFO ] 2024-11-13 15:08:27.125 - [任务 1] - Start task milestones: 67341f4058765d7645ee3f0b(任务 1) 
[INFO ] 2024-11-13 15:08:27.125 - [任务 1] - Task initialization... 
[INFO ] 2024-11-13 15:08:27.301 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-13 15:08:27.342 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-13 15:08:27.380 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] start preload schema,table counts: 1 
[INFO ] 2024-11-13 15:08:27.380 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] start preload schema,table counts: 1 
[INFO ] 2024-11-13 15:08:27.381 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] preload schema finished, cost 1 ms 
[INFO ] 2024-11-13 15:08:27.381 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] preload schema finished, cost 0 ms 
[INFO ] 2024-11-13 15:08:28.191 - [任务 1][testDateTime] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-13 15:08:28.383 - [任务 1][testDateTime] - Source node "testDateTime" read batch size: 100 
[INFO ] 2024-11-13 15:08:28.385 - [任务 1][testDateTime] - Source node "testDateTime" event queue capacity: 200 
[INFO ] 2024-11-13 15:08:28.385 - [任务 1][testDateTime] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-13 15:08:28.390 - [任务 1][testDateTime] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":32659,"gtidSet":""} 
[INFO ] 2024-11-13 15:08:28.454 - [任务 1][testDateTime] - Initial sync started 
[INFO ] 2024-11-13 15:08:28.463 - [任务 1][testDateTime] - Starting batch read, table name: testDateTime, offset: null 
[INFO ] 2024-11-13 15:08:28.511 - [任务 1][testDateTime] - Table testDateTime is going to be initial synced 
[INFO ] 2024-11-13 15:08:28.511 - [任务 1][testDateTime] - Table [testDateTime] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-13 15:08:28.516 - [任务 1][testDateTime] - Query table 'testDateTime' counts: 1 
[INFO ] 2024-11-13 15:08:28.516 - [任务 1][testDateTime] - Initial sync completed 
[INFO ] 2024-11-13 15:08:28.517 - [任务 1][testDateTime] - Incremental sync starting... 
[INFO ] 2024-11-13 15:08:28.518 - [任务 1][testDateTime] - Initial sync completed 
[INFO ] 2024-11-13 15:08:28.518 - [任务 1][testDateTime] - Starting stream read, table list: [testDateTime], offset: {"filename":"binlog.000038","position":32659,"gtidSet":""} 
[INFO ] 2024-11-13 15:08:28.561 - [任务 1][testDateTime] - Starting mysql cdc, server name: b41d96f3-ea04-49d0-864d-fcd4ba082281 
[INFO ] 2024-11-13 15:08:28.561 - [任务 1][testDateTime] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1066557532
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b41d96f3-ea04-49d0-864d-fcd4ba082281
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b41d96f3-ea04-49d0-864d-fcd4ba082281
  enable.time.adjuster: false
  database.hostname: localhost
  database.password: ********
  name: b41d96f3-ea04-49d0-864d-fcd4ba082281
  pdk.offset.string: {"name":"b41d96f3-ea04-49d0-864d-fcd4ba082281","offset":{"{\"server\":\"b41d96f3-ea04-49d0-864d-fcd4ba082281\"}":"{\"file\":\"binlog.000038\",\"pos\":32659,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDateTime
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-13 15:08:28.764 - [任务 1][testDateTime] - Connector Mysql incremental start succeed, tables: [testDateTime], data change syncing 
[INFO ] 2024-11-13 15:09:42.575 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] running status set to false 
[INFO ] 2024-11-13 15:09:42.637 - [任务 1][testDateTime] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-13 15:09:42.639 - [任务 1][testDateTime] - Mysql binlog reader stopped 
[INFO ] 2024-11-13 15:09:42.640 - [任务 1][testDateTime] - Incremental sync completed 
[INFO ] 2024-11-13 15:09:42.652 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 15:09:42.652 - [任务 1][testDateTime] - PDK connector node released: HazelcastSourcePdkDataNode-5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8 
[INFO ] 2024-11-13 15:09:42.652 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] schema data cleaned 
[INFO ] 2024-11-13 15:09:42.653 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] monitor closed 
[INFO ] 2024-11-13 15:09:42.656 - [任务 1][testDateTime] - Node testDateTime[5df85ff9-1d6f-4fcf-a8e3-2f0db79baaa8] close complete, cost 107 ms 
[INFO ] 2024-11-13 15:09:42.656 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] running status set to false 
[INFO ] 2024-11-13 15:09:42.677 - [任务 1][testDateTime] - PDK connector node stopped: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 15:09:42.677 - [任务 1][testDateTime] - PDK connector node released: HazelcastTargetPdkDataNode-bc253217-3ec1-4c2e-9a64-926ebd8ca9f0 
[INFO ] 2024-11-13 15:09:42.677 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] schema data cleaned 
[INFO ] 2024-11-13 15:09:42.677 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] monitor closed 
[INFO ] 2024-11-13 15:09:42.879 - [任务 1][testDateTime] - Node testDateTime[bc253217-3ec1-4c2e-9a64-926ebd8ca9f0] close complete, cost 22 ms 
[INFO ] 2024-11-13 15:09:42.963 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-13 15:09:42.963 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@61426dc8 
[INFO ] 2024-11-13 15:09:42.970 - [任务 1] - Stop task milestones: 67341f4058765d7645ee3f0b(任务 1)  
[INFO ] 2024-11-13 15:09:43.105 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-11-13 15:09:43.105 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-11-13 15:09:43.150 - [任务 1] - Remove memory task client succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
[INFO ] 2024-11-13 15:09:43.150 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[67341f4058765d7645ee3f0b] 
