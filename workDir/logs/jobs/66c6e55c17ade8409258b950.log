[INFO ] 2024-08-22 15:14:49.099 - [任务 11] - Start task milestones: 66c6e55c17ade8409258b950(任务 11) 
[INFO ] 2024-08-22 15:14:49.100 - [任务 11] - Task initialization... 
[INFO ] 2024-08-22 15:14:49.304 - [任务 11] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-08-22 15:14:49.398 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-22 15:14:49.605 - [任务 11][TestDummy] - Node TestDummy[a4fb1cd9-887c-4321-9465-dd4f0c3fb6a2] start preload schema,table counts: 37 
[INFO ] 2024-08-22 15:14:49.605 - [任务 11][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[e6d197bb-e2e2-4360-990a-9ade359d0cc0] start preload schema,table counts: 37 
[INFO ] 2024-08-22 15:14:50.005 - [任务 11][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[e6d197bb-e2e2-4360-990a-9ade359d0cc0] preload schema finished, cost 531 ms 
[INFO ] 2024-08-22 15:14:50.005 - [任务 11][TestDummy] - Node TestDummy[a4fb1cd9-887c-4321-9465-dd4f0c3fb6a2] preload schema finished, cost 534 ms 
[INFO ] 2024-08-22 15:14:50.805 - [任务 11][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-22 15:14:50.805 - [任务 11][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-22 15:14:51.032 - [任务 11][JARADDB2WIMTEST132] - Source node "JARADDB2WIMTEST132" read batch size: 100 
[INFO ] 2024-08-22 15:14:51.033 - [任务 11][JARADDB2WIMTEST132] - Source node "JARADDB2WIMTEST132" event queue capacity: 200 
[INFO ] 2024-08-22 15:14:51.037 - [任务 11][JARADDB2WIMTEST132] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-22 15:14:51.232 - [任务 11][JARADDB2WIMTEST132] - Table [TEST_DB2_KAFKA, dafasdf0906, Testddlora222, TestCarClaim09062, testoracle, TESTDATE1, ORDERS, TestTimestamp, TestCarClaim0906, HstestMultiKeys_bak, TestColumn, db2target1, Testdbdbdb, Testddlora111, TestIkas1, testCustom11, TestClob] not open CDC 
[INFO ] 2024-08-22 15:14:51.232 - [任务 11][JARADDB2WIMTEST132] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724310891231} 
[INFO ] 2024-08-22 15:14:51.232 - [任务 11][JARADDB2WIMTEST132] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-22 15:14:51.371 - [任务 11][JARADDB2WIMTEST132] - Initial sync started 
[INFO ] 2024-08-22 15:14:51.372 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: TestCarClaim, offset: null 
[INFO ] 2024-08-22 15:14:51.416 - [任务 11][JARADDB2WIMTEST132] - Table TestCarClaim is going to be initial synced 
[INFO ] 2024-08-22 15:14:51.416 - [任务 11][JARADDB2WIMTEST132] - Query table 'TestCarClaim' counts: 19 
[INFO ] 2024-08-22 15:14:51.425 - [任务 11][JARADDB2WIMTEST132] - Table [TestCarClaim] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:14:51.425 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: TEST_DB2_KAFKA, offset: null 
[INFO ] 2024-08-22 15:14:51.438 - [任务 11][JARADDB2WIMTEST132] - Table TEST_DB2_KAFKA is going to be initial synced 
[INFO ] 2024-08-22 15:14:51.438 - [任务 11][JARADDB2WIMTEST132] - Query table 'TEST_DB2_KAFKA' counts: 3 
[INFO ] 2024-08-22 15:14:51.442 - [任务 11][JARADDB2WIMTEST132] - Table [TEST_DB2_KAFKA] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:14:51.442 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: test111, offset: null 
[INFO ] 2024-08-22 15:14:51.456 - [任务 11][JARADDB2WIMTEST132] - Table test111 is going to be initial synced 
[INFO ] 2024-08-22 15:14:51.456 - [任务 11][JARADDB2WIMTEST132] - Table [test111] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:14:51.457 - [任务 11][JARADDB2WIMTEST132] - Query table 'test111' counts: 3 
[INFO ] 2024-08-22 15:14:51.457 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: dafasdf0906, offset: null 
[INFO ] 2024-08-22 15:14:51.475 - [任务 11][JARADDB2WIMTEST132] - Table dafasdf0906 is going to be initial synced 
[INFO ] 2024-08-22 15:14:51.475 - [任务 11][JARADDB2WIMTEST132] - Table [dafasdf0906] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:14:51.475 - [任务 11][JARADDB2WIMTEST132] - Query table 'dafasdf0906' counts: 23 
[INFO ] 2024-08-22 15:14:51.475 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: Testddlora222, offset: null 
[INFO ] 2024-08-22 15:14:51.475 - [任务 11][JARADDB2WIMTEST132] - Table Testddlora222 is going to be initial synced 
[INFO ] 2024-08-22 15:14:51.503 - [任务 11][JARADDB2WIMTEST132] - Table [Testddlora222] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:14:51.503 - [任务 11][JARADDB2WIMTEST132] - Query table 'Testddlora222' counts: 1 
[INFO ] 2024-08-22 15:14:51.503 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: TestCarClaim09062, offset: null 
[INFO ] 2024-08-22 15:14:51.514 - [任务 11][JARADDB2WIMTEST132] - Table TestCarClaim09062 is going to be initial synced 
[INFO ] 2024-08-22 15:14:51.514 - [任务 11][JARADDB2WIMTEST132] - Query table 'TestCarClaim09062' counts: 24 
[INFO ] 2024-08-22 15:14:51.519 - [任务 11][JARADDB2WIMTEST132] - Table [TestCarClaim09062] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:14:51.519 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: TestDb2, offset: null 
[INFO ] 2024-08-22 15:14:51.531 - [任务 11][JARADDB2WIMTEST132] - Table TestDb2 is going to be initial synced 
[INFO ] 2024-08-22 15:14:51.531 - [任务 11][JARADDB2WIMTEST132] - Query table 'TestDb2' counts: 20 
[INFO ] 2024-08-22 15:14:51.534 - [任务 11][JARADDB2WIMTEST132] - Table [TestDb2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:14:51.535 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: testoracle, offset: null 
[INFO ] 2024-08-22 15:14:51.535 - [任务 11][JARADDB2WIMTEST132] - Table testoracle is going to be initial synced 
[INFO ] 2024-08-22 15:14:51.553 - [任务 11][JARADDB2WIMTEST132] - Table [testoracle] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:14:51.556 - [任务 11][JARADDB2WIMTEST132] - Query table 'testoracle' counts: 12 
[INFO ] 2024-08-22 15:14:51.556 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: TESTDATE1, offset: null 
[INFO ] 2024-08-22 15:14:51.556 - [任务 11][JARADDB2WIMTEST132] - Table TESTDATE1 is going to be initial synced 
[INFO ] 2024-08-22 15:14:51.568 - [任务 11][JARADDB2WIMTEST132] - Query table 'TESTDATE1' counts: 5 
[INFO ] 2024-08-22 15:14:51.568 - [任务 11][JARADDB2WIMTEST132] - Table [TESTDATE1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:14:51.569 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: testTimestamp, offset: null 
[INFO ] 2024-08-22 15:14:51.569 - [任务 11][JARADDB2WIMTEST132] - Table testTimestamp is going to be initial synced 
[INFO ] 2024-08-22 15:14:51.583 - [任务 11][JARADDB2WIMTEST132] - Query table 'testTimestamp' counts: 7 
[INFO ] 2024-08-22 15:14:51.583 - [任务 11][JARADDB2WIMTEST132] - Table [testTimestamp] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:14:51.583 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: TESTDATE, offset: null 
[INFO ] 2024-08-22 15:14:51.608 - [任务 11][JARADDB2WIMTEST132] - Table TESTDATE is going to be initial synced 
[INFO ] 2024-08-22 15:14:51.610 - [任务 11][JARADDB2WIMTEST132] - Table [TESTDATE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:14:51.610 - [任务 11][JARADDB2WIMTEST132] - Query table 'TESTDATE' counts: 5 
[INFO ] 2024-08-22 15:14:51.610 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: Test_ikas, offset: null 
[INFO ] 2024-08-22 15:14:51.610 - [任务 11][JARADDB2WIMTEST132] - Table Test_ikas is going to be initial synced 
[INFO ] 2024-08-22 15:14:51.816 - [任务 11][JARADDB2WIMTEST132] - Query table 'Test_ikas' counts: 50000 
[INFO ] 2024-08-22 15:14:52.366 - [任务 11][JARADDB2WIMTEST132] - Table [Test_ikas] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:14:52.367 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: test222, offset: null 
[INFO ] 2024-08-22 15:14:52.368 - [任务 11][JARADDB2WIMTEST132] - Table test222 is going to be initial synced 
[INFO ] 2024-08-22 15:14:52.395 - [任务 11][JARADDB2WIMTEST132] - Query table 'test222' counts: 1 
[INFO ] 2024-08-22 15:14:52.397 - [任务 11][JARADDB2WIMTEST132] - Table [test222] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:14:52.397 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: Dummy_TT, offset: null 
[INFO ] 2024-08-22 15:14:52.598 - [任务 11][JARADDB2WIMTEST132] - Table Dummy_TT is going to be initial synced 
[INFO ] 2024-08-22 15:21:05.570 - [任务 11] - Stop task milestones: 66c6e55c17ade8409258b950(任务 11)  
[INFO ] 2024-08-22 15:21:05.575 - [任务 11][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[e6d197bb-e2e2-4360-990a-9ade359d0cc0] running status set to false 
[INFO ] 2024-08-22 15:21:05.593 - [任务 11][JARADDB2WIMTEST132] - Table [Dummy_TT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:21:05.757 - [任务 11][JARADDB2WIMTEST132] - Cancel query 'Dummy_TT' snapshot row size with task stopped. 
[INFO ] 2024-08-22 15:21:05.757 - [任务 11][JARADDB2WIMTEST132] - PDK connector node stopped: HazelcastSourcePdkDataNode-e6d197bb-e2e2-4360-990a-9ade359d0cc0 
[INFO ] 2024-08-22 15:21:05.758 - [任务 11][JARADDB2WIMTEST132] - PDK connector node released: HazelcastSourcePdkDataNode-e6d197bb-e2e2-4360-990a-9ade359d0cc0 
[INFO ] 2024-08-22 15:21:05.758 - [任务 11][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[e6d197bb-e2e2-4360-990a-9ade359d0cc0] schema data cleaned 
[INFO ] 2024-08-22 15:21:05.759 - [任务 11][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[e6d197bb-e2e2-4360-990a-9ade359d0cc0] monitor closed 
[INFO ] 2024-08-22 15:21:05.759 - [任务 11][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[e6d197bb-e2e2-4360-990a-9ade359d0cc0] close complete, cost 203 ms 
[INFO ] 2024-08-22 15:21:05.768 - [任务 11][TestDummy] - Node TestDummy[a4fb1cd9-887c-4321-9465-dd4f0c3fb6a2] running status set to false 
[INFO ] 2024-08-22 15:21:05.773 - [任务 11][TestDummy] - Stop connector 
[INFO ] 2024-08-22 15:21:05.778 - [任务 11][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-a4fb1cd9-887c-4321-9465-dd4f0c3fb6a2 
[INFO ] 2024-08-22 15:21:05.778 - [任务 11][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-a4fb1cd9-887c-4321-9465-dd4f0c3fb6a2 
[INFO ] 2024-08-22 15:21:05.778 - [任务 11][TestDummy] - Node TestDummy[a4fb1cd9-887c-4321-9465-dd4f0c3fb6a2] schema data cleaned 
[INFO ] 2024-08-22 15:21:05.778 - [任务 11][TestDummy] - Node TestDummy[a4fb1cd9-887c-4321-9465-dd4f0c3fb6a2] monitor closed 
[INFO ] 2024-08-22 15:21:05.778 - [任务 11][TestDummy] - Node TestDummy[a4fb1cd9-887c-4321-9465-dd4f0c3fb6a2] close complete, cost 18 ms 
[INFO ] 2024-08-22 15:21:09.252 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-22 15:21:09.253 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-08-22 15:21:09.253 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-08-22 15:21:09.289 - [任务 11] - Remove memory task client succeed, task: 任务 11[66c6e55c17ade8409258b950] 
[INFO ] 2024-08-22 15:21:09.291 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[66c6e55c17ade8409258b950] 
[INFO ] 2024-08-22 15:59:35.413 - [任务 11] - Start task milestones: 66c6e55c17ade8409258b950(任务 11) 
[INFO ] 2024-08-22 15:59:35.414 - [任务 11] - Task initialization... 
[INFO ] 2024-08-22 15:59:35.519 - [任务 11] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-22 15:59:35.520 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-22 15:59:35.725 - [任务 11][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[e6d197bb-e2e2-4360-990a-9ade359d0cc0] start preload schema,table counts: 37 
[INFO ] 2024-08-22 15:59:35.725 - [任务 11][TestDummy] - Node TestDummy[a4fb1cd9-887c-4321-9465-dd4f0c3fb6a2] start preload schema,table counts: 37 
[INFO ] 2024-08-22 15:59:36.035 - [任务 11][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[e6d197bb-e2e2-4360-990a-9ade359d0cc0] preload schema finished, cost 469 ms 
[INFO ] 2024-08-22 15:59:36.036 - [任务 11][TestDummy] - Node TestDummy[a4fb1cd9-887c-4321-9465-dd4f0c3fb6a2] preload schema finished, cost 469 ms 
[INFO ] 2024-08-22 15:59:36.831 - [任务 11][JARADDB2WIMTEST132] - Source node "JARADDB2WIMTEST132" read batch size: 100 
[INFO ] 2024-08-22 15:59:36.831 - [任务 11][JARADDB2WIMTEST132] - Source node "JARADDB2WIMTEST132" event queue capacity: 200 
[INFO ] 2024-08-22 15:59:36.831 - [任务 11][JARADDB2WIMTEST132] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-22 15:59:36.902 - [任务 11][JARADDB2WIMTEST132] - Table [TEST_DB2_KAFKA, dafasdf0906, Testddlora222, TestCarClaim09062, testoracle, TESTDATE1, ORDERS, TestTimestamp, TestCarClaim0906, HstestMultiKeys_bak, TestColumn, db2target1, Testdbdbdb, Testddlora111, TestIkas1, testCustom11, TestClob] not open CDC 
[INFO ] 2024-08-22 15:59:36.902 - [任务 11][JARADDB2WIMTEST132] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724313576901} 
[INFO ] 2024-08-22 15:59:36.902 - [任务 11][JARADDB2WIMTEST132] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-22 15:59:36.970 - [任务 11][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-22 15:59:36.971 - [任务 11][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-22 15:59:37.032 - [任务 11][JARADDB2WIMTEST132] - Initial sync started 
[INFO ] 2024-08-22 15:59:37.034 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: TestCarClaim, offset: null 
[INFO ] 2024-08-22 15:59:37.043 - [任务 11][JARADDB2WIMTEST132] - Table TestCarClaim is going to be initial synced 
[INFO ] 2024-08-22 15:59:37.215 - [任务 11][JARADDB2WIMTEST132] - Table [TestCarClaim] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:59:37.216 - [任务 11][JARADDB2WIMTEST132] - Query table 'TestCarClaim' counts: 19 
[INFO ] 2024-08-22 15:59:37.216 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: TEST_DB2_KAFKA, offset: null 
[INFO ] 2024-08-22 15:59:37.216 - [任务 11][JARADDB2WIMTEST132] - Table TEST_DB2_KAFKA is going to be initial synced 
[INFO ] 2024-08-22 15:59:37.226 - [任务 11][JARADDB2WIMTEST132] - Query table 'TEST_DB2_KAFKA' counts: 3 
[INFO ] 2024-08-22 15:59:37.226 - [任务 11][JARADDB2WIMTEST132] - Table [TEST_DB2_KAFKA] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:59:37.226 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: test111, offset: null 
[INFO ] 2024-08-22 15:59:37.227 - [任务 11][JARADDB2WIMTEST132] - Table test111 is going to be initial synced 
[INFO ] 2024-08-22 15:59:37.241 - [任务 11][JARADDB2WIMTEST132] - Query table 'test111' counts: 3 
[INFO ] 2024-08-22 15:59:37.241 - [任务 11][JARADDB2WIMTEST132] - Table [test111] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:59:37.241 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: dafasdf0906, offset: null 
[INFO ] 2024-08-22 15:59:37.261 - [任务 11][JARADDB2WIMTEST132] - Table dafasdf0906 is going to be initial synced 
[INFO ] 2024-08-22 15:59:37.261 - [任务 11][JARADDB2WIMTEST132] - Query table 'dafasdf0906' counts: 23 
[INFO ] 2024-08-22 15:59:37.261 - [任务 11][JARADDB2WIMTEST132] - Table [dafasdf0906] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:59:37.261 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: Testddlora222, offset: null 
[INFO ] 2024-08-22 15:59:37.261 - [任务 11][JARADDB2WIMTEST132] - Table Testddlora222 is going to be initial synced 
[INFO ] 2024-08-22 15:59:37.274 - [任务 11][JARADDB2WIMTEST132] - Table [Testddlora222] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:59:37.274 - [任务 11][JARADDB2WIMTEST132] - Query table 'Testddlora222' counts: 1 
[INFO ] 2024-08-22 15:59:37.275 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: TestCarClaim09062, offset: null 
[INFO ] 2024-08-22 15:59:37.275 - [任务 11][JARADDB2WIMTEST132] - Table TestCarClaim09062 is going to be initial synced 
[INFO ] 2024-08-22 15:59:37.287 - [任务 11][JARADDB2WIMTEST132] - Query table 'TestCarClaim09062' counts: 24 
[INFO ] 2024-08-22 15:59:37.287 - [任务 11][JARADDB2WIMTEST132] - Table [TestCarClaim09062] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:59:37.287 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: TestDb2, offset: null 
[INFO ] 2024-08-22 15:59:37.287 - [任务 11][JARADDB2WIMTEST132] - Table TestDb2 is going to be initial synced 
[INFO ] 2024-08-22 15:59:37.300 - [任务 11][JARADDB2WIMTEST132] - Query table 'TestDb2' counts: 20 
[INFO ] 2024-08-22 15:59:37.301 - [任务 11][JARADDB2WIMTEST132] - Table [TestDb2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:59:37.302 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: testoracle, offset: null 
[INFO ] 2024-08-22 15:59:37.302 - [任务 11][JARADDB2WIMTEST132] - Table testoracle is going to be initial synced 
[INFO ] 2024-08-22 15:59:37.311 - [任务 11][JARADDB2WIMTEST132] - Query table 'testoracle' counts: 12 
[INFO ] 2024-08-22 15:59:37.312 - [任务 11][JARADDB2WIMTEST132] - Table [testoracle] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:59:37.312 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: TESTDATE1, offset: null 
[INFO ] 2024-08-22 15:59:37.312 - [任务 11][JARADDB2WIMTEST132] - Table TESTDATE1 is going to be initial synced 
[INFO ] 2024-08-22 15:59:37.342 - [任务 11][JARADDB2WIMTEST132] - Query table 'TESTDATE1' counts: 5 
[INFO ] 2024-08-22 15:59:37.342 - [任务 11][JARADDB2WIMTEST132] - Table [TESTDATE1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:59:37.342 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: testTimestamp, offset: null 
[INFO ] 2024-08-22 15:59:37.343 - [任务 11][JARADDB2WIMTEST132] - Table testTimestamp is going to be initial synced 
[INFO ] 2024-08-22 15:59:37.354 - [任务 11][JARADDB2WIMTEST132] - Query table 'testTimestamp' counts: 7 
[INFO ] 2024-08-22 15:59:37.354 - [任务 11][JARADDB2WIMTEST132] - Table [testTimestamp] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:59:37.354 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: TESTDATE, offset: null 
[INFO ] 2024-08-22 15:59:37.354 - [任务 11][JARADDB2WIMTEST132] - Table TESTDATE is going to be initial synced 
[INFO ] 2024-08-22 15:59:37.362 - [任务 11][JARADDB2WIMTEST132] - Table [TESTDATE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:59:37.362 - [任务 11][JARADDB2WIMTEST132] - Query table 'TESTDATE' counts: 5 
[INFO ] 2024-08-22 15:59:37.362 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: Test_ikas, offset: null 
[INFO ] 2024-08-22 15:59:37.382 - [任务 11][JARADDB2WIMTEST132] - Table Test_ikas is going to be initial synced 
[INFO ] 2024-08-22 15:59:37.383 - [任务 11][JARADDB2WIMTEST132] - Query table 'Test_ikas' counts: 50000 
[INFO ] 2024-08-22 15:59:38.396 - [任务 11][JARADDB2WIMTEST132] - Table [Test_ikas] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:59:38.396 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: test222, offset: null 
[INFO ] 2024-08-22 15:59:38.396 - [任务 11][JARADDB2WIMTEST132] - Table test222 is going to be initial synced 
[INFO ] 2024-08-22 15:59:38.415 - [任务 11][JARADDB2WIMTEST132] - Table [test222] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 15:59:38.415 - [任务 11][JARADDB2WIMTEST132] - Query table 'test222' counts: 1 
[INFO ] 2024-08-22 15:59:38.415 - [任务 11][JARADDB2WIMTEST132] - Starting batch read, table name: Dummy_TT, offset: null 
[INFO ] 2024-08-22 15:59:38.620 - [任务 11][JARADDB2WIMTEST132] - Table Dummy_TT is going to be initial synced 
[INFO ] 2024-08-22 16:01:02.762 - [任务 11] - Stop task milestones: 66c6e55c17ade8409258b950(任务 11)  
[INFO ] 2024-08-22 16:01:02.780 - [任务 11][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[e6d197bb-e2e2-4360-990a-9ade359d0cc0] running status set to false 
[INFO ] 2024-08-22 16:01:02.781 - [任务 11][JARADDB2WIMTEST132] - Table [Dummy_TT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 16:01:05.968 - [任务 11][JARADDB2WIMTEST132] - Cancel query 'Dummy_TT' snapshot row size with task stopped. 
[INFO ] 2024-08-22 16:01:05.971 - [任务 11][JARADDB2WIMTEST132] - PDK connector node stopped: HazelcastSourcePdkDataNode-e6d197bb-e2e2-4360-990a-9ade359d0cc0 
[INFO ] 2024-08-22 16:01:05.971 - [任务 11][JARADDB2WIMTEST132] - PDK connector node released: HazelcastSourcePdkDataNode-e6d197bb-e2e2-4360-990a-9ade359d0cc0 
[INFO ] 2024-08-22 16:01:05.972 - [任务 11][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[e6d197bb-e2e2-4360-990a-9ade359d0cc0] schema data cleaned 
[INFO ] 2024-08-22 16:01:05.972 - [任务 11][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[e6d197bb-e2e2-4360-990a-9ade359d0cc0] monitor closed 
[INFO ] 2024-08-22 16:01:05.975 - [任务 11][JARADDB2WIMTEST132] - Node JARADDB2WIMTEST132[e6d197bb-e2e2-4360-990a-9ade359d0cc0] close complete, cost 3209 ms 
[INFO ] 2024-08-22 16:01:05.975 - [任务 11][TestDummy] - Node TestDummy[a4fb1cd9-887c-4321-9465-dd4f0c3fb6a2] running status set to false 
[INFO ] 2024-08-22 16:01:06.000 - [任务 11][TestDummy] - Stop connector 
[INFO ] 2024-08-22 16:01:06.000 - [任务 11][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-a4fb1cd9-887c-4321-9465-dd4f0c3fb6a2 
[INFO ] 2024-08-22 16:01:06.000 - [任务 11][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-a4fb1cd9-887c-4321-9465-dd4f0c3fb6a2 
[INFO ] 2024-08-22 16:01:06.000 - [任务 11][TestDummy] - Node TestDummy[a4fb1cd9-887c-4321-9465-dd4f0c3fb6a2] schema data cleaned 
[INFO ] 2024-08-22 16:01:06.001 - [任务 11][TestDummy] - Node TestDummy[a4fb1cd9-887c-4321-9465-dd4f0c3fb6a2] monitor closed 
[INFO ] 2024-08-22 16:01:06.001 - [任务 11][TestDummy] - Node TestDummy[a4fb1cd9-887c-4321-9465-dd4f0c3fb6a2] close complete, cost 27 ms 
[INFO ] 2024-08-22 16:01:10.903 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-22 16:01:10.904 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-08-22 16:01:10.904 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-08-22 16:01:10.955 - [任务 11] - Remove memory task client succeed, task: 任务 11[66c6e55c17ade8409258b950] 
[INFO ] 2024-08-22 16:01:10.955 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[66c6e55c17ade8409258b950] 
