[INFO ] 2024-10-16 00:16:42.232 - [任务 4] - Task initialization... 
[INFO ] 2024-10-16 00:16:42.234 - [任务 4] - Start task milestones: 670e9533f12cdc2b66d43fa6(任务 4) 
[INFO ] 2024-10-16 00:16:45.179 - [任务 4] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-10-16 00:16:45.385 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-16 00:16:45.658 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] start preload schema,table counts: 73 
[INFO ] 2024-10-16 00:16:45.659 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] start preload schema,table counts: 73 
[INFO ] 2024-10-16 00:16:45.659 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] preload schema finished, cost 3 ms 
[INFO ] 2024-10-16 00:16:45.661 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] preload schema finished, cost 2 ms 
[INFO ] 2024-10-16 00:16:46.644 - [任务 4][Mysql23306] - Node(Mysql23306) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-16 00:16:46.652 - [任务 4][Mysql23306] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-16 00:16:47.139 - [任务 4][MysqlCrmeb] - Source node "MysqlCrmeb" read batch size: 100 
[INFO ] 2024-10-16 00:16:47.140 - [任务 4][MysqlCrmeb] - Source node "MysqlCrmeb" event queue capacity: 200 
[INFO ] 2024-10-16 00:16:47.140 - [任务 4][MysqlCrmeb] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-16 00:16:47.145 - [任务 4][MysqlCrmeb] - batch offset found: {},stream offset not found. 
[INFO ] 2024-10-16 00:16:47.348 - [任务 4][MysqlCrmeb] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-16 00:16:47.389 - [任务 4][MysqlCrmeb] - Initial sync started 
[INFO ] 2024-10-16 00:16:47.397 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_order 
[INFO ] 2024-10-16 00:16:47.399 - [任务 4][MysqlCrmeb] - Table eb_store_order is going to be initial synced 
[INFO ] 2024-10-16 00:16:47.501 - [任务 4][MysqlCrmeb] - Table [eb_store_order] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:47.503 - [任务 4][MysqlCrmeb] - Query table 'eb_store_order' counts: 0 
[INFO ] 2024-10-16 00:16:47.505 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_attr_value 
[INFO ] 2024-10-16 00:16:47.505 - [任务 4][MysqlCrmeb] - Table eb_store_product_attr_value is going to be initial synced 
[INFO ] 2024-10-16 00:16:47.619 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_attr_value' counts: 38 
[INFO ] 2024-10-16 00:16:47.622 - [任务 4][MysqlCrmeb] - Table [eb_store_product_attr_value] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:47.624 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_wechat_callback 
[INFO ] 2024-10-16 00:16:47.624 - [任务 4][MysqlCrmeb] - Table eb_wechat_callback is going to be initial synced 
[INFO ] 2024-10-16 00:16:47.681 - [任务 4][MysqlCrmeb] - Query table 'eb_wechat_callback' counts: 0 
[INFO ] 2024-10-16 00:16:47.681 - [任务 4][MysqlCrmeb] - Table [eb_wechat_callback] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:47.689 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_shipping_templates_free 
[INFO ] 2024-10-16 00:16:47.690 - [任务 4][MysqlCrmeb] - Table eb_shipping_templates_free is going to be initial synced 
[INFO ] 2024-10-16 00:16:47.755 - [任务 4][MysqlCrmeb] - Query table 'eb_shipping_templates_free' counts: 32 
[INFO ] 2024-10-16 00:16:47.755 - [任务 4][MysqlCrmeb] - Table [eb_shipping_templates_free] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:47.758 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_notification 
[INFO ] 2024-10-16 00:16:47.759 - [任务 4][MysqlCrmeb] - Table eb_system_notification is going to be initial synced 
[INFO ] 2024-10-16 00:16:47.820 - [任务 4][MysqlCrmeb] - Query table 'eb_system_notification' counts: 12 
[INFO ] 2024-10-16 00:16:47.820 - [任务 4][MysqlCrmeb] - Table [eb_system_notification] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:47.821 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_heal_user 
[INFO ] 2024-10-16 00:16:47.821 - [任务 4][MysqlCrmeb] - Table eb_store_heal_user is going to be initial synced 
[INFO ] 2024-10-16 00:16:47.887 - [任务 4][MysqlCrmeb] - Query table 'eb_store_heal_user' counts: 9 
[INFO ] 2024-10-16 00:16:47.887 - [任务 4][MysqlCrmeb] - Table [eb_store_heal_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:47.889 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_level 
[INFO ] 2024-10-16 00:16:47.889 - [任务 4][MysqlCrmeb] - Table eb_user_level is going to be initial synced 
[INFO ] 2024-10-16 00:16:47.954 - [任务 4][MysqlCrmeb] - Query table 'eb_user_level' counts: 1 
[INFO ] 2024-10-16 00:16:47.955 - [任务 4][MysqlCrmeb] - Table [eb_user_level] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:47.955 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_token 
[INFO ] 2024-10-16 00:16:48.016 - [任务 4][MysqlCrmeb] - Table eb_user_token is going to be initial synced 
[INFO ] 2024-10-16 00:16:48.017 - [任务 4][MysqlCrmeb] - Query table 'eb_user_token' counts: 3 
[INFO ] 2024-10-16 00:16:48.025 - [任务 4][MysqlCrmeb] - Table [eb_user_token] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:48.026 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_group 
[INFO ] 2024-10-16 00:16:48.026 - [任务 4][MysqlCrmeb] - Table eb_system_group is going to be initial synced 
[INFO ] 2024-10-16 00:16:48.097 - [任务 4][MysqlCrmeb] - Query table 'eb_system_group' counts: 18 
[INFO ] 2024-10-16 00:16:48.097 - [任务 4][MysqlCrmeb] - Table [eb_system_group] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:48.100 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_config 
[INFO ] 2024-10-16 00:16:48.100 - [任务 4][MysqlCrmeb] - Table eb_system_config is going to be initial synced 
[INFO ] 2024-10-16 00:16:48.183 - [任务 4][MysqlCrmeb] - Query table 'eb_system_config' counts: 177 
[INFO ] 2024-10-16 00:16:48.186 - [任务 4][MysqlCrmeb] - Table [eb_system_config] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:48.188 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_sign 
[INFO ] 2024-10-16 00:16:48.189 - [任务 4][MysqlCrmeb] - Table eb_user_sign is going to be initial synced 
[INFO ] 2024-10-16 00:16:48.248 - [任务 4][MysqlCrmeb] - Query table 'eb_user_sign' counts: 1 
[INFO ] 2024-10-16 00:16:48.248 - [任务 4][MysqlCrmeb] - Table [eb_user_sign] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:48.249 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_cate 
[INFO ] 2024-10-16 00:16:48.249 - [任务 4][MysqlCrmeb] - Table eb_store_product_cate is going to be initial synced 
[INFO ] 2024-10-16 00:16:48.321 - [任务 4][MysqlCrmeb] - Table [eb_store_product_cate] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:48.325 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_cate' counts: 0 
[INFO ] 2024-10-16 00:16:48.325 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_experience_record 
[INFO ] 2024-10-16 00:16:48.325 - [任务 4][MysqlCrmeb] - Table eb_user_experience_record is going to be initial synced 
[INFO ] 2024-10-16 00:16:48.386 - [任务 4][MysqlCrmeb] - Table [eb_user_experience_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:48.386 - [任务 4][MysqlCrmeb] - Query table 'eb_user_experience_record' counts: 1 
[INFO ] 2024-10-16 00:16:48.387 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_brand_story 
[INFO ] 2024-10-16 00:16:48.389 - [任务 4][MysqlCrmeb] - Table eb_store_brand_story is going to be initial synced 
[INFO ] 2024-10-16 00:16:48.451 - [任务 4][MysqlCrmeb] - Query table 'eb_store_brand_story' counts: 1 
[INFO ] 2024-10-16 00:16:48.452 - [任务 4][MysqlCrmeb] - Table [eb_store_brand_story] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:48.452 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_heal 
[INFO ] 2024-10-16 00:16:48.453 - [任务 4][MysqlCrmeb] - Table eb_store_heal is going to be initial synced 
[INFO ] 2024-10-16 00:16:48.513 - [任务 4][MysqlCrmeb] - Query table 'eb_store_heal' counts: 5 
[INFO ] 2024-10-16 00:16:48.526 - [任务 4][MysqlCrmeb] - Table [eb_store_heal] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:48.526 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_description 
[INFO ] 2024-10-16 00:16:48.593 - [任务 4][MysqlCrmeb] - Table eb_store_product_description is going to be initial synced 
[INFO ] 2024-10-16 00:16:48.593 - [任务 4][MysqlCrmeb] - Table [eb_store_product_description] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:48.597 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_description' counts: 18 
[INFO ] 2024-10-16 00:16:48.600 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_reply 
[INFO ] 2024-10-16 00:16:48.601 - [任务 4][MysqlCrmeb] - Table eb_store_product_reply is going to be initial synced 
[INFO ] 2024-10-16 00:16:48.661 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_reply' counts: 1 
[INFO ] 2024-10-16 00:16:48.661 - [任务 4][MysqlCrmeb] - Table [eb_store_product_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:48.663 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_brokerage_record 
[INFO ] 2024-10-16 00:16:48.664 - [任务 4][MysqlCrmeb] - Table eb_user_brokerage_record is going to be initial synced 
[INFO ] 2024-10-16 00:16:48.724 - [任务 4][MysqlCrmeb] - Table [eb_user_brokerage_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:48.725 - [任务 4][MysqlCrmeb] - Query table 'eb_user_brokerage_record' counts: 0 
[INFO ] 2024-10-16 00:16:48.725 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_attachment 
[INFO ] 2024-10-16 00:16:48.779 - [任务 4][MysqlCrmeb] - Table eb_system_attachment is going to be initial synced 
[INFO ] 2024-10-16 00:16:48.781 - [任务 4][MysqlCrmeb] - Query table 'eb_system_attachment' counts: 288 
[INFO ] 2024-10-16 00:16:51.377 - [任务 4][MysqlCrmeb] - Table [eb_system_attachment] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:51.383 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_store_staff 
[INFO ] 2024-10-16 00:16:51.385 - [任务 4][MysqlCrmeb] - Table eb_system_store_staff is going to be initial synced 
[INFO ] 2024-10-16 00:16:51.453 - [任务 4][MysqlCrmeb] - Table [eb_system_store_staff] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:51.454 - [任务 4][MysqlCrmeb] - Query table 'eb_system_store_staff' counts: 0 
[INFO ] 2024-10-16 00:16:51.454 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_wechat_reply 
[INFO ] 2024-10-16 00:16:51.456 - [任务 4][MysqlCrmeb] - Table eb_wechat_reply is going to be initial synced 
[INFO ] 2024-10-16 00:16:51.518 - [任务 4][MysqlCrmeb] - Query table 'eb_wechat_reply' counts: 1 
[INFO ] 2024-10-16 00:16:51.519 - [任务 4][MysqlCrmeb] - Table [eb_wechat_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:51.519 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_bargain_user_help 
[INFO ] 2024-10-16 00:16:51.520 - [任务 4][MysqlCrmeb] - Table eb_store_bargain_user_help is going to be initial synced 
[INFO ] 2024-10-16 00:16:51.584 - [任务 4][MysqlCrmeb] - Query table 'eb_store_bargain_user_help' counts: 0 
[INFO ] 2024-10-16 00:16:51.585 - [任务 4][MysqlCrmeb] - Table [eb_store_bargain_user_help] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:51.585 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_seckill 
[INFO ] 2024-10-16 00:16:51.586 - [任务 4][MysqlCrmeb] - Table eb_store_seckill is going to be initial synced 
[INFO ] 2024-10-16 00:16:51.649 - [任务 4][MysqlCrmeb] - Query table 'eb_store_seckill' counts: 4 
[INFO ] 2024-10-16 00:16:51.649 - [任务 4][MysqlCrmeb] - Table [eb_store_seckill] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:51.650 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_attr_result 
[INFO ] 2024-10-16 00:16:51.650 - [任务 4][MysqlCrmeb] - Table eb_store_product_attr_result is going to be initial synced 
[INFO ] 2024-10-16 00:16:51.709 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_attr_result' counts: 0 
[INFO ] 2024-10-16 00:16:51.709 - [任务 4][MysqlCrmeb] - Table [eb_store_product_attr_result] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:51.710 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_shipping_templates 
[INFO ] 2024-10-16 00:16:51.711 - [任务 4][MysqlCrmeb] - Table eb_shipping_templates is going to be initial synced 
[INFO ] 2024-10-16 00:16:51.772 - [任务 4][MysqlCrmeb] - Table [eb_shipping_templates] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:51.772 - [任务 4][MysqlCrmeb] - Query table 'eb_shipping_templates' counts: 1 
[INFO ] 2024-10-16 00:16:51.772 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_seckill_manger 
[INFO ] 2024-10-16 00:16:51.772 - [任务 4][MysqlCrmeb] - Table eb_store_seckill_manger is going to be initial synced 
[INFO ] 2024-10-16 00:16:51.836 - [任务 4][MysqlCrmeb] - Query table 'eb_store_seckill_manger' counts: 3 
[INFO ] 2024-10-16 00:16:51.836 - [任务 4][MysqlCrmeb] - Table [eb_store_seckill_manger] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:51.838 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user 
[INFO ] 2024-10-16 00:16:51.838 - [任务 4][MysqlCrmeb] - Table eb_user is going to be initial synced 
[INFO ] 2024-10-16 00:16:51.902 - [任务 4][MysqlCrmeb] - Table [eb_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:51.902 - [任务 4][MysqlCrmeb] - Query table 'eb_user' counts: 4 
[INFO ] 2024-10-16 00:16:51.910 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_order_status 
[INFO ] 2024-10-16 00:16:51.910 - [任务 4][MysqlCrmeb] - Table eb_store_order_status is going to be initial synced 
[INFO ] 2024-10-16 00:16:51.985 - [任务 4][MysqlCrmeb] - Table [eb_store_order_status] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:51.985 - [任务 4][MysqlCrmeb] - Query table 'eb_store_order_status' counts: 0 
[INFO ] 2024-10-16 00:16:51.992 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_form_temp 
[INFO ] 2024-10-16 00:16:51.995 - [任务 4][MysqlCrmeb] - Table eb_system_form_temp is going to be initial synced 
[INFO ] 2024-10-16 00:16:52.051 - [任务 4][MysqlCrmeb] - Query table 'eb_system_form_temp' counts: 60 
[INFO ] 2024-10-16 00:16:52.332 - [任务 4][MysqlCrmeb] - Table [eb_system_form_temp] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:52.332 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_reply 
[INFO ] 2024-10-16 00:16:52.394 - [任务 4][MysqlCrmeb] - Table eb_store_experience_reply is going to be initial synced 
[INFO ] 2024-10-16 00:16:52.394 - [任务 4][MysqlCrmeb] - Table [eb_store_experience_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:52.440 - [任务 4][MysqlCrmeb] - Query table 'eb_store_experience_reply' counts: 2 
[INFO ] 2024-10-16 00:16:52.440 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_bargain_user 
[INFO ] 2024-10-16 00:16:52.440 - [任务 4][MysqlCrmeb] - Table eb_store_bargain_user is going to be initial synced 
[INFO ] 2024-10-16 00:16:52.499 - [任务 4][MysqlCrmeb] - Query table 'eb_store_bargain_user' counts: 0 
[INFO ] 2024-10-16 00:16:52.499 - [任务 4][MysqlCrmeb] - Table [eb_store_bargain_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:52.499 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_store 
[INFO ] 2024-10-16 00:16:52.499 - [任务 4][MysqlCrmeb] - Table eb_system_store is going to be initial synced 
[INFO ] 2024-10-16 00:16:52.679 - [任务 4][MysqlCrmeb] - Table [eb_system_store] has been completed batch read, will skip batch read on the next run 
[WARN ] 2024-10-16 00:16:52.679 - [任务 4][Mysql23306] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[3903a72b-a592-4673-bf5f-e83e5b1219eb], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-10-16 00:16:53.218 - [任务 4][MysqlCrmeb] - Query table 'eb_system_store' counts: 0 
[INFO ] 2024-10-16 00:16:53.226 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_shipping_templates_region 
[INFO ] 2024-10-16 00:16:53.228 - [任务 4][MysqlCrmeb] - Table eb_shipping_templates_region is going to be initial synced 
[INFO ] 2024-10-16 00:16:53.308 - [任务 4][MysqlCrmeb] - Query table 'eb_shipping_templates_region' counts: 369 
[INFO ] 2024-10-16 00:16:53.308 - [任务 4][MysqlCrmeb] - Table [eb_shipping_templates_region] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:53.308 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_recharge 
[INFO ] 2024-10-16 00:16:53.309 - [任务 4][MysqlCrmeb] - Table eb_user_recharge is going to be initial synced 
[INFO ] 2024-10-16 00:16:53.378 - [任务 4][MysqlCrmeb] - Query table 'eb_user_recharge' counts: 0 
[INFO ] 2024-10-16 00:16:53.378 - [任务 4][MysqlCrmeb] - Table [eb_user_recharge] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:53.378 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_coupon 
[INFO ] 2024-10-16 00:16:53.378 - [任务 4][MysqlCrmeb] - Table eb_store_product_coupon is going to be initial synced 
[INFO ] 2024-10-16 00:16:53.436 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_coupon' counts: 0 
[INFO ] 2024-10-16 00:16:53.436 - [任务 4][MysqlCrmeb] - Table [eb_store_product_coupon] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:53.437 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_wechat_pay_info 
[INFO ] 2024-10-16 00:16:53.437 - [任务 4][MysqlCrmeb] - Table eb_wechat_pay_info is going to be initial synced 
[INFO ] 2024-10-16 00:16:53.497 - [任务 4][MysqlCrmeb] - Query table 'eb_wechat_pay_info' counts: 0 
[INFO ] 2024-10-16 00:16:53.497 - [任务 4][MysqlCrmeb] - Table [eb_wechat_pay_info] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:53.498 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_combination 
[INFO ] 2024-10-16 00:16:53.498 - [任务 4][MysqlCrmeb] - Table eb_store_combination is going to be initial synced 
[INFO ] 2024-10-16 00:16:53.568 - [任务 4][MysqlCrmeb] - Query table 'eb_store_combination' counts: 4 
[INFO ] 2024-10-16 00:16:53.568 - [任务 4][MysqlCrmeb] - Table [eb_store_combination] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:53.569 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product 
[INFO ] 2024-10-16 00:16:53.569 - [任务 4][MysqlCrmeb] - Table eb_store_product is going to be initial synced 
[INFO ] 2024-10-16 00:16:53.631 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product' counts: 8 
[INFO ] 2024-10-16 00:16:53.631 - [任务 4][MysqlCrmeb] - Table [eb_store_product] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:53.632 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_article 
[INFO ] 2024-10-16 00:16:53.632 - [任务 4][MysqlCrmeb] - Table eb_article is going to be initial synced 
[INFO ] 2024-10-16 00:16:53.694 - [任务 4][MysqlCrmeb] - Query table 'eb_article' counts: 0 
[INFO ] 2024-10-16 00:16:53.695 - [任务 4][MysqlCrmeb] - Table [eb_article] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:53.696 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_bill 
[INFO ] 2024-10-16 00:16:53.697 - [任务 4][MysqlCrmeb] - Table eb_user_bill is going to be initial synced 
[INFO ] 2024-10-16 00:16:53.756 - [任务 4][MysqlCrmeb] - Table [eb_user_bill] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:53.757 - [任务 4][MysqlCrmeb] - Query table 'eb_user_bill' counts: 0 
[INFO ] 2024-10-16 00:16:53.758 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_relation 
[INFO ] 2024-10-16 00:16:53.758 - [任务 4][MysqlCrmeb] - Table eb_store_experience_relation is going to be initial synced 
[INFO ] 2024-10-16 00:16:53.819 - [任务 4][MysqlCrmeb] - Query table 'eb_store_experience_relation' counts: 2 
[INFO ] 2024-10-16 00:16:53.819 - [任务 4][MysqlCrmeb] - Table [eb_store_experience_relation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:53.819 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_wechat_exceptions 
[INFO ] 2024-10-16 00:16:53.820 - [任务 4][MysqlCrmeb] - Table eb_wechat_exceptions is going to be initial synced 
[INFO ] 2024-10-16 00:16:53.880 - [任务 4][MysqlCrmeb] - Query table 'eb_wechat_exceptions' counts: 31 
[INFO ] 2024-10-16 00:16:53.880 - [任务 4][MysqlCrmeb] - Table [eb_wechat_exceptions] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:53.884 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_template_message 
[INFO ] 2024-10-16 00:16:53.884 - [任务 4][MysqlCrmeb] - Table eb_template_message is going to be initial synced 
[INFO ] 2024-10-16 00:16:53.947 - [任务 4][MysqlCrmeb] - Query table 'eb_template_message' counts: 15 
[INFO ] 2024-10-16 00:16:53.948 - [任务 4][MysqlCrmeb] - Table [eb_template_message] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:53.948 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_integral_record 
[INFO ] 2024-10-16 00:16:53.949 - [任务 4][MysqlCrmeb] - Table eb_user_integral_record is going to be initial synced 
[INFO ] 2024-10-16 00:16:54.015 - [任务 4][MysqlCrmeb] - Query table 'eb_user_integral_record' counts: 1 
[INFO ] 2024-10-16 00:16:54.022 - [任务 4][MysqlCrmeb] - Table [eb_user_integral_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:54.030 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_coupon 
[INFO ] 2024-10-16 00:16:54.030 - [任务 4][MysqlCrmeb] - Table eb_store_coupon is going to be initial synced 
[INFO ] 2024-10-16 00:16:54.081 - [任务 4][MysqlCrmeb] - Query table 'eb_store_coupon' counts: 2 
[INFO ] 2024-10-16 00:16:54.081 - [任务 4][MysqlCrmeb] - Table [eb_store_coupon] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:54.081 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_visit_record 
[INFO ] 2024-10-16 00:16:54.081 - [任务 4][MysqlCrmeb] - Table eb_user_visit_record is going to be initial synced 
[INFO ] 2024-10-16 00:16:54.156 - [任务 4][MysqlCrmeb] - Query table 'eb_user_visit_record' counts: 1241 
[INFO ] 2024-10-16 00:16:54.156 - [任务 4][MysqlCrmeb] - Table [eb_user_visit_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:54.158 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_role_menu 
[INFO ] 2024-10-16 00:16:54.158 - [任务 4][MysqlCrmeb] - Table eb_system_role_menu is going to be initial synced 
[INFO ] 2024-10-16 00:16:54.227 - [任务 4][MysqlCrmeb] - Query table 'eb_system_role_menu' counts: 595 
[INFO ] 2024-10-16 00:16:54.227 - [任务 4][MysqlCrmeb] - Table [eb_system_role_menu] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:54.227 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_log 
[INFO ] 2024-10-16 00:16:54.228 - [任务 4][MysqlCrmeb] - Table eb_store_product_log is going to be initial synced 
[INFO ] 2024-10-16 00:16:54.287 - [任务 4][MysqlCrmeb] - Table [eb_store_product_log] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:54.287 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_log' counts: 0 
[INFO ] 2024-10-16 00:16:54.288 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_series 
[INFO ] 2024-10-16 00:16:54.288 - [任务 4][MysqlCrmeb] - Table eb_store_product_series is going to be initial synced 
[INFO ] 2024-10-16 00:16:54.346 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_series' counts: 5 
[INFO ] 2024-10-16 00:16:54.346 - [任务 4][MysqlCrmeb] - Table [eb_store_product_series] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:54.346 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_attr 
[INFO ] 2024-10-16 00:16:54.347 - [任务 4][MysqlCrmeb] - Table eb_store_product_attr is going to be initial synced 
[INFO ] 2024-10-16 00:16:54.410 - [任务 4][MysqlCrmeb] - Table [eb_store_product_attr] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:54.410 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_attr' counts: 24 
[INFO ] 2024-10-16 00:16:54.410 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_bargain 
[INFO ] 2024-10-16 00:16:54.472 - [任务 4][MysqlCrmeb] - Table eb_store_bargain is going to be initial synced 
[INFO ] 2024-10-16 00:16:54.472 - [任务 4][MysqlCrmeb] - Query table 'eb_store_bargain' counts: 2 
[INFO ] 2024-10-16 00:16:54.472 - [任务 4][MysqlCrmeb] - Table [eb_store_bargain] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:54.472 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_audio 
[INFO ] 2024-10-16 00:16:54.538 - [任务 4][MysqlCrmeb] - Table eb_store_experience_audio is going to be initial synced 
[INFO ] 2024-10-16 00:16:54.538 - [任务 4][MysqlCrmeb] - Table [eb_store_experience_audio] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:54.538 - [任务 4][MysqlCrmeb] - Query table 'eb_store_experience_audio' counts: 5 
[INFO ] 2024-10-16 00:16:54.539 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_city 
[INFO ] 2024-10-16 00:16:54.539 - [任务 4][MysqlCrmeb] - Table eb_system_city is going to be initial synced 
[INFO ] 2024-10-16 00:16:54.740 - [任务 4][MysqlCrmeb] - Query table 'eb_system_city' counts: 3938 
[INFO ] 2024-10-16 00:16:58.052 - [任务 4][MysqlCrmeb] - Table [eb_system_city] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:58.053 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_sms_record 
[INFO ] 2024-10-16 00:16:58.055 - [任务 4][MysqlCrmeb] - Table eb_sms_record is going to be initial synced 
[INFO ] 2024-10-16 00:16:58.130 - [任务 4][MysqlCrmeb] - Table [eb_sms_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:58.131 - [任务 4][MysqlCrmeb] - Query table 'eb_sms_record' counts: 0 
[INFO ] 2024-10-16 00:16:58.196 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_extract 
[INFO ] 2024-10-16 00:16:58.196 - [任务 4][MysqlCrmeb] - Table eb_user_extract is going to be initial synced 
[INFO ] 2024-10-16 00:16:58.196 - [任务 4][MysqlCrmeb] - Query table 'eb_user_extract' counts: 0 
[INFO ] 2024-10-16 00:16:58.196 - [任务 4][MysqlCrmeb] - Table [eb_user_extract] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:58.196 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_group 
[INFO ] 2024-10-16 00:16:58.257 - [任务 4][MysqlCrmeb] - Table eb_user_group is going to be initial synced 
[INFO ] 2024-10-16 00:16:58.258 - [任务 4][MysqlCrmeb] - Table [eb_user_group] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:58.921 - [任务 4][MysqlCrmeb] - Query table 'eb_user_group' counts: 3 
[INFO ] 2024-10-16 00:16:58.922 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_rule 
[INFO ] 2024-10-16 00:16:59.010 - [任务 4][MysqlCrmeb] - Table eb_store_product_rule is going to be initial synced 
[INFO ] 2024-10-16 00:16:59.011 - [任务 4][MysqlCrmeb] - Table [eb_store_product_rule] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:59.206 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_rule' counts: 2 
[INFO ] 2024-10-16 00:16:59.208 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_cart 
[INFO ] 2024-10-16 00:16:59.208 - [任务 4][MysqlCrmeb] - Table eb_store_cart is going to be initial synced 
[INFO ] 2024-10-16 00:16:59.276 - [任务 4][MysqlCrmeb] - Table [eb_store_cart] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:59.276 - [任务 4][MysqlCrmeb] - Query table 'eb_store_cart' counts: 3 
[INFO ] 2024-10-16 00:16:59.279 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_admin 
[INFO ] 2024-10-16 00:16:59.279 - [任务 4][MysqlCrmeb] - Table eb_system_admin is going to be initial synced 
[INFO ] 2024-10-16 00:16:59.346 - [任务 4][MysqlCrmeb] - Query table 'eb_system_admin' counts: 2 
[INFO ] 2024-10-16 00:16:59.346 - [任务 4][MysqlCrmeb] - Table [eb_system_admin] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:59.347 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_menu 
[INFO ] 2024-10-16 00:16:59.348 - [任务 4][MysqlCrmeb] - Table eb_system_menu is going to be initial synced 
[INFO ] 2024-10-16 00:16:59.456 - [任务 4][MysqlCrmeb] - Query table 'eb_system_menu' counts: 369 
[INFO ] 2024-10-16 00:16:59.457 - [任务 4][MysqlCrmeb] - Table [eb_system_menu] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:59.463 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_user_level 
[INFO ] 2024-10-16 00:16:59.464 - [任务 4][MysqlCrmeb] - Table eb_system_user_level is going to be initial synced 
[INFO ] 2024-10-16 00:16:59.519 - [任务 4][MysqlCrmeb] - Query table 'eb_system_user_level' counts: 5 
[INFO ] 2024-10-16 00:16:59.520 - [任务 4][MysqlCrmeb] - Table [eb_system_user_level] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:59.521 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_category 
[INFO ] 2024-10-16 00:16:59.581 - [任务 4][MysqlCrmeb] - Table eb_category is going to be initial synced 
[INFO ] 2024-10-16 00:16:59.582 - [任务 4][MysqlCrmeb] - Query table 'eb_category' counts: 295 
[INFO ] 2024-10-16 00:16:59.617 - [任务 4][MysqlCrmeb] - Table [eb_category] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:59.617 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_sms_template 
[INFO ] 2024-10-16 00:16:59.678 - [任务 4][MysqlCrmeb] - Table eb_sms_template is going to be initial synced 
[INFO ] 2024-10-16 00:16:59.679 - [任务 4][MysqlCrmeb] - Table [eb_sms_template] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:59.688 - [任务 4][MysqlCrmeb] - Query table 'eb_sms_template' counts: 7 
[INFO ] 2024-10-16 00:16:59.688 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_tag 
[INFO ] 2024-10-16 00:16:59.762 - [任务 4][MysqlCrmeb] - Table eb_user_tag is going to be initial synced 
[INFO ] 2024-10-16 00:16:59.762 - [任务 4][MysqlCrmeb] - Query table 'eb_user_tag' counts: 5 
[INFO ] 2024-10-16 00:16:59.762 - [任务 4][MysqlCrmeb] - Table [eb_user_tag] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:59.763 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_order_info 
[INFO ] 2024-10-16 00:16:59.828 - [任务 4][MysqlCrmeb] - Table eb_store_order_info is going to be initial synced 
[INFO ] 2024-10-16 00:16:59.829 - [任务 4][MysqlCrmeb] - Query table 'eb_store_order_info' counts: 0 
[INFO ] 2024-10-16 00:16:59.829 - [任务 4][MysqlCrmeb] - Table [eb_store_order_info] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:59.829 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_coupon_user 
[INFO ] 2024-10-16 00:16:59.891 - [任务 4][MysqlCrmeb] - Table eb_store_coupon_user is going to be initial synced 
[INFO ] 2024-10-16 00:16:59.891 - [任务 4][MysqlCrmeb] - Table [eb_store_coupon_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:59.891 - [任务 4][MysqlCrmeb] - Query table 'eb_store_coupon_user' counts: 0 
[INFO ] 2024-10-16 00:16:59.891 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_role 
[INFO ] 2024-10-16 00:16:59.956 - [任务 4][MysqlCrmeb] - Table eb_system_role is going to be initial synced 
[INFO ] 2024-10-16 00:16:59.956 - [任务 4][MysqlCrmeb] - Query table 'eb_system_role' counts: 2 
[INFO ] 2024-10-16 00:16:59.966 - [任务 4][MysqlCrmeb] - Table [eb_system_role] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:16:59.967 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_audio_history 
[INFO ] 2024-10-16 00:17:00.031 - [任务 4][MysqlCrmeb] - Table eb_store_experience_audio_history is going to be initial synced 
[INFO ] 2024-10-16 00:17:00.032 - [任务 4][MysqlCrmeb] - Query table 'eb_store_experience_audio_history' counts: 4 
[INFO ] 2024-10-16 00:17:00.032 - [任务 4][MysqlCrmeb] - Table [eb_store_experience_audio_history] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:17:00.034 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_pink 
[INFO ] 2024-10-16 00:17:00.034 - [任务 4][MysqlCrmeb] - Table eb_store_pink is going to be initial synced 
[INFO ] 2024-10-16 00:17:00.100 - [任务 4][MysqlCrmeb] - Query table 'eb_store_pink' counts: 0 
[INFO ] 2024-10-16 00:17:00.100 - [任务 4][MysqlCrmeb] - Table [eb_store_pink] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:17:00.101 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_express 
[INFO ] 2024-10-16 00:17:00.101 - [任务 4][MysqlCrmeb] - Table eb_express is going to be initial synced 
[INFO ] 2024-10-16 00:17:00.199 - [任务 4][MysqlCrmeb] - Query table 'eb_express' counts: 1067 
[INFO ] 2024-10-16 00:17:00.200 - [任务 4][MysqlCrmeb] - Table [eb_express] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:17:00.200 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_relation 
[INFO ] 2024-10-16 00:17:00.256 - [任务 4][MysqlCrmeb] - Table eb_store_product_relation is going to be initial synced 
[INFO ] 2024-10-16 00:17:00.256 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_relation' counts: 0 
[INFO ] 2024-10-16 00:17:00.261 - [任务 4][MysqlCrmeb] - Table [eb_store_product_relation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:17:00.261 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_group_data 
[INFO ] 2024-10-16 00:17:00.319 - [任务 4][MysqlCrmeb] - Table eb_system_group_data is going to be initial synced 
[INFO ] 2024-10-16 00:17:00.319 - [任务 4][MysqlCrmeb] - Query table 'eb_system_group_data' counts: 68 
[INFO ] 2024-10-16 00:17:00.330 - [任务 4][MysqlCrmeb] - Table [eb_system_group_data] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:17:00.330 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_address 
[INFO ] 2024-10-16 00:17:00.389 - [任务 4][MysqlCrmeb] - Table eb_user_address is going to be initial synced 
[INFO ] 2024-10-16 00:17:00.389 - [任务 4][MysqlCrmeb] - Table [eb_user_address] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:17:00.389 - [任务 4][MysqlCrmeb] - Query table 'eb_user_address' counts: 0 
[INFO ] 2024-10-16 00:17:00.390 - [任务 4][MysqlCrmeb] - Initial sync completed 
[INFO ] 2024-10-16 00:17:01.850 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] running status set to false 
[INFO ] 2024-10-16 00:17:01.850 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] running status set to false 
[INFO ] 2024-10-16 00:17:01.944 - [任务 4][MysqlCrmeb] - PDK connector node stopped: HazelcastSourcePdkDataNode-3903a72b-a592-4673-bf5f-e83e5b1219eb 
[INFO ] 2024-10-16 00:17:01.944 - [任务 4][Mysql23306] - PDK connector node stopped: HazelcastTargetPdkDataNode-eeb92868-7cb8-47f7-b7c0-193204274836 
[INFO ] 2024-10-16 00:17:01.946 - [任务 4][Mysql23306] - PDK connector node released: HazelcastTargetPdkDataNode-eeb92868-7cb8-47f7-b7c0-193204274836 
[INFO ] 2024-10-16 00:17:01.948 - [任务 4][MysqlCrmeb] - PDK connector node released: HazelcastSourcePdkDataNode-3903a72b-a592-4673-bf5f-e83e5b1219eb 
[INFO ] 2024-10-16 00:17:01.948 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] schema data cleaned 
[INFO ] 2024-10-16 00:17:01.950 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] schema data cleaned 
[INFO ] 2024-10-16 00:17:01.950 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] monitor closed 
[INFO ] 2024-10-16 00:17:01.965 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] monitor closed 
[INFO ] 2024-10-16 00:17:01.965 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] close complete, cost 109 ms 
[INFO ] 2024-10-16 00:17:01.965 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] close complete, cost 108 ms 
[INFO ] 2024-10-16 00:17:04.338 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-16 00:17:04.342 - [任务 4] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@8ad6c43 
[INFO ] 2024-10-16 00:17:04.351 - [任务 4] - Stop task milestones: 670e9533f12cdc2b66d43fa6(任务 4)  
[INFO ] 2024-10-16 00:17:04.475 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-10-16 00:17:04.475 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-10-16 00:17:04.532 - [任务 4] - Remove memory task client succeed, task: 任务 4[670e9533f12cdc2b66d43fa6] 
[INFO ] 2024-10-16 00:17:04.532 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[670e9533f12cdc2b66d43fa6] 
[INFO ] 2024-10-16 00:23:47.502 - [任务 4] - Task initialization... 
[INFO ] 2024-10-16 00:23:47.709 - [任务 4] - Start task milestones: 670e9533f12cdc2b66d43fa6(任务 4) 
[INFO ] 2024-10-16 00:23:50.274 - [任务 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-16 00:23:50.478 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-16 00:23:50.817 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] start preload schema,table counts: 73 
[INFO ] 2024-10-16 00:23:50.817 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] start preload schema,table counts: 73 
[INFO ] 2024-10-16 00:23:50.819 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] preload schema finished, cost 0 ms 
[INFO ] 2024-10-16 00:23:50.820 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] preload schema finished, cost 2 ms 
[INFO ] 2024-10-16 00:23:52.050 - [任务 4][Mysql23306] - Node(Mysql23306) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-16 00:23:52.052 - [任务 4][Mysql23306] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-16 00:23:52.274 - [任务 4][MysqlCrmeb] - Source node "MysqlCrmeb" read batch size: 100 
[INFO ] 2024-10-16 00:23:52.276 - [任务 4][MysqlCrmeb] - Source node "MysqlCrmeb" event queue capacity: 200 
[INFO ] 2024-10-16 00:23:52.277 - [任务 4][MysqlCrmeb] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-16 00:23:52.288 - [任务 4][MysqlCrmeb] - batch offset found: {},stream offset not found. 
[INFO ] 2024-10-16 00:23:52.290 - [任务 4][MysqlCrmeb] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-16 00:23:52.542 - [任务 4][MysqlCrmeb] - Initial sync started 
[INFO ] 2024-10-16 00:23:52.543 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_order 
[INFO ] 2024-10-16 00:23:52.562 - [任务 4][MysqlCrmeb] - Table eb_store_order is going to be initial synced 
[INFO ] 2024-10-16 00:23:52.659 - [任务 4][MysqlCrmeb] - Table [eb_store_order] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:52.659 - [任务 4][MysqlCrmeb] - Query table 'eb_store_order' counts: 0 
[INFO ] 2024-10-16 00:23:52.661 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_attr_value 
[INFO ] 2024-10-16 00:23:52.748 - [任务 4][MysqlCrmeb] - Table eb_store_product_attr_value is going to be initial synced 
[INFO ] 2024-10-16 00:23:52.751 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_attr_value' counts: 38 
[INFO ] 2024-10-16 00:23:52.789 - [任务 4][MysqlCrmeb] - Table [eb_store_product_attr_value] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:52.795 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_wechat_callback 
[INFO ] 2024-10-16 00:23:52.796 - [任务 4][MysqlCrmeb] - Table eb_wechat_callback is going to be initial synced 
[INFO ] 2024-10-16 00:23:52.866 - [任务 4][MysqlCrmeb] - Query table 'eb_wechat_callback' counts: 0 
[INFO ] 2024-10-16 00:23:52.869 - [任务 4][MysqlCrmeb] - Table [eb_wechat_callback] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:52.869 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_shipping_templates_free 
[INFO ] 2024-10-16 00:23:52.869 - [任务 4][MysqlCrmeb] - Table eb_shipping_templates_free is going to be initial synced 
[INFO ] 2024-10-16 00:23:52.940 - [任务 4][MysqlCrmeb] - Query table 'eb_shipping_templates_free' counts: 32 
[INFO ] 2024-10-16 00:23:52.941 - [任务 4][MysqlCrmeb] - Table [eb_shipping_templates_free] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:52.941 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_notification 
[INFO ] 2024-10-16 00:23:52.945 - [任务 4][MysqlCrmeb] - Table eb_system_notification is going to be initial synced 
[INFO ] 2024-10-16 00:23:53.012 - [任务 4][MysqlCrmeb] - Query table 'eb_system_notification' counts: 12 
[INFO ] 2024-10-16 00:23:53.012 - [任务 4][MysqlCrmeb] - Table [eb_system_notification] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:53.013 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_heal_user 
[INFO ] 2024-10-16 00:23:53.013 - [任务 4][MysqlCrmeb] - Table eb_store_heal_user is going to be initial synced 
[INFO ] 2024-10-16 00:23:53.084 - [任务 4][MysqlCrmeb] - Query table 'eb_store_heal_user' counts: 9 
[INFO ] 2024-10-16 00:23:53.085 - [任务 4][MysqlCrmeb] - Table [eb_store_heal_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:53.086 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_level 
[INFO ] 2024-10-16 00:23:53.088 - [任务 4][MysqlCrmeb] - Table eb_user_level is going to be initial synced 
[INFO ] 2024-10-16 00:23:53.158 - [任务 4][MysqlCrmeb] - Query table 'eb_user_level' counts: 1 
[INFO ] 2024-10-16 00:23:53.158 - [任务 4][MysqlCrmeb] - Table [eb_user_level] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:53.159 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_token 
[INFO ] 2024-10-16 00:23:53.159 - [任务 4][MysqlCrmeb] - Table eb_user_token is going to be initial synced 
[INFO ] 2024-10-16 00:23:53.224 - [任务 4][MysqlCrmeb] - Query table 'eb_user_token' counts: 3 
[INFO ] 2024-10-16 00:23:53.225 - [任务 4][MysqlCrmeb] - Table [eb_user_token] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:53.228 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_group 
[INFO ] 2024-10-16 00:23:53.230 - [任务 4][MysqlCrmeb] - Table eb_system_group is going to be initial synced 
[INFO ] 2024-10-16 00:23:53.295 - [任务 4][MysqlCrmeb] - Query table 'eb_system_group' counts: 18 
[INFO ] 2024-10-16 00:23:53.295 - [任务 4][MysqlCrmeb] - Table [eb_system_group] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:53.298 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_config 
[INFO ] 2024-10-16 00:23:53.299 - [任务 4][MysqlCrmeb] - Table eb_system_config is going to be initial synced 
[INFO ] 2024-10-16 00:23:53.395 - [任务 4][MysqlCrmeb] - Query table 'eb_system_config' counts: 177 
[INFO ] 2024-10-16 00:23:53.398 - [任务 4][MysqlCrmeb] - Table [eb_system_config] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:53.398 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_sign 
[INFO ] 2024-10-16 00:23:53.398 - [任务 4][MysqlCrmeb] - Table eb_user_sign is going to be initial synced 
[INFO ] 2024-10-16 00:23:53.455 - [任务 4][MysqlCrmeb] - Query table 'eb_user_sign' counts: 1 
[INFO ] 2024-10-16 00:23:53.465 - [任务 4][MysqlCrmeb] - Table [eb_user_sign] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:53.466 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_cate 
[INFO ] 2024-10-16 00:23:53.468 - [任务 4][MysqlCrmeb] - Table eb_store_product_cate is going to be initial synced 
[INFO ] 2024-10-16 00:23:53.532 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_cate' counts: 0 
[INFO ] 2024-10-16 00:23:53.532 - [任务 4][MysqlCrmeb] - Table [eb_store_product_cate] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:53.534 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_experience_record 
[INFO ] 2024-10-16 00:23:53.534 - [任务 4][MysqlCrmeb] - Table eb_user_experience_record is going to be initial synced 
[INFO ] 2024-10-16 00:23:53.596 - [任务 4][MysqlCrmeb] - Query table 'eb_user_experience_record' counts: 1 
[INFO ] 2024-10-16 00:23:53.596 - [任务 4][MysqlCrmeb] - Table [eb_user_experience_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:53.597 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_brand_story 
[INFO ] 2024-10-16 00:23:53.599 - [任务 4][MysqlCrmeb] - Table eb_store_brand_story is going to be initial synced 
[INFO ] 2024-10-16 00:23:53.660 - [任务 4][MysqlCrmeb] - Query table 'eb_store_brand_story' counts: 1 
[INFO ] 2024-10-16 00:23:53.660 - [任务 4][MysqlCrmeb] - Table [eb_store_brand_story] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:53.661 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_heal 
[INFO ] 2024-10-16 00:23:53.661 - [任务 4][MysqlCrmeb] - Table eb_store_heal is going to be initial synced 
[INFO ] 2024-10-16 00:23:53.727 - [任务 4][MysqlCrmeb] - Query table 'eb_store_heal' counts: 5 
[INFO ] 2024-10-16 00:23:53.727 - [任务 4][MysqlCrmeb] - Table [eb_store_heal] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:53.728 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_description 
[INFO ] 2024-10-16 00:23:53.729 - [任务 4][MysqlCrmeb] - Table eb_store_product_description is going to be initial synced 
[INFO ] 2024-10-16 00:23:53.806 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_description' counts: 18 
[INFO ] 2024-10-16 00:23:53.807 - [任务 4][MysqlCrmeb] - Table [eb_store_product_description] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:53.808 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_reply 
[INFO ] 2024-10-16 00:23:53.808 - [任务 4][MysqlCrmeb] - Table eb_store_product_reply is going to be initial synced 
[INFO ] 2024-10-16 00:23:53.873 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_reply' counts: 1 
[INFO ] 2024-10-16 00:23:53.874 - [任务 4][MysqlCrmeb] - Table [eb_store_product_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:53.875 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_brokerage_record 
[INFO ] 2024-10-16 00:23:53.876 - [任务 4][MysqlCrmeb] - Table eb_user_brokerage_record is going to be initial synced 
[INFO ] 2024-10-16 00:23:53.939 - [任务 4][MysqlCrmeb] - Query table 'eb_user_brokerage_record' counts: 0 
[INFO ] 2024-10-16 00:23:53.941 - [任务 4][MysqlCrmeb] - Table [eb_user_brokerage_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:53.942 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_attachment 
[INFO ] 2024-10-16 00:23:53.942 - [任务 4][MysqlCrmeb] - Table eb_system_attachment is going to be initial synced 
[INFO ] 2024-10-16 00:23:54.143 - [任务 4][MysqlCrmeb] - Query table 'eb_system_attachment' counts: 288 
[WARN ] 2024-10-16 00:23:56.947 - [任务 4][Mysql23306] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[3903a72b-a592-4673-bf5f-e83e5b1219eb], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-10-16 00:23:57.020 - [任务 4][MysqlCrmeb] - Table [eb_system_attachment] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:57.022 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_store_staff 
[INFO ] 2024-10-16 00:23:57.022 - [任务 4][MysqlCrmeb] - Table eb_system_store_staff is going to be initial synced 
[INFO ] 2024-10-16 00:23:57.097 - [任务 4][MysqlCrmeb] - Query table 'eb_system_store_staff' counts: 0 
[INFO ] 2024-10-16 00:23:57.100 - [任务 4][MysqlCrmeb] - Table [eb_system_store_staff] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:57.100 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_wechat_reply 
[INFO ] 2024-10-16 00:23:57.100 - [任务 4][MysqlCrmeb] - Table eb_wechat_reply is going to be initial synced 
[INFO ] 2024-10-16 00:23:57.183 - [任务 4][MysqlCrmeb] - Table [eb_wechat_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:57.187 - [任务 4][MysqlCrmeb] - Query table 'eb_wechat_reply' counts: 1 
[INFO ] 2024-10-16 00:23:57.187 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_bargain_user_help 
[INFO ] 2024-10-16 00:23:57.244 - [任务 4][MysqlCrmeb] - Table eb_store_bargain_user_help is going to be initial synced 
[INFO ] 2024-10-16 00:23:57.245 - [任务 4][MysqlCrmeb] - Table [eb_store_bargain_user_help] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:57.259 - [任务 4][MysqlCrmeb] - Query table 'eb_store_bargain_user_help' counts: 0 
[INFO ] 2024-10-16 00:23:57.260 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_seckill 
[INFO ] 2024-10-16 00:23:57.260 - [任务 4][MysqlCrmeb] - Table eb_store_seckill is going to be initial synced 
[INFO ] 2024-10-16 00:23:57.323 - [任务 4][MysqlCrmeb] - Table [eb_store_seckill] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:57.323 - [任务 4][MysqlCrmeb] - Query table 'eb_store_seckill' counts: 4 
[INFO ] 2024-10-16 00:23:57.323 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_attr_result 
[INFO ] 2024-10-16 00:23:57.323 - [任务 4][MysqlCrmeb] - Table eb_store_product_attr_result is going to be initial synced 
[INFO ] 2024-10-16 00:23:57.385 - [任务 4][MysqlCrmeb] - Table [eb_store_product_attr_result] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:57.389 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_attr_result' counts: 0 
[INFO ] 2024-10-16 00:23:57.389 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_shipping_templates 
[INFO ] 2024-10-16 00:23:57.389 - [任务 4][MysqlCrmeb] - Table eb_shipping_templates is going to be initial synced 
[INFO ] 2024-10-16 00:23:57.446 - [任务 4][MysqlCrmeb] - Table [eb_shipping_templates] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:57.446 - [任务 4][MysqlCrmeb] - Query table 'eb_shipping_templates' counts: 1 
[INFO ] 2024-10-16 00:23:57.446 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_seckill_manger 
[INFO ] 2024-10-16 00:23:57.446 - [任务 4][MysqlCrmeb] - Table eb_store_seckill_manger is going to be initial synced 
[INFO ] 2024-10-16 00:23:57.510 - [任务 4][MysqlCrmeb] - Table [eb_store_seckill_manger] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:57.510 - [任务 4][MysqlCrmeb] - Query table 'eb_store_seckill_manger' counts: 3 
[INFO ] 2024-10-16 00:23:57.510 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user 
[INFO ] 2024-10-16 00:23:57.513 - [任务 4][MysqlCrmeb] - Table eb_user is going to be initial synced 
[INFO ] 2024-10-16 00:23:57.576 - [任务 4][MysqlCrmeb] - Table [eb_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:57.577 - [任务 4][MysqlCrmeb] - Query table 'eb_user' counts: 4 
[INFO ] 2024-10-16 00:23:57.582 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_order_status 
[INFO ] 2024-10-16 00:23:57.582 - [任务 4][MysqlCrmeb] - Table eb_store_order_status is going to be initial synced 
[INFO ] 2024-10-16 00:23:57.649 - [任务 4][MysqlCrmeb] - Table [eb_store_order_status] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:57.652 - [任务 4][MysqlCrmeb] - Query table 'eb_store_order_status' counts: 0 
[INFO ] 2024-10-16 00:23:57.654 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_form_temp 
[INFO ] 2024-10-16 00:23:57.654 - [任务 4][MysqlCrmeb] - Table eb_system_form_temp is going to be initial synced 
[INFO ] 2024-10-16 00:23:57.859 - [任务 4][MysqlCrmeb] - Query table 'eb_system_form_temp' counts: 60 
[INFO ] 2024-10-16 00:23:58.059 - [任务 4][MysqlCrmeb] - Table [eb_system_form_temp] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:58.059 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_reply 
[INFO ] 2024-10-16 00:23:58.060 - [任务 4][MysqlCrmeb] - Table eb_store_experience_reply is going to be initial synced 
[INFO ] 2024-10-16 00:23:58.126 - [任务 4][MysqlCrmeb] - Table [eb_store_experience_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:58.127 - [任务 4][MysqlCrmeb] - Query table 'eb_store_experience_reply' counts: 2 
[INFO ] 2024-10-16 00:23:58.127 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_bargain_user 
[INFO ] 2024-10-16 00:23:58.128 - [任务 4][MysqlCrmeb] - Table eb_store_bargain_user is going to be initial synced 
[INFO ] 2024-10-16 00:23:58.193 - [任务 4][MysqlCrmeb] - Table [eb_store_bargain_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:58.193 - [任务 4][MysqlCrmeb] - Query table 'eb_store_bargain_user' counts: 0 
[INFO ] 2024-10-16 00:23:58.194 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_store 
[INFO ] 2024-10-16 00:23:58.194 - [任务 4][MysqlCrmeb] - Table eb_system_store is going to be initial synced 
[INFO ] 2024-10-16 00:23:58.400 - [任务 4][MysqlCrmeb] - Table [eb_system_store] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:58.705 - [任务 4][MysqlCrmeb] - Query table 'eb_system_store' counts: 0 
[INFO ] 2024-10-16 00:23:58.706 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_shipping_templates_region 
[INFO ] 2024-10-16 00:23:58.707 - [任务 4][MysqlCrmeb] - Table eb_shipping_templates_region is going to be initial synced 
[INFO ] 2024-10-16 00:23:58.910 - [任务 4][MysqlCrmeb] - Table [eb_shipping_templates_region] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:59.426 - [任务 4][MysqlCrmeb] - Query table 'eb_shipping_templates_region' counts: 369 
[INFO ] 2024-10-16 00:23:59.429 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_recharge 
[INFO ] 2024-10-16 00:23:59.429 - [任务 4][MysqlCrmeb] - Table eb_user_recharge is going to be initial synced 
[INFO ] 2024-10-16 00:23:59.505 - [任务 4][MysqlCrmeb] - Table [eb_user_recharge] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:23:59.935 - [任务 4][MysqlCrmeb] - Query table 'eb_user_recharge' counts: 0 
[INFO ] 2024-10-16 00:23:59.941 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_coupon 
[INFO ] 2024-10-16 00:24:00.014 - [任务 4][MysqlCrmeb] - Table eb_store_product_coupon is going to be initial synced 
[INFO ] 2024-10-16 00:24:00.015 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_coupon' counts: 0 
[INFO ] 2024-10-16 00:24:00.015 - [任务 4][MysqlCrmeb] - Table [eb_store_product_coupon] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:00.015 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_wechat_pay_info 
[INFO ] 2024-10-16 00:24:00.016 - [任务 4][MysqlCrmeb] - Table eb_wechat_pay_info is going to be initial synced 
[INFO ] 2024-10-16 00:24:00.087 - [任务 4][MysqlCrmeb] - Table [eb_wechat_pay_info] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:00.087 - [任务 4][MysqlCrmeb] - Query table 'eb_wechat_pay_info' counts: 0 
[INFO ] 2024-10-16 00:24:00.087 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_combination 
[INFO ] 2024-10-16 00:24:00.088 - [任务 4][MysqlCrmeb] - Table eb_store_combination is going to be initial synced 
[INFO ] 2024-10-16 00:24:00.156 - [任务 4][MysqlCrmeb] - Query table 'eb_store_combination' counts: 4 
[INFO ] 2024-10-16 00:24:00.157 - [任务 4][MysqlCrmeb] - Table [eb_store_combination] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:00.157 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product 
[INFO ] 2024-10-16 00:24:00.228 - [任务 4][MysqlCrmeb] - Table eb_store_product is going to be initial synced 
[INFO ] 2024-10-16 00:24:00.229 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product' counts: 8 
[INFO ] 2024-10-16 00:24:00.233 - [任务 4][MysqlCrmeb] - Table [eb_store_product] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:00.234 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_article 
[INFO ] 2024-10-16 00:24:00.234 - [任务 4][MysqlCrmeb] - Table eb_article is going to be initial synced 
[INFO ] 2024-10-16 00:24:00.295 - [任务 4][MysqlCrmeb] - Table [eb_article] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:00.302 - [任务 4][MysqlCrmeb] - Query table 'eb_article' counts: 0 
[INFO ] 2024-10-16 00:24:00.302 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_bill 
[INFO ] 2024-10-16 00:24:00.367 - [任务 4][MysqlCrmeb] - Table eb_user_bill is going to be initial synced 
[INFO ] 2024-10-16 00:24:00.367 - [任务 4][MysqlCrmeb] - Table [eb_user_bill] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:00.375 - [任务 4][MysqlCrmeb] - Query table 'eb_user_bill' counts: 0 
[INFO ] 2024-10-16 00:24:00.375 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_relation 
[INFO ] 2024-10-16 00:24:00.444 - [任务 4][MysqlCrmeb] - Table eb_store_experience_relation is going to be initial synced 
[INFO ] 2024-10-16 00:24:00.444 - [任务 4][MysqlCrmeb] - Query table 'eb_store_experience_relation' counts: 2 
[INFO ] 2024-10-16 00:24:00.444 - [任务 4][MysqlCrmeb] - Table [eb_store_experience_relation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:00.445 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_wechat_exceptions 
[INFO ] 2024-10-16 00:24:00.520 - [任务 4][MysqlCrmeb] - Table eb_wechat_exceptions is going to be initial synced 
[INFO ] 2024-10-16 00:24:00.521 - [任务 4][MysqlCrmeb] - Query table 'eb_wechat_exceptions' counts: 31 
[INFO ] 2024-10-16 00:24:00.522 - [任务 4][MysqlCrmeb] - Table [eb_wechat_exceptions] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:00.522 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_template_message 
[INFO ] 2024-10-16 00:24:00.522 - [任务 4][MysqlCrmeb] - Table eb_template_message is going to be initial synced 
[INFO ] 2024-10-16 00:24:00.594 - [任务 4][MysqlCrmeb] - Table [eb_template_message] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:00.594 - [任务 4][MysqlCrmeb] - Query table 'eb_template_message' counts: 15 
[INFO ] 2024-10-16 00:24:00.597 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_integral_record 
[INFO ] 2024-10-16 00:24:00.597 - [任务 4][MysqlCrmeb] - Table eb_user_integral_record is going to be initial synced 
[INFO ] 2024-10-16 00:24:00.668 - [任务 4][MysqlCrmeb] - Table [eb_user_integral_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:00.669 - [任务 4][MysqlCrmeb] - Query table 'eb_user_integral_record' counts: 1 
[INFO ] 2024-10-16 00:24:00.670 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_coupon 
[INFO ] 2024-10-16 00:24:00.670 - [任务 4][MysqlCrmeb] - Table eb_store_coupon is going to be initial synced 
[INFO ] 2024-10-16 00:24:00.744 - [任务 4][MysqlCrmeb] - Table [eb_store_coupon] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:00.745 - [任务 4][MysqlCrmeb] - Query table 'eb_store_coupon' counts: 2 
[INFO ] 2024-10-16 00:24:00.746 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_visit_record 
[INFO ] 2024-10-16 00:24:00.749 - [任务 4][MysqlCrmeb] - Table eb_user_visit_record is going to be initial synced 
[INFO ] 2024-10-16 00:24:00.923 - [任务 4][MysqlCrmeb] - Query table 'eb_user_visit_record' counts: 1247 
[INFO ] 2024-10-16 00:24:00.924 - [任务 4][MysqlCrmeb] - Table [eb_user_visit_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:00.924 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_role_menu 
[INFO ] 2024-10-16 00:24:00.995 - [任务 4][MysqlCrmeb] - Table eb_system_role_menu is going to be initial synced 
[INFO ] 2024-10-16 00:24:00.995 - [任务 4][MysqlCrmeb] - Table [eb_system_role_menu] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:00.997 - [任务 4][MysqlCrmeb] - Query table 'eb_system_role_menu' counts: 595 
[INFO ] 2024-10-16 00:24:00.997 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_log 
[INFO ] 2024-10-16 00:24:01.055 - [任务 4][MysqlCrmeb] - Table eb_store_product_log is going to be initial synced 
[INFO ] 2024-10-16 00:24:01.055 - [任务 4][MysqlCrmeb] - Table [eb_store_product_log] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:01.063 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_log' counts: 0 
[INFO ] 2024-10-16 00:24:01.063 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_series 
[INFO ] 2024-10-16 00:24:01.124 - [任务 4][MysqlCrmeb] - Table eb_store_product_series is going to be initial synced 
[INFO ] 2024-10-16 00:24:01.125 - [任务 4][MysqlCrmeb] - Table [eb_store_product_series] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:01.127 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_series' counts: 5 
[INFO ] 2024-10-16 00:24:01.127 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_attr 
[INFO ] 2024-10-16 00:24:01.130 - [任务 4][MysqlCrmeb] - Table eb_store_product_attr is going to be initial synced 
[INFO ] 2024-10-16 00:24:01.196 - [任务 4][MysqlCrmeb] - Table [eb_store_product_attr] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:01.196 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_attr' counts: 24 
[INFO ] 2024-10-16 00:24:01.197 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_bargain 
[INFO ] 2024-10-16 00:24:01.198 - [任务 4][MysqlCrmeb] - Table eb_store_bargain is going to be initial synced 
[INFO ] 2024-10-16 00:24:01.263 - [任务 4][MysqlCrmeb] - Table [eb_store_bargain] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:01.264 - [任务 4][MysqlCrmeb] - Query table 'eb_store_bargain' counts: 2 
[INFO ] 2024-10-16 00:24:01.266 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_audio 
[INFO ] 2024-10-16 00:24:01.266 - [任务 4][MysqlCrmeb] - Table eb_store_experience_audio is going to be initial synced 
[INFO ] 2024-10-16 00:24:01.342 - [任务 4][MysqlCrmeb] - Table [eb_store_experience_audio] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:01.342 - [任务 4][MysqlCrmeb] - Query table 'eb_store_experience_audio' counts: 5 
[INFO ] 2024-10-16 00:24:01.343 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_city 
[INFO ] 2024-10-16 00:24:01.344 - [任务 4][MysqlCrmeb] - Table eb_system_city is going to be initial synced 
[INFO ] 2024-10-16 00:24:01.546 - [任务 4][MysqlCrmeb] - Query table 'eb_system_city' counts: 3938 
[INFO ] 2024-10-16 00:24:04.833 - [任务 4][MysqlCrmeb] - Table [eb_system_city] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:04.839 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_sms_record 
[INFO ] 2024-10-16 00:24:04.841 - [任务 4][MysqlCrmeb] - Table eb_sms_record is going to be initial synced 
[INFO ] 2024-10-16 00:24:04.907 - [任务 4][MysqlCrmeb] - Table [eb_sms_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:04.923 - [任务 4][MysqlCrmeb] - Query table 'eb_sms_record' counts: 0 
[INFO ] 2024-10-16 00:24:04.924 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_extract 
[INFO ] 2024-10-16 00:24:04.924 - [任务 4][MysqlCrmeb] - Table eb_user_extract is going to be initial synced 
[INFO ] 2024-10-16 00:24:05.127 - [任务 4][MysqlCrmeb] - Table [eb_user_extract] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:05.207 - [任务 4][MysqlCrmeb] - Query table 'eb_user_extract' counts: 0 
[INFO ] 2024-10-16 00:24:05.208 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_group 
[INFO ] 2024-10-16 00:24:05.208 - [任务 4][MysqlCrmeb] - Table eb_user_group is going to be initial synced 
[INFO ] 2024-10-16 00:24:05.414 - [任务 4][MysqlCrmeb] - Table [eb_user_group] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:05.728 - [任务 4][MysqlCrmeb] - Query table 'eb_user_group' counts: 3 
[INFO ] 2024-10-16 00:24:05.733 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_rule 
[INFO ] 2024-10-16 00:24:05.733 - [任务 4][MysqlCrmeb] - Table eb_store_product_rule is going to be initial synced 
[INFO ] 2024-10-16 00:24:05.794 - [任务 4][MysqlCrmeb] - Table [eb_store_product_rule] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:05.796 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_rule' counts: 2 
[INFO ] 2024-10-16 00:24:05.796 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_cart 
[INFO ] 2024-10-16 00:24:05.861 - [任务 4][MysqlCrmeb] - Table eb_store_cart is going to be initial synced 
[INFO ] 2024-10-16 00:24:05.862 - [任务 4][MysqlCrmeb] - Table [eb_store_cart] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:05.874 - [任务 4][MysqlCrmeb] - Query table 'eb_store_cart' counts: 3 
[INFO ] 2024-10-16 00:24:05.874 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_admin 
[INFO ] 2024-10-16 00:24:05.932 - [任务 4][MysqlCrmeb] - Table eb_system_admin is going to be initial synced 
[INFO ] 2024-10-16 00:24:05.933 - [任务 4][MysqlCrmeb] - Table [eb_system_admin] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:05.942 - [任务 4][MysqlCrmeb] - Query table 'eb_system_admin' counts: 2 
[INFO ] 2024-10-16 00:24:05.942 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_menu 
[INFO ] 2024-10-16 00:24:06.010 - [任务 4][MysqlCrmeb] - Table eb_system_menu is going to be initial synced 
[INFO ] 2024-10-16 00:24:06.011 - [任务 4][MysqlCrmeb] - Query table 'eb_system_menu' counts: 369 
[INFO ] 2024-10-16 00:24:06.074 - [任务 4][MysqlCrmeb] - Table [eb_system_menu] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:06.076 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_user_level 
[INFO ] 2024-10-16 00:24:06.076 - [任务 4][MysqlCrmeb] - Table eb_system_user_level is going to be initial synced 
[INFO ] 2024-10-16 00:24:06.121 - [任务 4][MysqlCrmeb] - Table [eb_system_user_level] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:06.128 - [任务 4][MysqlCrmeb] - Query table 'eb_system_user_level' counts: 5 
[INFO ] 2024-10-16 00:24:06.129 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_category 
[INFO ] 2024-10-16 00:24:06.198 - [任务 4][MysqlCrmeb] - Table eb_category is going to be initial synced 
[INFO ] 2024-10-16 00:24:06.199 - [任务 4][MysqlCrmeb] - Query table 'eb_category' counts: 295 
[INFO ] 2024-10-16 00:24:06.230 - [任务 4][MysqlCrmeb] - Table [eb_category] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:06.230 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_sms_template 
[INFO ] 2024-10-16 00:24:06.289 - [任务 4][MysqlCrmeb] - Table eb_sms_template is going to be initial synced 
[INFO ] 2024-10-16 00:24:06.289 - [任务 4][MysqlCrmeb] - Table [eb_sms_template] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:06.299 - [任务 4][MysqlCrmeb] - Query table 'eb_sms_template' counts: 7 
[INFO ] 2024-10-16 00:24:06.299 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_tag 
[INFO ] 2024-10-16 00:24:06.357 - [任务 4][MysqlCrmeb] - Table eb_user_tag is going to be initial synced 
[INFO ] 2024-10-16 00:24:06.357 - [任务 4][MysqlCrmeb] - Table [eb_user_tag] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:06.365 - [任务 4][MysqlCrmeb] - Query table 'eb_user_tag' counts: 5 
[INFO ] 2024-10-16 00:24:06.365 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_order_info 
[INFO ] 2024-10-16 00:24:06.425 - [任务 4][MysqlCrmeb] - Table eb_store_order_info is going to be initial synced 
[INFO ] 2024-10-16 00:24:06.425 - [任务 4][MysqlCrmeb] - Table [eb_store_order_info] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:06.430 - [任务 4][MysqlCrmeb] - Query table 'eb_store_order_info' counts: 0 
[INFO ] 2024-10-16 00:24:06.430 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_coupon_user 
[INFO ] 2024-10-16 00:24:06.491 - [任务 4][MysqlCrmeb] - Table eb_store_coupon_user is going to be initial synced 
[INFO ] 2024-10-16 00:24:06.491 - [任务 4][MysqlCrmeb] - Table [eb_store_coupon_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:06.502 - [任务 4][MysqlCrmeb] - Query table 'eb_store_coupon_user' counts: 0 
[INFO ] 2024-10-16 00:24:06.502 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_role 
[INFO ] 2024-10-16 00:24:06.564 - [任务 4][MysqlCrmeb] - Table eb_system_role is going to be initial synced 
[INFO ] 2024-10-16 00:24:06.564 - [任务 4][MysqlCrmeb] - Table [eb_system_role] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:06.569 - [任务 4][MysqlCrmeb] - Query table 'eb_system_role' counts: 2 
[INFO ] 2024-10-16 00:24:06.570 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_audio_history 
[INFO ] 2024-10-16 00:24:06.571 - [任务 4][MysqlCrmeb] - Table eb_store_experience_audio_history is going to be initial synced 
[INFO ] 2024-10-16 00:24:06.640 - [任务 4][MysqlCrmeb] - Table [eb_store_experience_audio_history] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:06.640 - [任务 4][MysqlCrmeb] - Query table 'eb_store_experience_audio_history' counts: 4 
[INFO ] 2024-10-16 00:24:06.641 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_pink 
[INFO ] 2024-10-16 00:24:06.641 - [任务 4][MysqlCrmeb] - Table eb_store_pink is going to be initial synced 
[INFO ] 2024-10-16 00:24:06.705 - [任务 4][MysqlCrmeb] - Table [eb_store_pink] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:06.707 - [任务 4][MysqlCrmeb] - Query table 'eb_store_pink' counts: 0 
[INFO ] 2024-10-16 00:24:06.707 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_express 
[INFO ] 2024-10-16 00:24:06.707 - [任务 4][MysqlCrmeb] - Table eb_express is going to be initial synced 
[INFO ] 2024-10-16 00:24:06.824 - [任务 4][MysqlCrmeb] - Query table 'eb_express' counts: 1067 
[INFO ] 2024-10-16 00:24:06.824 - [任务 4][MysqlCrmeb] - Table [eb_express] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:06.824 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_relation 
[INFO ] 2024-10-16 00:24:06.824 - [任务 4][MysqlCrmeb] - Table eb_store_product_relation is going to be initial synced 
[INFO ] 2024-10-16 00:24:06.907 - [任务 4][MysqlCrmeb] - Table [eb_store_product_relation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:06.908 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_relation' counts: 0 
[INFO ] 2024-10-16 00:24:06.908 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_group_data 
[INFO ] 2024-10-16 00:24:06.908 - [任务 4][MysqlCrmeb] - Table eb_system_group_data is going to be initial synced 
[INFO ] 2024-10-16 00:24:07.109 - [任务 4][MysqlCrmeb] - Query table 'eb_system_group_data' counts: 68 
[INFO ] 2024-10-16 00:24:08.175 - [任务 4][MysqlCrmeb] - Table [eb_system_group_data] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:08.181 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_address 
[INFO ] 2024-10-16 00:24:08.181 - [任务 4][MysqlCrmeb] - Table eb_user_address is going to be initial synced 
[INFO ] 2024-10-16 00:24:08.253 - [任务 4][MysqlCrmeb] - Table [eb_user_address] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:24:08.253 - [任务 4][MysqlCrmeb] - Query table 'eb_user_address' counts: 0 
[INFO ] 2024-10-16 00:24:08.457 - [任务 4][MysqlCrmeb] - Initial sync completed 
[INFO ] 2024-10-16 00:24:09.162 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] running status set to false 
[INFO ] 2024-10-16 00:24:09.162 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] running status set to false 
[INFO ] 2024-10-16 00:24:09.218 - [任务 4][MysqlCrmeb] - PDK connector node stopped: HazelcastSourcePdkDataNode-3903a72b-a592-4673-bf5f-e83e5b1219eb 
[INFO ] 2024-10-16 00:24:09.218 - [任务 4][Mysql23306] - PDK connector node stopped: HazelcastTargetPdkDataNode-eeb92868-7cb8-47f7-b7c0-193204274836 
[INFO ] 2024-10-16 00:24:09.218 - [任务 4][MysqlCrmeb] - PDK connector node released: HazelcastSourcePdkDataNode-3903a72b-a592-4673-bf5f-e83e5b1219eb 
[INFO ] 2024-10-16 00:24:09.220 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] schema data cleaned 
[INFO ] 2024-10-16 00:24:09.220 - [任务 4][Mysql23306] - PDK connector node released: HazelcastTargetPdkDataNode-eeb92868-7cb8-47f7-b7c0-193204274836 
[INFO ] 2024-10-16 00:24:09.220 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] monitor closed 
[INFO ] 2024-10-16 00:24:09.220 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] schema data cleaned 
[INFO ] 2024-10-16 00:24:09.229 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] monitor closed 
[INFO ] 2024-10-16 00:24:09.229 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] close complete, cost 66 ms 
[INFO ] 2024-10-16 00:24:09.229 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] close complete, cost 65 ms 
[INFO ] 2024-10-16 00:24:11.763 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-16 00:24:11.779 - [任务 4] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@350f5ec7 
[INFO ] 2024-10-16 00:24:11.779 - [任务 4] - Stop task milestones: 670e9533f12cdc2b66d43fa6(任务 4)  
[INFO ] 2024-10-16 00:24:12.017 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-10-16 00:24:12.018 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-10-16 00:24:12.070 - [任务 4] - Remove memory task client succeed, task: 任务 4[670e9533f12cdc2b66d43fa6] 
[INFO ] 2024-10-16 00:24:12.070 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[670e9533f12cdc2b66d43fa6] 
[INFO ] 2024-10-16 01:04:25.449 - [任务 4] - Task initialization... 
[INFO ] 2024-10-16 01:04:25.450 - [任务 4] - Start task milestones: 670e9533f12cdc2b66d43fa6(任务 4) 
[INFO ] 2024-10-16 01:04:27.972 - [任务 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-16 01:04:28.179 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-16 01:04:28.538 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] start preload schema,table counts: 73 
[INFO ] 2024-10-16 01:04:28.539 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] start preload schema,table counts: 73 
[INFO ] 2024-10-16 01:04:28.540 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] preload schema finished, cost 1 ms 
[INFO ] 2024-10-16 01:04:28.541 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] preload schema finished, cost 0 ms 
[INFO ] 2024-10-16 01:04:29.632 - [任务 4][Mysql23306] - Node(Mysql23306) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-16 01:04:29.637 - [任务 4][Mysql23306] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-16 01:04:29.740 - [任务 4][Mysql23306] - Table "dragon2.eb_store_order" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:29.743 - [任务 4][Mysql23306] - The table eb_store_order has already exist. 
[INFO ] 2024-10-16 01:04:29.915 - [任务 4][Mysql23306] - Table "dragon2.eb_store_product_attr_value" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:29.916 - [任务 4][Mysql23306] - The table eb_store_product_attr_value has already exist. 
[INFO ] 2024-10-16 01:04:29.976 - [任务 4][Mysql23306] - Table "dragon2.eb_wechat_callback" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:29.977 - [任务 4][Mysql23306] - The table eb_wechat_callback has already exist. 
[INFO ] 2024-10-16 01:04:30.042 - [任务 4][Mysql23306] - Table "dragon2.eb_shipping_templates_free" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:30.043 - [任务 4][Mysql23306] - The table eb_shipping_templates_free has already exist. 
[INFO ] 2024-10-16 01:04:30.099 - [任务 4][Mysql23306] - Table "dragon2.eb_system_notification" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:30.099 - [任务 4][Mysql23306] - The table eb_system_notification has already exist. 
[INFO ] 2024-10-16 01:04:30.175 - [任务 4][Mysql23306] - Table "dragon2.eb_store_heal_user" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:30.175 - [任务 4][Mysql23306] - The table eb_store_heal_user has already exist. 
[INFO ] 2024-10-16 01:04:30.241 - [任务 4][Mysql23306] - Table "dragon2.eb_user_level" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:30.241 - [任务 4][Mysql23306] - The table eb_user_level has already exist. 
[INFO ] 2024-10-16 01:04:30.297 - [任务 4][Mysql23306] - Table "dragon2.eb_user_token" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:30.297 - [任务 4][Mysql23306] - The table eb_user_token has already exist. 
[INFO ] 2024-10-16 01:04:30.388 - [任务 4][Mysql23306] - Table "dragon2.eb_system_group" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:30.389 - [任务 4][Mysql23306] - The table eb_system_group has already exist. 
[INFO ] 2024-10-16 01:04:30.484 - [任务 4][MysqlCrmeb] - Source node "MysqlCrmeb" read batch size: 100 
[INFO ] 2024-10-16 01:04:30.485 - [任务 4][MysqlCrmeb] - Source node "MysqlCrmeb" event queue capacity: 200 
[INFO ] 2024-10-16 01:04:30.486 - [任务 4][MysqlCrmeb] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-16 01:04:30.486 - [任务 4][MysqlCrmeb] - batch offset found: {},stream offset not found. 
[INFO ] 2024-10-16 01:04:30.554 - [任务 4][MysqlCrmeb] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-16 01:04:30.555 - [任务 4][Mysql23306] - Table "dragon2.eb_system_config" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:30.555 - [任务 4][Mysql23306] - The table eb_system_config has already exist. 
[INFO ] 2024-10-16 01:04:30.614 - [任务 4][Mysql23306] - Table "dragon2.eb_user_sign" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:30.614 - [任务 4][Mysql23306] - The table eb_user_sign has already exist. 
[INFO ] 2024-10-16 01:04:30.692 - [任务 4][Mysql23306] - Table "dragon2.eb_store_product_cate" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:30.693 - [任务 4][Mysql23306] - The table eb_store_product_cate has already exist. 
[INFO ] 2024-10-16 01:04:30.775 - [任务 4][MysqlCrmeb] - Initial sync started 
[INFO ] 2024-10-16 01:04:30.778 - [任务 4][Mysql23306] - Table "dragon2.eb_user_experience_record" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:30.778 - [任务 4][Mysql23306] - The table eb_user_experience_record has already exist. 
[INFO ] 2024-10-16 01:04:30.778 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_order 
[INFO ] 2024-10-16 01:04:30.890 - [任务 4][MysqlCrmeb] - Table eb_store_order is going to be initial synced 
[INFO ] 2024-10-16 01:04:30.892 - [任务 4][Mysql23306] - Table "dragon2.eb_store_brand_story" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:30.892 - [任务 4][Mysql23306] - The table eb_store_brand_story has already exist. 
[INFO ] 2024-10-16 01:04:30.951 - [任务 4][Mysql23306] - Table "dragon2.eb_store_heal" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:30.951 - [任务 4][Mysql23306] - The table eb_store_heal has already exist. 
[INFO ] 2024-10-16 01:04:31.021 - [任务 4][Mysql23306] - Table "dragon2.eb_store_product_description" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:31.021 - [任务 4][Mysql23306] - The table eb_store_product_description has already exist. 
[INFO ] 2024-10-16 01:04:31.080 - [任务 4][Mysql23306] - Table "dragon2.eb_store_product_reply" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:31.083 - [任务 4][Mysql23306] - The table eb_store_product_reply has already exist. 
[INFO ] 2024-10-16 01:04:31.145 - [任务 4][MysqlCrmeb] - Query table 'eb_store_order' counts: 0 
[INFO ] 2024-10-16 01:04:31.146 - [任务 4][Mysql23306] - Table "dragon2.eb_user_brokerage_record" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:31.146 - [任务 4][Mysql23306] - The table eb_user_brokerage_record has already exist. 
[INFO ] 2024-10-16 01:04:31.208 - [任务 4][Mysql23306] - Table "dragon2.eb_system_attachment" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:31.265 - [任务 4][Mysql23306] - The table eb_system_attachment has already exist. 
[INFO ] 2024-10-16 01:04:31.265 - [任务 4][Mysql23306] - Table "dragon2.eb_system_store_staff" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:31.265 - [任务 4][Mysql23306] - The table eb_system_store_staff has already exist. 
[INFO ] 2024-10-16 01:04:31.318 - [任务 4][Mysql23306] - Table "dragon2.eb_wechat_reply" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:31.321 - [任务 4][Mysql23306] - The table eb_wechat_reply has already exist. 
[INFO ] 2024-10-16 01:04:31.395 - [任务 4][Mysql23306] - Table "dragon2.eb_store_bargain_user_help" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:31.395 - [任务 4][Mysql23306] - The table eb_store_bargain_user_help has already exist. 
[INFO ] 2024-10-16 01:04:31.448 - [任务 4][Mysql23306] - Table "dragon2.eb_store_seckill" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:31.449 - [任务 4][Mysql23306] - The table eb_store_seckill has already exist. 
[INFO ] 2024-10-16 01:04:31.515 - [任务 4][Mysql23306] - Table "dragon2.eb_store_product_attr_result" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:31.515 - [任务 4][Mysql23306] - The table eb_store_product_attr_result has already exist. 
[INFO ] 2024-10-16 01:04:31.580 - [任务 4][Mysql23306] - Table "dragon2.eb_shipping_templates" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:31.580 - [任务 4][Mysql23306] - The table eb_shipping_templates has already exist. 
[INFO ] 2024-10-16 01:04:31.629 - [任务 4][Mysql23306] - Table "dragon2.eb_store_seckill_manger" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:31.629 - [任务 4][Mysql23306] - The table eb_store_seckill_manger has already exist. 
[INFO ] 2024-10-16 01:04:31.690 - [任务 4][Mysql23306] - Table "dragon2.eb_user" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:31.691 - [任务 4][Mysql23306] - The table eb_user has already exist. 
[INFO ] 2024-10-16 01:04:31.761 - [任务 4][Mysql23306] - Table "dragon2.eb_store_order_status" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:31.761 - [任务 4][Mysql23306] - The table eb_store_order_status has already exist. 
[INFO ] 2024-10-16 01:04:31.813 - [任务 4][Mysql23306] - Table "dragon2.eb_system_form_temp" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:31.813 - [任务 4][Mysql23306] - The table eb_system_form_temp has already exist. 
[INFO ] 2024-10-16 01:04:31.873 - [任务 4][Mysql23306] - Table "dragon2.eb_store_experience_reply" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:31.875 - [任务 4][Mysql23306] - The table eb_store_experience_reply has already exist. 
[INFO ] 2024-10-16 01:04:31.929 - [任务 4][Mysql23306] - Table "dragon2.eb_store_bargain_user" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:31.929 - [任务 4][Mysql23306] - The table eb_store_bargain_user has already exist. 
[INFO ] 2024-10-16 01:04:31.986 - [任务 4][Mysql23306] - Table "dragon2.eb_system_store" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:32.047 - [任务 4][Mysql23306] - The table eb_system_store has already exist. 
[INFO ] 2024-10-16 01:04:32.049 - [任务 4][Mysql23306] - Table "dragon2.eb_shipping_templates_region" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:32.050 - [任务 4][Mysql23306] - The table eb_shipping_templates_region has already exist. 
[INFO ] 2024-10-16 01:04:32.102 - [任务 4][Mysql23306] - Table "dragon2.eb_user_recharge" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:32.105 - [任务 4][Mysql23306] - The table eb_user_recharge has already exist. 
[INFO ] 2024-10-16 01:04:32.157 - [任务 4][Mysql23306] - Table "dragon2.eb_store_product_coupon" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:32.157 - [任务 4][Mysql23306] - The table eb_store_product_coupon has already exist. 
[INFO ] 2024-10-16 01:04:32.223 - [任务 4][Mysql23306] - Table "dragon2.eb_wechat_pay_info" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:32.224 - [任务 4][Mysql23306] - The table eb_wechat_pay_info has already exist. 
[INFO ] 2024-10-16 01:04:32.294 - [任务 4][Mysql23306] - Table "dragon2.eb_store_combination" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:32.297 - [任务 4][Mysql23306] - The table eb_store_combination has already exist. 
[INFO ] 2024-10-16 01:04:32.368 - [任务 4][Mysql23306] - Table "dragon2.eb_store_product" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:32.368 - [任务 4][Mysql23306] - The table eb_store_product has already exist. 
[INFO ] 2024-10-16 01:04:32.459 - [任务 4][Mysql23306] - Table "dragon2.eb_article" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:32.459 - [任务 4][Mysql23306] - The table eb_article has already exist. 
[INFO ] 2024-10-16 01:04:32.516 - [任务 4][Mysql23306] - Table "dragon2.eb_user_bill" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:32.516 - [任务 4][Mysql23306] - The table eb_user_bill has already exist. 
[INFO ] 2024-10-16 01:04:32.585 - [任务 4][Mysql23306] - Table "dragon2.eb_store_experience_relation" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:32.586 - [任务 4][Mysql23306] - The table eb_store_experience_relation has already exist. 
[INFO ] 2024-10-16 01:04:32.638 - [任务 4][Mysql23306] - Table "dragon2.eb_wechat_exceptions" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:32.639 - [任务 4][Mysql23306] - The table eb_wechat_exceptions has already exist. 
[INFO ] 2024-10-16 01:04:32.697 - [任务 4][Mysql23306] - Table "dragon2.eb_template_message" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:32.697 - [任务 4][Mysql23306] - The table eb_template_message has already exist. 
[INFO ] 2024-10-16 01:04:32.749 - [任务 4][Mysql23306] - Table "dragon2.eb_user_integral_record" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:32.749 - [任务 4][Mysql23306] - The table eb_user_integral_record has already exist. 
[INFO ] 2024-10-16 01:04:32.805 - [任务 4][Mysql23306] - Table "dragon2.eb_store_coupon" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:32.805 - [任务 4][Mysql23306] - The table eb_store_coupon has already exist. 
[INFO ] 2024-10-16 01:04:32.857 - [任务 4][Mysql23306] - Table "dragon2.eb_user_visit_record" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:32.857 - [任务 4][Mysql23306] - The table eb_user_visit_record has already exist. 
[INFO ] 2024-10-16 01:04:32.902 - [任务 4][Mysql23306] - Table "dragon2.eb_system_role_menu" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:32.903 - [任务 4][Mysql23306] - The table eb_system_role_menu has already exist. 
[INFO ] 2024-10-16 01:04:32.951 - [任务 4][Mysql23306] - Table "dragon2.eb_store_product_log" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:32.951 - [任务 4][Mysql23306] - The table eb_store_product_log has already exist. 
[INFO ] 2024-10-16 01:04:33.007 - [任务 4][Mysql23306] - Table "dragon2.eb_store_product_series" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.008 - [任务 4][Mysql23306] - The table eb_store_product_series has already exist. 
[INFO ] 2024-10-16 01:04:33.046 - [任务 4][Mysql23306] - Table "dragon2.eb_store_product_attr" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.096 - [任务 4][Mysql23306] - The table eb_store_product_attr has already exist. 
[INFO ] 2024-10-16 01:04:33.096 - [任务 4][Mysql23306] - Table "dragon2.eb_store_bargain" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.097 - [任务 4][Mysql23306] - The table eb_store_bargain has already exist. 
[INFO ] 2024-10-16 01:04:33.152 - [任务 4][Mysql23306] - Table "dragon2.eb_store_experience_audio" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.209 - [任务 4][Mysql23306] - The table eb_store_experience_audio has already exist. 
[INFO ] 2024-10-16 01:04:33.209 - [任务 4][Mysql23306] - Table "dragon2.eb_system_city" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.264 - [任务 4][Mysql23306] - The table eb_system_city has already exist. 
[INFO ] 2024-10-16 01:04:33.264 - [任务 4][Mysql23306] - Table "dragon2.eb_sms_record" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.314 - [任务 4][Mysql23306] - The table eb_sms_record has already exist. 
[INFO ] 2024-10-16 01:04:33.314 - [任务 4][Mysql23306] - Table "dragon2.eb_user_extract" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.374 - [任务 4][Mysql23306] - The table eb_user_extract has already exist. 
[INFO ] 2024-10-16 01:04:33.375 - [任务 4][Mysql23306] - Table "dragon2.eb_user_group" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.375 - [任务 4][Mysql23306] - The table eb_user_group has already exist. 
[INFO ] 2024-10-16 01:04:33.471 - [任务 4][Mysql23306] - Table "dragon2.eb_store_product_rule" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.471 - [任务 4][Mysql23306] - The table eb_store_product_rule has already exist. 
[INFO ] 2024-10-16 01:04:33.514 - [任务 4][Mysql23306] - Table "dragon2.eb_store_cart" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.514 - [任务 4][Mysql23306] - The table eb_store_cart has already exist. 
[INFO ] 2024-10-16 01:04:33.567 - [任务 4][Mysql23306] - Table "dragon2.eb_system_admin" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.567 - [任务 4][Mysql23306] - The table eb_system_admin has already exist. 
[INFO ] 2024-10-16 01:04:33.620 - [任务 4][Mysql23306] - Table "dragon2.eb_system_menu" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.620 - [任务 4][Mysql23306] - The table eb_system_menu has already exist. 
[INFO ] 2024-10-16 01:04:33.676 - [任务 4][Mysql23306] - Table "dragon2.eb_system_user_level" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.676 - [任务 4][Mysql23306] - The table eb_system_user_level has already exist. 
[INFO ] 2024-10-16 01:04:33.725 - [任务 4][Mysql23306] - Table "dragon2.eb_category" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.725 - [任务 4][Mysql23306] - The table eb_category has already exist. 
[INFO ] 2024-10-16 01:04:33.772 - [任务 4][Mysql23306] - Table "dragon2.eb_sms_template" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.772 - [任务 4][Mysql23306] - The table eb_sms_template has already exist. 
[INFO ] 2024-10-16 01:04:33.821 - [任务 4][Mysql23306] - Table "dragon2.eb_user_tag" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.821 - [任务 4][Mysql23306] - The table eb_user_tag has already exist. 
[INFO ] 2024-10-16 01:04:33.885 - [任务 4][Mysql23306] - Table "dragon2.eb_store_order_info" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.886 - [任务 4][Mysql23306] - The table eb_store_order_info has already exist. 
[INFO ] 2024-10-16 01:04:33.950 - [任务 4][Mysql23306] - Table "dragon2.eb_store_coupon_user" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:33.950 - [任务 4][Mysql23306] - The table eb_store_coupon_user has already exist. 
[INFO ] 2024-10-16 01:04:34.016 - [任务 4][Mysql23306] - Table "dragon2.eb_system_role" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:34.017 - [任务 4][Mysql23306] - The table eb_system_role has already exist. 
[INFO ] 2024-10-16 01:04:34.087 - [任务 4][Mysql23306] - Table "dragon2.eb_store_experience_audio_history" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:34.093 - [任务 4][Mysql23306] - The table eb_store_experience_audio_history has already exist. 
[INFO ] 2024-10-16 01:04:34.119 - [任务 4][MysqlCrmeb] - Table [eb_store_order] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:34.124 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_attr_value 
[INFO ] 2024-10-16 01:04:34.125 - [任务 4][MysqlCrmeb] - Table eb_store_product_attr_value is going to be initial synced 
[INFO ] 2024-10-16 01:04:34.156 - [任务 4][Mysql23306] - Table "dragon2.eb_store_pink" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:34.156 - [任务 4][Mysql23306] - The table eb_store_pink has already exist. 
[INFO ] 2024-10-16 01:04:34.211 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_attr_value' counts: 38 
[INFO ] 2024-10-16 01:04:34.211 - [任务 4][Mysql23306] - Table "dragon2.eb_express" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:34.211 - [任务 4][Mysql23306] - The table eb_express has already exist. 
[INFO ] 2024-10-16 01:04:34.212 - [任务 4][MysqlCrmeb] - Table [eb_store_product_attr_value] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:34.212 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_wechat_callback 
[INFO ] 2024-10-16 01:04:34.212 - [任务 4][MysqlCrmeb] - Table eb_wechat_callback is going to be initial synced 
[INFO ] 2024-10-16 01:04:34.259 - [任务 4][Mysql23306] - Table "dragon2.eb_store_product_relation" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:34.260 - [任务 4][Mysql23306] - The table eb_store_product_relation has already exist. 
[INFO ] 2024-10-16 01:04:34.271 - [任务 4][MysqlCrmeb] - Table [eb_wechat_callback] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:34.271 - [任务 4][MysqlCrmeb] - Query table 'eb_wechat_callback' counts: 0 
[INFO ] 2024-10-16 01:04:34.272 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_shipping_templates_free 
[INFO ] 2024-10-16 01:04:34.272 - [任务 4][MysqlCrmeb] - Table eb_shipping_templates_free is going to be initial synced 
[INFO ] 2024-10-16 01:04:34.313 - [任务 4][Mysql23306] - Table "dragon2.eb_system_group_data" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:34.314 - [任务 4][Mysql23306] - The table eb_system_group_data has already exist. 
[INFO ] 2024-10-16 01:04:34.326 - [任务 4][MysqlCrmeb] - Query table 'eb_shipping_templates_free' counts: 32 
[INFO ] 2024-10-16 01:04:34.349 - [任务 4][MysqlCrmeb] - Table [eb_shipping_templates_free] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:34.349 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_notification 
[INFO ] 2024-10-16 01:04:34.369 - [任务 4][MysqlCrmeb] - Table eb_system_notification is going to be initial synced 
[INFO ] 2024-10-16 01:04:34.369 - [任务 4][Mysql23306] - Table "dragon2.eb_user_address" exists, skip auto create table 
[INFO ] 2024-10-16 01:04:34.369 - [任务 4][Mysql23306] - The table eb_user_address has already exist. 
[INFO ] 2024-10-16 01:04:34.409 - [任务 4][MysqlCrmeb] - Query table 'eb_system_notification' counts: 12 
[INFO ] 2024-10-16 01:04:34.410 - [任务 4][MysqlCrmeb] - Table [eb_system_notification] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:34.410 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_heal_user 
[INFO ] 2024-10-16 01:04:34.466 - [任务 4][MysqlCrmeb] - Table eb_store_heal_user is going to be initial synced 
[INFO ] 2024-10-16 01:04:34.466 - [任务 4][MysqlCrmeb] - Query table 'eb_store_heal_user' counts: 9 
[INFO ] 2024-10-16 01:04:34.466 - [任务 4][MysqlCrmeb] - Table [eb_store_heal_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:34.467 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_level 
[INFO ] 2024-10-16 01:04:34.526 - [任务 4][MysqlCrmeb] - Table eb_user_level is going to be initial synced 
[INFO ] 2024-10-16 01:04:34.526 - [任务 4][MysqlCrmeb] - Table [eb_user_level] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:34.535 - [任务 4][MysqlCrmeb] - Query table 'eb_user_level' counts: 1 
[INFO ] 2024-10-16 01:04:34.535 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_token 
[INFO ] 2024-10-16 01:04:34.595 - [任务 4][MysqlCrmeb] - Table eb_user_token is going to be initial synced 
[INFO ] 2024-10-16 01:04:34.596 - [任务 4][MysqlCrmeb] - Query table 'eb_user_token' counts: 3 
[INFO ] 2024-10-16 01:04:34.596 - [任务 4][MysqlCrmeb] - Table [eb_user_token] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:34.596 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_group 
[INFO ] 2024-10-16 01:04:34.597 - [任务 4][MysqlCrmeb] - Table eb_system_group is going to be initial synced 
[INFO ] 2024-10-16 01:04:34.657 - [任务 4][MysqlCrmeb] - Query table 'eb_system_group' counts: 18 
[INFO ] 2024-10-16 01:04:34.657 - [任务 4][MysqlCrmeb] - Table [eb_system_group] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:34.657 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_config 
[INFO ] 2024-10-16 01:04:34.657 - [任务 4][MysqlCrmeb] - Table eb_system_config is going to be initial synced 
[WARN ] 2024-10-16 01:04:34.678 - [任务 4][Mysql23306] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[3903a72b-a592-4673-bf5f-e83e5b1219eb], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-10-16 01:04:34.749 - [任务 4][MysqlCrmeb] - Query table 'eb_system_config' counts: 177 
[INFO ] 2024-10-16 01:04:34.749 - [任务 4][MysqlCrmeb] - Table [eb_system_config] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:34.750 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_sign 
[INFO ] 2024-10-16 01:04:34.809 - [任务 4][MysqlCrmeb] - Table eb_user_sign is going to be initial synced 
[INFO ] 2024-10-16 01:04:34.809 - [任务 4][MysqlCrmeb] - Query table 'eb_user_sign' counts: 1 
[INFO ] 2024-10-16 01:04:34.821 - [任务 4][MysqlCrmeb] - Table [eb_user_sign] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:34.830 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_cate 
[INFO ] 2024-10-16 01:04:34.831 - [任务 4][MysqlCrmeb] - Table eb_store_product_cate is going to be initial synced 
[INFO ] 2024-10-16 01:04:34.883 - [任务 4][MysqlCrmeb] - Table [eb_store_product_cate] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:34.883 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_cate' counts: 0 
[INFO ] 2024-10-16 01:04:34.884 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_experience_record 
[INFO ] 2024-10-16 01:04:34.884 - [任务 4][MysqlCrmeb] - Table eb_user_experience_record is going to be initial synced 
[INFO ] 2024-10-16 01:04:34.942 - [任务 4][MysqlCrmeb] - Query table 'eb_user_experience_record' counts: 1 
[INFO ] 2024-10-16 01:04:34.942 - [任务 4][MysqlCrmeb] - Table [eb_user_experience_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:34.943 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_brand_story 
[INFO ] 2024-10-16 01:04:34.943 - [任务 4][MysqlCrmeb] - Table eb_store_brand_story is going to be initial synced 
[INFO ] 2024-10-16 01:04:34.999 - [任务 4][MysqlCrmeb] - Query table 'eb_store_brand_story' counts: 1 
[INFO ] 2024-10-16 01:04:34.999 - [任务 4][MysqlCrmeb] - Table [eb_store_brand_story] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:35.000 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_heal 
[INFO ] 2024-10-16 01:04:35.000 - [任务 4][MysqlCrmeb] - Table eb_store_heal is going to be initial synced 
[INFO ] 2024-10-16 01:04:35.064 - [任务 4][MysqlCrmeb] - Query table 'eb_store_heal' counts: 5 
[INFO ] 2024-10-16 01:04:35.064 - [任务 4][MysqlCrmeb] - Table [eb_store_heal] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:35.065 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_description 
[INFO ] 2024-10-16 01:04:35.065 - [任务 4][MysqlCrmeb] - Table eb_store_product_description is going to be initial synced 
[INFO ] 2024-10-16 01:04:35.128 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_description' counts: 18 
[INFO ] 2024-10-16 01:04:35.129 - [任务 4][MysqlCrmeb] - Table [eb_store_product_description] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:35.133 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_reply 
[INFO ] 2024-10-16 01:04:35.133 - [任务 4][MysqlCrmeb] - Table eb_store_product_reply is going to be initial synced 
[INFO ] 2024-10-16 01:04:35.189 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_reply' counts: 1 
[INFO ] 2024-10-16 01:04:35.189 - [任务 4][MysqlCrmeb] - Table [eb_store_product_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:35.190 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_brokerage_record 
[INFO ] 2024-10-16 01:04:35.190 - [任务 4][MysqlCrmeb] - Table eb_user_brokerage_record is going to be initial synced 
[INFO ] 2024-10-16 01:04:35.249 - [任务 4][MysqlCrmeb] - Query table 'eb_user_brokerage_record' counts: 0 
[INFO ] 2024-10-16 01:04:35.249 - [任务 4][MysqlCrmeb] - Table [eb_user_brokerage_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:35.250 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_attachment 
[INFO ] 2024-10-16 01:04:35.250 - [任务 4][MysqlCrmeb] - Table eb_system_attachment is going to be initial synced 
[INFO ] 2024-10-16 01:04:35.452 - [任务 4][MysqlCrmeb] - Query table 'eb_system_attachment' counts: 288 
[INFO ] 2024-10-16 01:04:36.228 - [任务 4][MysqlCrmeb] - Table [eb_system_attachment] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:36.232 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_store_staff 
[INFO ] 2024-10-16 01:04:36.302 - [任务 4][MysqlCrmeb] - Table eb_system_store_staff is going to be initial synced 
[INFO ] 2024-10-16 01:04:36.303 - [任务 4][MysqlCrmeb] - Table [eb_system_store_staff] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:36.405 - [任务 4][MysqlCrmeb] - Query table 'eb_system_store_staff' counts: 0 
[INFO ] 2024-10-16 01:04:36.406 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_wechat_reply 
[INFO ] 2024-10-16 01:04:36.463 - [任务 4][MysqlCrmeb] - Table eb_wechat_reply is going to be initial synced 
[INFO ] 2024-10-16 01:04:36.463 - [任务 4][MysqlCrmeb] - Query table 'eb_wechat_reply' counts: 1 
[INFO ] 2024-10-16 01:04:36.466 - [任务 4][MysqlCrmeb] - Table [eb_wechat_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:36.466 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_bargain_user_help 
[INFO ] 2024-10-16 01:04:36.521 - [任务 4][MysqlCrmeb] - Table eb_store_bargain_user_help is going to be initial synced 
[INFO ] 2024-10-16 01:04:36.521 - [任务 4][MysqlCrmeb] - Table [eb_store_bargain_user_help] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:37.008 - [任务 4][MysqlCrmeb] - Query table 'eb_store_bargain_user_help' counts: 0 
[INFO ] 2024-10-16 01:04:37.008 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_seckill 
[INFO ] 2024-10-16 01:04:37.069 - [任务 4][MysqlCrmeb] - Table eb_store_seckill is going to be initial synced 
[INFO ] 2024-10-16 01:04:37.069 - [任务 4][MysqlCrmeb] - Query table 'eb_store_seckill' counts: 4 
[INFO ] 2024-10-16 01:04:37.086 - [任务 4][MysqlCrmeb] - Table [eb_store_seckill] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:37.086 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_attr_result 
[INFO ] 2024-10-16 01:04:37.144 - [任务 4][MysqlCrmeb] - Table eb_store_product_attr_result is going to be initial synced 
[INFO ] 2024-10-16 01:04:37.146 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_attr_result' counts: 0 
[INFO ] 2024-10-16 01:04:37.146 - [任务 4][MysqlCrmeb] - Table [eb_store_product_attr_result] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:37.146 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_shipping_templates 
[INFO ] 2024-10-16 01:04:37.147 - [任务 4][MysqlCrmeb] - Table eb_shipping_templates is going to be initial synced 
[INFO ] 2024-10-16 01:04:37.206 - [任务 4][MysqlCrmeb] - Query table 'eb_shipping_templates' counts: 1 
[INFO ] 2024-10-16 01:04:37.206 - [任务 4][MysqlCrmeb] - Table [eb_shipping_templates] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:37.206 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_seckill_manger 
[INFO ] 2024-10-16 01:04:37.206 - [任务 4][MysqlCrmeb] - Table eb_store_seckill_manger is going to be initial synced 
[INFO ] 2024-10-16 01:04:37.264 - [任务 4][MysqlCrmeb] - Query table 'eb_store_seckill_manger' counts: 3 
[INFO ] 2024-10-16 01:04:37.264 - [任务 4][MysqlCrmeb] - Table [eb_store_seckill_manger] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:37.264 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user 
[INFO ] 2024-10-16 01:04:37.264 - [任务 4][MysqlCrmeb] - Table eb_user is going to be initial synced 
[INFO ] 2024-10-16 01:04:37.324 - [任务 4][MysqlCrmeb] - Query table 'eb_user' counts: 4 
[INFO ] 2024-10-16 01:04:37.324 - [任务 4][MysqlCrmeb] - Table [eb_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:37.326 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_order_status 
[INFO ] 2024-10-16 01:04:37.326 - [任务 4][MysqlCrmeb] - Table eb_store_order_status is going to be initial synced 
[INFO ] 2024-10-16 01:04:37.388 - [任务 4][MysqlCrmeb] - Table [eb_store_order_status] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:37.388 - [任务 4][MysqlCrmeb] - Query table 'eb_store_order_status' counts: 0 
[INFO ] 2024-10-16 01:04:37.388 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_form_temp 
[INFO ] 2024-10-16 01:04:37.389 - [任务 4][MysqlCrmeb] - Table eb_system_form_temp is going to be initial synced 
[INFO ] 2024-10-16 01:04:37.594 - [任务 4][MysqlCrmeb] - Query table 'eb_system_form_temp' counts: 60 
[INFO ] 2024-10-16 01:04:37.651 - [任务 4][MysqlCrmeb] - Table [eb_system_form_temp] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:37.652 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_reply 
[INFO ] 2024-10-16 01:04:37.652 - [任务 4][MysqlCrmeb] - Table eb_store_experience_reply is going to be initial synced 
[INFO ] 2024-10-16 01:04:37.719 - [任务 4][MysqlCrmeb] - Query table 'eb_store_experience_reply' counts: 2 
[INFO ] 2024-10-16 01:04:37.719 - [任务 4][MysqlCrmeb] - Table [eb_store_experience_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:37.719 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_bargain_user 
[INFO ] 2024-10-16 01:04:37.720 - [任务 4][MysqlCrmeb] - Table eb_store_bargain_user is going to be initial synced 
[INFO ] 2024-10-16 01:04:37.775 - [任务 4][MysqlCrmeb] - Query table 'eb_store_bargain_user' counts: 0 
[INFO ] 2024-10-16 01:04:37.775 - [任务 4][MysqlCrmeb] - Table [eb_store_bargain_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:37.775 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_store 
[INFO ] 2024-10-16 01:04:37.832 - [任务 4][MysqlCrmeb] - Table eb_system_store is going to be initial synced 
[INFO ] 2024-10-16 01:04:37.832 - [任务 4][MysqlCrmeb] - Query table 'eb_system_store' counts: 0 
[INFO ] 2024-10-16 01:04:37.832 - [任务 4][MysqlCrmeb] - Table [eb_system_store] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:37.832 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_shipping_templates_region 
[INFO ] 2024-10-16 01:04:38.036 - [任务 4][MysqlCrmeb] - Table eb_shipping_templates_region is going to be initial synced 
[INFO ] 2024-10-16 01:04:38.220 - [任务 4][MysqlCrmeb] - Query table 'eb_shipping_templates_region' counts: 369 
[INFO ] 2024-10-16 01:04:38.220 - [任务 4][MysqlCrmeb] - Table [eb_shipping_templates_region] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:38.220 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_recharge 
[INFO ] 2024-10-16 01:04:38.220 - [任务 4][MysqlCrmeb] - Table eb_user_recharge is going to be initial synced 
[INFO ] 2024-10-16 01:04:38.424 - [任务 4][MysqlCrmeb] - Table [eb_user_recharge] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:38.494 - [任务 4][MysqlCrmeb] - Query table 'eb_user_recharge' counts: 0 
[INFO ] 2024-10-16 01:04:38.495 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_coupon 
[INFO ] 2024-10-16 01:04:38.495 - [任务 4][MysqlCrmeb] - Table eb_store_product_coupon is going to be initial synced 
[INFO ] 2024-10-16 01:04:38.553 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_coupon' counts: 0 
[INFO ] 2024-10-16 01:04:38.553 - [任务 4][MysqlCrmeb] - Table [eb_store_product_coupon] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:38.554 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_wechat_pay_info 
[INFO ] 2024-10-16 01:04:38.554 - [任务 4][MysqlCrmeb] - Table eb_wechat_pay_info is going to be initial synced 
[INFO ] 2024-10-16 01:04:38.755 - [任务 4][MysqlCrmeb] - Table [eb_wechat_pay_info] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:38.830 - [任务 4][MysqlCrmeb] - Query table 'eb_wechat_pay_info' counts: 0 
[INFO ] 2024-10-16 01:04:38.831 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_combination 
[INFO ] 2024-10-16 01:04:38.831 - [任务 4][MysqlCrmeb] - Table eb_store_combination is going to be initial synced 
[INFO ] 2024-10-16 01:04:39.032 - [任务 4][MysqlCrmeb] - Table [eb_store_combination] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:39.099 - [任务 4][MysqlCrmeb] - Query table 'eb_store_combination' counts: 4 
[INFO ] 2024-10-16 01:04:39.102 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product 
[INFO ] 2024-10-16 01:04:39.102 - [任务 4][MysqlCrmeb] - Table eb_store_product is going to be initial synced 
[INFO ] 2024-10-16 01:04:39.169 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product' counts: 8 
[INFO ] 2024-10-16 01:04:39.170 - [任务 4][MysqlCrmeb] - Table [eb_store_product] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:39.170 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_article 
[INFO ] 2024-10-16 01:04:39.170 - [任务 4][MysqlCrmeb] - Table eb_article is going to be initial synced 
[INFO ] 2024-10-16 01:04:39.228 - [任务 4][MysqlCrmeb] - Query table 'eb_article' counts: 0 
[INFO ] 2024-10-16 01:04:39.229 - [任务 4][MysqlCrmeb] - Table [eb_article] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:39.232 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_bill 
[INFO ] 2024-10-16 01:04:39.232 - [任务 4][MysqlCrmeb] - Table eb_user_bill is going to be initial synced 
[INFO ] 2024-10-16 01:04:39.291 - [任务 4][MysqlCrmeb] - Query table 'eb_user_bill' counts: 0 
[INFO ] 2024-10-16 01:04:39.292 - [任务 4][MysqlCrmeb] - Table [eb_user_bill] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:39.292 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_relation 
[INFO ] 2024-10-16 01:04:39.293 - [任务 4][MysqlCrmeb] - Table eb_store_experience_relation is going to be initial synced 
[INFO ] 2024-10-16 01:04:39.353 - [任务 4][MysqlCrmeb] - Table [eb_store_experience_relation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:39.353 - [任务 4][MysqlCrmeb] - Query table 'eb_store_experience_relation' counts: 2 
[INFO ] 2024-10-16 01:04:39.355 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_wechat_exceptions 
[INFO ] 2024-10-16 01:04:39.355 - [任务 4][MysqlCrmeb] - Table eb_wechat_exceptions is going to be initial synced 
[INFO ] 2024-10-16 01:04:39.422 - [任务 4][MysqlCrmeb] - Query table 'eb_wechat_exceptions' counts: 31 
[INFO ] 2024-10-16 01:04:39.423 - [任务 4][MysqlCrmeb] - Table [eb_wechat_exceptions] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:39.428 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_template_message 
[INFO ] 2024-10-16 01:04:39.429 - [任务 4][MysqlCrmeb] - Table eb_template_message is going to be initial synced 
[INFO ] 2024-10-16 01:04:39.489 - [任务 4][MysqlCrmeb] - Query table 'eb_template_message' counts: 15 
[INFO ] 2024-10-16 01:04:39.490 - [任务 4][MysqlCrmeb] - Table [eb_template_message] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:39.491 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_integral_record 
[INFO ] 2024-10-16 01:04:39.492 - [任务 4][MysqlCrmeb] - Table eb_user_integral_record is going to be initial synced 
[INFO ] 2024-10-16 01:04:39.554 - [任务 4][MysqlCrmeb] - Query table 'eb_user_integral_record' counts: 1 
[INFO ] 2024-10-16 01:04:39.555 - [任务 4][MysqlCrmeb] - Table [eb_user_integral_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:39.555 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_coupon 
[INFO ] 2024-10-16 01:04:39.555 - [任务 4][MysqlCrmeb] - Table eb_store_coupon is going to be initial synced 
[INFO ] 2024-10-16 01:04:39.622 - [任务 4][MysqlCrmeb] - Table [eb_store_coupon] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:39.622 - [任务 4][MysqlCrmeb] - Query table 'eb_store_coupon' counts: 2 
[INFO ] 2024-10-16 01:04:39.625 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_visit_record 
[INFO ] 2024-10-16 01:04:39.625 - [任务 4][MysqlCrmeb] - Table eb_user_visit_record is going to be initial synced 
[INFO ] 2024-10-16 01:04:39.811 - [任务 4][MysqlCrmeb] - Query table 'eb_user_visit_record' counts: 1257 
[INFO ] 2024-10-16 01:04:39.811 - [任务 4][MysqlCrmeb] - Table [eb_user_visit_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:39.811 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_role_menu 
[INFO ] 2024-10-16 01:04:39.811 - [任务 4][MysqlCrmeb] - Table eb_system_role_menu is going to be initial synced 
[INFO ] 2024-10-16 01:04:39.873 - [任务 4][MysqlCrmeb] - Query table 'eb_system_role_menu' counts: 595 
[INFO ] 2024-10-16 01:04:39.873 - [任务 4][MysqlCrmeb] - Table [eb_system_role_menu] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:39.873 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_log 
[INFO ] 2024-10-16 01:04:39.928 - [任务 4][MysqlCrmeb] - Table eb_store_product_log is going to be initial synced 
[INFO ] 2024-10-16 01:04:39.928 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_log' counts: 0 
[INFO ] 2024-10-16 01:04:39.929 - [任务 4][MysqlCrmeb] - Table [eb_store_product_log] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:39.930 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_series 
[INFO ] 2024-10-16 01:04:39.930 - [任务 4][MysqlCrmeb] - Table eb_store_product_series is going to be initial synced 
[INFO ] 2024-10-16 01:04:39.988 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_series' counts: 5 
[INFO ] 2024-10-16 01:04:39.989 - [任务 4][MysqlCrmeb] - Table [eb_store_product_series] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:39.989 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_attr 
[INFO ] 2024-10-16 01:04:39.989 - [任务 4][MysqlCrmeb] - Table eb_store_product_attr is going to be initial synced 
[INFO ] 2024-10-16 01:04:40.044 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_attr' counts: 24 
[INFO ] 2024-10-16 01:04:40.044 - [任务 4][MysqlCrmeb] - Table [eb_store_product_attr] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:40.044 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_bargain 
[INFO ] 2024-10-16 01:04:40.044 - [任务 4][MysqlCrmeb] - Table eb_store_bargain is going to be initial synced 
[INFO ] 2024-10-16 01:04:40.100 - [任务 4][MysqlCrmeb] - Query table 'eb_store_bargain' counts: 2 
[INFO ] 2024-10-16 01:04:40.100 - [任务 4][MysqlCrmeb] - Table [eb_store_bargain] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:40.102 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_audio 
[INFO ] 2024-10-16 01:04:40.102 - [任务 4][MysqlCrmeb] - Table eb_store_experience_audio is going to be initial synced 
[INFO ] 2024-10-16 01:04:40.157 - [任务 4][MysqlCrmeb] - Query table 'eb_store_experience_audio' counts: 5 
[INFO ] 2024-10-16 01:04:40.157 - [任务 4][MysqlCrmeb] - Table [eb_store_experience_audio] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:40.158 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_city 
[INFO ] 2024-10-16 01:04:40.158 - [任务 4][MysqlCrmeb] - Table eb_system_city is going to be initial synced 
[INFO ] 2024-10-16 01:04:40.359 - [任务 4][MysqlCrmeb] - Query table 'eb_system_city' counts: 3938 
[INFO ] 2024-10-16 01:04:43.803 - [任务 4][MysqlCrmeb] - Table [eb_system_city] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:43.803 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_sms_record 
[INFO ] 2024-10-16 01:04:43.804 - [任务 4][MysqlCrmeb] - Table eb_sms_record is going to be initial synced 
[INFO ] 2024-10-16 01:04:43.876 - [任务 4][MysqlCrmeb] - Table [eb_sms_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:43.877 - [任务 4][MysqlCrmeb] - Query table 'eb_sms_record' counts: 0 
[INFO ] 2024-10-16 01:04:43.879 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_extract 
[INFO ] 2024-10-16 01:04:43.879 - [任务 4][MysqlCrmeb] - Table eb_user_extract is going to be initial synced 
[INFO ] 2024-10-16 01:04:44.087 - [任务 4][MysqlCrmeb] - Table [eb_user_extract] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:44.163 - [任务 4][MysqlCrmeb] - Query table 'eb_user_extract' counts: 0 
[INFO ] 2024-10-16 01:04:44.179 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_group 
[INFO ] 2024-10-16 01:04:44.179 - [任务 4][MysqlCrmeb] - Table eb_user_group is going to be initial synced 
[INFO ] 2024-10-16 01:04:44.391 - [任务 4][MysqlCrmeb] - Table [eb_user_group] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:44.662 - [任务 4][MysqlCrmeb] - Query table 'eb_user_group' counts: 3 
[INFO ] 2024-10-16 01:04:44.663 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_rule 
[INFO ] 2024-10-16 01:04:44.723 - [任务 4][MysqlCrmeb] - Table eb_store_product_rule is going to be initial synced 
[INFO ] 2024-10-16 01:04:44.723 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_rule' counts: 2 
[INFO ] 2024-10-16 01:04:44.730 - [任务 4][MysqlCrmeb] - Table [eb_store_product_rule] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:44.730 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_cart 
[INFO ] 2024-10-16 01:04:44.788 - [任务 4][MysqlCrmeb] - Table eb_store_cart is going to be initial synced 
[INFO ] 2024-10-16 01:04:44.788 - [任务 4][MysqlCrmeb] - Table [eb_store_cart] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:44.788 - [任务 4][MysqlCrmeb] - Query table 'eb_store_cart' counts: 3 
[INFO ] 2024-10-16 01:04:44.788 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_admin 
[INFO ] 2024-10-16 01:04:44.846 - [任务 4][MysqlCrmeb] - Table eb_system_admin is going to be initial synced 
[INFO ] 2024-10-16 01:04:44.846 - [任务 4][MysqlCrmeb] - Table [eb_system_admin] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:44.847 - [任务 4][MysqlCrmeb] - Query table 'eb_system_admin' counts: 2 
[INFO ] 2024-10-16 01:04:44.847 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_menu 
[INFO ] 2024-10-16 01:04:44.906 - [任务 4][MysqlCrmeb] - Table eb_system_menu is going to be initial synced 
[INFO ] 2024-10-16 01:04:44.906 - [任务 4][MysqlCrmeb] - Query table 'eb_system_menu' counts: 369 
[INFO ] 2024-10-16 01:04:44.964 - [任务 4][MysqlCrmeb] - Table [eb_system_menu] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:44.964 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_user_level 
[INFO ] 2024-10-16 01:04:45.022 - [任务 4][MysqlCrmeb] - Table eb_system_user_level is going to be initial synced 
[INFO ] 2024-10-16 01:04:45.022 - [任务 4][MysqlCrmeb] - Query table 'eb_system_user_level' counts: 5 
[INFO ] 2024-10-16 01:04:45.024 - [任务 4][MysqlCrmeb] - Table [eb_system_user_level] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:45.024 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_category 
[INFO ] 2024-10-16 01:04:45.081 - [任务 4][MysqlCrmeb] - Table eb_category is going to be initial synced 
[INFO ] 2024-10-16 01:04:45.081 - [任务 4][MysqlCrmeb] - Query table 'eb_category' counts: 295 
[INFO ] 2024-10-16 01:04:45.113 - [任务 4][MysqlCrmeb] - Table [eb_category] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:45.113 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_sms_template 
[INFO ] 2024-10-16 01:04:45.184 - [任务 4][MysqlCrmeb] - Table eb_sms_template is going to be initial synced 
[INFO ] 2024-10-16 01:04:45.184 - [任务 4][MysqlCrmeb] - Query table 'eb_sms_template' counts: 7 
[INFO ] 2024-10-16 01:04:45.193 - [任务 4][MysqlCrmeb] - Table [eb_sms_template] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:45.193 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_tag 
[INFO ] 2024-10-16 01:04:45.247 - [任务 4][MysqlCrmeb] - Table eb_user_tag is going to be initial synced 
[INFO ] 2024-10-16 01:04:45.247 - [任务 4][MysqlCrmeb] - Query table 'eb_user_tag' counts: 5 
[INFO ] 2024-10-16 01:04:45.248 - [任务 4][MysqlCrmeb] - Table [eb_user_tag] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:45.248 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_order_info 
[INFO ] 2024-10-16 01:04:45.304 - [任务 4][MysqlCrmeb] - Table eb_store_order_info is going to be initial synced 
[INFO ] 2024-10-16 01:04:45.304 - [任务 4][MysqlCrmeb] - Query table 'eb_store_order_info' counts: 0 
[INFO ] 2024-10-16 01:04:45.306 - [任务 4][MysqlCrmeb] - Table [eb_store_order_info] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:45.306 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_coupon_user 
[INFO ] 2024-10-16 01:04:45.365 - [任务 4][MysqlCrmeb] - Table eb_store_coupon_user is going to be initial synced 
[INFO ] 2024-10-16 01:04:45.365 - [任务 4][MysqlCrmeb] - Table [eb_store_coupon_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:45.365 - [任务 4][MysqlCrmeb] - Query table 'eb_store_coupon_user' counts: 0 
[INFO ] 2024-10-16 01:04:45.365 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_role 
[INFO ] 2024-10-16 01:04:45.426 - [任务 4][MysqlCrmeb] - Table eb_system_role is going to be initial synced 
[INFO ] 2024-10-16 01:04:45.426 - [任务 4][MysqlCrmeb] - Query table 'eb_system_role' counts: 2 
[INFO ] 2024-10-16 01:04:45.426 - [任务 4][MysqlCrmeb] - Table [eb_system_role] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:45.427 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_audio_history 
[INFO ] 2024-10-16 01:04:45.488 - [任务 4][MysqlCrmeb] - Table eb_store_experience_audio_history is going to be initial synced 
[INFO ] 2024-10-16 01:04:45.488 - [任务 4][MysqlCrmeb] - Query table 'eb_store_experience_audio_history' counts: 4 
[INFO ] 2024-10-16 01:04:45.488 - [任务 4][MysqlCrmeb] - Table [eb_store_experience_audio_history] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:45.489 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_pink 
[INFO ] 2024-10-16 01:04:45.490 - [任务 4][MysqlCrmeb] - Table eb_store_pink is going to be initial synced 
[INFO ] 2024-10-16 01:04:45.549 - [任务 4][MysqlCrmeb] - Query table 'eb_store_pink' counts: 0 
[INFO ] 2024-10-16 01:04:45.549 - [任务 4][MysqlCrmeb] - Table [eb_store_pink] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:45.549 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_express 
[INFO ] 2024-10-16 01:04:45.550 - [任务 4][MysqlCrmeb] - Table eb_express is going to be initial synced 
[INFO ] 2024-10-16 01:04:45.645 - [任务 4][MysqlCrmeb] - Query table 'eb_express' counts: 1067 
[INFO ] 2024-10-16 01:04:45.645 - [任务 4][MysqlCrmeb] - Table [eb_express] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:45.645 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_store_product_relation 
[INFO ] 2024-10-16 01:04:45.645 - [任务 4][MysqlCrmeb] - Table eb_store_product_relation is going to be initial synced 
[INFO ] 2024-10-16 01:04:45.704 - [任务 4][MysqlCrmeb] - Table [eb_store_product_relation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:45.704 - [任务 4][MysqlCrmeb] - Query table 'eb_store_product_relation' counts: 0 
[INFO ] 2024-10-16 01:04:45.704 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_system_group_data 
[INFO ] 2024-10-16 01:04:45.758 - [任务 4][MysqlCrmeb] - Table eb_system_group_data is going to be initial synced 
[INFO ] 2024-10-16 01:04:45.758 - [任务 4][MysqlCrmeb] - Query table 'eb_system_group_data' counts: 68 
[INFO ] 2024-10-16 01:04:45.770 - [任务 4][MysqlCrmeb] - Table [eb_system_group_data] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:45.770 - [任务 4][MysqlCrmeb] - Starting batch read, table name: eb_user_address 
[INFO ] 2024-10-16 01:04:45.827 - [任务 4][MysqlCrmeb] - Table eb_user_address is going to be initial synced 
[INFO ] 2024-10-16 01:04:45.828 - [任务 4][MysqlCrmeb] - Table [eb_user_address] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 01:04:45.828 - [任务 4][MysqlCrmeb] - Query table 'eb_user_address' counts: 0 
[INFO ] 2024-10-16 01:04:46.032 - [任务 4][MysqlCrmeb] - Initial sync completed 
[INFO ] 2024-10-16 01:04:47.285 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] running status set to false 
[INFO ] 2024-10-16 01:04:47.348 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] running status set to false 
[INFO ] 2024-10-16 01:04:47.348 - [任务 4][Mysql23306] - PDK connector node stopped: HazelcastTargetPdkDataNode-eeb92868-7cb8-47f7-b7c0-193204274836 
[INFO ] 2024-10-16 01:04:47.348 - [任务 4][MysqlCrmeb] - PDK connector node stopped: HazelcastSourcePdkDataNode-3903a72b-a592-4673-bf5f-e83e5b1219eb 
[INFO ] 2024-10-16 01:04:47.349 - [任务 4][MysqlCrmeb] - PDK connector node released: HazelcastSourcePdkDataNode-3903a72b-a592-4673-bf5f-e83e5b1219eb 
[INFO ] 2024-10-16 01:04:47.351 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] schema data cleaned 
[INFO ] 2024-10-16 01:04:47.352 - [任务 4][Mysql23306] - PDK connector node released: HazelcastTargetPdkDataNode-eeb92868-7cb8-47f7-b7c0-193204274836 
[INFO ] 2024-10-16 01:04:47.352 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] schema data cleaned 
[INFO ] 2024-10-16 01:04:47.353 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] monitor closed 
[INFO ] 2024-10-16 01:04:47.353 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] monitor closed 
[INFO ] 2024-10-16 01:04:47.563 - [任务 4][MysqlCrmeb] - Node MysqlCrmeb[3903a72b-a592-4673-bf5f-e83e5b1219eb] close complete, cost 75 ms 
[INFO ] 2024-10-16 01:04:47.565 - [任务 4][Mysql23306] - Node Mysql23306[eeb92868-7cb8-47f7-b7c0-193204274836] close complete, cost 72 ms 
[INFO ] 2024-10-16 01:04:47.689 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-16 01:04:47.708 - [任务 4] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@65992255 
[INFO ] 2024-10-16 01:04:47.709 - [任务 4] - Stop task milestones: 670e9533f12cdc2b66d43fa6(任务 4)  
[INFO ] 2024-10-16 01:04:47.855 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-10-16 01:04:47.855 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-10-16 01:04:47.919 - [任务 4] - Remove memory task client succeed, task: 任务 4[670e9533f12cdc2b66d43fa6] 
[INFO ] 2024-10-16 01:04:47.919 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[670e9533f12cdc2b66d43fa6] 
