[INFO ] 2024-09-22 17:07:05.947 - [测试时区 - Copy] - Task initialization... 
[INFO ] 2024-09-22 17:07:06.137 - [测试时区 - Copy] - Start task milestones: 66e9cba96668000e49ab2465(测试时区 - Copy) 
[INFO ] 2024-09-22 17:07:08.058 - [测试时区 - Copy] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-09-22 17:07:08.433 - [测试时区 - Copy] - The engine receives 测试时区 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-22 17:07:09.369 - [测试时区 - Copy][localmaster] - Node localmaster[a883236c-56f5-4d07-9fb2-2d0750339dcd] start preload schema,table counts: 1 
[INFO ] 2024-09-22 17:07:09.371 - [测试时区 - Copy][localmaster] - Node localmaster[a883236c-56f5-4d07-9fb2-2d0750339dcd] preload schema finished, cost 0 ms 
[INFO ] 2024-09-22 17:07:09.376 - [测试时区 - Copy][mysql3307] - Node mysql3307[e721f614-424c-4a40-a5ad-3987afe2a2f8] start preload schema,table counts: 1 
[INFO ] 2024-09-22 17:07:09.376 - [测试时区 - Copy][mysql3307] - Node mysql3307[e721f614-424c-4a40-a5ad-3987afe2a2f8] preload schema finished, cost 0 ms 
[INFO ] 2024-09-22 17:07:15.312 - [测试时区 - Copy][mysql3307] - Node(mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-22 17:07:15.313 - [测试时区 - Copy][mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-09-22 17:07:15.859 - [测试时区 - Copy][localmaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1726996034997} and {hostPort=localhost:43306, time=1726967234994} 
[INFO ] 2024-09-22 17:07:15.895 - [测试时区 - Copy][localmaster] - Source node "localmaster" read batch size: 100 
[INFO ] 2024-09-22 17:07:15.895 - [测试时区 - Copy][localmaster] - Source node "localmaster" event queue capacity: 200 
[INFO ] 2024-09-22 17:07:15.896 - [测试时区 - Copy][localmaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-22 17:07:15.899 - [测试时区 - Copy][localmaster] - batch offset found: {},stream offset found: {"name":"1f633d5c-9e79-4a5c-9fbe-d89e1cd76ca4","offset":{"{\"server\":\"1f633d5c-9e79-4a5c-9fbe-d89e1cd76ca4\"}":"{\"ts_sec\":1726719145,\"file\":\"mysql-bin.000023\",\"pos\":9097,\"server_id\":1}"}} 
[INFO ] 2024-09-22 17:07:15.900 - [测试时区 - Copy][localmaster] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-22 17:07:16.013 - [测试时区 - Copy][localmaster] - Incremental sync starting... 
[INFO ] 2024-09-22 17:07:16.013 - [测试时区 - Copy][localmaster] - Initial sync completed 
[INFO ] 2024-09-22 17:07:16.230 - [测试时区 - Copy][localmaster] - Starting stream read, table list: [testDateTime], offset: {"name":"1f633d5c-9e79-4a5c-9fbe-d89e1cd76ca4","offset":{"{\"server\":\"1f633d5c-9e79-4a5c-9fbe-d89e1cd76ca4\"}":"{\"ts_sec\":1726719145,\"file\":\"mysql-bin.000023\",\"pos\":9097,\"server_id\":1}"}} 
[INFO ] 2024-09-22 17:07:16.621 - [测试时区 - Copy][localmaster] - Starting mysql cdc, server name: 1f633d5c-9e79-4a5c-9fbe-d89e1cd76ca4 
[INFO ] 2024-09-22 17:07:16.685 - [测试时区 - Copy][localmaster] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1254928582
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 1f633d5c-9e79-4a5c-9fbe-d89e1cd76ca4
  database.port: 33306
  threadName: Debezium-Mysql-Connector-1f633d5c-9e79-4a5c-9fbe-d89e1cd76ca4
  enable.time.adjuster: false
  database.hostname: localhost
  database.password: ********
  name: 1f633d5c-9e79-4a5c-9fbe-d89e1cd76ca4
  pdk.offset.string: {"name":"1f633d5c-9e79-4a5c-9fbe-d89e1cd76ca4","offset":{"{\"server\":\"1f633d5c-9e79-4a5c-9fbe-d89e1cd76ca4\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":8663,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDateTime
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-22 17:07:16.782 - [测试时区 - Copy][localmaster] - Connector Mysql incremental start succeed, tables: [testDateTime], data change syncing 
[INFO ] 2024-09-22 17:07:17.247 - [测试时区 - Copy][localmaster] - last event is io.tapdata.entity.event.dml.TapInsertRecordEvent@1b64bae: {"after":{"dateCol":-62135596800,"id":32},"containsIllegalDate":false,"referenceTime":1726718873000,"tableId":"testDateTime","time":1726996037241,"type":300} 
[INFO ] 2024-09-22 17:07:17.248 - [测试时区 - Copy][localmaster] - last event is io.tapdata.entity.event.dml.TapInsertRecordEvent@19be2a2b: {"after":{"dateCol":-62135596800,"id":33},"containsIllegalDate":false,"referenceTime":1726719145000,"tableId":"testDateTime","time":1726996037242,"type":300} 
[INFO ] 2024-09-22 17:07:26.742 - [测试时区 - Copy][localmaster] - last event is io.tapdata.entity.event.control.HeartbeatEvent@fe2470b: {"time":1726996046742,"type":501} 
[INFO ] 2024-09-22 17:08:14.744 - [测试时区 - Copy][localmaster] - last event is io.tapdata.entity.event.control.HeartbeatEvent@12d3e388: {"time":1726996094743,"type":501} 
[INFO ] 2024-09-22 17:08:50.979 - [测试时区 - Copy][localmaster] - Node localmaster[a883236c-56f5-4d07-9fb2-2d0750339dcd] running status set to false 
[INFO ] 2024-09-22 17:08:51.071 - [测试时区 - Copy][localmaster] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-22 17:08:51.071 - [测试时区 - Copy][localmaster] - Mysql binlog reader stopped 
[INFO ] 2024-09-22 17:08:51.115 - [测试时区 - Copy][localmaster] - PDK connector node stopped: HazelcastSourcePdkDataNode-a883236c-56f5-4d07-9fb2-2d0750339dcd 
[INFO ] 2024-09-22 17:08:51.136 - [测试时区 - Copy][localmaster] - PDK connector node released: HazelcastSourcePdkDataNode-a883236c-56f5-4d07-9fb2-2d0750339dcd 
[INFO ] 2024-09-22 17:08:51.193 - [测试时区 - Copy][localmaster] - Node localmaster[a883236c-56f5-4d07-9fb2-2d0750339dcd] schema data cleaned 
[INFO ] 2024-09-22 17:08:51.193 - [测试时区 - Copy][localmaster] - Node localmaster[a883236c-56f5-4d07-9fb2-2d0750339dcd] monitor closed 
[INFO ] 2024-09-22 17:08:51.195 - [测试时区 - Copy][localmaster] - Node localmaster[a883236c-56f5-4d07-9fb2-2d0750339dcd] close complete, cost 239 ms 
[INFO ] 2024-09-22 17:08:51.197 - [测试时区 - Copy][mysql3307] - Node mysql3307[e721f614-424c-4a40-a5ad-3987afe2a2f8] running status set to false 
[INFO ] 2024-09-22 17:08:51.312 - [测试时区 - Copy] - Stop task milestones: 66e9cba96668000e49ab2465(测试时区 - Copy)  
[INFO ] 2024-09-22 17:08:51.326 - [测试时区 - Copy][mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-e721f614-424c-4a40-a5ad-3987afe2a2f8 
[INFO ] 2024-09-22 17:08:51.326 - [测试时区 - Copy][mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-e721f614-424c-4a40-a5ad-3987afe2a2f8 
[INFO ] 2024-09-22 17:08:51.326 - [测试时区 - Copy][mysql3307] - Node mysql3307[e721f614-424c-4a40-a5ad-3987afe2a2f8] schema data cleaned 
[INFO ] 2024-09-22 17:08:51.327 - [测试时区 - Copy][mysql3307] - Node mysql3307[e721f614-424c-4a40-a5ad-3987afe2a2f8] monitor closed 
[INFO ] 2024-09-22 17:08:51.328 - [测试时区 - Copy][mysql3307] - Node mysql3307[e721f614-424c-4a40-a5ad-3987afe2a2f8] close complete, cost 131 ms 
[INFO ] 2024-09-22 17:08:54.953 - [测试时区 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-22 17:08:54.953 - [测试时区 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-09-22 17:08:54.954 - [测试时区 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-09-22 17:08:55.003 - [测试时区 - Copy] - Remove memory task client succeed, task: 测试时区 - Copy[66e9cba96668000e49ab2465] 
[INFO ] 2024-09-22 17:08:55.003 - [测试时区 - Copy] - Destroy memory task client cache succeed, task: 测试时区 - Copy[66e9cba96668000e49ab2465] 
