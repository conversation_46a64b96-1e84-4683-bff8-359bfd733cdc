[INFO ] 2024-09-27 15:59:16.847 - [任务 3 - Copy - Copy] - Start task milestones: 66f665c6e239ae1b0e7cb333(任务 3 - Copy - Copy) 
[INFO ] 2024-09-27 15:59:16.884 - [任务 3 - Copy - Copy] - Task initialization... 
[INFO ] 2024-09-27 15:59:17.143 - [任务 3 - Copy - Copy] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-09-27 15:59:17.215 - [任务 3 - Copy - Copy] - The engine receives 任务 3 - Copy - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 15:59:17.293 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] start preload schema,table counts: 1 
[INFO ] 2024-09-27 15:59:17.293 - [任务 3 - Copy - Copy][Source<PERSON><PERSON><PERSON>] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] start preload schema,table counts: 1 
[INFO ] 2024-09-27 15:59:17.293 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 15:59:17.293 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] preload schema finished, cost 1 ms 
[WARN ] 2024-09-27 15:59:18.685 - [任务 3 - Copy - Copy][LocalhostMaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1727423957997} and {hostPort=localhost:43306, time=1727395157995} 
[INFO ] 2024-09-27 15:59:18.716 - [任务 3 - Copy - Copy][LocalhostMaster] - Source node "LocalhostMaster" read batch size: 100 
[INFO ] 2024-09-27 15:59:18.716 - [任务 3 - Copy - Copy][LocalhostMaster] - Source node "LocalhostMaster" event queue capacity: 200 
[INFO ] 2024-09-27 15:59:18.716 - [任务 3 - Copy - Copy][LocalhostMaster] - Sync progress not exists, will run task as first time 
[INFO ] 2024-09-27 15:59:18.717 - [任务 3 - Copy - Copy][LocalhostMaster] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-27 15:59:18.720 - [任务 3 - Copy - Copy][LocalhostMaster] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000043","position":333484037,"gtidSet":""} 
[INFO ] 2024-09-27 15:59:18.783 - [任务 3 - Copy - Copy][LocalhostMaster] - Initial sync started 
[INFO ] 2024-09-27 15:59:18.783 - [任务 3 - Copy - Copy][LocalhostMaster] - Starting batch read, table name: t2 
[INFO ] 2024-09-27 15:59:18.783 - [任务 3 - Copy - Copy][LocalhostMaster] - Table t2 is going to be initial synced 
[INFO ] 2024-09-27 15:59:18.850 - [任务 3 - Copy - Copy][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 15:59:18.850 - [任务 3 - Copy - Copy][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-27 15:59:18.857 - [任务 3 - Copy - Copy][SourceMongo] - Sync progress not exists, will run task as first time 
[INFO ] 2024-09-27 15:59:19.227 - [任务 3 - Copy - Copy][LocalhostMaster] - Query table 't2' counts: 2178000 
[INFO ] 2024-09-27 16:11:08.988 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] running status set to false 
[WARN ] 2024-09-27 16:11:09.000 - [任务 3 - Copy - Copy][LocalhostMaster] - Stop PDK connector node failed: Unknown PDK exception occur, com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-ea973c51-6aa1-411a-be0f-5d6415d52bb5 
[INFO ] 2024-09-27 16:11:09.001 - [任务 3 - Copy - Copy][LocalhostMaster] - PDK connector node released: HazelcastSourcePdkDataNode-ea973c51-6aa1-411a-be0f-5d6415d52bb5 
[INFO ] 2024-09-27 16:11:09.001 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] schema data cleaned 
[INFO ] 2024-09-27 16:11:09.001 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] monitor closed 
[INFO ] 2024-09-27 16:11:09.015 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] close complete, cost 35 ms 
[INFO ] 2024-09-27 16:11:09.015 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] running status set to false 
[WARN ] 2024-09-27 16:11:09.168 - [任务 3 - Copy - Copy][SourceMongo] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastTargetPdkDataNode-935174d0-873e-4e47-8a00-9daa3d01bcf4 
[INFO ] 2024-09-27 16:11:09.178 - [任务 3 - Copy - Copy][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-935174d0-873e-4e47-8a00-9daa3d01bcf4 
[INFO ] 2024-09-27 16:11:09.178 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] schema data cleaned 
[INFO ] 2024-09-27 16:11:09.178 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] monitor closed 
[INFO ] 2024-09-27 16:11:09.178 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] close complete, cost 163 ms 
[INFO ] 2024-09-27 16:23:22.467 - [任务 3 - Copy - Copy] - Task initialization... 
[INFO ] 2024-09-27 16:23:22.692 - [任务 3 - Copy - Copy] - Start task milestones: 66f665c6e239ae1b0e7cb333(任务 3 - Copy - Copy) 
[INFO ] 2024-09-27 16:23:24.068 - [任务 3 - Copy - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 16:23:24.233 - [任务 3 - Copy - Copy] - The engine receives 任务 3 - Copy - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 16:23:25.011 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:23:25.016 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:23:25.016 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:23:25.017 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] preload schema finished, cost 1 ms 
[INFO ] 2024-09-27 16:23:26.589 - [任务 3 - Copy - Copy][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 16:23:26.606 - [任务 3 - Copy - Copy][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-09-27 16:23:27.358 - [任务 3 - Copy - Copy][LocalhostMaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1727425406996} and {hostPort=localhost:43306, time=1727396606991} 
[INFO ] 2024-09-27 16:23:27.379 - [任务 3 - Copy - Copy][LocalhostMaster] - Source node "LocalhostMaster" read batch size: 100 
[INFO ] 2024-09-27 16:23:27.388 - [任务 3 - Copy - Copy][LocalhostMaster] - Source node "LocalhostMaster" event queue capacity: 200 
[INFO ] 2024-09-27 16:23:27.390 - [任务 3 - Copy - Copy][LocalhostMaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-27 16:23:27.390 - [任务 3 - Copy - Copy][LocalhostMaster] - batch offset found: {"t2":{"batch_read_connector_status":"RUNNING"}},stream offset found: {"filename":"mysql-bin.000043","position":333484037,"gtidSet":""} 
[INFO ] 2024-09-27 16:23:27.457 - [任务 3 - Copy - Copy][LocalhostMaster] - Initial sync started 
[INFO ] 2024-09-27 16:23:27.461 - [任务 3 - Copy - Copy][LocalhostMaster] - Starting batch read, table name: t2 
[INFO ] 2024-09-27 16:23:27.464 - [任务 3 - Copy - Copy][LocalhostMaster] - Table t2 is going to be initial synced 
[INFO ] 2024-09-27 16:23:28.136 - [任务 3 - Copy - Copy][LocalhostMaster] - Query table 't2' counts: 2178000 
[INFO ] 2024-09-27 16:25:22.281 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] running status set to false 
[INFO ] 2024-09-27 16:25:22.417 - [任务 3 - Copy - Copy][LocalhostMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode-ea973c51-6aa1-411a-be0f-5d6415d52bb5 
[INFO ] 2024-09-27 16:25:22.417 - [任务 3 - Copy - Copy][LocalhostMaster] - PDK connector node released: HazelcastSourcePdkDataNode-ea973c51-6aa1-411a-be0f-5d6415d52bb5 
[INFO ] 2024-09-27 16:25:22.418 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] schema data cleaned 
[INFO ] 2024-09-27 16:25:22.419 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] monitor closed 
[INFO ] 2024-09-27 16:25:22.420 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] close complete, cost 142 ms 
[INFO ] 2024-09-27 16:25:22.420 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] running status set to false 
[INFO ] 2024-09-27 16:25:22.424 - [任务 3 - Copy - Copy][LocalhostMaster] - Initial sync completed 
[INFO ] 2024-09-27 16:25:22.450 - [任务 3 - Copy - Copy][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-935174d0-873e-4e47-8a00-9daa3d01bcf4 
[INFO ] 2024-09-27 16:25:22.451 - [任务 3 - Copy - Copy][LocalhostMaster] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t2`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 
[INFO ] 2024-09-27 16:25:22.500 - [任务 3 - Copy - Copy][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-935174d0-873e-4e47-8a00-9daa3d01bcf4 
[INFO ] 2024-09-27 16:25:22.500 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] schema data cleaned 
[INFO ] 2024-09-27 16:25:22.500 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] monitor closed 
[INFO ] 2024-09-27 16:25:22.500 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] close complete, cost 32 ms 
[ERROR] 2024-09-27 16:25:22.510 - [任务 3 - Copy - Copy][LocalhostMaster] - java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t2`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 <-- Error Message -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t2`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **



<-- Simple Stack Trace -->
Caused by: java.io.IOException: Stream closed.
	java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	java.net.SocketInputStream.available(SocketInputStream.java:259)
	com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	java.io.FilterInputStream.read(FilterInputStream.java:133)
	com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t2`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.handleThrowable(HazelcastSourcePdkDataNode.java:354)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:323)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t2`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	... 10 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t2`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 13 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t2`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:213)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more
Caused by: java.sql.SQLException: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:131)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1952)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	... 25 more
Caused by: java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	... 28 more

[ERROR] 2024-09-27 16:25:22.618 - [任务 3 - Copy - Copy][SourceMongo] - java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: t2 <-- Full Stack Trace -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: t2
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:638)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: t2
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:725)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:666)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:626)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:519)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:598)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:650)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:597)
	... 6 more
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: t2
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:814)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$26(HazelcastTargetPdkDataNode.java:514)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:514)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:784)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:717)
	... 12 more
Caused by: Execute PDK method: TARGET_WRITE_RECORD, tableName: t2
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:878)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$59(HazelcastTargetPdkDataNode.java:820)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: java.lang.InterruptedException
	at io.tapdata.mongodb.MongodbExceptionCollector.collectUserPwdInvalid(MongodbExceptionCollector.java:69)
	at io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1317)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:865)
	... 28 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.locks.AbstractQueuedSynchronizer.acquireInterruptibly(AbstractQueuedSynchronizer.java:1223)
	at java.util.concurrent.locks.ReentrantLock.lockInterruptibly(ReentrantLock.java:340)
	at com.mongodb.internal.Locks.checkedWithLock(Locks.java:41)
	at com.mongodb.internal.Locks.withLock(Locks.java:36)
	at com.mongodb.internal.Locks.withLock(Locks.java:29)
	at com.mongodb.internal.connection.BaseCluster.withLock(BaseCluster.java:275)
	at com.mongodb.internal.connection.SingleServerCluster.withLock(SingleServerCluster.java:46)
	at com.mongodb.internal.connection.DefaultSdamServerDescriptionManager.handleExceptionAfterHandshake(DefaultSdamServerDescriptionManager.java:92)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:206)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:122)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:87)
	at com.mongodb.internal.connection.DefaultServer$OperationCountTrackingConnection.command(DefaultServer.java:297)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.executeCommand(MixedBulkWriteOperation.java:393)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.executeBulkWriteBatch(MixedBulkWriteOperation.java:257)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.lambda$execute$0(MixedBulkWriteOperation.java:198)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$0(OperationHelper.java:358)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$1(OperationHelper.java:357)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.withSourceAndConnection(OperationHelper.java:356)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.lambda$execute$1(MixedBulkWriteOperation.java:181)
	at com.mongodb.internal.async.function.RetryingSyncSupplier.get(RetryingSyncSupplier.java:67)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.execute(MixedBulkWriteOperation.java:202)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.execute(MixedBulkWriteOperation.java:76)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:206)
	at com.mongodb.client.internal.MongoCollectionImpl.executeBulkWrite(MongoCollectionImpl.java:447)
	at com.mongodb.client.internal.MongoCollectionImpl.bulkWrite(MongoCollectionImpl.java:428)
	at io.tapdata.mongodb.writer.MongodbWriter.writeRecord(MongodbWriter.java:119)
	at io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1314)
	... 31 more

[INFO ] 2024-09-27 16:25:23.881 - [任务 3 - Copy - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 16:25:23.881 - [任务 3 - Copy - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@737ff6cf 
[INFO ] 2024-09-27 16:25:23.996 - [任务 3 - Copy - Copy] - Stop task milestones: 66f665c6e239ae1b0e7cb333(任务 3 - Copy - Copy)  
[INFO ] 2024-09-27 16:25:24.014 - [任务 3 - Copy - Copy] - Stopped task aspect(s) 
[INFO ] 2024-09-27 16:25:24.015 - [任务 3 - Copy - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 16:25:24.041 - [任务 3 - Copy - Copy] - Remove memory task client succeed, task: 任务 3 - Copy - Copy[66f665c6e239ae1b0e7cb333] 
[INFO ] 2024-09-27 16:25:24.042 - [任务 3 - Copy - Copy] - Destroy memory task client cache succeed, task: 任务 3 - Copy - Copy[66f665c6e239ae1b0e7cb333] 
[INFO ] 2024-09-27 16:26:18.729 - [任务 3 - Copy - Copy] - Task initialization... 
[INFO ] 2024-09-27 16:26:18.819 - [任务 3 - Copy - Copy] - Start task milestones: 66f665c6e239ae1b0e7cb333(任务 3 - Copy - Copy) 
[INFO ] 2024-09-27 16:26:18.997 - [任务 3 - Copy - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 16:26:19.040 - [任务 3 - Copy - Copy] - The engine receives 任务 3 - Copy - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 16:26:19.153 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:26:19.153 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:26:19.153 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] preload schema finished, cost 1 ms 
[INFO ] 2024-09-27 16:26:19.153 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:26:19.531 - [任务 3 - Copy - Copy][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 16:26:19.730 - [任务 3 - Copy - Copy][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-09-27 16:26:20.288 - [任务 3 - Copy - Copy][LocalhostMaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1727425579996} and {hostPort=localhost:43306, time=1727396779986} 
[INFO ] 2024-09-27 16:26:20.501 - [任务 3 - Copy - Copy][LocalhostMaster] - Source node "LocalhostMaster" read batch size: 100 
[INFO ] 2024-09-27 16:26:20.502 - [任务 3 - Copy - Copy][LocalhostMaster] - Source node "LocalhostMaster" event queue capacity: 200 
[INFO ] 2024-09-27 16:26:20.502 - [任务 3 - Copy - Copy][LocalhostMaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-27 16:26:20.502 - [任务 3 - Copy - Copy][LocalhostMaster] - batch offset found: {"t2":{"batch_read_connector_status":"RUNNING"}},stream offset found: {"filename":"mysql-bin.000043","position":333484037,"gtidSet":""} 
[INFO ] 2024-09-27 16:26:20.645 - [任务 3 - Copy - Copy][LocalhostMaster] - Initial sync started 
[INFO ] 2024-09-27 16:26:20.646 - [任务 3 - Copy - Copy][LocalhostMaster] - Starting batch read, table name: t2 
[INFO ] 2024-09-27 16:26:20.836 - [任务 3 - Copy - Copy][LocalhostMaster] - Table t2 is going to be initial synced 
[INFO ] 2024-09-27 16:26:21.120 - [任务 3 - Copy - Copy][LocalhostMaster] - Query table 't2' counts: 2178000 
[INFO ] 2024-09-27 16:50:47.287 - [任务 3 - Copy - Copy][LocalhostMaster] - Table [t2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-27 16:50:47.294 - [任务 3 - Copy - Copy][LocalhostMaster] - Initial sync completed 
[INFO ] 2024-09-27 16:50:47.294 - [任务 3 - Copy - Copy][LocalhostMaster] - Incremental sync starting... 
[INFO ] 2024-09-27 16:50:47.294 - [任务 3 - Copy - Copy][LocalhostMaster] - Initial sync completed 
[INFO ] 2024-09-27 16:50:47.294 - [任务 3 - Copy - Copy][LocalhostMaster] - Starting stream read, table list: [t2], offset: {"filename":"mysql-bin.000043","position":333484037,"gtidSet":""} 
[INFO ] 2024-09-27 16:50:47.397 - [任务 3 - Copy - Copy][LocalhostMaster] - Starting mysql cdc, server name: 239a51d6-1e01-499c-9e3b-a8fad45f6303 
[INFO ] 2024-09-27 16:50:47.399 - [任务 3 - Copy - Copy][LocalhostMaster] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"239a51d6-1e01-499c-9e3b-a8fad45f6303","offset":{"{\"server\":\"239a51d6-1e01-499c-9e3b-a8fad45f6303\"}":"{\"file\":\"mysql-bin.000043\",\"pos\":333484037,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1406609574
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 239a51d6-1e01-499c-9e3b-a8fad45f6303
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 33306
  threadName: Debezium-Mysql-Connector-239a51d6-1e01-499c-9e3b-a8fad45f6303
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 239a51d6-1e01-499c-9e3b-a8fad45f6303
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 16:50:47.603 - [任务 3 - Copy - Copy][LocalhostMaster] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-27 17:13:33.037 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] running status set to false 
[INFO ] 2024-09-27 17:13:33.227 - [任务 3 - Copy - Copy][LocalhostMaster] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-27 17:13:33.274 - [任务 3 - Copy - Copy][LocalhostMaster] - Mysql binlog reader stopped 
[INFO ] 2024-09-27 17:13:33.274 - [任务 3 - Copy - Copy][LocalhostMaster] - Incremental sync completed 
[INFO ] 2024-09-27 17:13:33.280 - [任务 3 - Copy - Copy][LocalhostMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode-ea973c51-6aa1-411a-be0f-5d6415d52bb5 
[INFO ] 2024-09-27 17:13:33.282 - [任务 3 - Copy - Copy][LocalhostMaster] - PDK connector node released: HazelcastSourcePdkDataNode-ea973c51-6aa1-411a-be0f-5d6415d52bb5 
[INFO ] 2024-09-27 17:13:33.282 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] schema data cleaned 
[INFO ] 2024-09-27 17:13:33.282 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] monitor closed 
[INFO ] 2024-09-27 17:13:33.286 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] close complete, cost 256 ms 
[INFO ] 2024-09-27 17:13:33.287 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] running status set to false 
[INFO ] 2024-09-27 17:13:33.345 - [任务 3 - Copy - Copy][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-935174d0-873e-4e47-8a00-9daa3d01bcf4 
[INFO ] 2024-09-27 17:13:33.345 - [任务 3 - Copy - Copy][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-935174d0-873e-4e47-8a00-9daa3d01bcf4 
[INFO ] 2024-09-27 17:13:33.345 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] schema data cleaned 
[INFO ] 2024-09-27 17:13:33.345 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] monitor closed 
[INFO ] 2024-09-27 17:13:33.346 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] close complete, cost 60 ms 
[INFO ] 2024-09-27 17:13:37.249 - [任务 3 - Copy - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 17:13:37.249 - [任务 3 - Copy - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@d9c9bfc 
[INFO ] 2024-09-27 17:13:37.365 - [任务 3 - Copy - Copy] - Stop task milestones: 66f665c6e239ae1b0e7cb333(任务 3 - Copy - Copy)  
[INFO ] 2024-09-27 17:13:37.365 - [任务 3 - Copy - Copy] - Stopped task aspect(s) 
[INFO ] 2024-09-27 17:13:37.366 - [任务 3 - Copy - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 17:13:37.377 - [任务 3 - Copy - Copy] - Remove memory task client succeed, task: 任务 3 - Copy - Copy[66f665c6e239ae1b0e7cb333] 
[INFO ] 2024-09-27 17:13:37.381 - [任务 3 - Copy - Copy] - Destroy memory task client cache succeed, task: 任务 3 - Copy - Copy[66f665c6e239ae1b0e7cb333] 
[INFO ] 2024-09-27 17:16:08.537 - [任务 3 - Copy - Copy] - Task initialization... 
[INFO ] 2024-09-27 17:16:08.650 - [任务 3 - Copy - Copy] - Start task milestones: 66f665c6e239ae1b0e7cb333(任务 3 - Copy - Copy) 
[INFO ] 2024-09-27 17:16:08.832 - [任务 3 - Copy - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 17:16:09.138 - [任务 3 - Copy - Copy] - The engine receives 任务 3 - Copy - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 17:16:09.371 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] start preload schema,table counts: 1 
[INFO ] 2024-09-27 17:16:09.371 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 17:16:09.374 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] start preload schema,table counts: 1 
[INFO ] 2024-09-27 17:16:09.374 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 17:16:10.192 - [任务 3 - Copy - Copy][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 17:16:10.193 - [任务 3 - Copy - Copy][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-09-27 17:16:11.132 - [任务 3 - Copy - Copy][LocalhostMaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1727428570985} and {hostPort=localhost:43306, time=1727399770977} 
[INFO ] 2024-09-27 17:16:11.153 - [任务 3 - Copy - Copy][LocalhostMaster] - Source node "LocalhostMaster" read batch size: 100 
[INFO ] 2024-09-27 17:16:11.154 - [任务 3 - Copy - Copy][LocalhostMaster] - Source node "LocalhostMaster" event queue capacity: 200 
[INFO ] 2024-09-27 17:16:11.155 - [任务 3 - Copy - Copy][LocalhostMaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-27 17:16:11.254 - [任务 3 - Copy - Copy][LocalhostMaster] - batch offset found: {"t2":{"batch_read_connector_status":"OVER"}},stream offset found: {"name":"239a51d6-1e01-499c-9e3b-a8fad45f6303","offset":{"{\"server\":\"239a51d6-1e01-499c-9e3b-a8fad45f6303\"}":"{\"ts_sec\":1727427047,\"file\":\"mysql-bin.000043\",\"pos\":333484037,\"server_id\":1}"}} 
[INFO ] 2024-09-27 17:16:11.255 - [任务 3 - Copy - Copy][LocalhostMaster] - Incremental sync starting... 
[INFO ] 2024-09-27 17:16:11.255 - [任务 3 - Copy - Copy][LocalhostMaster] - Initial sync completed 
[INFO ] 2024-09-27 17:16:11.255 - [任务 3 - Copy - Copy][LocalhostMaster] - Starting stream read, table list: [t2], offset: {"name":"239a51d6-1e01-499c-9e3b-a8fad45f6303","offset":{"{\"server\":\"239a51d6-1e01-499c-9e3b-a8fad45f6303\"}":"{\"ts_sec\":1727427047,\"file\":\"mysql-bin.000043\",\"pos\":333484037,\"server_id\":1}"}} 
[INFO ] 2024-09-27 17:16:19.753 - [任务 3 - Copy - Copy][LocalhostMaster] - Starting mysql cdc, server name: 239a51d6-1e01-499c-9e3b-a8fad45f6303 
[INFO ] 2024-09-27 17:16:19.754 - [任务 3 - Copy - Copy][LocalhostMaster] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"239a51d6-1e01-499c-9e3b-a8fad45f6303","offset":{"{\"server\":\"239a51d6-1e01-499c-9e3b-a8fad45f6303\"}":"{\"file\":\"mysql-bin.000043\",\"pos\":333483890,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 826112463
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 239a51d6-1e01-499c-9e3b-a8fad45f6303
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 33306
  threadName: Debezium-Mysql-Connector-239a51d6-1e01-499c-9e3b-a8fad45f6303
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 239a51d6-1e01-499c-9e3b-a8fad45f6303
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 17:16:19.962 - [任务 3 - Copy - Copy][LocalhostMaster] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-27 18:53:28.741 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] running status set to false 
[WARN ] 2024-09-27 18:53:28.752 - [任务 3 - Copy - Copy][LocalhostMaster] - Stop PDK connector node failed: Unknown PDK exception occur, com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-ea973c51-6aa1-411a-be0f-5d6415d52bb5 
[INFO ] 2024-09-27 18:53:28.752 - [任务 3 - Copy - Copy][LocalhostMaster] - PDK connector node released: HazelcastSourcePdkDataNode-ea973c51-6aa1-411a-be0f-5d6415d52bb5 
[INFO ] 2024-09-27 18:53:28.752 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] schema data cleaned 
[INFO ] 2024-09-27 18:53:28.752 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] monitor closed 
[INFO ] 2024-09-27 18:53:28.755 - [任务 3 - Copy - Copy][LocalhostMaster] - Node LocalhostMaster[ea973c51-6aa1-411a-be0f-5d6415d52bb5] close complete, cost 32 ms 
[INFO ] 2024-09-27 18:53:28.757 - [任务 3 - Copy - Copy][SourceMongo] - Node SourceMongo[935174d0-873e-4e47-8a00-9daa3d01bcf4] running status set to false 
