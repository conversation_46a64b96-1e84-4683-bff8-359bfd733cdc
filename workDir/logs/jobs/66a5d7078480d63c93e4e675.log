[INFO ] 2024-07-28 13:30:59.642 - [Heartbeat-SourceMongo] - Start task milestones: 66a5d7078480d63c93e4e675(Heartbeat-SourceMongo) 
[INFO ] 2024-07-28 13:31:00.492 - [Heartbeat-SourceMongo] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:31:00.493 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 13:31:00.497 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@730a7742 
[INFO ] 2024-07-28 13:31:00.500 - [Heartbeat-SourceMongo] - Stop task milestones: 66a5d7078480d63c93e4e675(Heartbeat-SourceMongo)  
[INFO ] 2024-07-28 13:31:00.627 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-28 13:31:00.629 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-28 13:31:00.684 - [Heartbeat-SourceMongo] - Node [id 051d72ac-2fe7-4015-abfa-787cdc9a0c5c, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 051d72ac-2fe7-4015-abfa-787cdc9a0c5c, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-28 13:33:19.941 - [Heartbeat-SourceMongo] - Start task milestones: 66a5d7078480d63c93e4e675(Heartbeat-SourceMongo) 
[INFO ] 2024-07-28 13:33:20.059 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:33:20.062 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 13:33:20.062 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5d19df48 
[INFO ] 2024-07-28 13:33:20.062 - [Heartbeat-SourceMongo] - Stop task milestones: 66a5d7078480d63c93e4e675(Heartbeat-SourceMongo)  
[INFO ] 2024-07-28 13:33:20.180 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-28 13:33:20.180 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-28 13:33:20.191 - [Heartbeat-SourceMongo] - Node [id 051d72ac-2fe7-4015-abfa-787cdc9a0c5c, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 051d72ac-2fe7-4015-abfa-787cdc9a0c5c, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-28 13:35:43.875 - [Heartbeat-SourceMongo] - Start task milestones: 66a5d7078480d63c93e4e675(Heartbeat-SourceMongo) 
[INFO ] 2024-07-28 13:35:43.984 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:35:43.984 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 13:35:43.985 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@774e6f65 
[INFO ] 2024-07-28 13:35:43.987 - [Heartbeat-SourceMongo] - Stop task milestones: 66a5d7078480d63c93e4e675(Heartbeat-SourceMongo)  
[INFO ] 2024-07-28 13:35:44.100 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-28 13:35:44.102 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-28 13:35:44.102 - [Heartbeat-SourceMongo] - Node [id 051d72ac-2fe7-4015-abfa-787cdc9a0c5c, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 051d72ac-2fe7-4015-abfa-787cdc9a0c5c, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-28 13:36:19.803 - [Heartbeat-SourceMongo] - Start task milestones: 66a5d7078480d63c93e4e675(Heartbeat-SourceMongo) 
[INFO ] 2024-07-28 13:36:19.835 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:36:19.836 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 13:36:19.836 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3d0df4d3 
[INFO ] 2024-07-28 13:36:19.888 - [Heartbeat-SourceMongo] - Stop task milestones: 66a5d7078480d63c93e4e675(Heartbeat-SourceMongo)  
[INFO ] 2024-07-28 13:36:19.945 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-28 13:36:19.945 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-28 13:36:19.945 - [Heartbeat-SourceMongo] - Node [id 051d72ac-2fe7-4015-abfa-787cdc9a0c5c, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 051d72ac-2fe7-4015-abfa-787cdc9a0c5c, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-28 13:36:45.375 - [Heartbeat-SourceMongo] - Start task milestones: 66a5d7078480d63c93e4e675(Heartbeat-SourceMongo) 
[INFO ] 2024-07-28 13:36:45.455 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:36:45.456 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 13:36:45.456 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@428450e5 
[INFO ] 2024-07-28 13:36:45.502 - [Heartbeat-SourceMongo] - Stop task milestones: 66a5d7078480d63c93e4e675(Heartbeat-SourceMongo)  
[INFO ] 2024-07-28 13:36:45.571 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-28 13:36:45.571 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-28 13:36:45.586 - [Heartbeat-SourceMongo] - Node [id 051d72ac-2fe7-4015-abfa-787cdc9a0c5c, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 051d72ac-2fe7-4015-abfa-787cdc9a0c5c, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-28 13:39:08.968 - [Heartbeat-SourceMongo] - Start task milestones: 66a5d7078480d63c93e4e675(Heartbeat-SourceMongo) 
[INFO ] 2024-07-28 13:39:09.031 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:39:09.031 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 13:39:09.032 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7cededa5 
[INFO ] 2024-07-28 13:39:09.032 - [Heartbeat-SourceMongo] - Stop task milestones: 66a5d7078480d63c93e4e675(Heartbeat-SourceMongo)  
[INFO ] 2024-07-28 13:39:09.158 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-28 13:39:09.158 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-28 13:39:09.159 - [Heartbeat-SourceMongo] - Node [id 051d72ac-2fe7-4015-abfa-787cdc9a0c5c, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 051d72ac-2fe7-4015-abfa-787cdc9a0c5c, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

