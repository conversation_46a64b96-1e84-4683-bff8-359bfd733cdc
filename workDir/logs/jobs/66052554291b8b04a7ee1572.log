[INFO ] 2024-03-28 16:08:00.368 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:00.368 - [orders(100)][e7bab3de-0b75-4b63-9f71-6576f86a0bb8] - Node e7bab3de-0b75-4b63-9f71-6576f86a0bb8[e7bab3de-0b75-4b63-9f71-6576f86a0bb8] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:00.368 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:00.373 - [orders(100)][e7bab3de-0b75-4b63-9f71-6576f86a0bb8] - Node e7bab3de-0b75-4b63-9f71-6576f86a0bb8[e7bab3de-0b75-4b63-9f71-6576f86a0bb8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:00.375 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 5 ms 
[INFO ] 2024-03-28 16:08:00.376 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:00.460 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 16:08:00.460 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:00.460 - [orders(100)][d54bb856-f57c-42be-9375-100e5e099fcc] - Node d54bb856-f57c-42be-9375-100e5e099fcc[d54bb856-f57c-42be-9375-100e5e099fcc] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:00.460 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:00.460 - [orders(100)][f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd] - Node f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd[f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:00.460 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:00.460 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:00.460 - [orders(100)][f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd] - Node f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd[f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:00.460 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:00.460 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:00.460 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:00.471 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 16:08:00.474 - [orders(100)][d54bb856-f57c-42be-9375-100e5e099fcc] - Node d54bb856-f57c-42be-9375-100e5e099fcc[d54bb856-f57c-42be-9375-100e5e099fcc] preload schema finished, cost 1 ms 
[ERROR] 2024-03-28 16:08:00.475 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@9e6396c error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@9e6396c error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@9e6396c error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 16:08:00.475 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 16:08:00.475 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:00.475 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@21e9c892 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@21e9c892 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@21e9c892 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-28 16:08:00.475 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@641c0439 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@641c0439 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@641c0439 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 16:08:00.956 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:00.973 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:00.973 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:00.973 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:00.974 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:00.974 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:00.974 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 15 ms 
[WARN ] 2024-03-28 16:08:01.106 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:01.106 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:01.114 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:01.114 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:01.114 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:01.114 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:01.261 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 9 ms 
[WARN ] 2024-03-28 16:08:01.261 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:01.274 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:01.274 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:01.274 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:01.274 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:01.274 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:01.479 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 10 ms 
[INFO ] 2024-03-28 16:08:01.541 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:01.541 - [orders(100)][aa7beb2f-b771-4881-8268-e7dccf9447b3] - Node aa7beb2f-b771-4881-8268-e7dccf9447b3[aa7beb2f-b771-4881-8268-e7dccf9447b3] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:01.541 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:01.541 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:01.542 - [orders(100)][aa7beb2f-b771-4881-8268-e7dccf9447b3] - Node aa7beb2f-b771-4881-8268-e7dccf9447b3[aa7beb2f-b771-4881-8268-e7dccf9447b3] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:01.542 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:01.559 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:01.725 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5f02f813 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5f02f813 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5f02f813 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 16:08:01.725 - [orders(100)][f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7] - Node f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7[f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:01.725 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:01.725 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:01.725 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:01.725 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 16:08:01.725 - [orders(100)][f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7] - Node f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7[f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:01.769 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 16:08:01.769 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:01.769 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:01.769 - [orders(100)][ea3676e6-2f85-45ed-9fed-8844c853fbe6] - Node ea3676e6-2f85-45ed-9fed-8844c853fbe6[ea3676e6-2f85-45ed-9fed-8844c853fbe6] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:01.769 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:01.769 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:01.770 - [orders(100)][ea3676e6-2f85-45ed-9fed-8844c853fbe6] - Node ea3676e6-2f85-45ed-9fed-8844c853fbe6[ea3676e6-2f85-45ed-9fed-8844c853fbe6] preload schema finished, cost 0 ms 
[ERROR] 2024-03-28 16:08:01.776 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@313d3c27 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@313d3c27 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@313d3c27 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 16:08:01.776 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:01.784 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@50ff517e error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@50ff517e error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@50ff517e error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 16:08:01.788 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:01.801 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:01.801 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:01.801 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:01.801 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:01.802 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:01.802 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 13 ms 
[WARN ] 2024-03-28 16:08:01.990 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:01.990 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:01.999 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:01.999 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:02.000 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:02.000 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:02.123 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 11 ms 
[WARN ] 2024-03-28 16:08:02.123 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:02.133 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:02.133 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:02.133 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:02.133 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:02.134 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:02.338 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 8 ms 
[INFO ] 2024-03-28 16:08:03.009 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:03.012 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:03.012 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:03.012 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:03.012 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:03.012 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:03.012 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:03.013 - [orders(100)][d54bb856-f57c-42be-9375-100e5e099fcc] - Node d54bb856-f57c-42be-9375-100e5e099fcc[d54bb856-f57c-42be-9375-100e5e099fcc] running status set to false 
[INFO ] 2024-03-28 16:08:03.013 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 8 ms 
[INFO ] 2024-03-28 16:08:03.028 - [orders(100)][d54bb856-f57c-42be-9375-100e5e099fcc] - Node d54bb856-f57c-42be-9375-100e5e099fcc[d54bb856-f57c-42be-9375-100e5e099fcc] schema data cleaned 
[INFO ] 2024-03-28 16:08:03.028 - [orders(100)][d54bb856-f57c-42be-9375-100e5e099fcc] - Node d54bb856-f57c-42be-9375-100e5e099fcc[d54bb856-f57c-42be-9375-100e5e099fcc] monitor closed 
[INFO ] 2024-03-28 16:08:03.029 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 8 ms 
[INFO ] 2024-03-28 16:08:03.029 - [orders(100)][e7bab3de-0b75-4b63-9f71-6576f86a0bb8] - Node e7bab3de-0b75-4b63-9f71-6576f86a0bb8[e7bab3de-0b75-4b63-9f71-6576f86a0bb8] running status set to false 
[INFO ] 2024-03-28 16:08:03.029 - [orders(100)][e7bab3de-0b75-4b63-9f71-6576f86a0bb8] - Node e7bab3de-0b75-4b63-9f71-6576f86a0bb8[e7bab3de-0b75-4b63-9f71-6576f86a0bb8] schema data cleaned 
[INFO ] 2024-03-28 16:08:03.029 - [orders(100)][e7bab3de-0b75-4b63-9f71-6576f86a0bb8] - Node e7bab3de-0b75-4b63-9f71-6576f86a0bb8[e7bab3de-0b75-4b63-9f71-6576f86a0bb8] monitor closed 
[INFO ] 2024-03-28 16:08:03.029 - [orders(100)][f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd] - Node f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd[f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd] running status set to false 
[INFO ] 2024-03-28 16:08:03.029 - [orders(100)][d54bb856-f57c-42be-9375-100e5e099fcc] - Node d54bb856-f57c-42be-9375-100e5e099fcc[d54bb856-f57c-42be-9375-100e5e099fcc] close complete, cost 8 ms 
[INFO ] 2024-03-28 16:08:03.029 - [orders(100)][f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd] - Node f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd[f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd] schema data cleaned 
[INFO ] 2024-03-28 16:08:03.029 - [orders(100)][f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd] - Node f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd[f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd] monitor closed 
[INFO ] 2024-03-28 16:08:03.030 - [orders(100)][e7bab3de-0b75-4b63-9f71-6576f86a0bb8] - Node e7bab3de-0b75-4b63-9f71-6576f86a0bb8[e7bab3de-0b75-4b63-9f71-6576f86a0bb8] close complete, cost 15 ms 
[INFO ] 2024-03-28 16:08:03.030 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:03.030 - [orders(100)][f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd] - Node f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd[f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd] close complete, cost 12 ms 
[INFO ] 2024-03-28 16:08:03.030 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:03.032 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 28 ms 
[INFO ] 2024-03-28 16:08:03.036 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-f6c40148-e3f5-4dd0-aa25-f51ef3a9aefd complete, cost 2611ms 
[INFO ] 2024-03-28 16:08:03.036 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-e7bab3de-0b75-4b63-9f71-6576f86a0bb8 complete, cost 2751ms 
[INFO ] 2024-03-28 16:08:03.036 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-d54bb856-f57c-42be-9375-100e5e099fcc complete, cost 2625ms 
[INFO ] 2024-03-28 16:08:03.754 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:03.754 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:03.754 - [orders(100)][c31c6f17-4afa-4851-8862-15f93fe039e8] - Node c31c6f17-4afa-4851-8862-15f93fe039e8[c31c6f17-4afa-4851-8862-15f93fe039e8] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:03.754 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:03.754 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:03.822 - [orders(100)][c31c6f17-4afa-4851-8862-15f93fe039e8] - Node c31c6f17-4afa-4851-8862-15f93fe039e8[c31c6f17-4afa-4851-8862-15f93fe039e8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:03.822 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:03.877 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1861311c error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1861311c error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1861311c error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 16:08:03.877 - [orders(100)][79626dad-5faa-4ce8-a374-6cc75da4981a] - Node 79626dad-5faa-4ce8-a374-6cc75da4981a[79626dad-5faa-4ce8-a374-6cc75da4981a] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:03.877 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:03.877 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:03.877 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:03.877 - [orders(100)][79626dad-5faa-4ce8-a374-6cc75da4981a] - Node 79626dad-5faa-4ce8-a374-6cc75da4981a[79626dad-5faa-4ce8-a374-6cc75da4981a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:03.877 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:03.920 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:03.921 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4ba397b0 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4ba397b0 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4ba397b0 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 16:08:04.033 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:04.033 - [orders(100)][d0bc9766-7aa6-4c56-ab9d-600f905145a4] - Node d0bc9766-7aa6-4c56-ab9d-600f905145a4[d0bc9766-7aa6-4c56-ab9d-600f905145a4] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:04.033 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:04.033 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:04.033 - [orders(100)][d0bc9766-7aa6-4c56-ab9d-600f905145a4] - Node d0bc9766-7aa6-4c56-ab9d-600f905145a4[d0bc9766-7aa6-4c56-ab9d-600f905145a4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:04.033 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[WARN ] 2024-03-28 16:08:04.043 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:04.073 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:04.073 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:04.073 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:04.073 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:04.074 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:04.075 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 26 ms 
[INFO ] 2024-03-28 16:08:04.089 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:04.089 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5275470e error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5275470e error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5275470e error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 16:08:04.123 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:04.124 - [orders(100)][aa7beb2f-b771-4881-8268-e7dccf9447b3] - Node aa7beb2f-b771-4881-8268-e7dccf9447b3[aa7beb2f-b771-4881-8268-e7dccf9447b3] running status set to false 
[INFO ] 2024-03-28 16:08:04.124 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:04.124 - [orders(100)][aa7beb2f-b771-4881-8268-e7dccf9447b3] - Node aa7beb2f-b771-4881-8268-e7dccf9447b3[aa7beb2f-b771-4881-8268-e7dccf9447b3] schema data cleaned 
[INFO ] 2024-03-28 16:08:04.124 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:04.124 - [orders(100)][aa7beb2f-b771-4881-8268-e7dccf9447b3] - Node aa7beb2f-b771-4881-8268-e7dccf9447b3[aa7beb2f-b771-4881-8268-e7dccf9447b3] monitor closed 
[INFO ] 2024-03-28 16:08:04.124 - [orders(100)][aa7beb2f-b771-4881-8268-e7dccf9447b3] - Node aa7beb2f-b771-4881-8268-e7dccf9447b3[aa7beb2f-b771-4881-8268-e7dccf9447b3] close complete, cost 6 ms 
[INFO ] 2024-03-28 16:08:04.124 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 7 ms 
[INFO ] 2024-03-28 16:08:04.125 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-aa7beb2f-b771-4881-8268-e7dccf9447b3 complete, cost 2619ms 
[WARN ] 2024-03-28 16:08:04.214 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:04.214 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:04.224 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:04.224 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:04.225 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:04.225 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:04.280 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 11 ms 
[WARN ] 2024-03-28 16:08:04.280 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:04.295 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:04.295 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:04.295 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:04.295 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:04.295 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:04.295 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:04.295 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:04.295 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:04.296 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 1 ms 
[INFO ] 2024-03-28 16:08:04.296 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 13 ms 
[INFO ] 2024-03-28 16:08:04.296 - [orders(100)][f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7] - Node f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7[f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7] running status set to false 
[INFO ] 2024-03-28 16:08:04.297 - [orders(100)][f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7] - Node f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7[f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7] schema data cleaned 
[INFO ] 2024-03-28 16:08:04.297 - [orders(100)][f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7] - Node f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7[f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7] monitor closed 
[INFO ] 2024-03-28 16:08:04.297 - [orders(100)][f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7] - Node f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7[f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:04.298 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-f363cfbd-ab59-4cc6-8a31-c8661cc4b8d7 complete, cost 2656ms 
[INFO ] 2024-03-28 16:08:04.308 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:04.308 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:04.308 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:04.308 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:04.314 - [orders(100)][ea3676e6-2f85-45ed-9fed-8844c853fbe6] - Node ea3676e6-2f85-45ed-9fed-8844c853fbe6[ea3676e6-2f85-45ed-9fed-8844c853fbe6] running status set to false 
[INFO ] 2024-03-28 16:08:04.314 - [orders(100)][ea3676e6-2f85-45ed-9fed-8844c853fbe6] - Node ea3676e6-2f85-45ed-9fed-8844c853fbe6[ea3676e6-2f85-45ed-9fed-8844c853fbe6] schema data cleaned 
[INFO ] 2024-03-28 16:08:04.314 - [orders(100)][ea3676e6-2f85-45ed-9fed-8844c853fbe6] - Node ea3676e6-2f85-45ed-9fed-8844c853fbe6[ea3676e6-2f85-45ed-9fed-8844c853fbe6] monitor closed 
[INFO ] 2024-03-28 16:08:04.314 - [orders(100)][ea3676e6-2f85-45ed-9fed-8844c853fbe6] - Node ea3676e6-2f85-45ed-9fed-8844c853fbe6[ea3676e6-2f85-45ed-9fed-8844c853fbe6] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:04.314 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-ea3676e6-2f85-45ed-9fed-8844c853fbe6 complete, cost 2583ms 
[INFO ] 2024-03-28 16:08:06.406 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:06.406 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:06.406 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:06.406 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 1 ms 
[INFO ] 2024-03-28 16:08:06.409 - [orders(100)][c31c6f17-4afa-4851-8862-15f93fe039e8] - Node c31c6f17-4afa-4851-8862-15f93fe039e8[c31c6f17-4afa-4851-8862-15f93fe039e8] running status set to false 
[INFO ] 2024-03-28 16:08:06.409 - [orders(100)][c31c6f17-4afa-4851-8862-15f93fe039e8] - Node c31c6f17-4afa-4851-8862-15f93fe039e8[c31c6f17-4afa-4851-8862-15f93fe039e8] schema data cleaned 
[INFO ] 2024-03-28 16:08:06.409 - [orders(100)][c31c6f17-4afa-4851-8862-15f93fe039e8] - Node c31c6f17-4afa-4851-8862-15f93fe039e8[c31c6f17-4afa-4851-8862-15f93fe039e8] monitor closed 
[INFO ] 2024-03-28 16:08:06.411 - [orders(100)][c31c6f17-4afa-4851-8862-15f93fe039e8] - Node c31c6f17-4afa-4851-8862-15f93fe039e8[c31c6f17-4afa-4851-8862-15f93fe039e8] close complete, cost 1 ms 
[INFO ] 2024-03-28 16:08:06.411 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-c31c6f17-4afa-4851-8862-15f93fe039e8 complete, cost 2700ms 
[INFO ] 2024-03-28 16:08:06.441 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:06.441 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:06.441 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:06.441 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:06.444 - [orders(100)][79626dad-5faa-4ce8-a374-6cc75da4981a] - Node 79626dad-5faa-4ce8-a374-6cc75da4981a[79626dad-5faa-4ce8-a374-6cc75da4981a] running status set to false 
[INFO ] 2024-03-28 16:08:06.444 - [orders(100)][79626dad-5faa-4ce8-a374-6cc75da4981a] - Node 79626dad-5faa-4ce8-a374-6cc75da4981a[79626dad-5faa-4ce8-a374-6cc75da4981a] schema data cleaned 
[INFO ] 2024-03-28 16:08:06.444 - [orders(100)][79626dad-5faa-4ce8-a374-6cc75da4981a] - Node 79626dad-5faa-4ce8-a374-6cc75da4981a[79626dad-5faa-4ce8-a374-6cc75da4981a] monitor closed 
[INFO ] 2024-03-28 16:08:06.445 - [orders(100)][79626dad-5faa-4ce8-a374-6cc75da4981a] - Node 79626dad-5faa-4ce8-a374-6cc75da4981a[79626dad-5faa-4ce8-a374-6cc75da4981a] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:06.445 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-79626dad-5faa-4ce8-a374-6cc75da4981a complete, cost 2618ms 
[INFO ] 2024-03-28 16:08:06.621 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:06.621 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:06.621 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:06.622 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 1 ms 
[INFO ] 2024-03-28 16:08:06.622 - [orders(100)][d0bc9766-7aa6-4c56-ab9d-600f905145a4] - Node d0bc9766-7aa6-4c56-ab9d-600f905145a4[d0bc9766-7aa6-4c56-ab9d-600f905145a4] running status set to false 
[INFO ] 2024-03-28 16:08:06.622 - [orders(100)][d0bc9766-7aa6-4c56-ab9d-600f905145a4] - Node d0bc9766-7aa6-4c56-ab9d-600f905145a4[d0bc9766-7aa6-4c56-ab9d-600f905145a4] schema data cleaned 
[INFO ] 2024-03-28 16:08:06.622 - [orders(100)][d0bc9766-7aa6-4c56-ab9d-600f905145a4] - Node d0bc9766-7aa6-4c56-ab9d-600f905145a4[d0bc9766-7aa6-4c56-ab9d-600f905145a4] monitor closed 
[INFO ] 2024-03-28 16:08:06.623 - [orders(100)][d0bc9766-7aa6-4c56-ab9d-600f905145a4] - Node d0bc9766-7aa6-4c56-ab9d-600f905145a4[d0bc9766-7aa6-4c56-ab9d-600f905145a4] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:06.623 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-d0bc9766-7aa6-4c56-ab9d-600f905145a4 complete, cost 2674ms 
[INFO ] 2024-03-28 16:08:07.480 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:07.480 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:07.480 - [orders(100)][f086733a-d210-4b42-b7d4-37896107b03b] - Node f086733a-d210-4b42-b7d4-37896107b03b[f086733a-d210-4b42-b7d4-37896107b03b] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:07.480 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 16:08:07.480 - [orders(100)][f086733a-d210-4b42-b7d4-37896107b03b] - Node f086733a-d210-4b42-b7d4-37896107b03b[f086733a-d210-4b42-b7d4-37896107b03b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:07.480 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:07.575 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:07.575 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@72dd2a31 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@72dd2a31 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@72dd2a31 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 16:08:07.749 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:07.762 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:07.762 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:07.763 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:07.763 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:07.763 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:07.763 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 11 ms 
[INFO ] 2024-03-28 16:08:08.271 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:08.271 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:08.272 - [orders(100)][38acfbd8-8932-4374-93a2-72c3233919af] - Node 38acfbd8-8932-4374-93a2-72c3233919af[38acfbd8-8932-4374-93a2-72c3233919af] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:08.272 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:08.272 - [orders(100)][38acfbd8-8932-4374-93a2-72c3233919af] - Node 38acfbd8-8932-4374-93a2-72c3233919af[38acfbd8-8932-4374-93a2-72c3233919af] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:08.272 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:08.282 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:08.283 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7f49c6ef error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7f49c6ef error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7f49c6ef error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 16:08:08.454 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:08.454 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:08.465 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:08.465 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:08.465 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:08.465 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:08.673 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 12 ms 
[INFO ] 2024-03-28 16:08:09.993 - [orders(100)][e04aab33-0828-4a40-9f13-05bae294b4b1] - Node e04aab33-0828-4a40-9f13-05bae294b4b1[e04aab33-0828-4a40-9f13-05bae294b4b1] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:09.993 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:09.993 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:09.993 - [orders(100)][e04aab33-0828-4a40-9f13-05bae294b4b1] - Node e04aab33-0828-4a40-9f13-05bae294b4b1[e04aab33-0828-4a40-9f13-05bae294b4b1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:09.993 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:09.993 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:10.018 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:10.113 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2d9decaf error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2d9decaf error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2d9decaf error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 16:08:10.114 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:10.114 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:10.114 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:10.115 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 2 ms 
[INFO ] 2024-03-28 16:08:10.115 - [orders(100)][f086733a-d210-4b42-b7d4-37896107b03b] - Node f086733a-d210-4b42-b7d4-37896107b03b[f086733a-d210-4b42-b7d4-37896107b03b] running status set to false 
[INFO ] 2024-03-28 16:08:10.115 - [orders(100)][f086733a-d210-4b42-b7d4-37896107b03b] - Node f086733a-d210-4b42-b7d4-37896107b03b[f086733a-d210-4b42-b7d4-37896107b03b] schema data cleaned 
[INFO ] 2024-03-28 16:08:10.115 - [orders(100)][f086733a-d210-4b42-b7d4-37896107b03b] - Node f086733a-d210-4b42-b7d4-37896107b03b[f086733a-d210-4b42-b7d4-37896107b03b] monitor closed 
[INFO ] 2024-03-28 16:08:10.116 - [orders(100)][f086733a-d210-4b42-b7d4-37896107b03b] - Node f086733a-d210-4b42-b7d4-37896107b03b[f086733a-d210-4b42-b7d4-37896107b03b] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:10.116 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-f086733a-d210-4b42-b7d4-37896107b03b complete, cost 2691ms 
[WARN ] 2024-03-28 16:08:10.232 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:10.232 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:10.242 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:10.242 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:10.242 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:10.242 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:10.447 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 10 ms 
[INFO ] 2024-03-28 16:08:10.722 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:10.722 - [orders(100)][32e91934-63e7-4002-ac1e-b08e32d87701] - Node 32e91934-63e7-4002-ac1e-b08e32d87701[32e91934-63e7-4002-ac1e-b08e32d87701] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:10.722 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:10.722 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:10.722 - [orders(100)][32e91934-63e7-4002-ac1e-b08e32d87701] - Node 32e91934-63e7-4002-ac1e-b08e32d87701[32e91934-63e7-4002-ac1e-b08e32d87701] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:10.722 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:10.805 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:10.805 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5a8a6aec error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5a8a6aec error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5a8a6aec error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 16:08:10.810 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:10.810 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:10.810 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:10.810 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:10.811 - [orders(100)][38acfbd8-8932-4374-93a2-72c3233919af] - Node 38acfbd8-8932-4374-93a2-72c3233919af[38acfbd8-8932-4374-93a2-72c3233919af] running status set to false 
[INFO ] 2024-03-28 16:08:10.811 - [orders(100)][38acfbd8-8932-4374-93a2-72c3233919af] - Node 38acfbd8-8932-4374-93a2-72c3233919af[38acfbd8-8932-4374-93a2-72c3233919af] schema data cleaned 
[INFO ] 2024-03-28 16:08:10.811 - [orders(100)][38acfbd8-8932-4374-93a2-72c3233919af] - Node 38acfbd8-8932-4374-93a2-72c3233919af[38acfbd8-8932-4374-93a2-72c3233919af] monitor closed 
[INFO ] 2024-03-28 16:08:10.811 - [orders(100)][38acfbd8-8932-4374-93a2-72c3233919af] - Node 38acfbd8-8932-4374-93a2-72c3233919af[38acfbd8-8932-4374-93a2-72c3233919af] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:10.811 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-38acfbd8-8932-4374-93a2-72c3233919af complete, cost 2570ms 
[WARN ] 2024-03-28 16:08:10.964 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:10.964 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:10.973 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:10.974 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:10.974 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:10.974 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:11.175 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 11 ms 
[INFO ] 2024-03-28 16:08:12.559 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:12.559 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:12.559 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:12.559 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:12.565 - [orders(100)][e04aab33-0828-4a40-9f13-05bae294b4b1] - Node e04aab33-0828-4a40-9f13-05bae294b4b1[e04aab33-0828-4a40-9f13-05bae294b4b1] running status set to false 
[INFO ] 2024-03-28 16:08:12.565 - [orders(100)][e04aab33-0828-4a40-9f13-05bae294b4b1] - Node e04aab33-0828-4a40-9f13-05bae294b4b1[e04aab33-0828-4a40-9f13-05bae294b4b1] schema data cleaned 
[INFO ] 2024-03-28 16:08:12.565 - [orders(100)][e04aab33-0828-4a40-9f13-05bae294b4b1] - Node e04aab33-0828-4a40-9f13-05bae294b4b1[e04aab33-0828-4a40-9f13-05bae294b4b1] monitor closed 
[INFO ] 2024-03-28 16:08:12.565 - [orders(100)][e04aab33-0828-4a40-9f13-05bae294b4b1] - Node e04aab33-0828-4a40-9f13-05bae294b4b1[e04aab33-0828-4a40-9f13-05bae294b4b1] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:12.597 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-e04aab33-0828-4a40-9f13-05bae294b4b1 complete, cost 2664ms 
[INFO ] 2024-03-28 16:08:12.597 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:12.597 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:12.598 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:12.598 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:12.598 - [orders(100)][f0b8a23e-7748-4d1c-86cd-e10c765949e1] - Node f0b8a23e-7748-4d1c-86cd-e10c765949e1[f0b8a23e-7748-4d1c-86cd-e10c765949e1] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:12.608 - [orders(100)][f0b8a23e-7748-4d1c-86cd-e10c765949e1] - Node f0b8a23e-7748-4d1c-86cd-e10c765949e1[f0b8a23e-7748-4d1c-86cd-e10c765949e1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:12.608 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:12.693 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@15855d5b error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@15855d5b error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@15855d5b error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 16:08:12.693 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:12.693 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:12.693 - [orders(100)][79af7f71-980b-4a2a-8a5e-fd8da697dbad] - Node 79af7f71-980b-4a2a-8a5e-fd8da697dbad[79af7f71-980b-4a2a-8a5e-fd8da697dbad] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:12.693 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:12.693 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:12.693 - [orders(100)][79af7f71-980b-4a2a-8a5e-fd8da697dbad] - Node 79af7f71-980b-4a2a-8a5e-fd8da697dbad[79af7f71-980b-4a2a-8a5e-fd8da697dbad] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:12.717 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:12.718 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2dba5ffc error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2dba5ffc error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2dba5ffc error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 16:08:12.804 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:12.804 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:12.812 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:12.813 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:12.813 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:12.813 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:12.961 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 11 ms 
[WARN ] 2024-03-28 16:08:12.961 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:12.970 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:12.970 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:12.970 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:12.970 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:12.970 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:12.971 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 9 ms 
[INFO ] 2024-03-28 16:08:13.349 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:13.350 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:13.350 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:13.350 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 2 ms 
[INFO ] 2024-03-28 16:08:13.356 - [orders(100)][32e91934-63e7-4002-ac1e-b08e32d87701] - Node 32e91934-63e7-4002-ac1e-b08e32d87701[32e91934-63e7-4002-ac1e-b08e32d87701] running status set to false 
[INFO ] 2024-03-28 16:08:13.356 - [orders(100)][32e91934-63e7-4002-ac1e-b08e32d87701] - Node 32e91934-63e7-4002-ac1e-b08e32d87701[32e91934-63e7-4002-ac1e-b08e32d87701] schema data cleaned 
[INFO ] 2024-03-28 16:08:13.357 - [orders(100)][32e91934-63e7-4002-ac1e-b08e32d87701] - Node 32e91934-63e7-4002-ac1e-b08e32d87701[32e91934-63e7-4002-ac1e-b08e32d87701] monitor closed 
[INFO ] 2024-03-28 16:08:13.357 - [orders(100)][32e91934-63e7-4002-ac1e-b08e32d87701] - Node 32e91934-63e7-4002-ac1e-b08e32d87701[32e91934-63e7-4002-ac1e-b08e32d87701] close complete, cost 4 ms 
[INFO ] 2024-03-28 16:08:13.561 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-32e91934-63e7-4002-ac1e-b08e32d87701 complete, cost 2680ms 
[INFO ] 2024-03-28 16:08:14.315 - [orders(100)][869c0a88-e41f-4f0b-8986-f84532b86cc1] - Node 869c0a88-e41f-4f0b-8986-f84532b86cc1[869c0a88-e41f-4f0b-8986-f84532b86cc1] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:14.315 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:14.315 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:14.315 - [orders(100)][869c0a88-e41f-4f0b-8986-f84532b86cc1] - Node 869c0a88-e41f-4f0b-8986-f84532b86cc1[869c0a88-e41f-4f0b-8986-f84532b86cc1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:14.315 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:14.339 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:14.343 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:14.439 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@22025e1 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@22025e1 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@22025e1 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 16:08:14.439 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:14.439 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:14.439 - [orders(100)][c50fb42c-cb43-41f0-8be7-f36751f76573] - Node c50fb42c-cb43-41f0-8be7-f36751f76573[c50fb42c-cb43-41f0-8be7-f36751f76573] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:14.439 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:14.440 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:14.440 - [orders(100)][c50fb42c-cb43-41f0-8be7-f36751f76573] - Node c50fb42c-cb43-41f0-8be7-f36751f76573[c50fb42c-cb43-41f0-8be7-f36751f76573] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:14.451 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:14.452 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6ca864ad error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6ca864ad error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6ca864ad error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 16:08:14.514 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:14.515 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:14.524 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:14.524 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:14.524 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:14.525 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:14.525 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 12 ms 
[WARN ] 2024-03-28 16:08:14.698 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:14.698 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:14.708 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:14.708 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:14.708 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:14.708 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:14.914 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 10 ms 
[INFO ] 2024-03-28 16:08:15.143 - [orders(100)][f0b8a23e-7748-4d1c-86cd-e10c765949e1] - Node f0b8a23e-7748-4d1c-86cd-e10c765949e1[f0b8a23e-7748-4d1c-86cd-e10c765949e1] running status set to false 
[INFO ] 2024-03-28 16:08:15.144 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:15.144 - [orders(100)][f0b8a23e-7748-4d1c-86cd-e10c765949e1] - Node f0b8a23e-7748-4d1c-86cd-e10c765949e1[f0b8a23e-7748-4d1c-86cd-e10c765949e1] schema data cleaned 
[INFO ] 2024-03-28 16:08:15.144 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:15.144 - [orders(100)][f0b8a23e-7748-4d1c-86cd-e10c765949e1] - Node f0b8a23e-7748-4d1c-86cd-e10c765949e1[f0b8a23e-7748-4d1c-86cd-e10c765949e1] monitor closed 
[INFO ] 2024-03-28 16:08:15.144 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:15.147 - [orders(100)][f0b8a23e-7748-4d1c-86cd-e10c765949e1] - Node f0b8a23e-7748-4d1c-86cd-e10c765949e1[f0b8a23e-7748-4d1c-86cd-e10c765949e1] close complete, cost 4 ms 
[INFO ] 2024-03-28 16:08:15.147 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 5 ms 
[INFO ] 2024-03-28 16:08:15.147 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-f0b8a23e-7748-4d1c-86cd-e10c765949e1 complete, cost 2582ms 
[INFO ] 2024-03-28 16:08:15.244 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:15.244 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:15.244 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:15.244 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 2 ms 
[INFO ] 2024-03-28 16:08:15.246 - [orders(100)][79af7f71-980b-4a2a-8a5e-fd8da697dbad] - Node 79af7f71-980b-4a2a-8a5e-fd8da697dbad[79af7f71-980b-4a2a-8a5e-fd8da697dbad] running status set to false 
[INFO ] 2024-03-28 16:08:15.246 - [orders(100)][79af7f71-980b-4a2a-8a5e-fd8da697dbad] - Node 79af7f71-980b-4a2a-8a5e-fd8da697dbad[79af7f71-980b-4a2a-8a5e-fd8da697dbad] schema data cleaned 
[INFO ] 2024-03-28 16:08:15.246 - [orders(100)][79af7f71-980b-4a2a-8a5e-fd8da697dbad] - Node 79af7f71-980b-4a2a-8a5e-fd8da697dbad[79af7f71-980b-4a2a-8a5e-fd8da697dbad] monitor closed 
[INFO ] 2024-03-28 16:08:15.246 - [orders(100)][79af7f71-980b-4a2a-8a5e-fd8da697dbad] - Node 79af7f71-980b-4a2a-8a5e-fd8da697dbad[79af7f71-980b-4a2a-8a5e-fd8da697dbad] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:15.443 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-79af7f71-980b-4a2a-8a5e-fd8da697dbad complete, cost 2583ms 
[INFO ] 2024-03-28 16:08:15.443 - [orders(100)][2628d0a9-1092-44fb-ba5a-b7b9b57e8175] - Node 2628d0a9-1092-44fb-ba5a-b7b9b57e8175[2628d0a9-1092-44fb-ba5a-b7b9b57e8175] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:15.443 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:15.443 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:15.443 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:15.443 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:15.443 - [orders(100)][2628d0a9-1092-44fb-ba5a-b7b9b57e8175] - Node 2628d0a9-1092-44fb-ba5a-b7b9b57e8175[2628d0a9-1092-44fb-ba5a-b7b9b57e8175] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:15.482 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:15.482 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3e70ff2e error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3e70ff2e error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3e70ff2e error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 16:08:15.594 - [orders(100)][f68b0a7a-ca1d-40e3-aa83-30a44fafba6c] - Node f68b0a7a-ca1d-40e3-aa83-30a44fafba6c[f68b0a7a-ca1d-40e3-aa83-30a44fafba6c] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:15.594 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:15.594 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:15.594 - [orders(100)][f68b0a7a-ca1d-40e3-aa83-30a44fafba6c] - Node f68b0a7a-ca1d-40e3-aa83-30a44fafba6c[f68b0a7a-ca1d-40e3-aa83-30a44fafba6c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:15.594 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:15.594 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:15.631 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:15.681 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@e982441 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@e982441 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@e982441 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 16:08:15.682 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:15.699 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:15.699 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:15.699 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:15.699 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:15.699 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:15.700 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 14 ms 
[WARN ] 2024-03-28 16:08:15.835 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:15.835 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:15.845 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:15.845 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:15.845 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:15.845 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:16.049 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 11 ms 
[INFO ] 2024-03-28 16:08:16.878 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:16.878 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:16.878 - [orders(100)][869c0a88-e41f-4f0b-8986-f84532b86cc1] - Node 869c0a88-e41f-4f0b-8986-f84532b86cc1[869c0a88-e41f-4f0b-8986-f84532b86cc1] running status set to false 
[INFO ] 2024-03-28 16:08:16.878 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:16.878 - [orders(100)][869c0a88-e41f-4f0b-8986-f84532b86cc1] - Node 869c0a88-e41f-4f0b-8986-f84532b86cc1[869c0a88-e41f-4f0b-8986-f84532b86cc1] schema data cleaned 
[INFO ] 2024-03-28 16:08:16.878 - [orders(100)][869c0a88-e41f-4f0b-8986-f84532b86cc1] - Node 869c0a88-e41f-4f0b-8986-f84532b86cc1[869c0a88-e41f-4f0b-8986-f84532b86cc1] monitor closed 
[INFO ] 2024-03-28 16:08:16.879 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 1 ms 
[INFO ] 2024-03-28 16:08:16.879 - [orders(100)][869c0a88-e41f-4f0b-8986-f84532b86cc1] - Node 869c0a88-e41f-4f0b-8986-f84532b86cc1[869c0a88-e41f-4f0b-8986-f84532b86cc1] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:16.971 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-869c0a88-e41f-4f0b-8986-f84532b86cc1 complete, cost 2598ms 
[INFO ] 2024-03-28 16:08:16.971 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:16.972 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:16.972 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:16.972 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:16.976 - [orders(100)][c50fb42c-cb43-41f0-8be7-f36751f76573] - Node c50fb42c-cb43-41f0-8be7-f36751f76573[c50fb42c-cb43-41f0-8be7-f36751f76573] running status set to false 
[INFO ] 2024-03-28 16:08:16.976 - [orders(100)][c50fb42c-cb43-41f0-8be7-f36751f76573] - Node c50fb42c-cb43-41f0-8be7-f36751f76573[c50fb42c-cb43-41f0-8be7-f36751f76573] schema data cleaned 
[INFO ] 2024-03-28 16:08:16.976 - [orders(100)][c50fb42c-cb43-41f0-8be7-f36751f76573] - Node c50fb42c-cb43-41f0-8be7-f36751f76573[c50fb42c-cb43-41f0-8be7-f36751f76573] monitor closed 
[INFO ] 2024-03-28 16:08:16.976 - [orders(100)][c50fb42c-cb43-41f0-8be7-f36751f76573] - Node c50fb42c-cb43-41f0-8be7-f36751f76573[c50fb42c-cb43-41f0-8be7-f36751f76573] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:16.977 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-c50fb42c-cb43-41f0-8be7-f36751f76573 complete, cost 2576ms 
[INFO ] 2024-03-28 16:08:17.182 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:17.182 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:17.182 - [orders(100)][ad0eef17-1719-4332-bf35-2ef70873e018] - Node ad0eef17-1719-4332-bf35-2ef70873e018[ad0eef17-1719-4332-bf35-2ef70873e018] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:17.183 - [orders(100)][ad0eef17-1719-4332-bf35-2ef70873e018] - Node ad0eef17-1719-4332-bf35-2ef70873e018[ad0eef17-1719-4332-bf35-2ef70873e018] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:17.183 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:17.183 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:17.230 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:17.230 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@56f3c5fe error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@56f3c5fe error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@56f3c5fe error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 16:08:17.400 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:17.400 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:17.408 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:17.408 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:17.408 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:17.409 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:17.649 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 10 ms 
[INFO ] 2024-03-28 16:08:17.825 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:17.825 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:17.825 - [orders(100)][a1692dfa-7ca2-4b9b-80f7-2070f4102261] - Node a1692dfa-7ca2-4b9b-80f7-2070f4102261[a1692dfa-7ca2-4b9b-80f7-2070f4102261] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:17.825 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:17.825 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:17.825 - [orders(100)][a1692dfa-7ca2-4b9b-80f7-2070f4102261] - Node a1692dfa-7ca2-4b9b-80f7-2070f4102261[a1692dfa-7ca2-4b9b-80f7-2070f4102261] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:17.835 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:17.838 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@51d267f3 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@51d267f3 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@51d267f3 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 16:08:18.010 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:18.010 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:18.010 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:18.011 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 3 ms 
[INFO ] 2024-03-28 16:08:18.011 - [orders(100)][2628d0a9-1092-44fb-ba5a-b7b9b57e8175] - Node 2628d0a9-1092-44fb-ba5a-b7b9b57e8175[2628d0a9-1092-44fb-ba5a-b7b9b57e8175] running status set to false 
[INFO ] 2024-03-28 16:08:18.011 - [orders(100)][2628d0a9-1092-44fb-ba5a-b7b9b57e8175] - Node 2628d0a9-1092-44fb-ba5a-b7b9b57e8175[2628d0a9-1092-44fb-ba5a-b7b9b57e8175] schema data cleaned 
[INFO ] 2024-03-28 16:08:18.011 - [orders(100)][2628d0a9-1092-44fb-ba5a-b7b9b57e8175] - Node 2628d0a9-1092-44fb-ba5a-b7b9b57e8175[2628d0a9-1092-44fb-ba5a-b7b9b57e8175] monitor closed 
[INFO ] 2024-03-28 16:08:18.011 - [orders(100)][2628d0a9-1092-44fb-ba5a-b7b9b57e8175] - Node 2628d0a9-1092-44fb-ba5a-b7b9b57e8175[2628d0a9-1092-44fb-ba5a-b7b9b57e8175] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:18.014 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-2628d0a9-1092-44fb-ba5a-b7b9b57e8175 complete, cost 2621ms 
[WARN ] 2024-03-28 16:08:18.014 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:18.033 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:18.033 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:18.033 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:18.033 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:18.034 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:18.034 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 16 ms 
[INFO ] 2024-03-28 16:08:18.183 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:18.183 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:18.183 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:18.186 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:18.186 - [orders(100)][f68b0a7a-ca1d-40e3-aa83-30a44fafba6c] - Node f68b0a7a-ca1d-40e3-aa83-30a44fafba6c[f68b0a7a-ca1d-40e3-aa83-30a44fafba6c] running status set to false 
[INFO ] 2024-03-28 16:08:18.186 - [orders(100)][f68b0a7a-ca1d-40e3-aa83-30a44fafba6c] - Node f68b0a7a-ca1d-40e3-aa83-30a44fafba6c[f68b0a7a-ca1d-40e3-aa83-30a44fafba6c] schema data cleaned 
[INFO ] 2024-03-28 16:08:18.186 - [orders(100)][f68b0a7a-ca1d-40e3-aa83-30a44fafba6c] - Node f68b0a7a-ca1d-40e3-aa83-30a44fafba6c[f68b0a7a-ca1d-40e3-aa83-30a44fafba6c] monitor closed 
[INFO ] 2024-03-28 16:08:18.187 - [orders(100)][f68b0a7a-ca1d-40e3-aa83-30a44fafba6c] - Node f68b0a7a-ca1d-40e3-aa83-30a44fafba6c[f68b0a7a-ca1d-40e3-aa83-30a44fafba6c] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:18.187 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-f68b0a7a-ca1d-40e3-aa83-30a44fafba6c complete, cost 2639ms 
[INFO ] 2024-03-28 16:08:19.749 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:19.749 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:19.749 - [orders(100)][ad0eef17-1719-4332-bf35-2ef70873e018] - Node ad0eef17-1719-4332-bf35-2ef70873e018[ad0eef17-1719-4332-bf35-2ef70873e018] running status set to false 
[INFO ] 2024-03-28 16:08:19.749 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:19.749 - [orders(100)][ad0eef17-1719-4332-bf35-2ef70873e018] - Node ad0eef17-1719-4332-bf35-2ef70873e018[ad0eef17-1719-4332-bf35-2ef70873e018] schema data cleaned 
[INFO ] 2024-03-28 16:08:19.749 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 1 ms 
[INFO ] 2024-03-28 16:08:19.749 - [orders(100)][ad0eef17-1719-4332-bf35-2ef70873e018] - Node ad0eef17-1719-4332-bf35-2ef70873e018[ad0eef17-1719-4332-bf35-2ef70873e018] monitor closed 
[INFO ] 2024-03-28 16:08:19.749 - [orders(100)][ad0eef17-1719-4332-bf35-2ef70873e018] - Node ad0eef17-1719-4332-bf35-2ef70873e018[ad0eef17-1719-4332-bf35-2ef70873e018] close complete, cost 1 ms 
[INFO ] 2024-03-28 16:08:19.952 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-ad0eef17-1719-4332-bf35-2ef70873e018 complete, cost 2612ms 
[INFO ] 2024-03-28 16:08:20.356 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:20.357 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:20.357 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:20.357 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 2 ms 
[INFO ] 2024-03-28 16:08:20.360 - [orders(100)][a1692dfa-7ca2-4b9b-80f7-2070f4102261] - Node a1692dfa-7ca2-4b9b-80f7-2070f4102261[a1692dfa-7ca2-4b9b-80f7-2070f4102261] running status set to false 
[INFO ] 2024-03-28 16:08:20.361 - [orders(100)][a1692dfa-7ca2-4b9b-80f7-2070f4102261] - Node a1692dfa-7ca2-4b9b-80f7-2070f4102261[a1692dfa-7ca2-4b9b-80f7-2070f4102261] schema data cleaned 
[INFO ] 2024-03-28 16:08:20.361 - [orders(100)][a1692dfa-7ca2-4b9b-80f7-2070f4102261] - Node a1692dfa-7ca2-4b9b-80f7-2070f4102261[a1692dfa-7ca2-4b9b-80f7-2070f4102261] monitor closed 
[INFO ] 2024-03-28 16:08:20.363 - [orders(100)][a1692dfa-7ca2-4b9b-80f7-2070f4102261] - Node a1692dfa-7ca2-4b9b-80f7-2070f4102261[a1692dfa-7ca2-4b9b-80f7-2070f4102261] close complete, cost 1 ms 
[INFO ] 2024-03-28 16:08:20.364 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-a1692dfa-7ca2-4b9b-80f7-2070f4102261 complete, cost 2579ms 
[INFO ] 2024-03-28 16:08:20.547 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:20.547 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:20.547 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:20.547 - [orders(100)][758e6114-2cb9-49a7-9072-d29cdcb61426] - Node 758e6114-2cb9-49a7-9072-d29cdcb61426[758e6114-2cb9-49a7-9072-d29cdcb61426] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:20.548 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:20.548 - [orders(100)][758e6114-2cb9-49a7-9072-d29cdcb61426] - Node 758e6114-2cb9-49a7-9072-d29cdcb61426[758e6114-2cb9-49a7-9072-d29cdcb61426] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:20.563 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:20.564 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5ea75c10 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5ea75c10 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5ea75c10 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 16:08:20.700 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:20.700 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:20.700 - [orders(100)][4d7de084-2282-4075-b592-5e2a29b7e1d4] - Node 4d7de084-2282-4075-b592-5e2a29b7e1d4[4d7de084-2282-4075-b592-5e2a29b7e1d4] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:20.700 - [orders(100)][4d7de084-2282-4075-b592-5e2a29b7e1d4] - Node 4d7de084-2282-4075-b592-5e2a29b7e1d4[4d7de084-2282-4075-b592-5e2a29b7e1d4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:20.700 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:20.700 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:20.707 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:20.736 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7575ca1e error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7575ca1e error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7575ca1e error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 16:08:20.741 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:20.741 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:20.749 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:20.749 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:20.749 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:20.750 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:20.750 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 10 ms 
[WARN ] 2024-03-28 16:08:20.894 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:20.894 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:20.899 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:20.899 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:20.899 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:20.900 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:21.105 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 6 ms 
[INFO ] 2024-03-28 16:08:21.903 - [orders(100)][a0450d43-bdc7-4484-8025-bae15f0208fb] - Node a0450d43-bdc7-4484-8025-bae15f0208fb[a0450d43-bdc7-4484-8025-bae15f0208fb] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:21.904 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:21.904 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:21.904 - [orders(100)][a0450d43-bdc7-4484-8025-bae15f0208fb] - Node a0450d43-bdc7-4484-8025-bae15f0208fb[a0450d43-bdc7-4484-8025-bae15f0208fb] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:21.904 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:21.904 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:21.944 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:22.149 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5c94b131 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5c94b131 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5c94b131 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 16:08:22.150 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:22.150 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:22.166 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:22.166 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:22.166 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:22.166 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:22.373 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 17 ms 
[INFO ] 2024-03-28 16:08:22.872 - [orders(100)][1e7476d0-8f6f-4d48-8be6-6f5901233d5e] - Node 1e7476d0-8f6f-4d48-8be6-6f5901233d5e[1e7476d0-8f6f-4d48-8be6-6f5901233d5e] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:22.872 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:22.872 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:22.872 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:22.873 - [orders(100)][1e7476d0-8f6f-4d48-8be6-6f5901233d5e] - Node 1e7476d0-8f6f-4d48-8be6-6f5901233d5e[1e7476d0-8f6f-4d48-8be6-6f5901233d5e] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 16:08:22.873 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:22.932 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:23.088 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1c9a12d1 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1c9a12d1 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1c9a12d1 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 16:08:23.089 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:23.089 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:23.089 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:23.090 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 1 ms 
[INFO ] 2024-03-28 16:08:23.094 - [orders(100)][758e6114-2cb9-49a7-9072-d29cdcb61426] - Node 758e6114-2cb9-49a7-9072-d29cdcb61426[758e6114-2cb9-49a7-9072-d29cdcb61426] running status set to false 
[INFO ] 2024-03-28 16:08:23.094 - [orders(100)][758e6114-2cb9-49a7-9072-d29cdcb61426] - Node 758e6114-2cb9-49a7-9072-d29cdcb61426[758e6114-2cb9-49a7-9072-d29cdcb61426] schema data cleaned 
[INFO ] 2024-03-28 16:08:23.094 - [orders(100)][758e6114-2cb9-49a7-9072-d29cdcb61426] - Node 758e6114-2cb9-49a7-9072-d29cdcb61426[758e6114-2cb9-49a7-9072-d29cdcb61426] monitor closed 
[INFO ] 2024-03-28 16:08:23.094 - [orders(100)][758e6114-2cb9-49a7-9072-d29cdcb61426] - Node 758e6114-2cb9-49a7-9072-d29cdcb61426[758e6114-2cb9-49a7-9072-d29cdcb61426] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:23.131 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-758e6114-2cb9-49a7-9072-d29cdcb61426 complete, cost 2588ms 
[WARN ] 2024-03-28 16:08:23.131 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:23.151 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:23.151 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:23.151 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:23.151 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:23.151 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:23.234 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 16 ms 
[INFO ] 2024-03-28 16:08:23.234 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:23.234 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:23.234 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:23.234 - [orders(100)][4d7de084-2282-4075-b592-5e2a29b7e1d4] - Node 4d7de084-2282-4075-b592-5e2a29b7e1d4[4d7de084-2282-4075-b592-5e2a29b7e1d4] running status set to false 
[INFO ] 2024-03-28 16:08:23.234 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:23.235 - [orders(100)][4d7de084-2282-4075-b592-5e2a29b7e1d4] - Node 4d7de084-2282-4075-b592-5e2a29b7e1d4[4d7de084-2282-4075-b592-5e2a29b7e1d4] schema data cleaned 
[INFO ] 2024-03-28 16:08:23.235 - [orders(100)][4d7de084-2282-4075-b592-5e2a29b7e1d4] - Node 4d7de084-2282-4075-b592-5e2a29b7e1d4[4d7de084-2282-4075-b592-5e2a29b7e1d4] monitor closed 
[INFO ] 2024-03-28 16:08:23.235 - [orders(100)][4d7de084-2282-4075-b592-5e2a29b7e1d4] - Node 4d7de084-2282-4075-b592-5e2a29b7e1d4[4d7de084-2282-4075-b592-5e2a29b7e1d4] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:23.235 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-4d7de084-2282-4075-b592-5e2a29b7e1d4 complete, cost 2617ms 
[INFO ] 2024-03-28 16:08:23.893 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:23.893 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:23.894 - [orders(100)][eb3b73a2-797f-4b0b-b561-1e222494b65a] - Node eb3b73a2-797f-4b0b-b561-1e222494b65a[eb3b73a2-797f-4b0b-b561-1e222494b65a] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:23.894 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:23.894 - [orders(100)][eb3b73a2-797f-4b0b-b561-1e222494b65a] - Node eb3b73a2-797f-4b0b-b561-1e222494b65a[eb3b73a2-797f-4b0b-b561-1e222494b65a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:23.894 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:23.899 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:24.105 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@85d3d2a error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@85d3d2a error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@85d3d2a error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 16:08:24.109 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:24.109 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:24.116 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:24.116 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:24.116 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:24.116 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:24.116 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 8 ms 
[INFO ] 2024-03-28 16:08:24.483 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:24.484 - [orders(100)][a0450d43-bdc7-4484-8025-bae15f0208fb] - Node a0450d43-bdc7-4484-8025-bae15f0208fb[a0450d43-bdc7-4484-8025-bae15f0208fb] running status set to false 
[INFO ] 2024-03-28 16:08:24.484 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:24.484 - [orders(100)][a0450d43-bdc7-4484-8025-bae15f0208fb] - Node a0450d43-bdc7-4484-8025-bae15f0208fb[a0450d43-bdc7-4484-8025-bae15f0208fb] schema data cleaned 
[INFO ] 2024-03-28 16:08:24.484 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:24.485 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 3 ms 
[INFO ] 2024-03-28 16:08:24.485 - [orders(100)][a0450d43-bdc7-4484-8025-bae15f0208fb] - Node a0450d43-bdc7-4484-8025-bae15f0208fb[a0450d43-bdc7-4484-8025-bae15f0208fb] monitor closed 
[INFO ] 2024-03-28 16:08:24.485 - [orders(100)][a0450d43-bdc7-4484-8025-bae15f0208fb] - Node a0450d43-bdc7-4484-8025-bae15f0208fb[a0450d43-bdc7-4484-8025-bae15f0208fb] close complete, cost 2 ms 
[INFO ] 2024-03-28 16:08:24.489 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-a0450d43-bdc7-4484-8025-bae15f0208fb complete, cost 2699ms 
[INFO ] 2024-03-28 16:08:25.479 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:25.479 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:25.480 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:25.480 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 1 ms 
[INFO ] 2024-03-28 16:08:25.480 - [orders(100)][1e7476d0-8f6f-4d48-8be6-6f5901233d5e] - Node 1e7476d0-8f6f-4d48-8be6-6f5901233d5e[1e7476d0-8f6f-4d48-8be6-6f5901233d5e] running status set to false 
[INFO ] 2024-03-28 16:08:25.480 - [orders(100)][1e7476d0-8f6f-4d48-8be6-6f5901233d5e] - Node 1e7476d0-8f6f-4d48-8be6-6f5901233d5e[1e7476d0-8f6f-4d48-8be6-6f5901233d5e] schema data cleaned 
[INFO ] 2024-03-28 16:08:25.481 - [orders(100)][1e7476d0-8f6f-4d48-8be6-6f5901233d5e] - Node 1e7476d0-8f6f-4d48-8be6-6f5901233d5e[1e7476d0-8f6f-4d48-8be6-6f5901233d5e] monitor closed 
[INFO ] 2024-03-28 16:08:25.481 - [orders(100)][1e7476d0-8f6f-4d48-8be6-6f5901233d5e] - Node 1e7476d0-8f6f-4d48-8be6-6f5901233d5e[1e7476d0-8f6f-4d48-8be6-6f5901233d5e] close complete, cost 0 ms 
[INFO ] 2024-03-28 16:08:25.482 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-1e7476d0-8f6f-4d48-8be6-6f5901233d5e complete, cost 2666ms 
[INFO ] 2024-03-28 16:08:25.706 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:25.706 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] start preload schema,table counts: 1 
[INFO ] 2024-03-28 16:08:25.706 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:25.707 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:25.707 - [orders(100)][b63dff45-7fce-46c1-9294-346f23c6d646] - Node b63dff45-7fce-46c1-9294-346f23c6d646[b63dff45-7fce-46c1-9294-346f23c6d646] start preload schema,table counts: 0 
[INFO ] 2024-03-28 16:08:25.715 - [orders(100)][b63dff45-7fce-46c1-9294-346f23c6d646] - Node b63dff45-7fce-46c1-9294-346f23c6d646[b63dff45-7fce-46c1-9294-346f23c6d646] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 16:08:25.715 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 16:08:25.902 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4e5487d9 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4e5487d9 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4e5487d9 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 16:08:25.908 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 16:08:25.908 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] running status set to false 
[INFO ] 2024-03-28 16:08:25.918 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:25.918 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-69a6879a-7abe-4cce-b2eb-7c7834cdc262 
[INFO ] 2024-03-28 16:08:25.918 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] schema data cleaned 
[INFO ] 2024-03-28 16:08:25.919 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] monitor closed 
[INFO ] 2024-03-28 16:08:25.919 - [orders(100)][Order Details] - Node Order Details[69a6879a-7abe-4cce-b2eb-7c7834cdc262] close complete, cost 13 ms 
[INFO ] 2024-03-28 16:08:26.439 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:26.439 - [orders(100)][eb3b73a2-797f-4b0b-b561-1e222494b65a] - Node eb3b73a2-797f-4b0b-b561-1e222494b65a[eb3b73a2-797f-4b0b-b561-1e222494b65a] running status set to false 
[INFO ] 2024-03-28 16:08:26.440 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:26.440 - [orders(100)][eb3b73a2-797f-4b0b-b561-1e222494b65a] - Node eb3b73a2-797f-4b0b-b561-1e222494b65a[eb3b73a2-797f-4b0b-b561-1e222494b65a] schema data cleaned 
[INFO ] 2024-03-28 16:08:26.441 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:26.441 - [orders(100)][eb3b73a2-797f-4b0b-b561-1e222494b65a] - Node eb3b73a2-797f-4b0b-b561-1e222494b65a[eb3b73a2-797f-4b0b-b561-1e222494b65a] monitor closed 
[INFO ] 2024-03-28 16:08:26.441 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 6 ms 
[INFO ] 2024-03-28 16:08:26.441 - [orders(100)][eb3b73a2-797f-4b0b-b561-1e222494b65a] - Node eb3b73a2-797f-4b0b-b561-1e222494b65a[eb3b73a2-797f-4b0b-b561-1e222494b65a] close complete, cost 5 ms 
[INFO ] 2024-03-28 16:08:26.443 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-eb3b73a2-797f-4b0b-b561-1e222494b65a complete, cost 2588ms 
[INFO ] 2024-03-28 16:08:28.269 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] running status set to false 
[INFO ] 2024-03-28 16:08:28.269 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] schema data cleaned 
[INFO ] 2024-03-28 16:08:28.269 - [orders(100)][b63dff45-7fce-46c1-9294-346f23c6d646] - Node b63dff45-7fce-46c1-9294-346f23c6d646[b63dff45-7fce-46c1-9294-346f23c6d646] running status set to false 
[INFO ] 2024-03-28 16:08:28.269 - [orders(100)][b63dff45-7fce-46c1-9294-346f23c6d646] - Node b63dff45-7fce-46c1-9294-346f23c6d646[b63dff45-7fce-46c1-9294-346f23c6d646] schema data cleaned 
[INFO ] 2024-03-28 16:08:28.271 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] monitor closed 
[INFO ] 2024-03-28 16:08:28.272 - [orders(100)][b63dff45-7fce-46c1-9294-346f23c6d646] - Node b63dff45-7fce-46c1-9294-346f23c6d646[b63dff45-7fce-46c1-9294-346f23c6d646] monitor closed 
[INFO ] 2024-03-28 16:08:28.272 - [orders(100)][Order Details] - Node Order Details[467cbef9-189f-42dc-bc56-442658f62214] close complete, cost 13 ms 
[INFO ] 2024-03-28 16:08:28.272 - [orders(100)][b63dff45-7fce-46c1-9294-346f23c6d646] - Node b63dff45-7fce-46c1-9294-346f23c6d646[b63dff45-7fce-46c1-9294-346f23c6d646] close complete, cost 3 ms 
[INFO ] 2024-03-28 16:08:28.272 - [orders(100)] - load tapTable task 66052554291b8b04a7ee1572-b63dff45-7fce-46c1-9294-346f23c6d646 complete, cost 2595ms 
