[INFO ] 2024-10-10 12:16:52.762 - [任务 2] - load tapTable task 6707547d5fe35676bdffab43-2cb9d683-18f9-4185-92ff-49fab1c51bbd complete, cost 13012ms 
[INFO ] 2024-10-10 12:17:08.791 - [任务 2] - load tapTable task 6707547d5fe35676bdffab43-7872251a-53d8-45ca-9761-42451b3aa1c8 complete, cost 11422ms 
[INFO ] 2024-10-10 12:17:14.241 - [任务 2] - load tapTable task 6707547d5fe35676bdffab43-d93a0722-a122-4167-978d-7e85fb0b1121 complete, cost 16784ms 
[INFO ] 2024-10-10 12:18:08.665 - [任务 2] - load tapTable task 6707547d5fe35676bdffab43-5b454001-d107-4742-bee1-40fc5974a103 complete, cost 11242ms 
[INFO ] 2024-10-10 12:18:21.522 - [任务 2] - load tapTable task 6707547d5fe35676bdffab43-6a3daf6d-8a5f-4ffb-a7d5-50df542c5296 complete, cost 11315ms 
[INFO ] 2024-10-10 12:18:34.764 - [任务 2] - load tapTable task 6707547d5fe35676bdffab43-2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b complete, cost 11297ms 
[INFO ] 2024-10-10 12:18:59.775 - [任务 2] - load tapTable task 6707547d5fe35676bdffab43-234bb5c7-2dfb-49c0-b71d-a91a0116cf92 complete, cost 13019ms 
[INFO ] 2024-10-10 12:19:05.065 - [任务 2] - load tapTable task 6707547d5fe35676bdffab43-f669bc9e-d665-42f6-b629-d70968f47cb9 complete, cost 17738ms 
[INFO ] 2024-10-10 12:19:18.581 - [任务 2] - load tapTable task 6707547d5fe35676bdffab43-72d6d2e9-354d-4451-8e49-fbfa844eccbf complete, cost 11215ms 
[INFO ] 2024-10-10 12:19:21.729 - [任务 2] - Start task milestones: 6707547d5fe35676bdffab44(任务 2) 
[INFO ] 2024-10-10 12:19:21.730 - [任务 2] - Task initialization... 
[INFO ] 2024-10-10 12:19:33.442 - [任务 2] - load tapTable task 6707547d5fe35676bdffab43-16b248ea-9037-4a8e-a8f2-b3aaa00c4169 complete, cost 11478ms 
[INFO ] 2024-10-10 12:19:33.613 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-10 12:19:33.615 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-10 12:19:33.754 - [任务 2][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:19:33.755 - [任务 2][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:19:33.755 - [任务 2][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:19:33.755 - [任务 2][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:19:33.757 - [任务 2][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 12:19:33.788 - [任务 2][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:19:33.788 - [任务 2][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 12:19:39.809 - [任务 2][testNotNull] - Source node "testNotNull" read batch size: 100 
[INFO ] 2024-10-10 12:19:39.813 - [任务 2][testNotNull] - Source node "testNotNull" event queue capacity: 200 
[INFO ] 2024-10-10 12:19:39.813 - [任务 2][testNotNull] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-10 12:19:39.841 - [任务 2][testNotNull] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":452,"gtidSet":""} 
[INFO ] 2024-10-10 12:19:39.924 - [任务 2][testNotNull] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-10 12:19:39.924 - [任务 2][testNotNull] - Initial sync started 
[INFO ] 2024-10-10 12:19:39.962 - [任务 2][testNotNull] - Starting batch read, table name: testNotNull 
[INFO ] 2024-10-10 12:19:39.963 - [任务 2][testNotNull] - Table testNotNull is going to be initial synced 
[INFO ] 2024-10-10 12:19:40.044 - [任务 2][testNotNull] - Table [testNotNull] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-10 12:19:40.045 - [任务 2][testNotNull] - Query table 'testNotNull' counts: 0 
[INFO ] 2024-10-10 12:19:40.054 - [任务 2][testNotNull] - Initial sync completed 
[INFO ] 2024-10-10 12:19:40.054 - [任务 2][testNotNull] - Incremental sync starting... 
[INFO ] 2024-10-10 12:19:40.054 - [任务 2][testNotNull] - Initial sync completed 
[INFO ] 2024-10-10 12:19:40.143 - [任务 2][testNotNull] - Starting stream read, table list: [testNotNull], offset: {"filename":"binlog.000036","position":452,"gtidSet":""} 
[INFO ] 2024-10-10 12:19:40.145 - [任务 2][testNotNull] - Starting mysql cdc, server name: f26efc21-5cd5-48fa-b88e-6e4ab90ddc49 
[INFO ] 2024-10-10 12:19:40.352 - [任务 2][testNotNull] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"f26efc21-5cd5-48fa-b88e-6e4ab90ddc49","offset":{"{\"server\":\"f26efc21-5cd5-48fa-b88e-6e4ab90ddc49\"}":"{\"file\":\"binlog.000036\",\"pos\":452,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1664873097
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f26efc21-5cd5-48fa-b88e-6e4ab90ddc49
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f26efc21-5cd5-48fa-b88e-6e4ab90ddc49
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: f26efc21-5cd5-48fa-b88e-6e4ab90ddc49
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.testNotNull
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-10 12:19:40.758 - [任务 2][testNotNull] - Connector Mysql incremental start succeed, tables: [testNotNull], data change syncing 
[INFO ] 2024-10-10 12:19:44.469 - [任务 2][testNotNull] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-10-10 12:19:45.687 - [任务 2][testNotNull] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-10-10 12:20:41.049 - [任务 2][testNotNull] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@119b9818: {"after":{"id":1},"containsIllegalDate":false,"referenceTime":1728534028000,"tableId":"testNotNull","time":1728534029049,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728534028000, sourceSerialNo=null} 
[ERROR] 2024-10-10 12:20:41.056 - [任务 2][testNotNull] - java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@119b9818: {"after":{"id":1},"containsIllegalDate":false,"referenceTime":1728534028000,"tableId":"testNotNull","time":1728534029049,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728534028000, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@119b9818: {"after":{"id":1},"containsIllegalDate":false,"referenceTime":1728534028000,"tableId":"testNotNull","time":1728534029049,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728534028000, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:725)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:695)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:676)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:629)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:519)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:598)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:650)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:597)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:814)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$26(HazelcastTargetPdkDataNode.java:514)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:514)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:784)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:717)
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectViolateNull(MysqlExceptionCollector.java:147)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:119)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:457)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:865)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$59(HazelcastTargetPdkDataNode.java:820)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 20 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:94)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:108)
	... 33 more

[INFO ] 2024-10-10 12:20:41.057 - [任务 2][testNotNull] - Job suspend in error handle 
[INFO ] 2024-10-10 12:20:41.472 - [任务 2][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 12:20:41.556 - [任务 2][testNotNull] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-10 12:20:41.556 - [任务 2][testNotNull] - Mysql binlog reader stopped 
[INFO ] 2024-10-10 12:20:41.559 - [任务 2][testNotNull] - Incremental sync completed 
[INFO ] 2024-10-10 12:20:41.570 - [任务 2][testNotNull] - PDK connector node stopped: HazelcastSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:20:41.570 - [任务 2][testNotNull] - PDK connector node released: HazelcastSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:20:41.573 - [任务 2][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 12:20:41.573 - [任务 2][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 12:20:41.578 - [任务 2][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 107 ms 
[INFO ] 2024-10-10 12:20:41.579 - [任务 2][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 12:20:41.588 - [任务 2][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-fab401cb-b5a5-4b6d-86e2-2901fbcc0085 
[INFO ] 2024-10-10 12:20:41.588 - [任务 2][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-fab401cb-b5a5-4b6d-86e2-2901fbcc0085 
[INFO ] 2024-10-10 12:20:41.588 - [任务 2][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab44-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 12:20:41.596 - [任务 2][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-947f5731-ea6b-42cb-870c-264f48f6aeeb 
[INFO ] 2024-10-10 12:20:41.596 - [任务 2][增强JS] - PDK connector node released: ScriptExecutor-mysql3307-947f5731-ea6b-42cb-870c-264f48f6aeeb 
[INFO ] 2024-10-10 12:20:41.597 - [任务 2][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab44-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-10 12:20:41.601 - [任务 2][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 12:20:41.601 - [任务 2][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 12:20:41.604 - [任务 2][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 25 ms 
[INFO ] 2024-10-10 12:20:41.604 - [任务 2][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] running status set to false 
[INFO ] 2024-10-10 12:20:41.634 - [任务 2][testNotNull] - PDK connector node stopped: HazelcastTargetPdkDataNode-ebf2fd1a-deb0-4487-a984-5e00499d358d 
[INFO ] 2024-10-10 12:20:41.634 - [任务 2][testNotNull] - PDK connector node released: HazelcastTargetPdkDataNode-ebf2fd1a-deb0-4487-a984-5e00499d358d 
[INFO ] 2024-10-10 12:20:41.634 - [任务 2][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] schema data cleaned 
[INFO ] 2024-10-10 12:20:41.635 - [任务 2][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] monitor closed 
[INFO ] 2024-10-10 12:20:41.842 - [任务 2][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] close complete, cost 31 ms 
[INFO ] 2024-10-10 12:20:43.342 - [任务 2] - Task [任务 2] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-10 12:20:43.343 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-10 12:20:43.463 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@76ef9467 
[INFO ] 2024-10-10 12:20:43.464 - [任务 2] - Stop task milestones: 6707547d5fe35676bdffab44(任务 2)  
[INFO ] 2024-10-10 12:20:43.474 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-10-10 12:20:43.474 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-10-10 12:20:43.504 - [任务 2] - Remove memory task client succeed, task: 任务 2[6707547d5fe35676bdffab44] 
[INFO ] 2024-10-10 12:20:43.708 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[6707547d5fe35676bdffab44] 
[INFO ] 2024-10-10 14:06:26.776 - [任务 2] - load tapTable task 6707547d5fe35676bdffab43-7e82d38b-6005-457b-a7eb-8fcf2727f3cc complete, cost 11906ms 
[INFO ] 2024-10-10 14:06:47.405 - [任务 2] - load tapTable task 6707547d5fe35676bdffab43-254860bf-4610-4829-9996-1dd9ba94cbd1 complete, cost 11364ms 
[INFO ] 2024-10-10 14:06:50.270 - [任务 2] - Start task milestones: 6707547d5fe35676bdffab44(任务 2) 
[INFO ] 2024-10-10 14:06:50.508 - [任务 2] - Task initialization... 
[INFO ] 2024-10-10 14:07:01.882 - [任务 2] - load tapTable task 6707547d5fe35676bdffab43-d3f393ab-8b8e-4779-8d95-ee222d803d99 complete, cost 11519ms 
[INFO ] 2024-10-10 14:07:01.941 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-10 14:07:02.073 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-10 14:07:02.074 - [任务 2][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:07:02.074 - [任务 2][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:07:02.074 - [任务 2][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:07:02.074 - [任务 2][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:07:02.075 - [任务 2][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:07:02.075 - [任务 2][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:07:02.076 - [任务 2][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 14:07:07.896 - [任务 2][testNotNull] - Source node "testNotNull" read batch size: 100 
[INFO ] 2024-10-10 14:07:07.900 - [任务 2][testNotNull] - Source node "testNotNull" event queue capacity: 200 
[INFO ] 2024-10-10 14:07:07.900 - [任务 2][testNotNull] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-10 14:07:07.934 - [任务 2][testNotNull] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":746,"gtidSet":""} 
[INFO ] 2024-10-10 14:07:07.935 - [任务 2][testNotNull] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-10 14:07:08.017 - [任务 2][testNotNull] - Initial sync started 
[INFO ] 2024-10-10 14:07:08.017 - [任务 2][testNotNull] - Starting batch read, table name: testNotNull 
[INFO ] 2024-10-10 14:07:08.024 - [任务 2][testNotNull] - Table testNotNull is going to be initial synced 
[INFO ] 2024-10-10 14:07:08.082 - [任务 2][testNotNull] - Table [testNotNull] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-10 14:07:08.082 - [任务 2][testNotNull] - Query table 'testNotNull' counts: 1 
[INFO ] 2024-10-10 14:07:08.083 - [任务 2][testNotNull] - Initial sync completed 
[INFO ] 2024-10-10 14:07:08.083 - [任务 2][testNotNull] - Incremental sync starting... 
[INFO ] 2024-10-10 14:07:08.083 - [任务 2][testNotNull] - Initial sync completed 
[INFO ] 2024-10-10 14:07:08.140 - [任务 2][testNotNull] - Starting stream read, table list: [testNotNull], offset: {"filename":"binlog.000036","position":746,"gtidSet":""} 
[INFO ] 2024-10-10 14:07:08.141 - [任务 2][testNotNull] - Starting mysql cdc, server name: 6168ef68-e162-49e6-a03a-a2db486539c8 
[INFO ] 2024-10-10 14:07:08.349 - [任务 2][testNotNull] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"6168ef68-e162-49e6-a03a-a2db486539c8","offset":{"{\"server\":\"6168ef68-e162-49e6-a03a-a2db486539c8\"}":"{\"file\":\"binlog.000036\",\"pos\":746,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 866055718
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 6168ef68-e162-49e6-a03a-a2db486539c8
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-6168ef68-e162-49e6-a03a-a2db486539c8
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 6168ef68-e162-49e6-a03a-a2db486539c8
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.testNotNull
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-10 14:07:08.467 - [任务 2][testNotNull] - Connector Mysql incremental start succeed, tables: [testNotNull], data change syncing 
[INFO ] 2024-10-10 14:07:12.471 - [任务 2][testNotNull] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-10 14:07:12.473 - [任务 2][testNotNull] - Table "test.testNotNull" exists, skip auto create table 
[INFO ] 2024-10-10 14:07:12.473 - [任务 2][testNotNull] - The table testNotNull has already exist. 
[INFO ] 2024-10-10 14:08:16.980 - [任务 2][testNotNull] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@364746ab: {"after":{"id":1},"containsIllegalDate":false,"tableId":"testNotNull","time":1728540428054,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728540427892, sourceSerialNo=null} 
[ERROR] 2024-10-10 14:08:16.983 - [任务 2][testNotNull] - java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@364746ab: {"after":{"id":1},"containsIllegalDate":false,"tableId":"testNotNull","time":1728540428054,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728540427892, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@364746ab: {"after":{"id":1},"containsIllegalDate":false,"tableId":"testNotNull","time":1728540428054,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728540427892, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:725)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:666)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:626)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:512)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:598)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:650)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:597)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:814)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$26(HazelcastTargetPdkDataNode.java:514)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:514)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:784)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:717)
	... 12 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectViolateNull(MysqlExceptionCollector.java:147)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:119)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:457)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:865)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$59(HazelcastTargetPdkDataNode.java:820)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:94)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:108)
	... 32 more

[INFO ] 2024-10-10 14:08:16.987 - [任务 2][testNotNull] - Job suspend in error handle 
[INFO ] 2024-10-10 14:08:17.353 - [任务 2][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 14:08:17.354 - [任务 2][testNotNull] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-10 14:08:17.354 - [任务 2][testNotNull] - Mysql binlog reader stopped 
[INFO ] 2024-10-10 14:08:17.354 - [任务 2][testNotNull] - Incremental sync completed 
[INFO ] 2024-10-10 14:08:17.372 - [任务 2][testNotNull] - PDK connector node stopped: HazelcastSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 14:08:17.372 - [任务 2][testNotNull] - PDK connector node released: HazelcastSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 14:08:17.372 - [任务 2][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 14:08:17.372 - [任务 2][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 14:08:17.375 - [任务 2][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 95 ms 
[INFO ] 2024-10-10 14:08:17.379 - [任务 2][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 14:08:17.384 - [任务 2][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-4cba2973-18f4-497e-9478-42ca0c683677 
[INFO ] 2024-10-10 14:08:17.386 - [任务 2][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-4cba2973-18f4-497e-9478-42ca0c683677 
[INFO ] 2024-10-10 14:08:17.387 - [任务 2][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab44-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 14:08:17.389 - [任务 2][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-e17c18fc-bc8a-4781-9055-5572ab8d748f 
[INFO ] 2024-10-10 14:08:17.389 - [任务 2][增强JS] - PDK connector node released: ScriptExecutor-mysql3307-e17c18fc-bc8a-4781-9055-5572ab8d748f 
[INFO ] 2024-10-10 14:08:17.397 - [任务 2][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab44-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-10 14:08:17.397 - [任务 2][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 14:08:17.399 - [任务 2][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 14:08:17.399 - [任务 2][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 25 ms 
[INFO ] 2024-10-10 14:08:17.409 - [任务 2][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] running status set to false 
[INFO ] 2024-10-10 14:08:17.409 - [任务 2][testNotNull] - PDK connector node stopped: HazelcastTargetPdkDataNode-ebf2fd1a-deb0-4487-a984-5e00499d358d 
[INFO ] 2024-10-10 14:08:17.410 - [任务 2][testNotNull] - PDK connector node released: HazelcastTargetPdkDataNode-ebf2fd1a-deb0-4487-a984-5e00499d358d 
[INFO ] 2024-10-10 14:08:17.411 - [任务 2][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] schema data cleaned 
[INFO ] 2024-10-10 14:08:17.412 - [任务 2][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] monitor closed 
[INFO ] 2024-10-10 14:08:17.412 - [任务 2][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] close complete, cost 13 ms 
[INFO ] 2024-10-10 14:08:21.813 - [任务 2] - Task [任务 2] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-10 14:08:21.813 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-10 14:08:21.813 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@25c87c0a 
[INFO ] 2024-10-10 14:08:21.947 - [任务 2] - Stop task milestones: 6707547d5fe35676bdffab44(任务 2)  
[INFO ] 2024-10-10 14:08:21.959 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-10-10 14:08:21.959 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-10-10 14:08:21.976 - [任务 2] - Remove memory task client succeed, task: 任务 2[6707547d5fe35676bdffab44] 
[INFO ] 2024-10-10 14:08:21.979 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[6707547d5fe35676bdffab44] 
[INFO ] 2024-10-10 16:11:08.450 - [测试PDK 错误，不能写入Null值] - load tapTable task 6707547d5fe35676bdffab43-b12119f2-ddf5-442b-bae5-a5a91f81625a complete, cost 12128ms 
[INFO ] 2024-10-10 16:11:22.564 - [测试PDK 错误，不能写入Null值] - load tapTable task 6707547d5fe35676bdffab43-4b9855e2-54bb-485b-bbda-a0957a19115e complete, cost 11334ms 
[INFO ] 2024-10-10 16:14:00.571 - [测试PDK 错误，不能写入Null值] - load tapTable task 6707547d5fe35676bdffab43-7c275bef-a071-4250-8434-b3816e8f0105 complete, cost 11598ms 
[INFO ] 2024-10-10 16:14:03.094 - [测试PDK 错误，不能写入Null值] - Start task milestones: 6707547d5fe35676bdffab44(测试PDK 错误，不能写入Null值) 
[INFO ] 2024-10-10 16:14:03.094 - [测试PDK 错误，不能写入Null值] - Task initialization... 
[INFO ] 2024-10-10 16:14:14.693 - [测试PDK 错误，不能写入Null值] - load tapTable task 6707547d5fe35676bdffab43-2a240ad9-7a14-431a-ac2b-49e8cbd0766d complete, cost 11521ms 
[INFO ] 2024-10-10 16:14:14.756 - [测试PDK 错误，不能写入Null值] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-10 16:14:14.890 - [测试PDK 错误，不能写入Null值] - The engine receives 测试PDK 错误，不能写入Null值 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-10 16:14:14.892 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:14:14.893 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:14:14.893 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:14:14.893 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:14:14.893 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:14:14.893 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:14:14.893 - [测试PDK 错误，不能写入Null值][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:14:20.519 - [测试PDK 错误，不能写入Null值][testNotNull] - Source node "testNotNull" read batch size: 100 
[INFO ] 2024-10-10 16:14:20.521 - [测试PDK 错误，不能写入Null值][testNotNull] - Source node "testNotNull" event queue capacity: 200 
[INFO ] 2024-10-10 16:14:20.521 - [测试PDK 错误，不能写入Null值][testNotNull] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-10 16:14:20.532 - [测试PDK 错误，不能写入Null值][testNotNull] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":2457,"gtidSet":""} 
[INFO ] 2024-10-10 16:14:20.532 - [测试PDK 错误，不能写入Null值][testNotNull] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-10 16:14:20.574 - [测试PDK 错误，不能写入Null值][testNotNull] - Initial sync started 
[INFO ] 2024-10-10 16:14:20.574 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting batch read, table name: testNotNull 
[INFO ] 2024-10-10 16:14:20.626 - [测试PDK 错误，不能写入Null值][testNotNull] - Table testNotNull is going to be initial synced 
[INFO ] 2024-10-10 16:14:20.627 - [测试PDK 错误，不能写入Null值][testNotNull] - Table [testNotNull] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-10 16:14:20.627 - [测试PDK 错误，不能写入Null值][testNotNull] - Query table 'testNotNull' counts: 2 
[INFO ] 2024-10-10 16:14:20.627 - [测试PDK 错误，不能写入Null值][testNotNull] - Initial sync completed 
[INFO ] 2024-10-10 16:14:20.628 - [测试PDK 错误，不能写入Null值][testNotNull] - Incremental sync starting... 
[INFO ] 2024-10-10 16:14:20.628 - [测试PDK 错误，不能写入Null值][testNotNull] - Initial sync completed 
[INFO ] 2024-10-10 16:14:20.633 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting stream read, table list: [testNotNull], offset: {"filename":"binlog.000036","position":2457,"gtidSet":""} 
[INFO ] 2024-10-10 16:14:20.718 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting mysql cdc, server name: 5416cc99-b87a-484f-8595-75eb7bdd2e0e 
[INFO ] 2024-10-10 16:14:20.719 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"5416cc99-b87a-484f-8595-75eb7bdd2e0e","offset":{"{\"server\":\"5416cc99-b87a-484f-8595-75eb7bdd2e0e\"}":"{\"file\":\"binlog.000036\",\"pos\":2457,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1630437698
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 5416cc99-b87a-484f-8595-75eb7bdd2e0e
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-5416cc99-b87a-484f-8595-75eb7bdd2e0e
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 5416cc99-b87a-484f-8595-75eb7bdd2e0e
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.testNotNull
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-10 16:14:21.122 - [测试PDK 错误，不能写入Null值][testNotNull] - Connector Mysql incremental start succeed, tables: [testNotNull], data change syncing 
[INFO ] 2024-10-10 16:14:25.064 - [测试PDK 错误，不能写入Null值][testNotNull] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-10 16:14:25.084 - [测试PDK 错误，不能写入Null值][testNotNull] - Table "test.testNotNull" exists, skip auto create table 
[INFO ] 2024-10-10 16:14:25.085 - [测试PDK 错误，不能写入Null值][testNotNull] - The table testNotNull has already exist. 
[INFO ] 2024-10-10 16:14:32.367 - [测试PDK 错误，不能写入Null值][testNotNull] - Table 'testNotNull' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-10 16:14:43.948 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:14:44.032 - [测试PDK 错误，不能写入Null值][testNotNull] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-10 16:14:44.038 - [测试PDK 错误，不能写入Null值][testNotNull] - Mysql binlog reader stopped 
[INFO ] 2024-10-10 16:14:44.038 - [测试PDK 错误，不能写入Null值][testNotNull] - Incremental sync completed 
[INFO ] 2024-10-10 16:14:44.055 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node stopped: HazelcastSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:14:44.055 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node released: HazelcastSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:14:44.055 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:14:44.058 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:14:44.061 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 114 ms 
[INFO ] 2024-10-10 16:14:44.063 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:14:44.068 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-a2271654-c6ef-4ecb-992e-d7713430a1c1 
[INFO ] 2024-10-10 16:14:44.068 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-a2271654-c6ef-4ecb-992e-d7713430a1c1 
[INFO ] 2024-10-10 16:14:44.069 - [测试PDK 错误，不能写入Null值][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab44-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:14:44.081 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-3d781d15-f89c-48d6-a409-c3585010f14d 
[INFO ] 2024-10-10 16:14:44.081 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node released: ScriptExecutor-mysql3307-3d781d15-f89c-48d6-a409-c3585010f14d 
[INFO ] 2024-10-10 16:14:44.088 - [测试PDK 错误，不能写入Null值][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab44-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-10 16:14:44.088 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:14:44.088 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:14:44.089 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 30 ms 
[INFO ] 2024-10-10 16:14:44.106 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] running status set to false 
[INFO ] 2024-10-10 16:14:44.106 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node stopped: HazelcastTargetPdkDataNode-ebf2fd1a-deb0-4487-a984-5e00499d358d 
[INFO ] 2024-10-10 16:14:44.107 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node released: HazelcastTargetPdkDataNode-ebf2fd1a-deb0-4487-a984-5e00499d358d 
[INFO ] 2024-10-10 16:14:44.107 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] schema data cleaned 
[INFO ] 2024-10-10 16:14:44.107 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] monitor closed 
[INFO ] 2024-10-10 16:14:44.310 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] close complete, cost 18 ms 
[INFO ] 2024-10-10 16:14:48.440 - [测试PDK 错误，不能写入Null值] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-10 16:14:48.441 - [测试PDK 错误，不能写入Null值] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5d48411d 
[INFO ] 2024-10-10 16:14:48.572 - [测试PDK 错误，不能写入Null值] - Stop task milestones: 6707547d5fe35676bdffab44(测试PDK 错误，不能写入Null值)  
[INFO ] 2024-10-10 16:14:48.573 - [测试PDK 错误，不能写入Null值] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:14:48.573 - [测试PDK 错误，不能写入Null值] - Snapshot order controller have been removed 
[INFO ] 2024-10-10 16:14:48.609 - [测试PDK 错误，不能写入Null值] - Remove memory task client succeed, task: 测试PDK 错误，不能写入Null值[6707547d5fe35676bdffab44] 
[INFO ] 2024-10-10 16:14:48.609 - [测试PDK 错误，不能写入Null值] - Destroy memory task client cache succeed, task: 测试PDK 错误，不能写入Null值[6707547d5fe35676bdffab44] 
[INFO ] 2024-10-10 16:16:44.993 - [测试PDK 错误，不能写入Null值] - load tapTable task 6707547d5fe35676bdffab43-421eb891-bb65-4a71-b9fc-db4ef3c6355b complete, cost 11953ms 
[INFO ] 2024-10-10 16:17:04.612 - [测试PDK 错误，不能写入Null值] - load tapTable task 6707547d5fe35676bdffab43-10dc1837-b72f-43e6-a784-f700afe2687a complete, cost 11404ms 
[INFO ] 2024-10-10 16:17:19.827 - [测试PDK 错误，不能写入Null值] - load tapTable task 6707547d5fe35676bdffab43-fd7f96c3-6eef-411b-9918-8d6a5a46d654 complete, cost 11400ms 
[INFO ] 2024-10-10 16:17:33.281 - [测试PDK 错误，不能写入Null值] - load tapTable task 6707547d5fe35676bdffab43-74de4fec-7547-4421-9a40-b49f1c662d1f complete, cost 11502ms 
[INFO ] 2024-10-10 16:17:48.547 - [测试PDK 错误，不能写入Null值] - load tapTable task 6707547d5fe35676bdffab43-89ca5614-ddab-4705-829e-72448f0728c6 complete, cost 11813ms 
[INFO ] 2024-10-10 16:17:50.904 - [测试PDK 错误，不能写入Null值] - Start task milestones: 6707547d5fe35676bdffab44(测试PDK 错误，不能写入Null值) 
[INFO ] 2024-10-10 16:17:50.905 - [测试PDK 错误，不能写入Null值] - Task initialization... 
[INFO ] 2024-10-10 16:18:03.098 - [测试PDK 错误，不能写入Null值] - load tapTable task 6707547d5fe35676bdffab43-830cb5d1-cffa-463a-a8bf-82b6465ccc53 complete, cost 12094ms 
[INFO ] 2024-10-10 16:18:03.176 - [测试PDK 错误，不能写入Null值] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-10 16:18:03.353 - [测试PDK 错误，不能写入Null值] - The engine receives 测试PDK 错误，不能写入Null值 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-10 16:18:03.354 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:18:03.354 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:18:03.354 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:18:03.355 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:18:03.355 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:18:03.355 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:18:03.355 - [测试PDK 错误，不能写入Null值][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:18:09.440 - [测试PDK 错误，不能写入Null值][testNotNull] - Source node "testNotNull" read batch size: 100 
[INFO ] 2024-10-10 16:18:09.441 - [测试PDK 错误，不能写入Null值][testNotNull] - Source node "testNotNull" event queue capacity: 200 
[INFO ] 2024-10-10 16:18:09.441 - [测试PDK 错误，不能写入Null值][testNotNull] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-10 16:18:09.453 - [测试PDK 错误，不能写入Null值][testNotNull] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":2965,"gtidSet":""} 
[INFO ] 2024-10-10 16:18:09.454 - [测试PDK 错误，不能写入Null值][testNotNull] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-10 16:18:09.512 - [测试PDK 错误，不能写入Null值][testNotNull] - Initial sync started 
[INFO ] 2024-10-10 16:18:09.522 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting batch read, table name: testNotNull 
[INFO ] 2024-10-10 16:18:09.568 - [测试PDK 错误，不能写入Null值][testNotNull] - Table testNotNull is going to be initial synced 
[INFO ] 2024-10-10 16:18:09.569 - [测试PDK 错误，不能写入Null值][testNotNull] - Table [testNotNull] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-10 16:18:09.571 - [测试PDK 错误，不能写入Null值][testNotNull] - Query table 'testNotNull' counts: 0 
[INFO ] 2024-10-10 16:18:09.571 - [测试PDK 错误，不能写入Null值][testNotNull] - Initial sync completed 
[INFO ] 2024-10-10 16:18:09.571 - [测试PDK 错误，不能写入Null值][testNotNull] - Incremental sync starting... 
[INFO ] 2024-10-10 16:18:09.571 - [测试PDK 错误，不能写入Null值][testNotNull] - Initial sync completed 
[INFO ] 2024-10-10 16:18:09.607 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting stream read, table list: [testNotNull], offset: {"filename":"binlog.000036","position":2965,"gtidSet":""} 
[INFO ] 2024-10-10 16:18:09.608 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting mysql cdc, server name: 11937e48-e578-4b80-9f31-ef8a9a105b0f 
[INFO ] 2024-10-10 16:18:09.818 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"11937e48-e578-4b80-9f31-ef8a9a105b0f","offset":{"{\"server\":\"11937e48-e578-4b80-9f31-ef8a9a105b0f\"}":"{\"file\":\"binlog.000036\",\"pos\":2965,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 834142562
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 11937e48-e578-4b80-9f31-ef8a9a105b0f
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-11937e48-e578-4b80-9f31-ef8a9a105b0f
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 11937e48-e578-4b80-9f31-ef8a9a105b0f
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.testNotNull
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-10 16:18:09.926 - [测试PDK 错误，不能写入Null值][testNotNull] - Connector Mysql incremental start succeed, tables: [testNotNull], data change syncing 
[INFO ] 2024-10-10 16:18:14.188 - [测试PDK 错误，不能写入Null值][testNotNull] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-10-10 16:18:15.044 - [测试PDK 错误，不能写入Null值][testNotNull] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-10-10 16:21:44.625 - [测试PDK 错误，不能写入Null值][testNotNull] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@74f50927: {"after":{"id":1},"containsIllegalDate":false,"referenceTime":1728548368000,"tableId":"testNotNull","time":1728548368904,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728548368000, sourceSerialNo=null} 
[ERROR] 2024-10-10 16:21:44.683 - [测试PDK 错误，不能写入Null值][testNotNull] - java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@74f50927: {"after":{"id":1},"containsIllegalDate":false,"referenceTime":1728548368000,"tableId":"testNotNull","time":1728548368904,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728548368000, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@74f50927: {"after":{"id":1},"containsIllegalDate":false,"referenceTime":1728548368000,"tableId":"testNotNull","time":1728548368904,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728548368000, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:725)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:695)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:676)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:629)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:519)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:598)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:650)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:597)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:814)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$26(HazelcastTargetPdkDataNode.java:514)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:514)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:784)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:717)
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectViolateNull(MysqlExceptionCollector.java:147)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:119)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:457)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:865)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$59(HazelcastTargetPdkDataNode.java:820)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 20 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:94)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:108)
	... 33 more

[INFO ] 2024-10-10 16:21:44.909 - [测试PDK 错误，不能写入Null值][testNotNull] - Job suspend in error handle 
[INFO ] 2024-10-10 16:21:44.970 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:21:45.071 - [测试PDK 错误，不能写入Null值][testNotNull] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-10 16:21:45.071 - [测试PDK 错误，不能写入Null值][testNotNull] - Mysql binlog reader stopped 
[INFO ] 2024-10-10 16:21:45.072 - [测试PDK 错误，不能写入Null值][testNotNull] - Incremental sync completed 
[INFO ] 2024-10-10 16:21:45.096 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node stopped: HazelcastSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:21:45.096 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node released: HazelcastSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:21:45.097 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:21:45.097 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:21:45.101 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 137 ms 
[INFO ] 2024-10-10 16:21:45.103 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:21:45.105 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-94d2105e-8040-4b84-9836-cc57f6fc10f4 
[INFO ] 2024-10-10 16:21:45.105 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-94d2105e-8040-4b84-9836-cc57f6fc10f4 
[INFO ] 2024-10-10 16:21:45.113 - [测试PDK 错误，不能写入Null值][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab44-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:21:45.113 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-67fc5806-f61c-4d59-9a79-3d216526c460 
[INFO ] 2024-10-10 16:21:45.113 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node released: ScriptExecutor-mysql3307-67fc5806-f61c-4d59-9a79-3d216526c460 
[INFO ] 2024-10-10 16:21:45.120 - [测试PDK 错误，不能写入Null值][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab44-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-10 16:21:45.120 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:21:45.120 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:21:45.121 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 22 ms 
[INFO ] 2024-10-10 16:21:45.136 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] running status set to false 
[INFO ] 2024-10-10 16:21:45.137 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node stopped: HazelcastTargetPdkDataNode-ebf2fd1a-deb0-4487-a984-5e00499d358d 
[INFO ] 2024-10-10 16:21:45.137 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node released: HazelcastTargetPdkDataNode-ebf2fd1a-deb0-4487-a984-5e00499d358d 
[INFO ] 2024-10-10 16:21:45.137 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] schema data cleaned 
[INFO ] 2024-10-10 16:21:45.137 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] monitor closed 
[INFO ] 2024-10-10 16:21:45.343 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] close complete, cost 16 ms 
[INFO ] 2024-10-10 16:21:49.536 - [测试PDK 错误，不能写入Null值] - Task [测试PDK 错误，不能写入Null值] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-10 16:21:49.550 - [测试PDK 错误，不能写入Null值] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-10 16:21:49.550 - [测试PDK 错误，不能写入Null值] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@72e3dd1 
[INFO ] 2024-10-10 16:21:49.666 - [测试PDK 错误，不能写入Null值] - Stop task milestones: 6707547d5fe35676bdffab44(测试PDK 错误，不能写入Null值)  
[INFO ] 2024-10-10 16:21:49.676 - [测试PDK 错误，不能写入Null值] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:21:49.676 - [测试PDK 错误，不能写入Null值] - Snapshot order controller have been removed 
[INFO ] 2024-10-10 16:21:49.691 - [测试PDK 错误，不能写入Null值] - Remove memory task client succeed, task: 测试PDK 错误，不能写入Null值[6707547d5fe35676bdffab44] 
[INFO ] 2024-10-10 16:21:49.695 - [测试PDK 错误，不能写入Null值] - Destroy memory task client cache succeed, task: 测试PDK 错误，不能写入Null值[6707547d5fe35676bdffab44] 
[INFO ] 2024-10-10 16:22:51.924 - [测试PDK 错误，不能写入Null值] - Start task milestones: 6707547d5fe35676bdffab44(测试PDK 错误，不能写入Null值) 
[INFO ] 2024-10-10 16:22:51.925 - [测试PDK 错误，不能写入Null值] - Task initialization... 
[INFO ] 2024-10-10 16:23:04.363 - [测试PDK 错误，不能写入Null值] - load tapTable task 6707547d5fe35676bdffab43-bfc94b7c-729b-41be-8044-2e07250f5ce8 complete, cost 12363ms 
[INFO ] 2024-10-10 16:23:04.507 - [测试PDK 错误，不能写入Null值] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-10 16:23:04.508 - [测试PDK 错误，不能写入Null值] - The engine receives 测试PDK 错误，不能写入Null值 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-10 16:23:04.585 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:23:04.585 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:23:04.586 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:23:04.586 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:23:04.586 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:23:04.586 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:23:04.586 - [测试PDK 错误，不能写入Null值][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:23:10.283 - [测试PDK 错误，不能写入Null值][testNotNull] - Source node "testNotNull" read batch size: 100 
[INFO ] 2024-10-10 16:23:10.284 - [测试PDK 错误，不能写入Null值][testNotNull] - Source node "testNotNull" event queue capacity: 200 
[INFO ] 2024-10-10 16:23:10.284 - [测试PDK 错误，不能写入Null值][testNotNull] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-10 16:23:10.298 - [测试PDK 错误，不能写入Null值][testNotNull] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":3259,"gtidSet":""} 
[INFO ] 2024-10-10 16:23:10.298 - [测试PDK 错误，不能写入Null值][testNotNull] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-10 16:23:10.339 - [测试PDK 错误，不能写入Null值][testNotNull] - Initial sync started 
[INFO ] 2024-10-10 16:23:10.340 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting batch read, table name: testNotNull 
[INFO ] 2024-10-10 16:23:10.394 - [测试PDK 错误，不能写入Null值][testNotNull] - Table testNotNull is going to be initial synced 
[INFO ] 2024-10-10 16:23:10.395 - [测试PDK 错误，不能写入Null值][testNotNull] - Table [testNotNull] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-10 16:23:10.397 - [测试PDK 错误，不能写入Null值][testNotNull] - Query table 'testNotNull' counts: 1 
[INFO ] 2024-10-10 16:23:10.397 - [测试PDK 错误，不能写入Null值][testNotNull] - Initial sync completed 
[INFO ] 2024-10-10 16:23:10.398 - [测试PDK 错误，不能写入Null值][testNotNull] - Incremental sync starting... 
[INFO ] 2024-10-10 16:23:10.398 - [测试PDK 错误，不能写入Null值][testNotNull] - Initial sync completed 
[INFO ] 2024-10-10 16:23:10.435 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting stream read, table list: [testNotNull], offset: {"filename":"binlog.000036","position":3259,"gtidSet":""} 
[INFO ] 2024-10-10 16:23:10.435 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting mysql cdc, server name: 6ef8b398-d985-4b72-9982-3c37b4833b04 
[INFO ] 2024-10-10 16:23:10.645 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"6ef8b398-d985-4b72-9982-3c37b4833b04","offset":{"{\"server\":\"6ef8b398-d985-4b72-9982-3c37b4833b04\"}":"{\"file\":\"binlog.000036\",\"pos\":3259,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 306003818
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 6ef8b398-d985-4b72-9982-3c37b4833b04
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-6ef8b398-d985-4b72-9982-3c37b4833b04
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 6ef8b398-d985-4b72-9982-3c37b4833b04
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.testNotNull
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-10 16:23:10.751 - [测试PDK 错误，不能写入Null值][testNotNull] - Connector Mysql incremental start succeed, tables: [testNotNull], data change syncing 
[INFO ] 2024-10-10 16:23:15.001 - [测试PDK 错误，不能写入Null值][testNotNull] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-10 16:25:24.179 - [测试PDK 错误，不能写入Null值][testNotNull] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@1b88ccf8: {"after":{"id":1},"containsIllegalDate":false,"tableId":"testNotNull","time":1728548590385,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728548590280, sourceSerialNo=null} 
[ERROR] 2024-10-10 16:25:24.228 - [测试PDK 错误，不能写入Null值][testNotNull] - java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@1b88ccf8: {"after":{"id":1},"containsIllegalDate":false,"tableId":"testNotNull","time":1728548590385,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728548590280, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@1b88ccf8: {"after":{"id":1},"containsIllegalDate":false,"tableId":"testNotNull","time":1728548590385,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728548590280, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:725)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:666)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:626)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:512)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:598)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:650)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:597)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:814)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$26(HazelcastTargetPdkDataNode.java:514)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:514)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:784)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:717)
	... 12 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectViolateNull(MysqlExceptionCollector.java:147)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:119)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:457)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:865)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$59(HazelcastTargetPdkDataNode.java:820)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:94)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:108)
	... 32 more

[INFO ] 2024-10-10 16:25:24.238 - [测试PDK 错误，不能写入Null值][testNotNull] - Job suspend in error handle 
[INFO ] 2024-10-10 16:25:24.581 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:25:24.583 - [测试PDK 错误，不能写入Null值][testNotNull] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-10 16:25:24.583 - [测试PDK 错误，不能写入Null值][testNotNull] - Mysql binlog reader stopped 
[INFO ] 2024-10-10 16:25:24.583 - [测试PDK 错误，不能写入Null值][testNotNull] - Incremental sync completed 
[INFO ] 2024-10-10 16:25:24.599 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node stopped: HazelcastSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:25:24.599 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node released: HazelcastSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:25:24.599 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:25:24.600 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:25:24.602 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 139 ms 
[INFO ] 2024-10-10 16:25:24.602 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:25:24.608 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-e01b6f66-665f-442b-a6a0-b6763201f41b 
[INFO ] 2024-10-10 16:25:24.608 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-e01b6f66-665f-442b-a6a0-b6763201f41b 
[INFO ] 2024-10-10 16:25:24.611 - [测试PDK 错误，不能写入Null值][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab44-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:25:24.617 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-8ebf269f-80be-47bf-b969-93754f7b63fd 
[INFO ] 2024-10-10 16:25:24.617 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node released: ScriptExecutor-mysql3307-8ebf269f-80be-47bf-b969-93754f7b63fd 
[INFO ] 2024-10-10 16:25:24.617 - [测试PDK 错误，不能写入Null值][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab44-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-10 16:25:24.624 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:25:24.624 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:25:24.625 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 23 ms 
[INFO ] 2024-10-10 16:25:24.625 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] running status set to false 
[INFO ] 2024-10-10 16:25:24.631 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node stopped: HazelcastTargetPdkDataNode-ebf2fd1a-deb0-4487-a984-5e00499d358d 
[INFO ] 2024-10-10 16:25:24.631 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node released: HazelcastTargetPdkDataNode-ebf2fd1a-deb0-4487-a984-5e00499d358d 
[INFO ] 2024-10-10 16:25:24.631 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] schema data cleaned 
[INFO ] 2024-10-10 16:25:24.631 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] monitor closed 
[INFO ] 2024-10-10 16:25:24.645 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] close complete, cost 19 ms 
[INFO ] 2024-10-10 16:25:26.396 - [测试PDK 错误，不能写入Null值] - Task [测试PDK 错误，不能写入Null值] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-10 16:25:26.412 - [测试PDK 错误，不能写入Null值] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-10 16:25:26.413 - [测试PDK 错误，不能写入Null值] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@45cb108a 
[INFO ] 2024-10-10 16:25:26.525 - [测试PDK 错误，不能写入Null值] - Stop task milestones: 6707547d5fe35676bdffab44(测试PDK 错误，不能写入Null值)  
[INFO ] 2024-10-10 16:25:26.547 - [测试PDK 错误，不能写入Null值] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:25:26.547 - [测试PDK 错误，不能写入Null值] - Snapshot order controller have been removed 
[INFO ] 2024-10-10 16:25:26.561 - [测试PDK 错误，不能写入Null值] - Remove memory task client succeed, task: 测试PDK 错误，不能写入Null值[6707547d5fe35676bdffab44] 
[INFO ] 2024-10-10 16:25:26.564 - [测试PDK 错误，不能写入Null值] - Destroy memory task client cache succeed, task: 测试PDK 错误，不能写入Null值[6707547d5fe35676bdffab44] 
[INFO ] 2024-10-10 16:26:53.571 - [测试PDK 错误，不能写入Null值] - Start task milestones: 6707547d5fe35676bdffab44(测试PDK 错误，不能写入Null值) 
[INFO ] 2024-10-10 16:26:53.571 - [测试PDK 错误，不能写入Null值] - Task initialization... 
[INFO ] 2024-10-10 16:27:05.532 - [测试PDK 错误，不能写入Null值] - load tapTable task 6707547d5fe35676bdffab43-dc07ebb7-5ee1-4fd4-811c-35164aef937f complete, cost 11878ms 
[INFO ] 2024-10-10 16:27:05.590 - [测试PDK 错误，不能写入Null值] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-10 16:27:05.720 - [测试PDK 错误，不能写入Null值] - The engine receives 测试PDK 错误，不能写入Null值 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-10 16:27:05.720 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:27:05.720 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:27:05.721 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:27:05.721 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:27:05.721 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:27:05.721 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:27:05.922 - [测试PDK 错误，不能写入Null值][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:27:11.569 - [测试PDK 错误，不能写入Null值][testNotNull] - Source node "testNotNull" read batch size: 100 
[INFO ] 2024-10-10 16:27:11.570 - [测试PDK 错误，不能写入Null值][testNotNull] - Source node "testNotNull" event queue capacity: 200 
[INFO ] 2024-10-10 16:27:11.570 - [测试PDK 错误，不能写入Null值][testNotNull] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-10 16:27:11.586 - [测试PDK 错误，不能写入Null值][testNotNull] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":3259,"gtidSet":""} 
[INFO ] 2024-10-10 16:27:11.588 - [测试PDK 错误，不能写入Null值][testNotNull] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-10 16:27:11.685 - [测试PDK 错误，不能写入Null值][testNotNull] - Initial sync started 
[INFO ] 2024-10-10 16:27:11.694 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting batch read, table name: testNotNull 
[INFO ] 2024-10-10 16:27:11.733 - [测试PDK 错误，不能写入Null值][testNotNull] - Table testNotNull is going to be initial synced 
[INFO ] 2024-10-10 16:27:11.734 - [测试PDK 错误，不能写入Null值][testNotNull] - Table [testNotNull] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-10 16:27:11.736 - [测试PDK 错误，不能写入Null值][testNotNull] - Query table 'testNotNull' counts: 1 
[INFO ] 2024-10-10 16:27:11.736 - [测试PDK 错误，不能写入Null值][testNotNull] - Initial sync completed 
[INFO ] 2024-10-10 16:27:11.736 - [测试PDK 错误，不能写入Null值][testNotNull] - Incremental sync starting... 
[INFO ] 2024-10-10 16:27:11.736 - [测试PDK 错误，不能写入Null值][testNotNull] - Initial sync completed 
[INFO ] 2024-10-10 16:27:11.780 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting stream read, table list: [testNotNull], offset: {"filename":"binlog.000036","position":3259,"gtidSet":""} 
[INFO ] 2024-10-10 16:27:11.781 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting mysql cdc, server name: 59de59c8-26fb-4808-be1f-f56365b0f281 
[INFO ] 2024-10-10 16:27:11.983 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"59de59c8-26fb-4808-be1f-f56365b0f281","offset":{"{\"server\":\"59de59c8-26fb-4808-be1f-f56365b0f281\"}":"{\"file\":\"binlog.000036\",\"pos\":3259,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 30966990
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 59de59c8-26fb-4808-be1f-f56365b0f281
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-59de59c8-26fb-4808-be1f-f56365b0f281
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 59de59c8-26fb-4808-be1f-f56365b0f281
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.testNotNull
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-10 16:27:12.021 - [测试PDK 错误，不能写入Null值][testNotNull] - Connector Mysql incremental start succeed, tables: [testNotNull], data change syncing 
[INFO ] 2024-10-10 16:27:16.202 - [测试PDK 错误，不能写入Null值][testNotNull] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-10 16:33:00.937 - [测试PDK 错误，不能写入Null值][testNotNull] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@7863540d: {"after":{"id":1},"containsIllegalDate":false,"tableId":"testNotNull","time":1728548831724,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728548831565, sourceSerialNo=null} 
[ERROR] 2024-10-10 16:33:00.937 - [测试PDK 错误，不能写入Null值][testNotNull] - java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@7863540d: {"after":{"id":1},"containsIllegalDate":false,"tableId":"testNotNull","time":1728548831724,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728548831565, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@7863540d: {"after":{"id":1},"containsIllegalDate":false,"tableId":"testNotNull","time":1728548831724,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728548831565, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:725)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:666)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:626)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:512)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:598)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:650)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:597)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:814)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$26(HazelcastTargetPdkDataNode.java:514)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:514)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:784)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:717)
	... 12 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectViolateNull(MysqlExceptionCollector.java:147)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:119)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:457)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:865)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$59(HazelcastTargetPdkDataNode.java:820)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:94)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:108)
	... 32 more

[INFO ] 2024-10-10 16:33:01.093 - [测试PDK 错误，不能写入Null值][testNotNull] - Job suspend in error handle 
[INFO ] 2024-10-10 16:33:01.094 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:33:01.185 - [测试PDK 错误，不能写入Null值][testNotNull] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-10 16:33:01.186 - [测试PDK 错误，不能写入Null值][testNotNull] - Mysql binlog reader stopped 
[INFO ] 2024-10-10 16:33:01.205 - [测试PDK 错误，不能写入Null值][testNotNull] - Incremental sync completed 
[INFO ] 2024-10-10 16:33:01.206 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node stopped: HazelcastSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:33:01.206 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node released: HazelcastSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:33:01.206 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:33:01.206 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:33:01.209 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 119 ms 
[INFO ] 2024-10-10 16:33:01.213 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:33:01.222 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-c918a2bf-f0f9-4b30-b6a2-e822e729b59e 
[INFO ] 2024-10-10 16:33:01.223 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-c918a2bf-f0f9-4b30-b6a2-e822e729b59e 
[INFO ] 2024-10-10 16:33:01.224 - [测试PDK 错误，不能写入Null值][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab44-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:33:01.231 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-c642bb53-0f99-444b-9c21-0516eee1a2e1 
[INFO ] 2024-10-10 16:33:01.232 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node released: ScriptExecutor-mysql3307-c642bb53-0f99-444b-9c21-0516eee1a2e1 
[INFO ] 2024-10-10 16:33:01.233 - [测试PDK 错误，不能写入Null值][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab44-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-10 16:33:01.242 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:33:01.242 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:33:01.245 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 34 ms 
[INFO ] 2024-10-10 16:33:01.245 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] running status set to false 
[INFO ] 2024-10-10 16:33:01.249 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node stopped: HazelcastTargetPdkDataNode-ebf2fd1a-deb0-4487-a984-5e00499d358d 
[INFO ] 2024-10-10 16:33:01.249 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node released: HazelcastTargetPdkDataNode-ebf2fd1a-deb0-4487-a984-5e00499d358d 
[INFO ] 2024-10-10 16:33:01.249 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] schema data cleaned 
[INFO ] 2024-10-10 16:33:01.249 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] monitor closed 
[INFO ] 2024-10-10 16:33:01.451 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] close complete, cost 5 ms 
[INFO ] 2024-10-10 16:33:05.715 - [测试PDK 错误，不能写入Null值] - Task [测试PDK 错误，不能写入Null值] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-10 16:33:05.730 - [测试PDK 错误，不能写入Null值] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-10 16:33:05.844 - [测试PDK 错误，不能写入Null值] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@133c69ab 
[INFO ] 2024-10-10 16:33:05.845 - [测试PDK 错误，不能写入Null值] - Stop task milestones: 6707547d5fe35676bdffab44(测试PDK 错误，不能写入Null值)  
[INFO ] 2024-10-10 16:33:05.889 - [测试PDK 错误，不能写入Null值] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:33:05.889 - [测试PDK 错误，不能写入Null值] - Snapshot order controller have been removed 
[INFO ] 2024-10-10 16:33:05.908 - [测试PDK 错误，不能写入Null值] - Remove memory task client succeed, task: 测试PDK 错误，不能写入Null值[6707547d5fe35676bdffab44] 
[INFO ] 2024-10-10 16:33:06.113 - [测试PDK 错误，不能写入Null值] - Destroy memory task client cache succeed, task: 测试PDK 错误，不能写入Null值[6707547d5fe35676bdffab44] 
[INFO ] 2024-10-10 16:33:41.735 - [测试PDK 错误，不能写入Null值] - Start task milestones: 6707547d5fe35676bdffab44(测试PDK 错误，不能写入Null值) 
[INFO ] 2024-10-10 16:33:41.735 - [测试PDK 错误，不能写入Null值] - Task initialization... 
[INFO ] 2024-10-10 16:33:53.820 - [测试PDK 错误，不能写入Null值] - load tapTable task 6707547d5fe35676bdffab43-955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c complete, cost 12001ms 
[INFO ] 2024-10-10 16:33:53.944 - [测试PDK 错误，不能写入Null值] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-10 16:33:53.946 - [测试PDK 错误，不能写入Null值] - The engine receives 测试PDK 错误，不能写入Null值 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-10 16:33:54.008 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:33:54.009 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:33:54.009 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:33:54.009 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:33:54.009 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:33:54.009 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:33:54.009 - [测试PDK 错误，不能写入Null值][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:33:59.616 - [测试PDK 错误，不能写入Null值][testNotNull] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-10 16:33:59.617 - [测试PDK 错误，不能写入Null值][testNotNull] - Sync progress not exists, will run task as first time 
[INFO ] 2024-10-10 16:34:04.168 - [测试PDK 错误，不能写入Null值][testNotNull] - Source node "testNotNull" read batch size: 100 
[INFO ] 2024-10-10 16:34:04.168 - [测试PDK 错误，不能写入Null值][testNotNull] - Source node "testNotNull" event queue capacity: 200 
[INFO ] 2024-10-10 16:34:04.168 - [测试PDK 错误，不能写入Null值][testNotNull] - Sync progress not exists, will run task as first time 
[INFO ] 2024-10-10 16:34:04.168 - [测试PDK 错误，不能写入Null值][testNotNull] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-10 16:34:04.200 - [测试PDK 错误，不能写入Null值][testNotNull] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":3259,"gtidSet":""} 
[INFO ] 2024-10-10 16:34:04.201 - [测试PDK 错误，不能写入Null值][testNotNull] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-10 16:34:04.269 - [测试PDK 错误，不能写入Null值][testNotNull] - Incremental sync starting... 
[INFO ] 2024-10-10 16:34:04.271 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:34:04.316 - [测试PDK 错误，不能写入Null值][testNotNull] - Incremental sync completed 
[INFO ] 2024-10-10 16:34:04.317 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node stopped: HazelcastSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:34:04.317 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node released: HazelcastSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:34:04.317 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:34:04.317 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:34:04.319 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 55 ms 
[INFO ] 2024-10-10 16:34:04.320 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:34:04.320 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:34:04.320 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:34:04.321 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 1 ms 
[INFO ] 2024-10-10 16:34:04.321 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] running status set to false 
[INFO ] 2024-10-10 16:34:04.331 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node stopped: HazelcastTargetPdkDataNode-ebf2fd1a-deb0-4487-a984-5e00499d358d 
[INFO ] 2024-10-10 16:34:04.331 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node released: HazelcastTargetPdkDataNode-ebf2fd1a-deb0-4487-a984-5e00499d358d 
[INFO ] 2024-10-10 16:34:04.331 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] schema data cleaned 
[INFO ] 2024-10-10 16:34:04.331 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] monitor closed 
[INFO ] 2024-10-10 16:34:04.540 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] close complete, cost 11 ms 
[INFO ] 2024-10-10 16:34:05.974 - [测试PDK 错误，不能写入Null值] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-10 16:34:05.978 - [测试PDK 错误，不能写入Null值] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@60dc9527 
[INFO ] 2024-10-10 16:34:06.110 - [测试PDK 错误，不能写入Null值] - Stop task milestones: 6707547d5fe35676bdffab44(测试PDK 错误，不能写入Null值)  
[INFO ] 2024-10-10 16:34:06.110 - [测试PDK 错误，不能写入Null值] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:34:06.110 - [测试PDK 错误，不能写入Null值] - Snapshot order controller have been removed 
[INFO ] 2024-10-10 16:34:06.110 - [测试PDK 错误，不能写入Null值] - Remove memory task client succeed, task: 测试PDK 错误，不能写入Null值[6707547d5fe35676bdffab44] 
[INFO ] 2024-10-10 16:34:06.110 - [测试PDK 错误，不能写入Null值] - Destroy memory task client cache succeed, task: 测试PDK 错误，不能写入Null值[6707547d5fe35676bdffab44] 
[INFO ] 2024-10-10 16:53:44.318 - [测试PDK 错误，不能写入Null值] - load tapTable task 6707547d5fe35676bdffab43-099b4a48-afdc-4c3c-a03c-a2e782899b10 complete, cost 12463ms 
[INFO ] 2024-10-10 16:53:45.794 - [测试PDK 错误，不能写入Null值] - Task initialization... 
[INFO ] 2024-10-10 16:53:45.798 - [测试PDK 错误，不能写入Null值] - Start task milestones: 6707547d5fe35676bdffab44(测试PDK 错误，不能写入Null值) 
[INFO ] 2024-10-10 16:53:58.067 - [测试PDK 错误，不能写入Null值] - load tapTable task 6707547d5fe35676bdffab43-4e14ed06-2341-4c7b-a73c-4c50d805562b complete, cost 12108ms 
[INFO ] 2024-10-10 16:53:58.257 - [测试PDK 错误，不能写入Null值] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-10 16:53:58.259 - [测试PDK 错误，不能写入Null值] - The engine receives 测试PDK 错误，不能写入Null值 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-10 16:53:58.393 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:53:58.394 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:53:58.396 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:53:58.397 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:53:58.400 - [测试PDK 错误，不能写入Null值][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:53:58.439 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:53:58.447 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:54:04.178 - [测试PDK 错误，不能写入Null值][testNotNull] - Source node "testNotNull" read batch size: 100 
[INFO ] 2024-10-10 16:54:04.180 - [测试PDK 错误，不能写入Null值][testNotNull] - Source node "testNotNull" event queue capacity: 200 
[INFO ] 2024-10-10 16:54:04.180 - [测试PDK 错误，不能写入Null值][testNotNull] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-10 16:54:04.202 - [测试PDK 错误，不能写入Null值][testNotNull] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":3259,"gtidSet":""} 
[INFO ] 2024-10-10 16:54:04.270 - [测试PDK 错误，不能写入Null值][testNotNull] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-10 16:54:04.271 - [测试PDK 错误，不能写入Null值][testNotNull] - Initial sync started 
[INFO ] 2024-10-10 16:54:04.291 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting batch read, table name: testNotNull 
[INFO ] 2024-10-10 16:54:04.292 - [测试PDK 错误，不能写入Null值][testNotNull] - Table testNotNull is going to be initial synced 
[INFO ] 2024-10-10 16:54:04.353 - [测试PDK 错误，不能写入Null值][testNotNull] - Query table 'testNotNull' counts: 1 
[INFO ] 2024-10-10 16:54:04.355 - [测试PDK 错误，不能写入Null值][testNotNull] - Table [testNotNull] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-10 16:54:04.359 - [测试PDK 错误，不能写入Null值][testNotNull] - Initial sync completed 
[INFO ] 2024-10-10 16:54:04.359 - [测试PDK 错误，不能写入Null值][testNotNull] - Incremental sync starting... 
[INFO ] 2024-10-10 16:54:04.365 - [测试PDK 错误，不能写入Null值][testNotNull] - Initial sync completed 
[INFO ] 2024-10-10 16:54:04.367 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting stream read, table list: [testNotNull], offset: {"filename":"binlog.000036","position":3259,"gtidSet":""} 
[INFO ] 2024-10-10 16:54:04.493 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting mysql cdc, server name: 715187f7-578a-4ca4-a844-3422721c737c 
[INFO ] 2024-10-10 16:54:04.495 - [测试PDK 错误，不能写入Null值][testNotNull] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"715187f7-578a-4ca4-a844-3422721c737c","offset":{"{\"server\":\"715187f7-578a-4ca4-a844-3422721c737c\"}":"{\"file\":\"binlog.000036\",\"pos\":3259,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1287463102
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 715187f7-578a-4ca4-a844-3422721c737c
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-715187f7-578a-4ca4-a844-3422721c737c
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 715187f7-578a-4ca4-a844-3422721c737c
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.testNotNull
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-10 16:54:04.906 - [测试PDK 错误，不能写入Null值][testNotNull] - Connector Mysql incremental start succeed, tables: [testNotNull], data change syncing 
[INFO ] 2024-10-10 16:54:10.708 - [测试PDK 错误，不能写入Null值][testNotNull] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-10 16:57:34.393 - [测试PDK 错误，不能写入Null值][testNotNull] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@782483b2: {"after":{"id":1},"containsIllegalDate":false,"tableId":"testNotNull","time":1728550444320,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728550444180, sourceSerialNo=null} 
[ERROR] 2024-10-10 16:57:34.462 - [测试PDK 错误，不能写入Null值][testNotNull] - java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@782483b2: {"after":{"id":1},"containsIllegalDate":false,"tableId":"testNotNull","time":1728550444320,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728550444180, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@782483b2: {"after":{"id":1},"containsIllegalDate":false,"tableId":"testNotNull","time":1728550444320,"type":300}, nodeIds=[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8, ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b], sourceTime=1728550444180, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:725)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:666)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:626)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:512)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:598)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:650)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:597)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:814)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$26(HazelcastTargetPdkDataNode.java:514)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:514)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:784)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:717)
	... 12 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectViolateNull(MysqlExceptionCollector.java:147)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:119)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:457)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:865)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$59(HazelcastTargetPdkDataNode.java:820)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:68)
	... 19 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'first_name' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:94)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:108)
	... 32 more

[INFO ] 2024-10-10 16:57:34.465 - [测试PDK 错误，不能写入Null值][testNotNull] - Job suspend in error handle 
[INFO ] 2024-10-10 16:57:34.694 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:57:34.757 - [测试PDK 错误，不能写入Null值][testNotNull] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-10 16:57:34.759 - [测试PDK 错误，不能写入Null值][testNotNull] - Mysql binlog reader stopped 
[INFO ] 2024-10-10 16:57:34.759 - [测试PDK 错误，不能写入Null值][testNotNull] - Incremental sync completed 
[INFO ] 2024-10-10 16:57:34.769 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node stopped: HazelcastSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:57:34.770 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node released: HazelcastSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:57:34.774 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:57:34.776 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:57:34.777 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 85 ms 
[INFO ] 2024-10-10 16:57:34.778 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:57:34.790 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-a7cf23c4-df49-46c8-aead-6bce8381bb6e 
[INFO ] 2024-10-10 16:57:34.795 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-a7cf23c4-df49-46c8-aead-6bce8381bb6e 
[INFO ] 2024-10-10 16:57:34.797 - [测试PDK 错误，不能写入Null值][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab44-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:57:34.803 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3307-36f12c65-2de5-4302-82be-341a50e0bd10 
[INFO ] 2024-10-10 16:57:34.804 - [测试PDK 错误，不能写入Null值][增强JS] - PDK connector node released: ScriptExecutor-mysql3307-36f12c65-2de5-4302-82be-341a50e0bd10 
[INFO ] 2024-10-10 16:57:34.804 - [测试PDK 错误，不能写入Null值][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab44-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754425fe35676bdffab35] schema data cleaned 
[INFO ] 2024-10-10 16:57:34.809 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:57:34.824 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:57:34.824 - [测试PDK 错误，不能写入Null值][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 43 ms 
[INFO ] 2024-10-10 16:57:34.825 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] running status set to false 
[INFO ] 2024-10-10 16:57:34.842 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node stopped: HazelcastTargetPdkDataNode-ebf2fd1a-deb0-4487-a984-5e00499d358d 
[INFO ] 2024-10-10 16:57:34.843 - [测试PDK 错误，不能写入Null值][testNotNull] - PDK connector node released: HazelcastTargetPdkDataNode-ebf2fd1a-deb0-4487-a984-5e00499d358d 
[INFO ] 2024-10-10 16:57:34.843 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] schema data cleaned 
[INFO ] 2024-10-10 16:57:34.843 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] monitor closed 
[INFO ] 2024-10-10 16:57:35.054 - [测试PDK 错误，不能写入Null值][testNotNull] - Node testNotNull[ebf2fd1a-deb0-4487-a984-5e00499d358d] close complete, cost 21 ms 
[INFO ] 2024-10-10 16:57:39.340 - [测试PDK 错误，不能写入Null值] - Task [测试PDK 错误，不能写入Null值] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-10 16:57:39.347 - [测试PDK 错误，不能写入Null值] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-10 16:57:39.347 - [测试PDK 错误，不能写入Null值] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1c23576f 
[INFO ] 2024-10-10 16:57:39.462 - [测试PDK 错误，不能写入Null值] - Stop task milestones: 6707547d5fe35676bdffab44(测试PDK 错误，不能写入Null值)  
[INFO ] 2024-10-10 16:57:39.470 - [测试PDK 错误，不能写入Null值] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:57:39.470 - [测试PDK 错误，不能写入Null值] - Snapshot order controller have been removed 
[INFO ] 2024-10-10 16:57:39.504 - [测试PDK 错误，不能写入Null值] - Remove memory task client succeed, task: 测试PDK 错误，不能写入Null值[6707547d5fe35676bdffab44] 
[INFO ] 2024-10-10 16:57:39.505 - [测试PDK 错误，不能写入Null值] - Destroy memory task client cache succeed, task: 测试PDK 错误，不能写入Null值[6707547d5fe35676bdffab44] 
