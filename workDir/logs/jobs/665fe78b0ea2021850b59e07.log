[INFO ] 2024-06-05 12:21:38.637 - [任务 4] - Task initialization... 
[INFO ] 2024-06-05 12:21:38.677 - [任务 4] - Start task milestones: 665fe78b0ea2021850b59e07(任务 4) 
[INFO ] 2024-06-05 12:21:38.678 - [任务 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-05 12:21:38.891 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-05 12:21:38.895 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[366752d4-c5d4-41be-b3cf-206a2cfda077] start preload schema,table counts: 1 
[INFO ] 2024-06-05 12:21:38.895 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[0464bd66-54bb-45ad-8a14-f7f43b51a500] start preload schema,table counts: 1 
[INFO ] 2024-06-05 12:21:38.938 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[366752d4-c5d4-41be-b3cf-206a2cfda077] preload schema finished, cost 45 ms 
[INFO ] 2024-06-05 12:21:38.939 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[0464bd66-54bb-45ad-8a14-f7f43b51a500] preload schema finished, cost 42 ms 
[INFO ] 2024-06-05 12:21:39.933 - [任务 4][TEST_IMAGE_TABLE] - Source node "TEST_IMAGE_TABLE" read batch size: 100 
[INFO ] 2024-06-05 12:21:39.934 - [任务 4][TEST_IMAGE_TABLE] - Source node "TEST_IMAGE_TABLE" event queue capacity: 200 
[INFO ] 2024-06-05 12:21:39.934 - [任务 4][TEST_IMAGE_TABLE] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-05 12:21:40.092 - [任务 4][TEST_IMAGE_TABLE] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-05 12:21:40.205 - [任务 4][TEST_IMAGE_TABLE] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53780489,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-05 12:21:40.287 - [任务 4][TEST_IMAGE_TABLE] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-05 12:21:40.287 - [任务 4][TEST_IMAGE_TABLE] - Initial sync started 
[INFO ] 2024-06-05 12:21:40.296 - [任务 4][TEST_IMAGE_TABLE] - Starting batch read, table name: TEST_IMAGE_TABLE, offset: null 
[INFO ] 2024-06-05 12:21:40.296 - [任务 4][TEST_IMAGE_TABLE] - Table TEST_IMAGE_TABLE is going to be initial synced 
[INFO ] 2024-06-05 12:21:40.364 - [任务 4][TEST_IMAGE_TABLE] - Query table 'TEST_IMAGE_TABLE' counts: 1 
[INFO ] 2024-06-05 12:21:40.365 - [任务 4][TEST_IMAGE_TABLE] - Table [TEST_IMAGE_TABLE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-05 12:21:40.365 - [任务 4][TEST_IMAGE_TABLE] - Initial sync completed 
[INFO ] 2024-06-05 12:21:40.365 - [任务 4][TEST_IMAGE_TABLE] - Incremental sync starting... 
[INFO ] 2024-06-05 12:21:40.369 - [任务 4][TEST_IMAGE_TABLE] - Initial sync completed 
[INFO ] 2024-06-05 12:21:40.369 - [任务 4][TEST_IMAGE_TABLE] - Starting stream read, table list: [TEST_IMAGE_TABLE], offset: {"sortString":null,"offsetValue":null,"lastScn":53780489,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-05 12:21:40.517 - [任务 4][TEST_IMAGE_TABLE] - The table TEST_IMAGE_TABLE has already exist. 
[INFO ] 2024-06-05 12:21:40.517 - [任务 4][TEST_IMAGE_TABLE] - total start mining scn: 53780489 
[INFO ] 2024-06-05 12:21:41.735 - [任务 4][TEST_IMAGE_TABLE] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-06-05 12:24:59.461 - [任务 4] - Stop task milestones: 665fe78b0ea2021850b59e07(任务 4)  
[INFO ] 2024-06-05 12:24:59.929 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[366752d4-c5d4-41be-b3cf-206a2cfda077] running status set to false 
[INFO ] 2024-06-05 12:24:59.929 - [任务 4][TEST_IMAGE_TABLE] - Log Miner is shutting down... 
[INFO ] 2024-06-05 12:25:00.072 - [任务 4][TEST_IMAGE_TABLE] - Log Miner has been closed! 
[ERROR] 2024-06-05 12:25:00.074 - [任务 4][TEST_IMAGE_TABLE] - Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 53781839 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TEST_IMAGE_TABLE'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 53781839 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TEST_IMAGE_TABLE'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()
 <-- Error Message -->
Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 53781839 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TEST_IMAGE_TABLE'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 53781839 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TEST_IMAGE_TABLE'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()


<-- Simple Stack Trace -->
Caused by: oracle.jdbc.OracleDatabaseException: ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	...

<-- Full Stack Trace -->
Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 53781839 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TEST_IMAGE_TABLE'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 53781839 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TEST_IMAGE_TABLE'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: Error : 1306, Position : 220, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 53781839 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TEST_IMAGE_TABLE'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID FROM V$LOGMNR_CONTENTS WHERE  SCN > 53781839 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('TEST_IMAGE_TABLE'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-06-05 12:25:00.177 - [任务 4][TEST_IMAGE_TABLE] - PDK connector node stopped: HazelcastSourcePdkDataNode-366752d4-c5d4-41be-b3cf-206a2cfda077 
[INFO ] 2024-06-05 12:25:00.177 - [任务 4][TEST_IMAGE_TABLE] - PDK connector node released: HazelcastSourcePdkDataNode-366752d4-c5d4-41be-b3cf-206a2cfda077 
[INFO ] 2024-06-05 12:25:00.177 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[366752d4-c5d4-41be-b3cf-206a2cfda077] schema data cleaned 
[INFO ] 2024-06-05 12:25:00.181 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[366752d4-c5d4-41be-b3cf-206a2cfda077] monitor closed 
[INFO ] 2024-06-05 12:25:00.181 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[366752d4-c5d4-41be-b3cf-206a2cfda077] close complete, cost 301 ms 
[INFO ] 2024-06-05 12:25:00.259 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[0464bd66-54bb-45ad-8a14-f7f43b51a500] running status set to false 
[INFO ] 2024-06-05 12:25:00.260 - [任务 4][TEST_IMAGE_TABLE] - PDK connector node stopped: HazelcastTargetPdkDataNode-0464bd66-54bb-45ad-8a14-f7f43b51a500 
[INFO ] 2024-06-05 12:25:00.261 - [任务 4][TEST_IMAGE_TABLE] - PDK connector node released: HazelcastTargetPdkDataNode-0464bd66-54bb-45ad-8a14-f7f43b51a500 
[INFO ] 2024-06-05 12:25:00.261 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[0464bd66-54bb-45ad-8a14-f7f43b51a500] schema data cleaned 
[INFO ] 2024-06-05 12:25:00.262 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[0464bd66-54bb-45ad-8a14-f7f43b51a500] monitor closed 
[INFO ] 2024-06-05 12:25:00.262 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[0464bd66-54bb-45ad-8a14-f7f43b51a500] close complete, cost 80 ms 
[INFO ] 2024-06-05 12:25:04.654 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-05 12:25:04.654 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-06-05 12:25:04.654 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-06-05 12:25:04.692 - [任务 4] - Remove memory task client succeed, task: 任务 4[665fe78b0ea2021850b59e07] 
[INFO ] 2024-06-05 12:25:04.692 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[665fe78b0ea2021850b59e07] 
[INFO ] 2024-06-05 14:54:57.100 - [任务 4] - Task initialization... 
[INFO ] 2024-06-05 14:54:57.102 - [任务 4] - Start task milestones: 665fe78b0ea2021850b59e07(任务 4) 
[INFO ] 2024-06-05 14:54:57.128 - [任务 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-05 14:54:57.266 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-05 14:54:57.267 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[0464bd66-54bb-45ad-8a14-f7f43b51a500] start preload schema,table counts: 1 
[INFO ] 2024-06-05 14:54:57.268 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[366752d4-c5d4-41be-b3cf-206a2cfda077] start preload schema,table counts: 1 
[INFO ] 2024-06-05 14:54:57.293 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[0464bd66-54bb-45ad-8a14-f7f43b51a500] preload schema finished, cost 22 ms 
[INFO ] 2024-06-05 14:54:57.294 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[366752d4-c5d4-41be-b3cf-206a2cfda077] preload schema finished, cost 21 ms 
[INFO ] 2024-06-05 14:54:58.284 - [任务 4][TEST_IMAGE_TABLE] - Source node "TEST_IMAGE_TABLE" read batch size: 100 
[INFO ] 2024-06-05 14:54:58.284 - [任务 4][TEST_IMAGE_TABLE] - Source node "TEST_IMAGE_TABLE" event queue capacity: 200 
[INFO ] 2024-06-05 14:54:58.284 - [任务 4][TEST_IMAGE_TABLE] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-05 14:54:58.403 - [任务 4][TEST_IMAGE_TABLE] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-05 14:54:58.540 - [任务 4][TEST_IMAGE_TABLE] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53821668,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-05 14:54:58.540 - [任务 4][TEST_IMAGE_TABLE] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-05 14:54:58.619 - [任务 4][TEST_IMAGE_TABLE] - Initial sync started 
[INFO ] 2024-06-05 14:54:58.620 - [任务 4][TEST_IMAGE_TABLE] - Starting batch read, table name: TEST_IMAGE_TABLE, offset: null 
[INFO ] 2024-06-05 14:54:58.629 - [任务 4][TEST_IMAGE_TABLE] - Table TEST_IMAGE_TABLE is going to be initial synced 
[INFO ] 2024-06-05 14:55:01.341 - [任务 4][TEST_IMAGE_TABLE] - Query table 'TEST_IMAGE_TABLE' counts: 2 
[INFO ] 2024-06-05 14:55:01.923 - [任务 4][TEST_IMAGE_TABLE] - The table TEST_IMAGE_TABLE has already exist. 
[INFO ] 2024-06-05 14:55:02.048 - [任务 4][TEST_IMAGE_TABLE] - Table [TEST_IMAGE_TABLE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-05 14:55:02.049 - [任务 4][TEST_IMAGE_TABLE] - Initial sync completed 
[INFO ] 2024-06-05 14:55:02.049 - [任务 4][TEST_IMAGE_TABLE] - Incremental sync starting... 
[INFO ] 2024-06-05 14:55:02.049 - [任务 4][TEST_IMAGE_TABLE] - Initial sync completed 
[INFO ] 2024-06-05 14:55:02.054 - [任务 4][TEST_IMAGE_TABLE] - Starting stream read, table list: [TEST_IMAGE_TABLE], offset: {"sortString":null,"offsetValue":null,"lastScn":53821668,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-05 14:55:02.281 - [任务 4][TEST_IMAGE_TABLE] - total start mining scn: 53821668 
[INFO ] 2024-06-05 14:55:14.936 - [任务 4][TEST_IMAGE_TABLE] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-06-05 14:56:07.541 - [任务 4] - Stop task milestones: 665fe78b0ea2021850b59e07(任务 4)  
[INFO ] 2024-06-05 14:56:08.016 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[366752d4-c5d4-41be-b3cf-206a2cfda077] running status set to false 
[INFO ] 2024-06-05 14:56:08.016 - [任务 4][TEST_IMAGE_TABLE] - Log Miner is shutting down... 
[INFO ] 2024-06-05 14:56:08.047 - [任务 4][TEST_IMAGE_TABLE] - Log Miner has been closed! 
[ERROR] 2024-06-05 14:56:08.049 - [任务 4][TEST_IMAGE_TABLE] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-06-05 14:56:08.120 - [任务 4][TEST_IMAGE_TABLE] - PDK connector node stopped: HazelcastSourcePdkDataNode-366752d4-c5d4-41be-b3cf-206a2cfda077 
[INFO ] 2024-06-05 14:56:08.120 - [任务 4][TEST_IMAGE_TABLE] - PDK connector node released: HazelcastSourcePdkDataNode-366752d4-c5d4-41be-b3cf-206a2cfda077 
[INFO ] 2024-06-05 14:56:08.120 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[366752d4-c5d4-41be-b3cf-206a2cfda077] schema data cleaned 
[INFO ] 2024-06-05 14:56:08.121 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[366752d4-c5d4-41be-b3cf-206a2cfda077] monitor closed 
[INFO ] 2024-06-05 14:56:08.122 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[366752d4-c5d4-41be-b3cf-206a2cfda077] close complete, cost 151 ms 
[INFO ] 2024-06-05 14:56:08.122 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[0464bd66-54bb-45ad-8a14-f7f43b51a500] running status set to false 
[INFO ] 2024-06-05 14:56:08.184 - [任务 4][TEST_IMAGE_TABLE] - PDK connector node stopped: HazelcastTargetPdkDataNode-0464bd66-54bb-45ad-8a14-f7f43b51a500 
[INFO ] 2024-06-05 14:56:08.184 - [任务 4][TEST_IMAGE_TABLE] - PDK connector node released: HazelcastTargetPdkDataNode-0464bd66-54bb-45ad-8a14-f7f43b51a500 
[INFO ] 2024-06-05 14:56:08.185 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[0464bd66-54bb-45ad-8a14-f7f43b51a500] schema data cleaned 
[INFO ] 2024-06-05 14:56:08.186 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[0464bd66-54bb-45ad-8a14-f7f43b51a500] monitor closed 
[INFO ] 2024-06-05 14:56:08.392 - [任务 4][TEST_IMAGE_TABLE] - Node TEST_IMAGE_TABLE[0464bd66-54bb-45ad-8a14-f7f43b51a500] close complete, cost 64 ms 
[INFO ] 2024-06-05 14:56:11.725 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-05 14:56:11.725 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-06-05 14:56:11.756 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-06-05 14:56:11.758 - [任务 4] - Remove memory task client succeed, task: 任务 4[665fe78b0ea2021850b59e07] 
[INFO ] 2024-06-05 14:56:11.758 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[665fe78b0ea2021850b59e07] 
