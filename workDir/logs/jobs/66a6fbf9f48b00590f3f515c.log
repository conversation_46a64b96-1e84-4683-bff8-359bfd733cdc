[WARN ] 2024-07-30 16:10:47.808 - [<PERSON><PERSON>-<PERSON><PERSON>] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=5ece3e71a4b54657ac105d31e33554f0b7937ddf42c04cf78e2b151895606424&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266a6fbf9f48b00590f3f515c%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@37c004c4, CDC=io.tapdata.milestone.entity.MilestoneEntity@88f6fb5, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@5b7859de, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@6da469e9, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@5af5b6bd, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@29f48891}, attrs.nodeMilestones={4bc3d983-0aa9-4059-a9d1-cd3d4ded610f={CDC_WRITE=io.tapdata.milestone.entity.MilestoneEntity@30db82d8, NODE=io.tapdata.milestone.entity.MilestoneEntity@7efe4e0b, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@9494f37, SNAPSHOT_WRITE=io.tapdata.milestone.entity.MilestoneEntity@4f4fe8f}, 64a755d2-1861-43b7-8861-3bdf4aaa6296={OPEN_CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@68d5718b, NODE=io.tapdata.milestone.entity.MilestoneEntity@6457f48c, SNAPSHOT_READ=io.tapdata.milestone.entity.MilestoneEntity@27242fc6, CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@487bd2b}}, syncStatus=CDC}}}}, code='IllegalArgument', data=null, reqId=400b502a-e652-42f7-89aa-069a07617610}: 无效参数: task not found 
[INFO ] 2024-07-30 16:10:48.024 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[64a755d2-1861-43b7-8861-3bdf4aaa6296] running status set to false 
[INFO ] 2024-07-30 16:10:48.059 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-30 16:10:48.167 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-64a755d2-1861-43b7-8861-3bdf4aaa6296 
[INFO ] 2024-07-30 16:10:48.172 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-64a755d2-1861-43b7-8861-3bdf4aaa6296 
[INFO ] 2024-07-30 16:10:48.174 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[64a755d2-1861-43b7-8861-3bdf4aaa6296] schema data cleaned 
[INFO ] 2024-07-30 16:10:48.179 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[64a755d2-1861-43b7-8861-3bdf4aaa6296] monitor closed 
[INFO ] 2024-07-30 16:10:48.191 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[64a755d2-1861-43b7-8861-3bdf4aaa6296] close complete, cost 183 ms 
[INFO ] 2024-07-30 16:10:48.193 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4bc3d983-0aa9-4059-a9d1-cd3d4ded610f] running status set to false 
[INFO ] 2024-07-30 16:10:48.326 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-4bc3d983-0aa9-4059-a9d1-cd3d4ded610f 
[INFO ] 2024-07-30 16:10:48.328 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-4bc3d983-0aa9-4059-a9d1-cd3d4ded610f 
[INFO ] 2024-07-30 16:10:48.328 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4bc3d983-0aa9-4059-a9d1-cd3d4ded610f] schema data cleaned 
[INFO ] 2024-07-30 16:10:48.328 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4bc3d983-0aa9-4059-a9d1-cd3d4ded610f] monitor closed 
[INFO ] 2024-07-30 16:10:48.433 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4bc3d983-0aa9-4059-a9d1-cd3d4ded610f] close complete, cost 138 ms 
[WARN ] 2024-07-30 16:10:52.778 - [Heartbeat-Mongo] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=f7bfafdcdbab440f81829972faabe0b8e6a0052419c04707a34c2087eafe5f1f&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266a6fbf9f48b00590f3f515c%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@37c004c4, CDC=io.tapdata.milestone.entity.MilestoneEntity@88f6fb5, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@5b7859de, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@6da469e9, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@5af5b6bd, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@29f48891}, attrs.nodeMilestones={4bc3d983-0aa9-4059-a9d1-cd3d4ded610f={CDC_WRITE=io.tapdata.milestone.entity.MilestoneEntity@30db82d8, NODE=io.tapdata.milestone.entity.MilestoneEntity@7efe4e0b, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@9494f37, SNAPSHOT_WRITE=io.tapdata.milestone.entity.MilestoneEntity@4f4fe8f}, 64a755d2-1861-43b7-8861-3bdf4aaa6296={OPEN_CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@68d5718b, NODE=io.tapdata.milestone.entity.MilestoneEntity@6457f48c, SNAPSHOT_READ=io.tapdata.milestone.entity.MilestoneEntity@27242fc6, CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@487bd2b}}, syncStatus=CDC}}}}, code='IllegalArgument', data=null, reqId=c08451cb-f410-4395-bd03-98901a9c6039}: 无效参数: task not found 
[INFO ] 2024-07-30 16:10:52.816 - [Heartbeat-Mongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5229479 
[INFO ] 2024-07-30 16:10:52.847 - [Heartbeat-Mongo] - Stop task milestones: 66a6fbf9f48b00590f3f515c(Heartbeat-Mongo)  
[WARN ] 2024-07-30 16:10:52.962 - [Heartbeat-Mongo] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=f7bfafdcdbab440f81829972faabe0b8e6a0052419c04707a34c2087eafe5f1f&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266a6fbf9f48b00590f3f515c%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@37c004c4, CDC=io.tapdata.milestone.entity.MilestoneEntity@88f6fb5, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@5b7859de, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@6da469e9, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@5af5b6bd, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@29f48891}, attrs.nodeMilestones={4bc3d983-0aa9-4059-a9d1-cd3d4ded610f={CDC_WRITE=io.tapdata.milestone.entity.MilestoneEntity@30db82d8, NODE=io.tapdata.milestone.entity.MilestoneEntity@7efe4e0b, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@9494f37, SNAPSHOT_WRITE=io.tapdata.milestone.entity.MilestoneEntity@4f4fe8f}, 64a755d2-1861-43b7-8861-3bdf4aaa6296={OPEN_CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@68d5718b, NODE=io.tapdata.milestone.entity.MilestoneEntity@6457f48c, SNAPSHOT_READ=io.tapdata.milestone.entity.MilestoneEntity@27242fc6, CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@487bd2b}}, syncStatus=CDC}}}}, code='IllegalArgument', data=null, reqId=58d10b58-7cf7-4d90-b93d-9f558429e05b}: 无效参数: task not found 
[INFO ] 2024-07-30 16:10:53.315 - [Heartbeat-Mongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-30 16:10:53.317 - [Heartbeat-Mongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5229479 
[INFO ] 2024-07-30 16:10:53.317 - [Heartbeat-Mongo] - Stopped task aspect(s) 
[INFO ] 2024-07-30 16:10:53.322 - [Heartbeat-Mongo] - Remove memory task client succeed, task: Heartbeat-Mongo[66a6fbf9f48b00590f3f515c] 
[INFO ] 2024-07-30 16:10:53.326 - [Heartbeat-Mongo] - Destroy memory task client cache succeed, task: Heartbeat-Mongo[66a6fbf9f48b00590f3f515c] 
