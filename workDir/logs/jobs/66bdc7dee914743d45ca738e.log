[INFO ] 2024-08-22 10:30:31.384 - [任务 9] - Start task milestones: 66bdc7dee914743d45ca738e(任务 9) 
[INFO ] 2024-08-22 10:30:31.401 - [任务 9] - Task initialization... 
[INFO ] 2024-08-22 10:30:31.599 - [任务 9] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-08-22 10:30:31.726 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-22 10:30:32.291 - [任务 9][Oracle] - Node Oracle[1734601f-363d-4436-8bda-fda285ae051a] start preload schema,table counts: 1 
[INFO ] 2024-08-22 10:30:32.466 - [任务 9][Mysql3306] - Node Mysql3306[4b8a9a99-42b2-45f1-b9fb-692042132b0a] start preload schema,table counts: 1 
[INFO ] 2024-08-22 10:30:32.467 - [任务 9][Mysql3306] - Node Mysql3306[4b8a9a99-42b2-45f1-b9fb-692042132b0a] preload schema finished, cost 153 ms 
[INFO ] 2024-08-22 10:30:32.676 - [任务 9][Oracle] - Node Oracle[1734601f-363d-4436-8bda-fda285ae051a] preload schema finished, cost 173 ms 
[INFO ] 2024-08-22 10:30:33.494 - [任务 9][Mysql3306] - Node(Mysql3306) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-22 10:30:33.495 - [任务 9][Mysql3306] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-22 10:30:34.631 - [任务 9][Oracle] - Source node "Oracle" read batch size: 100 
[INFO ] 2024-08-22 10:30:34.631 - [任务 9][Oracle] - Source node "Oracle" event queue capacity: 200 
[INFO ] 2024-08-22 10:30:34.631 - [任务 9][Oracle] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-22 10:30:34.652 - [任务 9][Oracle] - batch offset found: {},stream offset found: {"sequenceMap":{"TESTDDL":13},"streamOffset":{"sortString":null,"offsetValue":null,"lastScn":96993219,"pendingScn":96997134,"timestamp":1723781778000,"hexScn":null,"fno":0}} 
[INFO ] 2024-08-22 10:30:34.855 - [任务 9][Oracle] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-22 10:30:34.870 - [任务 9][Oracle] - Incremental sync starting... 
[INFO ] 2024-08-22 10:30:34.870 - [任务 9][Oracle] - Initial sync completed 
[INFO ] 2024-08-22 10:30:34.928 - [任务 9][Oracle] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-08-22 10:30:34.935 - [任务 9][Oracle] - [Share CDC Task HZ Reader] - Step 1 - Check connection Oracle enable share cdc: true 
[INFO ] 2024-08-22 10:30:34.936 - [任务 9][Oracle] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 9 enable share cdc: true 
[INFO ] 2024-08-22 10:30:35.027 - [任务 9][Oracle] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自Oracle的共享挖掘任务 
[INFO ] 2024-08-22 10:30:35.027 - [任务 9][Oracle] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav360?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-08-22 10:30:35.136 - [任务 9][Oracle] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66bdc839eb2fcdff3ba6a36b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66bdc702e914743d45ca7383_TESTDDL, version=v2, tableName=TESTDDL, externalStorageTableName=ExternalStorage_SHARE_CDC_456145858, shareCdcTaskId=66bdc839e914743d45ca7403, connectionId=66bdc702e914743d45ca7383) 
[INFO ] 2024-08-22 10:30:35.136 - [任务 9][Oracle] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Oracle的共享挖掘任务_C##TAPDATA.TESTDDL_任务 9', name space: 'tapdatav360.ExternalStorage_SHARE_CDC_456145858', head seq: 0, tail seq: 12 
[INFO ] 2024-08-22 10:30:35.152 - [任务 9][Oracle] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-08-22 10:30:35.154 - [任务 9][Oracle] - Init share cdc reader completed 
[INFO ] 2024-08-22 10:30:35.154 - [任务 9][Oracle] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-08-22 10:30:35.155 - [任务 9][Oracle] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-08-22 10:30:35.174 - [任务 9][Oracle] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-08-22 10:30:35.176 - [任务 9][Oracle] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66bdc839eb2fcdff3ba6a36b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66bdc702e914743d45ca7383_TESTDDL, version=v2, tableName=TESTDDL, externalStorageTableName=ExternalStorage_SHARE_CDC_456145858, shareCdcTaskId=66bdc839e914743d45ca7403, connectionId=66bdc702e914743d45ca7383) 
[INFO ] 2024-08-22 10:30:35.184 - [任务 9][Oracle] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Oracle的共享挖掘任务_C##TAPDATA.TESTDDL_任务 9', name space: 'tapdatav360.ExternalStorage_SHARE_CDC_456145858', head seq: 0, tail seq: 12 
[INFO ] 2024-08-22 10:30:35.184 - [任务 9][Oracle] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自Oracle的共享挖掘任务_C##TAPDATA.TESTDDL_任务 9, external storage name: ExternalStorage_SHARE_CDC_456145858 
[INFO ] 2024-08-22 10:30:35.193 - [任务 9][Oracle] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [TESTDDL] 
[INFO ] 2024-08-22 10:30:35.194 - [任务 9][Oracle] - Connector Oracle incremental start succeed, tables: [TESTDDL], data change syncing 
[INFO ] 2024-08-22 10:30:35.194 - [任务 9][Oracle] - [Share CDC Task HZ Reader] - Starting read 'TESTDDL' log, sequence: 13 
[INFO ] 2024-08-22 10:30:35.402 - [任务 9][Oracle] - [Share CDC Task HZ Reader] - Find by TESTDDL filter: {sequence=13} 
[INFO ] 2024-08-22 14:52:37.098 - [任务 9] - Stop task milestones: 66bdc7dee914743d45ca738e(任务 9)  
[INFO ] 2024-08-22 14:52:37.652 - [任务 9][Oracle] - Node Oracle[1734601f-363d-4436-8bda-fda285ae051a] running status set to false 
[INFO ] 2024-08-22 14:52:37.653 - [任务 9][Oracle] - PDK connector node stopped: HazelcastSourcePdkDataNode-1734601f-363d-4436-8bda-fda285ae051a 
[INFO ] 2024-08-22 14:52:37.653 - [任务 9][Oracle] - PDK connector node released: HazelcastSourcePdkDataNode-1734601f-363d-4436-8bda-fda285ae051a 
[INFO ] 2024-08-22 14:52:37.654 - [任务 9][Oracle] - Node Oracle[1734601f-363d-4436-8bda-fda285ae051a] schema data cleaned 
[INFO ] 2024-08-22 14:52:37.655 - [任务 9][Oracle] - Node Oracle[1734601f-363d-4436-8bda-fda285ae051a] monitor closed 
[INFO ] 2024-08-22 14:52:37.655 - [任务 9][Oracle] - Node Oracle[1734601f-363d-4436-8bda-fda285ae051a] close complete, cost 106 ms 
[INFO ] 2024-08-22 14:52:37.668 - [任务 9][Mysql3306] - Node Mysql3306[4b8a9a99-42b2-45f1-b9fb-692042132b0a] running status set to false 
[INFO ] 2024-08-22 14:52:37.669 - [任务 9][Mysql3306] - PDK connector node stopped: HazelcastTargetPdkDataNode-4b8a9a99-42b2-45f1-b9fb-692042132b0a 
[INFO ] 2024-08-22 14:52:37.669 - [任务 9][Mysql3306] - PDK connector node released: HazelcastTargetPdkDataNode-4b8a9a99-42b2-45f1-b9fb-692042132b0a 
[INFO ] 2024-08-22 14:52:37.669 - [任务 9][Mysql3306] - Node Mysql3306[4b8a9a99-42b2-45f1-b9fb-692042132b0a] schema data cleaned 
[INFO ] 2024-08-22 14:52:37.669 - [任务 9][Mysql3306] - Node Mysql3306[4b8a9a99-42b2-45f1-b9fb-692042132b0a] monitor closed 
[INFO ] 2024-08-22 14:52:37.669 - [任务 9][Mysql3306] - Node Mysql3306[4b8a9a99-42b2-45f1-b9fb-692042132b0a] close complete, cost 13 ms 
[INFO ] 2024-08-22 14:52:38.034 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-22 14:52:38.035 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-08-22 14:52:38.035 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-08-22 14:52:38.066 - [任务 9] - Remove memory task client succeed, task: 任务 9[66bdc7dee914743d45ca738e] 
[INFO ] 2024-08-22 14:52:38.066 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[66bdc7dee914743d45ca738e] 
