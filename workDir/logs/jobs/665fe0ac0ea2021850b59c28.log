[INFO ] 2024-06-07 08:46:41.294 - [Long] - Start task milestones: 665fe0ac0ea2021850b59c28(Long) 
[INFO ] 2024-06-07 08:46:41.306 - [Long] - Task initialization... 
[INFO ] 2024-06-07 08:46:41.452 - [Long] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-06-07 08:46:41.453 - [Long] - The engine receives Long task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-07 08:46:41.518 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] start preload schema,table counts: 1 
[INFO ] 2024-06-07 08:46:41.518 - [Long][TESTLONGCOPY] - Node TESTLONGCOPY[bfef8801-3001-4f05-974e-66a73964ff77] start preload schema,table counts: 1 
[INFO ] 2024-06-07 08:46:41.553 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] preload schema finished, cost 48 ms 
[INFO ] 2024-06-07 08:46:41.554 - [Long][TESTLONGCOPY] - Node TESTLONGCOPY[bfef8801-3001-4f05-974e-66a73964ff77] preload schema finished, cost 34 ms 
[INFO ] 2024-06-07 08:46:42.767 - [Long][TEST1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-07 08:46:42.830 - [Long][TESTLONGCOPY] - Source node "TESTLONGCOPY" read batch size: 1 
[INFO ] 2024-06-07 08:46:42.830 - [Long][TESTLONGCOPY] - Source node "TESTLONGCOPY" event queue capacity: 2 
[INFO ] 2024-06-07 08:46:42.830 - [Long][TESTLONGCOPY] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-06-07 08:46:43.032 - [Long][TESTLONGCOPY] - Found pending transaction, please check if there are any earlier transactions to prevent data loss, the earliest startScn: 54362544 
[INFO ] 2024-06-07 08:46:43.248 - [Long][TESTLONGCOPY] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":54362635,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-07 08:46:43.249 - [Long][TESTLONGCOPY] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-07 08:46:43.370 - [Long][TESTLONGCOPY] - Initial sync started 
[INFO ] 2024-06-07 08:46:43.373 - [Long][TESTLONGCOPY] - Starting batch read, table name: TESTLONGCOPY, offset: null 
[INFO ] 2024-06-07 08:46:43.374 - [Long][TESTLONGCOPY] - Table TESTLONGCOPY is going to be initial synced 
[INFO ] 2024-06-07 08:46:43.433 - [Long][TESTLONGCOPY] - Query table 'TESTLONGCOPY' counts: 17 
[INFO ] 2024-06-07 08:46:43.733 - [Long][TESTLONGCOPY] - Table [TESTLONGCOPY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-07 08:46:43.736 - [Long][TESTLONGCOPY] - Initial sync completed 
[INFO ] 2024-06-07 08:46:43.738 - [Long][TESTLONGCOPY] - Incremental sync starting... 
[INFO ] 2024-06-07 08:46:43.738 - [Long][TESTLONGCOPY] - Initial sync completed 
[INFO ] 2024-06-07 08:46:43.861 - [Long][TESTLONGCOPY] - Starting stream read, table list: [TESTLONGCOPY], offset: {"sortString":null,"offsetValue":null,"lastScn":54362635,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-07 08:46:43.861 - [Long][TESTLONGCOPY] - total start mining scn: 54362635 
[INFO ] 2024-06-07 08:46:44.983 - [Long][TESTLONGCOPY] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-06-07 09:01:02.647 - [Long] - Stop task milestones: 665fe0ac0ea2021850b59c28(Long)  
[INFO ] 2024-06-07 09:01:02.709 - [Long][TESTLONGCOPY] - Node TESTLONGCOPY[bfef8801-3001-4f05-974e-66a73964ff77] running status set to false 
[INFO ] 2024-06-07 09:01:02.728 - [Long][TESTLONGCOPY] - Log Miner is shutting down... 
[INFO ] 2024-06-07 09:01:02.728 - [Long][TESTLONGCOPY] - Log Miner has been closed! 
[ERROR] 2024-06-07 09:01:02.778 - [Long][TESTLONGCOPY] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-06-07 09:01:02.779 - [Long][TESTLONGCOPY] - PDK connector node stopped: HazelcastSourcePdkDataNode-bfef8801-3001-4f05-974e-66a73964ff77 
[INFO ] 2024-06-07 09:01:02.780 - [Long][TESTLONGCOPY] - PDK connector node released: HazelcastSourcePdkDataNode-bfef8801-3001-4f05-974e-66a73964ff77 
[INFO ] 2024-06-07 09:01:02.780 - [Long][TESTLONGCOPY] - Node TESTLONGCOPY[bfef8801-3001-4f05-974e-66a73964ff77] schema data cleaned 
[INFO ] 2024-06-07 09:01:02.780 - [Long][TESTLONGCOPY] - Node TESTLONGCOPY[bfef8801-3001-4f05-974e-66a73964ff77] monitor closed 
[INFO ] 2024-06-07 09:01:02.780 - [Long][TESTLONGCOPY] - Node TESTLONGCOPY[bfef8801-3001-4f05-974e-66a73964ff77] close complete, cost 76 ms 
[INFO ] 2024-06-07 09:01:02.809 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] running status set to false 
[INFO ] 2024-06-07 09:01:02.809 - [Long][TEST1] - PDK connector node stopped: HazelcastTargetPdkDataNode-2e553adc-e160-427f-9531-2f28f2fe60ca 
[INFO ] 2024-06-07 09:01:02.810 - [Long][TEST1] - PDK connector node released: HazelcastTargetPdkDataNode-2e553adc-e160-427f-9531-2f28f2fe60ca 
[INFO ] 2024-06-07 09:01:02.810 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] schema data cleaned 
[INFO ] 2024-06-07 09:01:02.810 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] monitor closed 
[INFO ] 2024-06-07 09:01:02.810 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] close complete, cost 29 ms 
[INFO ] 2024-06-07 09:01:06.846 - [Long] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-07 09:01:06.846 - [Long] - Stopped task aspect(s) 
[INFO ] 2024-06-07 09:01:06.846 - [Long] - Snapshot order controller have been removed 
[INFO ] 2024-06-07 09:01:06.876 - [Long] - Remove memory task client succeed, task: Long[665fe0ac0ea2021850b59c28] 
[INFO ] 2024-06-07 09:01:06.877 - [Long] - Destroy memory task client cache succeed, task: Long[665fe0ac0ea2021850b59c28] 
[INFO ] 2024-06-07 10:30:54.495 - [Long] - Start task milestones: 665fe0ac0ea2021850b59c28(Long) 
[INFO ] 2024-06-07 10:30:54.496 - [Long] - Task initialization... 
[INFO ] 2024-06-07 10:30:54.497 - [Long] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-07 10:30:54.601 - [Long] - The engine receives Long task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-07 10:30:54.954 - [Long][TESTLONGCOPY] - Node TESTLONGCOPY[bfef8801-3001-4f05-974e-66a73964ff77] start preload schema,table counts: 1 
[INFO ] 2024-06-07 10:30:54.959 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] start preload schema,table counts: 1 
[INFO ] 2024-06-07 10:30:55.174 - [Long][TESTLONGCOPY] - Node TESTLONGCOPY[bfef8801-3001-4f05-974e-66a73964ff77] preload schema finished, cost 215 ms 
[INFO ] 2024-06-07 10:30:55.378 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] preload schema finished, cost 214 ms 
[INFO ] 2024-06-07 10:30:56.341 - [Long][TESTLONGCOPY] - Source node "TESTLONGCOPY" read batch size: 1 
[INFO ] 2024-06-07 10:30:56.346 - [Long][TESTLONGCOPY] - Source node "TESTLONGCOPY" event queue capacity: 2 
[INFO ] 2024-06-07 10:30:56.346 - [Long][TESTLONGCOPY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-07 10:30:56.512 - [Long][TEST1] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-06-07 10:30:56.564 - [Long][TESTLONGCOPY] - Found pending transaction, please check if there are any earlier transactions to prevent data loss, the earliest startScn: 54362749 
[INFO ] 2024-06-07 10:30:56.834 - [Long][TESTLONGCOPY] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":54380019,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-07 10:30:56.835 - [Long][TESTLONGCOPY] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-07 10:30:57.040 - [Long][TESTLONGCOPY] - Initial sync started 
[INFO ] 2024-06-07 10:30:57.057 - [Long][TESTLONGCOPY] - Starting batch read, table name: TESTLONGCOPY, offset: null 
[INFO ] 2024-06-07 10:30:57.057 - [Long][TESTLONGCOPY] - Table TESTLONGCOPY is going to be initial synced 
[INFO ] 2024-06-07 10:30:57.103 - [Long][TESTLONGCOPY] - Query table 'TESTLONGCOPY' counts: 17 
[INFO ] 2024-06-07 10:30:57.423 - [Long][TESTLONGCOPY] - Table [TESTLONGCOPY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-07 10:30:57.423 - [Long][TESTLONGCOPY] - Initial sync completed 
[INFO ] 2024-06-07 10:30:57.430 - [Long][TESTLONGCOPY] - Incremental sync starting... 
[INFO ] 2024-06-07 10:30:57.430 - [Long][TESTLONGCOPY] - Initial sync completed 
[INFO ] 2024-06-07 10:31:00.625 - [Long][TESTLONGCOPY] - Starting stream read, table list: [TESTLONGCOPY], offset: {"sortString":null,"offsetValue":null,"lastScn":54380019,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-07 10:31:00.841 - [Long][TESTLONGCOPY] - total start mining scn: 54380019 
[INFO ] 2024-06-07 10:31:01.822 - [Long][TESTLONGCOPY] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-06-07 10:34:16.106 - [Long] - Stop task milestones: 665fe0ac0ea2021850b59c28(Long)  
[INFO ] 2024-06-07 10:34:16.269 - [Long][TESTLONGCOPY] - Node TESTLONGCOPY[bfef8801-3001-4f05-974e-66a73964ff77] running status set to false 
[INFO ] 2024-06-07 10:34:16.291 - [Long][TESTLONGCOPY] - Log Miner is shutting down... 
[INFO ] 2024-06-07 10:34:16.295 - [Long][TESTLONGCOPY] - Log Miner has been closed! 
[INFO ] 2024-06-07 10:34:16.357 - [Long][TESTLONGCOPY] - PDK connector node stopped: HazelcastSourcePdkDataNode-bfef8801-3001-4f05-974e-66a73964ff77 
[INFO ] 2024-06-07 10:34:16.358 - [Long][TESTLONGCOPY] - PDK connector node released: HazelcastSourcePdkDataNode-bfef8801-3001-4f05-974e-66a73964ff77 
[ERROR] 2024-06-07 10:34:16.362 - [Long][TESTLONGCOPY] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-06-07 10:34:16.362 - [Long][TESTLONGCOPY] - Node TESTLONGCOPY[bfef8801-3001-4f05-974e-66a73964ff77] schema data cleaned 
[INFO ] 2024-06-07 10:34:16.367 - [Long][TESTLONGCOPY] - Node TESTLONGCOPY[bfef8801-3001-4f05-974e-66a73964ff77] monitor closed 
[INFO ] 2024-06-07 10:34:16.368 - [Long][TESTLONGCOPY] - Node TESTLONGCOPY[bfef8801-3001-4f05-974e-66a73964ff77] close complete, cost 104 ms 
[INFO ] 2024-06-07 10:34:16.414 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] running status set to false 
[INFO ] 2024-06-07 10:34:16.415 - [Long][TEST1] - PDK connector node stopped: HazelcastTargetPdkDataNode-2e553adc-e160-427f-9531-2f28f2fe60ca 
[INFO ] 2024-06-07 10:34:16.416 - [Long][TEST1] - PDK connector node released: HazelcastTargetPdkDataNode-2e553adc-e160-427f-9531-2f28f2fe60ca 
[INFO ] 2024-06-07 10:34:16.416 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] schema data cleaned 
[INFO ] 2024-06-07 10:34:16.419 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] monitor closed 
[INFO ] 2024-06-07 10:34:16.419 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] close complete, cost 50 ms 
[INFO ] 2024-06-07 10:34:20.824 - [Long] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-07 10:34:20.824 - [Long] - Stopped task aspect(s) 
[INFO ] 2024-06-07 10:34:20.862 - [Long] - Snapshot order controller have been removed 
[INFO ] 2024-06-07 10:34:20.862 - [Long] - Remove memory task client succeed, task: Long[665fe0ac0ea2021850b59c28] 
[INFO ] 2024-06-07 10:34:21.064 - [Long] - Destroy memory task client cache succeed, task: Long[665fe0ac0ea2021850b59c28] 
[INFO ] 2024-06-07 10:35:30.762 - [Long] - Start task milestones: 665fe0ac0ea2021850b59c28(Long) 
[INFO ] 2024-06-07 10:35:30.765 - [Long] - Task initialization... 
[INFO ] 2024-06-07 10:35:30.855 - [Long] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-07 10:35:30.855 - [Long] - The engine receives Long task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-07 10:35:30.924 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] start preload schema,table counts: 1 
[INFO ] 2024-06-07 10:35:30.930 - [Long][TESTLONGCOPY] - Node TESTLONGT1[bfef8801-3001-4f05-974e-66a73964ff77] start preload schema,table counts: 1 
[INFO ] 2024-06-07 10:35:30.958 - [Long][TESTLONGCOPY] - Node TESTLONGT1[bfef8801-3001-4f05-974e-66a73964ff77] preload schema finished, cost 25 ms 
[INFO ] 2024-06-07 10:35:30.959 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] preload schema finished, cost 31 ms 
[INFO ] 2024-06-07 10:35:31.961 - [Long][TESTLONGCOPY] - Source node "TESTLONGT1" read batch size: 1 
[INFO ] 2024-06-07 10:35:31.962 - [Long][TESTLONGCOPY] - Source node "TESTLONGT1" event queue capacity: 2 
[INFO ] 2024-06-07 10:35:31.962 - [Long][TESTLONGCOPY] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-06-07 10:35:32.191 - [Long][TESTLONGCOPY] - Found pending transaction, please check if there are any earlier transactions to prevent data loss, the earliest startScn: 54362749 
[INFO ] 2024-06-07 10:35:32.396 - [Long][TEST1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-07 10:35:32.421 - [Long][TESTLONGCOPY] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":54381014,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-07 10:35:32.421 - [Long][TESTLONGCOPY] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-07 10:35:32.488 - [Long][TESTLONGCOPY] - Initial sync started 
[INFO ] 2024-06-07 10:35:32.494 - [Long][TESTLONGCOPY] - Starting batch read, table name: TESTLONGT1, offset: null 
[INFO ] 2024-06-07 10:35:32.494 - [Long][TESTLONGCOPY] - Table TESTLONGT1 is going to be initial synced 
[INFO ] 2024-06-07 10:35:32.695 - [Long][TESTLONGCOPY] - Query table 'TESTLONGT1' counts: 17 
[INFO ] 2024-06-07 10:35:32.716 - [Long][TESTLONGCOPY] - Table [TESTLONGT1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-07 10:35:32.716 - [Long][TESTLONGCOPY] - Initial sync completed 
[INFO ] 2024-06-07 10:35:32.716 - [Long][TESTLONGCOPY] - Incremental sync starting... 
[INFO ] 2024-06-07 10:35:32.717 - [Long][TESTLONGCOPY] - Initial sync completed 
[INFO ] 2024-06-07 10:35:32.839 - [Long][TESTLONGCOPY] - Starting stream read, table list: [TESTLONGT1], offset: {"sortString":null,"offsetValue":null,"lastScn":54381014,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-07 10:35:32.839 - [Long][TESTLONGCOPY] - total start mining scn: 54381014 
[INFO ] 2024-06-07 10:35:33.954 - [Long][TESTLONGCOPY] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-06-07 10:44:27.296 - [Long] - Stop task milestones: 665fe0ac0ea2021850b59c28(Long)  
[INFO ] 2024-06-07 10:44:27.483 - [Long][TESTLONGCOPY] - Node TESTLONGT1[bfef8801-3001-4f05-974e-66a73964ff77] running status set to false 
[INFO ] 2024-06-07 10:44:27.503 - [Long][TESTLONGCOPY] - Log Miner is shutting down... 
[INFO ] 2024-06-07 10:44:27.504 - [Long][TESTLONGCOPY] - Log Miner has been closed! 
[ERROR] 2024-06-07 10:44:27.559 - [Long][TESTLONGCOPY] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-06-07 10:44:27.560 - [Long][TESTLONGCOPY] - PDK connector node stopped: HazelcastSourcePdkDataNode-bfef8801-3001-4f05-974e-66a73964ff77 
[INFO ] 2024-06-07 10:44:27.560 - [Long][TESTLONGCOPY] - PDK connector node released: HazelcastSourcePdkDataNode-bfef8801-3001-4f05-974e-66a73964ff77 
[INFO ] 2024-06-07 10:44:27.562 - [Long][TESTLONGCOPY] - Node TESTLONGT1[bfef8801-3001-4f05-974e-66a73964ff77] schema data cleaned 
[INFO ] 2024-06-07 10:44:27.563 - [Long][TESTLONGCOPY] - Node TESTLONGT1[bfef8801-3001-4f05-974e-66a73964ff77] monitor closed 
[INFO ] 2024-06-07 10:44:27.564 - [Long][TESTLONGCOPY] - Node TESTLONGT1[bfef8801-3001-4f05-974e-66a73964ff77] close complete, cost 88 ms 
[INFO ] 2024-06-07 10:44:27.637 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] running status set to false 
[INFO ] 2024-06-07 10:44:27.637 - [Long][TEST1] - PDK connector node stopped: HazelcastTargetPdkDataNode-2e553adc-e160-427f-9531-2f28f2fe60ca 
[INFO ] 2024-06-07 10:44:27.637 - [Long][TEST1] - PDK connector node released: HazelcastTargetPdkDataNode-2e553adc-e160-427f-9531-2f28f2fe60ca 
[INFO ] 2024-06-07 10:44:27.637 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] schema data cleaned 
[INFO ] 2024-06-07 10:44:27.638 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] monitor closed 
[INFO ] 2024-06-07 10:44:27.639 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] close complete, cost 73 ms 
[INFO ] 2024-06-07 10:44:28.787 - [Long] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-07 10:44:28.788 - [Long] - Stopped task aspect(s) 
[INFO ] 2024-06-07 10:44:28.813 - [Long] - Snapshot order controller have been removed 
[INFO ] 2024-06-07 10:44:28.814 - [Long] - Remove memory task client succeed, task: Long[665fe0ac0ea2021850b59c28] 
[INFO ] 2024-06-07 10:44:28.815 - [Long] - Destroy memory task client cache succeed, task: Long[665fe0ac0ea2021850b59c28] 
[INFO ] 2024-06-07 10:44:50.794 - [Long] - Start task milestones: 665fe0ac0ea2021850b59c28(Long) 
[INFO ] 2024-06-07 10:44:50.794 - [Long] - Task initialization... 
[INFO ] 2024-06-07 10:44:50.800 - [Long] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-07 10:44:50.910 - [Long] - The engine receives Long task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-07 10:44:50.912 - [Long][TESTLONGCOPY] - Node TESTLONGT2[bfef8801-3001-4f05-974e-66a73964ff77] start preload schema,table counts: 1 
[INFO ] 2024-06-07 10:44:50.937 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] start preload schema,table counts: 1 
[INFO ] 2024-06-07 10:44:50.937 - [Long][TESTLONGCOPY] - Node TESTLONGT2[bfef8801-3001-4f05-974e-66a73964ff77] preload schema finished, cost 25 ms 
[INFO ] 2024-06-07 10:44:51.146 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] preload schema finished, cost 21 ms 
[INFO ] 2024-06-07 10:44:52.008 - [Long][TESTLONGCOPY] - Source node "TESTLONGT2" read batch size: 1 
[INFO ] 2024-06-07 10:44:52.009 - [Long][TESTLONGCOPY] - Source node "TESTLONGT2" event queue capacity: 2 
[INFO ] 2024-06-07 10:44:52.009 - [Long][TESTLONGCOPY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-07 10:44:52.129 - [Long][TEST1] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-06-07 10:44:52.222 - [Long][TESTLONGCOPY] - Found pending transaction, please check if there are any earlier transactions to prevent data loss, the earliest startScn: 54362749 
[INFO ] 2024-06-07 10:44:52.429 - [Long][TESTLONGCOPY] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":54381577,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-07 10:44:52.429 - [Long][TESTLONGCOPY] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-07 10:44:52.489 - [Long][TESTLONGCOPY] - Initial sync started 
[INFO ] 2024-06-07 10:44:52.489 - [Long][TESTLONGCOPY] - Starting batch read, table name: TESTLONGT2, offset: null 
[INFO ] 2024-06-07 10:44:52.499 - [Long][TESTLONGCOPY] - Table TESTLONGT2 is going to be initial synced 
[INFO ] 2024-06-07 10:44:52.701 - [Long][TESTLONGCOPY] - Query table 'TESTLONGT2' counts: 17 
[INFO ] 2024-06-07 10:44:52.838 - [Long][TESTLONGCOPY] - Table [TESTLONGT2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-07 10:44:52.839 - [Long][TESTLONGCOPY] - Initial sync completed 
[INFO ] 2024-06-07 10:44:52.840 - [Long][TESTLONGCOPY] - Incremental sync starting... 
[INFO ] 2024-06-07 10:44:52.841 - [Long][TESTLONGCOPY] - Initial sync completed 
[INFO ] 2024-06-07 10:44:52.842 - [Long][TESTLONGCOPY] - Starting stream read, table list: [TESTLONGT2], offset: {"sortString":null,"offsetValue":null,"lastScn":54381577,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-07 10:44:53.044 - [Long][TESTLONGCOPY] - total start mining scn: 54381577 
[INFO ] 2024-06-07 10:44:54.276 - [Long][TESTLONGCOPY] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[WARN ] 2024-06-07 13:29:59.402 - [Long][TESTLONGCOPY] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-07 13:30:03.023 - [Long][TESTLONGCOPY] - Log Miner is shutting down... 
[INFO ] 2024-06-07 13:30:03.023 - [Long][TESTLONGCOPY] - Log Miner has been closed! 
[INFO ] 2024-06-07 13:30:03.699 - [Long][TESTLONGCOPY] - total start mining scn: 54419050 
[INFO ] 2024-06-07 13:30:04.905 - [Long][TESTLONGCOPY] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-06-07 13:30:43.353 - [Long][TESTLONGCOPY] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-06-07 14:01:31.918 - [Long][TESTLONGCOPY] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_06_07/o1_mf_1_1072_m658j1h2_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-06-07 14:01:39.489 - [Long][TESTLONGCOPY] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-06-07 15:26:32.299 - [Long] - Stop task milestones: 665fe0ac0ea2021850b59c28(Long)  
[INFO ] 2024-06-07 15:26:32.646 - [Long][TESTLONGCOPY] - Node TESTLONGT2[bfef8801-3001-4f05-974e-66a73964ff77] running status set to false 
[INFO ] 2024-06-07 15:26:32.701 - [Long][TESTLONGCOPY] - Log Miner is shutting down... 
[ERROR] 2024-06-07 15:26:32.703 - [Long][TESTLONGCOPY] - java.lang.RuntimeException: java.sql.SQLRecoverableException: IO 错误: Socket read interrupted <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLRecoverableException: IO 错误: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLRecoverableException: IO 错误: Socket read interrupted
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLRecoverableException: IO 错误: Socket read interrupted
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.checkArchiveAndOnlineLogWithScn(SingleOracleLogMiner.java:227)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:111)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 18 more
Caused by: java.sql.SQLRecoverableException: IO 错误: Socket read interrupted
	at oracle.jdbc.driver.T4CConnection.doRollback(T4CConnection.java:2075)
	at oracle.jdbc.driver.PhysicalConnection.rollback(PhysicalConnection.java:2704)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:257)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.checkArchiveAndOnlineLogWithScn(SingleOracleLogMiner.java:226)
	... 23 more
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C7Ocommoncall.doOROLLBACK(T4C7Ocommoncall.java:84)
	at oracle.jdbc.driver.T4CConnection.doRollback(T4CConnection.java:2065)
	... 26 more

[INFO ] 2024-06-07 15:26:32.779 - [Long][TESTLONGCOPY] - Log Miner has been closed! 
[INFO ] 2024-06-07 15:26:32.779 - [Long][TESTLONGCOPY] - PDK connector node stopped: HazelcastSourcePdkDataNode-bfef8801-3001-4f05-974e-66a73964ff77 
[INFO ] 2024-06-07 15:26:32.779 - [Long][TESTLONGCOPY] - PDK connector node released: HazelcastSourcePdkDataNode-bfef8801-3001-4f05-974e-66a73964ff77 
[INFO ] 2024-06-07 15:26:32.780 - [Long][TESTLONGCOPY] - Node TESTLONGT2[bfef8801-3001-4f05-974e-66a73964ff77] schema data cleaned 
[INFO ] 2024-06-07 15:26:32.781 - [Long][TESTLONGCOPY] - Node TESTLONGT2[bfef8801-3001-4f05-974e-66a73964ff77] monitor closed 
[INFO ] 2024-06-07 15:26:32.781 - [Long][TESTLONGCOPY] - Node TESTLONGT2[bfef8801-3001-4f05-974e-66a73964ff77] close complete, cost 142 ms 
[INFO ] 2024-06-07 15:26:32.782 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] running status set to false 
[INFO ] 2024-06-07 15:26:32.824 - [Long][TEST1] - PDK connector node stopped: HazelcastTargetPdkDataNode-2e553adc-e160-427f-9531-2f28f2fe60ca 
[INFO ] 2024-06-07 15:26:32.824 - [Long][TEST1] - PDK connector node released: HazelcastTargetPdkDataNode-2e553adc-e160-427f-9531-2f28f2fe60ca 
[INFO ] 2024-06-07 15:26:32.825 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] schema data cleaned 
[INFO ] 2024-06-07 15:26:32.825 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] monitor closed 
[INFO ] 2024-06-07 15:26:33.030 - [Long][TEST1] - Node TEST1[2e553adc-e160-427f-9531-2f28f2fe60ca] close complete, cost 43 ms 
[INFO ] 2024-06-07 15:26:36.941 - [Long] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-07 15:26:36.941 - [Long] - Stopped task aspect(s) 
[INFO ] 2024-06-07 15:26:36.941 - [Long] - Snapshot order controller have been removed 
[INFO ] 2024-06-07 15:26:36.968 - [Long] - Remove memory task client succeed, task: Long[665fe0ac0ea2021850b59c28] 
[INFO ] 2024-06-07 15:26:36.971 - [Long] - Destroy memory task client cache succeed, task: Long[665fe0ac0ea2021850b59c28] 
