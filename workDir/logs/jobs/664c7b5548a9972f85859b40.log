[INFO ] 2024-05-22 10:50:14.675 - [任务 13] - Start task milestones: 664c7b5548a9972f85859b40(任务 13) 
[INFO ] 2024-05-22 10:50:14.694 - [任务 13] - Task initialization... 
[INFO ] 2024-05-22 10:50:14.695 - [任务 13] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-22 10:50:14.756 - [任务 13] - The engine receives 任务 13 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-22 10:50:15.135 - [任务 13][targetAA_5010] - Node targetAA_5010[fd64d36f-c202-4a35-8577-b3974ac4e624] start preload schema,table counts: 1 
[INFO ] 2024-05-22 10:50:15.135 - [任务 13][AA_0510] - Node AA_0510[870a620f-fbc2-45c0-972f-6620637a1c35] start preload schema,table counts: 1 
[INFO ] 2024-05-22 10:50:15.303 - [任务 13][targetAA_5010] - Node targetAA_5010[fd64d36f-c202-4a35-8577-b3974ac4e624] preload schema finished, cost 149 ms 
[INFO ] 2024-05-22 10:50:15.311 - [任务 13][AA_0510] - Node AA_0510[870a620f-fbc2-45c0-972f-6620637a1c35] preload schema finished, cost 146 ms 
[INFO ] 2024-05-22 10:50:16.525 - [任务 13][targetAA_5010] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-22 10:50:16.800 - [任务 13][AA_0510] - Source node "AA_0510" read batch size: 100 
[INFO ] 2024-05-22 10:50:16.800 - [任务 13][AA_0510] - Source node "AA_0510" event queue capacity: 200 
[INFO ] 2024-05-22 10:50:16.834 - [任务 13][AA_0510] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-22 10:50:16.834 - [任务 13][AA_0510] - batch offset found: {},stream offset found: {"filename":"mariadb-bin.000307","position":31430,"gtidSet":null} 
[INFO ] 2024-05-22 10:50:16.835 - [任务 13][AA_0510] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-22 10:50:17.005 - [任务 13][AA_0510] - Initial sync started 
[INFO ] 2024-05-22 10:50:17.029 - [任务 13][AA_0510] - Starting batch read, table name: AA_0510, offset: null 
[INFO ] 2024-05-22 10:50:17.030 - [任务 13][AA_0510] - Table AA_0510 is going to be initial synced 
[INFO ] 2024-05-22 10:50:17.163 - [任务 13][AA_0510] - Query table 'AA_0510' counts: 6 
[INFO ] 2024-05-22 10:50:17.165 - [任务 13][AA_0510] - Table [AA_0510] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-22 10:50:17.166 - [任务 13][AA_0510] - Initial sync completed 
[INFO ] 2024-05-22 10:50:17.168 - [任务 13][AA_0510] - Incremental sync starting... 
[INFO ] 2024-05-22 10:50:17.169 - [任务 13][AA_0510] - Initial sync completed 
[INFO ] 2024-05-22 10:50:17.253 - [任务 13][AA_0510] - Starting stream read, table list: [AA_0510], offset: {"filename":"mariadb-bin.000307","position":31430,"gtidSet":null} 
[INFO ] 2024-05-22 10:50:17.253 - [任务 13][AA_0510] - Starting mysql cdc, server name: 22fc7241-2593-4acf-88ab-c8e31c82d28c 
[INFO ] 2024-05-22 10:50:17.459 - [任务 13][AA_0510] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 948269192
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 22fc7241-2593-4acf-88ab-c8e31c82d28c
  database.port: 13306
  threadName: Debezium-Mysql-Connector-22fc7241-2593-4acf-88ab-c8e31c82d28c
  database.hostname: *************
  database.password: ********
  name: 22fc7241-2593-4acf-88ab-c8e31c82d28c
  pdk.offset.string: {"name":"22fc7241-2593-4acf-88ab-c8e31c82d28c","offset":{"{\"server\":\"22fc7241-2593-4acf-88ab-c8e31c82d28c\"}":"{\"file\":\"mariadb-bin.000307\",\"pos\":31430,\"server_id\":\"334455\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: demo.AA_0510
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: demo
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-05-22 10:50:22.006 - [任务 13][AA_0510] - Connector Mariadb incremental start succeed, tables: [AA_0510], data change syncing 
[INFO ] 2024-05-22 11:13:13.008 - [任务 13][AA_0510] - Read DDL: alter table AA_0510 modify column not_upload tinyint(10) default 2, about to be packaged as some event(s) 
[INFO ] 2024-05-22 11:13:13.010 - [任务 13][AA_0510] - DDL event  - Table: AA_0510
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='22fc7241-2593-4acf-88ab-c8e31c82d28c', offset={{"server":"22fc7241-2593-4acf-88ab-c8e31c82d28c"}={"ts_sec":1716347592,"file":"mariadb-bin.000307","pos":32330,"server_id":334455}}} 
[INFO ] 2024-05-22 11:13:13.010 - [任务 13][AA_0510] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='AA_0510', fieldName='not_upload', dataTypeChange=ValueChange{before=null, after=tinyint(10)}, checkChange=null, constraintChange=null, nullableChange=null, commentChange=null, defaultChange=ValueChange{before=null, after=2}, primaryChange=null} 
[INFO ] 2024-05-22 11:13:13.175 - [任务 13][AA_0510] - Alter table in memory, qualified name: T_mariadb_io_tapdata_1_0-SNAPSHOT_AA_0510_664c7aae48a9972f85859b28_664c7b5548a9972f85859b40 
[INFO ] 2024-05-22 11:13:13.788 - [任务 13][AA_0510] - Alter table schema transform finished 
[INFO ] 2024-05-22 11:15:20.375 - [任务 13][AA_0510] - Read DDL: alter table AA_0510 change column not_upload bbc int default 3, about to be packaged as some event(s) 
[INFO ] 2024-05-22 11:15:20.376 - [任务 13][AA_0510] - Read DDL: alter table AA_0510 change column not_upload bbc int default 3, about to be packaged as some event(s) 
[INFO ] 2024-05-22 11:15:20.376 - [任务 13][AA_0510] - DDL event  - Table: AA_0510
  - Event type: TapAlterFieldNameEvent
  - Offset: MysqlStreamOffset{name='22fc7241-2593-4acf-88ab-c8e31c82d28c', offset={{"server":"22fc7241-2593-4acf-88ab-c8e31c82d28c"}={"ts_sec":1716347719,"file":"mariadb-bin.000307","pos":32660,"server_id":334455}}} 
[INFO ] 2024-05-22 11:15:20.376 - [任务 13][AA_0510] - DDL event  - Table: AA_0510
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='22fc7241-2593-4acf-88ab-c8e31c82d28c', offset={{"server":"22fc7241-2593-4acf-88ab-c8e31c82d28c"}={"ts_sec":1716347719,"file":"mariadb-bin.000307","pos":32660,"server_id":334455}}} 
[INFO ] 2024-05-22 11:15:20.376 - [任务 13][AA_0510] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='AA_0510', nameChange=ValueChange{before=not_upload, after=bbc}} 
[INFO ] 2024-05-22 11:15:20.376 - [任务 13][AA_0510] - Alter table in memory, qualified name: T_mariadb_io_tapdata_1_0-SNAPSHOT_AA_0510_664c7aae48a9972f85859b28_664c7b5548a9972f85859b40 
[INFO ] 2024-05-22 11:15:20.376 - [任务 13][AA_0510] - Alter table schema transform finished 
[INFO ] 2024-05-22 11:15:20.377 - [任务 13][AA_0510] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='AA_0510', fieldName='bbc', dataTypeChange=ValueChange{before=null, after=int}, checkChange=null, constraintChange=null, nullableChange=null, commentChange=null, defaultChange=ValueChange{before=null, after=3}, primaryChange=null} 
[INFO ] 2024-05-22 11:15:20.377 - [任务 13][AA_0510] - Alter table in memory, qualified name: T_mariadb_io_tapdata_1_0-SNAPSHOT_AA_0510_664c7aae48a9972f85859b28_664c7b5548a9972f85859b40 
[INFO ] 2024-05-22 11:15:20.377 - [任务 13][AA_0510] - Alter table schema transform finished 
[ERROR] 2024-05-22 11:15:20.580 - [任务 13][targetAA_5010] - java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'column `not_upload` to `bbc`' at line 1 <-- Error Message -->
java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'column `not_upload` to `bbc`' at line 1

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'column `not_upload` to `bbc`' at line 1
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'column `not_upload` to `bbc`' at line 1
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$executeAlterFieldNameFunction$38(HazelcastTargetPdkDataNode.java:616)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeAlterFieldNameFunction(HazelcastTargetPdkDataNode.java:611)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:517)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$27(HazelcastTargetPdkDataNode.java:503)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1406)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:490)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:670)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:578)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:564)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:513)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:476)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:482)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:528)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'column `not_upload` to `bbc`' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.fieldDDLHandler(CommonDbConnector.java:436)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$37(HazelcastTargetPdkDataNode.java:617)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 28 more

[INFO ] 2024-05-22 11:15:20.786 - [任务 13][targetAA_5010] - Job suspend in error handle 
[INFO ] 2024-05-22 11:15:21.197 - [任务 13][AA_0510] - Node AA_0510[870a620f-fbc2-45c0-972f-6620637a1c35] running status set to false 
[INFO ] 2024-05-22 11:15:21.197 - [任务 13][AA_0510] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-05-22 11:15:21.198 - [任务 13][AA_0510] - Mysql binlog reader stopped 
[INFO ] 2024-05-22 11:15:21.199 - [任务 13][AA_0510] - Incremental sync completed 
[INFO ] 2024-05-22 11:15:21.213 - [任务 13][AA_0510] - PDK connector node stopped: HazelcastSourcePdkDataNode-870a620f-fbc2-45c0-972f-6620637a1c35 
[INFO ] 2024-05-22 11:15:21.213 - [任务 13][AA_0510] - PDK connector node released: HazelcastSourcePdkDataNode-870a620f-fbc2-45c0-972f-6620637a1c35 
[INFO ] 2024-05-22 11:15:21.215 - [任务 13][AA_0510] - Node AA_0510[870a620f-fbc2-45c0-972f-6620637a1c35] schema data cleaned 
[INFO ] 2024-05-22 11:15:21.216 - [任务 13][AA_0510] - Node AA_0510[870a620f-fbc2-45c0-972f-6620637a1c35] monitor closed 
[INFO ] 2024-05-22 11:15:21.226 - [任务 13][AA_0510] - Node AA_0510[870a620f-fbc2-45c0-972f-6620637a1c35] close complete, cost 118 ms 
[INFO ] 2024-05-22 11:15:21.227 - [任务 13][targetAA_5010] - Node targetAA_5010[fd64d36f-c202-4a35-8577-b3974ac4e624] running status set to false 
[INFO ] 2024-05-22 11:15:21.264 - [任务 13][targetAA_5010] - PDK connector node stopped: HazelcastTargetPdkDataNode-fd64d36f-c202-4a35-8577-b3974ac4e624 
[INFO ] 2024-05-22 11:15:21.264 - [任务 13][targetAA_5010] - PDK connector node released: HazelcastTargetPdkDataNode-fd64d36f-c202-4a35-8577-b3974ac4e624 
[INFO ] 2024-05-22 11:15:21.265 - [任务 13][targetAA_5010] - Node targetAA_5010[fd64d36f-c202-4a35-8577-b3974ac4e624] schema data cleaned 
[INFO ] 2024-05-22 11:15:21.266 - [任务 13][targetAA_5010] - Node targetAA_5010[fd64d36f-c202-4a35-8577-b3974ac4e624] monitor closed 
[INFO ] 2024-05-22 11:15:21.274 - [任务 13][targetAA_5010] - Node targetAA_5010[fd64d36f-c202-4a35-8577-b3974ac4e624] close complete, cost 42 ms 
[INFO ] 2024-05-22 11:15:21.277 - [任务 13] - Task [任务 13] cannot retry, reason: Task retry service not start 
[INFO ] 2024-05-22 11:15:26.302 - [任务 13] - Task [任务 13] cannot retry, reason: Task retry service not start 
[INFO ] 2024-05-22 11:15:26.352 - [任务 13] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-22 11:15:26.354 - [任务 13] - Stop task milestones: 664c7b5548a9972f85859b40(任务 13)  
[INFO ] 2024-05-22 11:15:26.390 - [任务 13] - Stopped task aspect(s) 
[INFO ] 2024-05-22 11:15:26.391 - [任务 13] - Snapshot order controller have been removed 
[INFO ] 2024-05-22 11:15:26.438 - [任务 13] - Remove memory task client succeed, task: 任务 13[664c7b5548a9972f85859b40] 
[INFO ] 2024-05-22 11:15:26.640 - [任务 13] - Destroy memory task client cache succeed, task: 任务 13[664c7b5548a9972f85859b40] 
[INFO ] 2024-05-22 11:24:19.471 - [任务 13] - Start task milestones: 664c7b5548a9972f85859b40(任务 13) 
[INFO ] 2024-05-22 11:24:19.476 - [任务 13] - Task initialization... 
[INFO ] 2024-05-22 11:24:19.478 - [任务 13] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-22 11:24:19.478 - [任务 13] - The engine receives 任务 13 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-22 11:24:19.478 - [任务 13][targetAA_5010] - Node targetAA_5010[fd64d36f-c202-4a35-8577-b3974ac4e624] start preload schema,table counts: 1 
[INFO ] 2024-05-22 11:24:19.478 - [任务 13][AA_0510] - Node AA_0510[870a620f-fbc2-45c0-972f-6620637a1c35] start preload schema,table counts: 1 
[INFO ] 2024-05-22 11:24:19.581 - [任务 13][targetAA_5010] - Node targetAA_5010[fd64d36f-c202-4a35-8577-b3974ac4e624] preload schema finished, cost 103 ms 
[INFO ] 2024-05-22 11:24:19.582 - [任务 13][AA_0510] - Node AA_0510[870a620f-fbc2-45c0-972f-6620637a1c35] preload schema finished, cost 103 ms 
[INFO ] 2024-05-22 11:24:20.626 - [任务 13][AA_0510] - Source node "AA_0510" read batch size: 100 
[INFO ] 2024-05-22 11:24:20.627 - [任务 13][AA_0510] - Source node "AA_0510" event queue capacity: 200 
[INFO ] 2024-05-22 11:24:20.627 - [任务 13][AA_0510] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-22 11:24:20.648 - [任务 13][AA_0510] - batch offset found: {},stream offset found: {"filename":"mariadb-bin.000307","position":32660,"gtidSet":null} 
[INFO ] 2024-05-22 11:24:20.649 - [任务 13][AA_0510] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-22 11:24:20.691 - [任务 13][AA_0510] - Initial sync started 
[INFO ] 2024-05-22 11:24:20.707 - [任务 13][AA_0510] - Starting batch read, table name: AA_0510, offset: null 
[INFO ] 2024-05-22 11:24:20.708 - [任务 13][AA_0510] - Table AA_0510 is going to be initial synced 
[INFO ] 2024-05-22 11:24:20.785 - [任务 13][AA_0510] - Query table 'AA_0510' counts: 6 
[INFO ] 2024-05-22 11:24:20.785 - [任务 13][AA_0510] - Table [AA_0510] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-22 11:24:20.786 - [任务 13][AA_0510] - Initial sync completed 
[INFO ] 2024-05-22 11:24:20.786 - [任务 13][AA_0510] - Incremental sync starting... 
[INFO ] 2024-05-22 11:24:20.792 - [任务 13][AA_0510] - Initial sync completed 
[INFO ] 2024-05-22 11:24:20.794 - [任务 13][AA_0510] - Starting stream read, table list: [AA_0510], offset: {"filename":"mariadb-bin.000307","position":32660,"gtidSet":null} 
[INFO ] 2024-05-22 11:24:20.876 - [任务 13][targetAA_5010] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-22 11:24:20.880 - [任务 13][AA_0510] - Starting mysql cdc, server name: 8a4469db-6d8c-4dcf-8ba8-4d39d46e929f 
[INFO ] 2024-05-22 11:24:21.072 - [任务 13][AA_0510] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2024366324
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 8a4469db-6d8c-4dcf-8ba8-4d39d46e929f
  database.port: 13306
  threadName: Debezium-Mysql-Connector-8a4469db-6d8c-4dcf-8ba8-4d39d46e929f
  database.hostname: *************
  database.password: ********
  name: 8a4469db-6d8c-4dcf-8ba8-4d39d46e929f
  pdk.offset.string: {"name":"8a4469db-6d8c-4dcf-8ba8-4d39d46e929f","offset":{"{\"server\":\"8a4469db-6d8c-4dcf-8ba8-4d39d46e929f\"}":"{\"file\":\"mariadb-bin.000307\",\"pos\":32660,\"server_id\":\"334455\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: demo.AA_0510
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: demo
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-05-22 11:24:21.073 - [任务 13][AA_0510] - Connector Mariadb incremental start succeed, tables: [AA_0510], data change syncing 
[INFO ] 2024-05-22 11:26:16.085 - [任务 13][AA_0510] - Read DDL: alter table AA_0510 modify column bbc tinyint(4) default 2, about to be packaged as some event(s) 
[INFO ] 2024-05-22 11:26:16.085 - [任务 13][AA_0510] - DDL event  - Table: AA_0510
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='8a4469db-6d8c-4dcf-8ba8-4d39d46e929f', offset={{"server":"8a4469db-6d8c-4dcf-8ba8-4d39d46e929f"}={"ts_sec":1716348375,"file":"mariadb-bin.000307","pos":33658,"server_id":334455}}} 
[INFO ] 2024-05-22 11:26:16.085 - [任务 13][AA_0510] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='AA_0510', fieldName='bbc', dataTypeChange=ValueChange{before=null, after=tinyint(4)}, checkChange=null, constraintChange=null, nullableChange=null, commentChange=null, defaultChange=ValueChange{before=null, after=2}, primaryChange=null} 
[INFO ] 2024-05-22 11:26:16.096 - [任务 13][AA_0510] - Alter table in memory, qualified name: T_mariadb_io_tapdata_1_0-SNAPSHOT_AA_0510_664c7aae48a9972f85859b28_664c7b5548a9972f85859b40 
[INFO ] 2024-05-22 11:26:16.305 - [任务 13][AA_0510] - Alter table schema transform finished 
[INFO ] 2024-05-22 11:30:50.652 - [任务 13][AA_0510] - Read DDL: alter table AA_0510 modify column bbc tinyint(4) default 3, about to be packaged as some event(s) 
[INFO ] 2024-05-22 11:30:50.653 - [任务 13][AA_0510] - DDL event  - Table: AA_0510
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='8a4469db-6d8c-4dcf-8ba8-4d39d46e929f', offset={{"server":"8a4469db-6d8c-4dcf-8ba8-4d39d46e929f"}={"ts_sec":1716348458,"file":"mariadb-bin.000307","pos":33976,"server_id":334455}}} 
[INFO ] 2024-05-22 11:30:50.653 - [任务 13][AA_0510] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='AA_0510', fieldName='bbc', dataTypeChange=ValueChange{before=null, after=tinyint(4)}, checkChange=null, constraintChange=null, nullableChange=null, commentChange=null, defaultChange=ValueChange{before=null, after=3}, primaryChange=null} 
[INFO ] 2024-05-22 11:30:50.727 - [任务 13][AA_0510] - Alter table in memory, qualified name: T_mariadb_io_tapdata_1_0-SNAPSHOT_AA_0510_664c7aae48a9972f85859b28_664c7b5548a9972f85859b40 
[INFO ] 2024-05-22 11:30:50.728 - [任务 13][AA_0510] - Alter table schema transform finished 
[INFO ] 2024-05-22 11:40:28.031 - [任务 13][AA_0510] - Read DDL: alter table AA_0510 modify column bbc tinyint(4) default 4, about to be packaged as some event(s) 
[INFO ] 2024-05-22 11:40:28.031 - [任务 13][AA_0510] - DDL event  - Table: AA_0510
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='8a4469db-6d8c-4dcf-8ba8-4d39d46e929f', offset={{"server":"8a4469db-6d8c-4dcf-8ba8-4d39d46e929f"}={"ts_sec":1716349224,"file":"mariadb-bin.000307","pos":34294,"server_id":334455}}} 
[INFO ] 2024-05-22 11:40:28.031 - [任务 13][AA_0510] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='AA_0510', fieldName='bbc', dataTypeChange=ValueChange{before=null, after=tinyint(4)}, checkChange=null, constraintChange=null, nullableChange=null, commentChange=null, defaultChange=ValueChange{before=null, after=4}, primaryChange=null} 
[INFO ] 2024-05-22 11:40:28.042 - [任务 13][AA_0510] - Alter table in memory, qualified name: T_mariadb_io_tapdata_1_0-SNAPSHOT_AA_0510_664c7aae48a9972f85859b28_664c7b5548a9972f85859b40 
[INFO ] 2024-05-22 11:40:28.246 - [任务 13][AA_0510] - Alter table schema transform finished 
[INFO ] 2024-05-22 11:43:14.609 - [任务 13][AA_0510] - Read DDL: alter table AA_0510 modify column bbc tinyint(4) default 4 comment 'abcd', about to be packaged as some event(s) 
[INFO ] 2024-05-22 11:43:14.610 - [任务 13][AA_0510] - DDL event  - Table: AA_0510
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='8a4469db-6d8c-4dcf-8ba8-4d39d46e929f', offset={{"server":"8a4469db-6d8c-4dcf-8ba8-4d39d46e929f"}={"ts_sec":1716349394,"file":"mariadb-bin.000307","pos":34627,"server_id":334455}}} 
[INFO ] 2024-05-22 11:43:14.610 - [任务 13][AA_0510] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='AA_0510', fieldName='bbc', dataTypeChange=ValueChange{before=null, after=tinyint(4)}, checkChange=null, constraintChange=null, nullableChange=null, commentChange=ValueChange{before=null, after=abcd}, defaultChange=ValueChange{before=null, after=4}, primaryChange=null} 
[INFO ] 2024-05-22 11:43:14.708 - [任务 13][AA_0510] - Alter table in memory, qualified name: T_mariadb_io_tapdata_1_0-SNAPSHOT_AA_0510_664c7aae48a9972f85859b28_664c7b5548a9972f85859b40 
[INFO ] 2024-05-22 11:43:14.708 - [任务 13][AA_0510] - Alter table schema transform finished 
[INFO ] 2024-05-22 11:45:05.099 - [任务 13][AA_0510] - Read DDL: alter table AA_0510 modify column bbc tinyint(4)  comment 'abcdd', about to be packaged as some event(s) 
[INFO ] 2024-05-22 11:45:05.099 - [任务 13][AA_0510] - DDL event  - Table: AA_0510
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='8a4469db-6d8c-4dcf-8ba8-4d39d46e929f', offset={{"server":"8a4469db-6d8c-4dcf-8ba8-4d39d46e929f"}={"ts_sec":1716349504,"file":"mariadb-bin.000307","pos":34967,"server_id":334455}}} 
[INFO ] 2024-05-22 11:45:05.099 - [任务 13][AA_0510] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='AA_0510', fieldName='bbc', dataTypeChange=ValueChange{before=null, after=tinyint(4)}, checkChange=null, constraintChange=null, nullableChange=null, commentChange=ValueChange{before=null, after=abcdd}, defaultChange=null, primaryChange=null} 
[INFO ] 2024-05-22 11:45:05.106 - [任务 13][AA_0510] - Alter table in memory, qualified name: T_mariadb_io_tapdata_1_0-SNAPSHOT_AA_0510_664c7aae48a9972f85859b28_664c7b5548a9972f85859b40 
[INFO ] 2024-05-22 11:45:05.203 - [任务 13][AA_0510] - Alter table schema transform finished 
[INFO ] 2024-05-22 11:47:24.152 - [任务 13][AA_0510] - Read DDL: alter table AA_0510 modify column bbc tinyint(4) default 3 comment 'abcddg', about to be packaged as some event(s) 
[INFO ] 2024-05-22 11:47:24.152 - [任务 13][AA_0510] - DDL event  - Table: AA_0510
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='8a4469db-6d8c-4dcf-8ba8-4d39d46e929f', offset={{"server":"8a4469db-6d8c-4dcf-8ba8-4d39d46e929f"}={"ts_sec":1716349643,"file":"mariadb-bin.000307","pos":35318,"server_id":334455}}} 
[INFO ] 2024-05-22 11:47:24.152 - [任务 13][AA_0510] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='AA_0510', fieldName='bbc', dataTypeChange=ValueChange{before=null, after=tinyint(4)}, checkChange=null, constraintChange=null, nullableChange=null, commentChange=ValueChange{before=null, after=abcddg}, defaultChange=ValueChange{before=null, after=3}, primaryChange=null} 
[INFO ] 2024-05-22 11:47:24.152 - [任务 13][AA_0510] - Alter table in memory, qualified name: T_mariadb_io_tapdata_1_0-SNAPSHOT_AA_0510_664c7aae48a9972f85859b28_664c7b5548a9972f85859b40 
[INFO ] 2024-05-22 11:47:24.152 - [任务 13][AA_0510] - Alter table schema transform finished 
[INFO ] 2024-05-22 11:47:55.562 - [任务 13][AA_0510] - Read DDL: alter table AA_0510 modify column bbc tinyint(4)  comment 'abcddvg', about to be packaged as some event(s) 
[INFO ] 2024-05-22 11:47:55.562 - [任务 13][AA_0510] - DDL event  - Table: AA_0510
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='8a4469db-6d8c-4dcf-8ba8-4d39d46e929f', offset={{"server":"8a4469db-6d8c-4dcf-8ba8-4d39d46e929f"}={"ts_sec":1716349675,"file":"mariadb-bin.000307","pos":35662,"server_id":334455}}} 
[INFO ] 2024-05-22 11:47:55.566 - [任务 13][AA_0510] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='AA_0510', fieldName='bbc', dataTypeChange=ValueChange{before=null, after=tinyint(4)}, checkChange=null, constraintChange=null, nullableChange=null, commentChange=ValueChange{before=null, after=abcddvg}, defaultChange=null, primaryChange=null} 
[INFO ] 2024-05-22 11:47:55.566 - [任务 13][AA_0510] - Alter table in memory, qualified name: T_mariadb_io_tapdata_1_0-SNAPSHOT_AA_0510_664c7aae48a9972f85859b28_664c7b5548a9972f85859b40 
[INFO ] 2024-05-22 11:47:55.770 - [任务 13][AA_0510] - Alter table schema transform finished 
[INFO ] 2024-05-22 11:52:27.615 - [任务 13][AA_0510] - Read DDL: alter table AA_0510 modify column bbc tinyint(4) default 4 comment 'abcddggvg', about to be packaged as some event(s) 
[INFO ] 2024-05-22 11:52:27.616 - [任务 13][AA_0510] - DDL event  - Table: AA_0510
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='8a4469db-6d8c-4dcf-8ba8-4d39d46e929f', offset={{"server":"8a4469db-6d8c-4dcf-8ba8-4d39d46e929f"}={"ts_sec":1716349944,"file":"mariadb-bin.000307","pos":36018,"server_id":334455}}} 
[INFO ] 2024-05-22 11:52:27.616 - [任务 13][AA_0510] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='AA_0510', fieldName='bbc', dataTypeChange=ValueChange{before=null, after=tinyint(4)}, checkChange=null, constraintChange=null, nullableChange=null, commentChange=ValueChange{before=null, after=abcddggvg}, defaultChange=ValueChange{before=null, after=4}, primaryChange=null} 
[INFO ] 2024-05-22 11:52:27.692 - [任务 13][AA_0510] - Alter table in memory, qualified name: T_mariadb_io_tapdata_1_0-SNAPSHOT_AA_0510_664c7aae48a9972f85859b28_664c7b5548a9972f85859b40 
[INFO ] 2024-05-22 11:52:27.692 - [任务 13][AA_0510] - Alter table schema transform finished 
[INFO ] 2024-05-22 11:55:44.924 - [任务 13][AA_0510] - Read DDL: alter table AA_0510 modify column bbc tinyint(4) default 4 comment 'abcddggvgggg', about to be packaged as some event(s) 
[INFO ] 2024-05-22 11:55:44.925 - [任务 13][AA_0510] - DDL event  - Table: AA_0510
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='8a4469db-6d8c-4dcf-8ba8-4d39d46e929f', offset={{"server":"8a4469db-6d8c-4dcf-8ba8-4d39d46e929f"}={"ts_sec":1716350141,"file":"mariadb-bin.000307","pos":36379,"server_id":334455}}} 
[INFO ] 2024-05-22 11:55:44.925 - [任务 13][AA_0510] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='AA_0510', fieldName='bbc', dataTypeChange=ValueChange{before=null, after=tinyint(4)}, checkChange=null, constraintChange=null, nullableChange=null, commentChange=ValueChange{before=null, after=abcddggvgggg}, defaultChange=ValueChange{before=null, after=4}, primaryChange=null} 
[INFO ] 2024-05-22 11:55:44.925 - [任务 13][AA_0510] - Alter table in memory, qualified name: T_mariadb_io_tapdata_1_0-SNAPSHOT_AA_0510_664c7aae48a9972f85859b28_664c7b5548a9972f85859b40 
[INFO ] 2024-05-22 11:55:44.925 - [任务 13][AA_0510] - Alter table schema transform finished 
[INFO ] 2024-05-22 11:58:02.198 - [任务 13][AA_0510] - Mysql binlog reader stopped 
[WARN ] 2024-05-22 11:58:02.401 - [任务 13][AA_0510] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-22 11:59:02.428 - [任务 13][AA_0510] - Starting mysql cdc, server name: 8a4469db-6d8c-4dcf-8ba8-4d39d46e929f 
[INFO ] 2024-05-22 11:59:02.634 - [任务 13][AA_0510] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2085055275
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 8a4469db-6d8c-4dcf-8ba8-4d39d46e929f
  database.port: 13306
  threadName: Debezium-Mysql-Connector-8a4469db-6d8c-4dcf-8ba8-4d39d46e929f
  database.hostname: *************
  database.password: ********
  name: 8a4469db-6d8c-4dcf-8ba8-4d39d46e929f
  pdk.offset.string: {"name":"8a4469db-6d8c-4dcf-8ba8-4d39d46e929f","offset":{"{\"server\":\"8a4469db-6d8c-4dcf-8ba8-4d39d46e929f\"}":"{\"ts_sec\":1716350141,\"file\":\"mariadb-bin.000307\",\"pos\":36379,\"server_id\":334455}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: demo.AA_0510
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: demo
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-05-22 11:59:02.635 - [任务 13][AA_0510] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-22 11:59:02.650 - [任务 13][AA_0510] - Connector Mariadb incremental start succeed, tables: [AA_0510], data change syncing 
[INFO ] 2024-05-22 12:11:58.991 - [任务 13][AA_0510] - Read DDL: alter table AA_0510 modify column bbc tinyint(4)  comment 'abcddggv', about to be packaged as some event(s) 
[INFO ] 2024-05-22 12:11:58.993 - [任务 13][AA_0510] - DDL event  - Table: AA_0510
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='8a4469db-6d8c-4dcf-8ba8-4d39d46e929f', offset={{"server":"8a4469db-6d8c-4dcf-8ba8-4d39d46e929f"}={"ts_sec":1716351115,"file":"mariadb-bin.000307","pos":36912,"server_id":334455}}} 
[INFO ] 2024-05-22 12:11:58.993 - [任务 13][AA_0510] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='AA_0510', fieldName='bbc', dataTypeChange=ValueChange{before=null, after=tinyint(4)}, checkChange=null, constraintChange=null, nullableChange=null, commentChange=ValueChange{before=null, after=abcddggv}, defaultChange=null, primaryChange=null} 
[INFO ] 2024-05-22 12:11:59.005 - [任务 13][AA_0510] - Alter table in memory, qualified name: T_mariadb_io_tapdata_1_0-SNAPSHOT_AA_0510_664c7aae48a9972f85859b28_664c7b5548a9972f85859b40 
[INFO ] 2024-05-22 12:11:59.214 - [任务 13][AA_0510] - Alter table schema transform finished 
[INFO ] 2024-05-22 12:13:46.289 - [任务 13][AA_0510] - Read DDL: alter table AA_0510 modify column bbc tinyint(4)  comment 'abcddggvgggg', about to be packaged as some event(s) 
[INFO ] 2024-05-22 12:13:46.289 - [任务 13][AA_0510] - DDL event  - Table: AA_0510
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='8a4469db-6d8c-4dcf-8ba8-4d39d46e929f', offset={{"server":"8a4469db-6d8c-4dcf-8ba8-4d39d46e929f"}={"ts_sec":1716351223,"file":"mariadb-bin.000307","pos":37633,"server_id":334455}}} 
[INFO ] 2024-05-22 12:13:46.289 - [任务 13][AA_0510] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='AA_0510', fieldName='bbc', dataTypeChange=ValueChange{before=null, after=tinyint(4)}, checkChange=null, constraintChange=null, nullableChange=null, commentChange=ValueChange{before=null, after=abcddggvgggg}, defaultChange=null, primaryChange=null} 
[INFO ] 2024-05-22 12:13:46.295 - [任务 13][AA_0510] - Alter table in memory, qualified name: T_mariadb_io_tapdata_1_0-SNAPSHOT_AA_0510_664c7aae48a9972f85859b28_664c7b5548a9972f85859b40 
[INFO ] 2024-05-22 12:13:46.504 - [任务 13][AA_0510] - Alter table schema transform finished 
[INFO ] 2024-05-22 12:15:43.826 - [任务 13][AA_0510] - Read DDL: alter table AA_0510 modify column bbc tinyint(4)  comment 'abc', about to be packaged as some event(s) 
[INFO ] 2024-05-22 12:15:43.828 - [任务 13][AA_0510] - DDL event  - Table: AA_0510
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='8a4469db-6d8c-4dcf-8ba8-4d39d46e929f', offset={{"server":"8a4469db-6d8c-4dcf-8ba8-4d39d46e929f"}={"ts_sec":1716351342,"file":"mariadb-bin.000307","pos":37979,"server_id":334455}}} 
[INFO ] 2024-05-22 12:15:43.833 - [任务 13][AA_0510] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='AA_0510', fieldName='bbc', dataTypeChange=ValueChange{before=null, after=tinyint(4)}, checkChange=null, constraintChange=null, nullableChange=null, commentChange=ValueChange{before=null, after=abc}, defaultChange=null, primaryChange=null} 
[INFO ] 2024-05-22 12:15:43.900 - [任务 13][AA_0510] - Alter table in memory, qualified name: T_mariadb_io_tapdata_1_0-SNAPSHOT_AA_0510_664c7aae48a9972f85859b28_664c7b5548a9972f85859b40 
[INFO ] 2024-05-22 12:15:43.900 - [任务 13][AA_0510] - Alter table schema transform finished 
[INFO ] 2024-05-22 12:44:10.495 - [任务 13] - Stop task milestones: 664c7b5548a9972f85859b40(任务 13)  
[INFO ] 2024-05-22 12:44:10.637 - [任务 13][AA_0510] - Node AA_0510[870a620f-fbc2-45c0-972f-6620637a1c35] running status set to false 
[INFO ] 2024-05-22 12:44:10.705 - [任务 13][AA_0510] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-05-22 12:44:10.705 - [任务 13][AA_0510] - Mysql binlog reader stopped 
[INFO ] 2024-05-22 12:44:10.809 - [任务 13][AA_0510] - PDK connector node stopped: HazelcastSourcePdkDataNode-870a620f-fbc2-45c0-972f-6620637a1c35 
[INFO ] 2024-05-22 12:44:10.810 - [任务 13][AA_0510] - PDK connector node released: HazelcastSourcePdkDataNode-870a620f-fbc2-45c0-972f-6620637a1c35 
[INFO ] 2024-05-22 12:44:10.811 - [任务 13][AA_0510] - Node AA_0510[870a620f-fbc2-45c0-972f-6620637a1c35] schema data cleaned 
[INFO ] 2024-05-22 12:44:10.811 - [任务 13][AA_0510] - Node AA_0510[870a620f-fbc2-45c0-972f-6620637a1c35] monitor closed 
[INFO ] 2024-05-22 12:44:10.812 - [任务 13][AA_0510] - Node AA_0510[870a620f-fbc2-45c0-972f-6620637a1c35] close complete, cost 180 ms 
[INFO ] 2024-05-22 12:44:10.812 - [任务 13][targetAA_5010] - Node targetAA_5010[fd64d36f-c202-4a35-8577-b3974ac4e624] running status set to false 
[INFO ] 2024-05-22 12:44:10.835 - [任务 13][targetAA_5010] - PDK connector node stopped: HazelcastTargetPdkDataNode-fd64d36f-c202-4a35-8577-b3974ac4e624 
[INFO ] 2024-05-22 12:44:10.835 - [任务 13][targetAA_5010] - PDK connector node released: HazelcastTargetPdkDataNode-fd64d36f-c202-4a35-8577-b3974ac4e624 
[INFO ] 2024-05-22 12:44:10.837 - [任务 13][targetAA_5010] - Node targetAA_5010[fd64d36f-c202-4a35-8577-b3974ac4e624] schema data cleaned 
[INFO ] 2024-05-22 12:44:10.837 - [任务 13][targetAA_5010] - Node targetAA_5010[fd64d36f-c202-4a35-8577-b3974ac4e624] monitor closed 
[INFO ] 2024-05-22 12:44:11.042 - [任务 13][targetAA_5010] - Node targetAA_5010[fd64d36f-c202-4a35-8577-b3974ac4e624] close complete, cost 25 ms 
[INFO ] 2024-05-22 12:44:14.963 - [任务 13] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-22 12:44:14.963 - [任务 13] - Stopped task aspect(s) 
[INFO ] 2024-05-22 12:44:15.010 - [任务 13] - Snapshot order controller have been removed 
[INFO ] 2024-05-22 12:44:15.011 - [任务 13] - Remove memory task client succeed, task: 任务 13[664c7b5548a9972f85859b40] 
[INFO ] 2024-05-22 12:44:15.216 - [任务 13] - Destroy memory task client cache succeed, task: 任务 13[664c7b5548a9972f85859b40] 
