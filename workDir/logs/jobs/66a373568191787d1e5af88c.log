[INFO ] 2024-07-26 17:59:36.881 - [任务 37] - Task initialization... 
[INFO ] 2024-07-26 17:59:36.882 - [任务 37] - Start task milestones: 66a373568191787d1e5af88c(任务 37) 
[INFO ] 2024-07-26 17:59:37.034 - [任务 37] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 17:59:37.079 - [任务 37] - The engine receives 任务 37 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 17:59:37.172 - [任务 37][CUSTOMER] - Node CUSTOMER[bb22635b-7b00-4c0c-b298-f6e9387f3491] start preload schema,table counts: 1 
[INFO ] 2024-07-26 17:59:37.172 - [任务 37][Test] - Node Test[770e8771-d81b-44bf-9884-dc0284a1666d] start preload schema,table counts: 1 
[INFO ] 2024-07-26 17:59:37.173 - [任务 37][CUSTOMER] - Node CUSTOMER[bb22635b-7b00-4c0c-b298-f6e9387f3491] preload schema finished, cost 1 ms 
[INFO ] 2024-07-26 17:59:37.173 - [任务 37][Test] - Node Test[770e8771-d81b-44bf-9884-dc0284a1666d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 17:59:38.068 - [任务 37][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-26 17:59:38.068 - [任务 37][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-26 17:59:38.068 - [任务 37][CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 17:59:38.281 - [任务 37][CUSTOMER] - batch offset found: {},stream offset found: {"cdcOffset":1721987978,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 17:59:38.281 - [任务 37][CUSTOMER] - Initial sync started 
[INFO ] 2024-07-26 17:59:38.282 - [任务 37][CUSTOMER] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-26 17:59:38.282 - [任务 37][CUSTOMER] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-26 17:59:38.309 - [任务 37][CUSTOMER] - Query table 'CUSTOMER' counts: 676 
[INFO ] 2024-07-26 17:59:38.339 - [任务 37][CUSTOMER] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 17:59:38.548 - [任务 37][Test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 17:59:38.559 - [任务 37][Test] - Table "test.Test" exists, skip auto create table 
[INFO ] 2024-07-26 17:59:38.559 - [任务 37][Test] - The table Test has already exist. 
[INFO ] 2024-07-26 17:59:38.621 - [任务 37][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 17:59:38.625 - [任务 37][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-26 17:59:38.625 - [任务 37][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 17:59:38.625 - [任务 37][CUSTOMER] - Starting stream read, table list: [CUSTOMER, _tapdata_heartbeat_table], offset: {"cdcOffset":1721987978,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 17:59:38.681 - [任务 37][CUSTOMER] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 17:59:38.681 - [任务 37][Test] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'CITY' in 'field list' 
[ERROR] 2024-07-26 17:59:38.700 - [任务 37][Test] - java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'CITY' in 'field list' <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'CITY' in 'field list'

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'CITY' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'CITY' in 'field list'
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:633)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:573)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:529)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:495)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:501)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:547)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'CITY' in 'field list'
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:799)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:499)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:499)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:688)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:625)
	... 11 more
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'CITY' in 'field list'
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:128)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:455)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:853)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:805)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 18 more
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'CITY' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:93)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:108)
	... 27 more

[INFO ] 2024-07-26 17:59:38.700 - [任务 37][Test] - Job suspend in error handle 
[INFO ] 2024-07-26 17:59:39.658 - [任务 37][CUSTOMER] - Node CUSTOMER[bb22635b-7b00-4c0c-b298-f6e9387f3491] running status set to false 
[INFO ] 2024-07-26 17:59:39.658 - [任务 37][CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-bb22635b-7b00-4c0c-b298-f6e9387f3491 
[INFO ] 2024-07-26 17:59:39.658 - [任务 37][CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-bb22635b-7b00-4c0c-b298-f6e9387f3491 
[INFO ] 2024-07-26 17:59:39.659 - [任务 37][CUSTOMER] - Node CUSTOMER[bb22635b-7b00-4c0c-b298-f6e9387f3491] schema data cleaned 
[INFO ] 2024-07-26 17:59:39.661 - [任务 37][CUSTOMER] - Node CUSTOMER[bb22635b-7b00-4c0c-b298-f6e9387f3491] monitor closed 
[INFO ] 2024-07-26 17:59:39.661 - [任务 37][CUSTOMER] - Node CUSTOMER[bb22635b-7b00-4c0c-b298-f6e9387f3491] close complete, cost 24 ms 
[INFO ] 2024-07-26 17:59:39.682 - [任务 37][Test] - Node Test[770e8771-d81b-44bf-9884-dc0284a1666d] running status set to false 
[INFO ] 2024-07-26 17:59:39.682 - [任务 37][Test] - PDK connector node stopped: HazelcastTargetPdkDataNode-770e8771-d81b-44bf-9884-dc0284a1666d 
[INFO ] 2024-07-26 17:59:39.682 - [任务 37][Test] - PDK connector node released: HazelcastTargetPdkDataNode-770e8771-d81b-44bf-9884-dc0284a1666d 
[INFO ] 2024-07-26 17:59:39.682 - [任务 37][Test] - Node Test[770e8771-d81b-44bf-9884-dc0284a1666d] schema data cleaned 
[INFO ] 2024-07-26 17:59:39.682 - [任务 37][Test] - Node Test[770e8771-d81b-44bf-9884-dc0284a1666d] monitor closed 
[INFO ] 2024-07-26 17:59:39.886 - [任务 37][Test] - Node Test[770e8771-d81b-44bf-9884-dc0284a1666d] close complete, cost 21 ms 
[INFO ] 2024-07-26 17:59:40.087 - [任务 37][CUSTOMER] - Incremental sync completed 
[INFO ] 2024-07-26 17:59:40.751 - [任务 37] - Task [任务 37] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-26 17:59:40.752 - [任务 37] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 17:59:40.752 - [任务 37] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@455792cc 
[INFO ] 2024-07-26 17:59:40.889 - [任务 37] - Stop task milestones: 66a373568191787d1e5af88c(任务 37)  
[INFO ] 2024-07-26 17:59:40.889 - [任务 37] - Stopped task aspect(s) 
[INFO ] 2024-07-26 17:59:40.889 - [任务 37] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 17:59:40.907 - [任务 37] - Remove memory task client succeed, task: 任务 37[66a373568191787d1e5af88c] 
[INFO ] 2024-07-26 17:59:40.910 - [任务 37] - Destroy memory task client cache succeed, task: 任务 37[66a373568191787d1e5af88c] 
[INFO ] 2024-07-26 18:00:51.896 - [任务 37] - Task initialization... 
[INFO ] 2024-07-26 18:00:51.896 - [任务 37] - Start task milestones: 66a373568191787d1e5af88c(任务 37) 
[INFO ] 2024-07-26 18:00:51.966 - [任务 37] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 18:00:52.010 - [任务 37] - The engine receives 任务 37 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 18:00:52.038 - [任务 37][Test] - Node WiTest[770e8771-d81b-44bf-9884-dc0284a1666d] start preload schema,table counts: 1 
[INFO ] 2024-07-26 18:00:52.038 - [任务 37][CUSTOMER] - Node CUSTOMER[bb22635b-7b00-4c0c-b298-f6e9387f3491] start preload schema,table counts: 1 
[INFO ] 2024-07-26 18:00:52.039 - [任务 37][Test] - Node WiTest[770e8771-d81b-44bf-9884-dc0284a1666d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 18:00:52.039 - [任务 37][CUSTOMER] - Node CUSTOMER[bb22635b-7b00-4c0c-b298-f6e9387f3491] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 18:00:52.881 - [任务 37][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-26 18:00:52.881 - [任务 37][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-26 18:00:52.881 - [任务 37][CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 18:00:53.041 - [任务 37][Test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 18:00:53.042 - [任务 37][CUSTOMER] - batch offset found: {},stream offset found: {"cdcOffset":1721988053,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 18:00:53.118 - [任务 37][CUSTOMER] - Initial sync started 
[INFO ] 2024-07-26 18:00:53.118 - [任务 37][CUSTOMER] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-26 18:00:53.128 - [任务 37][CUSTOMER] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-26 18:00:53.178 - [任务 37][CUSTOMER] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 18:00:53.242 - [任务 37][CUSTOMER] - Query table 'CUSTOMER' counts: 676 
[INFO ] 2024-07-26 18:00:53.242 - [任务 37][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 18:00:53.242 - [任务 37][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-26 18:00:53.242 - [任务 37][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-26 18:00:53.243 - [任务 37][CUSTOMER] - Starting stream read, table list: [CUSTOMER, _tapdata_heartbeat_table], offset: {"cdcOffset":1721988053,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-26 18:00:53.271 - [任务 37][CUSTOMER] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 18:02:26.019 - [任务 37][CUSTOMER] - Node CUSTOMER[bb22635b-7b00-4c0c-b298-f6e9387f3491] running status set to false 
[INFO ] 2024-07-26 18:02:26.020 - [任务 37][CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-bb22635b-7b00-4c0c-b298-f6e9387f3491 
[INFO ] 2024-07-26 18:02:26.020 - [任务 37][CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-bb22635b-7b00-4c0c-b298-f6e9387f3491 
[INFO ] 2024-07-26 18:02:26.020 - [任务 37][CUSTOMER] - Node CUSTOMER[bb22635b-7b00-4c0c-b298-f6e9387f3491] schema data cleaned 
[INFO ] 2024-07-26 18:02:26.021 - [任务 37][CUSTOMER] - Node CUSTOMER[bb22635b-7b00-4c0c-b298-f6e9387f3491] monitor closed 
[INFO ] 2024-07-26 18:02:26.021 - [任务 37][CUSTOMER] - Node CUSTOMER[bb22635b-7b00-4c0c-b298-f6e9387f3491] close complete, cost 16 ms 
[INFO ] 2024-07-26 18:02:26.021 - [任务 37][Test] - Node WiTest[770e8771-d81b-44bf-9884-dc0284a1666d] running status set to false 
[INFO ] 2024-07-26 18:02:26.033 - [任务 37][Test] - PDK connector node stopped: HazelcastTargetPdkDataNode-770e8771-d81b-44bf-9884-dc0284a1666d 
[INFO ] 2024-07-26 18:02:26.033 - [任务 37][Test] - PDK connector node released: HazelcastTargetPdkDataNode-770e8771-d81b-44bf-9884-dc0284a1666d 
[INFO ] 2024-07-26 18:02:26.034 - [任务 37][Test] - Node WiTest[770e8771-d81b-44bf-9884-dc0284a1666d] schema data cleaned 
[INFO ] 2024-07-26 18:02:26.034 - [任务 37][Test] - Node WiTest[770e8771-d81b-44bf-9884-dc0284a1666d] monitor closed 
[INFO ] 2024-07-26 18:02:26.034 - [任务 37][Test] - Node WiTest[770e8771-d81b-44bf-9884-dc0284a1666d] close complete, cost 13 ms 
[INFO ] 2024-07-26 18:02:26.240 - [任务 37][CUSTOMER] - Incremental sync completed 
[INFO ] 2024-07-26 18:02:27.815 - [任务 37] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:02:27.818 - [任务 37] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4fb560d 
[INFO ] 2024-07-26 18:02:27.818 - [任务 37] - Stop task milestones: 66a373568191787d1e5af88c(任务 37)  
[INFO ] 2024-07-26 18:02:27.952 - [任务 37] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:02:27.952 - [任务 37] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 18:02:28.002 - [任务 37] - Remove memory task client succeed, task: 任务 37[66a373568191787d1e5af88c] 
[INFO ] 2024-07-26 18:02:28.004 - [任务 37] - Destroy memory task client cache succeed, task: 任务 37[66a373568191787d1e5af88c] 
