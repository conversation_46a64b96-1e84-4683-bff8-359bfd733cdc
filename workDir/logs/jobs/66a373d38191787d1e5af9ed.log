[INFO ] 2024-07-26 18:00:51.894 - [Heartbeat-TestMongoHeartBeat] - Start task milestones: 66a373d38191787d1e5af9ed(Heartbeat-TestMongoHeartBeat) 
[INFO ] 2024-07-26 18:00:51.962 - [Heartbeat-TestMongoHeartBeat] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 18:00:52.010 - [Heartbeat-TestMongoHeartBeat] - The engine receives Heartbeat-TestMongoHeartBeat task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 18:00:52.013 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[13480181-4e8b-4dc4-86fb-5028e008094f] start preload schema,table counts: 1 
[INFO ] 2024-07-26 18:00:52.013 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c87e9bfe-56e6-4524-bab7-02349fcddb0b] start preload schema,table counts: 1 
[INFO ] 2024-07-26 18:00:52.014 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[13480181-4e8b-4dc4-86fb-5028e008094f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 18:00:52.014 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c87e9bfe-56e6-4524-bab7-02349fcddb0b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 18:00:53.129 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-26 18:00:53.129 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-26 18:00:53.129 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 18:00:53.175 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721988053129,"lastTimes":1721988053129,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 18:00:53.200 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-26 18:00:53.200 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-26 18:00:53.213 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-26 18:00:53.213 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 18:00:53.215 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-26 18:00:53.215 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721988053129,"lastTimes":1721988053129,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 18:00:53.218 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-26 18:00:53.218 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 18:00:53.271 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[WARN ] 2024-07-26 18:00:53.477 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Index [{id=1}] already exists but options is inconsistent, will ignore creating this index, server error detail message: Command failed with error 85 (IndexOptionsConflict): 'Index with name: id_1 already exists with different options' on server localhost:27017. The full response is {"operationTime": {"$timestamp": {"t": 1721988053, "i": 18}}, "ok": 0.0, "errmsg": "Index with name: id_1 already exists with different options", "code": 85, "codeName": "IndexOptionsConflict", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721988053, "i": 18}}, "signature": {"hash": {"$binary": {"base64": "gLp7JSxVypxTECbkpMjO/m1bYoc=", "subType": "00"}}, "keyId": 7376103549123428362}}} 
[INFO ] 2024-07-26 18:06:23.853 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[13480181-4e8b-4dc4-86fb-5028e008094f] running status set to false 
[INFO ] 2024-07-26 18:06:23.867 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-26 18:06:23.867 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-13480181-4e8b-4dc4-86fb-5028e008094f 
[INFO ] 2024-07-26 18:06:23.867 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-13480181-4e8b-4dc4-86fb-5028e008094f 
[INFO ] 2024-07-26 18:06:23.867 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[13480181-4e8b-4dc4-86fb-5028e008094f] schema data cleaned 
[INFO ] 2024-07-26 18:06:23.867 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[13480181-4e8b-4dc4-86fb-5028e008094f] monitor closed 
[INFO ] 2024-07-26 18:06:23.868 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[13480181-4e8b-4dc4-86fb-5028e008094f] close complete, cost 16 ms 
[INFO ] 2024-07-26 18:06:23.868 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c87e9bfe-56e6-4524-bab7-02349fcddb0b] running status set to false 
[INFO ] 2024-07-26 18:06:23.880 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-c87e9bfe-56e6-4524-bab7-02349fcddb0b 
[INFO ] 2024-07-26 18:06:23.880 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-c87e9bfe-56e6-4524-bab7-02349fcddb0b 
[INFO ] 2024-07-26 18:06:23.880 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c87e9bfe-56e6-4524-bab7-02349fcddb0b] schema data cleaned 
[INFO ] 2024-07-26 18:06:23.881 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c87e9bfe-56e6-4524-bab7-02349fcddb0b] monitor closed 
[INFO ] 2024-07-26 18:06:23.881 - [Heartbeat-TestMongoHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c87e9bfe-56e6-4524-bab7-02349fcddb0b] close complete, cost 12 ms 
[INFO ] 2024-07-26 18:06:28.736 - [Heartbeat-TestMongoHeartBeat] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:06:28.737 - [Heartbeat-TestMongoHeartBeat] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@267b2cb 
[INFO ] 2024-07-26 18:06:28.737 - [Heartbeat-TestMongoHeartBeat] - Stop task milestones: 66a373d38191787d1e5af9ed(Heartbeat-TestMongoHeartBeat)  
[INFO ] 2024-07-26 18:06:28.867 - [Heartbeat-TestMongoHeartBeat] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:06:28.896 - [Heartbeat-TestMongoHeartBeat] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 18:06:28.898 - [Heartbeat-TestMongoHeartBeat] - Remove memory task client succeed, task: Heartbeat-TestMongoHeartBeat[66a373d38191787d1e5af9ed] 
[INFO ] 2024-07-26 18:06:28.898 - [Heartbeat-TestMongoHeartBeat] - Destroy memory task client cache succeed, task: Heartbeat-TestMongoHeartBeat[66a373d38191787d1e5af9ed] 
