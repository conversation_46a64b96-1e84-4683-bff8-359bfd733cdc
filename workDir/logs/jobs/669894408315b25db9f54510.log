[INFO ] 2024-07-18 12:04:27.584 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365] - Task initialization... 
[INFO ] 2024-07-18 12:04:27.585 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365] - Start task milestones: 669894408315b25db9f54510(t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365) 
[INFO ] 2024-07-18 12:04:27.951 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365] - No<PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-18 12:04:27.952 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365] - The engine receives t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 12:04:28.024 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[65fb247c-ac74-47fd-a89e-95443bcf0599] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:04:28.025 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9a9ee87e-f646-4bae-8caa-e4bc2370e0ce] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:04:28.025 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9a9ee87e-f646-4bae-8caa-e4bc2370e0ce] preload schema finished, cost 1 ms 
[INFO ] 2024-07-18 12:04:28.025 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[65fb247c-ac74-47fd-a89e-95443bcf0599] preload schema finished, cost 1 ms 
[INFO ] 2024-07-18 12:04:28.359 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_6040_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 12:04:28.417 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Source node "qa_mongodb_repl_42240_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 12:04:28.418 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Source node "qa_mongodb_repl_42240_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 12:04:28.418 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 12:04:28.641 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - batch offset found: {},stream offset found: {"cdcOffset":1721275468,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 12:04:28.817 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 12:04:28.817 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Starting batch read, table name: t42240_462, offset: null 
[INFO ] 2024-07-18 12:04:28.817 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Table t42240_462 is going to be initial synced 
[INFO ] 2024-07-18 12:04:28.853 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Table [t42240_462] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 12:04:28.853 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Query table 't42240_462' counts: 1 
[INFO ] 2024-07-18 12:04:28.854 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 12:04:28.854 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 12:04:28.854 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 12:04:28.855 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Starting stream read, table list: [t42240_462, _tapdata_heartbeat_table], offset: {"cdcOffset":1721275468,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 12:04:29.062 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [t42240_462, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 12:06:35.808 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[65fb247c-ac74-47fd-a89e-95443bcf0599] running status set to false 
[INFO ] 2024-07-18 12:06:35.836 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 12:06:35.837 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-65fb247c-ac74-47fd-a89e-95443bcf0599 
[INFO ] 2024-07-18 12:06:35.837 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-65fb247c-ac74-47fd-a89e-95443bcf0599 
[INFO ] 2024-07-18 12:06:35.837 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[65fb247c-ac74-47fd-a89e-95443bcf0599] schema data cleaned 
[INFO ] 2024-07-18 12:06:35.841 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[65fb247c-ac74-47fd-a89e-95443bcf0599] monitor closed 
[INFO ] 2024-07-18 12:06:35.841 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[65fb247c-ac74-47fd-a89e-95443bcf0599] close complete, cost 61 ms 
[INFO ] 2024-07-18 12:06:35.879 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9a9ee87e-f646-4bae-8caa-e4bc2370e0ce] running status set to false 
[INFO ] 2024-07-18 12:06:35.880 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-9a9ee87e-f646-4bae-8caa-e4bc2370e0ce 
[INFO ] 2024-07-18 12:06:35.880 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-9a9ee87e-f646-4bae-8caa-e4bc2370e0ce 
[INFO ] 2024-07-18 12:06:35.880 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9a9ee87e-f646-4bae-8caa-e4bc2370e0ce] schema data cleaned 
[INFO ] 2024-07-18 12:06:35.880 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9a9ee87e-f646-4bae-8caa-e4bc2370e0ce] monitor closed 
[INFO ] 2024-07-18 12:06:36.083 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[9a9ee87e-f646-4bae-8caa-e4bc2370e0ce] close complete, cost 39 ms 
[INFO ] 2024-07-18 12:06:37.671 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 12:06:37.672 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@43140af1 
[INFO ] 2024-07-18 12:06:37.804 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365] - Stop task milestones: 669894408315b25db9f54510(t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365)  
[INFO ] 2024-07-18 12:06:37.823 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365] - Stopped task aspect(s) 
[INFO ] 2024-07-18 12:06:37.823 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 12:06:37.882 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365] - Remove memory task client succeed, task: t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365[669894408315b25db9f54510] 
[INFO ] 2024-07-18 12:06:37.882 - [t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365] - Destroy memory task client cache succeed, task: t_4.6.2-mdb-v4.2.24_to_mdb_with_check_data_1717403468657_3537-1721275365[669894408315b25db9f54510] 
