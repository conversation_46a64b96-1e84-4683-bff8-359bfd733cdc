[INFO ] 2024-06-25 15:50:53.029 - [任务 31] - Start task milestones: 667a767910fc5c6259c570d4(任务 31) 
[INFO ] 2024-06-25 15:50:53.029 - [任务 31] - Task initialization... 
[INFO ] 2024-06-25 15:50:53.242 - [任务 31] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-06-25 15:50:53.369 - [任务 31] - The engine receives 任务 31 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 15:50:53.370 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:50:53.370 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:50:53.370 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] start preload schema,table counts: 4 
[INFO ] 2024-06-25 15:50:53.370 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:50:53.370 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:50:53.371 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 15:50:53.948 - [任务 31][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 15:50:53.948 - [任务 31][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 15:50:54.129 - [任务 31][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-06-25 15:50:54.129 - [任务 31][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-06-25 15:50:54.129 - [任务 31][SouceMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 15:50:54.149 - [任务 31][SouceMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000031","position":1060989527,"gtidSet":""} 
[INFO ] 2024-06-25 15:50:54.157 - [任务 31] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 15:50:54.218 - [任务 31][SouceMysql] - Initial sync started 
[INFO ] 2024-06-25 15:50:54.219 - [任务 31][SouceMysql] - Starting batch read, table name: Inspectwimtest3, offset: null 
[INFO ] 2024-06-25 15:50:54.242 - [任务 31][SouceMysql] - Table Inspectwimtest3 is going to be initial synced 
[INFO ] 2024-06-25 15:50:54.244 - [任务 31][SouceMysql] - Table [Inspectwimtest3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:50:54.282 - [任务 31][SouceMysql] - Query table 'Inspectwimtest3' counts: 1 
[INFO ] 2024-06-25 15:50:54.283 - [任务 31][SouceMysql] - Starting batch read, table name: Inspectwimtest4, offset: null 
[INFO ] 2024-06-25 15:50:54.305 - [任务 31][SouceMysql] - Table Inspectwimtest4 is going to be initial synced 
[INFO ] 2024-06-25 15:50:54.305 - [任务 31][SouceMysql] - Query table 'Inspectwimtest4' counts: 1053 
[INFO ] 2024-06-25 15:50:54.480 - [任务 31][SouceMysql] - Table [Inspectwimtest4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:50:54.481 - [任务 31][SouceMysql] - Starting batch read, table name: Inspectwimtest1, offset: null 
[INFO ] 2024-06-25 15:50:54.481 - [任务 31][SouceMysql] - Table Inspectwimtest1 is going to be initial synced 
[INFO ] 2024-06-25 15:50:54.495 - [任务 31][SouceMysql] - Query table 'Inspectwimtest1' counts: 1053 
[INFO ] 2024-06-25 15:50:54.648 - [任务 31][SouceMysql] - Table [Inspectwimtest1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:50:54.648 - [任务 31][SouceMysql] - Starting batch read, table name: Inspectwimtest2, offset: null 
[INFO ] 2024-06-25 15:50:54.648 - [任务 31][SouceMysql] - Table Inspectwimtest2 is going to be initial synced 
[INFO ] 2024-06-25 15:50:54.670 - [任务 31][SouceMysql] - Query table 'Inspectwimtest2' counts: 1075 
[INFO ] 2024-06-25 15:50:54.782 - [任务 31][SouceMysql] - Table [Inspectwimtest2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:50:54.784 - [任务 31][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 15:50:54.784 - [任务 31][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 15:50:54.784 - [任务 31][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 15:50:54.784 - [任务 31][SouceMysql] - Starting stream read, table list: [Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2], offset: {"filename":"binlog.000031","position":1060989527,"gtidSet":""} 
[INFO ] 2024-06-25 15:50:54.836 - [任务 31][SouceMysql] - Starting mysql cdc, server name: f06aaef8-490c-42f6-8f9f-3eaf1883a841 
[INFO ] 2024-06-25 15:50:54.836 - [任务 31][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 996862857
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f06aaef8-490c-42f6-8f9f-3eaf1883a841
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f06aaef8-490c-42f6-8f9f-3eaf1883a841
  database.hostname: localhost
  database.password: ********
  name: f06aaef8-490c-42f6-8f9f-3eaf1883a841
  pdk.offset.string: {"name":"f06aaef8-490c-42f6-8f9f-3eaf1883a841","offset":{"{\"server\":\"f06aaef8-490c-42f6-8f9f-3eaf1883a841\"}":"{\"file\":\"binlog.000031\",\"pos\":1060989527,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Inspectwimtest3,test.Inspectwimtest4,test.Inspectwimtest1,test.Inspectwimtest2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 15:50:55.037 - [任务 31][SouceMysql] - Connector Mysql incremental start succeed, tables: [Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2], data change syncing 
[INFO ] 2024-06-25 15:52:54.223 - [任务 31][SouceMysql] - Found new table(s): [Inspectwimtest5] 
[INFO ] 2024-06-25 15:52:54.224 - [任务 31][SouceMysql] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-06-25 15:52:54.255 - [任务 31][SouceMysql] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@63f2418: {"table":{"comment":"","id":"Inspectwimtest5","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"}],"name":"PRIMARY","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"Inspectwimtest5","nameFieldMap":{"id":{"autoInc":false,"comment":"","dataType":"int","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"comment":"","dataType":"varchar(20)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"}},"unique":true}},"tableId":"Inspectwimtest5","type":206} 
[INFO ] 2024-06-25 15:52:54.256 - [任务 31][SouceMysql] - Create new table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_Inspectwimtest5_667413fd7b5e1f6c3b139e78_667a767910fc5c6259c570d4 
[INFO ] 2024-06-25 15:52:54.346 - [任务 31][SouceMysql] - Create new table schema transform finished: TapTable id Inspectwimtest5 name Inspectwimtest5 storageEngine null charset null number of fields 2 
[INFO ] 2024-06-25 15:52:54.347 - [任务 31][SouceMysql] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-06-25 15:52:54.445 - [任务 31][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-25 15:52:54.445 - [任务 31][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-06-25 15:52:54.646 - [任务 31][SouceMysql] - Incremental sync completed 
[INFO ] 2024-06-25 15:52:54.653 - [任务 31][SouceMysql] - Starting batch read, table name: Inspectwimtest5, offset: null 
[INFO ] 2024-06-25 15:52:54.660 - [任务 31][SouceMysql] - Table Inspectwimtest5 is going to be initial synced 
[INFO ] 2024-06-25 15:52:54.705 - [任务 31][SouceMysql] - Query table 'Inspectwimtest5' counts: 1 
[INFO ] 2024-06-25 15:52:54.712 - [任务 31][SouceMysql] - Table [Inspectwimtest5] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:52:54.712 - [任务 31][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 15:52:54.712 - [任务 31][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 15:52:54.715 - [任务 31][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 15:52:54.733 - [任务 31][SouceMysql] - Starting stream read, table list: [Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest5], offset: {"name":"f06aaef8-490c-42f6-8f9f-3eaf1883a841","offset":{"{\"server\":\"f06aaef8-490c-42f6-8f9f-3eaf1883a841\"}":"{\"ts_sec\":1719301854,\"file\":\"binlog.000031\",\"pos\":1060989886,\"server_id\":1}"}} 
[INFO ] 2024-06-25 15:52:54.734 - [任务 31][SouceMysql] - Starting mysql cdc, server name: f06aaef8-490c-42f6-8f9f-3eaf1883a841 
[INFO ] 2024-06-25 15:52:54.835 - [任务 31][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 310863504
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f06aaef8-490c-42f6-8f9f-3eaf1883a841
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f06aaef8-490c-42f6-8f9f-3eaf1883a841
  database.hostname: localhost
  database.password: ********
  name: f06aaef8-490c-42f6-8f9f-3eaf1883a841
  pdk.offset.string: {"name":"f06aaef8-490c-42f6-8f9f-3eaf1883a841","offset":{"{\"server\":\"f06aaef8-490c-42f6-8f9f-3eaf1883a841\"}":"{\"ts_sec\":1719301854,\"file\":\"binlog.000031\",\"pos\":1060989886,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Inspectwimtest3,test.Inspectwimtest4,test.Inspectwimtest1,test.Inspectwimtest2,test.Inspectwimtest5
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 15:52:54.837 - [任务 31][SouceMysql] - Connector Mysql incremental start succeed, tables: [Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest5], data change syncing 
[INFO ] 2024-06-25 15:56:32.363 - [任务 31][SouceMysql] - Found new table(s): [Inspectwimtest6] 
[INFO ] 2024-06-25 15:57:12.849 - [任务 31][SouceMysql] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-06-25 15:57:12.850 - [任务 31][SouceMysql] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@1e349478: {"table":{"comment":"","id":"Inspectwimtest6","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"}],"name":"PRIMARY","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"Inspectwimtest6","nameFieldMap":{"id":{"autoInc":false,"comment":"","dataType":"int","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"comment":"","dataType":"varchar(20)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"}},"unique":true}},"tableId":"Inspectwimtest6","type":206} 
[INFO ] 2024-06-25 15:57:13.093 - [任务 31][SouceMysql] - Create new table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_Inspectwimtest6_667413fd7b5e1f6c3b139e78_667a767910fc5c6259c570d4 
[INFO ] 2024-06-25 15:57:13.093 - [任务 31][SouceMysql] - Create new table schema transform finished: TapTable id Inspectwimtest6 name Inspectwimtest6 storageEngine null charset null number of fields 2 
[INFO ] 2024-06-25 15:57:13.210 - [任务 31][SouceMysql] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-06-25 15:57:13.211 - [任务 31][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-25 15:57:13.211 - [任务 31][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-06-25 15:57:13.211 - [任务 31][SouceMysql] - Incremental sync completed 
[INFO ] 2024-06-25 15:58:21.208 - [任务 31][SouceMysql] - Starting batch read, table name: Inspectwimtest6, offset: null 
[INFO ] 2024-06-25 15:58:21.242 - [任务 31][SouceMysql] - Table Inspectwimtest6 is going to be initial synced 
[WARN ] 2024-06-25 15:58:21.250 - [任务 31] - Found add/remove table failed, will retry next time, error: PDK retry exception (Server Error Code null): java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-85) has been closed. 
[INFO ] 2024-06-25 15:58:21.340 - [任务 31][SouceMysql] - Query table 'Inspectwimtest6' counts: 0 
[INFO ] 2024-06-25 15:58:21.341 - [任务 31][SouceMysql] - Table [Inspectwimtest6] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 15:58:21.366 - [任务 31][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 15:58:21.367 - [任务 31][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 15:58:21.367 - [任务 31][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 15:58:21.408 - [任务 31][SouceMysql] - Starting stream read, table list: [Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest5, Inspectwimtest6], offset: {"name":"f06aaef8-490c-42f6-8f9f-3eaf1883a841","offset":{"{\"server\":\"f06aaef8-490c-42f6-8f9f-3eaf1883a841\"}":"{\"ts_sec\":1719301967,\"file\":\"binlog.000031\",\"pos\":1060990386,\"server_id\":1}"}} 
[INFO ] 2024-06-25 15:58:21.409 - [任务 31][SouceMysql] - Starting mysql cdc, server name: f06aaef8-490c-42f6-8f9f-3eaf1883a841 
[INFO ] 2024-06-25 15:58:21.516 - [任务 31][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 940000948
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f06aaef8-490c-42f6-8f9f-3eaf1883a841
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f06aaef8-490c-42f6-8f9f-3eaf1883a841
  database.hostname: localhost
  database.password: ********
  name: f06aaef8-490c-42f6-8f9f-3eaf1883a841
  pdk.offset.string: {"name":"f06aaef8-490c-42f6-8f9f-3eaf1883a841","offset":{"{\"server\":\"f06aaef8-490c-42f6-8f9f-3eaf1883a841\"}":"{\"ts_sec\":1719301967,\"file\":\"binlog.000031\",\"pos\":1060990386,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Inspectwimtest3,test.Inspectwimtest4,test.Inspectwimtest1,test.Inspectwimtest2,test.Inspectwimtest5,test.Inspectwimtest6
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 15:58:21.518 - [任务 31][SouceMysql] - Connector Mysql incremental start succeed, tables: [Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest5, Inspectwimtest6], data change syncing 
[INFO ] 2024-06-25 16:01:00.640 - [任务 31][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-06-25 16:01:00.662 - [任务 31][SouceMysql] - Incremental sync completed 
[INFO ] 2024-06-25 16:01:00.664 - [任务 31][SouceMysql] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-06-25 16:01:00.729 - [任务 31][SouceMysql] - java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected <-- Error Message -->
java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: BinaryLogClient is already connected
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:566)
	com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected
	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:153)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:360)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:602)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.IllegalStateException: BinaryLogClient is already connected
	at com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:566)
	at com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	... 1 more

[INFO ] 2024-06-25 16:01:00.730 - [任务 31][SouceMysql] - Job suspend in error handle 
[INFO ] 2024-06-25 16:01:00.778 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] running status set to false 
[INFO ] 2024-06-25 16:01:00.782 - [任务 31][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833 
[INFO ] 2024-06-25 16:01:00.782 - [任务 31][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833 
[INFO ] 2024-06-25 16:01:00.782 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] schema data cleaned 
[INFO ] 2024-06-25 16:01:00.782 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] monitor closed 
[INFO ] 2024-06-25 16:01:00.782 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] close complete, cost 27 ms 
[INFO ] 2024-06-25 16:01:00.782 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] running status set to false 
[INFO ] 2024-06-25 16:01:00.783 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] schema data cleaned 
[INFO ] 2024-06-25 16:01:00.783 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] monitor closed 
[INFO ] 2024-06-25 16:01:00.783 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] close complete, cost 0 ms 
[INFO ] 2024-06-25 16:01:00.783 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] running status set to false 
[INFO ] 2024-06-25 16:01:00.801 - [任务 31][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-20936b02-bfff-40d4-bfca-ab06ee4b6f17 
[INFO ] 2024-06-25 16:01:00.801 - [任务 31][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-20936b02-bfff-40d4-bfca-ab06ee4b6f17 
[INFO ] 2024-06-25 16:01:00.802 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] schema data cleaned 
[INFO ] 2024-06-25 16:01:00.802 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] monitor closed 
[INFO ] 2024-06-25 16:01:00.802 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] close complete, cost 19 ms 
[INFO ] 2024-06-25 16:01:04.707 - [任务 31] - Task [任务 31] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-06-25 16:01:04.716 - [任务 31] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 16:01:04.717 - [任务 31] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4bf29df 
[INFO ] 2024-06-25 16:01:04.765 - [任务 31] - Stop task milestones: 667a767910fc5c6259c570d4(任务 31)  
[INFO ] 2024-06-25 16:01:04.765 - [任务 31] - Stopped task aspect(s) 
[INFO ] 2024-06-25 16:01:04.765 - [任务 31] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 16:01:04.785 - [任务 31] - Remove memory task client succeed, task: 任务 31[667a767910fc5c6259c570d4] 
[INFO ] 2024-06-25 16:01:04.786 - [任务 31] - Destroy memory task client cache succeed, task: 任务 31[667a767910fc5c6259c570d4] 
[INFO ] 2024-06-25 16:24:31.236 - [任务 31] - Start task milestones: 667a767910fc5c6259c570d4(任务 31) 
[INFO ] 2024-06-25 16:24:31.236 - [任务 31] - Task initialization... 
[INFO ] 2024-06-25 16:24:36.127 - [任务 31] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 16:24:36.127 - [任务 31] - The engine receives 任务 31 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 16:24:36.228 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] start preload schema,table counts: 6 
[INFO ] 2024-06-25 16:24:36.228 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] start preload schema,table counts: 6 
[INFO ] 2024-06-25 16:24:36.228 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] start preload schema,table counts: 6 
[INFO ] 2024-06-25 16:24:36.228 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 16:24:36.228 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 16:24:36.228 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 16:24:36.764 - [任务 31][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 16:24:36.764 - [任务 31][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 16:24:36.976 - [任务 31][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-06-25 16:24:36.976 - [任务 31][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-06-25 16:24:36.976 - [任务 31][SouceMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 16:24:36.978 - [任务 31][SouceMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000031","position":1060990386,"gtidSet":""} 
[INFO ] 2024-06-25 16:24:36.991 - [任务 31] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 16:24:37.062 - [任务 31][SouceMysql] - Initial sync started 
[INFO ] 2024-06-25 16:24:37.071 - [任务 31][SouceMysql] - Starting batch read, table name: Inspectwimtest3, offset: null 
[INFO ] 2024-06-25 16:24:37.071 - [任务 31][SouceMysql] - Table Inspectwimtest3 is going to be initial synced 
[INFO ] 2024-06-25 16:24:37.091 - [任务 31][SouceMysql] - Table [Inspectwimtest3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:24:37.105 - [任务 31][SouceMysql] - Query table 'Inspectwimtest3' counts: 1 
[INFO ] 2024-06-25 16:24:37.108 - [任务 31][SouceMysql] - Starting batch read, table name: Inspectwimtest4, offset: null 
[INFO ] 2024-06-25 16:24:37.108 - [任务 31][SouceMysql] - Table Inspectwimtest4 is going to be initial synced 
[INFO ] 2024-06-25 16:24:37.114 - [任务 31][SouceMysql] - Query table 'Inspectwimtest4' counts: 1053 
[INFO ] 2024-06-25 16:24:37.319 - [任务 31][SouceMysql] - Table [Inspectwimtest4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:24:37.327 - [任务 31][SouceMysql] - Starting batch read, table name: Inspectwimtest1, offset: null 
[INFO ] 2024-06-25 16:24:37.327 - [任务 31][SouceMysql] - Table Inspectwimtest1 is going to be initial synced 
[INFO ] 2024-06-25 16:24:37.489 - [任务 31][SouceMysql] - Query table 'Inspectwimtest1' counts: 1053 
[INFO ] 2024-06-25 16:24:37.495 - [任务 31][SouceMysql] - Table [Inspectwimtest1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:24:37.495 - [任务 31][SouceMysql] - Starting batch read, table name: Inspectwimtest2, offset: null 
[INFO ] 2024-06-25 16:24:37.495 - [任务 31][SouceMysql] - Table Inspectwimtest2 is going to be initial synced 
[INFO ] 2024-06-25 16:24:37.625 - [任务 31][SouceMysql] - Query table 'Inspectwimtest2' counts: 1075 
[INFO ] 2024-06-25 16:24:37.627 - [任务 31][SouceMysql] - Table [Inspectwimtest2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:24:37.627 - [任务 31][SouceMysql] - Starting batch read, table name: Inspectwimtest5, offset: null 
[INFO ] 2024-06-25 16:24:37.627 - [任务 31][SouceMysql] - Table Inspectwimtest5 is going to be initial synced 
[INFO ] 2024-06-25 16:24:37.634 - [任务 31][SouceMysql] - Table [Inspectwimtest5] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:24:37.637 - [任务 31][SouceMysql] - Query table 'Inspectwimtest5' counts: 1 
[INFO ] 2024-06-25 16:24:37.637 - [任务 31][SouceMysql] - Starting batch read, table name: Inspectwimtest6, offset: null 
[INFO ] 2024-06-25 16:24:37.637 - [任务 31][SouceMysql] - Table Inspectwimtest6 is going to be initial synced 
[INFO ] 2024-06-25 16:24:37.637 - [任务 31][SouceMysql] - Table [Inspectwimtest6] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:24:37.637 - [任务 31][SouceMysql] - Query table 'Inspectwimtest6' counts: 0 
[INFO ] 2024-06-25 16:24:37.637 - [任务 31][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:24:37.637 - [任务 31][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 16:24:37.637 - [任务 31][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:24:37.690 - [任务 31][SouceMysql] - Starting stream read, table list: [Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest5, Inspectwimtest6], offset: {"filename":"binlog.000031","position":1060990386,"gtidSet":""} 
[INFO ] 2024-06-25 16:24:37.691 - [任务 31][SouceMysql] - Starting mysql cdc, server name: 9529129e-1edc-4824-8929-f460c9ca5d59 
[INFO ] 2024-06-25 16:24:37.739 - [任务 31][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1061071150
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9529129e-1edc-4824-8929-f460c9ca5d59
  database.port: 3306
  threadName: Debezium-Mysql-Connector-9529129e-1edc-4824-8929-f460c9ca5d59
  database.hostname: localhost
  database.password: ********
  name: 9529129e-1edc-4824-8929-f460c9ca5d59
  pdk.offset.string: {"name":"9529129e-1edc-4824-8929-f460c9ca5d59","offset":{"{\"server\":\"9529129e-1edc-4824-8929-f460c9ca5d59\"}":"{\"file\":\"binlog.000031\",\"pos\":1060990386,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Inspectwimtest3,test.Inspectwimtest4,test.Inspectwimtest1,test.Inspectwimtest2,test.Inspectwimtest5,test.Inspectwimtest6
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 16:24:37.739 - [任务 31][SouceMysql] - Connector Mysql incremental start succeed, tables: [Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest5, Inspectwimtest6], data change syncing 
[INFO ] 2024-06-25 16:25:37.001 - [任务 31][SouceMysql] - Found new table(s): [Inspectwimtest7] 
[INFO ] 2024-06-25 16:25:37.001 - [任务 31][SouceMysql] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-06-25 16:25:37.028 - [任务 31][SouceMysql] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@4b47d469: {"table":{"comment":"","id":"Inspectwimtest7","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"}],"name":"PRIMARY","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"Inspectwimtest7","nameFieldMap":{"id":{"autoInc":false,"comment":"","dataType":"int","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"comment":"","dataType":"varchar(20)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"}},"unique":true}},"tableId":"Inspectwimtest7","type":206} 
[INFO ] 2024-06-25 16:25:37.028 - [任务 31][SouceMysql] - Create new table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_Inspectwimtest7_667413fd7b5e1f6c3b139e78_667a767910fc5c6259c570d4 
[INFO ] 2024-06-25 16:26:06.211 - [任务 31][SouceMysql] - Create new table schema transform finished: TapTable id Inspectwimtest7 name Inspectwimtest7 storageEngine null charset null number of fields 2 
[INFO ] 2024-06-25 16:26:06.213 - [任务 31][SouceMysql] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-06-25 16:26:06.279 - [任务 31][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-25 16:26:06.279 - [任务 31][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-06-25 16:26:06.483 - [任务 31][SouceMysql] - Incremental sync completed 
[INFO ] 2024-06-25 16:26:06.608 - [任务 31][SouceMysql] - Starting batch read, table name: Inspectwimtest7, offset: null 
[INFO ] 2024-06-25 16:26:06.608 - [任务 31][SouceMysql] - Table Inspectwimtest7 is going to be initial synced 
[INFO ] 2024-06-25 16:26:06.662 - [任务 31][SouceMysql] - Table [Inspectwimtest7] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:26:06.683 - [任务 31][SouceMysql] - Query table 'Inspectwimtest7' counts: 0 
[INFO ] 2024-06-25 16:26:06.683 - [任务 31][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:26:06.689 - [任务 31][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 16:26:06.689 - [任务 31][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:26:06.720 - [任务 31][SouceMysql] - Starting stream read, table list: [Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest7, Inspectwimtest5, Inspectwimtest6], offset: {"name":"9529129e-1edc-4824-8929-f460c9ca5d59","offset":{"{\"server\":\"9529129e-1edc-4824-8929-f460c9ca5d59\"}":"{\"ts_sec\":1719303877,\"file\":\"binlog.000031\",\"pos\":1060990465,\"server_id\":1}"}} 
[INFO ] 2024-06-25 16:26:06.721 - [任务 31][SouceMysql] - Starting mysql cdc, server name: 9529129e-1edc-4824-8929-f460c9ca5d59 
[INFO ] 2024-06-25 16:26:06.814 - [任务 31][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1046765924
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9529129e-1edc-4824-8929-f460c9ca5d59
  database.port: 3306
  threadName: Debezium-Mysql-Connector-9529129e-1edc-4824-8929-f460c9ca5d59
  database.hostname: localhost
  database.password: ********
  name: 9529129e-1edc-4824-8929-f460c9ca5d59
  pdk.offset.string: {"name":"9529129e-1edc-4824-8929-f460c9ca5d59","offset":{"{\"server\":\"9529129e-1edc-4824-8929-f460c9ca5d59\"}":"{\"ts_sec\":1719303877,\"file\":\"binlog.000031\",\"pos\":1060990465,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Inspectwimtest3,test.Inspectwimtest4,test.Inspectwimtest1,test.Inspectwimtest2,test.Inspectwimtest7,test.Inspectwimtest5,test.Inspectwimtest6
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 16:26:06.815 - [任务 31][SouceMysql] - Connector Mysql incremental start succeed, tables: [Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest7, Inspectwimtest5, Inspectwimtest6], data change syncing 
[INFO ] 2024-06-25 16:27:40.613 - [任务 31][SouceMysql] - Found new table(s): [Inspectwimtest8] 
[INFO ] 2024-06-25 16:27:40.613 - [任务 31][SouceMysql] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-06-25 16:27:40.670 - [任务 31][SouceMysql] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@4c6484ae: {"table":{"comment":"","id":"Inspectwimtest8","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"}],"name":"PRIMARY","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"Inspectwimtest8","nameFieldMap":{"id":{"autoInc":false,"comment":"","dataType":"int","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"comment":"","dataType":"varchar(20)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"}},"unique":true}},"tableId":"Inspectwimtest8","type":206} 
[INFO ] 2024-06-25 16:27:40.672 - [任务 31][SouceMysql] - Create new table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_Inspectwimtest8_667413fd7b5e1f6c3b139e78_667a767910fc5c6259c570d4 
[INFO ] 2024-06-25 16:27:43.287 - [任务 31][SouceMysql] - Create new table schema transform finished: TapTable id Inspectwimtest8 name Inspectwimtest8 storageEngine null charset null number of fields 2 
[INFO ] 2024-06-25 16:27:43.290 - [任务 31][SouceMysql] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-06-25 16:27:43.321 - [任务 31][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-25 16:27:43.321 - [任务 31][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-06-25 16:27:43.522 - [任务 31][SouceMysql] - Incremental sync completed 
[INFO ] 2024-06-25 16:27:43.534 - [任务 31][SouceMysql] - Starting batch read, table name: Inspectwimtest8, offset: null 
[INFO ] 2024-06-25 16:27:43.534 - [任务 31][SouceMysql] - Table Inspectwimtest8 is going to be initial synced 
[INFO ] 2024-06-25 16:27:43.553 - [任务 31][SouceMysql] - Query table 'Inspectwimtest8' counts: 0 
[INFO ] 2024-06-25 16:27:43.554 - [任务 31][SouceMysql] - Table [Inspectwimtest8] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:27:43.554 - [任务 31][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:27:43.554 - [任务 31][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 16:27:43.554 - [任务 31][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:27:43.554 - [任务 31][SouceMysql] - Starting stream read, table list: [Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest7, Inspectwimtest8, Inspectwimtest5, Inspectwimtest6], offset: {"name":"9529129e-1edc-4824-8929-f460c9ca5d59","offset":{"{\"server\":\"9529129e-1edc-4824-8929-f460c9ca5d59\"}":"{\"ts_sec\":1719303966,\"file\":\"binlog.000031\",\"pos\":1060990946,\"server_id\":1}"}} 
[INFO ] 2024-06-25 16:27:43.574 - [任务 31][SouceMysql] - Starting mysql cdc, server name: 9529129e-1edc-4824-8929-f460c9ca5d59 
[INFO ] 2024-06-25 16:27:43.574 - [任务 31][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 32266088
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9529129e-1edc-4824-8929-f460c9ca5d59
  database.port: 3306
  threadName: Debezium-Mysql-Connector-9529129e-1edc-4824-8929-f460c9ca5d59
  database.hostname: localhost
  database.password: ********
  name: 9529129e-1edc-4824-8929-f460c9ca5d59
  pdk.offset.string: {"name":"9529129e-1edc-4824-8929-f460c9ca5d59","offset":{"{\"server\":\"9529129e-1edc-4824-8929-f460c9ca5d59\"}":"{\"ts_sec\":1719303966,\"file\":\"binlog.000031\",\"pos\":1060990946,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Inspectwimtest3,test.Inspectwimtest4,test.Inspectwimtest1,test.Inspectwimtest2,test.Inspectwimtest7,test.Inspectwimtest8,test.Inspectwimtest5,test.Inspectwimtest6
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 16:27:43.781 - [任务 31][SouceMysql] - Connector Mysql incremental start succeed, tables: [Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest7, Inspectwimtest8, Inspectwimtest5, Inspectwimtest6], data change syncing 
[WARN ] 2024-06-25 16:34:22.038 - [任务 31][SourceMongo] - Save to snapshot failed, collection: Task/syncProgress/667a767910fc5c6259c570d4, object: {bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833,20936b02-bfff-40d4-bfca-ab06ee4b6f17=SyncProgress{eventSerialNo=49, syncStage='CDC', batchOffset='{Inspectwimtest3={batch_read_connector_offset=null, batch_read_connector_status=OVER}, Inspectwimtest4={batch_read_connector_offset=null, batch_read_connector_status=OVER}, Inspectwimtest1={batch_read_connector_offset=null, batch_read_connector_status=OVER}, Inspectwimtest2={batch_read_connector_offset=null, batch_read_connector_status=OVER}, Inspectwimtest5={batch_read_connector_offset={}, batch_read_connector_status=RUNNING}}', streamOffset='MysqlStreamOffset{name='9529129e-1edc-4824-8929-f460c9ca5d59', offset={{"server":"9529129e-1edc-4824-8929-f460c9ca5d59"}={"ts_sec":1719304063,"file":"binlog.000031","pos":1060990946,"server_id":1}}}'}}, errors: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/syncProgress/667a767910fc5c6259c570d4": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[WARN ] 2024-06-25 16:34:27.366 - [任务 31] - Save milestone failed: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/update": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[INFO ] 2024-06-25 16:34:27.488 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] running status set to false 
[INFO ] 2024-06-25 16:34:27.580 - [任务 31][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-25 16:34:27.580 - [任务 31][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-06-25 16:34:27.589 - [任务 31][SouceMysql] - Incremental sync completed 
[INFO ] 2024-06-25 16:34:27.605 - [任务 31][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833 
[INFO ] 2024-06-25 16:34:27.606 - [任务 31][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833 
[INFO ] 2024-06-25 16:34:27.606 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] schema data cleaned 
[INFO ] 2024-06-25 16:34:27.608 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] monitor closed 
[INFO ] 2024-06-25 16:34:27.610 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] close complete, cost 125 ms 
[INFO ] 2024-06-25 16:34:27.610 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] running status set to false 
[INFO ] 2024-06-25 16:34:27.610 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] schema data cleaned 
[INFO ] 2024-06-25 16:34:27.611 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] monitor closed 
[INFO ] 2024-06-25 16:34:27.614 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] close complete, cost 2 ms 
[INFO ] 2024-06-25 16:34:27.614 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] running status set to false 
[INFO ] 2024-06-25 16:38:42.615 - [任务 31][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-20936b02-bfff-40d4-bfca-ab06ee4b6f17 
[INFO ] 2024-06-25 16:38:42.617 - [任务 31][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-20936b02-bfff-40d4-bfca-ab06ee4b6f17 
[INFO ] 2024-06-25 16:38:42.618 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] schema data cleaned 
[INFO ] 2024-06-25 16:38:42.618 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] monitor closed 
[INFO ] 2024-06-25 16:38:42.625 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] close complete, cost 255010 ms 
[INFO ] 2024-06-25 16:38:44.081 - [任务 31] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 16:38:44.094 - [任务 31] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7d5382d 
[INFO ] 2024-06-25 16:38:44.094 - [任务 31] - Stop task milestones: 667a767910fc5c6259c570d4(任务 31)  
[INFO ] 2024-06-25 16:38:44.127 - [任务 31] - Stopped task aspect(s) 
[INFO ] 2024-06-25 16:38:44.127 - [任务 31] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 16:38:44.127 - [任务 31] - Remove memory task client succeed, task: 任务 31[667a767910fc5c6259c570d4] 
[INFO ] 2024-06-25 16:38:44.127 - [任务 31] - Destroy memory task client cache succeed, task: 任务 31[667a767910fc5c6259c570d4] 
[INFO ] 2024-06-25 16:43:48.610 - [任务 31] - Start task milestones: 667a767910fc5c6259c570d4(任务 31) 
[INFO ] 2024-06-25 16:43:48.611 - [任务 31] - Task initialization... 
[INFO ] 2024-06-25 16:43:48.888 - [任务 31] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 16:43:49.052 - [任务 31] - The engine receives 任务 31 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 16:43:49.053 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] start preload schema,table counts: 8 
[INFO ] 2024-06-25 16:43:49.053 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 16:43:49.053 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] start preload schema,table counts: 8 
[INFO ] 2024-06-25 16:43:49.053 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] start preload schema,table counts: 8 
[INFO ] 2024-06-25 16:43:49.054 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 16:43:49.054 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 16:43:49.266 - [任务 31][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 16:43:49.266 - [任务 31][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 16:43:49.500 - [任务 31][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-06-25 16:43:49.500 - [任务 31][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-06-25 16:43:49.504 - [任务 31][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-25 16:43:49.504 - [任务 31][SouceMysql] - batch offset found: {"Inspectwimtest3":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"Inspectwimtest4":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"Inspectwimtest1":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"Inspectwimtest2":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"Inspectwimtest5":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"name":"9529129e-1edc-4824-8929-f460c9ca5d59","offset":{"{\"server\":\"9529129e-1edc-4824-8929-f460c9ca5d59\"}":"{\"ts_sec\":1719304063,\"file\":\"binlog.000031\",\"pos\":1060990946,\"server_id\":1}"}} 
[INFO ] 2024-06-25 16:43:49.542 - [任务 31] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 16:43:49.594 - [任务 31][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 16:43:49.594 - [任务 31][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:43:49.599 - [任务 31][SouceMysql] - Starting stream read, table list: [Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest7, Inspectwimtest8, Inspectwimtest5, Inspectwimtest6], offset: {"name":"9529129e-1edc-4824-8929-f460c9ca5d59","offset":{"{\"server\":\"9529129e-1edc-4824-8929-f460c9ca5d59\"}":"{\"ts_sec\":1719304063,\"file\":\"binlog.000031\",\"pos\":1060990946,\"server_id\":1}"}} 
[INFO ] 2024-06-25 16:43:49.623 - [任务 31][SouceMysql] - Starting mysql cdc, server name: 9529129e-1edc-4824-8929-f460c9ca5d59 
[INFO ] 2024-06-25 16:43:49.623 - [任务 31][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1844246025
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9529129e-1edc-4824-8929-f460c9ca5d59
  database.port: 3306
  threadName: Debezium-Mysql-Connector-9529129e-1edc-4824-8929-f460c9ca5d59
  database.hostname: localhost
  database.password: ********
  name: 9529129e-1edc-4824-8929-f460c9ca5d59
  pdk.offset.string: {"name":"9529129e-1edc-4824-8929-f460c9ca5d59","offset":{"{\"server\":\"9529129e-1edc-4824-8929-f460c9ca5d59\"}":"{\"ts_sec\":1719304063,\"file\":\"binlog.000031\",\"pos\":1060990946,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Inspectwimtest3,test.Inspectwimtest4,test.Inspectwimtest1,test.Inspectwimtest2,test.Inspectwimtest7,test.Inspectwimtest8,test.Inspectwimtest5,test.Inspectwimtest6
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 16:43:49.693 - [任务 31][SouceMysql] - Connector Mysql incremental start succeed, tables: [Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest7, Inspectwimtest8, Inspectwimtest5, Inspectwimtest6], data change syncing 
[INFO ] 2024-06-25 16:45:57.357 - [任务 31][SouceMysql] - Found new table(s): [Inspectwimtest9] 
[INFO ] 2024-06-25 16:45:57.393 - [任务 31][SouceMysql] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-06-25 16:45:57.393 - [任务 31][SouceMysql] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@1838ee2b: {"table":{"comment":"","id":"Inspectwimtest9","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"}],"name":"PRIMARY","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"Inspectwimtest9","nameFieldMap":{"id":{"autoInc":false,"comment":"","dataType":"int","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"comment":"","dataType":"varchar(20)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"}},"unique":true}},"tableId":"Inspectwimtest9","type":206} 
[INFO ] 2024-06-25 16:46:17.127 - [任务 31][SouceMysql] - Create new table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_Inspectwimtest9_667413fd7b5e1f6c3b139e78_667a767910fc5c6259c570d4 
[INFO ] 2024-06-25 16:46:17.218 - [任务 31][SouceMysql] - Create new table schema transform finished: TapTable id Inspectwimtest9 name Inspectwimtest9 storageEngine null charset null number of fields 2 
[INFO ] 2024-06-25 16:46:17.222 - [任务 31][SouceMysql] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-06-25 16:46:17.347 - [任务 31][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-25 16:46:17.347 - [任务 31][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-06-25 16:46:17.372 - [任务 31][SouceMysql] - Incremental sync completed 
[INFO ] 2024-06-25 16:46:17.606 - [任务 31][SouceMysql] - Starting batch read, table name: Inspectwimtest9, offset: null 
[INFO ] 2024-06-25 16:46:17.606 - [任务 31][SouceMysql] - Table Inspectwimtest9 is going to be initial synced 
[INFO ] 2024-06-25 16:46:17.645 - [任务 31][SouceMysql] - Table [Inspectwimtest9] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:46:17.645 - [任务 31][SouceMysql] - Query table 'Inspectwimtest9' counts: 0 
[INFO ] 2024-06-25 16:46:17.673 - [任务 31][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:46:17.675 - [任务 31][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 16:46:17.675 - [任务 31][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:46:17.717 - [任务 31][SouceMysql] - Starting stream read, table list: [Inspectwimtest9, Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest7, Inspectwimtest8, Inspectwimtest5, Inspectwimtest6], offset: {"name":"9529129e-1edc-4824-8929-f460c9ca5d59","offset":{"{\"server\":\"9529129e-1edc-4824-8929-f460c9ca5d59\"}":"{\"ts_sec\":1719305029,\"file\":\"binlog.000031\",\"pos\":1060991226,\"server_id\":1}"}} 
[INFO ] 2024-06-25 16:46:17.717 - [任务 31][SouceMysql] - Starting mysql cdc, server name: 9529129e-1edc-4824-8929-f460c9ca5d59 
[INFO ] 2024-06-25 16:46:17.803 - [任务 31][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1722240997
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9529129e-1edc-4824-8929-f460c9ca5d59
  database.port: 3306
  threadName: Debezium-Mysql-Connector-9529129e-1edc-4824-8929-f460c9ca5d59
  database.hostname: localhost
  database.password: ********
  name: 9529129e-1edc-4824-8929-f460c9ca5d59
  pdk.offset.string: {"name":"9529129e-1edc-4824-8929-f460c9ca5d59","offset":{"{\"server\":\"9529129e-1edc-4824-8929-f460c9ca5d59\"}":"{\"ts_sec\":1719305029,\"file\":\"binlog.000031\",\"pos\":1060991226,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Inspectwimtest9,test.Inspectwimtest3,test.Inspectwimtest4,test.Inspectwimtest1,test.Inspectwimtest2,test.Inspectwimtest7,test.Inspectwimtest8,test.Inspectwimtest5,test.Inspectwimtest6
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 16:46:17.835 - [任务 31][SouceMysql] - Connector Mysql incremental start succeed, tables: [Inspectwimtest9, Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest7, Inspectwimtest8, Inspectwimtest5, Inspectwimtest6], data change syncing 
[INFO ] 2024-06-25 16:48:03.786 - [任务 31] - Stop task milestones: 667a767910fc5c6259c570d4(任务 31)  
[INFO ] 2024-06-25 16:48:03.963 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] running status set to false 
[INFO ] 2024-06-25 16:48:04.071 - [任务 31][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-25 16:48:04.072 - [任务 31][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-06-25 16:48:04.080 - [任务 31][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833 
[INFO ] 2024-06-25 16:48:04.080 - [任务 31][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833 
[INFO ] 2024-06-25 16:48:04.080 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] schema data cleaned 
[INFO ] 2024-06-25 16:48:04.081 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] monitor closed 
[INFO ] 2024-06-25 16:48:04.082 - [任务 31][SouceMysql] - Node SouceMysql[bdc4fd4b-23ea-4ab1-89fc-e2e5b5153833] close complete, cost 118 ms 
[INFO ] 2024-06-25 16:48:04.082 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] running status set to false 
[INFO ] 2024-06-25 16:48:04.082 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] schema data cleaned 
[INFO ] 2024-06-25 16:48:04.082 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] monitor closed 
[INFO ] 2024-06-25 16:48:04.082 - [任务 31][表编辑] - Node 表编辑[eb21087d-4ffb-42ed-a7c4-a583cc122d70] close complete, cost 0 ms 
[INFO ] 2024-06-25 16:48:04.082 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] running status set to false 
[INFO ] 2024-06-25 16:48:04.096 - [任务 31][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-20936b02-bfff-40d4-bfca-ab06ee4b6f17 
[INFO ] 2024-06-25 16:48:04.096 - [任务 31][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-20936b02-bfff-40d4-bfca-ab06ee4b6f17 
[INFO ] 2024-06-25 16:48:04.096 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] schema data cleaned 
[INFO ] 2024-06-25 16:48:04.096 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] monitor closed 
[INFO ] 2024-06-25 16:48:04.299 - [任务 31][SourceMongo] - Node SourceMongo[20936b02-bfff-40d4-bfca-ab06ee4b6f17] close complete, cost 14 ms 
[INFO ] 2024-06-25 16:48:07.286 - [任务 31] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 16:48:07.287 - [任务 31] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@307028b4 
[INFO ] 2024-06-25 16:48:07.287 - [任务 31] - Stopped task aspect(s) 
[INFO ] 2024-06-25 16:48:07.287 - [任务 31] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 16:48:07.337 - [任务 31] - Remove memory task client succeed, task: 任务 31[667a767910fc5c6259c570d4] 
[INFO ] 2024-06-25 16:48:07.337 - [任务 31] - Destroy memory task client cache succeed, task: 任务 31[667a767910fc5c6259c570d4] 
