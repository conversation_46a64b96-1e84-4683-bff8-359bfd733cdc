[INFO ] 2024-10-10 14:24:35.583 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:24:35.583 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:24:35.583 - [任务 2(100)][a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe] - Node a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe[a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe] start preload schema,table counts: 0 
[INFO ] 2024-10-10 14:24:35.583 - [任务 2(100)][a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe] - Node a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe[a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:24:35.583 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:24:35.583 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 14:24:35.583 - [任务 2(100)][增强JS] - Node js_processor(增强JS: b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90) enable batch process 
[INFO ] 2024-10-10 14:24:41.469 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] running status set to false 
[INFO ] 2024-10-10 14:24:41.470 - [任务 2(100)][AA00PP] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:24:41.470 - [任务 2(100)][AA00PP] - PDK connector node released: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:24:41.471 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] schema data cleaned 
[INFO ] 2024-10-10 14:24:41.472 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] monitor closed 
[INFO ] 2024-10-10 14:24:41.472 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] close complete, cost 26 ms 
[INFO ] 2024-10-10 14:24:41.550 - [任务 2(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@76b5c9d5, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null 
[ERROR] 2024-10-10 14:24:41.554 - [任务 2(100)][增强JS] - script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@76b5c9d5, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null <-- Error Message -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@76b5c9d5, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	...

<-- Full Stack Trace -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@76b5c9d5, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:250)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	... 26 more
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 29 more

[INFO ] 2024-10-10 14:24:44.099 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] running status set to false 
[INFO ] 2024-10-10 14:24:44.101 - [任务 2(100)][a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe] - Node a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe[a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe] running status set to false 
[INFO ] 2024-10-10 14:24:44.101 - [任务 2(100)][a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe] - Node a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe[a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe] schema data cleaned 
[INFO ] 2024-10-10 14:24:44.101 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] schema data cleaned 
[INFO ] 2024-10-10 14:24:44.101 - [任务 2(100)][a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe] - Node a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe[a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe] monitor closed 
[INFO ] 2024-10-10 14:24:44.101 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] monitor closed 
[INFO ] 2024-10-10 14:24:44.101 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] close complete, cost 7 ms 
[INFO ] 2024-10-10 14:24:44.101 - [任务 2(100)][a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe] - Node a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe[a2ec8fb7-b7a7-4e87-8593-6ecf56eb67fe] close complete, cost 3 ms 
[INFO ] 2024-10-10 14:24:44.104 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 14:24:44.104 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 14:24:44.313 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 14:24:45.327 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:24:45.327 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:24:45.329 - [任务 2(100)][46b9627d-39ff-447f-9593-278b274041fc] - Node 46b9627d-39ff-447f-9593-278b274041fc[46b9627d-39ff-447f-9593-278b274041fc] start preload schema,table counts: 0 
[INFO ] 2024-10-10 14:24:45.329 - [任务 2(100)][46b9627d-39ff-447f-9593-278b274041fc] - Node 46b9627d-39ff-447f-9593-278b274041fc[46b9627d-39ff-447f-9593-278b274041fc] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:24:45.330 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:24:45.330 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:24:45.330 - [任务 2(100)][增强JS] - Node js_processor(增强JS: b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90) enable batch process 
[INFO ] 2024-10-10 14:24:45.341 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:24:45.341 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:24:45.341 - [任务 2(100)][c009839d-871d-48cb-979b-de4cefd087a3] - Node c009839d-871d-48cb-979b-de4cefd087a3[c009839d-871d-48cb-979b-de4cefd087a3] start preload schema,table counts: 0 
[INFO ] 2024-10-10 14:24:45.341 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:24:45.341 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:24:45.341 - [任务 2(100)][c009839d-871d-48cb-979b-de4cefd087a3] - Node c009839d-871d-48cb-979b-de4cefd087a3[c009839d-871d-48cb-979b-de4cefd087a3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:24:45.342 - [任务 2(100)][增强JS] - Node js_processor(增强JS: b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90) enable batch process 
[INFO ] 2024-10-10 14:24:50.917 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] running status set to false 
[INFO ] 2024-10-10 14:24:50.917 - [任务 2(100)][AA00PP] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:24:50.918 - [任务 2(100)][AA00PP] - PDK connector node released: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:24:50.919 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] schema data cleaned 
[INFO ] 2024-10-10 14:24:50.919 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] monitor closed 
[INFO ] 2024-10-10 14:24:50.920 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] close complete, cost 34 ms 
[INFO ] 2024-10-10 14:24:50.993 - [任务 2(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@8a442c0, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null 
[ERROR] 2024-10-10 14:24:50.994 - [任务 2(100)][增强JS] - script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@8a442c0, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null <-- Error Message -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@8a442c0, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	...

<-- Full Stack Trace -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@8a442c0, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:250)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	... 26 more
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 29 more

[INFO ] 2024-10-10 14:24:53.523 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] running status set to false 
[INFO ] 2024-10-10 14:24:53.524 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] schema data cleaned 
[INFO ] 2024-10-10 14:24:53.524 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] monitor closed 
[INFO ] 2024-10-10 14:24:53.525 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] close complete, cost 5 ms 
[INFO ] 2024-10-10 14:24:53.525 - [任务 2(100)][c009839d-871d-48cb-979b-de4cefd087a3] - Node c009839d-871d-48cb-979b-de4cefd087a3[c009839d-871d-48cb-979b-de4cefd087a3] running status set to false 
[INFO ] 2024-10-10 14:24:53.525 - [任务 2(100)][c009839d-871d-48cb-979b-de4cefd087a3] - Node c009839d-871d-48cb-979b-de4cefd087a3[c009839d-871d-48cb-979b-de4cefd087a3] schema data cleaned 
[INFO ] 2024-10-10 14:24:53.526 - [任务 2(100)][c009839d-871d-48cb-979b-de4cefd087a3] - Node c009839d-871d-48cb-979b-de4cefd087a3[c009839d-871d-48cb-979b-de4cefd087a3] monitor closed 
[INFO ] 2024-10-10 14:24:53.526 - [任务 2(100)][c009839d-871d-48cb-979b-de4cefd087a3] - Node c009839d-871d-48cb-979b-de4cefd087a3[c009839d-871d-48cb-979b-de4cefd087a3] close complete, cost 1 ms 
[INFO ] 2024-10-10 14:24:53.529 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 14:24:53.529 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 14:24:53.739 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 14:24:56.539 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] running status set to false 
[INFO ] 2024-10-10 14:24:56.540 - [任务 2(100)][AA00PP] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:24:56.541 - [任务 2(100)][AA00PP] - PDK connector node released: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:24:56.541 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] schema data cleaned 
[INFO ] 2024-10-10 14:24:56.541 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] monitor closed 
[INFO ] 2024-10-10 14:24:56.571 - [任务 2(100)][AA00PP] - Node AA00PP[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] close complete, cost 39 ms 
[INFO ] 2024-10-10 14:24:56.572 - [任务 2(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@b9b2b3b, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null 
[ERROR] 2024-10-10 14:24:56.779 - [任务 2(100)][增强JS] - script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@b9b2b3b, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null <-- Error Message -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@b9b2b3b, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	...

<-- Full Stack Trace -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@b9b2b3b, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:250)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	... 26 more
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 29 more

[INFO ] 2024-10-10 14:24:59.151 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] running status set to false 
[INFO ] 2024-10-10 14:24:59.154 - [任务 2(100)][46b9627d-39ff-447f-9593-278b274041fc] - Node 46b9627d-39ff-447f-9593-278b274041fc[46b9627d-39ff-447f-9593-278b274041fc] running status set to false 
[INFO ] 2024-10-10 14:24:59.154 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] schema data cleaned 
[INFO ] 2024-10-10 14:24:59.154 - [任务 2(100)][46b9627d-39ff-447f-9593-278b274041fc] - Node 46b9627d-39ff-447f-9593-278b274041fc[46b9627d-39ff-447f-9593-278b274041fc] schema data cleaned 
[INFO ] 2024-10-10 14:24:59.154 - [任务 2(100)][46b9627d-39ff-447f-9593-278b274041fc] - Node 46b9627d-39ff-447f-9593-278b274041fc[46b9627d-39ff-447f-9593-278b274041fc] monitor closed 
[INFO ] 2024-10-10 14:24:59.154 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] monitor closed 
[INFO ] 2024-10-10 14:24:59.154 - [任务 2(100)][46b9627d-39ff-447f-9593-278b274041fc] - Node 46b9627d-39ff-447f-9593-278b274041fc[46b9627d-39ff-447f-9593-278b274041fc] close complete, cost 12 ms 
[INFO ] 2024-10-10 14:24:59.154 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] close complete, cost 16 ms 
[INFO ] 2024-10-10 14:24:59.157 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 14:24:59.157 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 14:24:59.158 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 14:25:00.727 - [任务 2(100)][cce84683-fdef-45aa-b0a3-0eb089510e6c] - Node cce84683-fdef-45aa-b0a3-0eb089510e6c[cce84683-fdef-45aa-b0a3-0eb089510e6c] start preload schema,table counts: 0 
[INFO ] 2024-10-10 14:25:00.729 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:25:00.729 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:25:00.729 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 14:25:00.729 - [任务 2(100)][cce84683-fdef-45aa-b0a3-0eb089510e6c] - Node cce84683-fdef-45aa-b0a3-0eb089510e6c[cce84683-fdef-45aa-b0a3-0eb089510e6c] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 14:25:00.730 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 14:25:00.730 - [任务 2(100)][增强JS] - Node js_processor(增强JS: b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90) enable batch process 
[WARN ] 2024-10-10 14:25:06.303 - [任务 2(100)][AA00PP] - Source table is empty, trying to mock data 
[INFO ] 2024-10-10 14:25:06.313 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] running status set to false 
[INFO ] 2024-10-10 14:25:06.333 - [任务 2(100)][AA00PP] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:25:06.333 - [任务 2(100)][AA00PP] - PDK connector node released: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:25:06.333 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] schema data cleaned 
[INFO ] 2024-10-10 14:25:06.333 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] monitor closed 
[INFO ] 2024-10-10 14:25:06.355 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] close complete, cost 31 ms 
[INFO ] 2024-10-10 14:25:06.356 - [任务 2(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@544b39ef, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null 
[ERROR] 2024-10-10 14:25:06.564 - [任务 2(100)][增强JS] - script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@544b39ef, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null <-- Error Message -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@544b39ef, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	...

<-- Full Stack Trace -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@544b39ef, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:250)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	... 26 more
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 29 more

[INFO ] 2024-10-10 14:25:08.903 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] running status set to false 
[INFO ] 2024-10-10 14:25:08.903 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] schema data cleaned 
[INFO ] 2024-10-10 14:25:08.903 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] monitor closed 
[INFO ] 2024-10-10 14:25:08.904 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] close complete, cost 2 ms 
[INFO ] 2024-10-10 14:25:08.906 - [任务 2(100)][cce84683-fdef-45aa-b0a3-0eb089510e6c] - Node cce84683-fdef-45aa-b0a3-0eb089510e6c[cce84683-fdef-45aa-b0a3-0eb089510e6c] running status set to false 
[INFO ] 2024-10-10 14:25:08.906 - [任务 2(100)][cce84683-fdef-45aa-b0a3-0eb089510e6c] - Node cce84683-fdef-45aa-b0a3-0eb089510e6c[cce84683-fdef-45aa-b0a3-0eb089510e6c] schema data cleaned 
[INFO ] 2024-10-10 14:25:08.906 - [任务 2(100)][cce84683-fdef-45aa-b0a3-0eb089510e6c] - Node cce84683-fdef-45aa-b0a3-0eb089510e6c[cce84683-fdef-45aa-b0a3-0eb089510e6c] monitor closed 
[INFO ] 2024-10-10 14:25:08.906 - [任务 2(100)][cce84683-fdef-45aa-b0a3-0eb089510e6c] - Node cce84683-fdef-45aa-b0a3-0eb089510e6c[cce84683-fdef-45aa-b0a3-0eb089510e6c] close complete, cost 0 ms 
[INFO ] 2024-10-10 14:25:08.908 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 14:25:08.908 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 14:25:09.115 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 14:25:12.598 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:25:12.598 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:25:12.601 - [任务 2(100)][b81301aa-5237-40bd-a7d3-cc6d7587992c] - Node b81301aa-5237-40bd-a7d3-cc6d7587992c[b81301aa-5237-40bd-a7d3-cc6d7587992c] start preload schema,table counts: 0 
[INFO ] 2024-10-10 14:25:12.601 - [任务 2(100)][b81301aa-5237-40bd-a7d3-cc6d7587992c] - Node b81301aa-5237-40bd-a7d3-cc6d7587992c[b81301aa-5237-40bd-a7d3-cc6d7587992c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:25:12.601 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:25:12.601 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:25:12.601 - [任务 2(100)][增强JS] - Node js_processor(增强JS: b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90) enable batch process 
[WARN ] 2024-10-10 14:25:18.109 - [任务 2(100)][AA00PP] - Source table is empty, trying to mock data 
[INFO ] 2024-10-10 14:25:18.110 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] running status set to false 
[INFO ] 2024-10-10 14:25:18.150 - [任务 2(100)][AA00PP] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:25:18.150 - [任务 2(100)][AA00PP] - PDK connector node released: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:25:18.150 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] schema data cleaned 
[INFO ] 2024-10-10 14:25:18.150 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] monitor closed 
[INFO ] 2024-10-10 14:25:18.170 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] close complete, cost 44 ms 
[INFO ] 2024-10-10 14:25:18.170 - [任务 2(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@3de0d51c, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null 
[ERROR] 2024-10-10 14:25:18.377 - [任务 2(100)][增强JS] - script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@3de0d51c, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null <-- Error Message -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@3de0d51c, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	...

<-- Full Stack Trace -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@3de0d51c, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:250)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	... 26 more
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 29 more

[INFO ] 2024-10-10 14:25:20.726 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] running status set to false 
[INFO ] 2024-10-10 14:25:20.729 - [任务 2(100)][b81301aa-5237-40bd-a7d3-cc6d7587992c] - Node b81301aa-5237-40bd-a7d3-cc6d7587992c[b81301aa-5237-40bd-a7d3-cc6d7587992c] running status set to false 
[INFO ] 2024-10-10 14:25:20.729 - [任务 2(100)][b81301aa-5237-40bd-a7d3-cc6d7587992c] - Node b81301aa-5237-40bd-a7d3-cc6d7587992c[b81301aa-5237-40bd-a7d3-cc6d7587992c] schema data cleaned 
[INFO ] 2024-10-10 14:25:20.729 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] schema data cleaned 
[INFO ] 2024-10-10 14:25:20.731 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] monitor closed 
[INFO ] 2024-10-10 14:25:20.731 - [任务 2(100)][b81301aa-5237-40bd-a7d3-cc6d7587992c] - Node b81301aa-5237-40bd-a7d3-cc6d7587992c[b81301aa-5237-40bd-a7d3-cc6d7587992c] monitor closed 
[INFO ] 2024-10-10 14:25:20.733 - [任务 2(100)][b81301aa-5237-40bd-a7d3-cc6d7587992c] - Node b81301aa-5237-40bd-a7d3-cc6d7587992c[b81301aa-5237-40bd-a7d3-cc6d7587992c] close complete, cost 4 ms 
[INFO ] 2024-10-10 14:25:20.733 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] close complete, cost 9 ms 
[INFO ] 2024-10-10 14:25:20.737 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 14:25:20.737 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 14:25:20.739 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 14:25:22.197 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:25:22.198 - [任务 2(100)][373d5398-3475-4367-be22-e7f8ae30c7ad] - Node 373d5398-3475-4367-be22-e7f8ae30c7ad[373d5398-3475-4367-be22-e7f8ae30c7ad] start preload schema,table counts: 0 
[INFO ] 2024-10-10 14:25:22.198 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:25:22.198 - [任务 2(100)][373d5398-3475-4367-be22-e7f8ae30c7ad] - Node 373d5398-3475-4367-be22-e7f8ae30c7ad[373d5398-3475-4367-be22-e7f8ae30c7ad] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:25:22.198 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:25:22.198 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 14:25:22.404 - [任务 2(100)][增强JS] - Node js_processor(增强JS: b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90) enable batch process 
[WARN ] 2024-10-10 14:25:27.690 - [任务 2(100)][AA00PP] - Source table is empty, trying to mock data 
[INFO ] 2024-10-10 14:25:27.712 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] running status set to false 
[INFO ] 2024-10-10 14:25:27.712 - [任务 2(100)][AA00PP] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:25:27.712 - [任务 2(100)][AA00PP] - PDK connector node released: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:25:27.712 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] schema data cleaned 
[INFO ] 2024-10-10 14:25:27.712 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] monitor closed 
[INFO ] 2024-10-10 14:25:27.747 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] close complete, cost 21 ms 
[INFO ] 2024-10-10 14:25:27.748 - [任务 2(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@14479271, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null 
[ERROR] 2024-10-10 14:25:27.955 - [任务 2(100)][增强JS] - script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@14479271, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null <-- Error Message -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@14479271, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	...

<-- Full Stack Trace -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@14479271, function process(record){
  int b=10;
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:250)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	... 26 more
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:6 Expected ; but found b
  int b=10;
      ^

	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 29 more

[INFO ] 2024-10-10 14:25:30.306 - [任务 2(100)][373d5398-3475-4367-be22-e7f8ae30c7ad] - Node 373d5398-3475-4367-be22-e7f8ae30c7ad[373d5398-3475-4367-be22-e7f8ae30c7ad] running status set to false 
[INFO ] 2024-10-10 14:25:30.307 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] running status set to false 
[INFO ] 2024-10-10 14:25:30.307 - [任务 2(100)][373d5398-3475-4367-be22-e7f8ae30c7ad] - Node 373d5398-3475-4367-be22-e7f8ae30c7ad[373d5398-3475-4367-be22-e7f8ae30c7ad] schema data cleaned 
[INFO ] 2024-10-10 14:25:30.307 - [任务 2(100)][373d5398-3475-4367-be22-e7f8ae30c7ad] - Node 373d5398-3475-4367-be22-e7f8ae30c7ad[373d5398-3475-4367-be22-e7f8ae30c7ad] monitor closed 
[INFO ] 2024-10-10 14:25:30.307 - [任务 2(100)][373d5398-3475-4367-be22-e7f8ae30c7ad] - Node 373d5398-3475-4367-be22-e7f8ae30c7ad[373d5398-3475-4367-be22-e7f8ae30c7ad] close complete, cost 6 ms 
[INFO ] 2024-10-10 14:25:30.308 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] schema data cleaned 
[INFO ] 2024-10-10 14:25:30.308 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] monitor closed 
[INFO ] 2024-10-10 14:25:30.309 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] close complete, cost 8 ms 
[INFO ] 2024-10-10 14:25:30.312 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 14:25:30.312 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 14:25:30.314 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 14:25:48.557 - [任务 2(100)][97de8853-ab21-422b-868a-f9372db42f09] - Node 97de8853-ab21-422b-868a-f9372db42f09[97de8853-ab21-422b-868a-f9372db42f09] start preload schema,table counts: 0 
[INFO ] 2024-10-10 14:25:48.557 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:25:48.557 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:25:48.557 - [任务 2(100)][97de8853-ab21-422b-868a-f9372db42f09] - Node 97de8853-ab21-422b-868a-f9372db42f09[97de8853-ab21-422b-868a-f9372db42f09] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:25:48.557 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:25:48.557 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:25:48.557 - [任务 2(100)][增强JS] - Node js_processor(增强JS: b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90) enable batch process 
[INFO ] 2024-10-10 14:25:49.109 - [任务 2(100)][c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682] - Node c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682[c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682] start preload schema,table counts: 0 
[INFO ] 2024-10-10 14:25:49.109 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:25:49.109 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:25:49.109 - [任务 2(100)][c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682] - Node c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682[c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:25:49.109 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:25:49.109 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:25:49.110 - [任务 2(100)][增强JS] - Node js_processor(增强JS: b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90) enable batch process 
[WARN ] 2024-10-10 14:25:54.255 - [任务 2(100)][AA00PP] - Source table is empty, trying to mock data 
[INFO ] 2024-10-10 14:25:54.257 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] running status set to false 
[INFO ] 2024-10-10 14:25:54.316 - [任务 2(100)][AA00PP] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:25:54.316 - [任务 2(100)][AA00PP] - PDK connector node released: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:25:54.317 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] schema data cleaned 
[INFO ] 2024-10-10 14:25:54.317 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] monitor closed 
[INFO ] 2024-10-10 14:25:54.318 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] close complete, cost 63 ms 
[INFO ] 2024-10-10 14:25:54.353 - [任务 2(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@3e10f824, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null 
[ERROR] 2024-10-10 14:25:54.353 - [任务 2(100)][增强JS] - script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@3e10f824, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null <-- Error Message -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@3e10f824, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:10 Expected ; but found i
  for(int i=0;i<10;i++){
          ^
<eval>:2:22 Expected ; but found )
  for(int i=0;i<10;i++){
                      ^
<eval>:6:1 Invalid return statement
	return record;
	^
<eval>:7:0 Expected eof but found }
}
^

	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	...

<-- Full Stack Trace -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@3e10f824, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:250)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:10 Expected ; but found i
  for(int i=0;i<10;i++){
          ^
<eval>:2:22 Expected ; but found )
  for(int i=0;i<10;i++){
                      ^
<eval>:6:1 Invalid return statement
	return record;
	^
<eval>:7:0 Expected eof but found }
}
^

	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	... 26 more
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:10 Expected ; but found i
  for(int i=0;i<10;i++){
          ^
<eval>:2:22 Expected ; but found )
  for(int i=0;i<10;i++){
                      ^
<eval>:6:1 Invalid return statement
	return record;
	^
<eval>:7:0 Expected eof but found }
}
^

	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 29 more

[INFO ] 2024-10-10 14:25:56.899 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] running status set to false 
[INFO ] 2024-10-10 14:25:56.899 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] schema data cleaned 
[INFO ] 2024-10-10 14:25:56.902 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] monitor closed 
[INFO ] 2024-10-10 14:25:56.902 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] close complete, cost 1 ms 
[INFO ] 2024-10-10 14:25:56.904 - [任务 2(100)][97de8853-ab21-422b-868a-f9372db42f09] - Node 97de8853-ab21-422b-868a-f9372db42f09[97de8853-ab21-422b-868a-f9372db42f09] running status set to false 
[INFO ] 2024-10-10 14:25:56.904 - [任务 2(100)][97de8853-ab21-422b-868a-f9372db42f09] - Node 97de8853-ab21-422b-868a-f9372db42f09[97de8853-ab21-422b-868a-f9372db42f09] schema data cleaned 
[INFO ] 2024-10-10 14:25:56.904 - [任务 2(100)][97de8853-ab21-422b-868a-f9372db42f09] - Node 97de8853-ab21-422b-868a-f9372db42f09[97de8853-ab21-422b-868a-f9372db42f09] monitor closed 
[INFO ] 2024-10-10 14:25:56.905 - [任务 2(100)][97de8853-ab21-422b-868a-f9372db42f09] - Node 97de8853-ab21-422b-868a-f9372db42f09[97de8853-ab21-422b-868a-f9372db42f09] close complete, cost 2 ms 
[INFO ] 2024-10-10 14:25:56.907 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 14:25:56.907 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 14:25:56.907 - [任务 2(100)] - Stopped task aspect(s) 
[WARN ] 2024-10-10 14:25:59.439 - [任务 2(100)][AA00PP] - Source table is empty, trying to mock data 
[INFO ] 2024-10-10 14:25:59.440 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] running status set to false 
[INFO ] 2024-10-10 14:25:59.462 - [任务 2(100)][AA00PP] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:25:59.462 - [任务 2(100)][AA00PP] - PDK connector node released: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:25:59.462 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] schema data cleaned 
[INFO ] 2024-10-10 14:25:59.462 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] monitor closed 
[INFO ] 2024-10-10 14:25:59.491 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] close complete, cost 28 ms 
[INFO ] 2024-10-10 14:25:59.491 - [任务 2(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@6c9cb1f8, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null 
[ERROR] 2024-10-10 14:25:59.697 - [任务 2(100)][增强JS] - script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@6c9cb1f8, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null <-- Error Message -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@6c9cb1f8, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:10 Expected ; but found i
  for(int i=0;i<10;i++){
          ^
<eval>:2:22 Expected ; but found )
  for(int i=0;i<10;i++){
                      ^
<eval>:6:1 Invalid return statement
	return record;
	^
<eval>:7:0 Expected eof but found }
}
^

	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	...

<-- Full Stack Trace -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@6c9cb1f8, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:250)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:10 Expected ; but found i
  for(int i=0;i<10;i++){
          ^
<eval>:2:22 Expected ; but found )
  for(int i=0;i<10;i++){
                      ^
<eval>:6:1 Invalid return statement
	return record;
	^
<eval>:7:0 Expected eof but found }
}
^

	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	... 26 more
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:10 Expected ; but found i
  for(int i=0;i<10;i++){
          ^
<eval>:2:22 Expected ; but found )
  for(int i=0;i<10;i++){
                      ^
<eval>:6:1 Invalid return statement
	return record;
	^
<eval>:7:0 Expected eof but found }
}
^

	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 29 more

[INFO ] 2024-10-10 14:26:02.037 - [任务 2(100)][c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682] - Node c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682[c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682] running status set to false 
[INFO ] 2024-10-10 14:26:02.037 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] running status set to false 
[INFO ] 2024-10-10 14:26:02.038 - [任务 2(100)][c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682] - Node c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682[c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682] schema data cleaned 
[INFO ] 2024-10-10 14:26:02.039 - [任务 2(100)][c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682] - Node c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682[c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682] monitor closed 
[INFO ] 2024-10-10 14:26:02.040 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] schema data cleaned 
[INFO ] 2024-10-10 14:26:02.040 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] monitor closed 
[INFO ] 2024-10-10 14:26:02.040 - [任务 2(100)][c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682] - Node c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682[c1dfd5fc-24e1-4ed5-bfc3-9c2f9b5a9682] close complete, cost 4 ms 
[INFO ] 2024-10-10 14:26:02.041 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] close complete, cost 6 ms 
[INFO ] 2024-10-10 14:26:02.045 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 14:26:02.047 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 14:26:02.047 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 14:30:57.453 - [任务 2(100)][4b034826-9d20-4f9d-a74d-5367f9bc68de] - Node 4b034826-9d20-4f9d-a74d-5367f9bc68de[4b034826-9d20-4f9d-a74d-5367f9bc68de] start preload schema,table counts: 0 
[INFO ] 2024-10-10 14:30:57.454 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:30:57.454 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:30:57.454 - [任务 2(100)][4b034826-9d20-4f9d-a74d-5367f9bc68de] - Node 4b034826-9d20-4f9d-a74d-5367f9bc68de[4b034826-9d20-4f9d-a74d-5367f9bc68de] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:30:57.454 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] preload schema finished, cost 2 ms 
[INFO ] 2024-10-10 14:30:57.454 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] preload schema finished, cost 2 ms 
[INFO ] 2024-10-10 14:30:57.454 - [任务 2(100)][增强JS] - Node js_processor(增强JS: b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90) enable batch process 
[WARN ] 2024-10-10 14:31:03.546 - [任务 2(100)][AA00PP] - Source table is empty, trying to mock data 
[INFO ] 2024-10-10 14:31:03.562 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] running status set to false 
[INFO ] 2024-10-10 14:31:03.660 - [任务 2(100)][AA00PP] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:31:03.662 - [任务 2(100)][AA00PP] - PDK connector node released: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:31:03.662 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] schema data cleaned 
[INFO ] 2024-10-10 14:31:03.662 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] monitor closed 
[INFO ] 2024-10-10 14:31:03.663 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] close complete, cost 111 ms 
[INFO ] 2024-10-10 14:31:03.703 - [任务 2(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@7461857a, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null 
[ERROR] 2024-10-10 14:31:03.707 - [任务 2(100)][增强JS] - script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@7461857a, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null <-- Error Message -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@7461857a, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:10 Expected ; but found i
  for(int i=0;i<10;i++){
          ^
<eval>:2:22 Expected ; but found )
  for(int i=0;i<10;i++){
                      ^
<eval>:6:1 Invalid return statement
	return record;
	^
<eval>:7:0 Expected eof but found }
}
^

	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	...

<-- Full Stack Trace -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@7461857a, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:250)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:10 Expected ; but found i
  for(int i=0;i<10;i++){
          ^
<eval>:2:22 Expected ; but found )
  for(int i=0;i<10;i++){
                      ^
<eval>:6:1 Invalid return statement
	return record;
	^
<eval>:7:0 Expected eof but found }
}
^

	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	... 26 more
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:10 Expected ; but found i
  for(int i=0;i<10;i++){
          ^
<eval>:2:22 Expected ; but found )
  for(int i=0;i<10;i++){
                      ^
<eval>:6:1 Invalid return statement
	return record;
	^
<eval>:7:0 Expected eof but found }
}
^

	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 29 more

[INFO ] 2024-10-10 14:31:06.261 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] running status set to false 
[INFO ] 2024-10-10 14:31:06.264 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] schema data cleaned 
[INFO ] 2024-10-10 14:31:06.264 - [任务 2(100)][4b034826-9d20-4f9d-a74d-5367f9bc68de] - Node 4b034826-9d20-4f9d-a74d-5367f9bc68de[4b034826-9d20-4f9d-a74d-5367f9bc68de] running status set to false 
[INFO ] 2024-10-10 14:31:06.264 - [任务 2(100)][4b034826-9d20-4f9d-a74d-5367f9bc68de] - Node 4b034826-9d20-4f9d-a74d-5367f9bc68de[4b034826-9d20-4f9d-a74d-5367f9bc68de] schema data cleaned 
[INFO ] 2024-10-10 14:31:06.265 - [任务 2(100)][4b034826-9d20-4f9d-a74d-5367f9bc68de] - Node 4b034826-9d20-4f9d-a74d-5367f9bc68de[4b034826-9d20-4f9d-a74d-5367f9bc68de] monitor closed 
[INFO ] 2024-10-10 14:31:06.265 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] monitor closed 
[INFO ] 2024-10-10 14:31:06.265 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] close complete, cost 6 ms 
[INFO ] 2024-10-10 14:31:06.265 - [任务 2(100)][4b034826-9d20-4f9d-a74d-5367f9bc68de] - Node 4b034826-9d20-4f9d-a74d-5367f9bc68de[4b034826-9d20-4f9d-a74d-5367f9bc68de] close complete, cost 2 ms 
[INFO ] 2024-10-10 14:31:06.266 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 14:31:06.266 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 14:31:06.267 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 14:31:11.662 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:31:11.663 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:31:11.663 - [任务 2(100)][0e620db5-efee-49ca-b917-b1c1fbcf00ca] - Node 0e620db5-efee-49ca-b917-b1c1fbcf00ca[0e620db5-efee-49ca-b917-b1c1fbcf00ca] start preload schema,table counts: 0 
[INFO ] 2024-10-10 14:31:11.663 - [任务 2(100)][0e620db5-efee-49ca-b917-b1c1fbcf00ca] - Node 0e620db5-efee-49ca-b917-b1c1fbcf00ca[0e620db5-efee-49ca-b917-b1c1fbcf00ca] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:31:11.663 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:31:11.664 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:31:11.872 - [任务 2(100)][增强JS] - Node js_processor(增强JS: b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90) enable batch process 
[WARN ] 2024-10-10 14:31:17.724 - [任务 2(100)][AA00PP] - Source table is empty, trying to mock data 
[INFO ] 2024-10-10 14:31:17.724 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] running status set to false 
[INFO ] 2024-10-10 14:31:17.788 - [任务 2(100)][AA00PP] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:31:17.796 - [任务 2(100)][AA00PP] - PDK connector node released: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:31:17.796 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] schema data cleaned 
[INFO ] 2024-10-10 14:31:17.796 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] monitor closed 
[INFO ] 2024-10-10 14:31:17.798 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] close complete, cost 71 ms 
[INFO ] 2024-10-10 14:31:17.838 - [任务 2(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@b8ec09f, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null 
[ERROR] 2024-10-10 14:31:17.854 - [任务 2(100)][增强JS] - script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@b8ec09f, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null <-- Error Message -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@b8ec09f, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:10 Expected ; but found i
  for(int i=0;i<10;i++){
          ^
<eval>:2:22 Expected ; but found )
  for(int i=0;i<10;i++){
                      ^
<eval>:6:1 Invalid return statement
	return record;
	^
<eval>:7:0 Expected eof but found }
}
^

	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	...

<-- Full Stack Trace -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@b8ec09f, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:250)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:10 Expected ; but found i
  for(int i=0;i<10;i++){
          ^
<eval>:2:22 Expected ; but found )
  for(int i=0;i<10;i++){
                      ^
<eval>:6:1 Invalid return statement
	return record;
	^
<eval>:7:0 Expected eof but found }
}
^

	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	... 26 more
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:10 Expected ; but found i
  for(int i=0;i<10;i++){
          ^
<eval>:2:22 Expected ; but found )
  for(int i=0;i<10;i++){
                      ^
<eval>:6:1 Invalid return statement
	return record;
	^
<eval>:7:0 Expected eof but found }
}
^

	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 29 more

[INFO ] 2024-10-10 14:31:20.399 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] running status set to false 
[INFO ] 2024-10-10 14:31:20.402 - [任务 2(100)][0e620db5-efee-49ca-b917-b1c1fbcf00ca] - Node 0e620db5-efee-49ca-b917-b1c1fbcf00ca[0e620db5-efee-49ca-b917-b1c1fbcf00ca] running status set to false 
[INFO ] 2024-10-10 14:31:20.402 - [任务 2(100)][0e620db5-efee-49ca-b917-b1c1fbcf00ca] - Node 0e620db5-efee-49ca-b917-b1c1fbcf00ca[0e620db5-efee-49ca-b917-b1c1fbcf00ca] schema data cleaned 
[INFO ] 2024-10-10 14:31:20.402 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] schema data cleaned 
[INFO ] 2024-10-10 14:31:20.402 - [任务 2(100)][0e620db5-efee-49ca-b917-b1c1fbcf00ca] - Node 0e620db5-efee-49ca-b917-b1c1fbcf00ca[0e620db5-efee-49ca-b917-b1c1fbcf00ca] monitor closed 
[INFO ] 2024-10-10 14:31:20.403 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] monitor closed 
[INFO ] 2024-10-10 14:31:20.403 - [任务 2(100)][0e620db5-efee-49ca-b917-b1c1fbcf00ca] - Node 0e620db5-efee-49ca-b917-b1c1fbcf00ca[0e620db5-efee-49ca-b917-b1c1fbcf00ca] close complete, cost 8 ms 
[INFO ] 2024-10-10 14:31:20.403 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] close complete, cost 10 ms 
[INFO ] 2024-10-10 14:31:20.404 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 14:31:20.404 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 14:31:20.455 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 14:41:01.569 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:41:01.570 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:41:01.570 - [任务 2(100)][7d979b00-7bbe-4254-a019-24d8d15cd71e] - Node 7d979b00-7bbe-4254-a019-24d8d15cd71e[7d979b00-7bbe-4254-a019-24d8d15cd71e] start preload schema,table counts: 0 
[INFO ] 2024-10-10 14:41:01.570 - [任务 2(100)][7d979b00-7bbe-4254-a019-24d8d15cd71e] - Node 7d979b00-7bbe-4254-a019-24d8d15cd71e[7d979b00-7bbe-4254-a019-24d8d15cd71e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:41:01.570 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:41:01.570 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:41:01.771 - [任务 2(100)][增强JS] - Node js_processor(增强JS: b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90) enable batch process 
[INFO ] 2024-10-10 14:41:07.471 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] running status set to false 
[INFO ] 2024-10-10 14:41:07.520 - [任务 2(100)][AA00PP] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:41:07.524 - [任务 2(100)][AA00PP] - PDK connector node released: HazelcastSampleSourcePdkDataNode-716bf9a0-1f35-461e-aa78-5e3b1cf211f4 
[INFO ] 2024-10-10 14:41:07.524 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] schema data cleaned 
[INFO ] 2024-10-10 14:41:07.524 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] monitor closed 
[INFO ] 2024-10-10 14:41:07.524 - [任务 2(100)][AA00PP] - Node BMSQL_ITEM[716bf9a0-1f35-461e-aa78-5e3b1cf211f4] close complete, cost 63 ms 
[INFO ] 2024-10-10 14:42:13.625 - [任务 2(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@c92a466, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null 
[ERROR] 2024-10-10 14:42:13.634 - [任务 2(100)][增强JS] - script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@c92a466, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null <-- Error Message -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@c92a466, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:10 Expected ; but found i
  for(int i=0;i<10;i++){
          ^
<eval>:2:22 Expected ; but found )
  for(int i=0;i<10;i++){
                      ^
<eval>:6:1 Invalid return statement
	return record;
	^
<eval>:7:0 Expected eof but found }
}
^

	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	...

<-- Full Stack Trace -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@c92a466, function process(record){
  for(int i=0;i<10;i++){
    log.info(i);
  }
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1660)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:250)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:10 Expected ; but found i
  for(int i=0;i<10;i++){
          ^
<eval>:2:22 Expected ; but found )
  for(int i=0;i<10;i++){
                      ^
<eval>:6:1 Invalid return statement
	return record;
	^
<eval>:7:0 Expected eof but found }
}
^

	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	... 26 more
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:2:10 Expected ; but found i
  for(int i=0;i<10;i++){
          ^
<eval>:2:22 Expected ; but found )
  for(int i=0;i<10;i++){
                      ^
<eval>:6:1 Invalid return statement
	return record;
	^
<eval>:7:0 Expected eof but found }
}
^

	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 29 more

[INFO ] 2024-10-10 14:42:16.211 - [任务 2(100)][7d979b00-7bbe-4254-a019-24d8d15cd71e] - Node 7d979b00-7bbe-4254-a019-24d8d15cd71e[7d979b00-7bbe-4254-a019-24d8d15cd71e] running status set to false 
[INFO ] 2024-10-10 14:42:16.213 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] running status set to false 
[INFO ] 2024-10-10 14:42:16.213 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] schema data cleaned 
[INFO ] 2024-10-10 14:42:16.213 - [任务 2(100)][7d979b00-7bbe-4254-a019-24d8d15cd71e] - Node 7d979b00-7bbe-4254-a019-24d8d15cd71e[7d979b00-7bbe-4254-a019-24d8d15cd71e] schema data cleaned 
[INFO ] 2024-10-10 14:42:16.213 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] monitor closed 
[INFO ] 2024-10-10 14:42:16.214 - [任务 2(100)][7d979b00-7bbe-4254-a019-24d8d15cd71e] - Node 7d979b00-7bbe-4254-a019-24d8d15cd71e[7d979b00-7bbe-4254-a019-24d8d15cd71e] monitor closed 
[INFO ] 2024-10-10 14:42:16.214 - [任务 2(100)][增强JS] - Node 增强JS[b1dfb7ae-65e9-4d9f-bd51-2cc9cfc0bf90] close complete, cost 25 ms 
[INFO ] 2024-10-10 14:42:16.214 - [任务 2(100)][7d979b00-7bbe-4254-a019-24d8d15cd71e] - Node 7d979b00-7bbe-4254-a019-24d8d15cd71e[7d979b00-7bbe-4254-a019-24d8d15cd71e] close complete, cost 16 ms 
[INFO ] 2024-10-10 14:42:16.218 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 14:42:16.218 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 14:42:16.218 - [任务 2(100)] - Stopped task aspect(s) 
