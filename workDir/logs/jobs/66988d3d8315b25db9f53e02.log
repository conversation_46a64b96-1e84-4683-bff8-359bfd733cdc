[INFO ] 2024-07-18 11:34:32.820 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658] - Task initialization... 
[INFO ] 2024-07-18 11:34:33.025 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658] - Start task milestones: 66988d3d8315b25db9f53e02(t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658) 
[INFO ] 2024-07-18 11:34:33.168 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 11:34:33.169 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658] - The engine receives t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 11:34:33.215 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[71bb845a-aa24-481b-bb98-30efc130efca] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:34:33.215 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][TableRename] - Node TableRename[c7bf4d19-41b8-428a-a11e-10649dc4123c] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:34:33.215 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[71bb845a-aa24-481b-bb98-30efc130efca] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:34:33.216 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][TableRename] - Node TableRename[c7bf4d19-41b8-428a-a11e-10649dc4123c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:34:33.216 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[deeb4429-5b9f-4a03-8cf5-61b57ed04b20] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:34:33.421 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[deeb4429-5b9f-4a03-8cf5-61b57ed04b20] preload schema finished, cost 1 ms 
[INFO ] 2024-07-18 11:34:33.710 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_t_1717403468657_3537] - Node(qa_mysql_repl_33306_t_1717403468657_3537) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-18 11:34:33.710 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_t_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 11:34:33.894 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Source node "qa_mysql_repl_33306_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 11:34:33.894 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Source node "qa_mysql_repl_33306_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 11:34:33.894 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 11:34:34.007 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000304","position":471467751,"gtidSet":""} 
[INFO ] 2024-07-18 11:34:34.007 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-18 11:34:34.008 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-07-18 11:34:34.127 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 11:34:34.127 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Starting batch read, table name: t_3_6_3_ddl, offset: null 
[INFO ] 2024-07-18 11:34:34.224 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Table t_3_6_3_ddl is going to be initial synced 
[INFO ] 2024-07-18 11:34:34.224 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Table [t_3_6_3_ddl] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:34:34.232 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Query table 't_3_6_3_ddl' counts: 1 
[INFO ] 2024-07-18 11:34:34.232 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:34:34.232 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 11:34:34.232 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:34:34.321 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Starting stream read, table list: [t_3_6_3_ddl, _tapdata_heartbeat_table], offset: {"filename":"mysql-bin.000304","position":471467751,"gtidSet":""} 
[INFO ] 2024-07-18 11:34:34.321 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Starting mysql cdc, server name: 588f638a-e2f8-4b49-ac22-57a522d403c3 
[INFO ] 2024-07-18 11:34:34.485 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 417475945
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 588f638a-e2f8-4b49-ac22-57a522d403c3
  database.port: 3306
  threadName: Debezium-Mysql-Connector-588f638a-e2f8-4b49-ac22-57a522d403c3
  database.hostname: *************
  database.password: ********
  name: 588f638a-e2f8-4b49-ac22-57a522d403c3
  pdk.offset.string: {"name":"588f638a-e2f8-4b49-ac22-57a522d403c3","offset":{"{\"server\":\"588f638a-e2f8-4b49-ac22-57a522d403c3\"}":"{\"file\":\"mysql-bin.000304\",\"pos\":471467751,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: t0.t_3_6_3_ddl,t0._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: t0
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-18 11:34:34.486 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Connector Mysql incremental start succeed, tables: [t_3_6_3_ddl, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 11:34:49.473 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Read DDL: alter table t0.t_3_6_3_ddl change name name varchar(30), about to be packaged as some event(s) 
[INFO ] 2024-07-18 11:34:49.475 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - DDL event  - Table: t_3_6_3_ddl
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='588f638a-e2f8-4b49-ac22-57a522d403c3', offset={{"server":"588f638a-e2f8-4b49-ac22-57a522d403c3"}={"ts_sec":1721273689,"file":"mysql-bin.000304","pos":471757622,"server_id":1121}}} 
[INFO ] 2024-07-18 11:34:49.475 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='t_3_6_3_ddl', fieldName='name', dataTypeChange=ValueChange{before=null, after=varchar(30)}, checkChange=null, constraintChange=null, nullableChange=null, commentChange=null, defaultChange=null, primaryChange=null} 
[INFO ] 2024-07-18 11:34:49.488 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_t_3_6_3_ddl_6697a4cfb92eda1a86f5244d_66988d3d8315b25db9f53e02 
[INFO ] 2024-07-18 11:34:49.694 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Alter table schema transform finished 
[INFO ] 2024-07-18 11:37:13.514 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[71bb845a-aa24-481b-bb98-30efc130efca] running status set to false 
[INFO ] 2024-07-18 11:37:13.564 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-18 11:37:13.564 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Mysql binlog reader stopped 
[INFO ] 2024-07-18 11:37:13.564 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 11:37:13.601 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-71bb845a-aa24-481b-bb98-30efc130efca 
[INFO ] 2024-07-18 11:37:13.601 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-71bb845a-aa24-481b-bb98-30efc130efca 
[INFO ] 2024-07-18 11:37:13.602 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[71bb845a-aa24-481b-bb98-30efc130efca] schema data cleaned 
[INFO ] 2024-07-18 11:37:13.602 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[71bb845a-aa24-481b-bb98-30efc130efca] monitor closed 
[INFO ] 2024-07-18 11:37:13.604 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[71bb845a-aa24-481b-bb98-30efc130efca] close complete, cost 147 ms 
[INFO ] 2024-07-18 11:37:13.604 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][TableRename] - Node TableRename[c7bf4d19-41b8-428a-a11e-10649dc4123c] running status set to false 
[INFO ] 2024-07-18 11:37:13.604 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][TableRename] - Node TableRename[c7bf4d19-41b8-428a-a11e-10649dc4123c] schema data cleaned 
[INFO ] 2024-07-18 11:37:13.604 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][TableRename] - Node TableRename[c7bf4d19-41b8-428a-a11e-10649dc4123c] monitor closed 
[INFO ] 2024-07-18 11:37:13.604 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][TableRename] - Node TableRename[c7bf4d19-41b8-428a-a11e-10649dc4123c] close complete, cost 0 ms 
[INFO ] 2024-07-18 11:37:13.604 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[deeb4429-5b9f-4a03-8cf5-61b57ed04b20] running status set to false 
[INFO ] 2024-07-18 11:37:13.689 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_t_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-deeb4429-5b9f-4a03-8cf5-61b57ed04b20 
[INFO ] 2024-07-18 11:37:13.690 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_t_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-deeb4429-5b9f-4a03-8cf5-61b57ed04b20 
[INFO ] 2024-07-18 11:37:13.690 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[deeb4429-5b9f-4a03-8cf5-61b57ed04b20] schema data cleaned 
[INFO ] 2024-07-18 11:37:13.690 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[deeb4429-5b9f-4a03-8cf5-61b57ed04b20] monitor closed 
[INFO ] 2024-07-18 11:37:13.690 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[deeb4429-5b9f-4a03-8cf5-61b57ed04b20] close complete, cost 85 ms 
[INFO ] 2024-07-18 11:37:13.998 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 11:37:14.117 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2656d35f 
[INFO ] 2024-07-18 11:37:14.117 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658] - Stop task milestones: 66988d3d8315b25db9f53e02(t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658)  
[INFO ] 2024-07-18 11:37:14.204 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658] - Stopped task aspect(s) 
[INFO ] 2024-07-18 11:37:14.204 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 11:37:14.412 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658] - Remove memory task client succeed, task: t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658[66988d3d8315b25db9f53e02] 
[INFO ] 2024-07-18 11:37:14.412 - [t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658] - Destroy memory task client cache succeed, task: t_3.6.3-mysql_to_mysql_ddl_alter_1717403468657_3537-1721273658[66988d3d8315b25db9f53e02] 
