[INFO ] 2024-04-07 01:50:44.485 - [任务 9] - Task initialization... 
[INFO ] 2024-04-07 01:50:44.486 - [任务 9] - Start task milestones: 66118ac543895b65e76afaab(任务 9) 
[INFO ] 2024-04-07 01:50:44.486 - [任务 9] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:50:44.486 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:50:44.486 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:50:44.486 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] start preload schema,table counts: 1 
[ERROR] 2024-04-07 01:50:44.486 - [任务 9][KafkaTest3] - java.lang.RuntimeException: Find schema failed, message: Table name "KafkaTest3" not exists, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_KafkaTest3_66116f05b896963849b3478f_66118ac543895b65e76afaab <-- Error Message -->
java.lang.RuntimeException: Find schema failed, message: Table name "KafkaTest3" not exists, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_KafkaTest3_66116f05b896963849b3478f_66118ac543895b65e76afaab

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "KafkaTest3" not exists, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_KafkaTest3_66116f05b896963849b3478f_66118ac543895b65e76afaab
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "KafkaTest3" not exists, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_KafkaTest3_66116f05b896963849b3478f_66118ac543895b65e76afaab
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:557)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:211)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "KafkaTest3" not exists, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_KafkaTest3_66116f05b896963849b3478f_66118ac543895b65e76afaab
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:203)
	... 12 more
Caused by: java.lang.RuntimeException: Table name "KafkaTest3" not exists, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_KafkaTest3_66116f05b896963849b3478f_66118ac543895b65e76afaab
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 15 more

[INFO ] 2024-04-07 01:50:44.486 - [任务 9][KafkaTest3] - Job suspend in error handle 
[INFO ] 2024-04-07 01:50:44.694 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] preload schema finished, cost 40 ms 
[INFO ] 2024-04-07 01:50:45.080 - [任务 9][CLAIMKAFKA] - Source node "CLAIMKAFKA" read batch size: 100 
[INFO ] 2024-04-07 01:50:45.080 - [任务 9][CLAIMKAFKA] - Source node "CLAIMKAFKA" event queue capacity: 200 
[INFO ] 2024-04-07 01:50:45.080 - [任务 9][CLAIMKAFKA] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 01:50:45.080 - [任务 9][CLAIMKAFKA] - batch offset found: {},stream offset found: [] 
[INFO ] 2024-04-07 01:50:45.122 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] running status set to false 
[WARN ] 2024-04-07 01:50:45.122 - [任务 9][CLAIMKAFKA] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-04-07 01:50:45.123 - [任务 9][CLAIMKAFKA] - Initial sync started 
[INFO ] 2024-04-07 01:50:45.123 - [任务 9][CLAIMKAFKA] - Initial sync completed 
[INFO ] 2024-04-07 01:50:45.127 - [任务 9][CLAIMKAFKA] - Incremental sync starting... 
[INFO ] 2024-04-07 01:50:45.127 - [任务 9][CLAIMKAFKA] - Incremental sync completed 
[INFO ] 2024-04-07 01:50:45.132 - [任务 9][CLAIMKAFKA] - PDK connector node stopped: HazelcastSourcePdkDataNode-ec4918ef-9790-4862-9468-49d492e37d73 
[INFO ] 2024-04-07 01:50:45.132 - [任务 9][CLAIMKAFKA] - PDK connector node released: HazelcastSourcePdkDataNode-ec4918ef-9790-4862-9468-49d492e37d73 
[INFO ] 2024-04-07 01:50:45.133 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] schema data cleaned 
[INFO ] 2024-04-07 01:50:45.135 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] monitor closed 
[INFO ] 2024-04-07 01:50:45.135 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] close complete, cost 13 ms 
[INFO ] 2024-04-07 01:50:45.135 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] running status set to false 
[INFO ] 2024-04-07 01:50:45.136 - [任务 9][KafkaTest3] - PDK connector node stopped: null 
[INFO ] 2024-04-07 01:50:45.136 - [任务 9][KafkaTest3] - PDK connector node released: null 
[INFO ] 2024-04-07 01:50:45.136 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] schema data cleaned 
[INFO ] 2024-04-07 01:50:45.136 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] monitor closed 
[INFO ] 2024-04-07 01:50:45.137 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] close complete, cost 1 ms 
[INFO ] 2024-04-07 01:50:47.369 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 01:50:47.411 - [任务 9] - Stop task milestones: 66118ac543895b65e76afaab(任务 9)  
[INFO ] 2024-04-07 01:50:47.411 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-04-07 01:50:47.412 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 01:50:47.432 - [任务 9] - Remove memory task client succeed, task: 任务 9[66118ac543895b65e76afaab] 
[INFO ] 2024-04-07 01:50:47.432 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[66118ac543895b65e76afaab] 
[INFO ] 2024-04-07 01:50:59.729 - [任务 9] - Task initialization... 
[INFO ] 2024-04-07 01:50:59.729 - [任务 9] - Start task milestones: 66118ac543895b65e76afaab(任务 9) 
[INFO ] 2024-04-07 01:50:59.801 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:50:59.801 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:50:59.860 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:50:59.860 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] start preload schema,table counts: 1 
[ERROR] 2024-04-07 01:50:59.872 - [任务 9][KafkaTest3] - java.lang.RuntimeException: Find schema failed, message: Table name "KafkaTest3" not exists, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_KafkaTest3_66116f05b896963849b3478f_66118ac543895b65e76afaab <-- Error Message -->
java.lang.RuntimeException: Find schema failed, message: Table name "KafkaTest3" not exists, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_KafkaTest3_66116f05b896963849b3478f_66118ac543895b65e76afaab

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "KafkaTest3" not exists, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_KafkaTest3_66116f05b896963849b3478f_66118ac543895b65e76afaab
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "KafkaTest3" not exists, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_KafkaTest3_66116f05b896963849b3478f_66118ac543895b65e76afaab
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:557)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:211)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "KafkaTest3" not exists, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_KafkaTest3_66116f05b896963849b3478f_66118ac543895b65e76afaab
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:203)
	... 12 more
Caused by: java.lang.RuntimeException: Table name "KafkaTest3" not exists, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_KafkaTest3_66116f05b896963849b3478f_66118ac543895b65e76afaab
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 15 more

[INFO ] 2024-04-07 01:50:59.872 - [任务 9][KafkaTest3] - Job suspend in error handle 
[INFO ] 2024-04-07 01:51:00.074 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] preload schema finished, cost 30 ms 
[INFO ] 2024-04-07 01:51:00.572 - [任务 9][CLAIMKAFKA] - Source node "CLAIMKAFKA" read batch size: 100 
[INFO ] 2024-04-07 01:51:00.573 - [任务 9][CLAIMKAFKA] - Source node "CLAIMKAFKA" event queue capacity: 200 
[INFO ] 2024-04-07 01:51:00.573 - [任务 9][CLAIMKAFKA] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 01:51:00.627 - [任务 9][CLAIMKAFKA] - batch offset found: {},stream offset found: [] 
[INFO ] 2024-04-07 01:51:00.627 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] running status set to false 
[WARN ] 2024-04-07 01:51:00.627 - [任务 9][CLAIMKAFKA] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-04-07 01:51:00.627 - [任务 9][CLAIMKAFKA] - Initial sync started 
[INFO ] 2024-04-07 01:51:00.627 - [任务 9][CLAIMKAFKA] - Initial sync completed 
[INFO ] 2024-04-07 01:51:00.627 - [任务 9][CLAIMKAFKA] - Incremental sync starting... 
[INFO ] 2024-04-07 01:51:00.627 - [任务 9][CLAIMKAFKA] - Incremental sync completed 
[INFO ] 2024-04-07 01:51:00.640 - [任务 9][CLAIMKAFKA] - PDK connector node stopped: HazelcastSourcePdkDataNode-ec4918ef-9790-4862-9468-49d492e37d73 
[INFO ] 2024-04-07 01:51:00.640 - [任务 9][CLAIMKAFKA] - PDK connector node released: HazelcastSourcePdkDataNode-ec4918ef-9790-4862-9468-49d492e37d73 
[INFO ] 2024-04-07 01:51:00.640 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] schema data cleaned 
[INFO ] 2024-04-07 01:51:00.640 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] monitor closed 
[INFO ] 2024-04-07 01:51:00.642 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] close complete, cost 16 ms 
[INFO ] 2024-04-07 01:51:00.642 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] running status set to false 
[INFO ] 2024-04-07 01:51:00.643 - [任务 9][KafkaTest3] - PDK connector node stopped: null 
[INFO ] 2024-04-07 01:51:00.643 - [任务 9][KafkaTest3] - PDK connector node released: null 
[INFO ] 2024-04-07 01:51:00.643 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] schema data cleaned 
[INFO ] 2024-04-07 01:51:00.644 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] monitor closed 
[INFO ] 2024-04-07 01:51:00.644 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] close complete, cost 1 ms 
[INFO ] 2024-04-07 01:51:04.100 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 01:51:04.103 - [任务 9] - Stop task milestones: 66118ac543895b65e76afaab(任务 9)  
[INFO ] 2024-04-07 01:51:04.115 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-04-07 01:51:04.132 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 01:51:04.134 - [任务 9] - Remove memory task client succeed, task: 任务 9[66118ac543895b65e76afaab] 
[INFO ] 2024-04-07 01:51:04.135 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[66118ac543895b65e76afaab] 
[INFO ] 2024-04-07 01:51:04.136 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 01:51:04.136 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-04-07 01:51:04.156 - [任务 9] - Remove memory task client succeed, task: 任务 9[66118ac543895b65e76afaab] 
[INFO ] 2024-04-07 01:51:04.159 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[66118ac543895b65e76afaab] 
[INFO ] 2024-04-07 02:00:15.759 - [任务 9] - Start task milestones: 66118ac543895b65e76afaab(任务 9) 
[INFO ] 2024-04-07 02:00:15.760 - [任务 9] - Task initialization... 
[INFO ] 2024-04-07 02:00:15.789 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 02:00:15.982 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 02:00:15.982 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] start preload schema,table counts: 1 
[INFO ] 2024-04-07 02:00:15.983 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] start preload schema,table counts: 1 
[INFO ] 2024-04-07 02:00:16.017 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] preload schema finished, cost 35 ms 
[INFO ] 2024-04-07 02:00:16.017 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] preload schema finished, cost 31 ms 
[INFO ] 2024-04-07 02:00:16.852 - [任务 9][CLAIMKAFKA] - Source node "CLAIMKAFKA" read batch size: 100 
[INFO ] 2024-04-07 02:00:16.852 - [任务 9][CLAIMKAFKA] - Source node "CLAIMKAFKA" event queue capacity: 200 
[INFO ] 2024-04-07 02:00:16.853 - [任务 9][CLAIMKAFKA] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 02:00:16.854 - [任务 9][CLAIMKAFKA] - batch offset found: {},stream offset found: [] 
[INFO ] 2024-04-07 02:00:16.931 - [任务 9][KafkaTest3] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-04-07 02:00:16.934 - [任务 9][CLAIMKAFKA] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-04-07 02:00:16.934 - [任务 9][CLAIMKAFKA] - Initial sync started 
[INFO ] 2024-04-07 02:00:16.934 - [任务 9][CLAIMKAFKA] - Starting batch read, table name: CLAIMKAFKA, offset: null 
[INFO ] 2024-04-07 02:00:16.935 - [任务 9][CLAIMKAFKA] - Table CLAIMKAFKA is going to be initial synced 
[ERROR] 2024-04-07 02:00:58.620 - [任务 9][CLAIMKAFKA] - java.lang.RuntimeException: event is null <-- Error Message -->
java.lang.RuntimeException: event is null

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: event is null
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:44)
	io.tapdata.entity.logger.Log.error(Log.java:43)
	io.tapdata.connector.kafka.KafkaService.makeCustomMessage(KafkaService.java:717)
	io.tapdata.connector.kafka.KafkaService.consumeOne(KafkaService.java:635)
	io.tapdata.connector.kafka.KafkaConnector.batchRead(KafkaConnector.java:313)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: event is null
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:46)
	at io.tapdata.entity.logger.Log.error(Log.java:43)
	at io.tapdata.connector.kafka.KafkaService.makeCustomMessage(KafkaService.java:717)
	at io.tapdata.connector.kafka.KafkaService.consumeOne(KafkaService.java:635)
	at io.tapdata.connector.kafka.KafkaConnector.batchRead(KafkaConnector.java:313)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:388)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshot$13(HazelcastSourcePdkDataNode.java:316)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:308)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:253)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:147)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:175)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: event is null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:44)
	... 24 more

[INFO ] 2024-04-07 02:00:58.642 - [任务 9][CLAIMKAFKA] - Job suspend in error handle 
[INFO ] 2024-04-07 02:00:58.643 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] running status set to false 
[INFO ] 2024-04-07 02:00:58.660 - [任务 9][CLAIMKAFKA] - Initial sync completed 
[INFO ] 2024-04-07 02:00:58.661 - [任务 9][CLAIMKAFKA] - PDK connector node stopped: HazelcastSourcePdkDataNode-ec4918ef-9790-4862-9468-49d492e37d73 
[INFO ] 2024-04-07 02:00:58.664 - [任务 9][CLAIMKAFKA] - PDK connector node released: HazelcastSourcePdkDataNode-ec4918ef-9790-4862-9468-49d492e37d73 
[INFO ] 2024-04-07 02:00:58.664 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] schema data cleaned 
[INFO ] 2024-04-07 02:00:58.676 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] monitor closed 
[INFO ] 2024-04-07 02:00:58.678 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] close complete, cost 30 ms 
[INFO ] 2024-04-07 02:00:58.718 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] running status set to false 
[INFO ] 2024-04-07 02:00:58.719 - [任务 9][KafkaTest3] - PDK connector node stopped: HazelcastTargetPdkDataNode-26059d21-e7ba-48db-8c58-50b8c3cc66b4 
[INFO ] 2024-04-07 02:00:58.720 - [任务 9][KafkaTest3] - PDK connector node released: HazelcastTargetPdkDataNode-26059d21-e7ba-48db-8c58-50b8c3cc66b4 
[INFO ] 2024-04-07 02:00:58.721 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] schema data cleaned 
[INFO ] 2024-04-07 02:00:58.723 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] monitor closed 
[INFO ] 2024-04-07 02:00:58.724 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] close complete, cost 25 ms 
[INFO ] 2024-04-07 02:01:03.137 - [任务 9] - Task [任务 9] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-04-07 02:01:03.138 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 02:01:03.156 - [任务 9] - Stop task milestones: 66118ac543895b65e76afaab(任务 9)  
[INFO ] 2024-04-07 02:01:03.156 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-04-07 02:01:03.182 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 02:01:03.183 - [任务 9] - Remove memory task client succeed, task: 任务 9[66118ac543895b65e76afaab] 
[INFO ] 2024-04-07 02:01:03.388 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[66118ac543895b65e76afaab] 
[INFO ] 2024-04-07 02:04:49.971 - [任务 9] - Start task milestones: 66118ac543895b65e76afaab(任务 9) 
[INFO ] 2024-04-07 02:04:49.972 - [任务 9] - Task initialization... 
[INFO ] 2024-04-07 02:04:49.972 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 02:04:49.972 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 02:04:49.972 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] start preload schema,table counts: 1 
[INFO ] 2024-04-07 02:04:49.972 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] start preload schema,table counts: 1 
[INFO ] 2024-04-07 02:04:50.005 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] preload schema finished, cost 28 ms 
[INFO ] 2024-04-07 02:04:50.007 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] preload schema finished, cost 27 ms 
[INFO ] 2024-04-07 02:04:57.779 - [任务 9][KafkaTest3] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 02:04:57.870 - [任务 9][CLAIMKAFKA] - Source node "CLAIMKAFKA" read batch size: 100 
[INFO ] 2024-04-07 02:04:57.871 - [任务 9][CLAIMKAFKA] - Source node "CLAIMKAFKA" event queue capacity: 200 
[INFO ] 2024-04-07 02:04:57.871 - [任务 9][CLAIMKAFKA] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 02:04:57.975 - [任务 9][CLAIMKAFKA] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-04-07 02:04:57.988 - [任务 9][CLAIMKAFKA] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-04-07 02:04:57.989 - [任务 9][CLAIMKAFKA] - Initial sync started 
[INFO ] 2024-04-07 02:04:57.989 - [任务 9][CLAIMKAFKA] - Starting batch read, table name: CLAIMKAFKA, offset: null 
[INFO ] 2024-04-07 02:04:58.205 - [任务 9][CLAIMKAFKA] - Table CLAIMKAFKA is going to be initial synced 
[INFO ] 2024-04-07 02:04:58.524 - [任务 9][CLAIMKAFKA] - Initial sync completed 
[ERROR] 2024-04-07 02:04:58.527 - [任务 9][CLAIMKAFKA] - java.lang.RuntimeException: java.lang.RuntimeException: op type not support <-- Error Message -->
java.lang.RuntimeException: java.lang.RuntimeException: op type not support

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: op type not support
	io.tapdata.connector.kafka.util.CustomParseUtil.applyCustomParse(CustomParseUtil.java:124)
	io.tapdata.connector.kafka.KafkaService.makeCustomMessage(KafkaService.java:719)
	io.tapdata.connector.kafka.KafkaService.consumeOne(KafkaService.java:635)
	io.tapdata.connector.kafka.KafkaConnector.batchRead(KafkaConnector.java:313)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:388)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.RuntimeException: op type not support
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:253)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:147)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:175)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: op type not support
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:308)
	... 10 more
Caused by: java.lang.RuntimeException: op type not support
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshot$13(HazelcastSourcePdkDataNode.java:316)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 12 more
Caused by: java.lang.RuntimeException: op type not support
	at io.tapdata.connector.kafka.util.CustomParseUtil.applyCustomParse(CustomParseUtil.java:124)
	at io.tapdata.connector.kafka.KafkaService.makeCustomMessage(KafkaService.java:719)
	at io.tapdata.connector.kafka.KafkaService.consumeOne(KafkaService.java:635)
	at io.tapdata.connector.kafka.KafkaConnector.batchRead(KafkaConnector.java:313)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:388)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 18 more

[INFO ] 2024-04-07 02:04:58.537 - [任务 9][CLAIMKAFKA] - Job suspend in error handle 
[INFO ] 2024-04-07 02:04:58.537 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] running status set to false 
[INFO ] 2024-04-07 02:04:58.559 - [任务 9][CLAIMKAFKA] - PDK connector node stopped: HazelcastSourcePdkDataNode-ec4918ef-9790-4862-9468-49d492e37d73 
[INFO ] 2024-04-07 02:04:58.559 - [任务 9][CLAIMKAFKA] - PDK connector node released: HazelcastSourcePdkDataNode-ec4918ef-9790-4862-9468-49d492e37d73 
[INFO ] 2024-04-07 02:04:58.559 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] schema data cleaned 
[INFO ] 2024-04-07 02:04:58.566 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] monitor closed 
[INFO ] 2024-04-07 02:04:58.566 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] close complete, cost 29 ms 
[INFO ] 2024-04-07 02:04:58.616 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] running status set to false 
[INFO ] 2024-04-07 02:04:58.617 - [任务 9][KafkaTest3] - PDK connector node stopped: HazelcastTargetPdkDataNode-26059d21-e7ba-48db-8c58-50b8c3cc66b4 
[INFO ] 2024-04-07 02:04:58.618 - [任务 9][KafkaTest3] - PDK connector node released: HazelcastTargetPdkDataNode-26059d21-e7ba-48db-8c58-50b8c3cc66b4 
[INFO ] 2024-04-07 02:04:58.618 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] schema data cleaned 
[INFO ] 2024-04-07 02:04:58.619 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] monitor closed 
[INFO ] 2024-04-07 02:04:58.827 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] close complete, cost 53 ms 
[INFO ] 2024-04-07 02:05:03.423 - [任务 9] - Task [任务 9] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-04-07 02:05:03.438 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 02:05:03.438 - [任务 9] - Stop task milestones: 66118ac543895b65e76afaab(任务 9)  
[INFO ] 2024-04-07 02:05:03.448 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-04-07 02:05:03.467 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 02:05:03.467 - [任务 9] - Remove memory task client succeed, task: 任务 9[66118ac543895b65e76afaab] 
[INFO ] 2024-04-07 02:05:03.468 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[66118ac543895b65e76afaab] 
[INFO ] 2024-04-07 02:06:02.928 - [任务 9] - Start task milestones: 66118ac543895b65e76afaab(任务 9) 
[INFO ] 2024-04-07 02:06:02.929 - [任务 9] - Task initialization... 
[INFO ] 2024-04-07 02:06:02.989 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 02:06:02.989 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 02:06:03.040 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] start preload schema,table counts: 1 
[INFO ] 2024-04-07 02:06:03.040 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] start preload schema,table counts: 1 
[INFO ] 2024-04-07 02:06:03.064 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] preload schema finished, cost 24 ms 
[INFO ] 2024-04-07 02:06:03.279 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] preload schema finished, cost 26 ms 
[INFO ] 2024-04-07 02:06:04.090 - [任务 9][KafkaTest3] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 02:06:10.832 - [任务 9][CLAIMKAFKA] - Source node "CLAIMKAFKA" read batch size: 100 
[INFO ] 2024-04-07 02:06:10.833 - [任务 9][CLAIMKAFKA] - Source node "CLAIMKAFKA" event queue capacity: 200 
[INFO ] 2024-04-07 02:06:10.833 - [任务 9][CLAIMKAFKA] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 02:06:10.924 - [任务 9][CLAIMKAFKA] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-04-07 02:06:10.928 - [任务 9][CLAIMKAFKA] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-04-07 02:06:10.928 - [任务 9][CLAIMKAFKA] - Initial sync started 
[INFO ] 2024-04-07 02:06:10.928 - [任务 9][CLAIMKAFKA] - Starting batch read, table name: CLAIMKAFKA, offset: null 
[INFO ] 2024-04-07 02:06:10.928 - [任务 9][CLAIMKAFKA] - Table CLAIMKAFKA is going to be initial synced 
[INFO ] 2024-04-07 02:07:31.816 - [任务 9][CLAIMKAFKA] - Initial sync completed 
[ERROR] 2024-04-07 02:07:31.827 - [任务 9][CLAIMKAFKA] - java.lang.RuntimeException: java.lang.RuntimeException: op type not support <-- Error Message -->
java.lang.RuntimeException: java.lang.RuntimeException: op type not support

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: op type not support
	io.tapdata.connector.kafka.util.CustomParseUtil.applyCustomParse(CustomParseUtil.java:124)
	io.tapdata.connector.kafka.KafkaService.makeCustomMessage(KafkaService.java:719)
	io.tapdata.connector.kafka.KafkaService.consumeOne(KafkaService.java:635)
	io.tapdata.connector.kafka.KafkaConnector.batchRead(KafkaConnector.java:313)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:388)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.RuntimeException: op type not support
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:253)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:147)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:175)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: op type not support
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:308)
	... 10 more
Caused by: java.lang.RuntimeException: op type not support
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshot$13(HazelcastSourcePdkDataNode.java:316)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 12 more
Caused by: java.lang.RuntimeException: op type not support
	at io.tapdata.connector.kafka.util.CustomParseUtil.applyCustomParse(CustomParseUtil.java:124)
	at io.tapdata.connector.kafka.KafkaService.makeCustomMessage(KafkaService.java:719)
	at io.tapdata.connector.kafka.KafkaService.consumeOne(KafkaService.java:635)
	at io.tapdata.connector.kafka.KafkaConnector.batchRead(KafkaConnector.java:313)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:388)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 18 more

[INFO ] 2024-04-07 02:07:31.901 - [任务 9][CLAIMKAFKA] - Job suspend in error handle 
[INFO ] 2024-04-07 02:07:31.901 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] running status set to false 
[INFO ] 2024-04-07 02:07:31.946 - [任务 9][CLAIMKAFKA] - PDK connector node stopped: HazelcastSourcePdkDataNode-ec4918ef-9790-4862-9468-49d492e37d73 
[INFO ] 2024-04-07 02:07:31.948 - [任务 9][CLAIMKAFKA] - PDK connector node released: HazelcastSourcePdkDataNode-ec4918ef-9790-4862-9468-49d492e37d73 
[INFO ] 2024-04-07 02:07:31.948 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] schema data cleaned 
[INFO ] 2024-04-07 02:07:31.948 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] monitor closed 
[INFO ] 2024-04-07 02:07:31.953 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] close complete, cost 52 ms 
[INFO ] 2024-04-07 02:07:31.953 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] running status set to false 
[INFO ] 2024-04-07 02:07:31.986 - [任务 9][KafkaTest3] - PDK connector node stopped: HazelcastTargetPdkDataNode-26059d21-e7ba-48db-8c58-50b8c3cc66b4 
[INFO ] 2024-04-07 02:07:31.986 - [任务 9][KafkaTest3] - PDK connector node released: HazelcastTargetPdkDataNode-26059d21-e7ba-48db-8c58-50b8c3cc66b4 
[INFO ] 2024-04-07 02:07:31.987 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] schema data cleaned 
[INFO ] 2024-04-07 02:07:31.988 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] monitor closed 
[INFO ] 2024-04-07 02:07:31.988 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] close complete, cost 39 ms 
[INFO ] 2024-04-07 02:07:36.695 - [任务 9] - Task [任务 9] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-04-07 02:07:36.700 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 02:07:36.733 - [任务 9] - Stop task milestones: 66118ac543895b65e76afaab(任务 9)  
[INFO ] 2024-04-07 02:07:36.733 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-04-07 02:07:36.733 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 02:07:36.778 - [任务 9] - Remove memory task client succeed, task: 任务 9[66118ac543895b65e76afaab] 
[INFO ] 2024-04-07 02:07:36.779 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[66118ac543895b65e76afaab] 
[INFO ] 2024-04-07 02:12:01.031 - [任务 9] - Start task milestones: 66118ac543895b65e76afaab(任务 9) 
[INFO ] 2024-04-07 02:12:01.032 - [任务 9] - Task initialization... 
[INFO ] 2024-04-07 02:12:01.074 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 02:12:01.261 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 02:12:01.261 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] start preload schema,table counts: 1 
[INFO ] 2024-04-07 02:12:01.261 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] start preload schema,table counts: 1 
[INFO ] 2024-04-07 02:12:01.306 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] preload schema finished, cost 36 ms 
[INFO ] 2024-04-07 02:12:01.307 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] preload schema finished, cost 36 ms 
[INFO ] 2024-04-07 02:12:02.114 - [任务 9][KafkaTest3] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 02:12:09.839 - [任务 9][CLAIMKAFKA] - Source node "CLAIMKAFKA" read batch size: 100 
[INFO ] 2024-04-07 02:12:09.848 - [任务 9][CLAIMKAFKA] - Source node "CLAIMKAFKA" event queue capacity: 200 
[INFO ] 2024-04-07 02:12:09.848 - [任务 9][CLAIMKAFKA] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 02:12:09.848 - [任务 9][CLAIMKAFKA] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-04-07 02:12:10.008 - [任务 9][CLAIMKAFKA] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-04-07 02:12:10.009 - [任务 9][CLAIMKAFKA] - Initial sync started 
[INFO ] 2024-04-07 02:12:10.022 - [任务 9][CLAIMKAFKA] - Starting batch read, table name: CLAIMKAFKA, offset: null 
[INFO ] 2024-04-07 02:12:10.023 - [任务 9][CLAIMKAFKA] - Table CLAIMKAFKA is going to be initial synced 
[INFO ] 2024-04-07 02:15:26.984 - [任务 9][CLAIMKAFKA] - Initial sync completed 
[ERROR] 2024-04-07 02:15:26.985 - [任务 9][CLAIMKAFKA] - java.lang.RuntimeException: io.tapdata.exception.NodeException: Invalid TapEvent, `TapEvent.time` should be NonNUll <-- Error Message -->
java.lang.RuntimeException: io.tapdata.exception.NodeException: Invalid TapEvent, `TapEvent.time` should be NonNUll

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.NodeException: Invalid TapEvent, `TapEvent.time` should be NonNUll
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$6(HazelcastSourcePdkDataNode.java:330)
	java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: io.tapdata.exception.NodeException: Invalid TapEvent, `TapEvent.time` should be NonNUll
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:253)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:147)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:175)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Invalid TapEvent, `TapEvent.time` should be NonNUll
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:308)
	... 10 more
Caused by: io.tapdata.exception.NodeException: Invalid TapEvent, `TapEvent.time` should be NonNUll
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshot$13(HazelcastSourcePdkDataNode.java:316)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 12 more
Caused by: io.tapdata.exception.NodeException: Invalid TapEvent, `TapEvent.time` should be NonNUll
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$6(HazelcastSourcePdkDataNode.java:330)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$7(HazelcastSourcePdkDataNode.java:333)
	at io.tapdata.connector.kafka.KafkaService.consumeOne(KafkaService.java:640)
	at io.tapdata.connector.kafka.KafkaConnector.batchRead(KafkaConnector.java:313)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:388)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 18 more

[INFO ] 2024-04-07 02:15:26.985 - [任务 9][CLAIMKAFKA] - Job suspend in error handle 
[INFO ] 2024-04-07 02:15:27.053 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] running status set to false 
[INFO ] 2024-04-07 02:15:27.094 - [任务 9][CLAIMKAFKA] - PDK connector node stopped: HazelcastSourcePdkDataNode-ec4918ef-9790-4862-9468-49d492e37d73 
[INFO ] 2024-04-07 02:15:27.094 - [任务 9][CLAIMKAFKA] - PDK connector node released: HazelcastSourcePdkDataNode-ec4918ef-9790-4862-9468-49d492e37d73 
[INFO ] 2024-04-07 02:15:27.094 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] schema data cleaned 
[INFO ] 2024-04-07 02:15:27.099 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] monitor closed 
[INFO ] 2024-04-07 02:15:27.099 - [任务 9][CLAIMKAFKA] - Node CLAIMKAFKA[ec4918ef-9790-4862-9468-49d492e37d73] close complete, cost 51 ms 
[INFO ] 2024-04-07 02:15:27.149 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] running status set to false 
[INFO ] 2024-04-07 02:15:27.149 - [任务 9][KafkaTest3] - PDK connector node stopped: HazelcastTargetPdkDataNode-26059d21-e7ba-48db-8c58-50b8c3cc66b4 
[INFO ] 2024-04-07 02:15:27.149 - [任务 9][KafkaTest3] - PDK connector node released: HazelcastTargetPdkDataNode-26059d21-e7ba-48db-8c58-50b8c3cc66b4 
[INFO ] 2024-04-07 02:15:27.150 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] schema data cleaned 
[INFO ] 2024-04-07 02:15:27.150 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] monitor closed 
[INFO ] 2024-04-07 02:15:27.362 - [任务 9][KafkaTest3] - Node KafkaTest3[26059d21-e7ba-48db-8c58-50b8c3cc66b4] close complete, cost 51 ms 
[INFO ] 2024-04-07 02:15:30.842 - [任务 9] - Task [任务 9] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-04-07 02:15:30.844 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 02:15:30.869 - [任务 9] - Stop task milestones: 66118ac543895b65e76afaab(任务 9)  
[INFO ] 2024-04-07 02:15:30.870 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-04-07 02:15:30.870 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 02:15:30.887 - [任务 9] - Remove memory task client succeed, task: 任务 9[66118ac543895b65e76afaab] 
[INFO ] 2024-04-07 02:15:30.888 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[66118ac543895b65e76afaab] 
