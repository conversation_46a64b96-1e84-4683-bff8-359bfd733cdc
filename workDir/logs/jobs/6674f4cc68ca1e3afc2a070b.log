[INFO ] 2024-06-21 11:52:22.943 - [任务 5(100)][4932d291-bff6-49c6-b539-dfbe75404b55] - Node 4932d291-bff6-49c6-b539-dfbe75404b55[4932d291-bff6-49c6-b539-dfbe75404b55] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:52:22.945 - [任务 5(100)][3795da56-77fd-4b61-ae78-3245339b6964] - Node 3795da56-77fd-4b61-ae78-3245339b6964[3795da56-77fd-4b61-ae78-3245339b6964] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:52:22.945 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:52:22.953 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:52:22.955 - [任务 5(100)][4932d291-bff6-49c6-b539-dfbe75404b55] - Node 4932d291-bff6-49c6-b539-dfbe75404b55[4932d291-bff6-49c6-b539-dfbe75404b55] preload schema finished, cost 1 ms 
[INFO ] 2024-06-21 11:52:22.957 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:52:22.957 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:52:22.957 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:52:22.957 - [任务 5(100)][3795da56-77fd-4b61-ae78-3245339b6964] - Node 3795da56-77fd-4b61-ae78-3245339b6964[3795da56-77fd-4b61-ae78-3245339b6964] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:52:22.957 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 3 ms 
[INFO ] 2024-06-21 11:52:22.957 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:52:22.957 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:52:23.545 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:52:23.545 - [任务 5(100)][5ddca7ed-20b0-43ca-8657-eff5f7d4615e] - Node 5ddca7ed-20b0-43ca-8657-eff5f7d4615e[5ddca7ed-20b0-43ca-8657-eff5f7d4615e] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:52:23.545 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:52:23.545 - [任务 5(100)][5ddca7ed-20b0-43ca-8657-eff5f7d4615e] - Node 5ddca7ed-20b0-43ca-8657-eff5f7d4615e[5ddca7ed-20b0-43ca-8657-eff5f7d4615e] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:52:23.545 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:52:23.545 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:52:23.819 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:52:23.823 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:52:23.824 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:52:23.824 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:52:23.824 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:52:23.825 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 32 ms 
[INFO ] 2024-06-21 11:52:23.993 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:52:23.994 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:52:23.994 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:52:23.994 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:52:23.994 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:52:23.995 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 20 ms 
[INFO ] 2024-06-21 11:52:24.217 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:52:24.224 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:52:24.253 - [任务 5(100)][3795da56-77fd-4b61-ae78-3245339b6964] - Node 3795da56-77fd-4b61-ae78-3245339b6964[3795da56-77fd-4b61-ae78-3245339b6964] running status set to false 
[INFO ] 2024-06-21 11:52:24.262 - [任务 5(100)][3795da56-77fd-4b61-ae78-3245339b6964] - Node 3795da56-77fd-4b61-ae78-3245339b6964[3795da56-77fd-4b61-ae78-3245339b6964] schema data cleaned 
[INFO ] 2024-06-21 11:52:24.263 - [任务 5(100)][3795da56-77fd-4b61-ae78-3245339b6964] - Node 3795da56-77fd-4b61-ae78-3245339b6964[3795da56-77fd-4b61-ae78-3245339b6964] monitor closed 
[INFO ] 2024-06-21 11:52:24.263 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:52:24.263 - [任务 5(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-9e00aff6-a8b0-4beb-be9a-7689a90ec04f 
[INFO ] 2024-06-21 11:52:24.263 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:52:24.263 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:52:24.266 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:52:24.266 - [任务 5(100)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-9e00aff6-a8b0-4beb-be9a-7689a90ec04f 
[INFO ] 2024-06-21 11:52:24.266 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 71 ms 
[INFO ] 2024-06-21 11:52:24.266 - [任务 5(100)][3795da56-77fd-4b61-ae78-3245339b6964] - Node 3795da56-77fd-4b61-ae78-3245339b6964[3795da56-77fd-4b61-ae78-3245339b6964] close complete, cost 53 ms 
[INFO ] 2024-06-21 11:52:24.266 - [任务 5(100)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070b-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:52:24.281 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:52:24.281 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:52:24.282 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 76 ms 
[INFO ] 2024-06-21 11:52:27.671 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:52:27.671 - [任务 5(100)][4932d291-bff6-49c6-b539-dfbe75404b55] - Node 4932d291-bff6-49c6-b539-dfbe75404b55[4932d291-bff6-49c6-b539-dfbe75404b55] running status set to false 
[INFO ] 2024-06-21 11:52:27.673 - [任务 5(100)][4932d291-bff6-49c6-b539-dfbe75404b55] - Node 4932d291-bff6-49c6-b539-dfbe75404b55[4932d291-bff6-49c6-b539-dfbe75404b55] schema data cleaned 
[INFO ] 2024-06-21 11:52:27.673 - [任务 5(100)][4932d291-bff6-49c6-b539-dfbe75404b55] - Node 4932d291-bff6-49c6-b539-dfbe75404b55[4932d291-bff6-49c6-b539-dfbe75404b55] monitor closed 
[INFO ] 2024-06-21 11:52:27.673 - [任务 5(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-1ca75770-3af4-42c7-8956-ffa4aac3f9f3 
[INFO ] 2024-06-21 11:52:27.673 - [任务 5(100)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-1ca75770-3af4-42c7-8956-ffa4aac3f9f3 
[INFO ] 2024-06-21 11:52:27.673 - [任务 5(100)][4932d291-bff6-49c6-b539-dfbe75404b55] - Node 4932d291-bff6-49c6-b539-dfbe75404b55[4932d291-bff6-49c6-b539-dfbe75404b55] close complete, cost 20 ms 
[INFO ] 2024-06-21 11:52:27.673 - [任务 5(100)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070b-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:52:27.675 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:52:27.675 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:52:27.676 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 27 ms 
[INFO ] 2024-06-21 11:52:27.830 - [任务 5(100)][5ddca7ed-20b0-43ca-8657-eff5f7d4615e] - Node 5ddca7ed-20b0-43ca-8657-eff5f7d4615e[5ddca7ed-20b0-43ca-8657-eff5f7d4615e] running status set to false 
[INFO ] 2024-06-21 11:52:27.845 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:52:27.846 - [任务 5(100)][5ddca7ed-20b0-43ca-8657-eff5f7d4615e] - Node 5ddca7ed-20b0-43ca-8657-eff5f7d4615e[5ddca7ed-20b0-43ca-8657-eff5f7d4615e] schema data cleaned 
[INFO ] 2024-06-21 11:52:27.846 - [任务 5(100)][5ddca7ed-20b0-43ca-8657-eff5f7d4615e] - Node 5ddca7ed-20b0-43ca-8657-eff5f7d4615e[5ddca7ed-20b0-43ca-8657-eff5f7d4615e] monitor closed 
[INFO ] 2024-06-21 11:52:27.846 - [任务 5(100)][5ddca7ed-20b0-43ca-8657-eff5f7d4615e] - Node 5ddca7ed-20b0-43ca-8657-eff5f7d4615e[5ddca7ed-20b0-43ca-8657-eff5f7d4615e] close complete, cost 9 ms 
[INFO ] 2024-06-21 11:52:27.847 - [任务 5(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-17fa8b4e-75af-42a3-86df-f9edb2facc72 
[INFO ] 2024-06-21 11:52:27.847 - [任务 5(100)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-17fa8b4e-75af-42a3-86df-f9edb2facc72 
[INFO ] 2024-06-21 11:52:27.849 - [任务 5(100)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070b-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:52:27.849 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:52:27.849 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:52:27.850 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 37 ms 
[INFO ] 2024-06-21 11:52:33.757 - [任务 5(100)][df6d59f5-47ed-4843-bc56-166b3838c6ee] - Node df6d59f5-47ed-4843-bc56-166b3838c6ee[df6d59f5-47ed-4843-bc56-166b3838c6ee] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:52:33.759 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:52:33.759 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:52:33.760 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:52:33.760 - [任务 5(100)][df6d59f5-47ed-4843-bc56-166b3838c6ee] - Node df6d59f5-47ed-4843-bc56-166b3838c6ee[df6d59f5-47ed-4843-bc56-166b3838c6ee] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:52:33.760 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:52:34.045 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:52:34.045 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:52:34.046 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:52:34.046 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:52:34.046 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:52:34.048 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 35 ms 
[INFO ] 2024-06-21 11:52:34.205 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:52:34.205 - [任务 5(100)][df6d59f5-47ed-4843-bc56-166b3838c6ee] - Node df6d59f5-47ed-4843-bc56-166b3838c6ee[df6d59f5-47ed-4843-bc56-166b3838c6ee] running status set to false 
[INFO ] 2024-06-21 11:52:34.205 - [任务 5(100)][df6d59f5-47ed-4843-bc56-166b3838c6ee] - Node df6d59f5-47ed-4843-bc56-166b3838c6ee[df6d59f5-47ed-4843-bc56-166b3838c6ee] schema data cleaned 
[INFO ] 2024-06-21 11:52:34.205 - [任务 5(100)][df6d59f5-47ed-4843-bc56-166b3838c6ee] - Node df6d59f5-47ed-4843-bc56-166b3838c6ee[df6d59f5-47ed-4843-bc56-166b3838c6ee] monitor closed 
[INFO ] 2024-06-21 11:52:34.219 - [任务 5(100)][df6d59f5-47ed-4843-bc56-166b3838c6ee] - Node df6d59f5-47ed-4843-bc56-166b3838c6ee[df6d59f5-47ed-4843-bc56-166b3838c6ee] close complete, cost 3 ms 
[INFO ] 2024-06-21 11:52:34.225 - [任务 5(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-b6472705-8a25-42dd-92ce-65c15f219988 
[INFO ] 2024-06-21 11:52:34.226 - [任务 5(100)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-b6472705-8a25-42dd-92ce-65c15f219988 
[INFO ] 2024-06-21 11:52:34.226 - [任务 5(100)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070b-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:52:34.228 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:52:34.228 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:52:34.232 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 28 ms 
[INFO ] 2024-06-21 11:53:55.531 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:53:55.537 - [任务 5(100)][efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8] - Node efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8[efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:53:55.537 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:53:55.537 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 2 ms 
[INFO ] 2024-06-21 11:53:55.537 - [任务 5(100)][efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8] - Node efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8[efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:53:55.540 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 1 ms 
[INFO ] 2024-06-21 11:53:55.792 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:53:55.794 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:53:55.794 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:53:55.795 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:53:55.795 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:53:55.984 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 21 ms 
[INFO ] 2024-06-21 11:53:55.986 - [任务 5(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: reocrd is not defined 
[ERROR] 2024-06-21 11:53:56.188 - [任务 5(100)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: reocrd is not defined <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: reocrd is not defined

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: reocrd is not defined
	<js>.process(<eval>:2)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$0(HazelcastJavaScriptProcessorNode.java:249)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: reocrd is not defined
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:262)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: reocrd is not defined
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$0(HazelcastJavaScriptProcessorNode.java:249)
	... 1 more
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: reocrd is not defined
	at <js>.process(<eval>:2)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 2 more

[INFO ] 2024-06-21 11:53:58.527 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:53:58.553 - [任务 5(100)][efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8] - Node efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8[efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8] running status set to false 
[INFO ] 2024-06-21 11:53:58.554 - [任务 5(100)][efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8] - Node efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8[efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8] schema data cleaned 
[INFO ] 2024-06-21 11:53:58.554 - [任务 5(100)][efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8] - Node efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8[efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8] monitor closed 
[INFO ] 2024-06-21 11:53:58.557 - [任务 5(100)][efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8] - Node efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8[efc7c9cc-7af0-4b8a-8257-545ddbc9f8a8] close complete, cost 26 ms 
[INFO ] 2024-06-21 11:53:58.557 - [任务 5(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-6d9f6ca4-faa4-4c1b-999a-fbb900aa9e8c 
[INFO ] 2024-06-21 11:53:58.561 - [任务 5(100)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-6d9f6ca4-faa4-4c1b-999a-fbb900aa9e8c 
[INFO ] 2024-06-21 11:53:58.561 - [任务 5(100)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070b-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:53:58.561 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:53:58.562 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:53:58.562 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 40 ms 
[INFO ] 2024-06-21 11:54:22.170 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:54:22.170 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:54:22.175 - [任务 5(100)][e41b2ab4-d933-4582-a442-f4336f86f728] - Node e41b2ab4-d933-4582-a442-f4336f86f728[e41b2ab4-d933-4582-a442-f4336f86f728] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:54:22.176 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 1 ms 
[INFO ] 2024-06-21 11:54:22.176 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:54:22.226 - [任务 5(100)][e41b2ab4-d933-4582-a442-f4336f86f728] - Node e41b2ab4-d933-4582-a442-f4336f86f728[e41b2ab4-d933-4582-a442-f4336f86f728] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:54:22.226 - [任务 5(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@3eed4e8b, function process(record){
  reocrd["created_at"]=
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null 
[ERROR] 2024-06-21 11:54:22.387 - [任务 5(100)][增强JS] - script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@3eed4e8b, function process(record){
  reocrd["created_at"]=
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null <-- Error Message -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@3eed4e8b, function process(record){
  reocrd["created_at"]=
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:4:1 Expected an operand but found return
	return record;
	^

	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	...

<-- Full Stack Trace -->
script eval error: graal.js, com.oracle.truffle.js.scriptengine.GraalJSScriptEngine@3eed4e8b, function process(record){
  reocrd["created_at"]=
	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var Date = Java.type("java.util.Date");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
, null
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:138)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:4:1 Expected an operand but found return
	return record;
	^

	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:183)
	... 14 more
Caused by: org.graalvm.polyglot.PolyglotException: SyntaxError: <eval>:4:1 Expected an operand but found return
	return record;
	^

	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 17 more

[INFO ] 2024-06-21 11:54:22.387 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:54:22.404 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:54:22.404 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:54:22.405 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:54:22.405 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:54:22.611 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 18 ms 
[INFO ] 2024-06-21 11:54:24.769 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:54:24.771 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:54:24.771 - [任务 5(100)][e41b2ab4-d933-4582-a442-f4336f86f728] - Node e41b2ab4-d933-4582-a442-f4336f86f728[e41b2ab4-d933-4582-a442-f4336f86f728] running status set to false 
[INFO ] 2024-06-21 11:54:24.771 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:54:24.771 - [任务 5(100)][e41b2ab4-d933-4582-a442-f4336f86f728] - Node e41b2ab4-d933-4582-a442-f4336f86f728[e41b2ab4-d933-4582-a442-f4336f86f728] schema data cleaned 
[INFO ] 2024-06-21 11:54:24.773 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 3 ms 
[INFO ] 2024-06-21 11:54:24.774 - [任务 5(100)][e41b2ab4-d933-4582-a442-f4336f86f728] - Node e41b2ab4-d933-4582-a442-f4336f86f728[e41b2ab4-d933-4582-a442-f4336f86f728] monitor closed 
[INFO ] 2024-06-21 11:54:24.775 - [任务 5(100)][e41b2ab4-d933-4582-a442-f4336f86f728] - Node e41b2ab4-d933-4582-a442-f4336f86f728[e41b2ab4-d933-4582-a442-f4336f86f728] close complete, cost 3 ms 
[INFO ] 2024-06-21 11:54:42.481 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:54:42.481 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:54:42.482 - [任务 5(100)][4333b022-53e5-480f-9d7b-03000611a5b6] - Node 4333b022-53e5-480f-9d7b-03000611a5b6[4333b022-53e5-480f-9d7b-03000611a5b6] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:54:42.482 - [任务 5(100)][4333b022-53e5-480f-9d7b-03000611a5b6] - Node 4333b022-53e5-480f-9d7b-03000611a5b6[4333b022-53e5-480f-9d7b-03000611a5b6] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:54:42.482 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:54:42.482 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:54:42.735 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:54:42.735 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:54:42.736 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:54:42.736 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:54:42.737 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:54:42.737 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 19 ms 
[INFO ] 2024-06-21 11:54:42.915 - [任务 5(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: reocrd is not defined 
[ERROR] 2024-06-21 11:54:42.916 - [任务 5(100)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: reocrd is not defined <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: reocrd is not defined

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: reocrd is not defined
	<js>.process(<eval>:2)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$0(HazelcastJavaScriptProcessorNode.java:249)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: reocrd is not defined
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:262)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$tryProcess$5(HazelcastProcessorBaseNode.java:238)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:190)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: reocrd is not defined
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$0(HazelcastJavaScriptProcessorNode.java:249)
	... 1 more
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: reocrd is not defined
	at <js>.process(<eval>:2)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 2 more

[INFO ] 2024-06-21 11:54:45.458 - [任务 5(100)][4333b022-53e5-480f-9d7b-03000611a5b6] - Node 4333b022-53e5-480f-9d7b-03000611a5b6[4333b022-53e5-480f-9d7b-03000611a5b6] running status set to false 
[INFO ] 2024-06-21 11:54:45.474 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:54:45.484 - [任务 5(100)][4333b022-53e5-480f-9d7b-03000611a5b6] - Node 4333b022-53e5-480f-9d7b-03000611a5b6[4333b022-53e5-480f-9d7b-03000611a5b6] schema data cleaned 
[INFO ] 2024-06-21 11:54:45.491 - [任务 5(100)][4333b022-53e5-480f-9d7b-03000611a5b6] - Node 4333b022-53e5-480f-9d7b-03000611a5b6[4333b022-53e5-480f-9d7b-03000611a5b6] monitor closed 
[INFO ] 2024-06-21 11:54:45.493 - [任务 5(100)][4333b022-53e5-480f-9d7b-03000611a5b6] - Node 4333b022-53e5-480f-9d7b-03000611a5b6[4333b022-53e5-480f-9d7b-03000611a5b6] close complete, cost 20 ms 
[INFO ] 2024-06-21 11:54:45.501 - [任务 5(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-11d06042-989f-4bff-b7b8-7392c58b9873 
[INFO ] 2024-06-21 11:54:45.502 - [任务 5(100)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-11d06042-989f-4bff-b7b8-7392c58b9873 
[INFO ] 2024-06-21 11:54:45.502 - [任务 5(100)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070b-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:54:45.507 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:54:45.507 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:54:45.509 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 54 ms 
[INFO ] 2024-06-21 11:55:01.511 - [任务 5(100)][4f478b26-33c2-45d1-92da-39db6b07e7ee] - Node 4f478b26-33c2-45d1-92da-39db6b07e7ee[4f478b26-33c2-45d1-92da-39db6b07e7ee] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:55:01.515 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:55:01.515 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:55:01.515 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:55:01.516 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:55:01.516 - [任务 5(100)][4f478b26-33c2-45d1-92da-39db6b07e7ee] - Node 4f478b26-33c2-45d1-92da-39db6b07e7ee[4f478b26-33c2-45d1-92da-39db6b07e7ee] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:55:01.765 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:55:01.784 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:55:01.784 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:55:01.785 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:55:01.785 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:55:01.992 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 21 ms 
[INFO ] 2024-06-21 11:55:02.019 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:55:02.020 - [任务 5(100)][4f478b26-33c2-45d1-92da-39db6b07e7ee] - Node 4f478b26-33c2-45d1-92da-39db6b07e7ee[4f478b26-33c2-45d1-92da-39db6b07e7ee] running status set to false 
[INFO ] 2024-06-21 11:55:02.021 - [任务 5(100)][4f478b26-33c2-45d1-92da-39db6b07e7ee] - Node 4f478b26-33c2-45d1-92da-39db6b07e7ee[4f478b26-33c2-45d1-92da-39db6b07e7ee] schema data cleaned 
[INFO ] 2024-06-21 11:55:02.046 - [任务 5(100)][4f478b26-33c2-45d1-92da-39db6b07e7ee] - Node 4f478b26-33c2-45d1-92da-39db6b07e7ee[4f478b26-33c2-45d1-92da-39db6b07e7ee] monitor closed 
[INFO ] 2024-06-21 11:55:02.062 - [任务 5(100)][4f478b26-33c2-45d1-92da-39db6b07e7ee] - Node 4f478b26-33c2-45d1-92da-39db6b07e7ee[4f478b26-33c2-45d1-92da-39db6b07e7ee] close complete, cost 28 ms 
[INFO ] 2024-06-21 11:55:02.063 - [任务 5(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-42e74b68-a9fd-4852-b538-854243e6db93 
[INFO ] 2024-06-21 11:55:02.063 - [任务 5(100)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-42e74b68-a9fd-4852-b538-854243e6db93 
[INFO ] 2024-06-21 11:55:02.063 - [任务 5(100)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070b-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:55:02.065 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:55:02.065 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:55:02.068 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 57 ms 
[INFO ] 2024-06-21 11:55:23.447 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:55:23.447 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:55:23.448 - [任务 5(100)][83503d9d-2c9c-4daf-86c7-e703fa60a544] - Node 83503d9d-2c9c-4daf-86c7-e703fa60a544[83503d9d-2c9c-4daf-86c7-e703fa60a544] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:55:23.448 - [任务 5(100)][83503d9d-2c9c-4daf-86c7-e703fa60a544] - Node 83503d9d-2c9c-4daf-86c7-e703fa60a544[83503d9d-2c9c-4daf-86c7-e703fa60a544] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:55:23.448 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:55:23.448 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:55:23.653 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:55:23.669 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:55:23.669 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:55:23.669 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:55:23.670 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:55:23.670 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 18 ms 
[INFO ] 2024-06-21 11:55:23.839 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:55:23.840 - [任务 5(100)][83503d9d-2c9c-4daf-86c7-e703fa60a544] - Node 83503d9d-2c9c-4daf-86c7-e703fa60a544[83503d9d-2c9c-4daf-86c7-e703fa60a544] running status set to false 
[INFO ] 2024-06-21 11:55:23.840 - [任务 5(100)][83503d9d-2c9c-4daf-86c7-e703fa60a544] - Node 83503d9d-2c9c-4daf-86c7-e703fa60a544[83503d9d-2c9c-4daf-86c7-e703fa60a544] schema data cleaned 
[INFO ] 2024-06-21 11:55:23.840 - [任务 5(100)][83503d9d-2c9c-4daf-86c7-e703fa60a544] - Node 83503d9d-2c9c-4daf-86c7-e703fa60a544[83503d9d-2c9c-4daf-86c7-e703fa60a544] monitor closed 
[INFO ] 2024-06-21 11:55:23.840 - [任务 5(100)][83503d9d-2c9c-4daf-86c7-e703fa60a544] - Node 83503d9d-2c9c-4daf-86c7-e703fa60a544[83503d9d-2c9c-4daf-86c7-e703fa60a544] close complete, cost 1 ms 
[INFO ] 2024-06-21 11:55:23.858 - [任务 5(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-c3a4d8d7-d4bd-4b62-b108-fb96fe550d3c 
[INFO ] 2024-06-21 11:55:23.859 - [任务 5(100)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-c3a4d8d7-d4bd-4b62-b108-fb96fe550d3c 
[INFO ] 2024-06-21 11:55:23.859 - [任务 5(100)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070b-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:55:23.861 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:55:23.862 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:55:23.863 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 26 ms 
[INFO ] 2024-06-21 11:55:53.957 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:55:53.957 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:55:53.957 - [任务 5(100)][29c6eb39-afa0-4727-a617-5ba0f97fd80d] - Node 29c6eb39-afa0-4727-a617-5ba0f97fd80d[29c6eb39-afa0-4727-a617-5ba0f97fd80d] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:55:53.958 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:55:53.958 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:55:53.958 - [任务 5(100)][29c6eb39-afa0-4727-a617-5ba0f97fd80d] - Node 29c6eb39-afa0-4727-a617-5ba0f97fd80d[29c6eb39-afa0-4727-a617-5ba0f97fd80d] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:55:54.235 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:55:54.236 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:55:54.236 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:55:54.236 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:55:54.236 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:55:54.440 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 28 ms 
[INFO ] 2024-06-21 11:55:54.639 - [任务 5(100)][增强JS] - 1718883457000 
[INFO ] 2024-06-21 11:55:54.640 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:55:54.653 - [任务 5(100)][29c6eb39-afa0-4727-a617-5ba0f97fd80d] - Node 29c6eb39-afa0-4727-a617-5ba0f97fd80d[29c6eb39-afa0-4727-a617-5ba0f97fd80d] running status set to false 
[INFO ] 2024-06-21 11:55:54.666 - [任务 5(100)][29c6eb39-afa0-4727-a617-5ba0f97fd80d] - Node 29c6eb39-afa0-4727-a617-5ba0f97fd80d[29c6eb39-afa0-4727-a617-5ba0f97fd80d] schema data cleaned 
[INFO ] 2024-06-21 11:55:54.666 - [任务 5(100)][29c6eb39-afa0-4727-a617-5ba0f97fd80d] - Node 29c6eb39-afa0-4727-a617-5ba0f97fd80d[29c6eb39-afa0-4727-a617-5ba0f97fd80d] monitor closed 
[INFO ] 2024-06-21 11:55:54.666 - [任务 5(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-f27a562f-4e6f-4238-b784-6fa4ddb1a27d 
[INFO ] 2024-06-21 11:55:54.666 - [任务 5(100)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-f27a562f-4e6f-4238-b784-6fa4ddb1a27d 
[INFO ] 2024-06-21 11:55:54.667 - [任务 5(100)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070b-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:55:54.667 - [任务 5(100)][29c6eb39-afa0-4727-a617-5ba0f97fd80d] - Node 29c6eb39-afa0-4727-a617-5ba0f97fd80d[29c6eb39-afa0-4727-a617-5ba0f97fd80d] close complete, cost 25 ms 
[INFO ] 2024-06-21 11:55:54.669 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:55:54.669 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:55:54.672 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 31 ms 
[INFO ] 2024-06-21 11:56:09.788 - [任务 5(100)][2a71718a-9061-4632-a415-c80823b1934c] - Node 2a71718a-9061-4632-a415-c80823b1934c[2a71718a-9061-4632-a415-c80823b1934c] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:56:09.788 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:56:09.788 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:56:09.788 - [任务 5(100)][2a71718a-9061-4632-a415-c80823b1934c] - Node 2a71718a-9061-4632-a415-c80823b1934c[2a71718a-9061-4632-a415-c80823b1934c] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:56:09.789 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:56:09.789 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:56:10.095 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:56:10.107 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:56:10.108 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:56:10.108 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:56:10.110 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:56:10.110 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 29 ms 
[INFO ] 2024-06-21 11:56:10.421 - [任务 5(100)][增强JS] - 1718883457000 
[INFO ] 2024-06-21 11:56:10.424 - [任务 5(100)][增强JS] - 1.718883457E9 
[INFO ] 2024-06-21 11:56:10.424 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:56:10.437 - [任务 5(100)][2a71718a-9061-4632-a415-c80823b1934c] - Node 2a71718a-9061-4632-a415-c80823b1934c[2a71718a-9061-4632-a415-c80823b1934c] running status set to false 
[INFO ] 2024-06-21 11:56:10.438 - [任务 5(100)][2a71718a-9061-4632-a415-c80823b1934c] - Node 2a71718a-9061-4632-a415-c80823b1934c[2a71718a-9061-4632-a415-c80823b1934c] schema data cleaned 
[INFO ] 2024-06-21 11:56:10.438 - [任务 5(100)][2a71718a-9061-4632-a415-c80823b1934c] - Node 2a71718a-9061-4632-a415-c80823b1934c[2a71718a-9061-4632-a415-c80823b1934c] monitor closed 
[INFO ] 2024-06-21 11:56:10.438 - [任务 5(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-59fcb525-fc20-4da1-9aaf-b0133553a42e 
[INFO ] 2024-06-21 11:56:10.438 - [任务 5(100)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-59fcb525-fc20-4da1-9aaf-b0133553a42e 
[INFO ] 2024-06-21 11:56:10.438 - [任务 5(100)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070b-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:56:10.438 - [任务 5(100)][2a71718a-9061-4632-a415-c80823b1934c] - Node 2a71718a-9061-4632-a415-c80823b1934c[2a71718a-9061-4632-a415-c80823b1934c] close complete, cost 2 ms 
[INFO ] 2024-06-21 11:56:10.442 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:56:10.442 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:56:10.444 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 17 ms 
[INFO ] 2024-06-21 11:56:42.237 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:56:42.238 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:56:42.239 - [任务 5(100)][0b929808-612a-46f6-ad8e-192f94e65bb7] - Node 0b929808-612a-46f6-ad8e-192f94e65bb7[0b929808-612a-46f6-ad8e-192f94e65bb7] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:56:42.239 - [任务 5(100)][0b929808-612a-46f6-ad8e-192f94e65bb7] - Node 0b929808-612a-46f6-ad8e-192f94e65bb7[0b929808-612a-46f6-ad8e-192f94e65bb7] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:56:42.239 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:56:42.239 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:56:42.290 - [任务 5(100)][增强JS] - Init standardized JS engine 
[INFO ] 2024-06-21 11:56:42.462 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:56:42.479 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:56:42.480 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:56:42.486 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:56:42.486 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:56:42.692 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 25 ms 
[INFO ] 2024-06-21 11:56:42.739 - [任务 5(100)][增强JS] - 1718883457000 
[INFO ] 2024-06-21 11:56:42.739 - [任务 5(100)][增强JS] - 1.718883457E9 
[INFO ] 2024-06-21 11:56:42.747 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:56:42.748 - [任务 5(100)][0b929808-612a-46f6-ad8e-192f94e65bb7] - Node 0b929808-612a-46f6-ad8e-192f94e65bb7[0b929808-612a-46f6-ad8e-192f94e65bb7] running status set to false 
[INFO ] 2024-06-21 11:56:42.748 - [任务 5(100)][0b929808-612a-46f6-ad8e-192f94e65bb7] - Node 0b929808-612a-46f6-ad8e-192f94e65bb7[0b929808-612a-46f6-ad8e-192f94e65bb7] schema data cleaned 
[INFO ] 2024-06-21 11:56:42.761 - [任务 5(100)][0b929808-612a-46f6-ad8e-192f94e65bb7] - Node 0b929808-612a-46f6-ad8e-192f94e65bb7[0b929808-612a-46f6-ad8e-192f94e65bb7] monitor closed 
[INFO ] 2024-06-21 11:56:42.764 - [任务 5(100)][0b929808-612a-46f6-ad8e-192f94e65bb7] - Node 0b929808-612a-46f6-ad8e-192f94e65bb7[0b929808-612a-46f6-ad8e-192f94e65bb7] close complete, cost 3 ms 
[INFO ] 2024-06-21 11:56:42.773 - [任务 5(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-55b0ded5-d39d-4e69-9661-db819b8298d7 
[INFO ] 2024-06-21 11:56:42.773 - [任务 5(100)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-55b0ded5-d39d-4e69-9661-db819b8298d7 
[INFO ] 2024-06-21 11:56:42.773 - [任务 5(100)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070b-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:56:42.774 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:56:42.774 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:56:42.776 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 28 ms 
[INFO ] 2024-06-21 11:56:56.898 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:56:56.899 - [任务 5(100)][6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a] - Node 6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a[6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:56:56.899 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:56:56.899 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:56:56.899 - [任务 5(100)][6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a] - Node 6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a[6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:56:56.899 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:56:56.993 - [任务 5(100)][增强JS] - Init standardized JS engine 
[INFO ] 2024-06-21 11:56:57.219 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:56:57.236 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:56:57.236 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:56:57.236 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:56:57.236 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:56:57.438 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 23 ms 
[INFO ] 2024-06-21 11:56:57.543 - [任务 5(100)][增强JS] - 1718883457000 
[INFO ] 2024-06-21 11:56:57.543 - [任务 5(100)][增强JS] - 1.718883457E9 
[INFO ] 2024-06-21 11:56:57.546 - [任务 5(100)][增强JS] - 1.718883457E9 
[INFO ] 2024-06-21 11:56:57.546 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:56:57.546 - [任务 5(100)][6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a] - Node 6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a[6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a] running status set to false 
[INFO ] 2024-06-21 11:56:57.546 - [任务 5(100)][6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a] - Node 6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a[6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a] schema data cleaned 
[INFO ] 2024-06-21 11:56:57.546 - [任务 5(100)][6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a] - Node 6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a[6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a] monitor closed 
[INFO ] 2024-06-21 11:56:57.561 - [任务 5(100)][6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a] - Node 6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a[6651ab31-4f8d-49d6-ab9c-f5c7c922ac9a] close complete, cost 0 ms 
[INFO ] 2024-06-21 11:56:57.567 - [任务 5(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-54855e4b-fb0d-405f-bb27-76d96732a126 
[INFO ] 2024-06-21 11:56:57.567 - [任务 5(100)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-54855e4b-fb0d-405f-bb27-76d96732a126 
[INFO ] 2024-06-21 11:56:57.568 - [任务 5(100)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070b-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:56:57.568 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:56:57.568 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:56:57.569 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 22 ms 
[INFO ] 2024-06-21 11:58:00.527 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:58:00.527 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:58:00.527 - [任务 5(100)][adfb28c1-f3a7-4fa2-b5ae-a072fbe98577] - Node adfb28c1-f3a7-4fa2-b5ae-a072fbe98577[adfb28c1-f3a7-4fa2-b5ae-a072fbe98577] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:58:00.527 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 1 ms 
[INFO ] 2024-06-21 11:58:00.527 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:58:00.527 - [任务 5(100)][adfb28c1-f3a7-4fa2-b5ae-a072fbe98577] - Node adfb28c1-f3a7-4fa2-b5ae-a072fbe98577[adfb28c1-f3a7-4fa2-b5ae-a072fbe98577] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:58:00.622 - [任务 5(100)][增强JS] - Init standardized JS engine 
[INFO ] 2024-06-21 11:58:00.834 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:58:00.844 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:58:00.844 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:58:00.844 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:58:00.844 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:58:00.846 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 33 ms 
[INFO ] 2024-06-21 11:58:00.990 - [任务 5(100)][增强JS] - 1718883457000 
[INFO ] 2024-06-21 11:58:00.993 - [任务 5(100)][增强JS] - 1.718883457E9 
[INFO ] 2024-06-21 11:58:00.993 - [任务 5(100)][增强JS] - 1.718883457E9 
[INFO ] 2024-06-21 11:58:01.014 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:58:01.014 - [任务 5(100)][adfb28c1-f3a7-4fa2-b5ae-a072fbe98577] - Node adfb28c1-f3a7-4fa2-b5ae-a072fbe98577[adfb28c1-f3a7-4fa2-b5ae-a072fbe98577] running status set to false 
[INFO ] 2024-06-21 11:58:01.014 - [任务 5(100)][adfb28c1-f3a7-4fa2-b5ae-a072fbe98577] - Node adfb28c1-f3a7-4fa2-b5ae-a072fbe98577[adfb28c1-f3a7-4fa2-b5ae-a072fbe98577] schema data cleaned 
[INFO ] 2024-06-21 11:58:01.015 - [任务 5(100)][adfb28c1-f3a7-4fa2-b5ae-a072fbe98577] - Node adfb28c1-f3a7-4fa2-b5ae-a072fbe98577[adfb28c1-f3a7-4fa2-b5ae-a072fbe98577] monitor closed 
[INFO ] 2024-06-21 11:58:01.052 - [任务 5(100)][adfb28c1-f3a7-4fa2-b5ae-a072fbe98577] - Node adfb28c1-f3a7-4fa2-b5ae-a072fbe98577[adfb28c1-f3a7-4fa2-b5ae-a072fbe98577] close complete, cost 40 ms 
[INFO ] 2024-06-21 11:58:01.053 - [任务 5(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-dea05bd7-7f01-4105-8aee-298e9e2a5afc 
[INFO ] 2024-06-21 11:58:01.053 - [任务 5(100)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-dea05bd7-7f01-4105-8aee-298e9e2a5afc 
[INFO ] 2024-06-21 11:58:01.054 - [任务 5(100)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070b-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:58:01.054 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:58:01.054 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:58:01.055 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 58 ms 
[INFO ] 2024-06-21 11:58:47.424 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:58:47.425 - [任务 5(100)][6cf9ad76-116d-47b9-a983-a2e4d844e8e4] - Node 6cf9ad76-116d-47b9-a983-a2e4d844e8e4[6cf9ad76-116d-47b9-a983-a2e4d844e8e4] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:58:47.425 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:58:47.425 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:58:47.425 - [任务 5(100)][6cf9ad76-116d-47b9-a983-a2e4d844e8e4] - Node 6cf9ad76-116d-47b9-a983-a2e4d844e8e4[6cf9ad76-116d-47b9-a983-a2e4d844e8e4] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:58:47.425 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:58:47.442 - [任务 5(100)][增强JS] - Init standardized JS engine 
[INFO ] 2024-06-21 11:58:48.223 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:58:48.224 - [任务 5(100)][增强JS] - 1718883457000 
[INFO ] 2024-06-21 11:58:48.224 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:58:48.224 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:58:48.225 - [任务 5(100)][增强JS] - 1.718883457E9 
[INFO ] 2024-06-21 11:58:48.225 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:58:48.225 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:58:48.225 - [任务 5(100)][增强JS] - 1.718883457E9 
[INFO ] 2024-06-21 11:58:48.239 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 40 ms 
[INFO ] 2024-06-21 11:58:48.239 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:58:48.262 - [任务 5(100)][6cf9ad76-116d-47b9-a983-a2e4d844e8e4] - Node 6cf9ad76-116d-47b9-a983-a2e4d844e8e4[6cf9ad76-116d-47b9-a983-a2e4d844e8e4] running status set to false 
[INFO ] 2024-06-21 11:58:48.263 - [任务 5(100)][6cf9ad76-116d-47b9-a983-a2e4d844e8e4] - Node 6cf9ad76-116d-47b9-a983-a2e4d844e8e4[6cf9ad76-116d-47b9-a983-a2e4d844e8e4] schema data cleaned 
[INFO ] 2024-06-21 11:58:48.267 - [任务 5(100)][6cf9ad76-116d-47b9-a983-a2e4d844e8e4] - Node 6cf9ad76-116d-47b9-a983-a2e4d844e8e4[6cf9ad76-116d-47b9-a983-a2e4d844e8e4] monitor closed 
[INFO ] 2024-06-21 11:58:48.267 - [任务 5(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-c7934ddf-5d2c-406f-ab1a-61cc48e28e61 
[INFO ] 2024-06-21 11:58:48.267 - [任务 5(100)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-c7934ddf-5d2c-406f-ab1a-61cc48e28e61 
[INFO ] 2024-06-21 11:58:48.267 - [任务 5(100)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070b-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:58:48.267 - [任务 5(100)][6cf9ad76-116d-47b9-a983-a2e4d844e8e4] - Node 6cf9ad76-116d-47b9-a983-a2e4d844e8e4[6cf9ad76-116d-47b9-a983-a2e4d844e8e4] close complete, cost 27 ms 
[INFO ] 2024-06-21 11:58:48.267 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:58:48.268 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:58:48.273 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 32 ms 
[INFO ] 2024-06-21 11:59:02.253 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:59:02.253 - [任务 5(100)][e96f042e-8159-469d-b2f2-a55b380a2642] - Node e96f042e-8159-469d-b2f2-a55b380a2642[e96f042e-8159-469d-b2f2-a55b380a2642] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:59:02.254 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:59:02.261 - [任务 5(100)][e96f042e-8159-469d-b2f2-a55b380a2642] - Node e96f042e-8159-469d-b2f2-a55b380a2642[e96f042e-8159-469d-b2f2-a55b380a2642] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:59:02.262 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:59:02.262 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 3 ms 
[INFO ] 2024-06-21 11:59:02.304 - [任务 5(100)][增强JS] - Init standardized JS engine 
[INFO ] 2024-06-21 11:59:02.474 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:59:02.487 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:59:02.487 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:59:02.487 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:59:02.487 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:59:02.488 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 15 ms 
[INFO ] 2024-06-21 11:59:02.665 - [任务 5(100)][增强JS] - 1718883457000 
[INFO ] 2024-06-21 11:59:02.665 - [任务 5(100)][增强JS] - 1.718883457E9 
[INFO ] 2024-06-21 11:59:02.665 - [任务 5(100)][增强JS] - 1.718883457E9 
[INFO ] 2024-06-21 11:59:02.671 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:59:02.672 - [任务 5(100)][e96f042e-8159-469d-b2f2-a55b380a2642] - Node e96f042e-8159-469d-b2f2-a55b380a2642[e96f042e-8159-469d-b2f2-a55b380a2642] running status set to false 
[INFO ] 2024-06-21 11:59:02.672 - [任务 5(100)][e96f042e-8159-469d-b2f2-a55b380a2642] - Node e96f042e-8159-469d-b2f2-a55b380a2642[e96f042e-8159-469d-b2f2-a55b380a2642] schema data cleaned 
[INFO ] 2024-06-21 11:59:02.672 - [任务 5(100)][e96f042e-8159-469d-b2f2-a55b380a2642] - Node e96f042e-8159-469d-b2f2-a55b380a2642[e96f042e-8159-469d-b2f2-a55b380a2642] monitor closed 
[INFO ] 2024-06-21 11:59:02.689 - [任务 5(100)][e96f042e-8159-469d-b2f2-a55b380a2642] - Node e96f042e-8159-469d-b2f2-a55b380a2642[e96f042e-8159-469d-b2f2-a55b380a2642] close complete, cost 1 ms 
[INFO ] 2024-06-21 11:59:02.690 - [任务 5(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-08904ef1-e282-4a80-afb5-6dc7190e34c9 
[INFO ] 2024-06-21 11:59:02.690 - [任务 5(100)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-08904ef1-e282-4a80-afb5-6dc7190e34c9 
[INFO ] 2024-06-21 11:59:02.690 - [任务 5(100)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070b-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:59:02.690 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:59:02.692 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:59:02.692 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 22 ms 
[INFO ] 2024-06-21 11:59:11.756 - [任务 5(100)][855c53df-d712-4828-ada7-2978eaa66116] - Node 855c53df-d712-4828-ada7-2978eaa66116[855c53df-d712-4828-ada7-2978eaa66116] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:59:11.757 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:59:11.757 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:59:11.757 - [任务 5(100)][855c53df-d712-4828-ada7-2978eaa66116] - Node 855c53df-d712-4828-ada7-2978eaa66116[855c53df-d712-4828-ada7-2978eaa66116] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:59:11.757 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:59:11.757 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:59:11.842 - [任务 5(100)][增强JS] - Init standardized JS engine 
[INFO ] 2024-06-21 11:59:12.001 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:59:12.014 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:59:12.014 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:59:12.014 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:59:12.014 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:59:12.015 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 14 ms 
[INFO ] 2024-06-21 11:59:12.327 - [任务 5(100)][增强JS] - 1718883457000 
[INFO ] 2024-06-21 11:59:12.328 - [任务 5(100)][增强JS] - 1.718883457E9 
[INFO ] 2024-06-21 11:59:12.328 - [任务 5(100)][增强JS] - 1.718883457E9 
[INFO ] 2024-06-21 11:59:12.334 - [任务 5(100)][855c53df-d712-4828-ada7-2978eaa66116] - Node 855c53df-d712-4828-ada7-2978eaa66116[855c53df-d712-4828-ada7-2978eaa66116] running status set to false 
[INFO ] 2024-06-21 11:59:12.334 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:59:12.334 - [任务 5(100)][855c53df-d712-4828-ada7-2978eaa66116] - Node 855c53df-d712-4828-ada7-2978eaa66116[855c53df-d712-4828-ada7-2978eaa66116] schema data cleaned 
[INFO ] 2024-06-21 11:59:12.334 - [任务 5(100)][855c53df-d712-4828-ada7-2978eaa66116] - Node 855c53df-d712-4828-ada7-2978eaa66116[855c53df-d712-4828-ada7-2978eaa66116] monitor closed 
[INFO ] 2024-06-21 11:59:12.335 - [任务 5(100)][855c53df-d712-4828-ada7-2978eaa66116] - Node 855c53df-d712-4828-ada7-2978eaa66116[855c53df-d712-4828-ada7-2978eaa66116] close complete, cost 2 ms 
[INFO ] 2024-06-21 11:59:12.353 - [任务 5(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-ba203179-8999-46ab-b4a7-109268b74200 
[INFO ] 2024-06-21 11:59:12.353 - [任务 5(100)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-ba203179-8999-46ab-b4a7-109268b74200 
[INFO ] 2024-06-21 11:59:12.353 - [任务 5(100)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070b-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:59:12.353 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:59:12.353 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:59:12.354 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 22 ms 
[INFO ] 2024-06-21 11:59:50.956 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:59:50.958 - [任务 5(100)][139a48aa-9470-4c2b-8ec6-6666018c17a5] - Node 139a48aa-9470-4c2b-8ec6-6666018c17a5[139a48aa-9470-4c2b-8ec6-6666018c17a5] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:59:50.958 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:59:50.958 - [任务 5(100)][139a48aa-9470-4c2b-8ec6-6666018c17a5] - Node 139a48aa-9470-4c2b-8ec6-6666018c17a5[139a48aa-9470-4c2b-8ec6-6666018c17a5] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:59:50.958 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 1 ms 
[INFO ] 2024-06-21 11:59:50.958 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 1 ms 
[INFO ] 2024-06-21 11:59:51.262 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:59:51.262 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:59:51.262 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:59:51.263 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:59:51.263 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:59:51.398 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 39 ms 
[INFO ] 2024-06-21 11:59:51.398 - [任务 5(100)][增强JS] - 1718883457000 
[INFO ] 2024-06-21 11:59:51.400 - [任务 5(100)][增强JS] - 1.718883457E9 
[INFO ] 2024-06-21 11:59:51.400 - [任务 5(100)][增强JS] - 1.718883457E9 
[INFO ] 2024-06-21 11:59:51.420 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:59:51.420 - [任务 5(100)][139a48aa-9470-4c2b-8ec6-6666018c17a5] - Node 139a48aa-9470-4c2b-8ec6-6666018c17a5[139a48aa-9470-4c2b-8ec6-6666018c17a5] running status set to false 
[INFO ] 2024-06-21 11:59:51.420 - [任务 5(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-6be6fa4f-eb7b-4ab2-be96-e169aa8b8900 
[INFO ] 2024-06-21 11:59:51.420 - [任务 5(100)][139a48aa-9470-4c2b-8ec6-6666018c17a5] - Node 139a48aa-9470-4c2b-8ec6-6666018c17a5[139a48aa-9470-4c2b-8ec6-6666018c17a5] schema data cleaned 
[INFO ] 2024-06-21 11:59:51.420 - [任务 5(100)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-6be6fa4f-eb7b-4ab2-be96-e169aa8b8900 
[INFO ] 2024-06-21 11:59:51.420 - [任务 5(100)][139a48aa-9470-4c2b-8ec6-6666018c17a5] - Node 139a48aa-9470-4c2b-8ec6-6666018c17a5[139a48aa-9470-4c2b-8ec6-6666018c17a5] monitor closed 
[INFO ] 2024-06-21 11:59:51.421 - [任务 5(100)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070b-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:59:51.421 - [任务 5(100)][139a48aa-9470-4c2b-8ec6-6666018c17a5] - Node 139a48aa-9470-4c2b-8ec6-6666018c17a5[139a48aa-9470-4c2b-8ec6-6666018c17a5] close complete, cost 5 ms 
[INFO ] 2024-06-21 11:59:51.424 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:59:51.424 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:59:51.426 - [任务 5(100)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 20 ms 
[INFO ] 2024-06-21 12:00:21.992 - [任务 5(100)][32540bfc-c654-4874-8c82-69fce99c1ac3] - Node 32540bfc-c654-4874-8c82-69fce99c1ac3[32540bfc-c654-4874-8c82-69fce99c1ac3] start preload schema,table counts: 0 
[INFO ] 2024-06-21 12:00:22.001 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] start preload schema,table counts: 1 
[INFO ] 2024-06-21 12:00:22.001 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 12:00:22.001 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 12:00:22.001 - [任务 5(100)][32540bfc-c654-4874-8c82-69fce99c1ac3] - Node 32540bfc-c654-4874-8c82-69fce99c1ac3[32540bfc-c654-4874-8c82-69fce99c1ac3] preload schema finished, cost 1 ms 
[INFO ] 2024-06-21 12:00:22.001 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 12:00:22.230 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 12:00:22.253 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 12:00:22.266 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 12:00:22.267 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 12:00:22.267 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 12:00:22.267 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 24 ms 
[INFO ] 2024-06-21 12:00:22.281 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] running status set to false 
[INFO ] 2024-06-21 12:00:22.281 - [任务 5(100)][32540bfc-c654-4874-8c82-69fce99c1ac3] - Node 32540bfc-c654-4874-8c82-69fce99c1ac3[32540bfc-c654-4874-8c82-69fce99c1ac3] running status set to false 
[INFO ] 2024-06-21 12:00:22.281 - [任务 5(100)][32540bfc-c654-4874-8c82-69fce99c1ac3] - Node 32540bfc-c654-4874-8c82-69fce99c1ac3[32540bfc-c654-4874-8c82-69fce99c1ac3] schema data cleaned 
[INFO ] 2024-06-21 12:00:22.281 - [任务 5(100)][32540bfc-c654-4874-8c82-69fce99c1ac3] - Node 32540bfc-c654-4874-8c82-69fce99c1ac3[32540bfc-c654-4874-8c82-69fce99c1ac3] monitor closed 
[INFO ] 2024-06-21 12:00:22.282 - [任务 5(100)][32540bfc-c654-4874-8c82-69fce99c1ac3] - Node 32540bfc-c654-4874-8c82-69fce99c1ac3[32540bfc-c654-4874-8c82-69fce99c1ac3] close complete, cost 3 ms 
[INFO ] 2024-06-21 12:00:22.285 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] schema data cleaned 
[INFO ] 2024-06-21 12:00:22.285 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] monitor closed 
[INFO ] 2024-06-21 12:00:22.288 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] close complete, cost 7 ms 
[INFO ] 2024-06-21 12:00:22.381 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 12:00:22.382 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] start preload schema,table counts: 1 
[INFO ] 2024-06-21 12:00:22.382 - [任务 5(100)][d1fa171e-2534-4b79-a453-d97bf2c8a812] - Node d1fa171e-2534-4b79-a453-d97bf2c8a812[d1fa171e-2534-4b79-a453-d97bf2c8a812] start preload schema,table counts: 0 
[INFO ] 2024-06-21 12:00:22.382 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 12:00:22.382 - [任务 5(100)][d1fa171e-2534-4b79-a453-d97bf2c8a812] - Node d1fa171e-2534-4b79-a453-d97bf2c8a812[d1fa171e-2534-4b79-a453-d97bf2c8a812] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 12:00:22.382 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 2 ms 
[INFO ] 2024-06-21 12:00:22.665 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 12:00:22.691 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 12:00:22.692 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] running status set to false 
[INFO ] 2024-06-21 12:00:22.695 - [任务 5(100)][d1fa171e-2534-4b79-a453-d97bf2c8a812] - Node d1fa171e-2534-4b79-a453-d97bf2c8a812[d1fa171e-2534-4b79-a453-d97bf2c8a812] running status set to false 
[INFO ] 2024-06-21 12:00:22.695 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] schema data cleaned 
[INFO ] 2024-06-21 12:00:22.696 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 12:00:22.696 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] monitor closed 
[INFO ] 2024-06-21 12:00:22.696 - [任务 5(100)][d1fa171e-2534-4b79-a453-d97bf2c8a812] - Node d1fa171e-2534-4b79-a453-d97bf2c8a812[d1fa171e-2534-4b79-a453-d97bf2c8a812] schema data cleaned 
[INFO ] 2024-06-21 12:00:22.696 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 12:00:22.696 - [任务 5(100)][d1fa171e-2534-4b79-a453-d97bf2c8a812] - Node d1fa171e-2534-4b79-a453-d97bf2c8a812[d1fa171e-2534-4b79-a453-d97bf2c8a812] monitor closed 
[INFO ] 2024-06-21 12:00:22.697 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 12:00:22.697 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] close complete, cost 9 ms 
[INFO ] 2024-06-21 12:00:22.697 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 59 ms 
[INFO ] 2024-06-21 12:00:22.697 - [任务 5(100)][d1fa171e-2534-4b79-a453-d97bf2c8a812] - Node d1fa171e-2534-4b79-a453-d97bf2c8a812[d1fa171e-2534-4b79-a453-d97bf2c8a812] close complete, cost 2 ms 
[INFO ] 2024-06-21 12:00:24.872 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] start preload schema,table counts: 1 
[INFO ] 2024-06-21 12:00:24.873 - [任务 5(100)][e9bfd711-b322-474b-ac81-e9cb02568395] - Node e9bfd711-b322-474b-ac81-e9cb02568395[e9bfd711-b322-474b-ac81-e9cb02568395] start preload schema,table counts: 0 
[INFO ] 2024-06-21 12:00:24.873 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 12:00:24.873 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 12:00:24.873 - [任务 5(100)][e9bfd711-b322-474b-ac81-e9cb02568395] - Node e9bfd711-b322-474b-ac81-e9cb02568395[e9bfd711-b322-474b-ac81-e9cb02568395] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 12:00:24.873 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 12:00:25.102 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 12:00:25.118 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 12:00:25.118 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 12:00:25.118 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 12:00:25.132 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 12:00:25.147 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 30 ms 
[INFO ] 2024-06-21 12:00:25.147 - [任务 5(100)][标准JS] - 1718883457000 
[INFO ] 2024-06-21 12:00:25.150 - [任务 5(100)][标准JS] - 1.718883457E9 
[INFO ] 2024-06-21 12:00:25.156 - [任务 5(100)][标准JS] - 1.718883457E9 
[INFO ] 2024-06-21 12:00:25.158 - [任务 5(100)][e9bfd711-b322-474b-ac81-e9cb02568395] - Node e9bfd711-b322-474b-ac81-e9cb02568395[e9bfd711-b322-474b-ac81-e9cb02568395] running status set to false 
[INFO ] 2024-06-21 12:00:25.158 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] running status set to false 
[INFO ] 2024-06-21 12:00:25.159 - [任务 5(100)][e9bfd711-b322-474b-ac81-e9cb02568395] - Node e9bfd711-b322-474b-ac81-e9cb02568395[e9bfd711-b322-474b-ac81-e9cb02568395] schema data cleaned 
[INFO ] 2024-06-21 12:00:25.159 - [任务 5(100)][e9bfd711-b322-474b-ac81-e9cb02568395] - Node e9bfd711-b322-474b-ac81-e9cb02568395[e9bfd711-b322-474b-ac81-e9cb02568395] monitor closed 
[INFO ] 2024-06-21 12:00:25.160 - [任务 5(100)][e9bfd711-b322-474b-ac81-e9cb02568395] - Node e9bfd711-b322-474b-ac81-e9cb02568395[e9bfd711-b322-474b-ac81-e9cb02568395] close complete, cost 3 ms 
[INFO ] 2024-06-21 12:00:25.160 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] schema data cleaned 
[INFO ] 2024-06-21 12:00:25.160 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] monitor closed 
[INFO ] 2024-06-21 12:00:25.160 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] close complete, cost 4 ms 
[INFO ] 2024-06-21 12:00:43.395 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 12:00:43.396 - [任务 5(100)][a293627e-31e2-4706-a702-8851ab3bdaaa] - Node a293627e-31e2-4706-a702-8851ab3bdaaa[a293627e-31e2-4706-a702-8851ab3bdaaa] start preload schema,table counts: 0 
[INFO ] 2024-06-21 12:00:43.396 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] start preload schema,table counts: 1 
[INFO ] 2024-06-21 12:00:43.396 - [任务 5(100)][a293627e-31e2-4706-a702-8851ab3bdaaa] - Node a293627e-31e2-4706-a702-8851ab3bdaaa[a293627e-31e2-4706-a702-8851ab3bdaaa] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 12:00:43.396 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 12:00:43.396 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 12:00:43.661 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 12:00:43.661 - [任务 5(100)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 12:00:43.665 - [任务 5(100)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 12:00:43.665 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 12:00:43.665 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 12:00:43.675 - [任务 5(100)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 24 ms 
[INFO ] 2024-06-21 12:00:43.676 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] running status set to false 
[INFO ] 2024-06-21 12:00:43.678 - [任务 5(100)][a293627e-31e2-4706-a702-8851ab3bdaaa] - Node a293627e-31e2-4706-a702-8851ab3bdaaa[a293627e-31e2-4706-a702-8851ab3bdaaa] running status set to false 
[INFO ] 2024-06-21 12:00:43.678 - [任务 5(100)][a293627e-31e2-4706-a702-8851ab3bdaaa] - Node a293627e-31e2-4706-a702-8851ab3bdaaa[a293627e-31e2-4706-a702-8851ab3bdaaa] schema data cleaned 
[INFO ] 2024-06-21 12:00:43.678 - [任务 5(100)][a293627e-31e2-4706-a702-8851ab3bdaaa] - Node a293627e-31e2-4706-a702-8851ab3bdaaa[a293627e-31e2-4706-a702-8851ab3bdaaa] monitor closed 
[INFO ] 2024-06-21 12:00:43.680 - [任务 5(100)][a293627e-31e2-4706-a702-8851ab3bdaaa] - Node a293627e-31e2-4706-a702-8851ab3bdaaa[a293627e-31e2-4706-a702-8851ab3bdaaa] close complete, cost 1 ms 
[INFO ] 2024-06-21 12:00:43.680 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] schema data cleaned 
[INFO ] 2024-06-21 12:00:43.680 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] monitor closed 
[INFO ] 2024-06-21 12:00:43.680 - [任务 5(100)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] close complete, cost 5 ms 
