[INFO ] 2024-12-18 00:49:41.140 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] running status set to false 
[INFO ] 2024-12-18 00:58:21.950 - [任务 57] - Start task milestones: 67614db292954b331b37572e(任务 57) 
[INFO ] 2024-12-18 00:58:21.952 - [任务 57] - Task initialization... 
[INFO ] 2024-12-18 00:58:22.959 - [任务 57] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 00:58:23.098 - [任务 57] - The engine receives 任务 57 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 00:58:23.099 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] start preload schema,table counts: 9 
[INFO ] 2024-12-18 00:58:23.102 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] start preload schema,table counts: 9 
[INFO ] 2024-12-18 00:58:23.102 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 00:58:23.103 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 01:00:06.275 - [任务 57][TestMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2483/1611448186@bb5b891 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 
[INFO ] 2024-12-18 01:00:06.276 - [任务 57][Dummy] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2463/1085280913@2068d427 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 
[ERROR] 2024-12-18 01:00:06.357 - [任务 57][TestMongo] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2483/1611448186@bb5b891 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2483/1611448186@bb5b891 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


<-- Simple Stack Trace -->
Caused by: java.lang.InterruptedException: null
	java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	java.util.concurrent.FutureTask.get(FutureTask.java:191)
	io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2483/1611448186@bb5b891 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2483/1611448186@bb5b891 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:82)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	... 13 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	... 15 more

[ERROR] 2024-12-18 01:00:06.363 - [任务 57][Dummy] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2463/1085280913@2068d427 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2463/1085280913@2068d427 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


<-- Simple Stack Trace -->
Caused by: java.lang.InterruptedException: null
	java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	java.util.concurrent.FutureTask.get(FutureTask.java:191)
	io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2463/1085280913@2068d427 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:119)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2463/1085280913@2068d427 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:82)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	... 13 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	... 15 more

[INFO ] 2024-12-18 01:00:06.392 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] running status set to false 
[INFO ] 2024-12-18 01:00:06.394 - [任务 57][TestMongo] - PDK connector node stopped: null 
[INFO ] 2024-12-18 01:00:06.395 - [任务 57][TestMongo] - PDK connector node released: null 
[INFO ] 2024-12-18 01:00:06.395 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] schema data cleaned 
[INFO ] 2024-12-18 01:00:06.410 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] monitor closed 
[INFO ] 2024-12-18 01:00:06.410 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] close complete, cost 17 ms 
[INFO ] 2024-12-18 01:00:06.410 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] running status set to false 
[INFO ] 2024-12-18 01:00:06.410 - [任务 57][Dummy] - PDK connector node stopped: null 
[INFO ] 2024-12-18 01:00:06.410 - [任务 57][Dummy] - PDK connector node released: null 
[INFO ] 2024-12-18 01:00:06.412 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] schema data cleaned 
[INFO ] 2024-12-18 01:00:06.412 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] monitor closed 
[INFO ] 2024-12-18 01:00:06.618 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] close complete, cost 8 ms 
[INFO ] 2024-12-18 01:49:26.261 - [任务 57] - Task initialization... 
[INFO ] 2024-12-18 01:49:26.284 - [任务 57] - Start task milestones: 67614db292954b331b37572e(任务 57) 
[INFO ] 2024-12-18 01:49:27.848 - [任务 57] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 01:49:27.849 - [任务 57] - The engine receives 任务 57 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 01:49:28.308 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] start preload schema,table counts: 12 
[INFO ] 2024-12-18 01:49:28.312 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] start preload schema,table counts: 12 
[INFO ] 2024-12-18 01:49:28.325 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] preload schema finished, cost 1 ms 
[INFO ] 2024-12-18 01:49:28.332 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 01:50:42.508 - [任务 57][TestMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2794/859236780@4fc285b8 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 
[INFO ] 2024-12-18 01:50:42.510 - [任务 57][Dummy] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2787/17680530@38f7e43e failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 
[ERROR] 2024-12-18 01:50:42.550 - [任务 57][TestMongo] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2794/859236780@4fc285b8 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2794/859236780@4fc285b8 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


<-- Simple Stack Trace -->
Caused by: java.lang.InterruptedException: null
	java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	java.util.concurrent.FutureTask.get(FutureTask.java:191)
	io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2794/859236780@4fc285b8 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2794/859236780@4fc285b8 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:82)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	... 13 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	... 15 more

[ERROR] 2024-12-18 01:50:42.551 - [任务 57][Dummy] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2787/17680530@38f7e43e failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2787/17680530@38f7e43e failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


<-- Simple Stack Trace -->
Caused by: java.lang.InterruptedException: null
	java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	java.util.concurrent.FutureTask.get(FutureTask.java:191)
	io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2787/17680530@38f7e43e failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:119)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2787/17680530@38f7e43e failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:82)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	... 13 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	... 15 more

[INFO ] 2024-12-18 01:50:42.577 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] running status set to false 
[INFO ] 2024-12-18 01:50:42.578 - [任务 57][TestMongo] - PDK connector node stopped: null 
[INFO ] 2024-12-18 01:50:42.579 - [任务 57][TestMongo] - PDK connector node released: null 
[INFO ] 2024-12-18 01:50:42.579 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] schema data cleaned 
[INFO ] 2024-12-18 01:50:42.588 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] monitor closed 
[INFO ] 2024-12-18 01:50:42.589 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] close complete, cost 14 ms 
[INFO ] 2024-12-18 01:50:42.610 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] running status set to false 
[WARN ] 2024-12-18 01:50:42.611 - [任务 57][Dummy] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: null 
[INFO ] 2024-12-18 01:50:42.611 - [任务 57][Dummy] - PDK connector node released: null 
[INFO ] 2024-12-18 01:50:42.612 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] schema data cleaned 
[INFO ] 2024-12-18 01:50:42.612 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] monitor closed 
[INFO ] 2024-12-18 01:50:42.612 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] close complete, cost 23 ms 
[INFO ] 2024-12-18 02:16:19.266 - [任务 57] - Start task milestones: 67614db292954b331b37572e(任务 57) 
[INFO ] 2024-12-18 02:16:19.269 - [任务 57] - Task initialization... 
[INFO ] 2024-12-18 02:16:20.434 - [任务 57] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-18 02:16:20.640 - [任务 57] - The engine receives 任务 57 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 02:17:19.880 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] start preload schema,table counts: 12 
[INFO ] 2024-12-18 02:17:19.880 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] start preload schema,table counts: 12 
[INFO ] 2024-12-18 02:17:19.881 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 02:17:19.881 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 02:18:33.958 - [任务 57][Dummy] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2431/654736285@62f27ee8 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 
[INFO ] 2024-12-18 02:18:33.960 - [任务 57][TestMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2456/689365080@61db94ab failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 
[ERROR] 2024-12-18 02:18:33.971 - [任务 57][TestMongo] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2456/689365080@61db94ab failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2456/689365080@61db94ab failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


<-- Simple Stack Trace -->
Caused by: java.lang.InterruptedException: null
	java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	java.util.concurrent.FutureTask.get(FutureTask.java:191)
	io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2456/689365080@61db94ab failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2456/689365080@61db94ab failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:82)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:261)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	... 13 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	... 15 more

[ERROR] 2024-12-18 02:18:33.972 - [任务 57][Dummy] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2431/654736285@62f27ee8 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2431/654736285@62f27ee8 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


<-- Simple Stack Trace -->
Caused by: java.lang.InterruptedException: null
	java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	java.util.concurrent.FutureTask.get(FutureTask.java:191)
	io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2431/654736285@62f27ee8 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:119)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode$$Lambda$2431/654736285@62f27ee8 failed, java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:235)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:82)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doInit(HazelcastTargetPdkBaseNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:97)
	... 13 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.FutureTask.awaitDone(FutureTask.java:404)
	at java.util.concurrent.FutureTask.get(FutureTask.java:191)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:71)
	... 15 more

[INFO ] 2024-12-18 02:18:34.005 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] running status set to false 
[INFO ] 2024-12-18 02:18:34.005 - [任务 57][TestMongo] - PDK connector node stopped: null 
[INFO ] 2024-12-18 02:18:34.005 - [任务 57][TestMongo] - PDK connector node released: null 
[INFO ] 2024-12-18 02:18:34.006 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] schema data cleaned 
[INFO ] 2024-12-18 02:18:34.011 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] monitor closed 
[INFO ] 2024-12-18 02:18:34.011 - [任务 57][TestMongo] - Node TestMongo[edf9f2aa-d1b7-42e2-9d43-2f0e63e0a3c8] close complete, cost 10 ms 
[INFO ] 2024-12-18 02:18:34.012 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] running status set to false 
[INFO ] 2024-12-18 02:18:34.013 - [任务 57][Dummy] - PDK connector node stopped: null 
[INFO ] 2024-12-18 02:18:34.013 - [任务 57][Dummy] - PDK connector node released: null 
[INFO ] 2024-12-18 02:18:34.013 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] schema data cleaned 
[INFO ] 2024-12-18 02:18:34.014 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] monitor closed 
[INFO ] 2024-12-18 02:18:34.014 - [任务 57][Dummy] - Node Dummy[5d663aa5-1e00-477a-abea-f4d20749ffa4] close complete, cost 4 ms 
