[INFO ] 2024-05-08 10:25:44.030 - [任务 1] - Start task milestones: 66389aa81f6f230096637149(任务 1) 
[INFO ] 2024-05-08 10:25:44.031 - [任务 1] - Task initialization... 
[INFO ] 2024-05-08 10:25:44.031 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-08 10:25:44.133 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-08 10:25:44.738 - [任务 1][CAR.CLIAM] - Node CAR.CLIAM[457dc2fe-1116-4cd9-a945-4e687651621a] start preload schema,table counts: 1 
[INFO ] 2024-05-08 10:25:44.740 - [任务 1][test2] - Node test2[ebac284d-33a1-46c7-91f4-9e3acef8069e] start preload schema,table counts: 1 
[INFO ] 2024-05-08 10:25:44.832 - [任务 1][CAR.CLIAM] - Node CAR.CLIAM[457dc2fe-1116-4cd9-a945-4e687651621a] preload schema finished, cost 139 ms 
[INFO ] 2024-05-08 10:25:44.834 - [任务 1][test2] - Node test2[ebac284d-33a1-46c7-91f4-9e3acef8069e] preload schema finished, cost 89 ms 
[INFO ] 2024-05-08 10:25:46.053 - [任务 1][CAR.CLIAM] - Source node "CAR.CLIAM" read batch size: 100 
[INFO ] 2024-05-08 10:25:46.055 - [任务 1][CAR.CLIAM] - Source node "CAR.CLIAM" event queue capacity: 200 
[INFO ] 2024-05-08 10:25:46.058 - [任务 1][CAR.CLIAM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-08 10:25:46.205 - [任务 1][test2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-08 10:25:46.207 - [任务 1][CAR.CLIAM] - batch offset found: {},stream offset found: {"cdcOffset":1715135146,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-05-08 10:25:46.298 - [任务 1][test2] - Table "test.test2" exists, skip auto create table 
[INFO ] 2024-05-08 10:25:46.303 - [任务 1][test2] - The table test2 has already exist. 
[INFO ] 2024-05-08 10:25:46.465 - [任务 1][CAR.CLIAM] - Initial sync started 
[INFO ] 2024-05-08 10:25:46.469 - [任务 1][CAR.CLIAM] - Starting batch read, table name: CAR.CLIAM, offset: null 
[INFO ] 2024-05-08 10:25:46.475 - [任务 1][CAR.CLIAM] - Table CAR.CLIAM is going to be initial synced 
[INFO ] 2024-05-08 10:25:46.603 - [任务 1][CAR.CLIAM] - Query table 'CAR.CLIAM' counts: 1076 
[WARN ] 2024-05-08 10:25:46.604 - [任务 1][test2] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLSyntaxErrorException: Unknown column '_id' in 'field list'
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-08 10:26:18.711 - [任务 1] - Stop task milestones: 66389aa81f6f230096637149(任务 1)  
[INFO ] 2024-05-08 10:26:19.678 - [任务 1][CAR.CLIAM] - Table [CAR.CLIAM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-08 10:26:19.680 - [任务 1][CAR.CLIAM] - Node CAR.CLIAM[457dc2fe-1116-4cd9-a945-4e687651621a] running status set to false 
[INFO ] 2024-05-08 10:26:19.718 - [任务 1][CAR.CLIAM] - PDK connector node stopped: HazelcastSourcePdkDataNode-457dc2fe-1116-4cd9-a945-4e687651621a 
[INFO ] 2024-05-08 10:26:19.718 - [任务 1][CAR.CLIAM] - PDK connector node released: HazelcastSourcePdkDataNode-457dc2fe-1116-4cd9-a945-4e687651621a 
[INFO ] 2024-05-08 10:26:19.722 - [任务 1][CAR.CLIAM] - Node CAR.CLIAM[457dc2fe-1116-4cd9-a945-4e687651621a] schema data cleaned 
[INFO ] 2024-05-08 10:26:19.723 - [任务 1][CAR.CLIAM] - Node CAR.CLIAM[457dc2fe-1116-4cd9-a945-4e687651621a] monitor closed 
[INFO ] 2024-05-08 10:26:19.731 - [任务 1][CAR.CLIAM] - Node CAR.CLIAM[457dc2fe-1116-4cd9-a945-4e687651621a] close complete, cost 46 ms 
[INFO ] 2024-05-08 10:26:19.732 - [任务 1][test2] - Node test2[ebac284d-33a1-46c7-91f4-9e3acef8069e] running status set to false 
[INFO ] 2024-05-08 10:26:19.789 - [任务 1][test2] - PDK connector node stopped: HazelcastTargetPdkDataNode-ebac284d-33a1-46c7-91f4-9e3acef8069e 
[INFO ] 2024-05-08 10:26:19.793 - [任务 1][test2] - PDK connector node released: HazelcastTargetPdkDataNode-ebac284d-33a1-46c7-91f4-9e3acef8069e 
[INFO ] 2024-05-08 10:26:19.793 - [任务 1][test2] - Node test2[ebac284d-33a1-46c7-91f4-9e3acef8069e] schema data cleaned 
[INFO ] 2024-05-08 10:26:19.796 - [任务 1][test2] - Node test2[ebac284d-33a1-46c7-91f4-9e3acef8069e] monitor closed 
[INFO ] 2024-05-08 10:26:19.796 - [任务 1][test2] - Node test2[ebac284d-33a1-46c7-91f4-9e3acef8069e] close complete, cost 66 ms 
[ERROR] 2024-05-08 10:26:19.817 - [任务 1][test2] - Execute PDK method: TARGET_WRITE_RECORD, tableName: test2 <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test2

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:52)
	io.tapdata.connector.mysql.dml.MysqlRecordWriter.<init>(MysqlRecordWriter.java:17)
	io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:409)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:828)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test2
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:834)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:66)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$55(HazelcastTargetPdkDataNode.java:780)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:774)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$24(HazelcastTargetPdkDataNode.java:483)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:483)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:557)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:513)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:485)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:531)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:52)
	at io.tapdata.connector.mysql.dml.MysqlRecordWriter.<init>(MysqlRecordWriter.java:17)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:409)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:828)
	... 26 more

[INFO ] 2024-05-08 10:26:22.217 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-08 10:26:22.217 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-05-08 10:26:22.217 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-05-08 10:26:22.298 - [任务 1] - Remove memory task client succeed, task: 任务 1[66389aa81f6f230096637149] 
[INFO ] 2024-05-08 10:26:22.299 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66389aa81f6f230096637149] 
[INFO ] 2024-05-08 10:27:10.539 - [任务 1] - Start task milestones: 66389aa81f6f230096637149(任务 1) 
[INFO ] 2024-05-08 10:27:10.539 - [任务 1] - Task initialization... 
[INFO ] 2024-05-08 10:27:10.705 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-08 10:27:10.709 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-08 10:27:10.822 - [任务 1][CAR.CLIAM] - Node CLAIM[457dc2fe-1116-4cd9-a945-4e687651621a] start preload schema,table counts: 1 
[INFO ] 2024-05-08 10:27:10.823 - [任务 1][test2] - Node test3[ebac284d-33a1-46c7-91f4-9e3acef8069e] start preload schema,table counts: 1 
[INFO ] 2024-05-08 10:27:10.854 - [任务 1][CAR.CLIAM] - Node CLAIM[457dc2fe-1116-4cd9-a945-4e687651621a] preload schema finished, cost 29 ms 
[INFO ] 2024-05-08 10:27:10.855 - [任务 1][test2] - Node test3[ebac284d-33a1-46c7-91f4-9e3acef8069e] preload schema finished, cost 29 ms 
[INFO ] 2024-05-08 10:27:11.512 - [任务 1][CAR.CLIAM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-05-08 10:27:11.516 - [任务 1][CAR.CLIAM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-05-08 10:27:11.516 - [任务 1][CAR.CLIAM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-08 10:27:11.717 - [任务 1][CAR.CLIAM] - batch offset found: {},stream offset found: {"cdcOffset":1715135231,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-05-08 10:27:11.726 - [任务 1][CAR.CLIAM] - Initial sync started 
[INFO ] 2024-05-08 10:27:11.732 - [任务 1][CAR.CLIAM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-05-08 10:27:11.770 - [任务 1][CAR.CLIAM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-05-08 10:27:11.771 - [任务 1][test2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-08 10:27:11.790 - [任务 1][test2] - Table "test.test3" exists, skip auto create table 
[INFO ] 2024-05-08 10:27:11.790 - [任务 1][test2] - The table test3 has already exist. 
[INFO ] 2024-05-08 10:27:11.926 - [任务 1][CAR.CLIAM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-05-08 10:27:11.932 - [任务 1][CAR.CLIAM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-08 10:27:11.936 - [任务 1][CAR.CLIAM] - Initial sync completed 
[INFO ] 2024-05-08 10:27:11.936 - [任务 1][CAR.CLIAM] - Incremental sync starting... 
[INFO ] 2024-05-08 10:27:11.940 - [任务 1][CAR.CLIAM] - Initial sync completed 
[INFO ] 2024-05-08 10:27:11.940 - [任务 1][CAR.CLIAM] - Starting stream read, table list: [CLAIM], offset: {"cdcOffset":1715135231,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-05-08 10:27:12.141 - [任务 1][CAR.CLIAM] - Connector MongoDB incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-05-08 10:36:54.843 - [任务 1] - Stop task milestones: 66389aa81f6f230096637149(任务 1)  
[INFO ] 2024-05-08 10:36:55.144 - [任务 1][CAR.CLIAM] - Node CLAIM[457dc2fe-1116-4cd9-a945-4e687651621a] running status set to false 
[INFO ] 2024-05-08 10:36:55.189 - [任务 1][CAR.CLIAM] - PDK connector node stopped: HazelcastSourcePdkDataNode-457dc2fe-1116-4cd9-a945-4e687651621a 
[INFO ] 2024-05-08 10:36:55.189 - [任务 1][CAR.CLIAM] - PDK connector node released: HazelcastSourcePdkDataNode-457dc2fe-1116-4cd9-a945-4e687651621a 
[INFO ] 2024-05-08 10:36:55.190 - [任务 1][CAR.CLIAM] - Node CLAIM[457dc2fe-1116-4cd9-a945-4e687651621a] schema data cleaned 
[INFO ] 2024-05-08 10:36:55.190 - [任务 1][CAR.CLIAM] - Node CLAIM[457dc2fe-1116-4cd9-a945-4e687651621a] monitor closed 
[INFO ] 2024-05-08 10:36:55.192 - [任务 1][CAR.CLIAM] - Node CLAIM[457dc2fe-1116-4cd9-a945-4e687651621a] close complete, cost 54 ms 
[INFO ] 2024-05-08 10:36:55.192 - [任务 1][test2] - Node test3[ebac284d-33a1-46c7-91f4-9e3acef8069e] running status set to false 
[INFO ] 2024-05-08 10:36:55.226 - [任务 1][test2] - PDK connector node stopped: HazelcastTargetPdkDataNode-ebac284d-33a1-46c7-91f4-9e3acef8069e 
[INFO ] 2024-05-08 10:36:55.227 - [任务 1][test2] - PDK connector node released: HazelcastTargetPdkDataNode-ebac284d-33a1-46c7-91f4-9e3acef8069e 
[INFO ] 2024-05-08 10:36:55.228 - [任务 1][test2] - Node test3[ebac284d-33a1-46c7-91f4-9e3acef8069e] schema data cleaned 
[INFO ] 2024-05-08 10:36:55.228 - [任务 1][test2] - Node test3[ebac284d-33a1-46c7-91f4-9e3acef8069e] monitor closed 
[INFO ] 2024-05-08 10:36:55.437 - [任务 1][test2] - Node test3[ebac284d-33a1-46c7-91f4-9e3acef8069e] close complete, cost 37 ms 
[INFO ] 2024-05-08 10:36:57.939 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-08 10:36:57.940 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-05-08 10:36:57.940 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-05-08 10:36:57.987 - [任务 1] - Remove memory task client succeed, task: 任务 1[66389aa81f6f230096637149] 
[INFO ] 2024-05-08 10:36:58.193 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66389aa81f6f230096637149] 
[INFO ] 2024-05-08 10:46:01.118 - [任务 1] - Start task milestones: 66389aa81f6f230096637149(任务 1) 
[INFO ] 2024-05-08 10:46:01.118 - [任务 1] - Task initialization... 
[INFO ] 2024-05-08 10:46:01.118 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-08 10:46:01.118 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-08 10:46:01.118 - [任务 1][test2] - Node test3[ebac284d-33a1-46c7-91f4-9e3acef8069e] start preload schema,table counts: 1 
[INFO ] 2024-05-08 10:46:01.119 - [任务 1][CAR.CLIAM] - Node CLAIM[457dc2fe-1116-4cd9-a945-4e687651621a] start preload schema,table counts: 1 
[INFO ] 2024-05-08 10:46:01.119 - [任务 1][CAR.CLIAM] - Node CLAIM[457dc2fe-1116-4cd9-a945-4e687651621a] preload schema finished, cost 56 ms 
[INFO ] 2024-05-08 10:46:01.119 - [任务 1][test2] - Node test3[ebac284d-33a1-46c7-91f4-9e3acef8069e] preload schema finished, cost 56 ms 
[INFO ] 2024-05-08 10:46:01.240 - [任务 1][CAR.CLIAM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-05-08 10:46:01.241 - [任务 1][CAR.CLIAM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-05-08 10:46:01.256 - [任务 1][CAR.CLIAM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-05-08 10:46:01.296 - [任务 1][CAR.CLIAM] - batch offset found: {"CLAIM":{"offset":{"sortKey":"_id","value":"657baffa62a1bacd962c870f","objectId":true},"status":"RUNNING"}},stream offset found: {"_data":{"value":"82663AE3E2000000022B022C0100296E5A1004791900F94AE1470C8F5395377B6297BD46645F696400646510F74CA270A1CF5533D1B40004","bsonType":"STRING","boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"int32":false,"string":true,"int64":false,"symbol":false,"double":false,"binary":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-05-08 10:46:01.296 - [任务 1][test2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-08 10:46:01.419 - [任务 1][CAR.CLIAM] - Incremental sync starting... 
[INFO ] 2024-05-08 10:46:01.421 - [任务 1][CAR.CLIAM] - Initial sync completed 
[INFO ] 2024-05-08 10:46:01.496 - [任务 1][CAR.CLIAM] - Starting stream read, table list: [CLAIM], offset: {"_data":{"value":"82663AE3E2000000022B022C0100296E5A1004791900F94AE1470C8F5395377B6297BD46645F696400646510F74CA270A1CF5533D1B40004","bsonType":"STRING","boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"int32":false,"string":true,"int64":false,"symbol":false,"double":false,"binary":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-05-08 10:46:01.496 - [任务 1][CAR.CLIAM] - Connector MongoDB incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-05-08 10:57:12.925 - [任务 1] - Stop task milestones: 66389aa81f6f230096637149(任务 1)  
[INFO ] 2024-05-08 10:57:13.227 - [任务 1][CAR.CLIAM] - Node CLAIM[457dc2fe-1116-4cd9-a945-4e687651621a] running status set to false 
[INFO ] 2024-05-08 10:57:13.271 - [任务 1][CAR.CLIAM] - PDK connector node stopped: HazelcastSourcePdkDataNode-457dc2fe-1116-4cd9-a945-4e687651621a 
[INFO ] 2024-05-08 10:57:13.273 - [任务 1][CAR.CLIAM] - PDK connector node released: HazelcastSourcePdkDataNode-457dc2fe-1116-4cd9-a945-4e687651621a 
[INFO ] 2024-05-08 10:57:13.275 - [任务 1][CAR.CLIAM] - Node CLAIM[457dc2fe-1116-4cd9-a945-4e687651621a] schema data cleaned 
[INFO ] 2024-05-08 10:57:13.278 - [任务 1][CAR.CLIAM] - Node CLAIM[457dc2fe-1116-4cd9-a945-4e687651621a] monitor closed 
[INFO ] 2024-05-08 10:57:13.279 - [任务 1][CAR.CLIAM] - Node CLAIM[457dc2fe-1116-4cd9-a945-4e687651621a] close complete, cost 62 ms 
[INFO ] 2024-05-08 10:57:13.297 - [任务 1][test2] - Node test3[ebac284d-33a1-46c7-91f4-9e3acef8069e] running status set to false 
[INFO ] 2024-05-08 10:57:13.297 - [任务 1][test2] - PDK connector node stopped: HazelcastTargetPdkDataNode-ebac284d-33a1-46c7-91f4-9e3acef8069e 
[INFO ] 2024-05-08 10:57:13.297 - [任务 1][test2] - PDK connector node released: HazelcastTargetPdkDataNode-ebac284d-33a1-46c7-91f4-9e3acef8069e 
[INFO ] 2024-05-08 10:57:13.298 - [任务 1][test2] - Node test3[ebac284d-33a1-46c7-91f4-9e3acef8069e] schema data cleaned 
[INFO ] 2024-05-08 10:57:13.298 - [任务 1][test2] - Node test3[ebac284d-33a1-46c7-91f4-9e3acef8069e] monitor closed 
[INFO ] 2024-05-08 10:57:13.505 - [任务 1][test2] - Node test3[ebac284d-33a1-46c7-91f4-9e3acef8069e] close complete, cost 20 ms 
[INFO ] 2024-05-08 10:57:18.174 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-08 10:57:18.174 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-05-08 10:57:18.174 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-05-08 10:57:18.226 - [任务 1] - Remove memory task client succeed, task: 任务 1[66389aa81f6f230096637149] 
[INFO ] 2024-05-08 10:57:18.230 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66389aa81f6f230096637149] 
