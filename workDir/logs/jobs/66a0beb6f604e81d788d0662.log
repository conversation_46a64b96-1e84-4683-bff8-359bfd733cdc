[INFO ] 2024-07-24 16:45:54.681 - [将测试环境中的M] - load tapTable task 66a0beb6f604e81d788d0661-d5ad162e-d6ac-4f77-be34-5a6b4f1861d4 complete, cost 1329ms 
[INFO ] 2024-07-24 16:45:54.916 - [将测试环境中的M] - load tapTable task 66a0beb6f604e81d788d0661-215afb76-8cc9-4044-9883-092c1672814b complete, cost 493ms 
[INFO ] 2024-07-24 16:45:57.027 - [将测试环境中的M] - load tapTable task 66a0beb6f604e81d788d0661-bada6db5-6903-4459-be66-6df5c63692cf complete, cost 3675ms 
[INFO ] 2024-07-24 16:46:06.140 - [将测试环境中的M] - load tapTable task 66a0beb6f604e81d788d0661-8e6eb56b-4cf3-4dd9-a9b7-6f01612dbd01 complete, cost 322ms 
[INFO ] 2024-07-24 16:47:14.486 - [将测试环境中的M] - load tapTable task 66a0beb6f604e81d788d0661-d8fc4683-488b-4d2e-a469-4f7d53be55ca complete, cost 413ms 
[INFO ] 2024-07-24 16:47:36.943 - [将测试环境中的M] - load tapTable task 66a0beb6f604e81d788d0661-b207218b-8b18-4c28-9dfe-5631061c5ef6 complete, cost 467ms 
[INFO ] 2024-07-24 16:48:00.270 - [将测试环境中的M] - load tapTable task 66a0beb6f604e81d788d0661-4f5bcc68-438a-4f24-8b80-d6e7a9521ff0 complete, cost 378ms 
[INFO ] 2024-07-24 16:48:05.628 - [将测试环境中的M] - load tapTable task 66a0beb6f604e81d788d0661-a1fe023e-3be7-47f4-966b-a6ee243fad55 complete, cost 256ms 
[INFO ] 2024-07-24 16:48:06.310 - [将测试环境中的M] - load tapTable task 66a0beb6f604e81d788d0661-fb0857e4-307e-4765-9985-2c41138adeb0 complete, cost 287ms 
[INFO ] 2024-07-24 16:48:18.514 - [将测试环境中的M] - load tapTable task 66a0beb6f604e81d788d0661-6694a63b-a7b8-43e0-b3c4-c1b90ef3ecf2 complete, cost 395ms 
[INFO ] 2024-07-24 16:48:27.819 - [将测试环境中的M] - load tapTable task 66a0beb6f604e81d788d0661-5dd7edbf-47d2-42df-b08f-a70a5d0245e0 complete, cost 290ms 
[INFO ] 2024-07-24 16:48:29.744 - [将测试环境中的M] - load tapTable task 66a0beb6f604e81d788d0661-6c643033-c9dc-43ca-a8fb-e9e9776b464b complete, cost 278ms 
[INFO ] 2024-07-24 16:48:33.054 - [将测试环境中的M] - load tapTable task 66a0beb6f604e81d788d0661-bd383b5f-77d7-46e1-ac97-f0d1add86f29 complete, cost 320ms 
[INFO ] 2024-07-24 16:48:35.117 - [将测试环境中的M] - load tapTable task 66a0beb6f604e81d788d0661-15133143-1c5e-435c-ba5d-4958b6cfb930 complete, cost 286ms 
[INFO ] 2024-07-24 16:48:38.428 - [将测试环境中的M] - Task initialization... 
[INFO ] 2024-07-24 16:48:38.435 - [将测试环境中的M] - Start task milestones: 66a0beb6f604e81d788d0662(将测试环境中的M) 
[INFO ] 2024-07-24 16:48:38.942 - [将测试环境中的M] - load tapTable task 66a0beb6f604e81d788d0661-4ef92b1e-852c-4b3b-940c-70f17929298b complete, cost 330ms 
[INFO ] 2024-07-24 16:48:38.996 - [将测试环境中的M] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 16:48:39.074 - [将测试环境中的M] - The engine receives 将测试环境中的M task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 16:48:39.074 - [将测试环境中的M][增强JS] - Node 增强JS[e8fa1b2f-4602-4bb0-bcf0-94d376903cbe] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:48:39.075 - [将测试环境中的M][ApiCall] - Node ApiCall[ad472c89-7117-4d9b-aa38-fcb8c127de36] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:48:39.075 - [将测试环境中的M][ApiCall] - Node ApiCall[3ae88074-9a6c-4e92-a0c6-5353d37fc9d6] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:48:39.075 - [将测试环境中的M][增强JS] - Node 增强JS[e8fa1b2f-4602-4bb0-bcf0-94d376903cbe] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:48:39.075 - [将测试环境中的M][ApiCall] - Node ApiCall[3ae88074-9a6c-4e92-a0c6-5353d37fc9d6] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:48:39.075 - [将测试环境中的M][ApiCall] - Node ApiCall[ad472c89-7117-4d9b-aa38-fcb8c127de36] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:48:39.202 - [将测试环境中的M][ApiCall] - Source node "ApiCall" read batch size: 100 
[INFO ] 2024-07-24 16:48:39.204 - [将测试环境中的M][ApiCall] - Source node "ApiCall" event queue capacity: 200 
[INFO ] 2024-07-24 16:48:39.204 - [将测试环境中的M][ApiCall] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 16:48:39.447 - [将测试环境中的M][ApiCall] - batch offset found: {},stream offset found: {"cdcOffset":1721810919,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-24 16:48:39.524 - [将测试环境中的M][ApiCall] - Initial sync started 
[INFO ] 2024-07-24 16:48:39.525 - [将测试环境中的M][ApiCall] - Starting batch read, table name: ApiCall, offset: null 
[INFO ] 2024-07-24 16:48:39.527 - [将测试环境中的M][ApiCall] - Table ApiCall is going to be initial synced 
[INFO ] 2024-07-24 16:48:39.714 - [将测试环境中的M][ApiCall] - Query table 'ApiCall' counts: 868 
[INFO ] 2024-07-24 16:48:39.715 - [将测试环境中的M][ApiCall] - Table [ApiCall] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 16:48:39.920 - [将测试环境中的M][ApiCall] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-24 16:48:40.145 - [将测试环境中的M][ApiCall] - Initial sync completed 
[INFO ] 2024-07-24 16:48:40.145 - [将测试环境中的M][ApiCall] - Incremental sync starting... 
[INFO ] 2024-07-24 16:48:40.145 - [将测试环境中的M][ApiCall] - Initial sync completed 
[INFO ] 2024-07-24 16:48:40.155 - [将测试环境中的M][ApiCall] - Starting stream read, table list: [ApiCall], offset: {"cdcOffset":1721810919,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-24 16:48:40.366 - [将测试环境中的M][ApiCall] - Connector MongoDB incremental start succeed, tables: [ApiCall], data change syncing 
[INFO ] 2024-07-24 21:02:09.215 - [将测试环境中的M][ApiCall] - Incremental sync completed 
[INFO ] 2024-07-24 21:02:09.216 - [将测试环境中的M][ApiCall] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-24 21:02:09.224 - [将测试环境中的M][ApiCall] - java.lang.RuntimeException: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=*************:37017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketReadException: Exception receiving message}, caused by {java.net.SocketException: Connection reset}}] <-- Error Message -->
java.lang.RuntimeException: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=*************:37017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketReadException: Exception receiving message}, caused by {java.net.SocketException: Connection reset}}]

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=*************:37017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketReadException: Exception receiving message}, caused by {java.net.SocketException: Connection reset}}]
	com.mongodb.internal.connection.BaseCluster.getDescription(BaseCluster.java:184)
	com.mongodb.internal.connection.SingleServerCluster.getDescription(SingleServerCluster.java:46)
	com.mongodb.client.internal.ClientSessionBinding.isConnectionSourcePinningRequired(ClientSessionBinding.java:129)
	com.mongodb.client.internal.ClientSessionBinding.getReadConnectionSource(ClientSessionBinding.java:88)
	com.mongodb.internal.operation.OperationHelper.withReadConnectionSource(OperationHelper.java:319)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=*************:37017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketReadException: Exception receiving message}, caused by {java.net.SocketException: Connection reset}}]
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=*************:37017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketReadException: Exception receiving message}, caused by {java.net.SocketException: Connection reset}}]
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:184)
	at io.tapdata.mongodb.MongodbConnector.doStreamRead(MongodbConnector.java:1573)
	at io.tapdata.mongodb.MongodbConnector.streamRead(MongodbConnector.java:1567)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=*************:37017, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketReadException: Exception receiving message}, caused by {java.net.SocketException: Connection reset}}]
	at com.mongodb.internal.connection.BaseCluster.getDescription(BaseCluster.java:184)
	at com.mongodb.internal.connection.SingleServerCluster.getDescription(SingleServerCluster.java:46)
	at com.mongodb.client.internal.ClientSessionBinding.isConnectionSourcePinningRequired(ClientSessionBinding.java:129)
	at com.mongodb.client.internal.ClientSessionBinding.getReadConnectionSource(ClientSessionBinding.java:88)
	at com.mongodb.internal.operation.OperationHelper.withReadConnectionSource(OperationHelper.java:319)
	at com.mongodb.internal.operation.ChangeStreamBatchCursor.resumeableOperation(ChangeStreamBatchCursor.java:198)
	at com.mongodb.internal.operation.ChangeStreamBatchCursor.tryNext(ChangeStreamBatchCursor.java:96)
	at com.mongodb.client.internal.MongoChangeStreamCursorImpl.tryNext(MongoChangeStreamCursorImpl.java:90)
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:149)
	... 20 more

[INFO ] 2024-07-24 21:02:09.232 - [将测试环境中的M][ApiCall] - Job suspend in error handle 
[INFO ] 2024-07-24 21:02:09.427 - [将测试环境中的M][ApiCall] - Node ApiCall[3ae88074-9a6c-4e92-a0c6-5353d37fc9d6] running status set to false 
[INFO ] 2024-07-24 21:02:09.427 - [将测试环境中的M][ApiCall] - PDK connector node stopped: HazelcastSourcePdkDataNode-3ae88074-9a6c-4e92-a0c6-5353d37fc9d6 
[INFO ] 2024-07-24 21:02:09.427 - [将测试环境中的M][ApiCall] - PDK connector node released: HazelcastSourcePdkDataNode-3ae88074-9a6c-4e92-a0c6-5353d37fc9d6 
[INFO ] 2024-07-24 21:02:09.427 - [将测试环境中的M][ApiCall] - Node ApiCall[3ae88074-9a6c-4e92-a0c6-5353d37fc9d6] schema data cleaned 
[INFO ] 2024-07-24 21:02:09.427 - [将测试环境中的M][ApiCall] - Node ApiCall[3ae88074-9a6c-4e92-a0c6-5353d37fc9d6] monitor closed 
[INFO ] 2024-07-24 21:02:09.428 - [将测试环境中的M][ApiCall] - Node ApiCall[3ae88074-9a6c-4e92-a0c6-5353d37fc9d6] close complete, cost 9 ms 
[INFO ] 2024-07-24 21:02:09.428 - [将测试环境中的M][增强JS] - Node 增强JS[e8fa1b2f-4602-4bb0-bcf0-94d376903cbe] running status set to false 
[INFO ] 2024-07-24 21:02:09.430 - [将测试环境中的M][增强JS] - PDK connector node stopped: ScriptExecutor-MiddleMongo184-0ba98826-046b-4d82-900d-61d57976ca9c 
[INFO ] 2024-07-24 21:02:09.430 - [将测试环境中的M][增强JS] - PDK connector node released: ScriptExecutor-MiddleMongo184-0ba98826-046b-4d82-900d-61d57976ca9c 
[INFO ] 2024-07-24 21:02:09.430 - [将测试环境中的M][增强JS] - [ScriptExecutorsManager-66a0beb6f604e81d788d0662-e8fa1b2f-4602-4bb0-bcf0-94d376903cbe-66a0bbeaf604e81d788d05a8] schema data cleaned 
[INFO ] 2024-07-24 21:02:09.435 - [将测试环境中的M][增强JS] - PDK connector node stopped: ScriptExecutor-Mongo-befb036f-e716-4275-b812-09704054612d 
[INFO ] 2024-07-24 21:02:09.437 - [将测试环境中的M][增强JS] - PDK connector node released: ScriptExecutor-Mongo-befb036f-e716-4275-b812-09704054612d 
[INFO ] 2024-07-24 21:02:09.437 - [将测试环境中的M][增强JS] - [ScriptExecutorsManager-66a0beb6f604e81d788d0662-e8fa1b2f-4602-4bb0-bcf0-94d376903cbe-6695b8966d76494ed53f3874] schema data cleaned 
[INFO ] 2024-07-24 21:02:09.465 - [将测试环境中的M][增强JS] - Node 增强JS[e8fa1b2f-4602-4bb0-bcf0-94d376903cbe] schema data cleaned 
[INFO ] 2024-07-24 21:02:09.466 - [将测试环境中的M][增强JS] - Node 增强JS[e8fa1b2f-4602-4bb0-bcf0-94d376903cbe] monitor closed 
[INFO ] 2024-07-24 21:02:09.467 - [将测试环境中的M][增强JS] - Node 增强JS[e8fa1b2f-4602-4bb0-bcf0-94d376903cbe] close complete, cost 38 ms 
[INFO ] 2024-07-24 21:02:09.467 - [将测试环境中的M][ApiCall] - Node ApiCall[ad472c89-7117-4d9b-aa38-fcb8c127de36] running status set to false 
[INFO ] 2024-07-24 21:02:09.479 - [将测试环境中的M][ApiCall] - PDK connector node stopped: HazelcastTargetPdkDataNode-ad472c89-7117-4d9b-aa38-fcb8c127de36 
[INFO ] 2024-07-24 21:02:09.480 - [将测试环境中的M][ApiCall] - PDK connector node released: HazelcastTargetPdkDataNode-ad472c89-7117-4d9b-aa38-fcb8c127de36 
[INFO ] 2024-07-24 21:02:09.486 - [将测试环境中的M][ApiCall] - Node ApiCall[ad472c89-7117-4d9b-aa38-fcb8c127de36] schema data cleaned 
[INFO ] 2024-07-24 21:02:09.486 - [将测试环境中的M][ApiCall] - Node ApiCall[ad472c89-7117-4d9b-aa38-fcb8c127de36] monitor closed 
[INFO ] 2024-07-24 21:02:09.486 - [将测试环境中的M][ApiCall] - Node ApiCall[ad472c89-7117-4d9b-aa38-fcb8c127de36] close complete, cost 14 ms 
[INFO ] 2024-07-24 21:02:11.556 - [将测试环境中的M] - Task [将测试环境中的M] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-24 21:02:11.556 - [将测试环境中的M] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 21:02:11.556 - [将测试环境中的M] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@372eaaa 
[INFO ] 2024-07-24 21:02:11.680 - [将测试环境中的M] - Stop task milestones: 66a0beb6f604e81d788d0662(将测试环境中的M)  
[INFO ] 2024-07-24 21:02:11.689 - [将测试环境中的M] - Stopped task aspect(s) 
[INFO ] 2024-07-24 21:02:11.690 - [将测试环境中的M] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 21:02:11.709 - [将测试环境中的M] - Remove memory task client succeed, task: 将测试环境中的M[66a0beb6f604e81d788d0662] 
[INFO ] 2024-07-24 21:02:11.713 - [将测试环境中的M] - Destroy memory task client cache succeed, task: 将测试环境中的M[66a0beb6f604e81d788d0662] 
