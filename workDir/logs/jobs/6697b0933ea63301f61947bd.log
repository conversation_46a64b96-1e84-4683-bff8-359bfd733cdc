[INFO ] 2024-07-17 19:53:29.633 - [任务 1] - Start task milestones: 6697b0933ea63301f61947bd(任务 1) 
[INFO ] 2024-07-17 19:53:29.634 - [任务 1] - Task initialization... 
[INFO ] 2024-07-17 19:53:30.367 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-17 19:53:30.369 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 19:53:30.649 - [任务 1][POLICY] - Node POLICY[31f4d10a-512f-4767-a916-0287cf364142] start preload schema,table counts: 1 
[INFO ] 2024-07-17 19:53:30.653 - [任务 1][POLICY] - Node POLICY[31f4d10a-512f-4767-a916-0287cf364142] preload schema finished, cost 3 ms 
[INFO ] 2024-07-17 19:53:30.655 - [任务 1][p] - Node p[9eeb7fde-0d99-4ba4-b639-4954ae4c6659] start preload schema,table counts: 1 
[INFO ] 2024-07-17 19:53:30.655 - [任务 1][p] - Node p[9eeb7fde-0d99-4ba4-b639-4954ae4c6659] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 19:53:32.264 - [任务 1][p] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-17 19:53:32.276 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-17 19:53:32.276 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-17 19:53:32.308 - [任务 1][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 19:53:32.435 - [任务 1][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1721217212,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 19:53:32.509 - [任务 1][POLICY] - Initial sync started 
[INFO ] 2024-07-17 19:53:32.516 - [任务 1][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-17 19:53:32.516 - [任务 1][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-17 19:53:32.649 - [任务 1][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-17 19:53:32.654 - [任务 1][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-17 19:53:32.655 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-17 19:53:32.657 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-17 19:53:32.726 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-17 19:53:32.728 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-17 19:53:32.731 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection source enable share cdc: true 
[INFO ] 2024-07-17 19:53:32.762 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 1 enable share cdc: true 
[INFO ] 2024-07-17 19:53:32.762 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自source的共享挖掘任务 
[INFO ] 2024-07-17 19:53:32.796 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav393?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-17 19:53:32.796 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6697b0b866ab5ede8acc2dd2, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697b0733ea63301f61947a0_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1377990793, shareCdcTaskId=6697b0b83ea63301f6194806, connectionId=6697b0733ea63301f61947a0) 
[INFO ] 2024-07-17 19:53:32.843 - [任务 1][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自source的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav393.ExternalStorage_SHARE_CDC_-1377990793', head seq: 0, tail seq: 0 
[INFO ] 2024-07-17 19:53:32.844 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-17 19:53:32.844 - [任务 1][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-17 19:53:32.844 - [任务 1][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-17 19:53:32.847 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-17 19:53:32.847 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-17 19:53:32.857 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6697b0b866ab5ede8acc2dd2, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697b0733ea63301f61947a0_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1377990793, shareCdcTaskId=6697b0b83ea63301f6194806, connectionId=6697b0733ea63301f61947a0) 
[INFO ] 2024-07-17 19:53:32.857 - [任务 1][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自source的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav393.ExternalStorage_SHARE_CDC_-1377990793', head seq: 0, tail seq: 0 
[INFO ] 2024-07-17 19:53:32.863 - [任务 1][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自source的共享挖掘任务_POLICY_任务 1, external storage name: ExternalStorage_SHARE_CDC_-1377990793 
[INFO ] 2024-07-17 19:53:32.863 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-17 19:53:32.869 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-17T11:53:32.276Z): 1 
[INFO ] 2024-07-17 19:53:32.871 - [任务 1][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-17 19:53:32.874 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 1 
[INFO ] 2024-07-17 19:53:32.874 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=1} 
[INFO ] 2024-07-17 19:54:26.833 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=POLICY, timestamp=1721217261000, date=Wed Jul 17 19:54:21 CST 2024, before=Document{{}}, after=Document{{_id=6510f74ca270a1cf5533cef3, CUSTOMER_ID=C000070250, MAX_COVERED=1.0E7, LAST_ANN_PREMIUM_GROSS=1266.0, CAR_MODEL=Ford Fve, POLICY_ID=PC_000000001, COVER_START=Thu Jul 09 09:46:43 CST 2009, LAST_CHANGE=Fri Jul 05 09:13:42 CST 2019}}, op=u, offsetString=gAFkABVvcmcuYnNvbi5Cc29uRG9jdW1lbnQBFAAFX2RhdGEBAgATb3JnLmJzb24uQnNvblN0cmlu
ZwAAAe57ImFycmF5IjpmYWxzZSwiYmluYXJ5IjpmYWxzZSwiYm9vbGVhbiI6ZmFsc2UsImJzb25U
eXBlIjoiU1RSSU5HIiwiZEJQb2ludGVyIjpmYWxzZSwiZGF0ZVRpbWUiOmZhbHNlLCJkZWNpbWFs
MTI4IjpmYWxzZSwiZG9jdW1lbnQiOmZhbHNlLCJkb3VibGUiOmZhbHNlLCJpbnQzMiI6ZmFsc2Us
ImludDY0IjpmYWxzZSwiamF2YVNjcmlwdCI6ZmFsc2UsImphdmFTY3JpcHRXaXRoU2NvcGUiOmZh
bHNlLCJudWxsIjpmYWxzZSwibnVtYmVyIjpmYWxzZSwib2JqZWN0SWQiOmZhbHNlLCJyZWd1bGFy
RXhwcmVzc2lvbiI6ZmFsc2UsInN0cmluZyI6dHJ1ZSwic3ltYm9sIjpmYWxzZSwidGltZXN0YW1w
IjpmYWxzZSwidmFsdWUiOiI4MjY2OTdCMEVEMDAwMDAwMDIyQjAyMkMwMTAwMjk2RTVBMTAwNDY3
QjdGOEM5NUFBMzQ5RDg5MUQyRDk2M0JCRUMzMzhGNDYzQzVGNjk2NDAwM0MzNjM1MzEzMDY2Mzcz
NDYzNjEzMjM3MzA2MTMxNjM2NjM1MzUzMzMzNjM2NTY2MzMwMDAwMDQifag=
, type=DATA, connectionId=6697b0733ea63301f61947a0, isReplaceEvent=false, _ts=1721217262}} 
[INFO ] 2024-07-17 19:54:50.105 - [任务 1][POLICY] - Node POLICY[31f4d10a-512f-4767-a916-0287cf364142] running status set to false 
[INFO ] 2024-07-17 19:54:50.148 - [任务 1][POLICY] - Incremental sync completed 
[INFO ] 2024-07-17 19:54:50.150 - [任务 1][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-31f4d10a-512f-4767-a916-0287cf364142 
[INFO ] 2024-07-17 19:54:50.154 - [任务 1][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-31f4d10a-512f-4767-a916-0287cf364142 
[INFO ] 2024-07-17 19:54:50.155 - [任务 1][POLICY] - Node POLICY[31f4d10a-512f-4767-a916-0287cf364142] schema data cleaned 
[INFO ] 2024-07-17 19:54:50.163 - [任务 1][POLICY] - Node POLICY[31f4d10a-512f-4767-a916-0287cf364142] monitor closed 
[INFO ] 2024-07-17 19:54:50.163 - [任务 1][POLICY] - Node POLICY[31f4d10a-512f-4767-a916-0287cf364142] close complete, cost 59 ms 
[INFO ] 2024-07-17 19:54:50.288 - [任务 1][p] - Node p[9eeb7fde-0d99-4ba4-b639-4954ae4c6659] running status set to false 
[INFO ] 2024-07-17 19:54:50.289 - [任务 1][p] - PDK connector node stopped: HazelcastTargetPdkDataNode-9eeb7fde-0d99-4ba4-b639-4954ae4c6659 
[INFO ] 2024-07-17 19:54:50.290 - [任务 1][p] - PDK connector node released: HazelcastTargetPdkDataNode-9eeb7fde-0d99-4ba4-b639-4954ae4c6659 
[INFO ] 2024-07-17 19:54:50.290 - [任务 1][p] - Node p[9eeb7fde-0d99-4ba4-b639-4954ae4c6659] schema data cleaned 
[INFO ] 2024-07-17 19:54:50.292 - [任务 1][p] - Node p[9eeb7fde-0d99-4ba4-b639-4954ae4c6659] monitor closed 
[INFO ] 2024-07-17 19:54:50.292 - [任务 1][p] - Node p[9eeb7fde-0d99-4ba4-b639-4954ae4c6659] close complete, cost 128 ms 
[INFO ] 2024-07-17 19:54:51.489 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 19:54:51.500 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6aeb5fdb 
[INFO ] 2024-07-17 19:54:51.500 - [任务 1] - Stop task milestones: 6697b0933ea63301f61947bd(任务 1)  
[INFO ] 2024-07-17 19:54:51.630 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-17 19:54:51.630 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 19:54:51.717 - [任务 1] - Remove memory task client succeed, task: 任务 1[6697b0933ea63301f61947bd] 
[INFO ] 2024-07-17 19:54:51.718 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6697b0933ea63301f61947bd] 
