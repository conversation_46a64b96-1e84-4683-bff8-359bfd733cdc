[INFO ] 2024-09-12 11:33:58.873 - [任务 3(101)] - 66e260f398613b484ab1c769 task start 
[INFO ] 2024-09-12 11:33:58.997 - [任务 3(101)][6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe] - Node 6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe[6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe] start preload schema,table counts: 0 
[INFO ] 2024-09-12 11:33:59.001 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] start preload schema,table counts: 1 
[INFO ] 2024-09-12 11:33:59.001 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] start preload schema,table counts: 1 
[INFO ] 2024-09-12 11:33:59.001 - [任务 3(101)][6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe] - Node 6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe[6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe] preload schema finished, cost 0 ms 
[INFO ] 2024-09-12 11:33:59.195 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] preload schema finished, cost 188 ms 
[INFO ] 2024-09-12 11:33:59.195 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] preload schema finished, cost 185 ms 
[INFO ] 2024-09-12 11:33:59.196 - [任务 3(101)][增强JS] - Node migrate_js_processor(增强JS: cf430bd6-9d32-4e41-b871-9253eec1ba67) enable batch process 
[INFO ] 2024-09-12 11:33:59.536 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] running status set to false 
[INFO ] 2024-09-12 11:33:59.591 - [任务 3(101)][Master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-308972b2-b27c-4ab1-a02d-1b459eae0df7 
[INFO ] 2024-09-12 11:33:59.591 - [任务 3(101)][Master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-308972b2-b27c-4ab1-a02d-1b459eae0df7 
[INFO ] 2024-09-12 11:33:59.591 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] schema data cleaned 
[INFO ] 2024-09-12 11:33:59.591 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] monitor closed 
[INFO ] 2024-09-12 11:33:59.592 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] close complete, cost 61 ms 
[INFO ] 2024-09-12 11:34:00.213 - [任务 3(101)][增强JS] - Thu Sep 12 11:34:00 CST 2024 
[INFO ] 2024-09-12 11:34:00.224 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] running status set to false 
[INFO ] 2024-09-12 11:34:00.225 - [任务 3(101)][6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe] - Node 6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe[6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe] running status set to false 
[INFO ] 2024-09-12 11:34:00.255 - [任务 3(101)][6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe] - Node 6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe[6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe] schema data cleaned 
[INFO ] 2024-09-12 11:34:00.255 - [任务 3(101)][6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe] - Node 6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe[6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe] monitor closed 
[INFO ] 2024-09-12 11:34:00.256 - [任务 3(101)][6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe] - Node 6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe[6c4a4ad4-a8e5-430f-b3c2-878dfb55d4fe] close complete, cost 37 ms 
[INFO ] 2024-09-12 11:34:00.264 - [任务 3(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Master-e844106b-d149-4267-a4f0-87ad485f617e 
[INFO ] 2024-09-12 11:34:00.265 - [任务 3(101)][增强JS] - PDK connector node released: ScriptExecutor-Master-e844106b-d149-4267-a4f0-87ad485f617e 
[INFO ] 2024-09-12 11:34:00.265 - [任务 3(101)][增强JS] - [ScriptExecutorsManager-66e260f398613b484ab1c769-cf430bd6-9d32-4e41-b871-9253eec1ba67-66e1667ac048f9707558d481] schema data cleaned 
[INFO ] 2024-09-12 11:34:00.268 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] schema data cleaned 
[INFO ] 2024-09-12 11:34:00.268 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] monitor closed 
[INFO ] 2024-09-12 11:34:00.301 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] close complete, cost 52 ms 
[INFO ] 2024-09-12 11:34:00.302 - [任务 3(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-12 11:34:00.302 - [任务 3(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-12 11:34:00.302 - [任务 3(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-12 11:34:00.303 - [任务 3(101)] - test run task 66e260f398613b484ab1c769 complete, cost 1464ms 
[INFO ] 2024-09-12 11:35:43.731 - [任务 3(101)] - 66e260f398613b484ab1c769 task start 
[INFO ] 2024-09-12 11:35:43.801 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] start preload schema,table counts: 1 
[INFO ] 2024-09-12 11:35:43.802 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] start preload schema,table counts: 1 
[INFO ] 2024-09-12 11:35:43.804 - [任务 3(101)][c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9] - Node c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9[c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9] start preload schema,table counts: 0 
[INFO ] 2024-09-12 11:35:43.809 - [任务 3(101)][c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9] - Node c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9[c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9] preload schema finished, cost 0 ms 
[INFO ] 2024-09-12 11:35:43.834 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] preload schema finished, cost 36 ms 
[INFO ] 2024-09-12 11:35:43.835 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] preload schema finished, cost 36 ms 
[INFO ] 2024-09-12 11:35:43.835 - [任务 3(101)][增强JS] - Node migrate_js_processor(增强JS: cf430bd6-9d32-4e41-b871-9253eec1ba67) enable batch process 
[INFO ] 2024-09-12 11:35:44.180 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] running status set to false 
[INFO ] 2024-09-12 11:35:44.181 - [任务 3(101)][Master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-308972b2-b27c-4ab1-a02d-1b459eae0df7 
[INFO ] 2024-09-12 11:35:44.181 - [任务 3(101)][Master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-308972b2-b27c-4ab1-a02d-1b459eae0df7 
[INFO ] 2024-09-12 11:35:44.181 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] schema data cleaned 
[INFO ] 2024-09-12 11:35:44.181 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] monitor closed 
[INFO ] 2024-09-12 11:35:44.362 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] close complete, cost 49 ms 
[INFO ] 2024-09-12 11:35:44.666 - [任务 3(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (parse) on java.util.Date failed due to: Arity error - expected: 1 actual: 2 
[ERROR] 2024-09-12 11:35:44.820 - [任务 3(101)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (parse) on java.util.Date failed due to: Arity error - expected: 1 actual: 2 <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (parse) on java.util.Date failed due to: Arity error - expected: 1 actual: 2

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (parse) on java.util.Date failed due to: Arity error - expected: 1 actual: 2
	<js>.process(<eval>:3)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (parse) on java.util.Date failed due to: Arity error - expected: 1 actual: 2
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.DGG.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (parse) on java.util.Date failed due to: Arity error - expected: 1 actual: 2
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (parse) on java.util.Date failed due to: Arity error - expected: 1 actual: 2
	at <js>.process(<eval>:3)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 2 more

[INFO ] 2024-09-12 11:35:47.206 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] running status set to false 
[INFO ] 2024-09-12 11:35:47.214 - [任务 3(101)][c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9] - Node c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9[c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9] running status set to false 
[INFO ] 2024-09-12 11:35:47.215 - [任务 3(101)][c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9] - Node c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9[c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9] schema data cleaned 
[INFO ] 2024-09-12 11:35:47.215 - [任务 3(101)][c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9] - Node c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9[c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9] monitor closed 
[INFO ] 2024-09-12 11:35:47.215 - [任务 3(101)][c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9] - Node c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9[c7c2b581-a80f-48c5-a2a9-0d3ace81e0f9] close complete, cost 4 ms 
[INFO ] 2024-09-12 11:35:47.278 - [任务 3(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Master-ef2de933-e2b2-4aa9-ba06-e5fef112580c 
[INFO ] 2024-09-12 11:35:47.278 - [任务 3(101)][增强JS] - PDK connector node released: ScriptExecutor-Master-ef2de933-e2b2-4aa9-ba06-e5fef112580c 
[INFO ] 2024-09-12 11:35:47.278 - [任务 3(101)][增强JS] - [ScriptExecutorsManager-66e260f398613b484ab1c769-cf430bd6-9d32-4e41-b871-9253eec1ba67-66e1667ac048f9707558d481] schema data cleaned 
[INFO ] 2024-09-12 11:35:47.286 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] schema data cleaned 
[INFO ] 2024-09-12 11:35:47.286 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] monitor closed 
[INFO ] 2024-09-12 11:35:47.287 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] close complete, cost 86 ms 
[INFO ] 2024-09-12 11:35:47.324 - [任务 3(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-12 11:35:47.324 - [任务 3(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-12 11:35:47.324 - [任务 3(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-12 11:35:47.331 - [任务 3(101)] - test run task 66e260f398613b484ab1c769 complete, cost 3613ms 
[INFO ] 2024-09-12 11:36:30.651 - [任务 3(101)] - 66e260f398613b484ab1c769 task start 
[INFO ] 2024-09-12 11:36:30.654 - [任务 3(101)][768c6660-ad68-4b02-8ffc-edfe29a83b47] - Node 768c6660-ad68-4b02-8ffc-edfe29a83b47[768c6660-ad68-4b02-8ffc-edfe29a83b47] start preload schema,table counts: 0 
[INFO ] 2024-09-12 11:36:30.654 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] start preload schema,table counts: 1 
[INFO ] 2024-09-12 11:36:30.654 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] start preload schema,table counts: 1 
[INFO ] 2024-09-12 11:36:30.654 - [任务 3(101)][768c6660-ad68-4b02-8ffc-edfe29a83b47] - Node 768c6660-ad68-4b02-8ffc-edfe29a83b47[768c6660-ad68-4b02-8ffc-edfe29a83b47] preload schema finished, cost 0 ms 
[INFO ] 2024-09-12 11:36:30.685 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] preload schema finished, cost 29 ms 
[INFO ] 2024-09-12 11:36:30.686 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] preload schema finished, cost 29 ms 
[INFO ] 2024-09-12 11:36:30.686 - [任务 3(101)][增强JS] - Node migrate_js_processor(增强JS: cf430bd6-9d32-4e41-b871-9253eec1ba67) enable batch process 
[INFO ] 2024-09-12 11:36:31.059 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] running status set to false 
[INFO ] 2024-09-12 11:36:31.115 - [任务 3(101)][Master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-308972b2-b27c-4ab1-a02d-1b459eae0df7 
[INFO ] 2024-09-12 11:36:31.115 - [任务 3(101)][Master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-308972b2-b27c-4ab1-a02d-1b459eae0df7 
[INFO ] 2024-09-12 11:36:31.116 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] schema data cleaned 
[INFO ] 2024-09-12 11:36:31.116 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] monitor closed 
[INFO ] 2024-09-12 11:36:31.322 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] close complete, cost 59 ms 
[INFO ] 2024-09-12 11:36:31.537 - [任务 3(101)][增强JS] - Thu Sep 12 11:36:31 CST 2024 
[INFO ] 2024-09-12 11:36:31.539 - [任务 3(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (ifno) on io.DGG.flow.engine.V2.script.ObsScriptLogger@1337c9a7 failed due to: Unknown identifier: ifno 
[ERROR] 2024-09-12 11:36:31.740 - [任务 3(101)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (ifno) on io.DGG.flow.engine.V2.script.ObsScriptLogger@1337c9a7 failed due to: Unknown identifier: ifno <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (ifno) on io.DGG.flow.engine.V2.script.ObsScriptLogger@1337c9a7 failed due to: Unknown identifier: ifno

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (ifno) on io.DGG.flow.engine.V2.script.ObsScriptLogger@1337c9a7 failed due to: Unknown identifier: ifno
	<js>.process(<eval>:5)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (ifno) on io.DGG.flow.engine.V2.script.ObsScriptLogger@1337c9a7 failed due to: Unknown identifier: ifno
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.DGG.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (ifno) on io.DGG.flow.engine.V2.script.ObsScriptLogger@1337c9a7 failed due to: Unknown identifier: ifno
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (ifno) on io.DGG.flow.engine.V2.script.ObsScriptLogger@1337c9a7 failed due to: Unknown identifier: ifno
	at <js>.process(<eval>:5)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 2 more

[INFO ] 2024-09-12 11:36:34.092 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] running status set to false 
[INFO ] 2024-09-12 11:36:34.093 - [任务 3(101)][768c6660-ad68-4b02-8ffc-edfe29a83b47] - Node 768c6660-ad68-4b02-8ffc-edfe29a83b47[768c6660-ad68-4b02-8ffc-edfe29a83b47] running status set to false 
[INFO ] 2024-09-12 11:36:34.094 - [任务 3(101)][768c6660-ad68-4b02-8ffc-edfe29a83b47] - Node 768c6660-ad68-4b02-8ffc-edfe29a83b47[768c6660-ad68-4b02-8ffc-edfe29a83b47] schema data cleaned 
[INFO ] 2024-09-12 11:36:34.100 - [任务 3(101)][768c6660-ad68-4b02-8ffc-edfe29a83b47] - Node 768c6660-ad68-4b02-8ffc-edfe29a83b47[768c6660-ad68-4b02-8ffc-edfe29a83b47] monitor closed 
[INFO ] 2024-09-12 11:36:34.101 - [任务 3(101)][768c6660-ad68-4b02-8ffc-edfe29a83b47] - Node 768c6660-ad68-4b02-8ffc-edfe29a83b47[768c6660-ad68-4b02-8ffc-edfe29a83b47] close complete, cost 7 ms 
[INFO ] 2024-09-12 11:36:34.143 - [任务 3(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Master-07f7e132-d309-4055-9de5-fa721073dac2 
[INFO ] 2024-09-12 11:36:34.144 - [任务 3(101)][增强JS] - PDK connector node released: ScriptExecutor-Master-07f7e132-d309-4055-9de5-fa721073dac2 
[INFO ] 2024-09-12 11:36:34.144 - [任务 3(101)][增强JS] - [ScriptExecutorsManager-66e260f398613b484ab1c769-cf430bd6-9d32-4e41-b871-9253eec1ba67-66e1667ac048f9707558d481] schema data cleaned 
[INFO ] 2024-09-12 11:36:34.145 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] schema data cleaned 
[INFO ] 2024-09-12 11:36:34.146 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] monitor closed 
[INFO ] 2024-09-12 11:36:34.146 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] close complete, cost 58 ms 
[INFO ] 2024-09-12 11:36:34.158 - [任务 3(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-12 11:36:34.158 - [任务 3(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-12 11:36:34.158 - [任务 3(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-12 11:36:34.159 - [任务 3(101)] - test run task 66e260f398613b484ab1c769 complete, cost 3615ms 
[INFO ] 2024-09-12 11:46:05.910 - [任务 3(101)] - 66e260f398613b484ab1c769 task start 
[INFO ] 2024-09-12 11:46:06.094 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] start preload schema,table counts: 1 
[INFO ] 2024-09-12 11:46:06.094 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] start preload schema,table counts: 1 
[INFO ] 2024-09-12 11:46:06.095 - [任务 3(101)][b9e14a32-9f60-4001-a1af-22623236eddf] - Node b9e14a32-9f60-4001-a1af-22623236eddf[b9e14a32-9f60-4001-a1af-22623236eddf] start preload schema,table counts: 0 
[INFO ] 2024-09-12 11:46:06.096 - [任务 3(101)][b9e14a32-9f60-4001-a1af-22623236eddf] - Node b9e14a32-9f60-4001-a1af-22623236eddf[b9e14a32-9f60-4001-a1af-22623236eddf] preload schema finished, cost 0 ms 
[INFO ] 2024-09-12 11:46:06.129 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] preload schema finished, cost 35 ms 
[INFO ] 2024-09-12 11:46:06.129 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] preload schema finished, cost 35 ms 
[INFO ] 2024-09-12 11:46:06.130 - [任务 3(101)][增强JS] - Node migrate_js_processor(增强JS: cf430bd6-9d32-4e41-b871-9253eec1ba67) enable batch process 
[INFO ] 2024-09-12 11:46:06.485 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] running status set to false 
[INFO ] 2024-09-12 11:46:06.533 - [任务 3(101)][Master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-308972b2-b27c-4ab1-a02d-1b459eae0df7 
[INFO ] 2024-09-12 11:46:06.533 - [任务 3(101)][Master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-308972b2-b27c-4ab1-a02d-1b459eae0df7 
[INFO ] 2024-09-12 11:46:06.533 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] schema data cleaned 
[INFO ] 2024-09-12 11:46:06.534 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] monitor closed 
[INFO ] 2024-09-12 11:46:06.537 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] close complete, cost 61 ms 
[INFO ] 2024-09-12 11:46:06.984 - [任务 3(101)][增强JS] - Thu Sep 12 11:46:06 CST 2024 
[INFO ] 2024-09-12 11:46:06.985 - [任务 3(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (ifno) on io.DGG.flow.engine.V2.script.ObsScriptLogger@6df294b4 failed due to: Unknown identifier: ifno 
[ERROR] 2024-09-12 11:46:07.186 - [任务 3(101)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (ifno) on io.DGG.flow.engine.V2.script.ObsScriptLogger@6df294b4 failed due to: Unknown identifier: ifno <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (ifno) on io.DGG.flow.engine.V2.script.ObsScriptLogger@6df294b4 failed due to: Unknown identifier: ifno

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (ifno) on io.DGG.flow.engine.V2.script.ObsScriptLogger@6df294b4 failed due to: Unknown identifier: ifno
	<js>.process(<eval>:5)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (ifno) on io.DGG.flow.engine.V2.script.ObsScriptLogger@6df294b4 failed due to: Unknown identifier: ifno
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.DGG.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (ifno) on io.DGG.flow.engine.V2.script.ObsScriptLogger@6df294b4 failed due to: Unknown identifier: ifno
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (ifno) on io.DGG.flow.engine.V2.script.ObsScriptLogger@6df294b4 failed due to: Unknown identifier: ifno
	at <js>.process(<eval>:5)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 2 more

[INFO ] 2024-09-12 11:46:09.542 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] running status set to false 
[INFO ] 2024-09-12 11:46:09.542 - [任务 3(101)][b9e14a32-9f60-4001-a1af-22623236eddf] - Node b9e14a32-9f60-4001-a1af-22623236eddf[b9e14a32-9f60-4001-a1af-22623236eddf] running status set to false 
[INFO ] 2024-09-12 11:46:09.543 - [任务 3(101)][b9e14a32-9f60-4001-a1af-22623236eddf] - Node b9e14a32-9f60-4001-a1af-22623236eddf[b9e14a32-9f60-4001-a1af-22623236eddf] schema data cleaned 
[INFO ] 2024-09-12 11:46:09.543 - [任务 3(101)][b9e14a32-9f60-4001-a1af-22623236eddf] - Node b9e14a32-9f60-4001-a1af-22623236eddf[b9e14a32-9f60-4001-a1af-22623236eddf] monitor closed 
[INFO ] 2024-09-12 11:46:09.594 - [任务 3(101)][b9e14a32-9f60-4001-a1af-22623236eddf] - Node b9e14a32-9f60-4001-a1af-22623236eddf[b9e14a32-9f60-4001-a1af-22623236eddf] close complete, cost 5 ms 
[INFO ] 2024-09-12 11:46:09.595 - [任务 3(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Master-c12e182f-a5bc-4374-8e1d-95dab039bc08 
[INFO ] 2024-09-12 11:46:09.596 - [任务 3(101)][增强JS] - PDK connector node released: ScriptExecutor-Master-c12e182f-a5bc-4374-8e1d-95dab039bc08 
[INFO ] 2024-09-12 11:46:09.596 - [任务 3(101)][增强JS] - [ScriptExecutorsManager-66e260f398613b484ab1c769-cf430bd6-9d32-4e41-b871-9253eec1ba67-66e1667ac048f9707558d481] schema data cleaned 
[INFO ] 2024-09-12 11:46:09.599 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] schema data cleaned 
[INFO ] 2024-09-12 11:46:09.599 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] monitor closed 
[INFO ] 2024-09-12 11:46:09.608 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] close complete, cost 69 ms 
[INFO ] 2024-09-12 11:46:09.608 - [任务 3(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-12 11:46:09.608 - [任务 3(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-12 11:46:09.609 - [任务 3(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-12 11:46:09.609 - [任务 3(101)] - test run task 66e260f398613b484ab1c769 complete, cost 3720ms 
[INFO ] 2024-09-12 11:46:16.307 - [任务 3(101)] - 66e260f398613b484ab1c769 task start 
[INFO ] 2024-09-12 11:46:16.309 - [任务 3(101)][5adcc4f4-ad2d-4259-990b-6f24e42acffb] - Node 5adcc4f4-ad2d-4259-990b-6f24e42acffb[5adcc4f4-ad2d-4259-990b-6f24e42acffb] start preload schema,table counts: 0 
[INFO ] 2024-09-12 11:46:16.309 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] start preload schema,table counts: 1 
[INFO ] 2024-09-12 11:46:16.309 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] start preload schema,table counts: 1 
[INFO ] 2024-09-12 11:46:16.309 - [任务 3(101)][5adcc4f4-ad2d-4259-990b-6f24e42acffb] - Node 5adcc4f4-ad2d-4259-990b-6f24e42acffb[5adcc4f4-ad2d-4259-990b-6f24e42acffb] preload schema finished, cost 0 ms 
[INFO ] 2024-09-12 11:46:16.336 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] preload schema finished, cost 21 ms 
[INFO ] 2024-09-12 11:46:16.338 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] preload schema finished, cost 21 ms 
[INFO ] 2024-09-12 11:46:16.338 - [任务 3(101)][增强JS] - Node migrate_js_processor(增强JS: cf430bd6-9d32-4e41-b871-9253eec1ba67) enable batch process 
[INFO ] 2024-09-12 11:46:16.952 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] running status set to false 
[INFO ] 2024-09-12 11:46:16.953 - [任务 3(101)][Master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-308972b2-b27c-4ab1-a02d-1b459eae0df7 
[INFO ] 2024-09-12 11:46:16.953 - [任务 3(101)][Master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-308972b2-b27c-4ab1-a02d-1b459eae0df7 
[INFO ] 2024-09-12 11:46:16.953 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] schema data cleaned 
[INFO ] 2024-09-12 11:46:16.954 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] monitor closed 
[INFO ] 2024-09-12 11:46:16.955 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] close complete, cost 75 ms 
[INFO ] 2024-09-12 11:46:17.433 - [任务 3(101)][增强JS] - Thu Sep 12 11:46:17 CST 2024 
[INFO ] 2024-09-12 11:46:17.436 - [任务 3(101)][增强JS] - Thu Sep 12 11:46:17 CST 2024 
[INFO ] 2024-09-12 11:46:17.437 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] running status set to false 
[INFO ] 2024-09-12 11:46:17.438 - [任务 3(101)][5adcc4f4-ad2d-4259-990b-6f24e42acffb] - Node 5adcc4f4-ad2d-4259-990b-6f24e42acffb[5adcc4f4-ad2d-4259-990b-6f24e42acffb] running status set to false 
[INFO ] 2024-09-12 11:46:17.438 - [任务 3(101)][5adcc4f4-ad2d-4259-990b-6f24e42acffb] - Node 5adcc4f4-ad2d-4259-990b-6f24e42acffb[5adcc4f4-ad2d-4259-990b-6f24e42acffb] schema data cleaned 
[INFO ] 2024-09-12 11:46:17.438 - [任务 3(101)][5adcc4f4-ad2d-4259-990b-6f24e42acffb] - Node 5adcc4f4-ad2d-4259-990b-6f24e42acffb[5adcc4f4-ad2d-4259-990b-6f24e42acffb] monitor closed 
[INFO ] 2024-09-12 11:46:17.469 - [任务 3(101)][5adcc4f4-ad2d-4259-990b-6f24e42acffb] - Node 5adcc4f4-ad2d-4259-990b-6f24e42acffb[5adcc4f4-ad2d-4259-990b-6f24e42acffb] close complete, cost 0 ms 
[INFO ] 2024-09-12 11:46:17.469 - [任务 3(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Master-6b732458-8ab8-484c-a73d-7a6de1400703 
[INFO ] 2024-09-12 11:46:17.469 - [任务 3(101)][增强JS] - PDK connector node released: ScriptExecutor-Master-6b732458-8ab8-484c-a73d-7a6de1400703 
[INFO ] 2024-09-12 11:46:17.471 - [任务 3(101)][增强JS] - [ScriptExecutorsManager-66e260f398613b484ab1c769-cf430bd6-9d32-4e41-b871-9253eec1ba67-66e1667ac048f9707558d481] schema data cleaned 
[INFO ] 2024-09-12 11:46:17.474 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] schema data cleaned 
[INFO ] 2024-09-12 11:46:17.474 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] monitor closed 
[INFO ] 2024-09-12 11:46:17.484 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] close complete, cost 38 ms 
[INFO ] 2024-09-12 11:46:17.484 - [任务 3(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-12 11:46:17.484 - [任务 3(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-12 11:46:17.484 - [任务 3(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-12 11:46:17.485 - [任务 3(101)] - test run task 66e260f398613b484ab1c769 complete, cost 1271ms 
[INFO ] 2024-09-12 11:46:33.062 - [任务 3(101)] - 66e260f398613b484ab1c769 task start 
[INFO ] 2024-09-12 11:46:33.148 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] start preload schema,table counts: 1 
[INFO ] 2024-09-12 11:46:33.149 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] start preload schema,table counts: 1 
[INFO ] 2024-09-12 11:46:33.149 - [任务 3(101)][19f7681f-9a32-4673-904c-26b23870a51b] - Node 19f7681f-9a32-4673-904c-26b23870a51b[19f7681f-9a32-4673-904c-26b23870a51b] start preload schema,table counts: 0 
[INFO ] 2024-09-12 11:46:33.149 - [任务 3(101)][19f7681f-9a32-4673-904c-26b23870a51b] - Node 19f7681f-9a32-4673-904c-26b23870a51b[19f7681f-9a32-4673-904c-26b23870a51b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-12 11:46:33.163 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] preload schema finished, cost 13 ms 
[INFO ] 2024-09-12 11:46:33.163 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] preload schema finished, cost 13 ms 
[INFO ] 2024-09-12 11:46:33.163 - [任务 3(101)][增强JS] - Node migrate_js_processor(增强JS: cf430bd6-9d32-4e41-b871-9253eec1ba67) enable batch process 
[INFO ] 2024-09-12 11:46:33.761 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] running status set to false 
[INFO ] 2024-09-12 11:46:33.848 - [任务 3(101)][Master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-308972b2-b27c-4ab1-a02d-1b459eae0df7 
[INFO ] 2024-09-12 11:46:33.849 - [任务 3(101)][Master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-308972b2-b27c-4ab1-a02d-1b459eae0df7 
[INFO ] 2024-09-12 11:46:33.849 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] schema data cleaned 
[INFO ] 2024-09-12 11:46:33.849 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] monitor closed 
[INFO ] 2024-09-12 11:46:33.853 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] close complete, cost 108 ms 
[INFO ] 2024-09-12 11:46:34.227 - [任务 3(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: getTime is not defined 
[ERROR] 2024-09-12 11:46:34.228 - [任务 3(101)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: getTime is not defined <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: getTime is not defined

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: getTime is not defined
	<js>.process(<eval>:4)
	org.graalvm.polyglot.Value.execute(Value.java:841)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: getTime is not defined
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:278)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:294)
	at io.DGG.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:271)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:255)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: ReferenceError: getTime is not defined
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:558)
	at io.DGG.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$tryProcess$3(HazelcastJavaScriptProcessorNode.java:265)
	... 1 more
Caused by: org.graalvm.polyglot.PolyglotException: ReferenceError: getTime is not defined
	at <js>.process(<eval>:4)
	at org.graalvm.polyglot.Value.execute(Value.java:841)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.invokeFunction(GraalJSScriptEngine.java:556)
	... 2 more

[INFO ] 2024-09-12 11:46:36.763 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] running status set to false 
[INFO ] 2024-09-12 11:46:36.764 - [任务 3(101)][19f7681f-9a32-4673-904c-26b23870a51b] - Node 19f7681f-9a32-4673-904c-26b23870a51b[19f7681f-9a32-4673-904c-26b23870a51b] running status set to false 
[INFO ] 2024-09-12 11:46:36.767 - [任务 3(101)][19f7681f-9a32-4673-904c-26b23870a51b] - Node 19f7681f-9a32-4673-904c-26b23870a51b[19f7681f-9a32-4673-904c-26b23870a51b] schema data cleaned 
[INFO ] 2024-09-12 11:46:36.767 - [任务 3(101)][19f7681f-9a32-4673-904c-26b23870a51b] - Node 19f7681f-9a32-4673-904c-26b23870a51b[19f7681f-9a32-4673-904c-26b23870a51b] monitor closed 
[INFO ] 2024-09-12 11:46:36.767 - [任务 3(101)][19f7681f-9a32-4673-904c-26b23870a51b] - Node 19f7681f-9a32-4673-904c-26b23870a51b[19f7681f-9a32-4673-904c-26b23870a51b] close complete, cost 8 ms 
[INFO ] 2024-09-12 11:46:36.847 - [任务 3(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Master-d2a4eb80-a4a5-48d7-9430-4460452bc2f0 
[INFO ] 2024-09-12 11:46:36.847 - [任务 3(101)][增强JS] - PDK connector node released: ScriptExecutor-Master-d2a4eb80-a4a5-48d7-9430-4460452bc2f0 
[INFO ] 2024-09-12 11:46:36.848 - [任务 3(101)][增强JS] - [ScriptExecutorsManager-66e260f398613b484ab1c769-cf430bd6-9d32-4e41-b871-9253eec1ba67-66e1667ac048f9707558d481] schema data cleaned 
[INFO ] 2024-09-12 11:46:36.855 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] schema data cleaned 
[INFO ] 2024-09-12 11:46:36.855 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] monitor closed 
[INFO ] 2024-09-12 11:46:36.856 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] close complete, cost 102 ms 
[INFO ] 2024-09-12 11:46:36.864 - [任务 3(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-12 11:46:36.864 - [任务 3(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-12 11:46:36.864 - [任务 3(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-12 11:46:37.070 - [任务 3(101)] - test run task 66e260f398613b484ab1c769 complete, cost 3812ms 
[INFO ] 2024-09-12 11:46:42.440 - [任务 3(101)] - 66e260f398613b484ab1c769 task start 
[INFO ] 2024-09-12 11:46:42.533 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] start preload schema,table counts: 1 
[INFO ] 2024-09-12 11:46:42.537 - [任务 3(101)][16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e] - Node 16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e[16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e] start preload schema,table counts: 0 
[INFO ] 2024-09-12 11:46:42.537 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] start preload schema,table counts: 1 
[INFO ] 2024-09-12 11:46:42.537 - [任务 3(101)][16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e] - Node 16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e[16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e] preload schema finished, cost 0 ms 
[INFO ] 2024-09-12 11:46:42.574 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] preload schema finished, cost 35 ms 
[INFO ] 2024-09-12 11:46:42.575 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] preload schema finished, cost 35 ms 
[INFO ] 2024-09-12 11:46:42.577 - [任务 3(101)][增强JS] - Node migrate_js_processor(增强JS: cf430bd6-9d32-4e41-b871-9253eec1ba67) enable batch process 
[INFO ] 2024-09-12 11:46:42.924 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] running status set to false 
[INFO ] 2024-09-12 11:46:42.972 - [任务 3(101)][Master] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-308972b2-b27c-4ab1-a02d-1b459eae0df7 
[INFO ] 2024-09-12 11:46:42.972 - [任务 3(101)][Master] - PDK connector node released: HazelcastSampleSourcePdkDataNode-308972b2-b27c-4ab1-a02d-1b459eae0df7 
[INFO ] 2024-09-12 11:46:42.973 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] schema data cleaned 
[INFO ] 2024-09-12 11:46:42.973 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] monitor closed 
[INFO ] 2024-09-12 11:46:42.974 - [任务 3(101)][Master] - Node Master[308972b2-b27c-4ab1-a02d-1b459eae0df7] close complete, cost 59 ms 
[INFO ] 2024-09-12 11:46:43.459 - [任务 3(101)][增强JS] - 1726112803447 
[INFO ] 2024-09-12 11:46:43.459 - [任务 3(101)][增强JS] - Thu Sep 12 11:46:43 CST 2024 
[INFO ] 2024-09-12 11:46:43.466 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] running status set to false 
[INFO ] 2024-09-12 11:46:43.467 - [任务 3(101)][16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e] - Node 16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e[16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e] running status set to false 
[INFO ] 2024-09-12 11:46:43.467 - [任务 3(101)][16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e] - Node 16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e[16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e] schema data cleaned 
[INFO ] 2024-09-12 11:46:43.467 - [任务 3(101)][16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e] - Node 16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e[16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e] monitor closed 
[INFO ] 2024-09-12 11:46:43.492 - [任务 3(101)][16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e] - Node 16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e[16440d3e-15ed-4bb8-b50f-a9bd5c8a1e8e] close complete, cost 0 ms 
[INFO ] 2024-09-12 11:46:43.493 - [任务 3(101)][增强JS] - PDK connector node stopped: ScriptExecutor-Master-ff071266-080b-4577-87c6-bbe0ae687184 
[INFO ] 2024-09-12 11:46:43.493 - [任务 3(101)][增强JS] - PDK connector node released: ScriptExecutor-Master-ff071266-080b-4577-87c6-bbe0ae687184 
[INFO ] 2024-09-12 11:46:43.493 - [任务 3(101)][增强JS] - [ScriptExecutorsManager-66e260f398613b484ab1c769-cf430bd6-9d32-4e41-b871-9253eec1ba67-66e1667ac048f9707558d481] schema data cleaned 
[INFO ] 2024-09-12 11:46:43.494 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] schema data cleaned 
[INFO ] 2024-09-12 11:46:43.494 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] monitor closed 
[INFO ] 2024-09-12 11:46:43.504 - [任务 3(101)][增强JS] - Node 增强JS[cf430bd6-9d32-4e41-b871-9253eec1ba67] close complete, cost 31 ms 
[INFO ] 2024-09-12 11:46:43.504 - [任务 3(101)] - Closed task monitor(s)
null 
[INFO ] 2024-09-12 11:46:43.504 - [任务 3(101)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-12 11:46:43.505 - [任务 3(101)] - Stopped task aspect(s) 
[INFO ] 2024-09-12 11:46:43.505 - [任务 3(101)] - test run task 66e260f398613b484ab1c769 complete, cost 1109ms 
