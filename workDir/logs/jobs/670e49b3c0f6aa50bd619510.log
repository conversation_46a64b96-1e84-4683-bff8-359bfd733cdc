[INFO ] 2024-10-15 18:54:16.905 - [任务 2] - Task initialization... 
[INFO ] 2024-10-15 18:54:16.907 - [任务 2] - Start task milestones: 670e49b3c0f6aa50bd619510(任务 2) 
[INFO ] 2024-10-15 18:54:17.117 - [任务 2] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-10-15 18:54:17.156 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-15 18:54:17.223 - [任务 2][testPlatForm] - Node testPlatForm[9940f3a6-1f11-4b92-9e5a-c9b1a3cebe63] start preload schema,table counts: 1 
[INFO ] 2024-10-15 18:54:17.223 - [任务 2][test_platform] - Node test_platform[8802dbd4-1922-4fbd-a813-4fde483e1fe1] start preload schema,table counts: 1 
[INFO ] 2024-10-15 18:54:17.223 - [任务 2][testPlatForm] - Node testPlatForm[9940f3a6-1f11-4b92-9e5a-c9b1a3cebe63] preload schema finished, cost 0 ms 
[INFO ] 2024-10-15 18:54:17.223 - [任务 2][test_platform] - Node test_platform[8802dbd4-1922-4fbd-a813-4fde483e1fe1] preload schema finished, cost 0 ms 
[INFO ] 2024-10-15 18:54:18.061 - [任务 2][testPlatForm] - Write batch size: 1, max wait ms per batch: 500 
[INFO ] 2024-10-15 18:54:18.591 - [任务 2][test_platform] - Source node "test_platform" read batch size: 100 
[INFO ] 2024-10-15 18:54:18.591 - [任务 2][test_platform] - Source node "test_platform" event queue capacity: 200 
[INFO ] 2024-10-15 18:54:18.591 - [任务 2][test_platform] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-15 18:54:18.675 - [任务 2][test_platform] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000143","position":9612,"gtidSet":""} 
[INFO ] 2024-10-15 18:54:18.742 - [任务 2][test_platform] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-15 18:54:18.742 - [任务 2][test_platform] - Initial sync started 
[INFO ] 2024-10-15 18:54:18.743 - [任务 2][test_platform] - Starting batch read, table name: test_platform 
[INFO ] 2024-10-15 18:54:18.755 - [任务 2][test_platform] - Table test_platform is going to be initial synced 
[INFO ] 2024-10-15 18:54:18.800 - [任务 2][test_platform] - Table [test_platform] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-15 18:54:18.801 - [任务 2][test_platform] - Query table 'test_platform' counts: 67 
[INFO ] 2024-10-15 18:54:18.804 - [任务 2][test_platform] - Initial sync completed 
[INFO ] 2024-10-15 18:54:18.804 - [任务 2][test_platform] - Incremental sync starting... 
[INFO ] 2024-10-15 18:54:18.804 - [任务 2][test_platform] - Initial sync completed 
[INFO ] 2024-10-15 18:54:18.807 - [任务 2][test_platform] - Starting stream read, table list: [test_platform], offset: {"filename":"mysql-bin.000143","position":9612,"gtidSet":""} 
[INFO ] 2024-10-15 18:54:18.921 - [任务 2][test_platform] - Starting mysql cdc, server name: 511f76b3-b88c-4d57-b1c6-27c3826678b7 
[INFO ] 2024-10-15 18:54:18.922 - [任务 2][test_platform] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"511f76b3-b88c-4d57-b1c6-27c3826678b7","offset":{"{\"server\":\"511f76b3-b88c-4d57-b1c6-27c3826678b7\"}":"{\"file\":\"mysql-bin.000143\",\"pos\":9612,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1375058724
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 511f76b3-b88c-4d57-b1c6-27c3826678b7
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 23306
  threadName: Debezium-Mysql-Connector-511f76b3-b88c-4d57-b1c6-27c3826678b7
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: *************
  database.password: ********
  name: 511f76b3-b88c-4d57-b1c6-27c3826678b7
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_platform
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-15 18:54:19.126 - [任务 2][test_platform] - Connector Mysql incremental start succeed, tables: [test_platform], data change syncing 
[INFO ] 2024-10-15 23:45:39.433 - [任务 2][test_platform] - Node test_platform[8802dbd4-1922-4fbd-a813-4fde483e1fe1] running status set to false 
[WARN ] 2024-10-15 23:45:39.435 - [任务 2][test_platform] - Stop PDK connector node failed: Unknown PDK exception occur, com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-8802dbd4-1922-4fbd-a813-4fde483e1fe1 
[INFO ] 2024-10-15 23:45:39.458 - [任务 2] - Start task milestones: 670e49b3c0f6aa50bd619510(任务 2) 
[INFO ] 2024-10-15 23:45:39.666 - [任务 2] - Task initialization... 
[INFO ] 2024-10-15 23:45:40.523 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-15 23:45:40.726 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-15 23:45:41.273 - [任务 2][testPlatForm] - Node testPlatForm[9940f3a6-1f11-4b92-9e5a-c9b1a3cebe63] start preload schema,table counts: 1 
[INFO ] 2024-10-15 23:45:41.273 - [任务 2][testPlatForm] - Node testPlatForm[9940f3a6-1f11-4b92-9e5a-c9b1a3cebe63] preload schema finished, cost 0 ms 
[INFO ] 2024-10-15 23:45:41.407 - [任务 2][test_platform] - Node test_platform[8802dbd4-1922-4fbd-a813-4fde483e1fe1] start preload schema,table counts: 1 
[INFO ] 2024-10-15 23:45:41.408 - [任务 2][test_platform] - Node test_platform[8802dbd4-1922-4fbd-a813-4fde483e1fe1] preload schema finished, cost 0 ms 
[INFO ] 2024-10-15 23:45:42.402 - [任务 2][testPlatForm] - Write batch size: 1, max wait ms per batch: 500 
[INFO ] 2024-10-15 23:45:43.431 - [任务 2][test_platform] - Source node "test_platform" read batch size: 100 
[INFO ] 2024-10-15 23:45:43.433 - [任务 2][test_platform] - Source node "test_platform" event queue capacity: 200 
[INFO ] 2024-10-15 23:45:43.435 - [任务 2][test_platform] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-15 23:45:43.449 - [任务 2][test_platform] - batch offset found: {"test_platform":{"batch_read_connector_status":"OVER"}},stream offset found: {"name":"511f76b3-b88c-4d57-b1c6-27c3826678b7","offset":{"{\"server\":\"511f76b3-b88c-4d57-b1c6-27c3826678b7\"}":"{\"ts_sec\":1728989659,\"file\":\"mysql-bin.000143\",\"pos\":9612,\"server_id\":1121}"}} 
[INFO ] 2024-10-15 23:45:43.449 - [任务 2][test_platform] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-15 23:45:43.587 - [任务 2][test_platform] - Incremental sync starting... 
[INFO ] 2024-10-15 23:45:43.588 - [任务 2][test_platform] - Initial sync completed 
[INFO ] 2024-10-15 23:45:43.599 - [任务 2][test_platform] - Starting stream read, table list: [test_platform], offset: {"name":"511f76b3-b88c-4d57-b1c6-27c3826678b7","offset":{"{\"server\":\"511f76b3-b88c-4d57-b1c6-27c3826678b7\"}":"{\"ts_sec\":1728989659,\"file\":\"mysql-bin.000143\",\"pos\":9612,\"server_id\":1121}"}} 
[INFO ] 2024-10-15 23:45:44.416 - [任务 2][test_platform] - Starting mysql cdc, server name: 511f76b3-b88c-4d57-b1c6-27c3826678b7 
[INFO ] 2024-10-15 23:45:44.623 - [任务 2][test_platform] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"511f76b3-b88c-4d57-b1c6-27c3826678b7","offset":{"{\"server\":\"511f76b3-b88c-4d57-b1c6-27c3826678b7\"}":"{\"file\":\"mysql-bin.000143\",\"pos\":3184,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 979692772
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 511f76b3-b88c-4d57-b1c6-27c3826678b7
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 23306
  threadName: Debezium-Mysql-Connector-511f76b3-b88c-4d57-b1c6-27c3826678b7
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: *************
  database.password: ********
  name: 511f76b3-b88c-4d57-b1c6-27c3826678b7
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_platform
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-15 23:45:44.904 - [任务 2][test_platform] - Connector Mysql incremental start succeed, tables: [test_platform], data change syncing 
[WARN ] 2024-10-15 23:45:55.169 - [任务 2][testPlatForm] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.net.SocketException: Connection reset
	java.net.SocketInputStream.read(SocketInputStream.java:210)
	java.net.SocketInputStream.read(SocketInputStream.java:141)
	org.apache.http.impl.conn.LoggingInputStream.read(LoggingInputStream.java:84)
	org.apache.http.impl.io.SessionInputBufferImpl.streamRead(SessionInputBufferImpl.java:137)
	org.apache.http.impl.io.SessionInputBufferImpl.fillBuffer(SessionInputBufferImpl.java:153)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-10-15 23:46:39.997 - [任务 2][test_platform] - Node test_platform[8802dbd4-1922-4fbd-a813-4fde483e1fe1] running status set to false 
[INFO ] 2024-10-15 23:46:39.997 - [任务 2][test_platform] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-15 23:46:39.998 - [任务 2][test_platform] - Mysql binlog reader stopped 
[INFO ] 2024-10-15 23:46:39.998 - [任务 2][test_platform] - Incremental sync completed 
[INFO ] 2024-10-15 23:46:40.151 - [任务 2][test_platform] - PDK connector node stopped: HazelcastSourcePdkDataNode-8802dbd4-1922-4fbd-a813-4fde483e1fe1 
[INFO ] 2024-10-15 23:46:40.152 - [任务 2][test_platform] - PDK connector node released: HazelcastSourcePdkDataNode-8802dbd4-1922-4fbd-a813-4fde483e1fe1 
[INFO ] 2024-10-15 23:46:40.155 - [任务 2][test_platform] - Node test_platform[8802dbd4-1922-4fbd-a813-4fde483e1fe1] schema data cleaned 
[INFO ] 2024-10-15 23:46:40.157 - [任务 2][test_platform] - Node test_platform[8802dbd4-1922-4fbd-a813-4fde483e1fe1] monitor closed 
[INFO ] 2024-10-15 23:46:40.164 - [任务 2][test_platform] - Node test_platform[8802dbd4-1922-4fbd-a813-4fde483e1fe1] close complete, cost 245 ms 
[INFO ] 2024-10-15 23:46:40.165 - [任务 2][testPlatForm] - Node testPlatForm[9940f3a6-1f11-4b92-9e5a-c9b1a3cebe63] running status set to false 
[INFO ] 2024-10-15 23:46:40.199 - [任务 2][testPlatForm] - PDK connector node stopped: HazelcastTargetPdkDataNode-9940f3a6-1f11-4b92-9e5a-c9b1a3cebe63 
[INFO ] 2024-10-15 23:46:40.200 - [任务 2][testPlatForm] - PDK connector node released: HazelcastTargetPdkDataNode-9940f3a6-1f11-4b92-9e5a-c9b1a3cebe63 
[INFO ] 2024-10-15 23:46:40.200 - [任务 2][testPlatForm] - Node testPlatForm[9940f3a6-1f11-4b92-9e5a-c9b1a3cebe63] schema data cleaned 
[INFO ] 2024-10-15 23:46:40.200 - [任务 2][testPlatForm] - Node testPlatForm[9940f3a6-1f11-4b92-9e5a-c9b1a3cebe63] monitor closed 
[INFO ] 2024-10-15 23:46:40.202 - [任务 2][testPlatForm] - Node testPlatForm[9940f3a6-1f11-4b92-9e5a-c9b1a3cebe63] close complete, cost 38 ms 
[INFO ] 2024-10-15 23:46:40.296 - [任务 2][testPlatForm] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: testPlatForm
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@54b24700: {"after":{"drflag":1,"create_time":"2019-12-16T21:41:29","application_type":0,"application_source":1,"update_time":"2022-06-02T14:00:38","application_name":"知嘛掌柜","deleted":0,"update_user":"4281103553885708314","enable":1,"application_code":1,"id":95,"belong_channel_desc":"新零售中台","belong_channel_code":"10005"},"containsIllegalDate":false,"referenceTime":1728989596000,"tableId":"test_platform","time":1729007145890,"type":300}, nodeIds=[8802dbd4-1922-4fbd-a813-4fde483e1fe1], sourceTime=1728989596000, sourceSerialNo=null} 
[ERROR] 2024-10-15 23:46:40.298 - [任务 2][testPlatForm] - java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: testPlatForm
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@54b24700: {"after":{"drflag":1,"create_time":"2019-12-16T21:41:29","application_type":0,"application_source":1,"update_time":"2022-06-02T14:00:38","application_name":"知嘛掌柜","deleted":0,"update_user":"4281103553885708314","enable":1,"application_code":1,"id":95,"belong_channel_desc":"新零售中台","belong_channel_code":"10005"},"containsIllegalDate":false,"referenceTime":1728989596000,"tableId":"test_platform","time":1729007145890,"type":300}, nodeIds=[8802dbd4-1922-4fbd-a813-4fde483e1fe1], sourceTime=1728989596000, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: testPlatForm
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@54b24700: {"after":{"drflag":1,"create_time":"2019-12-16T21:41:29","application_type":0,"application_source":1,"update_time":"2022-06-02T14:00:38","application_name":"知嘛掌柜","deleted":0,"update_user":"4281103553885708314","enable":1,"application_code":1,"id":95,"belong_channel_desc":"新零售中台","belong_channel_code":"10005"},"containsIllegalDate":false,"referenceTime":1728989596000,"tableId":"test_platform","time":1729007145890,"type":300}, nodeIds=[8802dbd4-1922-4fbd-a813-4fde483e1fe1], sourceTime=1728989596000, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: Connection pool shut down
	org.apache.http.util.Asserts.check(Asserts.java:34)
	org.apache.http.impl.conn.PoolingHttpClientConnectionManager.requestConnection(PoolingHttpClientConnectionManager.java:269)
	org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:176)
	org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: testPlatForm
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:692)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:626)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:516)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:595)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:647)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:594)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: testPlatForm
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:815)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$26(HazelcastTargetPdkDataNode.java:515)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:515)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:781)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:714)
	... 13 more
Caused by: Execute PDK method: TARGET_WRITE_RECORD, tableName: testPlatForm
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:879)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$59(HazelcastTargetPdkDataNode.java:821)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 20 more
Caused by: io.tapdata.connector.doris.streamload.exception.DorisRuntimeException: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Call stream load error: Connection pool shut down
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.flush(DorisStreamLoader.java:227)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.writeRecord(DorisStreamLoader.java:104)
	at io.tapdata.connector.doris.DorisConnector.writeRecord(DorisConnector.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:870)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:55)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:866)
	... 29 more
Caused by: io.tapdata.connector.doris.streamload.exception.StreamLoadException: Call stream load error: Connection pool shut down
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.put(DorisStreamLoader.java:182)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.flush(DorisStreamLoader.java:217)
	... 34 more
Caused by: java.lang.IllegalStateException: Connection pool shut down
	at org.apache.http.util.Asserts.check(Asserts.java:34)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.requestConnection(PoolingHttpClientConnectionManager.java:269)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:176)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at io.tapdata.connector.doris.streamload.DorisStreamLoader.put(DorisStreamLoader.java:178)
	... 35 more

[INFO ] 2024-10-15 23:46:44.810 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-15 23:46:44.819 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@135864c3 
[INFO ] 2024-10-15 23:46:44.823 - [任务 2] - Stop task milestones: 670e49b3c0f6aa50bd619510(任务 2)  
[INFO ] 2024-10-15 23:46:44.981 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-10-15 23:46:45.103 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-10-15 23:46:45.104 - [任务 2] - Remove memory task client succeed, task: 任务 2[670e49b3c0f6aa50bd619510] 
[INFO ] 2024-10-15 23:46:45.311 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[670e49b3c0f6aa50bd619510] 
