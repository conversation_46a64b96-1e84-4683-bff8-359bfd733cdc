[INFO ] 2024-03-27 08:29:14.820 - [任务 11] - Start task milestones: 6602954855700b348d5e913d(任务 11) 
[INFO ] 2024-03-27 08:29:14.851 - [任务 11] - Task initialization... 
[INFO ] 2024-03-27 08:29:14.852 - [任务 11] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 08:29:14.853 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 08:29:14.856 - [任务 11][CLAIM] - Node CLAIM[383b58b8-8fc7-4695-b892-7f9beae1ca1e] start preload schema,table counts: 1 
[INFO ] 2024-03-27 08:29:14.856 - [任务 11][test3] - Node test3[7fb8114e-c06c-4ccf-849f-453900a4db6b] start preload schema,table counts: 1 
[INFO ] 2024-03-27 08:29:15.044 - [任务 11][CLAIM] - Node CLAIM[383b58b8-8fc7-4695-b892-7f9beae1ca1e] preload schema finished, cost 215 ms 
[INFO ] 2024-03-27 08:29:15.045 - [任务 11][test3] - Node test3[7fb8114e-c06c-4ccf-849f-453900a4db6b] preload schema finished, cost 213 ms 
[INFO ] 2024-03-27 08:29:15.481 - [任务 11][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 08:29:15.488 - [任务 11][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 08:29:15.495 - [任务 11][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-27 08:29:15.508 - [任务 11][CLAIM] - batch offset found: {},stream offset found: {"name":"b7c65c5a-9ed0-4af2-ae2d-b49199d68b95","offset":{"{\"server\":\"b7c65c5a-9ed0-4af2-ae2d-b49199d68b95\"}":"{\"ts_sec\":1711449059,\"file\":\"binlog.000020\",\"pos\":144627947,\"server_id\":1}"}} 
[INFO ] 2024-03-27 08:29:15.546 - [任务 11][test3] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 08:29:15.696 - [任务 11][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 08:29:15.696 - [任务 11][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 08:29:15.703 - [任务 11][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"b7c65c5a-9ed0-4af2-ae2d-b49199d68b95","offset":{"{\"server\":\"b7c65c5a-9ed0-4af2-ae2d-b49199d68b95\"}":"{\"ts_sec\":1711449059,\"file\":\"binlog.000020\",\"pos\":144627947,\"server_id\":1}"}} 
[INFO ] 2024-03-27 08:29:15.749 - [任务 11][CLAIM] - Starting mysql cdc, server name: b7c65c5a-9ed0-4af2-ae2d-b49199d68b95 
[INFO ] 2024-03-27 08:29:15.811 - [任务 11][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 852950005
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b7c65c5a-9ed0-4af2-ae2d-b49199d68b95
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b7c65c5a-9ed0-4af2-ae2d-b49199d68b95
  database.hostname: 127.0.0.1
  database.password: ********
  name: b7c65c5a-9ed0-4af2-ae2d-b49199d68b95
  pdk.offset.string: {"name":"b7c65c5a-9ed0-4af2-ae2d-b49199d68b95","offset":{"{\"server\":\"b7c65c5a-9ed0-4af2-ae2d-b49199d68b95\"}":"{\"ts_sec\":1711449059,\"file\":\"binlog.000020\",\"pos\":144627947,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 08:29:16.484 - [任务 11][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 08:32:25.914 - [任务 11] - Stop task milestones: 6602954855700b348d5e913d(任务 11)  
[INFO ] 2024-03-27 08:32:25.917 - [任务 11][CLAIM] - Node CLAIM[383b58b8-8fc7-4695-b892-7f9beae1ca1e] running status set to false 
[INFO ] 2024-03-27 08:32:25.918 - [任务 11][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-27 08:32:25.918 - [任务 11][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-27 08:32:25.922 - [任务 11][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-383b58b8-8fc7-4695-b892-7f9beae1ca1e 
[INFO ] 2024-03-27 08:32:25.923 - [任务 11][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-383b58b8-8fc7-4695-b892-7f9beae1ca1e 
[INFO ] 2024-03-27 08:32:25.923 - [任务 11][CLAIM] - Node CLAIM[383b58b8-8fc7-4695-b892-7f9beae1ca1e] schema data cleaned 
[INFO ] 2024-03-27 08:32:25.924 - [任务 11][CLAIM] - Node CLAIM[383b58b8-8fc7-4695-b892-7f9beae1ca1e] monitor closed 
[INFO ] 2024-03-27 08:32:25.928 - [任务 11][CLAIM] - Node CLAIM[383b58b8-8fc7-4695-b892-7f9beae1ca1e] close complete, cost 157 ms 
[INFO ] 2024-03-27 08:32:25.928 - [任务 11][test3] - Node test3[7fb8114e-c06c-4ccf-849f-453900a4db6b] running status set to false 
[INFO ] 2024-03-27 08:32:25.977 - [任务 11][test3] - PDK connector node stopped: HazelcastTargetPdkDataNode-7fb8114e-c06c-4ccf-849f-453900a4db6b 
[INFO ] 2024-03-27 08:32:25.979 - [任务 11][test3] - PDK connector node released: HazelcastTargetPdkDataNode-7fb8114e-c06c-4ccf-849f-453900a4db6b 
[INFO ] 2024-03-27 08:32:25.979 - [任务 11][test3] - Node test3[7fb8114e-c06c-4ccf-849f-453900a4db6b] schema data cleaned 
[INFO ] 2024-03-27 08:32:25.981 - [任务 11][test3] - Node test3[7fb8114e-c06c-4ccf-849f-453900a4db6b] monitor closed 
[INFO ] 2024-03-27 08:32:25.981 - [任务 11][test3] - Node test3[7fb8114e-c06c-4ccf-849f-453900a4db6b] close complete, cost 51 ms 
[INFO ] 2024-03-27 08:32:30.491 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 08:32:30.494 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-03-27 08:32:30.495 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 08:32:30.531 - [任务 11] - Remove memory task client succeed, task: 任务 11[6602954855700b348d5e913d] 
[INFO ] 2024-03-27 08:32:30.532 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[6602954855700b348d5e913d] 
