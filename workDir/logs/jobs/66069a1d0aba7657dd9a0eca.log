[INFO ] 2024-03-29 18:39:53.167 - [suppliers] - Start task milestones: 66069a1d0aba7657dd9a0eca(suppliers) 
[INFO ] 2024-03-29 18:39:53.212 - [suppliers] - Task initialization... 
[INFO ] 2024-03-29 18:39:53.215 - [suppliers] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-03-29 18:39:53.371 - [suppliers] - The engine receives suppliers task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 18:39:53.466 - [suppliers][Delete Products] - Node Delete Products[2b06755e-7db7-4aef-ae66-e72a74abf1a2] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:53.477 - [suppliers][merge] - Node merge[21c4300c-acdc-4b35-96aa-8a2487a5c3ad] start preload schema,table counts: 3 
[INFO ] 2024-03-29 18:39:53.477 - [suppliers][Products] - Node Products[c5842b52-dfd2-4789-be3e-255f4541f589] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:53.477 - [suppliers][Rename Suppliers] - Node Rename Suppliers[28a746ea-93cc-46ca-b99a-e1e50cdb6c33] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:53.477 - [suppliers][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:53.477 - [suppliers][suppliers] - Node suppliers[ba5c0c22-4e38-4831-9716-251814ac38a5] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:53.477 - [suppliers][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:53.477 - [suppliers][Delete Suppliers] - Node Delete Suppliers[203e6093-9034-4bc0-bc99-15ec87ad8bf3] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:53.478 - [suppliers][Rename Products] - Node Rename Products[a5f3e3a0-da7c-4ceb-a214-13c1ad51531e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:39:53.557 - [suppliers][Delete Products] - Node Delete Products[2b06755e-7db7-4aef-ae66-e72a74abf1a2] preload schema finished, cost 80 ms 
[INFO ] 2024-03-29 18:39:53.557 - [suppliers][Rename Suppliers] - Node Rename Suppliers[28a746ea-93cc-46ca-b99a-e1e50cdb6c33] preload schema finished, cost 90 ms 
[INFO ] 2024-03-29 18:39:53.557 - [suppliers][Delete Suppliers] - Node Delete Suppliers[203e6093-9034-4bc0-bc99-15ec87ad8bf3] preload schema finished, cost 90 ms 
[INFO ] 2024-03-29 18:39:53.564 - [suppliers][Rename Products] - Node Rename Products[a5f3e3a0-da7c-4ceb-a214-13c1ad51531e] preload schema finished, cost 87 ms 
[INFO ] 2024-03-29 18:39:53.565 - [suppliers][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] preload schema finished, cost 98 ms 
[INFO ] 2024-03-29 18:39:53.565 - [suppliers][Products] - Node Products[c5842b52-dfd2-4789-be3e-255f4541f589] preload schema finished, cost 99 ms 
[INFO ] 2024-03-29 18:39:53.565 - [suppliers][suppliers] - Node suppliers[ba5c0c22-4e38-4831-9716-251814ac38a5] preload schema finished, cost 99 ms 
[INFO ] 2024-03-29 18:39:53.565 - [suppliers][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] preload schema finished, cost 99 ms 
[INFO ] 2024-03-29 18:39:53.611 - [suppliers][merge] - Node merge[21c4300c-acdc-4b35-96aa-8a2487a5c3ad] preload schema finished, cost 146 ms 
[INFO ] 2024-03-29 18:39:53.613 - [suppliers][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 18:39:53.613 - [suppliers][merge] - 
Merge lookup relation{
  Suppliers(203e6093-9034-4bc0-bc99-15ec87ad8bf3)
    ->Products(2b06755e-7db7-4aef-ae66-e72a74abf1a2)
} 
[INFO ] 2024-03-29 18:39:53.818 - [suppliers][Suppliers] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:39:54.016 - [suppliers][merge] - Create merge cache imap name: HazelcastMergeNode_Products_2b06755e-7db7-4aef-ae66-e72a74abf1a2__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:39:54.072 - [suppliers][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 18:39:54.074 - [suppliers][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 18:39:54.389 - [suppliers][Suppliers] - Source node "Suppliers" read batch size: 500 
[INFO ] 2024-03-29 18:39:54.389 - [suppliers][Suppliers] - Source node "Suppliers" event queue capacity: 1000 
[INFO ] 2024-03-29 18:39:54.389 - [suppliers][Suppliers] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:39:54.531 - [suppliers][Suppliers] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:39:54.531 - [suppliers][Suppliers] - Initial sync started 
[INFO ] 2024-03-29 18:39:54.531 - [suppliers][Suppliers] - Starting batch read, table name: Suppliers, offset: null 
[INFO ] 2024-03-29 18:39:54.532 - [suppliers][Suppliers] - Table Suppliers is going to be initial synced 
[INFO ] 2024-03-29 18:39:54.594 - [suppliers][suppliers] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 18:39:54.595 - [suppliers][Suppliers] - Query table 'Suppliers' counts: 1 
[INFO ] 2024-03-29 18:39:54.595 - [suppliers][Suppliers] - Initial sync completed 
[INFO ] 2024-03-29 18:39:54.783 - [suppliers][Products] - Source node "Products" read batch size: 500 
[INFO ] 2024-03-29 18:39:54.785 - [suppliers][Products] - Source node "Products" event queue capacity: 1000 
[INFO ] 2024-03-29 18:39:54.785 - [suppliers][Products] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:39:54.886 - [suppliers][Products] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:39:54.886 - [suppliers] - Node[Products] is waiting for running 
[INFO ] 2024-03-29 18:39:56.093 - [suppliers] - Node[Suppliers] finish, notify next layer to run 
[INFO ] 2024-03-29 18:39:56.113 - [suppliers] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 18:39:56.127 - [suppliers][Products] - Initial sync started 
[INFO ] 2024-03-29 18:39:56.136 - [suppliers][Products] - Starting batch read, table name: Products, offset: null 
[INFO ] 2024-03-29 18:39:56.137 - [suppliers][Products] - Table Products is going to be initial synced 
[INFO ] 2024-03-29 18:39:56.189 - [suppliers][Products] - Query table 'Products' counts: 1 
[INFO ] 2024-03-29 18:39:56.189 - [suppliers][Products] - Initial sync completed 
[INFO ] 2024-03-29 18:39:57.622 - [suppliers][Suppliers] - Incremental sync starting... 
[INFO ] 2024-03-29 18:39:57.631 - [suppliers][Suppliers] - Initial sync completed 
[INFO ] 2024-03-29 18:39:57.631 - [suppliers][Suppliers] - Starting stream read, table list: [Suppliers], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:39:57.673 - [suppliers][Suppliers] - Starting mysql cdc, server name: 9f7570cb-4198-4f54-b598-dc62ef558a87 
[INFO ] 2024-03-29 18:39:57.674 - [suppliers][Suppliers] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 418046061
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9f7570cb-4198-4f54-b598-dc62ef558a87
  database.port: 3307
  threadName: Debezium-Mysql-Connector-9f7570cb-4198-4f54-b598-dc62ef558a87
  database.hostname: 127.0.0.1
  database.password: ********
  name: 9f7570cb-4198-4f54-b598-dc62ef558a87
  pdk.offset.string: {"name":"9f7570cb-4198-4f54-b598-dc62ef558a87","offset":{"{\"server\":\"9f7570cb-4198-4f54-b598-dc62ef558a87\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Suppliers
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:39:57.876 - [suppliers][Suppliers] - Connector Mysql incremental start succeed, tables: [Suppliers], data change syncing 
[INFO ] 2024-03-29 18:39:58.202 - [suppliers][Products] - Incremental sync starting... 
[INFO ] 2024-03-29 18:39:58.202 - [suppliers][Products] - Initial sync completed 
[INFO ] 2024-03-29 18:39:58.203 - [suppliers][Products] - Starting stream read, table list: [Products], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:39:58.262 - [suppliers][Products] - Starting mysql cdc, server name: 1ef518af-9a88-4896-bf25-477731443f0f 
[INFO ] 2024-03-29 18:39:58.262 - [suppliers][Products] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 943298523
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 1ef518af-9a88-4896-bf25-477731443f0f
  database.port: 3307
  threadName: Debezium-Mysql-Connector-1ef518af-9a88-4896-bf25-477731443f0f
  database.hostname: 127.0.0.1
  database.password: ********
  name: 1ef518af-9a88-4896-bf25-477731443f0f
  pdk.offset.string: {"name":"1ef518af-9a88-4896-bf25-477731443f0f","offset":{"{\"server\":\"1ef518af-9a88-4896-bf25-477731443f0f\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Products
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:39:58.467 - [suppliers][Products] - Connector Mysql incremental start succeed, tables: [Products], data change syncing 
[INFO ] 2024-03-29 18:53:24.803 - [suppliers] - Stop task milestones: 66069a1d0aba7657dd9a0eca(suppliers)  
[INFO ] 2024-03-29 18:53:24.992 - [suppliers][Products] - Node Products[c5842b52-dfd2-4789-be3e-255f4541f589] running status set to false 
[INFO ] 2024-03-29 18:53:25.073 - [suppliers][Products] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:53:25.074 - [suppliers][Products] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:53:25.084 - [suppliers][Products] - PDK connector node stopped: HazelcastSourcePdkDataNode-c5842b52-dfd2-4789-be3e-255f4541f589 
[INFO ] 2024-03-29 18:53:25.085 - [suppliers][Products] - PDK connector node released: HazelcastSourcePdkDataNode-c5842b52-dfd2-4789-be3e-255f4541f589 
[INFO ] 2024-03-29 18:53:25.085 - [suppliers][Products] - Node Products[c5842b52-dfd2-4789-be3e-255f4541f589] schema data cleaned 
[INFO ] 2024-03-29 18:53:25.086 - [suppliers][Products] - Node Products[c5842b52-dfd2-4789-be3e-255f4541f589] monitor closed 
[INFO ] 2024-03-29 18:53:25.087 - [suppliers][Products] - Node Products[c5842b52-dfd2-4789-be3e-255f4541f589] close complete, cost 95 ms 
[INFO ] 2024-03-29 18:53:25.087 - [suppliers][Rename Products] - Node Rename Products[a5f3e3a0-da7c-4ceb-a214-13c1ad51531e] running status set to false 
[INFO ] 2024-03-29 18:53:25.142 - [suppliers][Rename Products] - Node Rename Products[a5f3e3a0-da7c-4ceb-a214-13c1ad51531e] schema data cleaned 
[INFO ] 2024-03-29 18:53:25.142 - [suppliers][Rename Products] - Node Rename Products[a5f3e3a0-da7c-4ceb-a214-13c1ad51531e] monitor closed 
[INFO ] 2024-03-29 18:53:25.143 - [suppliers][Rename Products] - Node Rename Products[a5f3e3a0-da7c-4ceb-a214-13c1ad51531e] close complete, cost 55 ms 
[INFO ] 2024-03-29 18:53:25.143 - [suppliers][Delete Products] - Node Delete Products[2b06755e-7db7-4aef-ae66-e72a74abf1a2] running status set to false 
[INFO ] 2024-03-29 18:53:25.185 - [suppliers][Delete Products] - Node Delete Products[2b06755e-7db7-4aef-ae66-e72a74abf1a2] schema data cleaned 
[INFO ] 2024-03-29 18:53:25.185 - [suppliers][Delete Products] - Node Delete Products[2b06755e-7db7-4aef-ae66-e72a74abf1a2] monitor closed 
[INFO ] 2024-03-29 18:53:25.186 - [suppliers][Delete Products] - Node Delete Products[2b06755e-7db7-4aef-ae66-e72a74abf1a2] close complete, cost 42 ms 
[INFO ] 2024-03-29 18:53:25.186 - [suppliers][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] running status set to false 
[INFO ] 2024-03-29 18:53:25.254 - [suppliers][Suppliers] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:53:25.254 - [suppliers][Suppliers] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:53:25.259 - [suppliers][Suppliers] - PDK connector node stopped: HazelcastSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:53:25.259 - [suppliers][Suppliers] - PDK connector node released: HazelcastSourcePdkDataNode-d5115c96-24bd-463a-99bb-bbd95a3f24db 
[INFO ] 2024-03-29 18:53:25.260 - [suppliers][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] schema data cleaned 
[INFO ] 2024-03-29 18:53:25.260 - [suppliers][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] monitor closed 
[INFO ] 2024-03-29 18:53:25.260 - [suppliers][Suppliers] - Node Suppliers[d5115c96-24bd-463a-99bb-bbd95a3f24db] close complete, cost 74 ms 
[INFO ] 2024-03-29 18:53:25.260 - [suppliers][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] running status set to false 
[INFO ] 2024-03-29 18:53:25.273 - [suppliers][Suppliers] - PDK connector node stopped: ScriptExecutor-TargetMysql-1cac10f1-a98d-4645-8c25-bf03c614b0e5 
[INFO ] 2024-03-29 18:53:25.273 - [suppliers][Suppliers] - PDK connector node released: ScriptExecutor-TargetMysql-1cac10f1-a98d-4645-8c25-bf03c614b0e5 
[INFO ] 2024-03-29 18:53:25.280 - [suppliers][Suppliers] - [ScriptExecutorsManager-66069a1d0aba7657dd9a0eca-d3f40865-b3ae-4f08-97a5-a361eca59fd4-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-29 18:53:25.280 - [suppliers][Suppliers] - PDK connector node stopped: ScriptExecutor-TestMongo-9742653a-ce3f-4f3e-bd3c-6524c05c750b 
[INFO ] 2024-03-29 18:53:25.281 - [suppliers][Suppliers] - PDK connector node released: ScriptExecutor-TestMongo-9742653a-ce3f-4f3e-bd3c-6524c05c750b 
[INFO ] 2024-03-29 18:53:25.281 - [suppliers][Suppliers] - [ScriptExecutorsManager-66069a1d0aba7657dd9a0eca-d3f40865-b3ae-4f08-97a5-a361eca59fd4-6600b651928fc21057b4810b] schema data cleaned 
[INFO ] 2024-03-29 18:53:25.282 - [suppliers][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] schema data cleaned 
[INFO ] 2024-03-29 18:53:25.282 - [suppliers][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] monitor closed 
[INFO ] 2024-03-29 18:53:25.282 - [suppliers][Suppliers] - Node Suppliers[d3f40865-b3ae-4f08-97a5-a361eca59fd4] close complete, cost 22 ms 
[INFO ] 2024-03-29 18:53:25.282 - [suppliers][Rename Suppliers] - Node Rename Suppliers[28a746ea-93cc-46ca-b99a-e1e50cdb6c33] running status set to false 
[INFO ] 2024-03-29 18:53:25.346 - [suppliers][Rename Suppliers] - Node Rename Suppliers[28a746ea-93cc-46ca-b99a-e1e50cdb6c33] schema data cleaned 
[INFO ] 2024-03-29 18:53:25.346 - [suppliers][Rename Suppliers] - Node Rename Suppliers[28a746ea-93cc-46ca-b99a-e1e50cdb6c33] monitor closed 
[INFO ] 2024-03-29 18:53:25.346 - [suppliers][Rename Suppliers] - Node Rename Suppliers[28a746ea-93cc-46ca-b99a-e1e50cdb6c33] close complete, cost 63 ms 
[INFO ] 2024-03-29 18:53:25.346 - [suppliers][Delete Suppliers] - Node Delete Suppliers[203e6093-9034-4bc0-bc99-15ec87ad8bf3] running status set to false 
[INFO ] 2024-03-29 18:53:25.405 - [suppliers][Delete Suppliers] - Node Delete Suppliers[203e6093-9034-4bc0-bc99-15ec87ad8bf3] schema data cleaned 
[INFO ] 2024-03-29 18:53:25.405 - [suppliers][Delete Suppliers] - Node Delete Suppliers[203e6093-9034-4bc0-bc99-15ec87ad8bf3] monitor closed 
[INFO ] 2024-03-29 18:53:25.405 - [suppliers][Delete Suppliers] - Node Delete Suppliers[203e6093-9034-4bc0-bc99-15ec87ad8bf3] close complete, cost 58 ms 
[INFO ] 2024-03-29 18:53:25.405 - [suppliers][merge] - Node merge[21c4300c-acdc-4b35-96aa-8a2487a5c3ad] running status set to false 
[INFO ] 2024-03-29 18:53:25.405 - [suppliers][merge] - Destroy merge cache resource: HazelcastMergeNode_Products_2b06755e-7db7-4aef-ae66-e72a74abf1a2__TPORIG 
[INFO ] 2024-03-29 18:53:25.411 - [suppliers][merge] - Node merge[21c4300c-acdc-4b35-96aa-8a2487a5c3ad] schema data cleaned 
[INFO ] 2024-03-29 18:53:25.411 - [suppliers][merge] - Node merge[21c4300c-acdc-4b35-96aa-8a2487a5c3ad] monitor closed 
[INFO ] 2024-03-29 18:53:25.412 - [suppliers][merge] - Node merge[21c4300c-acdc-4b35-96aa-8a2487a5c3ad] close complete, cost 6 ms 
[INFO ] 2024-03-29 18:53:25.412 - [suppliers][suppliers] - Node suppliers[ba5c0c22-4e38-4831-9716-251814ac38a5] running status set to false 
[INFO ] 2024-03-29 18:53:25.431 - [suppliers][suppliers] - PDK connector node stopped: HazelcastTargetPdkDataNode-ba5c0c22-4e38-4831-9716-251814ac38a5 
[INFO ] 2024-03-29 18:53:25.431 - [suppliers][suppliers] - PDK connector node released: HazelcastTargetPdkDataNode-ba5c0c22-4e38-4831-9716-251814ac38a5 
[INFO ] 2024-03-29 18:53:25.431 - [suppliers][suppliers] - Node suppliers[ba5c0c22-4e38-4831-9716-251814ac38a5] schema data cleaned 
[INFO ] 2024-03-29 18:53:25.431 - [suppliers][suppliers] - Node suppliers[ba5c0c22-4e38-4831-9716-251814ac38a5] monitor closed 
[INFO ] 2024-03-29 18:53:25.635 - [suppliers][suppliers] - Node suppliers[ba5c0c22-4e38-4831-9716-251814ac38a5] close complete, cost 19 ms 
[INFO ] 2024-03-29 18:53:30.209 - [suppliers] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 18:53:30.209 - [suppliers] - Stopped task aspect(s) 
[INFO ] 2024-03-29 18:53:30.209 - [suppliers] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 18:53:30.229 - [suppliers] - Remove memory task client succeed, task: suppliers[66069a1d0aba7657dd9a0eca] 
[INFO ] 2024-03-29 18:53:30.230 - [suppliers] - Destroy memory task client cache succeed, task: suppliers[66069a1d0aba7657dd9a0eca] 
