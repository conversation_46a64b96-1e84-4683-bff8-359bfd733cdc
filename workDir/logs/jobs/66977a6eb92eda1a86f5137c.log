[INFO ] 2024-07-17 16:02:22.564 - [任务 1] - Start task milestones: 66977a6eb92eda1a86f5137c(任务 1) 
[INFO ] 2024-07-17 16:02:22.566 - [任务 1] - Task initialization... 
[INFO ] 2024-07-17 16:02:23.411 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-17 16:02:23.617 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 16:02:24.178 - [任务 1][CLAIM2] - Node CLAIM2[779d5156-bf6d-45b1-827b-5af4bad8b881] start preload schema,table counts: 1 
[INFO ] 2024-07-17 16:02:24.180 - [任务 1][CLAIM2] - Node CLAIM2[8d380205-8b21-4220-b770-845656bedcc5] start preload schema,table counts: 1 
[INFO ] 2024-07-17 16:02:24.180 - [任务 1][CLAIM2] - Node CLAIM2[779d5156-bf6d-45b1-827b-5af4bad8b881] preload schema finished, cost 1 ms 
[INFO ] 2024-07-17 16:02:24.181 - [任务 1][CLAIM2] - Node CLAIM2[8d380205-8b21-4220-b770-845656bedcc5] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 16:02:25.004 - [任务 1][CLAIM2] - Source node "CLAIM2" read batch size: 100 
[INFO ] 2024-07-17 16:02:25.007 - [任务 1][CLAIM2] - Source node "CLAIM2" event queue capacity: 200 
[INFO ] 2024-07-17 16:02:25.007 - [任务 1][CLAIM2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 16:02:25.139 - [任务 1][CLAIM2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-17 16:02:25.140 - [任务 1][CLAIM2] - batch offset found: {},stream offset found: {"cdcOffset":1721203340,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 16:02:25.339 - [任务 1][CLAIM2] - Initial sync started 
[INFO ] 2024-07-17 16:02:25.353 - [任务 1][CLAIM2] - Starting batch read, table name: CLAIM2, offset: null 
[INFO ] 2024-07-17 16:02:25.353 - [任务 1][CLAIM2] - Table CLAIM2 is going to be initial synced 
[INFO ] 2024-07-17 16:02:25.402 - [任务 1][CLAIM2] - Query table 'CLAIM2' counts: 1 
[INFO ] 2024-07-17 16:02:25.403 - [任务 1][CLAIM2] - Table [CLAIM2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-17 16:02:25.407 - [任务 1][CLAIM2] - Initial sync completed 
[INFO ] 2024-07-17 16:02:25.407 - [任务 1][CLAIM2] - Incremental sync starting... 
[INFO ] 2024-07-17 16:02:25.407 - [任务 1][CLAIM2] - Initial sync completed 
[INFO ] 2024-07-17 16:02:25.413 - [任务 1][CLAIM2] - Starting stream read, table list: [CLAIM2], offset: {"cdcOffset":1721203340,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 16:02:25.632 - [任务 1][CLAIM2] - Connector MongoDB incremental start succeed, tables: [CLAIM2], data change syncing 
[INFO ] 2024-07-17 16:02:44.790 - [任务 1][CLAIM2] - Node CLAIM2[779d5156-bf6d-45b1-827b-5af4bad8b881] running status set to false 
[INFO ] 2024-07-17 16:02:44.812 - [任务 1][CLAIM2] - PDK connector node stopped: HazelcastSourcePdkDataNode-779d5156-bf6d-45b1-827b-5af4bad8b881 
[INFO ] 2024-07-17 16:02:44.813 - [任务 1][CLAIM2] - PDK connector node released: HazelcastSourcePdkDataNode-779d5156-bf6d-45b1-827b-5af4bad8b881 
[INFO ] 2024-07-17 16:02:44.817 - [任务 1][CLAIM2] - Node CLAIM2[779d5156-bf6d-45b1-827b-5af4bad8b881] schema data cleaned 
[INFO ] 2024-07-17 16:02:44.818 - [任务 1][CLAIM2] - Node CLAIM2[779d5156-bf6d-45b1-827b-5af4bad8b881] monitor closed 
[INFO ] 2024-07-17 16:02:44.835 - [任务 1][CLAIM2] - Node CLAIM2[779d5156-bf6d-45b1-827b-5af4bad8b881] close complete, cost 40 ms 
[INFO ] 2024-07-17 16:02:44.838 - [任务 1][CLAIM2] - Node CLAIM2[8d380205-8b21-4220-b770-845656bedcc5] running status set to false 
[INFO ] 2024-07-17 16:02:44.909 - [任务 1][CLAIM2] - PDK connector node stopped: HazelcastTargetPdkDataNode-8d380205-8b21-4220-b770-845656bedcc5 
[INFO ] 2024-07-17 16:02:44.911 - [任务 1][CLAIM2] - PDK connector node released: HazelcastTargetPdkDataNode-8d380205-8b21-4220-b770-845656bedcc5 
[INFO ] 2024-07-17 16:02:44.911 - [任务 1][CLAIM2] - Node CLAIM2[8d380205-8b21-4220-b770-845656bedcc5] schema data cleaned 
[INFO ] 2024-07-17 16:02:44.911 - [任务 1][CLAIM2] - Node CLAIM2[8d380205-8b21-4220-b770-845656bedcc5] monitor closed 
[INFO ] 2024-07-17 16:02:45.117 - [任务 1][CLAIM2] - Node CLAIM2[8d380205-8b21-4220-b770-845656bedcc5] close complete, cost 78 ms 
[INFO ] 2024-07-17 16:02:45.724 - [任务 1][CLAIM2] - Incremental sync completed 
[INFO ] 2024-07-17 16:02:48.571 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 16:02:48.573 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4df1521c 
[INFO ] 2024-07-17 16:02:48.728 - [任务 1] - Stop task milestones: 66977a6eb92eda1a86f5137c(任务 1)  
[INFO ] 2024-07-17 16:02:48.730 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-17 16:02:48.921 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 16:02:48.922 - [任务 1] - Remove memory task client succeed, task: 任务 1[66977a6eb92eda1a86f5137c] 
[INFO ] 2024-07-17 16:02:48.923 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66977a6eb92eda1a86f5137c] 
[INFO ] 2024-07-17 16:03:11.838 - [任务 1] - Start task milestones: 66977a6eb92eda1a86f5137c(任务 1) 
[INFO ] 2024-07-17 16:03:11.839 - [任务 1] - Task initialization... 
[INFO ] 2024-07-17 16:03:12.191 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-17 16:03:12.454 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 16:03:12.454 - [任务 1][CLAIM2] - Node CLAIM2[8d380205-8b21-4220-b770-845656bedcc5] start preload schema,table counts: 1 
[INFO ] 2024-07-17 16:03:12.454 - [任务 1][CLAIM2] - Node POLICY[779d5156-bf6d-45b1-827b-5af4bad8b881] start preload schema,table counts: 1 
[INFO ] 2024-07-17 16:03:12.455 - [任务 1][CLAIM2] - Node CLAIM2[8d380205-8b21-4220-b770-845656bedcc5] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 16:03:12.457 - [任务 1][CLAIM2] - Node POLICY[779d5156-bf6d-45b1-827b-5af4bad8b881] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 16:03:13.034 - [任务 1][CLAIM2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-17 16:03:13.055 - [任务 1][CLAIM2] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-17 16:03:13.055 - [任务 1][CLAIM2] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-17 16:03:13.057 - [任务 1][CLAIM2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 16:03:13.166 - [任务 1][CLAIM2] - batch offset found: {},stream offset found: {"cdcOffset":1721203390,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 16:03:13.355 - [任务 1][CLAIM2] - Initial sync started 
[INFO ] 2024-07-17 16:03:13.362 - [任务 1][CLAIM2] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-17 16:03:13.363 - [任务 1][CLAIM2] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-17 16:03:13.417 - [任务 1][CLAIM2] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-17 16:03:13.439 - [任务 1][CLAIM2] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-17 16:03:13.439 - [任务 1][CLAIM2] - Initial sync completed 
[INFO ] 2024-07-17 16:03:13.440 - [任务 1][CLAIM2] - Incremental sync starting... 
[INFO ] 2024-07-17 16:03:13.440 - [任务 1][CLAIM2] - Initial sync completed 
[INFO ] 2024-07-17 16:03:13.441 - [任务 1][CLAIM2] - Starting stream read, table list: [POLICY], offset: {"cdcOffset":1721203390,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 16:03:13.649 - [任务 1][CLAIM2] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-17 18:20:34.813 - [任务 1][CLAIM2] - Node POLICY[779d5156-bf6d-45b1-827b-5af4bad8b881] running status set to false 
[INFO ] 2024-07-17 18:20:34.822 - [任务 1][CLAIM2] - PDK connector node stopped: HazelcastSourcePdkDataNode-779d5156-bf6d-45b1-827b-5af4bad8b881 
[INFO ] 2024-07-17 18:20:34.822 - [任务 1][CLAIM2] - PDK connector node released: HazelcastSourcePdkDataNode-779d5156-bf6d-45b1-827b-5af4bad8b881 
[INFO ] 2024-07-17 18:20:34.822 - [任务 1][CLAIM2] - Node POLICY[779d5156-bf6d-45b1-827b-5af4bad8b881] schema data cleaned 
[INFO ] 2024-07-17 18:20:34.823 - [任务 1][CLAIM2] - Node POLICY[779d5156-bf6d-45b1-827b-5af4bad8b881] monitor closed 
[INFO ] 2024-07-17 18:20:34.823 - [任务 1][CLAIM2] - Node POLICY[779d5156-bf6d-45b1-827b-5af4bad8b881] close complete, cost 11 ms 
[INFO ] 2024-07-17 18:20:34.823 - [任务 1][CLAIM2] - Node CLAIM2[8d380205-8b21-4220-b770-845656bedcc5] running status set to false 
[INFO ] 2024-07-17 18:20:34.857 - [任务 1][CLAIM2] - PDK connector node stopped: HazelcastTargetPdkDataNode-8d380205-8b21-4220-b770-845656bedcc5 
[INFO ] 2024-07-17 18:20:34.858 - [任务 1][CLAIM2] - PDK connector node released: HazelcastTargetPdkDataNode-8d380205-8b21-4220-b770-845656bedcc5 
[INFO ] 2024-07-17 18:20:34.858 - [任务 1][CLAIM2] - Node CLAIM2[8d380205-8b21-4220-b770-845656bedcc5] schema data cleaned 
[INFO ] 2024-07-17 18:20:34.860 - [任务 1][CLAIM2] - Node CLAIM2[8d380205-8b21-4220-b770-845656bedcc5] monitor closed 
[INFO ] 2024-07-17 18:20:35.067 - [任务 1][CLAIM2] - Node CLAIM2[8d380205-8b21-4220-b770-845656bedcc5] close complete, cost 37 ms 
[INFO ] 2024-07-17 18:20:35.838 - [任务 1][CLAIM2] - Incremental sync completed 
[INFO ] 2024-07-17 18:21:28.293 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 18:21:28.293 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4bbe7e40 
[INFO ] 2024-07-17 18:21:28.304 - [任务 1] - Stop task milestones: 66977a6eb92eda1a86f5137c(任务 1)  
[INFO ] 2024-07-17 18:21:28.444 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-17 18:21:28.444 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 18:21:28.514 - [任务 1] - Remove memory task client succeed, task: 任务 1[66977a6eb92eda1a86f5137c] 
[INFO ] 2024-07-17 18:21:28.514 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66977a6eb92eda1a86f5137c] 
