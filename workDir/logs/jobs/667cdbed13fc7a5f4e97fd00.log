[INFO ] 2024-06-27 11:27:01.438 - [任务 1] - Start task milestones: 667cdbed13fc7a5f4e97fd00(任务 1) 
[INFO ] 2024-06-27 11:27:01.440 - [任务 1] - Task initialization... 
[INFO ] 2024-06-27 11:27:02.158 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-27 11:27:02.264 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-27 11:27:02.879 - [任务 1][表编辑] - Node 表编辑[47351528-57e8-4f1f-af82-d8f97b230c87] start preload schema,table counts: 1 
[INFO ] 2024-06-27 11:27:02.882 - [任务 1][sourceDummy] - Node sourceDummy[3e309dbb-db5d-4c1c-86b3-bc68af6e92b4] start preload schema,table counts: 1 
[INFO ] 2024-06-27 11:27:02.883 - [任务 1][表编辑] - Node 表编辑[47351528-57e8-4f1f-af82-d8f97b230c87] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 11:27:02.884 - [任务 1][sourceDummy] - Node sourceDummy[3e309dbb-db5d-4c1c-86b3-bc68af6e92b4] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 11:27:02.917 - [任务 1][targetDummy] - Node targetDummy[19c5aeff-8f1a-402a-a27f-455959cf9594] start preload schema,table counts: 1 
[INFO ] 2024-06-27 11:27:02.918 - [任务 1][targetDummy] - Node targetDummy[19c5aeff-8f1a-402a-a27f-455959cf9594] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 11:27:04.028 - [任务 1][targetDummy] - Node(targetDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-27 11:27:04.032 - [任务 1][sourceDummy] - Source node "sourceDummy" read batch size: 100 
[INFO ] 2024-06-27 11:27:04.032 - [任务 1][sourceDummy] - Source node "sourceDummy" event queue capacity: 200 
[INFO ] 2024-06-27 11:27:04.034 - [任务 1][targetDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-27 11:27:04.041 - [任务 1][sourceDummy] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-27 11:27:04.059 - [任务 1][sourceDummy] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1719458824034,"lastTimes":1719458824034,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-06-27 11:27:04.135 - [任务 1][sourceDummy] - Initial sync started 
[INFO ] 2024-06-27 11:27:04.135 - [任务 1][sourceDummy] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-06-27 11:27:04.158 - [任务 1][sourceDummy] - Table dummy_test is going to be initial synced 
[INFO ] 2024-06-27 11:27:04.158 - [任务 1][sourceDummy] - Start dummy_test batch read 
[INFO ] 2024-06-27 11:27:04.207 - [任务 1][sourceDummy] - Query table 'dummy_test' counts: 1 
[INFO ] 2024-06-27 11:27:04.209 - [任务 1][sourceDummy] - Compile dummy_test batch read 
[INFO ] 2024-06-27 11:27:04.210 - [任务 1][sourceDummy] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-27 11:27:04.216 - [任务 1][sourceDummy] - Initial sync completed 
[INFO ] 2024-06-27 11:27:04.217 - [任务 1][sourceDummy] - Incremental sync starting... 
[INFO ] 2024-06-27 11:27:04.217 - [任务 1][sourceDummy] - Initial sync completed 
[INFO ] 2024-06-27 11:27:04.223 - [任务 1][sourceDummy] - Starting stream read, table list: [dummy_test], offset: {"syncStage":null,"beginTimes":1719458824034,"lastTimes":1719458824034,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-06-27 11:27:04.229 - [任务 1][sourceDummy] - Start [dummy_test] stream read 
[INFO ] 2024-06-27 11:27:04.230 - [任务 1][sourceDummy] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[INFO ] 2024-06-27 11:27:42.212 - [任务 1] - Stop task milestones: 667cdbed13fc7a5f4e97fd00(任务 1)  
[INFO ] 2024-06-27 11:27:42.721 - [任务 1][sourceDummy] - Node sourceDummy[3e309dbb-db5d-4c1c-86b3-bc68af6e92b4] running status set to false 
[INFO ] 2024-06-27 11:27:42.767 - [任务 1][sourceDummy] - Stop connector 
[INFO ] 2024-06-27 11:27:42.768 - [任务 1][sourceDummy] - PDK connector node stopped: HazelcastSourcePdkDataNode-3e309dbb-db5d-4c1c-86b3-bc68af6e92b4 
[INFO ] 2024-06-27 11:27:42.769 - [任务 1][sourceDummy] - PDK connector node released: HazelcastSourcePdkDataNode-3e309dbb-db5d-4c1c-86b3-bc68af6e92b4 
[INFO ] 2024-06-27 11:27:42.769 - [任务 1][sourceDummy] - Node sourceDummy[3e309dbb-db5d-4c1c-86b3-bc68af6e92b4] schema data cleaned 
[INFO ] 2024-06-27 11:27:42.770 - [任务 1][sourceDummy] - Node sourceDummy[3e309dbb-db5d-4c1c-86b3-bc68af6e92b4] monitor closed 
[INFO ] 2024-06-27 11:27:42.780 - [任务 1][sourceDummy] - Node sourceDummy[3e309dbb-db5d-4c1c-86b3-bc68af6e92b4] close complete, cost 68 ms 
[INFO ] 2024-06-27 11:27:42.780 - [任务 1][表编辑] - Node 表编辑[47351528-57e8-4f1f-af82-d8f97b230c87] running status set to false 
[INFO ] 2024-06-27 11:27:42.781 - [任务 1][表编辑] - Node 表编辑[47351528-57e8-4f1f-af82-d8f97b230c87] schema data cleaned 
[INFO ] 2024-06-27 11:27:42.785 - [任务 1][表编辑] - Node 表编辑[47351528-57e8-4f1f-af82-d8f97b230c87] monitor closed 
[INFO ] 2024-06-27 11:27:42.791 - [任务 1][表编辑] - Node 表编辑[47351528-57e8-4f1f-af82-d8f97b230c87] close complete, cost 7 ms 
[INFO ] 2024-06-27 11:27:42.791 - [任务 1][targetDummy] - Node targetDummy[19c5aeff-8f1a-402a-a27f-455959cf9594] running status set to false 
[INFO ] 2024-06-27 11:27:42.830 - [任务 1][targetDummy] - Stop connector: first 1719458824159 3918ms, last 1719458861176 1428ms, counts: 36/38445ms, min: 1069, max: 4508, QPS: 0/s 
[INFO ] 2024-06-27 11:27:42.831 - [任务 1][targetDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-19c5aeff-8f1a-402a-a27f-455959cf9594 
[INFO ] 2024-06-27 11:27:42.832 - [任务 1][targetDummy] - PDK connector node released: HazelcastTargetPdkDataNode-19c5aeff-8f1a-402a-a27f-455959cf9594 
[INFO ] 2024-06-27 11:27:42.833 - [任务 1][targetDummy] - Node targetDummy[19c5aeff-8f1a-402a-a27f-455959cf9594] schema data cleaned 
[INFO ] 2024-06-27 11:27:42.833 - [任务 1][targetDummy] - Node targetDummy[19c5aeff-8f1a-402a-a27f-455959cf9594] monitor closed 
[INFO ] 2024-06-27 11:27:42.834 - [任务 1][targetDummy] - Node targetDummy[19c5aeff-8f1a-402a-a27f-455959cf9594] close complete, cost 46 ms 
[INFO ] 2024-06-27 11:27:43.243 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-27 11:27:43.251 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-06-27 11:27:43.252 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-06-27 11:27:43.301 - [任务 1] - Remove memory task client succeed, task: 任务 1[667cdbed13fc7a5f4e97fd00] 
[INFO ] 2024-06-27 11:27:43.301 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[667cdbed13fc7a5f4e97fd00] 
