[INFO ] 2024-07-17 18:36:30.043 - [来自NewSource的共享挖掘任务] - Start task milestones: 66979eacb92eda1a86f5239b(来自NewSource的共享挖掘任务) 
[INFO ] 2024-07-17 18:36:30.153 - [来自NewSource的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-17 18:36:30.228 - [来自NewSource的共享挖掘任务] - The engine receives 来自NewSource的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 18:36:30.435 - [来自NewSource的共享挖掘任务][NewSource] - Node NewSource[f3c43875d06f4731a2489590a3e0f7af] start preload schema,table counts: 2 
[INFO ] 2024-07-17 18:36:30.451 - [来自NewSource的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-17 18:36:30.451 - [来自NewSource的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-17 18:36:30.452 - [来自NewSource的共享挖掘任务][NewSource] - Node NewSource[f3c43875d06f4731a2489590a3e0f7af] preload schema finished, cost 2 ms 
[INFO ] 2024-07-17 18:36:30.638 - [来自NewSource的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979ead8e4a90a908f1d23f, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66979e7cb92eda1a86f52345__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_447599453, shareCdcTaskId=66979eacb92eda1a86f5239b, connectionId=66979e7cb92eda1a86f52345) 
[INFO ] 2024-07-17 18:36:30.638 - [来自NewSource的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979ead8e4a90a908f1d240, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=66979e7cb92eda1a86f52345_TESTPOLICY, version=v2, tableName=TESTPOLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1547021255, shareCdcTaskId=66979eacb92eda1a86f5239b, connectionId=66979e7cb92eda1a86f52345) 
[INFO ] 2024-07-17 18:36:30.640 - [来自NewSource的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自NewSource的共享挖掘任务_TESTPOLICY', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_1547021255', head seq: 0, tail seq: -1 
[INFO ] 2024-07-17 18:36:30.715 - [来自NewSource的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自NewSource的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_447599453', head seq: 0, tail seq: -1 
[INFO ] 2024-07-17 18:36:30.832 - [来自NewSource的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://root:******@192.168.1.184:57017/tapdatav310?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-17 18:36:31.396 - [来自NewSource的共享挖掘任务][NewSource] - Source node "NewSource" read batch size: 2000 
[INFO ] 2024-07-17 18:36:31.396 - [来自NewSource的共享挖掘任务][NewSource] - Source node "NewSource" event queue capacity: 4000 
[INFO ] 2024-07-17 18:36:31.396 - [来自NewSource的共享挖掘任务][NewSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 18:36:31.548 - [来自NewSource的共享挖掘任务][NewSource] - batch offset found: {},stream offset found: {"cdcOffset":1721212591,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 18:36:31.715 - [来自NewSource的共享挖掘任务][NewSource] - Starting stream read, table list: [TESTPOLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721212591,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 18:36:31.918 - [来自NewSource的共享挖掘任务][NewSource] - Connector MongoDB incremental start succeed, tables: [TESTPOLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-17 19:08:44.896 - [来自NewSource的共享挖掘任务][NewSource] - Node NewSource[f3c43875d06f4731a2489590a3e0f7af] running status set to false 
[INFO ] 2024-07-17 19:08:44.896 - [来自NewSource的共享挖掘任务][NewSource] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-f3c43875d06f4731a2489590a3e0f7af 
[INFO ] 2024-07-17 19:08:44.896 - [来自NewSource的共享挖掘任务][NewSource] - PDK connector node released: HazelcastSourcePdkShareCDCNode-f3c43875d06f4731a2489590a3e0f7af 
[INFO ] 2024-07-17 19:08:44.896 - [来自NewSource的共享挖掘任务][NewSource] - Node NewSource[f3c43875d06f4731a2489590a3e0f7af] schema data cleaned 
[INFO ] 2024-07-17 19:08:44.896 - [来自NewSource的共享挖掘任务][NewSource] - Node NewSource[f3c43875d06f4731a2489590a3e0f7af] monitor closed 
[INFO ] 2024-07-17 19:08:44.897 - [来自NewSource的共享挖掘任务][NewSource] - Node NewSource[f3c43875d06f4731a2489590a3e0f7af] close complete, cost 13 ms 
[INFO ] 2024-07-17 19:08:44.898 - [来自NewSource的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[35e7b7a7bb3f4e47b57ef5d44e38ebac] running status set to false 
[INFO ] 2024-07-17 19:08:44.989 - [来自NewSource的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-17 19:08:44.989 - [来自NewSource的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-17 19:08:44.989 - [来自NewSource的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[35e7b7a7bb3f4e47b57ef5d44e38ebac] schema data cleaned 
[INFO ] 2024-07-17 19:08:44.989 - [来自NewSource的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[35e7b7a7bb3f4e47b57ef5d44e38ebac] monitor closed 
[INFO ] 2024-07-17 19:08:44.990 - [来自NewSource的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[35e7b7a7bb3f4e47b57ef5d44e38ebac] close complete, cost 92 ms 
[INFO ] 2024-07-17 19:08:48.890 - [来自NewSource的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 19:08:48.891 - [来自NewSource的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@20a3d255 
[INFO ] 2024-07-17 19:08:48.891 - [来自NewSource的共享挖掘任务] - Stop task milestones: 66979eacb92eda1a86f5239b(来自NewSource的共享挖掘任务)  
[INFO ] 2024-07-17 19:08:49.031 - [来自NewSource的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-17 19:08:49.031 - [来自NewSource的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 19:08:49.081 - [来自NewSource的共享挖掘任务] - Remove memory task client succeed, task: 来自NewSource的共享挖掘任务[66979eacb92eda1a86f5239b] 
[INFO ] 2024-07-17 19:08:49.081 - [来自NewSource的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自NewSource的共享挖掘任务[66979eacb92eda1a86f5239b] 
[INFO ] 2024-07-17 19:21:58.337 - [来自NewSource的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自NewSource的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_447599453', head seq: 0, tail seq: 1923 
[INFO ] 2024-07-17 19:21:58.338 - [来自NewSource的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自NewSource的共享挖掘任务_TESTPOLICY', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_1547021255', head seq: 0, tail seq: 0 
