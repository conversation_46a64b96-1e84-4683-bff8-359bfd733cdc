[INFO ] 2024-03-28 08:41:21.681 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 08:41:21.694 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 08:41:21.896 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 08:41:22.253 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 08:41:22.253 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:41:22.253 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:41:22.254 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:41:22.372 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 114 ms 
[INFO ] 2024-03-28 08:41:22.374 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 113 ms 
[INFO ] 2024-03-28 08:41:22.378 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 124 ms 
[INFO ] 2024-03-28 08:41:22.723 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 08:41:22.723 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 08:41:22.723 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 08:41:22.735 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 08:41:22.921 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 08:41:22.924 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 08:41:22.924 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 08:41:22.944 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 08:41:23.751 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 08:41:45.588 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 08:41:51.874 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 08:41:51.876 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 08:41:56.995 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 08:46:06.593 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 08:47:19.523 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 08:47:22.202 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@13c09b1: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711586521970,"tableId":"test5","time":1711586521694,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@13c09b1: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711586521970,"tableId":"test5","time":1711586521694,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@13c09b1: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711586521970,"tableId":"test5","time":1711586521694,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 08:47:22.328 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 08:47:22.562 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 08:47:22.593 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 08:47:22.596 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 08:47:22.623 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 08:47:22.623 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 08:47:22.624 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 08:47:22.629 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 08:47:22.633 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 98 ms 
[INFO ] 2024-03-28 08:47:22.660 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 08:47:22.660 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-1d67565e-8657-4f04-9f7f-8ae30ec801b0 
[INFO ] 2024-03-28 08:47:22.661 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-1d67565e-8657-4f04-9f7f-8ae30ec801b0 
[INFO ] 2024-03-28 08:47:22.661 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 08:47:22.698 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-7e50da7d-f3e3-4dd1-bea2-0dde11821357 
[INFO ] 2024-03-28 08:47:22.699 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-7e50da7d-f3e3-4dd1-bea2-0dde11821357 
[INFO ] 2024-03-28 08:47:22.699 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 08:47:22.714 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 08:47:22.728 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 08:47:22.729 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 92 ms 
[INFO ] 2024-03-28 08:47:22.729 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 08:47:22.778 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 08:47:22.778 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 08:47:22.778 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 08:47:22.778 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 08:47:22.780 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 50 ms 
[INFO ] 2024-03-28 08:47:24.250 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 08:47:24.253 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 08:47:24.277 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 08:47:24.280 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 08:47:24.284 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 08:47:24.304 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 08:47:24.306 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 08:47:36.646 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 08:47:36.666 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 08:47:36.666 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 08:47:36.852 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 08:47:36.852 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:47:36.853 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:47:36.853 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:47:36.886 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 36 ms 
[INFO ] 2024-03-28 08:47:36.886 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 37 ms 
[INFO ] 2024-03-28 08:47:37.088 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 38 ms 
[INFO ] 2024-03-28 08:47:37.697 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 08:47:37.769 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 08:47:37.771 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 08:47:37.771 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 08:47:37.771 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 08:47:37.871 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 08:47:37.874 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 08:47:37.875 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 08:47:38.081 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 08:47:44.031 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 08:47:44.032 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 08:47:44.032 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 08:47:44.032 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 08:52:35.445 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 08:53:17.342 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 08:53:20.054 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@1ff75dc6: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711586867984,"tableId":"test5","time":1711586867984,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@1ff75dc6: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711586867984,"tableId":"test5","time":1711586867984,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@1ff75dc6: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711586867984,"tableId":"test5","time":1711586867984,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 08:53:20.125 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 08:53:20.465 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 08:53:20.465 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 08:53:20.478 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 08:53:20.479 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 08:53:20.479 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 08:53:20.479 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 08:53:20.481 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 08:53:20.482 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 30 ms 
[INFO ] 2024-03-28 08:53:20.490 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 08:53:20.497 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-2e88a458-8496-48da-927c-b1f6bccc6a32 
[INFO ] 2024-03-28 08:53:20.497 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-2e88a458-8496-48da-927c-b1f6bccc6a32 
[INFO ] 2024-03-28 08:53:20.504 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 08:53:20.524 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-cf37ced6-f71d-43ea-a0aa-0e5fbae637b2 
[INFO ] 2024-03-28 08:53:20.524 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-cf37ced6-f71d-43ea-a0aa-0e5fbae637b2 
[INFO ] 2024-03-28 08:53:20.539 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 08:53:20.541 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 08:53:20.541 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 08:53:20.542 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 58 ms 
[INFO ] 2024-03-28 08:53:20.542 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 08:53:20.580 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 08:53:20.580 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 08:53:20.581 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 08:53:20.581 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 08:53:20.787 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 40 ms 
[INFO ] 2024-03-28 08:53:22.192 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 08:53:22.197 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 08:53:22.207 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 08:53:22.208 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 08:53:22.209 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 08:53:22.231 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 08:53:22.231 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 08:53:35.348 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 08:53:35.348 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 08:53:35.460 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 08:53:35.460 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 08:53:35.501 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:53:35.501 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:53:35.501 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:53:35.529 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 24 ms 
[INFO ] 2024-03-28 08:53:35.529 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 25 ms 
[INFO ] 2024-03-28 08:53:35.539 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 36 ms 
[INFO ] 2024-03-28 08:53:36.369 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 08:53:36.370 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 08:53:36.370 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 08:53:36.371 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[INFO ] 2024-03-28 08:53:36.447 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-03-28 08:53:36.447 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 08:53:36.461 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 08:53:36.462 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 08:53:36.669 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 08:53:42.585 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 08:53:42.586 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 08:53:42.586 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 08:53:42.587 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 08:54:33.885 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 08:55:59.441 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 08:55:59.679 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@681a6755: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711587225410,"tableId":"test5","time":1711587225410,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@681a6755: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711587225410,"tableId":"test5","time":1711587225410,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@681a6755: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711587225410,"tableId":"test5","time":1711587225410,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 08:55:59.680 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 08:55:59.712 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 08:55:59.712 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 08:55:59.727 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 08:55:59.730 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 08:55:59.734 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 08:55:59.734 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 08:55:59.734 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 08:55:59.734 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 25 ms 
[INFO ] 2024-03-28 08:55:59.734 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 08:55:59.748 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-95b5b08a-8117-491e-b09b-f77f2302a765 
[INFO ] 2024-03-28 08:55:59.748 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-95b5b08a-8117-491e-b09b-f77f2302a765 
[INFO ] 2024-03-28 08:55:59.748 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 08:55:59.762 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-d2bb0b46-cc79-4e7c-8c6c-224a1bb158a1 
[INFO ] 2024-03-28 08:55:59.763 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-d2bb0b46-cc79-4e7c-8c6c-224a1bb158a1 
[INFO ] 2024-03-28 08:55:59.763 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 08:55:59.771 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 08:55:59.771 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 08:55:59.773 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 43 ms 
[INFO ] 2024-03-28 08:55:59.773 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 08:55:59.790 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 08:55:59.790 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 08:55:59.792 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 08:55:59.792 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 08:55:59.792 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 17 ms 
[INFO ] 2024-03-28 08:56:47.759 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 08:56:47.762 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 08:56:47.901 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 08:56:47.901 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 08:56:48.150 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:56:48.152 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:56:48.153 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:56:48.229 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 68 ms 
[INFO ] 2024-03-28 08:56:48.231 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 73 ms 
[INFO ] 2024-03-28 08:56:48.232 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 74 ms 
[INFO ] 2024-03-28 08:56:49.314 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 08:56:49.316 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 08:56:49.316 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 08:56:49.523 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[INFO ] 2024-03-28 08:56:49.587 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-03-28 08:56:49.589 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 08:56:49.590 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 08:56:49.590 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 08:56:49.798 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 08:56:56.141 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 08:56:56.142 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 08:56:56.150 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 08:56:56.162 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 08:57:23.791 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 08:58:03.571 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 08:58:03.912 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@74a02ea8: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711587420098,"tableId":"test5","time":1711587420098,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@74a02ea8: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711587420098,"tableId":"test5","time":1711587420098,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@74a02ea8: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711587420098,"tableId":"test5","time":1711587420098,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 08:58:04.116 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 08:58:04.391 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 08:58:04.405 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 08:58:04.407 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 08:58:04.423 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 08:58:04.424 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 08:58:04.424 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 08:58:04.430 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 08:58:04.430 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 48 ms 
[INFO ] 2024-03-28 08:58:04.447 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 08:58:04.447 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-a20ba896-1d0b-470c-bb6e-1781ebd845dd 
[INFO ] 2024-03-28 08:58:04.448 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-a20ba896-1d0b-470c-bb6e-1781ebd845dd 
[INFO ] 2024-03-28 08:58:04.448 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 08:58:04.466 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-323a9235-fca0-4b9a-9f63-4c21ea6165c5 
[INFO ] 2024-03-28 08:58:04.468 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-323a9235-fca0-4b9a-9f63-4c21ea6165c5 
[INFO ] 2024-03-28 08:58:04.468 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 08:58:04.475 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 08:58:04.481 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 08:58:04.482 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 48 ms 
[INFO ] 2024-03-28 08:58:04.488 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 08:58:04.584 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 08:58:04.584 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 08:58:04.584 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 08:58:04.584 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 08:58:04.795 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 104 ms 
[INFO ] 2024-03-28 08:58:08.485 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 08:58:08.520 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 08:58:08.520 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 08:58:08.546 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 08:58:08.547 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 08:58:08.585 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 08:58:08.586 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 08:58:20.147 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 08:58:20.149 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 08:58:20.238 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 08:58:20.240 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 08:58:20.305 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:58:20.306 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:58:20.306 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:58:20.332 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 25 ms 
[INFO ] 2024-03-28 08:58:20.333 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 25 ms 
[INFO ] 2024-03-28 08:58:20.333 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 23 ms 
[INFO ] 2024-03-28 08:58:21.099 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 08:58:21.100 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 08:58:21.100 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 08:58:21.100 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 08:58:21.166 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 08:58:21.167 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 08:58:21.168 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 08:58:21.228 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 08:58:21.230 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 08:58:27.320 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 08:58:27.320 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 08:58:27.321 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 08:58:27.321 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 08:58:44.449 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 09:04:01.771 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 09:05:29.815 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@7273e262: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711587509795,"tableId":"test5","time":1711587509795,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@7273e262: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711587509795,"tableId":"test5","time":1711587509795,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@7273e262: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711587509795,"tableId":"test5","time":1711587509795,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 09:05:29.816 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 09:05:30.154 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 09:05:30.162 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 09:05:30.176 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 09:05:30.177 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:05:30.177 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:05:30.178 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 09:05:30.182 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 09:05:30.183 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 46 ms 
[INFO ] 2024-03-28 09:05:30.195 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 09:05:30.195 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-27dc2919-cf52-46e1-8947-b862383f8849 
[INFO ] 2024-03-28 09:05:30.196 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-27dc2919-cf52-46e1-8947-b862383f8849 
[INFO ] 2024-03-28 09:05:30.197 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:05:30.207 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-d9215d7d-8a63-4c4e-b016-5d8e2421e021 
[INFO ] 2024-03-28 09:05:30.207 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-d9215d7d-8a63-4c4e-b016-5d8e2421e021 
[INFO ] 2024-03-28 09:05:30.218 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 09:05:30.218 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 09:05:30.221 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 09:05:30.223 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 38 ms 
[INFO ] 2024-03-28 09:05:30.224 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 09:05:30.237 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:05:30.240 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:05:30.240 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 09:05:30.240 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 09:05:30.240 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 18 ms 
[INFO ] 2024-03-28 09:05:34.718 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 09:05:34.718 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 09:05:34.755 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 09:05:34.755 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 09:05:34.787 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 09:05:34.788 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 09:05:34.788 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 09:06:32.631 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 09:06:32.632 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 09:06:32.681 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 09:06:32.865 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 09:06:32.866 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:06:32.873 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:06:32.873 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:06:32.968 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 103 ms 
[INFO ] 2024-03-28 09:06:32.968 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 95 ms 
[INFO ] 2024-03-28 09:06:32.968 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 92 ms 
[INFO ] 2024-03-28 09:06:33.893 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 09:06:33.959 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 09:06:33.959 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 09:06:33.959 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 09:06:33.960 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 09:06:34.047 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 09:06:34.053 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 09:06:34.053 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 09:06:34.053 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 09:06:40.194 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 09:06:40.194 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 09:06:40.200 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 09:06:44.289 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 09:10:03.953 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 09:10:36.453 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 09:10:39.398 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@7d41f890: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711588006475,"tableId":"test5","time":1711588006475,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@7d41f890: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711588006475,"tableId":"test5","time":1711588006475,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@7d41f890: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711588006475,"tableId":"test5","time":1711588006475,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 09:10:39.399 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 09:10:39.834 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 09:10:39.849 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 09:10:39.849 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 09:10:39.859 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:10:39.859 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:10:39.860 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 09:10:39.864 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 09:10:39.867 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 38 ms 
[INFO ] 2024-03-28 09:10:39.867 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 09:10:39.882 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-31850748-4b13-4529-b824-7217b2d36318 
[INFO ] 2024-03-28 09:10:39.882 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-31850748-4b13-4529-b824-7217b2d36318 
[INFO ] 2024-03-28 09:10:39.882 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:10:39.897 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-0989c245-460a-4b40-aedb-b54c2943aee0 
[INFO ] 2024-03-28 09:10:39.899 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-0989c245-460a-4b40-aedb-b54c2943aee0 
[INFO ] 2024-03-28 09:10:39.899 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 09:10:39.908 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 09:10:39.908 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 09:10:39.910 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 42 ms 
[INFO ] 2024-03-28 09:10:39.911 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 09:10:39.929 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:10:39.929 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:10:39.929 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 09:10:39.929 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 09:10:40.141 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 27 ms 
[INFO ] 2024-03-28 09:10:41.273 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 09:10:41.282 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 09:10:41.283 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 09:10:41.293 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 09:10:41.293 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 09:10:41.311 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 09:10:41.312 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 09:10:49.792 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 09:10:49.792 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 09:10:49.811 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 09:10:49.911 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 09:10:49.911 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:10:49.911 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:10:49.911 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:10:49.932 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 20 ms 
[INFO ] 2024-03-28 09:10:49.932 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 21 ms 
[INFO ] 2024-03-28 09:10:49.932 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 21 ms 
[INFO ] 2024-03-28 09:10:50.934 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 09:10:50.934 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 09:10:50.934 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 09:10:50.935 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 09:10:50.937 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 09:10:51.055 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 09:10:51.057 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 09:10:51.059 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 09:10:51.060 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 09:10:57.193 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 09:10:57.198 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 09:10:57.198 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 09:11:03.077 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 09:12:23.714 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 09:13:49.866 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 09:13:52.547 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@68642a97: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711588263640,"tableId":"test5","time":1711588263640,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@68642a97: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711588263640,"tableId":"test5","time":1711588263640,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@68642a97: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711588263640,"tableId":"test5","time":1711588263640,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 09:13:52.548 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 09:13:52.981 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 09:13:52.981 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 09:13:52.994 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 09:13:52.994 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:13:52.995 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:13:52.995 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 09:13:52.995 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 09:13:52.998 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 47 ms 
[INFO ] 2024-03-28 09:13:52.998 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 09:13:53.013 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-eed079af-bcd0-4094-b913-4abdf71d9d11 
[INFO ] 2024-03-28 09:13:53.013 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-eed079af-bcd0-4094-b913-4abdf71d9d11 
[INFO ] 2024-03-28 09:13:53.028 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:13:53.029 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-1dda5d21-240e-4f6b-b547-051405c9dd57 
[INFO ] 2024-03-28 09:13:53.029 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-1dda5d21-240e-4f6b-b547-051405c9dd57 
[INFO ] 2024-03-28 09:13:53.029 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 09:13:53.038 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 09:13:53.038 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 09:13:53.040 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 40 ms 
[INFO ] 2024-03-28 09:13:53.040 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 09:13:53.062 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:13:53.062 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:13:53.062 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 09:13:53.062 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 09:13:53.264 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 23 ms 
[INFO ] 2024-03-28 09:13:54.474 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 09:13:54.484 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 09:13:54.511 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 09:13:54.511 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 09:13:54.544 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 09:13:54.548 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 09:13:54.548 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 09:14:03.800 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 09:14:03.885 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 09:14:03.885 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 09:14:04.017 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 09:14:04.022 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:14:04.022 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:14:04.022 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:14:04.056 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 33 ms 
[INFO ] 2024-03-28 09:14:04.056 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 32 ms 
[INFO ] 2024-03-28 09:14:04.057 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 31 ms 
[INFO ] 2024-03-28 09:14:04.840 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 09:14:04.840 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 09:14:04.843 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 09:14:04.843 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 09:14:04.912 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 09:14:04.912 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 09:14:04.918 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 09:14:04.918 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 09:14:05.152 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 09:14:11.054 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 09:14:11.055 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 09:14:11.059 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 09:14:13.957 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 09:18:30.293 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 09:18:34.816 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 09:18:35.150 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@27dfe873: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711588455157,"tableId":"test5","time":1711588455157,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@27dfe873: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711588455157,"tableId":"test5","time":1711588455157,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@27dfe873: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711588455157,"tableId":"test5","time":1711588455157,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 09:18:35.223 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 09:18:35.224 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 09:18:35.645 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 09:18:35.645 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 09:18:35.658 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 09:18:35.658 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:18:35.659 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:18:35.659 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 09:18:35.663 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 09:18:35.663 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 24 ms 
[INFO ] 2024-03-28 09:18:35.673 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 09:18:35.673 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-6a92d44d-506d-4890-8b91-92c2dca7dc6c 
[INFO ] 2024-03-28 09:18:35.674 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-6a92d44d-506d-4890-8b91-92c2dca7dc6c 
[INFO ] 2024-03-28 09:18:35.674 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:18:35.683 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-128c215d-e34a-4ae7-8a47-15438590dbe9 
[INFO ] 2024-03-28 09:18:35.683 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-128c215d-e34a-4ae7-8a47-15438590dbe9 
[INFO ] 2024-03-28 09:18:35.683 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 09:18:35.692 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 09:18:35.692 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 09:18:35.692 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 29 ms 
[INFO ] 2024-03-28 09:18:35.705 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 09:18:35.705 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:18:35.705 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:18:35.705 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 09:18:35.706 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 09:18:35.912 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 13 ms 
[INFO ] 2024-03-28 09:18:40.244 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 09:18:40.258 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 09:18:40.258 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 09:18:40.336 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 09:18:40.342 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 09:18:40.359 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 09:18:40.360 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 09:20:31.735 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 09:20:31.736 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 09:20:31.737 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 09:20:31.857 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 09:20:32.645 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:20:32.655 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:20:32.655 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:20:32.889 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 231 ms 
[INFO ] 2024-03-28 09:20:32.904 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 258 ms 
[INFO ] 2024-03-28 09:20:32.904 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 257 ms 
[INFO ] 2024-03-28 09:20:34.065 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 09:20:34.066 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 09:20:34.066 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 09:20:34.077 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[INFO ] 2024-03-28 09:20:34.405 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-03-28 09:20:34.426 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 09:20:34.428 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 09:20:34.429 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 09:20:34.450 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 09:20:40.804 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 09:20:40.808 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 09:20:40.808 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 09:20:45.439 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 09:21:03.568 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 09:21:50.465 - [任务 28][增强JS] - Alter table schema transform finished 
[INFO ] 2024-03-28 09:22:11.919 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[ERROR] 2024-03-28 09:22:11.920 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@381d6089: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711588846546,"tableId":"test5","time":1711588846546,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@381d6089: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711588846546,"tableId":"test5","time":1711588846546,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@381d6089: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711588846546,"tableId":"test5","time":1711588846546,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 09:22:12.102 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[ERROR] 2024-03-28 09:22:12.147 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 09:22:12.148 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:22:12.148 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:22:12.151 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 09:22:12.164 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 09:22:12.165 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 75 ms 
[INFO ] 2024-03-28 09:22:12.165 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 09:22:12.197 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-64b1bf8c-cbc5-4314-98ae-a21b1181a68f 
[INFO ] 2024-03-28 09:22:12.201 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-64b1bf8c-cbc5-4314-98ae-a21b1181a68f 
[INFO ] 2024-03-28 09:22:12.202 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:22:12.232 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-f373f13e-7b44-4d2f-9d3d-f7d58710f6dc 
[INFO ] 2024-03-28 09:22:12.233 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-f373f13e-7b44-4d2f-9d3d-f7d58710f6dc 
[INFO ] 2024-03-28 09:22:12.233 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 09:22:12.248 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 09:22:12.249 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 09:22:12.255 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 89 ms 
[INFO ] 2024-03-28 09:22:12.306 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 09:22:12.306 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:22:12.306 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:22:12.307 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 09:22:12.307 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 09:22:12.308 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 53 ms 
[INFO ] 2024-03-28 09:22:16.718 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 09:22:16.719 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 09:22:16.720 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 09:22:16.762 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 09:22:16.762 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 09:23:28.253 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 09:23:28.257 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 09:23:28.365 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 09:23:28.365 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 09:23:28.412 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:23:28.418 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:23:28.420 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:23:28.458 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 36 ms 
[INFO ] 2024-03-28 09:23:28.464 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 45 ms 
[INFO ] 2024-03-28 09:23:28.465 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 44 ms 
[INFO ] 2024-03-28 09:23:29.279 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 09:23:29.422 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 09:23:29.422 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 09:23:29.428 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 09:23:29.430 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 09:23:29.502 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 09:23:29.502 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 09:23:29.502 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 09:23:29.505 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 09:23:35.641 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 09:23:35.642 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 09:23:35.642 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 09:23:35.643 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 09:23:58.103 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 09:25:05.744 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 09:25:05.761 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@6eef8778: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711589019043,"tableId":"test5","time":1711589019043,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@6eef8778: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711589019043,"tableId":"test5","time":1711589019043,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@6eef8778: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711589019043,"tableId":"test5","time":1711589019043,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 09:25:05.762 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 09:25:05.762 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 09:25:05.762 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 09:25:05.768 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 09:25:05.771 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:25:05.771 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:25:05.771 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 09:25:05.771 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 09:25:05.780 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 44 ms 
[INFO ] 2024-03-28 09:25:05.782 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 09:25:05.793 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-874e2b77-fd85-4e22-8fdd-9c29b2ee9488 
[INFO ] 2024-03-28 09:25:05.793 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-874e2b77-fd85-4e22-8fdd-9c29b2ee9488 
[INFO ] 2024-03-28 09:25:05.806 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:25:05.807 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-5973fe4f-4782-43c1-939d-ef050c419b41 
[INFO ] 2024-03-28 09:25:05.807 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-5973fe4f-4782-43c1-939d-ef050c419b41 
[INFO ] 2024-03-28 09:25:05.807 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 09:25:05.816 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 09:25:05.816 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 09:25:05.817 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 42 ms 
[INFO ] 2024-03-28 09:25:05.817 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 09:25:05.854 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:25:05.854 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:25:05.854 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 09:25:05.856 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 09:25:05.858 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 38 ms 
[INFO ] 2024-03-28 09:25:09.898 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 09:25:09.899 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 09:25:09.899 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 09:25:09.939 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 09:25:09.941 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 09:25:09.974 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 09:25:09.976 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 09:26:10.296 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 09:26:10.301 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 09:26:10.379 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 09:26:10.530 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 09:26:10.626 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:26:10.627 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:26:10.627 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:26:10.800 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 169 ms 
[INFO ] 2024-03-28 09:26:10.800 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 168 ms 
[INFO ] 2024-03-28 09:26:10.801 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 168 ms 
[INFO ] 2024-03-28 09:26:11.815 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 09:26:11.968 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 09:26:11.970 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 09:26:11.970 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 09:26:11.975 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 09:26:12.160 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 09:26:12.165 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 09:26:12.166 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 09:26:12.166 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 09:26:18.331 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 09:26:18.338 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 09:26:18.339 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 09:26:18.451 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 09:26:18.451 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 09:26:18.788 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 09:26:19.179 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@64ece4b3: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711589178364,"tableId":"test5","time":1711589178364,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@64ece4b3: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711589178364,"tableId":"test5","time":1711589178364,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@64ece4b3: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711589178364,"tableId":"test5","time":1711589178364,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 09:26:19.339 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 09:26:19.339 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 09:26:19.355 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 09:26:19.355 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 09:26:19.360 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:26:19.360 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:26:19.363 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 09:26:19.363 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 09:26:19.370 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 28 ms 
[INFO ] 2024-03-28 09:26:19.370 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 09:26:19.381 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-bdde2b79-6e84-4568-baeb-a60001ef5f67 
[INFO ] 2024-03-28 09:26:19.382 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-bdde2b79-6e84-4568-baeb-a60001ef5f67 
[INFO ] 2024-03-28 09:26:19.383 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:26:19.396 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-0b06e291-398b-4535-873c-746bedd43877 
[INFO ] 2024-03-28 09:26:19.397 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-0b06e291-398b-4535-873c-746bedd43877 
[INFO ] 2024-03-28 09:26:19.397 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 09:26:19.409 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 09:26:19.409 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 09:26:19.411 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 41 ms 
[INFO ] 2024-03-28 09:26:19.412 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 09:26:19.486 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:26:19.486 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:26:19.487 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 09:26:19.487 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 09:26:19.488 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 77 ms 
[INFO ] 2024-03-28 09:26:20.211 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 09:26:20.225 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 09:26:20.226 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 09:26:20.290 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 09:26:20.290 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 09:26:20.324 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 09:26:20.530 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 09:27:35.990 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 09:27:36.117 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 09:27:36.119 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 09:27:36.246 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 09:27:37.133 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:27:37.141 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:27:37.142 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:27:37.499 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 360 ms 
[INFO ] 2024-03-28 09:27:37.500 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 361 ms 
[INFO ] 2024-03-28 09:27:37.708 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 367 ms 
[INFO ] 2024-03-28 09:27:38.572 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 09:27:38.581 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 09:27:38.581 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 09:27:38.582 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[INFO ] 2024-03-28 09:27:38.698 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-03-28 09:27:38.962 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 09:27:38.972 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 09:27:38.989 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 09:27:38.990 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 09:27:45.340 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 09:27:45.344 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 09:27:45.345 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 09:27:47.386 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 09:28:02.982 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 09:30:36.673 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 09:31:10.716 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@52c354f5: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711589269376,"tableId":"test5","time":1711589269376,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@52c354f5: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711589269376,"tableId":"test5","time":1711589269376,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@52c354f5: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711589269376,"tableId":"test5","time":1711589269376,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 09:31:10.716 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 09:31:11.107 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 09:31:11.118 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 09:31:11.119 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 09:31:11.158 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:31:11.162 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:31:11.166 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 09:31:11.166 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 09:31:11.172 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 67 ms 
[INFO ] 2024-03-28 09:31:11.175 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 09:31:11.191 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-7b3edfad-69a4-4f04-8b2b-d277dd20152a 
[INFO ] 2024-03-28 09:31:11.193 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-7b3edfad-69a4-4f04-8b2b-d277dd20152a 
[INFO ] 2024-03-28 09:31:11.193 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:31:11.210 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-309dc20d-6678-4b4d-8a85-b185b4ebabe2 
[INFO ] 2024-03-28 09:31:11.218 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-309dc20d-6678-4b4d-8a85-b185b4ebabe2 
[INFO ] 2024-03-28 09:31:11.218 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 09:31:11.236 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 09:31:11.238 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 09:31:11.240 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 64 ms 
[INFO ] 2024-03-28 09:31:11.240 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 09:31:11.274 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:31:11.275 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:31:11.275 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 09:31:11.275 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 09:31:11.277 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 36 ms 
[INFO ] 2024-03-28 09:31:13.373 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 09:31:13.434 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 09:31:13.435 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 09:31:13.484 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 09:31:13.487 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 09:31:13.596 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 09:31:13.802 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 09:37:15.037 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 09:37:15.041 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 09:37:15.069 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 09:37:15.214 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 09:37:15.217 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:37:15.218 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:37:15.218 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:37:15.271 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 52 ms 
[INFO ] 2024-03-28 09:37:15.275 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 52 ms 
[INFO ] 2024-03-28 09:37:15.275 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 53 ms 
[INFO ] 2024-03-28 09:37:16.014 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 09:37:16.166 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 09:37:16.166 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 09:37:16.170 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 09:37:16.238 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 09:37:16.238 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 09:37:16.239 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 09:37:16.239 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 09:37:16.472 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 09:37:22.365 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 09:37:22.367 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 09:37:22.367 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 09:37:22.367 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 09:38:54.552 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 09:38:54.660 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 09:38:54.809 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@682c6fa9: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711589845449,"tableId":"test5","time":1711589845449,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@682c6fa9: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711589845449,"tableId":"test5","time":1711589845449,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@682c6fa9: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711589845449,"tableId":"test5","time":1711589845449,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 09:38:54.851 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 09:38:54.851 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 09:38:54.865 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 09:38:54.867 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 09:38:54.870 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:38:54.870 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 09:38:54.872 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 09:38:54.876 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 09:38:54.876 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 24 ms 
[INFO ] 2024-03-28 09:38:54.876 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 09:38:54.888 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-8c998769-5a63-41b1-9eee-fde683911ac8 
[INFO ] 2024-03-28 09:38:54.893 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-8c998769-5a63-41b1-9eee-fde683911ac8 
[INFO ] 2024-03-28 09:38:54.893 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 09:38:54.906 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-8444031a-b9d4-43df-bec0-731d4713ada1 
[INFO ] 2024-03-28 09:38:54.910 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-8444031a-b9d4-43df-bec0-731d4713ada1 
[INFO ] 2024-03-28 09:38:54.911 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 09:38:54.921 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 09:38:54.921 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 09:38:54.923 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 48 ms 
[INFO ] 2024-03-28 09:38:54.923 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 09:38:54.945 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:38:54.945 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 09:38:54.945 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 09:38:54.945 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 09:38:54.946 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 23 ms 
[INFO ] 2024-03-28 09:38:59.409 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 09:38:59.434 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 09:38:59.444 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 09:38:59.473 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 09:38:59.474 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 09:38:59.496 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 09:38:59.496 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 10:07:55.801 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 10:07:55.830 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 10:07:55.830 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 10:07:55.925 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 10:07:55.925 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:07:55.925 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:07:55.925 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:07:55.949 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 24 ms 
[INFO ] 2024-03-28 10:07:55.949 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 24 ms 
[INFO ] 2024-03-28 10:07:55.949 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 24 ms 
[INFO ] 2024-03-28 10:07:56.737 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 10:07:56.875 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 10:07:56.876 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 10:07:56.876 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 10:07:56.876 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 10:07:56.931 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 10:07:56.931 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 10:07:56.931 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 10:07:57.142 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 10:08:03.075 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:08:03.076 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 10:08:03.084 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:08:03.091 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 10:08:32.317 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 10:09:20.173 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 10:09:20.426 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@261d4431: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711591683107,"tableId":"test5","time":1711591683107,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@261d4431: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711591683107,"tableId":"test5","time":1711591683107,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@261d4431: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711591683107,"tableId":"test5","time":1711591683107,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 10:09:20.427 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 10:09:20.467 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 10:09:20.467 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 10:09:20.477 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 10:09:20.478 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 10:09:20.478 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 10:09:20.478 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 10:09:20.480 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 10:09:20.483 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 23 ms 
[INFO ] 2024-03-28 10:09:20.483 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 10:09:20.488 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-dc5d8fb1-f8fd-427d-a69c-48165f537b7b 
[INFO ] 2024-03-28 10:09:20.499 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-dc5d8fb1-f8fd-427d-a69c-48165f537b7b 
[INFO ] 2024-03-28 10:09:20.502 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 10:09:20.502 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-185c26bb-7a3d-4d9c-bddf-1d3d678feca5 
[INFO ] 2024-03-28 10:09:20.502 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-185c26bb-7a3d-4d9c-bddf-1d3d678feca5 
[INFO ] 2024-03-28 10:09:20.502 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 10:09:20.511 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 10:09:20.511 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 10:09:20.512 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 28 ms 
[INFO ] 2024-03-28 10:09:20.512 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 10:09:20.529 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 10:09:20.529 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 10:09:20.529 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 10:09:20.529 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 10:09:20.747 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 18 ms 
[INFO ] 2024-03-28 10:09:24.650 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 10:09:24.667 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 10:09:24.667 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 10:09:24.695 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 10:09:24.695 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 10:09:24.723 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 10:09:24.723 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 10:09:35.327 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 10:09:35.328 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 10:09:35.353 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 10:09:35.456 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 10:09:35.457 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:09:35.457 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:09:35.457 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:09:35.479 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 21 ms 
[INFO ] 2024-03-28 10:09:35.479 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 21 ms 
[INFO ] 2024-03-28 10:09:35.479 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 22 ms 
[INFO ] 2024-03-28 10:09:36.238 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 10:09:36.239 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 10:09:36.239 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 10:09:36.239 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 10:09:36.318 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 10:09:36.318 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 10:09:36.318 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 10:09:36.465 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 10:09:36.465 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 10:09:42.412 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:09:42.415 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 10:09:42.415 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:09:42.415 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 10:15:54.721 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 10:18:36.887 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 10:18:36.979 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@6f45d8cb: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711591782444,"tableId":"test5","time":1711591782444,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@6f45d8cb: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711591782444,"tableId":"test5","time":1711591782444,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@6f45d8cb: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711591782444,"tableId":"test5","time":1711591782444,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 10:18:36.983 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 10:18:37.471 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 10:18:37.486 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 10:18:37.486 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 10:18:37.494 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 10:18:37.495 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 10:18:37.495 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 10:18:37.495 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 10:18:37.499 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 35 ms 
[INFO ] 2024-03-28 10:18:37.499 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 10:18:37.501 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-e3666160-8bd3-40e8-bd2b-134163f8b38d 
[INFO ] 2024-03-28 10:18:37.501 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-e3666160-8bd3-40e8-bd2b-134163f8b38d 
[INFO ] 2024-03-28 10:18:37.502 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 10:18:37.518 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-add1f5fd-07d2-4f1c-8e93-774bcab5fa74 
[INFO ] 2024-03-28 10:18:37.518 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-add1f5fd-07d2-4f1c-8e93-774bcab5fa74 
[INFO ] 2024-03-28 10:18:37.520 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 10:18:37.529 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 10:18:37.530 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 10:18:37.530 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 33 ms 
[INFO ] 2024-03-28 10:18:37.530 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 10:18:37.563 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 10:18:37.563 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 10:18:37.563 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 10:18:37.563 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 10:18:37.768 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 33 ms 
[INFO ] 2024-03-28 10:18:41.488 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 10:18:41.489 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 10:18:41.489 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 10:18:41.520 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 10:18:41.520 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 10:18:41.541 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 10:18:41.544 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 10:19:37.645 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 10:19:37.645 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 10:19:37.695 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 10:19:37.885 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 10:19:37.885 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:19:37.885 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:19:37.885 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:19:37.940 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 50 ms 
[INFO ] 2024-03-28 10:19:37.944 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 48 ms 
[INFO ] 2024-03-28 10:19:37.944 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 49 ms 
[INFO ] 2024-03-28 10:19:38.748 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 10:19:38.881 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 10:19:38.882 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 10:19:38.882 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 10:19:38.883 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 10:19:38.981 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 10:19:38.983 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 10:19:38.984 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 10:19:39.193 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 10:19:45.092 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:19:45.092 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 10:19:45.092 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:19:45.103 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 10:19:45.219 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 10:19:45.219 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 10:19:45.562 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@189b92da: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711592385130,"tableId":"test5","time":1711592385130,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@189b92da: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711592385130,"tableId":"test5","time":1711592385130,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@189b92da: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711592385130,"tableId":"test5","time":1711592385130,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 10:19:45.565 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 10:19:45.605 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 10:19:45.605 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 10:19:45.616 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 10:19:45.616 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 10:19:45.616 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 10:19:45.617 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 10:19:45.620 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 10:19:45.620 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 21 ms 
[INFO ] 2024-03-28 10:19:45.625 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 10:19:45.625 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-f5dae619-a404-4973-8240-1e3b8c0b0aa5 
[INFO ] 2024-03-28 10:19:45.625 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-f5dae619-a404-4973-8240-1e3b8c0b0aa5 
[INFO ] 2024-03-28 10:19:45.625 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 10:19:45.635 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-8e23fe7f-ea4e-48b6-bffc-97d55d2743dc 
[INFO ] 2024-03-28 10:19:45.635 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-8e23fe7f-ea4e-48b6-bffc-97d55d2743dc 
[INFO ] 2024-03-28 10:19:45.635 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 10:19:45.638 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 10:19:45.638 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 10:19:45.638 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 17 ms 
[INFO ] 2024-03-28 10:19:45.638 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 10:19:45.674 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 10:19:45.674 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 10:19:45.674 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 10:19:45.674 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 10:19:45.885 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 36 ms 
[INFO ] 2024-03-28 10:19:46.646 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 10:19:46.666 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 10:19:46.667 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 10:19:46.693 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 10:19:46.693 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 10:19:46.711 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 10:19:46.714 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 10:19:56.973 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 10:19:56.976 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 10:19:57.072 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 10:19:57.072 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 10:19:57.123 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:19:57.123 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:19:57.123 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:19:57.152 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 28 ms 
[INFO ] 2024-03-28 10:19:57.153 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 28 ms 
[INFO ] 2024-03-28 10:19:57.153 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 27 ms 
[INFO ] 2024-03-28 10:19:57.847 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 10:19:57.997 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 10:19:57.998 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 10:19:57.998 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 10:19:57.998 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 10:19:58.045 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 10:19:58.045 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 10:19:58.046 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 10:19:58.246 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 10:20:04.209 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:20:04.211 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 10:20:04.211 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:20:04.211 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 10:20:06.797 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 10:25:54.910 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 10:25:55.154 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@18f73012: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711592404228,"tableId":"test5","time":1711592404228,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@18f73012: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711592404228,"tableId":"test5","time":1711592404228,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@18f73012: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711592404228,"tableId":"test5","time":1711592404228,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 10:25:55.367 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 10:25:55.677 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 10:25:55.686 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 10:25:55.686 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 10:25:55.692 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 10:25:55.692 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 10:25:55.693 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 10:25:55.693 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 10:25:55.694 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 18 ms 
[INFO ] 2024-03-28 10:25:55.699 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 10:25:55.702 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-bb79b39d-57b8-4bfd-8d1d-f4132a455645 
[INFO ] 2024-03-28 10:25:55.702 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-bb79b39d-57b8-4bfd-8d1d-f4132a455645 
[INFO ] 2024-03-28 10:25:55.702 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 10:25:55.717 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-26565ff4-c2b0-419e-bbee-dbc1b63582c5 
[INFO ] 2024-03-28 10:25:55.717 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-26565ff4-c2b0-419e-bbee-dbc1b63582c5 
[INFO ] 2024-03-28 10:25:55.717 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 10:25:55.726 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 10:25:55.726 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 10:25:55.726 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 31 ms 
[INFO ] 2024-03-28 10:25:55.726 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 10:25:55.753 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 10:25:55.753 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 10:25:55.753 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 10:25:55.753 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 10:25:55.954 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 27 ms 
[INFO ] 2024-03-28 10:25:59.726 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 10:25:59.726 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 10:25:59.750 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 10:25:59.750 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 10:25:59.750 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 10:25:59.793 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 10:25:59.794 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 10:26:20.713 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 10:26:20.714 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 10:26:20.743 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 10:26:20.844 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 10:26:20.845 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:26:20.846 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:26:20.875 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:26:20.876 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 26 ms 
[INFO ] 2024-03-28 10:26:20.876 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 31 ms 
[INFO ] 2024-03-28 10:26:20.876 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 31 ms 
[INFO ] 2024-03-28 10:26:21.695 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 10:26:21.772 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 10:26:21.774 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 10:26:21.775 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 10:26:21.775 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 10:26:21.830 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 10:26:21.830 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 10:26:21.831 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 10:26:22.039 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 10:26:27.915 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:26:27.917 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 10:26:27.917 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:26:27.917 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 10:26:29.818 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 10:26:54.209 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 10:26:54.526 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@1552c4e9: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711592787950,"tableId":"test5","time":1711592787950,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@1552c4e9: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711592787950,"tableId":"test5","time":1711592787950,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@1552c4e9: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711592787950,"tableId":"test5","time":1711592787950,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 10:26:54.526 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 10:26:55.026 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 10:26:55.040 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 10:26:55.041 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 10:26:55.055 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 10:26:55.055 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 10:26:55.056 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 10:26:55.061 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 10:26:55.064 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 39 ms 
[INFO ] 2024-03-28 10:26:55.064 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 10:26:55.071 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-c38ee3c7-fedd-4310-99ca-f05a6f401597 
[INFO ] 2024-03-28 10:26:55.071 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-c38ee3c7-fedd-4310-99ca-f05a6f401597 
[INFO ] 2024-03-28 10:26:55.071 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 10:26:55.084 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-102f7b81-9ae0-4247-80bf-f2a43b104f2c 
[INFO ] 2024-03-28 10:26:55.084 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-102f7b81-9ae0-4247-80bf-f2a43b104f2c 
[INFO ] 2024-03-28 10:26:55.084 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 10:26:55.096 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 10:26:55.096 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 10:26:55.097 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 32 ms 
[INFO ] 2024-03-28 10:26:55.097 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 10:26:55.120 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 10:26:55.120 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 10:26:55.120 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 10:26:55.120 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 10:26:55.324 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 23 ms 
[INFO ] 2024-03-28 10:26:59.116 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 10:26:59.117 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 10:26:59.117 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 10:26:59.147 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 10:26:59.148 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 10:26:59.215 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 10:26:59.215 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 10:31:14.099 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 10:31:14.100 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 10:31:14.100 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 10:31:14.100 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 10:31:14.100 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:31:14.101 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:31:14.101 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:31:14.139 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 35 ms 
[INFO ] 2024-03-28 10:31:14.141 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 35 ms 
[INFO ] 2024-03-28 10:31:14.141 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 35 ms 
[INFO ] 2024-03-28 10:31:14.874 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 10:31:14.874 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 10:31:14.874 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 10:31:14.874 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 10:31:14.927 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 10:31:14.928 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 10:31:14.928 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 10:31:14.940 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 10:31:15.141 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 10:31:21.054 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:31:21.054 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 10:31:21.054 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:31:21.054 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 10:31:23.080 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 10:33:02.313 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 10:33:02.766 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@4bdb725f: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711593081079,"tableId":"test5","time":1711593081079,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@4bdb725f: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711593081079,"tableId":"test5","time":1711593081079,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@4bdb725f: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711593081079,"tableId":"test5","time":1711593081079,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 10:33:02.766 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 10:33:03.083 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 10:33:03.083 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 10:33:03.102 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 10:33:03.103 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 10:33:03.103 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 10:33:03.103 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 10:33:03.106 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 10:33:03.106 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 38 ms 
[INFO ] 2024-03-28 10:33:03.106 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 10:33:03.114 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-e365e297-75c7-4e0f-8b35-591ec564626f 
[INFO ] 2024-03-28 10:33:03.114 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-e365e297-75c7-4e0f-8b35-591ec564626f 
[INFO ] 2024-03-28 10:33:03.114 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 10:33:03.127 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-9737d7be-315c-4082-b3b3-8ca63df263d0 
[INFO ] 2024-03-28 10:33:03.128 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-9737d7be-315c-4082-b3b3-8ca63df263d0 
[INFO ] 2024-03-28 10:33:03.129 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 10:33:03.140 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 10:33:03.140 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 10:33:03.140 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 34 ms 
[INFO ] 2024-03-28 10:33:03.141 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 10:33:03.217 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 10:33:03.218 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 10:33:03.219 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 10:33:03.219 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 10:33:03.220 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 78 ms 
[INFO ] 2024-03-28 10:33:07.108 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 10:33:07.128 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 10:33:07.129 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 10:33:07.156 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 10:33:07.156 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 10:33:07.174 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 10:33:07.177 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 10:36:46.661 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 10:36:46.662 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 10:36:46.689 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 10:36:46.791 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 10:36:46.791 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:36:46.791 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:36:46.795 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:36:46.818 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 21 ms 
[INFO ] 2024-03-28 10:36:46.818 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 26 ms 
[INFO ] 2024-03-28 10:36:46.818 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 25 ms 
[INFO ] 2024-03-28 10:36:47.533 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 10:36:47.673 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 10:36:47.674 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 10:36:47.674 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 10:36:47.754 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 10:36:47.754 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 10:36:47.754 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 10:36:47.755 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 10:36:47.965 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 10:36:53.919 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:36:53.932 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 10:36:53.932 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:36:53.932 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 10:43:44.048 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 10:46:10.501 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 10:46:10.833 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@4b646b89: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711593413970,"tableId":"test5","time":1711593413970,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@4b646b89: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711593413970,"tableId":"test5","time":1711593413970,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@4b646b89: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711593413970,"tableId":"test5","time":1711593413970,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 10:46:10.834 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 10:46:11.082 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 10:46:11.082 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 10:46:11.095 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 10:46:11.095 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 10:46:11.096 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 10:46:11.096 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 10:46:11.104 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 10:46:11.104 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 29 ms 
[INFO ] 2024-03-28 10:46:11.104 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 10:46:11.112 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-51258595-26ce-4031-b643-9e32b365f7ab 
[INFO ] 2024-03-28 10:46:11.119 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-51258595-26ce-4031-b643-9e32b365f7ab 
[INFO ] 2024-03-28 10:46:11.119 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 10:46:11.127 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-0749d494-fabe-4c59-a55a-0c60aff6baa4 
[INFO ] 2024-03-28 10:46:11.127 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-0749d494-fabe-4c59-a55a-0c60aff6baa4 
[INFO ] 2024-03-28 10:46:11.128 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 10:46:11.136 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 10:46:11.136 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 10:46:11.137 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 33 ms 
[INFO ] 2024-03-28 10:46:11.167 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 10:46:11.168 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 10:46:11.168 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 10:46:11.168 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 10:46:11.168 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 10:46:11.373 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 34 ms 
[INFO ] 2024-03-28 10:46:15.170 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 10:46:15.181 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 10:46:15.181 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 10:46:15.200 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 10:46:15.201 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 10:46:15.219 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 10:46:15.219 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 10:49:26.009 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 10:49:26.009 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 10:49:26.092 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 10:49:26.216 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 10:49:26.216 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:49:26.216 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:49:26.217 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:49:26.260 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 43 ms 
[INFO ] 2024-03-28 10:49:26.260 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 42 ms 
[INFO ] 2024-03-28 10:49:26.260 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 41 ms 
[INFO ] 2024-03-28 10:49:27.051 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 10:49:27.052 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 10:49:27.052 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 10:49:27.052 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 10:49:27.126 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 10:49:27.126 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 10:49:27.126 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 10:49:27.131 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 10:49:27.335 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 10:49:33.252 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:49:33.252 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 10:49:33.252 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:49:33.252 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 10:54:15.160 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 10:54:23.026 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 10:54:23.388 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@5463016d: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711594173282,"tableId":"test5","time":1711594173282,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@5463016d: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711594173282,"tableId":"test5","time":1711594173282,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@5463016d: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711594173282,"tableId":"test5","time":1711594173282,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 10:54:23.422 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 10:54:23.423 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 10:54:23.429 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 10:54:23.430 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 10:54:23.442 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 10:54:23.442 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 10:54:23.443 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 10:54:23.443 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 10:54:23.443 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 29 ms 
[INFO ] 2024-03-28 10:54:23.444 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 10:54:23.453 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-cfc64bfe-6167-4afd-93fc-87283c0583cd 
[INFO ] 2024-03-28 10:54:23.453 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-cfc64bfe-6167-4afd-93fc-87283c0583cd 
[INFO ] 2024-03-28 10:54:23.453 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 10:54:23.468 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-df33e8df-053a-4a84-afb2-867aec4cb1b9 
[INFO ] 2024-03-28 10:54:23.470 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-df33e8df-053a-4a84-afb2-867aec4cb1b9 
[INFO ] 2024-03-28 10:54:23.470 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 10:54:23.478 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 10:54:23.478 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 10:54:23.478 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 34 ms 
[INFO ] 2024-03-28 10:54:23.513 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 10:54:23.513 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 10:54:23.513 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 10:54:23.513 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 10:54:23.513 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 10:54:23.719 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 34 ms 
[INFO ] 2024-03-28 10:54:27.924 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 10:54:27.924 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 10:54:27.954 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 10:54:27.954 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 10:54:27.954 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 10:54:27.988 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 10:54:27.988 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 10:55:06.531 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 10:55:06.532 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 10:55:06.563 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 10:55:06.672 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 10:55:06.672 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:55:06.672 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:55:06.673 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 10:55:06.701 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 28 ms 
[INFO ] 2024-03-28 10:55:06.702 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 28 ms 
[INFO ] 2024-03-28 10:55:06.702 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 28 ms 
[INFO ] 2024-03-28 10:55:07.465 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 10:55:07.466 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 10:55:07.466 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 10:55:07.469 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 10:55:07.554 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 10:55:07.554 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 10:55:07.554 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 10:55:07.679 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 10:55:07.679 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 10:55:13.683 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:55:13.683 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 10:55:13.683 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 10:55:15.725 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 10:56:03.266 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 11:02:26.479 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 11:02:26.602 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@37dd3c09: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711594513750,"tableId":"test5","time":1711594513750,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@37dd3c09: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711594513750,"tableId":"test5","time":1711594513750,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@37dd3c09: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711594513750,"tableId":"test5","time":1711594513750,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 11:02:26.812 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 11:02:27.094 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 11:02:27.106 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 11:02:27.107 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 11:02:27.122 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 11:02:27.122 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 11:02:27.123 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 11:02:27.125 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 11:02:27.127 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 42 ms 
[INFO ] 2024-03-28 11:02:27.127 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 11:02:27.137 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-c8d4fdb5-2fe4-4494-9eed-e210f801465d 
[INFO ] 2024-03-28 11:02:27.150 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-c8d4fdb5-2fe4-4494-9eed-e210f801465d 
[INFO ] 2024-03-28 11:02:27.153 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 11:02:27.161 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-bb6b2efb-c5d4-4ed0-9c92-6ef0b3bb561e 
[INFO ] 2024-03-28 11:02:27.162 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-bb6b2efb-c5d4-4ed0-9c92-6ef0b3bb561e 
[INFO ] 2024-03-28 11:02:27.162 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 11:02:27.175 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 11:02:27.175 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 11:02:27.175 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 48 ms 
[INFO ] 2024-03-28 11:02:27.175 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 11:02:27.205 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 11:02:27.205 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 11:02:27.205 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 11:02:27.205 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 11:02:27.411 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 33 ms 
[INFO ] 2024-03-28 11:02:31.211 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 11:02:31.211 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 11:02:31.231 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 11:02:31.232 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 11:02:31.232 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 11:02:31.254 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 11:02:31.254 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 11:03:02.248 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 11:03:02.249 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 11:03:02.363 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 11:03:02.363 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 11:03:02.464 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 11:03:02.464 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 11:03:02.464 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 11:03:02.465 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 36 ms 
[INFO ] 2024-03-28 11:03:02.465 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 36 ms 
[INFO ] 2024-03-28 11:03:02.465 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 36 ms 
[INFO ] 2024-03-28 11:03:03.281 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 11:03:03.321 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 11:03:03.322 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 11:03:03.322 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 11:03:03.393 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 11:03:03.395 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 11:03:03.395 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 11:03:03.395 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 11:03:03.607 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 11:03:09.523 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 11:03:09.524 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 11:03:09.524 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 11:03:09.524 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 11:06:26.901 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 11:09:55.019 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 11:09:55.341 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@6d132974: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711594989548,"tableId":"test5","time":1711594989548,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@6d132974: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711594989548,"tableId":"test5","time":1711594989548,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@6d132974: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711594989548,"tableId":"test5","time":1711594989548,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 11:09:55.350 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 11:09:55.858 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 11:09:55.858 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 11:09:55.874 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 11:09:55.874 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 11:09:55.874 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 11:09:55.876 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 11:09:55.876 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 11:09:55.877 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 31 ms 
[INFO ] 2024-03-28 11:09:55.877 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 11:09:55.885 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-e04b27ad-f8e4-4324-9779-d80e6cde1b69 
[INFO ] 2024-03-28 11:09:55.885 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-e04b27ad-f8e4-4324-9779-d80e6cde1b69 
[INFO ] 2024-03-28 11:09:55.885 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 11:09:55.897 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-f751d70d-cd3a-42e0-84c8-b427eb275c75 
[INFO ] 2024-03-28 11:09:55.901 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-f751d70d-cd3a-42e0-84c8-b427eb275c75 
[INFO ] 2024-03-28 11:09:55.901 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 11:09:55.910 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 11:09:55.910 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 11:09:55.911 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 34 ms 
[INFO ] 2024-03-28 11:09:55.911 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 11:09:55.960 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 11:09:55.968 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 11:09:55.968 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 11:09:55.968 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 11:09:55.968 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 49 ms 
[INFO ] 2024-03-28 11:09:57.123 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 11:09:57.147 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 11:09:57.147 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 11:09:57.178 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 11:09:57.178 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 11:09:57.225 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 11:09:57.229 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 11:12:28.175 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 11:12:28.175 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 11:12:28.227 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 11:12:28.384 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 11:12:28.384 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 11:12:28.385 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 11:12:28.385 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 11:12:28.447 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 61 ms 
[INFO ] 2024-03-28 11:12:28.447 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 62 ms 
[INFO ] 2024-03-28 11:12:28.447 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 61 ms 
[INFO ] 2024-03-28 11:12:29.255 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 11:12:29.255 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 11:12:29.255 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 11:12:29.329 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[INFO ] 2024-03-28 11:12:29.330 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-03-28 11:12:29.360 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 11:12:29.372 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 11:12:29.372 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 11:12:29.373 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 11:12:35.510 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 11:12:35.515 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 11:12:35.516 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 11:12:35.516 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 11:12:38.837 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 11:13:23.502 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 11:13:23.854 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@48004223: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711595555544,"tableId":"test5","time":1711595555544,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@48004223: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711595555544,"tableId":"test5","time":1711595555544,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@48004223: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711595555544,"tableId":"test5","time":1711595555544,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 11:13:24.063 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 11:13:24.343 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 11:13:24.357 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 11:13:24.357 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 11:13:24.369 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 11:13:24.369 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 11:13:24.370 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 11:13:24.372 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 11:13:24.376 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 37 ms 
[INFO ] 2024-03-28 11:13:24.376 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 11:13:24.406 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-81013753-ebfd-4dfe-9d93-6f08e9d0ecee 
[INFO ] 2024-03-28 11:13:24.408 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-81013753-ebfd-4dfe-9d93-6f08e9d0ecee 
[INFO ] 2024-03-28 11:13:24.408 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 11:13:24.430 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-bf4654b6-e336-4328-af38-cb898e4dd4d2 
[INFO ] 2024-03-28 11:13:24.430 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-bf4654b6-e336-4328-af38-cb898e4dd4d2 
[INFO ] 2024-03-28 11:13:24.442 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 11:13:24.442 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 11:13:24.452 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 11:13:24.458 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 69 ms 
[INFO ] 2024-03-28 11:13:24.458 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 11:13:24.499 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 11:13:24.499 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 11:13:24.500 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 11:13:24.500 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 11:13:24.701 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 51 ms 
[INFO ] 2024-03-28 11:13:24.702 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 11:13:24.718 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 11:13:24.758 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 11:13:24.759 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 11:13:24.759 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 11:13:24.786 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 11:13:24.787 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 11:13:28.846 - [任务 28] - Start task milestones: 6603f9288b5bca60f72e00df(任务 28) 
[INFO ] 2024-03-28 11:13:28.849 - [任务 28] - Task initialization... 
[INFO ] 2024-03-28 11:13:28.928 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 11:13:28.928 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 11:13:28.969 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] start preload schema,table counts: 1 
[INFO ] 2024-03-28 11:13:28.970 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] start preload schema,table counts: 1 
[INFO ] 2024-03-28 11:13:28.970 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 11:13:29.006 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] preload schema finished, cost 32 ms 
[INFO ] 2024-03-28 11:13:29.007 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] preload schema finished, cost 32 ms 
[INFO ] 2024-03-28 11:13:29.007 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] preload schema finished, cost 32 ms 
[INFO ] 2024-03-28 11:13:29.820 - [任务 28][test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 11:13:29.893 - [任务 28][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-03-28 11:13:29.894 - [任务 28][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-03-28 11:13:29.894 - [任务 28][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 11:13:29.894 - [任务 28][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-28 11:13:29.969 - [任务 28][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-28 11:13:29.970 - [任务 28][test1] - Initial sync started 
[INFO ] 2024-03-28 11:13:29.970 - [任务 28][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-28 11:13:30.176 - [任务 28][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-28 11:13:36.069 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 11:13:36.069 - [任务 28][test1] - Incremental sync starting... 
[INFO ] 2024-03-28 11:13:36.069 - [任务 28][test1] - Initial sync completed 
[INFO ] 2024-03-28 11:13:36.070 - [任务 28][test1] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-03-28 11:15:38.429 - [任务 28][增强JS] - Alter table in memory, qualified name: PN_da09aa1b-d992-4450-91b1-b8d2d9ba6251 
[INFO ] 2024-03-28 11:17:48.516 - [任务 28][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-28 11:17:48.760 - [任务 28][test5] - Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@1339a76a: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711595616092,"tableId":"test5","time":1711595616092,"type":209} <-- Error Message -->
Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@1339a76a: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711595616092,"tableId":"test5","time":1711595616092,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test5, field name: name
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@1339a76a: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711595616092,"tableId":"test5","time":1711595616092,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test5, field name: name
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-28 11:17:48.964 - [任务 28][test5] - Job suspend in error handle 
[INFO ] 2024-03-28 11:17:49.238 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] running status set to false 
[INFO ] 2024-03-28 11:17:49.253 - [任务 28][test1] - Incremental sync completed 
[ERROR] 2024-03-28 11:17:49.254 - [任务 28][test1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-28 11:17:49.260 - [任务 28][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 11:17:49.260 - [任务 28][test1] - PDK connector node released: HazelcastSourcePdkDataNode-70931234-4fdf-4541-8a11-5826493d456a 
[INFO ] 2024-03-28 11:17:49.266 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] schema data cleaned 
[INFO ] 2024-03-28 11:17:49.266 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] monitor closed 
[INFO ] 2024-03-28 11:17:49.266 - [任务 28][test1] - Node test1[70931234-4fdf-4541-8a11-5826493d456a] close complete, cost 32 ms 
[INFO ] 2024-03-28 11:17:49.266 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] running status set to false 
[INFO ] 2024-03-28 11:17:49.270 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-Test-d11c0ad7-988d-4de3-b691-3276c632c44e 
[INFO ] 2024-03-28 11:17:49.270 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-Test-d11c0ad7-988d-4de3-b691-3276c632c44e 
[INFO ] 2024-03-28 11:17:49.270 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 11:17:49.283 - [任务 28][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-52a332dc-9cd1-4f2a-ae50-95e79efc19c2 
[INFO ] 2024-03-28 11:17:49.284 - [任务 28][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-52a332dc-9cd1-4f2a-ae50-95e79efc19c2 
[INFO ] 2024-03-28 11:17:49.284 - [任务 28][增强JS] - [ScriptExecutorsManager-6603f9288b5bca60f72e00df-da09aa1b-d992-4450-91b1-b8d2d9ba6251-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-28 11:17:49.306 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] schema data cleaned 
[INFO ] 2024-03-28 11:17:49.307 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] monitor closed 
[INFO ] 2024-03-28 11:17:49.307 - [任务 28][增强JS] - Node 增强JS[da09aa1b-d992-4450-91b1-b8d2d9ba6251] close complete, cost 44 ms 
[INFO ] 2024-03-28 11:17:49.307 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] running status set to false 
[INFO ] 2024-03-28 11:17:49.324 - [任务 28][test5] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 11:17:49.324 - [任务 28][test5] - PDK connector node released: HazelcastTargetPdkDataNode-8b4ecc6f-451e-463c-96d4-0838f9400f30 
[INFO ] 2024-03-28 11:17:49.324 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] schema data cleaned 
[INFO ] 2024-03-28 11:17:49.325 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] monitor closed 
[INFO ] 2024-03-28 11:17:49.530 - [任务 28][test5] - Node test5[8b4ecc6f-451e-463c-96d4-0838f9400f30] close complete, cost 17 ms 
[INFO ] 2024-03-28 11:17:53.253 - [任务 28] - Task [任务 28] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 11:17:53.253 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 11:17:53.285 - [任务 28] - Stop task milestones: 6603f9288b5bca60f72e00df(任务 28)  
[INFO ] 2024-03-28 11:17:53.286 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-03-28 11:17:53.286 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 11:17:53.309 - [任务 28] - Remove memory task client succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
[INFO ] 2024-03-28 11:17:53.312 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[6603f9288b5bca60f72e00df] 
