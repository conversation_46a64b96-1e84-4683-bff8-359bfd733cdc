[INFO ] 2024-07-18 11:57:41.574 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022] - Task initialization... 
[INFO ] 2024-07-18 11:57:41.574 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022] - Start task milestones: 669892aa8315b25db9f543bb(t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022) 
[INFO ] 2024-07-18 11:57:41.706 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022] - No<PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-18 11:57:41.869 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022] - The engine receives t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 11:57:41.869 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[285c1071-3fd0-4cf3-aaa6-eb49880786e1] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:57:41.869 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[ff508cab-f12f-4612-bbdd-f1fde0f5e6b9] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:57:41.869 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[285c1071-3fd0-4cf3-aaa6-eb49880786e1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:57:41.869 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[ff508cab-f12f-4612-bbdd-f1fde0f5e6b9] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:57:42.268 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 11:57:42.339 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Source node "qa_mongodb_repl_6040_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 11:57:42.339 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Source node "qa_mongodb_repl_6040_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 11:57:42.339 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 11:57:42.549 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - batch offset found: {},stream offset found: {"cdcOffset":1721275062,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 11:57:42.685 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 11:57:42.685 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Starting batch read, table name: t425, offset: null 
[INFO ] 2024-07-18 11:57:42.686 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Table t425 is going to be initial synced 
[INFO ] 2024-07-18 11:57:42.711 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Table [t425] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:57:42.712 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Query table 't425' counts: 1 
[INFO ] 2024-07-18 11:57:42.712 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:57:42.712 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 11:57:42.712 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:57:42.721 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Starting stream read, table list: [t425, _tapdata_heartbeat_table], offset: {"cdcOffset":1721275062,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 11:57:42.721 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [t425, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 11:59:46.331 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[285c1071-3fd0-4cf3-aaa6-eb49880786e1] running status set to false 
[INFO ] 2024-07-18 11:59:46.332 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-285c1071-3fd0-4cf3-aaa6-eb49880786e1 
[INFO ] 2024-07-18 11:59:46.332 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-285c1071-3fd0-4cf3-aaa6-eb49880786e1 
[INFO ] 2024-07-18 11:59:46.332 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[285c1071-3fd0-4cf3-aaa6-eb49880786e1] schema data cleaned 
[INFO ] 2024-07-18 11:59:46.332 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[285c1071-3fd0-4cf3-aaa6-eb49880786e1] monitor closed 
[INFO ] 2024-07-18 11:59:46.333 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[285c1071-3fd0-4cf3-aaa6-eb49880786e1] close complete, cost 62 ms 
[INFO ] 2024-07-18 11:59:46.334 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[ff508cab-f12f-4612-bbdd-f1fde0f5e6b9] running status set to false 
[INFO ] 2024-07-18 11:59:46.372 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-ff508cab-f12f-4612-bbdd-f1fde0f5e6b9 
[INFO ] 2024-07-18 11:59:46.373 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-ff508cab-f12f-4612-bbdd-f1fde0f5e6b9 
[INFO ] 2024-07-18 11:59:46.373 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[ff508cab-f12f-4612-bbdd-f1fde0f5e6b9] schema data cleaned 
[INFO ] 2024-07-18 11:59:46.373 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[ff508cab-f12f-4612-bbdd-f1fde0f5e6b9] monitor closed 
[INFO ] 2024-07-18 11:59:46.576 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[ff508cab-f12f-4612-bbdd-f1fde0f5e6b9] close complete, cost 39 ms 
[INFO ] 2024-07-18 11:59:46.852 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022][qa_mongodb_repl_6040_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 11:59:47.041 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 11:59:47.170 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6916053e 
[INFO ] 2024-07-18 11:59:47.170 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022] - Stop task milestones: 669892aa8315b25db9f543bb(t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022)  
[INFO ] 2024-07-18 11:59:47.190 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022] - Stopped task aspect(s) 
[INFO ] 2024-07-18 11:59:47.190 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 11:59:47.232 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022] - Remove memory task client succeed, task: t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022[669892aa8315b25db9f543bb] 
[INFO ] 2024-07-18 11:59:47.232 - [t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022] - Destroy memory task client cache succeed, task: t_4.2.5-mdb-v6.0.4_to_mdb_with_check_data_1717403468657_3537-1721275022[669892aa8315b25db9f543bb] 
