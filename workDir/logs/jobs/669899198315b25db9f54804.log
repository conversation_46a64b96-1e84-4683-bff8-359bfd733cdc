[INFO ] 2024-07-18 12:25:08.531 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666] - Task initialization... 
[INFO ] 2024-07-18 12:25:08.715 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666] - Start task milestones: 669899198315b25db9f54804(t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666) 
[INFO ] 2024-07-18 12:25:08.715 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 12:25:08.843 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666] - The engine receives t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 12:25:08.904 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[9501a325-7ebf-449e-b984-d633caeb1081] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:25:08.904 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[1323b413-9608-45db-bf33-e58675413fca] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:25:08.904 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[9501a325-7ebf-449e-b984-d633caeb1081] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:25:08.904 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[1323b413-9608-45db-bf33-e58675413fca] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:25:09.163 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 12:25:09.225 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Source node "qa_mongodb_repl_42240_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 12:25:09.225 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Source node "qa_mongodb_repl_42240_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 12:25:09.225 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 12:25:09.423 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - batch offset found: {},stream offset found: {"cdcOffset":1721276709,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 12:25:09.546 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 12:25:09.547 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Starting batch read, table name: t_4_2709, offset: null 
[INFO ] 2024-07-18 12:25:09.547 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Table t_4_2709 is going to be initial synced 
[INFO ] 2024-07-18 12:25:09.558 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Table [t_4_2709] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 12:25:09.558 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Query table 't_4_2709' counts: 5 
[INFO ] 2024-07-18 12:25:09.559 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 12:25:09.559 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 12:25:09.559 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 12:25:09.559 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Starting stream read, table list: [t_4_2709, _tapdata_heartbeat_table], offset: {"cdcOffset":1721276709,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 12:25:09.760 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [t_4_2709, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 12:27:27.329 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[1323b413-9608-45db-bf33-e58675413fca] running status set to false 
[INFO ] 2024-07-18 12:27:27.329 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-1323b413-9608-45db-bf33-e58675413fca 
[INFO ] 2024-07-18 12:27:27.330 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-1323b413-9608-45db-bf33-e58675413fca 
[INFO ] 2024-07-18 12:27:27.331 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[1323b413-9608-45db-bf33-e58675413fca] schema data cleaned 
[INFO ] 2024-07-18 12:27:27.332 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[1323b413-9608-45db-bf33-e58675413fca] monitor closed 
[INFO ] 2024-07-18 12:27:27.332 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[1323b413-9608-45db-bf33-e58675413fca] close complete, cost 32 ms 
[INFO ] 2024-07-18 12:27:27.354 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[9501a325-7ebf-449e-b984-d633caeb1081] running status set to false 
[INFO ] 2024-07-18 12:27:27.354 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-9501a325-7ebf-449e-b984-d633caeb1081 
[INFO ] 2024-07-18 12:27:27.354 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-9501a325-7ebf-449e-b984-d633caeb1081 
[INFO ] 2024-07-18 12:27:27.355 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[9501a325-7ebf-449e-b984-d633caeb1081] schema data cleaned 
[INFO ] 2024-07-18 12:27:27.355 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[9501a325-7ebf-449e-b984-d633caeb1081] monitor closed 
[INFO ] 2024-07-18 12:27:27.355 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[9501a325-7ebf-449e-b984-d633caeb1081] close complete, cost 22 ms 
[INFO ] 2024-07-18 12:27:27.964 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666][qa_mongodb_repl_42240_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 12:27:30.288 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 12:27:30.409 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6dd4feda 
[INFO ] 2024-07-18 12:27:30.409 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666] - Stop task milestones: 669899198315b25db9f54804(t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666)  
[INFO ] 2024-07-18 12:27:30.435 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666] - Stopped task aspect(s) 
[INFO ] 2024-07-18 12:27:30.435 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 12:27:30.482 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666] - Remove memory task client succeed, task: t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666[669899198315b25db9f54804] 
[INFO ] 2024-07-18 12:27:30.482 - [t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666] - Destroy memory task client cache succeed, task: t_4.2709-mdb_to_mdb_embedded_document_array_1717403468657_3537-1721276666[669899198315b25db9f54804] 
