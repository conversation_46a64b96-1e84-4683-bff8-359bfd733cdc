[INFO ] 2024-08-09 15:13:30.743 - [Mysql-PG] - Start task milestones: 66b5b77e685acb112ea02e7b(Mysql-PG) 
[INFO ] 2024-08-09 15:13:30.745 - [Mysql-PG] - Task initialization... 
[INFO ] 2024-08-09 15:13:30.871 - [Mysql-PG] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-08-09 15:13:30.873 - [Mysql-PG] - The engine receives Mysql-PG task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-09 15:13:31.278 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] start preload schema,table counts: 2 
[INFO ] 2024-08-09 15:13:31.280 - [Mysql-PG][PG] - Node <PERSON>[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] start preload schema,table counts: 2 
[INFO ] 2024-08-09 15:13:31.501 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] preload schema finished, cost 223 ms 
[INFO ] 2024-08-09 15:13:31.707 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] preload schema finished, cost 226 ms 
[INFO ] 2024-08-09 15:13:32.388 - [Mysql-PG][Mysql] - Source node "Mysql" read batch size: 100 
[INFO ] 2024-08-09 15:13:32.389 - [Mysql-PG][Mysql] - Source node "Mysql" event queue capacity: 200 
[INFO ] 2024-08-09 15:13:32.389 - [Mysql-PG][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-09 15:13:32.436 - [Mysql-PG][Mysql] - batch offset found: {},stream offset found: {"filename":"binlog.000029","position":1165,"gtidSet":""} 
[INFO ] 2024-08-09 15:13:32.436 - [Mysql-PG][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-09 15:13:32.459 - [Mysql-PG][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-09 15:13:32.462 - [Mysql-PG][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-09 15:13:32.651 - [Mysql-PG][Mysql] - Initial sync started 
[INFO ] 2024-08-09 15:13:32.672 - [Mysql-PG][Mysql] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-09 15:13:32.673 - [Mysql-PG][Mysql] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-09 15:13:32.739 - [Mysql-PG][Mysql] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-09 15:13:32.740 - [Mysql-PG][Mysql] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-09 15:13:32.741 - [Mysql-PG][Mysql] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-09 15:13:32.742 - [Mysql-PG][Mysql] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-09 15:13:32.753 - [Mysql-PG][Mysql] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-09 15:13:32.753 - [Mysql-PG][Mysql] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-09 15:13:32.753 - [Mysql-PG][Mysql] - Initial sync completed 
[INFO ] 2024-08-09 15:13:32.755 - [Mysql-PG][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-09 15:13:32.756 - [Mysql-PG][Mysql] - Initial sync completed 
[INFO ] 2024-08-09 15:13:41.575 - [Mysql-PG][Mysql] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CONFIG], offset: {"filename":"binlog.000029","position":1165,"gtidSet":""} 
[INFO ] 2024-08-09 15:13:41.717 - [Mysql-PG][Mysql] - Starting mysql cdc, server name: cd12ffc9-4b26-4fe8-a573-4515928651a5 
[INFO ] 2024-08-09 15:13:41.928 - [Mysql-PG][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1833621721
  time.precision.mode: adaptive_time_microseconds
  database.server.name: cd12ffc9-4b26-4fe8-a573-4515928651a5
  database.port: 3307
  threadName: Debezium-Mysql-Connector-cd12ffc9-4b26-4fe8-a573-4515928651a5
  database.hostname: localhost
  database.password: ********
  name: cd12ffc9-4b26-4fe8-a573-4515928651a5
  pdk.offset.string: {"name":"cd12ffc9-4b26-4fe8-a573-4515928651a5","offset":{"{\"server\":\"cd12ffc9-4b26-4fe8-a573-4515928651a5\"}":"{\"file\":\"binlog.000029\",\"pos\":1165,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-09 15:13:42.133 - [Mysql-PG][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-09 15:14:12.616 - [Mysql-PG] - Stop task milestones: 66b5b77e685acb112ea02e7b(Mysql-PG)  
[INFO ] 2024-08-09 15:14:12.774 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] running status set to false 
[INFO ] 2024-08-09 15:14:12.877 - [Mysql-PG][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-09 15:14:12.878 - [Mysql-PG][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-09 15:14:12.881 - [Mysql-PG][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-e2732bcc-3b87-4e20-8f3a-5ec01c00631d 
[INFO ] 2024-08-09 15:14:12.881 - [Mysql-PG][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-e2732bcc-3b87-4e20-8f3a-5ec01c00631d 
[INFO ] 2024-08-09 15:14:12.884 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] schema data cleaned 
[INFO ] 2024-08-09 15:14:12.884 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] monitor closed 
[INFO ] 2024-08-09 15:14:12.894 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] close complete, cost 124 ms 
[INFO ] 2024-08-09 15:14:12.895 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] running status set to false 
[INFO ] 2024-08-09 15:14:12.971 - [Mysql-PG][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode-577fb76e-1c53-4eae-97b5-13f5ba9cf9f5 
[INFO ] 2024-08-09 15:14:12.971 - [Mysql-PG][PG] - PDK connector node released: HazelcastTargetPdkDataNode-577fb76e-1c53-4eae-97b5-13f5ba9cf9f5 
[INFO ] 2024-08-09 15:14:12.971 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] schema data cleaned 
[INFO ] 2024-08-09 15:14:12.972 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] monitor closed 
[INFO ] 2024-08-09 15:14:12.972 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] close complete, cost 79 ms 
[INFO ] 2024-08-09 15:14:13.927 - [Mysql-PG] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-09 15:14:13.930 - [Mysql-PG] - Stopped task aspect(s) 
[INFO ] 2024-08-09 15:14:13.984 - [Mysql-PG] - Snapshot order controller have been removed 
[INFO ] 2024-08-09 15:14:13.985 - [Mysql-PG] - Remove memory task client succeed, task: Mysql-PG[66b5b77e685acb112ea02e7b] 
[INFO ] 2024-08-09 15:14:14.185 - [Mysql-PG] - Destroy memory task client cache succeed, task: Mysql-PG[66b5b77e685acb112ea02e7b] 
[INFO ] 2024-08-09 15:15:02.475 - [Mysql-PG] - Start task milestones: 66b5b77e685acb112ea02e7b(Mysql-PG) 
[INFO ] 2024-08-09 15:15:02.477 - [Mysql-PG] - Task initialization... 
[INFO ] 2024-08-09 15:15:02.576 - [Mysql-PG] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-09 15:15:02.578 - [Mysql-PG] - The engine receives Mysql-PG task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-09 15:15:02.723 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] start preload schema,table counts: 2 
[INFO ] 2024-08-09 15:15:02.800 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] start preload schema,table counts: 2 
[INFO ] 2024-08-09 15:15:02.801 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] preload schema finished, cost 70 ms 
[INFO ] 2024-08-09 15:15:03.003 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] preload schema finished, cost 75 ms 
[INFO ] 2024-08-09 15:15:03.713 - [Mysql-PG][Mysql] - Source node "Mysql" read batch size: 200 
[INFO ] 2024-08-09 15:15:03.716 - [Mysql-PG][Mysql] - Source node "Mysql" event queue capacity: 400 
[INFO ] 2024-08-09 15:15:03.716 - [Mysql-PG][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-09 15:15:03.730 - [Mysql-PG][Mysql] - batch offset found: {},stream offset found: {"filename":"binlog.000029","position":1165,"gtidSet":""} 
[INFO ] 2024-08-09 15:15:03.781 - [Mysql-PG][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-09 15:15:03.782 - [Mysql-PG][Mysql] - Initial sync started 
[INFO ] 2024-08-09 15:15:03.794 - [Mysql-PG][Mysql] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-09 15:15:03.794 - [Mysql-PG][Mysql] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-09 15:15:03.825 - [Mysql-PG][Mysql] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-09 15:15:03.825 - [Mysql-PG][Mysql] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-09 15:15:03.825 - [Mysql-PG][Mysql] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-09 15:15:03.826 - [Mysql-PG][Mysql] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-09 15:15:03.834 - [Mysql-PG][Mysql] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-09 15:15:03.835 - [Mysql-PG][Mysql] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-09 15:15:03.835 - [Mysql-PG][Mysql] - Initial sync completed 
[INFO ] 2024-08-09 15:15:03.836 - [Mysql-PG][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-09 15:15:03.836 - [Mysql-PG][Mysql] - Initial sync completed 
[INFO ] 2024-08-09 15:15:03.836 - [Mysql-PG][Mysql] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CONFIG], offset: {"filename":"binlog.000029","position":1165,"gtidSet":""} 
[INFO ] 2024-08-09 15:15:03.865 - [Mysql-PG][Mysql] - Starting mysql cdc, server name: 4b34cc37-d409-41bd-81d3-25a4347c0978 
[INFO ] 2024-08-09 15:15:03.866 - [Mysql-PG][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 398738264
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 4b34cc37-d409-41bd-81d3-25a4347c0978
  database.port: 3307
  threadName: Debezium-Mysql-Connector-4b34cc37-d409-41bd-81d3-25a4347c0978
  database.hostname: localhost
  database.password: ********
  name: 4b34cc37-d409-41bd-81d3-25a4347c0978
  pdk.offset.string: {"name":"4b34cc37-d409-41bd-81d3-25a4347c0978","offset":{"{\"server\":\"4b34cc37-d409-41bd-81d3-25a4347c0978\"}":"{\"file\":\"binlog.000029\",\"pos\":1165,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-09 15:15:03.905 - [Mysql-PG][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-09 15:15:03.905 - [Mysql-PG][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-09 15:15:03.926 - [Mysql-PG][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-09 15:15:03.927 - [Mysql-PG][PG] - The table BMSQL_DISTRICT has already exist. 
[INFO ] 2024-08-09 15:15:04.027 - [Mysql-PG][PG] - The table BMSQL_CONFIG has already exist. 
[INFO ] 2024-08-09 15:18:22.003 - [Mysql-PG] - Stop task milestones: 66b5b77e685acb112ea02e7b(Mysql-PG)  
[INFO ] 2024-08-09 15:18:22.004 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] running status set to false 
[INFO ] 2024-08-09 15:18:22.119 - [Mysql-PG][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-09 15:18:22.120 - [Mysql-PG][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-09 15:18:22.131 - [Mysql-PG][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-e2732bcc-3b87-4e20-8f3a-5ec01c00631d 
[INFO ] 2024-08-09 15:18:22.134 - [Mysql-PG][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-e2732bcc-3b87-4e20-8f3a-5ec01c00631d 
[INFO ] 2024-08-09 15:18:22.134 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] schema data cleaned 
[INFO ] 2024-08-09 15:18:22.135 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] monitor closed 
[INFO ] 2024-08-09 15:18:22.135 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] close complete, cost 133 ms 
[INFO ] 2024-08-09 15:18:22.135 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] running status set to false 
[INFO ] 2024-08-09 15:18:22.154 - [Mysql-PG][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode-577fb76e-1c53-4eae-97b5-13f5ba9cf9f5 
[INFO ] 2024-08-09 15:18:22.155 - [Mysql-PG][PG] - PDK connector node released: HazelcastTargetPdkDataNode-577fb76e-1c53-4eae-97b5-13f5ba9cf9f5 
[INFO ] 2024-08-09 15:18:22.156 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] schema data cleaned 
[INFO ] 2024-08-09 15:18:22.156 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] monitor closed 
[INFO ] 2024-08-09 15:18:22.363 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] close complete, cost 20 ms 
[INFO ] 2024-08-09 15:18:23.975 - [Mysql-PG] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-09 15:18:23.975 - [Mysql-PG] - Stopped task aspect(s) 
[INFO ] 2024-08-09 15:18:23.975 - [Mysql-PG] - Snapshot order controller have been removed 
[INFO ] 2024-08-09 15:18:24.012 - [Mysql-PG] - Remove memory task client succeed, task: Mysql-PG[66b5b77e685acb112ea02e7b] 
[INFO ] 2024-08-09 15:18:24.013 - [Mysql-PG] - Destroy memory task client cache succeed, task: Mysql-PG[66b5b77e685acb112ea02e7b] 
[INFO ] 2024-08-09 15:19:48.260 - [Mysql-PG] - Start task milestones: 66b5b77e685acb112ea02e7b(Mysql-PG) 
[INFO ] 2024-08-09 15:19:48.274 - [Mysql-PG] - Task initialization... 
[INFO ] 2024-08-09 15:19:48.274 - [Mysql-PG] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-09 15:19:48.353 - [Mysql-PG] - The engine receives Mysql-PG task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-09 15:19:48.354 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] start preload schema,table counts: 2 
[INFO ] 2024-08-09 15:19:48.438 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] start preload schema,table counts: 2 
[INFO ] 2024-08-09 15:19:48.441 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] preload schema finished, cost 60 ms 
[INFO ] 2024-08-09 15:19:48.441 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] preload schema finished, cost 66 ms 
[INFO ] 2024-08-09 15:19:49.210 - [Mysql-PG][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-09 15:19:49.212 - [Mysql-PG][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-09 15:19:49.309 - [Mysql-PG][PG] - The table BMSQL_DISTRICT has already exist. 
[INFO ] 2024-08-09 15:19:49.312 - [Mysql-PG][PG] - The table BMSQL_CONFIG has already exist. 
[INFO ] 2024-08-09 15:19:49.478 - [Mysql-PG][Mysql] - Source node "Mysql" read batch size: 200 
[INFO ] 2024-08-09 15:19:49.479 - [Mysql-PG][Mysql] - Source node "Mysql" event queue capacity: 400 
[INFO ] 2024-08-09 15:19:49.479 - [Mysql-PG][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-09 15:19:49.489 - [Mysql-PG][Mysql] - batch offset found: {},stream offset found: {"filename":"binlog.000029","position":1165,"gtidSet":""} 
[INFO ] 2024-08-09 15:19:49.489 - [Mysql-PG][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-09 15:19:49.623 - [Mysql-PG][Mysql] - Initial sync started 
[INFO ] 2024-08-09 15:19:49.624 - [Mysql-PG][Mysql] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-09 15:19:49.651 - [Mysql-PG][Mysql] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-09 15:19:49.651 - [Mysql-PG][Mysql] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-09 15:19:49.664 - [Mysql-PG][Mysql] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-09 15:19:49.664 - [Mysql-PG][Mysql] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-09 15:19:49.689 - [Mysql-PG][Mysql] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-09 15:19:49.700 - [Mysql-PG][Mysql] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-09 15:19:49.711 - [Mysql-PG][Mysql] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-09 15:19:49.712 - [Mysql-PG][Mysql] - Initial sync completed 
[INFO ] 2024-08-09 15:19:49.712 - [Mysql-PG][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-09 15:19:49.712 - [Mysql-PG][Mysql] - Initial sync completed 
[INFO ] 2024-08-09 15:19:49.712 - [Mysql-PG][Mysql] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CONFIG], offset: {"filename":"binlog.000029","position":1165,"gtidSet":""} 
[INFO ] 2024-08-09 15:19:49.760 - [Mysql-PG][Mysql] - Starting mysql cdc, server name: 8f2c1141-28fe-40e1-bfa6-69bfe059a2c0 
[INFO ] 2024-08-09 15:19:49.761 - [Mysql-PG][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 651392428
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 8f2c1141-28fe-40e1-bfa6-69bfe059a2c0
  database.port: 3307
  threadName: Debezium-Mysql-Connector-8f2c1141-28fe-40e1-bfa6-69bfe059a2c0
  database.hostname: localhost
  database.password: ********
  name: 8f2c1141-28fe-40e1-bfa6-69bfe059a2c0
  pdk.offset.string: {"name":"8f2c1141-28fe-40e1-bfa6-69bfe059a2c0","offset":{"{\"server\":\"8f2c1141-28fe-40e1-bfa6-69bfe059a2c0\"}":"{\"file\":\"binlog.000029\",\"pos\":1165,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-09 15:19:49.966 - [Mysql-PG][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CONFIG], data change syncing 
[ERROR] 2024-08-09 15:22:14.859 - [Mysql-PG][PG] - PDK retry exception (Server Error Code null): java.net.ConnectException: Connection refused (Connection refused) <-- Error Message -->
PDK retry exception (Server Error Code null): java.net.ConnectException: Connection refused (Connection refused)

<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:146)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.JdbcContext.queryAllIndexes(JdbcContext.java:235)
	at io.tapdata.connector.postgres.PostgresConnector.makeSureHasUnique(PostgresConnector.java:345)
	at io.tapdata.connector.postgres.PostgresConnector.writeRecord(PostgresConnector.java:358)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:828)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$55(HazelcastTargetPdkDataNode.java:780)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:774)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$24(HazelcastTargetPdkDataNode.java:483)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:483)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:670)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:554)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:510)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:469)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:482)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:528)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	... 5 more

[INFO ] 2024-08-09 15:22:14.918 - [Mysql-PG][PG] - Job suspend in error handle 
[INFO ] 2024-08-09 15:22:14.918 - [Mysql-PG] - Task [Mysql-PG] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-08-09 15:22:15.066 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] running status set to false 
[INFO ] 2024-08-09 15:22:15.067 - [Mysql-PG][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-09 15:22:15.067 - [Mysql-PG][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-09 15:22:15.067 - [Mysql-PG][Mysql] - Incremental sync completed 
[INFO ] 2024-08-09 15:22:15.075 - [Mysql-PG][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-e2732bcc-3b87-4e20-8f3a-5ec01c00631d 
[INFO ] 2024-08-09 15:22:15.075 - [Mysql-PG][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-e2732bcc-3b87-4e20-8f3a-5ec01c00631d 
[INFO ] 2024-08-09 15:22:15.075 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] schema data cleaned 
[INFO ] 2024-08-09 15:22:15.079 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] monitor closed 
[INFO ] 2024-08-09 15:22:15.079 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] close complete, cost 116 ms 
[INFO ] 2024-08-09 15:22:15.280 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] running status set to false 
[INFO ] 2024-08-09 15:22:19.935 - [Mysql-PG] - Task [Mysql-PG] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-08-09 15:22:23.381 - [Mysql-PG][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode-577fb76e-1c53-4eae-97b5-13f5ba9cf9f5 
[INFO ] 2024-08-09 15:22:23.381 - [Mysql-PG][PG] - PDK connector node released: HazelcastTargetPdkDataNode-577fb76e-1c53-4eae-97b5-13f5ba9cf9f5 
[INFO ] 2024-08-09 15:22:23.388 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] schema data cleaned 
[INFO ] 2024-08-09 15:22:23.389 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] monitor closed 
[INFO ] 2024-08-09 15:22:23.410 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] close complete, cost 8304 ms 
[INFO ] 2024-08-09 15:22:24.949 - [Mysql-PG] - Task [Mysql-PG] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-08-09 15:22:24.949 - [Mysql-PG] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-09 15:22:24.979 - [Mysql-PG] - Stop task milestones: 66b5b77e685acb112ea02e7b(Mysql-PG)  
[INFO ] 2024-08-09 15:22:24.979 - [Mysql-PG] - Stopped task aspect(s) 
[INFO ] 2024-08-09 15:22:24.979 - [Mysql-PG] - Snapshot order controller have been removed 
[INFO ] 2024-08-09 15:22:25.006 - [Mysql-PG] - Remove memory task client succeed, task: Mysql-PG[66b5b77e685acb112ea02e7b] 
[INFO ] 2024-08-09 15:22:25.009 - [Mysql-PG] - Destroy memory task client cache succeed, task: Mysql-PG[66b5b77e685acb112ea02e7b] 
[INFO ] 2024-08-09 15:23:13.323 - [Mysql-PG] - Start task milestones: 66b5b77e685acb112ea02e7b(Mysql-PG) 
[INFO ] 2024-08-09 15:23:13.433 - [Mysql-PG] - Task initialization... 
[INFO ] 2024-08-09 15:23:13.433 - [Mysql-PG] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-09 15:23:13.615 - [Mysql-PG] - The engine receives Mysql-PG task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-09 15:23:13.615 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] start preload schema,table counts: 2 
[INFO ] 2024-08-09 15:23:13.653 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] start preload schema,table counts: 2 
[INFO ] 2024-08-09 15:23:13.653 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] preload schema finished, cost 37 ms 
[INFO ] 2024-08-09 15:23:13.654 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] preload schema finished, cost 33 ms 
[INFO ] 2024-08-09 15:23:13.902 - [Mysql-PG][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-09 15:23:13.902 - [Mysql-PG][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-09 15:23:13.957 - [Mysql-PG][Mysql] - Source node "Mysql" read batch size: 200 
[INFO ] 2024-08-09 15:23:13.959 - [Mysql-PG][Mysql] - Source node "Mysql" event queue capacity: 400 
[INFO ] 2024-08-09 15:23:13.977 - [Mysql-PG][Mysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-09 15:23:13.979 - [Mysql-PG][Mysql] - batch offset found: {"BMSQL_DISTRICT":{"offset":{},"status":"RUNNING"}},stream offset found: {"filename":"binlog.000029","position":1165,"gtidSet":""} 
[INFO ] 2024-08-09 15:23:13.979 - [Mysql-PG][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-09 15:23:14.049 - [Mysql-PG][Mysql] - Initial sync started 
[INFO ] 2024-08-09 15:23:14.053 - [Mysql-PG][Mysql] - Starting batch read, table name: BMSQL_DISTRICT, offset: {} 
[INFO ] 2024-08-09 15:23:14.098 - [Mysql-PG][Mysql] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-09 15:23:14.098 - [Mysql-PG][Mysql] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-09 15:23:14.101 - [Mysql-PG][Mysql] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-09 15:23:14.101 - [Mysql-PG][Mysql] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-09 15:23:14.102 - [Mysql-PG][Mysql] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-09 15:23:14.119 - [Mysql-PG][Mysql] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-09 15:23:14.120 - [Mysql-PG][Mysql] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-09 15:23:14.120 - [Mysql-PG][Mysql] - Initial sync completed 
[INFO ] 2024-08-09 15:23:14.121 - [Mysql-PG][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-09 15:23:14.122 - [Mysql-PG][Mysql] - Initial sync completed 
[INFO ] 2024-08-09 15:23:14.122 - [Mysql-PG][Mysql] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CONFIG], offset: {"filename":"binlog.000029","position":1165,"gtidSet":""} 
[INFO ] 2024-08-09 15:23:14.158 - [Mysql-PG][Mysql] - Starting mysql cdc, server name: 8f2c1141-28fe-40e1-bfa6-69bfe059a2c0 
[INFO ] 2024-08-09 15:23:14.159 - [Mysql-PG][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1737848513
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 8f2c1141-28fe-40e1-bfa6-69bfe059a2c0
  database.port: 3307
  threadName: Debezium-Mysql-Connector-8f2c1141-28fe-40e1-bfa6-69bfe059a2c0
  database.hostname: localhost
  database.password: ********
  name: 8f2c1141-28fe-40e1-bfa6-69bfe059a2c0
  pdk.offset.string: {"name":"8f2c1141-28fe-40e1-bfa6-69bfe059a2c0","offset":{"{\"server\":\"8f2c1141-28fe-40e1-bfa6-69bfe059a2c0\"}":"{\"file\":\"binlog.000029\",\"pos\":1165,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-09 15:23:14.361 - [Mysql-PG][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-09 15:24:35.221 - [Mysql-PG] - Stop task milestones: 66b5b77e685acb112ea02e7b(Mysql-PG)  
[INFO ] 2024-08-09 15:24:35.378 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] running status set to false 
[INFO ] 2024-08-09 15:24:35.379 - [Mysql-PG][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-09 15:24:35.390 - [Mysql-PG][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-09 15:24:35.390 - [Mysql-PG][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-e2732bcc-3b87-4e20-8f3a-5ec01c00631d 
[INFO ] 2024-08-09 15:24:35.393 - [Mysql-PG][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-e2732bcc-3b87-4e20-8f3a-5ec01c00631d 
[INFO ] 2024-08-09 15:24:35.393 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] schema data cleaned 
[INFO ] 2024-08-09 15:24:35.393 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] monitor closed 
[INFO ] 2024-08-09 15:24:35.394 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] close complete, cost 102 ms 
[INFO ] 2024-08-09 15:24:35.394 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] running status set to false 
[INFO ] 2024-08-09 15:24:35.410 - [Mysql-PG][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode-577fb76e-1c53-4eae-97b5-13f5ba9cf9f5 
[INFO ] 2024-08-09 15:24:35.410 - [Mysql-PG][PG] - PDK connector node released: HazelcastTargetPdkDataNode-577fb76e-1c53-4eae-97b5-13f5ba9cf9f5 
[INFO ] 2024-08-09 15:24:35.410 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] schema data cleaned 
[INFO ] 2024-08-09 15:24:35.411 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] monitor closed 
[INFO ] 2024-08-09 15:24:35.413 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] close complete, cost 17 ms 
[INFO ] 2024-08-09 15:24:40.102 - [Mysql-PG] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-09 15:24:40.104 - [Mysql-PG] - Stopped task aspect(s) 
[INFO ] 2024-08-09 15:24:40.105 - [Mysql-PG] - Snapshot order controller have been removed 
[INFO ] 2024-08-09 15:24:40.138 - [Mysql-PG] - Remove memory task client succeed, task: Mysql-PG[66b5b77e685acb112ea02e7b] 
[INFO ] 2024-08-09 15:24:40.139 - [Mysql-PG] - Destroy memory task client cache succeed, task: Mysql-PG[66b5b77e685acb112ea02e7b] 
[INFO ] 2024-08-09 15:37:48.054 - [Mysql-PG] - Start task milestones: 66b5b77e685acb112ea02e7b(Mysql-PG) 
[INFO ] 2024-08-09 15:38:32.877 - [Mysql-PG] - Task initialization... 
[INFO ] 2024-08-09 15:38:32.993 - [Mysql-PG] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-09 15:38:32.995 - [Mysql-PG] - The engine receives Mysql-PG task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-09 15:38:33.048 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] start preload schema,table counts: 2 
[INFO ] 2024-08-09 15:38:33.049 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] start preload schema,table counts: 2 
[INFO ] 2024-08-09 15:38:33.257 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] preload schema finished, cost 52 ms 
[INFO ] 2024-08-09 15:38:33.258 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] preload schema finished, cost 52 ms 
[INFO ] 2024-08-09 15:38:33.982 - [Mysql-PG][Mysql] - Source node "Mysql" read batch size: 200 
[INFO ] 2024-08-09 15:38:33.983 - [Mysql-PG][Mysql] - Source node "Mysql" event queue capacity: 400 
[INFO ] 2024-08-09 15:38:33.983 - [Mysql-PG][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-09 15:38:33.998 - [Mysql-PG][Mysql] - batch offset found: {},stream offset found: {"filename":"binlog.000029","position":1165,"gtidSet":""} 
[INFO ] 2024-08-09 15:38:33.998 - [Mysql-PG][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-09 15:38:34.085 - [Mysql-PG][Mysql] - Initial sync started 
[INFO ] 2024-08-09 15:38:34.085 - [Mysql-PG][Mysql] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-09 15:38:34.096 - [Mysql-PG][Mysql] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-09 15:38:34.119 - [Mysql-PG][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-09 15:38:34.120 - [Mysql-PG][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-09 15:38:34.144 - [Mysql-PG][Mysql] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-09 15:38:34.145 - [Mysql-PG][PG] - The table BMSQL_DISTRICT has already exist. 
[INFO ] 2024-08-09 15:38:34.152 - [Mysql-PG][Mysql] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-09 15:38:34.153 - [Mysql-PG][Mysql] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-09 15:38:34.173 - [Mysql-PG][Mysql] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-09 15:38:34.173 - [Mysql-PG][Mysql] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-09 15:38:34.177 - [Mysql-PG][Mysql] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-09 15:38:34.177 - [Mysql-PG][Mysql] - Initial sync completed 
[INFO ] 2024-08-09 15:38:34.178 - [Mysql-PG][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-09 15:38:34.178 - [Mysql-PG][Mysql] - Initial sync completed 
[INFO ] 2024-08-09 15:38:34.219 - [Mysql-PG][Mysql] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CONFIG], offset: {"filename":"binlog.000029","position":1165,"gtidSet":""} 
[INFO ] 2024-08-09 15:38:34.221 - [Mysql-PG][Mysql] - Starting mysql cdc, server name: f8481faf-8057-490b-aa14-58182e35944b 
[INFO ] 2024-08-09 15:38:34.225 - [Mysql-PG][PG] - The table BMSQL_CONFIG has already exist. 
[INFO ] 2024-08-09 15:38:34.226 - [Mysql-PG][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 392497108
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f8481faf-8057-490b-aa14-58182e35944b
  database.port: 3307
  threadName: Debezium-Mysql-Connector-f8481faf-8057-490b-aa14-58182e35944b
  database.hostname: localhost
  database.password: ********
  name: f8481faf-8057-490b-aa14-58182e35944b
  pdk.offset.string: {"name":"f8481faf-8057-490b-aa14-58182e35944b","offset":{"{\"server\":\"f8481faf-8057-490b-aa14-58182e35944b\"}":"{\"file\":\"binlog.000029\",\"pos\":1165,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-09 15:38:34.291 - [Mysql-PG][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-09 16:17:40.049 - [Mysql-PG] - Stop task milestones: 66b5b77e685acb112ea02e7b(Mysql-PG)  
[INFO ] 2024-08-09 16:17:40.607 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] running status set to false 
[INFO ] 2024-08-09 16:17:40.607 - [Mysql-PG][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-09 16:17:40.607 - [Mysql-PG][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-e2732bcc-3b87-4e20-8f3a-5ec01c00631d 
[INFO ] 2024-08-09 16:17:40.608 - [Mysql-PG][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-e2732bcc-3b87-4e20-8f3a-5ec01c00631d 
[INFO ] 2024-08-09 16:17:40.612 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] schema data cleaned 
[INFO ] 2024-08-09 16:17:40.613 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] monitor closed 
[INFO ] 2024-08-09 16:17:40.615 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] close complete, cost 121 ms 
[INFO ] 2024-08-09 16:17:40.615 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] running status set to false 
[INFO ] 2024-08-09 16:17:40.633 - [Mysql-PG][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode-577fb76e-1c53-4eae-97b5-13f5ba9cf9f5 
[INFO ] 2024-08-09 16:17:40.634 - [Mysql-PG][PG] - PDK connector node released: HazelcastTargetPdkDataNode-577fb76e-1c53-4eae-97b5-13f5ba9cf9f5 
[INFO ] 2024-08-09 16:17:40.634 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] schema data cleaned 
[INFO ] 2024-08-09 16:17:40.634 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] monitor closed 
[INFO ] 2024-08-09 16:17:40.635 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] close complete, cost 21 ms 
[INFO ] 2024-08-09 16:17:44.976 - [Mysql-PG] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-09 16:17:44.976 - [Mysql-PG] - Stopped task aspect(s) 
[INFO ] 2024-08-09 16:17:44.976 - [Mysql-PG] - Snapshot order controller have been removed 
[INFO ] 2024-08-09 16:17:45.002 - [Mysql-PG] - Remove memory task client succeed, task: Mysql-PG[66b5b77e685acb112ea02e7b] 
[INFO ] 2024-08-09 16:17:45.002 - [Mysql-PG] - Destroy memory task client cache succeed, task: Mysql-PG[66b5b77e685acb112ea02e7b] 
[INFO ] 2024-08-09 16:17:49.558 - [Mysql-PG] - Start task milestones: 66b5b77e685acb112ea02e7b(Mysql-PG) 
[INFO ] 2024-08-09 16:17:49.586 - [Mysql-PG] - Task initialization... 
[INFO ] 2024-08-09 16:17:49.586 - [Mysql-PG] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-09 16:17:49.700 - [Mysql-PG] - The engine receives Mysql-PG task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-09 16:17:49.704 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] start preload schema,table counts: 2 
[INFO ] 2024-08-09 16:17:49.704 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] start preload schema,table counts: 2 
[INFO ] 2024-08-09 16:17:49.763 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] preload schema finished, cost 58 ms 
[INFO ] 2024-08-09 16:17:49.764 - [Mysql-PG][PG] - Node PG[577fb76e-1c53-4eae-97b5-13f5ba9cf9f5] preload schema finished, cost 58 ms 
[INFO ] 2024-08-09 16:17:50.571 - [Mysql-PG][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-09 16:17:50.572 - [Mysql-PG][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-09 16:17:50.591 - [Mysql-PG][PG] - The table BMSQL_DISTRICT has already exist. 
[INFO ] 2024-08-09 16:17:50.782 - [Mysql-PG][PG] - The table BMSQL_CONFIG has already exist. 
[INFO ] 2024-08-09 16:17:50.782 - [Mysql-PG][Mysql] - Source node "Mysql" read batch size: 200 
[INFO ] 2024-08-09 16:17:50.783 - [Mysql-PG][Mysql] - Source node "Mysql" event queue capacity: 400 
[INFO ] 2024-08-09 16:17:50.783 - [Mysql-PG][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-09 16:17:50.789 - [Mysql-PG][Mysql] - batch offset found: {},stream offset found: {"filename":"binlog.000029","position":1165,"gtidSet":""} 
[INFO ] 2024-08-09 16:17:50.837 - [Mysql-PG][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-09 16:17:50.837 - [Mysql-PG][Mysql] - Initial sync started 
[INFO ] 2024-08-09 16:17:50.843 - [Mysql-PG][Mysql] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-09 16:17:50.843 - [Mysql-PG][Mysql] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-09 16:17:50.873 - [Mysql-PG][Mysql] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-09 16:17:50.873 - [Mysql-PG][Mysql] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-09 16:17:50.874 - [Mysql-PG][Mysql] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-09 16:17:50.874 - [Mysql-PG][Mysql] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-09 16:17:50.890 - [Mysql-PG][Mysql] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-09 16:17:50.891 - [Mysql-PG][Mysql] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-09 16:17:50.891 - [Mysql-PG][Mysql] - Initial sync completed 
[INFO ] 2024-08-09 16:17:50.891 - [Mysql-PG][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-09 16:17:50.891 - [Mysql-PG][Mysql] - Initial sync completed 
[INFO ] 2024-08-09 16:17:50.919 - [Mysql-PG][Mysql] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CONFIG], offset: {"filename":"binlog.000029","position":1165,"gtidSet":""} 
[INFO ] 2024-08-09 16:17:50.921 - [Mysql-PG][Mysql] - Starting mysql cdc, server name: ee07f892-5151-4bbf-b56c-531fc1c35686 
[INFO ] 2024-08-09 16:17:50.964 - [Mysql-PG][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1048712421
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ee07f892-5151-4bbf-b56c-531fc1c35686
  database.port: 3307
  threadName: Debezium-Mysql-Connector-ee07f892-5151-4bbf-b56c-531fc1c35686
  database.hostname: localhost
  database.password: ********
  name: ee07f892-5151-4bbf-b56c-531fc1c35686
  pdk.offset.string: {"name":"ee07f892-5151-4bbf-b56c-531fc1c35686","offset":{"{\"server\":\"ee07f892-5151-4bbf-b56c-531fc1c35686\"}":"{\"file\":\"binlog.000029\",\"pos\":1165,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-09 16:17:50.964 - [Mysql-PG][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-09 16:28:32.183 - [Mysql-PG][Mysql] - Node Mysql[e2732bcc-3b87-4e20-8f3a-5ec01c00631d] running status set to false 
