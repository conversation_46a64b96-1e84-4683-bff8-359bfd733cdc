[INFO ] 2024-04-03 03:34:25.128 - [任务 43(100)][4e4d2d22-178a-4f91-b089-bf21317e27df] - Node 4e4d2d22-178a-4f91-b089-bf21317e27df[4e4d2d22-178a-4f91-b089-bf21317e27df] start preload schema,table counts: 0 
[INFO ] 2024-04-03 03:34:25.159 - [任务 43(100)][test2] - Node test2[8fcdda3b-8438-45a8-b53f-28a6c47ade37] start preload schema,table counts: 1 
[INFO ] 2024-04-03 03:34:25.162 - [任务 43(100)][增强JS] - Node 增强JS[de3cff20-f533-4269-86be-122c4a0476c4] start preload schema,table counts: 1 
[INFO ] 2024-04-03 03:34:25.163 - [任务 43(100)][test2] - Node test2[8fcdda3b-8438-45a8-b53f-28a6c47ade37] preload schema finished, cost 2 ms 
[INFO ] 2024-04-03 03:34:25.163 - [任务 43(100)][增强JS] - Node 增强JS[de3cff20-f533-4269-86be-122c4a0476c4] preload schema finished, cost 2 ms 
[INFO ] 2024-04-03 03:34:25.164 - [任务 43(100)][4e4d2d22-178a-4f91-b089-bf21317e27df] - Node 4e4d2d22-178a-4f91-b089-bf21317e27df[4e4d2d22-178a-4f91-b089-bf21317e27df] preload schema finished, cost 2 ms 
[INFO ] 2024-04-03 03:34:25.836 - [任务 43(100)][test2] - Node test2[8fcdda3b-8438-45a8-b53f-28a6c47ade37] running status set to false 
[INFO ] 2024-04-03 03:34:25.873 - [任务 43(100)][test2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-8fcdda3b-8438-45a8-b53f-28a6c47ade37 
[INFO ] 2024-04-03 03:34:25.873 - [任务 43(100)][test2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-8fcdda3b-8438-45a8-b53f-28a6c47ade37 
[INFO ] 2024-04-03 03:34:25.877 - [任务 43(100)][test2] - Node test2[8fcdda3b-8438-45a8-b53f-28a6c47ade37] schema data cleaned 
[INFO ] 2024-04-03 03:34:25.877 - [任务 43(100)][test2] - Node test2[8fcdda3b-8438-45a8-b53f-28a6c47ade37] monitor closed 
[INFO ] 2024-04-03 03:34:25.893 - [任务 43(100)][test2] - Node test2[8fcdda3b-8438-45a8-b53f-28a6c47ade37] close complete, cost 49 ms 
[INFO ] 2024-04-03 03:34:25.894 - [任务 43(100)][增强JS] - Node 增强JS[de3cff20-f533-4269-86be-122c4a0476c4] running status set to false 
[INFO ] 2024-04-03 03:34:25.902 - [任务 43(100)][4e4d2d22-178a-4f91-b089-bf21317e27df] - Node 4e4d2d22-178a-4f91-b089-bf21317e27df[4e4d2d22-178a-4f91-b089-bf21317e27df] running status set to false 
[INFO ] 2024-04-03 03:34:25.902 - [任务 43(100)][4e4d2d22-178a-4f91-b089-bf21317e27df] - Node 4e4d2d22-178a-4f91-b089-bf21317e27df[4e4d2d22-178a-4f91-b089-bf21317e27df] schema data cleaned 
[INFO ] 2024-04-03 03:34:25.911 - [任务 43(100)][4e4d2d22-178a-4f91-b089-bf21317e27df] - Node 4e4d2d22-178a-4f91-b089-bf21317e27df[4e4d2d22-178a-4f91-b089-bf21317e27df] monitor closed 
[INFO ] 2024-04-03 03:34:25.912 - [任务 43(100)][4e4d2d22-178a-4f91-b089-bf21317e27df] - Node 4e4d2d22-178a-4f91-b089-bf21317e27df[4e4d2d22-178a-4f91-b089-bf21317e27df] close complete, cost 9 ms 
[INFO ] 2024-04-03 03:34:25.914 - [任务 43(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-559943f3-82bf-46b2-a638-318ecc5c75ba 
[INFO ] 2024-04-03 03:34:25.915 - [任务 43(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-559943f3-82bf-46b2-a638-318ecc5c75ba 
[INFO ] 2024-04-03 03:34:25.915 - [任务 43(100)][增强JS] - [ScriptExecutorsManager-660bcdce03a225280dc4407c-de3cff20-f533-4269-86be-122c4a0476c4-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-03 03:34:25.920 - [任务 43(100)][增强JS] - Node 增强JS[de3cff20-f533-4269-86be-122c4a0476c4] schema data cleaned 
[INFO ] 2024-04-03 03:34:25.921 - [任务 43(100)][增强JS] - Node 增强JS[de3cff20-f533-4269-86be-122c4a0476c4] monitor closed 
[INFO ] 2024-04-03 03:34:25.921 - [任务 43(100)][增强JS] - Node 增强JS[de3cff20-f533-4269-86be-122c4a0476c4] close complete, cost 29 ms 
[INFO ] 2024-04-03 03:34:26.128 - [任务 43(100)] - load tapTable task 660bcdce03a225280dc4407c-4e4d2d22-178a-4f91-b089-bf21317e27df complete, cost 1613ms 
