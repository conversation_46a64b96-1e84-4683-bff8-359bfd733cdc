[INFO ] 2024-07-15 03:35:34.406 - [来自ShareMongoCDC-Test的共享挖掘任务] - Start task milestones: 6690d92e457e901dd0b0efac(来自ShareMongoCDC-Test的共享挖掘任务) 
[INFO ] 2024-07-15 03:35:34.520 - [来自ShareMongoCDC-Test的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 03:35:34.557 - [来自ShareMongoCDC-Test的共享挖掘任务] - The engine receives 来自ShareMongoCDC-Test的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 03:35:34.655 - [来自ShareMongoCDC-Test的共享挖掘任务][ShareMongoCDC-Test] - Node ShareMongoCDC-Test[8dba0026bcf64249bfcd8bbc6158986b] start preload schema,table counts: 2 
[INFO ] 2024-07-15 03:35:34.656 - [来自ShareMongoCDC-Test的共享挖掘任务][ShareMongoCDC-Test] - Node ShareMongoCDC-Test[8dba0026bcf64249bfcd8bbc6158986b] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 03:35:34.679 - [来自ShareMongoCDC-Test的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-15 03:35:34.679 - [来自ShareMongoCDC-Test的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 03:35:34.714 - [来自ShareMongoCDC-Test的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690d92e66ab5ede8a728308, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6690d8be457e901dd0b0ef12__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_1618843798, shareCdcTaskId=6690d92e457e901dd0b0efac, connectionId=6690d8be457e901dd0b0ef12) 
[INFO ] 2024-07-15 03:35:34.714 - [来自ShareMongoCDC-Test的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6690d92e66ab5ede8a728309, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6690d8be457e901dd0b0ef12_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1026742106, shareCdcTaskId=6690d92e457e901dd0b0efac, connectionId=6690d8be457e901dd0b0ef12) 
[INFO ] 2024-07-15 03:35:34.905 - [来自ShareMongoCDC-Test的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自ShareMongoCDC-Test的共享挖掘任务_CUSTOMER', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_1026742106', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 03:35:34.906 - [来自ShareMongoCDC-Test的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自ShareMongoCDC-Test的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_1618843798', head seq: 0, tail seq: 15575 
[INFO ] 2024-07-15 03:35:34.950 - [来自ShareMongoCDC-Test的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-15 03:35:35.133 - [来自ShareMongoCDC-Test的共享挖掘任务][ShareMongoCDC-Test] - Source node "ShareMongoCDC-Test" read batch size: 2000 
[INFO ] 2024-07-15 03:35:35.133 - [来自ShareMongoCDC-Test的共享挖掘任务][ShareMongoCDC-Test] - Source node "ShareMongoCDC-Test" event queue capacity: 4000 
[INFO ] 2024-07-15 03:35:35.142 - [来自ShareMongoCDC-Test的共享挖掘任务][ShareMongoCDC-Test] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-15 03:35:35.142 - [来自ShareMongoCDC-Test的共享挖掘任务][ShareMongoCDC-Test] - batch offset found: {},stream offset found: {"_data":{"value":"826694274F000000082B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F696400646690D93166AB5EDE8A72876E0004","bsonType":"STRING","boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"number":false,"array":false,"null":false}} 
[INFO ] 2024-07-15 03:35:35.182 - [来自ShareMongoCDC-Test的共享挖掘任务][ShareMongoCDC-Test] - Starting stream read, table list: [CUSTOMER, _tapdata_heartbeat_table], offset: {"_data":{"value":"826694274F000000082B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F696400646690D93166AB5EDE8A72876E0004","bsonType":"STRING","boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"number":false,"array":false,"null":false}} 
[INFO ] 2024-07-15 03:35:35.298 - [来自ShareMongoCDC-Test的共享挖掘任务][ShareMongoCDC-Test] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 03:39:13.932 - [来自ShareMongoCDC-Test的共享挖掘任务][ShareMongoCDC-Test] - Node ShareMongoCDC-Test[8dba0026bcf64249bfcd8bbc6158986b] running status set to false 
[WARN ] 2024-07-15 03:39:13.959 - [来自ShareMongoCDC-Test的共享挖掘任务][ShareMongoCDC-Test] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkShareCDCNode-8dba0026bcf64249bfcd8bbc6158986b 
[INFO ] 2024-07-15 03:39:13.960 - [来自ShareMongoCDC-Test的共享挖掘任务][ShareMongoCDC-Test] - PDK connector node released: HazelcastSourcePdkShareCDCNode-8dba0026bcf64249bfcd8bbc6158986b 
[INFO ] 2024-07-15 03:39:13.960 - [来自ShareMongoCDC-Test的共享挖掘任务][ShareMongoCDC-Test] - Node ShareMongoCDC-Test[8dba0026bcf64249bfcd8bbc6158986b] schema data cleaned 
[INFO ] 2024-07-15 03:39:13.962 - [来自ShareMongoCDC-Test的共享挖掘任务][ShareMongoCDC-Test] - Node ShareMongoCDC-Test[8dba0026bcf64249bfcd8bbc6158986b] monitor closed 
[INFO ] 2024-07-15 03:39:13.965 - [来自ShareMongoCDC-Test的共享挖掘任务][ShareMongoCDC-Test] - Node ShareMongoCDC-Test[8dba0026bcf64249bfcd8bbc6158986b] close complete, cost 41 ms 
[INFO ] 2024-07-15 03:39:13.998 - [来自ShareMongoCDC-Test的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[9300bdf2ccf045b79c85db938a53a42b] running status set to false 
