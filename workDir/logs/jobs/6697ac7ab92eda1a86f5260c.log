[INFO ] 2024-07-17 19:35:33.773 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122] - Start task milestones: 6697ac7ab92eda1a86f5260c(t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122) 
[INFO ] 2024-07-17 19:35:33.775 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122] - Task initialization... 
[INFO ] 2024-07-17 19:35:33.994 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-17 19:35:34.198 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122] - The engine receives t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 19:35:34.199 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[e3a9f2fc-f64e-4582-b0d6-9f232fd2e4ec] start preload schema,table counts: 1 
[INFO ] 2024-07-17 19:35:34.199 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[a2e2d8c5-1855-4fd1-8a35-1009654c67cd] start preload schema,table counts: 1 
[INFO ] 2024-07-17 19:35:34.199 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[a2e2d8c5-1855-4fd1-8a35-1009654c67cd] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 19:35:34.199 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[e3a9f2fc-f64e-4582-b0d6-9f232fd2e4ec] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 19:35:34.395 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-17 19:35:34.395 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Source node "qa_mock_1000w_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-17 19:35:34.395 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Source node "qa_mock_1000w_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-17 19:35:34.395 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 19:35:34.518 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-17 19:35:34.518 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-17 19:35:34.519 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Starting batch read, table name: mock_1000w, offset: null 
[INFO ] 2024-07-17 19:35:34.519 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Table mock_1000w is going to be initial synced 
[INFO ] 2024-07-17 19:35:34.528 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Start mock_1000w batch read 
[INFO ] 2024-07-17 19:35:34.528 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Query table 'mock_1000w' counts: 10000000 
[INFO ] 2024-07-17 19:36:03.236 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Compile mock_1000w batch read 
[INFO ] 2024-07-17 19:36:03.244 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Table [mock_1000w] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-17 19:36:03.244 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-17 19:36:04.255 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[e3a9f2fc-f64e-4582-b0d6-9f232fd2e4ec] running status set to false 
[INFO ] 2024-07-17 19:36:04.255 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[a2e2d8c5-1855-4fd1-8a35-1009654c67cd] running status set to false 
[INFO ] 2024-07-17 19:36:04.256 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Stop connector 
[INFO ] 2024-07-17 19:36:04.266 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-a2e2d8c5-1855-4fd1-8a35-1009654c67cd 
[INFO ] 2024-07-17 19:36:04.267 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-a2e2d8c5-1855-4fd1-8a35-1009654c67cd 
[INFO ] 2024-07-17 19:36:04.267 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[a2e2d8c5-1855-4fd1-8a35-1009654c67cd] schema data cleaned 
[INFO ] 2024-07-17 19:36:04.270 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[a2e2d8c5-1855-4fd1-8a35-1009654c67cd] monitor closed 
[INFO ] 2024-07-17 19:36:04.270 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[a2e2d8c5-1855-4fd1-8a35-1009654c67cd] close complete, cost 16 ms 
[INFO ] 2024-07-17 19:36:04.289 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Stop connector: first 1721216134520 20ms, last 1721216163235 8ms, counts: 10000000/28723ms, min: 2, max: 25, QPS: 357142/s 
[INFO ] 2024-07-17 19:36:04.290 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-e3a9f2fc-f64e-4582-b0d6-9f232fd2e4ec 
[INFO ] 2024-07-17 19:36:04.290 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-e3a9f2fc-f64e-4582-b0d6-9f232fd2e4ec 
[INFO ] 2024-07-17 19:36:04.290 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[e3a9f2fc-f64e-4582-b0d6-9f232fd2e4ec] schema data cleaned 
[INFO ] 2024-07-17 19:36:04.290 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[e3a9f2fc-f64e-4582-b0d6-9f232fd2e4ec] monitor closed 
[INFO ] 2024-07-17 19:36:04.292 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[e3a9f2fc-f64e-4582-b0d6-9f232fd2e4ec] close complete, cost 37 ms 
[INFO ] 2024-07-17 19:36:06.244 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 19:36:06.245 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3f104c9f 
[INFO ] 2024-07-17 19:36:06.255 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122] - Stop task milestones: 6697ac7ab92eda1a86f5260c(t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122)  
[INFO ] 2024-07-17 19:36:06.394 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122] - Stopped task aspect(s) 
[INFO ] 2024-07-17 19:36:06.394 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 19:36:06.423 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122] - Remove memory task client succeed, task: t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122[6697ac7ab92eda1a86f5260c] 
[INFO ] 2024-07-17 19:36:06.423 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122] - Destroy memory task client cache succeed, task: t_1.1-mock_to_mock_20fields_1717403468657_3537-1721216122[6697ac7ab92eda1a86f5260c] 
