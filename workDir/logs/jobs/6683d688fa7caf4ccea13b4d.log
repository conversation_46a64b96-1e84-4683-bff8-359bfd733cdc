[INFO ] 2024-07-08 19:35:02.874 - [任务 47] - Task initialization... 
[INFO ] 2024-07-08 19:35:03.089 - [任务 47] - Start task milestones: 6683d688fa7caf4ccea13b4d(任务 47) 
[INFO ] 2024-07-08 19:35:03.089 - [任务 47] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-08 19:35:03.132 - [任务 47] - The engine receives 任务 47 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-08 19:35:03.185 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] start preload schema,table counts: 1 
[INFO ] 2024-07-08 19:35:03.185 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] start preload schema,table counts: 1 
[INFO ] 2024-07-08 19:35:03.185 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] preload schema finished, cost 0 ms 
[INFO ] 2024-07-08 19:35:03.186 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] preload schema finished, cost 0 ms 
[INFO ] 2024-07-08 19:35:36.387 - [任务 47][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-07-08 19:35:36.389 - [任务 47][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-07-08 19:35:36.390 - [任务 47][SouceMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-08 19:35:36.479 - [任务 47][SouceMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":82219434,"gtidSet":""} 
[INFO ] 2024-07-08 19:35:36.480 - [任务 47][SouceMysql] - Initial sync started 
[INFO ] 2024-07-08 19:35:36.493 - [任务 47][SouceMysql] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-08 19:35:36.494 - [任务 47][SouceMysql] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-08 19:35:36.657 - [任务 47][SouceMysql] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-08 19:35:36.659 - [任务 47][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-08 19:35:36.659 - [任务 47][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-08 19:35:36.914 - [任务 47][SouceMysql] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-08 19:35:36.921 - [任务 47][SouceMysql] - Initial sync completed 
[INFO ] 2024-07-08 19:35:36.922 - [任务 47][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-07-08 19:35:36.979 - [任务 47][SouceMysql] - Initial sync completed 
[INFO ] 2024-07-08 19:35:36.987 - [任务 47][SouceMysql] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"filename":"binlog.000032","position":82219434,"gtidSet":""} 
[INFO ] 2024-07-08 19:35:37.061 - [任务 47][SouceMysql] - Starting mysql cdc, server name: 9b537871-f5d5-4d9c-a16f-3883444ff3b9 
[INFO ] 2024-07-08 19:35:37.061 - [任务 47][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1631827778
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9b537871-f5d5-4d9c-a16f-3883444ff3b9
  database.port: 3306
  threadName: Debezium-Mysql-Connector-9b537871-f5d5-4d9c-a16f-3883444ff3b9
  database.hostname: localhost
  database.password: ********
  name: 9b537871-f5d5-4d9c-a16f-3883444ff3b9
  pdk.offset.string: {"name":"9b537871-f5d5-4d9c-a16f-3883444ff3b9","offset":{"{\"server\":\"9b537871-f5d5-4d9c-a16f-3883444ff3b9\"}":"{\"file\":\"binlog.000032\",\"pos\":82219434,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.POLICY,test2._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-08 19:35:37.266 - [任务 47][SouceMysql] - Connector Mysql incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-08 19:43:16.696 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] running status set to false 
[INFO ] 2024-07-08 19:43:16.722 - [任务 47][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-08 19:43:16.722 - [任务 47][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-07-08 19:43:16.731 - [任务 47][SouceMysql] - Incremental sync completed 
[INFO ] 2024-07-08 19:43:16.750 - [任务 47][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-88e29199-d37b-4f8d-8041-25f256a6a945 
[INFO ] 2024-07-08 19:43:16.751 - [任务 47][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-88e29199-d37b-4f8d-8041-25f256a6a945 
[INFO ] 2024-07-08 19:43:16.753 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] schema data cleaned 
[INFO ] 2024-07-08 19:43:16.753 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] monitor closed 
[INFO ] 2024-07-08 19:43:16.757 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] close complete, cost 141 ms 
[INFO ] 2024-07-08 19:43:16.758 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] running status set to false 
[INFO ] 2024-07-08 19:43:16.804 - [任务 47][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-f5df3834-4b65-42c5-afe4-29fdc6a1eb87 
[INFO ] 2024-07-08 19:43:16.805 - [任务 47][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-f5df3834-4b65-42c5-afe4-29fdc6a1eb87 
[INFO ] 2024-07-08 19:43:16.805 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] schema data cleaned 
[INFO ] 2024-07-08 19:43:16.805 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] monitor closed 
[INFO ] 2024-07-08 19:43:17.010 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] close complete, cost 48 ms 
[INFO ] 2024-07-08 19:43:19.272 - [任务 47] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-08 19:43:19.272 - [任务 47] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1175cfe0 
[INFO ] 2024-07-08 19:43:19.392 - [任务 47] - Stop task milestones: 6683d688fa7caf4ccea13b4d(任务 47)  
[INFO ] 2024-07-08 19:43:19.402 - [任务 47] - Stopped task aspect(s) 
[INFO ] 2024-07-08 19:43:19.402 - [任务 47] - Snapshot order controller have been removed 
[INFO ] 2024-07-08 19:43:19.435 - [任务 47] - Remove memory task client succeed, task: 任务 47[6683d688fa7caf4ccea13b4d] 
[INFO ] 2024-07-08 19:43:19.435 - [任务 47] - Destroy memory task client cache succeed, task: 任务 47[6683d688fa7caf4ccea13b4d] 
[INFO ] 2024-07-08 19:48:32.423 - [任务 47] - Task initialization... 
[INFO ] 2024-07-08 19:48:32.589 - [任务 47] - Start task milestones: 6683d688fa7caf4ccea13b4d(任务 47) 
[INFO ] 2024-07-08 19:48:32.589 - [任务 47] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-08 19:48:32.695 - [任务 47] - The engine receives 任务 47 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-08 19:48:32.695 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] start preload schema,table counts: 1 
[INFO ] 2024-07-08 19:48:32.695 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] start preload schema,table counts: 1 
[INFO ] 2024-07-08 19:48:32.695 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] preload schema finished, cost 0 ms 
[INFO ] 2024-07-08 19:48:32.696 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] preload schema finished, cost 0 ms 
[INFO ] 2024-07-08 19:49:17.704 - [任务 47][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-07-08 19:49:17.704 - [任务 47][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-07-08 19:49:17.704 - [任务 47][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-08 19:49:17.718 - [任务 47][SouceMysql] - batch offset found: {"POLICY":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"name":"9b537871-f5d5-4d9c-a16f-3883444ff3b9","offset":{"{\"server\":\"9b537871-f5d5-4d9c-a16f-3883444ff3b9\"}":"{\"ts_sec\":1720438995,\"file\":\"binlog.000032\",\"pos\":82327685,\"row\":1,\"server_id\":1,\"event\":2}"}} 
[INFO ] 2024-07-08 19:49:17.784 - [任务 47][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-08 19:49:17.784 - [任务 47][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-08 19:49:17.784 - [任务 47][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-07-08 19:49:17.784 - [任务 47][SouceMysql] - Initial sync completed 
[INFO ] 2024-07-08 19:49:17.821 - [任务 47][SouceMysql] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"name":"9b537871-f5d5-4d9c-a16f-3883444ff3b9","offset":{"{\"server\":\"9b537871-f5d5-4d9c-a16f-3883444ff3b9\"}":"{\"ts_sec\":1720438995,\"file\":\"binlog.000032\",\"pos\":82327685,\"row\":1,\"server_id\":1,\"event\":2}"}} 
[INFO ] 2024-07-08 19:49:17.821 - [任务 47][SouceMysql] - Starting mysql cdc, server name: 9b537871-f5d5-4d9c-a16f-3883444ff3b9 
[INFO ] 2024-07-08 19:49:17.898 - [任务 47][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 491262807
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9b537871-f5d5-4d9c-a16f-3883444ff3b9
  database.port: 3306
  threadName: Debezium-Mysql-Connector-9b537871-f5d5-4d9c-a16f-3883444ff3b9
  database.hostname: localhost
  database.password: ********
  name: 9b537871-f5d5-4d9c-a16f-3883444ff3b9
  pdk.offset.string: {"name":"9b537871-f5d5-4d9c-a16f-3883444ff3b9","offset":{"{\"server\":\"9b537871-f5d5-4d9c-a16f-3883444ff3b9\"}":"{\"ts_sec\":1720438995,\"file\":\"binlog.000032\",\"pos\":82327685,\"row\":1,\"server_id\":1,\"event\":2}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.POLICY,test2._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-08 19:49:17.898 - [任务 47][SouceMysql] - Connector Mysql incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-08 19:55:47.910 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] running status set to false 
[INFO ] 2024-07-08 19:55:47.977 - [任务 47][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-08 19:55:47.977 - [任务 47][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-07-08 19:55:47.980 - [任务 47][SouceMysql] - Incremental sync completed 
[INFO ] 2024-07-08 19:55:48.002 - [任务 47][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-88e29199-d37b-4f8d-8041-25f256a6a945 
[INFO ] 2024-07-08 19:55:48.002 - [任务 47][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-88e29199-d37b-4f8d-8041-25f256a6a945 
[INFO ] 2024-07-08 19:55:48.002 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] schema data cleaned 
[INFO ] 2024-07-08 19:55:48.009 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] monitor closed 
[INFO ] 2024-07-08 19:55:48.011 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] close complete, cost 119 ms 
[INFO ] 2024-07-08 19:55:48.011 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] running status set to false 
[INFO ] 2024-07-08 19:55:48.052 - [任务 47][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-f5df3834-4b65-42c5-afe4-29fdc6a1eb87 
[INFO ] 2024-07-08 19:55:48.052 - [任务 47][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-f5df3834-4b65-42c5-afe4-29fdc6a1eb87 
[INFO ] 2024-07-08 19:55:48.052 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] schema data cleaned 
[INFO ] 2024-07-08 19:55:48.052 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] monitor closed 
[INFO ] 2024-07-08 19:55:48.079 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] close complete, cost 42 ms 
[INFO ] 2024-07-08 19:55:48.079 - [任务 47] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-08 19:55:48.079 - [任务 47] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@37c0d6a4 
[INFO ] 2024-07-08 19:55:48.215 - [任务 47] - Stop task milestones: 6683d688fa7caf4ccea13b4d(任务 47)  
[INFO ] 2024-07-08 19:55:48.237 - [任务 47] - Stopped task aspect(s) 
[INFO ] 2024-07-08 19:55:48.238 - [任务 47] - Snapshot order controller have been removed 
[INFO ] 2024-07-08 19:55:48.270 - [任务 47] - Remove memory task client succeed, task: 任务 47[6683d688fa7caf4ccea13b4d] 
[INFO ] 2024-07-08 19:55:48.272 - [任务 47] - Destroy memory task client cache succeed, task: 任务 47[6683d688fa7caf4ccea13b4d] 
[INFO ] 2024-07-08 19:55:50.542 - [任务 47] - Task initialization... 
[INFO ] 2024-07-08 19:55:50.543 - [任务 47] - Start task milestones: 6683d688fa7caf4ccea13b4d(任务 47) 
[INFO ] 2024-07-08 19:55:50.745 - [任务 47] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-08 19:55:50.746 - [任务 47] - The engine receives 任务 47 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-08 19:55:50.809 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] start preload schema,table counts: 1 
[INFO ] 2024-07-08 19:55:50.809 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] start preload schema,table counts: 1 
[INFO ] 2024-07-08 19:55:50.809 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] preload schema finished, cost 0 ms 
[INFO ] 2024-07-08 19:55:52.786 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] preload schema finished, cost 1 ms 
[INFO ] 2024-07-08 19:58:45.594 - [任务 47][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-08 19:58:45.594 - [任务 47][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-08 19:58:45.648 - [任务 47][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-07-08 19:58:45.648 - [任务 47][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-07-08 19:58:45.649 - [任务 47][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-08 19:58:45.719 - [任务 47][SouceMysql] - batch offset found: {},stream offset found: {"name":"9b537871-f5d5-4d9c-a16f-3883444ff3b9","offset":{"{\"server\":\"9b537871-f5d5-4d9c-a16f-3883444ff3b9\"}":"{\"ts_sec\":1720439747,\"file\":\"binlog.000032\",\"pos\":82554725,\"row\":1,\"server_id\":1,\"event\":2}"}} 
[INFO ] 2024-07-08 19:58:45.719 - [任务 47][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-07-08 19:58:45.719 - [任务 47][SouceMysql] - Initial sync completed 
[INFO ] 2024-07-08 19:58:45.720 - [任务 47][SouceMysql] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"name":"9b537871-f5d5-4d9c-a16f-3883444ff3b9","offset":{"{\"server\":\"9b537871-f5d5-4d9c-a16f-3883444ff3b9\"}":"{\"ts_sec\":1720439747,\"file\":\"binlog.000032\",\"pos\":82554725,\"row\":1,\"server_id\":1,\"event\":2}"}} 
[INFO ] 2024-07-08 19:58:45.754 - [任务 47][SouceMysql] - Starting mysql cdc, server name: 9b537871-f5d5-4d9c-a16f-3883444ff3b9 
[INFO ] 2024-07-08 19:58:45.754 - [任务 47][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1090109326
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9b537871-f5d5-4d9c-a16f-3883444ff3b9
  database.port: 3306
  threadName: Debezium-Mysql-Connector-9b537871-f5d5-4d9c-a16f-3883444ff3b9
  database.hostname: localhost
  database.password: ********
  name: 9b537871-f5d5-4d9c-a16f-3883444ff3b9
  pdk.offset.string: {"name":"9b537871-f5d5-4d9c-a16f-3883444ff3b9","offset":{"{\"server\":\"9b537871-f5d5-4d9c-a16f-3883444ff3b9\"}":"{\"ts_sec\":1720439747,\"file\":\"binlog.000032\",\"pos\":82554725,\"row\":1,\"server_id\":1,\"event\":2}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.POLICY,test2._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-08 19:58:45.957 - [任务 47][SouceMysql] - Connector Mysql incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-08 20:02:38.340 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] running status set to false 
[INFO ] 2024-07-08 20:02:38.422 - [任务 47][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-08 20:02:38.425 - [任务 47][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-07-08 20:02:38.425 - [任务 47][SouceMysql] - Incremental sync completed 
[INFO ] 2024-07-08 20:02:38.433 - [任务 47][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-88e29199-d37b-4f8d-8041-25f256a6a945 
[INFO ] 2024-07-08 20:02:38.434 - [任务 47][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-88e29199-d37b-4f8d-8041-25f256a6a945 
[INFO ] 2024-07-08 20:02:38.435 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] schema data cleaned 
[INFO ] 2024-07-08 20:02:38.435 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] monitor closed 
[INFO ] 2024-07-08 20:02:38.438 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] close complete, cost 115 ms 
[INFO ] 2024-07-08 20:02:38.438 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] running status set to false 
[INFO ] 2024-07-08 20:02:38.469 - [任务 47][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-f5df3834-4b65-42c5-afe4-29fdc6a1eb87 
[INFO ] 2024-07-08 20:02:38.470 - [任务 47][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-f5df3834-4b65-42c5-afe4-29fdc6a1eb87 
[INFO ] 2024-07-08 20:02:38.470 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] schema data cleaned 
[INFO ] 2024-07-08 20:02:38.471 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] monitor closed 
[INFO ] 2024-07-08 20:02:38.472 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] close complete, cost 33 ms 
[INFO ] 2024-07-08 20:02:40.472 - [任务 47] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-08 20:02:40.472 - [任务 47] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@54e750a7 
[INFO ] 2024-07-08 20:02:40.622 - [任务 47] - Stop task milestones: 6683d688fa7caf4ccea13b4d(任务 47)  
[INFO ] 2024-07-08 20:02:40.622 - [任务 47] - Stopped task aspect(s) 
[INFO ] 2024-07-08 20:02:40.622 - [任务 47] - Snapshot order controller have been removed 
[INFO ] 2024-07-08 20:02:40.643 - [任务 47] - Remove memory task client succeed, task: 任务 47[6683d688fa7caf4ccea13b4d] 
[INFO ] 2024-07-08 20:02:40.643 - [任务 47] - Destroy memory task client cache succeed, task: 任务 47[6683d688fa7caf4ccea13b4d] 
[INFO ] 2024-07-08 20:02:42.055 - [任务 47] - Task initialization... 
[INFO ] 2024-07-08 20:02:42.063 - [任务 47] - Start task milestones: 6683d688fa7caf4ccea13b4d(任务 47) 
[INFO ] 2024-07-08 20:02:42.255 - [任务 47] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-08 20:02:42.256 - [任务 47] - The engine receives 任务 47 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-08 20:02:42.314 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] start preload schema,table counts: 1 
[INFO ] 2024-07-08 20:02:42.314 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] start preload schema,table counts: 1 
[INFO ] 2024-07-08 20:02:42.314 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] preload schema finished, cost 1 ms 
[INFO ] 2024-07-08 20:02:48.687 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] preload schema finished, cost 1 ms 
[INFO ] 2024-07-08 20:06:24.672 - [任务 47][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-08 20:06:24.673 - [任务 47][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-08 20:06:24.729 - [任务 47][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-07-08 20:06:24.729 - [任务 47][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-07-08 20:06:24.737 - [任务 47][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-08 20:06:24.737 - [任务 47][SouceMysql] - batch offset found: {},stream offset found: {"name":"9b537871-f5d5-4d9c-a16f-3883444ff3b9","offset":{"{\"server\":\"9b537871-f5d5-4d9c-a16f-3883444ff3b9\"}":"{\"ts_sec\":1720440157,\"file\":\"binlog.000032\",\"pos\":82649508,\"row\":1,\"server_id\":1,\"event\":2}"}} 
[INFO ] 2024-07-08 20:06:24.797 - [任务 47][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-07-08 20:06:24.797 - [任务 47][SouceMysql] - Initial sync completed 
[INFO ] 2024-07-08 20:06:24.823 - [任务 47][SouceMysql] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"name":"9b537871-f5d5-4d9c-a16f-3883444ff3b9","offset":{"{\"server\":\"9b537871-f5d5-4d9c-a16f-3883444ff3b9\"}":"{\"ts_sec\":1720440157,\"file\":\"binlog.000032\",\"pos\":82649508,\"row\":1,\"server_id\":1,\"event\":2}"}} 
[INFO ] 2024-07-08 20:06:24.824 - [任务 47][SouceMysql] - Starting mysql cdc, server name: 9b537871-f5d5-4d9c-a16f-3883444ff3b9 
[INFO ] 2024-07-08 20:06:24.871 - [任务 47][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2137718269
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9b537871-f5d5-4d9c-a16f-3883444ff3b9
  database.port: 3306
  threadName: Debezium-Mysql-Connector-9b537871-f5d5-4d9c-a16f-3883444ff3b9
  database.hostname: localhost
  database.password: ********
  name: 9b537871-f5d5-4d9c-a16f-3883444ff3b9
  pdk.offset.string: {"name":"9b537871-f5d5-4d9c-a16f-3883444ff3b9","offset":{"{\"server\":\"9b537871-f5d5-4d9c-a16f-3883444ff3b9\"}":"{\"ts_sec\":1720440157,\"file\":\"binlog.000032\",\"pos\":82649508,\"row\":1,\"server_id\":1,\"event\":2}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.POLICY,test2._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-08 20:06:24.879 - [任务 47][SouceMysql] - Connector Mysql incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-08 21:32:44.876 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] running status set to false 
[INFO ] 2024-07-08 21:32:44.975 - [任务 47][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-08 21:32:44.975 - [任务 47][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-07-08 21:32:44.991 - [任务 47][SouceMysql] - Incremental sync completed 
[INFO ] 2024-07-08 21:32:44.993 - [任务 47][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-88e29199-d37b-4f8d-8041-25f256a6a945 
[INFO ] 2024-07-08 21:32:44.993 - [任务 47][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-88e29199-d37b-4f8d-8041-25f256a6a945 
[INFO ] 2024-07-08 21:32:44.994 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] schema data cleaned 
[INFO ] 2024-07-08 21:32:44.994 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] monitor closed 
[INFO ] 2024-07-08 21:32:44.996 - [任务 47][SouceMysql] - Node SouceMysql[88e29199-d37b-4f8d-8041-25f256a6a945] close complete, cost 120 ms 
[INFO ] 2024-07-08 21:32:44.998 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] running status set to false 
[INFO ] 2024-07-08 21:32:45.006 - [任务 47][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-f5df3834-4b65-42c5-afe4-29fdc6a1eb87 
[INFO ] 2024-07-08 21:32:45.006 - [任务 47][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-f5df3834-4b65-42c5-afe4-29fdc6a1eb87 
[INFO ] 2024-07-08 21:32:45.006 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] schema data cleaned 
[INFO ] 2024-07-08 21:32:45.006 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] monitor closed 
[INFO ] 2024-07-08 21:32:45.209 - [任务 47][SourceMongo] - Node SourceMongo[f5df3834-4b65-42c5-afe4-29fdc6a1eb87] close complete, cost 10 ms 
[INFO ] 2024-07-08 21:32:49.367 - [任务 47] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-08 21:32:49.369 - [任务 47] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@10b60d00 
[INFO ] 2024-07-08 21:32:49.489 - [任务 47] - Stop task milestones: 6683d688fa7caf4ccea13b4d(任务 47)  
[INFO ] 2024-07-08 21:32:49.490 - [任务 47] - Stopped task aspect(s) 
[INFO ] 2024-07-08 21:32:49.490 - [任务 47] - Snapshot order controller have been removed 
[INFO ] 2024-07-08 21:32:49.508 - [任务 47] - Remove memory task client succeed, task: 任务 47[6683d688fa7caf4ccea13b4d] 
[INFO ] 2024-07-08 21:32:49.511 - [任务 47] - Destroy memory task client cache succeed, task: 任务 47[6683d688fa7caf4ccea13b4d] 
