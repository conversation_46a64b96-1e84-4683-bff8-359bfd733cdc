[INFO ] 2024-04-01 18:03:43.972 - [任务 38] - Start task milestones: 660a841e38ef7c7c95b82df1(任务 38) 
[INFO ] 2024-04-01 18:03:43.972 - [任务 38] - Task initialization... 
[INFO ] 2024-04-01 18:03:44.086 - [任务 38] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-01 18:03:44.087 - [任务 38] - The engine receives 任务 38 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-01 18:03:44.175 - [任务 38][test9] - Node test9[a07c0919-725e-4880-a1f6-a5a17c48658c] start preload schema,table counts: 1 
[INFO ] 2024-04-01 18:03:44.178 - [任务 38][CLAIM] - Node CLAIM[74d47b08-c5b5-4a91-a7bc-59539c7e22ed] start preload schema,table counts: 1 
[INFO ] 2024-04-01 18:03:44.240 - [任务 38][CLAIM] - Node CLAIM[74d47b08-c5b5-4a91-a7bc-59539c7e22ed] preload schema finished, cost 65 ms 
[INFO ] 2024-04-01 18:03:44.247 - [任务 38][test9] - Node test9[a07c0919-725e-4880-a1f6-a5a17c48658c] preload schema finished, cost 65 ms 
[INFO ] 2024-04-01 18:03:45.229 - [任务 38][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-01 18:03:45.230 - [任务 38][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-01 18:03:45.230 - [任务 38][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-01 18:03:45.235 - [任务 38][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145948132,"gtidSet":""} 
[INFO ] 2024-04-01 18:03:45.235 - [任务 38][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-04-01 18:03:45.313 - [任务 38][CLAIM] - Initial sync started 
[INFO ] 2024-04-01 18:03:45.314 - [任务 38][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-01 18:03:45.321 - [任务 38][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-01 18:03:45.399 - [任务 38][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-04-01 18:03:45.399 - [任务 38][test9] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-01 18:03:45.562 - [任务 38][CLAIM] - Initial sync completed 
[INFO ] 2024-04-01 18:03:45.563 - [任务 38][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-01 18:03:45.563 - [任务 38][CLAIM] - Initial sync completed 
[INFO ] 2024-04-01 18:03:45.605 - [任务 38][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":145948132,"gtidSet":""} 
[INFO ] 2024-04-01 18:03:45.609 - [任务 38][CLAIM] - Starting mysql cdc, server name: 6bdac34d-1c24-4246-bf32-33f0249bb849 
[INFO ] 2024-04-01 18:03:45.609 - [任务 38][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 722436141
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 6bdac34d-1c24-4246-bf32-33f0249bb849
  database.port: 3306
  threadName: Debezium-Mysql-Connector-6bdac34d-1c24-4246-bf32-33f0249bb849
  database.hostname: 127.0.0.1
  database.password: ********
  name: 6bdac34d-1c24-4246-bf32-33f0249bb849
  pdk.offset.string: {"name":"6bdac34d-1c24-4246-bf32-33f0249bb849","offset":{"{\"server\":\"6bdac34d-1c24-4246-bf32-33f0249bb849\"}":"{\"file\":\"binlog.000020\",\"pos\":145948132,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-01 18:03:45.811 - [任务 38][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-04-01 18:05:50.803 - [任务 38] - Stop task milestones: 660a841e38ef7c7c95b82df1(任务 38)  
[INFO ] 2024-04-01 18:05:51.180 - [任务 38][CLAIM] - Node CLAIM[74d47b08-c5b5-4a91-a7bc-59539c7e22ed] running status set to false 
[INFO ] 2024-04-01 18:05:51.231 - [任务 38][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-04-01 18:05:51.237 - [任务 38][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-04-01 18:05:51.257 - [任务 38][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-74d47b08-c5b5-4a91-a7bc-59539c7e22ed 
[INFO ] 2024-04-01 18:05:51.257 - [任务 38][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-74d47b08-c5b5-4a91-a7bc-59539c7e22ed 
[INFO ] 2024-04-01 18:05:51.258 - [任务 38][CLAIM] - Node CLAIM[74d47b08-c5b5-4a91-a7bc-59539c7e22ed] schema data cleaned 
[INFO ] 2024-04-01 18:05:51.258 - [任务 38][CLAIM] - Node CLAIM[74d47b08-c5b5-4a91-a7bc-59539c7e22ed] monitor closed 
[INFO ] 2024-04-01 18:05:51.258 - [任务 38][CLAIM] - Node CLAIM[74d47b08-c5b5-4a91-a7bc-59539c7e22ed] close complete, cost 90 ms 
[INFO ] 2024-04-01 18:05:51.279 - [任务 38][test9] - Node test9[a07c0919-725e-4880-a1f6-a5a17c48658c] running status set to false 
[INFO ] 2024-04-01 18:05:51.280 - [任务 38][test9] - PDK connector node stopped: HazelcastTargetPdkDataNode-a07c0919-725e-4880-a1f6-a5a17c48658c 
[INFO ] 2024-04-01 18:05:51.281 - [任务 38][test9] - PDK connector node released: HazelcastTargetPdkDataNode-a07c0919-725e-4880-a1f6-a5a17c48658c 
[INFO ] 2024-04-01 18:05:51.281 - [任务 38][test9] - Node test9[a07c0919-725e-4880-a1f6-a5a17c48658c] schema data cleaned 
[INFO ] 2024-04-01 18:05:51.282 - [任务 38][test9] - Node test9[a07c0919-725e-4880-a1f6-a5a17c48658c] monitor closed 
[INFO ] 2024-04-01 18:05:51.282 - [任务 38][test9] - Node test9[a07c0919-725e-4880-a1f6-a5a17c48658c] close complete, cost 23 ms 
[INFO ] 2024-04-01 18:05:55.394 - [任务 38] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-01 18:05:55.394 - [任务 38] - Stopped task aspect(s) 
[INFO ] 2024-04-01 18:05:55.394 - [任务 38] - Snapshot order controller have been removed 
[INFO ] 2024-04-01 18:05:55.430 - [任务 38] - Remove memory task client succeed, task: 任务 38[660a841e38ef7c7c95b82df1] 
[INFO ] 2024-04-01 18:05:55.433 - [任务 38] - Destroy memory task client cache succeed, task: 任务 38[660a841e38ef7c7c95b82df1] 
