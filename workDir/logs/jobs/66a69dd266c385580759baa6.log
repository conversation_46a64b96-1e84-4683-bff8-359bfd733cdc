[INFO ] 2024-07-29 03:38:15.551 - [测试CK INT8~INT64] - Task initialization... 
[INFO ] 2024-07-29 03:38:15.743 - [测试CK INT8~INT64] - Start task milestones: 66a69dd266c385580759baa6(测试CK INT8~INT64) 
[INFO ] 2024-07-29 03:38:15.744 - [测试CK INT8~INT64] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-29 03:38:15.786 - [测试CK INT8~INT64] - The engine receives 测试CK INT8~INT64 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-29 03:38:15.851 - [测试CK INT8~INT64][testInt] - Node testInt[975f1698-4bba-460c-8802-981b1a6f1cb1] start preload schema,table counts: 1 
[INFO ] 2024-07-29 03:38:15.852 - [测试CK INT8~INT64][INT864] - Node INT864[d65c4405-c39a-4466-a632-8aea52133844] start preload schema,table counts: 1 
[INFO ] 2024-07-29 03:38:15.852 - [测试CK INT8~INT64][testInt] - Node testInt[975f1698-4bba-460c-8802-981b1a6f1cb1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 03:38:16.056 - [测试CK INT8~INT64][INT864] - Node INT864[d65c4405-c39a-4466-a632-8aea52133844] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 03:38:16.534 - [测试CK INT8~INT64][testInt] - Source node "testInt" read batch size: 100 
[INFO ] 2024-07-29 03:38:16.537 - [测试CK INT8~INT64][testInt] - Source node "testInt" event queue capacity: 200 
[INFO ] 2024-07-29 03:38:16.537 - [测试CK INT8~INT64][testInt] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-29 03:38:16.537 - [测试CK INT8~INT64][testInt] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-29 03:38:16.623 - [测试CK INT8~INT64][testInt] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-29 03:38:16.623 - [测试CK INT8~INT64][testInt] - Initial sync started 
[INFO ] 2024-07-29 03:38:16.624 - [测试CK INT8~INT64][testInt] - Starting batch read, table name: testInt, offset: null 
[INFO ] 2024-07-29 03:38:16.629 - [测试CK INT8~INT64][testInt] - Table testInt is going to be initial synced 
[INFO ] 2024-07-29 03:38:16.654 - [测试CK INT8~INT64][testInt] - Query table 'testInt' counts: 1 
[INFO ] 2024-07-29 03:38:16.666 - [测试CK INT8~INT64][testInt] - Table [testInt] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-29 03:38:16.666 - [测试CK INT8~INT64][testInt] - Initial sync completed 
[INFO ] 2024-07-29 03:38:17.070 - [测试CK INT8~INT64][INT864] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-29 03:38:17.701 - [测试CK INT8~INT64][testInt] - Node testInt[975f1698-4bba-460c-8802-981b1a6f1cb1] running status set to false 
[INFO ] 2024-07-29 03:38:17.702 - [测试CK INT8~INT64][INT864] - Node INT864[d65c4405-c39a-4466-a632-8aea52133844] running status set to false 
[INFO ] 2024-07-29 03:38:17.702 - [测试CK INT8~INT64][testInt] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-29 03:38:17.736 - [测试CK INT8~INT64][testInt] - Clickhouse Optimize Table end 
[INFO ] 2024-07-29 03:38:17.756 - [测试CK INT8~INT64][INT864] - PDK connector node stopped: HazelcastTargetPdkDataNode-d65c4405-c39a-4466-a632-8aea52133844 
[INFO ] 2024-07-29 03:38:17.757 - [测试CK INT8~INT64][INT864] - PDK connector node released: HazelcastTargetPdkDataNode-d65c4405-c39a-4466-a632-8aea52133844 
[INFO ] 2024-07-29 03:38:17.757 - [测试CK INT8~INT64][INT864] - Node INT864[d65c4405-c39a-4466-a632-8aea52133844] schema data cleaned 
[INFO ] 2024-07-29 03:38:17.771 - [测试CK INT8~INT64][INT864] - Node INT864[d65c4405-c39a-4466-a632-8aea52133844] monitor closed 
[INFO ] 2024-07-29 03:38:17.772 - [测试CK INT8~INT64][INT864] - Node INT864[d65c4405-c39a-4466-a632-8aea52133844] close complete, cost 71 ms 
[INFO ] 2024-07-29 03:38:17.787 - [测试CK INT8~INT64][testInt] - PDK connector node stopped: HazelcastSourcePdkDataNode-975f1698-4bba-460c-8802-981b1a6f1cb1 
[INFO ] 2024-07-29 03:38:17.787 - [测试CK INT8~INT64][testInt] - PDK connector node released: HazelcastSourcePdkDataNode-975f1698-4bba-460c-8802-981b1a6f1cb1 
[INFO ] 2024-07-29 03:38:17.788 - [测试CK INT8~INT64][testInt] - Node testInt[975f1698-4bba-460c-8802-981b1a6f1cb1] schema data cleaned 
[INFO ] 2024-07-29 03:38:17.788 - [测试CK INT8~INT64][testInt] - Node testInt[975f1698-4bba-460c-8802-981b1a6f1cb1] monitor closed 
[INFO ] 2024-07-29 03:38:17.995 - [测试CK INT8~INT64][testInt] - Node testInt[975f1698-4bba-460c-8802-981b1a6f1cb1] close complete, cost 109 ms 
[INFO ] 2024-07-29 03:38:18.908 - [测试CK INT8~INT64] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-29 03:38:19.054 - [测试CK INT8~INT64] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@c5492c3 
[INFO ] 2024-07-29 03:38:19.055 - [测试CK INT8~INT64] - Stop task milestones: 66a69dd266c385580759baa6(测试CK INT8~INT64)  
[INFO ] 2024-07-29 03:38:19.073 - [测试CK INT8~INT64] - Stopped task aspect(s) 
[INFO ] 2024-07-29 03:38:19.074 - [测试CK INT8~INT64] - Snapshot order controller have been removed 
[INFO ] 2024-07-29 03:38:19.106 - [测试CK INT8~INT64] - Remove memory task client succeed, task: 测试CK INT8~INT64[66a69dd266c385580759baa6] 
[INFO ] 2024-07-29 03:38:19.312 - [测试CK INT8~INT64] - Destroy memory task client cache succeed, task: 测试CK INT8~INT64[66a69dd266c385580759baa6] 
