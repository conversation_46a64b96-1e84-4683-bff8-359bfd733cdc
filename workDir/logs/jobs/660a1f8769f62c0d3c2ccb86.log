[INFO ] 2024-04-01 10:44:41.189 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:44:41.189 - [任务 36(100)][0c4459d7-7f39-49ca-8d32-d18f95270a3a] - Node 0c4459d7-7f39-49ca-8d32-d18f95270a3a[0c4459d7-7f39-49ca-8d32-d18f95270a3a] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:44:41.190 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:44:41.191 - [任务 36(100)][0c4459d7-7f39-49ca-8d32-d18f95270a3a] - Node 0c4459d7-7f39-49ca-8d32-d18f95270a3a[0c4459d7-7f39-49ca-8d32-d18f95270a3a] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:44:41.191 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:44:41.191 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:44:41.540 - [任务 36(100)][8d40070d-08c1-4477-a704-bc7b7a5987d4] - Node 8d40070d-08c1-4477-a704-bc7b7a5987d4[8d40070d-08c1-4477-a704-bc7b7a5987d4] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:44:41.543 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:44:41.544 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:44:41.544 - [任务 36(100)][8d40070d-08c1-4477-a704-bc7b7a5987d4] - Node 8d40070d-08c1-4477-a704-bc7b7a5987d4[8d40070d-08c1-4477-a704-bc7b7a5987d4] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:44:41.544 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:44:41.544 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:44:42.199 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:44:42.199 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:44:42.199 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:44:42.200 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:44:42.200 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:44:42.405 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 18 ms 
[INFO ] 2024-04-01 10:44:42.419 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:44:42.419 - [任务 36(100)][8d40070d-08c1-4477-a704-bc7b7a5987d4] - Node 8d40070d-08c1-4477-a704-bc7b7a5987d4[8d40070d-08c1-4477-a704-bc7b7a5987d4] running status set to false 
[INFO ] 2024-04-01 10:44:42.420 - [任务 36(100)][8d40070d-08c1-4477-a704-bc7b7a5987d4] - Node 8d40070d-08c1-4477-a704-bc7b7a5987d4[8d40070d-08c1-4477-a704-bc7b7a5987d4] schema data cleaned 
[INFO ] 2024-04-01 10:44:42.424 - [任务 36(100)][8d40070d-08c1-4477-a704-bc7b7a5987d4] - Node 8d40070d-08c1-4477-a704-bc7b7a5987d4[8d40070d-08c1-4477-a704-bc7b7a5987d4] monitor closed 
[INFO ] 2024-04-01 10:44:42.425 - [任务 36(100)][8d40070d-08c1-4477-a704-bc7b7a5987d4] - Node 8d40070d-08c1-4477-a704-bc7b7a5987d4[8d40070d-08c1-4477-a704-bc7b7a5987d4] close complete, cost 3 ms 
[INFO ] 2024-04-01 10:44:42.427 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-4a6d4fab-1266-4c45-aace-90a653bef6af 
[INFO ] 2024-04-01 10:44:42.428 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-4a6d4fab-1266-4c45-aace-90a653bef6af 
[INFO ] 2024-04-01 10:44:42.428 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:44:42.433 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:44:42.433 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:44:42.433 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 22 ms 
[INFO ] 2024-04-01 10:44:42.651 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-8d40070d-08c1-4477-a704-bc7b7a5987d4 complete, cost 959ms 
[INFO ] 2024-04-01 10:44:42.697 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:44:42.697 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:44:42.697 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:44:42.697 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:44:42.697 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:44:42.697 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:44:42.699 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-71bc336d-e889-45ef-b36b-82c2e041c194 
[INFO ] 2024-04-01 10:44:42.699 - [任务 36(100)][0c4459d7-7f39-49ca-8d32-d18f95270a3a] - Node 0c4459d7-7f39-49ca-8d32-d18f95270a3a[0c4459d7-7f39-49ca-8d32-d18f95270a3a] running status set to false 
[INFO ] 2024-04-01 10:44:42.699 - [任务 36(100)][0c4459d7-7f39-49ca-8d32-d18f95270a3a] - Node 0c4459d7-7f39-49ca-8d32-d18f95270a3a[0c4459d7-7f39-49ca-8d32-d18f95270a3a] schema data cleaned 
[INFO ] 2024-04-01 10:44:42.699 - [任务 36(100)][0c4459d7-7f39-49ca-8d32-d18f95270a3a] - Node 0c4459d7-7f39-49ca-8d32-d18f95270a3a[0c4459d7-7f39-49ca-8d32-d18f95270a3a] monitor closed 
[INFO ] 2024-04-01 10:44:42.699 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-71bc336d-e889-45ef-b36b-82c2e041c194 
[INFO ] 2024-04-01 10:44:42.699 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 45 ms 
[INFO ] 2024-04-01 10:44:42.699 - [任务 36(100)][0c4459d7-7f39-49ca-8d32-d18f95270a3a] - Node 0c4459d7-7f39-49ca-8d32-d18f95270a3a[0c4459d7-7f39-49ca-8d32-d18f95270a3a] close complete, cost 0 ms 
[INFO ] 2024-04-01 10:44:42.700 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:44:42.701 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:44:42.702 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:44:42.703 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 10 ms 
[INFO ] 2024-04-01 10:44:42.904 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-0c4459d7-7f39-49ca-8d32-d18f95270a3a complete, cost 1599ms 
[INFO ] 2024-04-01 10:44:48.719 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:44:48.721 - [任务 36(100)][f1dc76c0-ec43-4744-9de7-b33ba6bd85c4] - Node f1dc76c0-ec43-4744-9de7-b33ba6bd85c4[f1dc76c0-ec43-4744-9de7-b33ba6bd85c4] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:44:48.721 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:44:48.721 - [任务 36(100)][f1dc76c0-ec43-4744-9de7-b33ba6bd85c4] - Node f1dc76c0-ec43-4744-9de7-b33ba6bd85c4[f1dc76c0-ec43-4744-9de7-b33ba6bd85c4] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:44:48.723 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 10:44:48.723 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:44:49.053 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:44:49.053 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:44:49.054 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:44:49.054 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:44:49.055 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:44:49.055 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 18 ms 
[INFO ] 2024-04-01 10:44:49.271 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:44:49.272 - [任务 36(100)][f1dc76c0-ec43-4744-9de7-b33ba6bd85c4] - Node f1dc76c0-ec43-4744-9de7-b33ba6bd85c4[f1dc76c0-ec43-4744-9de7-b33ba6bd85c4] running status set to false 
[INFO ] 2024-04-01 10:44:49.272 - [任务 36(100)][f1dc76c0-ec43-4744-9de7-b33ba6bd85c4] - Node f1dc76c0-ec43-4744-9de7-b33ba6bd85c4[f1dc76c0-ec43-4744-9de7-b33ba6bd85c4] schema data cleaned 
[INFO ] 2024-04-01 10:44:49.272 - [任务 36(100)][f1dc76c0-ec43-4744-9de7-b33ba6bd85c4] - Node f1dc76c0-ec43-4744-9de7-b33ba6bd85c4[f1dc76c0-ec43-4744-9de7-b33ba6bd85c4] monitor closed 
[INFO ] 2024-04-01 10:44:49.278 - [任务 36(100)][f1dc76c0-ec43-4744-9de7-b33ba6bd85c4] - Node f1dc76c0-ec43-4744-9de7-b33ba6bd85c4[f1dc76c0-ec43-4744-9de7-b33ba6bd85c4] close complete, cost 2 ms 
[INFO ] 2024-04-01 10:44:49.278 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-4f58b4b1-3153-49c2-a604-240c3caf25a9 
[INFO ] 2024-04-01 10:44:49.279 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-4f58b4b1-3153-49c2-a604-240c3caf25a9 
[INFO ] 2024-04-01 10:44:49.279 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:44:49.281 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:44:49.282 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:44:49.283 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 14 ms 
[INFO ] 2024-04-01 10:44:49.283 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-f1dc76c0-ec43-4744-9de7-b33ba6bd85c4 complete, cost 621ms 
[INFO ] 2024-04-01 10:44:50.871 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:44:50.873 - [任务 36(100)][47bc4c24-17ab-487e-bf93-f31f02935b76] - Node 47bc4c24-17ab-487e-bf93-f31f02935b76[47bc4c24-17ab-487e-bf93-f31f02935b76] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:44:50.873 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:44:50.873 - [任务 36(100)][47bc4c24-17ab-487e-bf93-f31f02935b76] - Node 47bc4c24-17ab-487e-bf93-f31f02935b76[47bc4c24-17ab-487e-bf93-f31f02935b76] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 10:44:50.873 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 10:44:50.873 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:44:51.067 - [任务 36(100)][69de503e-4465-48c4-b3b6-9706c1f9f5ce] - Node 69de503e-4465-48c4-b3b6-9706c1f9f5ce[69de503e-4465-48c4-b3b6-9706c1f9f5ce] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:44:51.067 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:44:51.068 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:44:51.069 - [任务 36(100)][69de503e-4465-48c4-b3b6-9706c1f9f5ce] - Node 69de503e-4465-48c4-b3b6-9706c1f9f5ce[69de503e-4465-48c4-b3b6-9706c1f9f5ce] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:44:51.072 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:44:51.072 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:44:51.168 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:44:51.168 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:44:51.169 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:44:51.169 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:44:51.174 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:44:51.174 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 14 ms 
[INFO ] 2024-04-01 10:44:51.610 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:44:51.611 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:44:51.611 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:44:51.612 - [任务 36(100)][69de503e-4465-48c4-b3b6-9706c1f9f5ce] - Node 69de503e-4465-48c4-b3b6-9706c1f9f5ce[69de503e-4465-48c4-b3b6-9706c1f9f5ce] running status set to false 
[INFO ] 2024-04-01 10:44:51.612 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:44:51.612 - [任务 36(100)][69de503e-4465-48c4-b3b6-9706c1f9f5ce] - Node 69de503e-4465-48c4-b3b6-9706c1f9f5ce[69de503e-4465-48c4-b3b6-9706c1f9f5ce] schema data cleaned 
[INFO ] 2024-04-01 10:44:51.612 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:44:51.612 - [任务 36(100)][69de503e-4465-48c4-b3b6-9706c1f9f5ce] - Node 69de503e-4465-48c4-b3b6-9706c1f9f5ce[69de503e-4465-48c4-b3b6-9706c1f9f5ce] monitor closed 
[INFO ] 2024-04-01 10:44:51.613 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:44:51.614 - [任务 36(100)][69de503e-4465-48c4-b3b6-9706c1f9f5ce] - Node 69de503e-4465-48c4-b3b6-9706c1f9f5ce[69de503e-4465-48c4-b3b6-9706c1f9f5ce] close complete, cost 11 ms 
[INFO ] 2024-04-01 10:44:51.614 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 36 ms 
[INFO ] 2024-04-01 10:44:51.620 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-51218d52-1a00-49d8-8112-831a147a23f2 
[INFO ] 2024-04-01 10:44:51.621 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-51218d52-1a00-49d8-8112-831a147a23f2 
[INFO ] 2024-04-01 10:44:51.621 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:44:51.623 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:44:51.623 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:44:51.625 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 24 ms 
[INFO ] 2024-04-01 10:44:51.625 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-69de503e-4465-48c4-b3b6-9706c1f9f5ce complete, cost 590ms 
[INFO ] 2024-04-01 10:44:51.866 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:44:51.867 - [任务 36(100)][47bc4c24-17ab-487e-bf93-f31f02935b76] - Node 47bc4c24-17ab-487e-bf93-f31f02935b76[47bc4c24-17ab-487e-bf93-f31f02935b76] running status set to false 
[INFO ] 2024-04-01 10:44:51.867 - [任务 36(100)][47bc4c24-17ab-487e-bf93-f31f02935b76] - Node 47bc4c24-17ab-487e-bf93-f31f02935b76[47bc4c24-17ab-487e-bf93-f31f02935b76] schema data cleaned 
[INFO ] 2024-04-01 10:44:51.867 - [任务 36(100)][47bc4c24-17ab-487e-bf93-f31f02935b76] - Node 47bc4c24-17ab-487e-bf93-f31f02935b76[47bc4c24-17ab-487e-bf93-f31f02935b76] monitor closed 
[INFO ] 2024-04-01 10:44:51.867 - [任务 36(100)][47bc4c24-17ab-487e-bf93-f31f02935b76] - Node 47bc4c24-17ab-487e-bf93-f31f02935b76[47bc4c24-17ab-487e-bf93-f31f02935b76] close complete, cost 5 ms 
[INFO ] 2024-04-01 10:44:51.870 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-8f63ceb7-7448-4f50-8cfc-45720944afb2 
[INFO ] 2024-04-01 10:44:51.871 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-8f63ceb7-7448-4f50-8cfc-45720944afb2 
[INFO ] 2024-04-01 10:44:51.871 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:44:51.872 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:44:51.872 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:44:51.875 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 17 ms 
[INFO ] 2024-04-01 10:44:51.875 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-47bc4c24-17ab-487e-bf93-f31f02935b76 complete, cost 1076ms 
[INFO ] 2024-04-01 10:45:46.205 - [任务 36(100)][10c36a07-1e61-415f-b255-0a66b798cec0] - Node 10c36a07-1e61-415f-b255-0a66b798cec0[10c36a07-1e61-415f-b255-0a66b798cec0] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:45:46.206 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:45:46.206 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:45:46.206 - [任务 36(100)][10c36a07-1e61-415f-b255-0a66b798cec0] - Node 10c36a07-1e61-415f-b255-0a66b798cec0[10c36a07-1e61-415f-b255-0a66b798cec0] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:45:46.206 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:45:46.206 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:45:46.458 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:45:46.458 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:45:46.459 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:45:46.459 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:45:46.461 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:45:46.461 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 11 ms 
[INFO ] 2024-04-01 10:45:46.682 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:45:46.682 - [任务 36(100)][10c36a07-1e61-415f-b255-0a66b798cec0] - Node 10c36a07-1e61-415f-b255-0a66b798cec0[10c36a07-1e61-415f-b255-0a66b798cec0] running status set to false 
[INFO ] 2024-04-01 10:45:46.682 - [任务 36(100)][10c36a07-1e61-415f-b255-0a66b798cec0] - Node 10c36a07-1e61-415f-b255-0a66b798cec0[10c36a07-1e61-415f-b255-0a66b798cec0] schema data cleaned 
[INFO ] 2024-04-01 10:45:46.683 - [任务 36(100)][10c36a07-1e61-415f-b255-0a66b798cec0] - Node 10c36a07-1e61-415f-b255-0a66b798cec0[10c36a07-1e61-415f-b255-0a66b798cec0] monitor closed 
[INFO ] 2024-04-01 10:45:46.686 - [任务 36(100)][10c36a07-1e61-415f-b255-0a66b798cec0] - Node 10c36a07-1e61-415f-b255-0a66b798cec0[10c36a07-1e61-415f-b255-0a66b798cec0] close complete, cost 1 ms 
[INFO ] 2024-04-01 10:45:46.686 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-5e668a73-eff0-44fb-aaf2-deadc3f4cfd6 
[INFO ] 2024-04-01 10:45:46.687 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-5e668a73-eff0-44fb-aaf2-deadc3f4cfd6 
[INFO ] 2024-04-01 10:45:46.687 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:45:46.688 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:45:46.688 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:45:46.688 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 10 ms 
[INFO ] 2024-04-01 10:45:46.901 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-10c36a07-1e61-415f-b255-0a66b798cec0 complete, cost 592ms 
[INFO ] 2024-04-01 10:45:50.242 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:45:50.242 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:45:50.243 - [任务 36(100)][8003ad86-664e-48c8-80ef-d928d2fe5aab] - Node 8003ad86-664e-48c8-80ef-d928d2fe5aab[8003ad86-664e-48c8-80ef-d928d2fe5aab] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:45:50.243 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:45:50.243 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 10:45:50.243 - [任务 36(100)][8003ad86-664e-48c8-80ef-d928d2fe5aab] - Node 8003ad86-664e-48c8-80ef-d928d2fe5aab[8003ad86-664e-48c8-80ef-d928d2fe5aab] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:45:50.518 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:45:50.518 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:45:50.518 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:45:50.518 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:45:50.519 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:45:50.727 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 19 ms 
[INFO ] 2024-04-01 10:45:50.736 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:45:50.737 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-5cbe1ae4-97ae-450b-bb11-905a935c1836 
[INFO ] 2024-04-01 10:45:50.738 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-5cbe1ae4-97ae-450b-bb11-905a935c1836 
[INFO ] 2024-04-01 10:45:50.738 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:45:50.738 - [任务 36(100)][8003ad86-664e-48c8-80ef-d928d2fe5aab] - Node 8003ad86-664e-48c8-80ef-d928d2fe5aab[8003ad86-664e-48c8-80ef-d928d2fe5aab] running status set to false 
[INFO ] 2024-04-01 10:45:50.738 - [任务 36(100)][8003ad86-664e-48c8-80ef-d928d2fe5aab] - Node 8003ad86-664e-48c8-80ef-d928d2fe5aab[8003ad86-664e-48c8-80ef-d928d2fe5aab] schema data cleaned 
[INFO ] 2024-04-01 10:45:50.738 - [任务 36(100)][8003ad86-664e-48c8-80ef-d928d2fe5aab] - Node 8003ad86-664e-48c8-80ef-d928d2fe5aab[8003ad86-664e-48c8-80ef-d928d2fe5aab] monitor closed 
[INFO ] 2024-04-01 10:45:50.738 - [任务 36(100)][8003ad86-664e-48c8-80ef-d928d2fe5aab] - Node 8003ad86-664e-48c8-80ef-d928d2fe5aab[8003ad86-664e-48c8-80ef-d928d2fe5aab] close complete, cost 2 ms 
[INFO ] 2024-04-01 10:45:50.738 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:45:50.738 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:45:50.738 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 12 ms 
[INFO ] 2024-04-01 10:45:50.738 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-8003ad86-664e-48c8-80ef-d928d2fe5aab complete, cost 567ms 
[INFO ] 2024-04-01 10:45:52.025 - [任务 36(100)][eff15423-d71b-41ad-8964-e04370323d52] - Node eff15423-d71b-41ad-8964-e04370323d52[eff15423-d71b-41ad-8964-e04370323d52] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:45:52.025 - [任务 36(100)][eff15423-d71b-41ad-8964-e04370323d52] - Node eff15423-d71b-41ad-8964-e04370323d52[eff15423-d71b-41ad-8964-e04370323d52] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:45:52.026 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:45:52.026 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:45:52.026 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:45:52.026 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:45:52.262 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:45:52.278 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:45:52.278 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:45:52.279 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:45:52.279 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:45:52.481 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 22 ms 
[INFO ] 2024-04-01 10:45:52.542 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:45:52.542 - [任务 36(100)][eff15423-d71b-41ad-8964-e04370323d52] - Node eff15423-d71b-41ad-8964-e04370323d52[eff15423-d71b-41ad-8964-e04370323d52] running status set to false 
[INFO ] 2024-04-01 10:45:52.542 - [任务 36(100)][eff15423-d71b-41ad-8964-e04370323d52] - Node eff15423-d71b-41ad-8964-e04370323d52[eff15423-d71b-41ad-8964-e04370323d52] schema data cleaned 
[INFO ] 2024-04-01 10:45:52.543 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-1a95d65f-0d56-44d3-b864-30685be3fbbd 
[INFO ] 2024-04-01 10:45:52.543 - [任务 36(100)][eff15423-d71b-41ad-8964-e04370323d52] - Node eff15423-d71b-41ad-8964-e04370323d52[eff15423-d71b-41ad-8964-e04370323d52] monitor closed 
[INFO ] 2024-04-01 10:45:52.543 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-1a95d65f-0d56-44d3-b864-30685be3fbbd 
[INFO ] 2024-04-01 10:45:52.543 - [任务 36(100)][eff15423-d71b-41ad-8964-e04370323d52] - Node eff15423-d71b-41ad-8964-e04370323d52[eff15423-d71b-41ad-8964-e04370323d52] close complete, cost 6 ms 
[INFO ] 2024-04-01 10:45:52.543 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:45:52.545 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:45:52.545 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:45:52.546 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 15 ms 
[INFO ] 2024-04-01 10:45:52.546 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-eff15423-d71b-41ad-8964-e04370323d52 complete, cost 553ms 
[INFO ] 2024-04-01 10:45:54.279 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:45:54.279 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:45:54.280 - [任务 36(100)][f78715c9-b4dd-4a27-adb9-80de0ee92942] - Node f78715c9-b4dd-4a27-adb9-80de0ee92942[f78715c9-b4dd-4a27-adb9-80de0ee92942] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:45:54.280 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 10:45:54.280 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 10:45:54.280 - [任务 36(100)][f78715c9-b4dd-4a27-adb9-80de0ee92942] - Node f78715c9-b4dd-4a27-adb9-80de0ee92942[f78715c9-b4dd-4a27-adb9-80de0ee92942] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 10:45:54.354 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:45:54.354 - [任务 36(100)][aa13dc8a-bc26-47b0-a65b-a6950450d248] - Node aa13dc8a-bc26-47b0-a65b-a6950450d248[aa13dc8a-bc26-47b0-a65b-a6950450d248] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:45:54.354 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:45:54.355 - [任务 36(100)][aa13dc8a-bc26-47b0-a65b-a6950450d248] - Node aa13dc8a-bc26-47b0-a65b-a6950450d248[aa13dc8a-bc26-47b0-a65b-a6950450d248] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:45:54.356 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:45:54.356 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:45:54.552 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:45:54.552 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:45:54.553 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:45:54.553 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:45:54.554 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:45:54.554 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 12 ms 
[INFO ] 2024-04-01 10:45:55.004 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:45:55.004 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:45:55.004 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:45:55.005 - [任务 36(100)][aa13dc8a-bc26-47b0-a65b-a6950450d248] - Node aa13dc8a-bc26-47b0-a65b-a6950450d248[aa13dc8a-bc26-47b0-a65b-a6950450d248] running status set to false 
[INFO ] 2024-04-01 10:45:55.005 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:45:55.005 - [任务 36(100)][aa13dc8a-bc26-47b0-a65b-a6950450d248] - Node aa13dc8a-bc26-47b0-a65b-a6950450d248[aa13dc8a-bc26-47b0-a65b-a6950450d248] schema data cleaned 
[INFO ] 2024-04-01 10:45:55.005 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:45:55.005 - [任务 36(100)][aa13dc8a-bc26-47b0-a65b-a6950450d248] - Node aa13dc8a-bc26-47b0-a65b-a6950450d248[aa13dc8a-bc26-47b0-a65b-a6950450d248] monitor closed 
[INFO ] 2024-04-01 10:45:55.005 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:45:55.005 - [任务 36(100)][aa13dc8a-bc26-47b0-a65b-a6950450d248] - Node aa13dc8a-bc26-47b0-a65b-a6950450d248[aa13dc8a-bc26-47b0-a65b-a6950450d248] close complete, cost 3 ms 
[INFO ] 2024-04-01 10:45:55.005 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 30 ms 
[INFO ] 2024-04-01 10:45:55.009 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-227f8e60-8528-4011-873d-c7c8a4daaa6d 
[INFO ] 2024-04-01 10:45:55.013 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-227f8e60-8528-4011-873d-c7c8a4daaa6d 
[INFO ] 2024-04-01 10:45:55.013 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:45:55.013 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:45:55.013 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:45:55.013 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 13 ms 
[INFO ] 2024-04-01 10:45:55.172 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-aa13dc8a-bc26-47b0-a65b-a6950450d248 complete, cost 686ms 
[INFO ] 2024-04-01 10:45:55.172 - [任务 36(100)][9ad64398-9bef-4149-af7e-2c000821fbc8] - Node 9ad64398-9bef-4149-af7e-2c000821fbc8[9ad64398-9bef-4149-af7e-2c000821fbc8] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:45:55.172 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:45:55.172 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:45:55.172 - [任务 36(100)][9ad64398-9bef-4149-af7e-2c000821fbc8] - Node 9ad64398-9bef-4149-af7e-2c000821fbc8[9ad64398-9bef-4149-af7e-2c000821fbc8] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:45:55.172 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:45:55.172 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 10:45:55.273 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:45:55.274 - [任务 36(100)][f78715c9-b4dd-4a27-adb9-80de0ee92942] - Node f78715c9-b4dd-4a27-adb9-80de0ee92942[f78715c9-b4dd-4a27-adb9-80de0ee92942] running status set to false 
[INFO ] 2024-04-01 10:45:55.274 - [任务 36(100)][f78715c9-b4dd-4a27-adb9-80de0ee92942] - Node f78715c9-b4dd-4a27-adb9-80de0ee92942[f78715c9-b4dd-4a27-adb9-80de0ee92942] schema data cleaned 
[INFO ] 2024-04-01 10:45:55.274 - [任务 36(100)][f78715c9-b4dd-4a27-adb9-80de0ee92942] - Node f78715c9-b4dd-4a27-adb9-80de0ee92942[f78715c9-b4dd-4a27-adb9-80de0ee92942] monitor closed 
[INFO ] 2024-04-01 10:45:55.275 - [任务 36(100)][f78715c9-b4dd-4a27-adb9-80de0ee92942] - Node f78715c9-b4dd-4a27-adb9-80de0ee92942[f78715c9-b4dd-4a27-adb9-80de0ee92942] close complete, cost 4 ms 
[INFO ] 2024-04-01 10:45:55.279 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-c31709c1-659b-439f-a1b1-7a22242434e8 
[INFO ] 2024-04-01 10:45:55.279 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-c31709c1-659b-439f-a1b1-7a22242434e8 
[INFO ] 2024-04-01 10:45:55.281 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:45:55.281 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:45:55.281 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:45:55.282 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 16 ms 
[INFO ] 2024-04-01 10:45:55.469 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-f78715c9-b4dd-4a27-adb9-80de0ee92942 complete, cost 1055ms 
[INFO ] 2024-04-01 10:45:55.469 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:45:55.472 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:45:55.472 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:45:55.472 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:45:55.472 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:45:55.673 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 8 ms 
[INFO ] 2024-04-01 10:45:55.699 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:45:55.699 - [任务 36(100)][9ad64398-9bef-4149-af7e-2c000821fbc8] - Node 9ad64398-9bef-4149-af7e-2c000821fbc8[9ad64398-9bef-4149-af7e-2c000821fbc8] running status set to false 
[INFO ] 2024-04-01 10:45:55.699 - [任务 36(100)][9ad64398-9bef-4149-af7e-2c000821fbc8] - Node 9ad64398-9bef-4149-af7e-2c000821fbc8[9ad64398-9bef-4149-af7e-2c000821fbc8] schema data cleaned 
[INFO ] 2024-04-01 10:45:55.699 - [任务 36(100)][9ad64398-9bef-4149-af7e-2c000821fbc8] - Node 9ad64398-9bef-4149-af7e-2c000821fbc8[9ad64398-9bef-4149-af7e-2c000821fbc8] monitor closed 
[INFO ] 2024-04-01 10:45:55.699 - [任务 36(100)][9ad64398-9bef-4149-af7e-2c000821fbc8] - Node 9ad64398-9bef-4149-af7e-2c000821fbc8[9ad64398-9bef-4149-af7e-2c000821fbc8] close complete, cost 0 ms 
[INFO ] 2024-04-01 10:45:55.701 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-a48d87c5-0bd4-4b48-a319-cf72cdcf7d54 
[INFO ] 2024-04-01 10:45:55.701 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-a48d87c5-0bd4-4b48-a319-cf72cdcf7d54 
[INFO ] 2024-04-01 10:45:55.701 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:45:55.702 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:45:55.702 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:45:55.702 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 5 ms 
[INFO ] 2024-04-01 10:45:55.703 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-9ad64398-9bef-4149-af7e-2c000821fbc8 complete, cost 666ms 
[INFO ] 2024-04-01 10:46:02.023 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:46:02.024 - [任务 36(100)][7b559ecd-12c4-4846-ad3a-5658342fa673] - Node 7b559ecd-12c4-4846-ad3a-5658342fa673[7b559ecd-12c4-4846-ad3a-5658342fa673] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:46:02.029 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:46:02.031 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 4 ms 
[INFO ] 2024-04-01 10:46:02.032 - [任务 36(100)][7b559ecd-12c4-4846-ad3a-5658342fa673] - Node 7b559ecd-12c4-4846-ad3a-5658342fa673[7b559ecd-12c4-4846-ad3a-5658342fa673] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:02.032 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:02.383 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:46:02.383 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:46:02.384 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:46:02.384 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:46:02.384 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:46:02.384 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 24 ms 
[INFO ] 2024-04-01 10:46:02.586 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:46:02.589 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-0a346b0e-7cc2-473b-ac90-123524248fdb 
[INFO ] 2024-04-01 10:46:02.589 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-0a346b0e-7cc2-473b-ac90-123524248fdb 
[INFO ] 2024-04-01 10:46:02.591 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:46:02.591 - [任务 36(100)][7b559ecd-12c4-4846-ad3a-5658342fa673] - Node 7b559ecd-12c4-4846-ad3a-5658342fa673[7b559ecd-12c4-4846-ad3a-5658342fa673] running status set to false 
[INFO ] 2024-04-01 10:46:02.591 - [任务 36(100)][7b559ecd-12c4-4846-ad3a-5658342fa673] - Node 7b559ecd-12c4-4846-ad3a-5658342fa673[7b559ecd-12c4-4846-ad3a-5658342fa673] schema data cleaned 
[INFO ] 2024-04-01 10:46:02.592 - [任务 36(100)][7b559ecd-12c4-4846-ad3a-5658342fa673] - Node 7b559ecd-12c4-4846-ad3a-5658342fa673[7b559ecd-12c4-4846-ad3a-5658342fa673] monitor closed 
[INFO ] 2024-04-01 10:46:02.592 - [任务 36(100)][7b559ecd-12c4-4846-ad3a-5658342fa673] - Node 7b559ecd-12c4-4846-ad3a-5658342fa673[7b559ecd-12c4-4846-ad3a-5658342fa673] close complete, cost 1 ms 
[INFO ] 2024-04-01 10:46:02.592 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:46:02.592 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:46:02.593 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 7 ms 
[INFO ] 2024-04-01 10:46:02.798 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-7b559ecd-12c4-4846-ad3a-5658342fa673 complete, cost 679ms 
[INFO ] 2024-04-01 10:46:03.483 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:46:03.483 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:46:03.483 - [任务 36(100)][d025525c-efbd-4f9a-8017-1338a069bfc1] - Node d025525c-efbd-4f9a-8017-1338a069bfc1[d025525c-efbd-4f9a-8017-1338a069bfc1] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:46:03.484 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:03.484 - [任务 36(100)][d025525c-efbd-4f9a-8017-1338a069bfc1] - Node d025525c-efbd-4f9a-8017-1338a069bfc1[d025525c-efbd-4f9a-8017-1338a069bfc1] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:03.484 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:03.728 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:46:03.736 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:46:03.736 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:46:03.737 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:46:03.737 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:46:03.943 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 13 ms 
[INFO ] 2024-04-01 10:46:03.967 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:46:03.968 - [任务 36(100)][d025525c-efbd-4f9a-8017-1338a069bfc1] - Node d025525c-efbd-4f9a-8017-1338a069bfc1[d025525c-efbd-4f9a-8017-1338a069bfc1] running status set to false 
[INFO ] 2024-04-01 10:46:03.968 - [任务 36(100)][d025525c-efbd-4f9a-8017-1338a069bfc1] - Node d025525c-efbd-4f9a-8017-1338a069bfc1[d025525c-efbd-4f9a-8017-1338a069bfc1] schema data cleaned 
[INFO ] 2024-04-01 10:46:03.968 - [任务 36(100)][d025525c-efbd-4f9a-8017-1338a069bfc1] - Node d025525c-efbd-4f9a-8017-1338a069bfc1[d025525c-efbd-4f9a-8017-1338a069bfc1] monitor closed 
[INFO ] 2024-04-01 10:46:03.968 - [任务 36(100)][d025525c-efbd-4f9a-8017-1338a069bfc1] - Node d025525c-efbd-4f9a-8017-1338a069bfc1[d025525c-efbd-4f9a-8017-1338a069bfc1] close complete, cost 2 ms 
[INFO ] 2024-04-01 10:46:03.972 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-32545dd3-de01-4a82-a887-5e391b3722d4 
[INFO ] 2024-04-01 10:46:03.973 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-32545dd3-de01-4a82-a887-5e391b3722d4 
[INFO ] 2024-04-01 10:46:03.973 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:46:03.975 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:46:03.975 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:46:03.976 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 11 ms 
[INFO ] 2024-04-01 10:46:03.976 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-d025525c-efbd-4f9a-8017-1338a069bfc1 complete, cost 522ms 
[INFO ] 2024-04-01 10:46:04.703 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:46:04.704 - [任务 36(100)][f2315478-7f9f-4bab-9012-9cb8d4f01932] - Node f2315478-7f9f-4bab-9012-9cb8d4f01932[f2315478-7f9f-4bab-9012-9cb8d4f01932] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:46:04.704 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:46:04.705 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:04.705 - [任务 36(100)][f2315478-7f9f-4bab-9012-9cb8d4f01932] - Node f2315478-7f9f-4bab-9012-9cb8d4f01932[f2315478-7f9f-4bab-9012-9cb8d4f01932] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:04.705 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:05.008 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:46:05.008 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:46:05.008 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:46:05.008 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:46:05.009 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:46:05.009 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 14 ms 
[INFO ] 2024-04-01 10:46:05.172 - [任务 36(100)][1890af1b-fbc6-489c-be0c-76e9beb35678] - Node 1890af1b-fbc6-489c-be0c-76e9beb35678[1890af1b-fbc6-489c-be0c-76e9beb35678] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:46:05.172 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:46:05.172 - [任务 36(100)][1890af1b-fbc6-489c-be0c-76e9beb35678] - Node 1890af1b-fbc6-489c-be0c-76e9beb35678[1890af1b-fbc6-489c-be0c-76e9beb35678] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:05.172 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:05.173 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:46:05.173 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:05.244 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:46:05.245 - [任务 36(100)][f2315478-7f9f-4bab-9012-9cb8d4f01932] - Node f2315478-7f9f-4bab-9012-9cb8d4f01932[f2315478-7f9f-4bab-9012-9cb8d4f01932] running status set to false 
[INFO ] 2024-04-01 10:46:05.245 - [任务 36(100)][f2315478-7f9f-4bab-9012-9cb8d4f01932] - Node f2315478-7f9f-4bab-9012-9cb8d4f01932[f2315478-7f9f-4bab-9012-9cb8d4f01932] schema data cleaned 
[INFO ] 2024-04-01 10:46:05.245 - [任务 36(100)][f2315478-7f9f-4bab-9012-9cb8d4f01932] - Node f2315478-7f9f-4bab-9012-9cb8d4f01932[f2315478-7f9f-4bab-9012-9cb8d4f01932] monitor closed 
[INFO ] 2024-04-01 10:46:05.250 - [任务 36(100)][f2315478-7f9f-4bab-9012-9cb8d4f01932] - Node f2315478-7f9f-4bab-9012-9cb8d4f01932[f2315478-7f9f-4bab-9012-9cb8d4f01932] close complete, cost 4 ms 
[INFO ] 2024-04-01 10:46:05.250 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-bc21e9d8-711f-4300-a8c8-0422e09c818c 
[INFO ] 2024-04-01 10:46:05.250 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-bc21e9d8-711f-4300-a8c8-0422e09c818c 
[INFO ] 2024-04-01 10:46:05.250 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:46:05.253 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:46:05.253 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:46:05.255 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 19 ms 
[INFO ] 2024-04-01 10:46:05.255 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-f2315478-7f9f-4bab-9012-9cb8d4f01932 complete, cost 614ms 
[INFO ] 2024-04-01 10:46:05.490 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:46:05.499 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:46:05.499 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:46:05.500 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:46:05.500 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:46:05.699 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 20 ms 
[INFO ] 2024-04-01 10:46:05.699 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:46:05.701 - [任务 36(100)][1890af1b-fbc6-489c-be0c-76e9beb35678] - Node 1890af1b-fbc6-489c-be0c-76e9beb35678[1890af1b-fbc6-489c-be0c-76e9beb35678] running status set to false 
[INFO ] 2024-04-01 10:46:05.702 - [任务 36(100)][1890af1b-fbc6-489c-be0c-76e9beb35678] - Node 1890af1b-fbc6-489c-be0c-76e9beb35678[1890af1b-fbc6-489c-be0c-76e9beb35678] schema data cleaned 
[INFO ] 2024-04-01 10:46:05.702 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-2825a96d-232f-41cb-b0fa-aca97130460b 
[INFO ] 2024-04-01 10:46:05.702 - [任务 36(100)][1890af1b-fbc6-489c-be0c-76e9beb35678] - Node 1890af1b-fbc6-489c-be0c-76e9beb35678[1890af1b-fbc6-489c-be0c-76e9beb35678] monitor closed 
[INFO ] 2024-04-01 10:46:05.702 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-2825a96d-232f-41cb-b0fa-aca97130460b 
[INFO ] 2024-04-01 10:46:05.702 - [任务 36(100)][1890af1b-fbc6-489c-be0c-76e9beb35678] - Node 1890af1b-fbc6-489c-be0c-76e9beb35678[1890af1b-fbc6-489c-be0c-76e9beb35678] close complete, cost 7 ms 
[INFO ] 2024-04-01 10:46:05.703 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:46:05.703 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:46:05.704 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:46:05.704 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 10 ms 
[INFO ] 2024-04-01 10:46:05.907 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-1890af1b-fbc6-489c-be0c-76e9beb35678 complete, cost 562ms 
[INFO ] 2024-04-01 10:46:07.151 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:46:07.152 - [任务 36(100)][5332e932-c366-4fad-918d-d9c9b813c4f4] - Node 5332e932-c366-4fad-918d-d9c9b813c4f4[5332e932-c366-4fad-918d-d9c9b813c4f4] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:46:07.152 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:07.152 - [任务 36(100)][5332e932-c366-4fad-918d-d9c9b813c4f4] - Node 5332e932-c366-4fad-918d-d9c9b813c4f4[5332e932-c366-4fad-918d-d9c9b813c4f4] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:07.152 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:46:07.152 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:07.265 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:46:07.265 - [任务 36(100)][048e4b4b-b876-40c4-a375-8fa80ab788c4] - Node 048e4b4b-b876-40c4-a375-8fa80ab788c4[048e4b4b-b876-40c4-a375-8fa80ab788c4] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:46:07.265 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:46:07.265 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:07.265 - [任务 36(100)][048e4b4b-b876-40c4-a375-8fa80ab788c4] - Node 048e4b4b-b876-40c4-a375-8fa80ab788c4[048e4b4b-b876-40c4-a375-8fa80ab788c4] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:07.265 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:07.446 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:46:07.446 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:46:07.446 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:46:07.446 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:46:07.446 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:46:07.654 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 13 ms 
[INFO ] 2024-04-01 10:46:07.925 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:46:07.926 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:46:07.926 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:46:07.926 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:46:07.926 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:46:07.926 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:46:07.928 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 57 ms 
[INFO ] 2024-04-01 10:46:07.928 - [任务 36(100)][048e4b4b-b876-40c4-a375-8fa80ab788c4] - Node 048e4b4b-b876-40c4-a375-8fa80ab788c4[048e4b4b-b876-40c4-a375-8fa80ab788c4] running status set to false 
[INFO ] 2024-04-01 10:46:07.929 - [任务 36(100)][048e4b4b-b876-40c4-a375-8fa80ab788c4] - Node 048e4b4b-b876-40c4-a375-8fa80ab788c4[048e4b4b-b876-40c4-a375-8fa80ab788c4] schema data cleaned 
[INFO ] 2024-04-01 10:46:07.929 - [任务 36(100)][048e4b4b-b876-40c4-a375-8fa80ab788c4] - Node 048e4b4b-b876-40c4-a375-8fa80ab788c4[048e4b4b-b876-40c4-a375-8fa80ab788c4] monitor closed 
[INFO ] 2024-04-01 10:46:07.929 - [任务 36(100)][048e4b4b-b876-40c4-a375-8fa80ab788c4] - Node 048e4b4b-b876-40c4-a375-8fa80ab788c4[048e4b4b-b876-40c4-a375-8fa80ab788c4] close complete, cost 1 ms 
[INFO ] 2024-04-01 10:46:07.933 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-24d64c55-b227-41a9-be01-a3923c38ea01 
[INFO ] 2024-04-01 10:46:07.933 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-24d64c55-b227-41a9-be01-a3923c38ea01 
[INFO ] 2024-04-01 10:46:07.933 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:46:07.936 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:46:07.936 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:46:07.938 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 13 ms 
[INFO ] 2024-04-01 10:46:07.938 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-048e4b4b-b876-40c4-a375-8fa80ab788c4 complete, cost 703ms 
[INFO ] 2024-04-01 10:46:08.119 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:46:08.119 - [任务 36(100)][5332e932-c366-4fad-918d-d9c9b813c4f4] - Node 5332e932-c366-4fad-918d-d9c9b813c4f4[5332e932-c366-4fad-918d-d9c9b813c4f4] running status set to false 
[INFO ] 2024-04-01 10:46:08.119 - [任务 36(100)][5332e932-c366-4fad-918d-d9c9b813c4f4] - Node 5332e932-c366-4fad-918d-d9c9b813c4f4[5332e932-c366-4fad-918d-d9c9b813c4f4] schema data cleaned 
[INFO ] 2024-04-01 10:46:08.119 - [任务 36(100)][5332e932-c366-4fad-918d-d9c9b813c4f4] - Node 5332e932-c366-4fad-918d-d9c9b813c4f4[5332e932-c366-4fad-918d-d9c9b813c4f4] monitor closed 
[INFO ] 2024-04-01 10:46:08.119 - [任务 36(100)][5332e932-c366-4fad-918d-d9c9b813c4f4] - Node 5332e932-c366-4fad-918d-d9c9b813c4f4[5332e932-c366-4fad-918d-d9c9b813c4f4] close complete, cost 0 ms 
[INFO ] 2024-04-01 10:46:08.120 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-554aaee0-a082-4e6c-9812-9a76d24e6331 
[INFO ] 2024-04-01 10:46:08.121 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-554aaee0-a082-4e6c-9812-9a76d24e6331 
[INFO ] 2024-04-01 10:46:08.121 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:46:08.121 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:46:08.121 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:46:08.122 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 3 ms 
[INFO ] 2024-04-01 10:46:08.122 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-5332e932-c366-4fad-918d-d9c9b813c4f4 complete, cost 1016ms 
[INFO ] 2024-04-01 10:46:09.066 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:46:09.066 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:46:09.066 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 10:46:09.066 - [任务 36(100)][21abd7a5-4f68-43b5-b713-3e520beab5a1] - Node 21abd7a5-4f68-43b5-b713-3e520beab5a1[21abd7a5-4f68-43b5-b713-3e520beab5a1] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:46:09.066 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:09.271 - [任务 36(100)][21abd7a5-4f68-43b5-b713-3e520beab5a1] - Node 21abd7a5-4f68-43b5-b713-3e520beab5a1[21abd7a5-4f68-43b5-b713-3e520beab5a1] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:46:09.319 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:46:09.319 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:46:09.319 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:46:09.319 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:46:09.319 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:46:09.526 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 8 ms 
[INFO ] 2024-04-01 10:46:09.536 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:46:09.536 - [任务 36(100)][21abd7a5-4f68-43b5-b713-3e520beab5a1] - Node 21abd7a5-4f68-43b5-b713-3e520beab5a1[21abd7a5-4f68-43b5-b713-3e520beab5a1] running status set to false 
[INFO ] 2024-04-01 10:46:09.536 - [任务 36(100)][21abd7a5-4f68-43b5-b713-3e520beab5a1] - Node 21abd7a5-4f68-43b5-b713-3e520beab5a1[21abd7a5-4f68-43b5-b713-3e520beab5a1] schema data cleaned 
[INFO ] 2024-04-01 10:46:09.536 - [任务 36(100)][21abd7a5-4f68-43b5-b713-3e520beab5a1] - Node 21abd7a5-4f68-43b5-b713-3e520beab5a1[21abd7a5-4f68-43b5-b713-3e520beab5a1] monitor closed 
[INFO ] 2024-04-01 10:46:09.538 - [任务 36(100)][21abd7a5-4f68-43b5-b713-3e520beab5a1] - Node 21abd7a5-4f68-43b5-b713-3e520beab5a1[21abd7a5-4f68-43b5-b713-3e520beab5a1] close complete, cost 0 ms 
[INFO ] 2024-04-01 10:46:09.538 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-d84f5984-86a2-4c45-baf3-819344ca22ae 
[INFO ] 2024-04-01 10:46:09.539 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-d84f5984-86a2-4c45-baf3-819344ca22ae 
[INFO ] 2024-04-01 10:46:09.539 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:46:09.542 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:46:09.542 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:46:09.543 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 10 ms 
[INFO ] 2024-04-01 10:46:09.543 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-21abd7a5-4f68-43b5-b713-3e520beab5a1 complete, cost 520ms 
[INFO ] 2024-04-01 10:47:39.771 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:47:39.773 - [任务 36(100)][88263734-b92d-4510-a3e4-9e3cd6bb0abf] - Node 88263734-b92d-4510-a3e4-9e3cd6bb0abf[88263734-b92d-4510-a3e4-9e3cd6bb0abf] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:47:39.773 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:47:39.773 - [任务 36(100)][88263734-b92d-4510-a3e4-9e3cd6bb0abf] - Node 88263734-b92d-4510-a3e4-9e3cd6bb0abf[88263734-b92d-4510-a3e4-9e3cd6bb0abf] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:47:39.776 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 3 ms 
[INFO ] 2024-04-01 10:47:39.776 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:47:40.099 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:47:40.099 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:47:40.099 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:47:40.099 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:47:40.100 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:47:40.101 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 25 ms 
[INFO ] 2024-04-01 10:47:40.294 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:47:40.294 - [任务 36(100)][88263734-b92d-4510-a3e4-9e3cd6bb0abf] - Node 88263734-b92d-4510-a3e4-9e3cd6bb0abf[88263734-b92d-4510-a3e4-9e3cd6bb0abf] running status set to false 
[INFO ] 2024-04-01 10:47:40.294 - [任务 36(100)][88263734-b92d-4510-a3e4-9e3cd6bb0abf] - Node 88263734-b92d-4510-a3e4-9e3cd6bb0abf[88263734-b92d-4510-a3e4-9e3cd6bb0abf] schema data cleaned 
[INFO ] 2024-04-01 10:47:40.294 - [任务 36(100)][88263734-b92d-4510-a3e4-9e3cd6bb0abf] - Node 88263734-b92d-4510-a3e4-9e3cd6bb0abf[88263734-b92d-4510-a3e4-9e3cd6bb0abf] monitor closed 
[INFO ] 2024-04-01 10:47:40.296 - [任务 36(100)][88263734-b92d-4510-a3e4-9e3cd6bb0abf] - Node 88263734-b92d-4510-a3e4-9e3cd6bb0abf[88263734-b92d-4510-a3e4-9e3cd6bb0abf] close complete, cost 0 ms 
[INFO ] 2024-04-01 10:47:40.296 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-c048b77d-72f3-4cd6-88f1-56c1db1af90c 
[INFO ] 2024-04-01 10:47:40.296 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-c048b77d-72f3-4cd6-88f1-56c1db1af90c 
[INFO ] 2024-04-01 10:47:40.296 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:47:40.298 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:47:40.298 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:47:40.300 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 7 ms 
[INFO ] 2024-04-01 10:47:40.300 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-88263734-b92d-4510-a3e4-9e3cd6bb0abf complete, cost 661ms 
[INFO ] 2024-04-01 10:47:51.987 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:47:51.987 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:47:51.987 - [任务 36(100)][1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9] - Node 1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9[1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:47:51.987 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:47:51.987 - [任务 36(100)][1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9] - Node 1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9[1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:47:52.189 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:47:52.245 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:47:52.245 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:47:52.246 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:47:52.246 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:47:52.246 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:47:52.246 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 11 ms 
[INFO ] 2024-04-01 10:47:52.465 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:47:52.467 - [任务 36(100)][1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9] - Node 1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9[1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9] running status set to false 
[INFO ] 2024-04-01 10:47:52.467 - [任务 36(100)][1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9] - Node 1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9[1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9] schema data cleaned 
[INFO ] 2024-04-01 10:47:52.468 - [任务 36(100)][1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9] - Node 1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9[1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9] monitor closed 
[INFO ] 2024-04-01 10:47:52.468 - [任务 36(100)][1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9] - Node 1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9[1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9] close complete, cost 3 ms 
[INFO ] 2024-04-01 10:47:52.472 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-46a24326-ef7d-46b9-b393-e71011571565 
[INFO ] 2024-04-01 10:47:52.473 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-46a24326-ef7d-46b9-b393-e71011571565 
[INFO ] 2024-04-01 10:47:52.473 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:47:52.474 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:47:52.474 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:47:52.474 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 13 ms 
[INFO ] 2024-04-01 10:47:55.665 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-1d2b19c1-c60f-46ad-a3a1-ac20179d6ad9 complete, cost 539ms 
[INFO ] 2024-04-01 10:48:09.309 - [任务 36(100)][c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e] - Node c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e[c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:48:09.310 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:48:09.310 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:48:09.313 - [任务 36(100)][c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e] - Node c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e[c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 10:48:09.318 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:48:09.318 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:48:09.601 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:48:09.601 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:48:09.601 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:48:09.601 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:48:09.601 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:48:09.601 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 20 ms 
[INFO ] 2024-04-01 10:48:09.801 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:48:09.801 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-fba8418d-4c4f-400e-ab05-4740bbce73ed 
[INFO ] 2024-04-01 10:48:09.801 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-fba8418d-4c4f-400e-ab05-4740bbce73ed 
[INFO ] 2024-04-01 10:48:09.801 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:48:09.802 - [任务 36(100)][c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e] - Node c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e[c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e] running status set to false 
[INFO ] 2024-04-01 10:48:09.802 - [任务 36(100)][c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e] - Node c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e[c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e] schema data cleaned 
[INFO ] 2024-04-01 10:48:09.802 - [任务 36(100)][c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e] - Node c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e[c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e] monitor closed 
[INFO ] 2024-04-01 10:48:09.803 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:48:09.803 - [任务 36(100)][c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e] - Node c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e[c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e] close complete, cost 0 ms 
[INFO ] 2024-04-01 10:48:09.803 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:48:09.803 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 5 ms 
[INFO ] 2024-04-01 10:48:09.804 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-c10f0a3f-7a0c-4273-82cc-9f1d0e959f7e complete, cost 566ms 
[INFO ] 2024-04-01 10:48:11.703 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:48:11.704 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 10:48:11.704 - [任务 36(100)][8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2] - Node 8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2[8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2] start preload schema,table counts: 0 
[INFO ] 2024-04-01 10:48:11.704 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:48:11.704 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:48:11.704 - [任务 36(100)][8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2] - Node 8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2[8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 10:48:12.022 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 10:48:12.022 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:48:12.022 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 10:48:12.022 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 10:48:12.023 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 10:48:12.024 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 26 ms 
[INFO ] 2024-04-01 10:48:12.213 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 10:48:12.213 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-77c91ee3-baa7-4c9a-8ace-45a480e404db 
[INFO ] 2024-04-01 10:48:12.213 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-77c91ee3-baa7-4c9a-8ace-45a480e404db 
[INFO ] 2024-04-01 10:48:12.213 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 10:48:12.213 - [任务 36(100)][8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2] - Node 8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2[8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2] running status set to false 
[INFO ] 2024-04-01 10:48:12.214 - [任务 36(100)][8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2] - Node 8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2[8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2] schema data cleaned 
[INFO ] 2024-04-01 10:48:12.214 - [任务 36(100)][8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2] - Node 8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2[8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2] monitor closed 
[INFO ] 2024-04-01 10:48:12.214 - [任务 36(100)][8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2] - Node 8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2[8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2] close complete, cost 0 ms 
[INFO ] 2024-04-01 10:48:12.215 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 10:48:12.215 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 10:48:12.215 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 6 ms 
[INFO ] 2024-04-01 10:48:12.416 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-8bb72ae4-192a-4d3d-83c6-a3a3ca4f13b2 complete, cost 570ms 
[INFO ] 2024-04-01 11:51:59.855 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 11:51:59.859 - [任务 36(100)][6c5f811c-5f25-4551-b109-33d085ee6274] - Node 6c5f811c-5f25-4551-b109-33d085ee6274[6c5f811c-5f25-4551-b109-33d085ee6274] start preload schema,table counts: 0 
[INFO ] 2024-04-01 11:51:59.860 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 11:51:59.860 - [任务 36(100)][6c5f811c-5f25-4551-b109-33d085ee6274] - Node 6c5f811c-5f25-4551-b109-33d085ee6274[6c5f811c-5f25-4551-b109-33d085ee6274] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 11:51:59.861 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 11:51:59.865 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 11:52:00.609 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 11:52:00.609 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 11:52:00.624 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 11:52:00.626 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 11:52:00.628 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 11:52:00.640 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 11:52:00.641 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 107 ms 
[INFO ] 2024-04-01 11:52:00.647 - [任务 36(100)][6c5f811c-5f25-4551-b109-33d085ee6274] - Node 6c5f811c-5f25-4551-b109-33d085ee6274[6c5f811c-5f25-4551-b109-33d085ee6274] running status set to false 
[INFO ] 2024-04-01 11:52:00.647 - [任务 36(100)][6c5f811c-5f25-4551-b109-33d085ee6274] - Node 6c5f811c-5f25-4551-b109-33d085ee6274[6c5f811c-5f25-4551-b109-33d085ee6274] schema data cleaned 
[INFO ] 2024-04-01 11:52:00.648 - [任务 36(100)][6c5f811c-5f25-4551-b109-33d085ee6274] - Node 6c5f811c-5f25-4551-b109-33d085ee6274[6c5f811c-5f25-4551-b109-33d085ee6274] monitor closed 
[INFO ] 2024-04-01 11:52:00.657 - [任务 36(100)][6c5f811c-5f25-4551-b109-33d085ee6274] - Node 6c5f811c-5f25-4551-b109-33d085ee6274[6c5f811c-5f25-4551-b109-33d085ee6274] close complete, cost 4 ms 
[INFO ] 2024-04-01 11:52:00.657 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-78708fb2-c3f9-4c8c-a63d-caa9be39d5e1 
[INFO ] 2024-04-01 11:52:00.657 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-78708fb2-c3f9-4c8c-a63d-caa9be39d5e1 
[INFO ] 2024-04-01 11:52:00.662 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 11:52:00.662 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 11:52:00.662 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 11:52:00.673 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 49 ms 
[INFO ] 2024-04-01 11:52:00.674 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-6c5f811c-5f25-4551-b109-33d085ee6274 complete, cost 985ms 
[INFO ] 2024-04-01 11:55:00.623 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 11:55:00.623 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 11:55:00.623 - [任务 36(100)][8f2e0a78-287b-4b5a-a28e-0dffabb5b79a] - Node 8f2e0a78-287b-4b5a-a28e-0dffabb5b79a[8f2e0a78-287b-4b5a-a28e-0dffabb5b79a] start preload schema,table counts: 0 
[INFO ] 2024-04-01 11:55:00.623 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 11:55:00.623 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 11:55:00.624 - [任务 36(100)][8f2e0a78-287b-4b5a-a28e-0dffabb5b79a] - Node 8f2e0a78-287b-4b5a-a28e-0dffabb5b79a[8f2e0a78-287b-4b5a-a28e-0dffabb5b79a] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 11:55:00.624 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 11:55:00.624 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 11:55:00.624 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 11:55:00.624 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 11:55:00.624 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 11:55:00.833 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 32 ms 
[INFO ] 2024-04-01 11:55:00.838 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 11:55:00.839 - [任务 36(100)][8f2e0a78-287b-4b5a-a28e-0dffabb5b79a] - Node 8f2e0a78-287b-4b5a-a28e-0dffabb5b79a[8f2e0a78-287b-4b5a-a28e-0dffabb5b79a] running status set to false 
[INFO ] 2024-04-01 11:55:00.839 - [任务 36(100)][8f2e0a78-287b-4b5a-a28e-0dffabb5b79a] - Node 8f2e0a78-287b-4b5a-a28e-0dffabb5b79a[8f2e0a78-287b-4b5a-a28e-0dffabb5b79a] schema data cleaned 
[INFO ] 2024-04-01 11:55:00.839 - [任务 36(100)][8f2e0a78-287b-4b5a-a28e-0dffabb5b79a] - Node 8f2e0a78-287b-4b5a-a28e-0dffabb5b79a[8f2e0a78-287b-4b5a-a28e-0dffabb5b79a] monitor closed 
[INFO ] 2024-04-01 11:55:00.839 - [任务 36(100)][8f2e0a78-287b-4b5a-a28e-0dffabb5b79a] - Node 8f2e0a78-287b-4b5a-a28e-0dffabb5b79a[8f2e0a78-287b-4b5a-a28e-0dffabb5b79a] close complete, cost 2 ms 
[INFO ] 2024-04-01 11:55:00.846 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-f90a62c7-3754-4374-874f-121c230e3298 
[INFO ] 2024-04-01 11:55:00.847 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-f90a62c7-3754-4374-874f-121c230e3298 
[INFO ] 2024-04-01 11:55:00.847 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 11:55:00.849 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 11:55:00.849 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 11:55:00.850 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 16 ms 
[INFO ] 2024-04-01 11:55:01.054 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-8f2e0a78-287b-4b5a-a28e-0dffabb5b79a complete, cost 851ms 
[INFO ] 2024-04-01 11:57:56.433 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 11:57:56.437 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 11:57:56.438 - [任务 36(100)][0c9c9142-6752-48e2-baf4-d8ff0a67ecae] - Node 0c9c9142-6752-48e2-baf4-d8ff0a67ecae[0c9c9142-6752-48e2-baf4-d8ff0a67ecae] start preload schema,table counts: 0 
[INFO ] 2024-04-01 11:57:56.438 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 6 ms 
[INFO ] 2024-04-01 11:57:56.439 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 11:57:56.442 - [任务 36(100)][0c9c9142-6752-48e2-baf4-d8ff0a67ecae] - Node 0c9c9142-6752-48e2-baf4-d8ff0a67ecae[0c9c9142-6752-48e2-baf4-d8ff0a67ecae] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 11:57:58.173 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 11:57:58.253 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 11:57:58.256 - [任务 36(100)][0c9c9142-6752-48e2-baf4-d8ff0a67ecae] - Node 0c9c9142-6752-48e2-baf4-d8ff0a67ecae[0c9c9142-6752-48e2-baf4-d8ff0a67ecae] running status set to false 
[INFO ] 2024-04-01 11:57:58.257 - [任务 36(100)][0c9c9142-6752-48e2-baf4-d8ff0a67ecae] - Node 0c9c9142-6752-48e2-baf4-d8ff0a67ecae[0c9c9142-6752-48e2-baf4-d8ff0a67ecae] schema data cleaned 
[INFO ] 2024-04-01 11:57:58.260 - [任务 36(100)][0c9c9142-6752-48e2-baf4-d8ff0a67ecae] - Node 0c9c9142-6752-48e2-baf4-d8ff0a67ecae[0c9c9142-6752-48e2-baf4-d8ff0a67ecae] monitor closed 
[INFO ] 2024-04-01 11:57:58.369 - [任务 36(100)][0c9c9142-6752-48e2-baf4-d8ff0a67ecae] - Node 0c9c9142-6752-48e2-baf4-d8ff0a67ecae[0c9c9142-6752-48e2-baf4-d8ff0a67ecae] close complete, cost 12 ms 
[INFO ] 2024-04-01 11:57:58.370 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-b6185af8-5fe0-4f98-8f97-7c14e95948b2 
[INFO ] 2024-04-01 11:57:58.372 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 11:57:58.379 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 11:57:58.380 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-b6185af8-5fe0-4f98-8f97-7c14e95948b2 
[INFO ] 2024-04-01 11:57:58.381 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 11:57:58.383 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 11:57:58.384 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 11:57:58.384 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 224 ms 
[INFO ] 2024-04-01 11:57:58.393 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 11:57:58.393 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 11:57:58.394 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 143 ms 
[INFO ] 2024-04-01 11:58:02.273 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-0c9c9142-6752-48e2-baf4-d8ff0a67ecae complete, cost 2619ms 
[INFO ] 2024-04-01 12:01:08.715 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:01:08.718 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:01:08.720 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:01:08.724 - [任务 36(100)][e74427b5-7978-48d6-ab83-c0da17de33b8] - Node e74427b5-7978-48d6-ab83-c0da17de33b8[e74427b5-7978-48d6-ab83-c0da17de33b8] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:01:08.725 - [任务 36(100)][b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c] - Node b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c[b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:01:08.727 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:01:08.727 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:01:08.727 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:01:08.731 - [任务 36(100)][e74427b5-7978-48d6-ab83-c0da17de33b8] - Node e74427b5-7978-48d6-ab83-c0da17de33b8[e74427b5-7978-48d6-ab83-c0da17de33b8] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:01:08.732 - [任务 36(100)][b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c] - Node b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c[b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:01:08.732 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:01:08.732 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 12:01:09.078 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:01:09.097 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:01:09.100 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:01:09.101 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:01:09.101 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:01:09.103 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 35 ms 
[INFO ] 2024-04-01 12:01:09.536 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:01:09.541 - [任务 36(100)][e74427b5-7978-48d6-ab83-c0da17de33b8] - Node e74427b5-7978-48d6-ab83-c0da17de33b8[e74427b5-7978-48d6-ab83-c0da17de33b8] running status set to false 
[INFO ] 2024-04-01 12:01:09.541 - [任务 36(100)][e74427b5-7978-48d6-ab83-c0da17de33b8] - Node e74427b5-7978-48d6-ab83-c0da17de33b8[e74427b5-7978-48d6-ab83-c0da17de33b8] schema data cleaned 
[INFO ] 2024-04-01 12:01:09.541 - [任务 36(100)][e74427b5-7978-48d6-ab83-c0da17de33b8] - Node e74427b5-7978-48d6-ab83-c0da17de33b8[e74427b5-7978-48d6-ab83-c0da17de33b8] monitor closed 
[INFO ] 2024-04-01 12:01:09.545 - [任务 36(100)][e74427b5-7978-48d6-ab83-c0da17de33b8] - Node e74427b5-7978-48d6-ab83-c0da17de33b8[e74427b5-7978-48d6-ab83-c0da17de33b8] close complete, cost 3 ms 
[INFO ] 2024-04-01 12:01:09.545 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-ad7e44ca-d89b-4bfe-b18e-d9254a5e37b8 
[INFO ] 2024-04-01 12:01:09.545 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-ad7e44ca-d89b-4bfe-b18e-d9254a5e37b8 
[INFO ] 2024-04-01 12:01:09.546 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:01:09.555 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:01:09.556 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:01:09.558 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 25 ms 
[INFO ] 2024-04-01 12:01:09.561 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-e74427b5-7978-48d6-ab83-c0da17de33b8 complete, cost 1071ms 
[INFO ] 2024-04-01 12:01:09.820 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:01:09.832 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:01:09.833 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:01:09.833 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:01:09.834 - [任务 36(100)][b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c] - Node b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c[b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c] running status set to false 
[INFO ] 2024-04-01 12:01:09.836 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:01:09.836 - [任务 36(100)][b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c] - Node b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c[b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c] schema data cleaned 
[INFO ] 2024-04-01 12:01:09.836 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:01:09.837 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-2528a51b-0198-432b-affa-d803591d283b 
[INFO ] 2024-04-01 12:01:09.838 - [任务 36(100)][b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c] - Node b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c[b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c] monitor closed 
[INFO ] 2024-04-01 12:01:09.838 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 65 ms 
[INFO ] 2024-04-01 12:01:09.838 - [任务 36(100)][b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c] - Node b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c[b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c] close complete, cost 7 ms 
[INFO ] 2024-04-01 12:01:09.840 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-2528a51b-0198-432b-affa-d803591d283b 
[INFO ] 2024-04-01 12:01:09.840 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:01:09.841 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:01:09.841 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:01:09.850 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 16 ms 
[INFO ] 2024-04-01 12:01:09.850 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-b37ea9f8-6d14-4161-a2ee-d5b21eef3f2c complete, cost 1354ms 
[INFO ] 2024-04-01 12:01:20.921 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:01:20.925 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:01:20.926 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 12:01:20.926 - [任务 36(100)][76ab0fbd-9536-4450-993b-e0e2fb65811a] - Node 76ab0fbd-9536-4450-993b-e0e2fb65811a[76ab0fbd-9536-4450-993b-e0e2fb65811a] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:01:20.926 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:01:20.928 - [任务 36(100)][76ab0fbd-9536-4450-993b-e0e2fb65811a] - Node 76ab0fbd-9536-4450-993b-e0e2fb65811a[76ab0fbd-9536-4450-993b-e0e2fb65811a] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:01:21.223 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:01:21.223 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:01:21.224 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:01:21.224 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:01:21.224 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:01:21.227 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 12 ms 
[INFO ] 2024-04-01 12:01:21.461 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:01:21.464 - [任务 36(100)][76ab0fbd-9536-4450-993b-e0e2fb65811a] - Node 76ab0fbd-9536-4450-993b-e0e2fb65811a[76ab0fbd-9536-4450-993b-e0e2fb65811a] running status set to false 
[INFO ] 2024-04-01 12:01:21.465 - [任务 36(100)][76ab0fbd-9536-4450-993b-e0e2fb65811a] - Node 76ab0fbd-9536-4450-993b-e0e2fb65811a[76ab0fbd-9536-4450-993b-e0e2fb65811a] schema data cleaned 
[INFO ] 2024-04-01 12:01:21.467 - [任务 36(100)][76ab0fbd-9536-4450-993b-e0e2fb65811a] - Node 76ab0fbd-9536-4450-993b-e0e2fb65811a[76ab0fbd-9536-4450-993b-e0e2fb65811a] monitor closed 
[INFO ] 2024-04-01 12:01:21.467 - [任务 36(100)][76ab0fbd-9536-4450-993b-e0e2fb65811a] - Node 76ab0fbd-9536-4450-993b-e0e2fb65811a[76ab0fbd-9536-4450-993b-e0e2fb65811a] close complete, cost 3 ms 
[INFO ] 2024-04-01 12:01:21.470 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-7b114213-33ed-4239-9a95-690a8e7b46a1 
[INFO ] 2024-04-01 12:01:21.470 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-7b114213-33ed-4239-9a95-690a8e7b46a1 
[INFO ] 2024-04-01 12:01:21.470 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:01:21.472 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:01:21.474 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:01:21.474 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 15 ms 
[INFO ] 2024-04-01 12:01:21.684 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-76ab0fbd-9536-4450-993b-e0e2fb65811a complete, cost 638ms 
[INFO ] 2024-04-01 12:01:31.846 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:01:31.848 - [任务 36(100)][a375a51e-cafb-46b4-8f10-96987844430e] - Node a375a51e-cafb-46b4-8f10-96987844430e[a375a51e-cafb-46b4-8f10-96987844430e] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:01:31.848 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:01:31.849 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:01:31.850 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:01:31.853 - [任务 36(100)][a375a51e-cafb-46b4-8f10-96987844430e] - Node a375a51e-cafb-46b4-8f10-96987844430e[a375a51e-cafb-46b4-8f10-96987844430e] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:01:32.114 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:01:32.114 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:01:32.114 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:01:32.115 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:01:32.115 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:01:32.115 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 11 ms 
[INFO ] 2024-04-01 12:01:32.355 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:01:32.355 - [任务 36(100)][a375a51e-cafb-46b4-8f10-96987844430e] - Node a375a51e-cafb-46b4-8f10-96987844430e[a375a51e-cafb-46b4-8f10-96987844430e] running status set to false 
[INFO ] 2024-04-01 12:01:32.356 - [任务 36(100)][a375a51e-cafb-46b4-8f10-96987844430e] - Node a375a51e-cafb-46b4-8f10-96987844430e[a375a51e-cafb-46b4-8f10-96987844430e] schema data cleaned 
[INFO ] 2024-04-01 12:01:32.356 - [任务 36(100)][a375a51e-cafb-46b4-8f10-96987844430e] - Node a375a51e-cafb-46b4-8f10-96987844430e[a375a51e-cafb-46b4-8f10-96987844430e] monitor closed 
[INFO ] 2024-04-01 12:01:32.358 - [任务 36(100)][a375a51e-cafb-46b4-8f10-96987844430e] - Node a375a51e-cafb-46b4-8f10-96987844430e[a375a51e-cafb-46b4-8f10-96987844430e] close complete, cost 2 ms 
[INFO ] 2024-04-01 12:01:32.358 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-250c73ba-ac3d-4ef1-aa2d-2baece3d30e0 
[INFO ] 2024-04-01 12:01:32.358 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-250c73ba-ac3d-4ef1-aa2d-2baece3d30e0 
[INFO ] 2024-04-01 12:01:32.359 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:01:32.362 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:01:32.363 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:01:32.366 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 15 ms 
[INFO ] 2024-04-01 12:01:32.366 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-a375a51e-cafb-46b4-8f10-96987844430e complete, cost 581ms 
[INFO ] 2024-04-01 12:01:37.241 - [任务 36(100)][10592e56-b076-4348-90d5-2ea735ec552a] - Node 10592e56-b076-4348-90d5-2ea735ec552a[10592e56-b076-4348-90d5-2ea735ec552a] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:01:37.241 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:01:37.243 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:01:37.244 - [任务 36(100)][10592e56-b076-4348-90d5-2ea735ec552a] - Node 10592e56-b076-4348-90d5-2ea735ec552a[10592e56-b076-4348-90d5-2ea735ec552a] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:01:37.244 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:01:37.244 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:01:37.527 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:01:37.534 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:01:37.534 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:01:37.535 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:01:37.535 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:01:37.741 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 9 ms 
[INFO ] 2024-04-01 12:01:37.767 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:01:37.767 - [任务 36(100)][10592e56-b076-4348-90d5-2ea735ec552a] - Node 10592e56-b076-4348-90d5-2ea735ec552a[10592e56-b076-4348-90d5-2ea735ec552a] running status set to false 
[INFO ] 2024-04-01 12:01:37.768 - [任务 36(100)][10592e56-b076-4348-90d5-2ea735ec552a] - Node 10592e56-b076-4348-90d5-2ea735ec552a[10592e56-b076-4348-90d5-2ea735ec552a] schema data cleaned 
[INFO ] 2024-04-01 12:01:37.768 - [任务 36(100)][10592e56-b076-4348-90d5-2ea735ec552a] - Node 10592e56-b076-4348-90d5-2ea735ec552a[10592e56-b076-4348-90d5-2ea735ec552a] monitor closed 
[INFO ] 2024-04-01 12:01:37.769 - [任务 36(100)][10592e56-b076-4348-90d5-2ea735ec552a] - Node 10592e56-b076-4348-90d5-2ea735ec552a[10592e56-b076-4348-90d5-2ea735ec552a] close complete, cost 2 ms 
[INFO ] 2024-04-01 12:01:37.769 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-a44839f8-056b-496c-8054-381368852401 
[INFO ] 2024-04-01 12:01:37.770 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-a44839f8-056b-496c-8054-381368852401 
[INFO ] 2024-04-01 12:01:37.770 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:01:37.772 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:01:37.772 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:01:37.774 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 13 ms 
[INFO ] 2024-04-01 12:01:37.775 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-10592e56-b076-4348-90d5-2ea735ec552a complete, cost 618ms 
[INFO ] 2024-04-01 12:03:17.992 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:03:17.993 - [任务 36(100)][da953930-3e31-45e8-a603-45f8c45bf6c6] - Node da953930-3e31-45e8-a603-45f8c45bf6c6[da953930-3e31-45e8-a603-45f8c45bf6c6] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:03:17.993 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:03:17.993 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 12:03:17.993 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:03:17.993 - [任务 36(100)][da953930-3e31-45e8-a603-45f8c45bf6c6] - Node da953930-3e31-45e8-a603-45f8c45bf6c6[da953930-3e31-45e8-a603-45f8c45bf6c6] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:03:18.306 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:03:18.306 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:03:18.306 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:03:18.306 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:03:18.307 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:03:18.308 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 16 ms 
[INFO ] 2024-04-01 12:03:18.521 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:03:18.524 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-294f551d-25f4-44d6-b095-4e9a1eaac954 
[INFO ] 2024-04-01 12:03:18.524 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-294f551d-25f4-44d6-b095-4e9a1eaac954 
[INFO ] 2024-04-01 12:03:18.524 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:03:18.525 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:03:18.525 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:03:18.527 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 4 ms 
[INFO ] 2024-04-01 12:03:18.527 - [任务 36(100)][da953930-3e31-45e8-a603-45f8c45bf6c6] - Node da953930-3e31-45e8-a603-45f8c45bf6c6[da953930-3e31-45e8-a603-45f8c45bf6c6] running status set to false 
[INFO ] 2024-04-01 12:03:18.528 - [任务 36(100)][da953930-3e31-45e8-a603-45f8c45bf6c6] - Node da953930-3e31-45e8-a603-45f8c45bf6c6[da953930-3e31-45e8-a603-45f8c45bf6c6] schema data cleaned 
[INFO ] 2024-04-01 12:03:18.528 - [任务 36(100)][da953930-3e31-45e8-a603-45f8c45bf6c6] - Node da953930-3e31-45e8-a603-45f8c45bf6c6[da953930-3e31-45e8-a603-45f8c45bf6c6] monitor closed 
[INFO ] 2024-04-01 12:03:18.530 - [任务 36(100)][da953930-3e31-45e8-a603-45f8c45bf6c6] - Node da953930-3e31-45e8-a603-45f8c45bf6c6[da953930-3e31-45e8-a603-45f8c45bf6c6] close complete, cost 0 ms 
[INFO ] 2024-04-01 12:03:18.530 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-da953930-3e31-45e8-a603-45f8c45bf6c6 complete, cost 658ms 
[INFO ] 2024-04-01 12:03:21.894 - [任务 36(100)][763cefbe-7e50-4c5c-a863-1e3800176aa1] - Node 763cefbe-7e50-4c5c-a863-1e3800176aa1[763cefbe-7e50-4c5c-a863-1e3800176aa1] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:03:21.895 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:03:21.895 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:03:21.895 - [任务 36(100)][763cefbe-7e50-4c5c-a863-1e3800176aa1] - Node 763cefbe-7e50-4c5c-a863-1e3800176aa1[763cefbe-7e50-4c5c-a863-1e3800176aa1] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:03:21.895 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 2 ms 
[INFO ] 2024-04-01 12:03:21.895 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 2 ms 
[INFO ] 2024-04-01 12:03:22.183 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:03:22.193 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:03:22.193 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:03:22.193 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:03:22.193 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:03:22.397 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 21 ms 
[INFO ] 2024-04-01 12:03:22.397 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:03:22.404 - [任务 36(100)][763cefbe-7e50-4c5c-a863-1e3800176aa1] - Node 763cefbe-7e50-4c5c-a863-1e3800176aa1[763cefbe-7e50-4c5c-a863-1e3800176aa1] running status set to false 
[INFO ] 2024-04-01 12:03:22.404 - [任务 36(100)][763cefbe-7e50-4c5c-a863-1e3800176aa1] - Node 763cefbe-7e50-4c5c-a863-1e3800176aa1[763cefbe-7e50-4c5c-a863-1e3800176aa1] schema data cleaned 
[INFO ] 2024-04-01 12:03:22.404 - [任务 36(100)][763cefbe-7e50-4c5c-a863-1e3800176aa1] - Node 763cefbe-7e50-4c5c-a863-1e3800176aa1[763cefbe-7e50-4c5c-a863-1e3800176aa1] monitor closed 
[INFO ] 2024-04-01 12:03:22.404 - [任务 36(100)][763cefbe-7e50-4c5c-a863-1e3800176aa1] - Node 763cefbe-7e50-4c5c-a863-1e3800176aa1[763cefbe-7e50-4c5c-a863-1e3800176aa1] close complete, cost 1 ms 
[INFO ] 2024-04-01 12:03:22.404 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-f4366b8d-f63f-4172-b789-d84e0e2e01a5 
[INFO ] 2024-04-01 12:03:22.404 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-f4366b8d-f63f-4172-b789-d84e0e2e01a5 
[INFO ] 2024-04-01 12:03:22.404 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:03:22.405 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:03:22.405 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:03:22.406 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 9 ms 
[INFO ] 2024-04-01 12:03:22.406 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-763cefbe-7e50-4c5c-a863-1e3800176aa1 complete, cost 617ms 
[INFO ] 2024-04-01 12:03:22.846 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:03:22.846 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:03:22.847 - [任务 36(100)][04ed80bc-d311-4668-a28b-92b15c25671a] - Node 04ed80bc-d311-4668-a28b-92b15c25671a[04ed80bc-d311-4668-a28b-92b15c25671a] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:03:22.847 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:03:22.847 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:03:23.052 - [任务 36(100)][04ed80bc-d311-4668-a28b-92b15c25671a] - Node 04ed80bc-d311-4668-a28b-92b15c25671a[04ed80bc-d311-4668-a28b-92b15c25671a] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:03:23.093 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:03:23.093 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:03:23.094 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:03:23.094 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:03:23.094 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:03:23.295 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 11 ms 
[INFO ] 2024-04-01 12:03:23.330 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:03:23.330 - [任务 36(100)][04ed80bc-d311-4668-a28b-92b15c25671a] - Node 04ed80bc-d311-4668-a28b-92b15c25671a[04ed80bc-d311-4668-a28b-92b15c25671a] running status set to false 
[INFO ] 2024-04-01 12:03:23.330 - [任务 36(100)][04ed80bc-d311-4668-a28b-92b15c25671a] - Node 04ed80bc-d311-4668-a28b-92b15c25671a[04ed80bc-d311-4668-a28b-92b15c25671a] schema data cleaned 
[INFO ] 2024-04-01 12:03:23.330 - [任务 36(100)][04ed80bc-d311-4668-a28b-92b15c25671a] - Node 04ed80bc-d311-4668-a28b-92b15c25671a[04ed80bc-d311-4668-a28b-92b15c25671a] monitor closed 
[INFO ] 2024-04-01 12:03:23.330 - [任务 36(100)][04ed80bc-d311-4668-a28b-92b15c25671a] - Node 04ed80bc-d311-4668-a28b-92b15c25671a[04ed80bc-d311-4668-a28b-92b15c25671a] close complete, cost 0 ms 
[INFO ] 2024-04-01 12:03:23.336 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-c8c1bf31-64bb-49ff-8a09-8c0778e93c30 
[INFO ] 2024-04-01 12:03:23.337 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-c8c1bf31-64bb-49ff-8a09-8c0778e93c30 
[INFO ] 2024-04-01 12:03:23.337 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:03:23.338 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:03:23.339 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:03:23.339 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 11 ms 
[INFO ] 2024-04-01 12:03:23.547 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-04ed80bc-d311-4668-a28b-92b15c25671a complete, cost 531ms 
[INFO ] 2024-04-01 12:03:23.660 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:03:23.662 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:03:23.663 - [任务 36(100)][b575ea60-8f20-4138-926b-89c2aacaabf3] - Node b575ea60-8f20-4138-926b-89c2aacaabf3[b575ea60-8f20-4138-926b-89c2aacaabf3] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:03:23.663 - [任务 36(100)][b575ea60-8f20-4138-926b-89c2aacaabf3] - Node b575ea60-8f20-4138-926b-89c2aacaabf3[b575ea60-8f20-4138-926b-89c2aacaabf3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 12:03:23.663 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 2 ms 
[INFO ] 2024-04-01 12:03:23.864 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 12:03:23.950 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:03:23.958 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:03:23.958 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:03:23.958 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:03:23.958 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:03:24.164 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 12 ms 
[INFO ] 2024-04-01 12:03:24.174 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:03:24.175 - [任务 36(100)][b575ea60-8f20-4138-926b-89c2aacaabf3] - Node b575ea60-8f20-4138-926b-89c2aacaabf3[b575ea60-8f20-4138-926b-89c2aacaabf3] running status set to false 
[INFO ] 2024-04-01 12:03:24.175 - [任务 36(100)][b575ea60-8f20-4138-926b-89c2aacaabf3] - Node b575ea60-8f20-4138-926b-89c2aacaabf3[b575ea60-8f20-4138-926b-89c2aacaabf3] schema data cleaned 
[INFO ] 2024-04-01 12:03:24.175 - [任务 36(100)][b575ea60-8f20-4138-926b-89c2aacaabf3] - Node b575ea60-8f20-4138-926b-89c2aacaabf3[b575ea60-8f20-4138-926b-89c2aacaabf3] monitor closed 
[INFO ] 2024-04-01 12:03:24.175 - [任务 36(100)][b575ea60-8f20-4138-926b-89c2aacaabf3] - Node b575ea60-8f20-4138-926b-89c2aacaabf3[b575ea60-8f20-4138-926b-89c2aacaabf3] close complete, cost 1 ms 
[INFO ] 2024-04-01 12:03:24.177 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-de9522d8-e7fd-4619-9b59-26fae9227614 
[INFO ] 2024-04-01 12:03:24.177 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-de9522d8-e7fd-4619-9b59-26fae9227614 
[INFO ] 2024-04-01 12:03:24.177 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:03:24.178 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:03:24.178 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:03:24.179 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 7 ms 
[INFO ] 2024-04-01 12:03:24.179 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-b575ea60-8f20-4138-926b-89c2aacaabf3 complete, cost 591ms 
[INFO ] 2024-04-01 12:03:26.469 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:03:26.470 - [任务 36(100)][0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b] - Node 0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b[0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:03:26.470 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:03:26.471 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:03:26.471 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:03:26.471 - [任务 36(100)][0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b] - Node 0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b[0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:03:26.775 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:03:26.777 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:03:26.777 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:03:26.777 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:03:26.777 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:03:26.980 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 19 ms 
[INFO ] 2024-04-01 12:03:26.985 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:03:26.985 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-7b991ae8-7c1a-4e79-b49e-a0e183fb3400 
[INFO ] 2024-04-01 12:03:26.985 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-7b991ae8-7c1a-4e79-b49e-a0e183fb3400 
[INFO ] 2024-04-01 12:03:26.985 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:03:26.988 - [任务 36(100)][0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b] - Node 0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b[0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b] running status set to false 
[INFO ] 2024-04-01 12:03:26.988 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:03:26.988 - [任务 36(100)][0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b] - Node 0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b[0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b] schema data cleaned 
[INFO ] 2024-04-01 12:03:26.989 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:03:26.989 - [任务 36(100)][0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b] - Node 0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b[0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b] monitor closed 
[INFO ] 2024-04-01 12:03:26.991 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 8 ms 
[INFO ] 2024-04-01 12:03:26.991 - [任务 36(100)][0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b] - Node 0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b[0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b] close complete, cost 1 ms 
[INFO ] 2024-04-01 12:03:27.192 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-0f381ae1-c7b1-4886-ac3c-ee3ba4bf551b complete, cost 615ms 
[INFO ] 2024-04-01 12:03:27.766 - [任务 36(100)][552a252c-12b7-4dab-bfaa-ee64dc23bd7c] - Node 552a252c-12b7-4dab-bfaa-ee64dc23bd7c[552a252c-12b7-4dab-bfaa-ee64dc23bd7c] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:03:27.766 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:03:27.766 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:03:27.768 - [任务 36(100)][552a252c-12b7-4dab-bfaa-ee64dc23bd7c] - Node 552a252c-12b7-4dab-bfaa-ee64dc23bd7c[552a252c-12b7-4dab-bfaa-ee64dc23bd7c] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:03:27.769 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:03:27.974 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:03:28.013 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:03:28.014 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:03:28.014 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:03:28.014 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:03:28.015 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:03:28.015 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 16 ms 
[INFO ] 2024-04-01 12:03:28.255 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:03:28.256 - [任务 36(100)][552a252c-12b7-4dab-bfaa-ee64dc23bd7c] - Node 552a252c-12b7-4dab-bfaa-ee64dc23bd7c[552a252c-12b7-4dab-bfaa-ee64dc23bd7c] running status set to false 
[INFO ] 2024-04-01 12:03:28.256 - [任务 36(100)][552a252c-12b7-4dab-bfaa-ee64dc23bd7c] - Node 552a252c-12b7-4dab-bfaa-ee64dc23bd7c[552a252c-12b7-4dab-bfaa-ee64dc23bd7c] schema data cleaned 
[INFO ] 2024-04-01 12:03:28.256 - [任务 36(100)][552a252c-12b7-4dab-bfaa-ee64dc23bd7c] - Node 552a252c-12b7-4dab-bfaa-ee64dc23bd7c[552a252c-12b7-4dab-bfaa-ee64dc23bd7c] monitor closed 
[INFO ] 2024-04-01 12:03:28.256 - [任务 36(100)][552a252c-12b7-4dab-bfaa-ee64dc23bd7c] - Node 552a252c-12b7-4dab-bfaa-ee64dc23bd7c[552a252c-12b7-4dab-bfaa-ee64dc23bd7c] close complete, cost 2 ms 
[INFO ] 2024-04-01 12:03:28.264 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-5fa02c41-bc33-4e07-acc0-ddc9cde03759 
[INFO ] 2024-04-01 12:03:28.264 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-5fa02c41-bc33-4e07-acc0-ddc9cde03759 
[INFO ] 2024-04-01 12:03:28.268 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:03:28.269 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:03:28.269 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:03:28.269 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 16 ms 
[INFO ] 2024-04-01 12:03:28.478 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-552a252c-12b7-4dab-bfaa-ee64dc23bd7c complete, cost 534ms 
[INFO ] 2024-04-01 12:03:38.754 - [任务 36(100)][cb80a0ef-e220-4d86-88f7-21814e26fac4] - Node cb80a0ef-e220-4d86-88f7-21814e26fac4[cb80a0ef-e220-4d86-88f7-21814e26fac4] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:03:38.755 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:03:38.755 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:03:38.756 - [任务 36(100)][cb80a0ef-e220-4d86-88f7-21814e26fac4] - Node cb80a0ef-e220-4d86-88f7-21814e26fac4[cb80a0ef-e220-4d86-88f7-21814e26fac4] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:03:38.757 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 12:03:38.757 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 2 ms 
[INFO ] 2024-04-01 12:03:39.062 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:03:39.073 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:03:39.073 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:03:39.073 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:03:39.074 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:03:39.275 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 27 ms 
[INFO ] 2024-04-01 12:03:39.276 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:03:39.277 - [任务 36(100)][cb80a0ef-e220-4d86-88f7-21814e26fac4] - Node cb80a0ef-e220-4d86-88f7-21814e26fac4[cb80a0ef-e220-4d86-88f7-21814e26fac4] running status set to false 
[INFO ] 2024-04-01 12:03:39.277 - [任务 36(100)][cb80a0ef-e220-4d86-88f7-21814e26fac4] - Node cb80a0ef-e220-4d86-88f7-21814e26fac4[cb80a0ef-e220-4d86-88f7-21814e26fac4] schema data cleaned 
[INFO ] 2024-04-01 12:03:39.277 - [任务 36(100)][cb80a0ef-e220-4d86-88f7-21814e26fac4] - Node cb80a0ef-e220-4d86-88f7-21814e26fac4[cb80a0ef-e220-4d86-88f7-21814e26fac4] monitor closed 
[INFO ] 2024-04-01 12:03:39.277 - [任务 36(100)][cb80a0ef-e220-4d86-88f7-21814e26fac4] - Node cb80a0ef-e220-4d86-88f7-21814e26fac4[cb80a0ef-e220-4d86-88f7-21814e26fac4] close complete, cost 0 ms 
[INFO ] 2024-04-01 12:03:39.279 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-ea27b88b-3b84-4bff-b3dd-fc5d6c686104 
[INFO ] 2024-04-01 12:03:39.279 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-ea27b88b-3b84-4bff-b3dd-fc5d6c686104 
[INFO ] 2024-04-01 12:03:39.280 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:03:39.280 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:03:39.280 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:03:39.280 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 7 ms 
[INFO ] 2024-04-01 12:03:39.482 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-cb80a0ef-e220-4d86-88f7-21814e26fac4 complete, cost 627ms 
[INFO ] 2024-04-01 12:04:01.527 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:04:01.529 - [任务 36(100)][f21fed6a-9aa8-45d3-97d5-f925862d960e] - Node f21fed6a-9aa8-45d3-97d5-f925862d960e[f21fed6a-9aa8-45d3-97d5-f925862d960e] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:04:01.529 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:04:01.535 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:04:01.535 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 12:04:01.535 - [任务 36(100)][f21fed6a-9aa8-45d3-97d5-f925862d960e] - Node f21fed6a-9aa8-45d3-97d5-f925862d960e[f21fed6a-9aa8-45d3-97d5-f925862d960e] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:04:02.177 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:04:02.195 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:04:02.195 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:04:02.195 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:04:02.195 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:04:02.195 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 35 ms 
[INFO ] 2024-04-01 12:04:02.199 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:04:02.202 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-1e118df4-e4b7-4a29-aa37-9e7b15821041 
[INFO ] 2024-04-01 12:04:02.202 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-1e118df4-e4b7-4a29-aa37-9e7b15821041 
[INFO ] 2024-04-01 12:04:02.202 - [任务 36(100)][f21fed6a-9aa8-45d3-97d5-f925862d960e] - Node f21fed6a-9aa8-45d3-97d5-f925862d960e[f21fed6a-9aa8-45d3-97d5-f925862d960e] running status set to false 
[INFO ] 2024-04-01 12:04:02.202 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:04:02.203 - [任务 36(100)][f21fed6a-9aa8-45d3-97d5-f925862d960e] - Node f21fed6a-9aa8-45d3-97d5-f925862d960e[f21fed6a-9aa8-45d3-97d5-f925862d960e] schema data cleaned 
[INFO ] 2024-04-01 12:04:02.203 - [任务 36(100)][f21fed6a-9aa8-45d3-97d5-f925862d960e] - Node f21fed6a-9aa8-45d3-97d5-f925862d960e[f21fed6a-9aa8-45d3-97d5-f925862d960e] monitor closed 
[INFO ] 2024-04-01 12:04:02.203 - [任务 36(100)][f21fed6a-9aa8-45d3-97d5-f925862d960e] - Node f21fed6a-9aa8-45d3-97d5-f925862d960e[f21fed6a-9aa8-45d3-97d5-f925862d960e] close complete, cost 0 ms 
[INFO ] 2024-04-01 12:04:02.203 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:04:02.204 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:04:02.204 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 4 ms 
[INFO ] 2024-04-01 12:04:02.409 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-f21fed6a-9aa8-45d3-97d5-f925862d960e complete, cost 785ms 
[INFO ] 2024-04-01 12:06:50.961 - [任务 36(100)][bef7b331-d957-454a-9e56-158d8279c478] - Node bef7b331-d957-454a-9e56-158d8279c478[bef7b331-d957-454a-9e56-158d8279c478] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:06:50.961 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:06:50.961 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:06:50.961 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:06:50.961 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:06:50.961 - [任务 36(100)][bef7b331-d957-454a-9e56-158d8279c478] - Node bef7b331-d957-454a-9e56-158d8279c478[bef7b331-d957-454a-9e56-158d8279c478] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:06:51.358 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:06:51.359 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:06:51.359 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:06:51.359 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:06:51.359 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:06:51.567 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 10 ms 
[INFO ] 2024-04-01 12:06:51.636 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:06:51.637 - [任务 36(100)][bef7b331-d957-454a-9e56-158d8279c478] - Node bef7b331-d957-454a-9e56-158d8279c478[bef7b331-d957-454a-9e56-158d8279c478] running status set to false 
[INFO ] 2024-04-01 12:06:51.637 - [任务 36(100)][bef7b331-d957-454a-9e56-158d8279c478] - Node bef7b331-d957-454a-9e56-158d8279c478[bef7b331-d957-454a-9e56-158d8279c478] schema data cleaned 
[INFO ] 2024-04-01 12:06:51.637 - [任务 36(100)][bef7b331-d957-454a-9e56-158d8279c478] - Node bef7b331-d957-454a-9e56-158d8279c478[bef7b331-d957-454a-9e56-158d8279c478] monitor closed 
[INFO ] 2024-04-01 12:06:51.644 - [任务 36(100)][bef7b331-d957-454a-9e56-158d8279c478] - Node bef7b331-d957-454a-9e56-158d8279c478[bef7b331-d957-454a-9e56-158d8279c478] close complete, cost 0 ms 
[INFO ] 2024-04-01 12:06:51.644 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-7c718cf5-6057-404c-a9d6-69b4c1e52121 
[INFO ] 2024-04-01 12:06:51.644 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-7c718cf5-6057-404c-a9d6-69b4c1e52121 
[INFO ] 2024-04-01 12:06:51.644 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:06:51.645 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:06:51.646 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:06:51.646 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 18 ms 
[INFO ] 2024-04-01 12:06:51.853 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-bef7b331-d957-454a-9e56-158d8279c478 complete, cost 818ms 
[INFO ] 2024-04-01 12:07:05.732 - [任务 36(100)][2a9e7bb2-b5ec-4962-854b-4754a0d6828e] - Node 2a9e7bb2-b5ec-4962-854b-4754a0d6828e[2a9e7bb2-b5ec-4962-854b-4754a0d6828e] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:07:05.733 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:07:05.733 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:07:05.733 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:07:05.733 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:07:05.734 - [任务 36(100)][2a9e7bb2-b5ec-4962-854b-4754a0d6828e] - Node 2a9e7bb2-b5ec-4962-854b-4754a0d6828e[2a9e7bb2-b5ec-4962-854b-4754a0d6828e] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 12:07:06.398 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:07:06.398 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:07:06.398 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:07:06.398 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:07:06.399 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:07:06.416 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 43 ms 
[INFO ] 2024-04-01 12:07:06.416 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:07:06.418 - [任务 36(100)][2a9e7bb2-b5ec-4962-854b-4754a0d6828e] - Node 2a9e7bb2-b5ec-4962-854b-4754a0d6828e[2a9e7bb2-b5ec-4962-854b-4754a0d6828e] running status set to false 
[INFO ] 2024-04-01 12:07:06.418 - [任务 36(100)][2a9e7bb2-b5ec-4962-854b-4754a0d6828e] - Node 2a9e7bb2-b5ec-4962-854b-4754a0d6828e[2a9e7bb2-b5ec-4962-854b-4754a0d6828e] schema data cleaned 
[INFO ] 2024-04-01 12:07:06.418 - [任务 36(100)][2a9e7bb2-b5ec-4962-854b-4754a0d6828e] - Node 2a9e7bb2-b5ec-4962-854b-4754a0d6828e[2a9e7bb2-b5ec-4962-854b-4754a0d6828e] monitor closed 
[INFO ] 2024-04-01 12:07:06.420 - [任务 36(100)][2a9e7bb2-b5ec-4962-854b-4754a0d6828e] - Node 2a9e7bb2-b5ec-4962-854b-4754a0d6828e[2a9e7bb2-b5ec-4962-854b-4754a0d6828e] close complete, cost 0 ms 
[INFO ] 2024-04-01 12:07:06.420 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-ce10d05c-b21a-47ba-b7dd-2dbb9bb8db36 
[INFO ] 2024-04-01 12:07:06.420 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-ce10d05c-b21a-47ba-b7dd-2dbb9bb8db36 
[INFO ] 2024-04-01 12:07:06.420 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:07:06.421 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:07:06.421 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:07:06.423 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 7 ms 
[INFO ] 2024-04-01 12:07:06.423 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-2a9e7bb2-b5ec-4962-854b-4754a0d6828e complete, cost 758ms 
[INFO ] 2024-04-01 12:07:07.609 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:07:07.610 - [任务 36(100)][32fef0a8-d5a0-4002-9dec-fcdd71c9c063] - Node 32fef0a8-d5a0-4002-9dec-fcdd71c9c063[32fef0a8-d5a0-4002-9dec-fcdd71c9c063] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:07:07.610 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:07:07.610 - [任务 36(100)][32fef0a8-d5a0-4002-9dec-fcdd71c9c063] - Node 32fef0a8-d5a0-4002-9dec-fcdd71c9c063[32fef0a8-d5a0-4002-9dec-fcdd71c9c063] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:07:07.610 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:07:07.610 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:07:07.917 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:07:07.924 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:07:07.924 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:07:07.924 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:07:07.924 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:07:08.126 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 27 ms 
[INFO ] 2024-04-01 12:07:08.129 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:07:08.129 - [任务 36(100)][32fef0a8-d5a0-4002-9dec-fcdd71c9c063] - Node 32fef0a8-d5a0-4002-9dec-fcdd71c9c063[32fef0a8-d5a0-4002-9dec-fcdd71c9c063] running status set to false 
[INFO ] 2024-04-01 12:07:08.129 - [任务 36(100)][32fef0a8-d5a0-4002-9dec-fcdd71c9c063] - Node 32fef0a8-d5a0-4002-9dec-fcdd71c9c063[32fef0a8-d5a0-4002-9dec-fcdd71c9c063] schema data cleaned 
[INFO ] 2024-04-01 12:07:08.129 - [任务 36(100)][32fef0a8-d5a0-4002-9dec-fcdd71c9c063] - Node 32fef0a8-d5a0-4002-9dec-fcdd71c9c063[32fef0a8-d5a0-4002-9dec-fcdd71c9c063] monitor closed 
[INFO ] 2024-04-01 12:07:08.129 - [任务 36(100)][32fef0a8-d5a0-4002-9dec-fcdd71c9c063] - Node 32fef0a8-d5a0-4002-9dec-fcdd71c9c063[32fef0a8-d5a0-4002-9dec-fcdd71c9c063] close complete, cost 0 ms 
[INFO ] 2024-04-01 12:07:08.131 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-855ec934-19d6-443c-bfc8-ed27b7677a91 
[INFO ] 2024-04-01 12:07:08.131 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-855ec934-19d6-443c-bfc8-ed27b7677a91 
[INFO ] 2024-04-01 12:07:08.131 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:07:08.132 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:07:08.132 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:07:08.134 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 6 ms 
[INFO ] 2024-04-01 12:07:08.134 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-32fef0a8-d5a0-4002-9dec-fcdd71c9c063 complete, cost 574ms 
[INFO ] 2024-04-01 12:07:09.387 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:07:09.387 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:07:09.387 - [任务 36(100)][4f4dbffe-d14c-48f3-95dc-89cd88d68e9c] - Node 4f4dbffe-d14c-48f3-95dc-89cd88d68e9c[4f4dbffe-d14c-48f3-95dc-89cd88d68e9c] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:07:09.387 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:07:09.387 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:07:09.593 - [任务 36(100)][4f4dbffe-d14c-48f3-95dc-89cd88d68e9c] - Node 4f4dbffe-d14c-48f3-95dc-89cd88d68e9c[4f4dbffe-d14c-48f3-95dc-89cd88d68e9c] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:07:09.692 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:07:09.693 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:07:09.693 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:07:09.693 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:07:09.693 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:07:09.693 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 17 ms 
[INFO ] 2024-04-01 12:07:09.928 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:07:09.931 - [任务 36(100)][4f4dbffe-d14c-48f3-95dc-89cd88d68e9c] - Node 4f4dbffe-d14c-48f3-95dc-89cd88d68e9c[4f4dbffe-d14c-48f3-95dc-89cd88d68e9c] running status set to false 
[INFO ] 2024-04-01 12:07:09.931 - [任务 36(100)][4f4dbffe-d14c-48f3-95dc-89cd88d68e9c] - Node 4f4dbffe-d14c-48f3-95dc-89cd88d68e9c[4f4dbffe-d14c-48f3-95dc-89cd88d68e9c] schema data cleaned 
[INFO ] 2024-04-01 12:07:09.931 - [任务 36(100)][4f4dbffe-d14c-48f3-95dc-89cd88d68e9c] - Node 4f4dbffe-d14c-48f3-95dc-89cd88d68e9c[4f4dbffe-d14c-48f3-95dc-89cd88d68e9c] monitor closed 
[INFO ] 2024-04-01 12:07:09.931 - [任务 36(100)][4f4dbffe-d14c-48f3-95dc-89cd88d68e9c] - Node 4f4dbffe-d14c-48f3-95dc-89cd88d68e9c[4f4dbffe-d14c-48f3-95dc-89cd88d68e9c] close complete, cost 2 ms 
[INFO ] 2024-04-01 12:07:09.934 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-3e74d0ac-5b43-4d0f-801f-0939b093503f 
[INFO ] 2024-04-01 12:07:09.934 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-3e74d0ac-5b43-4d0f-801f-0939b093503f 
[INFO ] 2024-04-01 12:07:09.935 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:07:09.937 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:07:09.937 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:07:09.937 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 17 ms 
[INFO ] 2024-04-01 12:07:10.142 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-4f4dbffe-d14c-48f3-95dc-89cd88d68e9c complete, cost 586ms 
[INFO ] 2024-04-01 12:07:11.908 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:07:11.910 - [任务 36(100)][7283e6b4-308f-4726-89a0-c39d752634c9] - Node 7283e6b4-308f-4726-89a0-c39d752634c9[7283e6b4-308f-4726-89a0-c39d752634c9] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:07:11.910 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:07:11.911 - [任务 36(100)][7283e6b4-308f-4726-89a0-c39d752634c9] - Node 7283e6b4-308f-4726-89a0-c39d752634c9[7283e6b4-308f-4726-89a0-c39d752634c9] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:07:11.911 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 12:07:11.911 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 12:07:11.929 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:07:11.931 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:07:11.931 - [任务 36(100)][492facec-42f1-4b18-8be0-44a4fcaa915b] - Node 492facec-42f1-4b18-8be0-44a4fcaa915b[492facec-42f1-4b18-8be0-44a4fcaa915b] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:07:11.931 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:07:11.931 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:07:11.931 - [任务 36(100)][492facec-42f1-4b18-8be0-44a4fcaa915b] - Node 492facec-42f1-4b18-8be0-44a4fcaa915b[492facec-42f1-4b18-8be0-44a4fcaa915b] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:07:12.171 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:07:12.179 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:07:12.179 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:07:12.179 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:07:12.179 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:07:12.179 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 12 ms 
[INFO ] 2024-04-01 12:07:12.294 - [任务 36(100)][3840aaee-0f4b-4aad-8eca-4906914ca519] - Node 3840aaee-0f4b-4aad-8eca-4906914ca519[3840aaee-0f4b-4aad-8eca-4906914ca519] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:07:12.294 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:07:12.295 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:07:12.295 - [任务 36(100)][3840aaee-0f4b-4aad-8eca-4906914ca519] - Node 3840aaee-0f4b-4aad-8eca-4906914ca519[3840aaee-0f4b-4aad-8eca-4906914ca519] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:07:12.295 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:07:12.295 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:07:12.580 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:07:12.583 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:07:12.583 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:07:12.583 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:07:12.584 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:07:12.584 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 5 ms 
[INFO ] 2024-04-01 12:07:12.674 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:07:12.674 - [任务 36(100)][7283e6b4-308f-4726-89a0-c39d752634c9] - Node 7283e6b4-308f-4726-89a0-c39d752634c9[7283e6b4-308f-4726-89a0-c39d752634c9] running status set to false 
[INFO ] 2024-04-01 12:07:12.675 - [任务 36(100)][7283e6b4-308f-4726-89a0-c39d752634c9] - Node 7283e6b4-308f-4726-89a0-c39d752634c9[7283e6b4-308f-4726-89a0-c39d752634c9] schema data cleaned 
[INFO ] 2024-04-01 12:07:12.675 - [任务 36(100)][7283e6b4-308f-4726-89a0-c39d752634c9] - Node 7283e6b4-308f-4726-89a0-c39d752634c9[7283e6b4-308f-4726-89a0-c39d752634c9] monitor closed 
[INFO ] 2024-04-01 12:07:12.675 - [任务 36(100)][7283e6b4-308f-4726-89a0-c39d752634c9] - Node 7283e6b4-308f-4726-89a0-c39d752634c9[7283e6b4-308f-4726-89a0-c39d752634c9] close complete, cost 1 ms 
[INFO ] 2024-04-01 12:07:12.680 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-0d657995-b0fa-46cb-ac13-2428e9305a0e 
[INFO ] 2024-04-01 12:07:12.680 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-0d657995-b0fa-46cb-ac13-2428e9305a0e 
[INFO ] 2024-04-01 12:07:12.681 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:07:12.682 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:07:12.683 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:07:12.683 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 11 ms 
[INFO ] 2024-04-01 12:07:12.890 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-7283e6b4-308f-4726-89a0-c39d752634c9 complete, cost 828ms 
[INFO ] 2024-04-01 12:07:12.898 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:07:12.899 - [任务 36(100)][3840aaee-0f4b-4aad-8eca-4906914ca519] - Node 3840aaee-0f4b-4aad-8eca-4906914ca519[3840aaee-0f4b-4aad-8eca-4906914ca519] running status set to false 
[INFO ] 2024-04-01 12:07:12.899 - [任务 36(100)][3840aaee-0f4b-4aad-8eca-4906914ca519] - Node 3840aaee-0f4b-4aad-8eca-4906914ca519[3840aaee-0f4b-4aad-8eca-4906914ca519] schema data cleaned 
[INFO ] 2024-04-01 12:07:12.899 - [任务 36(100)][3840aaee-0f4b-4aad-8eca-4906914ca519] - Node 3840aaee-0f4b-4aad-8eca-4906914ca519[3840aaee-0f4b-4aad-8eca-4906914ca519] monitor closed 
[INFO ] 2024-04-01 12:07:12.899 - [任务 36(100)][3840aaee-0f4b-4aad-8eca-4906914ca519] - Node 3840aaee-0f4b-4aad-8eca-4906914ca519[3840aaee-0f4b-4aad-8eca-4906914ca519] close complete, cost 1 ms 
[INFO ] 2024-04-01 12:07:12.906 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-52bc842c-cc62-45bc-a778-2c54b450052c 
[INFO ] 2024-04-01 12:07:12.906 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-52bc842c-cc62-45bc-a778-2c54b450052c 
[INFO ] 2024-04-01 12:07:12.907 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:07:12.910 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:07:12.911 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:07:12.911 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 17 ms 
[INFO ] 2024-04-01 12:07:12.945 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-3840aaee-0f4b-4aad-8eca-4906914ca519 complete, cost 655ms 
[INFO ] 2024-04-01 12:07:12.945 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:07:12.967 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:07:12.967 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:07:12.968 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:07:12.968 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:07:12.972 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 26 ms 
[INFO ] 2024-04-01 12:07:12.972 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:07:12.972 - [任务 36(100)][492facec-42f1-4b18-8be0-44a4fcaa915b] - Node 492facec-42f1-4b18-8be0-44a4fcaa915b[492facec-42f1-4b18-8be0-44a4fcaa915b] running status set to false 
[INFO ] 2024-04-01 12:07:12.972 - [任务 36(100)][492facec-42f1-4b18-8be0-44a4fcaa915b] - Node 492facec-42f1-4b18-8be0-44a4fcaa915b[492facec-42f1-4b18-8be0-44a4fcaa915b] schema data cleaned 
[INFO ] 2024-04-01 12:07:12.972 - [任务 36(100)][492facec-42f1-4b18-8be0-44a4fcaa915b] - Node 492facec-42f1-4b18-8be0-44a4fcaa915b[492facec-42f1-4b18-8be0-44a4fcaa915b] monitor closed 
[INFO ] 2024-04-01 12:07:12.977 - [任务 36(100)][492facec-42f1-4b18-8be0-44a4fcaa915b] - Node 492facec-42f1-4b18-8be0-44a4fcaa915b[492facec-42f1-4b18-8be0-44a4fcaa915b] close complete, cost 0 ms 
[INFO ] 2024-04-01 12:07:12.980 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-41bf68d8-88ec-43d4-84d3-bb6e23e8b2e8 
[INFO ] 2024-04-01 12:07:12.980 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-41bf68d8-88ec-43d4-84d3-bb6e23e8b2e8 
[INFO ] 2024-04-01 12:07:12.981 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:07:12.982 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:07:12.982 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:07:12.983 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 11 ms 
[INFO ] 2024-04-01 12:07:12.983 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-492facec-42f1-4b18-8be0-44a4fcaa915b complete, cost 1103ms 
[INFO ] 2024-04-01 12:07:13.180 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:07:13.181 - [任务 36(100)][f25fb5d4-9585-40ab-8350-5821dfa93107] - Node f25fb5d4-9585-40ab-8350-5821dfa93107[f25fb5d4-9585-40ab-8350-5821dfa93107] start preload schema,table counts: 0 
[INFO ] 2024-04-01 12:07:13.181 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 12:07:13.181 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 12:07:13.181 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 12:07:13.181 - [任务 36(100)][f25fb5d4-9585-40ab-8350-5821dfa93107] - Node f25fb5d4-9585-40ab-8350-5821dfa93107[f25fb5d4-9585-40ab-8350-5821dfa93107] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 12:07:13.459 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 12:07:13.459 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:07:13.459 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 12:07:13.459 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 12:07:13.459 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 12:07:13.459 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 12 ms 
[INFO ] 2024-04-01 12:07:13.679 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 12:07:13.680 - [任务 36(100)][f25fb5d4-9585-40ab-8350-5821dfa93107] - Node f25fb5d4-9585-40ab-8350-5821dfa93107[f25fb5d4-9585-40ab-8350-5821dfa93107] running status set to false 
[INFO ] 2024-04-01 12:07:13.680 - [任务 36(100)][f25fb5d4-9585-40ab-8350-5821dfa93107] - Node f25fb5d4-9585-40ab-8350-5821dfa93107[f25fb5d4-9585-40ab-8350-5821dfa93107] schema data cleaned 
[INFO ] 2024-04-01 12:07:13.680 - [任务 36(100)][f25fb5d4-9585-40ab-8350-5821dfa93107] - Node f25fb5d4-9585-40ab-8350-5821dfa93107[f25fb5d4-9585-40ab-8350-5821dfa93107] monitor closed 
[INFO ] 2024-04-01 12:07:13.680 - [任务 36(100)][f25fb5d4-9585-40ab-8350-5821dfa93107] - Node f25fb5d4-9585-40ab-8350-5821dfa93107[f25fb5d4-9585-40ab-8350-5821dfa93107] close complete, cost 0 ms 
[INFO ] 2024-04-01 12:07:13.682 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-38e2c430-c705-4fd9-afb5-c3d0f2df49bd 
[INFO ] 2024-04-01 12:07:13.683 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-38e2c430-c705-4fd9-afb5-c3d0f2df49bd 
[INFO ] 2024-04-01 12:07:13.683 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 12:07:13.683 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 12:07:13.683 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 12:07:13.684 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 5 ms 
[INFO ] 2024-04-01 12:07:13.685 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-f25fb5d4-9585-40ab-8350-5821dfa93107 complete, cost 562ms 
[INFO ] 2024-04-01 14:06:12.022 - [任务 36(100)][3830182e-a220-45b7-a8ae-b277c2c4c267] - Node 3830182e-a220-45b7-a8ae-b277c2c4c267[3830182e-a220-45b7-a8ae-b277c2c4c267] start preload schema,table counts: 0 
[INFO ] 2024-04-01 14:06:12.023 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:06:12.023 - [任务 36(100)][a41c6818-6a4e-4297-9fcd-599066e07947] - Node a41c6818-6a4e-4297-9fcd-599066e07947[a41c6818-6a4e-4297-9fcd-599066e07947] start preload schema,table counts: 0 
[INFO ] 2024-04-01 14:06:12.023 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:06:12.023 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:06:12.023 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:06:12.023 - [任务 36(100)][a41c6818-6a4e-4297-9fcd-599066e07947] - Node a41c6818-6a4e-4297-9fcd-599066e07947[a41c6818-6a4e-4297-9fcd-599066e07947] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:06:12.023 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:06:12.023 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:06:12.023 - [任务 36(100)][3830182e-a220-45b7-a8ae-b277c2c4c267] - Node 3830182e-a220-45b7-a8ae-b277c2c4c267[3830182e-a220-45b7-a8ae-b277c2c4c267] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:06:12.023 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:06:12.023 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:06:12.145 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 14:06:12.146 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:06:12.146 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:06:12.146 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 14:06:12.146 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 14:06:12.354 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 23 ms 
[INFO ] 2024-04-01 14:06:12.427 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 14:06:12.428 - [任务 36(100)][a41c6818-6a4e-4297-9fcd-599066e07947] - Node a41c6818-6a4e-4297-9fcd-599066e07947[a41c6818-6a4e-4297-9fcd-599066e07947] running status set to false 
[INFO ] 2024-04-01 14:06:12.430 - [任务 36(100)][a41c6818-6a4e-4297-9fcd-599066e07947] - Node a41c6818-6a4e-4297-9fcd-599066e07947[a41c6818-6a4e-4297-9fcd-599066e07947] schema data cleaned 
[INFO ] 2024-04-01 14:06:12.430 - [任务 36(100)][a41c6818-6a4e-4297-9fcd-599066e07947] - Node a41c6818-6a4e-4297-9fcd-599066e07947[a41c6818-6a4e-4297-9fcd-599066e07947] monitor closed 
[INFO ] 2024-04-01 14:06:12.431 - [任务 36(100)][a41c6818-6a4e-4297-9fcd-599066e07947] - Node a41c6818-6a4e-4297-9fcd-599066e07947[a41c6818-6a4e-4297-9fcd-599066e07947] close complete, cost 6 ms 
[INFO ] 2024-04-01 14:06:12.440 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-389f32b8-622a-44f1-9686-d2a744bef8aa 
[INFO ] 2024-04-01 14:06:12.440 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-389f32b8-622a-44f1-9686-d2a744bef8aa 
[INFO ] 2024-04-01 14:06:12.440 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 14:06:12.442 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 14:06:12.442 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 14:06:12.443 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 25 ms 
[INFO ] 2024-04-01 14:06:12.649 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-a41c6818-6a4e-4297-9fcd-599066e07947 complete, cost 758ms 
[INFO ] 2024-04-01 14:06:12.832 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 14:06:12.832 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:06:12.832 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:06:12.833 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 14:06:12.834 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 14:06:12.834 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 11 ms 
[INFO ] 2024-04-01 14:06:12.842 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 14:06:12.842 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-02073feb-2d56-4a1f-8bd2-42e078558403 
[INFO ] 2024-04-01 14:06:12.842 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-02073feb-2d56-4a1f-8bd2-42e078558403 
[INFO ] 2024-04-01 14:06:12.843 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 14:06:12.843 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 14:06:12.843 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 14:06:12.844 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 3 ms 
[INFO ] 2024-04-01 14:06:12.844 - [任务 36(100)][3830182e-a220-45b7-a8ae-b277c2c4c267] - Node 3830182e-a220-45b7-a8ae-b277c2c4c267[3830182e-a220-45b7-a8ae-b277c2c4c267] running status set to false 
[INFO ] 2024-04-01 14:06:12.844 - [任务 36(100)][3830182e-a220-45b7-a8ae-b277c2c4c267] - Node 3830182e-a220-45b7-a8ae-b277c2c4c267[3830182e-a220-45b7-a8ae-b277c2c4c267] schema data cleaned 
[INFO ] 2024-04-01 14:06:12.844 - [任务 36(100)][3830182e-a220-45b7-a8ae-b277c2c4c267] - Node 3830182e-a220-45b7-a8ae-b277c2c4c267[3830182e-a220-45b7-a8ae-b277c2c4c267] monitor closed 
[INFO ] 2024-04-01 14:06:12.845 - [任务 36(100)][3830182e-a220-45b7-a8ae-b277c2c4c267] - Node 3830182e-a220-45b7-a8ae-b277c2c4c267[3830182e-a220-45b7-a8ae-b277c2c4c267] close complete, cost 0 ms 
[INFO ] 2024-04-01 14:06:12.845 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-3830182e-a220-45b7-a8ae-b277c2c4c267 complete, cost 1158ms 
[INFO ] 2024-04-01 14:06:29.106 - [任务 36(100)][e2dfa08b-4815-4ed4-9424-9608134d4a32] - Node e2dfa08b-4815-4ed4-9424-9608134d4a32[e2dfa08b-4815-4ed4-9424-9608134d4a32] start preload schema,table counts: 0 
[INFO ] 2024-04-01 14:06:29.106 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:06:29.106 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:06:29.106 - [任务 36(100)][e2dfa08b-4815-4ed4-9424-9608134d4a32] - Node e2dfa08b-4815-4ed4-9424-9608134d4a32[e2dfa08b-4815-4ed4-9424-9608134d4a32] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 14:06:29.106 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 14:06:29.106 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 4 ms 
[INFO ] 2024-04-01 14:06:29.412 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 14:06:29.412 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:06:29.412 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:06:29.412 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 14:06:29.412 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 14:06:29.614 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 13 ms 
[INFO ] 2024-04-01 14:06:29.629 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 14:06:29.629 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-becb7cfd-71bd-4bd5-ae0f-e32ad9bc9d32 
[INFO ] 2024-04-01 14:06:29.629 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-becb7cfd-71bd-4bd5-ae0f-e32ad9bc9d32 
[INFO ] 2024-04-01 14:06:29.629 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 14:06:29.630 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 14:06:29.630 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 14:06:29.631 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 4 ms 
[INFO ] 2024-04-01 14:06:29.631 - [任务 36(100)][e2dfa08b-4815-4ed4-9424-9608134d4a32] - Node e2dfa08b-4815-4ed4-9424-9608134d4a32[e2dfa08b-4815-4ed4-9424-9608134d4a32] running status set to false 
[INFO ] 2024-04-01 14:06:29.631 - [任务 36(100)][e2dfa08b-4815-4ed4-9424-9608134d4a32] - Node e2dfa08b-4815-4ed4-9424-9608134d4a32[e2dfa08b-4815-4ed4-9424-9608134d4a32] schema data cleaned 
[INFO ] 2024-04-01 14:06:29.631 - [任务 36(100)][e2dfa08b-4815-4ed4-9424-9608134d4a32] - Node e2dfa08b-4815-4ed4-9424-9608134d4a32[e2dfa08b-4815-4ed4-9424-9608134d4a32] monitor closed 
[INFO ] 2024-04-01 14:06:29.632 - [任务 36(100)][e2dfa08b-4815-4ed4-9424-9608134d4a32] - Node e2dfa08b-4815-4ed4-9424-9608134d4a32[e2dfa08b-4815-4ed4-9424-9608134d4a32] close complete, cost 0 ms 
[INFO ] 2024-04-01 14:06:29.632 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-e2dfa08b-4815-4ed4-9424-9608134d4a32 complete, cost 621ms 
[INFO ] 2024-04-01 14:06:39.831 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:06:39.831 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:06:39.832 - [任务 36(100)][a579defa-04a6-4895-8335-a30ce4106d6f] - Node a579defa-04a6-4895-8335-a30ce4106d6f[a579defa-04a6-4895-8335-a30ce4106d6f] start preload schema,table counts: 0 
[INFO ] 2024-04-01 14:06:39.832 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:06:39.832 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:06:39.832 - [任务 36(100)][a579defa-04a6-4895-8335-a30ce4106d6f] - Node a579defa-04a6-4895-8335-a30ce4106d6f[a579defa-04a6-4895-8335-a30ce4106d6f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:06:40.130 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 14:06:40.142 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:06:40.142 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:06:40.142 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 14:06:40.142 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 14:06:40.345 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 15 ms 
[INFO ] 2024-04-01 14:06:40.352 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 14:06:40.352 - [任务 36(100)][a579defa-04a6-4895-8335-a30ce4106d6f] - Node a579defa-04a6-4895-8335-a30ce4106d6f[a579defa-04a6-4895-8335-a30ce4106d6f] running status set to false 
[INFO ] 2024-04-01 14:06:40.352 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-d22d39f0-1c1b-4ced-8ee1-fdbce8e30c0e 
[INFO ] 2024-04-01 14:06:40.352 - [任务 36(100)][a579defa-04a6-4895-8335-a30ce4106d6f] - Node a579defa-04a6-4895-8335-a30ce4106d6f[a579defa-04a6-4895-8335-a30ce4106d6f] schema data cleaned 
[INFO ] 2024-04-01 14:06:40.352 - [任务 36(100)][a579defa-04a6-4895-8335-a30ce4106d6f] - Node a579defa-04a6-4895-8335-a30ce4106d6f[a579defa-04a6-4895-8335-a30ce4106d6f] monitor closed 
[INFO ] 2024-04-01 14:06:40.352 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-d22d39f0-1c1b-4ced-8ee1-fdbce8e30c0e 
[INFO ] 2024-04-01 14:06:40.352 - [任务 36(100)][a579defa-04a6-4895-8335-a30ce4106d6f] - Node a579defa-04a6-4895-8335-a30ce4106d6f[a579defa-04a6-4895-8335-a30ce4106d6f] close complete, cost 0 ms 
[INFO ] 2024-04-01 14:06:40.353 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 14:06:40.354 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 14:06:40.354 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 14:06:40.354 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 7 ms 
[INFO ] 2024-04-01 14:06:40.555 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-a579defa-04a6-4895-8335-a30ce4106d6f complete, cost 700ms 
[INFO ] 2024-04-01 14:22:16.472 - [任务 36(100)][9a8b244d-cbb2-4197-a658-c3c3b3425e42] - Node 9a8b244d-cbb2-4197-a658-c3c3b3425e42[9a8b244d-cbb2-4197-a658-c3c3b3425e42] start preload schema,table counts: 0 
[INFO ] 2024-04-01 14:22:16.473 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:22:16.473 - [任务 36(100)][9a8b244d-cbb2-4197-a658-c3c3b3425e42] - Node 9a8b244d-cbb2-4197-a658-c3c3b3425e42[9a8b244d-cbb2-4197-a658-c3c3b3425e42] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:22:16.473 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:22:16.474 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 14:22:16.474 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:22:16.474 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 14:22:16.474 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:22:16.476 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:22:16.477 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 14:22:16.492 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 14:22:16.493 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 32 ms 
[INFO ] 2024-04-01 14:22:16.751 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 14:22:16.773 - [任务 36(100)][9a8b244d-cbb2-4197-a658-c3c3b3425e42] - Node 9a8b244d-cbb2-4197-a658-c3c3b3425e42[9a8b244d-cbb2-4197-a658-c3c3b3425e42] running status set to false 
[INFO ] 2024-04-01 14:22:16.773 - [任务 36(100)][9a8b244d-cbb2-4197-a658-c3c3b3425e42] - Node 9a8b244d-cbb2-4197-a658-c3c3b3425e42[9a8b244d-cbb2-4197-a658-c3c3b3425e42] schema data cleaned 
[INFO ] 2024-04-01 14:22:16.780 - [任务 36(100)][9a8b244d-cbb2-4197-a658-c3c3b3425e42] - Node 9a8b244d-cbb2-4197-a658-c3c3b3425e42[9a8b244d-cbb2-4197-a658-c3c3b3425e42] monitor closed 
[INFO ] 2024-04-01 14:22:16.781 - [任务 36(100)][9a8b244d-cbb2-4197-a658-c3c3b3425e42] - Node 9a8b244d-cbb2-4197-a658-c3c3b3425e42[9a8b244d-cbb2-4197-a658-c3c3b3425e42] close complete, cost 17 ms 
[INFO ] 2024-04-01 14:22:16.785 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-b5d76ab4-1112-4885-bc33-ee770d05b611 
[INFO ] 2024-04-01 14:22:16.786 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-b5d76ab4-1112-4885-bc33-ee770d05b611 
[INFO ] 2024-04-01 14:22:16.792 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 14:22:16.793 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 14:22:16.798 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 14:22:16.798 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 50 ms 
[INFO ] 2024-04-01 14:22:33.158 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-9a8b244d-cbb2-4197-a658-c3c3b3425e42 complete, cost 879ms 
[INFO ] 2024-04-01 14:29:18.294 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:29:18.296 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:29:18.300 - [任务 36(100)][3984040c-5031-4a3f-82ae-89b3a7d001f5] - Node 3984040c-5031-4a3f-82ae-89b3a7d001f5[3984040c-5031-4a3f-82ae-89b3a7d001f5] start preload schema,table counts: 0 
[INFO ] 2024-04-01 14:29:18.305 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:29:18.307 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:29:18.308 - [任务 36(100)][3984040c-5031-4a3f-82ae-89b3a7d001f5] - Node 3984040c-5031-4a3f-82ae-89b3a7d001f5[3984040c-5031-4a3f-82ae-89b3a7d001f5] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:29:18.629 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 14:29:18.630 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:29:18.630 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:29:18.630 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 14:29:18.631 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 14:29:18.632 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 22 ms 
[INFO ] 2024-04-01 14:29:18.868 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 14:29:18.869 - [任务 36(100)][3984040c-5031-4a3f-82ae-89b3a7d001f5] - Node 3984040c-5031-4a3f-82ae-89b3a7d001f5[3984040c-5031-4a3f-82ae-89b3a7d001f5] running status set to false 
[INFO ] 2024-04-01 14:29:18.869 - [任务 36(100)][3984040c-5031-4a3f-82ae-89b3a7d001f5] - Node 3984040c-5031-4a3f-82ae-89b3a7d001f5[3984040c-5031-4a3f-82ae-89b3a7d001f5] schema data cleaned 
[INFO ] 2024-04-01 14:29:18.878 - [任务 36(100)][3984040c-5031-4a3f-82ae-89b3a7d001f5] - Node 3984040c-5031-4a3f-82ae-89b3a7d001f5[3984040c-5031-4a3f-82ae-89b3a7d001f5] monitor closed 
[INFO ] 2024-04-01 14:29:18.878 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-e84ed08a-4fc0-41c3-b28d-a00709b6c55d 
[INFO ] 2024-04-01 14:29:18.878 - [任务 36(100)][3984040c-5031-4a3f-82ae-89b3a7d001f5] - Node 3984040c-5031-4a3f-82ae-89b3a7d001f5[3984040c-5031-4a3f-82ae-89b3a7d001f5] close complete, cost 5 ms 
[INFO ] 2024-04-01 14:29:18.878 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-e84ed08a-4fc0-41c3-b28d-a00709b6c55d 
[INFO ] 2024-04-01 14:29:18.879 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 14:29:18.885 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 14:29:18.886 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 14:29:18.886 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 18 ms 
[INFO ] 2024-04-01 14:30:37.729 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-3984040c-5031-4a3f-82ae-89b3a7d001f5 complete, cost 698ms 
[INFO ] 2024-04-01 14:35:34.496 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:35:34.497 - [任务 36(100)][c15f3575-cef7-421c-b9aa-b42d5de9037d] - Node c15f3575-cef7-421c-b9aa-b42d5de9037d[c15f3575-cef7-421c-b9aa-b42d5de9037d] start preload schema,table counts: 0 
[INFO ] 2024-04-01 14:35:34.497 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 2 ms 
[INFO ] 2024-04-01 14:35:34.497 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:35:34.497 - [任务 36(100)][c15f3575-cef7-421c-b9aa-b42d5de9037d] - Node c15f3575-cef7-421c-b9aa-b42d5de9037d[c15f3575-cef7-421c-b9aa-b42d5de9037d] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:35:34.497 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:35:34.497 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 14:35:34.497 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:35:34.497 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:35:34.497 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 14:35:34.499 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 14:35:34.499 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 20 ms 
[INFO ] 2024-04-01 14:35:34.728 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 14:35:34.733 - [任务 36(100)][c15f3575-cef7-421c-b9aa-b42d5de9037d] - Node c15f3575-cef7-421c-b9aa-b42d5de9037d[c15f3575-cef7-421c-b9aa-b42d5de9037d] running status set to false 
[INFO ] 2024-04-01 14:35:34.734 - [任务 36(100)][c15f3575-cef7-421c-b9aa-b42d5de9037d] - Node c15f3575-cef7-421c-b9aa-b42d5de9037d[c15f3575-cef7-421c-b9aa-b42d5de9037d] schema data cleaned 
[INFO ] 2024-04-01 14:35:34.735 - [任务 36(100)][c15f3575-cef7-421c-b9aa-b42d5de9037d] - Node c15f3575-cef7-421c-b9aa-b42d5de9037d[c15f3575-cef7-421c-b9aa-b42d5de9037d] monitor closed 
[INFO ] 2024-04-01 14:35:34.735 - [任务 36(100)][c15f3575-cef7-421c-b9aa-b42d5de9037d] - Node c15f3575-cef7-421c-b9aa-b42d5de9037d[c15f3575-cef7-421c-b9aa-b42d5de9037d] close complete, cost 4 ms 
[INFO ] 2024-04-01 14:35:34.736 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-cf604936-71d2-4df9-8244-b86f36756baa 
[INFO ] 2024-04-01 14:35:34.736 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-cf604936-71d2-4df9-8244-b86f36756baa 
[INFO ] 2024-04-01 14:35:34.737 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 14:35:34.741 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 14:35:34.741 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 14:35:34.743 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 19 ms 
[INFO ] 2024-04-01 14:35:34.743 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-c15f3575-cef7-421c-b9aa-b42d5de9037d complete, cost 765ms 
[INFO ] 2024-04-01 14:36:02.752 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:36:02.752 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:36:02.754 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 6 ms 
[INFO ] 2024-04-01 14:36:02.765 - [任务 36(100)][facf5b64-f6a5-4e9b-9de3-3465e6e97062] - Node facf5b64-f6a5-4e9b-9de3-3465e6e97062[facf5b64-f6a5-4e9b-9de3-3465e6e97062] start preload schema,table counts: 0 
[INFO ] 2024-04-01 14:36:02.765 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 14:36:02.765 - [任务 36(100)][facf5b64-f6a5-4e9b-9de3-3465e6e97062] - Node facf5b64-f6a5-4e9b-9de3-3465e6e97062[facf5b64-f6a5-4e9b-9de3-3465e6e97062] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:36:03.018 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 14:36:03.044 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:36:03.044 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:36:03.048 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 14:36:03.049 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 14:36:03.251 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 35 ms 
[INFO ] 2024-04-01 14:36:03.268 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 14:36:03.271 - [任务 36(100)][facf5b64-f6a5-4e9b-9de3-3465e6e97062] - Node facf5b64-f6a5-4e9b-9de3-3465e6e97062[facf5b64-f6a5-4e9b-9de3-3465e6e97062] running status set to false 
[INFO ] 2024-04-01 14:36:03.271 - [任务 36(100)][facf5b64-f6a5-4e9b-9de3-3465e6e97062] - Node facf5b64-f6a5-4e9b-9de3-3465e6e97062[facf5b64-f6a5-4e9b-9de3-3465e6e97062] schema data cleaned 
[INFO ] 2024-04-01 14:36:03.272 - [任务 36(100)][facf5b64-f6a5-4e9b-9de3-3465e6e97062] - Node facf5b64-f6a5-4e9b-9de3-3465e6e97062[facf5b64-f6a5-4e9b-9de3-3465e6e97062] monitor closed 
[INFO ] 2024-04-01 14:36:03.278 - [任务 36(100)][facf5b64-f6a5-4e9b-9de3-3465e6e97062] - Node facf5b64-f6a5-4e9b-9de3-3465e6e97062[facf5b64-f6a5-4e9b-9de3-3465e6e97062] close complete, cost 2 ms 
[INFO ] 2024-04-01 14:36:03.278 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-ccbab79c-339b-4e7f-95b6-858b5ab0d85e 
[INFO ] 2024-04-01 14:36:03.278 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-ccbab79c-339b-4e7f-95b6-858b5ab0d85e 
[INFO ] 2024-04-01 14:36:03.279 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 14:36:03.282 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 14:36:03.282 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 14:36:03.284 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 18 ms 
[INFO ] 2024-04-01 14:36:03.284 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-facf5b64-f6a5-4e9b-9de3-3465e6e97062 complete, cost 607ms 
[INFO ] 2024-04-01 14:36:28.051 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:36:28.051 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:36:28.052 - [任务 36(100)][41a67912-d483-4934-ae42-54d596902c8b] - Node 41a67912-d483-4934-ae42-54d596902c8b[41a67912-d483-4934-ae42-54d596902c8b] start preload schema,table counts: 0 
[INFO ] 2024-04-01 14:36:28.053 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:36:28.057 - [任务 36(100)][41a67912-d483-4934-ae42-54d596902c8b] - Node 41a67912-d483-4934-ae42-54d596902c8b[41a67912-d483-4934-ae42-54d596902c8b] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:36:28.057 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:36:28.360 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 14:36:28.370 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:36:28.370 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:36:28.370 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 14:36:28.370 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 14:36:28.576 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 15 ms 
[INFO ] 2024-04-01 14:36:28.603 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 14:36:28.609 - [任务 36(100)][41a67912-d483-4934-ae42-54d596902c8b] - Node 41a67912-d483-4934-ae42-54d596902c8b[41a67912-d483-4934-ae42-54d596902c8b] running status set to false 
[INFO ] 2024-04-01 14:36:28.609 - [任务 36(100)][41a67912-d483-4934-ae42-54d596902c8b] - Node 41a67912-d483-4934-ae42-54d596902c8b[41a67912-d483-4934-ae42-54d596902c8b] schema data cleaned 
[INFO ] 2024-04-01 14:36:28.609 - [任务 36(100)][41a67912-d483-4934-ae42-54d596902c8b] - Node 41a67912-d483-4934-ae42-54d596902c8b[41a67912-d483-4934-ae42-54d596902c8b] monitor closed 
[INFO ] 2024-04-01 14:36:28.609 - [任务 36(100)][41a67912-d483-4934-ae42-54d596902c8b] - Node 41a67912-d483-4934-ae42-54d596902c8b[41a67912-d483-4934-ae42-54d596902c8b] close complete, cost 5 ms 
[INFO ] 2024-04-01 14:36:28.611 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-4a315cd5-b773-4d8a-ac36-d8c03dddc38c 
[INFO ] 2024-04-01 14:36:28.611 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-4a315cd5-b773-4d8a-ac36-d8c03dddc38c 
[INFO ] 2024-04-01 14:36:28.614 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 14:36:28.614 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 14:36:28.614 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 14:36:28.615 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 15 ms 
[INFO ] 2024-04-01 14:36:28.826 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-41a67912-d483-4934-ae42-54d596902c8b complete, cost 672ms 
[INFO ] 2024-04-01 14:41:26.604 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:41:26.609 - [任务 36(100)][be2459bc-d396-467d-9639-d6816ed2c94f] - Node be2459bc-d396-467d-9639-d6816ed2c94f[be2459bc-d396-467d-9639-d6816ed2c94f] start preload schema,table counts: 0 
[INFO ] 2024-04-01 14:41:26.609 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:41:26.609 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:41:26.609 - [任务 36(100)][c67971c6-1df4-4706-a781-f3463d8a5b3d] - Node c67971c6-1df4-4706-a781-f3463d8a5b3d[c67971c6-1df4-4706-a781-f3463d8a5b3d] start preload schema,table counts: 0 
[INFO ] 2024-04-01 14:41:26.609 - [任务 36(100)][be2459bc-d396-467d-9639-d6816ed2c94f] - Node be2459bc-d396-467d-9639-d6816ed2c94f[be2459bc-d396-467d-9639-d6816ed2c94f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:41:26.609 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:41:26.610 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:41:26.610 - [任务 36(100)][c67971c6-1df4-4706-a781-f3463d8a5b3d] - Node c67971c6-1df4-4706-a781-f3463d8a5b3d[c67971c6-1df4-4706-a781-f3463d8a5b3d] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:41:26.610 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 6 ms 
[INFO ] 2024-04-01 14:41:26.610 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 14:41:26.610 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:41:26.912 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 14:41:26.921 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:41:26.921 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:41:26.921 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 14:41:26.922 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 14:41:27.123 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 17 ms 
[INFO ] 2024-04-01 14:41:27.459 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 14:41:27.464 - [任务 36(100)][c67971c6-1df4-4706-a781-f3463d8a5b3d] - Node c67971c6-1df4-4706-a781-f3463d8a5b3d[c67971c6-1df4-4706-a781-f3463d8a5b3d] running status set to false 
[INFO ] 2024-04-01 14:41:27.465 - [任务 36(100)][c67971c6-1df4-4706-a781-f3463d8a5b3d] - Node c67971c6-1df4-4706-a781-f3463d8a5b3d[c67971c6-1df4-4706-a781-f3463d8a5b3d] schema data cleaned 
[INFO ] 2024-04-01 14:41:27.465 - [任务 36(100)][c67971c6-1df4-4706-a781-f3463d8a5b3d] - Node c67971c6-1df4-4706-a781-f3463d8a5b3d[c67971c6-1df4-4706-a781-f3463d8a5b3d] monitor closed 
[INFO ] 2024-04-01 14:41:27.466 - [任务 36(100)][c67971c6-1df4-4706-a781-f3463d8a5b3d] - Node c67971c6-1df4-4706-a781-f3463d8a5b3d[c67971c6-1df4-4706-a781-f3463d8a5b3d] close complete, cost 12 ms 
[INFO ] 2024-04-01 14:41:27.475 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-443e4fdf-3739-415b-90a2-40bd06e3a761 
[INFO ] 2024-04-01 14:41:27.476 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-443e4fdf-3739-415b-90a2-40bd06e3a761 
[INFO ] 2024-04-01 14:41:27.478 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 14:41:27.478 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 14:41:27.478 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 14:41:27.479 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 39 ms 
[INFO ] 2024-04-01 14:41:27.623 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-c67971c6-1df4-4706-a781-f3463d8a5b3d complete, cost 1012ms 
[INFO ] 2024-04-01 14:41:27.623 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 14:41:27.635 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:41:27.635 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 14:41:27.639 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:41:27.639 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 14:41:27.644 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 14:41:27.644 - [任务 36(100)][be2459bc-d396-467d-9639-d6816ed2c94f] - Node be2459bc-d396-467d-9639-d6816ed2c94f[be2459bc-d396-467d-9639-d6816ed2c94f] running status set to false 
[INFO ] 2024-04-01 14:41:27.645 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 45 ms 
[INFO ] 2024-04-01 14:41:27.646 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-6d9b78c2-c6ba-462a-826a-bb6ca8e25add 
[INFO ] 2024-04-01 14:41:27.646 - [任务 36(100)][be2459bc-d396-467d-9639-d6816ed2c94f] - Node be2459bc-d396-467d-9639-d6816ed2c94f[be2459bc-d396-467d-9639-d6816ed2c94f] schema data cleaned 
[INFO ] 2024-04-01 14:41:27.646 - [任务 36(100)][be2459bc-d396-467d-9639-d6816ed2c94f] - Node be2459bc-d396-467d-9639-d6816ed2c94f[be2459bc-d396-467d-9639-d6816ed2c94f] monitor closed 
[INFO ] 2024-04-01 14:41:27.646 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-6d9b78c2-c6ba-462a-826a-bb6ca8e25add 
[INFO ] 2024-04-01 14:41:27.647 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 14:41:27.647 - [任务 36(100)][be2459bc-d396-467d-9639-d6816ed2c94f] - Node be2459bc-d396-467d-9639-d6816ed2c94f[be2459bc-d396-467d-9639-d6816ed2c94f] close complete, cost 7 ms 
[INFO ] 2024-04-01 14:41:27.648 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 14:41:27.648 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 14:41:27.649 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 14 ms 
[INFO ] 2024-04-01 14:41:27.649 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-be2459bc-d396-467d-9639-d6816ed2c94f complete, cost 1175ms 
[INFO ] 2024-04-01 14:41:35.168 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:41:35.169 - [任务 36(100)][c9fe736d-7119-4a41-b372-35b14bea4617] - Node c9fe736d-7119-4a41-b372-35b14bea4617[c9fe736d-7119-4a41-b372-35b14bea4617] start preload schema,table counts: 0 
[INFO ] 2024-04-01 14:41:35.169 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 14:41:35.169 - [任务 36(100)][c9fe736d-7119-4a41-b372-35b14bea4617] - Node c9fe736d-7119-4a41-b372-35b14bea4617[c9fe736d-7119-4a41-b372-35b14bea4617] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:41:35.170 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:41:35.170 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 14:41:35.511 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 14:41:35.518 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:41:35.518 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 14:41:35.518 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 14:41:35.518 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 14:41:35.720 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 22 ms 
[INFO ] 2024-04-01 14:41:35.738 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 14:41:35.739 - [任务 36(100)][c9fe736d-7119-4a41-b372-35b14bea4617] - Node c9fe736d-7119-4a41-b372-35b14bea4617[c9fe736d-7119-4a41-b372-35b14bea4617] running status set to false 
[INFO ] 2024-04-01 14:41:35.739 - [任务 36(100)][c9fe736d-7119-4a41-b372-35b14bea4617] - Node c9fe736d-7119-4a41-b372-35b14bea4617[c9fe736d-7119-4a41-b372-35b14bea4617] schema data cleaned 
[INFO ] 2024-04-01 14:41:35.739 - [任务 36(100)][c9fe736d-7119-4a41-b372-35b14bea4617] - Node c9fe736d-7119-4a41-b372-35b14bea4617[c9fe736d-7119-4a41-b372-35b14bea4617] monitor closed 
[INFO ] 2024-04-01 14:41:35.739 - [任务 36(100)][c9fe736d-7119-4a41-b372-35b14bea4617] - Node c9fe736d-7119-4a41-b372-35b14bea4617[c9fe736d-7119-4a41-b372-35b14bea4617] close complete, cost 1 ms 
[INFO ] 2024-04-01 14:41:35.743 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-be32bcfa-0af6-4f3c-b3e1-d8fc52cd79c9 
[INFO ] 2024-04-01 14:41:35.743 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-be32bcfa-0af6-4f3c-b3e1-d8fc52cd79c9 
[INFO ] 2024-04-01 14:41:35.743 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 14:41:35.744 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 14:41:35.744 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 14:41:35.744 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 13 ms 
[INFO ] 2024-04-01 14:41:35.945 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-c9fe736d-7119-4a41-b372-35b14bea4617 complete, cost 716ms 
[INFO ] 2024-04-01 15:43:59.906 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:43:59.907 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:43:59.907 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:43:59.908 - [任务 36(100)][d9846008-a56f-47b9-bcc2-9462272cdba8] - Node d9846008-a56f-47b9-bcc2-9462272cdba8[d9846008-a56f-47b9-bcc2-9462272cdba8] start preload schema,table counts: 0 
[INFO ] 2024-04-01 15:43:59.909 - [任务 36(100)][3e3bd858-66d1-44a1-8377-2b3fd8be66d3] - Node 3e3bd858-66d1-44a1-8377-2b3fd8be66d3[3e3bd858-66d1-44a1-8377-2b3fd8be66d3] start preload schema,table counts: 0 
[INFO ] 2024-04-01 15:43:59.909 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:43:59.909 - [任务 36(100)][d9846008-a56f-47b9-bcc2-9462272cdba8] - Node d9846008-a56f-47b9-bcc2-9462272cdba8[d9846008-a56f-47b9-bcc2-9462272cdba8] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:43:59.909 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 2 ms 
[INFO ] 2024-04-01 15:43:59.909 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:43:59.909 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:43:59.909 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:43:59.909 - [任务 36(100)][3e3bd858-66d1-44a1-8377-2b3fd8be66d3] - Node 3e3bd858-66d1-44a1-8377-2b3fd8be66d3[3e3bd858-66d1-44a1-8377-2b3fd8be66d3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:44:00.714 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 15:44:00.714 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:44:00.717 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:44:00.721 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 15:44:00.722 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 15:44:00.918 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 18 ms 
[INFO ] 2024-04-01 15:44:00.918 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 15:44:00.951 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:44:00.951 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:44:00.952 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 15:44:00.952 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 15:44:00.952 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 15:44:00.953 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 35 ms 
[INFO ] 2024-04-01 15:44:00.958 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-a9e01a69-8d75-45c6-84c9-f7858bd3319a 
[INFO ] 2024-04-01 15:44:00.958 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-a9e01a69-8d75-45c6-84c9-f7858bd3319a 
[INFO ] 2024-04-01 15:44:00.958 - [任务 36(100)][d9846008-a56f-47b9-bcc2-9462272cdba8] - Node d9846008-a56f-47b9-bcc2-9462272cdba8[d9846008-a56f-47b9-bcc2-9462272cdba8] running status set to false 
[INFO ] 2024-04-01 15:44:00.958 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 15:44:00.958 - [任务 36(100)][d9846008-a56f-47b9-bcc2-9462272cdba8] - Node d9846008-a56f-47b9-bcc2-9462272cdba8[d9846008-a56f-47b9-bcc2-9462272cdba8] schema data cleaned 
[INFO ] 2024-04-01 15:44:00.958 - [任务 36(100)][d9846008-a56f-47b9-bcc2-9462272cdba8] - Node d9846008-a56f-47b9-bcc2-9462272cdba8[d9846008-a56f-47b9-bcc2-9462272cdba8] monitor closed 
[INFO ] 2024-04-01 15:44:00.958 - [任务 36(100)][d9846008-a56f-47b9-bcc2-9462272cdba8] - Node d9846008-a56f-47b9-bcc2-9462272cdba8[d9846008-a56f-47b9-bcc2-9462272cdba8] close complete, cost 0 ms 
[INFO ] 2024-04-01 15:44:00.960 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 15:44:00.960 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 15:44:00.962 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 8 ms 
[INFO ] 2024-04-01 15:44:00.962 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-d9846008-a56f-47b9-bcc2-9462272cdba8 complete, cost 1234ms 
[INFO ] 2024-04-01 15:44:01.202 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 15:44:01.203 - [任务 36(100)][3e3bd858-66d1-44a1-8377-2b3fd8be66d3] - Node 3e3bd858-66d1-44a1-8377-2b3fd8be66d3[3e3bd858-66d1-44a1-8377-2b3fd8be66d3] running status set to false 
[INFO ] 2024-04-01 15:44:01.204 - [任务 36(100)][3e3bd858-66d1-44a1-8377-2b3fd8be66d3] - Node 3e3bd858-66d1-44a1-8377-2b3fd8be66d3[3e3bd858-66d1-44a1-8377-2b3fd8be66d3] schema data cleaned 
[INFO ] 2024-04-01 15:44:01.204 - [任务 36(100)][3e3bd858-66d1-44a1-8377-2b3fd8be66d3] - Node 3e3bd858-66d1-44a1-8377-2b3fd8be66d3[3e3bd858-66d1-44a1-8377-2b3fd8be66d3] monitor closed 
[INFO ] 2024-04-01 15:44:01.204 - [任务 36(100)][3e3bd858-66d1-44a1-8377-2b3fd8be66d3] - Node 3e3bd858-66d1-44a1-8377-2b3fd8be66d3[3e3bd858-66d1-44a1-8377-2b3fd8be66d3] close complete, cost 3 ms 
[INFO ] 2024-04-01 15:44:01.208 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-f10408e1-c138-4d29-a00b-99661698eb7d 
[INFO ] 2024-04-01 15:44:01.208 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-f10408e1-c138-4d29-a00b-99661698eb7d 
[INFO ] 2024-04-01 15:44:01.208 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 15:44:01.210 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 15:44:01.210 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 15:44:01.213 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 13 ms 
[INFO ] 2024-04-01 15:44:01.213 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-3e3bd858-66d1-44a1-8377-2b3fd8be66d3 complete, cost 1485ms 
[INFO ] 2024-04-01 15:47:17.123 - [任务 36(100)][eeb54710-af69-4f6b-b7fd-e028d8862844] - Node eeb54710-af69-4f6b-b7fd-e028d8862844[eeb54710-af69-4f6b-b7fd-e028d8862844] start preload schema,table counts: 0 
[INFO ] 2024-04-01 15:47:17.123 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:47:17.124 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:47:17.124 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:47:17.124 - [任务 36(100)][eeb54710-af69-4f6b-b7fd-e028d8862844] - Node eeb54710-af69-4f6b-b7fd-e028d8862844[eeb54710-af69-4f6b-b7fd-e028d8862844] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:47:17.124 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:47:17.528 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 15:47:17.528 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:47:17.528 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:47:17.528 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 15:47:17.528 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 15:47:17.730 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 40 ms 
[INFO ] 2024-04-01 15:47:17.742 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 15:47:17.742 - [任务 36(100)][eeb54710-af69-4f6b-b7fd-e028d8862844] - Node eeb54710-af69-4f6b-b7fd-e028d8862844[eeb54710-af69-4f6b-b7fd-e028d8862844] running status set to false 
[INFO ] 2024-04-01 15:47:17.742 - [任务 36(100)][eeb54710-af69-4f6b-b7fd-e028d8862844] - Node eeb54710-af69-4f6b-b7fd-e028d8862844[eeb54710-af69-4f6b-b7fd-e028d8862844] schema data cleaned 
[INFO ] 2024-04-01 15:47:17.742 - [任务 36(100)][eeb54710-af69-4f6b-b7fd-e028d8862844] - Node eeb54710-af69-4f6b-b7fd-e028d8862844[eeb54710-af69-4f6b-b7fd-e028d8862844] monitor closed 
[INFO ] 2024-04-01 15:47:17.744 - [任务 36(100)][eeb54710-af69-4f6b-b7fd-e028d8862844] - Node eeb54710-af69-4f6b-b7fd-e028d8862844[eeb54710-af69-4f6b-b7fd-e028d8862844] close complete, cost 1 ms 
[INFO ] 2024-04-01 15:47:17.745 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-fa299543-c161-461e-87bb-cedd057e4efd 
[INFO ] 2024-04-01 15:47:17.745 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-fa299543-c161-461e-87bb-cedd057e4efd 
[INFO ] 2024-04-01 15:47:17.747 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 15:47:17.747 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 15:47:17.748 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 15:47:17.748 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 14 ms 
[INFO ] 2024-04-01 15:47:17.956 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-eeb54710-af69-4f6b-b7fd-e028d8862844 complete, cost 812ms 
[INFO ] 2024-04-01 15:49:31.452 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:49:31.452 - [任务 36(100)][2e5c6522-3053-4dfb-9c0b-1c5a88788cfe] - Node 2e5c6522-3053-4dfb-9c0b-1c5a88788cfe[2e5c6522-3053-4dfb-9c0b-1c5a88788cfe] start preload schema,table counts: 0 
[INFO ] 2024-04-01 15:49:31.452 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:49:31.453 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 3 ms 
[INFO ] 2024-04-01 15:49:31.453 - [任务 36(100)][2e5c6522-3053-4dfb-9c0b-1c5a88788cfe] - Node 2e5c6522-3053-4dfb-9c0b-1c5a88788cfe[2e5c6522-3053-4dfb-9c0b-1c5a88788cfe] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:49:31.453 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:49:31.521 - [任务 36(100)][d0496efe-02d9-4859-94ec-90dd72e53d51] - Node d0496efe-02d9-4859-94ec-90dd72e53d51[d0496efe-02d9-4859-94ec-90dd72e53d51] start preload schema,table counts: 0 
[INFO ] 2024-04-01 15:49:31.521 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:49:31.521 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:49:31.521 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:49:31.521 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:49:31.521 - [任务 36(100)][d0496efe-02d9-4859-94ec-90dd72e53d51] - Node d0496efe-02d9-4859-94ec-90dd72e53d51[d0496efe-02d9-4859-94ec-90dd72e53d51] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:49:31.807 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 15:49:31.807 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:49:31.808 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:49:31.808 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 15:49:31.809 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 15:49:31.809 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 26 ms 
[INFO ] 2024-04-01 15:49:31.855 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:49:31.855 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:49:31.855 - [任务 36(100)][6b782c50-1bb1-45be-a7c9-b6693e17f1c5] - Node 6b782c50-1bb1-45be-a7c9-b6693e17f1c5[6b782c50-1bb1-45be-a7c9-b6693e17f1c5] start preload schema,table counts: 0 
[INFO ] 2024-04-01 15:49:31.855 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:49:31.855 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:49:31.855 - [任务 36(100)][6b782c50-1bb1-45be-a7c9-b6693e17f1c5] - Node 6b782c50-1bb1-45be-a7c9-b6693e17f1c5[6b782c50-1bb1-45be-a7c9-b6693e17f1c5] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:49:32.144 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 15:49:32.144 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:49:32.144 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:49:32.144 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 15:49:32.144 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 15:49:32.144 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 7 ms 
[INFO ] 2024-04-01 15:49:32.313 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 15:49:32.313 - [任务 36(100)][2e5c6522-3053-4dfb-9c0b-1c5a88788cfe] - Node 2e5c6522-3053-4dfb-9c0b-1c5a88788cfe[2e5c6522-3053-4dfb-9c0b-1c5a88788cfe] running status set to false 
[INFO ] 2024-04-01 15:49:32.313 - [任务 36(100)][2e5c6522-3053-4dfb-9c0b-1c5a88788cfe] - Node 2e5c6522-3053-4dfb-9c0b-1c5a88788cfe[2e5c6522-3053-4dfb-9c0b-1c5a88788cfe] schema data cleaned 
[INFO ] 2024-04-01 15:49:32.314 - [任务 36(100)][2e5c6522-3053-4dfb-9c0b-1c5a88788cfe] - Node 2e5c6522-3053-4dfb-9c0b-1c5a88788cfe[2e5c6522-3053-4dfb-9c0b-1c5a88788cfe] monitor closed 
[INFO ] 2024-04-01 15:49:32.314 - [任务 36(100)][2e5c6522-3053-4dfb-9c0b-1c5a88788cfe] - Node 2e5c6522-3053-4dfb-9c0b-1c5a88788cfe[2e5c6522-3053-4dfb-9c0b-1c5a88788cfe] close complete, cost 2 ms 
[INFO ] 2024-04-01 15:49:32.316 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-aa5c5863-e4af-4dbc-b91c-eaf1485c18c7 
[INFO ] 2024-04-01 15:49:32.316 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-aa5c5863-e4af-4dbc-b91c-eaf1485c18c7 
[INFO ] 2024-04-01 15:49:32.317 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 15:49:32.318 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 15:49:32.318 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 15:49:32.318 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 12 ms 
[INFO ] 2024-04-01 15:49:32.460 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-2e5c6522-3053-4dfb-9c0b-1c5a88788cfe complete, cost 1008ms 
[INFO ] 2024-04-01 15:49:32.460 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 15:49:32.463 - [任务 36(100)][6b782c50-1bb1-45be-a7c9-b6693e17f1c5] - Node 6b782c50-1bb1-45be-a7c9-b6693e17f1c5[6b782c50-1bb1-45be-a7c9-b6693e17f1c5] running status set to false 
[INFO ] 2024-04-01 15:49:32.463 - [任务 36(100)][6b782c50-1bb1-45be-a7c9-b6693e17f1c5] - Node 6b782c50-1bb1-45be-a7c9-b6693e17f1c5[6b782c50-1bb1-45be-a7c9-b6693e17f1c5] schema data cleaned 
[INFO ] 2024-04-01 15:49:32.463 - [任务 36(100)][6b782c50-1bb1-45be-a7c9-b6693e17f1c5] - Node 6b782c50-1bb1-45be-a7c9-b6693e17f1c5[6b782c50-1bb1-45be-a7c9-b6693e17f1c5] monitor closed 
[INFO ] 2024-04-01 15:49:32.468 - [任务 36(100)][6b782c50-1bb1-45be-a7c9-b6693e17f1c5] - Node 6b782c50-1bb1-45be-a7c9-b6693e17f1c5[6b782c50-1bb1-45be-a7c9-b6693e17f1c5] close complete, cost 5 ms 
[INFO ] 2024-04-01 15:49:32.468 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-dc1bffed-5d81-4fbe-bc3b-e66553ed5d9a 
[INFO ] 2024-04-01 15:49:32.468 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-dc1bffed-5d81-4fbe-bc3b-e66553ed5d9a 
[INFO ] 2024-04-01 15:49:32.468 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 15:49:32.470 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 15:49:32.470 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 15:49:32.473 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 17 ms 
[INFO ] 2024-04-01 15:49:32.473 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-6b782c50-1bb1-45be-a7c9-b6693e17f1c5 complete, cost 652ms 
[INFO ] 2024-04-01 15:49:32.600 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 15:49:32.600 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:49:32.600 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 15:49:32.600 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:49:32.600 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 15:49:32.601 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 15:49:32.602 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 44 ms 
[INFO ] 2024-04-01 15:49:32.602 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-6565d19a-bee7-43da-bf76-95f9f3261199 
[INFO ] 2024-04-01 15:49:32.602 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-6565d19a-bee7-43da-bf76-95f9f3261199 
[INFO ] 2024-04-01 15:49:32.602 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 15:49:32.603 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 15:49:32.603 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 15:49:32.604 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 4 ms 
[INFO ] 2024-04-01 15:49:32.605 - [任务 36(100)][d0496efe-02d9-4859-94ec-90dd72e53d51] - Node d0496efe-02d9-4859-94ec-90dd72e53d51[d0496efe-02d9-4859-94ec-90dd72e53d51] running status set to false 
[INFO ] 2024-04-01 15:49:32.605 - [任务 36(100)][d0496efe-02d9-4859-94ec-90dd72e53d51] - Node d0496efe-02d9-4859-94ec-90dd72e53d51[d0496efe-02d9-4859-94ec-90dd72e53d51] schema data cleaned 
[INFO ] 2024-04-01 15:49:32.605 - [任务 36(100)][d0496efe-02d9-4859-94ec-90dd72e53d51] - Node d0496efe-02d9-4859-94ec-90dd72e53d51[d0496efe-02d9-4859-94ec-90dd72e53d51] monitor closed 
[INFO ] 2024-04-01 15:49:32.605 - [任务 36(100)][d0496efe-02d9-4859-94ec-90dd72e53d51] - Node d0496efe-02d9-4859-94ec-90dd72e53d51[d0496efe-02d9-4859-94ec-90dd72e53d51] close complete, cost 0 ms 
[INFO ] 2024-04-01 15:49:32.606 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-d0496efe-02d9-4859-94ec-90dd72e53d51 complete, cost 1159ms 
[INFO ] 2024-04-01 15:49:34.419 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:49:34.419 - [任务 36(100)][73ff669e-5cd2-4bfa-9298-a7b09618897a] - Node 73ff669e-5cd2-4bfa-9298-a7b09618897a[73ff669e-5cd2-4bfa-9298-a7b09618897a] start preload schema,table counts: 0 
[INFO ] 2024-04-01 15:49:34.419 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:49:34.419 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:49:34.419 - [任务 36(100)][73ff669e-5cd2-4bfa-9298-a7b09618897a] - Node 73ff669e-5cd2-4bfa-9298-a7b09618897a[73ff669e-5cd2-4bfa-9298-a7b09618897a] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:49:34.419 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:49:34.687 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 15:49:34.698 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:49:34.698 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:49:34.698 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 15:49:34.698 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 15:49:34.904 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 13 ms 
[INFO ] 2024-04-01 15:49:34.947 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 15:49:34.947 - [任务 36(100)][73ff669e-5cd2-4bfa-9298-a7b09618897a] - Node 73ff669e-5cd2-4bfa-9298-a7b09618897a[73ff669e-5cd2-4bfa-9298-a7b09618897a] running status set to false 
[INFO ] 2024-04-01 15:49:34.947 - [任务 36(100)][73ff669e-5cd2-4bfa-9298-a7b09618897a] - Node 73ff669e-5cd2-4bfa-9298-a7b09618897a[73ff669e-5cd2-4bfa-9298-a7b09618897a] schema data cleaned 
[INFO ] 2024-04-01 15:49:34.947 - [任务 36(100)][73ff669e-5cd2-4bfa-9298-a7b09618897a] - Node 73ff669e-5cd2-4bfa-9298-a7b09618897a[73ff669e-5cd2-4bfa-9298-a7b09618897a] monitor closed 
[INFO ] 2024-04-01 15:49:34.947 - [任务 36(100)][73ff669e-5cd2-4bfa-9298-a7b09618897a] - Node 73ff669e-5cd2-4bfa-9298-a7b09618897a[73ff669e-5cd2-4bfa-9298-a7b09618897a] close complete, cost 1 ms 
[INFO ] 2024-04-01 15:49:34.953 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-b53f9f3e-5ada-4a77-b4c5-4bd41dd07835 
[INFO ] 2024-04-01 15:49:34.954 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-b53f9f3e-5ada-4a77-b4c5-4bd41dd07835 
[INFO ] 2024-04-01 15:49:34.954 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 15:49:34.955 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 15:49:34.955 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 15:49:34.955 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 14 ms 
[INFO ] 2024-04-01 15:49:35.167 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-73ff669e-5cd2-4bfa-9298-a7b09618897a complete, cost 584ms 
[INFO ] 2024-04-01 15:49:38.566 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:49:38.566 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:49:38.567 - [任务 36(100)][78aad50b-95a2-4d62-90fc-69452a4a4116] - Node 78aad50b-95a2-4d62-90fc-69452a4a4116[78aad50b-95a2-4d62-90fc-69452a4a4116] start preload schema,table counts: 0 
[INFO ] 2024-04-01 15:49:38.568 - [任务 36(100)][78aad50b-95a2-4d62-90fc-69452a4a4116] - Node 78aad50b-95a2-4d62-90fc-69452a4a4116[78aad50b-95a2-4d62-90fc-69452a4a4116] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:49:38.568 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:49:38.568 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:49:38.837 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 15:49:38.849 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:49:38.849 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:49:38.849 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 15:49:38.849 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 15:49:39.056 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 15 ms 
[INFO ] 2024-04-01 15:49:39.067 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 15:49:39.067 - [任务 36(100)][78aad50b-95a2-4d62-90fc-69452a4a4116] - Node 78aad50b-95a2-4d62-90fc-69452a4a4116[78aad50b-95a2-4d62-90fc-69452a4a4116] running status set to false 
[INFO ] 2024-04-01 15:49:39.067 - [任务 36(100)][78aad50b-95a2-4d62-90fc-69452a4a4116] - Node 78aad50b-95a2-4d62-90fc-69452a4a4116[78aad50b-95a2-4d62-90fc-69452a4a4116] schema data cleaned 
[INFO ] 2024-04-01 15:49:39.067 - [任务 36(100)][78aad50b-95a2-4d62-90fc-69452a4a4116] - Node 78aad50b-95a2-4d62-90fc-69452a4a4116[78aad50b-95a2-4d62-90fc-69452a4a4116] monitor closed 
[INFO ] 2024-04-01 15:49:39.071 - [任务 36(100)][78aad50b-95a2-4d62-90fc-69452a4a4116] - Node 78aad50b-95a2-4d62-90fc-69452a4a4116[78aad50b-95a2-4d62-90fc-69452a4a4116] close complete, cost 2 ms 
[INFO ] 2024-04-01 15:49:39.071 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-41607e50-dd70-4d59-a0fd-79577a71594a 
[INFO ] 2024-04-01 15:49:39.071 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-41607e50-dd70-4d59-a0fd-79577a71594a 
[INFO ] 2024-04-01 15:49:39.072 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 15:49:39.074 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 15:49:39.074 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 15:49:39.076 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 10 ms 
[INFO ] 2024-04-01 15:49:39.076 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-78aad50b-95a2-4d62-90fc-69452a4a4116 complete, cost 570ms 
[INFO ] 2024-04-01 15:49:50.992 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:49:50.993 - [任务 36(100)][c823266c-7055-431e-b787-58126feeb12b] - Node c823266c-7055-431e-b787-58126feeb12b[c823266c-7055-431e-b787-58126feeb12b] start preload schema,table counts: 0 
[INFO ] 2024-04-01 15:49:50.993 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:49:50.993 - [任务 36(100)][c823266c-7055-431e-b787-58126feeb12b] - Node c823266c-7055-431e-b787-58126feeb12b[c823266c-7055-431e-b787-58126feeb12b] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:49:50.993 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:49:50.993 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:49:51.602 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 15:49:51.628 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:49:51.629 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:49:51.629 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 15:49:51.629 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 15:49:51.630 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 29 ms 
[INFO ] 2024-04-01 15:49:51.640 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 15:49:51.640 - [任务 36(100)][c823266c-7055-431e-b787-58126feeb12b] - Node c823266c-7055-431e-b787-58126feeb12b[c823266c-7055-431e-b787-58126feeb12b] running status set to false 
[INFO ] 2024-04-01 15:49:51.640 - [任务 36(100)][c823266c-7055-431e-b787-58126feeb12b] - Node c823266c-7055-431e-b787-58126feeb12b[c823266c-7055-431e-b787-58126feeb12b] schema data cleaned 
[INFO ] 2024-04-01 15:49:51.640 - [任务 36(100)][c823266c-7055-431e-b787-58126feeb12b] - Node c823266c-7055-431e-b787-58126feeb12b[c823266c-7055-431e-b787-58126feeb12b] monitor closed 
[INFO ] 2024-04-01 15:49:51.640 - [任务 36(100)][c823266c-7055-431e-b787-58126feeb12b] - Node c823266c-7055-431e-b787-58126feeb12b[c823266c-7055-431e-b787-58126feeb12b] close complete, cost 2 ms 
[INFO ] 2024-04-01 15:49:51.642 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-03cf17d9-9fbf-4f6e-b2eb-b8b15195a2b7 
[INFO ] 2024-04-01 15:49:51.642 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-03cf17d9-9fbf-4f6e-b2eb-b8b15195a2b7 
[INFO ] 2024-04-01 15:49:51.645 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 15:49:51.645 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 15:49:51.645 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 15:49:51.645 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 12 ms 
[INFO ] 2024-04-01 15:49:51.851 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-c823266c-7055-431e-b787-58126feeb12b complete, cost 710ms 
[INFO ] 2024-04-01 15:55:23.607 - [任务 36(100)][53c877d0-b8c5-44c3-b12a-e1f9e8b17879] - Node 53c877d0-b8c5-44c3-b12a-e1f9e8b17879[53c877d0-b8c5-44c3-b12a-e1f9e8b17879] start preload schema,table counts: 0 
[INFO ] 2024-04-01 15:55:23.607 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:55:23.607 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:55:23.608 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:55:23.608 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:55:23.608 - [任务 36(100)][53c877d0-b8c5-44c3-b12a-e1f9e8b17879] - Node 53c877d0-b8c5-44c3-b12a-e1f9e8b17879[53c877d0-b8c5-44c3-b12a-e1f9e8b17879] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:55:23.918 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 15:55:23.922 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:55:23.922 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:55:23.923 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 15:55:23.926 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 15:55:23.927 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 13 ms 
[INFO ] 2024-04-01 15:55:24.158 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 15:55:24.162 - [任务 36(100)][53c877d0-b8c5-44c3-b12a-e1f9e8b17879] - Node 53c877d0-b8c5-44c3-b12a-e1f9e8b17879[53c877d0-b8c5-44c3-b12a-e1f9e8b17879] running status set to false 
[INFO ] 2024-04-01 15:55:24.162 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-3a874ab1-8bd5-4a0d-961d-ee2bc93379b3 
[INFO ] 2024-04-01 15:55:24.162 - [任务 36(100)][53c877d0-b8c5-44c3-b12a-e1f9e8b17879] - Node 53c877d0-b8c5-44c3-b12a-e1f9e8b17879[53c877d0-b8c5-44c3-b12a-e1f9e8b17879] schema data cleaned 
[INFO ] 2024-04-01 15:55:24.163 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-3a874ab1-8bd5-4a0d-961d-ee2bc93379b3 
[INFO ] 2024-04-01 15:55:24.164 - [任务 36(100)][53c877d0-b8c5-44c3-b12a-e1f9e8b17879] - Node 53c877d0-b8c5-44c3-b12a-e1f9e8b17879[53c877d0-b8c5-44c3-b12a-e1f9e8b17879] monitor closed 
[INFO ] 2024-04-01 15:55:24.164 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 15:55:24.164 - [任务 36(100)][53c877d0-b8c5-44c3-b12a-e1f9e8b17879] - Node 53c877d0-b8c5-44c3-b12a-e1f9e8b17879[53c877d0-b8c5-44c3-b12a-e1f9e8b17879] close complete, cost 8 ms 
[INFO ] 2024-04-01 15:55:24.165 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 15:55:24.166 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 15:55:24.166 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 16 ms 
[INFO ] 2024-04-01 15:55:32.151 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-53c877d0-b8c5-44c3-b12a-e1f9e8b17879 complete, cost 777ms 
[INFO ] 2024-04-01 15:55:35.885 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:55:35.886 - [任务 36(100)][3d31d02b-5a63-4643-a94d-bb196c8af1b9] - Node 3d31d02b-5a63-4643-a94d-bb196c8af1b9[3d31d02b-5a63-4643-a94d-bb196c8af1b9] start preload schema,table counts: 0 
[INFO ] 2024-04-01 15:55:35.886 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:55:35.886 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:55:35.886 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 15:55:35.887 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:55:35.887 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:55:35.887 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:55:35.887 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:55:35.894 - [任务 36(100)][3d31d02b-5a63-4643-a94d-bb196c8af1b9] - Node 3d31d02b-5a63-4643-a94d-bb196c8af1b9[3d31d02b-5a63-4643-a94d-bb196c8af1b9] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:55:35.894 - [任务 36(100)][a2c1f11f-a0b6-4420-bd46-5e1b2c008de4] - Node a2c1f11f-a0b6-4420-bd46-5e1b2c008de4[a2c1f11f-a0b6-4420-bd46-5e1b2c008de4] start preload schema,table counts: 0 
[INFO ] 2024-04-01 15:55:35.894 - [任务 36(100)][a2c1f11f-a0b6-4420-bd46-5e1b2c008de4] - Node a2c1f11f-a0b6-4420-bd46-5e1b2c008de4[a2c1f11f-a0b6-4420-bd46-5e1b2c008de4] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 15:55:36.267 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 15:55:36.273 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:55:36.273 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:55:36.274 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 15:55:36.274 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 15:55:36.476 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 30 ms 
[INFO ] 2024-04-01 15:55:36.717 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 15:55:36.717 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-a23fd18a-4ba7-432c-8b11-5de06a8fc6f1 
[INFO ] 2024-04-01 15:55:36.717 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-a23fd18a-4ba7-432c-8b11-5de06a8fc6f1 
[INFO ] 2024-04-01 15:55:36.717 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 15:55:36.723 - [任务 36(100)][a2c1f11f-a0b6-4420-bd46-5e1b2c008de4] - Node a2c1f11f-a0b6-4420-bd46-5e1b2c008de4[a2c1f11f-a0b6-4420-bd46-5e1b2c008de4] running status set to false 
[INFO ] 2024-04-01 15:55:36.723 - [任务 36(100)][a2c1f11f-a0b6-4420-bd46-5e1b2c008de4] - Node a2c1f11f-a0b6-4420-bd46-5e1b2c008de4[a2c1f11f-a0b6-4420-bd46-5e1b2c008de4] schema data cleaned 
[INFO ] 2024-04-01 15:55:36.724 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 15:55:36.724 - [任务 36(100)][a2c1f11f-a0b6-4420-bd46-5e1b2c008de4] - Node a2c1f11f-a0b6-4420-bd46-5e1b2c008de4[a2c1f11f-a0b6-4420-bd46-5e1b2c008de4] monitor closed 
[INFO ] 2024-04-01 15:55:36.724 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 15:55:36.725 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 11 ms 
[INFO ] 2024-04-01 15:55:36.725 - [任务 36(100)][a2c1f11f-a0b6-4420-bd46-5e1b2c008de4] - Node a2c1f11f-a0b6-4420-bd46-5e1b2c008de4[a2c1f11f-a0b6-4420-bd46-5e1b2c008de4] close complete, cost 2 ms 
[INFO ] 2024-04-01 15:55:38.311 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-a2c1f11f-a0b6-4420-bd46-5e1b2c008de4 complete, cost 974ms 
[INFO ] 2024-04-01 15:55:38.471 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 15:55:38.497 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:55:38.497 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 15:55:38.497 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 15:55:38.497 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 15:55:38.503 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 51 ms 
[INFO ] 2024-04-01 15:55:38.503 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 15:55:38.505 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-5864c07e-bf22-4faa-aed6-0bf684d32ec8 
[INFO ] 2024-04-01 15:55:38.505 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-5864c07e-bf22-4faa-aed6-0bf684d32ec8 
[INFO ] 2024-04-01 15:55:38.505 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 15:55:38.507 - [任务 36(100)][3d31d02b-5a63-4643-a94d-bb196c8af1b9] - Node 3d31d02b-5a63-4643-a94d-bb196c8af1b9[3d31d02b-5a63-4643-a94d-bb196c8af1b9] running status set to false 
[INFO ] 2024-04-01 15:55:38.507 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 15:55:38.508 - [任务 36(100)][3d31d02b-5a63-4643-a94d-bb196c8af1b9] - Node 3d31d02b-5a63-4643-a94d-bb196c8af1b9[3d31d02b-5a63-4643-a94d-bb196c8af1b9] schema data cleaned 
[INFO ] 2024-04-01 15:55:38.508 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 15:55:38.508 - [任务 36(100)][3d31d02b-5a63-4643-a94d-bb196c8af1b9] - Node 3d31d02b-5a63-4643-a94d-bb196c8af1b9[3d31d02b-5a63-4643-a94d-bb196c8af1b9] monitor closed 
[INFO ] 2024-04-01 15:55:38.508 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 4 ms 
[INFO ] 2024-04-01 15:55:38.508 - [任务 36(100)][3d31d02b-5a63-4643-a94d-bb196c8af1b9] - Node 3d31d02b-5a63-4643-a94d-bb196c8af1b9[3d31d02b-5a63-4643-a94d-bb196c8af1b9] close complete, cost 0 ms 
[INFO ] 2024-04-01 15:55:39.817 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-3d31d02b-5a63-4643-a94d-bb196c8af1b9 complete, cost 2756ms 
[INFO ] 2024-04-01 16:15:32.912 - [任务 36(100)][684040b9-89ff-4edc-9fe2-961abecdf642] - Node 684040b9-89ff-4edc-9fe2-961abecdf642[684040b9-89ff-4edc-9fe2-961abecdf642] start preload schema,table counts: 0 
[INFO ] 2024-04-01 16:15:32.913 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:15:32.913 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:15:32.914 - [任务 36(100)][684040b9-89ff-4edc-9fe2-961abecdf642] - Node 684040b9-89ff-4edc-9fe2-961abecdf642[684040b9-89ff-4edc-9fe2-961abecdf642] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:15:32.914 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 16:15:32.918 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:15:33.086 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 16:15:33.086 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:15:33.087 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:15:33.087 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 16:15:33.087 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 16:15:33.087 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 29 ms 
[INFO ] 2024-04-01 16:15:33.317 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 16:15:33.326 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-deb39a51-4d73-431a-8470-093410f51773 
[INFO ] 2024-04-01 16:15:33.326 - [任务 36(100)][684040b9-89ff-4edc-9fe2-961abecdf642] - Node 684040b9-89ff-4edc-9fe2-961abecdf642[684040b9-89ff-4edc-9fe2-961abecdf642] running status set to false 
[INFO ] 2024-04-01 16:15:33.326 - [任务 36(100)][684040b9-89ff-4edc-9fe2-961abecdf642] - Node 684040b9-89ff-4edc-9fe2-961abecdf642[684040b9-89ff-4edc-9fe2-961abecdf642] schema data cleaned 
[INFO ] 2024-04-01 16:15:33.327 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-deb39a51-4d73-431a-8470-093410f51773 
[INFO ] 2024-04-01 16:15:33.327 - [任务 36(100)][684040b9-89ff-4edc-9fe2-961abecdf642] - Node 684040b9-89ff-4edc-9fe2-961abecdf642[684040b9-89ff-4edc-9fe2-961abecdf642] monitor closed 
[INFO ] 2024-04-01 16:15:33.327 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 16:15:33.327 - [任务 36(100)][684040b9-89ff-4edc-9fe2-961abecdf642] - Node 684040b9-89ff-4edc-9fe2-961abecdf642[684040b9-89ff-4edc-9fe2-961abecdf642] close complete, cost 4 ms 
[INFO ] 2024-04-01 16:15:33.329 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 16:15:33.329 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 16:15:33.333 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 14 ms 
[INFO ] 2024-04-01 16:15:33.334 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-684040b9-89ff-4edc-9fe2-961abecdf642 complete, cost 839ms 
[INFO ] 2024-04-01 16:17:07.464 - [任务 36(100)][591110a9-7439-4d8d-8913-8ae60a855a9c] - Node 591110a9-7439-4d8d-8913-8ae60a855a9c[591110a9-7439-4d8d-8913-8ae60a855a9c] start preload schema,table counts: 0 
[INFO ] 2024-04-01 16:17:07.465 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:17:07.465 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:17:07.466 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:17:07.466 - [任务 36(100)][591110a9-7439-4d8d-8913-8ae60a855a9c] - Node 591110a9-7439-4d8d-8913-8ae60a855a9c[591110a9-7439-4d8d-8913-8ae60a855a9c] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:17:07.466 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:17:07.812 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 16:17:07.812 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:17:07.812 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:17:07.813 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 16:17:07.813 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 16:17:08.018 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 29 ms 
[INFO ] 2024-04-01 16:17:08.030 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 16:17:08.030 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-9ff78cb9-18ef-4e0a-9098-bcc06cf4a114 
[INFO ] 2024-04-01 16:17:08.030 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-9ff78cb9-18ef-4e0a-9098-bcc06cf4a114 
[INFO ] 2024-04-01 16:17:08.030 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 16:17:08.034 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 16:17:08.034 - [任务 36(100)][591110a9-7439-4d8d-8913-8ae60a855a9c] - Node 591110a9-7439-4d8d-8913-8ae60a855a9c[591110a9-7439-4d8d-8913-8ae60a855a9c] running status set to false 
[INFO ] 2024-04-01 16:17:08.034 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 16:17:08.035 - [任务 36(100)][591110a9-7439-4d8d-8913-8ae60a855a9c] - Node 591110a9-7439-4d8d-8913-8ae60a855a9c[591110a9-7439-4d8d-8913-8ae60a855a9c] schema data cleaned 
[INFO ] 2024-04-01 16:17:08.035 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 10 ms 
[INFO ] 2024-04-01 16:17:08.035 - [任务 36(100)][591110a9-7439-4d8d-8913-8ae60a855a9c] - Node 591110a9-7439-4d8d-8913-8ae60a855a9c[591110a9-7439-4d8d-8913-8ae60a855a9c] monitor closed 
[INFO ] 2024-04-01 16:17:08.038 - [任务 36(100)][591110a9-7439-4d8d-8913-8ae60a855a9c] - Node 591110a9-7439-4d8d-8913-8ae60a855a9c[591110a9-7439-4d8d-8913-8ae60a855a9c] close complete, cost 3 ms 
[INFO ] 2024-04-01 16:17:08.241 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-591110a9-7439-4d8d-8913-8ae60a855a9c complete, cost 686ms 
[INFO ] 2024-04-01 16:33:40.277 - [任务 36(100)][fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff] - Node fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff[fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff] start preload schema,table counts: 0 
[INFO ] 2024-04-01 16:33:40.279 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:33:40.279 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:33:40.279 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:33:40.279 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:33:40.279 - [任务 36(100)][fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff] - Node fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff[fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:33:40.593 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 16:33:40.593 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:33:40.593 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:33:40.594 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 16:33:40.594 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 16:33:40.594 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 13 ms 
[INFO ] 2024-04-01 16:33:40.815 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 16:33:40.820 - [任务 36(100)][fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff] - Node fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff[fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff] running status set to false 
[INFO ] 2024-04-01 16:33:40.820 - [任务 36(100)][fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff] - Node fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff[fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff] schema data cleaned 
[INFO ] 2024-04-01 16:33:40.822 - [任务 36(100)][fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff] - Node fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff[fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff] monitor closed 
[INFO ] 2024-04-01 16:33:40.823 - [任务 36(100)][fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff] - Node fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff[fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff] close complete, cost 6 ms 
[INFO ] 2024-04-01 16:33:40.827 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-6f16056c-77e5-42a4-9738-f44fa4f83a4d 
[INFO ] 2024-04-01 16:33:40.827 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-6f16056c-77e5-42a4-9738-f44fa4f83a4d 
[INFO ] 2024-04-01 16:33:40.830 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 16:33:40.830 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 16:33:40.830 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 16:33:40.830 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 17 ms 
[INFO ] 2024-04-01 16:33:41.034 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-fc5ed3c4-fa8e-4b98-a349-f5f38d7908ff complete, cost 658ms 
[INFO ] 2024-04-01 16:34:15.831 - [任务 36(100)][bf25d0a7-8482-4250-a33f-a5630615a90c] - Node bf25d0a7-8482-4250-a33f-a5630615a90c[bf25d0a7-8482-4250-a33f-a5630615a90c] start preload schema,table counts: 0 
[INFO ] 2024-04-01 16:34:15.832 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:34:15.833 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:34:15.838 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 16:34:15.839 - [任务 36(100)][bf25d0a7-8482-4250-a33f-a5630615a90c] - Node bf25d0a7-8482-4250-a33f-a5630615a90c[bf25d0a7-8482-4250-a33f-a5630615a90c] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:34:15.840 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:34:16.139 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 16:34:16.148 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:34:16.148 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:34:16.148 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 16:34:16.148 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 16:34:16.349 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 14 ms 
[INFO ] 2024-04-01 16:34:16.375 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 16:34:16.375 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-e4feceb5-955b-4afa-8fa2-0b9be0e5d645 
[INFO ] 2024-04-01 16:34:16.375 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-e4feceb5-955b-4afa-8fa2-0b9be0e5d645 
[INFO ] 2024-04-01 16:34:16.375 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 16:34:16.376 - [任务 36(100)][bf25d0a7-8482-4250-a33f-a5630615a90c] - Node bf25d0a7-8482-4250-a33f-a5630615a90c[bf25d0a7-8482-4250-a33f-a5630615a90c] running status set to false 
[INFO ] 2024-04-01 16:34:16.378 - [任务 36(100)][bf25d0a7-8482-4250-a33f-a5630615a90c] - Node bf25d0a7-8482-4250-a33f-a5630615a90c[bf25d0a7-8482-4250-a33f-a5630615a90c] schema data cleaned 
[INFO ] 2024-04-01 16:34:16.379 - [任务 36(100)][bf25d0a7-8482-4250-a33f-a5630615a90c] - Node bf25d0a7-8482-4250-a33f-a5630615a90c[bf25d0a7-8482-4250-a33f-a5630615a90c] monitor closed 
[INFO ] 2024-04-01 16:34:16.379 - [任务 36(100)][bf25d0a7-8482-4250-a33f-a5630615a90c] - Node bf25d0a7-8482-4250-a33f-a5630615a90c[bf25d0a7-8482-4250-a33f-a5630615a90c] close complete, cost 3 ms 
[INFO ] 2024-04-01 16:34:16.380 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 16:34:16.380 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 16:34:16.381 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 9 ms 
[INFO ] 2024-04-01 16:34:16.381 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-bf25d0a7-8482-4250-a33f-a5630615a90c complete, cost 660ms 
[INFO ] 2024-04-01 16:34:17.277 - [任务 36(100)][8b58e80a-015e-458f-b39a-969b41b78823] - Node 8b58e80a-015e-458f-b39a-969b41b78823[8b58e80a-015e-458f-b39a-969b41b78823] start preload schema,table counts: 0 
[INFO ] 2024-04-01 16:34:17.278 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:34:17.278 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:34:17.278 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 2 ms 
[INFO ] 2024-04-01 16:34:17.278 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 3 ms 
[INFO ] 2024-04-01 16:34:17.278 - [任务 36(100)][8b58e80a-015e-458f-b39a-969b41b78823] - Node 8b58e80a-015e-458f-b39a-969b41b78823[8b58e80a-015e-458f-b39a-969b41b78823] preload schema finished, cost 2 ms 
[INFO ] 2024-04-01 16:34:17.616 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 16:34:17.629 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:34:17.629 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:34:17.629 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 16:34:17.629 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 16:34:17.834 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 22 ms 
[INFO ] 2024-04-01 16:34:17.851 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 16:34:17.852 - [任务 36(100)][8b58e80a-015e-458f-b39a-969b41b78823] - Node 8b58e80a-015e-458f-b39a-969b41b78823[8b58e80a-015e-458f-b39a-969b41b78823] running status set to false 
[INFO ] 2024-04-01 16:34:17.852 - [任务 36(100)][8b58e80a-015e-458f-b39a-969b41b78823] - Node 8b58e80a-015e-458f-b39a-969b41b78823[8b58e80a-015e-458f-b39a-969b41b78823] schema data cleaned 
[INFO ] 2024-04-01 16:34:17.852 - [任务 36(100)][8b58e80a-015e-458f-b39a-969b41b78823] - Node 8b58e80a-015e-458f-b39a-969b41b78823[8b58e80a-015e-458f-b39a-969b41b78823] monitor closed 
[INFO ] 2024-04-01 16:34:17.858 - [任务 36(100)][8b58e80a-015e-458f-b39a-969b41b78823] - Node 8b58e80a-015e-458f-b39a-969b41b78823[8b58e80a-015e-458f-b39a-969b41b78823] close complete, cost 1 ms 
[INFO ] 2024-04-01 16:34:17.859 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-0894e0ce-b694-49f3-b6f9-908eb8dd754b 
[INFO ] 2024-04-01 16:34:17.859 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-0894e0ce-b694-49f3-b6f9-908eb8dd754b 
[INFO ] 2024-04-01 16:34:17.859 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 16:34:17.861 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 16:34:17.862 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 16:34:17.862 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 13 ms 
[INFO ] 2024-04-01 16:34:18.066 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-8b58e80a-015e-458f-b39a-969b41b78823 complete, cost 685ms 
[INFO ] 2024-04-01 16:36:01.841 - [任务 36(100)][e5f18f70-8bde-4e32-a39e-ff0b5526fb6e] - Node e5f18f70-8bde-4e32-a39e-ff0b5526fb6e[e5f18f70-8bde-4e32-a39e-ff0b5526fb6e] start preload schema,table counts: 0 
[INFO ] 2024-04-01 16:36:01.843 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:36:01.843 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:36:01.846 - [任务 36(100)][e5f18f70-8bde-4e32-a39e-ff0b5526fb6e] - Node e5f18f70-8bde-4e32-a39e-ff0b5526fb6e[e5f18f70-8bde-4e32-a39e-ff0b5526fb6e] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:36:01.846 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:36:01.846 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 16:36:02.232 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 16:36:02.238 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:36:02.238 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:36:02.239 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 16:36:02.239 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 16:36:02.245 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 24 ms 
[INFO ] 2024-04-01 16:36:02.447 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 16:36:02.450 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-cfcb6c85-ad53-4002-b481-bae0e53b3ed7 
[INFO ] 2024-04-01 16:36:02.450 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-cfcb6c85-ad53-4002-b481-bae0e53b3ed7 
[INFO ] 2024-04-01 16:36:02.451 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 16:36:02.453 - [任务 36(100)][e5f18f70-8bde-4e32-a39e-ff0b5526fb6e] - Node e5f18f70-8bde-4e32-a39e-ff0b5526fb6e[e5f18f70-8bde-4e32-a39e-ff0b5526fb6e] running status set to false 
[INFO ] 2024-04-01 16:36:02.453 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 16:36:02.453 - [任务 36(100)][e5f18f70-8bde-4e32-a39e-ff0b5526fb6e] - Node e5f18f70-8bde-4e32-a39e-ff0b5526fb6e[e5f18f70-8bde-4e32-a39e-ff0b5526fb6e] schema data cleaned 
[INFO ] 2024-04-01 16:36:02.453 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 16:36:02.453 - [任务 36(100)][e5f18f70-8bde-4e32-a39e-ff0b5526fb6e] - Node e5f18f70-8bde-4e32-a39e-ff0b5526fb6e[e5f18f70-8bde-4e32-a39e-ff0b5526fb6e] monitor closed 
[INFO ] 2024-04-01 16:36:02.453 - [任务 36(100)][e5f18f70-8bde-4e32-a39e-ff0b5526fb6e] - Node e5f18f70-8bde-4e32-a39e-ff0b5526fb6e[e5f18f70-8bde-4e32-a39e-ff0b5526fb6e] close complete, cost 1 ms 
[INFO ] 2024-04-01 16:36:02.454 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 7 ms 
[INFO ] 2024-04-01 16:36:03.982 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-e5f18f70-8bde-4e32-a39e-ff0b5526fb6e complete, cost 763ms 
[INFO ] 2024-04-01 16:38:29.947 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:38:29.947 - [任务 36(100)][f1969ad4-3a1c-42ae-9077-606b84986676] - Node f1969ad4-3a1c-42ae-9077-606b84986676[f1969ad4-3a1c-42ae-9077-606b84986676] start preload schema,table counts: 0 
[INFO ] 2024-04-01 16:38:29.947 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:38:29.947 - [任务 36(100)][f1969ad4-3a1c-42ae-9077-606b84986676] - Node f1969ad4-3a1c-42ae-9077-606b84986676[f1969ad4-3a1c-42ae-9077-606b84986676] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:38:29.947 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 4 ms 
[INFO ] 2024-04-01 16:38:29.947 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:38:30.079 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 16:38:30.087 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:38:30.087 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:38:30.088 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 16:38:30.088 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 16:38:30.089 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 21 ms 
[INFO ] 2024-04-01 16:38:30.303 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 16:38:30.308 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-246cc83c-a440-4b96-901b-f7954e54b003 
[INFO ] 2024-04-01 16:38:30.309 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-246cc83c-a440-4b96-901b-f7954e54b003 
[INFO ] 2024-04-01 16:38:30.309 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 16:38:30.312 - [任务 36(100)][f1969ad4-3a1c-42ae-9077-606b84986676] - Node f1969ad4-3a1c-42ae-9077-606b84986676[f1969ad4-3a1c-42ae-9077-606b84986676] running status set to false 
[INFO ] 2024-04-01 16:38:30.312 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 16:38:30.312 - [任务 36(100)][f1969ad4-3a1c-42ae-9077-606b84986676] - Node f1969ad4-3a1c-42ae-9077-606b84986676[f1969ad4-3a1c-42ae-9077-606b84986676] schema data cleaned 
[INFO ] 2024-04-01 16:38:30.312 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 16:38:30.319 - [任务 36(100)][f1969ad4-3a1c-42ae-9077-606b84986676] - Node f1969ad4-3a1c-42ae-9077-606b84986676[f1969ad4-3a1c-42ae-9077-606b84986676] monitor closed 
[INFO ] 2024-04-01 16:38:30.319 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 11 ms 
[INFO ] 2024-04-01 16:38:30.319 - [任务 36(100)][f1969ad4-3a1c-42ae-9077-606b84986676] - Node f1969ad4-3a1c-42ae-9077-606b84986676[f1969ad4-3a1c-42ae-9077-606b84986676] close complete, cost 5 ms 
[INFO ] 2024-04-01 16:38:40.576 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-f1969ad4-3a1c-42ae-9077-606b84986676 complete, cost 748ms 
[INFO ] 2024-04-01 16:44:31.229 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:44:31.234 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:44:31.238 - [任务 36(100)][64bbd778-2e94-4ab2-9dc4-cef2c17beb4e] - Node 64bbd778-2e94-4ab2-9dc4-cef2c17beb4e[64bbd778-2e94-4ab2-9dc4-cef2c17beb4e] start preload schema,table counts: 0 
[INFO ] 2024-04-01 16:44:31.238 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 16:44:31.238 - [任务 36(100)][64bbd778-2e94-4ab2-9dc4-cef2c17beb4e] - Node 64bbd778-2e94-4ab2-9dc4-cef2c17beb4e[64bbd778-2e94-4ab2-9dc4-cef2c17beb4e] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:44:31.238 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:44:31.525 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 16:44:31.542 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:44:31.548 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:44:31.548 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 16:44:31.548 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 16:44:31.548 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 23 ms 
[INFO ] 2024-04-01 16:44:31.784 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 16:44:31.787 - [任务 36(100)][64bbd778-2e94-4ab2-9dc4-cef2c17beb4e] - Node 64bbd778-2e94-4ab2-9dc4-cef2c17beb4e[64bbd778-2e94-4ab2-9dc4-cef2c17beb4e] running status set to false 
[INFO ] 2024-04-01 16:44:31.787 - [任务 36(100)][64bbd778-2e94-4ab2-9dc4-cef2c17beb4e] - Node 64bbd778-2e94-4ab2-9dc4-cef2c17beb4e[64bbd778-2e94-4ab2-9dc4-cef2c17beb4e] schema data cleaned 
[INFO ] 2024-04-01 16:44:31.791 - [任务 36(100)][64bbd778-2e94-4ab2-9dc4-cef2c17beb4e] - Node 64bbd778-2e94-4ab2-9dc4-cef2c17beb4e[64bbd778-2e94-4ab2-9dc4-cef2c17beb4e] monitor closed 
[INFO ] 2024-04-01 16:44:31.791 - [任务 36(100)][64bbd778-2e94-4ab2-9dc4-cef2c17beb4e] - Node 64bbd778-2e94-4ab2-9dc4-cef2c17beb4e[64bbd778-2e94-4ab2-9dc4-cef2c17beb4e] close complete, cost 1 ms 
[INFO ] 2024-04-01 16:44:31.793 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-1976ed1f-3901-4d69-8141-e8c364c8192f 
[INFO ] 2024-04-01 16:44:31.793 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-1976ed1f-3901-4d69-8141-e8c364c8192f 
[INFO ] 2024-04-01 16:44:31.796 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 16:44:31.796 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 16:44:31.796 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 16:44:31.796 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 15 ms 
[INFO ] 2024-04-01 16:44:34.737 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-64bbd778-2e94-4ab2-9dc4-cef2c17beb4e complete, cost 761ms 
[INFO ] 2024-04-01 16:49:39.023 - [任务 36(100)][e4f656d5-2d8c-41c6-982e-b83039c2570e] - Node e4f656d5-2d8c-41c6-982e-b83039c2570e[e4f656d5-2d8c-41c6-982e-b83039c2570e] start preload schema,table counts: 0 
[INFO ] 2024-04-01 16:49:39.024 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:49:39.024 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:49:39.024 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 16:49:39.025 - [任务 36(100)][e4f656d5-2d8c-41c6-982e-b83039c2570e] - Node e4f656d5-2d8c-41c6-982e-b83039c2570e[e4f656d5-2d8c-41c6-982e-b83039c2570e] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 16:49:39.025 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:49:39.385 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 16:49:39.391 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:49:39.392 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:49:39.392 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 16:49:39.392 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 16:49:39.595 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 28 ms 
[INFO ] 2024-04-01 16:49:39.620 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 16:49:39.620 - [任务 36(100)][e4f656d5-2d8c-41c6-982e-b83039c2570e] - Node e4f656d5-2d8c-41c6-982e-b83039c2570e[e4f656d5-2d8c-41c6-982e-b83039c2570e] running status set to false 
[INFO ] 2024-04-01 16:49:39.620 - [任务 36(100)][e4f656d5-2d8c-41c6-982e-b83039c2570e] - Node e4f656d5-2d8c-41c6-982e-b83039c2570e[e4f656d5-2d8c-41c6-982e-b83039c2570e] schema data cleaned 
[INFO ] 2024-04-01 16:49:39.621 - [任务 36(100)][e4f656d5-2d8c-41c6-982e-b83039c2570e] - Node e4f656d5-2d8c-41c6-982e-b83039c2570e[e4f656d5-2d8c-41c6-982e-b83039c2570e] monitor closed 
[INFO ] 2024-04-01 16:49:39.624 - [任务 36(100)][e4f656d5-2d8c-41c6-982e-b83039c2570e] - Node e4f656d5-2d8c-41c6-982e-b83039c2570e[e4f656d5-2d8c-41c6-982e-b83039c2570e] close complete, cost 0 ms 
[INFO ] 2024-04-01 16:49:39.624 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-1411c2dd-5a34-4e7f-b7e5-d6cd2be9a718 
[INFO ] 2024-04-01 16:49:39.624 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-1411c2dd-5a34-4e7f-b7e5-d6cd2be9a718 
[INFO ] 2024-04-01 16:49:39.626 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 16:49:39.626 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 16:49:39.626 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 16:49:39.626 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 11 ms 
[INFO ] 2024-04-01 16:49:42.052 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-e4f656d5-2d8c-41c6-982e-b83039c2570e complete, cost 751ms 
[INFO ] 2024-04-01 16:50:58.916 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:50:58.920 - [任务 36(100)][6bf044a4-6913-4fca-94c9-4eec80a7481c] - Node 6bf044a4-6913-4fca-94c9-4eec80a7481c[6bf044a4-6913-4fca-94c9-4eec80a7481c] start preload schema,table counts: 0 
[INFO ] 2024-04-01 16:50:58.920 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:50:58.920 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 7 ms 
[INFO ] 2024-04-01 16:50:58.920 - [任务 36(100)][6bf044a4-6913-4fca-94c9-4eec80a7481c] - Node 6bf044a4-6913-4fca-94c9-4eec80a7481c[6bf044a4-6913-4fca-94c9-4eec80a7481c] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:50:58.920 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:50:59.255 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 16:50:59.255 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:50:59.256 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:50:59.256 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 16:50:59.256 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 16:50:59.464 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 25 ms 
[INFO ] 2024-04-01 16:50:59.495 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 16:50:59.495 - [任务 36(100)][6bf044a4-6913-4fca-94c9-4eec80a7481c] - Node 6bf044a4-6913-4fca-94c9-4eec80a7481c[6bf044a4-6913-4fca-94c9-4eec80a7481c] running status set to false 
[INFO ] 2024-04-01 16:50:59.496 - [任务 36(100)][6bf044a4-6913-4fca-94c9-4eec80a7481c] - Node 6bf044a4-6913-4fca-94c9-4eec80a7481c[6bf044a4-6913-4fca-94c9-4eec80a7481c] schema data cleaned 
[INFO ] 2024-04-01 16:50:59.496 - [任务 36(100)][6bf044a4-6913-4fca-94c9-4eec80a7481c] - Node 6bf044a4-6913-4fca-94c9-4eec80a7481c[6bf044a4-6913-4fca-94c9-4eec80a7481c] monitor closed 
[INFO ] 2024-04-01 16:50:59.496 - [任务 36(100)][6bf044a4-6913-4fca-94c9-4eec80a7481c] - Node 6bf044a4-6913-4fca-94c9-4eec80a7481c[6bf044a4-6913-4fca-94c9-4eec80a7481c] close complete, cost 3 ms 
[INFO ] 2024-04-01 16:50:59.498 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-693e25d5-5e92-4a7b-bd22-6f952fe5d6d7 
[INFO ] 2024-04-01 16:50:59.498 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-693e25d5-5e92-4a7b-bd22-6f952fe5d6d7 
[INFO ] 2024-04-01 16:50:59.499 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 16:50:59.500 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 16:50:59.500 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 16:50:59.502 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 16 ms 
[INFO ] 2024-04-01 16:50:59.502 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-6bf044a4-6913-4fca-94c9-4eec80a7481c complete, cost 802ms 
[INFO ] 2024-04-01 16:51:15.785 - [任务 36(100)][93928f80-03f5-443d-b41f-d707a9f40081] - Node 93928f80-03f5-443d-b41f-d707a9f40081[93928f80-03f5-443d-b41f-d707a9f40081] start preload schema,table counts: 0 
[INFO ] 2024-04-01 16:51:15.785 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:51:15.785 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:51:15.786 - [任务 36(100)][93928f80-03f5-443d-b41f-d707a9f40081] - Node 93928f80-03f5-443d-b41f-d707a9f40081[93928f80-03f5-443d-b41f-d707a9f40081] preload schema finished, cost 2 ms 
[INFO ] 2024-04-01 16:51:15.794 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 16:51:15.796 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:51:16.145 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 16:51:16.152 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:51:16.152 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:51:16.153 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 16:51:16.153 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 16:51:16.354 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 20 ms 
[INFO ] 2024-04-01 16:51:16.395 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 16:51:16.396 - [任务 36(100)][93928f80-03f5-443d-b41f-d707a9f40081] - Node 93928f80-03f5-443d-b41f-d707a9f40081[93928f80-03f5-443d-b41f-d707a9f40081] running status set to false 
[INFO ] 2024-04-01 16:51:16.396 - [任务 36(100)][93928f80-03f5-443d-b41f-d707a9f40081] - Node 93928f80-03f5-443d-b41f-d707a9f40081[93928f80-03f5-443d-b41f-d707a9f40081] schema data cleaned 
[INFO ] 2024-04-01 16:51:16.396 - [任务 36(100)][93928f80-03f5-443d-b41f-d707a9f40081] - Node 93928f80-03f5-443d-b41f-d707a9f40081[93928f80-03f5-443d-b41f-d707a9f40081] monitor closed 
[INFO ] 2024-04-01 16:51:16.396 - [任务 36(100)][93928f80-03f5-443d-b41f-d707a9f40081] - Node 93928f80-03f5-443d-b41f-d707a9f40081[93928f80-03f5-443d-b41f-d707a9f40081] close complete, cost 0 ms 
[INFO ] 2024-04-01 16:51:16.400 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-6a45136d-a656-425a-bab8-01ef312d95e5 
[INFO ] 2024-04-01 16:51:16.400 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-6a45136d-a656-425a-bab8-01ef312d95e5 
[INFO ] 2024-04-01 16:51:16.400 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 16:51:16.402 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 16:51:16.402 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 16:51:16.402 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 10 ms 
[INFO ] 2024-04-01 16:51:31.094 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-93928f80-03f5-443d-b41f-d707a9f40081 complete, cost 734ms 
[INFO ] 2024-04-01 16:51:32.644 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:51:32.644 - [任务 36(100)][9ae117bb-1516-4759-8a68-da073ed4e163] - Node 9ae117bb-1516-4759-8a68-da073ed4e163[9ae117bb-1516-4759-8a68-da073ed4e163] start preload schema,table counts: 0 
[INFO ] 2024-04-01 16:51:32.644 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:51:32.645 - [任务 36(100)][9ae117bb-1516-4759-8a68-da073ed4e163] - Node 9ae117bb-1516-4759-8a68-da073ed4e163[9ae117bb-1516-4759-8a68-da073ed4e163] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:51:32.645 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:51:32.645 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:51:33.091 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 16:51:33.091 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:51:33.091 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:51:33.092 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 16:51:33.092 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 16:51:33.094 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 43 ms 
[INFO ] 2024-04-01 16:51:33.316 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 16:51:33.322 - [任务 36(100)][9ae117bb-1516-4759-8a68-da073ed4e163] - Node 9ae117bb-1516-4759-8a68-da073ed4e163[9ae117bb-1516-4759-8a68-da073ed4e163] running status set to false 
[INFO ] 2024-04-01 16:51:33.322 - [任务 36(100)][9ae117bb-1516-4759-8a68-da073ed4e163] - Node 9ae117bb-1516-4759-8a68-da073ed4e163[9ae117bb-1516-4759-8a68-da073ed4e163] schema data cleaned 
[INFO ] 2024-04-01 16:51:33.322 - [任务 36(100)][9ae117bb-1516-4759-8a68-da073ed4e163] - Node 9ae117bb-1516-4759-8a68-da073ed4e163[9ae117bb-1516-4759-8a68-da073ed4e163] monitor closed 
[INFO ] 2024-04-01 16:51:33.323 - [任务 36(100)][9ae117bb-1516-4759-8a68-da073ed4e163] - Node 9ae117bb-1516-4759-8a68-da073ed4e163[9ae117bb-1516-4759-8a68-da073ed4e163] close complete, cost 0 ms 
[INFO ] 2024-04-01 16:51:33.325 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-8fbb8070-9ce9-4d22-8aa5-c855fd2cbd2a 
[INFO ] 2024-04-01 16:51:33.325 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-8fbb8070-9ce9-4d22-8aa5-c855fd2cbd2a 
[INFO ] 2024-04-01 16:51:33.325 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 16:51:33.327 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 16:51:33.327 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 16:51:33.327 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 11 ms 
[INFO ] 2024-04-01 16:56:33.660 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-9ae117bb-1516-4759-8a68-da073ed4e163 complete, cost 871ms 
[INFO ] 2024-04-01 16:56:52.923 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:56:52.923 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:56:52.924 - [任务 36(100)][49298f02-7c9d-4233-a156-e527827cd894] - Node 49298f02-7c9d-4233-a156-e527827cd894[49298f02-7c9d-4233-a156-e527827cd894] start preload schema,table counts: 0 
[INFO ] 2024-04-01 16:56:52.924 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 16:56:52.924 - [任务 36(100)][49298f02-7c9d-4233-a156-e527827cd894] - Node 49298f02-7c9d-4233-a156-e527827cd894[49298f02-7c9d-4233-a156-e527827cd894] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:56:52.924 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 16:56:53.272 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 16:56:53.288 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:56:53.289 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:56:53.289 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 16:56:53.289 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 16:56:53.290 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 24 ms 
[INFO ] 2024-04-01 16:56:53.519 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 16:56:53.527 - [任务 36(100)][49298f02-7c9d-4233-a156-e527827cd894] - Node 49298f02-7c9d-4233-a156-e527827cd894[49298f02-7c9d-4233-a156-e527827cd894] running status set to false 
[INFO ] 2024-04-01 16:56:53.527 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-cfb6e543-f533-4e40-b84c-27989180fee4 
[INFO ] 2024-04-01 16:56:53.527 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-cfb6e543-f533-4e40-b84c-27989180fee4 
[INFO ] 2024-04-01 16:56:53.527 - [任务 36(100)][49298f02-7c9d-4233-a156-e527827cd894] - Node 49298f02-7c9d-4233-a156-e527827cd894[49298f02-7c9d-4233-a156-e527827cd894] schema data cleaned 
[INFO ] 2024-04-01 16:56:53.528 - [任务 36(100)][49298f02-7c9d-4233-a156-e527827cd894] - Node 49298f02-7c9d-4233-a156-e527827cd894[49298f02-7c9d-4233-a156-e527827cd894] monitor closed 
[INFO ] 2024-04-01 16:56:53.528 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 16:56:53.528 - [任务 36(100)][49298f02-7c9d-4233-a156-e527827cd894] - Node 49298f02-7c9d-4233-a156-e527827cd894[49298f02-7c9d-4233-a156-e527827cd894] close complete, cost 1 ms 
[INFO ] 2024-04-01 16:56:53.531 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 16:56:53.531 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 16:56:53.531 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 13 ms 
[INFO ] 2024-04-01 16:56:53.533 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-49298f02-7c9d-4233-a156-e527827cd894 complete, cost 734ms 
[INFO ] 2024-04-01 16:57:35.690 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:57:35.691 - [任务 36(100)][00df83cf-52b7-490c-a116-c135031b66e9] - Node 00df83cf-52b7-490c-a116-c135031b66e9[00df83cf-52b7-490c-a116-c135031b66e9] start preload schema,table counts: 0 
[INFO ] 2024-04-01 16:57:35.695 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:57:35.701 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 16:57:35.702 - [任务 36(100)][00df83cf-52b7-490c-a116-c135031b66e9] - Node 00df83cf-52b7-490c-a116-c135031b66e9[00df83cf-52b7-490c-a116-c135031b66e9] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:57:35.702 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:57:36.013 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 16:57:36.013 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:57:36.013 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:57:36.013 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 16:57:36.013 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 16:57:36.014 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 12 ms 
[INFO ] 2024-04-01 16:57:36.278 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 16:57:36.279 - [任务 36(100)][00df83cf-52b7-490c-a116-c135031b66e9] - Node 00df83cf-52b7-490c-a116-c135031b66e9[00df83cf-52b7-490c-a116-c135031b66e9] running status set to false 
[INFO ] 2024-04-01 16:57:36.279 - [任务 36(100)][00df83cf-52b7-490c-a116-c135031b66e9] - Node 00df83cf-52b7-490c-a116-c135031b66e9[00df83cf-52b7-490c-a116-c135031b66e9] schema data cleaned 
[INFO ] 2024-04-01 16:57:36.279 - [任务 36(100)][00df83cf-52b7-490c-a116-c135031b66e9] - Node 00df83cf-52b7-490c-a116-c135031b66e9[00df83cf-52b7-490c-a116-c135031b66e9] monitor closed 
[INFO ] 2024-04-01 16:57:36.279 - [任务 36(100)][00df83cf-52b7-490c-a116-c135031b66e9] - Node 00df83cf-52b7-490c-a116-c135031b66e9[00df83cf-52b7-490c-a116-c135031b66e9] close complete, cost 0 ms 
[INFO ] 2024-04-01 16:57:36.285 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-10b08e78-2d54-4cf6-acb9-80c314270412 
[INFO ] 2024-04-01 16:57:36.285 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-10b08e78-2d54-4cf6-acb9-80c314270412 
[INFO ] 2024-04-01 16:57:36.286 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 16:57:36.288 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 16:57:36.288 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 16:57:36.288 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 13 ms 
[INFO ] 2024-04-01 16:57:36.493 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-00df83cf-52b7-490c-a116-c135031b66e9 complete, cost 671ms 
[INFO ] 2024-04-01 16:58:55.455 - [任务 36(100)][72048afd-cf87-4dec-a137-ec643631d327] - Node 72048afd-cf87-4dec-a137-ec643631d327[72048afd-cf87-4dec-a137-ec643631d327] start preload schema,table counts: 0 
[INFO ] 2024-04-01 16:58:55.456 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:58:55.456 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 16:58:55.466 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 16:58:55.466 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:58:55.466 - [任务 36(100)][72048afd-cf87-4dec-a137-ec643631d327] - Node 72048afd-cf87-4dec-a137-ec643631d327[72048afd-cf87-4dec-a137-ec643631d327] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 16:58:55.813 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 16:58:55.813 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:58:55.813 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 16:58:55.813 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 16:58:55.813 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 16:58:56.018 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 14 ms 
[INFO ] 2024-04-01 16:58:56.044 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 16:58:56.044 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-855a0e90-9a5f-4332-8041-a723ce84423f 
[INFO ] 2024-04-01 16:58:56.044 - [任务 36(100)][72048afd-cf87-4dec-a137-ec643631d327] - Node 72048afd-cf87-4dec-a137-ec643631d327[72048afd-cf87-4dec-a137-ec643631d327] running status set to false 
[INFO ] 2024-04-01 16:58:56.044 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-855a0e90-9a5f-4332-8041-a723ce84423f 
[INFO ] 2024-04-01 16:58:56.045 - [任务 36(100)][72048afd-cf87-4dec-a137-ec643631d327] - Node 72048afd-cf87-4dec-a137-ec643631d327[72048afd-cf87-4dec-a137-ec643631d327] schema data cleaned 
[INFO ] 2024-04-01 16:58:56.045 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 16:58:56.045 - [任务 36(100)][72048afd-cf87-4dec-a137-ec643631d327] - Node 72048afd-cf87-4dec-a137-ec643631d327[72048afd-cf87-4dec-a137-ec643631d327] monitor closed 
[INFO ] 2024-04-01 16:58:56.045 - [任务 36(100)][72048afd-cf87-4dec-a137-ec643631d327] - Node 72048afd-cf87-4dec-a137-ec643631d327[72048afd-cf87-4dec-a137-ec643631d327] close complete, cost 1 ms 
[INFO ] 2024-04-01 16:58:56.049 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 16:58:56.049 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 16:58:56.052 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 11 ms 
[INFO ] 2024-04-01 16:58:56.053 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-72048afd-cf87-4dec-a137-ec643631d327 complete, cost 682ms 
[INFO ] 2024-04-01 17:08:45.727 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:08:45.727 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:08:45.727 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 5 ms 
[INFO ] 2024-04-01 17:08:45.727 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:08:45.727 - [任务 36(100)][1ef3b39a-c796-42f0-81c7-e61135cb2fd5] - Node 1ef3b39a-c796-42f0-81c7-e61135cb2fd5[1ef3b39a-c796-42f0-81c7-e61135cb2fd5] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:08:45.727 - [任务 36(100)][1ef3b39a-c796-42f0-81c7-e61135cb2fd5] - Node 1ef3b39a-c796-42f0-81c7-e61135cb2fd5[1ef3b39a-c796-42f0-81c7-e61135cb2fd5] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:08:45.727 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:08:45.727 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:08:45.727 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:08:45.727 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:08:45.728 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:08:45.728 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 32 ms 
[INFO ] 2024-04-01 17:08:45.937 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:08:45.939 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-09c9681c-6c49-4243-8f69-00868352dac3 
[INFO ] 2024-04-01 17:08:45.939 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-09c9681c-6c49-4243-8f69-00868352dac3 
[INFO ] 2024-04-01 17:08:45.939 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:08:45.941 - [任务 36(100)][1ef3b39a-c796-42f0-81c7-e61135cb2fd5] - Node 1ef3b39a-c796-42f0-81c7-e61135cb2fd5[1ef3b39a-c796-42f0-81c7-e61135cb2fd5] running status set to false 
[INFO ] 2024-04-01 17:08:45.942 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:08:45.942 - [任务 36(100)][1ef3b39a-c796-42f0-81c7-e61135cb2fd5] - Node 1ef3b39a-c796-42f0-81c7-e61135cb2fd5[1ef3b39a-c796-42f0-81c7-e61135cb2fd5] schema data cleaned 
[INFO ] 2024-04-01 17:08:45.942 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:08:45.942 - [任务 36(100)][1ef3b39a-c796-42f0-81c7-e61135cb2fd5] - Node 1ef3b39a-c796-42f0-81c7-e61135cb2fd5[1ef3b39a-c796-42f0-81c7-e61135cb2fd5] monitor closed 
[INFO ] 2024-04-01 17:08:45.942 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 8 ms 
[INFO ] 2024-04-01 17:08:45.942 - [任务 36(100)][1ef3b39a-c796-42f0-81c7-e61135cb2fd5] - Node 1ef3b39a-c796-42f0-81c7-e61135cb2fd5[1ef3b39a-c796-42f0-81c7-e61135cb2fd5] close complete, cost 2 ms 
[INFO ] 2024-04-01 17:08:47.114 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-1ef3b39a-c796-42f0-81c7-e61135cb2fd5 complete, cost 829ms 
[INFO ] 2024-04-01 17:09:32.059 - [任务 36(100)][02d77c50-945f-41f9-b2de-1d06256fb5a9] - Node 02d77c50-945f-41f9-b2de-1d06256fb5a9[02d77c50-945f-41f9-b2de-1d06256fb5a9] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:09:32.060 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:09:32.061 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:09:32.061 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:09:32.062 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:09:32.062 - [任务 36(100)][02d77c50-945f-41f9-b2de-1d06256fb5a9] - Node 02d77c50-945f-41f9-b2de-1d06256fb5a9[02d77c50-945f-41f9-b2de-1d06256fb5a9] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:09:32.419 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:09:32.427 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:09:32.427 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:09:32.427 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:09:32.427 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:09:32.428 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 27 ms 
[INFO ] 2024-04-01 17:09:32.655 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:09:32.659 - [任务 36(100)][02d77c50-945f-41f9-b2de-1d06256fb5a9] - Node 02d77c50-945f-41f9-b2de-1d06256fb5a9[02d77c50-945f-41f9-b2de-1d06256fb5a9] running status set to false 
[INFO ] 2024-04-01 17:09:32.659 - [任务 36(100)][02d77c50-945f-41f9-b2de-1d06256fb5a9] - Node 02d77c50-945f-41f9-b2de-1d06256fb5a9[02d77c50-945f-41f9-b2de-1d06256fb5a9] schema data cleaned 
[INFO ] 2024-04-01 17:09:32.659 - [任务 36(100)][02d77c50-945f-41f9-b2de-1d06256fb5a9] - Node 02d77c50-945f-41f9-b2de-1d06256fb5a9[02d77c50-945f-41f9-b2de-1d06256fb5a9] monitor closed 
[INFO ] 2024-04-01 17:09:32.659 - [任务 36(100)][02d77c50-945f-41f9-b2de-1d06256fb5a9] - Node 02d77c50-945f-41f9-b2de-1d06256fb5a9[02d77c50-945f-41f9-b2de-1d06256fb5a9] close complete, cost 0 ms 
[INFO ] 2024-04-01 17:09:32.661 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-0523bccf-b36c-400a-92f0-df8590124052 
[INFO ] 2024-04-01 17:09:32.662 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-0523bccf-b36c-400a-92f0-df8590124052 
[INFO ] 2024-04-01 17:09:32.662 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:09:32.664 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:09:32.664 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:09:32.664 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 9 ms 
[INFO ] 2024-04-01 17:09:32.866 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-02d77c50-945f-41f9-b2de-1d06256fb5a9 complete, cost 716ms 
[INFO ] 2024-04-01 17:20:14.344 - [任务 36(100)][ddaae945-c7a6-429f-8e4c-0799e3ed23de] - Node ddaae945-c7a6-429f-8e4c-0799e3ed23de[ddaae945-c7a6-429f-8e4c-0799e3ed23de] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:20:14.345 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:20:14.345 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:20:14.345 - [任务 36(100)][ddaae945-c7a6-429f-8e4c-0799e3ed23de] - Node ddaae945-c7a6-429f-8e4c-0799e3ed23de[ddaae945-c7a6-429f-8e4c-0799e3ed23de] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:20:14.345 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 17:20:14.345 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:20:15.179 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:20:15.179 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:20:15.180 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:20:15.180 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:20:15.180 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:20:15.384 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 18 ms 
[INFO ] 2024-04-01 17:20:16.294 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:20:16.297 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-b49fc8d9-38a9-40e3-abf5-7d3d4139b8a9 
[INFO ] 2024-04-01 17:20:16.304 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-b49fc8d9-38a9-40e3-abf5-7d3d4139b8a9 
[INFO ] 2024-04-01 17:20:16.304 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:20:16.305 - [任务 36(100)][ddaae945-c7a6-429f-8e4c-0799e3ed23de] - Node ddaae945-c7a6-429f-8e4c-0799e3ed23de[ddaae945-c7a6-429f-8e4c-0799e3ed23de] running status set to false 
[INFO ] 2024-04-01 17:20:16.305 - [任务 36(100)][ddaae945-c7a6-429f-8e4c-0799e3ed23de] - Node ddaae945-c7a6-429f-8e4c-0799e3ed23de[ddaae945-c7a6-429f-8e4c-0799e3ed23de] schema data cleaned 
[INFO ] 2024-04-01 17:20:16.305 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:20:16.305 - [任务 36(100)][ddaae945-c7a6-429f-8e4c-0799e3ed23de] - Node ddaae945-c7a6-429f-8e4c-0799e3ed23de[ddaae945-c7a6-429f-8e4c-0799e3ed23de] monitor closed 
[INFO ] 2024-04-01 17:20:16.305 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:20:16.305 - [任务 36(100)][ddaae945-c7a6-429f-8e4c-0799e3ed23de] - Node ddaae945-c7a6-429f-8e4c-0799e3ed23de[ddaae945-c7a6-429f-8e4c-0799e3ed23de] close complete, cost 3 ms 
[INFO ] 2024-04-01 17:20:16.305 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 9 ms 
[INFO ] 2024-04-01 17:20:16.510 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-ddaae945-c7a6-429f-8e4c-0799e3ed23de complete, cost 111808ms 
[INFO ] 2024-04-01 17:20:17.075 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:20:17.075 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:20:17.076 - [任务 36(100)][73c36d91-fb3d-45f5-b50d-68e3157585cc] - Node 73c36d91-fb3d-45f5-b50d-68e3157585cc[73c36d91-fb3d-45f5-b50d-68e3157585cc] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:20:17.077 - [任务 36(100)][73c36d91-fb3d-45f5-b50d-68e3157585cc] - Node 73c36d91-fb3d-45f5-b50d-68e3157585cc[73c36d91-fb3d-45f5-b50d-68e3157585cc] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:20:17.077 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 17:20:17.077 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 17:20:17.393 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:20:17.402 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:20:17.402 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:20:17.402 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:20:17.402 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:20:17.609 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 11 ms 
[INFO ] 2024-04-01 17:20:17.631 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:20:17.632 - [任务 36(100)][73c36d91-fb3d-45f5-b50d-68e3157585cc] - Node 73c36d91-fb3d-45f5-b50d-68e3157585cc[73c36d91-fb3d-45f5-b50d-68e3157585cc] running status set to false 
[INFO ] 2024-04-01 17:20:17.632 - [任务 36(100)][73c36d91-fb3d-45f5-b50d-68e3157585cc] - Node 73c36d91-fb3d-45f5-b50d-68e3157585cc[73c36d91-fb3d-45f5-b50d-68e3157585cc] schema data cleaned 
[INFO ] 2024-04-01 17:20:17.632 - [任务 36(100)][73c36d91-fb3d-45f5-b50d-68e3157585cc] - Node 73c36d91-fb3d-45f5-b50d-68e3157585cc[73c36d91-fb3d-45f5-b50d-68e3157585cc] monitor closed 
[INFO ] 2024-04-01 17:20:17.636 - [任务 36(100)][73c36d91-fb3d-45f5-b50d-68e3157585cc] - Node 73c36d91-fb3d-45f5-b50d-68e3157585cc[73c36d91-fb3d-45f5-b50d-68e3157585cc] close complete, cost 2 ms 
[INFO ] 2024-04-01 17:20:17.636 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-58139207-2e59-44ef-949f-434147751c24 
[INFO ] 2024-04-01 17:20:17.636 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-58139207-2e59-44ef-949f-434147751c24 
[INFO ] 2024-04-01 17:20:17.637 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:20:17.640 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:20:17.640 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:20:17.642 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 13 ms 
[INFO ] 2024-04-01 17:20:17.642 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-73c36d91-fb3d-45f5-b50d-68e3157585cc complete, cost 676ms 
[INFO ] 2024-04-01 17:26:29.134 - [任务 36(100)][d2035edb-b9ee-41f1-bafe-1d0be3627e5b] - Node d2035edb-b9ee-41f1-bafe-1d0be3627e5b[d2035edb-b9ee-41f1-bafe-1d0be3627e5b] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:26:29.136 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:26:29.136 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:26:29.136 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:26:29.136 - [任务 36(100)][d2035edb-b9ee-41f1-bafe-1d0be3627e5b] - Node d2035edb-b9ee-41f1-bafe-1d0be3627e5b[d2035edb-b9ee-41f1-bafe-1d0be3627e5b] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:26:29.136 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:26:29.136 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:26:29.136 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:26:29.136 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:26:29.136 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:26:29.136 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:26:29.136 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 17 ms 
[INFO ] 2024-04-01 17:26:29.385 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:26:29.385 - [任务 36(100)][d2035edb-b9ee-41f1-bafe-1d0be3627e5b] - Node d2035edb-b9ee-41f1-bafe-1d0be3627e5b[d2035edb-b9ee-41f1-bafe-1d0be3627e5b] running status set to false 
[INFO ] 2024-04-01 17:26:29.385 - [任务 36(100)][d2035edb-b9ee-41f1-bafe-1d0be3627e5b] - Node d2035edb-b9ee-41f1-bafe-1d0be3627e5b[d2035edb-b9ee-41f1-bafe-1d0be3627e5b] schema data cleaned 
[INFO ] 2024-04-01 17:26:29.386 - [任务 36(100)][d2035edb-b9ee-41f1-bafe-1d0be3627e5b] - Node d2035edb-b9ee-41f1-bafe-1d0be3627e5b[d2035edb-b9ee-41f1-bafe-1d0be3627e5b] monitor closed 
[INFO ] 2024-04-01 17:26:29.389 - [任务 36(100)][d2035edb-b9ee-41f1-bafe-1d0be3627e5b] - Node d2035edb-b9ee-41f1-bafe-1d0be3627e5b[d2035edb-b9ee-41f1-bafe-1d0be3627e5b] close complete, cost 0 ms 
[INFO ] 2024-04-01 17:26:29.389 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-82089cbd-793a-4e93-8e16-50603e9671e6 
[INFO ] 2024-04-01 17:26:29.389 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-82089cbd-793a-4e93-8e16-50603e9671e6 
[INFO ] 2024-04-01 17:26:29.389 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:26:29.391 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:26:29.391 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:26:29.391 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 12 ms 
[INFO ] 2024-04-01 17:26:29.392 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-d2035edb-b9ee-41f1-bafe-1d0be3627e5b complete, cost 94854ms 
[INFO ] 2024-04-01 17:31:06.679 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:31:06.679 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:31:06.680 - [任务 36(100)][2e2b6382-862c-4053-ac25-ba34f2ca3ac6] - Node 2e2b6382-862c-4053-ac25-ba34f2ca3ac6[2e2b6382-862c-4053-ac25-ba34f2ca3ac6] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:31:06.680 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:31:06.680 - [任务 36(100)][2e2b6382-862c-4053-ac25-ba34f2ca3ac6] - Node 2e2b6382-862c-4053-ac25-ba34f2ca3ac6[2e2b6382-862c-4053-ac25-ba34f2ca3ac6] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:31:06.680 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 17:31:07.047 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:31:07.061 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:31:07.061 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:31:07.061 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:31:07.062 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:31:07.063 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 37 ms 
[INFO ] 2024-04-01 17:31:07.280 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:31:07.290 - [任务 36(100)][2e2b6382-862c-4053-ac25-ba34f2ca3ac6] - Node 2e2b6382-862c-4053-ac25-ba34f2ca3ac6[2e2b6382-862c-4053-ac25-ba34f2ca3ac6] running status set to false 
[INFO ] 2024-04-01 17:31:07.290 - [任务 36(100)][2e2b6382-862c-4053-ac25-ba34f2ca3ac6] - Node 2e2b6382-862c-4053-ac25-ba34f2ca3ac6[2e2b6382-862c-4053-ac25-ba34f2ca3ac6] schema data cleaned 
[INFO ] 2024-04-01 17:31:07.291 - [任务 36(100)][2e2b6382-862c-4053-ac25-ba34f2ca3ac6] - Node 2e2b6382-862c-4053-ac25-ba34f2ca3ac6[2e2b6382-862c-4053-ac25-ba34f2ca3ac6] monitor closed 
[INFO ] 2024-04-01 17:31:07.291 - [任务 36(100)][2e2b6382-862c-4053-ac25-ba34f2ca3ac6] - Node 2e2b6382-862c-4053-ac25-ba34f2ca3ac6[2e2b6382-862c-4053-ac25-ba34f2ca3ac6] close complete, cost 5 ms 
[INFO ] 2024-04-01 17:31:07.293 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-69aa3450-dd27-4db2-baa0-2e07e533cd32 
[INFO ] 2024-04-01 17:31:07.293 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-69aa3450-dd27-4db2-baa0-2e07e533cd32 
[INFO ] 2024-04-01 17:31:07.293 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:31:07.296 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:31:07.297 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:31:07.297 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 18 ms 
[INFO ] 2024-04-01 17:31:08.563 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-2e2b6382-862c-4053-ac25-ba34f2ca3ac6 complete, cost 747ms 
[INFO ] 2024-04-01 17:31:27.403 - [任务 36(100)][12a02946-64d0-4bb9-b0d5-1b0441c3a26f] - Node 12a02946-64d0-4bb9-b0d5-1b0441c3a26f[12a02946-64d0-4bb9-b0d5-1b0441c3a26f] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:31:27.403 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:31:27.403 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:31:27.403 - [任务 36(100)][12a02946-64d0-4bb9-b0d5-1b0441c3a26f] - Node 12a02946-64d0-4bb9-b0d5-1b0441c3a26f[12a02946-64d0-4bb9-b0d5-1b0441c3a26f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:31:27.403 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:31:27.403 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 2 ms 
[INFO ] 2024-04-01 17:31:27.697 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:31:27.707 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:31:27.707 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:31:27.707 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:31:27.707 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:31:27.911 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 12 ms 
[INFO ] 2024-04-01 17:31:27.925 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:31:27.928 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-e9a14b82-b465-46d8-82fa-3c6cb5df5451 
[INFO ] 2024-04-01 17:31:27.928 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-e9a14b82-b465-46d8-82fa-3c6cb5df5451 
[INFO ] 2024-04-01 17:31:27.928 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:31:27.929 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:31:27.930 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:31:27.933 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 5 ms 
[INFO ] 2024-04-01 17:31:27.933 - [任务 36(100)][12a02946-64d0-4bb9-b0d5-1b0441c3a26f] - Node 12a02946-64d0-4bb9-b0d5-1b0441c3a26f[12a02946-64d0-4bb9-b0d5-1b0441c3a26f] running status set to false 
[INFO ] 2024-04-01 17:31:27.934 - [任务 36(100)][12a02946-64d0-4bb9-b0d5-1b0441c3a26f] - Node 12a02946-64d0-4bb9-b0d5-1b0441c3a26f[12a02946-64d0-4bb9-b0d5-1b0441c3a26f] schema data cleaned 
[INFO ] 2024-04-01 17:31:27.934 - [任务 36(100)][12a02946-64d0-4bb9-b0d5-1b0441c3a26f] - Node 12a02946-64d0-4bb9-b0d5-1b0441c3a26f[12a02946-64d0-4bb9-b0d5-1b0441c3a26f] monitor closed 
[INFO ] 2024-04-01 17:31:27.938 - [任务 36(100)][12a02946-64d0-4bb9-b0d5-1b0441c3a26f] - Node 12a02946-64d0-4bb9-b0d5-1b0441c3a26f[12a02946-64d0-4bb9-b0d5-1b0441c3a26f] close complete, cost 2 ms 
[INFO ] 2024-04-01 17:31:27.938 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-12a02946-64d0-4bb9-b0d5-1b0441c3a26f complete, cost 615ms 
[INFO ] 2024-04-01 17:32:19.190 - [任务 36(100)][ab53ed7f-0538-4e64-98f7-f1ac98656614] - Node ab53ed7f-0538-4e64-98f7-f1ac98656614[ab53ed7f-0538-4e64-98f7-f1ac98656614] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:32:19.192 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:32:19.192 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:32:19.193 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:32:19.193 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:32:19.193 - [任务 36(100)][ab53ed7f-0538-4e64-98f7-f1ac98656614] - Node ab53ed7f-0538-4e64-98f7-f1ac98656614[ab53ed7f-0538-4e64-98f7-f1ac98656614] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:32:19.489 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:32:19.499 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:32:19.499 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:32:19.500 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:32:19.500 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:32:19.703 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 12 ms 
[INFO ] 2024-04-01 17:32:19.727 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:32:19.728 - [任务 36(100)][ab53ed7f-0538-4e64-98f7-f1ac98656614] - Node ab53ed7f-0538-4e64-98f7-f1ac98656614[ab53ed7f-0538-4e64-98f7-f1ac98656614] running status set to false 
[INFO ] 2024-04-01 17:32:19.728 - [任务 36(100)][ab53ed7f-0538-4e64-98f7-f1ac98656614] - Node ab53ed7f-0538-4e64-98f7-f1ac98656614[ab53ed7f-0538-4e64-98f7-f1ac98656614] schema data cleaned 
[INFO ] 2024-04-01 17:32:19.728 - [任务 36(100)][ab53ed7f-0538-4e64-98f7-f1ac98656614] - Node ab53ed7f-0538-4e64-98f7-f1ac98656614[ab53ed7f-0538-4e64-98f7-f1ac98656614] monitor closed 
[INFO ] 2024-04-01 17:32:19.733 - [任务 36(100)][ab53ed7f-0538-4e64-98f7-f1ac98656614] - Node ab53ed7f-0538-4e64-98f7-f1ac98656614[ab53ed7f-0538-4e64-98f7-f1ac98656614] close complete, cost 0 ms 
[INFO ] 2024-04-01 17:32:19.734 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-9f628e61-db8b-4ee6-9381-fcbcfff03f2a 
[INFO ] 2024-04-01 17:32:19.734 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-9f628e61-db8b-4ee6-9381-fcbcfff03f2a 
[INFO ] 2024-04-01 17:32:19.734 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:32:19.736 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:32:19.736 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:32:19.738 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 11 ms 
[INFO ] 2024-04-01 17:32:19.738 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-ab53ed7f-0538-4e64-98f7-f1ac98656614 complete, cost 623ms 
[INFO ] 2024-04-01 17:35:36.052 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:35:36.052 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:35:36.053 - [任务 36(100)][7a9ff6d5-ae83-44b8-ae9c-c176f61755ba] - Node 7a9ff6d5-ae83-44b8-ae9c-c176f61755ba[7a9ff6d5-ae83-44b8-ae9c-c176f61755ba] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:35:36.053 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 2 ms 
[INFO ] 2024-04-01 17:35:36.053 - [任务 36(100)][7a9ff6d5-ae83-44b8-ae9c-c176f61755ba] - Node 7a9ff6d5-ae83-44b8-ae9c-c176f61755ba[7a9ff6d5-ae83-44b8-ae9c-c176f61755ba] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:35:36.053 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 17:35:36.053 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:35:36.053 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:35:36.053 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:35:36.053 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:35:36.053 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:35:36.258 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 27 ms 
[INFO ] 2024-04-01 17:35:36.277 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:35:36.279 - [任务 36(100)][7a9ff6d5-ae83-44b8-ae9c-c176f61755ba] - Node 7a9ff6d5-ae83-44b8-ae9c-c176f61755ba[7a9ff6d5-ae83-44b8-ae9c-c176f61755ba] running status set to false 
[INFO ] 2024-04-01 17:35:36.279 - [任务 36(100)][7a9ff6d5-ae83-44b8-ae9c-c176f61755ba] - Node 7a9ff6d5-ae83-44b8-ae9c-c176f61755ba[7a9ff6d5-ae83-44b8-ae9c-c176f61755ba] schema data cleaned 
[INFO ] 2024-04-01 17:35:36.279 - [任务 36(100)][7a9ff6d5-ae83-44b8-ae9c-c176f61755ba] - Node 7a9ff6d5-ae83-44b8-ae9c-c176f61755ba[7a9ff6d5-ae83-44b8-ae9c-c176f61755ba] monitor closed 
[INFO ] 2024-04-01 17:35:36.279 - [任务 36(100)][7a9ff6d5-ae83-44b8-ae9c-c176f61755ba] - Node 7a9ff6d5-ae83-44b8-ae9c-c176f61755ba[7a9ff6d5-ae83-44b8-ae9c-c176f61755ba] close complete, cost 0 ms 
[INFO ] 2024-04-01 17:35:36.280 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-f8656496-d74b-4a9d-b153-c91bedbae3f5 
[INFO ] 2024-04-01 17:35:36.283 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-f8656496-d74b-4a9d-b153-c91bedbae3f5 
[INFO ] 2024-04-01 17:35:36.283 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:35:36.283 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:35:36.283 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:35:36.283 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 15 ms 
[INFO ] 2024-04-01 17:35:36.488 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-7a9ff6d5-ae83-44b8-ae9c-c176f61755ba complete, cost 626ms 
[INFO ] 2024-04-01 17:37:10.717 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:37:10.717 - [任务 36(100)][7fef1699-5278-411c-8825-7c4ba67d9350] - Node 7fef1699-5278-411c-8825-7c4ba67d9350[7fef1699-5278-411c-8825-7c4ba67d9350] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:37:10.717 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 3 ms 
[INFO ] 2024-04-01 17:37:10.717 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:37:10.717 - [任务 36(100)][7fef1699-5278-411c-8825-7c4ba67d9350] - Node 7fef1699-5278-411c-8825-7c4ba67d9350[7fef1699-5278-411c-8825-7c4ba67d9350] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:37:10.717 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 17:37:10.717 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:37:10.717 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:37:10.718 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:37:10.718 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:37:10.718 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:37:10.923 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 25 ms 
[INFO ] 2024-04-01 17:37:11.029 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:37:11.030 - [任务 36(100)][7fef1699-5278-411c-8825-7c4ba67d9350] - Node 7fef1699-5278-411c-8825-7c4ba67d9350[7fef1699-5278-411c-8825-7c4ba67d9350] running status set to false 
[INFO ] 2024-04-01 17:37:11.030 - [任务 36(100)][7fef1699-5278-411c-8825-7c4ba67d9350] - Node 7fef1699-5278-411c-8825-7c4ba67d9350[7fef1699-5278-411c-8825-7c4ba67d9350] schema data cleaned 
[INFO ] 2024-04-01 17:37:11.030 - [任务 36(100)][7fef1699-5278-411c-8825-7c4ba67d9350] - Node 7fef1699-5278-411c-8825-7c4ba67d9350[7fef1699-5278-411c-8825-7c4ba67d9350] monitor closed 
[INFO ] 2024-04-01 17:37:11.030 - [任务 36(100)][7fef1699-5278-411c-8825-7c4ba67d9350] - Node 7fef1699-5278-411c-8825-7c4ba67d9350[7fef1699-5278-411c-8825-7c4ba67d9350] close complete, cost 1 ms 
[INFO ] 2024-04-01 17:37:11.035 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-693d2634-c101-434b-8681-0f2620b3baed 
[INFO ] 2024-04-01 17:37:11.035 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-693d2634-c101-434b-8681-0f2620b3baed 
[INFO ] 2024-04-01 17:37:11.036 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:37:11.039 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:37:11.040 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:37:11.040 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 18 ms 
[INFO ] 2024-04-01 17:37:11.244 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-7fef1699-5278-411c-8825-7c4ba67d9350 complete, cost 848ms 
[INFO ] 2024-04-01 17:37:11.357 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:37:11.357 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:37:11.358 - [任务 36(100)][c3d33690-c7bd-4bbd-9f64-8d184723f069] - Node c3d33690-c7bd-4bbd-9f64-8d184723f069[c3d33690-c7bd-4bbd-9f64-8d184723f069] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:37:11.358 - [任务 36(100)][c3d33690-c7bd-4bbd-9f64-8d184723f069] - Node c3d33690-c7bd-4bbd-9f64-8d184723f069[c3d33690-c7bd-4bbd-9f64-8d184723f069] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:37:11.358 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:37:11.358 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:37:11.712 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:37:11.712 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:37:11.714 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:37:11.714 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:37:11.714 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:37:11.715 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 29 ms 
[INFO ] 2024-04-01 17:37:11.929 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:37:11.934 - [任务 36(100)][c3d33690-c7bd-4bbd-9f64-8d184723f069] - Node c3d33690-c7bd-4bbd-9f64-8d184723f069[c3d33690-c7bd-4bbd-9f64-8d184723f069] running status set to false 
[INFO ] 2024-04-01 17:37:11.934 - [任务 36(100)][c3d33690-c7bd-4bbd-9f64-8d184723f069] - Node c3d33690-c7bd-4bbd-9f64-8d184723f069[c3d33690-c7bd-4bbd-9f64-8d184723f069] schema data cleaned 
[INFO ] 2024-04-01 17:37:11.934 - [任务 36(100)][c3d33690-c7bd-4bbd-9f64-8d184723f069] - Node c3d33690-c7bd-4bbd-9f64-8d184723f069[c3d33690-c7bd-4bbd-9f64-8d184723f069] monitor closed 
[INFO ] 2024-04-01 17:37:11.934 - [任务 36(100)][c3d33690-c7bd-4bbd-9f64-8d184723f069] - Node c3d33690-c7bd-4bbd-9f64-8d184723f069[c3d33690-c7bd-4bbd-9f64-8d184723f069] close complete, cost 0 ms 
[INFO ] 2024-04-01 17:37:11.934 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-3c174bb0-0011-41db-bd23-e25128d6f036 
[INFO ] 2024-04-01 17:37:11.934 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-3c174bb0-0011-41db-bd23-e25128d6f036 
[INFO ] 2024-04-01 17:37:11.934 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:37:11.935 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:37:11.935 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:37:11.935 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 7 ms 
[INFO ] 2024-04-01 17:37:11.936 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-c3d33690-c7bd-4bbd-9f64-8d184723f069 complete, cost 685ms 
[INFO ] 2024-04-01 17:37:45.289 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:37:45.289 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:37:45.289 - [任务 36(100)][b63a1726-3157-4043-8f99-d48bebc6ca93] - Node b63a1726-3157-4043-8f99-d48bebc6ca93[b63a1726-3157-4043-8f99-d48bebc6ca93] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:37:45.289 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:37:45.289 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:37:45.290 - [任务 36(100)][b63a1726-3157-4043-8f99-d48bebc6ca93] - Node b63a1726-3157-4043-8f99-d48bebc6ca93[b63a1726-3157-4043-8f99-d48bebc6ca93] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:37:45.596 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:37:45.602 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:37:45.602 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:37:45.602 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:37:45.602 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:37:45.602 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 8 ms 
[INFO ] 2024-04-01 17:37:45.843 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:37:45.845 - [任务 36(100)][b63a1726-3157-4043-8f99-d48bebc6ca93] - Node b63a1726-3157-4043-8f99-d48bebc6ca93[b63a1726-3157-4043-8f99-d48bebc6ca93] running status set to false 
[INFO ] 2024-04-01 17:37:45.845 - [任务 36(100)][b63a1726-3157-4043-8f99-d48bebc6ca93] - Node b63a1726-3157-4043-8f99-d48bebc6ca93[b63a1726-3157-4043-8f99-d48bebc6ca93] schema data cleaned 
[INFO ] 2024-04-01 17:37:45.845 - [任务 36(100)][b63a1726-3157-4043-8f99-d48bebc6ca93] - Node b63a1726-3157-4043-8f99-d48bebc6ca93[b63a1726-3157-4043-8f99-d48bebc6ca93] monitor closed 
[INFO ] 2024-04-01 17:37:45.848 - [任务 36(100)][b63a1726-3157-4043-8f99-d48bebc6ca93] - Node b63a1726-3157-4043-8f99-d48bebc6ca93[b63a1726-3157-4043-8f99-d48bebc6ca93] close complete, cost 1 ms 
[INFO ] 2024-04-01 17:37:45.849 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-3c8f7dbb-9bf7-4e2b-91aa-67b55fa1be19 
[INFO ] 2024-04-01 17:37:45.849 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-3c8f7dbb-9bf7-4e2b-91aa-67b55fa1be19 
[INFO ] 2024-04-01 17:37:45.851 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:37:45.851 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:37:45.851 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:37:45.851 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 11 ms 
[INFO ] 2024-04-01 17:37:46.057 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-b63a1726-3157-4043-8f99-d48bebc6ca93 complete, cost 654ms 
[INFO ] 2024-04-01 17:37:57.008 - [任务 36(100)][be7570d4-fa15-44e0-808c-3ca4e1a3975e] - Node be7570d4-fa15-44e0-808c-3ca4e1a3975e[be7570d4-fa15-44e0-808c-3ca4e1a3975e] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:37:57.009 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:37:57.009 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:37:57.009 - [任务 36(100)][be7570d4-fa15-44e0-808c-3ca4e1a3975e] - Node be7570d4-fa15-44e0-808c-3ca4e1a3975e[be7570d4-fa15-44e0-808c-3ca4e1a3975e] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:37:57.009 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:37:57.009 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:37:57.348 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:37:57.348 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:37:57.348 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:37:57.349 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:37:57.350 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:37:57.350 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 36 ms 
[INFO ] 2024-04-01 17:37:57.566 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:37:57.568 - [任务 36(100)][be7570d4-fa15-44e0-808c-3ca4e1a3975e] - Node be7570d4-fa15-44e0-808c-3ca4e1a3975e[be7570d4-fa15-44e0-808c-3ca4e1a3975e] running status set to false 
[INFO ] 2024-04-01 17:37:57.568 - [任务 36(100)][be7570d4-fa15-44e0-808c-3ca4e1a3975e] - Node be7570d4-fa15-44e0-808c-3ca4e1a3975e[be7570d4-fa15-44e0-808c-3ca4e1a3975e] schema data cleaned 
[INFO ] 2024-04-01 17:37:57.568 - [任务 36(100)][be7570d4-fa15-44e0-808c-3ca4e1a3975e] - Node be7570d4-fa15-44e0-808c-3ca4e1a3975e[be7570d4-fa15-44e0-808c-3ca4e1a3975e] monitor closed 
[INFO ] 2024-04-01 17:37:57.568 - [任务 36(100)][be7570d4-fa15-44e0-808c-3ca4e1a3975e] - Node be7570d4-fa15-44e0-808c-3ca4e1a3975e[be7570d4-fa15-44e0-808c-3ca4e1a3975e] close complete, cost 0 ms 
[INFO ] 2024-04-01 17:37:57.576 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-ac9b1771-13ed-4616-a447-6df9773d33af 
[INFO ] 2024-04-01 17:37:57.576 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-ac9b1771-13ed-4616-a447-6df9773d33af 
[INFO ] 2024-04-01 17:37:57.576 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:37:57.577 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:37:57.577 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:37:57.578 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 13 ms 
[INFO ] 2024-04-01 17:37:57.579 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-be7570d4-fa15-44e0-808c-3ca4e1a3975e complete, cost 689ms 
[INFO ] 2024-04-01 17:39:13.594 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:39:13.594 - [任务 36(100)][bf954326-8610-4478-8191-c234bb33cd11] - Node bf954326-8610-4478-8191-c234bb33cd11[bf954326-8610-4478-8191-c234bb33cd11] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:39:13.594 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:39:13.594 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:39:13.594 - [任务 36(100)][bf954326-8610-4478-8191-c234bb33cd11] - Node bf954326-8610-4478-8191-c234bb33cd11[bf954326-8610-4478-8191-c234bb33cd11] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:39:13.595 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:39:13.702 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:39:13.702 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:39:13.703 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:39:13.703 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:39:13.703 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:39:13.703 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 19 ms 
[INFO ] 2024-04-01 17:39:13.937 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:39:13.937 - [任务 36(100)][bf954326-8610-4478-8191-c234bb33cd11] - Node bf954326-8610-4478-8191-c234bb33cd11[bf954326-8610-4478-8191-c234bb33cd11] running status set to false 
[INFO ] 2024-04-01 17:39:13.937 - [任务 36(100)][bf954326-8610-4478-8191-c234bb33cd11] - Node bf954326-8610-4478-8191-c234bb33cd11[bf954326-8610-4478-8191-c234bb33cd11] schema data cleaned 
[INFO ] 2024-04-01 17:39:13.937 - [任务 36(100)][bf954326-8610-4478-8191-c234bb33cd11] - Node bf954326-8610-4478-8191-c234bb33cd11[bf954326-8610-4478-8191-c234bb33cd11] monitor closed 
[INFO ] 2024-04-01 17:39:13.943 - [任务 36(100)][bf954326-8610-4478-8191-c234bb33cd11] - Node bf954326-8610-4478-8191-c234bb33cd11[bf954326-8610-4478-8191-c234bb33cd11] close complete, cost 1 ms 
[INFO ] 2024-04-01 17:39:13.943 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-f62688ed-5a0e-4ba1-b3f8-3d87b6135627 
[INFO ] 2024-04-01 17:39:13.943 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-f62688ed-5a0e-4ba1-b3f8-3d87b6135627 
[INFO ] 2024-04-01 17:39:13.943 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:39:13.945 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:39:13.945 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:39:13.947 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 12 ms 
[INFO ] 2024-04-01 17:39:13.947 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-bf954326-8610-4478-8191-c234bb33cd11 complete, cost 686ms 
[INFO ] 2024-04-01 17:52:05.289 - [任务 36(100)][85e4f71d-df91-4dcb-a6fd-1a60326d6107] - Node 85e4f71d-df91-4dcb-a6fd-1a60326d6107[85e4f71d-df91-4dcb-a6fd-1a60326d6107] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:52:05.289 - [任务 36(100)][1a8fc371-bfd5-4b03-a21c-3b6baf58e02f] - Node 1a8fc371-bfd5-4b03-a21c-3b6baf58e02f[1a8fc371-bfd5-4b03-a21c-3b6baf58e02f] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:52:05.289 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:52:05.289 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:52:05.289 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:52:05.290 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:52:05.290 - [任务 36(100)][85e4f71d-df91-4dcb-a6fd-1a60326d6107] - Node 85e4f71d-df91-4dcb-a6fd-1a60326d6107[85e4f71d-df91-4dcb-a6fd-1a60326d6107] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:52:05.290 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 17:52:05.290 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:52:05.290 - [任务 36(100)][1a8fc371-bfd5-4b03-a21c-3b6baf58e02f] - Node 1a8fc371-bfd5-4b03-a21c-3b6baf58e02f[1a8fc371-bfd5-4b03-a21c-3b6baf58e02f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:52:05.290 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:52:05.290 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:52:05.290 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:52:05.302 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:52:05.303 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:52:05.303 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:52:05.303 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:52:05.511 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 18 ms 
[INFO ] 2024-04-01 17:52:05.541 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:52:05.547 - [任务 36(100)][85e4f71d-df91-4dcb-a6fd-1a60326d6107] - Node 85e4f71d-df91-4dcb-a6fd-1a60326d6107[85e4f71d-df91-4dcb-a6fd-1a60326d6107] running status set to false 
[INFO ] 2024-04-01 17:52:05.547 - [任务 36(100)][85e4f71d-df91-4dcb-a6fd-1a60326d6107] - Node 85e4f71d-df91-4dcb-a6fd-1a60326d6107[85e4f71d-df91-4dcb-a6fd-1a60326d6107] schema data cleaned 
[INFO ] 2024-04-01 17:52:05.547 - [任务 36(100)][85e4f71d-df91-4dcb-a6fd-1a60326d6107] - Node 85e4f71d-df91-4dcb-a6fd-1a60326d6107[85e4f71d-df91-4dcb-a6fd-1a60326d6107] monitor closed 
[INFO ] 2024-04-01 17:52:05.547 - [任务 36(100)][85e4f71d-df91-4dcb-a6fd-1a60326d6107] - Node 85e4f71d-df91-4dcb-a6fd-1a60326d6107[85e4f71d-df91-4dcb-a6fd-1a60326d6107] close complete, cost 1 ms 
[INFO ] 2024-04-01 17:52:05.553 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-7769ba5d-35c6-4df5-892a-216b24daf3a8 
[INFO ] 2024-04-01 17:52:05.553 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-7769ba5d-35c6-4df5-892a-216b24daf3a8 
[INFO ] 2024-04-01 17:52:05.554 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:52:05.556 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:52:05.556 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:52:05.556 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 18 ms 
[INFO ] 2024-04-01 17:52:05.768 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-85e4f71d-df91-4dcb-a6fd-1a60326d6107 complete, cost 696ms 
[INFO ] 2024-04-01 17:52:06.017 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:52:06.018 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:52:06.022 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:52:06.022 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:52:06.024 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:52:06.024 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 53 ms 
[INFO ] 2024-04-01 17:52:06.024 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:52:06.029 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-e60eb441-0179-4ac1-9be5-a827f6c1e9d2 
[INFO ] 2024-04-01 17:52:06.029 - [任务 36(100)][1a8fc371-bfd5-4b03-a21c-3b6baf58e02f] - Node 1a8fc371-bfd5-4b03-a21c-3b6baf58e02f[1a8fc371-bfd5-4b03-a21c-3b6baf58e02f] running status set to false 
[INFO ] 2024-04-01 17:52:06.029 - [任务 36(100)][1a8fc371-bfd5-4b03-a21c-3b6baf58e02f] - Node 1a8fc371-bfd5-4b03-a21c-3b6baf58e02f[1a8fc371-bfd5-4b03-a21c-3b6baf58e02f] schema data cleaned 
[INFO ] 2024-04-01 17:52:06.029 - [任务 36(100)][1a8fc371-bfd5-4b03-a21c-3b6baf58e02f] - Node 1a8fc371-bfd5-4b03-a21c-3b6baf58e02f[1a8fc371-bfd5-4b03-a21c-3b6baf58e02f] monitor closed 
[INFO ] 2024-04-01 17:52:06.029 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-e60eb441-0179-4ac1-9be5-a827f6c1e9d2 
[INFO ] 2024-04-01 17:52:06.029 - [任务 36(100)][1a8fc371-bfd5-4b03-a21c-3b6baf58e02f] - Node 1a8fc371-bfd5-4b03-a21c-3b6baf58e02f[1a8fc371-bfd5-4b03-a21c-3b6baf58e02f] close complete, cost 0 ms 
[INFO ] 2024-04-01 17:52:06.029 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:52:06.031 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:52:06.031 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:52:06.035 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 7 ms 
[INFO ] 2024-04-01 17:52:06.035 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-1a8fc371-bfd5-4b03-a21c-3b6baf58e02f complete, cost 1172ms 
[INFO ] 2024-04-01 17:55:41.348 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:55:41.348 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:55:41.349 - [任务 36(100)][f53f753d-9661-473f-a49b-93cf15f0d03f] - Node f53f753d-9661-473f-a49b-93cf15f0d03f[f53f753d-9661-473f-a49b-93cf15f0d03f] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:55:41.349 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:55:41.349 - [任务 36(100)][f53f753d-9661-473f-a49b-93cf15f0d03f] - Node f53f753d-9661-473f-a49b-93cf15f0d03f[f53f753d-9661-473f-a49b-93cf15f0d03f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:55:41.349 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 17:55:41.457 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:55:41.457 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:55:41.457 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:55:41.457 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:55:41.458 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:55:41.459 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 20 ms 
[INFO ] 2024-04-01 17:55:41.668 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:55:41.672 - [任务 36(100)][f53f753d-9661-473f-a49b-93cf15f0d03f] - Node f53f753d-9661-473f-a49b-93cf15f0d03f[f53f753d-9661-473f-a49b-93cf15f0d03f] running status set to false 
[INFO ] 2024-04-01 17:55:41.672 - [任务 36(100)][f53f753d-9661-473f-a49b-93cf15f0d03f] - Node f53f753d-9661-473f-a49b-93cf15f0d03f[f53f753d-9661-473f-a49b-93cf15f0d03f] schema data cleaned 
[INFO ] 2024-04-01 17:55:41.672 - [任务 36(100)][f53f753d-9661-473f-a49b-93cf15f0d03f] - Node f53f753d-9661-473f-a49b-93cf15f0d03f[f53f753d-9661-473f-a49b-93cf15f0d03f] monitor closed 
[INFO ] 2024-04-01 17:55:41.673 - [任务 36(100)][f53f753d-9661-473f-a49b-93cf15f0d03f] - Node f53f753d-9661-473f-a49b-93cf15f0d03f[f53f753d-9661-473f-a49b-93cf15f0d03f] close complete, cost 0 ms 
[INFO ] 2024-04-01 17:55:41.673 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-5b5a5616-3e90-4593-86bc-a32b2a61ade0 
[INFO ] 2024-04-01 17:55:41.673 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-5b5a5616-3e90-4593-86bc-a32b2a61ade0 
[INFO ] 2024-04-01 17:55:41.673 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:55:41.678 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:55:41.678 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:55:41.679 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 10 ms 
[INFO ] 2024-04-01 17:55:41.680 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-f53f753d-9661-473f-a49b-93cf15f0d03f complete, cost 634ms 
[INFO ] 2024-04-01 17:55:48.214 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:55:48.214 - [任务 36(100)][d920e07a-92a9-474c-9665-499f51e93eb0] - Node d920e07a-92a9-474c-9665-499f51e93eb0[d920e07a-92a9-474c-9665-499f51e93eb0] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:55:48.215 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:55:48.215 - [任务 36(100)][d920e07a-92a9-474c-9665-499f51e93eb0] - Node d920e07a-92a9-474c-9665-499f51e93eb0[d920e07a-92a9-474c-9665-499f51e93eb0] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:55:48.215 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:55:48.215 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:55:48.503 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:55:48.503 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:55:48.504 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:55:48.504 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:55:48.504 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:55:48.505 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 18 ms 
[INFO ] 2024-04-01 17:55:48.721 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:55:48.725 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-71d2376d-28da-4e70-900c-0edd6d116762 
[INFO ] 2024-04-01 17:55:48.725 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-71d2376d-28da-4e70-900c-0edd6d116762 
[INFO ] 2024-04-01 17:55:48.725 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:55:48.726 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:55:48.726 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:55:48.727 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 5 ms 
[INFO ] 2024-04-01 17:55:48.727 - [任务 36(100)][d920e07a-92a9-474c-9665-499f51e93eb0] - Node d920e07a-92a9-474c-9665-499f51e93eb0[d920e07a-92a9-474c-9665-499f51e93eb0] running status set to false 
[INFO ] 2024-04-01 17:55:48.727 - [任务 36(100)][d920e07a-92a9-474c-9665-499f51e93eb0] - Node d920e07a-92a9-474c-9665-499f51e93eb0[d920e07a-92a9-474c-9665-499f51e93eb0] schema data cleaned 
[INFO ] 2024-04-01 17:55:48.727 - [任务 36(100)][d920e07a-92a9-474c-9665-499f51e93eb0] - Node d920e07a-92a9-474c-9665-499f51e93eb0[d920e07a-92a9-474c-9665-499f51e93eb0] monitor closed 
[INFO ] 2024-04-01 17:55:48.729 - [任务 36(100)][d920e07a-92a9-474c-9665-499f51e93eb0] - Node d920e07a-92a9-474c-9665-499f51e93eb0[d920e07a-92a9-474c-9665-499f51e93eb0] close complete, cost 0 ms 
[INFO ] 2024-04-01 17:55:48.729 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-d920e07a-92a9-474c-9665-499f51e93eb0 complete, cost 565ms 
[INFO ] 2024-04-01 17:56:16.397 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:56:16.397 - [任务 36(100)][1066686f-1016-4860-aa06-e2fca7adda2e] - Node 1066686f-1016-4860-aa06-e2fca7adda2e[1066686f-1016-4860-aa06-e2fca7adda2e] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:56:16.399 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:56:16.399 - [任务 36(100)][1066686f-1016-4860-aa06-e2fca7adda2e] - Node 1066686f-1016-4860-aa06-e2fca7adda2e[1066686f-1016-4860-aa06-e2fca7adda2e] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:56:16.399 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 2 ms 
[INFO ] 2024-04-01 17:56:16.400 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:56:16.688 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:56:16.694 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:56:16.694 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:56:16.694 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:56:16.695 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:56:16.695 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 12 ms 
[INFO ] 2024-04-01 17:56:16.944 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:56:16.950 - [任务 36(100)][1066686f-1016-4860-aa06-e2fca7adda2e] - Node 1066686f-1016-4860-aa06-e2fca7adda2e[1066686f-1016-4860-aa06-e2fca7adda2e] running status set to false 
[INFO ] 2024-04-01 17:56:16.950 - [任务 36(100)][1066686f-1016-4860-aa06-e2fca7adda2e] - Node 1066686f-1016-4860-aa06-e2fca7adda2e[1066686f-1016-4860-aa06-e2fca7adda2e] schema data cleaned 
[INFO ] 2024-04-01 17:56:16.950 - [任务 36(100)][1066686f-1016-4860-aa06-e2fca7adda2e] - Node 1066686f-1016-4860-aa06-e2fca7adda2e[1066686f-1016-4860-aa06-e2fca7adda2e] monitor closed 
[INFO ] 2024-04-01 17:56:16.952 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-07381293-154d-4b36-9003-eb23bffbcf41 
[INFO ] 2024-04-01 17:56:16.952 - [任务 36(100)][1066686f-1016-4860-aa06-e2fca7adda2e] - Node 1066686f-1016-4860-aa06-e2fca7adda2e[1066686f-1016-4860-aa06-e2fca7adda2e] close complete, cost 2 ms 
[INFO ] 2024-04-01 17:56:16.952 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-07381293-154d-4b36-9003-eb23bffbcf41 
[INFO ] 2024-04-01 17:56:16.952 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:56:16.954 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:56:16.954 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:56:16.957 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 12 ms 
[INFO ] 2024-04-01 17:56:16.957 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-1066686f-1016-4860-aa06-e2fca7adda2e complete, cost 621ms 
[INFO ] 2024-04-01 17:56:51.381 - [任务 36(100)][dc11df13-a031-4718-8c8b-01161e993f6b] - Node dc11df13-a031-4718-8c8b-01161e993f6b[dc11df13-a031-4718-8c8b-01161e993f6b] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:56:51.382 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:56:51.383 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:56:51.383 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:56:51.383 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:56:51.383 - [任务 36(100)][dc11df13-a031-4718-8c8b-01161e993f6b] - Node dc11df13-a031-4718-8c8b-01161e993f6b[dc11df13-a031-4718-8c8b-01161e993f6b] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:56:51.727 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:56:51.727 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:56:51.728 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:56:51.728 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:56:51.728 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:56:51.729 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 25 ms 
[INFO ] 2024-04-01 17:56:51.931 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:56:51.937 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-07b71514-576f-4cd0-9f1a-d49819ca3d80 
[INFO ] 2024-04-01 17:56:51.937 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-07b71514-576f-4cd0-9f1a-d49819ca3d80 
[INFO ] 2024-04-01 17:56:51.937 - [任务 36(100)][dc11df13-a031-4718-8c8b-01161e993f6b] - Node dc11df13-a031-4718-8c8b-01161e993f6b[dc11df13-a031-4718-8c8b-01161e993f6b] running status set to false 
[INFO ] 2024-04-01 17:56:51.938 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:56:51.938 - [任务 36(100)][dc11df13-a031-4718-8c8b-01161e993f6b] - Node dc11df13-a031-4718-8c8b-01161e993f6b[dc11df13-a031-4718-8c8b-01161e993f6b] schema data cleaned 
[INFO ] 2024-04-01 17:56:51.938 - [任务 36(100)][dc11df13-a031-4718-8c8b-01161e993f6b] - Node dc11df13-a031-4718-8c8b-01161e993f6b[dc11df13-a031-4718-8c8b-01161e993f6b] monitor closed 
[INFO ] 2024-04-01 17:56:51.938 - [任务 36(100)][dc11df13-a031-4718-8c8b-01161e993f6b] - Node dc11df13-a031-4718-8c8b-01161e993f6b[dc11df13-a031-4718-8c8b-01161e993f6b] close complete, cost 0 ms 
[INFO ] 2024-04-01 17:56:51.939 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:56:51.939 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:56:51.939 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 10 ms 
[INFO ] 2024-04-01 17:56:52.140 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-dc11df13-a031-4718-8c8b-01161e993f6b complete, cost 668ms 
[INFO ] 2024-04-01 17:57:24.644 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:57:24.645 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:57:24.645 - [任务 36(100)][a1990b80-93dc-4020-b927-c76209733574] - Node a1990b80-93dc-4020-b927-c76209733574[a1990b80-93dc-4020-b927-c76209733574] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:57:24.646 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 3 ms 
[INFO ] 2024-04-01 17:57:24.652 - [任务 36(100)][a1990b80-93dc-4020-b927-c76209733574] - Node a1990b80-93dc-4020-b927-c76209733574[a1990b80-93dc-4020-b927-c76209733574] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:57:24.652 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:57:24.914 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:57:24.922 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:57:24.922 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:57:24.922 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:57:24.922 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:57:25.122 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 12 ms 
[INFO ] 2024-04-01 17:57:25.154 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:57:25.154 - [任务 36(100)][a1990b80-93dc-4020-b927-c76209733574] - Node a1990b80-93dc-4020-b927-c76209733574[a1990b80-93dc-4020-b927-c76209733574] running status set to false 
[INFO ] 2024-04-01 17:57:25.154 - [任务 36(100)][a1990b80-93dc-4020-b927-c76209733574] - Node a1990b80-93dc-4020-b927-c76209733574[a1990b80-93dc-4020-b927-c76209733574] schema data cleaned 
[INFO ] 2024-04-01 17:57:25.154 - [任务 36(100)][a1990b80-93dc-4020-b927-c76209733574] - Node a1990b80-93dc-4020-b927-c76209733574[a1990b80-93dc-4020-b927-c76209733574] monitor closed 
[INFO ] 2024-04-01 17:57:25.158 - [任务 36(100)][a1990b80-93dc-4020-b927-c76209733574] - Node a1990b80-93dc-4020-b927-c76209733574[a1990b80-93dc-4020-b927-c76209733574] close complete, cost 0 ms 
[INFO ] 2024-04-01 17:57:25.159 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-6b9d71c5-f7f5-4ad0-9124-c85d78b59921 
[INFO ] 2024-04-01 17:57:25.160 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-6b9d71c5-f7f5-4ad0-9124-c85d78b59921 
[INFO ] 2024-04-01 17:57:25.160 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:57:25.163 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:57:25.163 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:57:25.164 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 11 ms 
[INFO ] 2024-04-01 17:57:25.165 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-a1990b80-93dc-4020-b927-c76209733574 complete, cost 566ms 
[INFO ] 2024-04-01 17:59:39.443 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:59:39.444 - [任务 36(100)][7d26f242-618a-41f5-96c6-3b9d124c9168] - Node 7d26f242-618a-41f5-96c6-3b9d124c9168[7d26f242-618a-41f5-96c6-3b9d124c9168] start preload schema,table counts: 0 
[INFO ] 2024-04-01 17:59:39.444 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 17:59:39.444 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:59:39.444 - [任务 36(100)][7d26f242-618a-41f5-96c6-3b9d124c9168] - Node 7d26f242-618a-41f5-96c6-3b9d124c9168[7d26f242-618a-41f5-96c6-3b9d124c9168] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:59:39.444 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 17:59:39.759 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 17:59:39.776 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:59:39.776 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 17:59:39.777 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 17:59:39.777 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 17:59:39.777 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 19 ms 
[INFO ] 2024-04-01 17:59:40.000 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 17:59:40.004 - [任务 36(100)][7d26f242-618a-41f5-96c6-3b9d124c9168] - Node 7d26f242-618a-41f5-96c6-3b9d124c9168[7d26f242-618a-41f5-96c6-3b9d124c9168] running status set to false 
[INFO ] 2024-04-01 17:59:40.004 - [任务 36(100)][7d26f242-618a-41f5-96c6-3b9d124c9168] - Node 7d26f242-618a-41f5-96c6-3b9d124c9168[7d26f242-618a-41f5-96c6-3b9d124c9168] schema data cleaned 
[INFO ] 2024-04-01 17:59:40.004 - [任务 36(100)][7d26f242-618a-41f5-96c6-3b9d124c9168] - Node 7d26f242-618a-41f5-96c6-3b9d124c9168[7d26f242-618a-41f5-96c6-3b9d124c9168] monitor closed 
[INFO ] 2024-04-01 17:59:40.006 - [任务 36(100)][7d26f242-618a-41f5-96c6-3b9d124c9168] - Node 7d26f242-618a-41f5-96c6-3b9d124c9168[7d26f242-618a-41f5-96c6-3b9d124c9168] close complete, cost 1 ms 
[INFO ] 2024-04-01 17:59:40.006 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-ed758712-72ad-4c08-b887-229ae618c4b6 
[INFO ] 2024-04-01 17:59:40.006 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-ed758712-72ad-4c08-b887-229ae618c4b6 
[INFO ] 2024-04-01 17:59:40.007 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 17:59:40.008 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 17:59:40.010 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 17:59:40.010 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 11 ms 
[INFO ] 2024-04-01 17:59:40.219 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-7d26f242-618a-41f5-96c6-3b9d124c9168 complete, cost 745ms 
[INFO ] 2024-04-01 18:01:15.139 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 18:01:15.141 - [任务 36(100)][af260084-3965-43cf-a533-707f1711c32f] - Node af260084-3965-43cf-a533-707f1711c32f[af260084-3965-43cf-a533-707f1711c32f] start preload schema,table counts: 0 
[INFO ] 2024-04-01 18:01:15.141 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 18:01:15.141 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 18:01:15.141 - [任务 36(100)][af260084-3965-43cf-a533-707f1711c32f] - Node af260084-3965-43cf-a533-707f1711c32f[af260084-3965-43cf-a533-707f1711c32f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 18:01:15.141 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 1 ms 
[INFO ] 2024-04-01 18:01:15.422 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 18:01:15.437 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 18:01:15.437 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 18:01:15.437 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 18:01:15.437 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 18:01:15.641 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 20 ms 
[INFO ] 2024-04-01 18:01:15.657 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 18:01:15.657 - [任务 36(100)][af260084-3965-43cf-a533-707f1711c32f] - Node af260084-3965-43cf-a533-707f1711c32f[af260084-3965-43cf-a533-707f1711c32f] running status set to false 
[INFO ] 2024-04-01 18:01:15.657 - [任务 36(100)][af260084-3965-43cf-a533-707f1711c32f] - Node af260084-3965-43cf-a533-707f1711c32f[af260084-3965-43cf-a533-707f1711c32f] schema data cleaned 
[INFO ] 2024-04-01 18:01:15.657 - [任务 36(100)][af260084-3965-43cf-a533-707f1711c32f] - Node af260084-3965-43cf-a533-707f1711c32f[af260084-3965-43cf-a533-707f1711c32f] monitor closed 
[INFO ] 2024-04-01 18:01:15.663 - [任务 36(100)][af260084-3965-43cf-a533-707f1711c32f] - Node af260084-3965-43cf-a533-707f1711c32f[af260084-3965-43cf-a533-707f1711c32f] close complete, cost 0 ms 
[INFO ] 2024-04-01 18:01:15.663 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-8b1d70e4-2dc2-4057-871b-da87dfe0c544 
[INFO ] 2024-04-01 18:01:15.663 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-8b1d70e4-2dc2-4057-871b-da87dfe0c544 
[INFO ] 2024-04-01 18:01:15.663 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 18:01:15.665 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 18:01:15.665 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 18:01:15.667 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 14 ms 
[INFO ] 2024-04-01 18:01:15.667 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-af260084-3965-43cf-a533-707f1711c32f complete, cost 614ms 
[INFO ] 2024-04-01 18:07:39.994 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] start preload schema,table counts: 1 
[INFO ] 2024-04-01 18:07:39.995 - [任务 36(100)][318c85fe-778f-4526-a757-a6767d93ed88] - Node 318c85fe-778f-4526-a757-a6767d93ed88[318c85fe-778f-4526-a757-a6767d93ed88] start preload schema,table counts: 0 
[INFO ] 2024-04-01 18:07:39.996 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] start preload schema,table counts: 1 
[INFO ] 2024-04-01 18:07:39.996 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 18:07:40.002 - [任务 36(100)][318c85fe-778f-4526-a757-a6767d93ed88] - Node 318c85fe-778f-4526-a757-a6767d93ed88[318c85fe-778f-4526-a757-a6767d93ed88] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 18:07:40.002 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-01 18:07:40.313 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] running status set to false 
[INFO ] 2024-04-01 18:07:40.328 - [任务 36(100)][test] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 18:07:40.328 - [任务 36(100)][test] - PDK connector node released: HazelcastSampleSourcePdkDataNode-b19c1ec1-27d2-4554-9b72-4bcb124b9cc3 
[INFO ] 2024-04-01 18:07:40.329 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] schema data cleaned 
[INFO ] 2024-04-01 18:07:40.329 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] monitor closed 
[INFO ] 2024-04-01 18:07:40.329 - [任务 36(100)][test] - Node test[b19c1ec1-27d2-4554-9b72-4bcb124b9cc3] close complete, cost 24 ms 
[INFO ] 2024-04-01 18:07:40.560 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] running status set to false 
[INFO ] 2024-04-01 18:07:40.566 - [任务 36(100)][318c85fe-778f-4526-a757-a6767d93ed88] - Node 318c85fe-778f-4526-a757-a6767d93ed88[318c85fe-778f-4526-a757-a6767d93ed88] running status set to false 
[INFO ] 2024-04-01 18:07:40.566 - [任务 36(100)][318c85fe-778f-4526-a757-a6767d93ed88] - Node 318c85fe-778f-4526-a757-a6767d93ed88[318c85fe-778f-4526-a757-a6767d93ed88] schema data cleaned 
[INFO ] 2024-04-01 18:07:40.566 - [任务 36(100)][318c85fe-778f-4526-a757-a6767d93ed88] - Node 318c85fe-778f-4526-a757-a6767d93ed88[318c85fe-778f-4526-a757-a6767d93ed88] monitor closed 
[INFO ] 2024-04-01 18:07:40.566 - [任务 36(100)][318c85fe-778f-4526-a757-a6767d93ed88] - Node 318c85fe-778f-4526-a757-a6767d93ed88[318c85fe-778f-4526-a757-a6767d93ed88] close complete, cost 3 ms 
[INFO ] 2024-04-01 18:07:40.571 - [任务 36(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-387b1a3b-e33d-4633-95b1-eba4611850e3 
[INFO ] 2024-04-01 18:07:40.571 - [任务 36(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-387b1a3b-e33d-4633-95b1-eba4611850e3 
[INFO ] 2024-04-01 18:07:40.571 - [任务 36(100)][增强JS] - [ScriptExecutorsManager-660a1f8769f62c0d3c2ccb86-7c47e693-1a4e-4a84-aa34-0ba49bf6e78f-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-04-01 18:07:40.573 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] schema data cleaned 
[INFO ] 2024-04-01 18:07:40.573 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] monitor closed 
[INFO ] 2024-04-01 18:07:40.576 - [任务 36(100)][增强JS] - Node 增强JS[7c47e693-1a4e-4a84-aa34-0ba49bf6e78f] close complete, cost 18 ms 
[INFO ] 2024-04-01 18:07:40.576 - [任务 36(100)] - load tapTable task 660a1f8769f62c0d3c2ccb86-318c85fe-778f-4526-a757-a6767d93ed88 complete, cost 680ms 
