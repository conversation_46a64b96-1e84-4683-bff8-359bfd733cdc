[INFO ] 2024-07-05 14:53:42.980 - [任务 6] - Task initialization... 
[INFO ] 2024-07-05 14:53:43.008 - [任务 6] - Start task milestones: 668281612501e932ee7994c1(任务 6) 
[INFO ] 2024-07-05 14:53:44.026 - [任务 6] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-05 14:53:44.266 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 14:53:45.188 - [任务 6][Mysql] - <PERSON>de <PERSON>sql[2f087017-4dd8-4b27-a25a-76dfbcfe0f7a] start preload schema,table counts: 1 
[INFO ] 2024-07-05 14:53:45.189 - [任务 6][Mongo] - No<PERSON>[503d3db1-cf85-4e73-9c6c-4e7fdb4caa00] start preload schema,table counts: 1 
[INFO ] 2024-07-05 14:53:45.191 - [任务 6][Mysql] - Node My<PERSON>ql[2f087017-4dd8-4b27-a25a-76dfbcfe0f7a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 14:53:45.192 - [任务 6][Mongo] - Node Mongo[503d3db1-cf85-4e73-9c6c-4e7fdb4caa00] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 14:53:45.201 - [任务 6][字段编辑] - Node 字段编辑[534a568b-b80e-4d09-9529-b0056238cd8c] start preload schema,table counts: 1 
[INFO ] 2024-07-05 14:53:45.447 - [任务 6][字段编辑] - Node 字段编辑[534a568b-b80e-4d09-9529-b0056238cd8c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 14:53:46.259 - [任务 6][Mongo] - Source node "Mongo" read batch size: 100 
[INFO ] 2024-07-05 14:53:46.260 - [任务 6][Mongo] - Source node "Mongo" event queue capacity: 200 
[INFO ] 2024-07-05 14:53:46.328 - [任务 6][Mongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-05 14:53:46.494 - [任务 6][Mongo] - batch offset found: {"CLAIM":{"batch_read_connector_offset":{"sortKey":"_id","value":"657baffa62a1bacd962c870f","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1719828889,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-05 14:53:46.494 - [任务 6][Mongo] - Incremental sync starting... 
[INFO ] 2024-07-05 14:53:46.494 - [任务 6][Mongo] - Initial sync completed 
[INFO ] 2024-07-05 14:53:46.514 - [任务 6][Mongo] - Starting stream read, table list: [CLAIM], offset: {"cdcOffset":1719828889,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-05 14:53:46.516 - [任务 6][Mysql] - Node(Mysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-05 14:53:46.517 - [任务 6][Mysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-05 14:53:46.684 - [任务 6][Mongo] - Connector MongoDB incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-07-05 14:53:46.782 - [任务 6][Mongo] - Incremental sync completed 
[INFO ] 2024-07-05 14:53:46.821 - [任务 6][Mongo] - Exception skipping - The current exception does not match the skip exception strategy, message: Increment start point exceeds the log time window of mongodb, start point: 1719828889 
[ERROR] 2024-07-05 14:53:46.822 - [任务 6][Mongo] - com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1720162426, "i": 19}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1720162426, "i": 19}}, "signature": {"hash": {"$binary": {"base64": "MmtAXHNMZfwyBYPmGpx/eq4AZPc=", "subType": "00"}}, "keyId": 7329804974198620162}}} <-- Error Message -->
com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1720162426, "i": 19}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1720162426, "i": 19}}, "signature": {"hash": {"$binary": {"base64": "MmtAXHNMZfwyBYPmGpx/eq4AZPc=", "subType": "00"}}, "keyId": 7329804974198620162}}}

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1720162426, "i": 19}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1720162426, "i": 19}}, "signature": {"hash": {"$binary": {"base64": "MmtAXHNMZfwyBYPmGpx/eq4AZPc=", "subType": "00"}}, "keyId": 7329804974198620162}}}
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:114)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:643)
	...

<-- Full Stack Trace -->
com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1720162426, "i": 19}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1720162426, "i": 19}}, "signature": {"hash": {"$binary": {"base64": "MmtAXHNMZfwyBYPmGpx/eq4AZPc=", "subType": "00"}}, "keyId": 7329804974198620162}}}
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:265)
	at io.tapdata.mongodb.MongodbConnector.doStreamRead(MongodbConnector.java:1542)
	at io.tapdata.mongodb.MongodbConnector.streamRead(MongodbConnector.java:1536)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1720162426, "i": 19}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1720162426, "i": 19}}, "signature": {"hash": {"$binary": {"base64": "MmtAXHNMZfwyBYPmGpx/eq4AZPc=", "subType": "00"}}, "keyId": 7329804974198620162}}}
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:114)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:643)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:73)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:204)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:122)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:87)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:76)
	at com.mongodb.internal.connection.DefaultServer$OperationCountTrackingConnection.command(DefaultServer.java:288)
	at com.mongodb.internal.operation.CommandOperationHelper.createReadCommandAndExecute(CommandOperationHelper.java:239)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$4(CommandOperationHelper.java:220)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$0(OperationHelper.java:358)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$1(OperationHelper.java:357)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.withSourceAndConnection(OperationHelper.java:356)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$5(CommandOperationHelper.java:218)
	at com.mongodb.internal.async.function.RetryingSyncSupplier.get(RetryingSyncSupplier.java:67)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:223)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:204)
	at com.mongodb.internal.operation.AggregateOperationImpl.execute(AggregateOperationImpl.java:191)
	at com.mongodb.internal.operation.ChangeStreamOperation.lambda$execute$0(ChangeStreamOperation.java:187)
	at com.mongodb.internal.operation.OperationHelper.withReadConnectionSource(OperationHelper.java:321)
	at com.mongodb.internal.operation.ChangeStreamOperation.execute(ChangeStreamOperation.java:185)
	at com.mongodb.internal.operation.ChangeStreamOperation.execute(ChangeStreamOperation.java:55)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:185)
	at com.mongodb.client.internal.ChangeStreamIterableImpl.execute(ChangeStreamIterableImpl.java:212)
	at com.mongodb.client.internal.ChangeStreamIterableImpl.cursor(ChangeStreamIterableImpl.java:187)
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:111)
	... 22 more

[INFO ] 2024-07-05 14:53:47.029 - [任务 6][Mongo] - Job suspend in error handle 
[INFO ] 2024-07-05 14:53:47.188 - [任务 6][Mongo] - Node Mongo[503d3db1-cf85-4e73-9c6c-4e7fdb4caa00] running status set to false 
[INFO ] 2024-07-05 14:53:47.212 - [任务 6][Mongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-503d3db1-cf85-4e73-9c6c-4e7fdb4caa00 
[INFO ] 2024-07-05 14:53:47.212 - [任务 6][Mongo] - PDK connector node released: HazelcastSourcePdkDataNode-503d3db1-cf85-4e73-9c6c-4e7fdb4caa00 
[INFO ] 2024-07-05 14:53:47.212 - [任务 6][Mongo] - Node Mongo[503d3db1-cf85-4e73-9c6c-4e7fdb4caa00] schema data cleaned 
[INFO ] 2024-07-05 14:53:47.234 - [任务 6][Mongo] - Node Mongo[503d3db1-cf85-4e73-9c6c-4e7fdb4caa00] monitor closed 
[INFO ] 2024-07-05 14:53:47.235 - [任务 6][Mongo] - Node Mongo[503d3db1-cf85-4e73-9c6c-4e7fdb4caa00] close complete, cost 41 ms 
[INFO ] 2024-07-05 14:53:47.235 - [任务 6][字段编辑] - Node 字段编辑[534a568b-b80e-4d09-9529-b0056238cd8c] running status set to false 
[INFO ] 2024-07-05 14:53:47.238 - [任务 6][字段编辑] - Node 字段编辑[534a568b-b80e-4d09-9529-b0056238cd8c] schema data cleaned 
[INFO ] 2024-07-05 14:53:47.242 - [任务 6][字段编辑] - Node 字段编辑[534a568b-b80e-4d09-9529-b0056238cd8c] monitor closed 
[INFO ] 2024-07-05 14:53:47.242 - [任务 6][字段编辑] - Node 字段编辑[534a568b-b80e-4d09-9529-b0056238cd8c] close complete, cost 6 ms 
[INFO ] 2024-07-05 14:53:47.242 - [任务 6][Mysql] - Node Mysql[2f087017-4dd8-4b27-a25a-76dfbcfe0f7a] running status set to false 
[INFO ] 2024-07-05 14:53:47.288 - [任务 6][Mysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-2f087017-4dd8-4b27-a25a-76dfbcfe0f7a 
[INFO ] 2024-07-05 14:53:47.288 - [任务 6][Mysql] - PDK connector node released: HazelcastTargetPdkDataNode-2f087017-4dd8-4b27-a25a-76dfbcfe0f7a 
[INFO ] 2024-07-05 14:53:47.288 - [任务 6][Mysql] - Node Mysql[2f087017-4dd8-4b27-a25a-76dfbcfe0f7a] schema data cleaned 
[INFO ] 2024-07-05 14:53:47.289 - [任务 6][Mysql] - Node Mysql[2f087017-4dd8-4b27-a25a-76dfbcfe0f7a] monitor closed 
[INFO ] 2024-07-05 14:53:47.291 - [任务 6][Mysql] - Node Mysql[2f087017-4dd8-4b27-a25a-76dfbcfe0f7a] close complete, cost 48 ms 
[INFO ] 2024-07-05 14:53:51.075 - [任务 6] - Task [任务 6] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-05 14:53:51.134 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-05 14:53:51.134 - [任务 6] - Stop task milestones: 668281612501e932ee7994c1(任务 6)  
[INFO ] 2024-07-05 14:53:51.174 - [任务 6] - Stopped task aspect(s) 
[INFO ] 2024-07-05 14:53:51.175 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2024-07-05 14:53:51.382 - [任务 6] - Remove memory task client succeed, task: 任务 6[668281612501e932ee7994c1] 
[INFO ] 2024-07-05 14:53:51.383 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[668281612501e932ee7994c1] 
