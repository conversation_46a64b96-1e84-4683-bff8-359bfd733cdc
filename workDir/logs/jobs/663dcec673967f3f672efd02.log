[INFO ] 2024-05-10 15:39:05.754 - [任务 6] - Task initialization... 
[INFO ] 2024-05-10 15:39:05.757 - [任务 6] - Start task milestones: 663dcec673967f3f672efd02(任务 6) 
[INFO ] 2024-05-10 15:39:05.757 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 15:39:05.757 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 15:39:05.758 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 15:39:05.758 - [任务 6][customer2] - Node customer2[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] start preload schema,table counts: 1 
[INFO ] 2024-05-10 15:39:05.758 - [任务 6][customer2] - Node customer2[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] preload schema finished, cost 35 ms 
[INFO ] 2024-05-10 15:39:05.758 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] preload schema finished, cost 36 ms 
[INFO ] 2024-05-10 15:39:06.307 - [任务 6][customer2] - Source node "customer2" read batch size: 100 
[INFO ] 2024-05-10 15:39:06.307 - [任务 6][customer2] - Source node "customer2" event queue capacity: 200 
[INFO ] 2024-05-10 15:39:06.307 - [任务 6][customer2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-10 15:39:06.307 - [任务 6][customer2] - batch offset found: {},stream offset not found. 
[INFO ] 2024-05-10 15:39:06.307 - [任务 6][customer2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 15:39:06.357 - [任务 6][customer2] - Initial sync started 
[INFO ] 2024-05-10 15:39:06.363 - [任务 6][customer2] - Starting batch read, table name: customer2, offset: null 
[INFO ] 2024-05-10 15:39:06.368 - [任务 6][customer2] - Table customer2 is going to be initial synced 
[INFO ] 2024-05-10 15:39:06.583 - [任务 6][customer2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 15:39:12.675 - [任务 6][customer2] - Query table 'customer2' counts: 20010000 
[INFO ] 2024-05-10 15:52:51.748 - [任务 6][customer2] - Table [customer2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 15:52:51.749 - [任务 6][customer2] - Initial sync completed 
[INFO ] 2024-05-10 15:52:52.252 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] running status set to false 
[INFO ] 2024-05-10 15:52:52.255 - [任务 6][customer2] - Node customer2[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] running status set to false 
[INFO ] 2024-05-10 15:52:52.347 - [任务 6][customer2] - PDK connector node stopped: HazelcastSourcePdkDataNode-e871aa14-c5ee-4ee5-bcb9-a64a06446f0d 
[INFO ] 2024-05-10 15:52:52.354 - [任务 6][customer2] - PDK connector node stopped: HazelcastTargetPdkDataNode-66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f 
[INFO ] 2024-05-10 15:52:52.354 - [任务 6][customer2] - PDK connector node released: HazelcastSourcePdkDataNode-e871aa14-c5ee-4ee5-bcb9-a64a06446f0d 
[INFO ] 2024-05-10 15:52:52.354 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] schema data cleaned 
[INFO ] 2024-05-10 15:52:52.354 - [任务 6][customer2] - PDK connector node released: HazelcastTargetPdkDataNode-66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f 
[INFO ] 2024-05-10 15:52:52.354 - [任务 6][customer2] - Node customer2[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] schema data cleaned 
[INFO ] 2024-05-10 15:52:52.354 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] monitor closed 
[INFO ] 2024-05-10 15:52:52.354 - [任务 6][customer2] - Node customer2[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] monitor closed 
[INFO ] 2024-05-10 15:52:52.562 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] close complete, cost 119 ms 
[INFO ] 2024-05-10 15:52:52.564 - [任务 6][customer2] - Node customer2[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] close complete, cost 112 ms 
[INFO ] 2024-05-10 15:52:55.882 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 15:52:55.919 - [任务 6] - Stop task milestones: 663dcec673967f3f672efd02(任务 6)  
[INFO ] 2024-05-10 15:52:55.919 - [任务 6] - Stopped task aspect(s) 
[INFO ] 2024-05-10 15:52:55.919 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 15:52:55.946 - [任务 6] - Remove memory task client succeed, task: 任务 6[663dcec673967f3f672efd02] 
[INFO ] 2024-05-10 15:52:55.946 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[663dcec673967f3f672efd02] 
[INFO ] 2024-05-10 16:19:39.079 - [任务 6] - Task initialization... 
[INFO ] 2024-05-10 16:19:39.080 - [任务 6] - Start task milestones: 663dcec673967f3f672efd02(任务 6) 
[INFO ] 2024-05-10 16:19:39.080 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 16:19:39.080 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 16:19:39.081 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 16:19:39.081 - [任务 6][customer2] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] start preload schema,table counts: 1 
[INFO ] 2024-05-10 16:19:39.081 - [任务 6][customer2] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] preload schema finished, cost 60 ms 
[INFO ] 2024-05-10 16:19:39.081 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] preload schema finished, cost 60 ms 
[INFO ] 2024-05-10 16:19:40.088 - [任务 6][customer2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 16:19:40.125 - [任务 6][customer2] - Source node "customer2" read batch size: 100 
[INFO ] 2024-05-10 16:19:40.125 - [任务 6][customer2] - Source node "customer2" event queue capacity: 200 
[INFO ] 2024-05-10 16:19:40.125 - [任务 6][customer2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-10 16:19:40.126 - [任务 6][customer2] - batch offset found: {},stream offset not found. 
[INFO ] 2024-05-10 16:19:40.141 - [任务 6][customer2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 16:19:40.221 - [任务 6][customer2] - Initial sync started 
[INFO ] 2024-05-10 16:19:40.222 - [任务 6][customer2] - Starting batch read, table name: customer2, offset: null 
[INFO ] 2024-05-10 16:19:40.245 - [任务 6][customer2] - Table customer2 is going to be initial synced 
[INFO ] 2024-05-10 16:19:40.450 - [任务 6][customer2] - Table [customer2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 16:19:41.680 - [任务 6][customer2] - Query table 'customer2' counts: 20010000 
[INFO ] 2024-05-10 16:19:41.681 - [任务 6][customer2] - Initial sync completed 
[INFO ] 2024-05-10 16:19:42.715 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] running status set to false 
[INFO ] 2024-05-10 16:19:42.778 - [任务 6][customer2] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] running status set to false 
[INFO ] 2024-05-10 16:19:42.779 - [任务 6][customer2] - PDK connector node stopped: HazelcastTargetPdkDataNode-66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f 
[INFO ] 2024-05-10 16:19:42.779 - [任务 6][customer2] - PDK connector node released: HazelcastTargetPdkDataNode-66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f 
[INFO ] 2024-05-10 16:19:42.779 - [任务 6][customer2] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] schema data cleaned 
[INFO ] 2024-05-10 16:19:42.779 - [任务 6][customer2] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] monitor closed 
[INFO ] 2024-05-10 16:19:42.783 - [任务 6][customer2] - PDK connector node stopped: HazelcastSourcePdkDataNode-e871aa14-c5ee-4ee5-bcb9-a64a06446f0d 
[INFO ] 2024-05-10 16:19:42.784 - [任务 6][customer2] - PDK connector node released: HazelcastSourcePdkDataNode-e871aa14-c5ee-4ee5-bcb9-a64a06446f0d 
[INFO ] 2024-05-10 16:19:42.785 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] schema data cleaned 
[INFO ] 2024-05-10 16:19:42.785 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] monitor closed 
[INFO ] 2024-05-10 16:19:42.789 - [任务 6][customer2] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] close complete, cost 59 ms 
[INFO ] 2024-05-10 16:19:42.790 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] close complete, cost 80 ms 
[INFO ] 2024-05-10 16:19:47.126 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 16:19:47.129 - [任务 6] - Stop task milestones: 663dcec673967f3f672efd02(任务 6)  
[INFO ] 2024-05-10 16:19:47.135 - [任务 6] - Stopped task aspect(s) 
[INFO ] 2024-05-10 16:19:47.135 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 16:19:47.148 - [任务 6] - Remove memory task client succeed, task: 任务 6[663dcec673967f3f672efd02] 
[INFO ] 2024-05-10 16:19:47.152 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[663dcec673967f3f672efd02] 
[INFO ] 2024-05-10 16:22:56.157 - [任务 6] - Task initialization... 
[INFO ] 2024-05-10 16:22:56.158 - [任务 6] - Start task milestones: 663dcec673967f3f672efd02(任务 6) 
[INFO ] 2024-05-10 16:22:56.159 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 16:22:56.159 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 16:22:56.580 - [任务 6][customer3] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] start preload schema,table counts: 1 
[INFO ] 2024-05-10 16:22:56.582 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 16:22:56.677 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] preload schema finished, cost 93 ms 
[INFO ] 2024-05-10 16:22:56.887 - [任务 6][customer3] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] preload schema finished, cost 94 ms 
[INFO ] 2024-05-10 16:22:57.829 - [任务 6][customer2] - Source node "customer2" read batch size: 100 
[INFO ] 2024-05-10 16:22:57.830 - [任务 6][customer2] - Source node "customer2" event queue capacity: 200 
[INFO ] 2024-05-10 16:22:57.830 - [任务 6][customer2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-10 16:22:57.832 - [任务 6][customer3] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 16:22:57.846 - [任务 6][customer2] - batch offset found: {},stream offset not found. 
[INFO ] 2024-05-10 16:22:58.006 - [任务 6][customer2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 16:22:58.008 - [任务 6][customer2] - Initial sync started 
[INFO ] 2024-05-10 16:22:58.032 - [任务 6][customer2] - Starting batch read, table name: customer2, offset: null 
[INFO ] 2024-05-10 16:22:58.041 - [任务 6][customer2] - Table customer2 is going to be initial synced 
[INFO ] 2024-05-10 16:22:58.082 - [任务 6][customer2] - Table [customer2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 16:23:59.443 - [任务 6][customer2] - Query table 'customer2' counts: 20010000 
[INFO ] 2024-05-10 16:23:59.445 - [任务 6][customer2] - Initial sync completed 
[INFO ] 2024-05-10 16:23:59.942 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] running status set to false 
[INFO ] 2024-05-10 16:23:59.949 - [任务 6][customer3] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] running status set to false 
[INFO ] 2024-05-10 16:24:00.017 - [任务 6][customer2] - PDK connector node stopped: HazelcastSourcePdkDataNode-e871aa14-c5ee-4ee5-bcb9-a64a06446f0d 
[INFO ] 2024-05-10 16:24:00.025 - [任务 6][customer3] - PDK connector node stopped: HazelcastTargetPdkDataNode-66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f 
[INFO ] 2024-05-10 16:24:00.025 - [任务 6][customer2] - PDK connector node released: HazelcastSourcePdkDataNode-e871aa14-c5ee-4ee5-bcb9-a64a06446f0d 
[INFO ] 2024-05-10 16:24:00.026 - [任务 6][customer3] - PDK connector node released: HazelcastTargetPdkDataNode-66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f 
[INFO ] 2024-05-10 16:24:00.026 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] schema data cleaned 
[INFO ] 2024-05-10 16:24:00.027 - [任务 6][customer3] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] schema data cleaned 
[INFO ] 2024-05-10 16:24:00.027 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] monitor closed 
[INFO ] 2024-05-10 16:24:00.028 - [任务 6][customer3] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] monitor closed 
[INFO ] 2024-05-10 16:24:00.032 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] close complete, cost 99 ms 
[INFO ] 2024-05-10 16:24:00.032 - [任务 6][customer3] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] close complete, cost 85 ms 
[INFO ] 2024-05-10 16:24:03.849 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 16:24:03.850 - [任务 6] - Stop task milestones: 663dcec673967f3f672efd02(任务 6)  
[INFO ] 2024-05-10 16:24:03.875 - [任务 6] - Stopped task aspect(s) 
[INFO ] 2024-05-10 16:24:03.877 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 16:24:03.932 - [任务 6] - Remove memory task client succeed, task: 任务 6[663dcec673967f3f672efd02] 
[INFO ] 2024-05-10 16:24:03.932 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[663dcec673967f3f672efd02] 
[INFO ] 2024-05-10 17:10:09.489 - [任务 6] - Task initialization... 
[INFO ] 2024-05-10 17:10:09.514 - [任务 6] - Start task milestones: 663dcec673967f3f672efd02(任务 6) 
[INFO ] 2024-05-10 17:10:09.576 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 17:10:09.578 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 17:10:09.625 - [任务 6][customer3] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] start preload schema,table counts: 1 
[INFO ] 2024-05-10 17:10:09.625 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 17:10:09.663 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] preload schema finished, cost 33 ms 
[INFO ] 2024-05-10 17:10:09.664 - [任务 6][customer3] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] preload schema finished, cost 33 ms 
[INFO ] 2024-05-10 17:10:10.422 - [任务 6][customer2] - Source node "customer2" read batch size: 100 
[INFO ] 2024-05-10 17:10:10.423 - [任务 6][customer2] - Source node "customer2" event queue capacity: 200 
[INFO ] 2024-05-10 17:10:10.424 - [任务 6][customer2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-10 17:10:10.427 - [任务 6][customer2] - batch offset found: {},stream offset not found. 
[INFO ] 2024-05-10 17:10:10.428 - [任务 6][customer2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 17:10:10.470 - [任务 6][customer2] - Initial sync started 
[INFO ] 2024-05-10 17:10:10.471 - [任务 6][customer2] - Starting batch read, table name: customer2, offset: null 
[INFO ] 2024-05-10 17:10:10.603 - [任务 6][customer2] - Table customer2 is going to be initial synced 
[INFO ] 2024-05-10 17:10:10.611 - [任务 6][customer3] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 17:11:12.032 - [任务 6][customer2] - Query table 'customer2' counts: 20010000 
[INFO ] 2024-05-10 17:24:43.667 - [任务 6][customer2] - Table [customer2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 17:24:43.668 - [任务 6][customer2] - Initial sync completed 
[INFO ] 2024-05-10 17:24:44.169 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] running status set to false 
[INFO ] 2024-05-10 17:24:44.175 - [任务 6][customer3] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] running status set to false 
[INFO ] 2024-05-10 17:24:44.212 - [任务 6][customer2] - PDK connector node stopped: HazelcastSourcePdkDataNode-e871aa14-c5ee-4ee5-bcb9-a64a06446f0d 
[INFO ] 2024-05-10 17:24:44.212 - [任务 6][customer3] - PDK connector node stopped: HazelcastTargetPdkDataNode-66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f 
[INFO ] 2024-05-10 17:24:44.212 - [任务 6][customer2] - PDK connector node released: HazelcastSourcePdkDataNode-e871aa14-c5ee-4ee5-bcb9-a64a06446f0d 
[INFO ] 2024-05-10 17:24:44.212 - [任务 6][customer3] - PDK connector node released: HazelcastTargetPdkDataNode-66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f 
[INFO ] 2024-05-10 17:24:44.213 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] schema data cleaned 
[INFO ] 2024-05-10 17:24:44.213 - [任务 6][customer3] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] schema data cleaned 
[INFO ] 2024-05-10 17:24:44.213 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] monitor closed 
[INFO ] 2024-05-10 17:24:44.218 - [任务 6][customer3] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] monitor closed 
[INFO ] 2024-05-10 17:24:44.219 - [任务 6][customer3] - Node customer3[66ee3b5e-7cb7-48c7-95af-d56c12eb8b7f] close complete, cost 60 ms 
[INFO ] 2024-05-10 17:24:44.219 - [任务 6][customer2] - Node customer2[e871aa14-c5ee-4ee5-bcb9-a64a06446f0d] close complete, cost 65 ms 
[INFO ] 2024-05-10 17:24:46.491 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 17:24:46.531 - [任务 6] - Stop task milestones: 663dcec673967f3f672efd02(任务 6)  
[INFO ] 2024-05-10 17:24:46.534 - [任务 6] - Stopped task aspect(s) 
[INFO ] 2024-05-10 17:24:46.534 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 17:24:46.554 - [任务 6] - Remove memory task client succeed, task: 任务 6[663dcec673967f3f672efd02] 
[INFO ] 2024-05-10 17:24:46.557 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[663dcec673967f3f672efd02] 
