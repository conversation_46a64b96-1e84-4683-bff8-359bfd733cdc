[INFO ] 2024-03-27 16:12:12.132 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:12.135 - [任务 25(100)][86a7317b-b16a-4fc8-bb18-aef49240391b] - Node 86a7317b-b16a-4fc8-bb18-aef49240391b[86a7317b-b16a-4fc8-bb18-aef49240391b] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:12:12.136 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] preload schema finished, cost 4 ms 
[INFO ] 2024-03-27 16:12:12.136 - [任务 25(100)][86a7317b-b16a-4fc8-bb18-aef49240391b] - Node 86a7317b-b16a-4fc8-bb18-aef49240391b[86a7317b-b16a-4fc8-bb18-aef49240391b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:12.136 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:12.142 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:12.142 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:12.142 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:12.142 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:12.146 - [任务 25(100)][f613aa0c-8188-49f9-bec6-628beb609793] - Node f613aa0c-8188-49f9-bec6-628beb609793[f613aa0c-8188-49f9-bec6-628beb609793] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:12:12.146 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:12.146 - [任务 25(100)][f613aa0c-8188-49f9-bec6-628beb609793] - Node f613aa0c-8188-49f9-bec6-628beb609793[f613aa0c-8188-49f9-bec6-628beb609793] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:12.920 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] running status set to false 
[INFO ] 2024-03-27 16:12:12.954 - [任务 25(100)][CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:12.954 - [任务 25(100)][CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:12.954 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] schema data cleaned 
[INFO ] 2024-03-27 16:12:12.954 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] monitor closed 
[INFO ] 2024-03-27 16:12:12.954 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] close complete, cost 31 ms 
[INFO ] 2024-03-27 16:12:12.972 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] running status set to false 
[INFO ] 2024-03-27 16:12:12.995 - [任务 25(100)][f613aa0c-8188-49f9-bec6-628beb609793] - Node f613aa0c-8188-49f9-bec6-628beb609793[f613aa0c-8188-49f9-bec6-628beb609793] running status set to false 
[INFO ] 2024-03-27 16:12:12.996 - [任务 25(100)][f613aa0c-8188-49f9-bec6-628beb609793] - Node f613aa0c-8188-49f9-bec6-628beb609793[f613aa0c-8188-49f9-bec6-628beb609793] schema data cleaned 
[INFO ] 2024-03-27 16:12:12.996 - [任务 25(100)][f613aa0c-8188-49f9-bec6-628beb609793] - Node f613aa0c-8188-49f9-bec6-628beb609793[f613aa0c-8188-49f9-bec6-628beb609793] monitor closed 
[INFO ] 2024-03-27 16:12:12.996 - [任务 25(100)][f613aa0c-8188-49f9-bec6-628beb609793] - Node f613aa0c-8188-49f9-bec6-628beb609793[f613aa0c-8188-49f9-bec6-628beb609793] close complete, cost 2 ms 
[INFO ] 2024-03-27 16:12:12.998 - [任务 25(100)][增强JS] - PDK connector node stopped: ScriptExecutor-testMysql-ec60686e-acfc-4fa8-9c04-0b9a7941084b 
[INFO ] 2024-03-27 16:12:12.998 - [任务 25(100)][增强JS] - PDK connector node released: ScriptExecutor-testMysql-ec60686e-acfc-4fa8-9c04-0b9a7941084b 
[INFO ] 2024-03-27 16:12:12.999 - [任务 25(100)][增强JS] - [ScriptExecutorsManager-6603d4cb8b5bca60f72df65d-dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1-65fd534767def503a78ea02d] schema data cleaned 
[INFO ] 2024-03-27 16:12:13.002 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] schema data cleaned 
[INFO ] 2024-03-27 16:12:13.002 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] monitor closed 
[INFO ] 2024-03-27 16:12:13.002 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] close complete, cost 34 ms 
[INFO ] 2024-03-27 16:12:13.008 - [任务 25(100)] - load tapTable task 6603d4cb8b5bca60f72df65d-f613aa0c-8188-49f9-bec6-628beb609793 complete, cost 919ms 
[INFO ] 2024-03-27 16:12:13.025 - [任务 25(100)][399bcf5a-775b-4740-a4a6-0d4f2d7861c1] - Node 399bcf5a-775b-4740-a4a6-0d4f2d7861c1[399bcf5a-775b-4740-a4a6-0d4f2d7861c1] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:12:13.029 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:13.029 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:13.029 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:13.029 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:13.029 - [任务 25(100)][399bcf5a-775b-4740-a4a6-0d4f2d7861c1] - Node 399bcf5a-775b-4740-a4a6-0d4f2d7861c1[399bcf5a-775b-4740-a4a6-0d4f2d7861c1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:13.291 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] running status set to false 
[INFO ] 2024-03-27 16:12:13.309 - [任务 25(100)][CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:13.309 - [任务 25(100)][CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:13.309 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] schema data cleaned 
[INFO ] 2024-03-27 16:12:13.309 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] monitor closed 
[INFO ] 2024-03-27 16:12:13.311 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] close complete, cost 30 ms 
[INFO ] 2024-03-27 16:12:13.358 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] running status set to false 
[INFO ] 2024-03-27 16:12:13.391 - [任务 25(100)][CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:13.391 - [任务 25(100)][CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:13.391 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] schema data cleaned 
[INFO ] 2024-03-27 16:12:13.391 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] monitor closed 
[INFO ] 2024-03-27 16:12:13.396 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] close complete, cost 43 ms 
[INFO ] 2024-03-27 16:12:13.429 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] running status set to false 
[INFO ] 2024-03-27 16:12:13.430 - [任务 25(100)][86a7317b-b16a-4fc8-bb18-aef49240391b] - Node 86a7317b-b16a-4fc8-bb18-aef49240391b[86a7317b-b16a-4fc8-bb18-aef49240391b] running status set to false 
[INFO ] 2024-03-27 16:12:13.430 - [任务 25(100)][86a7317b-b16a-4fc8-bb18-aef49240391b] - Node 86a7317b-b16a-4fc8-bb18-aef49240391b[86a7317b-b16a-4fc8-bb18-aef49240391b] schema data cleaned 
[INFO ] 2024-03-27 16:12:13.431 - [任务 25(100)][86a7317b-b16a-4fc8-bb18-aef49240391b] - Node 86a7317b-b16a-4fc8-bb18-aef49240391b[86a7317b-b16a-4fc8-bb18-aef49240391b] monitor closed 
[INFO ] 2024-03-27 16:12:13.431 - [任务 25(100)][86a7317b-b16a-4fc8-bb18-aef49240391b] - Node 86a7317b-b16a-4fc8-bb18-aef49240391b[86a7317b-b16a-4fc8-bb18-aef49240391b] close complete, cost 0 ms 
[INFO ] 2024-03-27 16:12:13.449 - [任务 25(100)][增强JS] - PDK connector node stopped: ScriptExecutor-testMysql-7caaa522-a593-469c-a45f-e9bf968b99d8 
[INFO ] 2024-03-27 16:12:13.449 - [任务 25(100)][增强JS] - PDK connector node released: ScriptExecutor-testMysql-7caaa522-a593-469c-a45f-e9bf968b99d8 
[INFO ] 2024-03-27 16:12:13.449 - [任务 25(100)][增强JS] - [ScriptExecutorsManager-6603d4cb8b5bca60f72df65d-dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1-65fd534767def503a78ea02d] schema data cleaned 
[INFO ] 2024-03-27 16:12:13.450 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] schema data cleaned 
[INFO ] 2024-03-27 16:12:13.451 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] monitor closed 
[INFO ] 2024-03-27 16:12:13.451 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] close complete, cost 26 ms 
[INFO ] 2024-03-27 16:12:13.456 - [任务 25(100)] - load tapTable task 6603d4cb8b5bca60f72df65d-86a7317b-b16a-4fc8-bb18-aef49240391b complete, cost 1511ms 
[INFO ] 2024-03-27 16:12:13.527 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] running status set to false 
[INFO ] 2024-03-27 16:12:13.539 - [任务 25(100)][399bcf5a-775b-4740-a4a6-0d4f2d7861c1] - Node 399bcf5a-775b-4740-a4a6-0d4f2d7861c1[399bcf5a-775b-4740-a4a6-0d4f2d7861c1] running status set to false 
[INFO ] 2024-03-27 16:12:13.540 - [任务 25(100)][增强JS] - PDK connector node stopped: ScriptExecutor-testMysql-563db1ab-a696-4c04-8b1b-cbb39b5a89d7 
[INFO ] 2024-03-27 16:12:13.540 - [任务 25(100)][399bcf5a-775b-4740-a4a6-0d4f2d7861c1] - Node 399bcf5a-775b-4740-a4a6-0d4f2d7861c1[399bcf5a-775b-4740-a4a6-0d4f2d7861c1] schema data cleaned 
[INFO ] 2024-03-27 16:12:13.540 - [任务 25(100)][增强JS] - PDK connector node released: ScriptExecutor-testMysql-563db1ab-a696-4c04-8b1b-cbb39b5a89d7 
[INFO ] 2024-03-27 16:12:13.540 - [任务 25(100)][399bcf5a-775b-4740-a4a6-0d4f2d7861c1] - Node 399bcf5a-775b-4740-a4a6-0d4f2d7861c1[399bcf5a-775b-4740-a4a6-0d4f2d7861c1] monitor closed 
[INFO ] 2024-03-27 16:12:13.540 - [任务 25(100)][增强JS] - [ScriptExecutorsManager-6603d4cb8b5bca60f72df65d-dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1-65fd534767def503a78ea02d] schema data cleaned 
[INFO ] 2024-03-27 16:12:13.540 - [任务 25(100)][399bcf5a-775b-4740-a4a6-0d4f2d7861c1] - Node 399bcf5a-775b-4740-a4a6-0d4f2d7861c1[399bcf5a-775b-4740-a4a6-0d4f2d7861c1] close complete, cost 2 ms 
[INFO ] 2024-03-27 16:12:13.541 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] schema data cleaned 
[INFO ] 2024-03-27 16:12:13.541 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] monitor closed 
[INFO ] 2024-03-27 16:12:13.541 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] close complete, cost 14 ms 
[INFO ] 2024-03-27 16:12:13.544 - [任务 25(100)] - load tapTable task 6603d4cb8b5bca60f72df65d-399bcf5a-775b-4740-a4a6-0d4f2d7861c1 complete, cost 629ms 
[INFO ] 2024-03-27 16:12:15.729 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:15.729 - [任务 25(100)][5dbe69b8-0beb-499e-8b67-45e855e32863] - Node 5dbe69b8-0beb-499e-8b67-45e855e32863[5dbe69b8-0beb-499e-8b67-45e855e32863] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:12:15.729 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:15.729 - [任务 25(100)][5dbe69b8-0beb-499e-8b67-45e855e32863] - Node 5dbe69b8-0beb-499e-8b67-45e855e32863[5dbe69b8-0beb-499e-8b67-45e855e32863] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:15.730 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:15.730 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:15.817 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:15.817 - [任务 25(100)][a8326276-ba67-43f4-b9b8-f7e8e21ecce3] - Node a8326276-ba67-43f4-b9b8-f7e8e21ecce3[a8326276-ba67-43f4-b9b8-f7e8e21ecce3] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:12:15.817 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:15.817 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:15.818 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:15.818 - [任务 25(100)][a8326276-ba67-43f4-b9b8-f7e8e21ecce3] - Node a8326276-ba67-43f4-b9b8-f7e8e21ecce3[a8326276-ba67-43f4-b9b8-f7e8e21ecce3] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:15.956 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] running status set to false 
[INFO ] 2024-03-27 16:12:15.966 - [任务 25(100)][CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:15.966 - [任务 25(100)][CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:15.966 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] schema data cleaned 
[INFO ] 2024-03-27 16:12:15.966 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] monitor closed 
[INFO ] 2024-03-27 16:12:15.966 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] close complete, cost 10 ms 
[INFO ] 2024-03-27 16:12:16.263 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] running status set to false 
[INFO ] 2024-03-27 16:12:16.290 - [任务 25(100)][CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:16.291 - [任务 25(100)][CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:16.292 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] schema data cleaned 
[INFO ] 2024-03-27 16:12:16.293 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] monitor closed 
[INFO ] 2024-03-27 16:12:16.293 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] close complete, cost 29 ms 
[INFO ] 2024-03-27 16:12:16.302 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] running status set to false 
[INFO ] 2024-03-27 16:12:16.319 - [任务 25(100)][a8326276-ba67-43f4-b9b8-f7e8e21ecce3] - Node a8326276-ba67-43f4-b9b8-f7e8e21ecce3[a8326276-ba67-43f4-b9b8-f7e8e21ecce3] running status set to false 
[INFO ] 2024-03-27 16:12:16.321 - [任务 25(100)][a8326276-ba67-43f4-b9b8-f7e8e21ecce3] - Node a8326276-ba67-43f4-b9b8-f7e8e21ecce3[a8326276-ba67-43f4-b9b8-f7e8e21ecce3] schema data cleaned 
[INFO ] 2024-03-27 16:12:16.321 - [任务 25(100)][a8326276-ba67-43f4-b9b8-f7e8e21ecce3] - Node a8326276-ba67-43f4-b9b8-f7e8e21ecce3[a8326276-ba67-43f4-b9b8-f7e8e21ecce3] monitor closed 
[INFO ] 2024-03-27 16:12:16.321 - [任务 25(100)][a8326276-ba67-43f4-b9b8-f7e8e21ecce3] - Node a8326276-ba67-43f4-b9b8-f7e8e21ecce3[a8326276-ba67-43f4-b9b8-f7e8e21ecce3] close complete, cost 4 ms 
[INFO ] 2024-03-27 16:12:16.325 - [任务 25(100)][增强JS] - PDK connector node stopped: ScriptExecutor-testMysql-dfb617e6-0cd2-4b22-9b96-a29d9ba60c15 
[INFO ] 2024-03-27 16:12:16.325 - [任务 25(100)][增强JS] - PDK connector node released: ScriptExecutor-testMysql-dfb617e6-0cd2-4b22-9b96-a29d9ba60c15 
[INFO ] 2024-03-27 16:12:16.325 - [任务 25(100)][增强JS] - [ScriptExecutorsManager-6603d4cb8b5bca60f72df65d-dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1-65fd534767def503a78ea02d] schema data cleaned 
[INFO ] 2024-03-27 16:12:16.327 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] schema data cleaned 
[INFO ] 2024-03-27 16:12:16.328 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] monitor closed 
[INFO ] 2024-03-27 16:12:16.328 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] close complete, cost 26 ms 
[INFO ] 2024-03-27 16:12:16.330 - [任务 25(100)] - load tapTable task 6603d4cb8b5bca60f72df65d-a8326276-ba67-43f4-b9b8-f7e8e21ecce3 complete, cost 537ms 
[INFO ] 2024-03-27 16:12:16.519 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] running status set to false 
[INFO ] 2024-03-27 16:12:16.530 - [任务 25(100)][5dbe69b8-0beb-499e-8b67-45e855e32863] - Node 5dbe69b8-0beb-499e-8b67-45e855e32863[5dbe69b8-0beb-499e-8b67-45e855e32863] running status set to false 
[INFO ] 2024-03-27 16:12:16.534 - [任务 25(100)][5dbe69b8-0beb-499e-8b67-45e855e32863] - Node 5dbe69b8-0beb-499e-8b67-45e855e32863[5dbe69b8-0beb-499e-8b67-45e855e32863] schema data cleaned 
[INFO ] 2024-03-27 16:12:16.534 - [任务 25(100)][5dbe69b8-0beb-499e-8b67-45e855e32863] - Node 5dbe69b8-0beb-499e-8b67-45e855e32863[5dbe69b8-0beb-499e-8b67-45e855e32863] monitor closed 
[INFO ] 2024-03-27 16:12:16.534 - [任务 25(100)][5dbe69b8-0beb-499e-8b67-45e855e32863] - Node 5dbe69b8-0beb-499e-8b67-45e855e32863[5dbe69b8-0beb-499e-8b67-45e855e32863] close complete, cost 1 ms 
[INFO ] 2024-03-27 16:12:16.535 - [任务 25(100)][增强JS] - PDK connector node stopped: ScriptExecutor-testMysql-118e9de5-4858-4f29-9eea-d60a9feb3c2b 
[INFO ] 2024-03-27 16:12:16.535 - [任务 25(100)][增强JS] - PDK connector node released: ScriptExecutor-testMysql-118e9de5-4858-4f29-9eea-d60a9feb3c2b 
[INFO ] 2024-03-27 16:12:16.535 - [任务 25(100)][增强JS] - [ScriptExecutorsManager-6603d4cb8b5bca60f72df65d-dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1-65fd534767def503a78ea02d] schema data cleaned 
[INFO ] 2024-03-27 16:12:16.537 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] schema data cleaned 
[INFO ] 2024-03-27 16:12:16.538 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] monitor closed 
[INFO ] 2024-03-27 16:12:16.538 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] close complete, cost 25 ms 
[INFO ] 2024-03-27 16:12:16.543 - [任务 25(100)] - load tapTable task 6603d4cb8b5bca60f72df65d-5dbe69b8-0beb-499e-8b67-45e855e32863 complete, cost 855ms 
[INFO ] 2024-03-27 16:12:18.969 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:18.970 - [任务 25(100)][78979bb0-d283-4aca-879c-fee48f81109c] - Node 78979bb0-d283-4aca-879c-fee48f81109c[78979bb0-d283-4aca-879c-fee48f81109c] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:12:18.970 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:18.970 - [任务 25(100)][78979bb0-d283-4aca-879c-fee48f81109c] - Node 78979bb0-d283-4aca-879c-fee48f81109c[78979bb0-d283-4aca-879c-fee48f81109c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:18.970 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:18.970 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:19.184 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] running status set to false 
[INFO ] 2024-03-27 16:12:19.197 - [任务 25(100)][CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:19.197 - [任务 25(100)][CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:19.197 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] schema data cleaned 
[INFO ] 2024-03-27 16:12:19.197 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] monitor closed 
[INFO ] 2024-03-27 16:12:19.197 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] close complete, cost 13 ms 
[INFO ] 2024-03-27 16:12:19.347 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] running status set to false 
[INFO ] 2024-03-27 16:12:19.359 - [任务 25(100)][78979bb0-d283-4aca-879c-fee48f81109c] - Node 78979bb0-d283-4aca-879c-fee48f81109c[78979bb0-d283-4aca-879c-fee48f81109c] running status set to false 
[INFO ] 2024-03-27 16:12:19.359 - [任务 25(100)][78979bb0-d283-4aca-879c-fee48f81109c] - Node 78979bb0-d283-4aca-879c-fee48f81109c[78979bb0-d283-4aca-879c-fee48f81109c] schema data cleaned 
[INFO ] 2024-03-27 16:12:19.359 - [任务 25(100)][78979bb0-d283-4aca-879c-fee48f81109c] - Node 78979bb0-d283-4aca-879c-fee48f81109c[78979bb0-d283-4aca-879c-fee48f81109c] monitor closed 
[INFO ] 2024-03-27 16:12:19.359 - [任务 25(100)][78979bb0-d283-4aca-879c-fee48f81109c] - Node 78979bb0-d283-4aca-879c-fee48f81109c[78979bb0-d283-4aca-879c-fee48f81109c] close complete, cost 0 ms 
[INFO ] 2024-03-27 16:12:19.359 - [任务 25(100)][增强JS] - PDK connector node stopped: ScriptExecutor-testMysql-bfb0bba3-cf49-4f48-a895-bb2b94b7cfff 
[INFO ] 2024-03-27 16:12:19.359 - [任务 25(100)][增强JS] - PDK connector node released: ScriptExecutor-testMysql-bfb0bba3-cf49-4f48-a895-bb2b94b7cfff 
[INFO ] 2024-03-27 16:12:19.360 - [任务 25(100)][增强JS] - [ScriptExecutorsManager-6603d4cb8b5bca60f72df65d-dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1-65fd534767def503a78ea02d] schema data cleaned 
[INFO ] 2024-03-27 16:12:19.360 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] schema data cleaned 
[INFO ] 2024-03-27 16:12:19.360 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] monitor closed 
[INFO ] 2024-03-27 16:12:19.360 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] close complete, cost 13 ms 
[INFO ] 2024-03-27 16:12:19.367 - [任务 25(100)] - load tapTable task 6603d4cb8b5bca60f72df65d-78979bb0-d283-4aca-879c-fee48f81109c complete, cost 445ms 
[INFO ] 2024-03-27 16:12:20.894 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:20.896 - [任务 25(100)][530cdb0e-ccd4-43b1-b4af-c51434195f5e] - Node 530cdb0e-ccd4-43b1-b4af-c51434195f5e[530cdb0e-ccd4-43b1-b4af-c51434195f5e] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:12:20.896 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:20.896 - [任务 25(100)][530cdb0e-ccd4-43b1-b4af-c51434195f5e] - Node 530cdb0e-ccd4-43b1-b4af-c51434195f5e[530cdb0e-ccd4-43b1-b4af-c51434195f5e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:20.896 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:20.896 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 16:12:21.154 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] running status set to false 
[INFO ] 2024-03-27 16:12:21.171 - [任务 25(100)][CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:21.172 - [任务 25(100)][CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:21.172 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] schema data cleaned 
[INFO ] 2024-03-27 16:12:21.172 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] monitor closed 
[INFO ] 2024-03-27 16:12:21.172 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] close complete, cost 21 ms 
[INFO ] 2024-03-27 16:12:21.296 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] running status set to false 
[INFO ] 2024-03-27 16:12:21.306 - [任务 25(100)][530cdb0e-ccd4-43b1-b4af-c51434195f5e] - Node 530cdb0e-ccd4-43b1-b4af-c51434195f5e[530cdb0e-ccd4-43b1-b4af-c51434195f5e] running status set to false 
[INFO ] 2024-03-27 16:12:21.306 - [任务 25(100)][530cdb0e-ccd4-43b1-b4af-c51434195f5e] - Node 530cdb0e-ccd4-43b1-b4af-c51434195f5e[530cdb0e-ccd4-43b1-b4af-c51434195f5e] schema data cleaned 
[INFO ] 2024-03-27 16:12:21.306 - [任务 25(100)][530cdb0e-ccd4-43b1-b4af-c51434195f5e] - Node 530cdb0e-ccd4-43b1-b4af-c51434195f5e[530cdb0e-ccd4-43b1-b4af-c51434195f5e] monitor closed 
[INFO ] 2024-03-27 16:12:21.306 - [任务 25(100)][530cdb0e-ccd4-43b1-b4af-c51434195f5e] - Node 530cdb0e-ccd4-43b1-b4af-c51434195f5e[530cdb0e-ccd4-43b1-b4af-c51434195f5e] close complete, cost 0 ms 
[INFO ] 2024-03-27 16:12:21.308 - [任务 25(100)][增强JS] - PDK connector node stopped: ScriptExecutor-testMysql-c4dd35af-302d-446d-abda-d0a0d7a6a120 
[INFO ] 2024-03-27 16:12:21.308 - [任务 25(100)][增强JS] - PDK connector node released: ScriptExecutor-testMysql-c4dd35af-302d-446d-abda-d0a0d7a6a120 
[INFO ] 2024-03-27 16:12:21.309 - [任务 25(100)][增强JS] - [ScriptExecutorsManager-6603d4cb8b5bca60f72df65d-dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1-65fd534767def503a78ea02d] schema data cleaned 
[INFO ] 2024-03-27 16:12:21.311 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] schema data cleaned 
[INFO ] 2024-03-27 16:12:21.312 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] monitor closed 
[INFO ] 2024-03-27 16:12:21.312 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] close complete, cost 16 ms 
[INFO ] 2024-03-27 16:12:21.319 - [任务 25(100)] - load tapTable task 6603d4cb8b5bca60f72df65d-530cdb0e-ccd4-43b1-b4af-c51434195f5e complete, cost 498ms 
[INFO ] 2024-03-27 16:12:21.857 - [任务 25(100)][39aea981-b95d-4007-9865-6b832559d34e] - Node 39aea981-b95d-4007-9865-6b832559d34e[39aea981-b95d-4007-9865-6b832559d34e] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:12:21.859 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:21.859 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:21.860 - [任务 25(100)][39aea981-b95d-4007-9865-6b832559d34e] - Node 39aea981-b95d-4007-9865-6b832559d34e[39aea981-b95d-4007-9865-6b832559d34e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:21.860 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:21.860 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:22.110 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] running status set to false 
[INFO ] 2024-03-27 16:12:22.125 - [任务 25(100)][CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:22.125 - [任务 25(100)][CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:22.125 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] schema data cleaned 
[INFO ] 2024-03-27 16:12:22.125 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] monitor closed 
[INFO ] 2024-03-27 16:12:22.126 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] close complete, cost 16 ms 
[INFO ] 2024-03-27 16:12:22.273 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] running status set to false 
[INFO ] 2024-03-27 16:12:22.283 - [任务 25(100)][39aea981-b95d-4007-9865-6b832559d34e] - Node 39aea981-b95d-4007-9865-6b832559d34e[39aea981-b95d-4007-9865-6b832559d34e] running status set to false 
[INFO ] 2024-03-27 16:12:22.283 - [任务 25(100)][39aea981-b95d-4007-9865-6b832559d34e] - Node 39aea981-b95d-4007-9865-6b832559d34e[39aea981-b95d-4007-9865-6b832559d34e] schema data cleaned 
[INFO ] 2024-03-27 16:12:22.283 - [任务 25(100)][39aea981-b95d-4007-9865-6b832559d34e] - Node 39aea981-b95d-4007-9865-6b832559d34e[39aea981-b95d-4007-9865-6b832559d34e] monitor closed 
[INFO ] 2024-03-27 16:12:22.283 - [任务 25(100)][39aea981-b95d-4007-9865-6b832559d34e] - Node 39aea981-b95d-4007-9865-6b832559d34e[39aea981-b95d-4007-9865-6b832559d34e] close complete, cost 0 ms 
[INFO ] 2024-03-27 16:12:22.287 - [任务 25(100)][增强JS] - PDK connector node stopped: ScriptExecutor-testMysql-f742f820-e36f-4310-afed-722c70fcd959 
[INFO ] 2024-03-27 16:12:22.287 - [任务 25(100)][增强JS] - PDK connector node released: ScriptExecutor-testMysql-f742f820-e36f-4310-afed-722c70fcd959 
[INFO ] 2024-03-27 16:12:22.287 - [任务 25(100)][增强JS] - [ScriptExecutorsManager-6603d4cb8b5bca60f72df65d-dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1-65fd534767def503a78ea02d] schema data cleaned 
[INFO ] 2024-03-27 16:12:22.289 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] schema data cleaned 
[INFO ] 2024-03-27 16:12:22.289 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] monitor closed 
[INFO ] 2024-03-27 16:12:22.289 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] close complete, cost 16 ms 
[INFO ] 2024-03-27 16:12:22.296 - [任务 25(100)] - load tapTable task 6603d4cb8b5bca60f72df65d-39aea981-b95d-4007-9865-6b832559d34e complete, cost 528ms 
[INFO ] 2024-03-27 16:12:26.952 - [任务 25(100)][ea6ea5a7-091b-436d-a572-8ca1d5d38df4] - Node ea6ea5a7-091b-436d-a572-8ca1d5d38df4[ea6ea5a7-091b-436d-a572-8ca1d5d38df4] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:12:26.953 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:26.953 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:26.953 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:26.953 - [任务 25(100)][ea6ea5a7-091b-436d-a572-8ca1d5d38df4] - Node ea6ea5a7-091b-436d-a572-8ca1d5d38df4[ea6ea5a7-091b-436d-a572-8ca1d5d38df4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:26.953 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:27.196 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] running status set to false 
[INFO ] 2024-03-27 16:12:27.214 - [任务 25(100)][CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:27.215 - [任务 25(100)][CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:27.215 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] schema data cleaned 
[INFO ] 2024-03-27 16:12:27.215 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] monitor closed 
[INFO ] 2024-03-27 16:12:27.216 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] close complete, cost 25 ms 
[INFO ] 2024-03-27 16:12:27.354 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] running status set to false 
[INFO ] 2024-03-27 16:12:27.366 - [任务 25(100)][ea6ea5a7-091b-436d-a572-8ca1d5d38df4] - Node ea6ea5a7-091b-436d-a572-8ca1d5d38df4[ea6ea5a7-091b-436d-a572-8ca1d5d38df4] running status set to false 
[INFO ] 2024-03-27 16:12:27.367 - [任务 25(100)][ea6ea5a7-091b-436d-a572-8ca1d5d38df4] - Node ea6ea5a7-091b-436d-a572-8ca1d5d38df4[ea6ea5a7-091b-436d-a572-8ca1d5d38df4] schema data cleaned 
[INFO ] 2024-03-27 16:12:27.367 - [任务 25(100)][ea6ea5a7-091b-436d-a572-8ca1d5d38df4] - Node ea6ea5a7-091b-436d-a572-8ca1d5d38df4[ea6ea5a7-091b-436d-a572-8ca1d5d38df4] monitor closed 
[INFO ] 2024-03-27 16:12:27.367 - [任务 25(100)][ea6ea5a7-091b-436d-a572-8ca1d5d38df4] - Node ea6ea5a7-091b-436d-a572-8ca1d5d38df4[ea6ea5a7-091b-436d-a572-8ca1d5d38df4] close complete, cost 0 ms 
[INFO ] 2024-03-27 16:12:27.369 - [任务 25(100)][增强JS] - PDK connector node stopped: ScriptExecutor-testMysql-f00cf827-e90f-46f9-b46c-18d797f77a61 
[INFO ] 2024-03-27 16:12:27.369 - [任务 25(100)][增强JS] - PDK connector node released: ScriptExecutor-testMysql-f00cf827-e90f-46f9-b46c-18d797f77a61 
[INFO ] 2024-03-27 16:12:27.369 - [任务 25(100)][增强JS] - [ScriptExecutorsManager-6603d4cb8b5bca60f72df65d-dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1-65fd534767def503a78ea02d] schema data cleaned 
[INFO ] 2024-03-27 16:12:27.372 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] schema data cleaned 
[INFO ] 2024-03-27 16:12:27.372 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] monitor closed 
[INFO ] 2024-03-27 16:12:27.373 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] close complete, cost 18 ms 
[INFO ] 2024-03-27 16:12:27.380 - [任务 25(100)] - load tapTable task 6603d4cb8b5bca60f72df65d-ea6ea5a7-091b-436d-a572-8ca1d5d38df4 complete, cost 500ms 
[INFO ] 2024-03-27 16:12:53.987 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:53.989 - [任务 25(100)][d7284e7a-ddd3-4c8c-b121-5efc639ac721] - Node d7284e7a-ddd3-4c8c-b121-5efc639ac721[d7284e7a-ddd3-4c8c-b121-5efc639ac721] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:12:53.989 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:12:53.989 - [任务 25(100)][d7284e7a-ddd3-4c8c-b121-5efc639ac721] - Node d7284e7a-ddd3-4c8c-b121-5efc639ac721[d7284e7a-ddd3-4c8c-b121-5efc639ac721] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:53.989 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:53.989 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:12:54.263 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] running status set to false 
[INFO ] 2024-03-27 16:12:54.277 - [任务 25(100)][CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:54.278 - [任务 25(100)][CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:12:54.278 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] schema data cleaned 
[INFO ] 2024-03-27 16:12:54.278 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] monitor closed 
[INFO ] 2024-03-27 16:12:54.279 - [任务 25(100)][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] close complete, cost 23 ms 
[INFO ] 2024-03-27 16:12:54.429 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] running status set to false 
[INFO ] 2024-03-27 16:12:54.429 - [任务 25(100)][d7284e7a-ddd3-4c8c-b121-5efc639ac721] - Node d7284e7a-ddd3-4c8c-b121-5efc639ac721[d7284e7a-ddd3-4c8c-b121-5efc639ac721] running status set to false 
[INFO ] 2024-03-27 16:12:54.433 - [任务 25(100)][d7284e7a-ddd3-4c8c-b121-5efc639ac721] - Node d7284e7a-ddd3-4c8c-b121-5efc639ac721[d7284e7a-ddd3-4c8c-b121-5efc639ac721] schema data cleaned 
[INFO ] 2024-03-27 16:12:54.433 - [任务 25(100)][d7284e7a-ddd3-4c8c-b121-5efc639ac721] - Node d7284e7a-ddd3-4c8c-b121-5efc639ac721[d7284e7a-ddd3-4c8c-b121-5efc639ac721] monitor closed 
[INFO ] 2024-03-27 16:12:54.433 - [任务 25(100)][d7284e7a-ddd3-4c8c-b121-5efc639ac721] - Node d7284e7a-ddd3-4c8c-b121-5efc639ac721[d7284e7a-ddd3-4c8c-b121-5efc639ac721] close complete, cost 0 ms 
[INFO ] 2024-03-27 16:12:54.443 - [任务 25(100)][增强JS] - PDK connector node stopped: ScriptExecutor-testMysql-d9dea712-2f45-440e-af2b-1c47a0205ad1 
[INFO ] 2024-03-27 16:12:54.443 - [任务 25(100)][增强JS] - PDK connector node released: ScriptExecutor-testMysql-d9dea712-2f45-440e-af2b-1c47a0205ad1 
[INFO ] 2024-03-27 16:12:54.444 - [任务 25(100)][增强JS] - [ScriptExecutorsManager-6603d4cb8b5bca60f72df65d-dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1-65fd534767def503a78ea02d] schema data cleaned 
[INFO ] 2024-03-27 16:12:54.445 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] schema data cleaned 
[INFO ] 2024-03-27 16:12:54.446 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] monitor closed 
[INFO ] 2024-03-27 16:12:54.446 - [任务 25(100)][增强JS] - Node 增强JS[dcaa07ac-98f9-42a3-bb8e-7303d7e0b6e1] close complete, cost 18 ms 
[INFO ] 2024-03-27 16:12:54.454 - [任务 25(100)] - load tapTable task 6603d4cb8b5bca60f72df65d-d7284e7a-ddd3-4c8c-b121-5efc639ac721 complete, cost 534ms 
