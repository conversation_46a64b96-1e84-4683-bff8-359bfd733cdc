[INFO ] 2024-07-12 19:09:16.873 - [测试时间] - Task initialization... 
[INFO ] 2024-07-12 19:09:16.875 - [测试时间] - Start task milestones: 66910e44df7eaa0ea4a21743(测试时间) 
[INFO ] 2024-07-12 19:09:16.996 - [测试时间] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-12 19:09:16.996 - [测试时间] - The engine receives 测试时间 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-12 19:09:17.030 - [测试时间][TestMysql] - Node TestMysql[874fa2f0-65e9-4db2-9fad-ff73126ad6a5] start preload schema,table counts: 1 
[INFO ] 2024-07-12 19:09:17.030 - [测试时间][SourceMongo] - Node SourceMongo[776ddbcd-0bb0-4ab5-8cbe-b5ac69a9b0a6] start preload schema,table counts: 1 
[INFO ] 2024-07-12 19:09:17.030 - [测试时间][TestMysql] - Node TestMysql[874fa2f0-65e9-4db2-9fad-ff73126ad6a5] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 19:09:17.030 - [测试时间][SourceMongo] - Node SourceMongo[776ddbcd-0bb0-4ab5-8cbe-b5ac69a9b0a6] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 19:09:17.670 - [测试时间][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-07-12 19:09:17.670 - [测试时间][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-07-12 19:09:17.671 - [测试时间][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-12 19:09:17.807 - [测试时间][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1720782557,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-12 19:09:17.807 - [测试时间][SourceMongo] - Initial sync started 
[INFO ] 2024-07-12 19:09:17.814 - [测试时间][SourceMongo] - Starting batch read, table name: repliceTime, offset: null 
[INFO ] 2024-07-12 19:09:17.814 - [测试时间][SourceMongo] - Table repliceTime is going to be initial synced 
[INFO ] 2024-07-12 19:09:17.818 - [测试时间][SourceMongo] - Query table 'repliceTime' counts: 1 
[INFO ] 2024-07-12 19:09:17.818 - [测试时间][SourceMongo] - Table [repliceTime] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-12 19:09:17.818 - [测试时间][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-12 19:09:17.818 - [测试时间][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-12 19:09:17.819 - [测试时间][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-12 19:09:17.820 - [测试时间][SourceMongo] - Starting stream read, table list: [repliceTime, _tapdata_heartbeat_table], offset: {"cdcOffset":1720782557,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-12 19:09:18.025 - [测试时间][SourceMongo] - Connector MongoDB incremental start succeed, tables: [repliceTime, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-12 19:09:18.263 - [测试时间][TestMysql] - Node(TestMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-12 19:09:18.263 - [测试时间][TestMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-12 19:10:01.614 - [测试时间][SourceMongo] - Node SourceMongo[776ddbcd-0bb0-4ab5-8cbe-b5ac69a9b0a6] running status set to false 
[INFO ] 2024-07-12 19:10:01.617 - [测试时间][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-776ddbcd-0bb0-4ab5-8cbe-b5ac69a9b0a6 
[INFO ] 2024-07-12 19:10:01.619 - [测试时间][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-776ddbcd-0bb0-4ab5-8cbe-b5ac69a9b0a6 
[INFO ] 2024-07-12 19:10:01.619 - [测试时间][SourceMongo] - Node SourceMongo[776ddbcd-0bb0-4ab5-8cbe-b5ac69a9b0a6] schema data cleaned 
[INFO ] 2024-07-12 19:10:01.619 - [测试时间][SourceMongo] - Node SourceMongo[776ddbcd-0bb0-4ab5-8cbe-b5ac69a9b0a6] monitor closed 
[INFO ] 2024-07-12 19:10:01.620 - [测试时间][SourceMongo] - Node SourceMongo[776ddbcd-0bb0-4ab5-8cbe-b5ac69a9b0a6] close complete, cost 25 ms 
[INFO ] 2024-07-12 19:10:01.621 - [测试时间][TestMysql] - Node TestMysql[874fa2f0-65e9-4db2-9fad-ff73126ad6a5] running status set to false 
[INFO ] 2024-07-12 19:10:01.688 - [测试时间][TestMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-874fa2f0-65e9-4db2-9fad-ff73126ad6a5 
[INFO ] 2024-07-12 19:10:01.688 - [测试时间][TestMysql] - PDK connector node released: HazelcastTargetPdkDataNode-874fa2f0-65e9-4db2-9fad-ff73126ad6a5 
[INFO ] 2024-07-12 19:10:01.689 - [测试时间][TestMysql] - Node TestMysql[874fa2f0-65e9-4db2-9fad-ff73126ad6a5] schema data cleaned 
[INFO ] 2024-07-12 19:10:01.689 - [测试时间][TestMysql] - Node TestMysql[874fa2f0-65e9-4db2-9fad-ff73126ad6a5] monitor closed 
[INFO ] 2024-07-12 19:10:01.895 - [测试时间][TestMysql] - Node TestMysql[874fa2f0-65e9-4db2-9fad-ff73126ad6a5] close complete, cost 70 ms 
[INFO ] 2024-07-12 19:10:02.098 - [测试时间][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-12 19:10:02.941 - [测试时间] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-12 19:10:02.941 - [测试时间] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7a906f87 
[INFO ] 2024-07-12 19:10:03.091 - [测试时间] - Stop task milestones: 66910e44df7eaa0ea4a21743(测试时间)  
[INFO ] 2024-07-12 19:10:03.094 - [测试时间] - Stopped task aspect(s) 
[INFO ] 2024-07-12 19:10:03.094 - [测试时间] - Snapshot order controller have been removed 
[INFO ] 2024-07-12 19:10:03.124 - [测试时间] - Remove memory task client succeed, task: 测试时间[66910e44df7eaa0ea4a21743] 
[INFO ] 2024-07-12 19:10:03.124 - [测试时间] - Destroy memory task client cache succeed, task: 测试时间[66910e44df7eaa0ea4a21743] 
[INFO ] 2024-07-12 19:10:16.734 - [测试时间] - Task initialization... 
[INFO ] 2024-07-12 19:10:16.858 - [测试时间] - Start task milestones: 66910e44df7eaa0ea4a21743(测试时间) 
[INFO ] 2024-07-12 19:10:16.858 - [测试时间] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-12 19:10:16.930 - [测试时间] - The engine receives 测试时间 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-12 19:10:16.931 - [测试时间][SourceMongo] - Node SourceMongo[776ddbcd-0bb0-4ab5-8cbe-b5ac69a9b0a6] start preload schema,table counts: 1 
[INFO ] 2024-07-12 19:10:16.931 - [测试时间][时间运算] - Node 时间运算[c373cc50-4362-428a-bf37-fb6dc8397a55] start preload schema,table counts: 1 
[INFO ] 2024-07-12 19:10:16.931 - [测试时间][TestMysql] - Node TestMysql[874fa2f0-65e9-4db2-9fad-ff73126ad6a5] start preload schema,table counts: 1 
[INFO ] 2024-07-12 19:10:16.931 - [测试时间][SourceMongo] - Node SourceMongo[776ddbcd-0bb0-4ab5-8cbe-b5ac69a9b0a6] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 19:10:16.931 - [测试时间][时间运算] - Node 时间运算[c373cc50-4362-428a-bf37-fb6dc8397a55] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 19:10:16.931 - [测试时间][TestMysql] - Node TestMysql[874fa2f0-65e9-4db2-9fad-ff73126ad6a5] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 19:10:17.599 - [测试时间][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-07-12 19:10:17.599 - [测试时间][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-07-12 19:10:17.599 - [测试时间][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-12 19:10:17.694 - [测试时间][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1720782617,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-12 19:10:17.739 - [测试时间][SourceMongo] - Initial sync started 
[INFO ] 2024-07-12 19:10:17.739 - [测试时间][SourceMongo] - Starting batch read, table name: repliceTime, offset: null 
[INFO ] 2024-07-12 19:10:17.744 - [测试时间][SourceMongo] - Table repliceTime is going to be initial synced 
[INFO ] 2024-07-12 19:10:17.758 - [测试时间][SourceMongo] - Table [repliceTime] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-12 19:10:17.759 - [测试时间][SourceMongo] - Query table 'repliceTime' counts: 1 
[INFO ] 2024-07-12 19:10:17.773 - [测试时间][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-12 19:10:17.774 - [测试时间][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-12 19:10:17.776 - [测试时间][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-12 19:10:17.779 - [测试时间][SourceMongo] - Starting stream read, table list: [repliceTime, _tapdata_heartbeat_table], offset: {"cdcOffset":1720782617,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-12 19:10:17.982 - [测试时间][SourceMongo] - Connector MongoDB incremental start succeed, tables: [repliceTime, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-12 19:10:18.139 - [测试时间][TestMysql] - Node(TestMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-12 19:10:18.139 - [测试时间][TestMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-12 19:11:08.630 - [测试时间][SourceMongo] - Node SourceMongo[776ddbcd-0bb0-4ab5-8cbe-b5ac69a9b0a6] running status set to false 
[INFO ] 2024-07-12 19:11:08.631 - [测试时间][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-776ddbcd-0bb0-4ab5-8cbe-b5ac69a9b0a6 
[INFO ] 2024-07-12 19:11:08.631 - [测试时间][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-776ddbcd-0bb0-4ab5-8cbe-b5ac69a9b0a6 
[INFO ] 2024-07-12 19:11:08.631 - [测试时间][SourceMongo] - Node SourceMongo[776ddbcd-0bb0-4ab5-8cbe-b5ac69a9b0a6] schema data cleaned 
[INFO ] 2024-07-12 19:11:08.633 - [测试时间][SourceMongo] - Node SourceMongo[776ddbcd-0bb0-4ab5-8cbe-b5ac69a9b0a6] monitor closed 
[INFO ] 2024-07-12 19:11:08.635 - [测试时间][SourceMongo] - Node SourceMongo[776ddbcd-0bb0-4ab5-8cbe-b5ac69a9b0a6] close complete, cost 18 ms 
[INFO ] 2024-07-12 19:11:08.636 - [测试时间][时间运算] - Node 时间运算[c373cc50-4362-428a-bf37-fb6dc8397a55] running status set to false 
[INFO ] 2024-07-12 19:11:08.643 - [测试时间][时间运算] - Node 时间运算[c373cc50-4362-428a-bf37-fb6dc8397a55] schema data cleaned 
[INFO ] 2024-07-12 19:11:08.643 - [测试时间][时间运算] - Node 时间运算[c373cc50-4362-428a-bf37-fb6dc8397a55] monitor closed 
[INFO ] 2024-07-12 19:11:08.645 - [测试时间][时间运算] - Node 时间运算[c373cc50-4362-428a-bf37-fb6dc8397a55] close complete, cost 10 ms 
[INFO ] 2024-07-12 19:11:08.645 - [测试时间][TestMysql] - Node TestMysql[874fa2f0-65e9-4db2-9fad-ff73126ad6a5] running status set to false 
[INFO ] 2024-07-12 19:11:08.791 - [测试时间][TestMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-874fa2f0-65e9-4db2-9fad-ff73126ad6a5 
[INFO ] 2024-07-12 19:11:08.791 - [测试时间][TestMysql] - PDK connector node released: HazelcastTargetPdkDataNode-874fa2f0-65e9-4db2-9fad-ff73126ad6a5 
[INFO ] 2024-07-12 19:11:08.791 - [测试时间][TestMysql] - Node TestMysql[874fa2f0-65e9-4db2-9fad-ff73126ad6a5] schema data cleaned 
[INFO ] 2024-07-12 19:11:08.792 - [测试时间][TestMysql] - Node TestMysql[874fa2f0-65e9-4db2-9fad-ff73126ad6a5] monitor closed 
[INFO ] 2024-07-12 19:11:08.792 - [测试时间][TestMysql] - Node TestMysql[874fa2f0-65e9-4db2-9fad-ff73126ad6a5] close complete, cost 147 ms 
[INFO ] 2024-07-12 19:11:09.151 - [测试时间][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-12 19:11:13.423 - [测试时间] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-12 19:11:13.424 - [测试时间] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1a4a0dd3 
[INFO ] 2024-07-12 19:11:13.568 - [测试时间] - Stop task milestones: 66910e44df7eaa0ea4a21743(测试时间)  
[INFO ] 2024-07-12 19:11:13.568 - [测试时间] - Stopped task aspect(s) 
[INFO ] 2024-07-12 19:11:13.568 - [测试时间] - Snapshot order controller have been removed 
[INFO ] 2024-07-12 19:11:13.599 - [测试时间] - Remove memory task client succeed, task: 测试时间[66910e44df7eaa0ea4a21743] 
[INFO ] 2024-07-12 19:11:13.599 - [测试时间] - Destroy memory task client cache succeed, task: 测试时间[66910e44df7eaa0ea4a21743] 
