[INFO ] 2024-03-31 20:49:32.535 - [任务 31(100)][6c378420-232a-41b2-be15-35511bfd1e82] - Node 6c378420-232a-41b2-be15-35511bfd1e82[6c378420-232a-41b2-be15-35511bfd1e82] start preload schema,table counts: 0 
[INFO ] 2024-03-31 20:49:32.571 - [任务 31(100)][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] start preload schema,table counts: 1 
[INFO ] 2024-03-31 20:49:32.574 - [任务 31(100)][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] start preload schema,table counts: 1 
[INFO ] 2024-03-31 20:49:32.592 - [任务 31(100)][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] preload schema finished, cost 1 ms 
[INFO ] 2024-03-31 20:49:32.604 - [任务 31(100)][6c378420-232a-41b2-be15-35511bfd1e82] - Node 6c378420-232a-41b2-be15-35511bfd1e82[6c378420-232a-41b2-be15-35511bfd1e82] preload schema finished, cost 7 ms 
[INFO ] 2024-03-31 20:49:32.614 - [任务 31(100)][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] preload schema finished, cost 2 ms 
[INFO ] 2024-03-31 20:49:33.016 - [任务 31(100)][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] running status set to false 
[INFO ] 2024-03-31 20:49:33.028 - [任务 31(100)][test2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-44390385-f613-432e-83f8-802220979f8c 
[INFO ] 2024-03-31 20:49:33.028 - [任务 31(100)][test2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-44390385-f613-432e-83f8-802220979f8c 
[INFO ] 2024-03-31 20:49:33.028 - [任务 31(100)][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] schema data cleaned 
[INFO ] 2024-03-31 20:49:33.039 - [任务 31(100)][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] monitor closed 
[INFO ] 2024-03-31 20:49:33.040 - [任务 31(100)][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] close complete, cost 23 ms 
[INFO ] 2024-03-31 20:49:33.312 - [任务 31(100)][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] running status set to false 
[INFO ] 2024-03-31 20:49:33.312 - [任务 31(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-8f698e76-d8aa-4a2b-9612-c076c86550c8 
[INFO ] 2024-03-31 20:49:33.313 - [任务 31(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-8f698e76-d8aa-4a2b-9612-c076c86550c8 
[INFO ] 2024-03-31 20:49:33.313 - [任务 31(100)][增强JS] - [ScriptExecutorsManager-6604cccfc9eca62bdcca68bc-450a5214-9ffe-4d3d-9f78-9fe251cb3683-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-31 20:49:33.326 - [任务 31(100)][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] schema data cleaned 
[INFO ] 2024-03-31 20:49:33.327 - [任务 31(100)][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] monitor closed 
[INFO ] 2024-03-31 20:49:33.329 - [任务 31(100)][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] close complete, cost 62 ms 
[INFO ] 2024-03-31 20:49:33.366 - [任务 31(100)][6c378420-232a-41b2-be15-35511bfd1e82] - Node 6c378420-232a-41b2-be15-35511bfd1e82[6c378420-232a-41b2-be15-35511bfd1e82] running status set to false 
[INFO ] 2024-03-31 20:49:33.367 - [任务 31(100)][6c378420-232a-41b2-be15-35511bfd1e82] - Node 6c378420-232a-41b2-be15-35511bfd1e82[6c378420-232a-41b2-be15-35511bfd1e82] schema data cleaned 
[INFO ] 2024-03-31 20:49:33.368 - [任务 31(100)][6c378420-232a-41b2-be15-35511bfd1e82] - Node 6c378420-232a-41b2-be15-35511bfd1e82[6c378420-232a-41b2-be15-35511bfd1e82] monitor closed 
[INFO ] 2024-03-31 20:49:33.373 - [任务 31(100)][6c378420-232a-41b2-be15-35511bfd1e82] - Node 6c378420-232a-41b2-be15-35511bfd1e82[6c378420-232a-41b2-be15-35511bfd1e82] close complete, cost 5 ms 
[INFO ] 2024-03-31 20:53:42.483 - [任务 31(100)] - load tapTable task 6604cccfc9eca62bdcca68bc-6c378420-232a-41b2-be15-35511bfd1e82 complete, cost 974ms 
[INFO ] 2024-03-31 20:55:33.731 - [任务 31(100)][bc247203-6514-4773-8887-6dbe0ae7e19a] - Node bc247203-6514-4773-8887-6dbe0ae7e19a[bc247203-6514-4773-8887-6dbe0ae7e19a] start preload schema,table counts: 0 
[INFO ] 2024-03-31 20:55:33.731 - [任务 31(100)][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] start preload schema,table counts: 1 
[INFO ] 2024-03-31 20:55:33.731 - [任务 31(100)][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] start preload schema,table counts: 1 
[INFO ] 2024-03-31 20:55:33.732 - [任务 31(100)][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] preload schema finished, cost 1 ms 
[INFO ] 2024-03-31 20:55:33.734 - [任务 31(100)][bc247203-6514-4773-8887-6dbe0ae7e19a] - Node bc247203-6514-4773-8887-6dbe0ae7e19a[bc247203-6514-4773-8887-6dbe0ae7e19a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-31 20:55:33.939 - [任务 31(100)][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] preload schema finished, cost 0 ms 
[INFO ] 2024-03-31 20:55:34.013 - [任务 31(100)][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] running status set to false 
[INFO ] 2024-03-31 20:55:34.015 - [任务 31(100)][test2] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-44390385-f613-432e-83f8-802220979f8c 
[INFO ] 2024-03-31 20:55:34.015 - [任务 31(100)][test2] - PDK connector node released: HazelcastSampleSourcePdkDataNode-44390385-f613-432e-83f8-802220979f8c 
[INFO ] 2024-03-31 20:55:34.015 - [任务 31(100)][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] schema data cleaned 
[INFO ] 2024-03-31 20:55:34.019 - [任务 31(100)][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] monitor closed 
[INFO ] 2024-03-31 20:55:34.019 - [任务 31(100)][test2] - Node test2[44390385-f613-432e-83f8-802220979f8c] close complete, cost 18 ms 
[INFO ] 2024-03-31 20:55:34.222 - [任务 31(100)][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] running status set to false 
[INFO ] 2024-03-31 20:55:34.225 - [任务 31(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-3d121f96-78e7-4044-a050-8c4508a476b5 
[INFO ] 2024-03-31 20:55:34.225 - [任务 31(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-3d121f96-78e7-4044-a050-8c4508a476b5 
[INFO ] 2024-03-31 20:55:34.228 - [任务 31(100)][增强JS] - [ScriptExecutorsManager-6604cccfc9eca62bdcca68bc-450a5214-9ffe-4d3d-9f78-9fe251cb3683-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-31 20:55:34.228 - [任务 31(100)][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] schema data cleaned 
[INFO ] 2024-03-31 20:55:34.229 - [任务 31(100)][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] monitor closed 
[INFO ] 2024-03-31 20:55:34.229 - [任务 31(100)][增强JS] - Node 增强JS[450a5214-9ffe-4d3d-9f78-9fe251cb3683] close complete, cost 17 ms 
[INFO ] 2024-03-31 20:55:34.259 - [任务 31(100)][bc247203-6514-4773-8887-6dbe0ae7e19a] - Node bc247203-6514-4773-8887-6dbe0ae7e19a[bc247203-6514-4773-8887-6dbe0ae7e19a] running status set to false 
[INFO ] 2024-03-31 20:55:34.261 - [任务 31(100)][bc247203-6514-4773-8887-6dbe0ae7e19a] - Node bc247203-6514-4773-8887-6dbe0ae7e19a[bc247203-6514-4773-8887-6dbe0ae7e19a] schema data cleaned 
[INFO ] 2024-03-31 20:55:34.261 - [任务 31(100)][bc247203-6514-4773-8887-6dbe0ae7e19a] - Node bc247203-6514-4773-8887-6dbe0ae7e19a[bc247203-6514-4773-8887-6dbe0ae7e19a] monitor closed 
[INFO ] 2024-03-31 20:55:34.261 - [任务 31(100)][bc247203-6514-4773-8887-6dbe0ae7e19a] - Node bc247203-6514-4773-8887-6dbe0ae7e19a[bc247203-6514-4773-8887-6dbe0ae7e19a] close complete, cost 3 ms 
[INFO ] 2024-03-31 20:55:34.471 - [任务 31(100)] - load tapTable task 6604cccfc9eca62bdcca68bc-bc247203-6514-4773-8887-6dbe0ae7e19a complete, cost 586ms 
