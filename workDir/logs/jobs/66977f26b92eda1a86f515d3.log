[INFO ] 2024-07-17 16:54:19.147 - [任务 4] - Start task milestones: 66977f26b92eda1a86f515d3(任务 4) 
[INFO ] 2024-07-17 16:54:19.149 - [任务 4] - Task initialization... 
[INFO ] 2024-07-17 16:54:19.657 - [任务 4] - <PERSON><PERSON> performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-07-17 16:54:19.991 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 16:54:19.991 - [任务 4][主从合并] - Node 主从合并[0a8e1535-73c4-48ea-a395-64e379d77ea9] start preload schema,table counts: 3 
[INFO ] 2024-07-17 16:54:19.991 - [任务 4][Merge] - Node Merge[4170f464-3269-4fb4-af25-59328139b637] start preload schema,table counts: 1 
[INFO ] 2024-07-17 16:54:19.992 - [任务 4][主从合并] - Node 主从合并[0a8e1535-73c4-48ea-a395-64e379d77ea9] preload schema finished, cost 1 ms 
[INFO ] 2024-07-17 16:54:19.992 - [任务 4][Merge] - Node Merge[4170f464-3269-4fb4-af25-59328139b637] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 16:54:19.997 - [任务 4][主从合并] - Node %s(%s) enable initial batch 
[INFO ] 2024-07-17 16:54:19.998 - [任务 4][主从合并] - 
Merge lookup relation{
  CAR.POLICY(5d869245-bd35-4915-bafa-3deb9704da59)
    ->CUSTOMER(61198220-f60e-4c18-9f9b-f232a5697150)
} 
[INFO ] 2024-07-17 16:54:20.054 - [任务 4][CAR.POLICY] - Node CAR.POLICY[5d869245-bd35-4915-bafa-3deb9704da59] start preload schema,table counts: 1 
[INFO ] 2024-07-17 16:54:20.054 - [任务 4][CUSTOMER] - Node CUSTOMER[61198220-f60e-4c18-9f9b-f232a5697150] start preload schema,table counts: 1 
[INFO ] 2024-07-17 16:54:20.055 - [任务 4][CUSTOMER] - Node CUSTOMER[61198220-f60e-4c18-9f9b-f232a5697150] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 16:54:20.055 - [任务 4][CAR.POLICY] - Node CAR.POLICY[5d869245-bd35-4915-bafa-3deb9704da59] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 16:54:20.175 - [任务 4][主从合并] - Create merge cache imap name: HazelcastMergeNode_CUSTOMER_61198220-f60e-4c18-9f9b-f232a5697150__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://root:******@192.168.1.184:57017/tapdatav310?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-17 16:54:20.324 - [任务 4][主从合并] - Merge table processor lookup thread num: 8 
[INFO ] 2024-07-17 16:54:20.324 - [任务 4][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-07-17 16:54:20.598 - [任务 4][CAR.POLICY] - Source node "CAR.POLICY" read batch size: 100 
[INFO ] 2024-07-17 16:54:20.600 - [任务 4][CAR.POLICY] - Source node "CAR.POLICY" event queue capacity: 200 
[INFO ] 2024-07-17 16:54:20.600 - [任务 4][CAR.POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 16:54:20.640 - [任务 4][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-17 16:54:20.641 - [任务 4][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-17 16:54:20.641 - [任务 4][CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 16:54:20.762 - [任务 4][CAR.POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1721206452,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 16:54:20.872 - [任务 4][CUSTOMER] - batch offset found: {},stream offset found: {"cdcOffset":1721206452,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 16:54:20.880 - [任务 4][Merge] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-17 16:54:21.037 - [任务 4][CAR.POLICY] - Initial sync started 
[INFO ] 2024-07-17 16:54:21.051 - [任务 4] - Node[CUSTOMER] is waiting for running 
[INFO ] 2024-07-17 16:54:21.051 - [任务 4][CAR.POLICY] - Starting batch read, table name: CAR.POLICY, offset: null 
[INFO ] 2024-07-17 16:54:21.088 - [任务 4][CAR.POLICY] - Table CAR.POLICY is going to be initial synced 
[INFO ] 2024-07-17 16:54:21.088 - [任务 4][CAR.POLICY] - Query table 'CAR.POLICY' counts: 695 
[INFO ] 2024-07-17 16:54:21.112 - [任务 4][CAR.POLICY] - Table [CAR.POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-17 16:54:21.112 - [任务 4][CAR.POLICY] - Initial sync completed 
[INFO ] 2024-07-17 16:54:22.645 - [任务 4] - Node[CAR.POLICY] finish, notify next layer to run 
[INFO ] 2024-07-17 16:54:22.651 - [任务 4] - Next layer have been notified: [null] 
[INFO ] 2024-07-17 16:54:22.653 - [任务 4][CUSTOMER] - Initial sync started 
[INFO ] 2024-07-17 16:54:22.653 - [任务 4][CUSTOMER] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-17 16:54:22.655 - [任务 4][CUSTOMER] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-17 16:54:22.689 - [任务 4][CUSTOMER] - Query table 'CUSTOMER' counts: 676 
[INFO ] 2024-07-17 16:54:22.689 - [任务 4][CUSTOMER] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-17 16:54:22.689 - [任务 4][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-17 16:54:26.731 - [任务 4][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-17 16:54:26.737 - [任务 4][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-17 16:54:26.753 - [任务 4][CUSTOMER] - Starting stream read, table list: [CUSTOMER], offset: {"cdcOffset":1721206452,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 16:54:26.753 - [任务 4][CUSTOMER] - Connector MongoDB incremental start succeed, tables: [CUSTOMER], data change syncing 
[INFO ] 2024-07-17 16:54:27.143 - [任务 4][CAR.POLICY] - Incremental sync starting... 
[INFO ] 2024-07-17 16:54:27.144 - [任务 4][CAR.POLICY] - Initial sync completed 
[INFO ] 2024-07-17 16:54:27.147 - [任务 4][CAR.POLICY] - Starting stream read, table list: [CAR.POLICY], offset: {"cdcOffset":1721206452,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 16:54:27.147 - [任务 4][CAR.POLICY] - Connector MongoDB incremental start succeed, tables: [CAR.POLICY], data change syncing 
[INFO ] 2024-07-17 18:20:32.293 - [任务 4][CAR.POLICY] - Node CAR.POLICY[5d869245-bd35-4915-bafa-3deb9704da59] running status set to false 
[INFO ] 2024-07-17 18:20:32.301 - [任务 4][CAR.POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-5d869245-bd35-4915-bafa-3deb9704da59 
[INFO ] 2024-07-17 18:20:32.301 - [任务 4][CAR.POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-5d869245-bd35-4915-bafa-3deb9704da59 
[INFO ] 2024-07-17 18:20:32.301 - [任务 4][CAR.POLICY] - Node CAR.POLICY[5d869245-bd35-4915-bafa-3deb9704da59] schema data cleaned 
[INFO ] 2024-07-17 18:20:32.303 - [任务 4][CAR.POLICY] - Node CAR.POLICY[5d869245-bd35-4915-bafa-3deb9704da59] monitor closed 
[INFO ] 2024-07-17 18:20:32.303 - [任务 4][CAR.POLICY] - Node CAR.POLICY[5d869245-bd35-4915-bafa-3deb9704da59] close complete, cost 18 ms 
[INFO ] 2024-07-17 18:20:32.323 - [任务 4][CUSTOMER] - Node CUSTOMER[61198220-f60e-4c18-9f9b-f232a5697150] running status set to false 
[INFO ] 2024-07-17 18:20:32.323 - [任务 4][CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-61198220-f60e-4c18-9f9b-f232a5697150 
[INFO ] 2024-07-17 18:20:32.323 - [任务 4][CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-61198220-f60e-4c18-9f9b-f232a5697150 
[INFO ] 2024-07-17 18:20:32.323 - [任务 4][CUSTOMER] - Node CUSTOMER[61198220-f60e-4c18-9f9b-f232a5697150] schema data cleaned 
[INFO ] 2024-07-17 18:20:32.323 - [任务 4][CUSTOMER] - Node CUSTOMER[61198220-f60e-4c18-9f9b-f232a5697150] monitor closed 
[INFO ] 2024-07-17 18:20:32.324 - [任务 4][CUSTOMER] - Node CUSTOMER[61198220-f60e-4c18-9f9b-f232a5697150] close complete, cost 19 ms 
[INFO ] 2024-07-17 18:20:32.324 - [任务 4][主从合并] - Node 主从合并[0a8e1535-73c4-48ea-a395-64e379d77ea9] running status set to false 
[INFO ] 2024-07-17 18:20:32.324 - [任务 4][主从合并] - Destroy merge cache resource: HazelcastMergeNode_CUSTOMER_61198220-f60e-4c18-9f9b-f232a5697150__TPORIG 
[INFO ] 2024-07-17 18:20:32.357 - [任务 4][主从合并] - Node 主从合并[0a8e1535-73c4-48ea-a395-64e379d77ea9] schema data cleaned 
[INFO ] 2024-07-17 18:20:32.358 - [任务 4][主从合并] - Node 主从合并[0a8e1535-73c4-48ea-a395-64e379d77ea9] monitor closed 
[INFO ] 2024-07-17 18:20:32.359 - [任务 4][主从合并] - Node 主从合并[0a8e1535-73c4-48ea-a395-64e379d77ea9] close complete, cost 34 ms 
[INFO ] 2024-07-17 18:20:32.359 - [任务 4][Merge] - Node Merge[4170f464-3269-4fb4-af25-59328139b637] running status set to false 
[INFO ] 2024-07-17 18:20:32.393 - [任务 4][Merge] - PDK connector node stopped: HazelcastTargetPdkDataNode-4170f464-3269-4fb4-af25-59328139b637 
[INFO ] 2024-07-17 18:20:32.393 - [任务 4][Merge] - PDK connector node released: HazelcastTargetPdkDataNode-4170f464-3269-4fb4-af25-59328139b637 
[INFO ] 2024-07-17 18:20:32.393 - [任务 4][Merge] - Node Merge[4170f464-3269-4fb4-af25-59328139b637] schema data cleaned 
[INFO ] 2024-07-17 18:20:32.393 - [任务 4][Merge] - Node Merge[4170f464-3269-4fb4-af25-59328139b637] monitor closed 
[INFO ] 2024-07-17 18:20:32.394 - [任务 4][Merge] - Node Merge[4170f464-3269-4fb4-af25-59328139b637] close complete, cost 35 ms 
[INFO ] 2024-07-17 18:20:32.607 - [任务 4][CAR.POLICY] - Incremental sync completed 
[INFO ] 2024-07-17 18:20:32.810 - [任务 4][CUSTOMER] - Incremental sync completed 
[INFO ] 2024-07-17 18:20:35.209 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 18:20:35.210 - [任务 4] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3a3ae4f8 
[INFO ] 2024-07-17 18:20:35.221 - [任务 4] - Stop task milestones: 66977f26b92eda1a86f515d3(任务 4)  
[INFO ] 2024-07-17 18:20:35.580 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-07-17 18:20:35.580 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 18:20:35.637 - [任务 4] - Remove memory task client succeed, task: 任务 4[66977f26b92eda1a86f515d3] 
[INFO ] 2024-07-17 18:20:35.637 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[66977f26b92eda1a86f515d3] 
