[INFO ] 2024-07-18 11:22:48.473 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952] - Task initialization... 
[INFO ] 2024-07-18 11:22:48.473 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952] - Start task milestones: 66988a7c8315b25db9f53b42(t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952) 
[INFO ] 2024-07-18 11:22:49.002 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952] - No<PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-18 11:22:49.136 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952] - The engine receives t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 11:22:49.202 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[6c20b539-4030-49db-86cb-0e5b23305a85] start preload schema,table counts: 10 
[INFO ] 2024-07-18 11:22:49.202 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Node qa_mysql_repl_33306_331_1717403468657_3537[5c5b81ea-3d27-4dff-8dc0-641c5f919990] start preload schema,table counts: 10 
[INFO ] 2024-07-18 11:22:49.202 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][TableRename] - Node TableRename[6e194a62-0cea-47ba-9db3-f00a5b73f137] start preload schema,table counts: 10 
[INFO ] 2024-07-18 11:22:49.202 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Node qa_mysql_repl_33306_331_1717403468657_3537[5c5b81ea-3d27-4dff-8dc0-641c5f919990] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:22:49.202 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[6c20b539-4030-49db-86cb-0e5b23305a85] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:22:49.212 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][TableRename] - Node TableRename[6e194a62-0cea-47ba-9db3-f00a5b73f137] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:22:50.086 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_t_1717403468657_3537] - Node(qa_mysql_repl_33306_t_1717403468657_3537) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-18 11:22:50.086 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_t_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 11:22:50.327 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Source node "qa_mysql_repl_33306_331_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 11:22:50.327 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Source node "qa_mysql_repl_33306_331_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 11:22:50.327 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 11:22:50.543 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000304","position":369565313,"gtidSet":""} 
[INFO ] 2024-07-18 11:22:50.543 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-18 11:22:50.749 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-07-18 11:22:50.797 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 11:22:50.797 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Starting batch read, table name: bmsql_item, offset: null 
[INFO ] 2024-07-18 11:22:50.798 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table bmsql_item is going to be initial synced 
[INFO ] 2024-07-18 11:22:51.002 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Query table 'bmsql_item' counts: 10000 
[INFO ] 2024-07-18 11:22:56.472 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table [bmsql_item] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:22:56.486 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Starting batch read, table name: bmsql_district, offset: null 
[INFO ] 2024-07-18 11:22:56.486 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table bmsql_district is going to be initial synced 
[INFO ] 2024-07-18 11:22:56.575 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Query table 'bmsql_district' counts: 100 
[INFO ] 2024-07-18 11:22:56.586 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table [bmsql_district] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:22:56.587 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Starting batch read, table name: bmsql_new_order, offset: null 
[INFO ] 2024-07-18 11:22:56.696 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table bmsql_new_order is going to be initial synced 
[INFO ] 2024-07-18 11:22:56.696 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Query table 'bmsql_new_order' counts: 49981 
[INFO ] 2024-07-18 11:23:02.548 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][TableRename] - [6e194a62-0cea-47ba-9db3-f00a5b73f137-TableRename] Successor node processing speed is limited, about to delay 10 millisecond 
[INFO ] 2024-07-18 11:23:03.573 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][TableRename] - [6e194a62-0cea-47ba-9db3-f00a5b73f137-TableRename] Successor node processing speed recovery 
[INFO ] 2024-07-18 11:23:04.094 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table [bmsql_new_order] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:23:04.110 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Starting batch read, table name: bmsql_oorder, offset: null 
[INFO ] 2024-07-18 11:23:04.110 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table bmsql_oorder is going to be initial synced 
[INFO ] 2024-07-18 11:23:04.313 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Query table 'bmsql_oorder' counts: 100026 
[INFO ] 2024-07-18 11:23:14.510 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table [bmsql_oorder] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:23:14.510 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Starting batch read, table name: bmsql_stock, offset: null 
[INFO ] 2024-07-18 11:23:14.715 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table bmsql_stock is going to be initial synced 
[INFO ] 2024-07-18 11:23:14.750 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Query table 'bmsql_stock' counts: 100000 
[INFO ] 2024-07-18 11:23:24.930 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table [bmsql_stock] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:23:24.931 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Starting batch read, table name: bmsql_warehouse, offset: null 
[INFO ] 2024-07-18 11:23:24.931 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table bmsql_warehouse is going to be initial synced 
[INFO ] 2024-07-18 11:23:25.005 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Query table 'bmsql_warehouse' counts: 10 
[INFO ] 2024-07-18 11:23:25.028 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table [bmsql_warehouse] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:23:25.044 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Starting batch read, table name: bmsql_customer, offset: null 
[INFO ] 2024-07-18 11:23:25.044 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table bmsql_customer is going to be initial synced 
[INFO ] 2024-07-18 11:23:25.247 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Query table 'bmsql_customer' counts: 100000 
[INFO ] 2024-07-18 11:23:31.123 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][TableRename] - [6e194a62-0cea-47ba-9db3-f00a5b73f137-TableRename] Successor node processing speed is limited, about to delay 10 millisecond 
[INFO ] 2024-07-18 11:23:33.831 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][TableRename] - [6e194a62-0cea-47ba-9db3-f00a5b73f137-TableRename] Successor node processing speed recovery 
[INFO ] 2024-07-18 11:23:33.848 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][TableRename] - [6e194a62-0cea-47ba-9db3-f00a5b73f137-TableRename] Successor node processing speed is limited, about to delay 11 millisecond 
[INFO ] 2024-07-18 11:23:33.849 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][TableRename] - [6e194a62-0cea-47ba-9db3-f00a5b73f137-TableRename] Successor node processing speed recovery 
[INFO ] 2024-07-18 11:23:38.597 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table [bmsql_customer] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:23:38.598 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Starting batch read, table name: bmsql_history, offset: null 
[INFO ] 2024-07-18 11:23:38.598 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table bmsql_history is going to be initial synced 
[INFO ] 2024-07-18 11:23:38.802 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Query table 'bmsql_history' counts: 100079 
[INFO ] 2024-07-18 11:23:49.046 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table [bmsql_history] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:23:49.047 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Starting batch read, table name: bmsql_config, offset: null 
[INFO ] 2024-07-18 11:23:49.047 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table bmsql_config is going to be initial synced 
[INFO ] 2024-07-18 11:23:49.139 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table [bmsql_config] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:23:49.139 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Query table 'bmsql_config' counts: 4 
[INFO ] 2024-07-18 11:23:49.139 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Starting batch read, table name: bmsql_order_line, offset: null 
[INFO ] 2024-07-18 11:23:49.140 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table bmsql_order_line is going to be initial synced 
[INFO ] 2024-07-18 11:23:49.956 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Query table 'bmsql_order_line' counts: 1000180 
[INFO ] 2024-07-18 11:23:55.059 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][TableRename] - [6e194a62-0cea-47ba-9db3-f00a5b73f137-TableRename] Successor node processing speed is limited, about to delay 10 millisecond 
[INFO ] 2024-07-18 11:23:57.898 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][TableRename] - [6e194a62-0cea-47ba-9db3-f00a5b73f137-TableRename] Successor node processing speed recovery 
[INFO ] 2024-07-18 11:24:53.956 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Table [bmsql_order_line] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:24:53.956 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:24:53.956 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 11:24:53.956 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:24:53.956 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Starting stream read, table list: [bmsql_item, bmsql_district, bmsql_new_order, bmsql_oorder, bmsql_stock, bmsql_warehouse, bmsql_customer, bmsql_history, bmsql_config, bmsql_order_line, _tapdata_heartbeat_table], offset: {"filename":"mysql-bin.000304","position":369565313,"gtidSet":""} 
[INFO ] 2024-07-18 11:24:54.092 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Starting mysql cdc, server name: f351dc8f-0878-4a9c-9cf2-d336f7a33cab 
[INFO ] 2024-07-18 11:24:54.092 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1368156991
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f351dc8f-0878-4a9c-9cf2-d336f7a33cab
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f351dc8f-0878-4a9c-9cf2-d336f7a33cab
  database.hostname: *************
  database.password: ********
  name: f351dc8f-0878-4a9c-9cf2-d336f7a33cab
  pdk.offset.string: {"name":"f351dc8f-0878-4a9c-9cf2-d336f7a33cab","offset":{"{\"server\":\"f351dc8f-0878-4a9c-9cf2-d336f7a33cab\"}":"{\"file\":\"mysql-bin.000304\",\"pos\":369565313,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: tpcc_331.bmsql_item,tpcc_331.bmsql_district,tpcc_331.bmsql_new_order,tpcc_331.bmsql_oorder,tpcc_331.bmsql_stock,tpcc_331.bmsql_warehouse,tpcc_331.bmsql_customer,tpcc_331.bmsql_history,tpcc_331.bmsql_config,tpcc_331.bmsql_order_line,tpcc_331._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: tpcc_331
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-18 11:24:54.335 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Connector Mysql incremental start succeed, tables: [bmsql_item, bmsql_district, bmsql_new_order, bmsql_oorder, bmsql_stock, bmsql_warehouse, bmsql_customer, bmsql_history, bmsql_config, bmsql_order_line, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 11:25:01.615 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][TableRename] - [6e194a62-0cea-47ba-9db3-f00a5b73f137-TableRename] Successor node processing speed is limited, about to delay 10 millisecond 
[INFO ] 2024-07-18 11:25:11.365 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][TableRename] - [6e194a62-0cea-47ba-9db3-f00a5b73f137-TableRename] Successor node processing speed recovery 
[INFO ] 2024-07-18 11:26:43.294 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Node qa_mysql_repl_33306_331_1717403468657_3537[5c5b81ea-3d27-4dff-8dc0-641c5f919990] running status set to false 
[INFO ] 2024-07-18 11:26:43.399 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-18 11:26:43.399 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Mysql binlog reader stopped 
[INFO ] 2024-07-18 11:26:43.401 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 11:26:43.417 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-5c5b81ea-3d27-4dff-8dc0-641c5f919990 
[INFO ] 2024-07-18 11:26:43.418 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-5c5b81ea-3d27-4dff-8dc0-641c5f919990 
[INFO ] 2024-07-18 11:26:43.419 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Node qa_mysql_repl_33306_331_1717403468657_3537[5c5b81ea-3d27-4dff-8dc0-641c5f919990] schema data cleaned 
[INFO ] 2024-07-18 11:26:43.419 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Node qa_mysql_repl_33306_331_1717403468657_3537[5c5b81ea-3d27-4dff-8dc0-641c5f919990] monitor closed 
[INFO ] 2024-07-18 11:26:43.423 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_331_1717403468657_3537] - Node qa_mysql_repl_33306_331_1717403468657_3537[5c5b81ea-3d27-4dff-8dc0-641c5f919990] close complete, cost 141 ms 
[INFO ] 2024-07-18 11:26:43.423 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][TableRename] - Node TableRename[6e194a62-0cea-47ba-9db3-f00a5b73f137] running status set to false 
[INFO ] 2024-07-18 11:26:43.433 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][TableRename] - Node TableRename[6e194a62-0cea-47ba-9db3-f00a5b73f137] schema data cleaned 
[INFO ] 2024-07-18 11:26:43.433 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][TableRename] - Node TableRename[6e194a62-0cea-47ba-9db3-f00a5b73f137] monitor closed 
[INFO ] 2024-07-18 11:26:43.433 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][TableRename] - Node TableRename[6e194a62-0cea-47ba-9db3-f00a5b73f137] close complete, cost 9 ms 
[INFO ] 2024-07-18 11:26:43.506 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[6c20b539-4030-49db-86cb-0e5b23305a85] running status set to false 
[INFO ] 2024-07-18 11:26:43.506 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_t_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-6c20b539-4030-49db-86cb-0e5b23305a85 
[INFO ] 2024-07-18 11:26:43.506 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_t_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-6c20b539-4030-49db-86cb-0e5b23305a85 
[INFO ] 2024-07-18 11:26:43.506 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[6c20b539-4030-49db-86cb-0e5b23305a85] schema data cleaned 
[INFO ] 2024-07-18 11:26:43.506 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[6c20b539-4030-49db-86cb-0e5b23305a85] monitor closed 
[INFO ] 2024-07-18 11:26:43.506 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[6c20b539-4030-49db-86cb-0e5b23305a85] close complete, cost 73 ms 
[INFO ] 2024-07-18 11:26:47.719 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 11:26:47.719 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7d8df78 
[INFO ] 2024-07-18 11:26:47.836 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952] - Stop task milestones: 66988a7c8315b25db9f53b42(t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952)  
[INFO ] 2024-07-18 11:26:47.867 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952] - Stopped task aspect(s) 
[INFO ] 2024-07-18 11:26:47.867 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 11:26:47.930 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952] - Remove memory task client succeed, task: t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952[66988a7c8315b25db9f53b42] 
[INFO ] 2024-07-18 11:26:47.930 - [t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952] - Destroy memory task client cache succeed, task: t_3.3.1-mysql_to_mysql_tpcc_full/realtime_1717403468657_3537-1721272952[66988a7c8315b25db9f53b42] 
