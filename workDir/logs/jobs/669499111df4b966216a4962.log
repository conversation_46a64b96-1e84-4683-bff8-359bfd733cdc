[INFO ] 2024-07-15 11:36:15.692 - [任务 1] - Task initialization... 
[INFO ] 2024-07-15 11:36:15.734 - [任务 1] - Start task milestones: 669499111df4b966216a4962(任务 1) 
[INFO ] 2024-07-15 11:36:16.002 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 11:36:16.069 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 11:36:16.127 - [任务 1][POLICY] - Node POLICY[b8bc8937-b871-4d04-af4d-8ad3556e157f] start preload schema,table counts: 1 
[INFO ] 2024-07-15 11:36:16.139 - [任务 1][POLICY] - Node POLICY[8b90cd90-31c6-4c50-9d8e-8bcd40e2e912] start preload schema,table counts: 1 
[INFO ] 2024-07-15 11:36:16.139 - [任务 1][POLICY] - Node POLICY[b8bc8937-b871-4d04-af4d-8ad3556e157f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 11:36:16.228 - [任务 1][POLICY] - Node POLICY[8b90cd90-31c6-4c50-9d8e-8bcd40e2e912] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 11:36:17.380 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-15 11:36:17.380 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-15 11:36:17.382 - [任务 1][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 11:36:17.537 - [任务 1][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1721014577,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 11:36:17.593 - [任务 1][POLICY] - Initial sync started 
[INFO ] 2024-07-15 11:36:17.601 - [任务 1][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-15 11:36:17.601 - [任务 1][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-15 11:36:17.720 - [任务 1][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-15 11:36:17.922 - [任务 1][POLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 11:36:17.923 - [任务 1][POLICY] - Table "test.POLICY" exists, skip auto create table 
[INFO ] 2024-07-15 11:36:17.992 - [任务 1][POLICY] - The table POLICY has already exist. 
[INFO ] 2024-07-15 11:36:17.992 - [任务 1][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-15 11:36:17.996 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 11:36:18.006 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-15 11:36:18.007 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 11:36:18.067 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-15 11:36:18.067 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection Mongo enable share cdc: true 
[INFO ] 2024-07-15 11:36:18.067 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 1 enable share cdc: true 
[INFO ] 2024-07-15 11:36:18.084 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自Mongo的共享挖掘任务 
[INFO ] 2024-07-15 11:36:18.095 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-15 11:36:18.095 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 11:36:18.145 - [任务 1][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 11:36:18.145 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-15 11:36:18.145 - [任务 1][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-15 11:36:18.145 - [任务 1][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-15 11:36:18.146 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-15 11:36:18.155 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-15 11:36:18.155 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6694992f66ab5ede8a8474cd, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669497151df4b966216a48aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1603875826, shareCdcTaskId=6694992f1df4b966216a499f, connectionId=669497151df4b966216a48aa) 
[INFO ] 2024-07-15 11:36:18.158 - [任务 1][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1603875826', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 11:36:18.158 - [任务 1][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 1, external storage name: ExternalStorage_SHARE_CDC_-1603875826 
[INFO ] 2024-07-15 11:36:18.166 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-15 11:36:18.166 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-15T03:36:17.382Z): 1 
[INFO ] 2024-07-15 11:36:18.170 - [任务 1][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-15 11:36:18.171 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 1 
[INFO ] 2024-07-15 11:36:18.171 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=1} 
[INFO ] 2024-07-15 11:38:27.022 - [任务 1][POLICY] - Node POLICY[8b90cd90-31c6-4c50-9d8e-8bcd40e2e912] running status set to false 
[INFO ] 2024-07-15 11:38:27.023 - [任务 1][POLICY] - Incremental sync completed 
[INFO ] 2024-07-15 11:38:27.034 - [任务 1][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-8b90cd90-31c6-4c50-9d8e-8bcd40e2e912 
[INFO ] 2024-07-15 11:38:27.036 - [任务 1][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-8b90cd90-31c6-4c50-9d8e-8bcd40e2e912 
[INFO ] 2024-07-15 11:38:27.036 - [任务 1][POLICY] - Node POLICY[8b90cd90-31c6-4c50-9d8e-8bcd40e2e912] schema data cleaned 
[INFO ] 2024-07-15 11:38:27.036 - [任务 1][POLICY] - Node POLICY[8b90cd90-31c6-4c50-9d8e-8bcd40e2e912] monitor closed 
[INFO ] 2024-07-15 11:38:27.036 - [任务 1][POLICY] - Node POLICY[8b90cd90-31c6-4c50-9d8e-8bcd40e2e912] close complete, cost 70 ms 
[INFO ] 2024-07-15 11:38:27.036 - [任务 1][POLICY] - Node POLICY[b8bc8937-b871-4d04-af4d-8ad3556e157f] running status set to false 
[INFO ] 2024-07-15 11:38:27.052 - [任务 1][POLICY] - PDK connector node stopped: HazelcastTargetPdkDataNode-b8bc8937-b871-4d04-af4d-8ad3556e157f 
[INFO ] 2024-07-15 11:38:27.053 - [任务 1][POLICY] - PDK connector node released: HazelcastTargetPdkDataNode-b8bc8937-b871-4d04-af4d-8ad3556e157f 
[INFO ] 2024-07-15 11:38:27.053 - [任务 1][POLICY] - Node POLICY[b8bc8937-b871-4d04-af4d-8ad3556e157f] schema data cleaned 
[INFO ] 2024-07-15 11:38:27.053 - [任务 1][POLICY] - Node POLICY[b8bc8937-b871-4d04-af4d-8ad3556e157f] monitor closed 
[INFO ] 2024-07-15 11:38:27.261 - [任务 1][POLICY] - Node POLICY[b8bc8937-b871-4d04-af4d-8ad3556e157f] close complete, cost 19 ms 
[INFO ] 2024-07-15 11:38:29.097 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 11:38:29.098 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3e543722 
[INFO ] 2024-07-15 11:38:29.236 - [任务 1] - Stop task milestones: 669499111df4b966216a4962(任务 1)  
[INFO ] 2024-07-15 11:38:29.255 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-15 11:38:29.255 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 11:38:29.289 - [任务 1] - Remove memory task client succeed, task: 任务 1[669499111df4b966216a4962] 
[INFO ] 2024-07-15 11:38:29.289 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[669499111df4b966216a4962] 
