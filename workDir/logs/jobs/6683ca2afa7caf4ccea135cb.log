[INFO ] 2024-07-02 17:37:40.447 - [任务 45] - Start task milestones: 6683ca2afa7caf4ccea135cb(任务 45) 
[INFO ] 2024-07-02 17:37:40.447 - [任务 45] - Task initialization... 
[INFO ] 2024-07-02 17:37:40.573 - [任务 45] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:37:40.573 - [任务 45] - The engine receives 任务 45 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 17:37:40.611 - [任务 45][POLICY] - Node POLICY[d73f93ff-0111-4fe2-abd5-08ce73d5bd69] start preload schema,table counts: 1 
[INFO ] 2024-07-02 17:37:40.611 - [任务 45][POLICYABC] - Node POLICYABC[4a14623f-b11e-4244-8ae9-d3f6ae477a30] start preload schema,table counts: 1 
[INFO ] 2024-07-02 17:37:40.611 - [任务 45][POLICY] - Node POLICY[d73f93ff-0111-4fe2-abd5-08ce73d5bd69] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 17:37:40.812 - [任务 45][POLICYABC] - Node POLICYABC[4a14623f-b11e-4244-8ae9-d3f6ae477a30] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 17:37:41.216 - [任务 45][POLICYABC] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 17:37:41.386 - [任务 45][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-02 17:37:41.387 - [任务 45][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-02 17:37:41.387 - [任务 45][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-02 17:37:41.512 - [任务 45][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1719913061,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 17:37:41.568 - [任务 45][POLICY] - Initial sync started 
[INFO ] 2024-07-02 17:37:41.568 - [任务 45][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-02 17:37:41.570 - [任务 45][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-02 17:37:41.655 - [任务 45][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-02 17:37:41.656 - [任务 45][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-02 17:37:41.656 - [任务 45][POLICY] - Initial sync completed 
[INFO ] 2024-07-02 17:37:41.656 - [任务 45][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-02 17:37:41.656 - [任务 45][POLICY] - Initial sync completed 
[INFO ] 2024-07-02 17:37:41.719 - [任务 45][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-02 17:37:41.719 - [任务 45][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceMongo enable share cdc: true 
[INFO ] 2024-07-02 17:37:41.720 - [任务 45][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 45 enable share cdc: true 
[INFO ] 2024-07-02 17:37:41.737 - [任务 45][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceMongo的共享挖掘任务 
[INFO ] 2024-07-02 17:37:41.751 - [任务 45][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-02 17:37:41.751 - [任务 45][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 17:37:41.832 - [任务 45][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 45', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 4 
[INFO ] 2024-07-02 17:37:41.832 - [任务 45][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-02 17:37:41.832 - [任务 45][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-02 17:37:41.832 - [任务 45][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-02 17:37:41.832 - [任务 45][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-02 17:37:41.832 - [任务 45][POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-02 17:37:41.839 - [任务 45][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 17:37:41.839 - [任务 45][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 45', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 4 
[INFO ] 2024-07-02 17:37:41.840 - [任务 45][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 45, external storage name: ExternalStorage_SHARE_CDC_553058525 
[INFO ] 2024-07-02 17:37:41.840 - [任务 45][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-02 17:37:41.845 - [任务 45][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-02T09:37:41.386Z): 5 
[INFO ] 2024-07-02 17:37:41.845 - [任务 45][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 17:37:41.846 - [任务 45][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 5 
[INFO ] 2024-07-02 17:37:41.846 - [任务 45][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=5} 
[INFO ] 2024-07-02 18:15:03.454 - [任务 45][POLICY] - Node POLICY[d73f93ff-0111-4fe2-abd5-08ce73d5bd69] running status set to false 
[INFO ] 2024-07-02 18:15:03.467 - [任务 45][POLICY] - Incremental sync completed 
[INFO ] 2024-07-02 18:15:03.467 - [任务 45][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-d73f93ff-0111-4fe2-abd5-08ce73d5bd69 
[INFO ] 2024-07-02 18:15:03.467 - [任务 45][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-d73f93ff-0111-4fe2-abd5-08ce73d5bd69 
[INFO ] 2024-07-02 18:15:03.468 - [任务 45][POLICY] - Node POLICY[d73f93ff-0111-4fe2-abd5-08ce73d5bd69] schema data cleaned 
[INFO ] 2024-07-02 18:15:03.469 - [任务 45][POLICY] - Node POLICY[d73f93ff-0111-4fe2-abd5-08ce73d5bd69] monitor closed 
[INFO ] 2024-07-02 18:15:03.469 - [任务 45][POLICY] - Node POLICY[d73f93ff-0111-4fe2-abd5-08ce73d5bd69] close complete, cost 48 ms 
[INFO ] 2024-07-02 18:15:03.469 - [任务 45][POLICYABC] - Node POLICYABC[4a14623f-b11e-4244-8ae9-d3f6ae477a30] running status set to false 
[INFO ] 2024-07-02 18:15:03.486 - [任务 45][POLICYABC] - PDK connector node stopped: HazelcastTargetPdkDataNode-4a14623f-b11e-4244-8ae9-d3f6ae477a30 
[INFO ] 2024-07-02 18:15:03.486 - [任务 45][POLICYABC] - PDK connector node released: HazelcastTargetPdkDataNode-4a14623f-b11e-4244-8ae9-d3f6ae477a30 
[INFO ] 2024-07-02 18:15:03.486 - [任务 45][POLICYABC] - Node POLICYABC[4a14623f-b11e-4244-8ae9-d3f6ae477a30] schema data cleaned 
[INFO ] 2024-07-02 18:15:03.487 - [任务 45][POLICYABC] - Node POLICYABC[4a14623f-b11e-4244-8ae9-d3f6ae477a30] monitor closed 
[INFO ] 2024-07-02 18:15:03.487 - [任务 45][POLICYABC] - Node POLICYABC[4a14623f-b11e-4244-8ae9-d3f6ae477a30] close complete, cost 17 ms 
[INFO ] 2024-07-02 18:15:05.913 - [任务 45] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 18:15:05.915 - [任务 45] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7f76e08a 
[INFO ] 2024-07-02 18:15:05.926 - [任务 45] - Stop task milestones: 6683ca2afa7caf4ccea135cb(任务 45)  
[INFO ] 2024-07-02 18:15:06.076 - [任务 45] - Stopped task aspect(s) 
[INFO ] 2024-07-02 18:15:06.076 - [任务 45] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 18:15:06.124 - [任务 45] - Remove memory task client succeed, task: 任务 45[6683ca2afa7caf4ccea135cb] 
[INFO ] 2024-07-02 18:15:06.124 - [任务 45] - Destroy memory task client cache succeed, task: 任务 45[6683ca2afa7caf4ccea135cb] 
