[INFO ] 2024-07-22 09:32:51.667 - [任务 4][SouceMysql] - Mysql binlog reader stopped 
[WARN ] 2024-07-22 09:32:51.702 - [任务 4][SouceMysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.EOFException: null
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:580)
	com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	java.lang.Thread.run(Thread.java:750)
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-22 10:06:13.136 - [任务 4][SouceMysql] - Node SouceMysql[de5259fe-0ce8-46a7-80eb-c044101c91e9] running status set to false 
[INFO ] 2024-07-22 10:11:10.971 - [任务 4] - Start task milestones: 669d16450c195b2c7b95623c(任务 4) 
[INFO ] 2024-07-22 10:11:11.188 - [任务 4] - Task initialization... 
[INFO ] 2024-07-22 10:11:12.322 - [任务 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-22 10:11:12.325 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-22 10:11:12.928 - [任务 4][SouceMysql] - Node SouceMysql[de5259fe-0ce8-46a7-80eb-c044101c91e9] start preload schema,table counts: 2 
[INFO ] 2024-07-22 10:11:12.931 - [任务 4][SouceMysql] - Node SouceMysql[de5259fe-0ce8-46a7-80eb-c044101c91e9] preload schema finished, cost 1 ms 
[INFO ] 2024-07-22 10:11:12.959 - [任务 4][targetMysql] - Node targetMysql[f7cc2419-4123-4fea-8171-3176c1e94e89] start preload schema,table counts: 2 
[INFO ] 2024-07-22 10:11:12.965 - [任务 4][targetMysql] - Node targetMysql[f7cc2419-4123-4fea-8171-3176c1e94e89] preload schema finished, cost 0 ms 
[INFO ] 2024-07-22 10:11:14.843 - [任务 4][targetMysql] - Node(targetMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-22 10:11:14.866 - [任务 4][targetMysql] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-22 10:11:14.884 - [任务 4][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-07-22 10:11:14.885 - [任务 4][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-07-22 10:11:14.886 - [任务 4][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-22 10:11:14.890 - [任务 4][SouceMysql] - batch offset found: {"生产订单表":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"},"产品信息表":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"}},stream offset found: {"name":"fcec5c2d-d1b3-48c6-89a9-71bca5599043","offset":{"{\"server\":\"fcec5c2d-d1b3-48c6-89a9-71bca5599043\"}":"{\"ts_sec\":1721571405,\"file\":\"binlog.000002\",\"pos\":249224840,\"server_id\":1}"}} 
[INFO ] 2024-07-22 10:11:14.891 - [任务 4][SouceMysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-22 10:11:15.004 - [任务 4][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-07-22 10:11:15.004 - [任务 4][SouceMysql] - Initial sync completed 
[INFO ] 2024-07-22 10:11:15.004 - [任务 4][SouceMysql] - Starting stream read, table list: [产品信息表, 生产订单表], offset: {"name":"fcec5c2d-d1b3-48c6-89a9-71bca5599043","offset":{"{\"server\":\"fcec5c2d-d1b3-48c6-89a9-71bca5599043\"}":"{\"ts_sec\":1721571405,\"file\":\"binlog.000002\",\"pos\":249224840,\"server_id\":1}"}} 
[INFO ] 2024-07-22 10:11:15.067 - [任务 4][SouceMysql] - Starting mysql cdc, server name: fcec5c2d-d1b3-48c6-89a9-71bca5599043 
[INFO ] 2024-07-22 10:11:15.067 - [任务 4][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2103788224
  time.precision.mode: adaptive_time_microseconds
  database.server.name: fcec5c2d-d1b3-48c6-89a9-71bca5599043
  database.port: 3306
  threadName: Debezium-Mysql-Connector-fcec5c2d-d1b3-48c6-89a9-71bca5599043
  database.hostname: ***********
  database.password: ********
  name: fcec5c2d-d1b3-48c6-89a9-71bca5599043
  pdk.offset.string: {"name":"fcec5c2d-d1b3-48c6-89a9-71bca5599043","offset":{"{\"server\":\"fcec5c2d-d1b3-48c6-89a9-71bca5599043\"}":"{\"ts_sec\":1721571405,\"file\":\"binlog.000002\",\"pos\":249224840,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: 库存.产品信息表,库存.生产订单表
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: 库存
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-22 10:11:16.046 - [任务 4][SouceMysql] - Connector Mysql incremental start succeed, tables: [产品信息表, 生产订单表], data change syncing 
[INFO ] 2024-07-22 11:06:34.595 - [任务 4][SouceMysql] - Node SouceMysql[de5259fe-0ce8-46a7-80eb-c044101c91e9] running status set to false 
[INFO ] 2024-07-22 11:06:34.621 - [任务 4][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-22 11:06:34.621 - [任务 4][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-07-22 11:06:34.644 - [任务 4][SouceMysql] - Incremental sync completed 
[INFO ] 2024-07-22 11:06:34.644 - [任务 4][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-de5259fe-0ce8-46a7-80eb-c044101c91e9 
[INFO ] 2024-07-22 11:06:34.645 - [任务 4][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-de5259fe-0ce8-46a7-80eb-c044101c91e9 
[INFO ] 2024-07-22 11:06:34.648 - [任务 4][SouceMysql] - Node SouceMysql[de5259fe-0ce8-46a7-80eb-c044101c91e9] schema data cleaned 
[INFO ] 2024-07-22 11:06:34.650 - [任务 4][SouceMysql] - Node SouceMysql[de5259fe-0ce8-46a7-80eb-c044101c91e9] monitor closed 
[INFO ] 2024-07-22 11:06:34.656 - [任务 4][SouceMysql] - Node SouceMysql[de5259fe-0ce8-46a7-80eb-c044101c91e9] close complete, cost 66 ms 
[INFO ] 2024-07-22 11:06:34.657 - [任务 4][targetMysql] - Node targetMysql[f7cc2419-4123-4fea-8171-3176c1e94e89] running status set to false 
[INFO ] 2024-07-22 11:06:34.711 - [任务 4][targetMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-f7cc2419-4123-4fea-8171-3176c1e94e89 
[INFO ] 2024-07-22 11:06:34.712 - [任务 4][targetMysql] - PDK connector node released: HazelcastTargetPdkDataNode-f7cc2419-4123-4fea-8171-3176c1e94e89 
[INFO ] 2024-07-22 11:06:34.713 - [任务 4][targetMysql] - Node targetMysql[f7cc2419-4123-4fea-8171-3176c1e94e89] schema data cleaned 
[INFO ] 2024-07-22 11:06:34.713 - [任务 4][targetMysql] - Node targetMysql[f7cc2419-4123-4fea-8171-3176c1e94e89] monitor closed 
[INFO ] 2024-07-22 11:06:34.917 - [任务 4][targetMysql] - Node targetMysql[f7cc2419-4123-4fea-8171-3176c1e94e89] close complete, cost 57 ms 
[INFO ] 2024-07-22 11:06:35.969 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-22 11:06:35.979 - [任务 4] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@73c053b7 
[INFO ] 2024-07-22 11:06:35.980 - [任务 4] - Stop task milestones: 669d16450c195b2c7b95623c(任务 4)  
[INFO ] 2024-07-22 11:06:36.117 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-07-22 11:06:36.124 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-07-22 11:06:36.205 - [任务 4] - Remove memory task client succeed, task: 任务 4[669d16450c195b2c7b95623c] 
[INFO ] 2024-07-22 11:06:36.207 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[669d16450c195b2c7b95623c] 
