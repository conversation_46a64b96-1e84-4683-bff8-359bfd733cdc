[INFO ] 2024-08-30 17:20:29.470 - [任务 16] - Task initialization... 
[INFO ] 2024-08-30 17:20:29.473 - [任务 16] - Start task milestones: 66d18ebab452330bf594cf69(任务 16) 
[INFO ] 2024-08-30 17:20:29.485 - [任务 16] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-08-30 17:20:29.615 - [任务 16] - The engine receives 任务 16 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-30 17:20:29.615 - [任务 16][Oracle] - Node Oracle[8ae0e287-3026-4722-9f44-9050479b459f] start preload schema,table counts: 1 
[INFO ] 2024-08-30 17:20:29.693 - [任务 16][Mysql3306] - Node Mysql3306[e4968bae-4d75-4faf-8456-38fe5cb1d7c8] start preload schema,table counts: 1 
[INFO ] 2024-08-30 17:20:29.694 - [任务 16][Mysql3306] - Node Mysql3306[e4968bae-4d75-4faf-8456-38fe5cb1d7c8] preload schema finished, cost 72 ms 
[INFO ] 2024-08-30 17:20:29.902 - [任务 16][Oracle] - Node Oracle[8ae0e287-3026-4722-9f44-9050479b459f] preload schema finished, cost 75 ms 
[INFO ] 2024-08-30 17:20:35.778 - [任务 16][Oracle] - Source node "Oracle" read batch size: 100 
[INFO ] 2024-08-30 17:20:35.781 - [任务 16][Oracle] - Source node "Oracle" event queue capacity: 200 
[INFO ] 2024-08-30 17:20:35.781 - [任务 16][Oracle] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-30 17:20:36.031 - [任务 16][Oracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":104386295,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-30 17:20:36.043 - [任务 16][Oracle] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-30 17:20:36.188 - [任务 16][Oracle] - Initial sync started 
[INFO ] 2024-08-30 17:20:36.189 - [任务 16][Oracle] - Starting batch read, table name: AA00PP, offset: null 
[INFO ] 2024-08-30 17:20:36.263 - [任务 16][Oracle] - Table AA00PP is going to be initial synced 
[INFO ] 2024-08-30 17:20:36.263 - [任务 16][Oracle] - Query table 'AA00PP' counts: 1 
[INFO ] 2024-08-30 17:20:36.305 - [任务 16][Oracle] - Table [AA00PP] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-30 17:20:36.306 - [任务 16][Oracle] - Initial sync completed 
[INFO ] 2024-08-30 17:20:36.308 - [任务 16][Oracle] - Incremental sync starting... 
[INFO ] 2024-08-30 17:20:36.308 - [任务 16][Oracle] - Initial sync completed 
[INFO ] 2024-08-30 17:20:36.472 - [任务 16][Oracle] - Starting stream read, table list: [AA00PP], offset: {"sortString":null,"offsetValue":null,"lastScn":104386295,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-08-30 17:20:36.474 - [任务 16][Oracle] - total start mining scn: 104386295 
[INFO ] 2024-08-30 17:20:36.796 - [任务 16][Mysql3306] - Node(Mysql3306) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-30 17:20:36.798 - [任务 16][Mysql3306] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-30 17:20:37.631 - [任务 16][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-08-30 17:25:48.960 - [任务 16] - Stop task milestones: 66d18ebab452330bf594cf69(任务 16)  
[INFO ] 2024-08-30 17:25:49.012 - [任务 16][Oracle] - Node Oracle[8ae0e287-3026-4722-9f44-9050479b459f] running status set to false 
[INFO ] 2024-08-30 17:25:49.068 - [任务 16][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-08-30 17:25:49.069 - [任务 16][Oracle] - Log Miner has been closed! 
[INFO ] 2024-08-30 17:25:49.162 - [任务 16][Oracle] - PDK connector node stopped: HazelcastSourcePdkDataNode-8ae0e287-3026-4722-9f44-9050479b459f 
[INFO ] 2024-08-30 17:25:49.162 - [任务 16][Oracle] - PDK connector node released: HazelcastSourcePdkDataNode-8ae0e287-3026-4722-9f44-9050479b459f 
[INFO ] 2024-08-30 17:25:49.162 - [任务 16][Oracle] - Node Oracle[8ae0e287-3026-4722-9f44-9050479b459f] schema data cleaned 
[INFO ] 2024-08-30 17:25:49.162 - [任务 16][Oracle] - Node Oracle[8ae0e287-3026-4722-9f44-9050479b459f] monitor closed 
[INFO ] 2024-08-30 17:25:49.164 - [任务 16][Oracle] - Node Oracle[8ae0e287-3026-4722-9f44-9050479b459f] close complete, cost 157 ms 
[INFO ] 2024-08-30 17:25:49.166 - [任务 16][Mysql3306] - Node Mysql3306[e4968bae-4d75-4faf-8456-38fe5cb1d7c8] running status set to false 
[INFO ] 2024-08-30 17:25:49.207 - [任务 16][Mysql3306] - PDK connector node stopped: HazelcastTargetPdkDataNode-e4968bae-4d75-4faf-8456-38fe5cb1d7c8 
[INFO ] 2024-08-30 17:25:49.208 - [任务 16][Mysql3306] - PDK connector node released: HazelcastTargetPdkDataNode-e4968bae-4d75-4faf-8456-38fe5cb1d7c8 
[INFO ] 2024-08-30 17:25:49.208 - [任务 16][Mysql3306] - Node Mysql3306[e4968bae-4d75-4faf-8456-38fe5cb1d7c8] schema data cleaned 
[INFO ] 2024-08-30 17:25:49.208 - [任务 16][Mysql3306] - Node Mysql3306[e4968bae-4d75-4faf-8456-38fe5cb1d7c8] monitor closed 
[INFO ] 2024-08-30 17:25:49.208 - [任务 16][Mysql3306] - Node Mysql3306[e4968bae-4d75-4faf-8456-38fe5cb1d7c8] close complete, cost 43 ms 
[INFO ] 2024-08-30 17:25:50.154 - [任务 16] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-30 17:25:50.154 - [任务 16] - Stopped task aspect(s) 
[INFO ] 2024-08-30 17:25:50.154 - [任务 16] - Snapshot order controller have been removed 
[INFO ] 2024-08-30 17:25:50.245 - [任务 16] - Remove memory task client succeed, task: 任务 16[66d18ebab452330bf594cf69] 
[INFO ] 2024-08-30 17:25:50.246 - [任务 16] - Destroy memory task client cache succeed, task: 任务 16[66d18ebab452330bf594cf69] 
