[INFO ] 2024-07-18 12:08:55.895 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537] - Start task milestones: 669895578315b25db9f54591(Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537) 
[INFO ] 2024-07-18 12:08:55.956 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-18 12:08:56.125 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537] - The engine receives Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 12:08:56.204 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6704f780-f0f6-46a7-bdfe-492f5b9d5d69] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:08:56.205 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8b1aa7d0-f9bd-4102-9f8b-551714dea608] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:08:56.205 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8b1aa7d0-f9bd-4102-9f8b-551714dea608] preload schema finished, cost 1 ms 
[INFO ] 2024-07-18 12:08:56.205 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6704f780-f0f6-46a7-bdfe-492f5b9d5d69] preload schema finished, cost 1 ms 
[INFO ] 2024-07-18 12:08:56.515 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-18 12:08:56.515 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-18 12:08:56.515 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 12:08:56.517 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721275736515,"lastTimes":1721275736515,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-18 12:08:56.673 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-18 12:08:56.680 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-18 12:08:56.680 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 12:08:56.708 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-18 12:08:56.709 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 12:08:56.711 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-18 12:08:56.715 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721275736515,"lastTimes":1721275736515,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-18 12:08:56.715 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-18 12:08:56.919 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 12:32:55.806 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6704f780-f0f6-46a7-bdfe-492f5b9d5d69] running status set to false 
[INFO ] 2024-07-18 12:32:55.807 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-18 12:32:55.808 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-6704f780-f0f6-46a7-bdfe-492f5b9d5d69 
[INFO ] 2024-07-18 12:32:55.808 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-6704f780-f0f6-46a7-bdfe-492f5b9d5d69 
[INFO ] 2024-07-18 12:32:55.808 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6704f780-f0f6-46a7-bdfe-492f5b9d5d69] schema data cleaned 
[INFO ] 2024-07-18 12:32:55.812 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6704f780-f0f6-46a7-bdfe-492f5b9d5d69] monitor closed 
[INFO ] 2024-07-18 12:32:55.812 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[6704f780-f0f6-46a7-bdfe-492f5b9d5d69] close complete, cost 17 ms 
[INFO ] 2024-07-18 12:32:55.848 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8b1aa7d0-f9bd-4102-9f8b-551714dea608] running status set to false 
[INFO ] 2024-07-18 12:32:55.848 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b1aa7d0-f9bd-4102-9f8b-551714dea608 
[INFO ] 2024-07-18 12:32:55.848 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-8b1aa7d0-f9bd-4102-9f8b-551714dea608 
[INFO ] 2024-07-18 12:32:55.848 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8b1aa7d0-f9bd-4102-9f8b-551714dea608] schema data cleaned 
[INFO ] 2024-07-18 12:32:55.849 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8b1aa7d0-f9bd-4102-9f8b-551714dea608] monitor closed 
[INFO ] 2024-07-18 12:32:55.849 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8b1aa7d0-f9bd-4102-9f8b-551714dea608] close complete, cost 37 ms 
[INFO ] 2024-07-18 12:33:00.754 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 12:33:00.754 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2d7e2832 
[INFO ] 2024-07-18 12:33:00.879 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537] - Stop task milestones: 669895578315b25db9f54591(Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537)  
[INFO ] 2024-07-18 12:33:00.879 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537] - Stopped task aspect(s) 
[INFO ] 2024-07-18 12:33:00.879 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 12:33:00.921 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537] - Remove memory task client succeed, task: Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537[669895578315b25db9f54591] 
[INFO ] 2024-07-18 12:33:00.921 - [Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537] - Destroy memory task client cache succeed, task: Heartbeat-qa_mongodb_cluster_27017_1717403468657_3537[669895578315b25db9f54591] 
