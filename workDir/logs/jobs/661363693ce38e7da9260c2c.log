[INFO ] 2024-04-08 11:24:55.405 - [任务 10] - Task initialization... 
[INFO ] 2024-04-08 11:24:55.406 - [任务 10] - Start task milestones: 661363693ce38e7da9260c2c(任务 10) 
[INFO ] 2024-04-08 11:24:55.407 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-08 11:24:55.407 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-08 11:24:55.620 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] start preload schema,table counts: 1 
[INFO ] 2024-04-08 11:24:55.622 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] start preload schema,table counts: 1 
[INFO ] 2024-04-08 11:24:55.721 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] preload schema finished, cost 102 ms 
[INFO ] 2024-04-08 11:24:55.927 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] preload schema finished, cost 99 ms 
[INFO ] 2024-04-08 11:24:57.175 - [任务 10][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-08 11:24:57.175 - [任务 10][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-08 11:24:57.180 - [任务 10][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-08 11:24:57.230 - [任务 10][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000021","position":3360464,"gtidSet":""} 
[INFO ] 2024-04-08 11:24:57.231 - [任务 10][test8] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-08 11:24:57.381 - [任务 10][CLAIM] - Initial sync started 
[INFO ] 2024-04-08 11:24:57.383 - [任务 10][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-08 11:24:57.445 - [任务 10][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-08 11:24:57.445 - [任务 10][CLAIM] - Query table 'CLAIM' counts: 1078 
[INFO ] 2024-04-08 11:24:57.445 - [任务 10][CLAIM] - Initial sync completed 
[ERROR] 2024-04-08 11:24:57.458 - [任务 10][CLAIM] - java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list' <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:253)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:147)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:175)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:308)
	... 10 more
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshot$13(HazelcastSourcePdkDataNode.java:316)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 12 more
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1140)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:94)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadV2(MysqlConnector.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:388)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 18 more

[INFO ] 2024-04-08 11:24:57.667 - [任务 10][CLAIM] - Job suspend in error handle 
[INFO ] 2024-04-08 11:24:57.961 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] running status set to false 
[INFO ] 2024-04-08 11:24:57.997 - [任务 10][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-f311bb48-c96c-4c49-847c-30528ec9e0c4 
[INFO ] 2024-04-08 11:24:57.998 - [任务 10][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-f311bb48-c96c-4c49-847c-30528ec9e0c4 
[INFO ] 2024-04-08 11:24:58.002 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] schema data cleaned 
[INFO ] 2024-04-08 11:24:58.002 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] monitor closed 
[INFO ] 2024-04-08 11:24:58.022 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] close complete, cost 52 ms 
[INFO ] 2024-04-08 11:24:58.023 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] running status set to false 
[INFO ] 2024-04-08 11:24:58.047 - [任务 10][test8] - PDK connector node stopped: HazelcastTargetPdkDataNode-ac81ed8d-ab94-44f6-b325-dd66288e49c3 
[INFO ] 2024-04-08 11:24:58.048 - [任务 10][test8] - PDK connector node released: HazelcastTargetPdkDataNode-ac81ed8d-ab94-44f6-b325-dd66288e49c3 
[INFO ] 2024-04-08 11:24:58.049 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] schema data cleaned 
[INFO ] 2024-04-08 11:24:58.049 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] monitor closed 
[INFO ] 2024-04-08 11:24:58.051 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] close complete, cost 29 ms 
[INFO ] 2024-04-08 11:24:58.164 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-04-08 11:24:58.169 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-08 11:24:58.193 - [任务 10] - Stop task milestones: 661363693ce38e7da9260c2c(任务 10)  
[INFO ] 2024-04-08 11:24:58.193 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-04-08 11:24:58.225 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-04-08 11:24:58.227 - [任务 10] - Remove memory task client succeed, task: 任务 10[661363693ce38e7da9260c2c] 
[INFO ] 2024-04-08 11:24:58.232 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[661363693ce38e7da9260c2c] 
[INFO ] 2024-04-08 11:25:18.909 - [任务 10] - Task initialization... 
[INFO ] 2024-04-08 11:25:18.912 - [任务 10] - Start task milestones: 661363693ce38e7da9260c2c(任务 10) 
[INFO ] 2024-04-08 11:25:18.947 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-08 11:25:19.097 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-08 11:25:19.098 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] start preload schema,table counts: 1 
[INFO ] 2024-04-08 11:25:19.098 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] start preload schema,table counts: 1 
[INFO ] 2024-04-08 11:25:19.165 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] preload schema finished, cost 62 ms 
[INFO ] 2024-04-08 11:25:19.166 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] preload schema finished, cost 60 ms 
[INFO ] 2024-04-08 11:25:20.034 - [任务 10][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-08 11:25:20.036 - [任务 10][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-08 11:25:20.036 - [任务 10][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-08 11:25:20.040 - [任务 10][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000021","position":3360464,"gtidSet":""} 
[INFO ] 2024-04-08 11:25:20.123 - [任务 10][CLAIM] - Initial sync started 
[INFO ] 2024-04-08 11:25:20.124 - [任务 10][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-08 11:25:20.217 - [任务 10][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-08 11:25:20.218 - [任务 10][CLAIM] - Query table 'CLAIM' counts: 1078 
[INFO ] 2024-04-08 11:25:20.218 - [任务 10][test8] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-08 11:25:20.427 - [任务 10][test8] - The table test8 has already exist. 
[ERROR] 2024-04-08 11:25:20.477 - [任务 10][test8] - Execute PDK method: TARGET_WRITE_RECORD, tableName: test8 <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test8

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Engine initialization failed!
	io.tapdata.connector.kafka.KafkaService.produce(KafkaService.java:476)
	io.tapdata.connector.kafka.KafkaConnector.writeRecord(KafkaConnector.java:292)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:827)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test8
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:833)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$55(HazelcastTargetPdkDataNode.java:779)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:773)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$24(HazelcastTargetPdkDataNode.java:482)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:482)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:445)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 0 | message: Engine initialization failed!
	at io.tapdata.connector.kafka.KafkaService.produce(KafkaService.java:476)
	at io.tapdata.connector.kafka.KafkaConnector.writeRecord(KafkaConnector.java:292)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:827)
	... 24 more

[INFO ] 2024-04-08 11:25:20.686 - [任务 10][test8] - Job suspend in error handle 
[INFO ] 2024-04-08 11:25:21.347 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] running status set to false 
[INFO ] 2024-04-08 11:25:21.347 - [任务 10][CLAIM] - Initial sync completed 
[INFO ] 2024-04-08 11:25:21.352 - [任务 10][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-08 11:25:21.352 - [任务 10][CLAIM] - Incremental sync completed 
[INFO ] 2024-04-08 11:25:21.372 - [任务 10][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-f311bb48-c96c-4c49-847c-30528ec9e0c4 
[INFO ] 2024-04-08 11:25:21.372 - [任务 10][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-f311bb48-c96c-4c49-847c-30528ec9e0c4 
[INFO ] 2024-04-08 11:25:21.372 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] schema data cleaned 
[INFO ] 2024-04-08 11:25:21.372 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] monitor closed 
[INFO ] 2024-04-08 11:25:21.375 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] close complete, cost 42 ms 
[INFO ] 2024-04-08 11:25:21.375 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] running status set to false 
[INFO ] 2024-04-08 11:25:21.391 - [任务 10][test8] - PDK connector node stopped: HazelcastTargetPdkDataNode-ac81ed8d-ab94-44f6-b325-dd66288e49c3 
[INFO ] 2024-04-08 11:25:21.391 - [任务 10][test8] - PDK connector node released: HazelcastTargetPdkDataNode-ac81ed8d-ab94-44f6-b325-dd66288e49c3 
[INFO ] 2024-04-08 11:25:21.393 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] schema data cleaned 
[INFO ] 2024-04-08 11:25:21.393 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] monitor closed 
[INFO ] 2024-04-08 11:25:21.599 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] close complete, cost 19 ms 
[INFO ] 2024-04-08 11:25:23.250 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-04-08 11:25:23.264 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-08 11:25:23.264 - [任务 10] - Stop task milestones: 661363693ce38e7da9260c2c(任务 10)  
[INFO ] 2024-04-08 11:25:23.292 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-04-08 11:25:23.292 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-04-08 11:25:23.316 - [任务 10] - Remove memory task client succeed, task: 任务 10[661363693ce38e7da9260c2c] 
[INFO ] 2024-04-08 11:25:23.316 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[661363693ce38e7da9260c2c] 
[INFO ] 2024-04-08 11:26:12.708 - [任务 10] - Task initialization... 
[INFO ] 2024-04-08 11:26:12.711 - [任务 10] - Start task milestones: 661363693ce38e7da9260c2c(任务 10) 
[INFO ] 2024-04-08 11:26:12.784 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-08 11:26:12.785 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-08 11:26:12.900 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] start preload schema,table counts: 1 
[INFO ] 2024-04-08 11:26:12.900 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] start preload schema,table counts: 1 
[INFO ] 2024-04-08 11:26:12.952 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] preload schema finished, cost 52 ms 
[INFO ] 2024-04-08 11:26:12.952 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] preload schema finished, cost 52 ms 
[INFO ] 2024-04-08 11:26:13.868 - [任务 10][test8] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-08 11:26:13.983 - [任务 10][test8] - The table test8 has already exist. 
[INFO ] 2024-04-08 11:26:13.984 - [任务 10][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-08 11:26:13.986 - [任务 10][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-08 11:26:13.986 - [任务 10][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-08 11:26:14.044 - [任务 10][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000021","position":3360464,"gtidSet":""} 
[INFO ] 2024-04-08 11:26:14.044 - [任务 10][CLAIM] - Initial sync started 
[INFO ] 2024-04-08 11:26:14.050 - [任务 10][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-08 11:26:14.064 - [任务 10][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-08 11:26:14.086 - [任务 10][CLAIM] - Query table 'CLAIM' counts: 1078 
[ERROR] 2024-04-08 11:26:36.886 - [任务 10][test8] - Execute PDK method: TARGET_WRITE_RECORD, tableName: test8 <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test8

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Engine initialization failed!
	io.tapdata.connector.kafka.KafkaService.produce(KafkaService.java:476)
	io.tapdata.connector.kafka.KafkaConnector.writeRecord(KafkaConnector.java:292)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:827)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test8
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:833)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$55(HazelcastTargetPdkDataNode.java:779)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:773)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$24(HazelcastTargetPdkDataNode.java:482)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:482)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:445)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 0 | message: Engine initialization failed!
	at io.tapdata.connector.kafka.KafkaService.produce(KafkaService.java:476)
	at io.tapdata.connector.kafka.KafkaConnector.writeRecord(KafkaConnector.java:292)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:827)
	... 24 more

[INFO ] 2024-04-08 11:26:37.092 - [任务 10][test8] - Job suspend in error handle 
[INFO ] 2024-04-08 11:26:37.743 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] running status set to false 
[INFO ] 2024-04-08 11:26:37.776 - [任务 10][CLAIM] - Initial sync completed 
[INFO ] 2024-04-08 11:26:37.776 - [任务 10][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-08 11:26:37.777 - [任务 10][CLAIM] - Incremental sync completed 
[INFO ] 2024-04-08 11:26:37.786 - [任务 10][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-f311bb48-c96c-4c49-847c-30528ec9e0c4 
[INFO ] 2024-04-08 11:26:37.786 - [任务 10][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-f311bb48-c96c-4c49-847c-30528ec9e0c4 
[INFO ] 2024-04-08 11:26:37.788 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] schema data cleaned 
[INFO ] 2024-04-08 11:26:37.788 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] monitor closed 
[INFO ] 2024-04-08 11:26:37.792 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] close complete, cost 48 ms 
[INFO ] 2024-04-08 11:26:37.794 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] running status set to false 
[INFO ] 2024-04-08 11:26:37.803 - [任务 10][test8] - PDK connector node stopped: HazelcastTargetPdkDataNode-ac81ed8d-ab94-44f6-b325-dd66288e49c3 
[INFO ] 2024-04-08 11:26:37.804 - [任务 10][test8] - PDK connector node released: HazelcastTargetPdkDataNode-ac81ed8d-ab94-44f6-b325-dd66288e49c3 
[INFO ] 2024-04-08 11:26:37.806 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] schema data cleaned 
[INFO ] 2024-04-08 11:26:37.807 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] monitor closed 
[INFO ] 2024-04-08 11:26:38.011 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] close complete, cost 16 ms 
[INFO ] 2024-04-08 11:26:41.847 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-04-08 11:26:41.865 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-08 11:26:41.865 - [任务 10] - Stop task milestones: 661363693ce38e7da9260c2c(任务 10)  
[INFO ] 2024-04-08 11:26:41.889 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-04-08 11:26:41.889 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-04-08 11:26:41.912 - [任务 10] - Remove memory task client succeed, task: 任务 10[661363693ce38e7da9260c2c] 
[INFO ] 2024-04-08 11:26:41.913 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[661363693ce38e7da9260c2c] 
[INFO ] 2024-04-08 11:26:47.378 - [任务 10] - Task initialization... 
[INFO ] 2024-04-08 11:26:47.381 - [任务 10] - Start task milestones: 661363693ce38e7da9260c2c(任务 10) 
[INFO ] 2024-04-08 11:26:47.492 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-08 11:26:47.492 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-08 11:26:47.533 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] start preload schema,table counts: 1 
[INFO ] 2024-04-08 11:26:47.533 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] start preload schema,table counts: 1 
[INFO ] 2024-04-08 11:26:47.580 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] preload schema finished, cost 38 ms 
[INFO ] 2024-04-08 11:26:47.584 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] preload schema finished, cost 41 ms 
[INFO ] 2024-04-08 11:26:48.480 - [任务 10][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-08 11:26:48.480 - [任务 10][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-08 11:26:48.480 - [任务 10][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-08 11:26:48.481 - [任务 10][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000021","position":3360464,"gtidSet":""} 
[INFO ] 2024-04-08 11:26:48.527 - [任务 10][CLAIM] - Initial sync started 
[INFO ] 2024-04-08 11:26:48.533 - [任务 10][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-08 11:26:48.573 - [任务 10][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-08 11:26:48.574 - [任务 10][CLAIM] - Query table 'CLAIM' counts: 1078 
[INFO ] 2024-04-08 11:26:48.735 - [任务 10][test8] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-08 11:26:48.736 - [任务 10][test8] - The table test8 has already exist. 
[ERROR] 2024-04-08 11:26:50.614 - [任务 10][test8] - Execute PDK method: TARGET_WRITE_RECORD, tableName: test8 <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test8

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Engine initialization failed!
	io.tapdata.connector.kafka.KafkaService.produce(KafkaService.java:476)
	io.tapdata.connector.kafka.KafkaConnector.writeRecord(KafkaConnector.java:292)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:827)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test8
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:833)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$55(HazelcastTargetPdkDataNode.java:779)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:773)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$24(HazelcastTargetPdkDataNode.java:482)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:482)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:445)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 0 | message: Engine initialization failed!
	at io.tapdata.connector.kafka.KafkaService.produce(KafkaService.java:476)
	at io.tapdata.connector.kafka.KafkaConnector.writeRecord(KafkaConnector.java:292)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:827)
	... 24 more

[INFO ] 2024-04-08 11:26:50.615 - [任务 10][test8] - Job suspend in error handle 
[INFO ] 2024-04-08 11:26:51.605 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] running status set to false 
[INFO ] 2024-04-08 11:26:51.636 - [任务 10][CLAIM] - Initial sync completed 
[ERROR] 2024-04-08 11:26:51.638 - [任务 10][CLAIM] - java.lang.RuntimeException: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 3,069 milliseconds ago. The last packet sent successfully to the server was 3,070 milliseconds ago. <-- Error Message -->
java.lang.RuntimeException: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 3,069 milliseconds ago. The last packet sent successfully to the server was 3,070 milliseconds ago.

<-- Simple Stack Trace -->
Caused by: java.io.IOException: Socket is closed.
	com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	com.mysql.cj.protocol.a.NativeProtocol.clearInputStream(NativeProtocol.java:866)
	com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:682)
	com.mysql.cj.jdbc.ServerPreparedStatement.realClose(ServerPreparedStatement.java:458)
	com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1958)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 3,069 milliseconds ago. The last packet sent successfully to the server was 3,070 milliseconds ago.
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:253)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:147)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:175)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 3,069 milliseconds ago. The last packet sent successfully to the server was 3,070 milliseconds ago.
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:308)
	... 10 more
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 3,069 milliseconds ago. The last packet sent successfully to the server was 3,070 milliseconds ago.
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshot$13(HazelcastSourcePdkDataNode.java:316)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 12 more
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 3,069 milliseconds ago. The last packet sent successfully to the server was 3,070 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ServerPreparedStatement.realClose(ServerPreparedStatement.java:471)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1958)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:99)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadV2(MysqlConnector.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:388)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 18 more
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 3,069 milliseconds ago. The last packet sent successfully to the server was 3,070 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeProtocol.clearInputStream(NativeProtocol.java:870)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:682)
	at com.mysql.cj.jdbc.ServerPreparedStatement.realClose(ServerPreparedStatement.java:458)
	... 25 more
Caused by: java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.NativeProtocol.clearInputStream(NativeProtocol.java:866)
	... 27 more

[INFO ] 2024-04-08 11:26:51.652 - [任务 10][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-f311bb48-c96c-4c49-847c-30528ec9e0c4 
[INFO ] 2024-04-08 11:26:51.652 - [任务 10][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-f311bb48-c96c-4c49-847c-30528ec9e0c4 
[INFO ] 2024-04-08 11:26:51.654 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] schema data cleaned 
[INFO ] 2024-04-08 11:26:51.656 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] monitor closed 
[INFO ] 2024-04-08 11:26:51.656 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] close complete, cost 73 ms 
[INFO ] 2024-04-08 11:26:51.667 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] running status set to false 
[INFO ] 2024-04-08 11:26:51.667 - [任务 10][test8] - PDK connector node stopped: HazelcastTargetPdkDataNode-ac81ed8d-ab94-44f6-b325-dd66288e49c3 
[INFO ] 2024-04-08 11:26:51.669 - [任务 10][test8] - PDK connector node released: HazelcastTargetPdkDataNode-ac81ed8d-ab94-44f6-b325-dd66288e49c3 
[INFO ] 2024-04-08 11:26:51.669 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] schema data cleaned 
[INFO ] 2024-04-08 11:26:51.676 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] monitor closed 
[INFO ] 2024-04-08 11:26:51.677 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] close complete, cost 16 ms 
[INFO ] 2024-04-08 11:26:51.941 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-04-08 11:26:51.941 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-08 11:26:51.977 - [任务 10] - Stop task milestones: 661363693ce38e7da9260c2c(任务 10)  
[INFO ] 2024-04-08 11:26:51.978 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-04-08 11:26:51.978 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-04-08 11:26:52.005 - [任务 10] - Remove memory task client succeed, task: 任务 10[661363693ce38e7da9260c2c] 
[INFO ] 2024-04-08 11:26:52.005 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[661363693ce38e7da9260c2c] 
[INFO ] 2024-04-08 11:27:33.091 - [任务 10] - Start task milestones: 661363693ce38e7da9260c2c(任务 10) 
[INFO ] 2024-04-08 11:27:33.091 - [任务 10] - Task initialization... 
[INFO ] 2024-04-08 11:27:33.179 - [任务 10] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-08 11:27:33.383 - [任务 10] - The engine receives 任务 10 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-08 11:27:34.011 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] start preload schema,table counts: 1 
[INFO ] 2024-04-08 11:27:34.021 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] start preload schema,table counts: 1 
[INFO ] 2024-04-08 11:27:34.145 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] preload schema finished, cost 135 ms 
[INFO ] 2024-04-08 11:27:34.346 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] preload schema finished, cost 132 ms 
[INFO ] 2024-04-08 11:27:35.535 - [任务 10][test8] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-08 11:27:35.538 - [任务 10][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-08 11:27:35.539 - [任务 10][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-08 11:27:35.540 - [任务 10][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-08 11:27:35.558 - [任务 10][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000021","position":3360464,"gtidSet":""} 
[INFO ] 2024-04-08 11:27:35.760 - [任务 10][test8] - The table test8 has already exist. 
[INFO ] 2024-04-08 11:27:35.857 - [任务 10][CLAIM] - Initial sync started 
[INFO ] 2024-04-08 11:27:35.875 - [任务 10][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-08 11:27:35.879 - [任务 10][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-08 11:27:35.957 - [任务 10][CLAIM] - Query table 'CLAIM' counts: 1078 
[ERROR] 2024-04-08 11:27:36.153 - [任务 10][test8] - Execute PDK method: TARGET_WRITE_RECORD, tableName: test8 <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test8

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Engine initialization failed!
	io.tapdata.connector.kafka.KafkaService.produce(KafkaService.java:476)
	io.tapdata.connector.kafka.KafkaConnector.writeRecord(KafkaConnector.java:292)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:827)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: test8
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:833)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$55(HazelcastTargetPdkDataNode.java:779)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:773)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$24(HazelcastTargetPdkDataNode.java:482)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:482)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:445)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 0 | message: Engine initialization failed!
	at io.tapdata.connector.kafka.KafkaService.produce(KafkaService.java:476)
	at io.tapdata.connector.kafka.KafkaConnector.writeRecord(KafkaConnector.java:292)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:827)
	... 24 more

[INFO ] 2024-04-08 11:27:36.356 - [任务 10][test8] - Job suspend in error handle 
[INFO ] 2024-04-08 11:27:36.983 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] running status set to false 
[INFO ] 2024-04-08 11:27:36.990 - [任务 10][CLAIM] - Initial sync completed 
[INFO ] 2024-04-08 11:27:36.990 - [任务 10][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-08 11:27:36.990 - [任务 10][CLAIM] - Incremental sync completed 
[INFO ] 2024-04-08 11:27:37.005 - [任务 10][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-f311bb48-c96c-4c49-847c-30528ec9e0c4 
[INFO ] 2024-04-08 11:27:37.010 - [任务 10][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-f311bb48-c96c-4c49-847c-30528ec9e0c4 
[INFO ] 2024-04-08 11:27:37.010 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] schema data cleaned 
[INFO ] 2024-04-08 11:27:37.028 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] monitor closed 
[INFO ] 2024-04-08 11:27:37.029 - [任务 10][CLAIM] - Node CLAIM[f311bb48-c96c-4c49-847c-30528ec9e0c4] close complete, cost 49 ms 
[INFO ] 2024-04-08 11:27:37.030 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] running status set to false 
[INFO ] 2024-04-08 11:27:37.052 - [任务 10][test8] - PDK connector node stopped: HazelcastTargetPdkDataNode-ac81ed8d-ab94-44f6-b325-dd66288e49c3 
[INFO ] 2024-04-08 11:27:37.053 - [任务 10][test8] - PDK connector node released: HazelcastTargetPdkDataNode-ac81ed8d-ab94-44f6-b325-dd66288e49c3 
[INFO ] 2024-04-08 11:27:37.057 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] schema data cleaned 
[INFO ] 2024-04-08 11:27:37.058 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] monitor closed 
[INFO ] 2024-04-08 11:27:37.058 - [任务 10][test8] - Node test8[ac81ed8d-ab94-44f6-b325-dd66288e49c3] close complete, cost 33 ms 
[INFO ] 2024-04-08 11:27:40.628 - [任务 10] - Task [任务 10] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-04-08 11:27:40.666 - [任务 10] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-08 11:27:40.666 - [任务 10] - Stop task milestones: 661363693ce38e7da9260c2c(任务 10)  
[INFO ] 2024-04-08 11:27:40.684 - [任务 10] - Stopped task aspect(s) 
[INFO ] 2024-04-08 11:27:40.723 - [任务 10] - Snapshot order controller have been removed 
[INFO ] 2024-04-08 11:27:40.724 - [任务 10] - Remove memory task client succeed, task: 任务 10[661363693ce38e7da9260c2c] 
[INFO ] 2024-04-08 11:27:40.724 - [任务 10] - Destroy memory task client cache succeed, task: 任务 10[661363693ce38e7da9260c2c] 
