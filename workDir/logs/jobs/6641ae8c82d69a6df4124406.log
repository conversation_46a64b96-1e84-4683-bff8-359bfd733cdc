[INFO ] 2024-05-14 10:31:16.373 - [任务 2] - Task initialization... 
[INFO ] 2024-05-14 10:31:16.386 - [任务 2] - Start task milestones: 6641ae8c82d69a6df4124406(任务 2) 
[INFO ] 2024-05-14 10:31:16.387 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-14 10:31:16.467 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-14 10:31:16.800 - [任务 2][dorisTest1] - Node dorisTest1[c5f39c86-ac4b-4694-b642-d39bbc930835] start preload schema,table counts: 1 
[INFO ] 2024-05-14 10:31:16.800 - [任务 2][test] - Node test[ed84b84c-4267-4ebc-ba4a-2a34f77cbd75] start preload schema,table counts: 1 
[INFO ] 2024-05-14 10:31:16.966 - [任务 2][test] - Node test[ed84b84c-4267-4ebc-ba4a-2a34f77cbd75] preload schema finished, cost 160 ms 
[INFO ] 2024-05-14 10:31:16.970 - [任务 2][dorisTest1] - Node dorisTest1[c5f39c86-ac4b-4694-b642-d39bbc930835] preload schema finished, cost 164 ms 
[INFO ] 2024-05-14 10:31:18.188 - [任务 2][dorisTest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-14 10:31:18.220 - [任务 2][test] - Source node "test" read batch size: 100 
[INFO ] 2024-05-14 10:31:18.221 - [任务 2][test] - Source node "test" event queue capacity: 200 
[INFO ] 2024-05-14 10:31:18.222 - [任务 2][test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-14 10:31:18.231 - [任务 2][test] - batch offset found: {},stream offset found: {} 
[INFO ] 2024-05-14 10:31:18.231 - [任务 2][test] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-14 10:31:18.489 - [任务 2][test] - Initial sync started 
[INFO ] 2024-05-14 10:31:18.519 - [任务 2][test] - Starting batch read, table name: test, offset: null 
[INFO ] 2024-05-14 10:31:18.526 - [任务 2][test] - Table test is going to be initial synced 
[INFO ] 2024-05-14 10:31:18.641 - [任务 2][test] - Query table 'test' counts: 11 
[INFO ] 2024-05-14 10:31:18.648 - [任务 2][test] - Initial sync completed 
[INFO ] 2024-05-14 10:31:18.648 - [任务 2][test] - Incremental sync starting... 
[INFO ] 2024-05-14 10:31:18.650 - [任务 2][test] - Initial sync completed 
[INFO ] 2024-05-14 10:31:18.653 - [任务 2][test] - Start run table [test] polling cdc with parameters 
 - Conditional field(s): {test={created=2024-05-19 23:46:43.0}}
 - Loop polling interval: 500 ms
 - Batch size: 1000 
[INFO ] 2024-05-14 10:31:18.655 - [任务 2][test] - Query by advance filter
 - loop time: 1
 - table: test
 - filter: [created>'2024-05-19 23:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 10:36:56.538 - [任务 2][test] - Query by advance filter
 - loop time: 600
 - table: test
 - filter: [created>'2024-05-19 23:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 10:48:28.955 - [任务 2][test] - Query by advance filter
 - loop time: 1200
 - table: test
 - filter: [created>'2024-05-21 23:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 10:56:26.347 - [任务 2][test] - Query by advance filter
 - loop time: 1800
 - table: test
 - filter: [created>'2024-05-23 23:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 11:09:56.330 - [任务 2][test] - Query by advance filter
 - loop time: 2400
 - table: test
 - filter: [created>'2024-05-25 23:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 11:19:36.837 - [任务 2][test] - Query by advance filter
 - loop time: 3000
 - table: test
 - filter: [created>'2024-05-26 23:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 11:33:59.479 - [任务 2][test] - Query by advance filter
 - loop time: 3600
 - table: test
 - filter: [created>'2024-05-26 23:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 11:39:46.553 - [任务 2][test] - Query by advance filter
 - loop time: 4200
 - table: test
 - filter: [created>'2024-05-26 23:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 11:45:34.926 - [任务 2][test] - Query by advance filter
 - loop time: 4800
 - table: test
 - filter: [created>'2024-05-26 23:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 11:51:22.985 - [任务 2][test] - Query by advance filter
 - loop time: 5400
 - table: test
 - filter: [created>'2024-05-26 23:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[WARN ] 2024-05-14 12:18:16.521 - [任务 2][dorisTest1] - Save to snapshot failed, collection: Task/syncProgress/6641ae8c82d69a6df4124406, object: {ed84b84c-4267-4ebc-ba4a-2a34f77cbd75,c5f39c86-ac4b-4694-b642-d39bbc930835=SyncProgress{eventSerialNo=62, syncStage='CDC', batchOffset='{test=null}', streamOffset='{test={created=2024-05-29 23:46:43.0}}'}}, errors: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/syncProgress/6641ae8c82d69a6df4124406": Broken pipe (Write failed); nested exception is java.net.SocketException: Broken pipe (Write failed). 
[WARN ] 2024-05-14 12:18:16.522 - [任务 2] - Save milestone failed: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/update": Broken pipe (Write failed); nested exception is java.net.SocketException: Broken pipe (Write failed). 
[INFO ] 2024-05-14 12:30:58.320 - [任务 2][test] - Query by advance filter
 - loop time: 6000
 - table: test
 - filter: [created>'2024-06-04 23:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 12:36:46.387 - [任务 2][test] - Query by advance filter
 - loop time: 6600
 - table: test
 - filter: [created>'2024-06-04 23:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 12:42:30.369 - [任务 2][test] - Query by advance filter
 - loop time: 7200
 - table: test
 - filter: [created>'2024-06-04 23:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 12:48:17.791 - [任务 2][test] - Query by advance filter
 - loop time: 7800
 - table: test
 - filter: [created>'2024-06-04 23:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 12:55:18.999 - [任务 2][test] - Query by advance filter
 - loop time: 8400
 - table: test
 - filter: [created>'2024-06-04 23:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 13:07:39.150 - [任务 2][test] - Query by advance filter
 - loop time: 9000
 - table: test
 - filter: [created>'2024-06-05 23:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 13:13:24.360 - [任务 2][test] - Query by advance filter
 - loop time: 9600
 - table: test
 - filter: [created>'2024-06-05 23:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 13:22:25.114 - [任务 2][test] - Query by advance filter
 - loop time: 10200
 - table: test
 - filter: [created>'2024-06-06 15:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 13:29:00.040 - [任务 2][test] - Query by advance filter
 - loop time: 10800
 - table: test
 - filter: [created>'2024-06-06 15:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 13:56:22.489 - [任务 2][test] - Query by advance filter
 - loop time: 11400
 - table: test
 - filter: [created>'2024-06-06 15:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 14:03:17.082 - [任务 2][test] - Query by advance filter
 - loop time: 12000
 - table: test
 - filter: [created>'2024-06-06 15:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 14:09:16.121 - [任务 2][test] - Query by advance filter
 - loop time: 12600
 - table: test
 - filter: [created>'2024-06-06 15:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 14:15:07.245 - [任务 2][test] - Query by advance filter
 - loop time: 13200
 - table: test
 - filter: [created>'2024-06-06 15:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 14:21:05.941 - [任务 2][test] - Query by advance filter
 - loop time: 13800
 - table: test
 - filter: [created>'2024-06-06 15:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 14:26:59.903 - [任务 2][test] - Query by advance filter
 - loop time: 14400
 - table: test
 - filter: [created>'2024-06-06 15:46:43.0']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 14:32:14.649 - [任务 2] - Stop task milestones: 6641ae8c82d69a6df4124406(任务 2)  
[INFO ] 2024-05-14 14:32:14.651 - [任务 2][test] - Node test[ed84b84c-4267-4ebc-ba4a-2a34f77cbd75] running status set to false 
[INFO ] 2024-05-14 14:32:14.651 - [任务 2][test] - PDK connector node stopped: HazelcastSourcePdkDataNode-ed84b84c-4267-4ebc-ba4a-2a34f77cbd75 
[INFO ] 2024-05-14 14:32:14.651 - [任务 2][test] - PDK connector node released: HazelcastSourcePdkDataNode-ed84b84c-4267-4ebc-ba4a-2a34f77cbd75 
[INFO ] 2024-05-14 14:32:14.652 - [任务 2][test] - Node test[ed84b84c-4267-4ebc-ba4a-2a34f77cbd75] schema data cleaned 
[INFO ] 2024-05-14 14:32:14.652 - [任务 2][test] - Node test[ed84b84c-4267-4ebc-ba4a-2a34f77cbd75] monitor closed 
[INFO ] 2024-05-14 14:32:14.652 - [任务 2][test] - Node test[ed84b84c-4267-4ebc-ba4a-2a34f77cbd75] close complete, cost 26 ms 
[INFO ] 2024-05-14 14:32:14.652 - [任务 2][dorisTest1] - Node dorisTest1[c5f39c86-ac4b-4694-b642-d39bbc930835] running status set to false 
[INFO ] 2024-05-14 14:32:14.653 - [任务 2][dorisTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-c5f39c86-ac4b-4694-b642-d39bbc930835 
[INFO ] 2024-05-14 14:32:14.653 - [任务 2][dorisTest1] - PDK connector node released: HazelcastTargetPdkDataNode-c5f39c86-ac4b-4694-b642-d39bbc930835 
[INFO ] 2024-05-14 14:32:14.653 - [任务 2][dorisTest1] - Node dorisTest1[c5f39c86-ac4b-4694-b642-d39bbc930835] schema data cleaned 
[INFO ] 2024-05-14 14:32:14.654 - [任务 2][dorisTest1] - Node dorisTest1[c5f39c86-ac4b-4694-b642-d39bbc930835] monitor closed 
[INFO ] 2024-05-14 14:32:14.655 - [任务 2][dorisTest1] - Node dorisTest1[c5f39c86-ac4b-4694-b642-d39bbc930835] close complete, cost 26 ms 
[INFO ] 2024-05-14 14:32:16.875 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-14 14:32:16.876 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-05-14 14:32:16.876 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-05-14 14:32:16.942 - [任务 2] - Remove memory task client succeed, task: 任务 2[6641ae8c82d69a6df4124406] 
[INFO ] 2024-05-14 14:32:16.945 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[6641ae8c82d69a6df4124406] 
[INFO ] 2024-05-14 14:35:33.888 - [任务 2] - Task initialization... 
[INFO ] 2024-05-14 14:35:33.889 - [任务 2] - Start task milestones: 6641ae8c82d69a6df4124406(任务 2) 
[INFO ] 2024-05-14 14:35:33.890 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-14 14:35:33.890 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-14 14:35:33.890 - [任务 2][test] - Node test[ed84b84c-4267-4ebc-ba4a-2a34f77cbd75] start preload schema,table counts: 1 
[INFO ] 2024-05-14 14:35:33.890 - [任务 2][dorisTest1] - Node dorisTest1[c5f39c86-ac4b-4694-b642-d39bbc930835] start preload schema,table counts: 1 
[INFO ] 2024-05-14 14:35:34.098 - [任务 2][test] - Node test[ed84b84c-4267-4ebc-ba4a-2a34f77cbd75] preload schema finished, cost 63 ms 
[INFO ] 2024-05-14 14:35:34.098 - [任务 2][dorisTest1] - Node dorisTest1[c5f39c86-ac4b-4694-b642-d39bbc930835] preload schema finished, cost 46 ms 
[INFO ] 2024-05-14 14:35:35.103 - [任务 2][dorisTest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-14 14:35:41.471 - [任务 2][test] - Source node "test" read batch size: 100 
[INFO ] 2024-05-14 14:35:41.474 - [任务 2][test] - Source node "test" event queue capacity: 200 
[INFO ] 2024-05-14 14:35:41.474 - [任务 2][test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-14 14:35:41.476 - [任务 2][test] - batch offset found: {},stream offset found: {} 
[INFO ] 2024-05-14 14:35:41.543 - [任务 2][test] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-14 14:35:41.544 - [任务 2][test] - Initial sync started 
[INFO ] 2024-05-14 14:35:41.547 - [任务 2][test] - Starting batch read, table name: test, offset: null 
[INFO ] 2024-05-14 14:35:41.547 - [任务 2][test] - Table test is going to be initial synced 
[INFO ] 2024-05-14 14:35:41.702 - [任务 2][test] - Query table 'test' counts: 16 
[INFO ] 2024-05-14 14:35:41.707 - [任务 2][test] - Initial sync completed 
[INFO ] 2024-05-14 14:35:41.707 - [任务 2][test] - Incremental sync starting... 
[INFO ] 2024-05-14 14:35:41.707 - [任务 2][test] - Initial sync completed 
[INFO ] 2024-05-14 14:35:41.710 - [任务 2][test] - Start run table [test] polling cdc with parameters 
 - Conditional field(s): {test={created=2024-06-06 15:46:43.000000}}
 - Loop polling interval: 500 ms
 - Batch size: 1000 
[INFO ] 2024-05-14 14:35:41.711 - [任务 2][test] - Query by advance filter
 - loop time: 1
 - table: test
 - filter: [created>'2024-06-06 15:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 14:44:20.205 - [任务 2][test] - Query by advance filter
 - loop time: 600
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 14:50:03.546 - [任务 2][test] - Query by advance filter
 - loop time: 1200
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 14:55:46.887 - [任务 2][test] - Query by advance filter
 - loop time: 1800
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 15:02:56.084 - [任务 2][test] - Query by advance filter
 - loop time: 2400
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 15:08:40.290 - [任务 2][test] - Query by advance filter
 - loop time: 3000
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 15:14:25.891 - [任务 2][test] - Query by advance filter
 - loop time: 3600
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 15:20:34.167 - [任务 2][test] - Query by advance filter
 - loop time: 4200
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 15:26:28.417 - [任务 2][test] - Query by advance filter
 - loop time: 4800
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 15:32:23.009 - [任务 2][test] - Query by advance filter
 - loop time: 5400
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 15:38:26.017 - [任务 2][test] - Query by advance filter
 - loop time: 6000
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 15:44:15.860 - [任务 2][test] - Query by advance filter
 - loop time: 6600
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 15:50:08.915 - [任务 2][test] - Query by advance filter
 - loop time: 7200
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 15:55:53.270 - [任务 2][test] - Query by advance filter
 - loop time: 7800
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 16:01:33.969 - [任务 2][test] - Query by advance filter
 - loop time: 8400
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 16:06:44.436 - [任务 2][test] - Query by advance filter
 - loop time: 8400
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 16:07:16.592 - [任务 2][test] - Query by advance filter
 - loop time: 9000
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 16:12:52.316 - [任务 2][test] - Query by advance filter
 - loop time: 9600
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 16:18:29.214 - [任务 2][test] - Query by advance filter
 - loop time: 10200
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 16:24:21.998 - [任务 2][test] - Query by advance filter
 - loop time: 10800
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 16:30:02.815 - [任务 2][test] - Query by advance filter
 - loop time: 11400
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 16:35:44.182 - [任务 2][test] - Query by advance filter
 - loop time: 12000
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 16:41:23.621 - [任务 2][test] - Query by advance filter
 - loop time: 12600
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 16:47:10.466 - [任务 2][test] - Query by advance filter
 - loop time: 13200
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 16:52:57.137 - [任务 2][test] - Query by advance filter
 - loop time: 13800
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 16:58:39.063 - [任务 2][test] - Query by advance filter
 - loop time: 14400
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 17:04:34.120 - [任务 2][test] - Query by advance filter
 - loop time: 15000
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 17:10:10.083 - [任务 2][test] - Query by advance filter
 - loop time: 15600
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 17:15:50.097 - [任务 2][test] - Query by advance filter
 - loop time: 16200
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 17:21:36.062 - [任务 2][test] - Query by advance filter
 - loop time: 16800
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 17:28:06.918 - [任务 2][test] - Query by advance filter
 - loop time: 17400
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 17:34:30.134 - [任务 2][test] - Query by advance filter
 - loop time: 18000
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 17:42:57.282 - [任务 2][test] - Query by advance filter
 - loop time: 18600
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 17:50:41.047 - [任务 2][test] - Query by advance filter
 - loop time: 19200
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 17:56:24.839 - [任务 2][test] - Query by advance filter
 - loop time: 19800
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 18:02:21.994 - [任务 2][test] - Query by advance filter
 - loop time: 20400
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 18:08:33.682 - [任务 2][test] - Query by advance filter
 - loop time: 21000
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 18:14:37.070 - [任务 2][test] - Query by advance filter
 - loop time: 21600
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 18:20:21.729 - [任务 2][test] - Query by advance filter
 - loop time: 22200
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 18:26:14.793 - [任务 2][test] - Query by advance filter
 - loop time: 22800
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 18:31:59.438 - [任务 2][test] - Query by advance filter
 - loop time: 23400
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 18:37:46.205 - [任务 2][test] - Query by advance filter
 - loop time: 24000
 - table: test
 - filter: [created>'2024-06-06 16:46:43.000000']
 - limit: 1000
 - sort: [created ASC] 
[INFO ] 2024-05-14 18:41:08.633 - [任务 2] - Stop task milestones: 6641ae8c82d69a6df4124406(任务 2)  
[INFO ] 2024-05-14 18:41:09.002 - [任务 2][test] - Node test[ed84b84c-4267-4ebc-ba4a-2a34f77cbd75] running status set to false 
[INFO ] 2024-05-14 18:41:09.017 - [任务 2][test] - PDK connector node stopped: HazelcastSourcePdkDataNode-ed84b84c-4267-4ebc-ba4a-2a34f77cbd75 
[INFO ] 2024-05-14 18:41:09.017 - [任务 2][test] - PDK connector node released: HazelcastSourcePdkDataNode-ed84b84c-4267-4ebc-ba4a-2a34f77cbd75 
[INFO ] 2024-05-14 18:41:09.017 - [任务 2][test] - Node test[ed84b84c-4267-4ebc-ba4a-2a34f77cbd75] schema data cleaned 
[INFO ] 2024-05-14 18:41:09.017 - [任务 2][test] - Node test[ed84b84c-4267-4ebc-ba4a-2a34f77cbd75] monitor closed 
[INFO ] 2024-05-14 18:41:09.018 - [任务 2][test] - Node test[ed84b84c-4267-4ebc-ba4a-2a34f77cbd75] close complete, cost 20 ms 
[INFO ] 2024-05-14 18:41:09.018 - [任务 2][dorisTest1] - Node dorisTest1[c5f39c86-ac4b-4694-b642-d39bbc930835] running status set to false 
[ERROR] 2024-05-14 18:41:09.060 - [任务 2][test] - Query by advance filter failed, table: test, filer: [created>'2024-06-06 16:46:43.000000'], sort: [created ASC], limit: 1000 <-- Full Stack Trace -->
java.lang.RuntimeException: Query by advance filter failed, table: test, filer: [created>'2024-06-06 16:46:43.000000'], sort: [created ASC], limit: 1000
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Query by advance filter failed, table: test, filer: [created>'2024-06-06 16:46:43.000000'], sort: [created ASC], limit: 1000
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doPollingCDC(HazelcastSourcePdkDataNode.java:1030)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:583)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.RuntimeException: java.sql.SQLException: Connection is closed
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:82)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doPollingCDC(HazelcastSourcePdkDataNode.java:979)
	... 8 more
Caused by: java.sql.SQLException: Connection is closed
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:209)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:130)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:155)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doPollingCDC$51(HazelcastSourcePdkDataNode.java:987)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:80)
	... 10 more
Caused by: java.sql.SQLException: Connection is closed
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:190)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:155)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:155)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	... 14 more
Caused by: java.sql.SQLException: Connection is closed
	at com.zaxxer.hikari.pool.ProxyConnection$ClosedConnection.lambda$getClosedConnection$0(ProxyConnection.java:515)
	at com.sun.proxy.$Proxy120.createStatement(Unknown Source)
	at com.zaxxer.hikari.pool.ProxyConnection.createStatement(ProxyConnection.java:301)
	at com.zaxxer.hikari.pool.HikariProxyConnection.createStatement(HikariProxyConnection.java)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:90)
	at io.tapdata.common.CommonDbConnector.queryByAdvanceFilterWithOffset(CommonDbConnector.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$50(HazelcastSourcePdkDataNode.java:1020)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:168)
	... 18 more

[INFO ] 2024-05-14 18:41:09.060 - [任务 2][dorisTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-c5f39c86-ac4b-4694-b642-d39bbc930835 
[INFO ] 2024-05-14 18:41:09.060 - [任务 2][dorisTest1] - PDK connector node released: HazelcastTargetPdkDataNode-c5f39c86-ac4b-4694-b642-d39bbc930835 
[INFO ] 2024-05-14 18:41:09.061 - [任务 2][dorisTest1] - Node dorisTest1[c5f39c86-ac4b-4694-b642-d39bbc930835] schema data cleaned 
[INFO ] 2024-05-14 18:41:09.061 - [任务 2][dorisTest1] - Node dorisTest1[c5f39c86-ac4b-4694-b642-d39bbc930835] monitor closed 
[INFO ] 2024-05-14 18:41:09.061 - [任务 2][dorisTest1] - Node dorisTest1[c5f39c86-ac4b-4694-b642-d39bbc930835] close complete, cost 43 ms 
[INFO ] 2024-05-14 18:41:10.703 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-14 18:41:10.703 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-05-14 18:41:10.703 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-05-14 18:41:10.727 - [任务 2] - Remove memory task client succeed, task: 任务 2[6641ae8c82d69a6df4124406] 
[INFO ] 2024-05-14 18:41:10.934 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[6641ae8c82d69a6df4124406] 
