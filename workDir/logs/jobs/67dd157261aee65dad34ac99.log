[TRACE] 2025-03-21 15:30:36.916 - [任务 14] - Task initialization... 
[TRACE] 2025-03-21 15:30:36.916 - [任务 14] - Start task milestones: 67dd157261aee65dad34ac99(任务 14) 
[INFO ] 2025-03-21 15:30:37.062 - [任务 14] - Loading table structure completed 
[TRACE] 2025-03-21 15:30:37.065 - [任务 14] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-21 15:30:37.115 - [任务 14] - The engine receives 任务 14 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-21 15:30:37.115 - [任务 14] - Task started 
[TRACE] 2025-03-21 15:30:37.133 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] start preload schema,table counts: 1 
[TRACE] 2025-03-21 15:30:37.134 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] start preload schema,table counts: 1 
[TRACE] 2025-03-21 15:30:37.134 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] preload schema finished, cost 0 ms 
[TRACE] 2025-03-21 15:30:37.338 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] preload schema finished, cost 0 ms 
[INFO ] 2025-03-21 15:30:37.872 - [任务 14][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-03-21 15:30:37.879 - [任务 14][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-03-21 15:30:37.879 - [任务 14][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-03-21 15:30:37.880 - [任务 14][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-03-21 15:30:37.880 - [任务 14][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-03-21 15:30:38.149 - [任务 14][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-03-21 15:30:38.152 - [任务 14][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-03-21 15:30:38.176 - [任务 14][PG] - Sink connector(PG) initialization completed 
[TRACE] 2025-03-21 15:30:38.176 - [任务 14][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-21 15:30:38.176 - [任务 14][PG] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-03-21 15:30:38.313 - [任务 14][PG] - Apply table structure to target database 
[INFO ] 2025-03-21 15:30:38.315 - [任务 14][Sybase] - Starting batch read from 1 tables 
[WARN ] 2025-03-21 15:30:38.334 - [任务 14][PG] - Create index failed ERROR: relation "testTimeStampWithUniqueIndex" already exists, rename testTimeStampWithUniqueIndex to testTimeStampWithUniqueIndex_602a and retry ... 
[TRACE] 2025-03-21 15:30:38.336 - [任务 14][Sybase] - Initial sync started 
[INFO ] 2025-03-21 15:30:38.337 - [任务 14][Sybase] - Starting batch read from table: testNullLastW 
[TRACE] 2025-03-21 15:30:38.337 - [任务 14][Sybase] - Table testNullLastW is going to be initial synced 
[TRACE] 2025-03-21 15:30:38.553 - [任务 14][Sybase] - Query snapshot row size completed: Sybase(a6c916c8-e5b8-4d11-8d3e-e24c99823953) 
[INFO ] 2025-03-21 15:30:38.559 - [任务 14][Sybase] - Table testNullLastW has been completed batch read 
[TRACE] 2025-03-21 15:30:38.559 - [任务 14][Sybase] - Initial sync completed 
[INFO ] 2025-03-21 15:30:38.559 - [任务 14][Sybase] - Batch read completed. 
[TRACE] 2025-03-21 15:30:38.559 - [任务 14][Sybase] - Incremental sync starting... 
[TRACE] 2025-03-21 15:30:38.559 - [任务 14][Sybase] - Initial sync completed 
[TRACE] 2025-03-21 15:30:38.560 - [任务 14][Sybase] - Starting stream read, table list: [testNullLastW], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-03-21 15:30:38.560 - [任务 14][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-03-21 15:30:38.659 - [任务 14][Sybase] - startRid: 368667, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-21 15:30:38.659 - [任务 14][Sybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-21 15:30:38.659 - [任务 14][Sybase] - sybase offset in database is: startRid: 368667, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-03-21 15:30:38.660 - [任务 14][Sybase] - we will use offset in database, how ever, this is safe: startRid: 368667, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-21 15:30:38.862 - [任务 14][Sybase] - sybase cdc work with mode v1: auto rescan 
[INFO ] 2025-03-21 15:30:38.967 - [任务 14][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-03-21 15:30:39.031 - [任务 14][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-03-21 15:30:39.032 - [任务 14][Sybase] - opened cdc for tables: {dbo=[testNullLastW]} 
[INFO ] 2025-03-21 15:30:39.169 - [任务 14][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-03-21 15:30:39.170 - [任务 14][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-03-21 15:30:39.481 - [任务 14][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-03-21 15:30:39.482 - [任务 14][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-03-21 15:30:39.482 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[TRACE] 2025-03-21 15:30:40.248 - [任务 14][PG] - Process after table "testNullLastW" initial sync finished, cost: 1 ms 
[INFO ] 2025-03-21 15:30:40.250 - [任务 14][PG] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-03-21 15:30:42.692 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:30:42.904 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:30:45.821 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:30:46.022 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:30:48.910 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:30:49.091 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:30:52.292 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:30:52.701 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:30:55.565 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:30:55.745 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:30:58.750 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:30:58.929 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:31:01.975 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:31:02.178 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:31:05.161 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:31:05.366 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:31:08.412 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:31:08.619 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:31:11.580 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:31:11.787 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:31:14.737 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:31:14.738 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:31:17.744 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:31:17.896 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:31:20.904 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:31:21.070 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:31:24.075 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:31:24.281 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:31:27.273 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:31:27.414 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:31:34.373 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:31:45.570 - [任务 14][Sybase] - uncommit trans size: 0 
[INFO ] 2025-03-21 15:31:45.576 - [任务 14][Sybase] - uncommit trans: {} 
[INFO ] 2025-03-21 15:31:45.576 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:45.576 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:45.576 - [任务 14][Sybase] - column: 3, value: 0 
[INFO ] 2025-03-21 15:31:45.576 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:45.576 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:45.576 - [任务 14][Sybase] - column: 6, value: 2 
[INFO ] 2025-03-21 15:31:45.576 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:45.576 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:45.577 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:17.633+0800 
[INFO ] 2025-03-21 15:31:45.577 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:45.577 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:45.577 - [任务 14][Sybase] - column: 3, value: 3 
[INFO ] 2025-03-21 15:31:45.577 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:17.633+0800 
[INFO ] 2025-03-21 15:31:45.577 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:45.577 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:45.577 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:45.577 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:45.577 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:45.577 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:31:45.577 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:45.577 - [任务 14][Sybase] - column: 3, value: 3 
[INFO ] 2025-03-21 15:31:45.577 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:45.577 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 48, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:45.577 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 56, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 8, value: 4 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 3, value: 3 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 6, value: 5 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:17.633+0800 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 3, value: 6 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:18.633+0800 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:45.578 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:45.579 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:45.579 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:45.579 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:45.579 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:31:45.579 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:45.579 - [任务 14][Sybase] - column: 3, value: 6 
[INFO ] 2025-03-21 15:31:45.579 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:45.579 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 56, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:45.579 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 58, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:45.579 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:45.579 - [任务 14][Sybase] - column: 8, value: 7 
[INFO ] 2025-03-21 15:31:45.579 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:45.579 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:45.580 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:45.580 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:45.580 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:45.581 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:45.581 - [任务 14][Sybase] - column: 3, value: 6 
[INFO ] 2025-03-21 15:31:45.581 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:45.581 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:45.581 - [任务 14][Sybase] - column: 6, value: 8 
[INFO ] 2025-03-21 15:31:45.581 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:45.581 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:45.581 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:18.633+0800 
[INFO ] 2025-03-21 15:31:45.581 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:45.581 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:45.581 - [任务 14][Sybase] - column: 3, value: 9 
[INFO ] 2025-03-21 15:31:45.582 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:20.633+0800 
[INFO ] 2025-03-21 15:31:45.582 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:45.582 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:45.583 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:45.583 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:45.583 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:45.768 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:31:45.768 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:45.768 - [任务 14][Sybase] - column: 3, value: 9 
[INFO ] 2025-03-21 15:31:45.768 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 58, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 63, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 8, value: 10 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 3, value: 9 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 6, value: 11 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:20.633+0800 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 3, value: 12 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:21.633+0800 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:45.769 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:45.770 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:45.770 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:31:45.770 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:45.770 - [任务 14][Sybase] - column: 3, value: 12 
[INFO ] 2025-03-21 15:31:45.770 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:45.770 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 63, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:45.770 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 65, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:45.770 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:45.770 - [任务 14][Sybase] - column: 8, value: 13 
[INFO ] 2025-03-21 15:31:45.770 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:45.770 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:45.770 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:45.771 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:46.089 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:46.089 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:46.090 - [任务 14][Sybase] - column: 3, value: 12 
[INFO ] 2025-03-21 15:31:46.090 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:46.090 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:46.090 - [任务 14][Sybase] - column: 6, value: 14 
[INFO ] 2025-03-21 15:31:46.090 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:46.090 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:46.090 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:21.633+0800 
[INFO ] 2025-03-21 15:31:46.091 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:46.091 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:46.091 - [任务 14][Sybase] - column: 3, value: 15 
[INFO ] 2025-03-21 15:31:46.091 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:23.633+0800 
[INFO ] 2025-03-21 15:31:46.091 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:46.091 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:46.092 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:46.092 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:46.092 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:46.092 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:31:46.092 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:46.092 - [任务 14][Sybase] - column: 3, value: 15 
[INFO ] 2025-03-21 15:31:46.092 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:46.092 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 65, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:46.093 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 70, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:46.093 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:46.093 - [任务 14][Sybase] - column: 8, value: 16 
[INFO ] 2025-03-21 15:31:46.093 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:46.093 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:46.093 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:46.093 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:46.093 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:46.094 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:46.094 - [任务 14][Sybase] - column: 3, value: 15 
[INFO ] 2025-03-21 15:31:46.094 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:46.094 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:46.340 - [任务 14][Sybase] - column: 6, value: 17 
[INFO ] 2025-03-21 15:31:46.340 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:46.340 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:46.340 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:23.633+0800 
[INFO ] 2025-03-21 15:31:46.340 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:46.341 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:46.341 - [任务 14][Sybase] - column: 3, value: 18 
[INFO ] 2025-03-21 15:31:46.341 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:24.633+0800 
[INFO ] 2025-03-21 15:31:46.341 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:46.341 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:46.341 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:46.341 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:46.341 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:46.341 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:31:46.341 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:46.342 - [任务 14][Sybase] - column: 3, value: 18 
[INFO ] 2025-03-21 15:31:46.342 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:46.342 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 70, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:46.342 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 72, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:46.342 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:46.342 - [任务 14][Sybase] - column: 8, value: 19 
[INFO ] 2025-03-21 15:31:46.342 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:46.342 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:46.342 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:46.343 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:46.343 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:46.343 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:46.343 - [任务 14][Sybase] - column: 3, value: 18 
[INFO ] 2025-03-21 15:31:46.343 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:46.343 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:46.343 - [任务 14][Sybase] - column: 6, value: 20 
[INFO ] 2025-03-21 15:31:46.343 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:46.343 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:46.343 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:24.633+0800 
[INFO ] 2025-03-21 15:31:46.344 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:46.344 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:46.344 - [任务 14][Sybase] - column: 3, value: 21 
[INFO ] 2025-03-21 15:31:46.868 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:27.633+0800 
[INFO ] 2025-03-21 15:31:46.868 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:46.868 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:46.869 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:46.869 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:46.869 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:46.869 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:31:46.870 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:46.870 - [任务 14][Sybase] - column: 3, value: 21 
[INFO ] 2025-03-21 15:31:46.870 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:46.870 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 72, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:46.870 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 77, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:46.870 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:46.870 - [任务 14][Sybase] - column: 8, value: 22 
[INFO ] 2025-03-21 15:31:46.870 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:46.871 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:46.871 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:46.871 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:46.871 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:46.871 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:46.871 - [任务 14][Sybase] - column: 3, value: 21 
[INFO ] 2025-03-21 15:31:46.872 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:46.872 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:46.872 - [任务 14][Sybase] - column: 6, value: 23 
[INFO ] 2025-03-21 15:31:46.872 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:46.873 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:46.873 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:27.633+0800 
[INFO ] 2025-03-21 15:31:46.874 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:46.874 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:46.874 - [任务 14][Sybase] - column: 3, value: 24 
[INFO ] 2025-03-21 15:31:47.474 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:27.633+0800 
[INFO ] 2025-03-21 15:31:47.474 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:47.474 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:47.474 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:47.474 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:47.474 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:47.475 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:31:47.475 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:47.476 - [任务 14][Sybase] - column: 3, value: 24 
[INFO ] 2025-03-21 15:31:47.476 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:47.476 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 77, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:47.476 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 79, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:47.476 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:47.477 - [任务 14][Sybase] - column: 8, value: 25 
[INFO ] 2025-03-21 15:31:47.477 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:47.477 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:47.477 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:47.477 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:47.477 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:47.477 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:47.478 - [任务 14][Sybase] - column: 3, value: 24 
[INFO ] 2025-03-21 15:31:47.478 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:47.478 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:47.478 - [任务 14][Sybase] - column: 6, value: 26 
[INFO ] 2025-03-21 15:31:47.478 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:47.478 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:47.478 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:27.633+0800 
[INFO ] 2025-03-21 15:31:47.478 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:47.479 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:47.479 - [任务 14][Sybase] - column: 3, value: 27 
[INFO ] 2025-03-21 15:31:47.479 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:29.633+0800 
[INFO ] 2025-03-21 15:31:47.479 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:47.480 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.880 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.880 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.881 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.881 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:31:52.881 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.881 - [任务 14][Sybase] - column: 3, value: 27 
[INFO ] 2025-03-21 15:31:52.881 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.882 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 79, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.882 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 84, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.882 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.882 - [任务 14][Sybase] - column: 8, value: 28 
[INFO ] 2025-03-21 15:31:52.882 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.882 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.882 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.882 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.882 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.882 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.882 - [任务 14][Sybase] - column: 3, value: 27 
[INFO ] 2025-03-21 15:31:52.882 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.884 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.884 - [任务 14][Sybase] - column: 6, value: 29 
[INFO ] 2025-03-21 15:31:52.884 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.884 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.884 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:29.633+0800 
[INFO ] 2025-03-21 15:31:52.884 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.885 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.885 - [任务 14][Sybase] - column: 3, value: 30 
[INFO ] 2025-03-21 15:31:52.885 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:31.633+0800 
[INFO ] 2025-03-21 15:31:52.885 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.885 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.885 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.885 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.885 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.885 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:31:52.885 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.885 - [任务 14][Sybase] - column: 3, value: 30 
[INFO ] 2025-03-21 15:31:52.885 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.886 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 84, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.886 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 86, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.886 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.886 - [任务 14][Sybase] - column: 8, value: 31 
[INFO ] 2025-03-21 15:31:52.886 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.886 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.886 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.886 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.886 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.886 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.886 - [任务 14][Sybase] - column: 3, value: 30 
[INFO ] 2025-03-21 15:31:52.886 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.886 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.886 - [任务 14][Sybase] - column: 6, value: 32 
[INFO ] 2025-03-21 15:31:52.886 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.887 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.887 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:31.633+0800 
[INFO ] 2025-03-21 15:31:52.887 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.887 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.887 - [任务 14][Sybase] - column: 3, value: 33 
[INFO ] 2025-03-21 15:31:52.887 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:32.633+0800 
[INFO ] 2025-03-21 15:31:52.887 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.887 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.887 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.887 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.888 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.888 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:31:52.888 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.888 - [任务 14][Sybase] - column: 3, value: 33 
[INFO ] 2025-03-21 15:31:52.888 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.888 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 86, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.888 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 88, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.888 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.888 - [任务 14][Sybase] - column: 8, value: 34 
[INFO ] 2025-03-21 15:31:52.888 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.888 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.888 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.888 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.888 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.888 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.888 - [任务 14][Sybase] - column: 3, value: 33 
[INFO ] 2025-03-21 15:31:52.895 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.896 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.896 - [任务 14][Sybase] - column: 6, value: 35 
[INFO ] 2025-03-21 15:31:52.896 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.896 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.896 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:32.633+0800 
[INFO ] 2025-03-21 15:31:52.896 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.896 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.896 - [任务 14][Sybase] - column: 3, value: 36 
[INFO ] 2025-03-21 15:31:52.896 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:33.633+0800 
[INFO ] 2025-03-21 15:31:52.896 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.897 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.897 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.897 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.897 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.897 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:31:52.897 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.897 - [任务 14][Sybase] - column: 3, value: 36 
[INFO ] 2025-03-21 15:31:52.897 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.897 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 88, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.897 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 93, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.897 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.897 - [任务 14][Sybase] - column: 8, value: 37 
[INFO ] 2025-03-21 15:31:52.897 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.897 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.898 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.898 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.898 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.898 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.898 - [任务 14][Sybase] - column: 3, value: 36 
[INFO ] 2025-03-21 15:31:52.898 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.898 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.898 - [任务 14][Sybase] - column: 6, value: 38 
[INFO ] 2025-03-21 15:31:52.898 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.898 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.898 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:33.633+0800 
[INFO ] 2025-03-21 15:31:52.898 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.898 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.898 - [任务 14][Sybase] - column: 3, value: 39 
[INFO ] 2025-03-21 15:31:52.899 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:35.610+0800 
[INFO ] 2025-03-21 15:31:52.899 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.899 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.899 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.899 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.899 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.899 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:31:52.899 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.899 - [任务 14][Sybase] - column: 3, value: 39 
[INFO ] 2025-03-21 15:31:52.899 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.899 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 93, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 95, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 8, value: 40 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 3, value: 39 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 6, value: 41 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:35.610+0800 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.900 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 3, value: 42 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:36.610+0800 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 3, value: 42 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 95, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 100, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 8, value: 43 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.901 - [任务 14][Sybase] - column: 3, value: 42 
[INFO ] 2025-03-21 15:31:52.902 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.902 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.902 - [任务 14][Sybase] - column: 6, value: 44 
[INFO ] 2025-03-21 15:31:52.902 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.902 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.902 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:36.610+0800 
[INFO ] 2025-03-21 15:31:52.902 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.902 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.902 - [任务 14][Sybase] - column: 3, value: 45 
[INFO ] 2025-03-21 15:31:52.902 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:39.610+0800 
[INFO ] 2025-03-21 15:31:52.902 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.902 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.902 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.902 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.902 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.903 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:31:52.903 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.903 - [任务 14][Sybase] - column: 3, value: 45 
[INFO ] 2025-03-21 15:31:52.903 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.903 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 100, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.903 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 102, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.903 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.903 - [任务 14][Sybase] - column: 8, value: 46 
[INFO ] 2025-03-21 15:31:52.903 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.903 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.903 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.903 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.903 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.903 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.903 - [任务 14][Sybase] - column: 3, value: 45 
[INFO ] 2025-03-21 15:31:52.903 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.904 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.904 - [任务 14][Sybase] - column: 6, value: 47 
[INFO ] 2025-03-21 15:31:52.904 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.904 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.904 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:39.610+0800 
[INFO ] 2025-03-21 15:31:52.904 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.904 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.904 - [任务 14][Sybase] - column: 3, value: 48 
[INFO ] 2025-03-21 15:31:52.904 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:40.610+0800 
[INFO ] 2025-03-21 15:31:52.904 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.904 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.904 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.904 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.904 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.904 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:31:52.905 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.905 - [任务 14][Sybase] - column: 3, value: 48 
[INFO ] 2025-03-21 15:31:52.905 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.905 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 102, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.905 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 107, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.905 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.905 - [任务 14][Sybase] - column: 8, value: 49 
[INFO ] 2025-03-21 15:31:52.905 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.905 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.905 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.905 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.905 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.905 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.905 - [任务 14][Sybase] - column: 3, value: 48 
[INFO ] 2025-03-21 15:31:52.905 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.906 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.906 - [任务 14][Sybase] - column: 6, value: 50 
[INFO ] 2025-03-21 15:31:52.906 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.906 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.906 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:40.610+0800 
[INFO ] 2025-03-21 15:31:52.906 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.906 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.906 - [任务 14][Sybase] - column: 3, value: 51 
[INFO ] 2025-03-21 15:31:52.906 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:41.610+0800 
[INFO ] 2025-03-21 15:31:52.906 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.906 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.906 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.906 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.906 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.906 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:31:52.906 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.907 - [任务 14][Sybase] - column: 3, value: 51 
[INFO ] 2025-03-21 15:31:52.907 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.907 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 107, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.907 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 109, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.907 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.907 - [任务 14][Sybase] - column: 8, value: 52 
[INFO ] 2025-03-21 15:31:52.907 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.907 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.907 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.907 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.907 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.907 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.907 - [任务 14][Sybase] - column: 3, value: 51 
[INFO ] 2025-03-21 15:31:52.907 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.907 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.907 - [任务 14][Sybase] - column: 6, value: 53 
[INFO ] 2025-03-21 15:31:52.908 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.908 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.908 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:41.610+0800 
[INFO ] 2025-03-21 15:31:52.908 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.908 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.908 - [任务 14][Sybase] - column: 3, value: 54 
[INFO ] 2025-03-21 15:31:52.908 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:43.610+0800 
[INFO ] 2025-03-21 15:31:52.908 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.908 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.908 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.908 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.908 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.908 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:31:52.908 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.908 - [任务 14][Sybase] - column: 3, value: 54 
[INFO ] 2025-03-21 15:31:52.909 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.909 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 109, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.909 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 114, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.909 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.909 - [任务 14][Sybase] - column: 8, value: 55 
[INFO ] 2025-03-21 15:31:52.909 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.909 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.909 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.909 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.909 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.909 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.909 - [任务 14][Sybase] - column: 3, value: 54 
[INFO ] 2025-03-21 15:31:52.910 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.910 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.910 - [任务 14][Sybase] - column: 6, value: 56 
[INFO ] 2025-03-21 15:31:52.910 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.910 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.910 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:43.610+0800 
[INFO ] 2025-03-21 15:31:52.910 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.910 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.910 - [任务 14][Sybase] - column: 3, value: 57 
[INFO ] 2025-03-21 15:31:52.910 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:44.610+0800 
[INFO ] 2025-03-21 15:31:52.911 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.911 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.911 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.911 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.911 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.911 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:31:52.911 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.911 - [任务 14][Sybase] - column: 3, value: 57 
[INFO ] 2025-03-21 15:31:52.911 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.911 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 114, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.911 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 116, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.911 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.911 - [任务 14][Sybase] - column: 8, value: 58 
[INFO ] 2025-03-21 15:31:52.911 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.911 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.911 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.912 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.912 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.912 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.912 - [任务 14][Sybase] - column: 3, value: 57 
[INFO ] 2025-03-21 15:31:52.912 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.912 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.912 - [任务 14][Sybase] - column: 6, value: 59 
[INFO ] 2025-03-21 15:31:52.912 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.912 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.912 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:44.610+0800 
[INFO ] 2025-03-21 15:31:52.912 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.912 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.912 - [任务 14][Sybase] - column: 3, value: 60 
[INFO ] 2025-03-21 15:31:52.912 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:45.610+0800 
[INFO ] 2025-03-21 15:31:52.912 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.912 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.913 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.913 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.913 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.913 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:31:52.913 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.913 - [任务 14][Sybase] - column: 3, value: 60 
[INFO ] 2025-03-21 15:31:52.913 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.913 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 116, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.913 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 121, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.913 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.913 - [任务 14][Sybase] - column: 8, value: 61 
[INFO ] 2025-03-21 15:31:52.913 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.913 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.913 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.913 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.913 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.914 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.914 - [任务 14][Sybase] - column: 3, value: 60 
[INFO ] 2025-03-21 15:31:52.914 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.914 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.914 - [任务 14][Sybase] - column: 6, value: 62 
[INFO ] 2025-03-21 15:31:52.914 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.914 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.914 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:45.610+0800 
[INFO ] 2025-03-21 15:31:52.914 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.914 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.914 - [任务 14][Sybase] - column: 3, value: 63 
[INFO ] 2025-03-21 15:31:52.914 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:48.610+0800 
[INFO ] 2025-03-21 15:31:52.914 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.914 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.914 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.914 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.915 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.915 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:31:52.915 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.915 - [任务 14][Sybase] - column: 3, value: 63 
[INFO ] 2025-03-21 15:31:52.915 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.915 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 121, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.915 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 123, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.915 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.915 - [任务 14][Sybase] - column: 8, value: 64 
[INFO ] 2025-03-21 15:31:52.915 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.915 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.915 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.915 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.915 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.915 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.915 - [任务 14][Sybase] - column: 3, value: 63 
[INFO ] 2025-03-21 15:31:52.916 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.916 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.916 - [任务 14][Sybase] - column: 6, value: 65 
[INFO ] 2025-03-21 15:31:52.916 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.916 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.916 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:48.610+0800 
[INFO ] 2025-03-21 15:31:52.916 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.916 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.916 - [任务 14][Sybase] - column: 3, value: 66 
[INFO ] 2025-03-21 15:31:52.916 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:48.610+0800 
[INFO ] 2025-03-21 15:31:52.916 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.916 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.916 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.916 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.916 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.917 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:31:52.917 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.917 - [任务 14][Sybase] - column: 3, value: 66 
[INFO ] 2025-03-21 15:31:52.917 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.917 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 123, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.917 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, -128, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.917 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.917 - [任务 14][Sybase] - column: 8, value: 67 
[INFO ] 2025-03-21 15:31:52.917 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.917 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.917 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.917 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.917 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.917 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.917 - [任务 14][Sybase] - column: 3, value: 66 
[INFO ] 2025-03-21 15:31:52.917 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.917 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.918 - [任务 14][Sybase] - column: 6, value: 68 
[INFO ] 2025-03-21 15:31:52.918 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.918 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.918 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:48.610+0800 
[INFO ] 2025-03-21 15:31:52.918 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.918 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.918 - [任务 14][Sybase] - column: 3, value: 69 
[INFO ] 2025-03-21 15:31:52.918 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:51.610+0800 
[INFO ] 2025-03-21 15:31:52.918 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.918 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.918 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.918 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.918 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.918 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:31:52.918 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.919 - [任务 14][Sybase] - column: 3, value: 69 
[INFO ] 2025-03-21 15:31:52.919 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.919 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, -128, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.919 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, -126, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.919 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.919 - [任务 14][Sybase] - column: 8, value: 70 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 3, value: 69 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 6, value: 71 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:51.610+0800 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 3, value: 72 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:52.610+0800 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 3, value: 72 
[INFO ] 2025-03-21 15:31:52.920 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.921 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, -126, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.921 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, -121, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.921 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.921 - [任务 14][Sybase] - column: 8, value: 73 
[INFO ] 2025-03-21 15:31:52.921 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.921 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.921 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.921 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.921 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.921 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.921 - [任务 14][Sybase] - column: 3, value: 72 
[INFO ] 2025-03-21 15:31:52.921 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.921 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.922 - [任务 14][Sybase] - column: 6, value: 74 
[INFO ] 2025-03-21 15:31:52.922 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.922 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.922 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:52.610+0800 
[INFO ] 2025-03-21 15:31:52.922 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.922 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.922 - [任务 14][Sybase] - column: 3, value: 75 
[INFO ] 2025-03-21 15:31:52.922 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:53.610+0800 
[INFO ] 2025-03-21 15:31:52.922 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.922 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.922 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.922 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.922 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.923 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:31:52.923 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.923 - [任务 14][Sybase] - column: 3, value: 75 
[INFO ] 2025-03-21 15:31:52.923 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.923 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, -121, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.923 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, -119, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.923 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.923 - [任务 14][Sybase] - column: 8, value: 76 
[INFO ] 2025-03-21 15:31:52.923 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.923 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.923 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.923 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.923 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.923 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.923 - [任务 14][Sybase] - column: 3, value: 75 
[INFO ] 2025-03-21 15:31:52.923 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.924 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.924 - [任务 14][Sybase] - column: 6, value: 77 
[INFO ] 2025-03-21 15:31:52.924 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.924 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.924 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:53.610+0800 
[INFO ] 2025-03-21 15:31:52.924 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.924 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.924 - [任务 14][Sybase] - column: 3, value: 78 
[INFO ] 2025-03-21 15:31:52.924 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:55.610+0800 
[INFO ] 2025-03-21 15:31:52.924 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.924 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.924 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.924 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.924 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.924 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:31:52.925 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.925 - [任务 14][Sybase] - column: 3, value: 78 
[INFO ] 2025-03-21 15:31:52.925 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.925 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, -119, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.925 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, -114, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.925 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.925 - [任务 14][Sybase] - column: 8, value: 79 
[INFO ] 2025-03-21 15:31:52.925 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.925 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.925 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.925 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.925 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.925 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.925 - [任务 14][Sybase] - column: 3, value: 78 
[INFO ] 2025-03-21 15:31:52.925 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.926 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.926 - [任务 14][Sybase] - column: 6, value: 80 
[INFO ] 2025-03-21 15:31:52.926 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.926 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.926 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:55.610+0800 
[INFO ] 2025-03-21 15:31:52.926 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.926 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.926 - [任务 14][Sybase] - column: 3, value: 81 
[INFO ] 2025-03-21 15:31:52.926 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:57.610+0800 
[INFO ] 2025-03-21 15:31:52.926 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.927 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.927 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.927 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.927 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.927 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:31:52.927 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.927 - [任务 14][Sybase] - column: 3, value: 81 
[INFO ] 2025-03-21 15:31:52.927 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.927 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, -114, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.927 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, -112, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.927 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.927 - [任务 14][Sybase] - column: 8, value: 82 
[INFO ] 2025-03-21 15:31:52.927 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.928 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.928 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.928 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.928 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.928 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.928 - [任务 14][Sybase] - column: 3, value: 81 
[INFO ] 2025-03-21 15:31:52.928 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.928 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.928 - [任务 14][Sybase] - column: 6, value: 83 
[INFO ] 2025-03-21 15:31:52.928 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.928 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.928 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:57.610+0800 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 3, value: 84 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:57.610+0800 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 3, value: 84 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, -112, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, -110, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 8, value: 85 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 3, value: 84 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 6, value: 86 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.933 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:57.610+0800 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 3, value: 87 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:09:17.610+0800 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 7, value: _systsflush 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 8, value: null 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 9, value: null 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 3, value: 87 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 6, value: 89 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:09:17.610+0800 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 3, value: 90 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 4, value: 2025-03-21T05:57:19.606+0800 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 7, value: _write_stats 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 8, value: null 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 9, value: null 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 1, value: 72 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 3, value: 90 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 4, value: 96 
[INFO ] 2025-03-21 15:31:52.934 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 6, value: 368667 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 7, value: 91 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 8, value: 0 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 9, value: 11434804 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 1, value: 67 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 3, value: 90 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 4, value: 56 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 5, value: 256 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 6, value: 368667 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 7, value: 93 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 8, value: 0 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 9, value: 11434804 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 1, value: 71 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 3, value: 90 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 4, value: 96 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 6, value: 368667 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 7, value: 95 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 8, value: 0 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 9, value: 11434804 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 3, value: 90 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.935 - [任务 14][Sybase] - column: 6, value: 96 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 9, value: 2025-03-21T05:57:19.606+0800 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 1, value: 17 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 2, value: 8256 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 3, value: 2025-03-21T06:00:27.610+0800 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 4, value: [0, 0, 0, 0, -100, 123, -82, 0] 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 6, value: 0 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 8, value: 0 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 9, value: 368667 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 10, value: 97 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 11, value: 0 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 12, value: 11434804 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 3, value: 98 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 4, value: 2025-03-21T07:26:11.910+0800 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 7, value: _ins 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 8, value: null 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 9, value: null 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 3, value: 98 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, -8, -67, -83, 0] 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 70, -128, -82, 0] 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.936 - [任务 14][Sybase] - column: 8, value: 99 
[INFO ] 2025-03-21 15:31:52.937 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.937 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.937 - [任务 14][Sybase] - column: 11, value: testNullLastW 
[INFO ] 2025-03-21 15:31:52.937 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.937 - [任务 14][Sybase] - column: 1, value: 26 
[INFO ] 2025-03-21 15:31:52.937 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.937 - [任务 14][Sybase] - column: 3, value: 98 
[INFO ] 2025-03-21 15:31:52.937 - [任务 14][Sybase] - column: 4, value: 368667 
[INFO ] 2025-03-21 15:31:52.937 - [任务 14][Sybase] - column: 5, value: 99 
[INFO ] 2025-03-21 15:31:52.937 - [任务 14][Sybase] - column: 6, value: 0 
[INFO ] 2025-03-21 15:31:52.937 - [任务 14][Sybase] - column: 7, value: 11434804 
[INFO ] 2025-03-21 15:31:52.937 - [任务 14][Sybase] - column: 8, value: 368667 
[INFO ] 2025-03-21 15:31:52.938 - [任务 14][Sybase] - column: 9, value: 100 
[INFO ] 2025-03-21 15:31:52.938 - [任务 14][Sybase] - column: 10, value: 0 
[INFO ] 2025-03-21 15:31:52.938 - [任务 14][Sybase] - column: 11, value: 11434804 
[INFO ] 2025-03-21 15:31:52.938 - [任务 14][Sybase] - column: 12, value: 0 
[INFO ] 2025-03-21 15:31:52.938 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.938 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.938 - [任务 14][Sybase] - column: 3, value: 98 
[INFO ] 2025-03-21 15:31:52.938 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.938 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.938 - [任务 14][Sybase] - column: 6, value: 101 
[INFO ] 2025-03-21 15:31:52.938 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.938 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.938 - [任务 14][Sybase] - column: 9, value: 2025-03-21T07:26:11.910+0800 
[INFO ] 2025-03-21 15:31:52.938 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.938 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.939 - [任务 14][Sybase] - column: 3, value: 102 
[INFO ] 2025-03-21 15:31:52.939 - [任务 14][Sybase] - column: 4, value: 2025-03-21T07:26:17.910+0800 
[INFO ] 2025-03-21 15:31:52.939 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.939 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.939 - [任务 14][Sybase] - column: 7, value: _systsflush 
[INFO ] 2025-03-21 15:31:52.939 - [任务 14][Sybase] - column: 8, value: null 
[INFO ] 2025-03-21 15:31:52.939 - [任务 14][Sybase] - column: 9, value: null 
[INFO ] 2025-03-21 15:31:52.939 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.939 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.939 - [任务 14][Sybase] - column: 3, value: 102 
[INFO ] 2025-03-21 15:31:52.939 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.939 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.939 - [任务 14][Sybase] - column: 6, value: 104 
[INFO ] 2025-03-21 15:31:52.939 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.939 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.939 - [任务 14][Sybase] - column: 9, value: 2025-03-21T07:26:17.910+0800 
[INFO ] 2025-03-21 15:31:52.940 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.940 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.940 - [任务 14][Sybase] - column: 3, value: 105 
[INFO ] 2025-03-21 15:31:52.940 - [任务 14][Sybase] - column: 4, value: 2025-03-21T07:26:17.910+0800 
[INFO ] 2025-03-21 15:31:52.940 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.940 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.940 - [任务 14][Sybase] - column: 7, value: _dmpxact 
[INFO ] 2025-03-21 15:31:52.940 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:31:52.940 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:31:52.940 - [任务 14][Sybase] - column: 1, value: 17 
[INFO ] 2025-03-21 15:31:52.940 - [任务 14][Sybase] - column: 2, value: 8202 
[INFO ] 2025-03-21 15:31:52.940 - [任务 14][Sybase] - column: 3, value: 2025-03-21T07:26:17.910+0800 
[INFO ] 2025-03-21 15:31:52.940 - [任务 14][Sybase] - column: 4, value: [0, 0, 0, 0, 84, -128, -82, 0] 
[INFO ] 2025-03-21 15:31:52.940 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.940 - [任务 14][Sybase] - column: 6, value: 105 
[INFO ] 2025-03-21 15:31:52.940 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.941 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.941 - [任务 14][Sybase] - column: 9, value: 368667 
[INFO ] 2025-03-21 15:31:52.941 - [任务 14][Sybase] - column: 10, value: 106 
[INFO ] 2025-03-21 15:31:52.941 - [任务 14][Sybase] - column: 11, value: 0 
[INFO ] 2025-03-21 15:31:52.941 - [任务 14][Sybase] - column: 12, value: 11434804 
[INFO ] 2025-03-21 15:31:52.941 - [任务 14][Sybase] - column: 1, value: 54 
[INFO ] 2025-03-21 15:31:52.941 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.941 - [任务 14][Sybase] - column: 3, value: 105 
[INFO ] 2025-03-21 15:31:52.941 - [任务 14][Sybase] - column: 4, value: 48 
[INFO ] 2025-03-21 15:31:52.941 - [任务 14][Sybase] - column: 5, value: 576 
[INFO ] 2025-03-21 15:31:52.941 - [任务 14][Sybase] - column: 6, value: 368667 
[INFO ] 2025-03-21 15:31:52.941 - [任务 14][Sybase] - column: 7, value: 107 
[INFO ] 2025-03-21 15:31:52.941 - [任务 14][Sybase] - column: 8, value: 0 
[INFO ] 2025-03-21 15:31:52.941 - [任务 14][Sybase] - column: 9, value: 11434804 
[INFO ] 2025-03-21 15:31:52.941 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.941 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.941 - [任务 14][Sybase] - column: 3, value: 105 
[INFO ] 2025-03-21 15:31:52.942 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.942 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.942 - [任务 14][Sybase] - column: 6, value: 115 
[INFO ] 2025-03-21 15:31:52.942 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.942 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.942 - [任务 14][Sybase] - column: 9, value: 2025-03-21T07:26:17.910+0800 
[INFO ] 2025-03-21 15:31:52.942 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.942 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.942 - [任务 14][Sybase] - column: 3, value: 116 
[INFO ] 2025-03-21 15:31:52.942 - [任务 14][Sybase] - column: 4, value: 2025-03-21T07:26:26.910+0800 
[INFO ] 2025-03-21 15:31:52.942 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.942 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.942 - [任务 14][Sybase] - column: 7, value: _ins 
[INFO ] 2025-03-21 15:31:52.942 - [任务 14][Sybase] - column: 8, value: null 
[INFO ] 2025-03-21 15:31:52.942 - [任务 14][Sybase] - column: 9, value: null 
[INFO ] 2025-03-21 15:31:52.943 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:31:52.943 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.943 - [任务 14][Sybase] - column: 3, value: 116 
[INFO ] 2025-03-21 15:31:52.943 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.943 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 72, -128, -82, 0] 
[INFO ] 2025-03-21 15:31:52.943 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 100, -128, -82, 0] 
[INFO ] 2025-03-21 15:31:52.943 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.943 - [任务 14][Sybase] - column: 8, value: 117 
[INFO ] 2025-03-21 15:31:52.943 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.943 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.943 - [任务 14][Sybase] - column: 11, value: testNullLastW 
[INFO ] 2025-03-21 15:31:52.943 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.943 - [任务 14][Sybase] - column: 1, value: 26 
[INFO ] 2025-03-21 15:31:52.943 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.943 - [任务 14][Sybase] - column: 3, value: 116 
[INFO ] 2025-03-21 15:31:52.943 - [任务 14][Sybase] - column: 4, value: 368667 
[INFO ] 2025-03-21 15:31:52.944 - [任务 14][Sybase] - column: 5, value: 117 
[INFO ] 2025-03-21 15:31:52.944 - [任务 14][Sybase] - column: 6, value: 0 
[INFO ] 2025-03-21 15:31:52.944 - [任务 14][Sybase] - column: 7, value: 11434804 
[INFO ] 2025-03-21 15:31:52.944 - [任务 14][Sybase] - column: 8, value: 368667 
[INFO ] 2025-03-21 15:31:52.944 - [任务 14][Sybase] - column: 9, value: 118 
[INFO ] 2025-03-21 15:31:52.944 - [任务 14][Sybase] - column: 10, value: 0 
[INFO ] 2025-03-21 15:31:52.944 - [任务 14][Sybase] - column: 11, value: 11434804 
[INFO ] 2025-03-21 15:31:52.944 - [任务 14][Sybase] - column: 12, value: 0 
[INFO ] 2025-03-21 15:31:52.944 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.944 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.944 - [任务 14][Sybase] - column: 3, value: 116 
[INFO ] 2025-03-21 15:31:52.944 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.944 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.944 - [任务 14][Sybase] - column: 6, value: 119 
[INFO ] 2025-03-21 15:31:52.945 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.945 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.945 - [任务 14][Sybase] - column: 9, value: 2025-03-21T07:26:26.910+0800 
[INFO ] 2025-03-21 15:31:52.945 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.945 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.945 - [任务 14][Sybase] - column: 3, value: 120 
[INFO ] 2025-03-21 15:31:52.945 - [任务 14][Sybase] - column: 4, value: 2025-03-21T07:26:39.906+0800 
[INFO ] 2025-03-21 15:31:52.945 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.945 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.945 - [任务 14][Sybase] - column: 7, value: _ins 
[INFO ] 2025-03-21 15:31:52.945 - [任务 14][Sybase] - column: 8, value: null 
[INFO ] 2025-03-21 15:31:52.945 - [任务 14][Sybase] - column: 9, value: null 
[INFO ] 2025-03-21 15:31:52.945 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:31:52.946 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.946 - [任务 14][Sybase] - column: 3, value: 120 
[INFO ] 2025-03-21 15:31:52.946 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:31:52.946 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 102, -128, -82, 0] 
[INFO ] 2025-03-21 15:31:52.946 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 119, -128, -82, 0] 
[INFO ] 2025-03-21 15:31:52.946 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:31:52.946 - [任务 14][Sybase] - column: 8, value: 121 
[INFO ] 2025-03-21 15:31:52.946 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:31:52.946 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:31:52.946 - [任务 14][Sybase] - column: 11, value: testNullLastW 
[INFO ] 2025-03-21 15:31:52.946 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:31:52.946 - [任务 14][Sybase] - column: 1, value: 26 
[INFO ] 2025-03-21 15:31:52.946 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.946 - [任务 14][Sybase] - column: 3, value: 120 
[INFO ] 2025-03-21 15:31:52.946 - [任务 14][Sybase] - column: 4, value: 368667 
[INFO ] 2025-03-21 15:31:52.947 - [任务 14][Sybase] - column: 5, value: 121 
[INFO ] 2025-03-21 15:31:52.947 - [任务 14][Sybase] - column: 6, value: 0 
[INFO ] 2025-03-21 15:31:52.947 - [任务 14][Sybase] - column: 7, value: 11434804 
[INFO ] 2025-03-21 15:31:52.947 - [任务 14][Sybase] - column: 8, value: 368667 
[INFO ] 2025-03-21 15:31:52.947 - [任务 14][Sybase] - column: 9, value: 122 
[INFO ] 2025-03-21 15:31:52.947 - [任务 14][Sybase] - column: 10, value: 0 
[INFO ] 2025-03-21 15:31:52.947 - [任务 14][Sybase] - column: 11, value: 11434804 
[INFO ] 2025-03-21 15:31:52.947 - [任务 14][Sybase] - column: 12, value: 0 
[INFO ] 2025-03-21 15:31:52.947 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.947 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.947 - [任务 14][Sybase] - column: 3, value: 120 
[INFO ] 2025-03-21 15:31:52.947 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.947 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.947 - [任务 14][Sybase] - column: 6, value: 123 
[INFO ] 2025-03-21 15:31:52.947 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.947 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.948 - [任务 14][Sybase] - column: 9, value: 2025-03-21T07:26:39.906+0800 
[INFO ] 2025-03-21 15:31:52.948 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:31:52.948 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.948 - [任务 14][Sybase] - column: 3, value: 124 
[INFO ] 2025-03-21 15:31:52.948 - [任务 14][Sybase] - column: 4, value: 2025-03-21T07:27:17.906+0800 
[INFO ] 2025-03-21 15:31:52.948 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:31:52.948 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:31:52.948 - [任务 14][Sybase] - column: 7, value: _systsflush 
[INFO ] 2025-03-21 15:31:52.948 - [任务 14][Sybase] - column: 8, value: null 
[INFO ] 2025-03-21 15:31:52.948 - [任务 14][Sybase] - column: 9, value: null 
[INFO ] 2025-03-21 15:31:52.949 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:31:52.949 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:31:52.949 - [任务 14][Sybase] - column: 3, value: 124 
[INFO ] 2025-03-21 15:31:52.949 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:31:52.949 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:31:52.949 - [任务 14][Sybase] - column: 6, value: 126 
[INFO ] 2025-03-21 15:31:52.949 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:31:52.949 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:31:52.950 - [任务 14][Sybase] - column: 9, value: 2025-03-21T07:27:17.906+0800 
[INFO ] 2025-03-21 15:31:52.950 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:31:55.957 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:31:56.162 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:31:59.164 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:31:59.441 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:02.443 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:02.647 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:05.613 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:05.941 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:08.943 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:09.206 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:12.209 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:12.416 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:15.355 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:15.541 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:18.547 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:18.693 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:21.699 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:21.839 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:24.845 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:25.049 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:28.057 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:28.389 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:31.393 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:31.805 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:34.773 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:34.976 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:37.967 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:37.967 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:41.017 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:41.178 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:44.184 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:44.365 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:47.369 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:47.560 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:50.566 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:50.699 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:53.701 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:53.896 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:32:57.035 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:32:57.237 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:00.193 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:00.193 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:03.197 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:03.356 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:06.382 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:06.683 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:09.685 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:09.944 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:12.949 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:13.180 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:16.186 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:16.313 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:19.318 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:19.483 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:22.487 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:22.608 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:25.650 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:25.794 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:28.778 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:29.138 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:33.832 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:37.696 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:40.433 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:40.635 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:43.656 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:43.861 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:46.765 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:46.935 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:50.097 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:50.295 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:53.456 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:53.659 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:56.608 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:56.813 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:33:59.765 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:33:59.971 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:34:02.917 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:34:03.123 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:34:06.026 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:34:06.438 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:34:09.364 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:34:09.773 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:34:12.641 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:34:13.049 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:34:15.940 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:34:16.349 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:34:19.230 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:34:19.433 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:34:22.379 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:34:22.584 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:34:25.534 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:34:25.862 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:34:28.869 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:34:29.277 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:34:32.213 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:34:32.620 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:34:35.542 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:34:35.950 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:34:38.923 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:34:39.079 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:34:42.246 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:34:42.422 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:34:45.596 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:34:45.845 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[TRACE] 2025-03-21 15:34:45.847 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] running status set to false 
[INFO ] 2025-03-21 15:34:45.847 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:34:46.047 - [任务 14][Sybase] - Log Miner is shutting down... 
[TRACE] 2025-03-21 15:34:46.163 - [任务 14][Sybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_a6c916c8-e5b8-4d11-8d3e-e24c99823953_1742542237694 
[TRACE] 2025-03-21 15:34:46.170 - [任务 14][Sybase] - PDK connector node released: HazelcastSourcePdkDataNode_a6c916c8-e5b8-4d11-8d3e-e24c99823953_1742542237694 
[TRACE] 2025-03-21 15:34:46.171 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] schema data cleaned 
[TRACE] 2025-03-21 15:34:46.179 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] monitor closed 
[TRACE] 2025-03-21 15:34:46.180 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] close complete, cost 334 ms 
[TRACE] 2025-03-21 15:34:46.218 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] running status set to false 
[TRACE] 2025-03-21 15:34:46.219 - [任务 14][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode_640451ea-6772-4adb-a89d-802ef648efbe_1742542237831 
[TRACE] 2025-03-21 15:34:46.220 - [任务 14][PG] - PDK connector node released: HazelcastTargetPdkDataNode_640451ea-6772-4adb-a89d-802ef648efbe_1742542237831 
[TRACE] 2025-03-21 15:34:46.220 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] schema data cleaned 
[TRACE] 2025-03-21 15:34:46.221 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] monitor closed 
[TRACE] 2025-03-21 15:34:46.222 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] close complete, cost 42 ms 
[TRACE] 2025-03-21 15:34:46.426 - [任务 14][Sybase] - Incremental sync completed 
[TRACE] 2025-03-21 15:34:49.439 - [任务 14] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-21 15:34:49.440 - [任务 14] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@28646562 
[TRACE] 2025-03-21 15:34:49.606 - [任务 14] - Stop task milestones: 67dd157261aee65dad34ac99(任务 14)  
[TRACE] 2025-03-21 15:34:49.607 - [任务 14] - Stopped task aspect(s) 
[TRACE] 2025-03-21 15:34:49.608 - [任务 14] - Snapshot order controller have been removed 
[INFO ] 2025-03-21 15:34:49.611 - [任务 14] - Task stopped. 
[TRACE] 2025-03-21 15:34:56.575 - [任务 14] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-21 15:34:56.575 - [任务 14] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@28646562 
[TRACE] 2025-03-21 15:34:56.576 - [任务 14] - Stopped task aspect(s) 
[INFO ] 2025-03-21 15:34:56.576 - [任务 14] - Task stopped. 
[TRACE] 2025-03-21 15:34:56.732 - [任务 14] - Remove memory task client succeed, task: 任务 14[67dd157261aee65dad34ac99] 
[TRACE] 2025-03-21 15:34:56.733 - [任务 14] - Destroy memory task client cache succeed, task: 任务 14[67dd157261aee65dad34ac99] 
[TRACE] 2025-03-21 15:35:57.176 - [任务 14] - Task initialization... 
[TRACE] 2025-03-21 15:35:57.178 - [任务 14] - Start task milestones: 67dd157261aee65dad34ac99(任务 14) 
[INFO ] 2025-03-21 15:35:57.597 - [任务 14] - Loading table structure completed 
[TRACE] 2025-03-21 15:35:57.674 - [任务 14] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-21 15:35:57.674 - [任务 14] - The engine receives 任务 14 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-21 15:35:57.810 - [任务 14] - Task started 
[TRACE] 2025-03-21 15:35:57.815 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] start preload schema,table counts: 1 
[TRACE] 2025-03-21 15:35:57.815 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] start preload schema,table counts: 1 
[TRACE] 2025-03-21 15:35:57.815 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] preload schema finished, cost 0 ms 
[TRACE] 2025-03-21 15:35:57.816 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] preload schema finished, cost 1 ms 
[INFO ] 2025-03-21 15:35:58.675 - [任务 14][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-03-21 15:35:58.677 - [任务 14][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-03-21 15:35:58.678 - [任务 14][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-03-21 15:35:58.678 - [任务 14][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-03-21 15:35:58.678 - [任务 14][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-03-21 15:35:58.785 - [任务 14][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-03-21 15:35:58.824 - [任务 14][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-03-21 15:35:58.827 - [任务 14][PG] - Sink connector(PG) initialization completed 
[TRACE] 2025-03-21 15:35:58.827 - [任务 14][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-21 15:35:58.827 - [任务 14][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-21 15:35:58.853 - [任务 14][PG] - Apply table structure to target database 
[TRACE] 2025-03-21 15:35:58.855 - [任务 14][PG] - The table testNullLastW has already exist. 
[INFO ] 2025-03-21 15:35:58.969 - [任务 14][Sybase] - Starting batch read from 1 tables 
[TRACE] 2025-03-21 15:35:58.973 - [任务 14][Sybase] - Initial sync started 
[INFO ] 2025-03-21 15:35:58.973 - [任务 14][Sybase] - Starting batch read from table: testNullLastW 
[TRACE] 2025-03-21 15:35:58.973 - [任务 14][Sybase] - Table testNullLastW is going to be initial synced 
[TRACE] 2025-03-21 15:35:59.179 - [任务 14][PG] - Table: testNullLastW already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; TapIndexField name name fieldAsc true indexType null; TapIndexField name name2 fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-21 15:35:59.211 - [任务 14][Sybase] - Query snapshot row size completed: Sybase(a6c916c8-e5b8-4d11-8d3e-e24c99823953) 
[INFO ] 2025-03-21 15:35:59.220 - [任务 14][Sybase] - Table testNullLastW has been completed batch read 
[TRACE] 2025-03-21 15:35:59.220 - [任务 14][Sybase] - Initial sync completed 
[INFO ] 2025-03-21 15:35:59.220 - [任务 14][Sybase] - Batch read completed. 
[TRACE] 2025-03-21 15:35:59.221 - [任务 14][Sybase] - Incremental sync starting... 
[TRACE] 2025-03-21 15:35:59.221 - [任务 14][Sybase] - Initial sync completed 
[TRACE] 2025-03-21 15:35:59.222 - [任务 14][Sybase] - Starting stream read, table list: [testNullLastW], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-03-21 15:35:59.222 - [任务 14][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-03-21 15:35:59.359 - [任务 14][Sybase] - startRid: 368667, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-21 15:35:59.359 - [任务 14][Sybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-21 15:35:59.359 - [任务 14][Sybase] - sybase offset in database is: startRid: 368667, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-03-21 15:35:59.359 - [任务 14][Sybase] - we will use offset in database, how ever, this is safe: startRid: 368667, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-21 15:35:59.459 - [任务 14][Sybase] - sybase cdc work with mode v1: auto rescan 
[INFO ] 2025-03-21 15:35:59.787 - [任务 14][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-03-21 15:35:59.790 - [任务 14][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-03-21 15:35:59.805 - [任务 14][Sybase] - opened cdc for tables: {dbo=[testNullLastW]} 
[INFO ] 2025-03-21 15:35:59.806 - [任务 14][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[TRACE] 2025-03-21 15:35:59.885 - [任务 14][PG] - Table 'testNullLastW' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-03-21 15:35:59.885 - [任务 14][PG] - Process after table "testNullLastW" initial sync finished, cost: 0 ms 
[INFO ] 2025-03-21 15:35:59.939 - [任务 14][PG] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-03-21 15:35:59.939 - [任务 14][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-03-21 15:36:00.368 - [任务 14][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-03-21 15:36:00.371 - [任务 14][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-03-21 15:36:00.371 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:36:03.374 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:03.489 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:36:06.607 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:06.608 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:36:09.791 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:09.829 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:36:12.997 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:12.998 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:36:16.003 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:16.413 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:36:19.434 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:19.435 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:36:22.491 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:22.694 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:36:25.590 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:25.792 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:36:28.784 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:28.986 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:36:31.973 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:32.379 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:36:35.318 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:35.449 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:36:38.637 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:38.638 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:36:41.744 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:41.879 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:36:44.884 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:45.304 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:36:48.132 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:48.277 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:36:51.283 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:51.478 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:36:54.672 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:54.729 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[TRACE] 2025-03-21 15:36:54.843 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] running status set to false 
[INFO ] 2025-03-21 15:36:54.844 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:36:55.060 - [任务 14][Sybase] - Log Miner is shutting down... 
[TRACE] 2025-03-21 15:36:55.098 - [任务 14][Sybase] - Incremental sync completed 
[TRACE] 2025-03-21 15:36:55.101 - [任务 14][Sybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_a6c916c8-e5b8-4d11-8d3e-e24c99823953_1742542558530 
[TRACE] 2025-03-21 15:36:55.101 - [任务 14][Sybase] - PDK connector node released: HazelcastSourcePdkDataNode_a6c916c8-e5b8-4d11-8d3e-e24c99823953_1742542558530 
[TRACE] 2025-03-21 15:36:55.102 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] schema data cleaned 
[TRACE] 2025-03-21 15:36:55.103 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] monitor closed 
[TRACE] 2025-03-21 15:36:55.122 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] close complete, cost 272 ms 
[TRACE] 2025-03-21 15:36:55.122 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] running status set to false 
[TRACE] 2025-03-21 15:36:55.175 - [任务 14][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode_640451ea-6772-4adb-a89d-802ef648efbe_1742542558625 
[TRACE] 2025-03-21 15:36:55.175 - [任务 14][PG] - PDK connector node released: HazelcastTargetPdkDataNode_640451ea-6772-4adb-a89d-802ef648efbe_1742542558625 
[TRACE] 2025-03-21 15:36:55.175 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] schema data cleaned 
[TRACE] 2025-03-21 15:36:55.175 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] monitor closed 
[TRACE] 2025-03-21 15:36:55.175 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] close complete, cost 56 ms 
[TRACE] 2025-03-21 15:36:56.882 - [任务 14] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-21 15:36:56.882 - [任务 14] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7ec264dd 
[TRACE] 2025-03-21 15:36:57.010 - [任务 14] - Stop task milestones: 67dd157261aee65dad34ac99(任务 14)  
[TRACE] 2025-03-21 15:36:57.010 - [任务 14] - Stopped task aspect(s) 
[TRACE] 2025-03-21 15:36:57.010 - [任务 14] - Snapshot order controller have been removed 
[INFO ] 2025-03-21 15:36:57.011 - [任务 14] - Task stopped. 
[TRACE] 2025-03-21 15:37:02.563 - [任务 14] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-21 15:37:02.564 - [任务 14] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7ec264dd 
[TRACE] 2025-03-21 15:37:02.565 - [任务 14] - Stopped task aspect(s) 
[INFO ] 2025-03-21 15:37:02.566 - [任务 14] - Task stopped. 
[TRACE] 2025-03-21 15:37:02.620 - [任务 14] - Remove memory task client succeed, task: 任务 14[67dd157261aee65dad34ac99] 
[TRACE] 2025-03-21 15:37:02.621 - [任务 14] - Destroy memory task client cache succeed, task: 任务 14[67dd157261aee65dad34ac99] 
[TRACE] 2025-03-21 15:37:25.079 - [任务 14] - Task initialization... 
[TRACE] 2025-03-21 15:37:25.102 - [任务 14] - Start task milestones: 67dd157261aee65dad34ac99(任务 14) 
[INFO ] 2025-03-21 15:37:25.306 - [任务 14] - Loading table structure completed 
[TRACE] 2025-03-21 15:37:25.505 - [任务 14] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-21 15:37:25.509 - [任务 14] - The engine receives 任务 14 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-21 15:37:25.604 - [任务 14] - Task started 
[TRACE] 2025-03-21 15:37:25.604 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] start preload schema,table counts: 1 
[TRACE] 2025-03-21 15:37:25.604 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] start preload schema,table counts: 1 
[TRACE] 2025-03-21 15:37:25.606 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] preload schema finished, cost 1 ms 
[TRACE] 2025-03-21 15:37:25.606 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] preload schema finished, cost 1 ms 
[INFO ] 2025-03-21 15:37:26.580 - [任务 14][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-03-21 15:37:26.580 - [任务 14][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-03-21 15:37:26.580 - [任务 14][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-03-21 15:37:26.581 - [任务 14][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-03-21 15:37:26.791 - [任务 14][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-03-21 15:37:26.818 - [任务 14][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-03-21 15:37:26.826 - [任务 14][PG] - Sink connector(PG) initialization completed 
[TRACE] 2025-03-21 15:37:26.826 - [任务 14][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-21 15:37:26.826 - [任务 14][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-21 15:37:26.833 - [任务 14][PG] - Apply table structure to target database 
[INFO ] 2025-03-21 15:37:26.833 - [任务 14][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[TRACE] 2025-03-21 15:37:26.975 - [任务 14][PG] - The table testNullLastW has already exist. 
[INFO ] 2025-03-21 15:37:26.979 - [任务 14][Sybase] - Starting batch read from 1 tables 
[TRACE] 2025-03-21 15:37:27.016 - [任务 14][Sybase] - Initial sync started 
[TRACE] 2025-03-21 15:37:27.017 - [任务 14][PG] - Table: testNullLastW already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; TapIndexField name name fieldAsc true indexType null; TapIndexField name name2 fieldAsc true indexType null; ] and will no longer create index 
[INFO ] 2025-03-21 15:37:27.018 - [任务 14][Sybase] - Starting batch read from table: testNullLastW 
[TRACE] 2025-03-21 15:37:27.228 - [任务 14][Sybase] - Table testNullLastW is going to be initial synced 
[TRACE] 2025-03-21 15:37:27.315 - [任务 14][Sybase] - Query snapshot row size completed: Sybase(a6c916c8-e5b8-4d11-8d3e-e24c99823953) 
[INFO ] 2025-03-21 15:37:27.315 - [任务 14][Sybase] - Table testNullLastW has been completed batch read 
[TRACE] 2025-03-21 15:37:27.316 - [任务 14][Sybase] - Initial sync completed 
[INFO ] 2025-03-21 15:37:27.316 - [任务 14][Sybase] - Batch read completed. 
[TRACE] 2025-03-21 15:37:27.317 - [任务 14][Sybase] - Incremental sync starting... 
[TRACE] 2025-03-21 15:37:27.317 - [任务 14][Sybase] - Initial sync completed 
[TRACE] 2025-03-21 15:37:27.318 - [任务 14][Sybase] - Starting stream read, table list: [testNullLastW], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-03-21 15:37:27.318 - [任务 14][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-03-21 15:37:27.432 - [任务 14][Sybase] - startRid: 368667, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-21 15:37:27.432 - [任务 14][Sybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-21 15:37:27.432 - [任务 14][Sybase] - sybase offset in database is: startRid: 368667, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-03-21 15:37:27.432 - [任务 14][Sybase] - we will use offset in database, how ever, this is safe: startRid: 368667, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-21 15:37:27.648 - [任务 14][Sybase] - sybase cdc work with mode v1: auto rescan 
[INFO ] 2025-03-21 15:37:27.800 - [任务 14][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-03-21 15:37:27.817 - [任务 14][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-03-21 15:37:27.817 - [任务 14][Sybase] - opened cdc for tables: {dbo=[testNullLastW]} 
[INFO ] 2025-03-21 15:37:27.919 - [任务 14][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-03-21 15:37:27.919 - [任务 14][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-03-21 15:37:28.325 - [任务 14][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-03-21 15:37:28.326 - [任务 14][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-03-21 15:37:28.439 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[TRACE] 2025-03-21 15:37:28.440 - [任务 14][PG] - Table 'testNullLastW' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-03-21 15:37:28.653 - [任务 14][PG] - Process after table "testNullLastW" initial sync finished, cost: 0 ms 
[INFO ] 2025-03-21 15:37:28.656 - [任务 14][PG] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-03-21 15:37:31.389 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:37:31.594 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:37:34.641 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:37:34.852 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:37:37.764 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:37:37.978 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:37:40.982 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:37:41.194 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:37:44.070 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:37:44.485 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:37:47.326 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:37:47.522 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:37:50.628 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:37:50.629 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:37:53.762 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:37:53.763 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:37:56.766 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:37:57.127 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:38:00.132 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:38:00.484 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:38:03.708 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:38:03.727 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:38:06.814 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:38:06.949 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:38:09.982 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:38:10.391 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:38:13.276 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:38:13.683 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:38:16.610 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:38:16.814 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:38:19.790 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:38:20.013 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:38:22.941 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:38:23.144 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:38:26.127 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:38:26.127 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 0 
[INFO ] 2025-03-21 15:38:29.200 - [任务 14][Sybase] - rebuild statement with 368667, 0 
[INFO ] 2025-03-21 15:38:29.346 - [任务 14][Sybase] - uncommit trans size: 0 
[INFO ] 2025-03-21 15:38:29.347 - [任务 14][Sybase] - uncommit trans: {} 
[INFO ] 2025-03-21 15:38:29.347 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.350 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.350 - [任务 14][Sybase] - column: 3, value: 0 
[INFO ] 2025-03-21 15:38:29.353 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.353 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.353 - [任务 14][Sybase] - column: 6, value: 2 
[INFO ] 2025-03-21 15:38:29.353 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.353 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.357 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:17.633+0800 
[INFO ] 2025-03-21 15:38:29.357 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.357 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.357 - [任务 14][Sybase] - column: 3, value: 3 
[INFO ] 2025-03-21 15:38:29.357 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:17.633+0800 
[INFO ] 2025-03-21 15:38:29.358 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.358 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.358 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.358 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.358 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.360 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:38:29.360 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.361 - [任务 14][Sybase] - column: 3, value: 3 
[INFO ] 2025-03-21 15:38:29.361 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.361 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 48, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.361 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 56, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.362 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.369 - [任务 14][Sybase] - column: 8, value: 4 
[INFO ] 2025-03-21 15:38:29.369 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.369 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.369 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.370 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.370 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.370 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.371 - [任务 14][Sybase] - column: 3, value: 3 
[INFO ] 2025-03-21 15:38:29.371 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.372 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.372 - [任务 14][Sybase] - column: 6, value: 5 
[INFO ] 2025-03-21 15:38:29.372 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.374 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.374 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:17.633+0800 
[INFO ] 2025-03-21 15:38:29.375 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.378 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.378 - [任务 14][Sybase] - column: 3, value: 6 
[INFO ] 2025-03-21 15:38:29.379 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:18.633+0800 
[INFO ] 2025-03-21 15:38:29.379 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.379 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.379 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.379 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.379 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.393 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:38:29.397 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.423 - [任务 14][Sybase] - column: 3, value: 6 
[INFO ] 2025-03-21 15:38:29.429 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.439 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 56, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.443 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 58, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.444 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.445 - [任务 14][Sybase] - column: 8, value: 7 
[INFO ] 2025-03-21 15:38:29.445 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.445 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.445 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.446 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.446 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.447 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.447 - [任务 14][Sybase] - column: 3, value: 6 
[INFO ] 2025-03-21 15:38:29.447 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.447 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.447 - [任务 14][Sybase] - column: 6, value: 8 
[INFO ] 2025-03-21 15:38:29.447 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.448 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.448 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:18.633+0800 
[INFO ] 2025-03-21 15:38:29.448 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.449 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.449 - [任务 14][Sybase] - column: 3, value: 9 
[INFO ] 2025-03-21 15:38:29.449 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:20.633+0800 
[INFO ] 2025-03-21 15:38:29.449 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.449 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.450 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.450 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.450 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.450 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:38:29.451 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.452 - [任务 14][Sybase] - column: 3, value: 9 
[INFO ] 2025-03-21 15:38:29.452 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.452 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 58, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.452 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 63, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.452 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.452 - [任务 14][Sybase] - column: 8, value: 10 
[INFO ] 2025-03-21 15:38:29.452 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.452 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.452 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.452 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.453 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.454 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.454 - [任务 14][Sybase] - column: 3, value: 9 
[INFO ] 2025-03-21 15:38:29.454 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.454 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.454 - [任务 14][Sybase] - column: 6, value: 11 
[INFO ] 2025-03-21 15:38:29.455 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.455 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.455 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:20.633+0800 
[INFO ] 2025-03-21 15:38:29.455 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.455 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.455 - [任务 14][Sybase] - column: 3, value: 12 
[INFO ] 2025-03-21 15:38:29.455 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:21.633+0800 
[INFO ] 2025-03-21 15:38:29.456 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.456 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.456 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.456 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.457 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.457 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:38:29.457 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.457 - [任务 14][Sybase] - column: 3, value: 12 
[INFO ] 2025-03-21 15:38:29.457 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.457 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 63, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.458 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 65, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.464 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.465 - [任务 14][Sybase] - column: 8, value: 13 
[INFO ] 2025-03-21 15:38:29.465 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.465 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.465 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.465 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.465 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.465 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.466 - [任务 14][Sybase] - column: 3, value: 12 
[INFO ] 2025-03-21 15:38:29.467 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.467 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.467 - [任务 14][Sybase] - column: 6, value: 14 
[INFO ] 2025-03-21 15:38:29.467 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.467 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.467 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:21.633+0800 
[INFO ] 2025-03-21 15:38:29.467 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.467 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.467 - [任务 14][Sybase] - column: 3, value: 15 
[INFO ] 2025-03-21 15:38:29.467 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:23.633+0800 
[INFO ] 2025-03-21 15:38:29.467 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.467 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.467 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.467 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.467 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.467 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:38:29.467 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 3, value: 15 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 65, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 70, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 8, value: 16 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 3, value: 15 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 6, value: 17 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:23.633+0800 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.468 - [任务 14][Sybase] - column: 3, value: 18 
[INFO ] 2025-03-21 15:38:29.470 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:24.633+0800 
[INFO ] 2025-03-21 15:38:29.470 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.470 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.470 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.470 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.470 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.470 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:38:29.471 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.471 - [任务 14][Sybase] - column: 3, value: 18 
[INFO ] 2025-03-21 15:38:29.472 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.472 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 70, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.472 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 72, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.472 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.474 - [任务 14][Sybase] - column: 8, value: 19 
[INFO ] 2025-03-21 15:38:29.474 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.477 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.477 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.477 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.477 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.478 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.478 - [任务 14][Sybase] - column: 3, value: 18 
[INFO ] 2025-03-21 15:38:29.478 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.478 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.478 - [任务 14][Sybase] - column: 6, value: 20 
[INFO ] 2025-03-21 15:38:29.481 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.481 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.481 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:24.633+0800 
[INFO ] 2025-03-21 15:38:29.481 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.482 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.482 - [任务 14][Sybase] - column: 3, value: 21 
[INFO ] 2025-03-21 15:38:29.482 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:27.633+0800 
[INFO ] 2025-03-21 15:38:29.482 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.482 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.489 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.489 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.489 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.489 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:38:29.489 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.490 - [任务 14][Sybase] - column: 3, value: 21 
[INFO ] 2025-03-21 15:38:29.490 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.490 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 72, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.490 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 77, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.490 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.490 - [任务 14][Sybase] - column: 8, value: 22 
[INFO ] 2025-03-21 15:38:29.490 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.490 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.490 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.490 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.490 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.490 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.490 - [任务 14][Sybase] - column: 3, value: 21 
[INFO ] 2025-03-21 15:38:29.490 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.490 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.490 - [任务 14][Sybase] - column: 6, value: 23 
[INFO ] 2025-03-21 15:38:29.490 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.491 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.491 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:27.633+0800 
[INFO ] 2025-03-21 15:38:29.493 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.493 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.493 - [任务 14][Sybase] - column: 3, value: 24 
[INFO ] 2025-03-21 15:38:29.493 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:27.633+0800 
[INFO ] 2025-03-21 15:38:29.493 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.493 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.493 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.493 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.493 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.493 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:38:29.493 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.493 - [任务 14][Sybase] - column: 3, value: 24 
[INFO ] 2025-03-21 15:38:29.496 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.498 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 77, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.498 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 79, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.498 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.498 - [任务 14][Sybase] - column: 8, value: 25 
[INFO ] 2025-03-21 15:38:29.498 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.498 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.498 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.498 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.503 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.503 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.506 - [任务 14][Sybase] - column: 3, value: 24 
[INFO ] 2025-03-21 15:38:29.506 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 6, value: 26 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:27.633+0800 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 3, value: 27 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:29.633+0800 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 3, value: 27 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.507 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 79, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 84, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 8, value: 28 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 3, value: 27 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 6, value: 29 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:29.633+0800 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 3, value: 30 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:31.633+0800 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.508 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 3, value: 30 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 84, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 86, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 8, value: 31 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 3, value: 30 
[INFO ] 2025-03-21 15:38:29.509 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.510 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.510 - [任务 14][Sybase] - column: 6, value: 32 
[INFO ] 2025-03-21 15:38:29.510 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.510 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.510 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:31.633+0800 
[INFO ] 2025-03-21 15:38:29.511 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.511 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.511 - [任务 14][Sybase] - column: 3, value: 33 
[INFO ] 2025-03-21 15:38:29.511 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:32.633+0800 
[INFO ] 2025-03-21 15:38:29.511 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.511 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.511 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.511 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.513 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.513 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:38:29.513 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.513 - [任务 14][Sybase] - column: 3, value: 33 
[INFO ] 2025-03-21 15:38:29.514 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.514 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 86, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.514 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 88, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.514 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.514 - [任务 14][Sybase] - column: 8, value: 34 
[INFO ] 2025-03-21 15:38:29.514 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.514 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.514 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.514 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.514 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.514 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.514 - [任务 14][Sybase] - column: 3, value: 33 
[INFO ] 2025-03-21 15:38:29.514 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.514 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.514 - [任务 14][Sybase] - column: 6, value: 35 
[INFO ] 2025-03-21 15:38:29.515 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.515 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.515 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:32.633+0800 
[INFO ] 2025-03-21 15:38:29.515 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.515 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.515 - [任务 14][Sybase] - column: 3, value: 36 
[INFO ] 2025-03-21 15:38:29.515 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:33.633+0800 
[INFO ] 2025-03-21 15:38:29.515 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.515 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.515 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.515 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.515 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.515 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:38:29.515 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.515 - [任务 14][Sybase] - column: 3, value: 36 
[INFO ] 2025-03-21 15:38:29.516 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.516 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 88, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.516 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 93, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.516 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.516 - [任务 14][Sybase] - column: 8, value: 37 
[INFO ] 2025-03-21 15:38:29.516 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.516 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.516 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.516 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.516 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.516 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.516 - [任务 14][Sybase] - column: 3, value: 36 
[INFO ] 2025-03-21 15:38:29.516 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.517 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 6, value: 38 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:33.633+0800 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 3, value: 39 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:35.610+0800 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 3, value: 39 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.520 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 93, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.521 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 95, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.521 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.521 - [任务 14][Sybase] - column: 8, value: 40 
[INFO ] 2025-03-21 15:38:29.521 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.521 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.521 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.521 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.521 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.521 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.521 - [任务 14][Sybase] - column: 3, value: 39 
[INFO ] 2025-03-21 15:38:29.521 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.521 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.521 - [任务 14][Sybase] - column: 6, value: 41 
[INFO ] 2025-03-21 15:38:29.521 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.522 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.522 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:35.610+0800 
[INFO ] 2025-03-21 15:38:29.522 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.522 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.522 - [任务 14][Sybase] - column: 3, value: 42 
[INFO ] 2025-03-21 15:38:29.522 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:36.610+0800 
[INFO ] 2025-03-21 15:38:29.522 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.522 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.522 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.522 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.522 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.522 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:38:29.523 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.524 - [任务 14][Sybase] - column: 3, value: 42 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 95, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 100, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 8, value: 43 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 3, value: 42 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 6, value: 44 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:36.610+0800 
[INFO ] 2025-03-21 15:38:29.525 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.526 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.526 - [任务 14][Sybase] - column: 3, value: 45 
[INFO ] 2025-03-21 15:38:29.526 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:39.610+0800 
[INFO ] 2025-03-21 15:38:29.526 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.526 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.526 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.526 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.526 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.529 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:38:29.529 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.529 - [任务 14][Sybase] - column: 3, value: 45 
[INFO ] 2025-03-21 15:38:29.529 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.530 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 100, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.530 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 102, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.530 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.530 - [任务 14][Sybase] - column: 8, value: 46 
[INFO ] 2025-03-21 15:38:29.534 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.534 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.534 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.534 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.534 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.534 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.534 - [任务 14][Sybase] - column: 3, value: 45 
[INFO ] 2025-03-21 15:38:29.534 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.534 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.534 - [任务 14][Sybase] - column: 6, value: 47 
[INFO ] 2025-03-21 15:38:29.534 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.534 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:39.610+0800 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 3, value: 48 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:40.610+0800 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 3, value: 48 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 102, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 107, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 8, value: 49 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.535 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.536 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.536 - [任务 14][Sybase] - column: 3, value: 48 
[INFO ] 2025-03-21 15:38:29.536 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.536 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.536 - [任务 14][Sybase] - column: 6, value: 50 
[INFO ] 2025-03-21 15:38:29.536 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.536 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.536 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:40.610+0800 
[INFO ] 2025-03-21 15:38:29.536 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.536 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.536 - [任务 14][Sybase] - column: 3, value: 51 
[INFO ] 2025-03-21 15:38:29.536 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:41.610+0800 
[INFO ] 2025-03-21 15:38:29.536 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.536 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 3, value: 51 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 107, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 109, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 8, value: 52 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 3, value: 51 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 6, value: 53 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:41.610+0800 
[INFO ] 2025-03-21 15:38:29.537 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 3, value: 54 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:43.610+0800 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 3, value: 54 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 109, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 114, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 8, value: 55 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.538 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.539 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.539 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.539 - [任务 14][Sybase] - column: 3, value: 54 
[INFO ] 2025-03-21 15:38:29.539 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.539 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.539 - [任务 14][Sybase] - column: 6, value: 56 
[INFO ] 2025-03-21 15:38:29.539 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.539 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.539 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:43.610+0800 
[INFO ] 2025-03-21 15:38:29.539 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.539 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.539 - [任务 14][Sybase] - column: 3, value: 57 
[INFO ] 2025-03-21 15:38:29.539 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:44.610+0800 
[INFO ] 2025-03-21 15:38:29.539 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.539 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.539 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.540 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.540 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.540 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:38:29.541 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.541 - [任务 14][Sybase] - column: 3, value: 57 
[INFO ] 2025-03-21 15:38:29.542 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.542 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 114, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.542 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 116, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.542 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.542 - [任务 14][Sybase] - column: 8, value: 58 
[INFO ] 2025-03-21 15:38:29.542 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.542 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.542 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.543 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.543 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.543 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.543 - [任务 14][Sybase] - column: 3, value: 57 
[INFO ] 2025-03-21 15:38:29.543 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.543 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.543 - [任务 14][Sybase] - column: 6, value: 59 
[INFO ] 2025-03-21 15:38:29.543 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.543 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.543 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:44.610+0800 
[INFO ] 2025-03-21 15:38:29.543 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.543 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.544 - [任务 14][Sybase] - column: 3, value: 60 
[INFO ] 2025-03-21 15:38:29.544 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:45.610+0800 
[INFO ] 2025-03-21 15:38:29.544 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.544 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.544 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.544 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.544 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.544 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:38:29.544 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.544 - [任务 14][Sybase] - column: 3, value: 60 
[INFO ] 2025-03-21 15:38:29.544 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.544 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 116, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.544 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 121, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.544 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.544 - [任务 14][Sybase] - column: 8, value: 61 
[INFO ] 2025-03-21 15:38:29.544 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.545 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.545 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.545 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.545 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.545 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.545 - [任务 14][Sybase] - column: 3, value: 60 
[INFO ] 2025-03-21 15:38:29.545 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.545 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.545 - [任务 14][Sybase] - column: 6, value: 62 
[INFO ] 2025-03-21 15:38:29.549 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.549 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.549 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:45.610+0800 
[INFO ] 2025-03-21 15:38:29.549 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.549 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.555 - [任务 14][Sybase] - column: 3, value: 63 
[INFO ] 2025-03-21 15:38:29.555 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:48.610+0800 
[INFO ] 2025-03-21 15:38:29.555 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.555 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.555 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.555 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.555 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.555 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:38:29.555 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.555 - [任务 14][Sybase] - column: 3, value: 63 
[INFO ] 2025-03-21 15:38:29.555 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.555 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 121, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.555 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 123, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 8, value: 64 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 3, value: 63 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 6, value: 65 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:48.610+0800 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 3, value: 66 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:48.610+0800 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:38:29.556 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.557 - [任务 14][Sybase] - column: 3, value: 66 
[INFO ] 2025-03-21 15:38:29.557 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.557 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 123, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.557 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, -128, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.557 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.557 - [任务 14][Sybase] - column: 8, value: 67 
[INFO ] 2025-03-21 15:38:29.557 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.557 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.557 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.557 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.557 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.557 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.557 - [任务 14][Sybase] - column: 3, value: 66 
[INFO ] 2025-03-21 15:38:29.557 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.557 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.557 - [任务 14][Sybase] - column: 6, value: 68 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:48.610+0800 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 3, value: 69 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:51.610+0800 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 3, value: 69 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, -128, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, -126, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.558 - [任务 14][Sybase] - column: 8, value: 70 
[INFO ] 2025-03-21 15:38:29.559 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.559 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.559 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.559 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.559 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.559 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.559 - [任务 14][Sybase] - column: 3, value: 69 
[INFO ] 2025-03-21 15:38:29.559 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.559 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.560 - [任务 14][Sybase] - column: 6, value: 71 
[INFO ] 2025-03-21 15:38:29.560 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.560 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.560 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:51.610+0800 
[INFO ] 2025-03-21 15:38:29.560 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.560 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.560 - [任务 14][Sybase] - column: 3, value: 72 
[INFO ] 2025-03-21 15:38:29.560 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:52.610+0800 
[INFO ] 2025-03-21 15:38:29.560 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.560 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.560 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.560 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.560 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.561 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:38:29.561 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.561 - [任务 14][Sybase] - column: 3, value: 72 
[INFO ] 2025-03-21 15:38:29.561 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.561 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, -126, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.561 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, -121, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.561 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.561 - [任务 14][Sybase] - column: 8, value: 73 
[INFO ] 2025-03-21 15:38:29.561 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.561 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.561 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.561 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.562 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.562 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.562 - [任务 14][Sybase] - column: 3, value: 72 
[INFO ] 2025-03-21 15:38:29.562 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.562 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.562 - [任务 14][Sybase] - column: 6, value: 74 
[INFO ] 2025-03-21 15:38:29.562 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.562 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.562 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:52.610+0800 
[INFO ] 2025-03-21 15:38:29.562 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.562 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.562 - [任务 14][Sybase] - column: 3, value: 75 
[INFO ] 2025-03-21 15:38:29.563 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:53.610+0800 
[INFO ] 2025-03-21 15:38:29.564 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.564 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.564 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.565 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.565 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.565 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:38:29.566 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.570 - [任务 14][Sybase] - column: 3, value: 75 
[INFO ] 2025-03-21 15:38:29.570 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.570 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, -121, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.573 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, -119, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.574 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.574 - [任务 14][Sybase] - column: 8, value: 76 
[INFO ] 2025-03-21 15:38:29.574 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.575 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.575 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.575 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.575 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 3, value: 75 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 6, value: 77 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:53.610+0800 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 3, value: 78 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:55.610+0800 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 3, value: 78 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, -119, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, -114, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.576 - [任务 14][Sybase] - column: 8, value: 79 
[INFO ] 2025-03-21 15:38:29.577 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.577 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.577 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.577 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.577 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.577 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.577 - [任务 14][Sybase] - column: 3, value: 78 
[INFO ] 2025-03-21 15:38:29.577 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.577 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.578 - [任务 14][Sybase] - column: 6, value: 80 
[INFO ] 2025-03-21 15:38:29.578 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.578 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.578 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:55.610+0800 
[INFO ] 2025-03-21 15:38:29.579 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.579 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.579 - [任务 14][Sybase] - column: 3, value: 81 
[INFO ] 2025-03-21 15:38:29.579 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:57.610+0800 
[INFO ] 2025-03-21 15:38:29.580 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.580 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.580 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.580 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.580 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.580 - [任务 14][Sybase] - column: 1, value: 5 
[INFO ] 2025-03-21 15:38:29.580 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.580 - [任务 14][Sybase] - column: 3, value: 81 
[INFO ] 2025-03-21 15:38:29.581 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.581 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, -114, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.581 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, -112, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.581 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.583 - [任务 14][Sybase] - column: 8, value: 82 
[INFO ] 2025-03-21 15:38:29.583 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.583 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.583 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.583 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.583 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.584 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.584 - [任务 14][Sybase] - column: 3, value: 81 
[INFO ] 2025-03-21 15:38:29.584 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.584 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.585 - [任务 14][Sybase] - column: 6, value: 83 
[INFO ] 2025-03-21 15:38:29.586 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.586 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.586 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:57.610+0800 
[INFO ] 2025-03-21 15:38:29.588 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.589 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.589 - [任务 14][Sybase] - column: 3, value: 84 
[INFO ] 2025-03-21 15:38:29.590 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:08:57.610+0800 
[INFO ] 2025-03-21 15:38:29.590 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.590 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.590 - [任务 14][Sybase] - column: 7, value: _chained_transaction 
[INFO ] 2025-03-21 15:38:29.591 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.591 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.592 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:38:29.592 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.592 - [任务 14][Sybase] - column: 3, value: 84 
[INFO ] 2025-03-21 15:38:29.594 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.594 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, -112, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.594 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, -110, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.594 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.594 - [任务 14][Sybase] - column: 8, value: 85 
[INFO ] 2025-03-21 15:38:29.595 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.595 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.595 - [任务 14][Sybase] - column: 11, value: bmsql_config 
[INFO ] 2025-03-21 15:38:29.595 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.595 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.595 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.595 - [任务 14][Sybase] - column: 3, value: 84 
[INFO ] 2025-03-21 15:38:29.595 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.596 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.596 - [任务 14][Sybase] - column: 6, value: 86 
[INFO ] 2025-03-21 15:38:29.596 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.596 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.596 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:08:57.610+0800 
[INFO ] 2025-03-21 15:38:29.596 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.596 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.597 - [任务 14][Sybase] - column: 3, value: 87 
[INFO ] 2025-03-21 15:38:29.597 - [任务 14][Sybase] - column: 4, value: 2025-03-21T04:09:17.610+0800 
[INFO ] 2025-03-21 15:38:29.597 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.597 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.597 - [任务 14][Sybase] - column: 7, value: _systsflush 
[INFO ] 2025-03-21 15:38:29.597 - [任务 14][Sybase] - column: 8, value: null 
[INFO ] 2025-03-21 15:38:29.597 - [任务 14][Sybase] - column: 9, value: null 
[INFO ] 2025-03-21 15:38:29.598 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.598 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.598 - [任务 14][Sybase] - column: 3, value: 87 
[INFO ] 2025-03-21 15:38:29.598 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.598 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.598 - [任务 14][Sybase] - column: 6, value: 89 
[INFO ] 2025-03-21 15:38:29.598 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.598 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.598 - [任务 14][Sybase] - column: 9, value: 2025-03-21T04:09:17.610+0800 
[INFO ] 2025-03-21 15:38:29.599 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.599 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.599 - [任务 14][Sybase] - column: 3, value: 90 
[INFO ] 2025-03-21 15:38:29.599 - [任务 14][Sybase] - column: 4, value: 2025-03-21T05:57:19.606+0800 
[INFO ] 2025-03-21 15:38:29.599 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.599 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.599 - [任务 14][Sybase] - column: 7, value: _write_stats 
[INFO ] 2025-03-21 15:38:29.599 - [任务 14][Sybase] - column: 8, value: null 
[INFO ] 2025-03-21 15:38:29.599 - [任务 14][Sybase] - column: 9, value: null 
[INFO ] 2025-03-21 15:38:29.599 - [任务 14][Sybase] - column: 1, value: 72 
[INFO ] 2025-03-21 15:38:29.599 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.599 - [任务 14][Sybase] - column: 3, value: 90 
[INFO ] 2025-03-21 15:38:29.600 - [任务 14][Sybase] - column: 4, value: 96 
[INFO ] 2025-03-21 15:38:29.600 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.600 - [任务 14][Sybase] - column: 6, value: 368667 
[INFO ] 2025-03-21 15:38:29.600 - [任务 14][Sybase] - column: 7, value: 91 
[INFO ] 2025-03-21 15:38:29.600 - [任务 14][Sybase] - column: 8, value: 0 
[INFO ] 2025-03-21 15:38:29.600 - [任务 14][Sybase] - column: 9, value: 11434804 
[INFO ] 2025-03-21 15:38:29.600 - [任务 14][Sybase] - column: 1, value: 67 
[INFO ] 2025-03-21 15:38:29.600 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.600 - [任务 14][Sybase] - column: 3, value: 90 
[INFO ] 2025-03-21 15:38:29.600 - [任务 14][Sybase] - column: 4, value: 56 
[INFO ] 2025-03-21 15:38:29.600 - [任务 14][Sybase] - column: 5, value: 256 
[INFO ] 2025-03-21 15:38:29.600 - [任务 14][Sybase] - column: 6, value: 368667 
[INFO ] 2025-03-21 15:38:29.600 - [任务 14][Sybase] - column: 7, value: 93 
[INFO ] 2025-03-21 15:38:29.601 - [任务 14][Sybase] - column: 8, value: 0 
[INFO ] 2025-03-21 15:38:29.601 - [任务 14][Sybase] - column: 9, value: 11434804 
[INFO ] 2025-03-21 15:38:29.601 - [任务 14][Sybase] - column: 1, value: 71 
[INFO ] 2025-03-21 15:38:29.601 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.601 - [任务 14][Sybase] - column: 3, value: 90 
[INFO ] 2025-03-21 15:38:29.601 - [任务 14][Sybase] - column: 4, value: 96 
[INFO ] 2025-03-21 15:38:29.601 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.601 - [任务 14][Sybase] - column: 6, value: 368667 
[INFO ] 2025-03-21 15:38:29.601 - [任务 14][Sybase] - column: 7, value: 95 
[INFO ] 2025-03-21 15:38:29.601 - [任务 14][Sybase] - column: 8, value: 0 
[INFO ] 2025-03-21 15:38:29.601 - [任务 14][Sybase] - column: 9, value: 11434804 
[INFO ] 2025-03-21 15:38:29.602 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.602 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.602 - [任务 14][Sybase] - column: 3, value: 90 
[INFO ] 2025-03-21 15:38:29.602 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.602 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.602 - [任务 14][Sybase] - column: 6, value: 96 
[INFO ] 2025-03-21 15:38:29.602 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.602 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.602 - [任务 14][Sybase] - column: 9, value: 2025-03-21T05:57:19.606+0800 
[INFO ] 2025-03-21 15:38:29.602 - [任务 14][Sybase] - column: 1, value: 17 
[INFO ] 2025-03-21 15:38:29.602 - [任务 14][Sybase] - column: 2, value: 8256 
[INFO ] 2025-03-21 15:38:29.603 - [任务 14][Sybase] - column: 3, value: 2025-03-21T06:00:27.610+0800 
[INFO ] 2025-03-21 15:38:29.603 - [任务 14][Sybase] - column: 4, value: [0, 0, 0, 0, -100, 123, -82, 0] 
[INFO ] 2025-03-21 15:38:29.603 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.603 - [任务 14][Sybase] - column: 6, value: 0 
[INFO ] 2025-03-21 15:38:29.603 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.603 - [任务 14][Sybase] - column: 8, value: 0 
[INFO ] 2025-03-21 15:38:29.605 - [任务 14][Sybase] - column: 9, value: 368667 
[INFO ] 2025-03-21 15:38:29.606 - [任务 14][Sybase] - column: 10, value: 97 
[INFO ] 2025-03-21 15:38:29.609 - [任务 14][Sybase] - column: 11, value: 0 
[INFO ] 2025-03-21 15:38:29.610 - [任务 14][Sybase] - column: 12, value: 11434804 
[INFO ] 2025-03-21 15:38:29.610 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.611 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.611 - [任务 14][Sybase] - column: 3, value: 98 
[INFO ] 2025-03-21 15:38:29.611 - [任务 14][Sybase] - column: 4, value: 2025-03-21T07:26:11.910+0800 
[INFO ] 2025-03-21 15:38:29.611 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.611 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.611 - [任务 14][Sybase] - column: 7, value: _ins 
[INFO ] 2025-03-21 15:38:29.611 - [任务 14][Sybase] - column: 8, value: null 
[INFO ] 2025-03-21 15:38:29.611 - [任务 14][Sybase] - column: 9, value: null 
[INFO ] 2025-03-21 15:38:29.611 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:38:29.611 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.611 - [任务 14][Sybase] - column: 3, value: 98 
[INFO ] 2025-03-21 15:38:29.611 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.611 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, -8, -67, -83, 0] 
[INFO ] 2025-03-21 15:38:29.612 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 70, -128, -82, 0] 
[INFO ] 2025-03-21 15:38:29.612 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.612 - [任务 14][Sybase] - column: 8, value: 99 
[INFO ] 2025-03-21 15:38:29.612 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.612 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.612 - [任务 14][Sybase] - column: 11, value: testNullLastW 
[INFO ] 2025-03-21 15:38:29.612 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.615 - [任务 14][Sybase] - column: 1, value: 26 
[INFO ] 2025-03-21 15:38:29.616 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.617 - [任务 14][Sybase] - column: 3, value: 98 
[INFO ] 2025-03-21 15:38:29.617 - [任务 14][Sybase] - column: 4, value: 368667 
[INFO ] 2025-03-21 15:38:29.621 - [任务 14][Sybase] - column: 5, value: 99 
[INFO ] 2025-03-21 15:38:29.621 - [任务 14][Sybase] - column: 6, value: 0 
[INFO ] 2025-03-21 15:38:29.621 - [任务 14][Sybase] - column: 7, value: 11434804 
[INFO ] 2025-03-21 15:38:29.621 - [任务 14][Sybase] - column: 8, value: 368667 
[INFO ] 2025-03-21 15:38:29.621 - [任务 14][Sybase] - column: 9, value: 100 
[INFO ] 2025-03-21 15:38:29.621 - [任务 14][Sybase] - column: 10, value: 0 
[INFO ] 2025-03-21 15:38:29.621 - [任务 14][Sybase] - column: 11, value: 11434804 
[INFO ] 2025-03-21 15:38:29.621 - [任务 14][Sybase] - column: 12, value: 0 
[INFO ] 2025-03-21 15:38:29.622 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.622 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.622 - [任务 14][Sybase] - column: 3, value: 98 
[INFO ] 2025-03-21 15:38:29.622 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.622 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.622 - [任务 14][Sybase] - column: 6, value: 101 
[INFO ] 2025-03-21 15:38:29.622 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.623 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.624 - [任务 14][Sybase] - column: 9, value: 2025-03-21T07:26:11.910+0800 
[INFO ] 2025-03-21 15:38:29.624 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.624 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.624 - [任务 14][Sybase] - column: 3, value: 102 
[INFO ] 2025-03-21 15:38:29.624 - [任务 14][Sybase] - column: 4, value: 2025-03-21T07:26:17.910+0800 
[INFO ] 2025-03-21 15:38:29.625 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.625 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.625 - [任务 14][Sybase] - column: 7, value: _systsflush 
[INFO ] 2025-03-21 15:38:29.625 - [任务 14][Sybase] - column: 8, value: null 
[INFO ] 2025-03-21 15:38:29.626 - [任务 14][Sybase] - column: 9, value: null 
[INFO ] 2025-03-21 15:38:29.627 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.627 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.627 - [任务 14][Sybase] - column: 3, value: 102 
[INFO ] 2025-03-21 15:38:29.627 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.627 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.627 - [任务 14][Sybase] - column: 6, value: 104 
[INFO ] 2025-03-21 15:38:29.627 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.627 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.629 - [任务 14][Sybase] - column: 9, value: 2025-03-21T07:26:17.910+0800 
[INFO ] 2025-03-21 15:38:29.629 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.629 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.629 - [任务 14][Sybase] - column: 3, value: 105 
[INFO ] 2025-03-21 15:38:29.629 - [任务 14][Sybase] - column: 4, value: 2025-03-21T07:26:17.910+0800 
[INFO ] 2025-03-21 15:38:29.629 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.629 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.629 - [任务 14][Sybase] - column: 7, value: _dmpxact 
[INFO ] 2025-03-21 15:38:29.629 - [任务 14][Sybase] - column: 8, value: tester 
[INFO ] 2025-03-21 15:38:29.629 - [任务 14][Sybase] - column: 9, value: guest1234 
[INFO ] 2025-03-21 15:38:29.629 - [任务 14][Sybase] - column: 1, value: 17 
[INFO ] 2025-03-21 15:38:29.630 - [任务 14][Sybase] - column: 2, value: 8202 
[INFO ] 2025-03-21 15:38:29.630 - [任务 14][Sybase] - column: 3, value: 2025-03-21T07:26:17.910+0800 
[INFO ] 2025-03-21 15:38:29.630 - [任务 14][Sybase] - column: 4, value: [0, 0, 0, 0, 84, -128, -82, 0] 
[INFO ] 2025-03-21 15:38:29.630 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.630 - [任务 14][Sybase] - column: 6, value: 105 
[INFO ] 2025-03-21 15:38:29.630 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.630 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.630 - [任务 14][Sybase] - column: 9, value: 368667 
[INFO ] 2025-03-21 15:38:29.630 - [任务 14][Sybase] - column: 10, value: 106 
[INFO ] 2025-03-21 15:38:29.630 - [任务 14][Sybase] - column: 11, value: 0 
[INFO ] 2025-03-21 15:38:29.630 - [任务 14][Sybase] - column: 12, value: 11434804 
[INFO ] 2025-03-21 15:38:29.630 - [任务 14][Sybase] - column: 1, value: 54 
[INFO ] 2025-03-21 15:38:29.630 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.630 - [任务 14][Sybase] - column: 3, value: 105 
[INFO ] 2025-03-21 15:38:29.630 - [任务 14][Sybase] - column: 4, value: 48 
[INFO ] 2025-03-21 15:38:29.631 - [任务 14][Sybase] - column: 5, value: 576 
[INFO ] 2025-03-21 15:38:29.631 - [任务 14][Sybase] - column: 6, value: 368667 
[INFO ] 2025-03-21 15:38:29.631 - [任务 14][Sybase] - column: 7, value: 107 
[INFO ] 2025-03-21 15:38:29.631 - [任务 14][Sybase] - column: 8, value: 0 
[INFO ] 2025-03-21 15:38:29.631 - [任务 14][Sybase] - column: 9, value: 11434804 
[INFO ] 2025-03-21 15:38:29.631 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.631 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.631 - [任务 14][Sybase] - column: 3, value: 105 
[INFO ] 2025-03-21 15:38:29.631 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.631 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.631 - [任务 14][Sybase] - column: 6, value: 115 
[INFO ] 2025-03-21 15:38:29.631 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.631 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.631 - [任务 14][Sybase] - column: 9, value: 2025-03-21T07:26:17.910+0800 
[INFO ] 2025-03-21 15:38:29.631 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.632 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.632 - [任务 14][Sybase] - column: 3, value: 116 
[INFO ] 2025-03-21 15:38:29.632 - [任务 14][Sybase] - column: 4, value: 2025-03-21T07:26:26.910+0800 
[INFO ] 2025-03-21 15:38:29.632 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.632 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.632 - [任务 14][Sybase] - column: 7, value: _ins 
[INFO ] 2025-03-21 15:38:29.632 - [任务 14][Sybase] - column: 8, value: null 
[INFO ] 2025-03-21 15:38:29.632 - [任务 14][Sybase] - column: 9, value: null 
[INFO ] 2025-03-21 15:38:29.632 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:38:29.632 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.632 - [任务 14][Sybase] - column: 3, value: 116 
[INFO ] 2025-03-21 15:38:29.632 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.632 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 72, -128, -82, 0] 
[INFO ] 2025-03-21 15:38:29.632 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 100, -128, -82, 0] 
[INFO ] 2025-03-21 15:38:29.632 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.633 - [任务 14][Sybase] - column: 8, value: 117 
[INFO ] 2025-03-21 15:38:29.633 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.633 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.633 - [任务 14][Sybase] - column: 11, value: testNullLastW 
[INFO ] 2025-03-21 15:38:29.633 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.633 - [任务 14][Sybase] - column: 1, value: 26 
[INFO ] 2025-03-21 15:38:29.633 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.633 - [任务 14][Sybase] - column: 3, value: 116 
[INFO ] 2025-03-21 15:38:29.633 - [任务 14][Sybase] - column: 4, value: 368667 
[INFO ] 2025-03-21 15:38:29.633 - [任务 14][Sybase] - column: 5, value: 117 
[INFO ] 2025-03-21 15:38:29.633 - [任务 14][Sybase] - column: 6, value: 0 
[INFO ] 2025-03-21 15:38:29.634 - [任务 14][Sybase] - column: 7, value: 11434804 
[INFO ] 2025-03-21 15:38:29.634 - [任务 14][Sybase] - column: 8, value: 368667 
[INFO ] 2025-03-21 15:38:29.634 - [任务 14][Sybase] - column: 9, value: 118 
[INFO ] 2025-03-21 15:38:29.634 - [任务 14][Sybase] - column: 10, value: 0 
[INFO ] 2025-03-21 15:38:29.634 - [任务 14][Sybase] - column: 11, value: 11434804 
[INFO ] 2025-03-21 15:38:29.634 - [任务 14][Sybase] - column: 12, value: 0 
[INFO ] 2025-03-21 15:38:29.634 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.634 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.634 - [任务 14][Sybase] - column: 3, value: 116 
[INFO ] 2025-03-21 15:38:29.634 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.634 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.634 - [任务 14][Sybase] - column: 6, value: 119 
[INFO ] 2025-03-21 15:38:29.635 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.635 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.635 - [任务 14][Sybase] - column: 9, value: 2025-03-21T07:26:26.910+0800 
[INFO ] 2025-03-21 15:38:29.635 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.640 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.642 - [任务 14][Sybase] - column: 3, value: 120 
[INFO ] 2025-03-21 15:38:29.642 - [任务 14][Sybase] - column: 4, value: 2025-03-21T07:26:39.906+0800 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 7, value: _ins 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 8, value: null 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 9, value: null 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 1, value: 4 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 3, value: 120 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 4, value: 2048 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 5, value: [0, 0, 0, 0, 102, -128, -82, 0] 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 6, value: [0, 0, 0, 0, 119, -128, -82, 0] 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 7, value: 368667 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 8, value: 121 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 9, value: 0 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 10, value: 11434804 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 11, value: testNullLastW 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 12, value: dbo 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 1, value: 26 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 3, value: 120 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 4, value: 368667 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 5, value: 121 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 6, value: 0 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 7, value: 11434804 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 8, value: 368667 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 9, value: 122 
[INFO ] 2025-03-21 15:38:29.643 - [任务 14][Sybase] - column: 10, value: 0 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 11, value: 11434804 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 12, value: 0 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 3, value: 120 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 6, value: 123 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 9, value: 2025-03-21T07:26:39.906+0800 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 1, value: 0 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 3, value: 124 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 4, value: 2025-03-21T07:27:17.906+0800 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 5, value: 0 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 6, value: 11434804 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 7, value: _systsflush 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 8, value: null 
[INFO ] 2025-03-21 15:38:29.644 - [任务 14][Sybase] - column: 9, value: null 
[INFO ] 2025-03-21 15:38:29.645 - [任务 14][Sybase] - column: 1, value: 30 
[INFO ] 2025-03-21 15:38:29.645 - [任务 14][Sybase] - column: 2, value: 368667 
[INFO ] 2025-03-21 15:38:29.645 - [任务 14][Sybase] - column: 3, value: 124 
[INFO ] 2025-03-21 15:38:29.645 - [任务 14][Sybase] - column: 4, value: 2 
[INFO ] 2025-03-21 15:38:29.645 - [任务 14][Sybase] - column: 5, value: 368667 
[INFO ] 2025-03-21 15:38:29.645 - [任务 14][Sybase] - column: 6, value: 126 
[INFO ] 2025-03-21 15:38:29.645 - [任务 14][Sybase] - column: 7, value: 0 
[INFO ] 2025-03-21 15:38:29.645 - [任务 14][Sybase] - column: 8, value: 11434804 
[INFO ] 2025-03-21 15:38:29.646 - [任务 14][Sybase] - column: 9, value: 2025-03-21T07:27:17.906+0800 
[INFO ] 2025-03-21 15:38:29.647 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:38:32.756 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:38:33.169 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:38:36.148 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:38:36.494 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:38:39.589 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:38:39.999 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:38:42.839 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:38:43.013 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:38:46.183 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:38:46.183 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:38:49.363 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:38:49.396 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:38:52.399 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:38:52.705 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:38:55.827 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:38:55.936 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:38:58.945 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:38:59.353 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:02.333 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:02.479 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:05.655 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:05.656 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:08.770 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:08.976 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:11.798 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:12.138 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:15.294 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:15.426 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:18.564 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:18.766 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:21.704 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:21.909 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:24.781 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:24.904 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:27.909 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:28.105 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:31.109 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:31.515 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:34.361 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:34.764 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:37.758 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:37.912 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:40.919 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:41.322 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:44.298 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:44.394 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:47.595 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:47.799 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:50.659 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:51.064 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:54.055 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:54.460 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:39:57.416 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:39:57.620 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:00.569 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:00.770 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:03.757 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:03.865 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:07.023 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:07.229 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:10.056 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:10.370 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:13.379 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:13.787 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:16.623 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:16.845 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:19.802 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:20.218 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:23.211 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:23.618 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:26.466 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:26.877 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:29.725 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:29.891 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:32.898 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:33.303 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:36.180 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:36.364 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:39.370 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:39.760 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:42.805 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:43.205 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:46.135 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:46.501 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:49.354 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:49.621 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:52.626 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:52.927 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:55.930 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:56.167 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:40:59.153 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:40:59.311 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:02.425 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:02.627 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:05.696 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:05.696 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:08.704 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:08.908 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:11.815 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:12.020 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:15.099 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:15.100 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:18.246 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:18.246 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:21.248 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:21.454 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:24.511 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:24.511 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:27.519 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:27.724 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:30.657 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:30.859 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:33.852 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:33.994 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:37.000 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:37.207 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:40.188 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:40.393 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:43.372 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:43.577 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:46.515 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:46.783 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:49.891 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:50.061 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:53.069 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:53.256 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:56.262 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:56.431 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:41:59.437 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:41:59.568 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:02.635 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:02.840 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:05.813 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:06.018 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:08.922 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:09.057 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:12.060 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:12.262 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:15.299 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:15.406 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:18.413 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:18.612 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:21.616 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:21.736 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:24.840 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:25.046 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:27.889 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:28.035 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:31.035 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:31.191 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:34.197 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:34.377 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:37.456 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:37.632 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:40.634 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:41.037 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:44.026 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:44.232 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:47.200 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:47.261 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:50.377 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:50.583 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:53.397 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:53.811 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:42:56.662 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:42:57.068 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:00.099 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:00.213 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:03.218 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:03.538 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:06.545 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:06.823 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:09.833 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:10.038 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:12.959 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:13.146 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:16.332 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:16.416 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:19.420 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:19.831 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:22.762 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:22.955 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:25.965 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:26.140 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:29.285 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:29.489 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:32.461 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:32.667 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:35.652 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:35.855 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:38.795 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:39.000 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:41.924 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:42.128 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:45.087 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:45.087 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:48.094 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:48.249 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:51.252 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:51.405 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:54.410 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:54.518 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:43:57.642 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:43:57.850 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:00.824 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:01.030 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:03.839 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:04.045 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:07.106 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:07.276 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:10.451 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:10.488 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:13.665 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:13.733 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:16.739 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:16.883 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:20.075 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:20.077 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:23.254 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:23.255 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:26.397 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:26.398 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:29.401 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:29.547 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:32.655 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:32.862 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:35.828 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:36.035 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:38.849 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:39.462 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:42.326 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:42.638 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:45.814 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:45.877 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:48.886 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:49.297 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:52.235 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:52.642 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:55.596 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:55.799 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:44:58.738 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:44:58.908 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:02.065 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:02.267 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:05.258 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:05.465 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:08.268 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:08.465 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:11.656 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:11.774 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:14.781 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:15.193 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:18.136 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:18.210 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:21.217 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:21.367 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:24.372 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:24.722 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:27.532 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:27.734 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:30.752 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:30.909 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:33.918 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:34.048 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:37.096 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:37.259 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:40.290 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:40.679 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:43.491 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:43.777 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:46.965 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:47.132 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:50.142 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:50.583 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:53.589 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:54.203 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:45:57.009 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:45:57.281 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:46:00.284 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:46:00.709 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:46:03.716 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:46:04.125 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:46:07.133 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:46:07.180 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:46:10.216 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:46:10.501 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:46:13.698 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:46:13.934 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:46:16.909 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:46:17.114 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:46:20.121 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:46:20.169 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:46:23.177 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:46:23.382 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:46:26.387 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:46:26.744 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:46:29.745 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:46:30.128 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[INFO ] 2025-03-21 15:46:33.132 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[INFO ] 2025-03-21 15:46:33.462 - [任务 14][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368667, rowId: 126 
[TRACE] 2025-03-21 15:46:34.428 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] running status set to false 
[INFO ] 2025-03-21 15:46:34.429 - [任务 14][Sybase] - Log Miner is shutting down... 
[INFO ] 2025-03-21 15:46:34.429 - [任务 14][Sybase] - rebuild statement with 368667, 126 
[TRACE] 2025-03-21 15:46:34.732 - [任务 14][Sybase] - Incremental sync completed 
[TRACE] 2025-03-21 15:46:34.738 - [任务 14][Sybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_a6c916c8-e5b8-4d11-8d3e-e24c99823953_1742542646388 
[TRACE] 2025-03-21 15:46:34.738 - [任务 14][Sybase] - PDK connector node released: HazelcastSourcePdkDataNode_a6c916c8-e5b8-4d11-8d3e-e24c99823953_1742542646388 
[TRACE] 2025-03-21 15:46:34.738 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] schema data cleaned 
[TRACE] 2025-03-21 15:46:34.742 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] monitor closed 
[TRACE] 2025-03-21 15:46:34.747 - [任务 14][Sybase] - Node Sybase[a6c916c8-e5b8-4d11-8d3e-e24c99823953] close complete, cost 314 ms 
[TRACE] 2025-03-21 15:46:34.747 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] running status set to false 
[TRACE] 2025-03-21 15:46:34.761 - [任务 14][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode_640451ea-6772-4adb-a89d-802ef648efbe_1742542646576 
[TRACE] 2025-03-21 15:46:34.761 - [任务 14][PG] - PDK connector node released: HazelcastTargetPdkDataNode_640451ea-6772-4adb-a89d-802ef648efbe_1742542646576 
[TRACE] 2025-03-21 15:46:34.761 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] schema data cleaned 
[TRACE] 2025-03-21 15:46:34.763 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] monitor closed 
[TRACE] 2025-03-21 15:46:34.765 - [任务 14][PG] - Node PG[640451ea-6772-4adb-a89d-802ef648efbe] close complete, cost 21 ms 
[TRACE] 2025-03-21 15:46:38.604 - [任务 14] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-21 15:46:38.607 - [任务 14] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1c5b08a8 
[TRACE] 2025-03-21 15:46:38.725 - [任务 14] - Stop task milestones: 67dd157261aee65dad34ac99(任务 14)  
[TRACE] 2025-03-21 15:46:38.725 - [任务 14] - Stopped task aspect(s) 
[TRACE] 2025-03-21 15:46:38.725 - [任务 14] - Snapshot order controller have been removed 
[INFO ] 2025-03-21 15:46:38.725 - [任务 14] - Task stopped. 
[TRACE] 2025-03-21 15:46:44.553 - [任务 14] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-21 15:46:44.554 - [任务 14] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1c5b08a8 
[TRACE] 2025-03-21 15:46:44.554 - [任务 14] - Stopped task aspect(s) 
[INFO ] 2025-03-21 15:46:44.555 - [任务 14] - Task stopped. 
[TRACE] 2025-03-21 15:46:44.599 - [任务 14] - Remove memory task client succeed, task: 任务 14[67dd157261aee65dad34ac99] 
[TRACE] 2025-03-21 15:46:44.600 - [任务 14] - Destroy memory task client cache succeed, task: 任务 14[67dd157261aee65dad34ac99] 
