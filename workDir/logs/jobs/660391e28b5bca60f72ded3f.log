[INFO ] 2024-03-27 11:26:37.801 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:37.803 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:37.804 - [任务 21(100)][f6c7120c-f715-48b4-873c-4326c4bd9e80] - Node f6c7120c-f715-48b4-873c-4326c4bd9e80[f6c7120c-f715-48b4-873c-4326c4bd9e80] start preload schema,table counts: 0 
[INFO ] 2024-03-27 11:26:37.813 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:37.814 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:37.814 - [任务 21(100)][f6c7120c-f715-48b4-873c-4326c4bd9e80] - Node f6c7120c-f715-48b4-873c-4326c4bd9e80[f6c7120c-f715-48b4-873c-4326c4bd9e80] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:38.612 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 11:26:38.639 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:38.641 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:38.641 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 11:26:38.641 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 11:26:38.641 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 40 ms 
[INFO ] 2024-03-27 11:26:38.781 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:38.782 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 11:26:38.785 - [任务 21(100)][894f1e85-8381-4b79-86c8-368a0a6e0339] - Node 894f1e85-8381-4b79-86c8-368a0a6e0339[894f1e85-8381-4b79-86c8-368a0a6e0339] start preload schema,table counts: 0 
[INFO ] 2024-03-27 11:26:38.785 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:38.786 - [任务 21(100)][894f1e85-8381-4b79-86c8-368a0a6e0339] - Node 894f1e85-8381-4b79-86c8-368a0a6e0339[894f1e85-8381-4b79-86c8-368a0a6e0339] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:38.786 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:41.585 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 11:26:41.588 - [任务 21(100)][f6c7120c-f715-48b4-873c-4326c4bd9e80] - Node f6c7120c-f715-48b4-873c-4326c4bd9e80[f6c7120c-f715-48b4-873c-4326c4bd9e80] running status set to false 
[INFO ] 2024-03-27 11:26:41.589 - [任务 21(100)][f6c7120c-f715-48b4-873c-4326c4bd9e80] - Node f6c7120c-f715-48b4-873c-4326c4bd9e80[f6c7120c-f715-48b4-873c-4326c4bd9e80] schema data cleaned 
[INFO ] 2024-03-27 11:26:41.589 - [任务 21(100)][f6c7120c-f715-48b4-873c-4326c4bd9e80] - Node f6c7120c-f715-48b4-873c-4326c4bd9e80[f6c7120c-f715-48b4-873c-4326c4bd9e80] monitor closed 
[INFO ] 2024-03-27 11:26:41.589 - [任务 21(100)][f6c7120c-f715-48b4-873c-4326c4bd9e80] - Node f6c7120c-f715-48b4-873c-4326c4bd9e80[f6c7120c-f715-48b4-873c-4326c4bd9e80] close complete, cost 1 ms 
[INFO ] 2024-03-27 11:26:41.595 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-d6a77924-9c0d-479f-956f-330d27bed371 
[INFO ] 2024-03-27 11:26:41.595 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-d6a77924-9c0d-479f-956f-330d27bed371 
[INFO ] 2024-03-27 11:26:41.596 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 11:26:41.598 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 11:26:41.598 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 11:26:41.598 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 15 ms 
[INFO ] 2024-03-27 11:26:41.602 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-f6c7120c-f715-48b4-873c-4326c4bd9e80 complete, cost 3882ms 
[INFO ] 2024-03-27 11:26:41.800 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 11:26:41.811 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:41.811 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:41.811 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 11:26:41.811 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 11:26:41.812 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 25 ms 
[INFO ] 2024-03-27 11:26:42.037 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 11:26:42.039 - [任务 21(100)][894f1e85-8381-4b79-86c8-368a0a6e0339] - Node 894f1e85-8381-4b79-86c8-368a0a6e0339[894f1e85-8381-4b79-86c8-368a0a6e0339] running status set to false 
[INFO ] 2024-03-27 11:26:42.039 - [任务 21(100)][894f1e85-8381-4b79-86c8-368a0a6e0339] - Node 894f1e85-8381-4b79-86c8-368a0a6e0339[894f1e85-8381-4b79-86c8-368a0a6e0339] schema data cleaned 
[INFO ] 2024-03-27 11:26:42.039 - [任务 21(100)][894f1e85-8381-4b79-86c8-368a0a6e0339] - Node 894f1e85-8381-4b79-86c8-368a0a6e0339[894f1e85-8381-4b79-86c8-368a0a6e0339] monitor closed 
[INFO ] 2024-03-27 11:26:42.039 - [任务 21(100)][894f1e85-8381-4b79-86c8-368a0a6e0339] - Node 894f1e85-8381-4b79-86c8-368a0a6e0339[894f1e85-8381-4b79-86c8-368a0a6e0339] close complete, cost 2 ms 
[INFO ] 2024-03-27 11:26:42.041 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-28d8fded-395e-459d-95e1-eb1e09af980a 
[INFO ] 2024-03-27 11:26:42.041 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-28d8fded-395e-459d-95e1-eb1e09af980a 
[INFO ] 2024-03-27 11:26:42.041 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 11:26:42.043 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 11:26:42.043 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 11:26:42.043 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 11 ms 
[INFO ] 2024-03-27 11:26:42.047 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-894f1e85-8381-4b79-86c8-368a0a6e0339 complete, cost 3312ms 
[INFO ] 2024-03-27 11:26:46.985 - [任务 21(100)][09f3818c-47d9-4521-9028-1ceedd388f39] - Node 09f3818c-47d9-4521-9028-1ceedd388f39[09f3818c-47d9-4521-9028-1ceedd388f39] start preload schema,table counts: 0 
[INFO ] 2024-03-27 11:26:46.986 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:46.986 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:46.986 - [任务 21(100)][09f3818c-47d9-4521-9028-1ceedd388f39] - Node 09f3818c-47d9-4521-9028-1ceedd388f39[09f3818c-47d9-4521-9028-1ceedd388f39] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:46.986 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:46.986 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:47.238 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 11:26:47.244 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:47.244 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:47.244 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 11:26:47.244 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 11:26:47.244 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 8 ms 
[INFO ] 2024-03-27 11:26:47.474 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 11:26:47.477 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-0adad44f-e221-41dc-a3f6-99fd6618b179 
[INFO ] 2024-03-27 11:26:47.477 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-0adad44f-e221-41dc-a3f6-99fd6618b179 
[INFO ] 2024-03-27 11:26:47.477 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 11:26:47.478 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 11:26:47.478 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 11:26:47.478 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 4 ms 
[INFO ] 2024-03-27 11:26:47.479 - [任务 21(100)][09f3818c-47d9-4521-9028-1ceedd388f39] - Node 09f3818c-47d9-4521-9028-1ceedd388f39[09f3818c-47d9-4521-9028-1ceedd388f39] running status set to false 
[INFO ] 2024-03-27 11:26:47.479 - [任务 21(100)][09f3818c-47d9-4521-9028-1ceedd388f39] - Node 09f3818c-47d9-4521-9028-1ceedd388f39[09f3818c-47d9-4521-9028-1ceedd388f39] schema data cleaned 
[INFO ] 2024-03-27 11:26:47.480 - [任务 21(100)][09f3818c-47d9-4521-9028-1ceedd388f39] - Node 09f3818c-47d9-4521-9028-1ceedd388f39[09f3818c-47d9-4521-9028-1ceedd388f39] monitor closed 
[INFO ] 2024-03-27 11:26:47.480 - [任务 21(100)][09f3818c-47d9-4521-9028-1ceedd388f39] - Node 09f3818c-47d9-4521-9028-1ceedd388f39[09f3818c-47d9-4521-9028-1ceedd388f39] close complete, cost 0 ms 
[INFO ] 2024-03-27 11:26:47.482 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-09f3818c-47d9-4521-9028-1ceedd388f39 complete, cost 534ms 
[INFO ] 2024-03-27 11:26:50.611 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:50.612 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:50.612 - [任务 21(100)][c939c089-5c72-4ead-ab6e-971ec413e608] - Node c939c089-5c72-4ead-ab6e-971ec413e608[c939c089-5c72-4ead-ab6e-971ec413e608] start preload schema,table counts: 0 
[INFO ] 2024-03-27 11:26:50.613 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 11:26:50.613 - [任务 21(100)][c939c089-5c72-4ead-ab6e-971ec413e608] - Node c939c089-5c72-4ead-ab6e-971ec413e608[c939c089-5c72-4ead-ab6e-971ec413e608] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 11:26:50.613 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 11:26:50.916 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 11:26:50.925 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:50.925 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:50.926 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 11:26:50.926 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 11:26:50.926 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 14 ms 
[INFO ] 2024-03-27 11:26:51.145 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 11:26:51.147 - [任务 21(100)][c939c089-5c72-4ead-ab6e-971ec413e608] - Node c939c089-5c72-4ead-ab6e-971ec413e608[c939c089-5c72-4ead-ab6e-971ec413e608] running status set to false 
[INFO ] 2024-03-27 11:26:51.147 - [任务 21(100)][c939c089-5c72-4ead-ab6e-971ec413e608] - Node c939c089-5c72-4ead-ab6e-971ec413e608[c939c089-5c72-4ead-ab6e-971ec413e608] schema data cleaned 
[INFO ] 2024-03-27 11:26:51.148 - [任务 21(100)][c939c089-5c72-4ead-ab6e-971ec413e608] - Node c939c089-5c72-4ead-ab6e-971ec413e608[c939c089-5c72-4ead-ab6e-971ec413e608] monitor closed 
[INFO ] 2024-03-27 11:26:51.148 - [任务 21(100)][c939c089-5c72-4ead-ab6e-971ec413e608] - Node c939c089-5c72-4ead-ab6e-971ec413e608[c939c089-5c72-4ead-ab6e-971ec413e608] close complete, cost 0 ms 
[INFO ] 2024-03-27 11:26:51.150 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-449b619e-4bb9-482e-ba2d-44b87cd6bc58 
[INFO ] 2024-03-27 11:26:51.150 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-449b619e-4bb9-482e-ba2d-44b87cd6bc58 
[INFO ] 2024-03-27 11:26:51.150 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 11:26:51.150 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 11:26:51.150 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 11:26:51.151 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 6 ms 
[INFO ] 2024-03-27 11:26:51.153 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-c939c089-5c72-4ead-ab6e-971ec413e608 complete, cost 633ms 
[INFO ] 2024-03-27 11:26:52.609 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:52.613 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:52.614 - [任务 21(100)][3e22cb48-6499-4bcf-b5d6-52f04ad1a213] - Node 3e22cb48-6499-4bcf-b5d6-52f04ad1a213[3e22cb48-6499-4bcf-b5d6-52f04ad1a213] start preload schema,table counts: 0 
[INFO ] 2024-03-27 11:26:52.614 - [任务 21(100)][3e22cb48-6499-4bcf-b5d6-52f04ad1a213] - Node 3e22cb48-6499-4bcf-b5d6-52f04ad1a213[3e22cb48-6499-4bcf-b5d6-52f04ad1a213] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:52.614 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:52.614 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:52.910 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 11:26:52.916 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:52.916 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:52.916 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 11:26:52.916 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 11:26:52.918 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 20 ms 
[INFO ] 2024-03-27 11:26:53.138 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 11:26:53.138 - [任务 21(100)][3e22cb48-6499-4bcf-b5d6-52f04ad1a213] - Node 3e22cb48-6499-4bcf-b5d6-52f04ad1a213[3e22cb48-6499-4bcf-b5d6-52f04ad1a213] running status set to false 
[INFO ] 2024-03-27 11:26:53.139 - [任务 21(100)][3e22cb48-6499-4bcf-b5d6-52f04ad1a213] - Node 3e22cb48-6499-4bcf-b5d6-52f04ad1a213[3e22cb48-6499-4bcf-b5d6-52f04ad1a213] schema data cleaned 
[INFO ] 2024-03-27 11:26:53.140 - [任务 21(100)][3e22cb48-6499-4bcf-b5d6-52f04ad1a213] - Node 3e22cb48-6499-4bcf-b5d6-52f04ad1a213[3e22cb48-6499-4bcf-b5d6-52f04ad1a213] monitor closed 
[INFO ] 2024-03-27 11:26:53.140 - [任务 21(100)][3e22cb48-6499-4bcf-b5d6-52f04ad1a213] - Node 3e22cb48-6499-4bcf-b5d6-52f04ad1a213[3e22cb48-6499-4bcf-b5d6-52f04ad1a213] close complete, cost 0 ms 
[INFO ] 2024-03-27 11:26:53.146 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-2c2a7b74-faae-49f8-8fd5-2378bbf32acb 
[INFO ] 2024-03-27 11:26:53.151 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-2c2a7b74-faae-49f8-8fd5-2378bbf32acb 
[INFO ] 2024-03-27 11:26:53.166 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 11:26:53.173 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 11:26:53.173 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 11:26:53.173 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 14 ms 
[INFO ] 2024-03-27 11:26:53.173 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-3e22cb48-6499-4bcf-b5d6-52f04ad1a213 complete, cost 632ms 
[INFO ] 2024-03-27 11:26:54.901 - [任务 21(100)][07211530-26d5-432b-87df-fb1f50c9dc03] - Node 07211530-26d5-432b-87df-fb1f50c9dc03[07211530-26d5-432b-87df-fb1f50c9dc03] start preload schema,table counts: 0 
[INFO ] 2024-03-27 11:26:54.903 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:54.903 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:54.903 - [任务 21(100)][07211530-26d5-432b-87df-fb1f50c9dc03] - Node 07211530-26d5-432b-87df-fb1f50c9dc03[07211530-26d5-432b-87df-fb1f50c9dc03] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:54.903 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:54.903 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 2 ms 
[INFO ] 2024-03-27 11:26:55.202 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 11:26:55.214 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:55.214 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:55.215 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 11:26:55.215 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 11:26:55.216 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 25 ms 
[INFO ] 2024-03-27 11:26:55.419 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 11:26:55.424 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-288de71d-f4b5-4a4d-9a19-42df89933d14 
[INFO ] 2024-03-27 11:26:55.424 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-288de71d-f4b5-4a4d-9a19-42df89933d14 
[INFO ] 2024-03-27 11:26:55.424 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 11:26:55.424 - [任务 21(100)][07211530-26d5-432b-87df-fb1f50c9dc03] - Node 07211530-26d5-432b-87df-fb1f50c9dc03[07211530-26d5-432b-87df-fb1f50c9dc03] running status set to false 
[INFO ] 2024-03-27 11:26:55.424 - [任务 21(100)][07211530-26d5-432b-87df-fb1f50c9dc03] - Node 07211530-26d5-432b-87df-fb1f50c9dc03[07211530-26d5-432b-87df-fb1f50c9dc03] schema data cleaned 
[INFO ] 2024-03-27 11:26:55.424 - [任务 21(100)][07211530-26d5-432b-87df-fb1f50c9dc03] - Node 07211530-26d5-432b-87df-fb1f50c9dc03[07211530-26d5-432b-87df-fb1f50c9dc03] monitor closed 
[INFO ] 2024-03-27 11:26:55.424 - [任务 21(100)][07211530-26d5-432b-87df-fb1f50c9dc03] - Node 07211530-26d5-432b-87df-fb1f50c9dc03[07211530-26d5-432b-87df-fb1f50c9dc03] close complete, cost 0 ms 
[INFO ] 2024-03-27 11:26:55.424 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 11:26:55.424 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 11:26:55.424 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 5 ms 
[INFO ] 2024-03-27 11:26:55.427 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-07211530-26d5-432b-87df-fb1f50c9dc03 complete, cost 625ms 
[INFO ] 2024-03-27 11:26:56.347 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:56.347 - [任务 21(100)][0feb80ea-9eca-455c-ba6e-2c4f3c894928] - Node 0feb80ea-9eca-455c-ba6e-2c4f3c894928[0feb80ea-9eca-455c-ba6e-2c4f3c894928] start preload schema,table counts: 0 
[INFO ] 2024-03-27 11:26:56.347 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:56.347 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:56.347 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:56.347 - [任务 21(100)][0feb80ea-9eca-455c-ba6e-2c4f3c894928] - Node 0feb80ea-9eca-455c-ba6e-2c4f3c894928[0feb80ea-9eca-455c-ba6e-2c4f3c894928] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:56.455 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:56.455 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:56.455 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:56.455 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:56.456 - [任务 21(100)][fe439e18-44a2-44f2-8d32-11ce417a103c] - Node fe439e18-44a2-44f2-8d32-11ce417a103c[fe439e18-44a2-44f2-8d32-11ce417a103c] start preload schema,table counts: 0 
[INFO ] 2024-03-27 11:26:56.456 - [任务 21(100)][fe439e18-44a2-44f2-8d32-11ce417a103c] - Node fe439e18-44a2-44f2-8d32-11ce417a103c[fe439e18-44a2-44f2-8d32-11ce417a103c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:56.586 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 11:26:56.590 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:56.591 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:56.591 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 11:26:56.591 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 11:26:56.591 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 5 ms 
[INFO ] 2024-03-27 11:26:57.033 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 11:26:57.054 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 11:26:57.054 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:57.054 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:57.054 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 11:26:57.054 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 11:26:57.055 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 26 ms 
[INFO ] 2024-03-27 11:26:57.058 - [任务 21(100)][fe439e18-44a2-44f2-8d32-11ce417a103c] - Node fe439e18-44a2-44f2-8d32-11ce417a103c[fe439e18-44a2-44f2-8d32-11ce417a103c] running status set to false 
[INFO ] 2024-03-27 11:26:57.058 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-f9f3aea4-1e91-4f36-97f4-f5388933aab5 
[INFO ] 2024-03-27 11:26:57.058 - [任务 21(100)][fe439e18-44a2-44f2-8d32-11ce417a103c] - Node fe439e18-44a2-44f2-8d32-11ce417a103c[fe439e18-44a2-44f2-8d32-11ce417a103c] schema data cleaned 
[INFO ] 2024-03-27 11:26:57.058 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-f9f3aea4-1e91-4f36-97f4-f5388933aab5 
[INFO ] 2024-03-27 11:26:57.058 - [任务 21(100)][fe439e18-44a2-44f2-8d32-11ce417a103c] - Node fe439e18-44a2-44f2-8d32-11ce417a103c[fe439e18-44a2-44f2-8d32-11ce417a103c] monitor closed 
[INFO ] 2024-03-27 11:26:57.058 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 11:26:57.059 - [任务 21(100)][fe439e18-44a2-44f2-8d32-11ce417a103c] - Node fe439e18-44a2-44f2-8d32-11ce417a103c[fe439e18-44a2-44f2-8d32-11ce417a103c] close complete, cost 0 ms 
[INFO ] 2024-03-27 11:26:57.059 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 11:26:57.060 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 11:26:57.060 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 6 ms 
[INFO ] 2024-03-27 11:26:57.062 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-fe439e18-44a2-44f2-8d32-11ce417a103c complete, cost 644ms 
[INFO ] 2024-03-27 11:26:57.377 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 11:26:57.379 - [任务 21(100)][0feb80ea-9eca-455c-ba6e-2c4f3c894928] - Node 0feb80ea-9eca-455c-ba6e-2c4f3c894928[0feb80ea-9eca-455c-ba6e-2c4f3c894928] running status set to false 
[INFO ] 2024-03-27 11:26:57.380 - [任务 21(100)][0feb80ea-9eca-455c-ba6e-2c4f3c894928] - Node 0feb80ea-9eca-455c-ba6e-2c4f3c894928[0feb80ea-9eca-455c-ba6e-2c4f3c894928] schema data cleaned 
[INFO ] 2024-03-27 11:26:57.380 - [任务 21(100)][0feb80ea-9eca-455c-ba6e-2c4f3c894928] - Node 0feb80ea-9eca-455c-ba6e-2c4f3c894928[0feb80ea-9eca-455c-ba6e-2c4f3c894928] monitor closed 
[INFO ] 2024-03-27 11:26:57.384 - [任务 21(100)][0feb80ea-9eca-455c-ba6e-2c4f3c894928] - Node 0feb80ea-9eca-455c-ba6e-2c4f3c894928[0feb80ea-9eca-455c-ba6e-2c4f3c894928] close complete, cost 0 ms 
[INFO ] 2024-03-27 11:26:57.390 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-bc674caf-dd28-4124-bd17-bb5e69ee7457 
[INFO ] 2024-03-27 11:26:57.390 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-bc674caf-dd28-4124-bd17-bb5e69ee7457 
[INFO ] 2024-03-27 11:26:57.390 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 11:26:57.390 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 11:26:57.390 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 11:26:57.390 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 21 ms 
[INFO ] 2024-03-27 11:26:57.390 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-0feb80ea-9eca-455c-ba6e-2c4f3c894928 complete, cost 1077ms 
[INFO ] 2024-03-27 11:26:57.829 - [任务 21(100)][f6225d0b-ecef-4bc6-8981-4d7327a34a8a] - Node f6225d0b-ecef-4bc6-8981-4d7327a34a8a[f6225d0b-ecef-4bc6-8981-4d7327a34a8a] start preload schema,table counts: 0 
[INFO ] 2024-03-27 11:26:57.829 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:57.829 - [任务 21(100)][f6225d0b-ecef-4bc6-8981-4d7327a34a8a] - Node f6225d0b-ecef-4bc6-8981-4d7327a34a8a[f6225d0b-ecef-4bc6-8981-4d7327a34a8a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:26:57.829 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:26:57.829 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 11:26:57.829 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 11:26:58.085 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 11:26:58.094 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:58.094 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:26:58.094 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 11:26:58.094 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 11:26:58.094 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 9 ms 
[INFO ] 2024-03-27 11:26:58.315 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 11:26:58.317 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-bded90f2-e013-4269-80c7-98cb37bbc624 
[INFO ] 2024-03-27 11:26:58.317 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-bded90f2-e013-4269-80c7-98cb37bbc624 
[INFO ] 2024-03-27 11:26:58.317 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 11:26:58.317 - [任务 21(100)][f6225d0b-ecef-4bc6-8981-4d7327a34a8a] - Node f6225d0b-ecef-4bc6-8981-4d7327a34a8a[f6225d0b-ecef-4bc6-8981-4d7327a34a8a] running status set to false 
[INFO ] 2024-03-27 11:26:58.317 - [任务 21(100)][f6225d0b-ecef-4bc6-8981-4d7327a34a8a] - Node f6225d0b-ecef-4bc6-8981-4d7327a34a8a[f6225d0b-ecef-4bc6-8981-4d7327a34a8a] schema data cleaned 
[INFO ] 2024-03-27 11:26:58.318 - [任务 21(100)][f6225d0b-ecef-4bc6-8981-4d7327a34a8a] - Node f6225d0b-ecef-4bc6-8981-4d7327a34a8a[f6225d0b-ecef-4bc6-8981-4d7327a34a8a] monitor closed 
[INFO ] 2024-03-27 11:26:58.318 - [任务 21(100)][f6225d0b-ecef-4bc6-8981-4d7327a34a8a] - Node f6225d0b-ecef-4bc6-8981-4d7327a34a8a[f6225d0b-ecef-4bc6-8981-4d7327a34a8a] close complete, cost 0 ms 
[INFO ] 2024-03-27 11:26:58.318 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 11:26:58.318 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 11:26:58.318 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 3 ms 
[INFO ] 2024-03-27 11:26:58.320 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-f6225d0b-ecef-4bc6-8981-4d7327a34a8a complete, cost 543ms 
[INFO ] 2024-03-27 11:27:20.204 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:27:20.205 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:27:20.223 - [任务 21(100)][9aa337a2-f3c8-4cc7-a85a-0119f737f585] - Node 9aa337a2-f3c8-4cc7-a85a-0119f737f585[9aa337a2-f3c8-4cc7-a85a-0119f737f585] start preload schema,table counts: 0 
[INFO ] 2024-03-27 11:27:20.227 - [任务 21(100)][9aa337a2-f3c8-4cc7-a85a-0119f737f585] - Node 9aa337a2-f3c8-4cc7-a85a-0119f737f585[9aa337a2-f3c8-4cc7-a85a-0119f737f585] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:27:20.230 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:27:20.232 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:27:20.478 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 11:27:20.484 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:27:20.484 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:27:20.485 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 11:27:20.485 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 11:27:20.485 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 16 ms 
[INFO ] 2024-03-27 11:27:20.705 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 11:27:20.708 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-3b9c7778-8ff8-4e84-934a-ee4129662dc0 
[INFO ] 2024-03-27 11:27:20.708 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-3b9c7778-8ff8-4e84-934a-ee4129662dc0 
[INFO ] 2024-03-27 11:27:20.709 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 11:27:20.709 - [任务 21(100)][9aa337a2-f3c8-4cc7-a85a-0119f737f585] - Node 9aa337a2-f3c8-4cc7-a85a-0119f737f585[9aa337a2-f3c8-4cc7-a85a-0119f737f585] running status set to false 
[INFO ] 2024-03-27 11:27:20.709 - [任务 21(100)][9aa337a2-f3c8-4cc7-a85a-0119f737f585] - Node 9aa337a2-f3c8-4cc7-a85a-0119f737f585[9aa337a2-f3c8-4cc7-a85a-0119f737f585] schema data cleaned 
[INFO ] 2024-03-27 11:27:20.709 - [任务 21(100)][9aa337a2-f3c8-4cc7-a85a-0119f737f585] - Node 9aa337a2-f3c8-4cc7-a85a-0119f737f585[9aa337a2-f3c8-4cc7-a85a-0119f737f585] monitor closed 
[INFO ] 2024-03-27 11:27:20.709 - [任务 21(100)][9aa337a2-f3c8-4cc7-a85a-0119f737f585] - Node 9aa337a2-f3c8-4cc7-a85a-0119f737f585[9aa337a2-f3c8-4cc7-a85a-0119f737f585] close complete, cost 0 ms 
[INFO ] 2024-03-27 11:27:20.710 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 11:27:20.710 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 11:27:20.710 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 5 ms 
[INFO ] 2024-03-27 11:27:20.713 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-9aa337a2-f3c8-4cc7-a85a-0119f737f585 complete, cost 563ms 
[INFO ] 2024-03-27 11:27:42.403 - [任务 21(100)][991174b3-7c40-43af-b81e-bb94bb9a8856] - Node 991174b3-7c40-43af-b81e-bb94bb9a8856[991174b3-7c40-43af-b81e-bb94bb9a8856] start preload schema,table counts: 0 
[INFO ] 2024-03-27 11:27:42.404 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:27:42.404 - [任务 21(100)][991174b3-7c40-43af-b81e-bb94bb9a8856] - Node 991174b3-7c40-43af-b81e-bb94bb9a8856[991174b3-7c40-43af-b81e-bb94bb9a8856] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:27:42.404 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:27:42.404 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:27:42.404 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:27:42.663 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 11:27:42.682 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:27:42.682 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:27:42.682 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 11:27:42.682 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 11:27:42.682 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 19 ms 
[INFO ] 2024-03-27 11:27:42.898 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 11:27:42.902 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-36107136-8056-4509-8639-7edc7e194811 
[INFO ] 2024-03-27 11:27:42.902 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-36107136-8056-4509-8639-7edc7e194811 
[INFO ] 2024-03-27 11:27:42.902 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 11:27:42.903 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 11:27:42.903 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 11:27:42.903 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 5 ms 
[INFO ] 2024-03-27 11:27:42.904 - [任务 21(100)][991174b3-7c40-43af-b81e-bb94bb9a8856] - Node 991174b3-7c40-43af-b81e-bb94bb9a8856[991174b3-7c40-43af-b81e-bb94bb9a8856] running status set to false 
[INFO ] 2024-03-27 11:27:42.904 - [任务 21(100)][991174b3-7c40-43af-b81e-bb94bb9a8856] - Node 991174b3-7c40-43af-b81e-bb94bb9a8856[991174b3-7c40-43af-b81e-bb94bb9a8856] schema data cleaned 
[INFO ] 2024-03-27 11:27:42.904 - [任务 21(100)][991174b3-7c40-43af-b81e-bb94bb9a8856] - Node 991174b3-7c40-43af-b81e-bb94bb9a8856[991174b3-7c40-43af-b81e-bb94bb9a8856] monitor closed 
[INFO ] 2024-03-27 11:27:42.904 - [任务 21(100)][991174b3-7c40-43af-b81e-bb94bb9a8856] - Node 991174b3-7c40-43af-b81e-bb94bb9a8856[991174b3-7c40-43af-b81e-bb94bb9a8856] close complete, cost 0 ms 
[INFO ] 2024-03-27 11:27:42.908 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-991174b3-7c40-43af-b81e-bb94bb9a8856 complete, cost 558ms 
[INFO ] 2024-03-27 11:27:43.032 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:27:43.033 - [任务 21(100)][929cb90a-7a7b-4d52-8039-35123a322299] - Node 929cb90a-7a7b-4d52-8039-35123a322299[929cb90a-7a7b-4d52-8039-35123a322299] start preload schema,table counts: 0 
[INFO ] 2024-03-27 11:27:43.033 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:27:43.033 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:27:43.033 - [任务 21(100)][929cb90a-7a7b-4d52-8039-35123a322299] - Node 929cb90a-7a7b-4d52-8039-35123a322299[929cb90a-7a7b-4d52-8039-35123a322299] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:27:43.033 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:27:43.284 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 11:27:43.294 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:27:43.295 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:27:43.295 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 11:27:43.295 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 11:27:43.295 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 12 ms 
[INFO ] 2024-03-27 11:27:43.530 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 11:27:43.534 - [任务 21(100)][929cb90a-7a7b-4d52-8039-35123a322299] - Node 929cb90a-7a7b-4d52-8039-35123a322299[929cb90a-7a7b-4d52-8039-35123a322299] running status set to false 
[INFO ] 2024-03-27 11:27:43.535 - [任务 21(100)][929cb90a-7a7b-4d52-8039-35123a322299] - Node 929cb90a-7a7b-4d52-8039-35123a322299[929cb90a-7a7b-4d52-8039-35123a322299] schema data cleaned 
[INFO ] 2024-03-27 11:27:43.535 - [任务 21(100)][929cb90a-7a7b-4d52-8039-35123a322299] - Node 929cb90a-7a7b-4d52-8039-35123a322299[929cb90a-7a7b-4d52-8039-35123a322299] monitor closed 
[INFO ] 2024-03-27 11:27:43.535 - [任务 21(100)][929cb90a-7a7b-4d52-8039-35123a322299] - Node 929cb90a-7a7b-4d52-8039-35123a322299[929cb90a-7a7b-4d52-8039-35123a322299] close complete, cost 0 ms 
[INFO ] 2024-03-27 11:27:43.537 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-02f8e897-7a71-4cd5-b4d7-1788295fa39e 
[INFO ] 2024-03-27 11:27:43.537 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-02f8e897-7a71-4cd5-b4d7-1788295fa39e 
[INFO ] 2024-03-27 11:27:43.537 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 11:27:43.538 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 11:27:43.538 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 11:27:43.538 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 8 ms 
[INFO ] 2024-03-27 11:27:43.541 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-929cb90a-7a7b-4d52-8039-35123a322299 complete, cost 546ms 
[INFO ] 2024-03-27 11:27:45.203 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:27:45.205 - [任务 21(100)][19770809-224c-49a8-86b0-e865520fbf95] - Node 19770809-224c-49a8-86b0-e865520fbf95[19770809-224c-49a8-86b0-e865520fbf95] start preload schema,table counts: 0 
[INFO ] 2024-03-27 11:27:45.205 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:27:45.205 - [任务 21(100)][19770809-224c-49a8-86b0-e865520fbf95] - Node 19770809-224c-49a8-86b0-e865520fbf95[19770809-224c-49a8-86b0-e865520fbf95] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:27:45.205 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 11:27:45.205 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 11:27:45.482 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 11:27:45.492 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:27:45.492 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:27:45.492 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 11:27:45.493 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 11:27:45.493 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 10 ms 
[INFO ] 2024-03-27 11:27:45.709 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 11:27:45.713 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-ab26239a-d9bc-48db-ab8d-bec041a7d072 
[INFO ] 2024-03-27 11:27:45.713 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-ab26239a-d9bc-48db-ab8d-bec041a7d072 
[INFO ] 2024-03-27 11:27:45.713 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 11:27:45.714 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 11:27:45.714 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 11:27:45.714 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 5 ms 
[INFO ] 2024-03-27 11:27:45.715 - [任务 21(100)][19770809-224c-49a8-86b0-e865520fbf95] - Node 19770809-224c-49a8-86b0-e865520fbf95[19770809-224c-49a8-86b0-e865520fbf95] running status set to false 
[INFO ] 2024-03-27 11:27:45.715 - [任务 21(100)][19770809-224c-49a8-86b0-e865520fbf95] - Node 19770809-224c-49a8-86b0-e865520fbf95[19770809-224c-49a8-86b0-e865520fbf95] schema data cleaned 
[INFO ] 2024-03-27 11:27:45.715 - [任务 21(100)][19770809-224c-49a8-86b0-e865520fbf95] - Node 19770809-224c-49a8-86b0-e865520fbf95[19770809-224c-49a8-86b0-e865520fbf95] monitor closed 
[INFO ] 2024-03-27 11:27:45.715 - [任务 21(100)][19770809-224c-49a8-86b0-e865520fbf95] - Node 19770809-224c-49a8-86b0-e865520fbf95[19770809-224c-49a8-86b0-e865520fbf95] close complete, cost 0 ms 
[INFO ] 2024-03-27 11:27:45.718 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-19770809-224c-49a8-86b0-e865520fbf95 complete, cost 579ms 
[INFO ] 2024-03-27 11:27:53.969 - [任务 21(100)][e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f] - Node e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f[e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f] start preload schema,table counts: 0 
[INFO ] 2024-03-27 11:27:53.970 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:27:53.970 - [任务 21(100)][e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f] - Node e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f[e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:27:53.970 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:27:53.970 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:27:53.970 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 11:27:54.208 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 11:27:54.217 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:27:54.217 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 11:27:54.217 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 11:27:54.217 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 11:27:54.217 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 9 ms 
[INFO ] 2024-03-27 11:27:54.454 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 11:27:54.455 - [任务 21(100)][e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f] - Node e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f[e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f] running status set to false 
[INFO ] 2024-03-27 11:27:54.455 - [任务 21(100)][e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f] - Node e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f[e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f] schema data cleaned 
[INFO ] 2024-03-27 11:27:54.455 - [任务 21(100)][e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f] - Node e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f[e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f] monitor closed 
[INFO ] 2024-03-27 11:27:54.456 - [任务 21(100)][e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f] - Node e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f[e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f] close complete, cost 0 ms 
[INFO ] 2024-03-27 11:27:54.460 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-72207cde-6906-41bf-99a9-1e843fba4caf 
[INFO ] 2024-03-27 11:27:54.461 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-72207cde-6906-41bf-99a9-1e843fba4caf 
[INFO ] 2024-03-27 11:27:54.461 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 11:27:54.462 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 11:27:54.462 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 11:27:54.463 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 10 ms 
[INFO ] 2024-03-27 11:27:54.466 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-e1d4d3ea-6e95-4ee6-9ae2-53283e87a94f complete, cost 553ms 
[INFO ] 2024-03-27 12:22:54.209 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 12:22:54.210 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 12:22:54.212 - [任务 21(100)][fea59fbe-7c8d-42e3-b3c9-dca9d4955d86] - Node fea59fbe-7c8d-42e3-b3c9-dca9d4955d86[fea59fbe-7c8d-42e3-b3c9-dca9d4955d86] start preload schema,table counts: 0 
[INFO ] 2024-03-27 12:22:54.219 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 12:22:54.220 - [任务 21(100)][fea59fbe-7c8d-42e3-b3c9-dca9d4955d86] - Node fea59fbe-7c8d-42e3-b3c9-dca9d4955d86[fea59fbe-7c8d-42e3-b3c9-dca9d4955d86] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 12:22:54.220 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 12:22:54.533 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 12:22:54.549 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 12:22:54.549 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 12:22:54.549 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 12:22:54.549 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 12:22:54.549 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 20 ms 
[INFO ] 2024-03-27 12:22:54.800 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 12:22:54.804 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-7c8e4995-a846-4704-99b7-ea97354a2f7e 
[INFO ] 2024-03-27 12:22:54.805 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-7c8e4995-a846-4704-99b7-ea97354a2f7e 
[INFO ] 2024-03-27 12:22:54.810 - [任务 21(100)][fea59fbe-7c8d-42e3-b3c9-dca9d4955d86] - Node fea59fbe-7c8d-42e3-b3c9-dca9d4955d86[fea59fbe-7c8d-42e3-b3c9-dca9d4955d86] running status set to false 
[INFO ] 2024-03-27 12:22:54.812 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 12:22:54.813 - [任务 21(100)][fea59fbe-7c8d-42e3-b3c9-dca9d4955d86] - Node fea59fbe-7c8d-42e3-b3c9-dca9d4955d86[fea59fbe-7c8d-42e3-b3c9-dca9d4955d86] schema data cleaned 
[INFO ] 2024-03-27 12:22:54.814 - [任务 21(100)][fea59fbe-7c8d-42e3-b3c9-dca9d4955d86] - Node fea59fbe-7c8d-42e3-b3c9-dca9d4955d86[fea59fbe-7c8d-42e3-b3c9-dca9d4955d86] monitor closed 
[INFO ] 2024-03-27 12:22:54.815 - [任务 21(100)][fea59fbe-7c8d-42e3-b3c9-dca9d4955d86] - Node fea59fbe-7c8d-42e3-b3c9-dca9d4955d86[fea59fbe-7c8d-42e3-b3c9-dca9d4955d86] close complete, cost 2 ms 
[INFO ] 2024-03-27 12:22:54.815 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 12:22:54.816 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 12:22:54.816 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 16 ms 
[INFO ] 2024-03-27 12:22:54.819 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-fea59fbe-7c8d-42e3-b3c9-dca9d4955d86 complete, cost 771ms 
[INFO ] 2024-03-27 13:05:33.558 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:05:33.559 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:05:33.560 - [任务 21(100)][be3220a5-1b71-4c6b-8cf2-4fc5290763a9] - Node be3220a5-1b71-4c6b-8cf2-4fc5290763a9[be3220a5-1b71-4c6b-8cf2-4fc5290763a9] start preload schema,table counts: 0 
[INFO ] 2024-03-27 13:05:33.561 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:05:33.561 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:05:33.561 - [任务 21(100)][be3220a5-1b71-4c6b-8cf2-4fc5290763a9] - Node be3220a5-1b71-4c6b-8cf2-4fc5290763a9[be3220a5-1b71-4c6b-8cf2-4fc5290763a9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:05:33.561 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 13:05:33.561 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:05:33.561 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:05:33.561 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 13:05:33.561 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 13:05:33.561 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 10 ms 
[INFO ] 2024-03-27 13:05:33.561 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 13:05:33.562 - [任务 21(100)][be3220a5-1b71-4c6b-8cf2-4fc5290763a9] - Node be3220a5-1b71-4c6b-8cf2-4fc5290763a9[be3220a5-1b71-4c6b-8cf2-4fc5290763a9] running status set to false 
[INFO ] 2024-03-27 13:05:33.562 - [任务 21(100)][be3220a5-1b71-4c6b-8cf2-4fc5290763a9] - Node be3220a5-1b71-4c6b-8cf2-4fc5290763a9[be3220a5-1b71-4c6b-8cf2-4fc5290763a9] schema data cleaned 
[INFO ] 2024-03-27 13:05:33.562 - [任务 21(100)][be3220a5-1b71-4c6b-8cf2-4fc5290763a9] - Node be3220a5-1b71-4c6b-8cf2-4fc5290763a9[be3220a5-1b71-4c6b-8cf2-4fc5290763a9] monitor closed 
[INFO ] 2024-03-27 13:05:33.562 - [任务 21(100)][be3220a5-1b71-4c6b-8cf2-4fc5290763a9] - Node be3220a5-1b71-4c6b-8cf2-4fc5290763a9[be3220a5-1b71-4c6b-8cf2-4fc5290763a9] close complete, cost 2 ms 
[INFO ] 2024-03-27 13:05:33.562 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-270bf833-0c0f-4781-bfe0-82c4a887a3dd 
[INFO ] 2024-03-27 13:05:33.562 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-270bf833-0c0f-4781-bfe0-82c4a887a3dd 
[INFO ] 2024-03-27 13:05:33.562 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 13:05:33.562 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 13:05:33.562 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 13:05:33.562 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 14 ms 
[INFO ] 2024-03-27 13:05:33.563 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-be3220a5-1b71-4c6b-8cf2-4fc5290763a9 complete, cost 668ms 
[INFO ] 2024-03-27 13:08:58.549 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:08:58.550 - [任务 21(100)][d7d4997e-e761-4049-8ef9-fa4d98ec02f5] - Node d7d4997e-e761-4049-8ef9-fa4d98ec02f5[d7d4997e-e761-4049-8ef9-fa4d98ec02f5] start preload schema,table counts: 0 
[INFO ] 2024-03-27 13:08:58.551 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:08:58.551 - [任务 21(100)][d7d4997e-e761-4049-8ef9-fa4d98ec02f5] - Node d7d4997e-e761-4049-8ef9-fa4d98ec02f5[d7d4997e-e761-4049-8ef9-fa4d98ec02f5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:08:58.551 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:08:58.552 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 13:08:58.552 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 13:08:58.552 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:08:58.552 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:08:58.552 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 13:08:58.552 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 13:08:58.552 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 10 ms 
[INFO ] 2024-03-27 13:08:58.552 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 13:08:58.556 - [任务 21(100)][d7d4997e-e761-4049-8ef9-fa4d98ec02f5] - Node d7d4997e-e761-4049-8ef9-fa4d98ec02f5[d7d4997e-e761-4049-8ef9-fa4d98ec02f5] running status set to false 
[INFO ] 2024-03-27 13:08:58.560 - [任务 21(100)][d7d4997e-e761-4049-8ef9-fa4d98ec02f5] - Node d7d4997e-e761-4049-8ef9-fa4d98ec02f5[d7d4997e-e761-4049-8ef9-fa4d98ec02f5] schema data cleaned 
[INFO ] 2024-03-27 13:08:58.560 - [任务 21(100)][d7d4997e-e761-4049-8ef9-fa4d98ec02f5] - Node d7d4997e-e761-4049-8ef9-fa4d98ec02f5[d7d4997e-e761-4049-8ef9-fa4d98ec02f5] monitor closed 
[INFO ] 2024-03-27 13:08:58.561 - [任务 21(100)][d7d4997e-e761-4049-8ef9-fa4d98ec02f5] - Node d7d4997e-e761-4049-8ef9-fa4d98ec02f5[d7d4997e-e761-4049-8ef9-fa4d98ec02f5] close complete, cost 1 ms 
[INFO ] 2024-03-27 13:08:58.561 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-f0af3642-7980-4d44-8568-5d536469527d 
[INFO ] 2024-03-27 13:08:58.561 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-f0af3642-7980-4d44-8568-5d536469527d 
[INFO ] 2024-03-27 13:08:58.561 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 13:08:58.561 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 13:08:58.561 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 13:08:58.566 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 11 ms 
[INFO ] 2024-03-27 13:08:58.566 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-d7d4997e-e761-4049-8ef9-fa4d98ec02f5 complete, cost 606ms 
[INFO ] 2024-03-27 13:09:03.572 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:03.573 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:03.575 - [任务 21(100)][c85ad8e2-f639-400d-88cd-b7d7907c9d1f] - Node c85ad8e2-f639-400d-88cd-b7d7907c9d1f[c85ad8e2-f639-400d-88cd-b7d7907c9d1f] start preload schema,table counts: 0 
[INFO ] 2024-03-27 13:09:03.576 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 13:09:03.576 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 4 ms 
[INFO ] 2024-03-27 13:09:03.576 - [任务 21(100)][c85ad8e2-f639-400d-88cd-b7d7907c9d1f] - Node c85ad8e2-f639-400d-88cd-b7d7907c9d1f[c85ad8e2-f639-400d-88cd-b7d7907c9d1f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:03.843 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 13:09:03.849 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:03.849 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:03.850 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 13:09:03.850 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 13:09:03.850 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 11 ms 
[INFO ] 2024-03-27 13:09:04.089 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 13:09:04.091 - [任务 21(100)][c85ad8e2-f639-400d-88cd-b7d7907c9d1f] - Node c85ad8e2-f639-400d-88cd-b7d7907c9d1f[c85ad8e2-f639-400d-88cd-b7d7907c9d1f] running status set to false 
[INFO ] 2024-03-27 13:09:04.091 - [任务 21(100)][c85ad8e2-f639-400d-88cd-b7d7907c9d1f] - Node c85ad8e2-f639-400d-88cd-b7d7907c9d1f[c85ad8e2-f639-400d-88cd-b7d7907c9d1f] schema data cleaned 
[INFO ] 2024-03-27 13:09:04.091 - [任务 21(100)][c85ad8e2-f639-400d-88cd-b7d7907c9d1f] - Node c85ad8e2-f639-400d-88cd-b7d7907c9d1f[c85ad8e2-f639-400d-88cd-b7d7907c9d1f] monitor closed 
[INFO ] 2024-03-27 13:09:04.091 - [任务 21(100)][c85ad8e2-f639-400d-88cd-b7d7907c9d1f] - Node c85ad8e2-f639-400d-88cd-b7d7907c9d1f[c85ad8e2-f639-400d-88cd-b7d7907c9d1f] close complete, cost 4 ms 
[INFO ] 2024-03-27 13:09:04.094 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-858d6ecb-e150-4c33-9f04-48a888c95d13 
[INFO ] 2024-03-27 13:09:04.094 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-858d6ecb-e150-4c33-9f04-48a888c95d13 
[INFO ] 2024-03-27 13:09:04.095 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 13:09:04.096 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 13:09:04.096 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 13:09:04.097 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 15 ms 
[INFO ] 2024-03-27 13:09:04.100 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-c85ad8e2-f639-400d-88cd-b7d7907c9d1f complete, cost 606ms 
[INFO ] 2024-03-27 13:09:06.004 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:06.006 - [任务 21(100)][9071043e-a8fc-49bf-b66f-d1038d269b25] - Node 9071043e-a8fc-49bf-b66f-d1038d269b25[9071043e-a8fc-49bf-b66f-d1038d269b25] start preload schema,table counts: 0 
[INFO ] 2024-03-27 13:09:06.006 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:06.006 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:06.006 - [任务 21(100)][9071043e-a8fc-49bf-b66f-d1038d269b25] - Node 9071043e-a8fc-49bf-b66f-d1038d269b25[9071043e-a8fc-49bf-b66f-d1038d269b25] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:06.006 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:06.307 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 13:09:06.317 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:06.317 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:06.317 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 13:09:06.317 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 13:09:06.318 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 14 ms 
[INFO ] 2024-03-27 13:09:06.538 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 13:09:06.545 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-4b469dac-d49d-4264-b2d1-a2c14d142edc 
[INFO ] 2024-03-27 13:09:06.546 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-4b469dac-d49d-4264-b2d1-a2c14d142edc 
[INFO ] 2024-03-27 13:09:06.547 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 13:09:06.547 - [任务 21(100)][9071043e-a8fc-49bf-b66f-d1038d269b25] - Node 9071043e-a8fc-49bf-b66f-d1038d269b25[9071043e-a8fc-49bf-b66f-d1038d269b25] running status set to false 
[INFO ] 2024-03-27 13:09:06.547 - [任务 21(100)][9071043e-a8fc-49bf-b66f-d1038d269b25] - Node 9071043e-a8fc-49bf-b66f-d1038d269b25[9071043e-a8fc-49bf-b66f-d1038d269b25] schema data cleaned 
[INFO ] 2024-03-27 13:09:06.547 - [任务 21(100)][9071043e-a8fc-49bf-b66f-d1038d269b25] - Node 9071043e-a8fc-49bf-b66f-d1038d269b25[9071043e-a8fc-49bf-b66f-d1038d269b25] monitor closed 
[INFO ] 2024-03-27 13:09:06.547 - [任务 21(100)][9071043e-a8fc-49bf-b66f-d1038d269b25] - Node 9071043e-a8fc-49bf-b66f-d1038d269b25[9071043e-a8fc-49bf-b66f-d1038d269b25] close complete, cost 0 ms 
[INFO ] 2024-03-27 13:09:06.547 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 13:09:06.548 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 13:09:06.548 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 9 ms 
[INFO ] 2024-03-27 13:09:06.548 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-9071043e-a8fc-49bf-b66f-d1038d269b25 complete, cost 683ms 
[INFO ] 2024-03-27 13:09:33.695 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:33.697 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:33.698 - [任务 21(100)][8067b742-ba37-478d-8392-8a69e8904ff4] - Node 8067b742-ba37-478d-8392-8a69e8904ff4[8067b742-ba37-478d-8392-8a69e8904ff4] start preload schema,table counts: 0 
[INFO ] 2024-03-27 13:09:33.699 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:33.701 - [任务 21(100)][8067b742-ba37-478d-8392-8a69e8904ff4] - Node 8067b742-ba37-478d-8392-8a69e8904ff4[8067b742-ba37-478d-8392-8a69e8904ff4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:33.701 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:33.948 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 13:09:33.955 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:33.955 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:33.955 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 13:09:33.955 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 13:09:33.955 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 8 ms 
[INFO ] 2024-03-27 13:09:34.182 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 13:09:34.198 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-b912d638-d728-45a2-97b1-d9ee61f0fa1a 
[INFO ] 2024-03-27 13:09:34.198 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-b912d638-d728-45a2-97b1-d9ee61f0fa1a 
[INFO ] 2024-03-27 13:09:34.199 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 13:09:34.202 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 13:09:34.206 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 13:09:34.207 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 21 ms 
[INFO ] 2024-03-27 13:09:34.240 - [任务 21(100)][8067b742-ba37-478d-8392-8a69e8904ff4] - Node 8067b742-ba37-478d-8392-8a69e8904ff4[8067b742-ba37-478d-8392-8a69e8904ff4] running status set to false 
[INFO ] 2024-03-27 13:09:34.241 - [任务 21(100)][8067b742-ba37-478d-8392-8a69e8904ff4] - Node 8067b742-ba37-478d-8392-8a69e8904ff4[8067b742-ba37-478d-8392-8a69e8904ff4] schema data cleaned 
[INFO ] 2024-03-27 13:09:34.241 - [任务 21(100)][8067b742-ba37-478d-8392-8a69e8904ff4] - Node 8067b742-ba37-478d-8392-8a69e8904ff4[8067b742-ba37-478d-8392-8a69e8904ff4] monitor closed 
[INFO ] 2024-03-27 13:09:34.242 - [任务 21(100)][8067b742-ba37-478d-8392-8a69e8904ff4] - Node 8067b742-ba37-478d-8392-8a69e8904ff4[8067b742-ba37-478d-8392-8a69e8904ff4] close complete, cost 1 ms 
[INFO ] 2024-03-27 13:09:34.245 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-8067b742-ba37-478d-8392-8a69e8904ff4 complete, cost 609ms 
[INFO ] 2024-03-27 13:09:43.919 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:43.920 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:43.921 - [任务 21(100)][f946d78f-9f31-49f1-8dcc-06903fa91db8] - Node f946d78f-9f31-49f1-8dcc-06903fa91db8[f946d78f-9f31-49f1-8dcc-06903fa91db8] start preload schema,table counts: 0 
[INFO ] 2024-03-27 13:09:43.921 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:43.921 - [任务 21(100)][f946d78f-9f31-49f1-8dcc-06903fa91db8] - Node f946d78f-9f31-49f1-8dcc-06903fa91db8[f946d78f-9f31-49f1-8dcc-06903fa91db8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:43.921 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:44.193 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 13:09:44.202 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:44.203 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:44.203 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 13:09:44.204 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 13:09:44.204 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 13 ms 
[INFO ] 2024-03-27 13:09:44.427 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 13:09:44.430 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-0049e9ae-c585-4378-96ba-b860cccf61a2 
[INFO ] 2024-03-27 13:09:44.430 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-0049e9ae-c585-4378-96ba-b860cccf61a2 
[INFO ] 2024-03-27 13:09:44.430 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 13:09:44.432 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 13:09:44.433 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 13:09:44.433 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 5 ms 
[INFO ] 2024-03-27 13:09:44.463 - [任务 21(100)][f946d78f-9f31-49f1-8dcc-06903fa91db8] - Node f946d78f-9f31-49f1-8dcc-06903fa91db8[f946d78f-9f31-49f1-8dcc-06903fa91db8] running status set to false 
[INFO ] 2024-03-27 13:09:44.466 - [任务 21(100)][f946d78f-9f31-49f1-8dcc-06903fa91db8] - Node f946d78f-9f31-49f1-8dcc-06903fa91db8[f946d78f-9f31-49f1-8dcc-06903fa91db8] schema data cleaned 
[INFO ] 2024-03-27 13:09:44.466 - [任务 21(100)][f946d78f-9f31-49f1-8dcc-06903fa91db8] - Node f946d78f-9f31-49f1-8dcc-06903fa91db8[f946d78f-9f31-49f1-8dcc-06903fa91db8] monitor closed 
[INFO ] 2024-03-27 13:09:44.467 - [任务 21(100)][f946d78f-9f31-49f1-8dcc-06903fa91db8] - Node f946d78f-9f31-49f1-8dcc-06903fa91db8[f946d78f-9f31-49f1-8dcc-06903fa91db8] close complete, cost 2 ms 
[INFO ] 2024-03-27 13:09:44.468 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-f946d78f-9f31-49f1-8dcc-06903fa91db8 complete, cost 593ms 
[INFO ] 2024-03-27 13:09:45.942 - [任务 21(100)][f8a93043-87cd-46a7-ac7c-8a61bca0b1c6] - Node f8a93043-87cd-46a7-ac7c-8a61bca0b1c6[f8a93043-87cd-46a7-ac7c-8a61bca0b1c6] start preload schema,table counts: 0 
[INFO ] 2024-03-27 13:09:45.944 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:45.945 - [任务 21(100)][f8a93043-87cd-46a7-ac7c-8a61bca0b1c6] - Node f8a93043-87cd-46a7-ac7c-8a61bca0b1c6[f8a93043-87cd-46a7-ac7c-8a61bca0b1c6] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:45.945 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:45.946 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:45.946 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:46.033 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:46.034 - [任务 21(100)][fe9ecef2-17dc-4d9e-848d-87428efc777a] - Node fe9ecef2-17dc-4d9e-848d-87428efc777a[fe9ecef2-17dc-4d9e-848d-87428efc777a] start preload schema,table counts: 0 
[INFO ] 2024-03-27 13:09:46.035 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:46.035 - [任务 21(100)][fe9ecef2-17dc-4d9e-848d-87428efc777a] - Node fe9ecef2-17dc-4d9e-848d-87428efc777a[fe9ecef2-17dc-4d9e-848d-87428efc777a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:46.035 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:46.035 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 13:09:46.258 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 13:09:46.268 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:46.269 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:46.269 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 13:09:46.269 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 13:09:46.269 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 15 ms 
[INFO ] 2024-03-27 13:09:46.740 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 13:09:46.761 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-a6d1ba8b-705a-42cd-9782-87a3ceb82636 
[INFO ] 2024-03-27 13:09:46.762 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-a6d1ba8b-705a-42cd-9782-87a3ceb82636 
[INFO ] 2024-03-27 13:09:46.762 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 13:09:46.764 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 13:09:46.768 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 13:09:46.769 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 25 ms 
[INFO ] 2024-03-27 13:09:46.787 - [任务 21(100)][f8a93043-87cd-46a7-ac7c-8a61bca0b1c6] - Node f8a93043-87cd-46a7-ac7c-8a61bca0b1c6[f8a93043-87cd-46a7-ac7c-8a61bca0b1c6] running status set to false 
[INFO ] 2024-03-27 13:09:46.787 - [任务 21(100)][f8a93043-87cd-46a7-ac7c-8a61bca0b1c6] - Node f8a93043-87cd-46a7-ac7c-8a61bca0b1c6[f8a93043-87cd-46a7-ac7c-8a61bca0b1c6] schema data cleaned 
[INFO ] 2024-03-27 13:09:46.787 - [任务 21(100)][f8a93043-87cd-46a7-ac7c-8a61bca0b1c6] - Node f8a93043-87cd-46a7-ac7c-8a61bca0b1c6[f8a93043-87cd-46a7-ac7c-8a61bca0b1c6] monitor closed 
[INFO ] 2024-03-27 13:09:46.787 - [任务 21(100)][f8a93043-87cd-46a7-ac7c-8a61bca0b1c6] - Node f8a93043-87cd-46a7-ac7c-8a61bca0b1c6[f8a93043-87cd-46a7-ac7c-8a61bca0b1c6] close complete, cost 1 ms 
[INFO ] 2024-03-27 13:09:46.795 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-f8a93043-87cd-46a7-ac7c-8a61bca0b1c6 complete, cost 962ms 
[INFO ] 2024-03-27 13:09:46.909 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:46.911 - [任务 21(100)][b5e05543-322f-453c-bb3a-9464e916f6b8] - Node b5e05543-322f-453c-bb3a-9464e916f6b8[b5e05543-322f-453c-bb3a-9464e916f6b8] start preload schema,table counts: 0 
[INFO ] 2024-03-27 13:09:46.911 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:46.911 - [任务 21(100)][b5e05543-322f-453c-bb3a-9464e916f6b8] - Node b5e05543-322f-453c-bb3a-9464e916f6b8[b5e05543-322f-453c-bb3a-9464e916f6b8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:46.912 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 13:09:46.912 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:46.994 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 13:09:47.035 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 13:09:47.047 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-87ed64c0-5574-43e2-a831-f60be8c57150 
[INFO ] 2024-03-27 13:09:47.048 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-87ed64c0-5574-43e2-a831-f60be8c57150 
[INFO ] 2024-03-27 13:09:47.048 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 13:09:47.049 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 13:09:47.049 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 13:09:47.051 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 17 ms 
[INFO ] 2024-03-27 13:09:47.061 - [任务 21(100)][fe9ecef2-17dc-4d9e-848d-87428efc777a] - Node fe9ecef2-17dc-4d9e-848d-87428efc777a[fe9ecef2-17dc-4d9e-848d-87428efc777a] running status set to false 
[INFO ] 2024-03-27 13:09:47.061 - [任务 21(100)][fe9ecef2-17dc-4d9e-848d-87428efc777a] - Node fe9ecef2-17dc-4d9e-848d-87428efc777a[fe9ecef2-17dc-4d9e-848d-87428efc777a] schema data cleaned 
[INFO ] 2024-03-27 13:09:47.061 - [任务 21(100)][fe9ecef2-17dc-4d9e-848d-87428efc777a] - Node fe9ecef2-17dc-4d9e-848d-87428efc777a[fe9ecef2-17dc-4d9e-848d-87428efc777a] monitor closed 
[INFO ] 2024-03-27 13:09:47.063 - [任务 21(100)][fe9ecef2-17dc-4d9e-848d-87428efc777a] - Node fe9ecef2-17dc-4d9e-848d-87428efc777a[fe9ecef2-17dc-4d9e-848d-87428efc777a] close complete, cost 0 ms 
[INFO ] 2024-03-27 13:09:47.064 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:47.065 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:47.065 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 13:09:47.065 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 13:09:47.065 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 79 ms 
[INFO ] 2024-03-27 13:09:47.069 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-fe9ecef2-17dc-4d9e-848d-87428efc777a complete, cost 1092ms 
[INFO ] 2024-03-27 13:09:47.224 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 13:09:47.227 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:47.227 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:47.227 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 13:09:47.227 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 13:09:47.227 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 10 ms 
[INFO ] 2024-03-27 13:09:47.457 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 13:09:47.461 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-1356bbba-009d-46c0-b001-0cd645e3f063 
[INFO ] 2024-03-27 13:09:47.462 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-1356bbba-009d-46c0-b001-0cd645e3f063 
[INFO ] 2024-03-27 13:09:47.462 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 13:09:47.464 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 13:09:47.464 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 13:09:47.464 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 6 ms 
[INFO ] 2024-03-27 13:09:47.476 - [任务 21(100)][b5e05543-322f-453c-bb3a-9464e916f6b8] - Node b5e05543-322f-453c-bb3a-9464e916f6b8[b5e05543-322f-453c-bb3a-9464e916f6b8] running status set to false 
[INFO ] 2024-03-27 13:09:47.477 - [任务 21(100)][b5e05543-322f-453c-bb3a-9464e916f6b8] - Node b5e05543-322f-453c-bb3a-9464e916f6b8[b5e05543-322f-453c-bb3a-9464e916f6b8] schema data cleaned 
[INFO ] 2024-03-27 13:09:47.477 - [任务 21(100)][b5e05543-322f-453c-bb3a-9464e916f6b8] - Node b5e05543-322f-453c-bb3a-9464e916f6b8[b5e05543-322f-453c-bb3a-9464e916f6b8] monitor closed 
[INFO ] 2024-03-27 13:09:47.477 - [任务 21(100)][b5e05543-322f-453c-bb3a-9464e916f6b8] - Node b5e05543-322f-453c-bb3a-9464e916f6b8[b5e05543-322f-453c-bb3a-9464e916f6b8] close complete, cost 0 ms 
[INFO ] 2024-03-27 13:09:47.480 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-b5e05543-322f-453c-bb3a-9464e916f6b8 complete, cost 647ms 
[INFO ] 2024-03-27 13:09:49.030 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:49.030 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:49.030 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:49.031 - [任务 21(100)][a539a110-1151-4e5a-aa2d-ab9b4d26010b] - Node a539a110-1151-4e5a-aa2d-ab9b4d26010b[a539a110-1151-4e5a-aa2d-ab9b4d26010b] start preload schema,table counts: 0 
[INFO ] 2024-03-27 13:09:49.031 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 13:09:49.031 - [任务 21(100)][a539a110-1151-4e5a-aa2d-ab9b4d26010b] - Node a539a110-1151-4e5a-aa2d-ab9b4d26010b[a539a110-1151-4e5a-aa2d-ab9b4d26010b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:49.324 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 13:09:49.328 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:49.328 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:49.328 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 13:09:49.328 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 13:09:49.329 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 20 ms 
[INFO ] 2024-03-27 13:09:49.546 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 13:09:49.551 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-3dc0ff2f-44a5-4ff4-bdf7-b2e81136791f 
[INFO ] 2024-03-27 13:09:49.552 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-3dc0ff2f-44a5-4ff4-bdf7-b2e81136791f 
[INFO ] 2024-03-27 13:09:49.552 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 13:09:49.553 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 13:09:49.553 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 13:09:49.553 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 7 ms 
[INFO ] 2024-03-27 13:09:49.573 - [任务 21(100)][a539a110-1151-4e5a-aa2d-ab9b4d26010b] - Node a539a110-1151-4e5a-aa2d-ab9b4d26010b[a539a110-1151-4e5a-aa2d-ab9b4d26010b] running status set to false 
[INFO ] 2024-03-27 13:09:49.574 - [任务 21(100)][a539a110-1151-4e5a-aa2d-ab9b4d26010b] - Node a539a110-1151-4e5a-aa2d-ab9b4d26010b[a539a110-1151-4e5a-aa2d-ab9b4d26010b] schema data cleaned 
[INFO ] 2024-03-27 13:09:49.574 - [任务 21(100)][a539a110-1151-4e5a-aa2d-ab9b4d26010b] - Node a539a110-1151-4e5a-aa2d-ab9b4d26010b[a539a110-1151-4e5a-aa2d-ab9b4d26010b] monitor closed 
[INFO ] 2024-03-27 13:09:49.574 - [任务 21(100)][a539a110-1151-4e5a-aa2d-ab9b4d26010b] - Node a539a110-1151-4e5a-aa2d-ab9b4d26010b[a539a110-1151-4e5a-aa2d-ab9b4d26010b] close complete, cost 0 ms 
[INFO ] 2024-03-27 13:09:49.579 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-a539a110-1151-4e5a-aa2d-ab9b4d26010b complete, cost 595ms 
[INFO ] 2024-03-27 13:09:53.125 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:53.127 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:53.128 - [任务 21(100)][8224cfa4-69a3-43f9-83bc-bb6e42259a4f] - Node 8224cfa4-69a3-43f9-83bc-bb6e42259a4f[8224cfa4-69a3-43f9-83bc-bb6e42259a4f] start preload schema,table counts: 0 
[INFO ] 2024-03-27 13:09:53.129 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:53.129 - [任务 21(100)][8224cfa4-69a3-43f9-83bc-bb6e42259a4f] - Node 8224cfa4-69a3-43f9-83bc-bb6e42259a4f[8224cfa4-69a3-43f9-83bc-bb6e42259a4f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:53.129 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:53.424 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 13:09:53.430 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:53.430 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:53.431 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 13:09:53.431 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 13:09:53.432 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 17 ms 
[INFO ] 2024-03-27 13:09:53.645 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 13:09:53.648 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-e5b087d7-0416-4a66-bdf5-a49da75521d9 
[INFO ] 2024-03-27 13:09:53.649 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-e5b087d7-0416-4a66-bdf5-a49da75521d9 
[INFO ] 2024-03-27 13:09:53.649 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 13:09:53.650 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 13:09:53.651 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 13:09:53.651 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 6 ms 
[INFO ] 2024-03-27 13:09:53.672 - [任务 21(100)][8224cfa4-69a3-43f9-83bc-bb6e42259a4f] - Node 8224cfa4-69a3-43f9-83bc-bb6e42259a4f[8224cfa4-69a3-43f9-83bc-bb6e42259a4f] running status set to false 
[INFO ] 2024-03-27 13:09:53.672 - [任务 21(100)][8224cfa4-69a3-43f9-83bc-bb6e42259a4f] - Node 8224cfa4-69a3-43f9-83bc-bb6e42259a4f[8224cfa4-69a3-43f9-83bc-bb6e42259a4f] schema data cleaned 
[INFO ] 2024-03-27 13:09:53.672 - [任务 21(100)][8224cfa4-69a3-43f9-83bc-bb6e42259a4f] - Node 8224cfa4-69a3-43f9-83bc-bb6e42259a4f[8224cfa4-69a3-43f9-83bc-bb6e42259a4f] monitor closed 
[INFO ] 2024-03-27 13:09:53.672 - [任务 21(100)][8224cfa4-69a3-43f9-83bc-bb6e42259a4f] - Node 8224cfa4-69a3-43f9-83bc-bb6e42259a4f[8224cfa4-69a3-43f9-83bc-bb6e42259a4f] close complete, cost 0 ms 
[INFO ] 2024-03-27 13:09:53.675 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-8224cfa4-69a3-43f9-83bc-bb6e42259a4f complete, cost 669ms 
[INFO ] 2024-03-27 13:09:55.827 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:55.829 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:09:55.829 - [任务 21(100)][a89c04c5-00f7-4f90-a32d-0f20dda4c861] - Node a89c04c5-00f7-4f90-a32d-0f20dda4c861[a89c04c5-00f7-4f90-a32d-0f20dda4c861] start preload schema,table counts: 0 
[INFO ] 2024-03-27 13:09:55.829 - [任务 21(100)][a89c04c5-00f7-4f90-a32d-0f20dda4c861] - Node a89c04c5-00f7-4f90-a32d-0f20dda4c861[a89c04c5-00f7-4f90-a32d-0f20dda4c861] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:09:55.829 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 13:09:55.829 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 13:09:56.143 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 13:09:56.150 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:56.150 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:09:56.151 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 13:09:56.151 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 13:09:56.152 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 15 ms 
[INFO ] 2024-03-27 13:09:56.369 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 13:09:56.373 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-f00901d5-bf54-4b6f-b87e-a79ba29bc6a5 
[INFO ] 2024-03-27 13:09:56.373 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-f00901d5-bf54-4b6f-b87e-a79ba29bc6a5 
[INFO ] 2024-03-27 13:09:56.374 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 13:09:56.374 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 13:09:56.374 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 13:09:56.374 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 5 ms 
[INFO ] 2024-03-27 13:09:56.399 - [任务 21(100)][a89c04c5-00f7-4f90-a32d-0f20dda4c861] - Node a89c04c5-00f7-4f90-a32d-0f20dda4c861[a89c04c5-00f7-4f90-a32d-0f20dda4c861] running status set to false 
[INFO ] 2024-03-27 13:09:56.409 - [任务 21(100)][a89c04c5-00f7-4f90-a32d-0f20dda4c861] - Node a89c04c5-00f7-4f90-a32d-0f20dda4c861[a89c04c5-00f7-4f90-a32d-0f20dda4c861] schema data cleaned 
[INFO ] 2024-03-27 13:09:56.413 - [任务 21(100)][a89c04c5-00f7-4f90-a32d-0f20dda4c861] - Node a89c04c5-00f7-4f90-a32d-0f20dda4c861[a89c04c5-00f7-4f90-a32d-0f20dda4c861] monitor closed 
[INFO ] 2024-03-27 13:09:56.415 - [任务 21(100)][a89c04c5-00f7-4f90-a32d-0f20dda4c861] - Node a89c04c5-00f7-4f90-a32d-0f20dda4c861[a89c04c5-00f7-4f90-a32d-0f20dda4c861] close complete, cost 0 ms 
[INFO ] 2024-03-27 13:09:56.415 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-a89c04c5-00f7-4f90-a32d-0f20dda4c861 complete, cost 713ms 
[INFO ] 2024-03-27 13:10:01.089 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:10:01.089 - [任务 21(100)][efee7680-b182-48d4-acc4-dd2f6243f330] - Node efee7680-b182-48d4-acc4-dd2f6243f330[efee7680-b182-48d4-acc4-dd2f6243f330] start preload schema,table counts: 0 
[INFO ] 2024-03-27 13:10:01.090 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:10:01.090 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 13:10:01.090 - [任务 21(100)][efee7680-b182-48d4-acc4-dd2f6243f330] - Node efee7680-b182-48d4-acc4-dd2f6243f330[efee7680-b182-48d4-acc4-dd2f6243f330] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:10:01.090 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:10:01.336 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 13:10:01.349 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:10:01.349 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:10:01.349 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 13:10:01.349 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 13:10:01.349 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 14 ms 
[INFO ] 2024-03-27 13:10:01.700 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 13:10:01.707 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-1616965f-4aec-441c-b15b-5f10f7570664 
[INFO ] 2024-03-27 13:10:01.707 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-1616965f-4aec-441c-b15b-5f10f7570664 
[INFO ] 2024-03-27 13:10:01.707 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 13:10:01.708 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 13:10:01.709 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 13:10:01.709 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 19 ms 
[INFO ] 2024-03-27 13:10:01.733 - [任务 21(100)][efee7680-b182-48d4-acc4-dd2f6243f330] - Node efee7680-b182-48d4-acc4-dd2f6243f330[efee7680-b182-48d4-acc4-dd2f6243f330] running status set to false 
[INFO ] 2024-03-27 13:10:01.734 - [任务 21(100)][efee7680-b182-48d4-acc4-dd2f6243f330] - Node efee7680-b182-48d4-acc4-dd2f6243f330[efee7680-b182-48d4-acc4-dd2f6243f330] schema data cleaned 
[INFO ] 2024-03-27 13:10:01.734 - [任务 21(100)][efee7680-b182-48d4-acc4-dd2f6243f330] - Node efee7680-b182-48d4-acc4-dd2f6243f330[efee7680-b182-48d4-acc4-dd2f6243f330] monitor closed 
[INFO ] 2024-03-27 13:10:01.735 - [任务 21(100)][efee7680-b182-48d4-acc4-dd2f6243f330] - Node efee7680-b182-48d4-acc4-dd2f6243f330[efee7680-b182-48d4-acc4-dd2f6243f330] close complete, cost 1 ms 
[INFO ] 2024-03-27 13:10:01.736 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-efee7680-b182-48d4-acc4-dd2f6243f330 complete, cost 686ms 
[INFO ] 2024-03-27 13:10:25.554 - [任务 21(100)][de9a26d6-1233-4c36-9f27-b2853ecf8fc2] - Node de9a26d6-1233-4c36-9f27-b2853ecf8fc2[de9a26d6-1233-4c36-9f27-b2853ecf8fc2] start preload schema,table counts: 0 
[INFO ] 2024-03-27 13:10:25.554 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:10:25.555 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:10:25.555 - [任务 21(100)][de9a26d6-1233-4c36-9f27-b2853ecf8fc2] - Node de9a26d6-1233-4c36-9f27-b2853ecf8fc2[de9a26d6-1233-4c36-9f27-b2853ecf8fc2] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:10:25.555 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:10:25.555 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:10:25.821 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 13:10:25.829 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:10:25.829 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:10:25.829 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 13:10:25.829 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 13:10:25.829 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 9 ms 
[INFO ] 2024-03-27 13:10:26.060 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 13:10:26.069 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-ed735f1c-1f93-4eec-a0a0-3d75bfea0422 
[INFO ] 2024-03-27 13:10:26.070 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-ed735f1c-1f93-4eec-a0a0-3d75bfea0422 
[INFO ] 2024-03-27 13:10:26.070 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 13:10:26.070 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 13:10:26.070 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 13:10:26.070 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 10 ms 
[INFO ] 2024-03-27 13:10:26.094 - [任务 21(100)][de9a26d6-1233-4c36-9f27-b2853ecf8fc2] - Node de9a26d6-1233-4c36-9f27-b2853ecf8fc2[de9a26d6-1233-4c36-9f27-b2853ecf8fc2] running status set to false 
[INFO ] 2024-03-27 13:10:26.095 - [任务 21(100)][de9a26d6-1233-4c36-9f27-b2853ecf8fc2] - Node de9a26d6-1233-4c36-9f27-b2853ecf8fc2[de9a26d6-1233-4c36-9f27-b2853ecf8fc2] schema data cleaned 
[INFO ] 2024-03-27 13:10:26.095 - [任务 21(100)][de9a26d6-1233-4c36-9f27-b2853ecf8fc2] - Node de9a26d6-1233-4c36-9f27-b2853ecf8fc2[de9a26d6-1233-4c36-9f27-b2853ecf8fc2] monitor closed 
[INFO ] 2024-03-27 13:10:26.095 - [任务 21(100)][de9a26d6-1233-4c36-9f27-b2853ecf8fc2] - Node de9a26d6-1233-4c36-9f27-b2853ecf8fc2[de9a26d6-1233-4c36-9f27-b2853ecf8fc2] close complete, cost 1 ms 
[INFO ] 2024-03-27 13:10:26.099 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-de9a26d6-1233-4c36-9f27-b2853ecf8fc2 complete, cost 605ms 
[INFO ] 2024-03-27 13:10:28.623 - [任务 21(100)][bc7b27ef-3b7f-461a-85ad-931b3aad2df9] - Node bc7b27ef-3b7f-461a-85ad-931b3aad2df9[bc7b27ef-3b7f-461a-85ad-931b3aad2df9] start preload schema,table counts: 0 
[INFO ] 2024-03-27 13:10:28.624 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:10:28.624 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 13:10:28.624 - [任务 21(100)][bc7b27ef-3b7f-461a-85ad-931b3aad2df9] - Node bc7b27ef-3b7f-461a-85ad-931b3aad2df9[bc7b27ef-3b7f-461a-85ad-931b3aad2df9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 13:10:28.624 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 13:10:28.624 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 13:10:28.903 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 13:10:28.913 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:10:28.914 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 13:10:28.914 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 13:10:28.914 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 13:10:28.914 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 11 ms 
[INFO ] 2024-03-27 13:10:29.139 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 13:10:29.143 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-c8322f87-d202-4876-b4cc-74f144431152 
[INFO ] 2024-03-27 13:10:29.143 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-c8322f87-d202-4876-b4cc-74f144431152 
[INFO ] 2024-03-27 13:10:29.144 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 13:10:29.145 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 13:10:29.145 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 13:10:29.146 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 6 ms 
[INFO ] 2024-03-27 13:10:29.153 - [任务 21(100)][bc7b27ef-3b7f-461a-85ad-931b3aad2df9] - Node bc7b27ef-3b7f-461a-85ad-931b3aad2df9[bc7b27ef-3b7f-461a-85ad-931b3aad2df9] running status set to false 
[INFO ] 2024-03-27 13:10:29.153 - [任务 21(100)][bc7b27ef-3b7f-461a-85ad-931b3aad2df9] - Node bc7b27ef-3b7f-461a-85ad-931b3aad2df9[bc7b27ef-3b7f-461a-85ad-931b3aad2df9] schema data cleaned 
[INFO ] 2024-03-27 13:10:29.153 - [任务 21(100)][bc7b27ef-3b7f-461a-85ad-931b3aad2df9] - Node bc7b27ef-3b7f-461a-85ad-931b3aad2df9[bc7b27ef-3b7f-461a-85ad-931b3aad2df9] monitor closed 
[INFO ] 2024-03-27 13:10:29.153 - [任务 21(100)][bc7b27ef-3b7f-461a-85ad-931b3aad2df9] - Node bc7b27ef-3b7f-461a-85ad-931b3aad2df9[bc7b27ef-3b7f-461a-85ad-931b3aad2df9] close complete, cost 0 ms 
[INFO ] 2024-03-27 13:10:29.161 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-bc7b27ef-3b7f-461a-85ad-931b3aad2df9 complete, cost 604ms 
[INFO ] 2024-03-27 14:30:16.115 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:30:16.119 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 14:30:16.120 - [任务 21(100)][017c5142-5e61-44f6-8612-f5849bca6393] - Node 017c5142-5e61-44f6-8612-f5849bca6393[017c5142-5e61-44f6-8612-f5849bca6393] start preload schema,table counts: 0 
[INFO ] 2024-03-27 14:30:16.120 - [任务 21(100)][017c5142-5e61-44f6-8612-f5849bca6393] - Node 017c5142-5e61-44f6-8612-f5849bca6393[017c5142-5e61-44f6-8612-f5849bca6393] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:30:16.120 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 14:30:16.120 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 14:30:16.416 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 14:30:16.426 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 14:30:16.426 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 14:30:16.426 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 14:30:16.426 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 14:30:16.427 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 19 ms 
[INFO ] 2024-03-27 14:30:16.642 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 14:30:16.647 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-e7c31bab-db64-4a87-bdd0-1495b46670f7 
[INFO ] 2024-03-27 14:30:16.647 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-e7c31bab-db64-4a87-bdd0-1495b46670f7 
[INFO ] 2024-03-27 14:30:16.647 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 14:30:16.648 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 14:30:16.648 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 14:30:16.648 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 6 ms 
[INFO ] 2024-03-27 14:30:16.661 - [任务 21(100)][017c5142-5e61-44f6-8612-f5849bca6393] - Node 017c5142-5e61-44f6-8612-f5849bca6393[017c5142-5e61-44f6-8612-f5849bca6393] running status set to false 
[INFO ] 2024-03-27 14:30:16.661 - [任务 21(100)][017c5142-5e61-44f6-8612-f5849bca6393] - Node 017c5142-5e61-44f6-8612-f5849bca6393[017c5142-5e61-44f6-8612-f5849bca6393] schema data cleaned 
[INFO ] 2024-03-27 14:30:16.661 - [任务 21(100)][017c5142-5e61-44f6-8612-f5849bca6393] - Node 017c5142-5e61-44f6-8612-f5849bca6393[017c5142-5e61-44f6-8612-f5849bca6393] monitor closed 
[INFO ] 2024-03-27 14:30:16.661 - [任务 21(100)][017c5142-5e61-44f6-8612-f5849bca6393] - Node 017c5142-5e61-44f6-8612-f5849bca6393[017c5142-5e61-44f6-8612-f5849bca6393] close complete, cost 0 ms 
[INFO ] 2024-03-27 14:30:16.665 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-017c5142-5e61-44f6-8612-f5849bca6393 complete, cost 630ms 
[INFO ] 2024-03-27 16:09:12.265 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:09:12.269 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:09:12.270 - [任务 21(100)][44597c8a-a671-4490-9747-81c071c47e19] - Node 44597c8a-a671-4490-9747-81c071c47e19[44597c8a-a671-4490-9747-81c071c47e19] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:09:12.270 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:09:12.270 - [任务 21(100)][44597c8a-a671-4490-9747-81c071c47e19] - Node 44597c8a-a671-4490-9747-81c071c47e19[44597c8a-a671-4490-9747-81c071c47e19] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:09:12.270 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 5 ms 
[INFO ] 2024-03-27 16:09:12.270 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 16:09:12.270 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 16:09:12.270 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 16:09:12.270 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 16:09:12.270 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 16:09:12.270 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 19 ms 
[INFO ] 2024-03-27 16:09:12.270 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 16:09:12.270 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-0705b477-81ee-438a-9150-b605ada8109a 
[INFO ] 2024-03-27 16:09:12.270 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-0705b477-81ee-438a-9150-b605ada8109a 
[INFO ] 2024-03-27 16:09:12.270 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:09:12.270 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 5 ms 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][44597c8a-a671-4490-9747-81c071c47e19] - Node 44597c8a-a671-4490-9747-81c071c47e19[44597c8a-a671-4490-9747-81c071c47e19] running status set to false 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][44597c8a-a671-4490-9747-81c071c47e19] - Node 44597c8a-a671-4490-9747-81c071c47e19[44597c8a-a671-4490-9747-81c071c47e19] schema data cleaned 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][44597c8a-a671-4490-9747-81c071c47e19] - Node 44597c8a-a671-4490-9747-81c071c47e19[44597c8a-a671-4490-9747-81c071c47e19] monitor closed 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][44597c8a-a671-4490-9747-81c071c47e19] - Node 44597c8a-a671-4490-9747-81c071c47e19[44597c8a-a671-4490-9747-81c071c47e19] close complete, cost 0 ms 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-44597c8a-a671-4490-9747-81c071c47e19 complete, cost 659ms 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][4b4fdb0e-c2e9-4061-b1fd-11b90408fb52] - Node 4b4fdb0e-c2e9-4061-b1fd-11b90408fb52[4b4fdb0e-c2e9-4061-b1fd-11b90408fb52] start preload schema,table counts: 0 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][4b4fdb0e-c2e9-4061-b1fd-11b90408fb52] - Node 4b4fdb0e-c2e9-4061-b1fd-11b90408fb52[4b4fdb0e-c2e9-4061-b1fd-11b90408fb52] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 16 ms 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 16:09:12.271 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-145c33f7-e896-47c3-b94a-b107c6b8d1ca 
[INFO ] 2024-03-27 16:09:12.272 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-145c33f7-e896-47c3-b94a-b107c6b8d1ca 
[INFO ] 2024-03-27 16:09:12.272 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 16:09:12.272 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 16:09:12.272 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 16:09:12.272 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 13 ms 
[INFO ] 2024-03-27 16:09:12.272 - [任务 21(100)][4b4fdb0e-c2e9-4061-b1fd-11b90408fb52] - Node 4b4fdb0e-c2e9-4061-b1fd-11b90408fb52[4b4fdb0e-c2e9-4061-b1fd-11b90408fb52] running status set to false 
[INFO ] 2024-03-27 16:09:12.272 - [任务 21(100)][4b4fdb0e-c2e9-4061-b1fd-11b90408fb52] - Node 4b4fdb0e-c2e9-4061-b1fd-11b90408fb52[4b4fdb0e-c2e9-4061-b1fd-11b90408fb52] schema data cleaned 
[INFO ] 2024-03-27 16:09:12.272 - [任务 21(100)][4b4fdb0e-c2e9-4061-b1fd-11b90408fb52] - Node 4b4fdb0e-c2e9-4061-b1fd-11b90408fb52[4b4fdb0e-c2e9-4061-b1fd-11b90408fb52] monitor closed 
[INFO ] 2024-03-27 16:09:12.272 - [任务 21(100)][4b4fdb0e-c2e9-4061-b1fd-11b90408fb52] - Node 4b4fdb0e-c2e9-4061-b1fd-11b90408fb52[4b4fdb0e-c2e9-4061-b1fd-11b90408fb52] close complete, cost 0 ms 
[INFO ] 2024-03-27 16:09:12.272 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-4b4fdb0e-c2e9-4061-b1fd-11b90408fb52 complete, cost 687ms 
[INFO ] 2024-03-27 18:28:53.270 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 18:28:53.273 - [任务 21(100)][f632710b-0f7c-4e0c-9858-1b8e116576c0] - Node f632710b-0f7c-4e0c-9858-1b8e116576c0[f632710b-0f7c-4e0c-9858-1b8e116576c0] start preload schema,table counts: 0 
[INFO ] 2024-03-27 18:28:53.274 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-27 18:28:53.274 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 18:28:53.274 - [任务 21(100)][f632710b-0f7c-4e0c-9858-1b8e116576c0] - Node f632710b-0f7c-4e0c-9858-1b8e116576c0[f632710b-0f7c-4e0c-9858-1b8e116576c0] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 18:28:53.276 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 18:28:53.277 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 18:28:53.279 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 18:28:53.279 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 18:28:53.280 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 18:28:53.280 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 18:28:53.280 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 21 ms 
[INFO ] 2024-03-27 18:28:53.280 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 18:28:53.280 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-a7a52bd3-5956-49ed-bd44-744e6e7c7a83 
[INFO ] 2024-03-27 18:28:53.280 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-a7a52bd3-5956-49ed-bd44-744e6e7c7a83 
[INFO ] 2024-03-27 18:28:53.280 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 18:28:53.280 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 18:28:53.281 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 18:28:53.281 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 11 ms 
[INFO ] 2024-03-27 18:28:53.281 - [任务 21(100)][f632710b-0f7c-4e0c-9858-1b8e116576c0] - Node f632710b-0f7c-4e0c-9858-1b8e116576c0[f632710b-0f7c-4e0c-9858-1b8e116576c0] running status set to false 
[INFO ] 2024-03-27 18:28:53.281 - [任务 21(100)][f632710b-0f7c-4e0c-9858-1b8e116576c0] - Node f632710b-0f7c-4e0c-9858-1b8e116576c0[f632710b-0f7c-4e0c-9858-1b8e116576c0] schema data cleaned 
[INFO ] 2024-03-27 18:28:53.281 - [任务 21(100)][f632710b-0f7c-4e0c-9858-1b8e116576c0] - Node f632710b-0f7c-4e0c-9858-1b8e116576c0[f632710b-0f7c-4e0c-9858-1b8e116576c0] monitor closed 
[INFO ] 2024-03-27 18:28:53.282 - [任务 21(100)][f632710b-0f7c-4e0c-9858-1b8e116576c0] - Node f632710b-0f7c-4e0c-9858-1b8e116576c0[f632710b-0f7c-4e0c-9858-1b8e116576c0] close complete, cost 2 ms 
[INFO ] 2024-03-27 18:28:53.282 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-f632710b-0f7c-4e0c-9858-1b8e116576c0 complete, cost 664ms 
[INFO ] 2024-03-27 18:44:58.945 - [任务 21(100)][401e7be9-5629-499b-9778-57cae2765949] - Node 401e7be9-5629-499b-9778-57cae2765949[401e7be9-5629-499b-9778-57cae2765949] start preload schema,table counts: 0 
[INFO ] 2024-03-27 18:44:58.947 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 18:44:58.947 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] start preload schema,table counts: 1 
[INFO ] 2024-03-27 18:44:58.947 - [任务 21(100)][401e7be9-5629-499b-9778-57cae2765949] - Node 401e7be9-5629-499b-9778-57cae2765949[401e7be9-5629-499b-9778-57cae2765949] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 18:44:58.947 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 18:44:58.947 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-27 18:44:59.254 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] running status set to false 
[INFO ] 2024-03-27 18:44:59.254 - [任务 21(100)][test9] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 18:44:59.255 - [任务 21(100)][test9] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f7ec5853-292f-49fc-9f1d-c37eaab5572d 
[INFO ] 2024-03-27 18:44:59.255 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] schema data cleaned 
[INFO ] 2024-03-27 18:44:59.257 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] monitor closed 
[INFO ] 2024-03-27 18:44:59.257 - [任务 21(100)][test9] - Node test9[f7ec5853-292f-49fc-9f1d-c37eaab5572d] close complete, cost 29 ms 
[INFO ] 2024-03-27 18:44:59.475 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] running status set to false 
[INFO ] 2024-03-27 18:44:59.486 - [任务 21(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-d55a51e0-904e-4dce-88e4-302ab2a865cd 
[INFO ] 2024-03-27 18:44:59.486 - [任务 21(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-d55a51e0-904e-4dce-88e4-302ab2a865cd 
[INFO ] 2024-03-27 18:44:59.489 - [任务 21(100)][增强JS] - [ScriptExecutorsManager-660391e28b5bca60f72ded3f-36f4920e-c770-457a-89bb-853f92de19fc-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 18:44:59.489 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] schema data cleaned 
[INFO ] 2024-03-27 18:44:59.489 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] monitor closed 
[INFO ] 2024-03-27 18:44:59.490 - [任务 21(100)][增强JS] - Node 增强JS[36f4920e-c770-457a-89bb-853f92de19fc] close complete, cost 20 ms 
[INFO ] 2024-03-27 18:44:59.563 - [任务 21(100)][401e7be9-5629-499b-9778-57cae2765949] - Node 401e7be9-5629-499b-9778-57cae2765949[401e7be9-5629-499b-9778-57cae2765949] running status set to false 
[INFO ] 2024-03-27 18:44:59.566 - [任务 21(100)][401e7be9-5629-499b-9778-57cae2765949] - Node 401e7be9-5629-499b-9778-57cae2765949[401e7be9-5629-499b-9778-57cae2765949] schema data cleaned 
[INFO ] 2024-03-27 18:44:59.566 - [任务 21(100)][401e7be9-5629-499b-9778-57cae2765949] - Node 401e7be9-5629-499b-9778-57cae2765949[401e7be9-5629-499b-9778-57cae2765949] monitor closed 
[INFO ] 2024-03-27 18:44:59.566 - [任务 21(100)][401e7be9-5629-499b-9778-57cae2765949] - Node 401e7be9-5629-499b-9778-57cae2765949[401e7be9-5629-499b-9778-57cae2765949] close complete, cost 6 ms 
[INFO ] 2024-03-27 18:44:59.775 - [任务 21(100)] - load tapTable task 660391e28b5bca60f72ded3f-401e7be9-5629-499b-9778-57cae2765949 complete, cost 669ms 
