[TRACE] 2025-03-04 20:08:04.969 - [任务 39] - Task initialization... 
[TRACE] 2025-03-04 20:08:04.971 - [任务 39] - Start task milestones: 67c6d8576ed4fc58ed9923cb(任务 39) 
[INFO ] 2025-03-04 20:08:05.176 - [任务 39] - Loading table structure completed 
[TRACE] 2025-03-04 20:08:05.230 - [任务 39] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-03-04 20:08:05.230 - [任务 39] - The engine receives 任务 39 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-04 20:08:05.271 - [任务 39] - Task started 
[TRACE] 2025-03-04 20:08:05.271 - [任务 39][Sybase-Wim] - Node Sybase-Wim[1742df4d-6e67-4553-99d0-a6b60243cef8] start preload schema,table counts: 4 
[TRACE] 2025-03-04 20:08:05.271 - [任务 39][Pg - Copy] - Node Pg - Copy[3d6414f6-5142-4608-b869-0b969abe08f6] start preload schema,table counts: 4 
[TRACE] 2025-03-04 20:08:05.271 - [任务 39][Sybase-Wim] - Node Sybase-Wim[1742df4d-6e67-4553-99d0-a6b60243cef8] preload schema finished, cost 0 ms 
[TRACE] 2025-03-04 20:08:05.271 - [任务 39][Pg - Copy] - Node Pg - Copy[3d6414f6-5142-4608-b869-0b969abe08f6] preload schema finished, cost 0 ms 
[INFO ] 2025-03-04 20:08:05.967 - [任务 39][Sybase-Wim] - Source connector(Sybase-Wim) initialization completed 
[TRACE] 2025-03-04 20:08:05.971 - [任务 39][Sybase-Wim] - Source node "Sybase-Wim" read batch size: 100 
[TRACE] 2025-03-04 20:08:05.971 - [任务 39][Sybase-Wim] - Source node "Sybase-Wim" event queue capacity: 200 
[TRACE] 2025-03-04 20:08:05.971 - [任务 39][Sybase-Wim] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-03-04 20:08:05.971 - [任务 39][Sybase-Wim] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-03-04 20:08:06.179 - [任务 39][Sybase-Wim] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-03-04 20:08:06.289 - [任务 39][Pg - Copy] - Sink connector(Pg - Copy) initialization completed 
[TRACE] 2025-03-04 20:08:06.291 - [任务 39][Pg - Copy] - Node(Pg - Copy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-04 20:08:06.291 - [任务 39][Pg - Copy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-04 20:08:06.294 - [任务 39][Pg - Copy] - Apply table structure to target database 
[INFO ] 2025-03-04 20:08:06.398 - [任务 39][Sybase-Wim] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[TRACE] 2025-03-04 20:08:06.402 - [任务 39][Sybase-Wim] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-03-04 20:08:06.402 - [任务 39][Sybase-Wim] - Starting batch read from 4 tables 
[TRACE] 2025-03-04 20:08:06.420 - [任务 39][Sybase-Wim] - Initial sync started 
[INFO ] 2025-03-04 20:08:06.420 - [任务 39][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_warehouse 
[TRACE] 2025-03-04 20:08:06.472 - [任务 39][Sybase-Wim] - Table pg_yyy_bmsql_warehouse is going to be initial synced 
[TRACE] 2025-03-04 20:08:06.472 - [任务 39][Pg - Copy] - Table: pg_yyy_bmsql_warehouse already exists Index: pg_yyy_bms_20000071251 and will no longer create index 
[TRACE] 2025-03-04 20:08:06.472 - [任务 39][Pg - Copy] - Table: pg_yyy_bmsql_warehouse already exists Index list: [TapIndex name pg_yyy_bms_20000071251 indexFields: [TapIndexField name w_id fieldAsc true indexType null; ]] 
[INFO ] 2025-03-04 20:08:06.531 - [任务 39][Sybase-Wim] - Table pg_yyy_bmsql_warehouse has been completed batch read 
[INFO ] 2025-03-04 20:08:06.531 - [任务 39][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_stock 
[TRACE] 2025-03-04 20:08:06.531 - [任务 39][Sybase-Wim] - Table pg_yyy_bmsql_stock is going to be initial synced 
[TRACE] 2025-03-04 20:08:06.588 - [任务 39][Pg - Copy] - Table: pg_yyy_bmsql_stock already exists Index: pg_yyy_bms_765242751 and will no longer create index 
[TRACE] 2025-03-04 20:08:06.589 - [任务 39][Pg - Copy] - Table: pg_yyy_bmsql_stock already exists Index list: [TapIndex name pg_yyy_bms_765242751 indexFields: [TapIndexField name s_w_id fieldAsc true indexType null; TapIndexField name s_i_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 20:08:06.749 - [任务 39][Pg - Copy] - Table: pg_yyy_bmsql_order_line already exists Index: pg_yyy_bms_2045247311 and will no longer create index 
[TRACE] 2025-03-04 20:08:06.750 - [任务 39][Pg - Copy] - Table: pg_yyy_bmsql_order_line already exists Index list: [TapIndex name pg_yyy_bms_2045247311 indexFields: [TapIndexField name ol_w_id fieldAsc true indexType null; TapIndexField name ol_d_id fieldAsc true indexType null; TapIndexField name ol_o_id fieldAsc true indexType null; TapIndexField name ol_number fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 20:08:06.942 - [任务 39][Pg - Copy] - Table: s2p_datetime_idt already exists Index: s2p_datetime_idt_unique_index and will no longer create index 
[TRACE] 2025-03-04 20:08:06.942 - [任务 39][Pg - Copy] - Table: s2p_datetime_idt already exists Index: s2p_datetime_idt_cahr_unique_index and will no longer create index 
[TRACE] 2025-03-04 20:08:06.942 - [任务 39][Pg - Copy] - Table: s2p_datetime_idt already exists Index list: [TapIndex name s2p_datetime_idt_unique_index indexFields: [TapIndexField name id fieldAsc true indexType null; ], TapIndex name s2p_datetime_idt_cahr_unique_index indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 20:08:07.145 - [任务 39][Sybase-Wim] - Query snapshot row size completed: Sybase-Wim(1742df4d-6e67-4553-99d0-a6b60243cef8) 
[INFO ] 2025-03-04 20:08:09.404 - [任务 39][Sybase-Wim] - Table pg_yyy_bmsql_stock has been completed batch read 
[INFO ] 2025-03-04 20:08:09.405 - [任务 39][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_order_line 
[TRACE] 2025-03-04 20:08:09.405 - [任务 39][Sybase-Wim] - Table pg_yyy_bmsql_order_line is going to be initial synced 
[TRACE] 2025-03-04 20:08:16.288 - [任务 39][Sybase-Wim] - Node Sybase-Wim[1742df4d-6e67-4553-99d0-a6b60243cef8] running status set to false 
[TRACE] 2025-03-04 20:08:16.289 - [任务 39][Sybase-Wim] - PDK connector node stopped: HazelcastSourcePdkDataNode_1742df4d-6e67-4553-99d0-a6b60243cef8_1741090085882 
[TRACE] 2025-03-04 20:08:16.289 - [任务 39][Sybase-Wim] - PDK connector node released: HazelcastSourcePdkDataNode_1742df4d-6e67-4553-99d0-a6b60243cef8_1741090085882 
[TRACE] 2025-03-04 20:08:16.289 - [任务 39][Sybase-Wim] - Node Sybase-Wim[1742df4d-6e67-4553-99d0-a6b60243cef8] schema data cleaned 
[TRACE] 2025-03-04 20:08:16.293 - [任务 39][Sybase-Wim] - Node Sybase-Wim[1742df4d-6e67-4553-99d0-a6b60243cef8] monitor closed 
[TRACE] 2025-03-04 20:08:16.293 - [任务 39][Sybase-Wim] - Node Sybase-Wim[1742df4d-6e67-4553-99d0-a6b60243cef8] close complete, cost 168 ms 
[TRACE] 2025-03-04 20:08:16.293 - [任务 39][Pg - Copy] - Node Pg - Copy[3d6414f6-5142-4608-b869-0b969abe08f6] running status set to false 
[TRACE] 2025-03-04 20:08:16.312 - [任务 39][Pg - Copy] - PDK connector node stopped: HazelcastTargetPdkDataNode_3d6414f6-5142-4608-b869-0b969abe08f6_1741090086000 
[TRACE] 2025-03-04 20:08:16.312 - [任务 39][Pg - Copy] - PDK connector node released: HazelcastTargetPdkDataNode_3d6414f6-5142-4608-b869-0b969abe08f6_1741090086000 
[TRACE] 2025-03-04 20:08:16.313 - [任务 39][Pg - Copy] - Node Pg - Copy[3d6414f6-5142-4608-b869-0b969abe08f6] schema data cleaned 
[TRACE] 2025-03-04 20:08:16.313 - [任务 39][Pg - Copy] - Node Pg - Copy[3d6414f6-5142-4608-b869-0b969abe08f6] monitor closed 
[TRACE] 2025-03-04 20:08:16.515 - [任务 39][Pg - Copy] - Node Pg - Copy[3d6414f6-5142-4608-b869-0b969abe08f6] close complete, cost 20 ms 
[TRACE] 2025-03-04 20:08:17.594 - [任务 39][Sybase-Wim] - Initial sync completed 
[TRACE] 2025-03-04 20:08:17.600 - [任务 39][Sybase-Wim] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "s2p_datetime_idt" not exists, qualified name: null tableNameAndQualifiedNameMap: {} 
[ERROR] 2025-03-04 20:08:17.601 - [任务 39][Sybase-Wim] - Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "s2p_datetime_idt" not exists, qualified name: null tableNameAndQualifiedNameMap: {} <-- Error Message -->
Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "s2p_datetime_idt" not exists, qualified name: null tableNameAndQualifiedNameMap: {}

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "s2p_datetime_idt" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:442)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "s2p_datetime_idt" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:219)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:282)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:142)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:187)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:391)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:356)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:269)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "s2p_datetime_idt" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:173)
	... 12 more
Caused by: java.lang.RuntimeException: Table name "s2p_datetime_idt" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:442)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 14 more

[TRACE] 2025-03-04 20:08:21.149 - [任务 39] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-04 20:08:21.149 - [任务 39] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@cb5660e 
[TRACE] 2025-03-04 20:08:21.289 - [任务 39] - Stop task milestones: 67c6d8576ed4fc58ed9923cb(任务 39)  
[TRACE] 2025-03-04 20:08:21.289 - [任务 39] - Stopped task aspect(s) 
[TRACE] 2025-03-04 20:08:21.289 - [任务 39] - Snapshot order controller have been removed 
[INFO ] 2025-03-04 20:08:21.290 - [任务 39] - Task stopped. 
[TRACE] 2025-03-04 20:08:21.318 - [任务 39] - Remove memory task client succeed, task: 任务 39[67c6d8576ed4fc58ed9923cb] 
[TRACE] 2025-03-04 20:08:21.327 - [任务 39] - Destroy memory task client cache succeed, task: 任务 39[67c6d8576ed4fc58ed9923cb] 
