[INFO ] 2024-07-01 17:13:36.377 - [任务 4] - Task initialization... 
[INFO ] 2024-07-01 17:13:36.584 - [任务 4] - Start task milestones: 6682731fd75a141ecca27598(任务 4) 
[INFO ] 2024-07-01 17:13:37.164 - [任务 4] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-01 17:13:37.374 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-01 17:13:37.595 - [任务 4][Mongo] - <PERSON><PERSON>[6bb62153-673e-4fdd-a8b2-765fcfe78d16] start preload schema,table counts: 1 
[INFO ] 2024-07-01 17:13:37.598 - [任务 4][Mysql] - Node Mysql[4b8a946c-aefb-4e8b-ae7d-bc4092f454a3] start preload schema,table counts: 1 
[INFO ] 2024-07-01 17:13:37.598 - [任务 4][<PERSON><PERSON>] - <PERSON><PERSON>[6bb62153-673e-4fdd-a8b2-765fcfe78d16] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 17:13:37.599 - [任务 4][Mysql] - Node Mysql[4b8a946c-aefb-4e8b-ae7d-bc4092f454a3] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 17:13:38.631 - [任务 4][Mongo] - Source node "Mongo" read batch size: 100 
[INFO ] 2024-07-01 17:13:38.635 - [任务 4][Mongo] - Source node "Mongo" event queue capacity: 200 
[INFO ] 2024-07-01 17:13:38.635 - [任务 4][Mongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-01 17:13:38.796 - [任务 4][Mongo] - batch offset found: {},stream offset found: {"cdcOffset":1719825218,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 17:13:38.902 - [任务 4][Mongo] - Initial sync started 
[INFO ] 2024-07-01 17:13:38.922 - [任务 4][Mongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-07-01 17:13:38.922 - [任务 4][Mongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-07-01 17:13:38.996 - [任务 4][Mongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-07-01 17:13:38.997 - [任务 4][Mysql] - Node(Mysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-01 17:13:38.998 - [任务 4][Mysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-01 17:13:39.382 - [任务 4][Mongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 17:13:39.389 - [任务 4][Mongo] - Initial sync completed 
[INFO ] 2024-07-01 17:13:39.390 - [任务 4][Mongo] - Incremental sync starting... 
[INFO ] 2024-07-01 17:13:39.390 - [任务 4][Mongo] - Initial sync completed 
[INFO ] 2024-07-01 17:13:39.390 - [任务 4][Mongo] - Starting stream read, table list: [CLAIM], offset: {"cdcOffset":1719825218,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 17:13:39.437 - [任务 4][Mongo] - Connector MongoDB incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-07-01 17:15:40.799 - [任务 4] - Stop task milestones: 6682731fd75a141ecca27598(任务 4)  
[INFO ] 2024-07-01 17:15:40.833 - [任务 4][Mongo] - Node Mongo[6bb62153-673e-4fdd-a8b2-765fcfe78d16] running status set to false 
[INFO ] 2024-07-01 17:15:40.838 - [任务 4][Mongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-6bb62153-673e-4fdd-a8b2-765fcfe78d16 
[INFO ] 2024-07-01 17:15:40.841 - [任务 4][Mongo] - PDK connector node released: HazelcastSourcePdkDataNode-6bb62153-673e-4fdd-a8b2-765fcfe78d16 
[INFO ] 2024-07-01 17:15:40.841 - [任务 4][Mongo] - Node Mongo[6bb62153-673e-4fdd-a8b2-765fcfe78d16] schema data cleaned 
[INFO ] 2024-07-01 17:15:40.844 - [任务 4][Mongo] - Node Mongo[6bb62153-673e-4fdd-a8b2-765fcfe78d16] monitor closed 
[INFO ] 2024-07-01 17:15:40.854 - [任务 4][Mongo] - Node Mongo[6bb62153-673e-4fdd-a8b2-765fcfe78d16] close complete, cost 35 ms 
[INFO ] 2024-07-01 17:15:40.854 - [任务 4][Mysql] - Node Mysql[4b8a946c-aefb-4e8b-ae7d-bc4092f454a3] running status set to false 
[INFO ] 2024-07-01 17:15:40.898 - [任务 4][Mysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-4b8a946c-aefb-4e8b-ae7d-bc4092f454a3 
[INFO ] 2024-07-01 17:15:40.899 - [任务 4][Mysql] - PDK connector node released: HazelcastTargetPdkDataNode-4b8a946c-aefb-4e8b-ae7d-bc4092f454a3 
[INFO ] 2024-07-01 17:15:40.899 - [任务 4][Mysql] - Node Mysql[4b8a946c-aefb-4e8b-ae7d-bc4092f454a3] schema data cleaned 
[INFO ] 2024-07-01 17:15:40.900 - [任务 4][Mysql] - Node Mysql[4b8a946c-aefb-4e8b-ae7d-bc4092f454a3] monitor closed 
[INFO ] 2024-07-01 17:15:40.901 - [任务 4][Mysql] - Node Mysql[4b8a946c-aefb-4e8b-ae7d-bc4092f454a3] close complete, cost 47 ms 
[INFO ] 2024-07-01 17:15:41.024 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-01 17:15:41.025 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-07-01 17:15:41.061 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-07-01 17:15:41.061 - [任务 4] - Remove memory task client succeed, task: 任务 4[6682731fd75a141ecca27598] 
[INFO ] 2024-07-01 17:15:41.269 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[6682731fd75a141ecca27598] 
