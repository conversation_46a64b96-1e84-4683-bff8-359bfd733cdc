[INFO ] 2024-06-25 14:47:39.128 - [任务 28] - Start task milestones: 667a67d910fc5c6259c55cdd(任务 28) 
[INFO ] 2024-06-25 14:47:39.259 - [任务 28] - Task initialization... 
[INFO ] 2024-06-25 14:47:39.260 - [任务 28] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-06-25 14:47:39.382 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 14:47:39.382 - [任务 28][SourceMongo] - Node SourceMongo[3eb123ce-aa3c-4e79-9982-7a43f6cab59e] start preload schema,table counts: 2 
[INFO ] 2024-06-25 14:47:39.382 - [任务 28][SouceMysql] - Node SouceMysql[bc41bbd4-efdb-4eaf-b08e-fd741f25dea0] start preload schema,table counts: 2 
[INFO ] 2024-06-25 14:47:39.382 - [任务 28][表编辑] - Node 表编辑[4dda9ab3-fe7a-48a9-b1b9-6c1d4e098c73] start preload schema,table counts: 2 
[INFO ] 2024-06-25 14:47:39.382 - [任务 28][SourceMongo] - Node SourceMongo[3eb123ce-aa3c-4e79-9982-7a43f6cab59e] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:47:39.382 - [任务 28][SouceMysql] - Node SouceMysql[bc41bbd4-efdb-4eaf-b08e-fd741f25dea0] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:47:39.383 - [任务 28][表编辑] - Node 表编辑[4dda9ab3-fe7a-48a9-b1b9-6c1d4e098c73] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:47:40.005 - [任务 28][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-06-25 14:47:40.006 - [任务 28][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-06-25 14:47:40.007 - [任务 28][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 14:47:40.109 - [任务 28][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719298060,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 14:47:40.109 - [任务 28] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 14:47:40.178 - [任务 28][SourceMongo] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapDropTableEvent@25d4e419: {"tableId":"CLAIM1","type":208} 
[INFO ] 2024-06-25 14:47:40.178 - [任务 28][SourceMongo] - Initial sync started 
[INFO ] 2024-06-25 14:47:40.181 - [任务 28][SourceMongo] - Drop table in memory qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT_CLAIM1_6674feb868ca1e3afc2a0d99_667a67d910fc5c6259c55cdd 
[INFO ] 2024-06-25 14:47:40.203 - [任务 28][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 14:47:40.299 - [任务 28][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 14:47:40.302 - [任务 28][SourceMongo] - Drop table schema transform finished 
[INFO ] 2024-06-25 14:47:40.306 - [任务 28][SourceMongo] - Table CLAIM1 is detected that it has been removed, the snapshot read will be skipped 
[INFO ] 2024-06-25 14:47:40.306 - [任务 28][SourceMongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-25 14:47:40.312 - [任务 28][SourceMongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-25 14:47:40.373 - [任务 28][SourceMongo] - Query table 'CLAIM' counts: 1130 
[WARN ] 2024-06-25 14:47:40.376 - [任务 28][SouceMysql] - Drop table event will be ignored at sink node: wimCLAIM1inspect 
[WARN ] 2024-06-25 14:47:40.376 - [任务 28][SouceMysql] - DDL event: TapDropTableEvent does not supported 
[INFO ] 2024-06-25 14:47:40.426 - [任务 28][SourceMongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 14:47:40.427 - [任务 28][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 14:47:40.427 - [任务 28][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-25 14:47:40.427 - [任务 28][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 14:47:40.428 - [任务 28][SourceMongo] - Starting stream read, table list: [CLAIM1, CLAIM], offset: {"cdcOffset":1719298060,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 14:47:40.631 - [任务 28][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM1, CLAIM], data change syncing 
[INFO ] 2024-06-25 14:48:24.236 - [任务 28] - Stop task milestones: 667a67d910fc5c6259c55cdd(任务 28)  
[INFO ] 2024-06-25 14:48:24.242 - [任务 28][SourceMongo] - Node SourceMongo[3eb123ce-aa3c-4e79-9982-7a43f6cab59e] running status set to false 
[INFO ] 2024-06-25 14:48:24.260 - [任务 28][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-3eb123ce-aa3c-4e79-9982-7a43f6cab59e 
[INFO ] 2024-06-25 14:48:24.260 - [任务 28][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-3eb123ce-aa3c-4e79-9982-7a43f6cab59e 
[INFO ] 2024-06-25 14:48:24.260 - [任务 28][SourceMongo] - Node SourceMongo[3eb123ce-aa3c-4e79-9982-7a43f6cab59e] schema data cleaned 
[INFO ] 2024-06-25 14:48:24.261 - [任务 28][SourceMongo] - Node SourceMongo[3eb123ce-aa3c-4e79-9982-7a43f6cab59e] monitor closed 
[INFO ] 2024-06-25 14:48:24.262 - [任务 28][SourceMongo] - Node SourceMongo[3eb123ce-aa3c-4e79-9982-7a43f6cab59e] close complete, cost 34 ms 
[INFO ] 2024-06-25 14:48:24.262 - [任务 28][表编辑] - Node 表编辑[4dda9ab3-fe7a-48a9-b1b9-6c1d4e098c73] running status set to false 
[INFO ] 2024-06-25 14:48:24.262 - [任务 28][表编辑] - Node 表编辑[4dda9ab3-fe7a-48a9-b1b9-6c1d4e098c73] schema data cleaned 
[INFO ] 2024-06-25 14:48:24.262 - [任务 28][表编辑] - Node 表编辑[4dda9ab3-fe7a-48a9-b1b9-6c1d4e098c73] monitor closed 
[INFO ] 2024-06-25 14:48:24.263 - [任务 28][表编辑] - Node 表编辑[4dda9ab3-fe7a-48a9-b1b9-6c1d4e098c73] close complete, cost 1 ms 
[INFO ] 2024-06-25 14:48:24.263 - [任务 28][SouceMysql] - Node SouceMysql[bc41bbd4-efdb-4eaf-b08e-fd741f25dea0] running status set to false 
[INFO ] 2024-06-25 14:48:24.292 - [任务 28][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-bc41bbd4-efdb-4eaf-b08e-fd741f25dea0 
[INFO ] 2024-06-25 14:48:24.292 - [任务 28][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-bc41bbd4-efdb-4eaf-b08e-fd741f25dea0 
[INFO ] 2024-06-25 14:48:24.292 - [任务 28][SouceMysql] - Node SouceMysql[bc41bbd4-efdb-4eaf-b08e-fd741f25dea0] schema data cleaned 
[INFO ] 2024-06-25 14:48:24.292 - [任务 28][SouceMysql] - Node SouceMysql[bc41bbd4-efdb-4eaf-b08e-fd741f25dea0] monitor closed 
[INFO ] 2024-06-25 14:48:24.292 - [任务 28][SouceMysql] - Node SouceMysql[bc41bbd4-efdb-4eaf-b08e-fd741f25dea0] close complete, cost 28 ms 
[INFO ] 2024-06-25 14:48:24.958 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 14:48:24.960 - [任务 28] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@35158620 
[INFO ] 2024-06-25 14:48:24.960 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-06-25 14:48:24.960 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 14:48:25.002 - [任务 28] - Remove memory task client succeed, task: 任务 28[667a67d910fc5c6259c55cdd] 
[INFO ] 2024-06-25 14:48:25.002 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[667a67d910fc5c6259c55cdd] 
[INFO ] 2024-06-25 14:49:16.007 - [任务 28] - Start task milestones: 667a67d910fc5c6259c55cdd(任务 28) 
[INFO ] 2024-06-25 14:49:16.007 - [任务 28] - Task initialization... 
[INFO ] 2024-06-25 14:49:16.242 - [任务 28] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 14:49:16.344 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 14:49:16.345 - [任务 28][表编辑] - Node 表编辑[4dda9ab3-fe7a-48a9-b1b9-6c1d4e098c73] start preload schema,table counts: 7 
[INFO ] 2024-06-25 14:49:16.345 - [任务 28][SouceMysql] - Node SouceMysql[bc41bbd4-efdb-4eaf-b08e-fd741f25dea0] start preload schema,table counts: 7 
[INFO ] 2024-06-25 14:49:16.346 - [任务 28][SourceMongo] - Node SourceMongo[3eb123ce-aa3c-4e79-9982-7a43f6cab59e] start preload schema,table counts: 7 
[INFO ] 2024-06-25 14:49:16.346 - [任务 28][表编辑] - Node 表编辑[4dda9ab3-fe7a-48a9-b1b9-6c1d4e098c73] preload schema finished, cost 1 ms 
[INFO ] 2024-06-25 14:49:16.346 - [任务 28][SouceMysql] - Node SouceMysql[bc41bbd4-efdb-4eaf-b08e-fd741f25dea0] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:49:16.347 - [任务 28][SourceMongo] - Node SourceMongo[3eb123ce-aa3c-4e79-9982-7a43f6cab59e] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:49:16.947 - [任务 28][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-06-25 14:49:16.947 - [任务 28][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-06-25 14:49:17.042 - [任务 28][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 14:49:17.043 - [任务 28][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719298156,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 14:49:17.114 - [任务 28] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 14:49:17.114 - [任务 28][SourceMongo] - Initial sync started 
[INFO ] 2024-06-25 14:49:17.127 - [任务 28][SourceMongo] - Starting batch read, table name: test4, offset: null 
[INFO ] 2024-06-25 14:49:17.128 - [任务 28][SourceMongo] - Table test4 is going to be initial synced 
[INFO ] 2024-06-25 14:49:17.150 - [任务 28][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 14:49:17.151 - [任务 28][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 14:49:17.361 - [任务 28][SourceMongo] - Query table 'test4' counts: 694 
[INFO ] 2024-06-25 14:49:17.634 - [任务 28][SourceMongo] - Table [test4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 14:49:17.634 - [任务 28][SourceMongo] - Starting batch read, table name: test5, offset: null 
[INFO ] 2024-06-25 14:49:17.635 - [任务 28][SourceMongo] - Table test5 is going to be initial synced 
[INFO ] 2024-06-25 14:49:17.640 - [任务 28][SourceMongo] - Query table 'test5' counts: 1 
[INFO ] 2024-06-25 14:49:17.645 - [任务 28][SourceMongo] - Table [test5] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 14:49:17.645 - [任务 28][SourceMongo] - Starting batch read, table name: test2, offset: null 
[INFO ] 2024-06-25 14:49:17.646 - [任务 28][SourceMongo] - Table test2 is going to be initial synced 
[INFO ] 2024-06-25 14:49:17.646 - [任务 28][SourceMongo] - Query table 'test2' counts: 1 
[INFO ] 2024-06-25 14:49:17.653 - [任务 28][SourceMongo] - Table [test2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 14:49:17.653 - [任务 28][SourceMongo] - Starting batch read, table name: test3, offset: null 
[INFO ] 2024-06-25 14:49:17.661 - [任务 28][SourceMongo] - Table test3 is going to be initial synced 
[INFO ] 2024-06-25 14:49:17.662 - [任务 28][SourceMongo] - Query table 'test3' counts: 1 
[INFO ] 2024-06-25 14:49:17.671 - [任务 28][SourceMongo] - Table [test3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 14:49:17.671 - [任务 28][SourceMongo] - Starting batch read, table name: test6, offset: null 
[INFO ] 2024-06-25 14:49:17.679 - [任务 28][SourceMongo] - Table test6 is going to be initial synced 
[INFO ] 2024-06-25 14:49:17.679 - [任务 28][SourceMongo] - Query table 'test6' counts: 1 
[INFO ] 2024-06-25 14:49:17.691 - [任务 28][SourceMongo] - Table [test6] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 14:49:17.691 - [任务 28][SourceMongo] - Starting batch read, table name: test7, offset: null 
[INFO ] 2024-06-25 14:49:17.709 - [任务 28][SourceMongo] - Table test7 is going to be initial synced 
[INFO ] 2024-06-25 14:49:17.709 - [任务 28][SourceMongo] - Query table 'test7' counts: 1075 
[INFO ] 2024-06-25 14:49:17.809 - [任务 28][SourceMongo] - Table [test7] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 14:49:17.809 - [任务 28][SourceMongo] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-06-25 14:49:17.811 - [任务 28][SourceMongo] - Table test1 is going to be initial synced 
[INFO ] 2024-06-25 14:49:17.811 - [任务 28][SourceMongo] - Query table 'test1' counts: 1 
[INFO ] 2024-06-25 14:49:17.816 - [任务 28][SourceMongo] - Table [test1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 14:49:17.816 - [任务 28][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 14:49:17.817 - [任务 28][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-25 14:49:17.818 - [任务 28][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-25 14:49:17.819 - [任务 28][SourceMongo] - Starting stream read, table list: [test4, test5, test2, test3, test6, test7, test1], offset: {"cdcOffset":1719298156,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-25 14:49:18.025 - [任务 28][SourceMongo] - Connector MongoDB incremental start succeed, tables: [test4, test5, test2, test3, test6, test7, test1], data change syncing 
[INFO ] 2024-06-25 14:50:46.267 - [任务 28] - Stop task milestones: 667a67d910fc5c6259c55cdd(任务 28)  
[INFO ] 2024-06-25 14:50:46.544 - [任务 28][SourceMongo] - Node SourceMongo[3eb123ce-aa3c-4e79-9982-7a43f6cab59e] running status set to false 
[INFO ] 2024-06-25 14:50:46.545 - [任务 28][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-3eb123ce-aa3c-4e79-9982-7a43f6cab59e 
[INFO ] 2024-06-25 14:50:46.554 - [任务 28][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-3eb123ce-aa3c-4e79-9982-7a43f6cab59e 
[INFO ] 2024-06-25 14:50:46.555 - [任务 28][SourceMongo] - Node SourceMongo[3eb123ce-aa3c-4e79-9982-7a43f6cab59e] schema data cleaned 
[INFO ] 2024-06-25 14:50:46.559 - [任务 28][SourceMongo] - Node SourceMongo[3eb123ce-aa3c-4e79-9982-7a43f6cab59e] monitor closed 
[INFO ] 2024-06-25 14:50:46.562 - [任务 28][SourceMongo] - Node SourceMongo[3eb123ce-aa3c-4e79-9982-7a43f6cab59e] close complete, cost 40 ms 
[INFO ] 2024-06-25 14:50:46.562 - [任务 28][表编辑] - Node 表编辑[4dda9ab3-fe7a-48a9-b1b9-6c1d4e098c73] running status set to false 
[INFO ] 2024-06-25 14:50:46.564 - [任务 28][表编辑] - Node 表编辑[4dda9ab3-fe7a-48a9-b1b9-6c1d4e098c73] schema data cleaned 
[INFO ] 2024-06-25 14:50:46.564 - [任务 28][表编辑] - Node 表编辑[4dda9ab3-fe7a-48a9-b1b9-6c1d4e098c73] monitor closed 
[INFO ] 2024-06-25 14:50:46.564 - [任务 28][表编辑] - Node 表编辑[4dda9ab3-fe7a-48a9-b1b9-6c1d4e098c73] close complete, cost 7 ms 
[INFO ] 2024-06-25 14:50:46.565 - [任务 28][SouceMysql] - Node SouceMysql[bc41bbd4-efdb-4eaf-b08e-fd741f25dea0] running status set to false 
[INFO ] 2024-06-25 14:50:46.589 - [任务 28][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-bc41bbd4-efdb-4eaf-b08e-fd741f25dea0 
[INFO ] 2024-06-25 14:50:46.589 - [任务 28][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-bc41bbd4-efdb-4eaf-b08e-fd741f25dea0 
[INFO ] 2024-06-25 14:50:46.589 - [任务 28][SouceMysql] - Node SouceMysql[bc41bbd4-efdb-4eaf-b08e-fd741f25dea0] schema data cleaned 
[INFO ] 2024-06-25 14:50:46.590 - [任务 28][SouceMysql] - Node SouceMysql[bc41bbd4-efdb-4eaf-b08e-fd741f25dea0] monitor closed 
[INFO ] 2024-06-25 14:50:46.792 - [任务 28][SouceMysql] - Node SouceMysql[bc41bbd4-efdb-4eaf-b08e-fd741f25dea0] close complete, cost 25 ms 
[INFO ] 2024-06-25 14:50:50.167 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 14:50:50.167 - [任务 28] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6e527395 
[INFO ] 2024-06-25 14:50:50.167 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-06-25 14:50:50.195 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 14:50:50.197 - [任务 28] - Remove memory task client succeed, task: 任务 28[667a67d910fc5c6259c55cdd] 
[INFO ] 2024-06-25 14:50:50.197 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[667a67d910fc5c6259c55cdd] 
