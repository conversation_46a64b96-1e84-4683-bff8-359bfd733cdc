[INFO ] 2024-06-25 12:03:09.506 - [任务 26] - Task initialization... 
[INFO ] 2024-06-25 12:03:09.708 - [任务 26] - Start task milestones: 667a3ed45d7b3052166837b9(任务 26) 
[INFO ] 2024-06-25 12:03:10.650 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 12:03:10.852 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 12:03:11.229 - [任务 26][SourceMongo] - Node SourceMongo[1d73b7c5-b26b-41d0-aa80-cc41b583dc99] start preload schema,table counts: 2 
[INFO ] 2024-06-25 12:03:11.231 - [任务 26][表编辑] - Node 表编辑[14ecb9f2-923b-4184-ba5c-0dcadb188261] start preload schema,table counts: 2 
[INFO ] 2024-06-25 12:03:11.231 - [任务 26][SourceMongo] - Node SourceMongo[1d73b7c5-b26b-41d0-aa80-cc41b583dc99] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 12:03:11.231 - [任务 26][表编辑] - Node 表编辑[14ecb9f2-923b-4184-ba5c-0dcadb188261] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 12:03:11.316 - [任务 26][SouceMysql] - Node SouceMysql[6018f17a-1b92-4f35-939e-237cf6f63d22] start preload schema,table counts: 2 
[INFO ] 2024-06-25 12:03:11.318 - [任务 26][SouceMysql] - Node SouceMysql[6018f17a-1b92-4f35-939e-237cf6f63d22] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 12:03:12.757 - [任务 26][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 12:03:12.758 - [任务 26][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 12:03:13.120 - [任务 26][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-06-25 12:03:13.121 - [任务 26][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-06-25 12:03:13.122 - [任务 26][SouceMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 12:03:13.141 - [任务 26][SouceMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000031","position":1058090632,"gtidSet":""} 
[INFO ] 2024-06-25 12:03:13.275 - [任务 26][SouceMysql] - Initial sync started 
[INFO ] 2024-06-25 12:03:13.277 - [任务 26][SouceMysql] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-06-25 12:03:13.290 - [任务 26][SouceMysql] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-06-25 12:03:13.478 - [任务 26][SouceMysql] - Query table 'CUSTOMER' counts: 674 
[INFO ] 2024-06-25 12:03:13.481 - [任务 26][SouceMysql] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 12:03:13.482 - [任务 26][SouceMysql] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-25 12:03:13.484 - [任务 26][SouceMysql] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-25 12:03:13.489 - [任务 26][SouceMysql] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-25 12:03:13.971 - [任务 26][SouceMysql] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 12:03:13.978 - [任务 26][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 12:03:13.979 - [任务 26][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 12:03:13.984 - [任务 26][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 12:03:13.994 - [任务 26][SouceMysql] - Starting stream read, table list: [CUSTOMER, CLAIM], offset: {"filename":"binlog.000031","position":1058090632,"gtidSet":""} 
[INFO ] 2024-06-25 12:03:14.046 - [任务 26][SouceMysql] - Starting mysql cdc, server name: 0eb8310b-4e37-4461-8ec3-b85062884784 
[INFO ] 2024-06-25 12:03:14.249 - [任务 26][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2014518747
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 0eb8310b-4e37-4461-8ec3-b85062884784
  database.port: 3306
  threadName: Debezium-Mysql-Connector-0eb8310b-4e37-4461-8ec3-b85062884784
  database.hostname: localhost
  database.password: ********
  name: 0eb8310b-4e37-4461-8ec3-b85062884784
  pdk.offset.string: {"name":"0eb8310b-4e37-4461-8ec3-b85062884784","offset":{"{\"server\":\"0eb8310b-4e37-4461-8ec3-b85062884784\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CUSTOMER,test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 12:03:14.384 - [任务 26][SouceMysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER, CLAIM], data change syncing 
[INFO ] 2024-06-25 14:21:47.759 - [任务 26] - Task initialization... 
[INFO ] 2024-06-25 14:21:47.783 - [任务 26] - Start task milestones: 667a3ed45d7b3052166837b9(任务 26) 
[INFO ] 2024-06-25 14:21:48.763 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 14:21:48.827 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 14:21:49.321 - [任务 26][SourceMongo] - Node SourceMongo[1d73b7c5-b26b-41d0-aa80-cc41b583dc99] start preload schema,table counts: 2 
[INFO ] 2024-06-25 14:21:49.321 - [任务 26][SouceMysql] - Node SouceMysql[6018f17a-1b92-4f35-939e-237cf6f63d22] start preload schema,table counts: 2 
[INFO ] 2024-06-25 14:21:49.322 - [任务 26][SourceMongo] - Node SourceMongo[1d73b7c5-b26b-41d0-aa80-cc41b583dc99] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:21:49.325 - [任务 26][SouceMysql] - Node SouceMysql[6018f17a-1b92-4f35-939e-237cf6f63d22] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:21:49.335 - [任务 26][表编辑] - Node 表编辑[14ecb9f2-923b-4184-ba5c-0dcadb188261] start preload schema,table counts: 2 
[INFO ] 2024-06-25 14:21:49.553 - [任务 26][表编辑] - Node 表编辑[14ecb9f2-923b-4184-ba5c-0dcadb188261] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:21:49.623 - [任务 26][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 14:21:49.629 - [任务 26][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 14:21:49.684 - [任务 26][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-06-25 14:21:49.687 - [任务 26][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-06-25 14:21:49.687 - [任务 26][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-25 14:21:49.889 - [任务 26][SouceMysql] - batch offset found: {"CUSTOMER":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"CLAIM":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"name":"0eb8310b-4e37-4461-8ec3-b85062884784","offset":{"{\"server\":\"0eb8310b-4e37-4461-8ec3-b85062884784\"}":"{\"ts_sec\":1719288194,\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-25 14:21:49.906 - [任务 26][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 14:21:49.906 - [任务 26][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 14:21:49.965 - [任务 26][SouceMysql] - Starting stream read, table list: [CUSTOMER, CLAIM], offset: {"name":"0eb8310b-4e37-4461-8ec3-b85062884784","offset":{"{\"server\":\"0eb8310b-4e37-4461-8ec3-b85062884784\"}":"{\"ts_sec\":1719288194,\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-25 14:21:49.966 - [任务 26][SouceMysql] - Starting mysql cdc, server name: 0eb8310b-4e37-4461-8ec3-b85062884784 
[INFO ] 2024-06-25 14:21:50.170 - [任务 26][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2097049228
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 0eb8310b-4e37-4461-8ec3-b85062884784
  database.port: 3306
  threadName: Debezium-Mysql-Connector-0eb8310b-4e37-4461-8ec3-b85062884784
  database.hostname: localhost
  database.password: ********
  name: 0eb8310b-4e37-4461-8ec3-b85062884784
  pdk.offset.string: {"name":"0eb8310b-4e37-4461-8ec3-b85062884784","offset":{"{\"server\":\"0eb8310b-4e37-4461-8ec3-b85062884784\"}":"{\"ts_sec\":1719288194,\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CUSTOMER,test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 14:21:50.721 - [任务 26][SouceMysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER, CLAIM], data change syncing 
[INFO ] 2024-06-25 14:37:16.948 - [任务 26][SouceMysql] - Node SouceMysql[6018f17a-1b92-4f35-939e-237cf6f63d22] running status set to false 
[INFO ] 2024-06-25 14:37:17.186 - [任务 26] - Start task milestones: 667a3ed45d7b3052166837b9(任务 26) 
[INFO ] 2024-06-25 14:37:17.217 - [任务 26] - Task initialization... 
[INFO ] 2024-06-25 14:37:18.691 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 14:37:18.900 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 14:37:19.662 - [任务 26][SouceMysql] - Node SouceMysql[6018f17a-1b92-4f35-939e-237cf6f63d22] start preload schema,table counts: 2 
[INFO ] 2024-06-25 14:37:19.664 - [任务 26][表编辑] - Node 表编辑[14ecb9f2-923b-4184-ba5c-0dcadb188261] start preload schema,table counts: 2 
[INFO ] 2024-06-25 14:37:19.665 - [任务 26][SourceMongo] - Node SourceMongo[1d73b7c5-b26b-41d0-aa80-cc41b583dc99] start preload schema,table counts: 2 
[INFO ] 2024-06-25 14:37:19.667 - [任务 26][SouceMysql] - Node SouceMysql[6018f17a-1b92-4f35-939e-237cf6f63d22] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:37:19.673 - [任务 26][表编辑] - Node 表编辑[14ecb9f2-923b-4184-ba5c-0dcadb188261] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:37:19.687 - [任务 26][SourceMongo] - Node SourceMongo[1d73b7c5-b26b-41d0-aa80-cc41b583dc99] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 14:37:20.782 - [任务 26][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 14:37:20.783 - [任务 26][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 14:37:20.864 - [任务 26][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-06-25 14:37:20.866 - [任务 26][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-06-25 14:37:20.866 - [任务 26][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-25 14:37:20.909 - [任务 26][SouceMysql] - batch offset found: {},stream offset found: {"name":"0eb8310b-4e37-4461-8ec3-b85062884784","offset":{"{\"server\":\"0eb8310b-4e37-4461-8ec3-b85062884784\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-25 14:37:21.044 - [任务 26][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 14:37:21.050 - [任务 26][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 14:37:21.066 - [任务 26][SouceMysql] - Starting stream read, table list: [CUSTOMER, CLAIM], offset: {"name":"0eb8310b-4e37-4461-8ec3-b85062884784","offset":{"{\"server\":\"0eb8310b-4e37-4461-8ec3-b85062884784\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}} 
[INFO ] 2024-06-25 14:37:21.134 - [任务 26][SouceMysql] - Starting mysql cdc, server name: 0eb8310b-4e37-4461-8ec3-b85062884784 
[INFO ] 2024-06-25 14:37:21.197 - [任务 26][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 451990697
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 0eb8310b-4e37-4461-8ec3-b85062884784
  database.port: 3306
  threadName: Debezium-Mysql-Connector-0eb8310b-4e37-4461-8ec3-b85062884784
  database.hostname: localhost
  database.password: ********
  name: 0eb8310b-4e37-4461-8ec3-b85062884784
  pdk.offset.string: {"name":"0eb8310b-4e37-4461-8ec3-b85062884784","offset":{"{\"server\":\"0eb8310b-4e37-4461-8ec3-b85062884784\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CUSTOMER,test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 14:37:22.232 - [任务 26][SouceMysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER, CLAIM], data change syncing 
[INFO ] 2024-06-25 15:57:12.911 - [任务 26][SouceMysql] - Mysql binlog reader stopped 
[WARN ] 2024-06-25 15:57:12.913 - [任务 26][SouceMysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Unknown exception occur when operate table: unknown
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-25 15:58:21.006 - [任务 26][SouceMysql] - Starting mysql cdc, server name: 0eb8310b-4e37-4461-8ec3-b85062884784 
[INFO ] 2024-06-25 15:58:21.111 - [任务 26][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1532741324
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 0eb8310b-4e37-4461-8ec3-b85062884784
  database.port: 3306
  threadName: Debezium-Mysql-Connector-0eb8310b-4e37-4461-8ec3-b85062884784
  database.hostname: localhost
  database.password: ********
  name: 0eb8310b-4e37-4461-8ec3-b85062884784
  pdk.offset.string: {"name":"0eb8310b-4e37-4461-8ec3-b85062884784","offset":{"{\"server\":\"0eb8310b-4e37-4461-8ec3-b85062884784\"}":"{\"file\":\"binlog.000031\",\"pos\":1060990386,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CUSTOMER,test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 15:58:21.113 - [任务 26][SouceMysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-06-25 15:58:21.208 - [任务 26][SouceMysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER, CLAIM], data change syncing 
[WARN ] 2024-06-25 16:34:20.146 - [任务 26] - Save milestone failed: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/update": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[WARN ] 2024-06-25 16:34:22.037 - [任务 26][SourceMongo] - Save to snapshot failed, collection: Task/syncProgress/667a3ed45d7b3052166837b9, object: {6018f17a-1b92-4f35-939e-237cf6f63d22,1d73b7c5-b26b-41d0-aa80-cc41b583dc99=SyncProgress{eventSerialNo=146, syncStage='CDC', batchOffset='{}', streamOffset='MysqlStreamOffset{name='0eb8310b-4e37-4461-8ec3-b85062884784', offset={{"server":"0eb8310b-4e37-4461-8ec3-b85062884784"}={"file":"binlog.000031","pos":1060990946,"server_id":1}}}'}}, errors: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/syncProgress/667a3ed45d7b3052166837b9": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[INFO ] 2024-06-25 16:34:27.811 - [任务 26][SouceMysql] - Node SouceMysql[6018f17a-1b92-4f35-939e-237cf6f63d22] running status set to false 
[INFO ] 2024-06-25 16:34:27.811 - [任务 26][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-25 16:34:27.816 - [任务 26][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-06-25 16:34:27.824 - [任务 26][SouceMysql] - Incremental sync completed 
[INFO ] 2024-06-25 16:34:27.824 - [任务 26][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-6018f17a-1b92-4f35-939e-237cf6f63d22 
[INFO ] 2024-06-25 16:34:27.824 - [任务 26][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-6018f17a-1b92-4f35-939e-237cf6f63d22 
[INFO ] 2024-06-25 16:34:27.824 - [任务 26][SouceMysql] - Node SouceMysql[6018f17a-1b92-4f35-939e-237cf6f63d22] schema data cleaned 
[INFO ] 2024-06-25 16:34:27.824 - [任务 26][SouceMysql] - Node SouceMysql[6018f17a-1b92-4f35-939e-237cf6f63d22] monitor closed 
[INFO ] 2024-06-25 16:34:27.825 - [任务 26][SouceMysql] - Node SouceMysql[6018f17a-1b92-4f35-939e-237cf6f63d22] close complete, cost 44 ms 
[INFO ] 2024-06-25 16:34:27.826 - [任务 26][表编辑] - Node 表编辑[14ecb9f2-923b-4184-ba5c-0dcadb188261] running status set to false 
[INFO ] 2024-06-25 16:34:27.826 - [任务 26][表编辑] - Node 表编辑[14ecb9f2-923b-4184-ba5c-0dcadb188261] schema data cleaned 
[INFO ] 2024-06-25 16:34:27.826 - [任务 26][表编辑] - Node 表编辑[14ecb9f2-923b-4184-ba5c-0dcadb188261] monitor closed 
[INFO ] 2024-06-25 16:34:27.826 - [任务 26][表编辑] - Node 表编辑[14ecb9f2-923b-4184-ba5c-0dcadb188261] close complete, cost 0 ms 
[INFO ] 2024-06-25 16:34:27.826 - [任务 26][SourceMongo] - Node SourceMongo[1d73b7c5-b26b-41d0-aa80-cc41b583dc99] running status set to false 
[INFO ] 2024-06-25 16:38:42.616 - [任务 26][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-1d73b7c5-b26b-41d0-aa80-cc41b583dc99 
[INFO ] 2024-06-25 16:38:42.617 - [任务 26][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-1d73b7c5-b26b-41d0-aa80-cc41b583dc99 
[INFO ] 2024-06-25 16:38:42.617 - [任务 26][SourceMongo] - Node SourceMongo[1d73b7c5-b26b-41d0-aa80-cc41b583dc99] schema data cleaned 
[INFO ] 2024-06-25 16:38:42.617 - [任务 26][SourceMongo] - Node SourceMongo[1d73b7c5-b26b-41d0-aa80-cc41b583dc99] monitor closed 
[INFO ] 2024-06-25 16:38:42.625 - [任务 26][SourceMongo] - Node SourceMongo[1d73b7c5-b26b-41d0-aa80-cc41b583dc99] close complete, cost 254796 ms 
[INFO ] 2024-06-25 16:38:44.053 - [任务 26] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 16:38:44.053 - [任务 26] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@59455fcf 
[INFO ] 2024-06-25 16:38:44.061 - [任务 26] - Stop task milestones: 667a3ed45d7b3052166837b9(任务 26)  
[INFO ] 2024-06-25 16:38:44.064 - [任务 26] - Stopped task aspect(s) 
[INFO ] 2024-06-25 16:38:44.064 - [任务 26] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 16:38:44.064 - [任务 26] - Remove memory task client succeed, task: 任务 26[667a3ed45d7b3052166837b9] 
[INFO ] 2024-06-25 16:38:44.064 - [任务 26] - Destroy memory task client cache succeed, task: 任务 26[667a3ed45d7b3052166837b9] 
[INFO ] 2024-06-25 16:43:48.565 - [任务 26] - Start task milestones: 667a3ed45d7b3052166837b9(任务 26) 
[INFO ] 2024-06-25 16:43:48.569 - [任务 26] - Task initialization... 
[INFO ] 2024-06-25 16:43:48.676 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 16:43:48.736 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 16:43:48.871 - [任务 26][SouceMysql] - Node SouceMysql[6018f17a-1b92-4f35-939e-237cf6f63d22] start preload schema,table counts: 2 
[INFO ] 2024-06-25 16:43:48.871 - [任务 26][表编辑] - Node 表编辑[14ecb9f2-923b-4184-ba5c-0dcadb188261] start preload schema,table counts: 2 
[INFO ] 2024-06-25 16:43:48.871 - [任务 26][SourceMongo] - Node SourceMongo[1d73b7c5-b26b-41d0-aa80-cc41b583dc99] start preload schema,table counts: 2 
[INFO ] 2024-06-25 16:43:48.871 - [任务 26][SourceMongo] - Node SourceMongo[1d73b7c5-b26b-41d0-aa80-cc41b583dc99] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 16:43:48.871 - [任务 26][SouceMysql] - Node SouceMysql[6018f17a-1b92-4f35-939e-237cf6f63d22] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 16:43:48.871 - [任务 26][表编辑] - Node 表编辑[14ecb9f2-923b-4184-ba5c-0dcadb188261] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 16:43:49.206 - [任务 26][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 16:43:49.207 - [任务 26][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 16:43:49.399 - [任务 26][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-06-25 16:43:49.399 - [任务 26][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-06-25 16:43:49.399 - [任务 26][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-25 16:43:49.481 - [任务 26][SouceMysql] - batch offset found: {},stream offset found: {"name":"0eb8310b-4e37-4461-8ec3-b85062884784","offset":{"{\"server\":\"0eb8310b-4e37-4461-8ec3-b85062884784\"}":"{\"file\":\"binlog.000031\",\"pos\":1060990946,\"server_id\":1}"}} 
[INFO ] 2024-06-25 16:43:49.482 - [任务 26][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 16:43:49.482 - [任务 26][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:43:49.500 - [任务 26][SouceMysql] - Starting stream read, table list: [CUSTOMER, CLAIM], offset: {"name":"0eb8310b-4e37-4461-8ec3-b85062884784","offset":{"{\"server\":\"0eb8310b-4e37-4461-8ec3-b85062884784\"}":"{\"file\":\"binlog.000031\",\"pos\":1060990946,\"server_id\":1}"}} 
[INFO ] 2024-06-25 16:43:49.545 - [任务 26][SouceMysql] - Starting mysql cdc, server name: 0eb8310b-4e37-4461-8ec3-b85062884784 
[INFO ] 2024-06-25 16:43:49.594 - [任务 26][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 590289438
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 0eb8310b-4e37-4461-8ec3-b85062884784
  database.port: 3306
  threadName: Debezium-Mysql-Connector-0eb8310b-4e37-4461-8ec3-b85062884784
  database.hostname: localhost
  database.password: ********
  name: 0eb8310b-4e37-4461-8ec3-b85062884784
  pdk.offset.string: {"name":"0eb8310b-4e37-4461-8ec3-b85062884784","offset":{"{\"server\":\"0eb8310b-4e37-4461-8ec3-b85062884784\"}":"{\"file\":\"binlog.000031\",\"pos\":1060990946,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CUSTOMER,test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 16:43:49.689 - [任务 26][SouceMysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER, CLAIM], data change syncing 
[INFO ] 2024-06-25 16:58:42.675 - [任务 26][SouceMysql] - Node SouceMysql[6018f17a-1b92-4f35-939e-237cf6f63d22] running status set to false 
[WARN ] 2024-06-25 16:58:42.770 - [任务 26][SouceMysql] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-6018f17a-1b92-4f35-939e-237cf6f63d22 
