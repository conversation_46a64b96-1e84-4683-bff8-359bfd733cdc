[TRACE] 2025-04-16 01:36:01.397 - [CDC log cache task from Sybase] - Start task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase) 
[INFO ] 2025-04-16 01:36:01.799 - [CDC log cache task from Sybase] - Loading table structure completed 
[TRACE] 2025-04-16 01:36:01.859 - [CDC log cache task from Sybase] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-16 01:36:02.066 - [CDC log cache task from Sybase] - The engine receives CDC log cache task from Sybase task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-16 01:36:02.244 - [CDC log cache task from Sybase] - Task started 
[TRACE] 2025-04-16 01:36:02.376 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] start preload schema,table counts: 2 
[TRACE] 2025-04-16 01:36:02.381 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] preload schema finished, cost 0 ms 
[TRACE] 2025-04-16 01:36:02.409 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-04-16 01:36:02.411 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-04-16 01:36:02.561 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67dbec3d0a3b2cf7b959ffd2, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_bmsql_config, version=v2, tableName=bmsql_config, externalStorageTableName=ExternalStorage_SHARE_CDC_1902686287, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 01:36:02.561 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67fe99000a3b2cf7b97ebe99, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_s2p_text_ddd, version=v2, tableName=s2p_text_ddd, externalStorageTableName=ExternalStorage_SHARE_CDC_-94928111, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 01:36:02.584 - [CDC log cache task from Sybase][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-04-16 01:36:02.585 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-04-16 01:36:02.588 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" event queue capacity: 200 
[INFO ] 2025-04-16 01:36:02.589 - [CDC log cache task from Sybase][Sybase] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-04-16 01:36:02.792 - [CDC log cache task from Sybase][Sybase] - Use existing stream offset: {"startRid":368667,"rowId":84,"h":0,"l":11434804} 
[INFO ] 2025-04-16 01:36:02.820 - [CDC log cache task from Sybase][Sybase] - Batch read completed. 
[TRACE] 2025-04-16 01:36:02.852 - [CDC log cache task from Sybase][Sybase] - Starting stream read, table list: [dbo.bmsql_config, dbo.s2p_text_ddd], offset: {"startRid":368667,"rowId":84,"h":0,"l":11434804} 
[INFO ] 2025-04-16 01:36:02.859 - [CDC log cache task from Sybase][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-04-16 01:36:03.061 - [CDC log cache task from Sybase][Sybase] - sybase cdc work with mode v1: auto rescan 
[INFO ] 2025-04-16 01:36:03.240 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav17ha?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2025-04-16 01:36:03.268 - [CDC log cache task from Sybase][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-04-16 01:36:03.269 - [CDC log cache task from Sybase][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-04-16 01:36:03.333 - [CDC log cache task from Sybase][Sybase] - opened cdc for tables: {dbo=[s2p_text_ddd, bmsql_config]} 
[INFO ] 2025-04-16 01:36:03.334 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 01:36:03.596 - [CDC log cache task from Sybase][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-04-16 01:36:05.783 - [CDC log cache task from Sybase] - This task is already running 
[TRACE] 2025-04-16 01:36:29.712 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] running status set to false 
[INFO ] 2025-04-16 01:36:29.714 - [CDC log cache task from Sybase][Sybase] - Log Miner is shutting down... 
[INFO ] 2025-04-16 01:36:50.814 - [CDC log cache task from Sybase][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-04-16 01:36:50.816 - [CDC log cache task from Sybase][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-04-16 01:36:50.823 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 0 
[INFO ] 2025-04-16 01:36:53.824 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 0 
[INFO ] 2025-04-16 01:36:54.087 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 01:36:54.100 - [CDC log cache task from Sybase][Sybase] - uncommit trans: {} 
[INFO ] 2025-04-16 01:36:54.102 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 46 
[INFO ] 2025-04-16 01:36:54.103 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368679 
[INFO ] 2025-04-16 01:36:54.104 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 125 
[INFO ] 2025-04-16 01:36:54.106 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 600 
[INFO ] 2025-04-16 01:36:54.113 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 8 
[INFO ] 2025-04-16 01:36:54.114 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.120 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: 3 
[INFO ] 2025-04-16 01:36:54.120 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.126 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.126 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 30 
[INFO ] 2025-04-16 01:36:54.127 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368679 
[INFO ] 2025-04-16 01:36:54.127 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 125 
[INFO ] 2025-04-16 01:36:54.127 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 2 
[INFO ] 2025-04-16 01:36:54.132 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.135 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 4 
[INFO ] 2025-04-16 01:36:54.138 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.139 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.149 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:, value: 2025-04-15T16:46:13.523+0800 
[INFO ] 2025-04-16 01:36:54.150 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 0 
[INFO ] 2025-04-16 01:36:54.152 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.152 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 5 
[INFO ] 2025-04-16 01:36:54.153 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 2025-04-15T16:48:06.523+0800 
[INFO ] 2025-04-16 01:36:54.153 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.158 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.158 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: _dmpxact 
[INFO ] 2025-04-16 01:36:54.161 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:, value: tester 
[INFO ] 2025-04-16 01:36:54.161 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:, value: guest1234 
[INFO ] 2025-04-16 01:36:54.165 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 17 
[INFO ] 2025-04-16 01:36:54.165 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 8202 
[INFO ] 2025-04-16 01:36:54.166 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 2025-04-15T16:48:06.523+0800 
[INFO ] 2025-04-16 01:36:54.166 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: [0, 0, 0, 0, 101, -81, -80, 0] 
[INFO ] 2025-04-16 01:36:54.167 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.167 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 5 
[INFO ] 2025-04-16 01:36:54.169 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: 0 
[INFO ] 2025-04-16 01:36:54.169 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:, value: 11579232 
[INFO ] 2025-04-16 01:36:54.170 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.170 - [CDC log cache task from Sybase][Sybase] - column: 10,columnName:, value: 6 
[INFO ] 2025-04-16 01:36:54.170 - [CDC log cache task from Sybase][Sybase] - column: 11,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.170 - [CDC log cache task from Sybase][Sybase] - column: 12,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.171 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 54 
[INFO ] 2025-04-16 01:36:54.171 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.171 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 5 
[INFO ] 2025-04-16 01:36:54.177 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 48 
[INFO ] 2025-04-16 01:36:54.178 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 576 
[INFO ] 2025-04-16 01:36:54.178 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.178 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: 7 
[INFO ] 2025-04-16 01:36:54.180 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.180 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.180 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 30 
[INFO ] 2025-04-16 01:36:54.180 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.180 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 5 
[INFO ] 2025-04-16 01:36:54.181 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 2 
[INFO ] 2025-04-16 01:36:54.184 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.184 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 15 
[INFO ] 2025-04-16 01:36:54.184 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.185 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.185 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:, value: 2025-04-15T16:48:06.523+0800 
[INFO ] 2025-04-16 01:36:54.186 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 0 
[INFO ] 2025-04-16 01:36:54.188 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.188 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 16 
[INFO ] 2025-04-16 01:36:54.188 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 2025-04-15T16:50:55.533+0800 
[INFO ] 2025-04-16 01:36:54.188 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.190 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.192 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: s2p_text_ddd 
[INFO ] 2025-04-16 01:36:54.194 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:, value: tester 
[INFO ] 2025-04-16 01:36:54.195 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:, value: guest1234 
[INFO ] 2025-04-16 01:36:54.195 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 77 
[INFO ] 2025-04-16 01:36:54.201 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.202 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 16 
[INFO ] 2025-04-16 01:36:54.204 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 152 
[INFO ] 2025-04-16 01:36:54.205 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 512 
[INFO ] 2025-04-16 01:36:54.217 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.223 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: 17 
[INFO ] 2025-04-16 01:36:54.223 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.223 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.224 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 77 
[INFO ] 2025-04-16 01:36:54.224 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.224 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 16 
[INFO ] 2025-04-16 01:36:54.224 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 152 
[INFO ] 2025-04-16 01:36:54.224 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 512 
[INFO ] 2025-04-16 01:36:54.224 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.224 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: 20 
[INFO ] 2025-04-16 01:36:54.224 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.224 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.224 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 77 
[INFO ] 2025-04-16 01:36:54.224 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.224 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 16 
[INFO ] 2025-04-16 01:36:54.228 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 152 
[INFO ] 2025-04-16 01:36:54.231 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 512 
[INFO ] 2025-04-16 01:36:54.231 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.231 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: 23 
[INFO ] 2025-04-16 01:36:54.231 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.231 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.231 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 46 
[INFO ] 2025-04-16 01:36:54.231 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.231 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 16 
[INFO ] 2025-04-16 01:36:54.231 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 600 
[INFO ] 2025-04-16 01:36:54.231 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 8 
[INFO ] 2025-04-16 01:36:54.231 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.231 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: 26 
[INFO ] 2025-04-16 01:36:54.232 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.232 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.232 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 30 
[INFO ] 2025-04-16 01:36:54.232 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.232 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 16 
[INFO ] 2025-04-16 01:36:54.232 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 2 
[INFO ] 2025-04-16 01:36:54.232 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.232 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 27 
[INFO ] 2025-04-16 01:36:54.233 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.233 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.233 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:, value: 2025-04-15T16:50:55.533+0800 
[INFO ] 2025-04-16 01:36:54.234 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 0 
[INFO ] 2025-04-16 01:36:54.234 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.234 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 28 
[INFO ] 2025-04-16 01:36:54.234 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 2025-04-15T16:55:03.500+0800 
[INFO ] 2025-04-16 01:36:54.235 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.235 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.236 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: s2p_text_ddd 
[INFO ] 2025-04-16 01:36:54.237 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:, value: tester 
[INFO ] 2025-04-16 01:36:54.237 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:, value: guest1234 
[INFO ] 2025-04-16 01:36:54.237 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 77 
[INFO ] 2025-04-16 01:36:54.238 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.238 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 28 
[INFO ] 2025-04-16 01:36:54.238 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 152 
[INFO ] 2025-04-16 01:36:54.238 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 512 
[INFO ] 2025-04-16 01:36:54.238 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.238 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: 29 
[INFO ] 2025-04-16 01:36:54.238 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.238 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.239 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 77 
[INFO ] 2025-04-16 01:36:54.239 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.243 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 28 
[INFO ] 2025-04-16 01:36:54.243 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 152 
[INFO ] 2025-04-16 01:36:54.245 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 512 
[INFO ] 2025-04-16 01:36:54.245 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.255 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: 32 
[INFO ] 2025-04-16 01:36:54.259 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.260 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.260 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 77 
[INFO ] 2025-04-16 01:36:54.261 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.261 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 28 
[INFO ] 2025-04-16 01:36:54.261 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 152 
[INFO ] 2025-04-16 01:36:54.261 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 512 
[INFO ] 2025-04-16 01:36:54.261 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.261 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: 35 
[INFO ] 2025-04-16 01:36:54.263 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.263 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.263 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 46 
[INFO ] 2025-04-16 01:36:54.263 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.263 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 28 
[INFO ] 2025-04-16 01:36:54.265 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 600 
[INFO ] 2025-04-16 01:36:54.266 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 8 
[INFO ] 2025-04-16 01:36:54.266 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.266 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: 38 
[INFO ] 2025-04-16 01:36:54.266 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.266 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.267 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 30 
[INFO ] 2025-04-16 01:36:54.267 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.268 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 28 
[INFO ] 2025-04-16 01:36:54.268 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 2 
[INFO ] 2025-04-16 01:36:54.268 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.268 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 39 
[INFO ] 2025-04-16 01:36:54.268 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.268 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.268 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:, value: 2025-04-15T16:55:03.500+0800 
[INFO ] 2025-04-16 01:36:54.269 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 0 
[INFO ] 2025-04-16 01:36:54.269 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.269 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 40 
[INFO ] 2025-04-16 01:36:54.269 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 2025-04-15T17:10:56.516+0800 
[INFO ] 2025-04-16 01:36:54.269 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.270 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.270 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: _ins 
[INFO ] 2025-04-16 01:36:54.270 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:, value: null 
[INFO ] 2025-04-16 01:36:54.270 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:, value: null 
[INFO ] 2025-04-16 01:36:54.271 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 13 
[INFO ] 2025-04-16 01:36:54.271 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.271 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 40 
[INFO ] 2025-04-16 01:36:54.271 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 128 
[INFO ] 2025-04-16 01:36:54.271 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 512 
[INFO ] 2025-04-16 01:36:54.271 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.272 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: 42 
[INFO ] 2025-04-16 01:36:54.272 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.274 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.274 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 32 
[INFO ] 2025-04-16 01:36:54.275 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.275 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 40 
[INFO ] 2025-04-16 01:36:54.276 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 64 
[INFO ] 2025-04-16 01:36:54.276 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 8 
[INFO ] 2025-04-16 01:36:54.276 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.276 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: 43 
[INFO ] 2025-04-16 01:36:54.277 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.277 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.277 - [CDC log cache task from Sybase][Sybase] - find TEXT UPDATE in auto miner, will fallback to manual miner to get it, transId: 368680-40, startRid: 368680, rowId: 40 
[INFO ] 2025-04-16 01:36:54.277 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 4 
[INFO ] 2025-04-16 01:36:54.277 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.278 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 40 
[INFO ] 2025-04-16 01:36:54.278 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 2056 
[INFO ] 2025-04-16 01:36:54.278 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: [0, 0, 0, 0, -67, -81, -80, 0] 
[INFO ] 2025-04-16 01:36:54.278 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: [0, 0, 0, 0, -5, -81, -80, 0] 
[INFO ] 2025-04-16 01:36:54.278 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.278 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:, value: 48 
[INFO ] 2025-04-16 01:36:54.278 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.278 - [CDC log cache task from Sybase][Sybase] - column: 10,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.279 - [CDC log cache task from Sybase][Sybase] - column: 11,columnName:, value: s2p_text_ddd 
[INFO ] 2025-04-16 01:36:54.279 - [CDC log cache task from Sybase][Sybase] - column: 12,columnName:, value: dbo 
[INFO ] 2025-04-16 01:36:54.289 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 7 
[INFO ] 2025-04-16 01:36:54.293 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.293 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 40 
[INFO ] 2025-04-16 01:36:54.293 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 64 
[INFO ] 2025-04-16 01:36:54.293 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 8 
[INFO ] 2025-04-16 01:36:54.293 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.293 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: 49 
[INFO ] 2025-04-16 01:36:54.293 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.293 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.294 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 30 
[INFO ] 2025-04-16 01:36:54.294 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.294 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 40 
[INFO ] 2025-04-16 01:36:54.294 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 2 
[INFO ] 2025-04-16 01:36:54.295 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:54.295 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 52 
[INFO ] 2025-04-16 01:36:54.296 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:54.296 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:54.297 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:, value: 2025-04-15T17:10:56.516+0800 
[INFO ] 2025-04-16 01:36:54.369 - [CDC log cache task from Sybase][Sybase] - transBroken find, will rescan: 368680, 40 
[INFO ] 2025-04-16 01:36:55.021 - [CDC log cache task from Sybase][Sybase] - rescan archive: 368680, 40, size: 2, cost: 723 ms 
[INFO ] 2025-04-16 01:36:55.021 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 0 
[INFO ] 2025-04-16 01:36:55.022 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:55.022 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 53 
[INFO ] 2025-04-16 01:36:55.022 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 2025-04-15T17:11:51.503+0800 
[INFO ] 2025-04-16 01:36:55.022 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:55.022 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:55.023 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:, value: _systsflush 
[INFO ] 2025-04-16 01:36:55.023 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:, value: null 
[INFO ] 2025-04-16 01:36:55.023 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:, value: null 
[INFO ] 2025-04-16 01:36:55.025 - [CDC log cache task from Sybase][Sybase] - column: 1,columnName:, value: 30 
[INFO ] 2025-04-16 01:36:55.025 - [CDC log cache task from Sybase][Sybase] - column: 2,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:55.025 - [CDC log cache task from Sybase][Sybase] - column: 3,columnName:, value: 53 
[INFO ] 2025-04-16 01:36:55.026 - [CDC log cache task from Sybase][Sybase] - column: 4,columnName:, value: 2 
[INFO ] 2025-04-16 01:36:55.026 - [CDC log cache task from Sybase][Sybase] - column: 5,columnName:, value: 368680 
[INFO ] 2025-04-16 01:36:55.026 - [CDC log cache task from Sybase][Sybase] - column: 6,columnName:, value: 55 
[INFO ] 2025-04-16 01:36:55.026 - [CDC log cache task from Sybase][Sybase] - column: 7,columnName:log_ts_high, value: 0 
[INFO ] 2025-04-16 01:36:55.026 - [CDC log cache task from Sybase][Sybase] - column: 8,columnName:log_ts_low, value: 11579232 
[INFO ] 2025-04-16 01:36:55.027 - [CDC log cache task from Sybase][Sybase] - column: 9,columnName:, value: 2025-04-15T17:11:51.503+0800 
[INFO ] 2025-04-16 01:36:55.028 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[TRACE] 2025-04-16 01:36:55.226 - [CDC log cache task from Sybase][Sybase] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744738562508 
[TRACE] 2025-04-16 01:36:55.226 - [CDC log cache task from Sybase][Sybase] - PDK connector node released: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744738562508 
[TRACE] 2025-04-16 01:36:55.227 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] schema data cleaned 
[TRACE] 2025-04-16 01:36:55.227 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] monitor closed 
[TRACE] 2025-04-16 01:36:55.231 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] close complete, cost 25521 ms 
[TRACE] 2025-04-16 01:36:55.231 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] running status set to false 
[TRACE] 2025-04-16 01:36:55.233 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[TRACE] 2025-04-16 01:36:55.233 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node released: null 
[TRACE] 2025-04-16 01:36:55.233 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] schema data cleaned 
[TRACE] 2025-04-16 01:36:55.234 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] monitor closed 
[TRACE] 2025-04-16 01:36:55.434 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] close complete, cost 2 ms 
[INFO ] 2025-04-16 01:36:58.069 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:36:58.471 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[TRACE] 2025-04-16 01:36:59.941 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 01:36:59.941 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7ef00004 
[TRACE] 2025-04-16 01:37:00.062 - [CDC log cache task from Sybase] - Stop task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase)  
[TRACE] 2025-04-16 01:37:00.063 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[TRACE] 2025-04-16 01:37:00.064 - [CDC log cache task from Sybase] - Snapshot order controller have been removed 
[INFO ] 2025-04-16 01:37:00.065 - [CDC log cache task from Sybase] - Task stopped. 
[INFO ] 2025-04-16 01:37:01.480 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:01.682 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:37:04.688 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:05.092 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[TRACE] 2025-04-16 01:37:05.520 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 01:37:05.520 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7ef00004 
[TRACE] 2025-04-16 01:37:05.520 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[INFO ] 2025-04-16 01:37:05.520 - [CDC log cache task from Sybase] - Task stopped. 
[TRACE] 2025-04-16 01:37:05.543 - [CDC log cache task from Sybase] - Remove memory task client succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[TRACE] 2025-04-16 01:37:05.545 - [CDC log cache task from Sybase] - Destroy memory task client cache succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[INFO ] 2025-04-16 01:37:08.168 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:08.313 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:37:11.318 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:11.729 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:37:14.781 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:15.190 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:37:18.244 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:18.360 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:37:21.366 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:21.770 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:37:24.822 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:24.934 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:37:27.941 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:28.348 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:37:31.171 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:31.452 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:37:34.460 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:34.871 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:37:37.881 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:38.194 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:37:41.199 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:41.602 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[TRACE] 2025-04-16 01:37:44.224 - [CDC log cache task from Sybase] - Start task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase) 
[INFO ] 2025-04-16 01:37:44.631 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:44.769 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:37:45.171 - [CDC log cache task from Sybase] - Loading table structure completed 
[TRACE] 2025-04-16 01:37:45.384 - [CDC log cache task from Sybase] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-16 01:37:45.586 - [CDC log cache task from Sybase] - The engine receives CDC log cache task from Sybase task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-16 01:37:45.586 - [CDC log cache task from Sybase] - Task started 
[TRACE] 2025-04-16 01:37:45.664 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] start preload schema,table counts: 2 
[TRACE] 2025-04-16 01:37:45.664 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] preload schema finished, cost 0 ms 
[TRACE] 2025-04-16 01:37:45.722 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-04-16 01:37:45.722 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-04-16 01:37:45.744 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67fe99000a3b2cf7b97ebe99, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_s2p_text_ddd, version=v2, tableName=s2p_text_ddd, externalStorageTableName=ExternalStorage_SHARE_CDC_-94928111, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 01:37:45.744 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67dbec3d0a3b2cf7b959ffd2, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_bmsql_config, version=v2, tableName=bmsql_config, externalStorageTableName=ExternalStorage_SHARE_CDC_1902686287, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 01:37:45.749 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.s2p_text_ddd', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_-94928111', head seq: 0, tail seq: 0 
[INFO ] 2025-04-16 01:37:45.749 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.bmsql_config', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_1902686287', head seq: 0, tail seq: 13830 
[INFO ] 2025-04-16 01:37:45.802 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav17ha?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2025-04-16 01:37:46.221 - [CDC log cache task from Sybase][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-04-16 01:37:46.223 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-04-16 01:37:46.223 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-04-16 01:37:46.223 - [CDC log cache task from Sybase][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-16 01:37:46.223 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 01:37:46.423 - [CDC log cache task from Sybase][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-04-16 01:37:46.734 - [CDC log cache task from Sybase][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 01:37:46.839 - [CDC log cache task from Sybase][Sybase] - Batch read completed. 
[TRACE] 2025-04-16 01:37:46.839 - [CDC log cache task from Sybase][Sybase] - Starting stream read, table list: [dbo.bmsql_config, dbo.s2p_text_ddd], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 01:37:46.839 - [CDC log cache task from Sybase][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-04-16 01:37:46.905 - [CDC log cache task from Sybase][Sybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-04-16 01:37:47.775 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:48.181 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:37:51.232 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:51.343 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:37:54.349 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:54.754 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:37:57.795 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:37:58.105 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:38:01.137 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:38:01.542 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:38:04.596 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:38:04.692 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:38:07.696 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:38:07.928 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:38:10.985 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:38:11.395 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:38:14.241 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:38:14.441 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:38:17.445 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:38:17.770 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:38:20.777 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:38:21.202 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:38:24.205 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:38:24.815 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:38:27.636 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:38:27.915 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:38:30.921 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:38:31.326 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[TRACE] 2025-04-16 01:38:34.114 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] running status set to false 
[INFO ] 2025-04-16 01:38:34.119 - [CDC log cache task from Sybase][Sybase] - Log Miner is shutting down... 
[WARN ] 2025-04-16 01:38:34.123 - [CDC log cache task from Sybase][Sybase] - io.tapdata.sybase.extend.SybaseContext.getConnection(SybaseContext.java:134) 
[WARN ] 2025-04-16 01:38:34.124 - [CDC log cache task from Sybase][Sybase] - io.tapdata.sybase.SybaseBeforeCdc.doBefore(SybaseBeforeCdc.java:53) 
[WARN ] 2025-04-16 01:38:34.124 - [CDC log cache task from Sybase][Sybase] - io.tapdata.sybase.cdc.SybaseLogMiner.startMinerV2(SybaseLogMiner.java:2081) 
[WARN ] 2025-04-16 01:38:34.124 - [CDC log cache task from Sybase][Sybase] - io.tapdata.sybase.SybaseConnectorV2.multiStreamReadV2(SybaseConnectorV2.java:576) 
[WARN ] 2025-04-16 01:38:34.124 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$27(HazelcastSourcePdkDataNode.java:866) 
[WARN ] 2025-04-16 01:38:34.125 - [CDC log cache task from Sybase][Sybase] - io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165) 
[WARN ] 2025-04-16 01:38:34.125 - [CDC log cache task from Sybase][Sybase] - io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154) 
[WARN ] 2025-04-16 01:38:34.125 - [CDC log cache task from Sybase][Sybase] - io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27) 
[WARN ] 2025-04-16 01:38:34.125 - [CDC log cache task from Sybase][Sybase] - io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154) 
[WARN ] 2025-04-16 01:38:34.125 - [CDC log cache task from Sybase][Sybase] - io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96) 
[WARN ] 2025-04-16 01:38:34.125 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:926) 
[WARN ] 2025-04-16 01:38:34.125 - [CDC log cache task from Sybase][Sybase] - io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67) 
[WARN ] 2025-04-16 01:38:34.126 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167) 
[WARN ] 2025-04-16 01:38:34.126 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:916) 
[WARN ] 2025-04-16 01:38:34.126 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:777) 
[WARN ] 2025-04-16 01:38:34.127 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:290) 
[WARN ] 2025-04-16 01:38:34.127 - [CDC log cache task from Sybase][Sybase] - java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) 
[WARN ] 2025-04-16 01:38:34.127 - [CDC log cache task from Sybase][Sybase] - java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) 
[WARN ] 2025-04-16 01:38:34.128 - [CDC log cache task from Sybase][Sybase] - java.base/java.util.concurrent.FutureTask.run(FutureTask.java) 
[WARN ] 2025-04-16 01:38:34.128 - [CDC log cache task from Sybase][Sybase] - java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) 
[WARN ] 2025-04-16 01:38:34.128 - [CDC log cache task from Sybase][Sybase] - java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) 
[WARN ] 2025-04-16 01:38:34.174 - [CDC log cache task from Sybase][Sybase] - java.base/java.lang.Thread.run(Thread.java:840) 
[INFO ] 2025-04-16 01:38:34.174 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:38:34.789 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:38:37.769 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:38:37.918 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[TRACE] 2025-04-16 01:38:39.378 - [CDC log cache task from Sybase][Sybase] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744738666105 
[TRACE] 2025-04-16 01:38:39.378 - [CDC log cache task from Sybase][Sybase] - PDK connector node released: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744738666105 
[TRACE] 2025-04-16 01:38:39.379 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] schema data cleaned 
[TRACE] 2025-04-16 01:38:39.379 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] monitor closed 
[TRACE] 2025-04-16 01:38:39.382 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] close complete, cost 5268 ms 
[TRACE] 2025-04-16 01:38:39.383 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] running status set to false 
[TRACE] 2025-04-16 01:38:39.402 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[TRACE] 2025-04-16 01:38:39.402 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node released: null 
[TRACE] 2025-04-16 01:38:39.402 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] schema data cleaned 
[TRACE] 2025-04-16 01:38:39.404 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] monitor closed 
[TRACE] 2025-04-16 01:38:39.404 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] close complete, cost 21 ms 
[INFO ] 2025-04-16 01:38:40.919 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[TRACE] 2025-04-16 01:38:40.949 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 01:38:40.950 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7c8f1f5b 
[TRACE] 2025-04-16 01:38:41.076 - [CDC log cache task from Sybase] - Stop task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase)  
[TRACE] 2025-04-16 01:38:41.077 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[TRACE] 2025-04-16 01:38:41.077 - [CDC log cache task from Sybase] - Snapshot order controller have been removed 
[INFO ] 2025-04-16 01:38:41.077 - [CDC log cache task from Sybase] - Task stopped. 
[INFO ] 2025-04-16 01:38:41.284 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:38:44.338 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:38:44.490 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:38:47.493 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:38:47.905 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:38:50.961 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:38:51.105 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:38:54.109 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:38:54.489 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:38:57.540 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:38:57.946 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:39:00.761 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:01.084 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:39:04.090 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:04.292 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:39:07.342 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:07.950 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[TRACE] 2025-04-16 01:39:10.564 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 01:39:10.564 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7c8f1f5b 
[TRACE] 2025-04-16 01:39:10.564 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[INFO ] 2025-04-16 01:39:10.564 - [CDC log cache task from Sybase] - Task stopped. 
[TRACE] 2025-04-16 01:39:10.603 - [CDC log cache task from Sybase] - Remove memory task client succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[TRACE] 2025-04-16 01:39:10.603 - [CDC log cache task from Sybase] - Destroy memory task client cache succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[INFO ] 2025-04-16 01:39:10.892 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:10.893 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:39:13.896 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:14.505 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:39:17.443 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:17.781 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:39:20.809 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:21.215 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:39:24.042 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:24.391 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:39:27.397 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:27.801 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:39:30.855 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:30.965 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:39:33.971 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:34.578 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:39:37.629 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:37.807 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:39:40.813 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:41.422 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:39:44.270 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:44.676 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:39:47.729 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:47.933 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:39:50.799 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:51.014 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:39:54.067 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:54.254 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:39:57.258 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:39:57.578 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:40:00.618 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:01.028 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:40:03.848 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:04.195 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:40:07.199 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:07.605 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:40:10.454 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:10.726 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:40:13.760 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:14.167 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[TRACE] 2025-04-16 01:40:15.649 - [CDC log cache task from Sybase] - Start task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase) 
[INFO ] 2025-04-16 01:40:15.649 - [CDC log cache task from Sybase] - Loading table structure completed 
[TRACE] 2025-04-16 01:40:15.695 - [CDC log cache task from Sybase] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-16 01:40:15.695 - [CDC log cache task from Sybase] - The engine receives CDC log cache task from Sybase task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-16 01:40:15.785 - [CDC log cache task from Sybase] - Task started 
[TRACE] 2025-04-16 01:40:15.785 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] start preload schema,table counts: 2 
[TRACE] 2025-04-16 01:40:15.785 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] preload schema finished, cost 0 ms 
[TRACE] 2025-04-16 01:40:15.792 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-04-16 01:40:15.792 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-04-16 01:40:15.802 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67fe99000a3b2cf7b97ebe99, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_s2p_text_ddd, version=v2, tableName=s2p_text_ddd, externalStorageTableName=ExternalStorage_SHARE_CDC_-94928111, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 01:40:15.802 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67dbec3d0a3b2cf7b959ffd2, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_bmsql_config, version=v2, tableName=bmsql_config, externalStorageTableName=ExternalStorage_SHARE_CDC_1902686287, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 01:40:15.814 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.s2p_text_ddd', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_-94928111', head seq: 0, tail seq: 0 
[INFO ] 2025-04-16 01:40:15.814 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.bmsql_config', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_1902686287', head seq: 0, tail seq: 13830 
[INFO ] 2025-04-16 01:40:16.017 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav17ha?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2025-04-16 01:40:16.243 - [CDC log cache task from Sybase][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-04-16 01:40:16.243 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-04-16 01:40:16.243 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-04-16 01:40:16.243 - [CDC log cache task from Sybase][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-16 01:40:16.243 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 01:40:16.449 - [CDC log cache task from Sybase][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-04-16 01:40:17.261 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:17.668 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:40:20.478 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:20.855 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:40:23.862 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:24.271 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:40:27.115 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:27.522 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:40:30.347 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:30.523 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:40:33.527 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:33.936 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:40:36.985 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:37.046 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:40:40.091 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:40.493 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:40:43.527 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:44.136 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:40:46.951 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:47.317 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:40:50.324 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:50.633 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:40:53.674 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:54.289 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:40:57.340 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:40:57.541 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:00.596 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:00.797 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:03.717 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:04.079 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:06.981 - [CDC log cache task from Sybase][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 01:41:06.982 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:07.039 - [CDC log cache task from Sybase][Sybase] - Batch read completed. 
[TRACE] 2025-04-16 01:41:07.039 - [CDC log cache task from Sybase][Sybase] - Starting stream read, table list: [dbo.bmsql_config, dbo.s2p_text_ddd], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 01:41:07.039 - [CDC log cache task from Sybase][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-04-16 01:41:07.241 - [CDC log cache task from Sybase][Sybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-04-16 01:41:07.292 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:10.335 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:10.741 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:13.794 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:14.191 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:17.196 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:17.603 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:20.451 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:20.853 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:23.695 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:24.100 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:26.931 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:27.283 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:30.287 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:30.656 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:33.659 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:33.975 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:36.978 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:37.385 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:40.435 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:40.435 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:43.482 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:43.892 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:46.692 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:47.167 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:50.225 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:50.628 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:53.474 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:54.083 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:41:56.915 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:41:57.260 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[WARN ] 2025-04-16 01:41:57.719 - [CDC log cache task from Sybase][Sybase] - io.tapdata.sybase.extend.SybaseContext.getConnection(SybaseContext.java:134) 
[WARN ] 2025-04-16 01:41:57.720 - [CDC log cache task from Sybase][Sybase] - io.tapdata.sybase.SybaseBeforeCdc.doBefore(SybaseBeforeCdc.java:53) 
[WARN ] 2025-04-16 01:41:57.720 - [CDC log cache task from Sybase][Sybase] - io.tapdata.sybase.cdc.SybaseLogMiner.startMinerV2(SybaseLogMiner.java:2081) 
[WARN ] 2025-04-16 01:41:57.720 - [CDC log cache task from Sybase][Sybase] - io.tapdata.sybase.SybaseConnectorV2.multiStreamReadV2(SybaseConnectorV2.java:576) 
[WARN ] 2025-04-16 01:41:57.721 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$27(HazelcastSourcePdkDataNode.java:866) 
[WARN ] 2025-04-16 01:41:57.722 - [CDC log cache task from Sybase][Sybase] - io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165) 
[WARN ] 2025-04-16 01:41:57.722 - [CDC log cache task from Sybase][Sybase] - io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154) 
[WARN ] 2025-04-16 01:41:57.724 - [CDC log cache task from Sybase][Sybase] - io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27) 
[WARN ] 2025-04-16 01:41:57.724 - [CDC log cache task from Sybase][Sybase] - io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154) 
[WARN ] 2025-04-16 01:41:57.724 - [CDC log cache task from Sybase][Sybase] - io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96) 
[WARN ] 2025-04-16 01:41:57.725 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:926) 
[WARN ] 2025-04-16 01:41:57.725 - [CDC log cache task from Sybase][Sybase] - io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67) 
[WARN ] 2025-04-16 01:41:57.725 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167) 
[WARN ] 2025-04-16 01:41:57.725 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:916) 
[WARN ] 2025-04-16 01:41:57.725 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:777) 
[WARN ] 2025-04-16 01:41:57.725 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:290) 
[WARN ] 2025-04-16 01:41:57.726 - [CDC log cache task from Sybase][Sybase] - java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) 
[WARN ] 2025-04-16 01:41:57.726 - [CDC log cache task from Sybase][Sybase] - java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) 
[WARN ] 2025-04-16 01:41:57.726 - [CDC log cache task from Sybase][Sybase] - java.base/java.util.concurrent.FutureTask.run(FutureTask.java) 
[WARN ] 2025-04-16 01:41:57.726 - [CDC log cache task from Sybase][Sybase] - java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) 
[WARN ] 2025-04-16 01:41:57.726 - [CDC log cache task from Sybase][Sybase] - java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) 
[WARN ] 2025-04-16 01:41:57.853 - [CDC log cache task from Sybase][Sybase] - java.base/java.lang.Thread.run(Thread.java:840) 
[TRACE] 2025-04-16 01:41:57.853 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] running status set to false 
[INFO ] 2025-04-16 01:41:57.866 - [CDC log cache task from Sybase][Sybase] - Log Miner is shutting down... 
[TRACE] 2025-04-16 01:41:57.867 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] running status set to false 
[TRACE] 2025-04-16 01:41:57.884 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[TRACE] 2025-04-16 01:41:57.884 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node released: null 
[TRACE] 2025-04-16 01:41:57.884 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] schema data cleaned 
[TRACE] 2025-04-16 01:41:57.886 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] monitor closed 
[TRACE] 2025-04-16 01:41:57.886 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] close complete, cost 20 ms 
[INFO ] 2025-04-16 01:42:00.264 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:00.667 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:42:03.489 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:03.671 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:42:06.677 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:07.059 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:42:10.065 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:10.472 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:42:13.525 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:13.903 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:42:16.909 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:17.319 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:42:20.163 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:20.370 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:42:23.425 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:23.543 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:42:26.577 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:26.983 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:42:29.814 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:30.290 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:42:33.343 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:33.749 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:42:36.792 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:37.202 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:42:40.040 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:40.335 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:42:43.385 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:43.789 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:42:46.832 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:46.950 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[TRACE] 2025-04-16 01:42:48.525 - [CDC log cache task from Sybase][Sybase] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744738816159 
[TRACE] 2025-04-16 01:42:48.526 - [CDC log cache task from Sybase][Sybase] - PDK connector node released: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744738816159 
[TRACE] 2025-04-16 01:42:48.526 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] schema data cleaned 
[TRACE] 2025-04-16 01:42:48.526 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] monitor closed 
[TRACE] 2025-04-16 01:42:48.730 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] close complete, cost 50676 ms 
[INFO ] 2025-04-16 01:42:49.956 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:50.557 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:42:50.781 - [CDC log cache task from Sybase] - Task [CDC log cache task from Sybase] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-04-16 01:42:50.781 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 01:42:50.781 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@552b3042 
[TRACE] 2025-04-16 01:42:50.781 - [CDC log cache task from Sybase] - Stop task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase)  
[TRACE] 2025-04-16 01:42:50.907 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[TRACE] 2025-04-16 01:42:50.907 - [CDC log cache task from Sybase] - Snapshot order controller have been removed 
[INFO ] 2025-04-16 01:42:50.907 - [CDC log cache task from Sybase] - Task stopped. 
[INFO ] 2025-04-16 01:42:53.541 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:53.702 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[TRACE] 2025-04-16 01:42:56.655 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 01:42:56.655 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@552b3042 
[TRACE] 2025-04-16 01:42:56.656 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[INFO ] 2025-04-16 01:42:56.656 - [CDC log cache task from Sybase] - Task stopped. 
[TRACE] 2025-04-16 01:42:56.673 - [CDC log cache task from Sybase] - Remove memory task client succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[TRACE] 2025-04-16 01:42:56.675 - [CDC log cache task from Sybase] - Destroy memory task client cache succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[INFO ] 2025-04-16 01:42:56.880 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:42:57.014 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:43:00.018 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:43:00.183 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:43:03.215 - [CDC log cache task from Sybase][Sybase] - rebuild statement with 368680, 55 
[INFO ] 2025-04-16 01:43:03.467 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368680, rowId: 55 
[TRACE] 2025-04-16 01:44:20.673 - [CDC log cache task from Sybase] - Start task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase) 
[INFO ] 2025-04-16 01:44:20.965 - [CDC log cache task from Sybase] - Loading table structure completed 
[TRACE] 2025-04-16 01:44:20.965 - [CDC log cache task from Sybase] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-16 01:44:21.159 - [CDC log cache task from Sybase] - The engine receives CDC log cache task from Sybase task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-16 01:44:21.159 - [CDC log cache task from Sybase] - Task started 
[TRACE] 2025-04-16 01:44:21.261 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] start preload schema,table counts: 2 
[TRACE] 2025-04-16 01:44:21.263 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] preload schema finished, cost 0 ms 
[TRACE] 2025-04-16 01:44:21.270 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-04-16 01:44:21.277 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-04-16 01:44:21.310 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67dbec3d0a3b2cf7b959ffd2, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_bmsql_config, version=v2, tableName=bmsql_config, externalStorageTableName=ExternalStorage_SHARE_CDC_1902686287, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 01:44:21.314 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67fe99000a3b2cf7b97ebe99, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_s2p_text_ddd, version=v2, tableName=s2p_text_ddd, externalStorageTableName=ExternalStorage_SHARE_CDC_-94928111, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 01:44:21.625 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav17ha?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2025-04-16 01:44:21.852 - [CDC log cache task from Sybase][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-04-16 01:44:21.854 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-04-16 01:44:21.855 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-04-16 01:44:21.857 - [CDC log cache task from Sybase][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-16 01:44:21.861 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 01:44:22.068 - [CDC log cache task from Sybase][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-04-16 01:44:22.407 - [CDC log cache task from Sybase][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 01:44:22.496 - [CDC log cache task from Sybase][Sybase] - Batch read completed. 
[TRACE] 2025-04-16 01:44:22.497 - [CDC log cache task from Sybase][Sybase] - Starting stream read, table list: [dbo.bmsql_config, dbo.s2p_text_ddd], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 01:44:22.497 - [CDC log cache task from Sybase][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-04-16 01:44:22.700 - [CDC log cache task from Sybase][Sybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-04-16 01:44:22.942 - [CDC log cache task from Sybase][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-04-16 01:44:22.942 - [CDC log cache task from Sybase][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-04-16 01:44:23.048 - [CDC log cache task from Sybase][Sybase] - opened cdc for tables: {dbo=[s2p_text_ddd, bmsql_config]} 
[INFO ] 2025-04-16 01:44:23.049 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 01:44:23.251 - [CDC log cache task from Sybase][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-04-16 01:44:23.395 - [CDC log cache task from Sybase][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-04-16 01:44:26.371 - [CDC log cache task from Sybase][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-04-16 01:44:26.589 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368680, rowId: 0 
[INFO ] 2025-04-16 01:44:28.156 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 0, size: 14, cost: 1753 ms 
[INFO ] 2025-04-16 01:44:28.162 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 56, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 01:44:30.437 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 0, size: 14, cost: 265 ms 
[INFO ] 2025-04-16 01:44:30.442 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 56, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 01:44:32.639 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 0, size: 14, cost: 187 ms 
[INFO ] 2025-04-16 01:44:32.641 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 56, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 01:44:34.925 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 0, size: 14, cost: 279 ms 
[INFO ] 2025-04-16 01:44:34.926 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 56, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 01:44:37.481 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 0, size: 14, cost: 548 ms 
[INFO ] 2025-04-16 01:44:37.482 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 56, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 01:44:39.511 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 01:44:39.517 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:44:39.782 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 55, size: 1, cost: 270 ms 
[INFO ] 2025-04-16 01:44:39.782 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 01:44:41.964 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 55, size: 1, cost: 170 ms 
[INFO ] 2025-04-16 01:44:41.965 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[TRACE] 2025-04-16 01:44:42.620 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@783c517a: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":28800000,"tableId":"s2p_text_ddd","time":1744739079506,"type":205} 
[TRACE] 2025-04-16 01:44:42.624 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@21030787: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744735855533,"tableId":"s2p_text_ddd","time":1744739079523,"type":205} 
[TRACE] 2025-04-16 01:44:42.624 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@5e315b55: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744736103500,"tableId":"s2p_text_ddd","time":1744739079523,"type":205} 
[TRACE] 2025-04-16 01:44:43.472 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@783c517a: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":28800000,"tableId":"s2p_text_ddd","time":1744739079506,"type":205}). Wait for all previous events to be processed 
[TRACE] 2025-04-16 01:44:43.475 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@783c517a: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":28800000,"tableId":"s2p_text_ddd","time":1744739079506,"type":205}) 
[TRACE] 2025-04-16 01:44:43.476 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@21030787: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744735855533,"tableId":"s2p_text_ddd","time":1744739079523,"type":205}). Wait for all previous events to be processed 
[INFO ] 2025-04-16 01:44:44.128 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 55, size: 1, cost: 151 ms 
[INFO ] 2025-04-16 01:44:44.130 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[TRACE] 2025-04-16 01:44:44.506 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@21030787: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744735855533,"tableId":"s2p_text_ddd","time":1744739079523,"type":205}) 
[TRACE] 2025-04-16 01:44:44.507 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@5e315b55: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744736103500,"tableId":"s2p_text_ddd","time":1744739079523,"type":205}). Wait for all previous events to be processed 
[TRACE] 2025-04-16 01:44:45.535 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@5e315b55: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744736103500,"tableId":"s2p_text_ddd","time":1744739079523,"type":205}) 
[INFO ] 2025-04-16 01:44:46.300 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 55, size: 1, cost: 162 ms 
[INFO ] 2025-04-16 01:44:46.509 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 01:44:48.593 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 55, size: 1, cost: 278 ms 
[INFO ] 2025-04-16 01:44:48.593 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 01:44:50.608 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 01:44:50.821 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:45:00.920 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 55, size: 1, cost: 288 ms 
[INFO ] 2025-04-16 01:45:00.923 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 01:45:03.185 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 55, size: 1, cost: 257 ms 
[INFO ] 2025-04-16 01:45:03.189 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 01:45:05.362 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 55, size: 1, cost: 167 ms 
[INFO ] 2025-04-16 01:45:05.571 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 01:45:07.774 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 55, size: 1, cost: 402 ms 
[INFO ] 2025-04-16 01:45:07.775 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 01:45:10.003 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 55, size: 1, cost: 216 ms 
[INFO ] 2025-04-16 01:45:10.021 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 01:45:12.022 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 01:45:12.035 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 55 
[INFO ] 2025-04-16 01:45:22.454 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 55, size: 11, cost: 431 ms 
[INFO ] 2025-04-16 01:45:22.455 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 19, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 01:45:24.839 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 55, size: 11, cost: 379 ms 
[INFO ] 2025-04-16 01:45:24.840 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 19, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 01:45:27.062 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 55, size: 13, cost: 204 ms 
[INFO ] 2025-04-16 01:45:27.063 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 32, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 01:45:29.300 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 55, size: 13, cost: 230 ms 
[INFO ] 2025-04-16 01:45:29.506 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 32, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 01:45:31.541 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 55, size: 13, cost: 229 ms 
[INFO ] 2025-04-16 01:45:31.751 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 32, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 01:45:33.554 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 01:45:33.556 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368680, rowId: 86 
[INFO ] 2025-04-16 01:45:33.736 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 86, size: 1, cost: 178 ms 
[INFO ] 2025-04-16 01:45:33.738 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 01:45:36.105 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 86, size: 1, cost: 359 ms 
[INFO ] 2025-04-16 01:45:36.107 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 01:45:38.370 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 86, size: 1, cost: 252 ms 
[INFO ] 2025-04-16 01:45:38.371 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 01:45:40.530 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 86, size: 1, cost: 157 ms 
[INFO ] 2025-04-16 01:45:40.531 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 01:45:42.684 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 86, size: 1, cost: 145 ms 
[INFO ] 2025-04-16 01:45:42.685 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 01:45:44.694 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 01:45:44.697 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 86 
[INFO ] 2025-04-16 01:45:55.085 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 86, size: 3, cost: 386 ms 
[INFO ] 2025-04-16 01:45:55.087 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 01:45:57.390 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 86, size: 3, cost: 300 ms 
[INFO ] 2025-04-16 01:45:57.391 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 01:45:59.544 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 86, size: 3, cost: 145 ms 
[INFO ] 2025-04-16 01:45:59.545 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 01:46:01.758 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 86, size: 3, cost: 206 ms 
[INFO ] 2025-04-16 01:46:01.760 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 4 to 5 
[TRACE] 2025-04-16 01:46:01.911 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] running status set to false 
[INFO ] 2025-04-16 01:46:02.058 - [CDC log cache task from Sybase][Sybase] - Log Miner is shutting down... 
[INFO ] 2025-04-16 01:46:02.060 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 86, size: 3, cost: 139 ms 
[INFO ] 2025-04-16 01:46:02.060 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 01:46:04.071 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[TRACE] 2025-04-16 01:46:07.343 - [CDC log cache task from Sybase][Sybase] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744739061783 
[TRACE] 2025-04-16 01:46:07.343 - [CDC log cache task from Sybase][Sybase] - PDK connector node released: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744739061783 
[TRACE] 2025-04-16 01:46:07.344 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] schema data cleaned 
[TRACE] 2025-04-16 01:46:07.344 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] monitor closed 
[TRACE] 2025-04-16 01:46:07.350 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] close complete, cost 5440 ms 
[TRACE] 2025-04-16 01:46:07.350 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] running status set to false 
[TRACE] 2025-04-16 01:46:07.357 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[TRACE] 2025-04-16 01:46:07.357 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node released: null 
[TRACE] 2025-04-16 01:46:07.358 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] schema data cleaned 
[TRACE] 2025-04-16 01:46:07.360 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] monitor closed 
[TRACE] 2025-04-16 01:46:07.362 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] close complete, cost 10 ms 
[TRACE] 2025-04-16 01:46:11.499 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 01:46:11.499 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@20ac1420 
[TRACE] 2025-04-16 01:46:11.503 - [CDC log cache task from Sybase] - Stop task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase)  
[TRACE] 2025-04-16 01:46:11.642 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[TRACE] 2025-04-16 01:46:11.652 - [CDC log cache task from Sybase] - Snapshot order controller have been removed 
[INFO ] 2025-04-16 01:46:11.653 - [CDC log cache task from Sybase] - Task stopped. 
[TRACE] 2025-04-16 01:46:16.660 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 01:46:16.662 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@20ac1420 
[TRACE] 2025-04-16 01:46:16.662 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[INFO ] 2025-04-16 01:46:16.662 - [CDC log cache task from Sybase] - Task stopped. 
[TRACE] 2025-04-16 01:46:16.706 - [CDC log cache task from Sybase] - Remove memory task client succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[TRACE] 2025-04-16 01:46:16.708 - [CDC log cache task from Sybase] - Destroy memory task client cache succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[TRACE] 2025-04-16 01:47:49.073 - [CDC log cache task from Sybase] - Start task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase) 
[INFO ] 2025-04-16 01:47:49.166 - [CDC log cache task from Sybase] - Loading table structure completed 
[TRACE] 2025-04-16 01:47:49.166 - [CDC log cache task from Sybase] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-16 01:47:49.200 - [CDC log cache task from Sybase] - The engine receives CDC log cache task from Sybase task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-16 01:47:49.201 - [CDC log cache task from Sybase] - Task started 
[TRACE] 2025-04-16 01:47:49.217 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] start preload schema,table counts: 2 
[TRACE] 2025-04-16 01:47:49.217 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] preload schema finished, cost 0 ms 
[TRACE] 2025-04-16 01:47:49.229 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-04-16 01:47:49.229 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-04-16 01:47:49.236 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67fe99000a3b2cf7b97ebe99, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_s2p_text_ddd, version=v2, tableName=s2p_text_ddd, externalStorageTableName=ExternalStorage_SHARE_CDC_-94928111, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 01:47:49.236 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67dbec3d0a3b2cf7b959ffd2, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_bmsql_config, version=v2, tableName=bmsql_config, externalStorageTableName=ExternalStorage_SHARE_CDC_1902686287, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 01:47:49.243 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.s2p_text_ddd', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_-94928111', head seq: 0, tail seq: 0 
[INFO ] 2025-04-16 01:47:49.243 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.bmsql_config', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_1902686287', head seq: 0, tail seq: 13830 
[INFO ] 2025-04-16 01:47:49.444 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav17ha?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2025-04-16 01:47:49.690 - [CDC log cache task from Sybase][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-04-16 01:47:49.692 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-04-16 01:47:49.692 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-04-16 01:47:49.692 - [CDC log cache task from Sybase][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-16 01:47:49.692 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 01:47:49.998 - [CDC log cache task from Sybase][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-04-16 01:48:40.591 - [CDC log cache task from Sybase][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 01:48:40.726 - [CDC log cache task from Sybase][Sybase] - Batch read completed. 
[TRACE] 2025-04-16 01:48:40.726 - [CDC log cache task from Sybase][Sybase] - Starting stream read, table list: [dbo.bmsql_config, dbo.s2p_text_ddd], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 01:48:40.726 - [CDC log cache task from Sybase][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-04-16 01:48:40.928 - [CDC log cache task from Sybase][Sybase] - sybase cdc work with mode v2: manual rescan 
[WARN ] 2025-04-16 01:49:31.716 - [CDC log cache task from Sybase][Sybase] - io.tapdata.sybase.extend.SybaseContext.getConnection(SybaseContext.java:134) 
[WARN ] 2025-04-16 01:49:31.717 - [CDC log cache task from Sybase][Sybase] - io.tapdata.sybase.SybaseBeforeCdc.doBefore(SybaseBeforeCdc.java:53) 
[WARN ] 2025-04-16 01:49:31.717 - [CDC log cache task from Sybase][Sybase] - io.tapdata.sybase.cdc.SybaseLogMiner.startMinerV2(SybaseLogMiner.java:2081) 
[WARN ] 2025-04-16 01:49:31.717 - [CDC log cache task from Sybase][Sybase] - io.tapdata.sybase.SybaseConnectorV2.multiStreamReadV2(SybaseConnectorV2.java:576) 
[WARN ] 2025-04-16 01:49:31.718 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$27(HazelcastSourcePdkDataNode.java:866) 
[WARN ] 2025-04-16 01:49:31.718 - [CDC log cache task from Sybase][Sybase] - io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165) 
[WARN ] 2025-04-16 01:49:31.719 - [CDC log cache task from Sybase][Sybase] - io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154) 
[WARN ] 2025-04-16 01:49:31.719 - [CDC log cache task from Sybase][Sybase] - io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27) 
[WARN ] 2025-04-16 01:49:31.720 - [CDC log cache task from Sybase][Sybase] - io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154) 
[WARN ] 2025-04-16 01:49:31.720 - [CDC log cache task from Sybase][Sybase] - io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96) 
[WARN ] 2025-04-16 01:49:31.721 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:926) 
[WARN ] 2025-04-16 01:49:31.721 - [CDC log cache task from Sybase][Sybase] - io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67) 
[WARN ] 2025-04-16 01:49:31.722 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167) 
[WARN ] 2025-04-16 01:49:31.722 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:916) 
[WARN ] 2025-04-16 01:49:31.722 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:777) 
[WARN ] 2025-04-16 01:49:31.722 - [CDC log cache task from Sybase][Sybase] - io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:290) 
[WARN ] 2025-04-16 01:49:31.723 - [CDC log cache task from Sybase][Sybase] - java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) 
[WARN ] 2025-04-16 01:49:31.727 - [CDC log cache task from Sybase][Sybase] - java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264) 
[WARN ] 2025-04-16 01:49:31.729 - [CDC log cache task from Sybase][Sybase] - java.base/java.util.concurrent.FutureTask.run(FutureTask.java) 
[WARN ] 2025-04-16 01:49:31.730 - [CDC log cache task from Sybase][Sybase] - java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) 
[WARN ] 2025-04-16 01:49:31.730 - [CDC log cache task from Sybase][Sybase] - java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) 
[WARN ] 2025-04-16 01:49:31.730 - [CDC log cache task from Sybase][Sybase] - java.base/java.lang.Thread.run(Thread.java:840) 
[TRACE] 2025-04-16 01:49:31.798 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] running status set to false 
[INFO ] 2025-04-16 01:49:31.808 - [CDC log cache task from Sybase][Sybase] - Log Miner is shutting down... 
[TRACE] 2025-04-16 01:49:31.809 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] running status set to false 
[TRACE] 2025-04-16 01:49:31.815 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[TRACE] 2025-04-16 01:49:31.816 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node released: null 
[TRACE] 2025-04-16 01:49:31.816 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] schema data cleaned 
[TRACE] 2025-04-16 01:49:31.816 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] monitor closed 
[TRACE] 2025-04-16 01:49:32.017 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] close complete, cost 13 ms 
[TRACE] 2025-04-16 01:50:22.321 - [CDC log cache task from Sybase][Sybase] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744739269603 
[TRACE] 2025-04-16 01:50:22.323 - [CDC log cache task from Sybase][Sybase] - PDK connector node released: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744739269603 
[TRACE] 2025-04-16 01:50:22.323 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] schema data cleaned 
[TRACE] 2025-04-16 01:50:22.325 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] monitor closed 
[TRACE] 2025-04-16 01:50:22.325 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] close complete, cost 50530 ms 
[TRACE] 2025-04-16 01:50:26.960 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 01:50:26.962 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@72efcac3 
[TRACE] 2025-04-16 01:50:26.962 - [CDC log cache task from Sybase] - Stop task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase)  
[TRACE] 2025-04-16 01:50:27.086 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[TRACE] 2025-04-16 01:50:27.086 - [CDC log cache task from Sybase] - Snapshot order controller have been removed 
[INFO ] 2025-04-16 01:50:27.288 - [CDC log cache task from Sybase] - Task stopped. 
[TRACE] 2025-04-16 01:50:32.618 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 01:50:32.619 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@72efcac3 
[TRACE] 2025-04-16 01:50:32.619 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[INFO ] 2025-04-16 01:50:32.619 - [CDC log cache task from Sybase] - Task stopped. 
[TRACE] 2025-04-16 01:50:32.668 - [CDC log cache task from Sybase] - Remove memory task client succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[TRACE] 2025-04-16 01:50:32.669 - [CDC log cache task from Sybase] - Destroy memory task client cache succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[TRACE] 2025-04-16 09:13:20.295 - [CDC log cache task from Sybase] - Start task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase) 
[INFO ] 2025-04-16 09:13:20.524 - [CDC log cache task from Sybase] - Loading table structure completed 
[TRACE] 2025-04-16 09:13:20.729 - [CDC log cache task from Sybase] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-16 09:13:20.934 - [CDC log cache task from Sybase] - The engine receives CDC log cache task from Sybase task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-16 09:13:20.985 - [CDC log cache task from Sybase] - Task started 
[TRACE] 2025-04-16 09:13:21.121 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] start preload schema,table counts: 2 
[TRACE] 2025-04-16 09:13:21.122 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] preload schema finished, cost 1 ms 
[TRACE] 2025-04-16 09:13:21.122 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-04-16 09:13:21.123 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-04-16 09:13:21.186 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67dbec3d0a3b2cf7b959ffd2, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_bmsql_config, version=v2, tableName=bmsql_config, externalStorageTableName=ExternalStorage_SHARE_CDC_1902686287, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 09:13:21.187 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67fe99000a3b2cf7b97ebe99, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_s2p_text_ddd, version=v2, tableName=s2p_text_ddd, externalStorageTableName=ExternalStorage_SHARE_CDC_-94928111, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 09:13:21.593 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav17ha?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2025-04-16 09:13:21.715 - [CDC log cache task from Sybase][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-04-16 09:13:21.721 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-04-16 09:13:21.722 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-04-16 09:13:21.729 - [CDC log cache task from Sybase][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-16 09:13:21.798 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 09:13:21.799 - [CDC log cache task from Sybase][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-04-16 09:13:22.093 - [CDC log cache task from Sybase][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 09:13:22.094 - [CDC log cache task from Sybase][Sybase] - Batch read completed. 
[TRACE] 2025-04-16 09:13:22.113 - [CDC log cache task from Sybase][Sybase] - Starting stream read, table list: [dbo.bmsql_config, dbo.s2p_text_ddd], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 09:13:22.114 - [CDC log cache task from Sybase][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-04-16 09:13:22.282 - [CDC log cache task from Sybase][Sybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-04-16 09:13:22.282 - [CDC log cache task from Sybase][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-04-16 09:13:22.320 - [CDC log cache task from Sybase][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-04-16 09:13:22.321 - [CDC log cache task from Sybase][Sybase] - opened cdc for tables: {dbo=[s2p_text_ddd, bmsql_config]} 
[INFO ] 2025-04-16 09:13:22.394 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 09:13:22.397 - [CDC log cache task from Sybase][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-04-16 09:13:22.564 - [CDC log cache task from Sybase][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-04-16 09:13:33.279 - [CDC log cache task from Sybase][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-04-16 09:13:33.484 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368680, rowId: 0 
[INFO ] 2025-04-16 09:13:34.008 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 0, size: 32, cost: 674 ms 
[INFO ] 2025-04-16 09:13:34.209 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 102, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:13:36.145 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 0, size: 32, cost: 130 ms 
[INFO ] 2025-04-16 09:13:36.146 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 102, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:13:38.503 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 0, size: 32, cost: 347 ms 
[INFO ] 2025-04-16 09:13:38.704 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 102, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:13:40.664 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 0, size: 32, cost: 157 ms 
[INFO ] 2025-04-16 09:13:40.664 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 102, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:13:42.820 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 0, size: 32, cost: 149 ms 
[INFO ] 2025-04-16 09:13:42.820 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 102, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:13:44.845 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:13:44.847 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368680, rowId: 101 
[INFO ] 2025-04-16 09:13:44.953 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 101, size: 1, cost: 101 ms 
[INFO ] 2025-04-16 09:13:44.954 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:13:47.155 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 101, size: 1, cost: 189 ms 
[INFO ] 2025-04-16 09:13:47.156 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[TRACE] 2025-04-16 09:13:48.353 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@6a06f61: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":28800000,"tableId":"s2p_text_ddd","time":1744766024842,"type":205} 
[TRACE] 2025-04-16 09:13:48.356 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@65af07c9: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744735855533,"tableId":"s2p_text_ddd","time":1744766024850,"type":205} 
[TRACE] 2025-04-16 09:13:48.357 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@186c8128: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744736103500,"tableId":"s2p_text_ddd","time":1744766024850,"type":205} 
[TRACE] 2025-04-16 09:13:49.359 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@6a06f61: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":28800000,"tableId":"s2p_text_ddd","time":1744766024842,"type":205}). Wait for all previous events to be processed 
[TRACE] 2025-04-16 09:13:49.360 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@6a06f61: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":28800000,"tableId":"s2p_text_ddd","time":1744766024842,"type":205}) 
[TRACE] 2025-04-16 09:13:49.361 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@65af07c9: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744735855533,"tableId":"s2p_text_ddd","time":1744766024850,"type":205}). Wait for all previous events to be processed 
[INFO ] 2025-04-16 09:13:49.503 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 101, size: 1, cost: 336 ms 
[INFO ] 2025-04-16 09:13:49.503 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[TRACE] 2025-04-16 09:13:50.375 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@65af07c9: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744735855533,"tableId":"s2p_text_ddd","time":1744766024850,"type":205}) 
[TRACE] 2025-04-16 09:13:50.376 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@186c8128: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744736103500,"tableId":"s2p_text_ddd","time":1744766024850,"type":205}). Wait for all previous events to be processed 
[TRACE] 2025-04-16 09:13:51.389 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@186c8128: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744736103500,"tableId":"s2p_text_ddd","time":1744766024850,"type":205}) 
[INFO ] 2025-04-16 09:13:51.645 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 101, size: 1, cost: 135 ms 
[INFO ] 2025-04-16 09:13:51.647 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:13:53.984 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 101, size: 1, cost: 319 ms 
[INFO ] 2025-04-16 09:13:53.996 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:13:55.996 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:13:56.202 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 101 
[INFO ] 2025-04-16 09:14:06.287 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 101, size: 1, cost: 283 ms 
[INFO ] 2025-04-16 09:14:06.288 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:14:08.404 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 101, size: 1, cost: 110 ms 
[INFO ] 2025-04-16 09:14:08.404 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:14:10.491 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 101, size: 1, cost: 83 ms 
[INFO ] 2025-04-16 09:14:10.492 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:14:12.606 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 101, size: 1, cost: 111 ms 
[INFO ] 2025-04-16 09:14:12.606 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:14:25.308 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 101, size: 1, cost: 10697 ms 
[INFO ] 2025-04-16 09:14:25.308 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:14:32.429 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:14:32.430 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 101 
[INFO ] 2025-04-16 09:14:42.601 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 101, size: 1, cost: 166 ms 
[INFO ] 2025-04-16 09:14:42.809 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:14:44.753 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 101, size: 1, cost: 139 ms 
[INFO ] 2025-04-16 09:14:44.754 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:14:47.218 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 101, size: 1, cost: 457 ms 
[INFO ] 2025-04-16 09:14:47.219 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:14:49.541 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 101, size: 1, cost: 317 ms 
[INFO ] 2025-04-16 09:14:49.550 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:14:51.639 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 101, size: 1, cost: 93 ms 
[INFO ] 2025-04-16 09:14:51.846 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:14:53.649 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:14:53.649 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 101 
[INFO ] 2025-04-16 09:15:06.677 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 101, size: 4, cost: 3025 ms 
[INFO ] 2025-04-16 09:15:06.678 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 13, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:15:10.356 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 101, size: 4, cost: 1678 ms 
[INFO ] 2025-04-16 09:15:10.356 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 13, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:15:14.903 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 101, size: 4, cost: 2539 ms 
[INFO ] 2025-04-16 09:15:14.904 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 13, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:15:17.130 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 101, size: 4, cost: 220 ms 
[INFO ] 2025-04-16 09:15:17.131 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 13, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:15:19.268 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 101, size: 4, cost: 132 ms 
[INFO ] 2025-04-16 09:15:19.268 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 13, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:15:21.273 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:15:25.766 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368680, rowId: 113 
[INFO ] 2025-04-16 09:15:25.937 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 113, size: 1, cost: 4660 ms 
[INFO ] 2025-04-16 09:15:25.940 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:15:28.590 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 113, size: 1, cost: 633 ms 
[INFO ] 2025-04-16 09:15:28.590 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[TRACE] 2025-04-16 09:15:29.403 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@20a551e2: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744766096000,"tableId":"s2p_text_ddd","time":1744766124273,"type":205} 
[TRACE] 2025-04-16 09:15:29.749 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@20a551e2: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744766096000,"tableId":"s2p_text_ddd","time":1744766124273,"type":205}). Wait for all previous events to be processed 
[TRACE] 2025-04-16 09:15:29.752 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@20a551e2: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744766096000,"tableId":"s2p_text_ddd","time":1744766124273,"type":205}) 
[INFO ] 2025-04-16 09:15:30.715 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 113, size: 1, cost: 123 ms 
[INFO ] 2025-04-16 09:15:30.716 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:15:47.975 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 113, size: 1, cost: 11069 ms 
[INFO ] 2025-04-16 09:15:48.428 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:16:02.281 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 113, size: 3, cost: 11692 ms 
[INFO ] 2025-04-16 09:16:02.283 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:16:04.884 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:16:13.810 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368680, rowId: 116 
[INFO ] 2025-04-16 09:20:03.876 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 116, size: 1, cost: 238973 ms 
[INFO ] 2025-04-16 09:20:03.883 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:20:06.576 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 116, size: 1, cost: 168 ms 
[INFO ] 2025-04-16 09:20:06.577 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:20:08.752 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 116, size: 1, cost: 165 ms 
[INFO ] 2025-04-16 09:20:08.957 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:20:10.936 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 116, size: 1, cost: 172 ms 
[INFO ] 2025-04-16 09:20:11.139 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:20:13.278 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 116, size: 1, cost: 332 ms 
[INFO ] 2025-04-16 09:20:13.278 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:20:15.284 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:20:15.286 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 116 
[INFO ] 2025-04-16 09:20:25.499 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 116, size: 1, cost: 206 ms 
[INFO ] 2025-04-16 09:20:25.707 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:20:27.645 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 116, size: 1, cost: 129 ms 
[INFO ] 2025-04-16 09:20:27.646 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:20:29.814 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 116, size: 1, cost: 162 ms 
[INFO ] 2025-04-16 09:20:29.814 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:20:31.935 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 116, size: 1, cost: 115 ms 
[INFO ] 2025-04-16 09:20:31.936 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:20:34.202 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 116, size: 1, cost: 260 ms 
[INFO ] 2025-04-16 09:20:34.202 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:20:36.210 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:20:36.211 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 116 
[INFO ] 2025-04-16 09:20:46.443 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 116, size: 1, cost: 225 ms 
[INFO ] 2025-04-16 09:20:46.444 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:20:48.679 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 116, size: 1, cost: 236 ms 
[INFO ] 2025-04-16 09:20:48.680 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:20:50.861 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 116, size: 1, cost: 177 ms 
[INFO ] 2025-04-16 09:20:50.861 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:20:53.098 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 116, size: 1, cost: 235 ms 
[INFO ] 2025-04-16 09:20:53.099 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:20:55.358 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 116, size: 1, cost: 259 ms 
[INFO ] 2025-04-16 09:20:55.358 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:20:57.362 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:20:57.363 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 116 
[INFO ] 2025-04-16 09:21:07.555 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 116, size: 1, cost: 187 ms 
[INFO ] 2025-04-16 09:21:07.555 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:21:09.866 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 116, size: 1, cost: 298 ms 
[INFO ] 2025-04-16 09:21:10.072 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:21:12.144 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 116, size: 1, cost: 270 ms 
[INFO ] 2025-04-16 09:21:12.144 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:21:14.296 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 116, size: 1, cost: 142 ms 
[INFO ] 2025-04-16 09:21:14.296 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:21:16.497 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 116, size: 1, cost: 198 ms 
[INFO ] 2025-04-16 09:21:16.497 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:21:18.501 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:21:18.502 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 116 
[INFO ] 2025-04-16 09:21:28.757 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 116, size: 1, cost: 249 ms 
[INFO ] 2025-04-16 09:21:28.757 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:21:30.969 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 116, size: 1, cost: 206 ms 
[INFO ] 2025-04-16 09:21:30.969 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:21:33.131 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 116, size: 1, cost: 158 ms 
[INFO ] 2025-04-16 09:21:33.133 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:21:35.287 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 116, size: 1, cost: 150 ms 
[INFO ] 2025-04-16 09:21:35.287 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:21:37.423 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 116, size: 1, cost: 134 ms 
[INFO ] 2025-04-16 09:21:37.424 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:21:39.426 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:21:39.426 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 116 
[INFO ] 2025-04-16 09:21:49.737 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 116, size: 1, cost: 305 ms 
[INFO ] 2025-04-16 09:21:49.738 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:21:51.932 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 116, size: 1, cost: 188 ms 
[INFO ] 2025-04-16 09:21:51.933 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:21:54.127 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 116, size: 1, cost: 189 ms 
[INFO ] 2025-04-16 09:21:54.127 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:21:56.259 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 116, size: 1, cost: 128 ms 
[INFO ] 2025-04-16 09:21:56.259 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:21:58.421 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 116, size: 1, cost: 159 ms 
[INFO ] 2025-04-16 09:21:58.421 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:22:00.428 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:22:00.630 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 116 
[INFO ] 2025-04-16 09:22:10.556 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 116, size: 1, cost: 121 ms 
[INFO ] 2025-04-16 09:22:10.556 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:22:12.858 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 116, size: 1, cost: 296 ms 
[INFO ] 2025-04-16 09:22:12.858 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:22:14.966 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 116, size: 1, cost: 103 ms 
[INFO ] 2025-04-16 09:22:14.967 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:22:17.201 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 116, size: 1, cost: 220 ms 
[INFO ] 2025-04-16 09:22:17.202 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:22:19.444 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 116, size: 1, cost: 246 ms 
[INFO ] 2025-04-16 09:22:19.444 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:22:21.451 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:22:21.451 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 116 
[INFO ] 2025-04-16 09:22:31.676 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 116, size: 1, cost: 217 ms 
[INFO ] 2025-04-16 09:22:31.677 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:22:33.859 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 116, size: 1, cost: 177 ms 
[INFO ] 2025-04-16 09:22:33.860 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:22:36.050 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 116, size: 1, cost: 185 ms 
[INFO ] 2025-04-16 09:22:36.051 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:22:38.245 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 116, size: 1, cost: 190 ms 
[INFO ] 2025-04-16 09:22:38.245 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:22:40.597 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 116, size: 1, cost: 346 ms 
[INFO ] 2025-04-16 09:22:40.598 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:22:42.603 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:22:42.604 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 116 
[INFO ] 2025-04-16 09:22:52.728 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 116, size: 1, cost: 118 ms 
[INFO ] 2025-04-16 09:22:52.728 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:22:54.889 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 116, size: 1, cost: 160 ms 
[INFO ] 2025-04-16 09:22:54.889 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:22:57.193 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 116, size: 1, cost: 297 ms 
[INFO ] 2025-04-16 09:22:57.193 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:22:59.457 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 116, size: 1, cost: 262 ms 
[INFO ] 2025-04-16 09:22:59.457 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:23:01.635 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 116, size: 1, cost: 172 ms 
[INFO ] 2025-04-16 09:23:01.635 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:23:03.639 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:23:03.639 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 116 
[TRACE] 2025-04-16 09:23:13.316 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] running status set to false 
[INFO ] 2025-04-16 09:23:13.522 - [CDC log cache task from Sybase][Sybase] - Log Miner is shutting down... 
[INFO ] 2025-04-16 09:23:13.621 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 116, size: 1, cost: 297 ms 
[INFO ] 2025-04-16 09:23:13.624 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 1 to 5 
[TRACE] 2025-04-16 09:23:14.767 - [CDC log cache task from Sybase][Sybase] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744766001645 
[TRACE] 2025-04-16 09:23:14.767 - [CDC log cache task from Sybase][Sybase] - PDK connector node released: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744766001645 
[TRACE] 2025-04-16 09:23:14.768 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] schema data cleaned 
[TRACE] 2025-04-16 09:23:14.769 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] monitor closed 
[TRACE] 2025-04-16 09:23:14.772 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] close complete, cost 1461 ms 
[TRACE] 2025-04-16 09:23:14.772 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] running status set to false 
[TRACE] 2025-04-16 09:23:14.779 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[TRACE] 2025-04-16 09:23:14.780 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node released: null 
[TRACE] 2025-04-16 09:23:14.781 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] schema data cleaned 
[TRACE] 2025-04-16 09:23:14.781 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] monitor closed 
[TRACE] 2025-04-16 09:23:14.987 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] close complete, cost 9 ms 
[INFO ] 2025-04-16 09:23:15.877 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 116, size: 1, cost: 248 ms 
[INFO ] 2025-04-16 09:23:16.082 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 2 to 5 
[TRACE] 2025-04-16 09:23:16.589 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 09:23:16.592 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@62d60bcd 
[TRACE] 2025-04-16 09:23:16.593 - [CDC log cache task from Sybase] - Stop task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase)  
[TRACE] 2025-04-16 09:23:16.719 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[TRACE] 2025-04-16 09:23:16.720 - [CDC log cache task from Sybase] - Snapshot order controller have been removed 
[INFO ] 2025-04-16 09:23:16.928 - [CDC log cache task from Sybase] - Task stopped. 
[INFO ] 2025-04-16 09:23:18.402 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 116, size: 1, cost: 511 ms 
[INFO ] 2025-04-16 09:23:18.405 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:23:20.596 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 116, size: 1, cost: 180 ms 
[INFO ] 2025-04-16 09:23:20.803 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 4 to 5 
[TRACE] 2025-04-16 09:23:21.772 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 09:23:21.772 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@62d60bcd 
[TRACE] 2025-04-16 09:23:21.781 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[INFO ] 2025-04-16 09:23:21.781 - [CDC log cache task from Sybase] - Task stopped. 
[TRACE] 2025-04-16 09:23:21.828 - [CDC log cache task from Sybase] - Remove memory task client succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[TRACE] 2025-04-16 09:23:21.829 - [CDC log cache task from Sybase] - Destroy memory task client cache succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[INFO ] 2025-04-16 09:23:22.752 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 116, size: 1, cost: 122 ms 
[INFO ] 2025-04-16 09:23:22.959 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:23:24.767 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[TRACE] 2025-04-16 09:25:17.690 - [CDC log cache task from Sybase] - Start task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase) 
[INFO ] 2025-04-16 09:25:18.032 - [CDC log cache task from Sybase] - Loading table structure completed 
[TRACE] 2025-04-16 09:25:18.033 - [CDC log cache task from Sybase] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-16 09:25:18.239 - [CDC log cache task from Sybase] - The engine receives CDC log cache task from Sybase task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-16 09:25:18.364 - [CDC log cache task from Sybase] - Task started 
[TRACE] 2025-04-16 09:25:18.365 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] start preload schema,table counts: 2 
[TRACE] 2025-04-16 09:25:18.368 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] preload schema finished, cost 0 ms 
[TRACE] 2025-04-16 09:25:18.386 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-04-16 09:25:18.387 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-04-16 09:25:18.444 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67dbec3d0a3b2cf7b959ffd2, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_bmsql_config, version=v2, tableName=bmsql_config, externalStorageTableName=ExternalStorage_SHARE_CDC_1902686287, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 09:25:18.651 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67fe99000a3b2cf7b97ebe99, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_s2p_text_ddd, version=v2, tableName=s2p_text_ddd, externalStorageTableName=ExternalStorage_SHARE_CDC_-94928111, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 09:25:18.742 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav17ha?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2025-04-16 09:25:19.075 - [CDC log cache task from Sybase][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-04-16 09:25:19.082 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-04-16 09:25:19.083 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-04-16 09:25:19.087 - [CDC log cache task from Sybase][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-16 09:25:19.090 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 09:25:19.293 - [CDC log cache task from Sybase][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-04-16 09:25:19.675 - [CDC log cache task from Sybase][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 09:25:19.735 - [CDC log cache task from Sybase][Sybase] - Batch read completed. 
[TRACE] 2025-04-16 09:25:19.756 - [CDC log cache task from Sybase][Sybase] - Starting stream read, table list: [dbo.bmsql_config, dbo.s2p_text_ddd], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 09:25:19.756 - [CDC log cache task from Sybase][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-04-16 09:25:19.959 - [CDC log cache task from Sybase][Sybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-04-16 09:25:20.142 - [CDC log cache task from Sybase][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-04-16 09:25:20.204 - [CDC log cache task from Sybase][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-04-16 09:25:20.205 - [CDC log cache task from Sybase][Sybase] - opened cdc for tables: {dbo=[s2p_text_ddd, bmsql_config]} 
[INFO ] 2025-04-16 09:25:20.350 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 09:25:20.354 - [CDC log cache task from Sybase][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-04-16 09:25:20.644 - [CDC log cache task from Sybase][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-04-16 09:25:20.807 - [CDC log cache task from Sybase][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-04-16 09:25:20.808 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368680, rowId: 0 
[INFO ] 2025-04-16 09:25:22.453 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 0, size: 37, cost: 1637 ms 
[INFO ] 2025-04-16 09:25:22.454 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 118, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:25:24.890 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 0, size: 37, cost: 421 ms 
[INFO ] 2025-04-16 09:25:24.891 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 118, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:25:27.181 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 0, size: 37, cost: 284 ms 
[INFO ] 2025-04-16 09:25:27.184 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 118, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:25:29.488 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 0, size: 37, cost: 301 ms 
[INFO ] 2025-04-16 09:25:29.488 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 118, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:25:31.708 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 0, size: 37, cost: 218 ms 
[INFO ] 2025-04-16 09:25:31.709 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 118, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:25:33.717 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:25:35.809 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368680, rowId: 116 
[INFO ] 2025-04-16 09:25:38.170 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 116, size: 1, cost: 4441 ms 
[INFO ] 2025-04-16 09:25:38.170 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:25:40.435 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 116, size: 1, cost: 263 ms 
[INFO ] 2025-04-16 09:25:40.438 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 2 to 5 
[TRACE] 2025-04-16 09:25:40.874 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@4a3e9b9: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":28800000,"tableId":"s2p_text_ddd","time":1744766735815,"type":205} 
[TRACE] 2025-04-16 09:25:40.875 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@72ed8098: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744735855533,"tableId":"s2p_text_ddd","time":1744766737064,"type":205} 
[TRACE] 2025-04-16 09:25:40.876 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@37ab0e4d: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744736103500,"tableId":"s2p_text_ddd","time":1744766737570,"type":205} 
[TRACE] 2025-04-16 09:25:40.877 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@535482a1: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744766096000,"tableId":"s2p_text_ddd","time":1744766738027,"type":205} 
[TRACE] 2025-04-16 09:25:41.079 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@4a3e9b9: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":28800000,"tableId":"s2p_text_ddd","time":1744766735815,"type":205}). Wait for all previous events to be processed 
[TRACE] 2025-04-16 09:25:42.073 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@4a3e9b9: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":28800000,"tableId":"s2p_text_ddd","time":1744766735815,"type":205}) 
[TRACE] 2025-04-16 09:25:42.074 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@72ed8098: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744735855533,"tableId":"s2p_text_ddd","time":1744766737064,"type":205}). Wait for all previous events to be processed 
[INFO ] 2025-04-16 09:25:42.633 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 116, size: 1, cost: 190 ms 
[INFO ] 2025-04-16 09:25:42.634 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:25:52.673 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 116, size: 1, cost: 7031 ms 
[INFO ] 2025-04-16 09:25:53.271 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 4 to 5 
[TRACE] 2025-04-16 09:26:01.867 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@72ed8098: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744735855533,"tableId":"s2p_text_ddd","time":1744766737064,"type":205}) 
[TRACE] 2025-04-16 09:26:01.873 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@37ab0e4d: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744736103500,"tableId":"s2p_text_ddd","time":1744766737570,"type":205}). Wait for all previous events to be processed 
[INFO ] 2025-04-16 09:26:01.941 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 116, size: 1, cost: 7305 ms 
[INFO ] 2025-04-16 09:26:01.942 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 5 to 5 
[TRACE] 2025-04-16 09:26:02.884 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@37ab0e4d: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744736103500,"tableId":"s2p_text_ddd","time":1744766737570,"type":205}) 
[TRACE] 2025-04-16 09:26:03.091 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@535482a1: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744766096000,"tableId":"s2p_text_ddd","time":1744766738027,"type":205}). Wait for all previous events to be processed 
[TRACE] 2025-04-16 09:26:03.900 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@535482a1: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744766096000,"tableId":"s2p_text_ddd","time":1744766738027,"type":205}) 
[INFO ] 2025-04-16 09:26:03.944 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:26:03.947 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 116 
[INFO ] 2025-04-16 09:26:14.190 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 116, size: 1, cost: 237 ms 
[INFO ] 2025-04-16 09:26:14.191 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:26:16.399 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 116, size: 1, cost: 199 ms 
[INFO ] 2025-04-16 09:26:16.611 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:26:18.673 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 116, size: 1, cost: 260 ms 
[INFO ] 2025-04-16 09:26:18.673 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:26:20.905 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 116, size: 1, cost: 230 ms 
[INFO ] 2025-04-16 09:26:20.911 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:26:23.081 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 116, size: 1, cost: 160 ms 
[INFO ] 2025-04-16 09:26:23.082 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:26:25.088 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:26:25.088 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 116 
[INFO ] 2025-04-16 09:26:35.314 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 116, size: 1, cost: 216 ms 
[INFO ] 2025-04-16 09:26:35.315 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:26:37.587 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 116, size: 1, cost: 262 ms 
[INFO ] 2025-04-16 09:26:37.798 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:26:39.826 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 116, size: 1, cost: 221 ms 
[INFO ] 2025-04-16 09:26:39.840 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:26:42.104 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 116, size: 1, cost: 256 ms 
[INFO ] 2025-04-16 09:26:42.312 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:26:44.386 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 116, size: 1, cost: 271 ms 
[INFO ] 2025-04-16 09:26:44.387 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:26:46.391 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:26:46.391 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 116 
[INFO ] 2025-04-16 09:26:56.609 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 116, size: 1, cost: 230 ms 
[INFO ] 2025-04-16 09:26:56.609 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:26:58.776 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 116, size: 1, cost: 163 ms 
[INFO ] 2025-04-16 09:26:58.777 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:27:00.937 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 116, size: 1, cost: 157 ms 
[INFO ] 2025-04-16 09:27:00.937 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:27:03.227 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 116, size: 1, cost: 289 ms 
[INFO ] 2025-04-16 09:27:03.228 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:27:05.540 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 116, size: 1, cost: 314 ms 
[INFO ] 2025-04-16 09:27:05.540 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:27:07.545 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:27:07.545 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 116 
[INFO ] 2025-04-16 09:27:17.702 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 116, size: 1, cost: 158 ms 
[INFO ] 2025-04-16 09:27:17.703 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:27:19.853 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 116, size: 1, cost: 149 ms 
[INFO ] 2025-04-16 09:27:19.853 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:27:22.164 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 116, size: 1, cost: 299 ms 
[INFO ] 2025-04-16 09:27:22.164 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:27:24.350 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 116, size: 1, cost: 178 ms 
[INFO ] 2025-04-16 09:27:24.353 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:27:26.509 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 116, size: 1, cost: 154 ms 
[INFO ] 2025-04-16 09:27:26.509 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:27:28.511 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:27:28.715 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 116 
[INFO ] 2025-04-16 09:27:38.849 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 116, size: 3, cost: 330 ms 
[INFO ] 2025-04-16 09:27:38.850 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 15, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:27:41.370 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 116, size: 3, cost: 513 ms 
[INFO ] 2025-04-16 09:27:41.370 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 15, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:27:43.895 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 116, size: 3, cost: 520 ms 
[INFO ] 2025-04-16 09:27:43.895 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 15, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:27:46.196 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 116, size: 3, cost: 297 ms 
[INFO ] 2025-04-16 09:27:46.197 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 15, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:27:48.472 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 116, size: 3, cost: 271 ms 
[INFO ] 2025-04-16 09:27:48.473 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 15, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:27:50.481 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:27:50.687 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368680, rowId: 130 
[INFO ] 2025-04-16 09:27:50.695 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 130, size: 1, cost: 211 ms 
[INFO ] 2025-04-16 09:27:50.696 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:27:52.992 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 130, size: 3, cost: 291 ms 
[INFO ] 2025-04-16 09:27:52.994 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:27:55.274 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 130, size: 3, cost: 279 ms 
[INFO ] 2025-04-16 09:27:55.276 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:27:57.488 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 130, size: 3, cost: 212 ms 
[INFO ] 2025-04-16 09:27:57.488 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:27:59.630 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 130, size: 3, cost: 134 ms 
[INFO ] 2025-04-16 09:27:59.630 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:28:01.640 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:28:01.846 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368680, rowId: 133 
[INFO ] 2025-04-16 09:28:01.949 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 133, size: 1, cost: 304 ms 
[INFO ] 2025-04-16 09:28:01.950 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:28:04.213 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 133, size: 1, cost: 242 ms 
[INFO ] 2025-04-16 09:28:04.215 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:28:06.447 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 133, size: 1, cost: 230 ms 
[INFO ] 2025-04-16 09:28:06.447 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:28:08.721 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 133, size: 1, cost: 267 ms 
[INFO ] 2025-04-16 09:28:08.722 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:28:11.154 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 133, size: 1, cost: 424 ms 
[INFO ] 2025-04-16 09:28:11.155 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:28:13.162 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:28:13.167 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 133 
[INFO ] 2025-04-16 09:28:24.143 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 133, size: 1, cost: 976 ms 
[INFO ] 2025-04-16 09:28:24.145 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:28:26.378 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 133, size: 1, cost: 224 ms 
[INFO ] 2025-04-16 09:28:26.379 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:28:28.859 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 133, size: 1, cost: 474 ms 
[INFO ] 2025-04-16 09:28:28.859 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:28:31.170 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 133, size: 1, cost: 303 ms 
[INFO ] 2025-04-16 09:28:31.170 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:28:33.600 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 133, size: 1, cost: 425 ms 
[INFO ] 2025-04-16 09:28:33.801 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:28:35.607 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:28:35.811 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368680, rowId: 133 
[INFO ] 2025-04-16 09:28:45.839 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368680, 133, size: 3, cost: 226 ms 
[INFO ] 2025-04-16 09:28:46.041 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 14, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:28:48.173 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368680, 133, size: 3, cost: 325 ms 
[INFO ] 2025-04-16 09:28:48.173 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 14, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:28:50.363 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368680, 133, size: 3, cost: 185 ms 
[INFO ] 2025-04-16 09:28:50.364 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 14, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:28:52.620 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368680, 133, size: 5, cost: 250 ms 
[INFO ] 2025-04-16 09:28:52.826 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:28:54.894 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368680, 133, size: 5, cost: 267 ms 
[INFO ] 2025-04-16 09:28:54.894 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:28:56.899 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:28:56.899 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 8 
[INFO ] 2025-04-16 09:28:57.133 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 8, size: 1, cost: 233 ms 
[INFO ] 2025-04-16 09:28:57.133 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:28:59.608 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 8, size: 1, cost: 469 ms 
[INFO ] 2025-04-16 09:28:59.609 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:29:01.839 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 8, size: 1, cost: 225 ms 
[INFO ] 2025-04-16 09:29:01.840 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:29:04.115 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 8, size: 1, cost: 268 ms 
[INFO ] 2025-04-16 09:29:04.116 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:29:06.429 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 8, size: 1, cost: 311 ms 
[INFO ] 2025-04-16 09:29:06.430 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:29:08.434 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:29:08.647 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 8 
[INFO ] 2025-04-16 09:29:18.625 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 8, size: 1, cost: 185 ms 
[INFO ] 2025-04-16 09:29:18.627 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:29:20.939 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 8, size: 1, cost: 308 ms 
[INFO ] 2025-04-16 09:29:20.940 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:29:23.206 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 8, size: 1, cost: 262 ms 
[INFO ] 2025-04-16 09:29:23.207 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:29:25.412 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 8, size: 1, cost: 205 ms 
[INFO ] 2025-04-16 09:29:25.412 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:29:27.562 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 8, size: 1, cost: 148 ms 
[INFO ] 2025-04-16 09:29:27.563 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:29:29.567 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:29:29.567 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 8 
[INFO ] 2025-04-16 09:29:39.713 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 8, size: 1, cost: 141 ms 
[INFO ] 2025-04-16 09:29:39.713 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:29:41.934 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 8, size: 1, cost: 214 ms 
[INFO ] 2025-04-16 09:29:41.934 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:29:44.252 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 8, size: 1, cost: 311 ms 
[INFO ] 2025-04-16 09:29:44.252 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:29:46.410 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 8, size: 1, cost: 148 ms 
[INFO ] 2025-04-16 09:29:46.617 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:29:48.614 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 8, size: 1, cost: 195 ms 
[INFO ] 2025-04-16 09:29:48.614 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:29:50.619 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:29:50.619 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 8 
[INFO ] 2025-04-16 09:30:00.932 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 8, size: 1, cost: 308 ms 
[INFO ] 2025-04-16 09:30:00.932 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:30:03.225 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 8, size: 1, cost: 287 ms 
[INFO ] 2025-04-16 09:30:03.226 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:30:05.397 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 8, size: 1, cost: 169 ms 
[INFO ] 2025-04-16 09:30:05.603 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:30:07.568 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 8, size: 1, cost: 166 ms 
[INFO ] 2025-04-16 09:30:07.568 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:30:09.846 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 8, size: 1, cost: 274 ms 
[INFO ] 2025-04-16 09:30:09.847 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:30:11.851 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:30:11.851 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 8 
[INFO ] 2025-04-16 09:30:22.138 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 8, size: 1, cost: 273 ms 
[INFO ] 2025-04-16 09:30:22.138 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:30:24.244 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 8, size: 1, cost: 101 ms 
[INFO ] 2025-04-16 09:30:24.244 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:30:26.428 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 8, size: 1, cost: 181 ms 
[INFO ] 2025-04-16 09:30:26.428 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:30:28.571 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 8, size: 1, cost: 141 ms 
[INFO ] 2025-04-16 09:30:28.571 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:30:30.668 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 8, size: 1, cost: 91 ms 
[INFO ] 2025-04-16 09:30:30.668 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:30:32.674 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:30:32.675 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 8 
[INFO ] 2025-04-16 09:30:49.310 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 8, size: 1, cost: 247 ms 
[INFO ] 2025-04-16 09:30:49.515 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:30:51.454 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 8, size: 1, cost: 134 ms 
[INFO ] 2025-04-16 09:30:51.655 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:30:53.612 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 8, size: 1, cost: 143 ms 
[INFO ] 2025-04-16 09:30:53.612 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:30:55.809 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 8, size: 1, cost: 188 ms 
[INFO ] 2025-04-16 09:30:55.809 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:30:57.967 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 8, size: 1, cost: 154 ms 
[INFO ] 2025-04-16 09:30:58.173 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:31:06.482 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:31:06.485 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 8 
[INFO ] 2025-04-16 09:31:16.715 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 8, size: 1, cost: 223 ms 
[INFO ] 2025-04-16 09:31:16.715 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:31:18.948 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 8, size: 1, cost: 222 ms 
[INFO ] 2025-04-16 09:31:18.949 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:31:21.237 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 8, size: 1, cost: 279 ms 
[INFO ] 2025-04-16 09:31:21.237 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:31:23.501 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 8, size: 1, cost: 248 ms 
[INFO ] 2025-04-16 09:31:23.501 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:31:25.713 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 8, size: 1, cost: 204 ms 
[INFO ] 2025-04-16 09:31:25.918 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:31:27.730 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:31:27.934 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 8 
[INFO ] 2025-04-16 09:31:38.120 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 8, size: 1, cost: 364 ms 
[INFO ] 2025-04-16 09:31:38.120 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:31:40.562 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 8, size: 1, cost: 428 ms 
[INFO ] 2025-04-16 09:31:40.771 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:31:42.873 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 8, size: 1, cost: 291 ms 
[INFO ] 2025-04-16 09:31:42.873 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:31:45.340 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 8, size: 1, cost: 448 ms 
[INFO ] 2025-04-16 09:31:45.340 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:31:47.673 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 8, size: 1, cost: 322 ms 
[INFO ] 2025-04-16 09:31:47.876 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:31:49.685 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:31:49.886 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 8 
[TRACE] 2025-04-16 09:31:54.824 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] running status set to false 
[INFO ] 2025-04-16 09:31:54.825 - [CDC log cache task from Sybase][Sybase] - Log Miner is shutting down... 
[TRACE] 2025-04-16 09:31:55.004 - [CDC log cache task from Sybase][Sybase] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744766718883 
[TRACE] 2025-04-16 09:31:55.004 - [CDC log cache task from Sybase][Sybase] - PDK connector node released: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744766718883 
[TRACE] 2025-04-16 09:31:55.004 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] schema data cleaned 
[TRACE] 2025-04-16 09:31:55.005 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] monitor closed 
[TRACE] 2025-04-16 09:31:55.008 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] close complete, cost 186 ms 
[TRACE] 2025-04-16 09:31:55.008 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] running status set to false 
[TRACE] 2025-04-16 09:31:55.014 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[TRACE] 2025-04-16 09:31:55.016 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node released: null 
[TRACE] 2025-04-16 09:31:55.016 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] schema data cleaned 
[TRACE] 2025-04-16 09:31:55.016 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] monitor closed 
[TRACE] 2025-04-16 09:31:55.017 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] close complete, cost 7 ms 
[INFO ] 2025-04-16 09:31:55.063 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 8, size: 1, cost: 235 ms 
[INFO ] 2025-04-16 09:31:55.063 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[TRACE] 2025-04-16 09:31:56.586 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 09:31:56.589 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@38b0d4f1 
[TRACE] 2025-04-16 09:31:56.589 - [CDC log cache task from Sybase] - Stop task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase)  
[TRACE] 2025-04-16 09:31:56.710 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[TRACE] 2025-04-16 09:31:56.711 - [CDC log cache task from Sybase] - Snapshot order controller have been removed 
[INFO ] 2025-04-16 09:31:56.917 - [CDC log cache task from Sybase] - Task stopped. 
[INFO ] 2025-04-16 09:31:57.365 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 8, size: 1, cost: 286 ms 
[INFO ] 2025-04-16 09:31:57.571 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:31:59.627 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 8, size: 1, cost: 240 ms 
[INFO ] 2025-04-16 09:31:59.628 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:32:01.828 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 8, size: 1, cost: 194 ms 
[INFO ] 2025-04-16 09:32:02.034 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[TRACE] 2025-04-16 09:32:03.127 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 09:32:03.129 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@38b0d4f1 
[TRACE] 2025-04-16 09:32:03.129 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[INFO ] 2025-04-16 09:32:03.130 - [CDC log cache task from Sybase] - Task stopped. 
[TRACE] 2025-04-16 09:32:03.177 - [CDC log cache task from Sybase] - Remove memory task client succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[TRACE] 2025-04-16 09:32:03.181 - [CDC log cache task from Sybase] - Destroy memory task client cache succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[INFO ] 2025-04-16 09:32:04.040 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 8, size: 1, cost: 197 ms 
[INFO ] 2025-04-16 09:32:04.245 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:32:06.056 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[TRACE] 2025-04-16 09:32:09.439 - [CDC log cache task from Sybase] - Start task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase) 
[INFO ] 2025-04-16 09:32:09.527 - [CDC log cache task from Sybase] - Loading table structure completed 
[TRACE] 2025-04-16 09:32:09.527 - [CDC log cache task from Sybase] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-16 09:32:09.561 - [CDC log cache task from Sybase] - The engine receives CDC log cache task from Sybase task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-16 09:32:09.561 - [CDC log cache task from Sybase] - Task started 
[TRACE] 2025-04-16 09:32:09.581 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] start preload schema,table counts: 2 
[TRACE] 2025-04-16 09:32:09.582 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] preload schema finished, cost 0 ms 
[TRACE] 2025-04-16 09:32:09.592 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-04-16 09:32:09.592 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-04-16 09:32:09.601 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67dbec3d0a3b2cf7b959ffd2, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_bmsql_config, version=v2, tableName=bmsql_config, externalStorageTableName=ExternalStorage_SHARE_CDC_1902686287, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 09:32:09.601 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.bmsql_config', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_1902686287', head seq: 0, tail seq: 13830 
[INFO ] 2025-04-16 09:32:09.635 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67fe99000a3b2cf7b97ebe99, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_s2p_text_ddd, version=v2, tableName=s2p_text_ddd, externalStorageTableName=ExternalStorage_SHARE_CDC_-94928111, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 09:32:09.635 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.s2p_text_ddd', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_-94928111', head seq: 0, tail seq: 4 
[INFO ] 2025-04-16 09:32:09.837 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav17ha?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2025-04-16 09:32:10.107 - [CDC log cache task from Sybase][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-04-16 09:32:10.110 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-04-16 09:32:10.110 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-04-16 09:32:10.110 - [CDC log cache task from Sybase][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-16 09:32:10.110 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 09:32:10.311 - [CDC log cache task from Sybase][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-04-16 09:32:10.714 - [CDC log cache task from Sybase][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 09:32:10.729 - [CDC log cache task from Sybase][Sybase] - Batch read completed. 
[TRACE] 2025-04-16 09:32:10.730 - [CDC log cache task from Sybase][Sybase] - Starting stream read, table list: [dbo.bmsql_config, dbo.s2p_text_ddd], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 09:32:10.730 - [CDC log cache task from Sybase][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-04-16 09:32:10.742 - [CDC log cache task from Sybase][Sybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-04-16 09:32:11.289 - [CDC log cache task from Sybase][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-04-16 09:32:11.290 - [CDC log cache task from Sybase][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-04-16 09:32:11.336 - [CDC log cache task from Sybase][Sybase] - opened cdc for tables: {dbo=[s2p_text_ddd, bmsql_config]} 
[INFO ] 2025-04-16 09:32:11.340 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 09:32:11.744 - [CDC log cache task from Sybase][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-04-16 09:32:11.836 - [CDC log cache task from Sybase][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-04-16 09:32:12.143 - [CDC log cache task from Sybase][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-04-16 09:32:12.143 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 0 
[INFO ] 2025-04-16 09:32:12.701 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 0, size: 5, cost: 557 ms 
[INFO ] 2025-04-16 09:32:12.701 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 20, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:32:14.920 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 0, size: 5, cost: 215 ms 
[INFO ] 2025-04-16 09:32:14.920 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 20, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:32:17.330 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 0, size: 5, cost: 401 ms 
[INFO ] 2025-04-16 09:32:17.335 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 20, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:32:19.547 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 0, size: 5, cost: 211 ms 
[INFO ] 2025-04-16 09:32:19.548 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 20, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:32:21.783 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 0, size: 5, cost: 228 ms 
[INFO ] 2025-04-16 09:32:21.783 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 20, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:32:23.799 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:32:23.801 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 19 
[INFO ] 2025-04-16 09:32:24.089 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 19, size: 1, cost: 289 ms 
[INFO ] 2025-04-16 09:32:24.090 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:32:26.508 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 19, size: 1, cost: 416 ms 
[INFO ] 2025-04-16 09:32:26.508 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:32:28.861 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 19, size: 1, cost: 341 ms 
[INFO ] 2025-04-16 09:32:28.862 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:32:31.053 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 19, size: 1, cost: 190 ms 
[INFO ] 2025-04-16 09:32:31.054 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:32:33.216 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 19, size: 1, cost: 156 ms 
[INFO ] 2025-04-16 09:32:33.220 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:32:35.222 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:32:35.222 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 19 
[INFO ] 2025-04-16 09:32:45.574 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 19, size: 1, cost: 346 ms 
[INFO ] 2025-04-16 09:32:45.574 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:32:47.894 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 19, size: 1, cost: 313 ms 
[INFO ] 2025-04-16 09:32:47.894 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:32:50.196 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 19, size: 1, cost: 279 ms 
[INFO ] 2025-04-16 09:32:50.197 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:32:52.470 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 19, size: 1, cost: 273 ms 
[INFO ] 2025-04-16 09:32:52.472 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:32:54.692 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 19, size: 1, cost: 215 ms 
[INFO ] 2025-04-16 09:32:54.693 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:32:56.698 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:32:56.698 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 19 
[INFO ] 2025-04-16 09:33:06.834 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 19, size: 1, cost: 132 ms 
[INFO ] 2025-04-16 09:33:06.835 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:33:09.025 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 19, size: 1, cost: 185 ms 
[INFO ] 2025-04-16 09:33:09.025 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:33:11.323 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 19, size: 3, cost: 292 ms 
[INFO ] 2025-04-16 09:33:11.324 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 14, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:33:13.537 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 19, size: 3, cost: 203 ms 
[INFO ] 2025-04-16 09:33:13.538 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 14, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:33:15.688 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 19, size: 3, cost: 145 ms 
[INFO ] 2025-04-16 09:33:15.688 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 14, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:33:17.695 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:33:17.695 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 32 
[INFO ] 2025-04-16 09:33:17.943 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 32, size: 1, cost: 247 ms 
[INFO ] 2025-04-16 09:33:17.944 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:33:20.215 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 32, size: 1, cost: 270 ms 
[INFO ] 2025-04-16 09:33:20.215 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:33:22.414 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 32, size: 1, cost: 194 ms 
[INFO ] 2025-04-16 09:33:22.416 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:33:24.583 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 32, size: 1, cost: 165 ms 
[INFO ] 2025-04-16 09:33:24.583 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:33:26.794 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 32, size: 1, cost: 210 ms 
[INFO ] 2025-04-16 09:33:26.794 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:33:28.796 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:33:28.797 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 32 
[INFO ] 2025-04-16 09:33:41.730 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 32, size: 1, cost: 234 ms 
[INFO ] 2025-04-16 09:33:41.936 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:33:43.917 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 32, size: 1, cost: 176 ms 
[INFO ] 2025-04-16 09:33:43.917 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:33:46.160 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 32, size: 1, cost: 234 ms 
[INFO ] 2025-04-16 09:33:46.361 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:33:48.336 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 32, size: 1, cost: 168 ms 
[INFO ] 2025-04-16 09:33:48.336 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:33:50.597 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 32, size: 1, cost: 250 ms 
[INFO ] 2025-04-16 09:33:50.598 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:34:01.870 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:34:02.075 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 32 
[INFO ] 2025-04-16 09:34:12.229 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 32, size: 3, cost: 351 ms 
[INFO ] 2025-04-16 09:34:12.229 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:34:19.726 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 32, size: 3, cost: 5492 ms 
[INFO ] 2025-04-16 09:34:19.726 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:34:22.114 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 32, size: 3, cost: 382 ms 
[INFO ] 2025-04-16 09:34:22.315 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:34:24.319 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 32, size: 3, cost: 192 ms 
[INFO ] 2025-04-16 09:34:24.525 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:34:26.518 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 32, size: 3, cost: 183 ms 
[INFO ] 2025-04-16 09:34:26.519 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 4, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:34:28.531 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:34:28.737 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 35 
[INFO ] 2025-04-16 09:34:28.846 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 35, size: 1, cost: 303 ms 
[INFO ] 2025-04-16 09:34:29.053 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:34:31.079 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 35, size: 1, cost: 220 ms 
[INFO ] 2025-04-16 09:34:31.080 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:34:33.318 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 35, size: 1, cost: 228 ms 
[INFO ] 2025-04-16 09:34:33.318 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:34:35.672 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 35, size: 1, cost: 342 ms 
[INFO ] 2025-04-16 09:34:35.672 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:34:38.004 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 35, size: 1, cost: 321 ms 
[INFO ] 2025-04-16 09:34:38.209 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:34:40.022 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:34:40.228 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 35 
[INFO ] 2025-04-16 09:34:50.334 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 35, size: 1, cost: 306 ms 
[INFO ] 2025-04-16 09:34:50.540 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:34:52.581 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 35, size: 1, cost: 229 ms 
[INFO ] 2025-04-16 09:34:52.581 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:34:54.733 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 35, size: 1, cost: 146 ms 
[INFO ] 2025-04-16 09:34:54.938 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:34:57.163 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 35, size: 1, cost: 415 ms 
[INFO ] 2025-04-16 09:34:57.366 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:34:59.413 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 35, size: 1, cost: 234 ms 
[INFO ] 2025-04-16 09:34:59.614 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:35:01.439 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:35:01.440 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 35 
[INFO ] 2025-04-16 09:35:11.648 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 35, size: 1, cost: 198 ms 
[INFO ] 2025-04-16 09:35:11.648 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:35:14.236 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 35, size: 1, cost: 570 ms 
[INFO ] 2025-04-16 09:35:14.438 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:35:17.376 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 35, size: 1, cost: 1120 ms 
[INFO ] 2025-04-16 09:35:17.582 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:35:19.704 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 35, size: 1, cost: 304 ms 
[INFO ] 2025-04-16 09:35:19.705 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:35:22.045 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 35, size: 1, cost: 331 ms 
[INFO ] 2025-04-16 09:35:22.251 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:35:24.062 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:35:24.073 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 35 
[INFO ] 2025-04-16 09:35:34.373 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 35, size: 1, cost: 294 ms 
[INFO ] 2025-04-16 09:35:34.579 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:35:36.633 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 35, size: 1, cost: 241 ms 
[INFO ] 2025-04-16 09:35:36.633 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:35:38.799 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 35, size: 1, cost: 155 ms 
[INFO ] 2025-04-16 09:35:38.799 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:35:40.916 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 35, size: 1, cost: 110 ms 
[INFO ] 2025-04-16 09:35:40.917 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:35:43.163 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 35, size: 1, cost: 244 ms 
[INFO ] 2025-04-16 09:35:43.369 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:35:45.182 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:35:45.387 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 35 
[INFO ] 2025-04-16 09:35:55.568 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 35, size: 1, cost: 362 ms 
[INFO ] 2025-04-16 09:35:55.774 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:35:57.795 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 35, size: 1, cost: 203 ms 
[INFO ] 2025-04-16 09:35:57.796 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:36:00.135 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 35, size: 1, cost: 329 ms 
[INFO ] 2025-04-16 09:36:00.341 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:36:02.351 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 35, size: 1, cost: 198 ms 
[INFO ] 2025-04-16 09:36:02.351 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:36:04.614 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 35, size: 1, cost: 250 ms 
[INFO ] 2025-04-16 09:36:04.615 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:36:06.633 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:36:06.836 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 35 
[INFO ] 2025-04-16 09:36:16.994 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 35, size: 1, cost: 343 ms 
[INFO ] 2025-04-16 09:36:16.994 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:36:19.358 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 35, size: 1, cost: 358 ms 
[INFO ] 2025-04-16 09:36:19.358 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:36:21.652 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 35, size: 1, cost: 275 ms 
[INFO ] 2025-04-16 09:36:21.652 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:36:23.909 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 35, size: 1, cost: 250 ms 
[INFO ] 2025-04-16 09:36:23.909 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:36:26.308 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 35, size: 1, cost: 377 ms 
[INFO ] 2025-04-16 09:36:26.308 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:36:28.316 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:36:28.317 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 35 
[INFO ] 2025-04-16 09:36:38.605 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 35, size: 1, cost: 272 ms 
[INFO ] 2025-04-16 09:36:38.606 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:36:40.780 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 35, size: 1, cost: 166 ms 
[INFO ] 2025-04-16 09:36:40.780 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:36:43.155 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 35, size: 1, cost: 353 ms 
[INFO ] 2025-04-16 09:36:43.156 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:36:45.369 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 35, size: 1, cost: 200 ms 
[INFO ] 2025-04-16 09:36:45.369 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:36:47.576 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 35, size: 1, cost: 197 ms 
[INFO ] 2025-04-16 09:36:47.576 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:36:49.580 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:36:49.787 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 35 
[TRACE] 2025-04-16 09:36:57.085 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] running status set to false 
[INFO ] 2025-04-16 09:36:57.288 - [CDC log cache task from Sybase][Sybase] - Log Miner is shutting down... 
[INFO ] 2025-04-16 09:36:57.373 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 35, size: 1, cost: 284 ms 
[TRACE] 2025-04-16 09:36:57.373 - [CDC log cache task from Sybase][Sybase] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744767130027 
[TRACE] 2025-04-16 09:36:57.373 - [CDC log cache task from Sybase][Sybase] - PDK connector node released: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744767130027 
[INFO ] 2025-04-16 09:36:57.376 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[TRACE] 2025-04-16 09:36:57.376 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] schema data cleaned 
[TRACE] 2025-04-16 09:36:57.376 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] monitor closed 
[TRACE] 2025-04-16 09:36:57.377 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] close complete, cost 291 ms 
[TRACE] 2025-04-16 09:36:57.377 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] running status set to false 
[TRACE] 2025-04-16 09:36:57.384 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[TRACE] 2025-04-16 09:36:57.384 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node released: null 
[TRACE] 2025-04-16 09:36:57.385 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] schema data cleaned 
[TRACE] 2025-04-16 09:36:57.385 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] monitor closed 
[TRACE] 2025-04-16 09:36:57.591 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] close complete, cost 8 ms 
[TRACE] 2025-04-16 09:36:59.000 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 09:36:59.002 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@11a95301 
[TRACE] 2025-04-16 09:36:59.002 - [CDC log cache task from Sybase] - Stop task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase)  
[TRACE] 2025-04-16 09:36:59.141 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[TRACE] 2025-04-16 09:36:59.141 - [CDC log cache task from Sybase] - Snapshot order controller have been removed 
[INFO ] 2025-04-16 09:36:59.141 - [CDC log cache task from Sybase] - Task stopped. 
[INFO ] 2025-04-16 09:36:59.631 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 35, size: 1, cost: 237 ms 
[INFO ] 2025-04-16 09:36:59.632 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:37:01.951 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 35, size: 1, cost: 309 ms 
[INFO ] 2025-04-16 09:37:01.951 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[TRACE] 2025-04-16 09:37:04.155 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 09:37:04.157 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@11a95301 
[TRACE] 2025-04-16 09:37:04.157 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[INFO ] 2025-04-16 09:37:04.157 - [CDC log cache task from Sybase] - Task stopped. 
[INFO ] 2025-04-16 09:37:04.263 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 35, size: 1, cost: 303 ms 
[INFO ] 2025-04-16 09:37:04.271 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[TRACE] 2025-04-16 09:37:04.271 - [CDC log cache task from Sybase] - Remove memory task client succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[TRACE] 2025-04-16 09:37:04.271 - [CDC log cache task from Sybase] - Destroy memory task client cache succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[INFO ] 2025-04-16 09:37:06.462 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 35, size: 1, cost: 189 ms 
[INFO ] 2025-04-16 09:37:06.667 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:37:08.472 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[TRACE] 2025-04-16 09:53:20.588 - [CDC log cache task from Sybase] - Start task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase) 
[INFO ] 2025-04-16 09:53:20.588 - [CDC log cache task from Sybase] - Loading table structure completed 
[TRACE] 2025-04-16 09:53:20.638 - [CDC log cache task from Sybase] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-16 09:53:20.638 - [CDC log cache task from Sybase] - The engine receives CDC log cache task from Sybase task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-16 09:53:20.660 - [CDC log cache task from Sybase] - Task started 
[TRACE] 2025-04-16 09:53:20.660 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] start preload schema,table counts: 2 
[TRACE] 2025-04-16 09:53:20.660 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] preload schema finished, cost 0 ms 
[TRACE] 2025-04-16 09:53:20.666 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-04-16 09:53:20.666 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-04-16 09:53:20.673 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67dbec3d0a3b2cf7b959ffd2, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_bmsql_config, version=v2, tableName=bmsql_config, externalStorageTableName=ExternalStorage_SHARE_CDC_1902686287, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 09:53:20.676 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67fe99000a3b2cf7b97ebe99, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_s2p_text_ddd, version=v2, tableName=s2p_text_ddd, externalStorageTableName=ExternalStorage_SHARE_CDC_-94928111, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 09:53:20.682 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.bmsql_config', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_1902686287', head seq: 0, tail seq: 13830 
[INFO ] 2025-04-16 09:53:20.682 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.s2p_text_ddd', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_-94928111', head seq: 0, tail seq: 4 
[INFO ] 2025-04-16 09:53:20.886 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav17ha?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2025-04-16 09:53:21.088 - [CDC log cache task from Sybase][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-04-16 09:53:21.091 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-04-16 09:53:21.091 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-04-16 09:53:21.091 - [CDC log cache task from Sybase][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-16 09:53:21.091 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 09:53:21.494 - [CDC log cache task from Sybase][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-04-16 09:53:21.994 - [CDC log cache task from Sybase][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 09:53:21.994 - [CDC log cache task from Sybase][Sybase] - Batch read completed. 
[TRACE] 2025-04-16 09:53:22.007 - [CDC log cache task from Sybase][Sybase] - Starting stream read, table list: [dbo.bmsql_config, dbo.s2p_text_ddd], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 09:53:22.008 - [CDC log cache task from Sybase][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-04-16 09:53:22.210 - [CDC log cache task from Sybase][Sybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-04-16 09:53:22.272 - [CDC log cache task from Sybase][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-04-16 09:53:22.388 - [CDC log cache task from Sybase][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-04-16 09:53:22.389 - [CDC log cache task from Sybase][Sybase] - opened cdc for tables: {dbo=[s2p_text_ddd, bmsql_config]} 
[INFO ] 2025-04-16 09:53:22.479 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 09:53:22.479 - [CDC log cache task from Sybase][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-04-16 09:53:22.673 - [CDC log cache task from Sybase][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-04-16 09:53:22.674 - [CDC log cache task from Sybase][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-04-16 09:53:22.675 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 0 
[INFO ] 2025-04-16 09:53:23.485 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 0, size: 26, cost: 812 ms 
[INFO ] 2025-04-16 09:53:23.486 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 87, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:53:25.829 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 0, size: 26, cost: 339 ms 
[INFO ] 2025-04-16 09:53:25.829 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 87, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:53:28.368 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 0, size: 26, cost: 532 ms 
[INFO ] 2025-04-16 09:53:28.369 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 87, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:53:30.612 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 0, size: 26, cost: 240 ms 
[INFO ] 2025-04-16 09:53:30.612 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 87, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:53:33.188 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 0, size: 26, cost: 568 ms 
[INFO ] 2025-04-16 09:53:33.189 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 87, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:53:35.206 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:53:35.207 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 86 
[INFO ] 2025-04-16 09:53:35.388 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 86, size: 1, cost: 180 ms 
[INFO ] 2025-04-16 09:53:35.389 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[TRACE] 2025-04-16 09:53:37.038 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@55e1f9bf: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744767641020,"tableId":"s2p_text_ddd","time":1744768415205,"type":205} 
[TRACE] 2025-04-16 09:53:37.038 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@5f1992cf: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744768329036,"tableId":"s2p_text_ddd","time":1744768415206,"type":205} 
[INFO ] 2025-04-16 09:53:37.728 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 86, size: 1, cost: 335 ms 
[INFO ] 2025-04-16 09:53:37.728 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[TRACE] 2025-04-16 09:53:37.777 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@55e1f9bf: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744767641020,"tableId":"s2p_text_ddd","time":1744768415205,"type":205}). Wait for all previous events to be processed 
[TRACE] 2025-04-16 09:53:37.777 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@55e1f9bf: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744767641020,"tableId":"s2p_text_ddd","time":1744768415205,"type":205}) 
[TRACE] 2025-04-16 09:53:37.778 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@5f1992cf: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744768329036,"tableId":"s2p_text_ddd","time":1744768415206,"type":205}). Wait for all previous events to be processed 
[TRACE] 2025-04-16 09:53:38.799 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@5f1992cf: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744768329036,"tableId":"s2p_text_ddd","time":1744768415206,"type":205}) 
[INFO ] 2025-04-16 09:53:39.938 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 86, size: 1, cost: 205 ms 
[INFO ] 2025-04-16 09:53:39.938 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:53:42.324 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 86, size: 1, cost: 382 ms 
[INFO ] 2025-04-16 09:53:42.325 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:53:44.570 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 86, size: 1, cost: 241 ms 
[INFO ] 2025-04-16 09:53:44.571 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:53:46.576 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:53:46.577 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 86 
[INFO ] 2025-04-16 09:53:56.694 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 86, size: 1, cost: 112 ms 
[INFO ] 2025-04-16 09:53:56.695 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:53:58.815 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 86, size: 1, cost: 115 ms 
[INFO ] 2025-04-16 09:53:58.816 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:54:00.993 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 86, size: 1, cost: 172 ms 
[INFO ] 2025-04-16 09:54:00.993 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[TRACE] 2025-04-16 09:54:02.344 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] running status set to false 
[INFO ] 2025-04-16 09:54:02.344 - [CDC log cache task from Sybase][Sybase] - Log Miner is shutting down... 
[TRACE] 2025-04-16 09:54:02.504 - [CDC log cache task from Sybase][Sybase] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744768401010 
[TRACE] 2025-04-16 09:54:02.504 - [CDC log cache task from Sybase][Sybase] - PDK connector node released: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744768401010 
[TRACE] 2025-04-16 09:54:02.504 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] schema data cleaned 
[TRACE] 2025-04-16 09:54:02.504 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] monitor closed 
[TRACE] 2025-04-16 09:54:02.505 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] close complete, cost 161 ms 
[TRACE] 2025-04-16 09:54:02.505 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] running status set to false 
[INFO ] 2025-04-16 09:54:02.506 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 86, size: 1, cost: 161 ms 
[INFO ] 2025-04-16 09:54:02.506 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[TRACE] 2025-04-16 09:54:02.511 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[TRACE] 2025-04-16 09:54:02.511 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node released: null 
[TRACE] 2025-04-16 09:54:02.512 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] schema data cleaned 
[TRACE] 2025-04-16 09:54:02.512 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] monitor closed 
[TRACE] 2025-04-16 09:54:02.717 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] close complete, cost 7 ms 
[INFO ] 2025-04-16 09:54:04.841 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 86, size: 1, cost: 328 ms 
[INFO ] 2025-04-16 09:54:04.841 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[TRACE] 2025-04-16 09:54:05.916 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 09:54:05.916 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2f21440b 
[TRACE] 2025-04-16 09:54:05.916 - [CDC log cache task from Sybase] - Stop task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase)  
[TRACE] 2025-04-16 09:54:06.041 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[TRACE] 2025-04-16 09:54:06.041 - [CDC log cache task from Sybase] - Snapshot order controller have been removed 
[INFO ] 2025-04-16 09:54:06.246 - [CDC log cache task from Sybase] - Task stopped. 
[INFO ] 2025-04-16 09:54:06.848 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[TRACE] 2025-04-16 09:54:11.067 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 09:54:11.067 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2f21440b 
[TRACE] 2025-04-16 09:54:11.067 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[INFO ] 2025-04-16 09:54:11.068 - [CDC log cache task from Sybase] - Task stopped. 
[TRACE] 2025-04-16 09:54:11.142 - [CDC log cache task from Sybase] - Remove memory task client succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[TRACE] 2025-04-16 09:54:11.145 - [CDC log cache task from Sybase] - Destroy memory task client cache succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[TRACE] 2025-04-16 09:54:13.858 - [CDC log cache task from Sybase] - Start task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase) 
[INFO ] 2025-04-16 09:54:13.954 - [CDC log cache task from Sybase] - Loading table structure completed 
[TRACE] 2025-04-16 09:54:13.954 - [CDC log cache task from Sybase] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-16 09:54:14.004 - [CDC log cache task from Sybase] - The engine receives CDC log cache task from Sybase task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-16 09:54:14.005 - [CDC log cache task from Sybase] - Task started 
[TRACE] 2025-04-16 09:54:14.020 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] start preload schema,table counts: 1 
[TRACE] 2025-04-16 09:54:14.020 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] preload schema finished, cost 0 ms 
[TRACE] 2025-04-16 09:54:14.029 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-04-16 09:54:14.029 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-04-16 09:54:14.057 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67fe99000a3b2cf7b97ebe99, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_s2p_text_ddd, version=v2, tableName=s2p_text_ddd, externalStorageTableName=ExternalStorage_SHARE_CDC_-94928111, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 09:54:14.057 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.s2p_text_ddd', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_-94928111', head seq: 0, tail seq: 6 
[INFO ] 2025-04-16 09:54:14.174 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav17ha?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2025-04-16 09:54:14.174 - [CDC log cache task from Sybase][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-04-16 09:54:14.174 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-04-16 09:54:14.174 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" event queue capacity: 200 
[INFO ] 2025-04-16 09:54:14.175 - [CDC log cache task from Sybase][Sybase] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-04-16 09:54:14.175 - [CDC log cache task from Sybase][Sybase] - Use existing stream offset: {"startRid":368681,"rowId":72,"h":11580746,"l":0} 
[INFO ] 2025-04-16 09:54:14.225 - [CDC log cache task from Sybase][Sybase] - Batch read completed. 
[TRACE] 2025-04-16 09:54:14.226 - [CDC log cache task from Sybase][Sybase] - Starting stream read, table list: [dbo.s2p_text_ddd], offset: {"startRid":368681,"rowId":72,"h":11580746,"l":0} 
[INFO ] 2025-04-16 09:54:14.226 - [CDC log cache task from Sybase][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-04-16 09:54:14.431 - [CDC log cache task from Sybase][Sybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-04-16 09:54:14.473 - [CDC log cache task from Sybase][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-04-16 09:54:14.473 - [CDC log cache task from Sybase][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-04-16 09:54:14.487 - [CDC log cache task from Sybase][Sybase] - opened cdc for tables: {dbo=[s2p_text_ddd]} 
[INFO ] 2025-04-16 09:54:14.488 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 09:54:14.693 - [CDC log cache task from Sybase][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-04-16 09:54:14.856 - [CDC log cache task from Sybase][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-04-16 09:54:14.953 - [CDC log cache task from Sybase][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-04-16 09:54:14.953 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 0 
[INFO ] 2025-04-16 09:54:15.731 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 0, size: 26, cost: 778 ms 
[INFO ] 2025-04-16 09:54:15.732 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 87, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:54:17.984 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 0, size: 26, cost: 250 ms 
[INFO ] 2025-04-16 09:54:17.985 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 87, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:54:20.277 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 0, size: 26, cost: 289 ms 
[INFO ] 2025-04-16 09:54:20.277 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 87, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:54:22.458 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 0, size: 26, cost: 176 ms 
[INFO ] 2025-04-16 09:54:22.459 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 87, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:54:24.941 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 0, size: 26, cost: 475 ms 
[INFO ] 2025-04-16 09:54:24.942 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 87, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:54:26.950 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:54:26.951 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 86 
[INFO ] 2025-04-16 09:54:27.251 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 86, size: 1, cost: 300 ms 
[INFO ] 2025-04-16 09:54:27.252 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[TRACE] 2025-04-16 09:54:29.280 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@228322ca: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744767641020,"tableId":"s2p_text_ddd","time":1744768466949,"type":205} 
[TRACE] 2025-04-16 09:54:29.280 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@287b49ba: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744768329036,"tableId":"s2p_text_ddd","time":1744768466950,"type":205} 
[INFO ] 2025-04-16 09:54:29.655 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 86, size: 1, cost: 396 ms 
[INFO ] 2025-04-16 09:54:29.862 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[TRACE] 2025-04-16 09:54:30.096 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@228322ca: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744767641020,"tableId":"s2p_text_ddd","time":1744768466949,"type":205}). Wait for all previous events to be processed 
[TRACE] 2025-04-16 09:54:30.096 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@228322ca: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744767641020,"tableId":"s2p_text_ddd","time":1744768466949,"type":205}) 
[TRACE] 2025-04-16 09:54:30.097 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@287b49ba: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744768329036,"tableId":"s2p_text_ddd","time":1744768466950,"type":205}). Wait for all previous events to be processed 
[TRACE] 2025-04-16 09:54:31.113 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@287b49ba: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744768329036,"tableId":"s2p_text_ddd","time":1744768466950,"type":205}) 
[INFO ] 2025-04-16 09:54:31.804 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 86, size: 3, cost: 141 ms 
[INFO ] 2025-04-16 09:54:31.804 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 14, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:54:33.919 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 86, size: 3, cost: 109 ms 
[INFO ] 2025-04-16 09:54:33.920 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 14, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:54:36.090 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 86, size: 3, cost: 168 ms 
[INFO ] 2025-04-16 09:54:36.090 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 14, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:54:38.096 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:54:38.096 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 99 
[INFO ] 2025-04-16 09:54:38.231 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 99, size: 1, cost: 135 ms 
[INFO ] 2025-04-16 09:54:38.232 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:54:40.507 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 99, size: 1, cost: 268 ms 
[INFO ] 2025-04-16 09:54:40.508 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:54:42.731 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 99, size: 1, cost: 218 ms 
[INFO ] 2025-04-16 09:54:42.732 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:54:44.924 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 99, size: 1, cost: 187 ms 
[INFO ] 2025-04-16 09:54:44.925 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:54:47.122 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 99, size: 1, cost: 197 ms 
[INFO ] 2025-04-16 09:54:47.123 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:54:49.127 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:54:49.128 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 99 
[INFO ] 2025-04-16 09:54:59.429 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 99, size: 5, cost: 300 ms 
[INFO ] 2025-04-16 09:54:59.430 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:55:01.568 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 99, size: 5, cost: 132 ms 
[INFO ] 2025-04-16 09:55:01.569 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:55:03.766 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 99, size: 5, cost: 195 ms 
[INFO ] 2025-04-16 09:55:03.766 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:55:05.911 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 99, size: 5, cost: 143 ms 
[INFO ] 2025-04-16 09:55:05.911 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:55:08.128 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 99, size: 5, cost: 210 ms 
[INFO ] 2025-04-16 09:55:08.129 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:55:10.133 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:55:10.134 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 115 
[INFO ] 2025-04-16 09:55:10.208 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 115, size: 1, cost: 74 ms 
[INFO ] 2025-04-16 09:55:10.209 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:55:18.064 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 115, size: 1, cost: 5851 ms 
[INFO ] 2025-04-16 09:55:18.064 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:56:40.030 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 115, size: 1, cost: 79964 ms 
[INFO ] 2025-04-16 09:56:40.032 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 2, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:56:42.238 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 115, size: 3, cost: 203 ms 
[INFO ] 2025-04-16 09:56:42.239 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 5, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:56:44.412 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 115, size: 3, cost: 169 ms 
[INFO ] 2025-04-16 09:56:44.412 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 5, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:56:46.417 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:56:46.417 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 119 
[INFO ] 2025-04-16 09:56:46.507 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 119, size: 1, cost: 89 ms 
[INFO ] 2025-04-16 09:56:46.507 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:56:48.659 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 119, size: 1, cost: 149 ms 
[INFO ] 2025-04-16 09:56:48.659 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[TRACE] 2025-04-16 09:56:50.067 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] running status set to false 
[INFO ] 2025-04-16 09:56:50.067 - [CDC log cache task from Sybase][Sybase] - Log Miner is shutting down... 
[INFO ] 2025-04-16 09:56:50.196 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 119, size: 1, cost: 128 ms 
[INFO ] 2025-04-16 09:56:50.196 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[TRACE] 2025-04-16 09:56:50.202 - [CDC log cache task from Sybase][Sybase] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744768454115 
[TRACE] 2025-04-16 09:56:50.203 - [CDC log cache task from Sybase][Sybase] - PDK connector node released: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744768454115 
[TRACE] 2025-04-16 09:56:50.203 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] schema data cleaned 
[TRACE] 2025-04-16 09:56:50.203 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] monitor closed 
[TRACE] 2025-04-16 09:56:50.203 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] close complete, cost 136 ms 
[TRACE] 2025-04-16 09:56:50.203 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] running status set to false 
[TRACE] 2025-04-16 09:56:50.212 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[TRACE] 2025-04-16 09:56:50.212 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node released: null 
[TRACE] 2025-04-16 09:56:50.212 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] schema data cleaned 
[TRACE] 2025-04-16 09:56:50.212 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] monitor closed 
[TRACE] 2025-04-16 09:56:50.417 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] close complete, cost 9 ms 
[INFO ] 2025-04-16 09:56:52.323 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 119, size: 1, cost: 122 ms 
[INFO ] 2025-04-16 09:56:52.324 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:56:54.420 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 119, size: 1, cost: 91 ms 
[INFO ] 2025-04-16 09:56:54.420 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[TRACE] 2025-04-16 09:56:55.033 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 09:56:55.034 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@20162444 
[TRACE] 2025-04-16 09:56:55.158 - [CDC log cache task from Sybase] - Stop task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase)  
[TRACE] 2025-04-16 09:56:55.158 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[TRACE] 2025-04-16 09:56:55.158 - [CDC log cache task from Sybase] - Snapshot order controller have been removed 
[INFO ] 2025-04-16 09:56:55.158 - [CDC log cache task from Sybase] - Task stopped. 
[TRACE] 2025-04-16 09:56:55.161 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 09:56:55.161 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@20162444 
[TRACE] 2025-04-16 09:56:55.161 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[INFO ] 2025-04-16 09:56:55.161 - [CDC log cache task from Sybase] - Task stopped. 
[TRACE] 2025-04-16 09:56:55.178 - [CDC log cache task from Sybase] - Remove memory task client succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[TRACE] 2025-04-16 09:56:55.181 - [CDC log cache task from Sybase] - Destroy memory task client cache succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[INFO ] 2025-04-16 09:56:56.425 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:57:20.457 - [CDC log cache task from Sybase] - This task already stopped. 
[TRACE] 2025-04-16 09:57:29.143 - [CDC log cache task from Sybase] - Start task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase) 
[INFO ] 2025-04-16 09:57:29.144 - [CDC log cache task from Sybase] - Loading table structure completed 
[TRACE] 2025-04-16 09:57:29.195 - [CDC log cache task from Sybase] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-16 09:57:29.195 - [CDC log cache task from Sybase] - The engine receives CDC log cache task from Sybase task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-16 09:57:29.213 - [CDC log cache task from Sybase] - Task started 
[TRACE] 2025-04-16 09:57:29.213 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] start preload schema,table counts: 1 
[TRACE] 2025-04-16 09:57:29.213 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] preload schema finished, cost 0 ms 
[TRACE] 2025-04-16 09:57:29.228 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-04-16 09:57:29.228 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-04-16 09:57:29.237 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67fe99000a3b2cf7b97ebe99, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_s2p_text_ddd, version=v2, tableName=s2p_text_ddd, externalStorageTableName=ExternalStorage_SHARE_CDC_-94928111, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 09:57:29.237 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.s2p_text_ddd', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_-94928111', head seq: 0, tail seq: 8 
[INFO ] 2025-04-16 09:57:29.439 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav17ha?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2025-04-16 09:57:29.668 - [CDC log cache task from Sybase][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-04-16 09:57:29.671 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-04-16 09:57:29.671 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-04-16 09:57:29.671 - [CDC log cache task from Sybase][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-16 09:57:29.671 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 09:57:30.077 - [CDC log cache task from Sybase][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-04-16 09:57:30.189 - [CDC log cache task from Sybase][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 09:57:30.257 - [CDC log cache task from Sybase][Sybase] - Batch read completed. 
[TRACE] 2025-04-16 09:57:30.258 - [CDC log cache task from Sybase][Sybase] - Starting stream read, table list: [dbo.s2p_text_ddd], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 09:57:30.258 - [CDC log cache task from Sybase][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-04-16 09:57:30.273 - [CDC log cache task from Sybase][Sybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-04-16 09:57:30.539 - [CDC log cache task from Sybase][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-04-16 09:57:30.539 - [CDC log cache task from Sybase][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-04-16 09:57:30.557 - [CDC log cache task from Sybase][Sybase] - opened cdc for tables: {dbo=[s2p_text_ddd]} 
[INFO ] 2025-04-16 09:57:30.557 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 09:57:30.758 - [CDC log cache task from Sybase][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-04-16 09:57:30.961 - [CDC log cache task from Sybase][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-04-16 09:57:30.998 - [CDC log cache task from Sybase][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-04-16 09:57:30.998 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 0 
[INFO ] 2025-04-16 09:57:44.734 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 0, size: 34, cost: 13735 ms 
[INFO ] 2025-04-16 09:57:44.734 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 120, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:57:47.150 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 0, size: 34, cost: 415 ms 
[INFO ] 2025-04-16 09:57:47.151 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 120, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:57:49.467 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 0, size: 34, cost: 311 ms 
[INFO ] 2025-04-16 09:57:49.468 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 120, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:57:51.722 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 0, size: 34, cost: 250 ms 
[INFO ] 2025-04-16 09:57:51.723 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 120, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:57:54.137 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 0, size: 34, cost: 409 ms 
[INFO ] 2025-04-16 09:57:54.137 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 120, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:57:56.149 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:57:56.149 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 119 
[INFO ] 2025-04-16 09:57:56.266 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 119, size: 1, cost: 117 ms 
[INFO ] 2025-04-16 09:57:56.266 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:57:58.396 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 119, size: 1, cost: 127 ms 
[INFO ] 2025-04-16 09:57:58.396 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[TRACE] 2025-04-16 09:58:00.539 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@653e9937: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744767641020,"tableId":"s2p_text_ddd","time":1744768676143,"type":205} 
[TRACE] 2025-04-16 09:58:00.539 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@6fc6f247: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744768329036,"tableId":"s2p_text_ddd","time":1744768676143,"type":205} 
[INFO ] 2025-04-16 09:58:00.563 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 119, size: 1, cost: 165 ms 
[INFO ] 2025-04-16 09:58:00.564 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[TRACE] 2025-04-16 09:58:01.233 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@653e9937: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744767641020,"tableId":"s2p_text_ddd","time":1744768676143,"type":205}). Wait for all previous events to be processed 
[TRACE] 2025-04-16 09:58:02.235 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@653e9937: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744767641020,"tableId":"s2p_text_ddd","time":1744768676143,"type":205}) 
[TRACE] 2025-04-16 09:58:02.236 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@6fc6f247: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744768329036,"tableId":"s2p_text_ddd","time":1744768676143,"type":205}). Wait for all previous events to be processed 
[INFO ] 2025-04-16 09:58:02.699 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 119, size: 1, cost: 132 ms 
[INFO ] 2025-04-16 09:58:02.700 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[TRACE] 2025-04-16 09:58:03.269 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@6fc6f247: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744768329036,"tableId":"s2p_text_ddd","time":1744768676143,"type":205}) 
[INFO ] 2025-04-16 09:58:04.997 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 119, size: 1, cost: 291 ms 
[INFO ] 2025-04-16 09:58:04.997 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:58:07.000 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:58:07.000 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 119 
[INFO ] 2025-04-16 09:58:17.146 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 119, size: 1, cost: 140 ms 
[INFO ] 2025-04-16 09:58:17.146 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:58:19.229 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 119, size: 1, cost: 77 ms 
[INFO ] 2025-04-16 09:58:19.230 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:58:21.349 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 119, size: 1, cost: 114 ms 
[INFO ] 2025-04-16 09:58:21.349 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:58:23.481 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 119, size: 1, cost: 129 ms 
[INFO ] 2025-04-16 09:58:23.481 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 09:58:25.587 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 119, size: 1, cost: 107 ms 
[INFO ] 2025-04-16 09:58:25.588 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 09:58:27.591 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 09:58:27.591 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 119 
[INFO ] 2025-04-16 09:58:49.816 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 119, size: 3, cost: 12219 ms 
[INFO ] 2025-04-16 09:58:49.816 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 14, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 09:59:11.298 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 119, size: 3, cost: 19480 ms 
[INFO ] 2025-04-16 09:59:11.301 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 14, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 09:59:27.804 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 119, size: 5, cost: 14501 ms 
[INFO ] 2025-04-16 09:59:27.805 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 09:59:48.826 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 119, size: 5, cost: 19020 ms 
[INFO ] 2025-04-16 09:59:48.827 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 10:00:47.244 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 119, size: 5, cost: 56412 ms 
[INFO ] 2025-04-16 10:00:47.244 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 17, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:00:49.245 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 10:00:49.246 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 135 
[INFO ] 2025-04-16 10:01:06.622 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 135, size: 1, cost: 17376 ms 
[INFO ] 2025-04-16 10:01:06.623 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 10:01:17.907 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 135, size: 1, cost: 9280 ms 
[INFO ] 2025-04-16 10:01:17.907 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 10:01:25.245 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 135, size: 1, cost: 5332 ms 
[INFO ] 2025-04-16 10:01:25.245 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 10:01:27.525 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 135, size: 1, cost: 275 ms 
[INFO ] 2025-04-16 10:01:27.525 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 10:01:29.649 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 135, size: 1, cost: 116 ms 
[INFO ] 2025-04-16 10:01:29.650 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:01:31.655 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 10:01:31.655 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 135 
[INFO ] 2025-04-16 10:01:41.772 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 135, size: 1, cost: 112 ms 
[INFO ] 2025-04-16 10:01:41.773 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 10:01:43.877 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 135, size: 1, cost: 100 ms 
[INFO ] 2025-04-16 10:01:43.877 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 10:01:46.048 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 135, size: 1, cost: 165 ms 
[INFO ] 2025-04-16 10:01:46.049 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 10:01:48.350 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 135, size: 1, cost: 296 ms 
[INFO ] 2025-04-16 10:01:48.350 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 10:01:50.490 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 135, size: 1, cost: 139 ms 
[INFO ] 2025-04-16 10:01:50.490 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:01:52.495 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 10:01:52.495 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 135 
[INFO ] 2025-04-16 10:02:02.605 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 135, size: 1, cost: 104 ms 
[INFO ] 2025-04-16 10:02:02.605 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 10:02:04.712 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 135, size: 1, cost: 104 ms 
[INFO ] 2025-04-16 10:02:04.712 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 10:02:06.839 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 135, size: 1, cost: 120 ms 
[INFO ] 2025-04-16 10:02:06.839 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 10:02:08.988 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 135, size: 1, cost: 144 ms 
[INFO ] 2025-04-16 10:02:08.988 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 10:02:11.172 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 135, size: 1, cost: 179 ms 
[INFO ] 2025-04-16 10:02:11.175 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:02:13.179 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 10:02:13.180 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 135 
[TRACE] 2025-04-16 10:02:14.990 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] running status set to false 
[INFO ] 2025-04-16 10:02:15.157 - [CDC log cache task from Sybase][Sybase] - Log Miner is shutting down... 
[TRACE] 2025-04-16 10:02:15.157 - [CDC log cache task from Sybase][Sybase] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744768649578 
[TRACE] 2025-04-16 10:02:15.157 - [CDC log cache task from Sybase][Sybase] - PDK connector node released: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744768649578 
[TRACE] 2025-04-16 10:02:15.157 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] schema data cleaned 
[TRACE] 2025-04-16 10:02:15.157 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] monitor closed 
[TRACE] 2025-04-16 10:02:15.158 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] close complete, cost 169 ms 
[TRACE] 2025-04-16 10:02:15.158 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] running status set to false 
[INFO ] 2025-04-16 10:02:15.159 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 135, size: 1, cost: 168 ms 
[INFO ] 2025-04-16 10:02:15.159 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[TRACE] 2025-04-16 10:02:15.162 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[TRACE] 2025-04-16 10:02:15.163 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node released: null 
[TRACE] 2025-04-16 10:02:15.163 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] schema data cleaned 
[TRACE] 2025-04-16 10:02:15.163 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] monitor closed 
[TRACE] 2025-04-16 10:02:15.163 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] close complete, cost 5 ms 
[TRACE] 2025-04-16 10:02:15.171 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 10:02:15.171 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5b8a85fc 
[TRACE] 2025-04-16 10:02:15.287 - [CDC log cache task from Sybase] - Stop task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase)  
[TRACE] 2025-04-16 10:02:15.287 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[TRACE] 2025-04-16 10:02:15.287 - [CDC log cache task from Sybase] - Snapshot order controller have been removed 
[INFO ] 2025-04-16 10:02:15.287 - [CDC log cache task from Sybase] - Task stopped. 
[INFO ] 2025-04-16 10:02:17.285 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 135, size: 1, cost: 119 ms 
[INFO ] 2025-04-16 10:02:17.287 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 10:02:19.418 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 135, size: 1, cost: 130 ms 
[INFO ] 2025-04-16 10:02:19.419 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[TRACE] 2025-04-16 10:02:20.296 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 10:02:20.296 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5b8a85fc 
[TRACE] 2025-04-16 10:02:20.297 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[INFO ] 2025-04-16 10:02:20.297 - [CDC log cache task from Sybase] - Task stopped. 
[TRACE] 2025-04-16 10:02:20.327 - [CDC log cache task from Sybase] - Remove memory task client succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[TRACE] 2025-04-16 10:02:20.329 - [CDC log cache task from Sybase] - Destroy memory task client cache succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[INFO ] 2025-04-16 10:02:21.516 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 135, size: 1, cost: 91 ms 
[INFO ] 2025-04-16 10:02:21.517 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 10:02:23.642 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 135, size: 1, cost: 124 ms 
[INFO ] 2025-04-16 10:02:23.643 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:02:25.670 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[TRACE] 2025-04-16 10:06:06.311 - [CDC log cache task from Sybase] - Start task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase) 
[INFO ] 2025-04-16 10:06:06.369 - [CDC log cache task from Sybase] - Loading table structure completed 
[TRACE] 2025-04-16 10:06:06.437 - [CDC log cache task from Sybase] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-16 10:06:06.437 - [CDC log cache task from Sybase] - The engine receives CDC log cache task from Sybase task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-16 10:06:06.474 - [CDC log cache task from Sybase] - Task started 
[TRACE] 2025-04-16 10:06:06.474 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] start preload schema,table counts: 1 
[TRACE] 2025-04-16 10:06:06.474 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] preload schema finished, cost 0 ms 
[TRACE] 2025-04-16 10:06:06.483 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-04-16 10:06:06.483 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-04-16 10:06:06.488 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67fe99000a3b2cf7b97ebe99, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_s2p_text_ddd, version=v2, tableName=s2p_text_ddd, externalStorageTableName=ExternalStorage_SHARE_CDC_-94928111, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 10:06:06.511 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.s2p_text_ddd', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_-94928111', head seq: 0, tail seq: 10 
[INFO ] 2025-04-16 10:06:06.512 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav17ha?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2025-04-16 10:06:07.012 - [CDC log cache task from Sybase][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-04-16 10:06:07.016 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-04-16 10:06:07.016 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-04-16 10:06:07.016 - [CDC log cache task from Sybase][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-16 10:06:07.016 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 10:06:07.418 - [CDC log cache task from Sybase][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-04-16 10:06:07.570 - [CDC log cache task from Sybase][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 10:06:07.570 - [CDC log cache task from Sybase][Sybase] - Batch read completed. 
[TRACE] 2025-04-16 10:06:07.582 - [CDC log cache task from Sybase][Sybase] - Starting stream read, table list: [dbo.s2p_text_ddd], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 10:06:07.583 - [CDC log cache task from Sybase][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-04-16 10:06:07.788 - [CDC log cache task from Sybase][Sybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-04-16 10:06:07.904 - [CDC log cache task from Sybase][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-04-16 10:06:07.904 - [CDC log cache task from Sybase][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-04-16 10:06:07.922 - [CDC log cache task from Sybase][Sybase] - opened cdc for tables: {dbo=[s2p_text_ddd]} 
[INFO ] 2025-04-16 10:06:07.923 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 10:06:08.128 - [CDC log cache task from Sybase][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-04-16 10:06:08.262 - [CDC log cache task from Sybase][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-04-16 10:06:08.262 - [CDC log cache task from Sybase][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-04-16 10:06:08.263 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 0 
[INFO ] 2025-04-16 10:08:18.903 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 0, size: 42, cost: 130642 ms 
[INFO ] 2025-04-16 10:08:18.903 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 152, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 10:08:21.253 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 0, size: 42, cost: 347 ms 
[INFO ] 2025-04-16 10:08:21.254 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 152, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 10:08:23.620 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 0, size: 42, cost: 365 ms 
[INFO ] 2025-04-16 10:08:23.621 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 152, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 10:08:25.953 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 0, size: 42, cost: 328 ms 
[INFO ] 2025-04-16 10:08:25.953 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 152, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 10:08:28.210 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 0, size: 42, cost: 256 ms 
[INFO ] 2025-04-16 10:08:28.210 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 152, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:08:30.213 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 10:08:30.419 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 151 
[INFO ] 2025-04-16 10:08:30.540 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 151, size: 1, cost: 319 ms 
[INFO ] 2025-04-16 10:08:30.540 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 10:08:32.694 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 151, size: 1, cost: 148 ms 
[INFO ] 2025-04-16 10:08:32.695 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[TRACE] 2025-04-16 10:08:33.107 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@1135b5e6: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744767641020,"tableId":"s2p_text_ddd","time":1744769310218,"type":205} 
[TRACE] 2025-04-16 10:08:33.108 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@1bcdde96: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744768329036,"tableId":"s2p_text_ddd","time":1744769310221,"type":205} 
[TRACE] 2025-04-16 10:08:34.127 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@1135b5e6: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744767641020,"tableId":"s2p_text_ddd","time":1744769310218,"type":205}). Wait for all previous events to be processed 
[INFO ] 2025-04-16 10:08:34.837 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 151, size: 1, cost: 136 ms 
[INFO ] 2025-04-16 10:08:34.837 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[TRACE] 2025-04-16 10:08:35.097 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@1135b5e6: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744767641020,"tableId":"s2p_text_ddd","time":1744769310218,"type":205}) 
[TRACE] 2025-04-16 10:08:35.098 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@1bcdde96: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744768329036,"tableId":"s2p_text_ddd","time":1744769310221,"type":205}). Wait for all previous events to be processed 
[TRACE] 2025-04-16 10:08:36.106 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@1bcdde96: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744768329036,"tableId":"s2p_text_ddd","time":1744769310221,"type":205}) 
[INFO ] 2025-04-16 10:08:36.930 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 151, size: 1, cost: 86 ms 
[INFO ] 2025-04-16 10:08:36.930 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 10:08:39.065 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 151, size: 1, cost: 129 ms 
[INFO ] 2025-04-16 10:08:39.066 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:08:41.069 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 10:08:41.070 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 151 
[INFO ] 2025-04-16 10:08:51.294 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 151, size: 1, cost: 219 ms 
[INFO ] 2025-04-16 10:08:51.294 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 10:08:53.392 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 151, size: 1, cost: 92 ms 
[INFO ] 2025-04-16 10:08:53.393 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 10:08:55.621 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 151, size: 1, cost: 223 ms 
[INFO ] 2025-04-16 10:08:55.622 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 10:08:57.848 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 151, size: 1, cost: 213 ms 
[INFO ] 2025-04-16 10:08:57.849 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 10:08:59.996 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 151, size: 1, cost: 143 ms 
[INFO ] 2025-04-16 10:08:59.997 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:09:02.001 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 10:09:02.202 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 151 
[INFO ] 2025-04-16 10:09:12.140 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 151, size: 1, cost: 129 ms 
[INFO ] 2025-04-16 10:09:12.141 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 10:09:14.302 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 151, size: 1, cost: 160 ms 
[INFO ] 2025-04-16 10:09:14.302 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 10:09:16.667 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 151, size: 1, cost: 358 ms 
[INFO ] 2025-04-16 10:09:16.668 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 10:09:18.802 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 151, size: 1, cost: 130 ms 
[INFO ] 2025-04-16 10:09:18.803 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 10:09:20.900 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 151, size: 1, cost: 94 ms 
[INFO ] 2025-04-16 10:09:20.901 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:09:22.907 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 10:09:22.907 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 151 
[INFO ] 2025-04-16 10:09:33.127 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 151, size: 1, cost: 213 ms 
[INFO ] 2025-04-16 10:09:33.128 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[TRACE] 2025-04-16 10:09:35.265 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] running status set to false 
[INFO ] 2025-04-16 10:09:35.277 - [CDC log cache task from Sybase][Sybase] - Log Miner is shutting down... 
[INFO ] 2025-04-16 10:09:35.277 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 151, size: 1, cost: 145 ms 
[INFO ] 2025-04-16 10:09:35.277 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[TRACE] 2025-04-16 10:09:35.365 - [CDC log cache task from Sybase][Sybase] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744769166924 
[TRACE] 2025-04-16 10:09:35.366 - [CDC log cache task from Sybase][Sybase] - PDK connector node released: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744769166924 
[TRACE] 2025-04-16 10:09:35.366 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] schema data cleaned 
[TRACE] 2025-04-16 10:09:35.366 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] monitor closed 
[TRACE] 2025-04-16 10:09:35.366 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] close complete, cost 101 ms 
[TRACE] 2025-04-16 10:09:35.366 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] running status set to false 
[TRACE] 2025-04-16 10:09:35.375 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[TRACE] 2025-04-16 10:09:35.375 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node released: null 
[TRACE] 2025-04-16 10:09:35.376 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] schema data cleaned 
[TRACE] 2025-04-16 10:09:35.377 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] monitor closed 
[TRACE] 2025-04-16 10:09:35.457 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] close complete, cost 10 ms 
[INFO ] 2025-04-16 10:09:35.457 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 151, size: 1, cost: 178 ms 
[INFO ] 2025-04-16 10:09:35.457 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 10:09:37.639 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 151, size: 1, cost: 173 ms 
[INFO ] 2025-04-16 10:09:37.639 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[TRACE] 2025-04-16 10:09:38.104 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 10:09:38.105 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@44aa9fe6 
[TRACE] 2025-04-16 10:09:38.249 - [CDC log cache task from Sybase] - Stop task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase)  
[TRACE] 2025-04-16 10:09:38.249 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[TRACE] 2025-04-16 10:09:38.250 - [CDC log cache task from Sybase] - Snapshot order controller have been removed 
[INFO ] 2025-04-16 10:09:38.250 - [CDC log cache task from Sybase] - Task stopped. 
[INFO ] 2025-04-16 10:09:39.770 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 151, size: 1, cost: 126 ms 
[INFO ] 2025-04-16 10:09:39.771 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:09:41.804 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[TRACE] 2025-04-16 10:09:43.269 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 10:09:43.269 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@44aa9fe6 
[TRACE] 2025-04-16 10:09:43.269 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[INFO ] 2025-04-16 10:09:43.269 - [CDC log cache task from Sybase] - Task stopped. 
[TRACE] 2025-04-16 10:09:43.307 - [CDC log cache task from Sybase] - Remove memory task client succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[TRACE] 2025-04-16 10:09:43.309 - [CDC log cache task from Sybase] - Destroy memory task client cache succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[TRACE] 2025-04-16 10:10:15.261 - [CDC log cache task from Sybase] - Start task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase) 
[INFO ] 2025-04-16 10:10:15.263 - [CDC log cache task from Sybase] - Loading table structure completed 
[TRACE] 2025-04-16 10:10:15.338 - [CDC log cache task from Sybase] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-16 10:10:15.338 - [CDC log cache task from Sybase] - The engine receives CDC log cache task from Sybase task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-16 10:10:15.381 - [CDC log cache task from Sybase] - Task started 
[TRACE] 2025-04-16 10:10:15.381 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] start preload schema,table counts: 1 
[TRACE] 2025-04-16 10:10:15.381 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] preload schema finished, cost 0 ms 
[TRACE] 2025-04-16 10:10:15.384 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-04-16 10:10:15.384 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-04-16 10:10:15.389 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67fe99000a3b2cf7b97ebe99, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67dbd5a2adfcb354e5171aa5_s2p_text_ddd, version=v2, tableName=s2p_text_ddd, externalStorageTableName=ExternalStorage_SHARE_CDC_-94928111, shareCdcTaskId=67dbec3dadfcb354e517583f, connectionId=67dbd5a2adfcb354e5171aa5) 
[INFO ] 2025-04-16 10:10:15.398 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.s2p_text_ddd', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_-94928111', head seq: 0, tail seq: 12 
[INFO ] 2025-04-16 10:10:15.399 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav17ha?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2025-04-16 10:10:15.836 - [CDC log cache task from Sybase][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-04-16 10:10:15.840 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-04-16 10:10:15.840 - [CDC log cache task from Sybase][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-04-16 10:10:15.840 - [CDC log cache task from Sybase][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-16 10:10:15.840 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 10:10:16.040 - [CDC log cache task from Sybase][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-04-16 10:10:16.536 - [CDC log cache task from Sybase][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 10:10:16.536 - [CDC log cache task from Sybase][Sybase] - Batch read completed. 
[TRACE] 2025-04-16 10:10:16.555 - [CDC log cache task from Sybase][Sybase] - Starting stream read, table list: [dbo.s2p_text_ddd], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-04-16 10:10:16.555 - [CDC log cache task from Sybase][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-04-16 10:10:16.756 - [CDC log cache task from Sybase][Sybase] - sybase cdc work with mode v2: manual rescan 
[INFO ] 2025-04-16 10:10:16.756 - [CDC log cache task from Sybase][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-04-16 10:10:16.762 - [CDC log cache task from Sybase][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-04-16 10:10:16.868 - [CDC log cache task from Sybase][Sybase] - opened cdc for tables: {dbo=[s2p_text_ddd]} 
[INFO ] 2025-04-16 10:10:16.869 - [CDC log cache task from Sybase][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-04-16 10:10:17.065 - [CDC log cache task from Sybase][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-04-16 10:10:17.067 - [CDC log cache task from Sybase][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-04-16 10:10:17.167 - [CDC log cache task from Sybase][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-04-16 10:10:17.167 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 0 
[INFO ] 2025-04-16 10:11:55.716 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 0, size: 42, cost: 98544 ms 
[INFO ] 2025-04-16 10:11:55.716 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 152, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 10:11:57.961 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 0, size: 42, cost: 241 ms 
[INFO ] 2025-04-16 10:11:57.961 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 152, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 10:12:00.440 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 0, size: 42, cost: 477 ms 
[INFO ] 2025-04-16 10:12:00.440 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 152, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 10:12:02.657 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 0, size: 42, cost: 212 ms 
[INFO ] 2025-04-16 10:12:02.657 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 152, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 10:12:48.184 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 0, size: 42, cost: 43482 ms 
[INFO ] 2025-04-16 10:12:48.187 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 152, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:12:50.188 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 10:12:50.189 - [CDC log cache task from Sybase][Sybase] - continue normal rescan from startRid: 368681, rowId: 151 
[INFO ] 2025-04-16 10:12:50.567 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 151, size: 1, cost: 377 ms 
[INFO ] 2025-04-16 10:12:50.567 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 10:12:52.783 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 151, size: 1, cost: 212 ms 
[INFO ] 2025-04-16 10:12:52.783 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 10:12:54.869 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 151, size: 1, cost: 84 ms 
[INFO ] 2025-04-16 10:12:54.870 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[TRACE] 2025-04-16 10:12:55.034 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@144e3059: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744767641020,"tableId":"s2p_text_ddd","time":1744769570188,"type":205} 
[TRACE] 2025-04-16 10:12:55.034 - [CDC log cache task from Sybase][Sybase] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapClearTableEvent@1eb65b29: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744768329036,"tableId":"s2p_text_ddd","time":1744769570189,"type":205} 
[TRACE] 2025-04-16 10:12:55.850 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@144e3059: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744767641020,"tableId":"s2p_text_ddd","time":1744769570188,"type":205}). Wait for all previous events to be processed 
[TRACE] 2025-04-16 10:12:56.854 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@144e3059: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744767641020,"tableId":"s2p_text_ddd","time":1744769570188,"type":205}) 
[TRACE] 2025-04-16 10:12:56.855 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@1eb65b29: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744768329036,"tableId":"s2p_text_ddd","time":1744769570189,"type":205}). Wait for all previous events to be processed 
[INFO ] 2025-04-16 10:12:56.992 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 151, size: 1, cost: 121 ms 
[INFO ] 2025-04-16 10:12:56.992 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[TRACE] 2025-04-16 10:12:57.879 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapClearTableEvent@1eb65b29: {"namespaces":["dbo","s2p_text_ddd"],"referenceTime":1744768329036,"tableId":"s2p_text_ddd","time":1744769570189,"type":205}) 
[INFO ] 2025-04-16 10:12:59.120 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 151, size: 1, cost: 127 ms 
[INFO ] 2025-04-16 10:12:59.121 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:13:01.126 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 10:13:01.126 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 151 
[INFO ] 2025-04-16 10:13:11.525 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 151, size: 1, cost: 392 ms 
[INFO ] 2025-04-16 10:13:11.525 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 10:13:13.675 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 151, size: 1, cost: 148 ms 
[INFO ] 2025-04-16 10:13:13.675 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 10:13:15.763 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 151, size: 1, cost: 85 ms 
[INFO ] 2025-04-16 10:13:15.763 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 10:13:17.912 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 151, size: 1, cost: 145 ms 
[INFO ] 2025-04-16 10:13:17.912 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 10:13:20.122 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 151, size: 1, cost: 207 ms 
[INFO ] 2025-04-16 10:13:20.122 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:13:22.127 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 10:13:22.127 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 151 
[INFO ] 2025-04-16 10:13:32.491 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 151, size: 1, cost: 339 ms 
[INFO ] 2025-04-16 10:13:32.491 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 10:13:34.678 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 151, size: 1, cost: 180 ms 
[INFO ] 2025-04-16 10:13:34.678 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 10:13:36.855 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 151, size: 1, cost: 165 ms 
[INFO ] 2025-04-16 10:13:36.855 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 10:13:38.965 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 151, size: 1, cost: 102 ms 
[INFO ] 2025-04-16 10:13:38.965 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 10:13:41.134 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 151, size: 1, cost: 165 ms 
[INFO ] 2025-04-16 10:13:41.134 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:13:43.138 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 10:13:43.138 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 151 
[INFO ] 2025-04-16 10:13:53.347 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 151, size: 1, cost: 200 ms 
[INFO ] 2025-04-16 10:13:53.347 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 10:13:55.543 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 151, size: 1, cost: 192 ms 
[INFO ] 2025-04-16 10:13:55.543 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 10:13:57.767 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 151, size: 1, cost: 219 ms 
[INFO ] 2025-04-16 10:13:57.767 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 10:13:59.901 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 151, size: 1, cost: 133 ms 
[INFO ] 2025-04-16 10:13:59.902 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 10:14:02.012 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 151, size: 1, cost: 105 ms 
[INFO ] 2025-04-16 10:14:02.012 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:14:04.016 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 10:14:04.016 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 151 
[INFO ] 2025-04-16 10:14:14.242 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 151, size: 1, cost: 221 ms 
[INFO ] 2025-04-16 10:14:14.243 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 10:14:16.571 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 151, size: 1, cost: 326 ms 
[INFO ] 2025-04-16 10:14:16.571 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 10:14:18.693 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 151, size: 1, cost: 118 ms 
[INFO ] 2025-04-16 10:14:18.694 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 10:14:20.875 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 151, size: 1, cost: 177 ms 
[INFO ] 2025-04-16 10:14:20.875 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 10:14:22.973 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 151, size: 1, cost: 93 ms 
[INFO ] 2025-04-16 10:14:22.973 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:14:24.979 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 10:14:24.979 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 151 
[INFO ] 2025-04-16 10:14:35.103 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 151, size: 1, cost: 123 ms 
[INFO ] 2025-04-16 10:14:35.103 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[INFO ] 2025-04-16 10:14:37.254 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 151, size: 1, cost: 151 ms 
[INFO ] 2025-04-16 10:14:37.255 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[INFO ] 2025-04-16 10:14:39.408 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 151, size: 1, cost: 148 ms 
[INFO ] 2025-04-16 10:14:39.408 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 10:14:42.289 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 151, size: 1, cost: 875 ms 
[INFO ] 2025-04-16 10:14:42.289 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 10:14:44.438 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 151, size: 1, cost: 146 ms 
[INFO ] 2025-04-16 10:14:44.438 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:14:46.439 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 10:14:46.439 - [CDC log cache task from Sybase][Sybase] - normal rescan, will sleep 1s, and scan from startRid: 368681, rowId: 151 
[TRACE] 2025-04-16 10:14:49.780 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] running status set to false 
[INFO ] 2025-04-16 10:14:49.780 - [CDC log cache task from Sybase][Sybase] - Log Miner is shutting down... 
[TRACE] 2025-04-16 10:14:53.419 - [CDC log cache task from Sybase][Sybase] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744769415766 
[TRACE] 2025-04-16 10:14:53.419 - [CDC log cache task from Sybase][Sybase] - PDK connector node released: HazelcastSourcePdkShareCDCNode_c883fca04449418d8bb64a8018c26177_1744769415766 
[TRACE] 2025-04-16 10:14:53.419 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] schema data cleaned 
[TRACE] 2025-04-16 10:14:53.419 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] monitor closed 
[TRACE] 2025-04-16 10:14:53.877 - [CDC log cache task from Sybase][Sybase] - Node Sybase[c883fca04449418d8bb64a8018c26177] close complete, cost 4099 ms 
[TRACE] 2025-04-16 10:14:53.877 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] running status set to false 
[TRACE] 2025-04-16 10:14:55.357 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[TRACE] 2025-04-16 10:14:55.357 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - PDK connector node released: null 
[TRACE] 2025-04-16 10:14:55.358 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] schema data cleaned 
[TRACE] 2025-04-16 10:14:55.358 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] monitor closed 
[TRACE] 2025-04-16 10:14:55.366 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[17d99cb06490484194b989b11a320ad3] close complete, cost 1481 ms 
[INFO ] 2025-04-16 10:14:55.367 - [CDC log cache task from Sybase][Sybase] - scan online t: 1, 368681, 151, size: 1, cost: 5587 ms 
[INFO ] 2025-04-16 10:14:55.367 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 1 to 5 
[TRACE] 2025-04-16 10:15:04.681 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 10:15:04.681 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7ae80baf 
[TRACE] 2025-04-16 10:15:04.682 - [CDC log cache task from Sybase] - Stop task milestones: 67dbec3dadfcb354e517583f(CDC log cache task from Sybase)  
[INFO ] 2025-04-16 10:15:04.756 - [CDC log cache task from Sybase][Sybase] - scan online t: 2, 368681, 151, size: 1, cost: 7387 ms 
[INFO ] 2025-04-16 10:15:04.756 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 2 to 5 
[TRACE] 2025-04-16 10:15:04.789 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[TRACE] 2025-04-16 10:15:04.789 - [CDC log cache task from Sybase] - Snapshot order controller have been removed 
[INFO ] 2025-04-16 10:15:04.789 - [CDC log cache task from Sybase] - Task stopped. 
[TRACE] 2025-04-16 10:15:10.566 - [CDC log cache task from Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-16 10:15:10.566 - [CDC log cache task from Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7ae80baf 
[TRACE] 2025-04-16 10:15:10.566 - [CDC log cache task from Sybase] - Stopped task aspect(s) 
[INFO ] 2025-04-16 10:15:10.566 - [CDC log cache task from Sybase] - Task stopped. 
[TRACE] 2025-04-16 10:15:11.234 - [CDC log cache task from Sybase] - Remove memory task client succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[TRACE] 2025-04-16 10:15:11.234 - [CDC log cache task from Sybase] - Destroy memory task client cache succeed, task: CDC log cache task from Sybase[67dbec3dadfcb354e517583f] 
[INFO ] 2025-04-16 10:15:25.784 - [CDC log cache task from Sybase][Sybase] - scan online t: 3, 368681, 151, size: 1, cost: 19023 ms 
[INFO ] 2025-04-16 10:15:25.784 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 3 to 5 
[INFO ] 2025-04-16 10:16:38.747 - [CDC log cache task from Sybase][Sybase] - scan online t: 4, 368681, 151, size: 1, cost: 70957 ms 
[INFO ] 2025-04-16 10:16:38.749 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 4 to 5 
[INFO ] 2025-04-16 10:16:40.916 - [CDC log cache task from Sybase][Sybase] - scan online t: 5, 368681, 151, size: 1, cost: 163 ms 
[INFO ] 2025-04-16 10:16:40.916 - [CDC log cache task from Sybase][Sybase] - scan online logs not enough: 1, will rescan it until it's enough: 999, or loop: 5 to 5 
[INFO ] 2025-04-16 10:16:42.938 - [CDC log cache task from Sybase][Sybase] - uncommit trans size: 0 
[INFO ] 2025-04-16 10:17:43.956 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.s2p_text_ddd', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_-94928111', head seq: 0, tail seq: 14 
[INFO ] 2025-04-16 10:20:06.241 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.s2p_text_ddd', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_-94928111', head seq: 0, tail seq: 16 
[INFO ] 2025-04-16 10:25:06.944 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.s2p_text_ddd', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_-94928111', head seq: 0, tail seq: 16 
[INFO ] 2025-04-16 10:41:00.991 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.s2p_text_ddd', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_-94928111', head seq: 0, tail seq: 16 
[INFO ] 2025-04-16 10:53:29.035 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.s2p_text_ddd', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_-94928111', head seq: 0, tail seq: 16 
[INFO ] 2025-04-16 10:55:56.142 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.s2p_text_ddd', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_-94928111', head seq: 0, tail seq: 16 
[INFO ] 2025-04-16 10:59:17.488 - [CDC log cache task from Sybase][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_CDC log cache task from Sybase_dbo.s2p_text_ddd', name space: 'tapdatav17ha.ExternalStorage_SHARE_CDC_-94928111', head seq: 0, tail seq: 16 
