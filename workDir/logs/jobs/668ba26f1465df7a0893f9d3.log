[INFO ] 2024-07-12 10:22:35.273 - [任务 50][SouceMysql] - Node SouceMysql[5b2d2243-b321-46d4-9a91-93bfe16431ab] running status set to false 
[INFO ] 2024-07-12 10:22:35.357 - [任务 50] - Task initialization... 
[INFO ] 2024-07-12 10:22:35.381 - [任务 50] - Start task milestones: 668ba26f1465df7a0893f9d3(任务 50) 
[INFO ] 2024-07-12 10:22:37.574 - [任务 50] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-12 10:22:38.021 - [任务 50] - The engine receives 任务 50 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-12 10:22:38.797 - [任务 50][TargetMysql] - Node TargetMysql[0599f520-246c-4137-bbbd-2e3fd3f4d255] start preload schema,table counts: 1 
[INFO ] 2024-07-12 10:22:38.803 - [任务 50][TargetMysql] - Node TargetMysql[0599f520-246c-4137-bbbd-2e3fd3f4d255] preload schema finished, cost 1 ms 
[INFO ] 2024-07-12 10:22:38.821 - [任务 50][SouceMysql] - Node SouceMysql[5b2d2243-b321-46d4-9a91-93bfe16431ab] start preload schema,table counts: 1 
[INFO ] 2024-07-12 10:22:38.821 - [任务 50][SouceMysql] - Node SouceMysql[5b2d2243-b321-46d4-9a91-93bfe16431ab] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 10:22:40.069 - [任务 50][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-07-12 10:22:40.070 - [任务 50][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-07-12 10:22:40.070 - [任务 50][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-12 10:22:40.088 - [任务 50][SouceMysql] - batch offset found: {},stream offset found: {"name":"45cd1298-4585-4448-b1e1-5fd53df620b1","offset":{"{\"server\":\"45cd1298-4585-4448-b1e1-5fd53df620b1\"}":"{\"file\":\"binlog.000032\",\"pos\":92396103,\"server_id\":1}"}} 
[INFO ] 2024-07-12 10:22:40.089 - [任务 50][SouceMysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-12 10:22:40.175 - [任务 50][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-07-12 10:22:40.175 - [任务 50][SouceMysql] - Initial sync completed 
[INFO ] 2024-07-12 10:22:40.219 - [任务 50][SouceMysql] - Starting stream read, table list: [POLICY], offset: {"name":"45cd1298-4585-4448-b1e1-5fd53df620b1","offset":{"{\"server\":\"45cd1298-4585-4448-b1e1-5fd53df620b1\"}":"{\"file\":\"binlog.000032\",\"pos\":92396103,\"server_id\":1}"}} 
[INFO ] 2024-07-12 10:22:40.220 - [任务 50][SouceMysql] - Starting mysql cdc, server name: 45cd1298-4585-4448-b1e1-5fd53df620b1 
[INFO ] 2024-07-12 10:22:40.318 - [任务 50][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1122555733
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 45cd1298-4585-4448-b1e1-5fd53df620b1
  database.port: 3306
  threadName: Debezium-Mysql-Connector-45cd1298-4585-4448-b1e1-5fd53df620b1
  database.hostname: localhost
  database.password: ********
  name: 45cd1298-4585-4448-b1e1-5fd53df620b1
  pdk.offset.string: {"name":"45cd1298-4585-4448-b1e1-5fd53df620b1","offset":{"{\"server\":\"45cd1298-4585-4448-b1e1-5fd53df620b1\"}":"{\"file\":\"binlog.000032\",\"pos\":92396103,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.POLICY
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-12 10:22:40.430 - [任务 50][TargetMysql] - Node(TargetMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-12 10:22:40.430 - [任务 50][TargetMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-12 10:22:41.274 - [任务 50][SouceMysql] - Connector Mysql incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-12 10:51:23.117 - [任务 50][SouceMysql] - Node SouceMysql[5b2d2243-b321-46d4-9a91-93bfe16431ab] running status set to false 
