[INFO ] 2024-07-03 10:48:37.031 - [来自SourceMongo的共享挖掘任务] - Start task milestones: 6683c412fa7caf4ccea13264(来自SourceMongo的共享挖掘任务) 
[INFO ] 2024-07-03 10:48:37.057 - [来自SourceMongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-03 10:48:37.117 - [来自SourceMongo的共享挖掘任务] - The engine receives 来自SourceMongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-03 10:48:37.117 - [来自SourceMongo的共享挖掘任务][SourceMongo] - Node SourceMongo[351e6a06bb654273b11331c3379e041d] start preload schema,table counts: 1 
[INFO ] 2024-07-03 10:48:37.117 - [来自SourceMongo的共享挖掘任务][SourceMongo] - Node SourceMongo[351e6a06bb654273b11331c3379e041d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-03 10:48:37.121 - [来自SourceMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-03 10:48:37.133 - [来自SourceMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-03 10:48:37.133 - [来自SourceMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-03 10:48:37.147 - [来自SourceMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
[INFO ] 2024-07-03 10:48:37.147 - [来自SourceMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-03 10:48:37.280 - [来自SourceMongo的共享挖掘任务][SourceMongo] - Source node "SourceMongo" read batch size: 2000 
[INFO ] 2024-07-03 10:48:37.281 - [来自SourceMongo的共享挖掘任务][SourceMongo] - Source node "SourceMongo" event queue capacity: 4000 
[INFO ] 2024-07-03 10:48:37.281 - [来自SourceMongo的共享挖掘任务][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-03 10:48:37.402 - [来自SourceMongo的共享挖掘任务][SourceMongo] - batch offset found: {},stream offset found: {"_data":{"value":"8266841A63000000012B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F696400646683D43666AB5EDE8A9E619B0004","bsonType":"STRING","double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"number":false,"array":false,"null":false}} 
[INFO ] 2024-07-03 10:48:37.405 - [来自SourceMongo的共享挖掘任务][SourceMongo] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"_data":{"value":"8266841A63000000012B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F696400646683D43666AB5EDE8A9E619B0004","bsonType":"STRING","double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"number":false,"array":false,"null":false}} 
[INFO ] 2024-07-03 10:48:37.542 - [来自SourceMongo的共享挖掘任务][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-03 10:49:03.576 - [来自SourceMongo的共享挖掘任务][SourceMongo] - Node SourceMongo[351e6a06bb654273b11331c3379e041d] running status set to false 
[INFO ] 2024-07-03 10:49:03.591 - [来自SourceMongo的共享挖掘任务][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-351e6a06bb654273b11331c3379e041d 
[INFO ] 2024-07-03 10:49:03.591 - [来自SourceMongo的共享挖掘任务][SourceMongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-351e6a06bb654273b11331c3379e041d 
[INFO ] 2024-07-03 10:49:03.593 - [来自SourceMongo的共享挖掘任务][SourceMongo] - Node SourceMongo[351e6a06bb654273b11331c3379e041d] schema data cleaned 
[INFO ] 2024-07-03 10:49:03.593 - [来自SourceMongo的共享挖掘任务][SourceMongo] - Node SourceMongo[351e6a06bb654273b11331c3379e041d] monitor closed 
[INFO ] 2024-07-03 10:49:03.595 - [来自SourceMongo的共享挖掘任务][SourceMongo] - Node SourceMongo[351e6a06bb654273b11331c3379e041d] close complete, cost 49 ms 
[INFO ] 2024-07-03 10:49:03.595 - [来自SourceMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[43b2c20e831445f4b63f1d8d054bca30] running status set to false 
[INFO ] 2024-07-03 10:49:03.608 - [来自SourceMongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-03 10:49:03.614 - [来自SourceMongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-03 10:49:03.614 - [来自SourceMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[43b2c20e831445f4b63f1d8d054bca30] schema data cleaned 
[INFO ] 2024-07-03 10:49:03.614 - [来自SourceMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[43b2c20e831445f4b63f1d8d054bca30] monitor closed 
[INFO ] 2024-07-03 10:49:03.614 - [来自SourceMongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[43b2c20e831445f4b63f1d8d054bca30] close complete, cost 14 ms 
[INFO ] 2024-07-03 10:49:06.006 - [来自SourceMongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-03 10:49:06.014 - [来自SourceMongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3894cbcb 
[INFO ] 2024-07-03 10:49:06.015 - [来自SourceMongo的共享挖掘任务] - Stop task milestones: 6683c412fa7caf4ccea13264(来自SourceMongo的共享挖掘任务)  
[INFO ] 2024-07-03 10:49:06.151 - [来自SourceMongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-03 10:49:06.151 - [来自SourceMongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-03 10:49:06.173 - [来自SourceMongo的共享挖掘任务] - Remove memory task client succeed, task: 来自SourceMongo的共享挖掘任务[6683c412fa7caf4ccea13264] 
[INFO ] 2024-07-03 10:49:06.178 - [来自SourceMongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自SourceMongo的共享挖掘任务[6683c412fa7caf4ccea13264] 
[INFO ] 2024-07-03 11:34:01.381 - [来自SourceMongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 699 
