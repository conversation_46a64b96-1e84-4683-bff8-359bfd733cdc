[INFO ] 2024-07-24 15:20:47.558 - [测试184SqlServer] - Task initialization... 
[INFO ] 2024-07-24 15:20:47.560 - [测试184SqlServer] - Start task milestones: 66a0ab20f604e81d788d03e3(测试184SqlServer) 
[INFO ] 2024-07-24 15:20:47.652 - [测试184SqlServer] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 15:20:47.709 - [测试184SqlServer] - The engine receives 测试184SqlServer task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 15:20:47.810 - [测试184SqlServer][CLAIM] - Node CLAIM[fff2d76b-cb53-492f-97d6-9bcc7a009c72] start preload schema,table counts: 1 
[INFO ] 2024-07-24 15:20:47.810 - [测试184SqlServer][dummy_test] - Node dummy_test[5d361351-118f-4a3d-83c2-3b76b48c57ee] start preload schema,table counts: 1 
[INFO ] 2024-07-24 15:20:47.810 - [测试184SqlServer][dummy_test] - Node dummy_test[5d361351-118f-4a3d-83c2-3b76b48c57ee] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 15:20:47.810 - [测试184SqlServer][CLAIM] - Node CLAIM[fff2d76b-cb53-492f-97d6-9bcc7a009c72] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 15:20:48.924 - [测试184SqlServer][dummy_test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-24 15:20:49.108 - [测试184SqlServer][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-07-24 15:20:49.108 - [测试184SqlServer][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-07-24 15:20:49.109 - [测试184SqlServer][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 15:20:49.154 - [测试184SqlServer][CLAIM] - batch offset found: {},stream offset found: {"currentStartLSN":"000036ED000173C00001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-24 15:20:49.155 - [测试184SqlServer][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-24 15:20:49.344 - [测试184SqlServer][CLAIM] - Initial sync started 
[INFO ] 2024-07-24 15:20:49.363 - [测试184SqlServer][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-07-24 15:20:49.370 - [测试184SqlServer][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-07-24 15:20:49.526 - [测试184SqlServer][CLAIM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-07-24 15:20:49.526 - [测试184SqlServer][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 15:20:49.527 - [测试184SqlServer][CLAIM] - Initial sync completed 
[INFO ] 2024-07-24 15:20:49.527 - [测试184SqlServer][CLAIM] - Incremental sync starting... 
[INFO ] 2024-07-24 15:20:49.532 - [测试184SqlServer][CLAIM] - Initial sync completed 
[INFO ] 2024-07-24 15:20:49.533 - [测试184SqlServer][CLAIM] - Starting stream read, table list: [CLAIM, _tapdata_heartbeat_table], offset: {"currentStartLSN":"000036ED000173C00001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-07-24 15:20:49.788 - [测试184SqlServer][CLAIM] - opened cdc tables: [Category0620, Category_test, Category, Customer, ChildTable, a_test, test001_dummy_test, dummy612, Product, TEST_LENGTH, table_name, IdentityServerIdentityResourceProperties, orderitem, Category1, testdate2, testTable, Supplier, SupplierInfo, TEST_DDL_001, reimbursement_approval, _tapdata_heartbeat_table, my_table, MyTable, t_9_1_3, ParentTable, t_9_1_1, testdate, test8, test9, AA_0601, Category1_test, test7, AA_0606, orders, AA_0607] 
[INFO ] 2024-07-24 15:20:49.991 - [测试184SqlServer][CLAIM] - building CT table for table CLAIM 
[INFO ] 2024-07-24 15:20:50.534 - [测试184SqlServer][CLAIM] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-07-24 15:20:50.534 - [测试184SqlServer][CLAIM] - Connector SQL Server incremental start succeed, tables: [CLAIM, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-24 15:54:42.791 - [测试184SqlServer][CLAIM] - Node CLAIM[fff2d76b-cb53-492f-97d6-9bcc7a009c72] running status set to false 
[INFO ] 2024-07-24 15:54:43.404 - [测试184SqlServer][CLAIM] - Incremental sync completed 
[INFO ] 2024-07-24 15:54:45.776 - [测试184SqlServer][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-fff2d76b-cb53-492f-97d6-9bcc7a009c72 
[INFO ] 2024-07-24 15:54:45.778 - [测试184SqlServer][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-fff2d76b-cb53-492f-97d6-9bcc7a009c72 
[INFO ] 2024-07-24 15:54:45.778 - [测试184SqlServer][CLAIM] - Node CLAIM[fff2d76b-cb53-492f-97d6-9bcc7a009c72] schema data cleaned 
[INFO ] 2024-07-24 15:54:45.778 - [测试184SqlServer][CLAIM] - Node CLAIM[fff2d76b-cb53-492f-97d6-9bcc7a009c72] monitor closed 
[INFO ] 2024-07-24 15:54:45.779 - [测试184SqlServer][CLAIM] - Node CLAIM[fff2d76b-cb53-492f-97d6-9bcc7a009c72] close complete, cost 3037 ms 
[INFO ] 2024-07-24 15:54:45.779 - [测试184SqlServer][dummy_test] - Node dummy_test[5d361351-118f-4a3d-83c2-3b76b48c57ee] running status set to false 
[INFO ] 2024-07-24 15:54:45.801 - [测试184SqlServer][dummy_test] - Stop connector 
[INFO ] 2024-07-24 15:54:45.801 - [测试184SqlServer][dummy_test] - PDK connector node stopped: HazelcastTargetPdkDataNode-5d361351-118f-4a3d-83c2-3b76b48c57ee 
[INFO ] 2024-07-24 15:54:45.801 - [测试184SqlServer][dummy_test] - PDK connector node released: HazelcastTargetPdkDataNode-5d361351-118f-4a3d-83c2-3b76b48c57ee 
[INFO ] 2024-07-24 15:54:45.801 - [测试184SqlServer][dummy_test] - Node dummy_test[5d361351-118f-4a3d-83c2-3b76b48c57ee] schema data cleaned 
[INFO ] 2024-07-24 15:54:45.802 - [测试184SqlServer][dummy_test] - Node dummy_test[5d361351-118f-4a3d-83c2-3b76b48c57ee] monitor closed 
[INFO ] 2024-07-24 15:54:45.803 - [测试184SqlServer][dummy_test] - Node dummy_test[5d361351-118f-4a3d-83c2-3b76b48c57ee] close complete, cost 23 ms 
[INFO ] 2024-07-24 15:54:47.593 - [测试184SqlServer] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 15:54:47.610 - [测试184SqlServer] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6175886a 
[INFO ] 2024-07-24 15:54:47.728 - [测试184SqlServer] - Stop task milestones: 66a0ab20f604e81d788d03e3(测试184SqlServer)  
[INFO ] 2024-07-24 15:54:47.728 - [测试184SqlServer] - Stopped task aspect(s) 
[INFO ] 2024-07-24 15:54:47.760 - [测试184SqlServer] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 15:54:47.762 - [测试184SqlServer] - Remove memory task client succeed, task: 测试184SqlServer[66a0ab20f604e81d788d03e3] 
[INFO ] 2024-07-24 15:54:47.762 - [测试184SqlServer] - Destroy memory task client cache succeed, task: 测试184SqlServer[66a0ab20f604e81d788d03e3] 
