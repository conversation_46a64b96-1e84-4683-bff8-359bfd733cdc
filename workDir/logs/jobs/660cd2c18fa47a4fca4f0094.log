[INFO ] 2024-04-03 11:56:00.622 - [任务 47] - Start task milestones: 660cd2c18fa47a4fca4f0094(任务 47) 
[INFO ] 2024-04-03 11:56:00.628 - [任务 47] - Task initialization... 
[INFO ] 2024-04-03 11:56:00.629 - [任务 47] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-03 11:56:00.629 - [任务 47] - The engine receives 任务 47 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-03 11:56:00.871 - [任务 47][test1] - Node test1[e4251c88-d359-4553-9242-2ee90170b684] start preload schema,table counts: 1 
[INFO ] 2024-04-03 11:56:00.872 - [任务 47][CLAIM] - Node CLAIM[f7430b8f-57ca-417b-bbd9-ea41c2abc600] start preload schema,table counts: 1 
[INFO ] 2024-04-03 11:56:00.947 - [任务 47][CLAIM] - Node CLAIM[f7430b8f-57ca-417b-bbd9-ea41c2abc600] preload schema finished, cost 75 ms 
[INFO ] 2024-04-03 11:56:01.150 - [任务 47][test1] - Node test1[e4251c88-d359-4553-9242-2ee90170b684] preload schema finished, cost 75 ms 
[INFO ] 2024-04-03 11:56:02.022 - [任务 47][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-03 11:56:02.023 - [任务 47][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-03 11:56:02.039 - [任务 47][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-03 11:56:02.040 - [任务 47][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145949093,"gtidSet":""} 
[INFO ] 2024-04-03 11:56:02.190 - [任务 47][CLAIM] - Initial sync started 
[INFO ] 2024-04-03 11:56:02.190 - [任务 47][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-03 11:56:02.203 - [任务 47][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-03 11:56:02.407 - [任务 47][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-04-03 11:56:08.660 - [任务 47][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-03 11:56:09.320 - [任务 47][CLAIM] - Initial sync completed 
[INFO ] 2024-04-03 11:56:09.326 - [任务 47][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-03 11:56:09.329 - [任务 47][CLAIM] - Initial sync completed 
[INFO ] 2024-04-03 11:56:09.331 - [任务 47][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":145949093,"gtidSet":""} 
[INFO ] 2024-04-03 11:56:09.516 - [任务 47][CLAIM] - Starting mysql cdc, server name: 1920e388-ae07-417e-8a8e-760247c77277 
[INFO ] 2024-04-03 11:56:09.517 - [任务 47][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1315998754
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 1920e388-ae07-417e-8a8e-760247c77277
  database.port: 3306
  threadName: Debezium-Mysql-Connector-1920e388-ae07-417e-8a8e-760247c77277
  database.hostname: 127.0.0.1
  database.password: ********
  name: 1920e388-ae07-417e-8a8e-760247c77277
  pdk.offset.string: {"name":"1920e388-ae07-417e-8a8e-760247c77277","offset":{"{\"server\":\"1920e388-ae07-417e-8a8e-760247c77277\"}":"{\"file\":\"binlog.000020\",\"pos\":145949093,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-03 11:56:09.923 - [任务 47][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-04-03 11:56:21.807 - [任务 47] - Stop task milestones: 660cd2c18fa47a4fca4f0094(任务 47)  
[INFO ] 2024-04-03 11:56:21.981 - [任务 47][CLAIM] - Node CLAIM[f7430b8f-57ca-417b-bbd9-ea41c2abc600] running status set to false 
[INFO ] 2024-04-03 11:56:22.081 - [任务 47][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-04-03 11:56:22.081 - [任务 47][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-04-03 11:56:22.092 - [任务 47][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-f7430b8f-57ca-417b-bbd9-ea41c2abc600 
[INFO ] 2024-04-03 11:56:22.093 - [任务 47][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-f7430b8f-57ca-417b-bbd9-ea41c2abc600 
[INFO ] 2024-04-03 11:56:22.096 - [任务 47][CLAIM] - Node CLAIM[f7430b8f-57ca-417b-bbd9-ea41c2abc600] schema data cleaned 
[INFO ] 2024-04-03 11:56:22.096 - [任务 47][CLAIM] - Node CLAIM[f7430b8f-57ca-417b-bbd9-ea41c2abc600] monitor closed 
[INFO ] 2024-04-03 11:56:22.104 - [任务 47][CLAIM] - Node CLAIM[f7430b8f-57ca-417b-bbd9-ea41c2abc600] close complete, cost 120 ms 
[INFO ] 2024-04-03 11:56:22.104 - [任务 47][test1] - Node test1[e4251c88-d359-4553-9242-2ee90170b684] running status set to false 
[INFO ] 2024-04-03 11:56:22.148 - [任务 47][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-e4251c88-d359-4553-9242-2ee90170b684 
[INFO ] 2024-04-03 11:56:22.148 - [任务 47][test1] - PDK connector node released: HazelcastTargetPdkDataNode-e4251c88-d359-4553-9242-2ee90170b684 
[INFO ] 2024-04-03 11:56:22.149 - [任务 47][test1] - Node test1[e4251c88-d359-4553-9242-2ee90170b684] schema data cleaned 
[INFO ] 2024-04-03 11:56:22.149 - [任务 47][test1] - Node test1[e4251c88-d359-4553-9242-2ee90170b684] monitor closed 
[INFO ] 2024-04-03 11:56:22.356 - [任务 47][test1] - Node test1[e4251c88-d359-4553-9242-2ee90170b684] close complete, cost 49 ms 
[INFO ] 2024-04-03 11:56:26.465 - [任务 47] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-03 11:56:26.466 - [任务 47] - Stopped task aspect(s) 
[INFO ] 2024-04-03 11:56:26.466 - [任务 47] - Snapshot order controller have been removed 
[INFO ] 2024-04-03 11:56:26.554 - [任务 47] - Remove memory task client succeed, task: 任务 47[660cd2c18fa47a4fca4f0094] 
[INFO ] 2024-04-03 11:56:26.756 - [任务 47] - Destroy memory task client cache succeed, task: 任务 47[660cd2c18fa47a4fca4f0094] 
