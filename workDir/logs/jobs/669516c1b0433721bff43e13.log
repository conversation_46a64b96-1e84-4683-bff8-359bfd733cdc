[INFO ] 2024-07-16 05:26:35.056 - [Heart<PERSON>-<PERSON><PERSON>] - Start task milestones: 669516c1b0433721bff43e13(Heartbeat-<PERSON><PERSON>) 
[INFO ] 2024-07-16 05:26:35.751 - [Heartbeat-<PERSON><PERSON>] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-16 05:26:35.751 - [Heartbeat-<PERSON><PERSON>] - The engine receives Heartbeat-Mongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-16 05:26:36.458 - [Heartbeat-<PERSON><PERSON>][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e218b9ee-21c1-405a-9154-c041428c5c5d] start preload schema,table counts: 1 
[INFO ] 2024-07-16 05:26:36.462 - [Heartbeat-Mon<PERSON>][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e218b9ee-21c1-405a-9154-c041428c5c5d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-16 05:26:36.493 - [<PERSON><PERSON>-<PERSON><PERSON>][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b2c499c5-02e3-453c-bd66-2be4dd9efba8] start preload schema,table counts: 1 
[INFO ] 2024-07-16 05:26:36.495 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b2c499c5-02e3-453c-bd66-2be4dd9efba8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-16 05:26:37.009 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-16 05:26:37.019 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-16 05:26:37.054 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-16 05:26:37.055 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1721046723358,"lastTimes":1721048019273,"lastTN":7,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":92,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-16 05:26:37.133 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721046723358,"lastTimes":1721048019273,"lastTN":7,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":92,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-16 05:26:37.133 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-16 05:26:37.345 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-16 05:26:37.465 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-16 06:07:28.742 - [Heartbeat-Mongo] - Start task milestones: 669516c1b0433721bff43e13(Heartbeat-Mongo) 
[INFO ] 2024-07-16 06:07:29.601 - [Heartbeat-Mongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-16 06:07:29.810 - [Heartbeat-Mongo] - The engine receives Heartbeat-Mongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-16 06:07:30.884 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e218b9ee-21c1-405a-9154-c041428c5c5d] start preload schema,table counts: 1 
[INFO ] 2024-07-16 06:07:30.885 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e218b9ee-21c1-405a-9154-c041428c5c5d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-16 06:07:30.912 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b2c499c5-02e3-453c-bd66-2be4dd9efba8] start preload schema,table counts: 1 
[INFO ] 2024-07-16 06:07:30.914 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b2c499c5-02e3-453c-bd66-2be4dd9efba8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-16 06:07:36.016 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-16 06:07:36.024 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-16 06:07:36.058 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-16 06:07:36.061 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1721046723358,"lastTimes":1721080619698,"lastTN":1818,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1910,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-16 06:07:36.189 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721046723358,"lastTimes":1721080619698,"lastTN":1818,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1910,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-16 06:07:36.191 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-16 06:07:36.383 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-16 06:07:36.385 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-16 06:20:46.181 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b2c499c5-02e3-453c-bd66-2be4dd9efba8] running status set to false 
[INFO ] 2024-07-16 06:20:46.195 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-16 06:20:46.196 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-b2c499c5-02e3-453c-bd66-2be4dd9efba8 
[INFO ] 2024-07-16 06:20:46.196 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-b2c499c5-02e3-453c-bd66-2be4dd9efba8 
[INFO ] 2024-07-16 06:20:46.197 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b2c499c5-02e3-453c-bd66-2be4dd9efba8] schema data cleaned 
[INFO ] 2024-07-16 06:20:46.202 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b2c499c5-02e3-453c-bd66-2be4dd9efba8] monitor closed 
[INFO ] 2024-07-16 06:20:46.203 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b2c499c5-02e3-453c-bd66-2be4dd9efba8] close complete, cost 23 ms 
[INFO ] 2024-07-16 06:20:46.231 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e218b9ee-21c1-405a-9154-c041428c5c5d] running status set to false 
[INFO ] 2024-07-16 06:20:46.232 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-e218b9ee-21c1-405a-9154-c041428c5c5d 
[INFO ] 2024-07-16 06:20:46.232 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-e218b9ee-21c1-405a-9154-c041428c5c5d 
[INFO ] 2024-07-16 06:20:46.232 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e218b9ee-21c1-405a-9154-c041428c5c5d] schema data cleaned 
[INFO ] 2024-07-16 06:20:46.232 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e218b9ee-21c1-405a-9154-c041428c5c5d] monitor closed 
[INFO ] 2024-07-16 06:20:46.438 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e218b9ee-21c1-405a-9154-c041428c5c5d] close complete, cost 31 ms 
[INFO ] 2024-07-16 06:20:51.045 - [Heartbeat-Mongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-16 06:20:51.045 - [Heartbeat-Mongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@75a092b9 
[INFO ] 2024-07-16 06:20:51.153 - [Heartbeat-Mongo] - Stop task milestones: 669516c1b0433721bff43e13(Heartbeat-Mongo)  
[INFO ] 2024-07-16 06:20:51.170 - [Heartbeat-Mongo] - Stopped task aspect(s) 
[INFO ] 2024-07-16 06:20:51.170 - [Heartbeat-Mongo] - Snapshot order controller have been removed 
[INFO ] 2024-07-16 06:20:51.210 - [Heartbeat-Mongo] - Remove memory task client succeed, task: Heartbeat-Mongo[669516c1b0433721bff43e13] 
[INFO ] 2024-07-16 06:20:51.210 - [Heartbeat-Mongo] - Destroy memory task client cache succeed, task: Heartbeat-Mongo[669516c1b0433721bff43e13] 
[INFO ] 2024-07-16 06:21:00.627 - [Heartbeat-Mongo] - Start task milestones: 669516c1b0433721bff43e13(Heartbeat-Mongo) 
[INFO ] 2024-07-16 06:21:00.627 - [Heartbeat-Mongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-16 06:21:00.706 - [Heartbeat-Mongo] - The engine receives Heartbeat-Mongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-16 06:21:00.706 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b2c499c5-02e3-453c-bd66-2be4dd9efba8] start preload schema,table counts: 1 
[INFO ] 2024-07-16 06:21:00.706 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e218b9ee-21c1-405a-9154-c041428c5c5d] start preload schema,table counts: 1 
[INFO ] 2024-07-16 06:21:00.706 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b2c499c5-02e3-453c-bd66-2be4dd9efba8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-16 06:21:00.706 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e218b9ee-21c1-405a-9154-c041428c5c5d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-16 06:21:00.823 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-16 06:21:00.823 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-16 06:21:00.827 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-16 06:21:00.828 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1721046723358,"lastTimes":1721082046173,"lastTN":769,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":2679,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-16 06:21:00.919 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721046723358,"lastTimes":1721082046173,"lastTN":769,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":2679,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-16 06:21:00.919 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-16 06:21:00.920 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-16 06:21:01.125 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-16 07:51:24.137 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b2c499c5-02e3-453c-bd66-2be4dd9efba8] running status set to false 
[INFO ] 2024-07-16 07:51:24.138 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-16 07:51:24.146 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-b2c499c5-02e3-453c-bd66-2be4dd9efba8 
[INFO ] 2024-07-16 07:51:24.148 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-b2c499c5-02e3-453c-bd66-2be4dd9efba8 
[INFO ] 2024-07-16 07:51:24.148 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b2c499c5-02e3-453c-bd66-2be4dd9efba8] schema data cleaned 
[INFO ] 2024-07-16 07:51:24.149 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b2c499c5-02e3-453c-bd66-2be4dd9efba8] monitor closed 
[INFO ] 2024-07-16 07:51:24.151 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b2c499c5-02e3-453c-bd66-2be4dd9efba8] close complete, cost 34 ms 
[INFO ] 2024-07-16 07:51:24.151 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e218b9ee-21c1-405a-9154-c041428c5c5d] running status set to false 
[INFO ] 2024-07-16 07:51:24.188 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-e218b9ee-21c1-405a-9154-c041428c5c5d 
[INFO ] 2024-07-16 07:51:24.195 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-e218b9ee-21c1-405a-9154-c041428c5c5d 
[INFO ] 2024-07-16 07:51:24.196 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e218b9ee-21c1-405a-9154-c041428c5c5d] schema data cleaned 
[INFO ] 2024-07-16 07:51:24.196 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e218b9ee-21c1-405a-9154-c041428c5c5d] monitor closed 
[INFO ] 2024-07-16 07:51:24.399 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e218b9ee-21c1-405a-9154-c041428c5c5d] close complete, cost 46 ms 
[INFO ] 2024-07-16 07:51:26.782 - [Heartbeat-Mongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-16 07:51:26.895 - [Heartbeat-Mongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6f1c2735 
[INFO ] 2024-07-16 07:51:26.898 - [Heartbeat-Mongo] - Stop task milestones: 669516c1b0433721bff43e13(Heartbeat-Mongo)  
[INFO ] 2024-07-16 07:51:26.918 - [Heartbeat-Mongo] - Stopped task aspect(s) 
[INFO ] 2024-07-16 07:51:26.918 - [Heartbeat-Mongo] - Snapshot order controller have been removed 
[INFO ] 2024-07-16 07:51:26.965 - [Heartbeat-Mongo] - Remove memory task client succeed, task: Heartbeat-Mongo[669516c1b0433721bff43e13] 
[INFO ] 2024-07-16 07:51:26.965 - [Heartbeat-Mongo] - Destroy memory task client cache succeed, task: Heartbeat-Mongo[669516c1b0433721bff43e13] 
[INFO ] 2024-07-16 07:53:36.722 - [Heartbeat-Mongo] - Start task milestones: 669516c1b0433721bff43e13(Heartbeat-Mongo) 
[INFO ] 2024-07-16 07:53:37.055 - [Heartbeat-Mongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-16 07:53:37.096 - [Heartbeat-Mongo] - The engine receives Heartbeat-Mongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-16 07:53:37.176 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e218b9ee-21c1-405a-9154-c041428c5c5d] start preload schema,table counts: 1 
[INFO ] 2024-07-16 07:53:37.176 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b2c499c5-02e3-453c-bd66-2be4dd9efba8] start preload schema,table counts: 1 
[INFO ] 2024-07-16 07:53:37.177 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[e218b9ee-21c1-405a-9154-c041428c5c5d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-16 07:53:37.177 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b2c499c5-02e3-453c-bd66-2be4dd9efba8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-16 07:53:37.398 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-16 07:53:37.398 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-16 07:53:37.400 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-16 07:53:37.415 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1721046723358,"lastTimes":1721087483611,"lastTN":5404,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":8083,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-16 07:53:37.532 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-16 07:53:37.536 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721046723358,"lastTimes":1721087483611,"lastTN":5404,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":8083,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-16 07:53:37.537 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-16 07:53:37.537 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-16 07:55:47.599 - [Heartbeat-Mongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[b2c499c5-02e3-453c-bd66-2be4dd9efba8] running status set to false 
