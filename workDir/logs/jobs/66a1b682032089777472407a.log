[INFO ] 2024-07-25 10:25:21.790 - [测试自动校验二次校验] - Task initialization... 
[INFO ] 2024-07-25 10:25:21.790 - [测试自动校验二次校验] - Start task milestones: 66a1b682032089777472407a(测试自动校验二次校验) 
[INFO ] 2024-07-25 10:25:22.133 - [测试自动校验二次校验] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 10:25:22.176 - [测试自动校验二次校验] - The engine receives 测试自动校验二次校验 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 10:25:22.280 - [测试自动校验二次校验][TestInspect] - Node TestInspect[ba98c41e-1023-486e-88a9-c225adcaf270] start preload schema,table counts: 1 
[INFO ] 2024-07-25 10:25:22.286 - [测试自动校验二次校验][POLICY] - Node POLICY[31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720] start preload schema,table counts: 1 
[INFO ] 2024-07-25 10:25:22.288 - [测试自动校验二次校验][TestInspect] - Node TestInspect[ba98c41e-1023-486e-88a9-c225adcaf270] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 10:25:22.291 - [测试自动校验二次校验][POLICY] - Node POLICY[31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 10:25:22.976 - [测试自动校验二次校验][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-25 10:25:22.976 - [测试自动校验二次校验][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-25 10:25:22.977 - [测试自动校验二次校验][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 10:25:23.203 - [测试自动校验二次校验][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1721874323,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-25 10:25:23.204 - [测试自动校验二次校验][POLICY] - Initial sync started 
[INFO ] 2024-07-25 10:25:23.219 - [测试自动校验二次校验][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-25 10:25:23.219 - [测试自动校验二次校验][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-25 10:25:23.270 - [测试自动校验二次校验][TestInspect] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 10:25:23.270 - [测试自动校验二次校验][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 10:25:23.456 - [测试自动校验二次校验][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-25 10:25:23.456 - [测试自动校验二次校验][POLICY] - Initial sync completed 
[INFO ] 2024-07-25 10:25:23.457 - [测试自动校验二次校验][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-25 10:25:23.457 - [测试自动校验二次校验][POLICY] - Initial sync completed 
[INFO ] 2024-07-25 10:25:23.458 - [测试自动校验二次校验][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721874323,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-25 10:25:23.505 - [测试自动校验二次校验][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-25 10:38:03.061 - [测试自动校验二次校验][POLICY] - Node POLICY[31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720] running status set to false 
[INFO ] 2024-07-25 10:38:03.083 - [测试自动校验二次校验][POLICY] - Incremental sync completed 
[INFO ] 2024-07-25 10:38:03.083 - [测试自动校验二次校验][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720 
[INFO ] 2024-07-25 10:38:03.085 - [测试自动校验二次校验][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720 
[INFO ] 2024-07-25 10:38:03.085 - [测试自动校验二次校验][POLICY] - Node POLICY[31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720] schema data cleaned 
[INFO ] 2024-07-25 10:38:03.093 - [测试自动校验二次校验][POLICY] - Node POLICY[31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720] monitor closed 
[INFO ] 2024-07-25 10:38:03.094 - [测试自动校验二次校验][POLICY] - Node POLICY[31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720] close complete, cost 37 ms 
[INFO ] 2024-07-25 10:38:03.125 - [测试自动校验二次校验][TestInspect] - Node TestInspect[ba98c41e-1023-486e-88a9-c225adcaf270] running status set to false 
[INFO ] 2024-07-25 10:38:03.125 - [测试自动校验二次校验][TestInspect] - PDK connector node stopped: HazelcastTargetPdkDataNode-ba98c41e-1023-486e-88a9-c225adcaf270 
[INFO ] 2024-07-25 10:38:03.126 - [测试自动校验二次校验][TestInspect] - PDK connector node released: HazelcastTargetPdkDataNode-ba98c41e-1023-486e-88a9-c225adcaf270 
[INFO ] 2024-07-25 10:38:03.126 - [测试自动校验二次校验][TestInspect] - Node TestInspect[ba98c41e-1023-486e-88a9-c225adcaf270] schema data cleaned 
[INFO ] 2024-07-25 10:38:03.127 - [测试自动校验二次校验][TestInspect] - Node TestInspect[ba98c41e-1023-486e-88a9-c225adcaf270] monitor closed 
[INFO ] 2024-07-25 10:38:03.127 - [测试自动校验二次校验][TestInspect] - Node TestInspect[ba98c41e-1023-486e-88a9-c225adcaf270] close complete, cost 33 ms 
[INFO ] 2024-07-25 10:38:03.497 - [测试自动校验二次校验] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 10:38:03.628 - [测试自动校验二次校验] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1bb8456e 
[INFO ] 2024-07-25 10:38:03.629 - [测试自动校验二次校验] - Stop task milestones: 66a1b682032089777472407a(测试自动校验二次校验)  
[INFO ] 2024-07-25 10:38:03.664 - [测试自动校验二次校验] - Stopped task aspect(s) 
[INFO ] 2024-07-25 10:38:03.665 - [测试自动校验二次校验] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 10:38:03.737 - [测试自动校验二次校验] - Remove memory task client succeed, task: 测试自动校验二次校验[66a1b682032089777472407a] 
[INFO ] 2024-07-25 10:38:03.739 - [测试自动校验二次校验] - Destroy memory task client cache succeed, task: 测试自动校验二次校验[66a1b682032089777472407a] 
[INFO ] 2024-07-25 11:12:23.755 - [测试自动校验二次校验] - Task initialization... 
[INFO ] 2024-07-25 11:12:23.756 - [测试自动校验二次校验] - Start task milestones: 66a1b682032089777472407a(测试自动校验二次校验) 
[INFO ] 2024-07-25 11:12:23.960 - [测试自动校验二次校验] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 11:12:23.960 - [测试自动校验二次校验] - The engine receives 测试自动校验二次校验 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 11:12:24.006 - [测试自动校验二次校验][POLICY] - Node POLICY[31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720] start preload schema,table counts: 1 
[INFO ] 2024-07-25 11:12:24.006 - [测试自动校验二次校验][TestInspect] - Node TestInspect[ba98c41e-1023-486e-88a9-c225adcaf270] start preload schema,table counts: 1 
[INFO ] 2024-07-25 11:12:24.008 - [测试自动校验二次校验][POLICY] - Node POLICY[31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 11:12:24.008 - [测试自动校验二次校验][TestInspect] - Node TestInspect[ba98c41e-1023-486e-88a9-c225adcaf270] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 11:12:24.822 - [测试自动校验二次校验][TestInspect] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 11:12:24.848 - [测试自动校验二次校验][TestInspect] - Table "test.TestInspect" exists, skip auto create table 
[INFO ] 2024-07-25 11:12:24.858 - [测试自动校验二次校验][TestInspect] - The table TestInspect has already exist. 
[INFO ] 2024-07-25 11:12:24.884 - [测试自动校验二次校验][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-25 11:12:24.885 - [测试自动校验二次校验][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-25 11:12:24.885 - [测试自动校验二次校验][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 11:12:25.073 - [测试自动校验二次校验][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1721877144,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-25 11:12:25.073 - [测试自动校验二次校验][POLICY] - Initial sync started 
[INFO ] 2024-07-25 11:12:25.081 - [测试自动校验二次校验][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-25 11:12:25.082 - [测试自动校验二次校验][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-25 11:12:25.146 - [测试自动校验二次校验][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 11:12:25.147 - [测试自动校验二次校验][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-25 11:12:25.147 - [测试自动校验二次校验][POLICY] - Initial sync completed 
[INFO ] 2024-07-25 11:12:25.151 - [测试自动校验二次校验][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-25 11:12:25.154 - [测试自动校验二次校验][POLICY] - Initial sync completed 
[INFO ] 2024-07-25 11:12:25.154 - [测试自动校验二次校验][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721877144,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-25 11:12:25.359 - [测试自动校验二次校验][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-25 11:22:57.660 - [测试自动校验二次校验][POLICY] - Node POLICY[31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720] running status set to false 
[INFO ] 2024-07-25 11:45:23.266 - [测试自动校验二次校验] - Task initialization... 
[INFO ] 2024-07-25 11:45:23.270 - [测试自动校验二次校验] - Start task milestones: 66a1b682032089777472407a(测试自动校验二次校验) 
[INFO ] 2024-07-25 11:45:23.627 - [测试自动校验二次校验] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 11:45:23.633 - [测试自动校验二次校验] - The engine receives 测试自动校验二次校验 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 11:45:23.752 - [测试自动校验二次校验][POLICY] - Node POLICY[31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720] start preload schema,table counts: 1 
[INFO ] 2024-07-25 11:45:23.755 - [测试自动校验二次校验][TestInspect] - Node TestInspect[ba98c41e-1023-486e-88a9-c225adcaf270] start preload schema,table counts: 1 
[INFO ] 2024-07-25 11:45:23.755 - [测试自动校验二次校验][POLICY] - Node POLICY[31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 11:45:23.755 - [测试自动校验二次校验][TestInspect] - Node TestInspect[ba98c41e-1023-486e-88a9-c225adcaf270] preload schema finished, cost 1 ms 
[INFO ] 2024-07-25 11:45:24.080 - [测试自动校验二次校验][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-25 11:45:24.080 - [测试自动校验二次校验][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-25 11:45:24.080 - [测试自动校验二次校验][TestInspect] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 11:45:24.080 - [测试自动校验二次校验][POLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-25 11:45:24.105 - [测试自动校验二次校验][POLICY] - batch offset found: {"POLICY":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"_data":{"value":"8266A1C50C000000012B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F696400646695B95266AB5EDE8A9DCD3E0004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-25 11:45:24.163 - [测试自动校验二次校验][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-25 11:45:24.163 - [测试自动校验二次校验][POLICY] - Initial sync completed 
[INFO ] 2024-07-25 11:45:24.261 - [测试自动校验二次校验][POLICY] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"_data":{"value":"8266A1C50C000000012B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F696400646695B95266AB5EDE8A9DCD3E0004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-25 11:45:24.261 - [测试自动校验二次校验][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-25 16:46:27.137 - [测试自动校验二次校验][POLICY] - Node POLICY[31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720] running status set to false 
[INFO ] 2024-07-25 16:46:27.149 - [测试自动校验二次校验][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720 
[INFO ] 2024-07-25 16:46:27.149 - [测试自动校验二次校验][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720 
[INFO ] 2024-07-25 16:46:27.149 - [测试自动校验二次校验][POLICY] - Node POLICY[31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720] schema data cleaned 
[INFO ] 2024-07-25 16:46:27.149 - [测试自动校验二次校验][POLICY] - Node POLICY[31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720] monitor closed 
[INFO ] 2024-07-25 16:46:27.151 - [测试自动校验二次校验][POLICY] - Node POLICY[31eb8f1f-a5a7-4c7c-b3c1-4dc8c266b720] close complete, cost 17 ms 
[INFO ] 2024-07-25 16:46:27.171 - [测试自动校验二次校验][TestInspect] - Node TestInspect[ba98c41e-1023-486e-88a9-c225adcaf270] running status set to false 
[INFO ] 2024-07-25 16:46:27.171 - [测试自动校验二次校验][TestInspect] - PDK connector node stopped: HazelcastTargetPdkDataNode-ba98c41e-1023-486e-88a9-c225adcaf270 
[INFO ] 2024-07-25 16:46:27.171 - [测试自动校验二次校验][TestInspect] - PDK connector node released: HazelcastTargetPdkDataNode-ba98c41e-1023-486e-88a9-c225adcaf270 
[INFO ] 2024-07-25 16:46:27.172 - [测试自动校验二次校验][TestInspect] - Node TestInspect[ba98c41e-1023-486e-88a9-c225adcaf270] schema data cleaned 
[INFO ] 2024-07-25 16:46:27.172 - [测试自动校验二次校验][TestInspect] - Node TestInspect[ba98c41e-1023-486e-88a9-c225adcaf270] monitor closed 
[INFO ] 2024-07-25 16:46:27.376 - [测试自动校验二次校验][TestInspect] - Node TestInspect[ba98c41e-1023-486e-88a9-c225adcaf270] close complete, cost 21 ms 
[INFO ] 2024-07-25 16:46:27.784 - [测试自动校验二次校验][POLICY] - Incremental sync completed 
[INFO ] 2024-07-25 16:46:31.830 - [测试自动校验二次校验] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 16:46:31.943 - [测试自动校验二次校验] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5d805746 
[INFO ] 2024-07-25 16:46:31.945 - [测试自动校验二次校验] - Stop task milestones: 66a1b682032089777472407a(测试自动校验二次校验)  
[INFO ] 2024-07-25 16:46:31.964 - [测试自动校验二次校验] - Stopped task aspect(s) 
[INFO ] 2024-07-25 16:46:31.964 - [测试自动校验二次校验] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 16:46:31.988 - [测试自动校验二次校验] - Remove memory task client succeed, task: 测试自动校验二次校验[66a1b682032089777472407a] 
[INFO ] 2024-07-25 16:46:31.990 - [测试自动校验二次校验] - Destroy memory task client cache succeed, task: 测试自动校验二次校验[66a1b682032089777472407a] 
